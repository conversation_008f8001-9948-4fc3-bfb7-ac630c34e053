{"name": "@modelcontextprotocol/sdk", "version": "0.6.0", "description": "Model Context Protocol implementation for TypeScript", "license": "MIT", "author": "An<PERSON><PERSON>, PBC (https://anthropic.com)", "homepage": "https://modelcontextprotocol.github.io", "bugs": "https://github.com/modelcontextprotocol/typescript-sdk/issues", "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {"./*": "./dist/*"}, "typesVersions": {"*": {"*": ["./dist/*"]}}, "files": ["dist"], "scripts": {"build": "tsc", "prepack": "tsc", "lint": "eslint src/", "test": "jest", "start": "npm run server", "server": "tsx watch --clear-screen=false src/cli.ts server", "client": "tsx src/cli.ts client"}, "dependencies": {"content-type": "^1.0.5", "raw-body": "^3.0.0", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.8.0", "@types/content-type": "^1.1.8", "@types/eslint__js": "^8.42.3", "@types/eventsource": "^1.1.15", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/node": "^22.0.2", "@types/ws": "^8.5.12", "eslint": "^9.8.0", "eventsource": "^2.0.2", "express": "^4.19.2", "jest": "^29.7.0", "ts-jest": "^29.2.4", "tsx": "^4.16.5", "typescript": "^5.5.4", "typescript-eslint": "^8.0.0", "ws": "^8.18.0"}, "resolutions": {"strip-ansi": "6.0.1"}}