{"name": "semantic-scholar-server", "version": "0.1.0", "description": "MCP server for accessing the Semantic Scholar Academic Graph API", "private": true, "type": "module", "bin": {"semantic-scholar-server": "./build/index.js"}, "files": ["build"], "scripts": {"build": "tsc && node -e \"require('fs').chmodSync('build/index.js', '755')\"", "prepare": "npm run build", "watch": "tsc --watch", "inspector": "npx @modelcontextprotocol/inspector build/index.js"}, "dependencies": {"@modelcontextprotocol/sdk": "0.6.0"}, "devDependencies": {"@types/node": "^20.11.24", "typescript": "^5.3.3"}}