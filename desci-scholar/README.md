# DeSci-Scholar: NFT-DOI Bridge System

**DOIs (+ ORCIDs) zu NFTs** - Revolutionäre Blockchain-Integration für wissenschaftliche Publikationen

## 🎯 Kernfokus: NFT-DOI Bridge System

DeSci-Scholar ist ein spezialisiertes **NFT-DOI Bridge System**, das wissenschaftliche DOIs in blockchain-verifizierte NFTs mit eingebetteten **DataCite-Metadaten** und **Crossref-Zitationsdaten** konvertiert. Das System fokussiert primär auf **DOI-zu-NFT-Konvertierung** mit **ORCID-Integration** als sekundäre Funktion für transparente, zitierbare und monetarisierbare wissenschaftliche Forschung.

### 🔗 Bridge-Architektur

Das NFT-DOI Bridge System integriert:
- **DataCite**: DOI-Registrierung und standardisierte Metadaten-Verwaltung
- **Crossref**: Zitationsverknüpfung und Event Data-Tracking für Forschungsimpact
- **ORCID**: Autorenidentifikation und -verifikation (sekundäre Funktion)
- **Polkadot Blockchain**: Dezentrale NFT-Prägung mit eingebetteten Metadaten
### 🚀 Bridge-Funktionen

- **🔗 DOI-zu-NFT-Konvertierung**: Automatisierte Transformation von DOIs in blockchain-verifizierte NFTs
- **📊 DataCite-Integration**: Vollständige DOI-Registrierung und Metadaten-Management über DataCite API
- **🔍 Crossref-Zitationsnetzwerk**: Echtzeit-Zitationstracking und Event Data über Crossref API
- **👤 ORCID-Anreicherung**: Autorenidentifikation und -verifikation als ergänzende Funktion
- **⛓️ Metadaten-Embedding**: Vollständige Einbettung von DataCite/Crossref-Metadaten in NFT Smart Contracts
- **📈 Impact-Tracking**: Kontinuierliche Überwachung von Zitationen, Downloads und Forschungsimpact
- **🔐 Eigentumsverifikation**: Blockchain-basierte Authentifizierung und Eigentumsnachweis
- **🔄 Synchronisation**: Automatische Aktualisierung von NFT-Metadaten bei DOI-Änderungen
## Statistik-Dashboard
Das Statistik-Dashboard bietet:
- **Plattform-Übersicht**: Gesamtstatistiken zu Publikationen, Aufrufen und Downloads
- **Trending-Publikationen**: Die aktivsten wissenschaftlichen Arbeiten mit visueller Aktivitätsanzeige
- **Zeitreihen-Analysen**: Nutzungstrends über verschiedene Zeiträume
- **Speicherstatistiken**: Einblicke in die Verteilung und Effizienz der dezentralen Speicherung
## Technologien
- **Frontend**: React mit Chart.js für Datenvisualisierung
- **Backend**: Node.js mit Express
- **Datenspeicherung**: EnhancedStorageService mit IPFS und BitTorrent-Integration
- **Datenbank**: MongoDB für strukturierte Daten und Metadaten
- **Authentifizierung**: JWT-basierte sichere Benutzerauthentifizierung
## Open Science
DeSci-Scholar ist der Open-Science-Bewegung verpflichtet. Wir glauben an:
- **Offenen Zugang zu Wissen**: Wissenschaftliche Publikationen sollten für alle zugänglich sein
- **Offene Daten**: Forschungsdaten sollten frei verfügbar und wiederverwendbar sein
- **Offene Methoden**: Forschungsmethoden sollten transparent und reproduzierbar sein
- **Offene Infrastruktur**: Wissenschaftliche Infrastruktur sollte offen und gemeinschaftlich entwickelt werden
Unsere vollständige Open-Science-Richtlinie finden Sie in der [OPEN_SCIENCE_POLICY.md](OPEN_SCIENCE_POLICY.md)-Datei.
## Integrierte Open-Source/Open-Science-Dienste
DeSci-Scholar integriert mehrere Open-Source- und Open-Science-Dienste:
- **[OpenCitations](https://opencitations.net/)**: Offene bibliografische und Zitationsdaten
- **[ORCID](https://orcid.org/)**: Offene Forscher- und Beitragsidentifikatoren
- **[DataCite](https://datacite.org/)**: Offene DOI-Registrierung und Metadaten
- **[arXiv](https://arxiv.org/)**: Offenes Preprint-Repository
- **[IPFS](https://ipfs.io/)**: Dezentrales, offenes Speichersystem
- **[BitTorrent](https://www.bittorrent.org/)**: Offenes Protokoll für Peer-to-Peer-Datenaustausch
## Installation und Start
1. Repository klonen:
   ```
   git clone https://github.com/username/desci-scholar.git
   cd desci-scholar
   ```
2. Abhängigkeiten installieren:
   ```
   npm install
   ```
3. Umgebungsvariablen konfigurieren:
   Erstellen Sie eine `.env`-Datei im Hauptverzeichnis und fügen Sie folgende Konfigurationen hinzu:
   ```
   PORT=3000
   MONGODB_URI=mongodb://localhost:27017/desci-scholar
   JWT_SECRET=your_jwt_secret
   IPFS_API_URL=http://localhost:5001
   ```
4. Anwendung starten:
   ```
   npm start
   ```
5. Öffnen Sie [http://localhost:3000](http://localhost:3000) in Ihrem Browser.
## API-Endpunkte
Die Anwendung stellt RESTful API-Endpunkte zur Verfügung:
- `/api/publications`: Verwalten wissenschaftlicher Publikationen
- `/api/auth`: Benutzerauthentifizierung
- `/api/stats`: Plattform- und Publikationsstatistiken
- `/api/storage`: Dezentrale Speicherverwaltung
## Beitragen
Wir freuen uns über Beiträge zur Weiterentwicklung von DeSci-Scholar. Bitte lesen Sie unsere Beitragsrichtlinien in der [CONTRIBUTING.md](CONTRIBUTING.md)-Datei und unseren Verhaltenskodex in der [CODE_OF_CONDUCT.md](CODE_OF_CONDUCT.md)-Datei.
## Lizenz
Dieses Projekt ist unter der MIT-Lizenz lizenziert - siehe die [LICENSE](LICENSE)-Datei für Details.