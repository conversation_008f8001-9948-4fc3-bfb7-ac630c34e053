# DeSci-Scholar
Eine dezentrale Open-Source-Plattform für wissenschaftliche Publikationen mit Fokus auf die Konvertierung von Digital Object Identifiers (DOIs) zu Non-Fungible Tokens (NFTs). DeSci-Scholar modernisiert das Konzept digitaler Notariatsdienste für wissenschaftliche Publikationen durch Blockchain-Technologie und ist der Open-Science-Bewegung verpflichtet.
## Kernfunktionalität: DOI-zu-NFT-Konvertierung
DeSci-Scholar transformiert Digital Object Identifiers (DOIs) wissenschaftlicher Publikationen in Non-Fungible Tokens (NFTs) auf der Blockchain. Diese Kernfunktionalität modernisiert das Konzept digitaler Notariatsdienste für wissenschaftliche Publikationen, ähnlich wie signfirst.com, ein Dienst zur Urheberrechtssicherung von Werken mittels Hashwerten, der 2005 als klassisches Client-Server-Modell entwickelt wurde, jedoch mit den erweiterten Möglichkeiten der Blockchain-Technologie.
### Hauptmerkmale
- **Digitale Authentifizierung**: Kryptografisch gesicherte Verifizierung der Existenz und Urheberschaft wissenschaftlicher Publikationen
- **Zeitstempelung**: Unveränderlicher Nachweis des Veröffentlichungszeitpunkts auf der Blockchain
- **Zitationsnachweis**: Transparente Aufzeichnung und Nachverfolgung von Zitationsbeziehungen
- **Rechtemanagement**: Klare Darstellung und Verwaltung von Urheberrechten und Lizenzen
- **Dezentrale Speicherung**: Robuste, verteilte Speicherung wissenschaftlicher Inhalte über IPFS und BitTorrent
- **Metadaten-Anreicherung**: Integration mit OpenCitations und anderen offenen wissenschaftlichen Diensten
- **Verifizierbare Zertifikate**: Generierung kryptografisch gesicherter Nachweise für wissenschaftliche Publikationen
## Statistik-Dashboard
Das Statistik-Dashboard bietet:
- **Plattform-Übersicht**: Gesamtstatistiken zu Publikationen, Aufrufen und Downloads
- **Trending-Publikationen**: Die aktivsten wissenschaftlichen Arbeiten mit visueller Aktivitätsanzeige
- **Zeitreihen-Analysen**: Nutzungstrends über verschiedene Zeiträume
- **Speicherstatistiken**: Einblicke in die Verteilung und Effizienz der dezentralen Speicherung
## Technologien
- **Frontend**: React mit Chart.js für Datenvisualisierung
- **Backend**: Node.js mit Express
- **Datenspeicherung**: EnhancedStorageService mit IPFS und BitTorrent-Integration
- **Datenbank**: MongoDB für strukturierte Daten und Metadaten
- **Authentifizierung**: JWT-basierte sichere Benutzerauthentifizierung
## Open Science
DeSci-Scholar ist der Open-Science-Bewegung verpflichtet. Wir glauben an:
- **Offenen Zugang zu Wissen**: Wissenschaftliche Publikationen sollten für alle zugänglich sein
- **Offene Daten**: Forschungsdaten sollten frei verfügbar und wiederverwendbar sein
- **Offene Methoden**: Forschungsmethoden sollten transparent und reproduzierbar sein
- **Offene Infrastruktur**: Wissenschaftliche Infrastruktur sollte offen und gemeinschaftlich entwickelt werden
Unsere vollständige Open-Science-Richtlinie finden Sie in der [OPEN_SCIENCE_POLICY.md](OPEN_SCIENCE_POLICY.md)-Datei.
## Integrierte Open-Source/Open-Science-Dienste
DeSci-Scholar integriert mehrere Open-Source- und Open-Science-Dienste:
- **[OpenCitations](https://opencitations.net/)**: Offene bibliografische und Zitationsdaten
- **[ORCID](https://orcid.org/)**: Offene Forscher- und Beitragsidentifikatoren
- **[DataCite](https://datacite.org/)**: Offene DOI-Registrierung und Metadaten
- **[arXiv](https://arxiv.org/)**: Offenes Preprint-Repository
- **[IPFS](https://ipfs.io/)**: Dezentrales, offenes Speichersystem
- **[BitTorrent](https://www.bittorrent.org/)**: Offenes Protokoll für Peer-to-Peer-Datenaustausch
## Installation und Start
1. Repository klonen:
   ```
   git clone https://github.com/username/desci-scholar.git
   cd desci-scholar
   ```
2. Abhängigkeiten installieren:
   ```
   npm install
   ```
3. Umgebungsvariablen konfigurieren:
   Erstellen Sie eine `.env`-Datei im Hauptverzeichnis und fügen Sie folgende Konfigurationen hinzu:
   ```
   PORT=3000
   MONGODB_URI=mongodb://localhost:27017/desci-scholar
   JWT_SECRET=your_jwt_secret
   IPFS_API_URL=http://localhost:5001
   ```
4. Anwendung starten:
   ```
   npm start
   ```
5. Öffnen Sie [http://localhost:3000](http://localhost:3000) in Ihrem Browser.
## API-Endpunkte
Die Anwendung stellt RESTful API-Endpunkte zur Verfügung:
- `/api/publications`: Verwalten wissenschaftlicher Publikationen
- `/api/auth`: Benutzerauthentifizierung
- `/api/stats`: Plattform- und Publikationsstatistiken
- `/api/storage`: Dezentrale Speicherverwaltung
## Beitragen
Wir freuen uns über Beiträge zur Weiterentwicklung von DeSci-Scholar. Bitte lesen Sie unsere Beitragsrichtlinien in der [CONTRIBUTING.md](CONTRIBUTING.md)-Datei und unseren Verhaltenskodex in der [CODE_OF_CONDUCT.md](CODE_OF_CONDUCT.md)-Datei.
## Lizenz
Dieses Projekt ist unter der MIT-Lizenz lizenziert - siehe die [LICENSE](LICENSE)-Datei für Details.