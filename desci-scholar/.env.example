# DeSci-Scholar Umgebungskonfiguration
# Kopieren Sie diese Datei zu .env und passen Sie die Werte entsprechend an
# WICHTIG: <PERSON>ilen Sie niemals Ihre .env-Datei oder private Schl<PERSON><PERSON> öffentlich!

# Server-Konfiguration
PORT=3000
NODE_ENV=development
LOG_LEVEL=info

# Datenbank-Konfiguration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=desci_scholar
DB_USER=postgres
DB_PASSWORD=

# Sicherheit
# Für Produktion: Verwenden Sie einen starken, zufälligen String mit mindestens 32 Zeichen
# Befehl für zufälligen String: node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
JWT_SECRET=deine-sichere-jwt-schlüssel-hier
JWT_EXPIRATION=24h

# Web-Anwendung
# Frontend-URLs sollten mit NEXT_PUBLIC_ beginnen, damit sie clientseitig verfügbar sind
NEXT_PUBLIC_API_URL=http://localhost:3000/api

# Blockchain-Konfiguration
# -------------------------

# Polkadot (Standard-Blockchain)
POLKADOT_PROVIDER=wss://rpc.polkadot.io
POLKADOT_NETWORK=polkadot
POLKADOT_MNEMONIC=
POLKADOT_SEED=
POLKADOT_PRIVATE_KEY=
# Parachains
MOONBEAM_PROVIDER_URL=wss://moonbeam.api.onfinality.io/public-ws
ACALA_PROVIDER_URL=wss://acala-rpc.dwellir.com

# Ethereum
ETHEREUM_PROVIDER=https://mainnet.infura.io/v3/your-api-key
ETHEREUM_NETWORK=mainnet
ETHEREUM_PRIVATE_KEY=
ETHEREUM_GAS_LIMIT=3000000
ETHEREUM_GAS_PRICE=auto

# Polygon
POLYGON_PROVIDER=https://polygon-rpc.com
POLYGON_NETWORK=mainnet
POLYGON_PRIVATE_KEY=
POLYGON_GAS_LIMIT=3000000
POLYGON_GAS_PRICE=auto

# Optimism (Ethereum L2)
OPTIMISM_PROVIDER=https://mainnet.optimism.io
OPTIMISM_NETWORK=mainnet
OPTIMISM_PRIVATE_KEY=
OPTIMISM_GAS_LIMIT=3000000
OPTIMISM_GAS_PRICE=auto

# Solana
SOLANA_ENDPOINT=https://api.mainnet-beta.solana.com
SOLANA_NETWORK=mainnet-beta
SOLANA_PRIVATE_KEY=

# Speicher-Konfiguration
# ----------------------

# IPFS-Konfiguration
IPFS_API_URL=http://localhost:5001
IPFS_GATEWAY=https://ipfs.io/ipfs/
IPFS_API_KEY=
IPFS_API_SECRET=
IPFS_PINNING_SERVICE=local

# BitTorrent-Konfiguration
BT_DOWNLOAD_PATH=./downloads
BT_UPLOAD_SPEED_LIMIT=1000000
BT_DOWNLOAD_SPEED_LIMIT=1000000
BT_DHT_ENABLED=true
BT_TRACKERS=udp://tracker.opentrackr.org:1337,udp://tracker.leechers-paradise.org:6969

# Arweave-Konfiguration
ARWEAVE_HOST=arweave.net
ARWEAVE_PORT=443
ARWEAVE_PROTOCOL=https
ARWEAVE_KEY=

# Filecoin-Konfiguration
FILECOIN_API_KEY=
FILECOIN_ENDPOINT=https://api.web3.storage

# Optionen für dezentralisierte Speicherung
DEFAULT_STORAGE_PROTOCOL=BOTH  # IPFS, BITTORRENT, oder BOTH
STORAGE_SIZE_THRESHOLD=10485760  # 10MB

# API-Schlüssel für externe Dienste
# ---------------------------------

# DataCite-API
DATACITE_API_URL=https://api.test.datacite.org
DATACITE_USERNAME=
DATACITE_PASSWORD=
DATACITE_PREFIX=10.12345
DATACITE_TEST_MODE=true
DATACITE_API_KEY=

# CrossRef
CROSSREF_API_TOKEN=

# Patent Office
PATENT_OFFICE_API_KEY=

# Semantic Scholar
# Beantragen Sie einen Schlüssel unter: https://www.semanticscholar.org/product/api
S2_API_KEY=

# OpenCitations
OPENCITATIONS_API_URL=https://opencitations.net/index/api/v1
OPENCITATIONS_ACCESS_TOKEN=

# KI-Dienste
# ----------

# OpenAI
OPENAI_API_KEY=
OPENAI_MODEL=gpt-4

# Hugging Face
HUGGINGFACE_API_KEY=
HUGGINGFACE_MODEL=gpt2

# Lokale KI-Modelle
AI_MODEL_PATH=./models/research_analysis_model

# Zahlungs-Integrationen
# ----------------------

# Polkadot-Zahlungskonfiguration
# Adresse des Projekt-Wallets für den Empfang von Zahlungen
DESCI_PLATFORM_ADDRESS=

# Stripe-Zahlungskonfiguration (https://stripe.com)
STRIPE_SECRET_KEY=
STRIPE_PUBLISHABLE_KEY=
STRIPE_RETURN_URL=http://localhost:3000/payments/success

# PayPal-Zahlungskonfiguration (https://developer.paypal.com)
PAYPAL_CLIENT_ID=
PAYPAL_SECRET=
PAYPAL_ENVIRONMENT=sandbox

# Cache-Konfiguration
CACHE_MAX_ITEMS=1000
CACHE_TTL=3600000

# ZKP-Parameter für Zero-Knowledge-Proofs
ZKP_PARAMETERS_PATH=./zkp-params
