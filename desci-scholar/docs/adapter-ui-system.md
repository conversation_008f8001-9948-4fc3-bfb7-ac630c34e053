# Adapter-UI-System für DeSci-Scholar

Dieses Dokument beschreibt das UI-System für die Adapter-Konfiguration in DeSci-Scholar, das eine Balance zwischen Benutzerfreundlichkeit und Flexibilität bietet.

## Überblick

Das Adapter-UI-System ermöglicht es Nutzern, die Blockchain-, Speicher- und Wallet-Adapter für ihre DeSci-Scholar-Instanz zu konfigurieren. Es bietet verschiedene Ansichten für unterschiedliche Erfahrungslevel und unterstützt sowohl vordefinierte Profile als auch individuelle Anpassungen.

## Kernkonzepte

1. **Standardeinstellungen mit optionalen Alternativen**: Das System bietet sorgfältig ausgewählte Standardeinstellungen, erlaubt aber auch individuelle Anpassungen.

2. **Gestaffeltes UI**: Verschiedene Ansichten für Anfänger, fortgeschrittene Nutzer und Experten.

3. **Vordefinierte Profile**: Optimierte Konfigurationen für verschiedene Anwendungsfälle.

4. **Konfigurationsassistent**: Ein geführter Prozess zur Auswahl der optimalen Konfiguration.

5. **Transparente Standardeinstellungen**: Klare Kommunikation der Standardeinstellungen und ihrer Begründungen.

## Komponenten

### AdapterConfiguration

Die Hauptkomponente, die alle anderen Komponenten orchestriert und den Gesamtzustand der Adapter-Konfiguration verwaltet.

```jsx
<AdapterConfiguration
  initialAdapters={initialAdapters}
  onConfigurationChange={handleConfigChange}
  experienceLevel="intermediate"
/>
```

### AdapterSelector

Komponente zur Auswahl eines Adapters für eine bestimmte Kategorie (Blockchain, Speicher, Wallet).

```jsx
<AdapterSelector
  category="blockchain"
  selectedAdapter="polkadot"
  onAdapterChange={handleAdapterChange}
  experienceLevel="intermediate"
/>
```

### AdapterCard

Karte zur Darstellung eines einzelnen Adapters mit grundlegenden Informationen.

```jsx
<AdapterCard
  adapter={adapter}
  adapterKey="polkadot"
  isSelected={true}
  isDefault={true}
  onClick={handleClick}
  onInfoClick={handleInfoClick}
/>
```

### AdapterDetails

Komponente zur Anzeige detaillierter Informationen über einen Adapter.

```jsx
<AdapterDetails
  adapter={adapter}
  expanded={false}
/>
```

### AdapterProfileSelector

Komponente zur Auswahl vordefinierter Adapter-Profile.

```jsx
<AdapterProfileSelector
  selectedProfile="default"
  onProfileChange={handleProfileChange}
  onAdaptersChange={handleAdaptersChange}
/>
```

### GuidedSetup

Konfigurationsassistent, der Nutzer durch den Prozess der Adapter-Auswahl führt.

```jsx
<GuidedSetup
  onComplete={handleSetupComplete}
  onCancel={handleSetupCancel}
/>
```

## Erfahrungslevel

Das System unterstützt drei Erfahrungslevel:

1. **Anfänger**: Vereinfachte Oberfläche mit Standardoptionen und Konfigurationsassistent.
2. **Fortgeschritten**: Zugriff auf alle Adapter-Optionen und vordefinierte Profile.
3. **Experte**: Vollständiger Zugriff auf alle Konfigurationsmöglichkeiten.

## Vordefinierte Profile

Das System bietet vordefinierte Profile für verschiedene Anwendungsfälle:

1. **Standard**: Ausgewogene Konfiguration für die meisten Nutzer (Polkadot, IPFS, Polkadot.js).
2. **Kosteneffizient**: Optimiert für minimale Kosten (Polygon, BitTorrent, MetaMask).
3. **Hohe Performance**: Maximale Geschwindigkeit und Durchsatz (Solana, IPFS, Phantom).
4. **Maximale Dezentralisierung**: Höchste Dezentralisierung und Zensurresistenz (Ethereum, Arweave, MetaMask).
5. **Langzeitarchivierung**: Optimiert für dauerhafte Speicherung (Ethereum, Filecoin, MetaMask).
6. **Chain-übergreifende Kompatibilität**: Maximale Interoperabilität (Polkadot, IPFS, WalletConnect).

## Konfigurationsassistent

Der Konfigurationsassistent führt Nutzer durch einen schrittweisen Prozess zur Auswahl der optimalen Konfiguration:

1. **Erfahrungslevel**: Wie vertraut ist der Nutzer mit Blockchain-Technologie?
2. **Anwendungsfall**: Was ist der primäre Anwendungsfall (Publikation, Archivierung, Zusammenarbeit, Monetarisierung)?
3. **Datengröße**: Wie groß sind die typischen Forschungsdaten?
4. **Kostenempfindlichkeit**: Wie wichtig sind niedrige Transaktions- und Speicherkosten?

Basierend auf den Antworten empfiehlt der Assistent ein passendes Profil.

## Implementierungsdetails

### Konfigurationsdatei

Die Konfiguration für das UI-System ist in `src/config/ui-config.js` definiert und enthält:

- Beschreibungen und Begründungen für Standardeinstellungen
- Informationen zu allen verfügbaren Adaptern
- Vordefinierte Profile
- Konfiguration für den Konfigurationsassistenten

### Zustandsverwaltung

Die Komponenten verwenden React-Hooks für die Zustandsverwaltung:

- `useState` für lokalen Zustand
- `useEffect` für Seiteneffekte
- `useContext` könnte für globalen Zustand verwendet werden (nicht implementiert)

### Styling

Das Styling verwendet CSS-Dateien für jede Komponente:

- `AdapterSelector.css`
- `AdapterCard.css`
- `AdapterDetails.css`
- `AdapterProfileSelector.css`
- `GuidedSetup.css`
- `AdapterConfiguration.css`

## Verwendung

### Einbindung in eine Seite

```jsx
import React, { useState } from 'react';
import AdapterConfiguration from '../components/adapters/AdapterConfiguration';

const SettingsPage = () => {
  const [adapterConfig, setAdapterConfig] = useState({
    blockchain: 'polkadot',
    storage: 'ipfs',
    wallet: 'polkadot'
  });
  
  const handleConfigChange = (newConfig) => {
    setAdapterConfig(newConfig);
    // Speichern der Konfiguration
  };
  
  return (
    <div className="settings-page">
      <h1>Einstellungen</h1>
      <AdapterConfiguration
        initialAdapters={adapterConfig}
        onConfigurationChange={handleConfigChange}
        experienceLevel="intermediate"
      />
    </div>
  );
};

export default SettingsPage;
```

### Zugriff auf die Adapter-Konfiguration

```jsx
import { getAdapterRegistry } from '../core/adapter/AdapterRegistry';

// Adapter-Registry abrufen
const registry = getAdapterRegistry();

// Blockchain-Adapter abrufen (verwendet Standard-Adapter, wenn keiner angegeben)
const blockchainAdapter = registry.getAdapter('blockchain');

// Spezifischen Adapter abrufen
const ethereumAdapter = registry.getAdapter('blockchain', 'ethereum');

// Adapter verwenden
await ethereumAdapter.mintNFT(contractAddress, recipient, tokenURI);
```

## Best Practices

1. **Transparente Kommunikation**: Kommunizieren Sie klar, welche Adapter standardmäßig verwendet werden und warum.

2. **Sichtbare Anpassungsoptionen**: Machen Sie die Möglichkeit zur Anpassung leicht zugänglich.

3. **Kontextbezogene Hilfe**: Bieten Sie Erklärungen und Hilfestellungen für jede Option.

4. **Konsistente Terminologie**: Verwenden Sie konsistente Begriffe für Adapter-Kategorien und -Funktionen.

5. **Responsive Design**: Stellen Sie sicher, dass das UI auf verschiedenen Geräten gut funktioniert.

## Erweiterung des Systems

### Hinzufügen eines neuen Adapters

1. Implementieren Sie den Adapter in `src/services/[category]/[AdapterName].js`
2. Aktualisieren Sie die Adapter-Konfiguration in `src/config/adapters.js`
3. Fügen Sie UI-Informationen in `src/config/ui-config.js` hinzu
4. Erstellen Sie ein Icon für den Adapter in `public/assets/icons/`

### Hinzufügen eines neuen Profils

Fügen Sie ein neues Profil in `src/config/ui-config.js` hinzu:

```javascript
export const adapterProfiles = {
  // Bestehende Profile
  
  // Neues Profil
  newProfile: {
    name: 'Neues Profil',
    description: 'Beschreibung des neuen Profils',
    adapters: {
      blockchain: 'adapter1',
      storage: 'adapter2',
      wallet: 'adapter3'
    }
  }
};
```
