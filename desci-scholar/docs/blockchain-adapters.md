# Blockchain-Adapt<PERSON> für DeSci-Scholar

DeSci-Scholar unterstützt mehrere Blockchain-Netzwerke durch ein modulares Adapter-System. Dieses Dokument beschreibt die verfügbaren Blockchain-Adapter, ihre Funktionen und Konfigurationsoptionen.

## Übersicht

Das Adapter-System ermöglicht die nahtlose Integration verschiedener Blockchain-Netzwerke in DeSci-Scholar. Jeder Adapter implementiert eine einheitliche Schnittstelle, die es der Anwendung ermöglicht, mit dem jeweiligen Blockchain-Netzwerk zu interagieren.

### Verfügbare Blockchain-Adapter

1. **Polkadot** (Standard) - Für Interoperabilität und Skalierbarkeit
2. **Ethereum** - Für Kompatibilität mit dem Ethereum-Ökosystem
3. **Polygon** - Für kostengünstige Transaktionen auf einem EVM-kompatiblen Layer-2
4. **Optimism** - Für schnelle Transaktionen auf einem Ethereum Layer-2
5. **Solana** - Für hohen Durchsatz und niedrige Transaktionskosten

## Polkadot-Adapter

Der Polkadot-Adapter ist der Standard-Adapter für DeSci-Scholar und bietet Zugang zum Polkadot-Ökosystem, einschließlich seiner Parachains.

### Funktionen

- Verbindung zum Polkadot-Relay-Chain und Parachains
- Unterstützung für Substrate-basierte Smart Contracts
- Integration mit Polkadot.js
- Unterstützung für XCMP (Cross-Chain Message Passing)
- Interoperabilität mit anderen Blockchain-Netzwerken

### Konfiguration

```env
# Polkadot-Konfiguration
POLKADOT_PROVIDER=wss://rpc.polkadot.io
POLKADOT_NETWORK=polkadot
POLKADOT_MNEMONIC=
POLKADOT_SEED=
POLKADOT_PRIVATE_KEY=
# Parachains
MOONBEAM_PROVIDER_URL=wss://moonbeam.api.onfinality.io/public-ws
ACALA_PROVIDER_URL=wss://acala-rpc.dwellir.com
```

## Ethereum-Adapter

Der Ethereum-Adapter ermöglicht die Interaktion mit dem Ethereum-Netzwerk und Smart Contracts.

### Funktionen

- Verbindung zum Ethereum-Netzwerk (Mainnet, Testnets)
- Unterstützung für ERC-Standards (ERC20, ERC721, ERC1155)
- Smart Contract-Interaktion
- Transaktionsverwaltung

### Konfiguration

```env
# Ethereum-Konfiguration
ETHEREUM_PROVIDER=https://mainnet.infura.io/v3/your-api-key
ETHEREUM_NETWORK=mainnet
ETHEREUM_PRIVATE_KEY=
ETHEREUM_GAS_LIMIT=3000000
ETHEREUM_GAS_PRICE=auto
```

## Polygon-Adapter

Der Polygon-Adapter bietet Zugang zum Polygon-Netzwerk, einem EVM-kompatiblen Layer-2 für Ethereum mit hohem Durchsatz und niedrigen Gebühren.

### Funktionen

- Verbindung zum Polygon-Netzwerk (Mainnet, Mumbai Testnet)
- Unterstützung für ERC-Standards (ERC20, ERC721, ERC1155)
- Smart Contract-Interaktion
- Transaktionsverwaltung mit niedrigen Gebühren

### Konfiguration

```env
# Polygon-Konfiguration
POLYGON_PROVIDER=https://polygon-rpc.com
POLYGON_NETWORK=mainnet
POLYGON_PRIVATE_KEY=
POLYGON_GAS_LIMIT=3000000
POLYGON_GAS_PRICE=auto
```

## Optimism-Adapter

Der Optimism-Adapter bietet Zugang zum Optimism-Netzwerk, einem Ethereum Layer-2 mit schnellen Transaktionen und niedrigen Gebühren.

### Funktionen

- Verbindung zum Optimism-Netzwerk (Mainnet, Goerli Testnet)
- Unterstützung für ERC-Standards (ERC20, ERC721, ERC1155)
- Smart Contract-Interaktion
- Schnelle Transaktionen mit niedrigen Gebühren

### Konfiguration

```env
# Optimism-Konfiguration
OPTIMISM_PROVIDER=https://mainnet.optimism.io
OPTIMISM_NETWORK=mainnet
OPTIMISM_PRIVATE_KEY=
OPTIMISM_GAS_LIMIT=3000000
OPTIMISM_GAS_PRICE=auto
```

## Solana-Adapter

Der Solana-Adapter ermöglicht die Interaktion mit dem Solana-Netzwerk, das für hohen Durchsatz und niedrige Transaktionskosten bekannt ist.

### Funktionen

- Verbindung zum Solana-Netzwerk (Mainnet, Devnet, Testnet)
- Unterstützung für Solana-Programme
- NFT-Prägung und -Verwaltung mit Metaplex
- Transaktionsverwaltung

### Konfiguration

```env
# Solana-Konfiguration
SOLANA_ENDPOINT=https://api.mainnet-beta.solana.com
SOLANA_NETWORK=mainnet-beta
SOLANA_PRIVATE_KEY=
```

## Verwendung der Adapter

### Auswahl eines Adapters

Der Standard-Adapter (Polkadot) wird automatisch verwendet, wenn kein anderer Adapter explizit angegeben wird. Um einen bestimmten Adapter zu verwenden, können Sie den Adapter-Namen in der Konfiguration oder bei API-Aufrufen angeben.

```javascript
// Verwendung des Standard-Adapters (Polkadot)
const blockchainAdapter = adapterRegistry.getAdapter('blockchain');

// Verwendung eines spezifischen Adapters
const ethereumAdapter = adapterRegistry.getAdapter('blockchain', 'ethereum');
const polygonAdapter = adapterRegistry.getAdapter('blockchain', 'polygon');
const optimismAdapter = adapterRegistry.getAdapter('blockchain', 'optimism');
const solanaAdapter = adapterRegistry.getAdapter('blockchain', 'solana');
```

### Beispiel: NFT-Prägung auf verschiedenen Blockchains

```javascript
// NFT auf Polkadot prägen
const polkadotAdapter = adapterRegistry.getAdapter('blockchain', 'polkadot');
await polkadotAdapter.mintNFT(contractAddress, recipient, tokenURI);

// NFT auf Ethereum prägen
const ethereumAdapter = adapterRegistry.getAdapter('blockchain', 'ethereum');
await ethereumAdapter.mintNFT(contractAddress, recipient, tokenURI);

// NFT auf Polygon prägen
const polygonAdapter = adapterRegistry.getAdapter('blockchain', 'polygon');
await polygonAdapter.mintNFT(contractAddress, recipient, tokenURI);

// NFT auf Optimism prägen
const optimismAdapter = adapterRegistry.getAdapter('blockchain', 'optimism');
await optimismAdapter.mintNFT(contractAddress, recipient, tokenURI);

// NFT auf Solana prägen
const solanaAdapter = adapterRegistry.getAdapter('blockchain', 'solana');
await solanaAdapter.mintNFT(name, description, imageUrl);
```

## Erweiterung des Adapter-Systems

Das Adapter-System ist erweiterbar und ermöglicht die einfache Integration weiterer Blockchain-Netzwerke. Um einen neuen Adapter hinzuzufügen:

1. Erstellen Sie eine neue Adapter-Klasse, die von `BaseAdapter` erbt
2. Implementieren Sie die erforderlichen Methoden
3. Registrieren Sie den Adapter in der Konfiguration

Beispiel für einen neuen Adapter:

```javascript
import { BaseAdapter } from '../../core/adapter/BaseAdapter.js';

export class NewBlockchainAdapter extends BaseAdapter {
  constructor(options = {}) {
    super(options);
    // Adapter-spezifische Initialisierung
  }
  
  async initialize() {
    // Initialisierungslogik
    return true;
  }
  
  getCapabilities() {
    return ['capability1', 'capability2'];
  }
  
  // Weitere Methoden
}
```

Registrierung in der Konfiguration:

```javascript
// In config/adapters.js
export const blockchainAdapterConfig = {
  // Bestehende Adapter
  
  // Neuer Adapter
  newBlockchain: {
    option1: 'value1',
    option2: 'value2'
  }
};
```
