# DOI-Integration in DeSci Scholar

Diese Dokumentation beschreibt die Integration von Digital Object Identifiers (DOIs) in DeSci Scholar. Die Integration ermöglicht die Erstellung, Aktualisierung und Abfrage von DOIs für wissenschaftliche Publikationen und Datensätze über die Dienste DataCite und Crossref.

## Überblick

DOIs (Digital Object Identifiers) sind persistente Identifikatoren für digitale Objekte, die in wissenschaftlichen Publikationen und Datensätzen weit verbreitet sind. DeSci Scholar integriert zwei der wichtigsten DOI-Registrierungsagenturen:

1. **DataCite** - Spezialisiert auf Forschungsdaten und andere wissenschaftliche Ressourcen
2. **Crossref** - Spezialisiert auf wissenschaftliche Publikationen

Die Integration bietet folgende Funktionen:

- Erstellung von DOIs für wissenschaftliche Publikationen
- Erstellung von DOIs für Forschungsdaten
- Aktualisierung von DOI-Metadaten
- Abfrage von DOI-Metadaten
- Suche nach DOIs

## Konfiguration

Die DOI-Integration erfordert Zugangsdaten für DataCite und/oder Crossref. Diese können in der Umgebungskonfiguration oder direkt bei der Initialisierung des DOIControllers angegeben werden.

### Umgebungsvariablen
