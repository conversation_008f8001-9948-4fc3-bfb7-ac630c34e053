# NFT-DOI Bridge API Dokumentation

## Überblick

Die NFT-DOI Bridge API ermöglicht die Konvertierung von wissenschaftlichen DOIs in blockchain-verifizierte NFTs mit eingebetteten DataCite- und Crossref-Metadaten. Diese API ist das Herzstück des DeSci-Scholar Systems.

## Basis-URL

```
https://api.desci-scholar.org
```

## Authentifizierung

Alle API-Aufrufe erfordern einen gültigen API-Schlüssel im Header:

```http
Authorization: Bearer YOUR_API_KEY
Content-Type: application/json
```

## Core Bridge Endpoints

### 1. DOI-zu-NFT Bridge

**Endpoint:** `POST /api/bridge/mint`

Konvertiert einen DOI in einen NFT mit vollständiger DataCite/Crossref-Integration.

**Request Body:**
```json
{
  "doi": "10.1000/example.1",
  "includeDataCite": true,
  "includeCrossref": true,
  "includeOrcid": true,
  "blockchain": "polkadot",
  "enableZeroKnowledgeProofs": false,
  "recipient": "0x1234567890abcdef1234567890abcdef12345678",
  "royaltyPercentage": 5
}
```

**Response:**
```json
{
  "success": true,
  "bridgeType": "NFT-DOI Bridge v2.0",
  "doi": "10.1000/example.1",
  "nft": {
    "tokenId": 123456,
    "contractAddress": "0xabcdef1234567890abcdef1234567890abcdef12",
    "transactionHash": "0x9876543210fedcba9876543210fedcba9876543210fedcba9876543210fedcba",
    "blockchain": "polkadot",
    "status": "minted",
    "metadataUri": "ipfs://QmXxXxXxXxXxXxXxXxXxXxXxXxXxXxXxXxXxXxXxXxXxXx"
  },
  "metadata": {
    "name": "DOI-NFT Bridge: Blockchain-Based Scientific Publishing",
    "description": "NFT-DOI Bridge representation with integrated metadata",
    "scientific_metadata": {
      "doi": "10.1000/example.1",
      "dataCite": { /* DataCite metadata */ },
      "crossref": { /* Crossref metadata */ },
      "orcid": { /* ORCID enrichments */ }
    }
  },
  "integrations": {
    "dataCite": { "enabled": true, "status": "resolved" },
    "crossref": { "enabled": true, "status": "resolved" },
    "orcid": { "enabled": true, "status": "enriched" }
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### 2. DataCite DOI-Auflösung

**Endpoint:** `GET /api/datacite/{doi}`

Löst einen DOI über die DataCite API auf und ruft standardisierte Metadaten ab.

**Response:**
```json
{
  "service": "DataCite",
  "doi": "10.1000/example.1",
  "status": "resolved",
  "metadata": {
    "title": "Blockchain-Based Scientific Publishing",
    "creators": [
      { "name": "Smith, Jane", "orcid": "0000-0000-0000-0001" }
    ],
    "publisher": "DataCite Research Institute",
    "publicationYear": 2024,
    "resourceType": "Journal Article",
    "registrationInfo": {
      "registrationAgency": "DataCite",
      "registeredDate": "2024-01-15T10:30:00Z"
    }
  }
}
```

### 3. Crossref Zitationsnetzwerk

**Endpoint:** `GET /api/crossref/{doi}`

Ruft Crossref-Metadaten und Event Data für umfassendes Impact-Tracking ab.

**Response:**
```json
{
  "service": "Crossref",
  "doi": "10.1000/example.1",
  "status": "resolved",
  "metadata": {
    "title": "Blockchain-Based Scientific Publishing",
    "authors": [
      { "given": "Jane", "family": "Smith", "orcid": "0000-0000-0000-0001" }
    ],
    "journal": "Journal of Decentralized Science",
    "citations": {
      "citedByCount": 42,
      "referencesCount": 67,
      "citationTrend": "increasing"
    },
    "eventData": {
      "downloads": 1337,
      "mentions": 25,
      "socialShares": 15
    }
  }
}
```

### 4. Zitations-Tracking

**Endpoint:** `GET /api/citations/{doi}`

Überwacht Zitationsnetzwerke und Forschungsimpact durch integrierte Services.

**Response:**
```json
{
  "doi": "10.1000/example.1",
  "citationTracking": {
    "totalCitations": 42,
    "recentCitations": 8,
    "citationTrend": "increasing",
    "topCitingSources": [
      "Nature Blockchain",
      "IEEE Transactions on Decentralized Systems"
    ]
  },
  "impactMetrics": {
    "altmetricScore": 156,
    "fieldCitationRatio": 2.3,
    "relativeImpact": "high"
  },
  "crossrefEvents": {
    "downloads": 1337,
    "views": 5420,
    "mentions": 25
  }
}
```

## Bridge Management Endpoints

### 5. Bridge-Status abfragen

**Endpoint:** `GET /api/bridge/status/{doi}`

Ruft den aktuellen Status einer DOI-NFT-Bridge ab.

**Response:**
```json
{
  "doi": "10.1000/example.1",
  "bridge": {
    "exists": true,
    "nftTokenId": 123456,
    "blockchain": "polkadot",
    "status": "active",
    "lastSync": "2024-01-15T10:30:00Z"
  },
  "synchronization": {
    "dataCiteLastUpdate": "2024-01-15T09:00:00Z",
    "crossrefLastUpdate": "2024-01-15T09:15:00Z",
    "nextSyncScheduled": "2024-01-16T10:30:00Z"
  }
}
```

### 6. Bridge-Synchronisation

**Endpoint:** `POST /api/bridge/sync/{doi}`

Synchronisiert NFT-Metadaten mit aktuellen DataCite/Crossref-Daten.

**Response:**
```json
{
  "doi": "10.1000/example.1",
  "syncResult": {
    "success": true,
    "updatedFields": ["citations", "eventData"],
    "dataCiteChanges": 2,
    "crossrefChanges": 5,
    "nftMetadataUpdated": true,
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

## Fehlerbehandlung

Alle API-Endpunkte verwenden standardisierte HTTP-Statuscodes:

- `200 OK`: Erfolgreiche Anfrage
- `400 Bad Request`: Ungültige Anfrageparameter
- `401 Unauthorized`: Fehlende oder ungültige Authentifizierung
- `404 Not Found`: DOI oder Ressource nicht gefunden
- `429 Too Many Requests`: Rate-Limit überschritten
- `500 Internal Server Error`: Serverfehler

**Fehler-Response-Format:**
```json
{
  "error": "Invalid DOI format",
  "code": "INVALID_DOI",
  "message": "The provided DOI does not match the expected format",
  "details": {
    "doi": "invalid-doi",
    "expectedFormat": "10.xxxx/xxxxx"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## Rate Limiting

- **Standard-Benutzer**: 100 Anfragen pro Minute
- **Premium-Benutzer**: 1000 Anfragen pro Minute
- **Enterprise**: Unbegrenzt

## Webhooks

Das System unterstützt Webhooks für Echtzeit-Benachrichtigungen:

- `bridge.created`: Neue DOI-NFT-Bridge erstellt
- `bridge.updated`: Bridge-Metadaten aktualisiert
- `citation.added`: Neue Zitation erkannt
- `impact.threshold`: Impact-Schwellenwert erreicht

## SDK und Libraries

Offizielle SDKs verfügbar für:
- JavaScript/Node.js
- Python
- Rust
- Go

## Support

- **Dokumentation**: https://docs.desci-scholar.org
- **GitHub**: https://github.com/desci-scholar/nft-doi-bridge
- **Discord**: https://discord.gg/desci-scholar
- **Email**: <EMAIL>
