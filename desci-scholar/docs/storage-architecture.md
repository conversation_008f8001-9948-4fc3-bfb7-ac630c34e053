# DeSci-Scholar: Dezentrale Speicherarchitektur

## Übersicht

Die DeSci-Scholar-Plattform nutzt eine hybride dezentrale Speicherlösung, die zwei führende Technologien kombiniert:

1. **IPFS (InterPlanetary File System)** für Metadaten und kleine Dateien
2. **BitTorrent** für große Dateien und Datensätze

Diese Kombination optimiert die Speicherung wissenschaftlicher Forschung, indem sie die Stärken beider Technologien nutzt.

## Architektur

![Speicherarchitektur](./images/storage-architecture.png)

### IPFS für Metadaten

IPFS wird primär für die Speicherung von Metadaten und kleinen Dateien verwendet. Metadaten sind entscheidend für die Auffindbarkeit, Zitierbarkeit und Verwaltbarkeit wissenschaftlicher Inhalte.

**Vorteile der IPFS-Metadatenspeicherung:**

- **Content-Addressing**: IPFS verwendet kryptografische Hashes als Identifikatoren, was Inhaltsintegrität garantiert
- **Dezentralisierung**: Keine zentrale Autorität kontrolliert die Daten
- **Dauerhaftigkeit**: "Pinning" garantiert die Verfügbarkeit von Metadaten, selbst wenn der ursprüngliche Ersteller offline ist
- **Verifizierbarkeit**: Jeder kann die Authentizität der Metadaten überprüfen, da die CID (Content Identifier) vom Inhalt selbst abgeleitet wird

### BitTorrent für große Dateien

BitTorrent wird für die Verteilung großer Dateien verwendet, wie umfangreiche Forschungsdatensätze, hochauflösende Bilder, Videos oder umfangreiche Publikationen.

**Vorteile der BitTorrent-Dateiverteilung:**

- **Skalierbarkeit**: Effiziente Verteilung großer Dateien durch Peer-to-Peer-Sharing
- **Bandbreiteneffizienz**: Verringert die Last auf einzelnen Servern durch verteilte Datenübertragung
- **Robustheit**: Dateien bleiben verfügbar, solange mindestens ein Seeder online ist
- **Wiederaufnehmbarkeit**: Downloads können unterbrochen und später fortgesetzt werden

## Integrierter Speichermanager

Der `StorageManager` in DeSci-Scholar integriert beide Technologien nahtlos und bietet eine einheitliche Schnittstelle für Speicheroperationen:

```javascript
const storageManager = new StorageManager();
await storageManager.init();

// Speichern einer Publikation (entscheidet automatisch zwischen IPFS und BitTorrent)
const result = await storageManager.storePublication(metadata, filePath);

// Speichern eines Datensatzes (verwendet immer BitTorrent)
const datasetResult = await storageManager.storeDataset(datasetMetadata, datasetPath);

// Abrufen von Inhalten unabhängig von der Speichermethode
const retrievedPath = await storageManager.retrieveContent(contentReference, outputPath);
```

## Entscheidungskriterien

Die Entscheidung zwischen IPFS und BitTorrent basiert auf diesen Faktoren:

1. **Dateigröße**: Dateien über einem konfigurierbaren Schwellenwert (standardmäßig 10MB) werden über BitTorrent verteilt
2. **Datentyp**: Metadaten werden immer in IPFS gespeichert, unabhängig von ihrer Größe
3. **Zugriffsfrequenz**: Häufig abgerufene kleine Dateien werden in IPFS gespeichert, da dies schnelleren Zugriff ermöglicht

## Implementierungsdetails

### Metadatenstruktur in IPFS

Alle in IPFS gespeicherten Metadaten folgen einer standardisierten Struktur:

```json
{
  "title": "Titel der Publikation oder des Datensatzes",
  "authors": ["Autor 1", "Autor 2"],
  "abstract": "Zusammenfassung der Forschung",
  "doi": "10.xxxx/xxxxx",
  "date": "YYYY-MM-DD",
  "schemaVersion": "1.0",
  "timestamp": "ISO-Datums-/Zeitstempel",
  "type": "scientific-publication",
  "content": {
    "type": "ipfs", // oder "bittorrent"
    "cid": "Qm...", // wenn IPFS
    "magnetURI": "magnet:?xt=urn:btih:...", // wenn BitTorrent
    "infoHash": "...", // wenn BitTorrent
    "size": 1234567 // Größe in Bytes
  }
}
```

### BitTorrent-Integration

Für BitTorrent-basierte Inhalte wird ein Torrent mit eingebetteten Metadaten erstellt:

1. Die vollständigen Metadaten werden in IPFS gespeichert
2. Ein Magnetlink wird generiert und mit den Metadaten verknüpft
3. Der Torrent selbst enthält eine Kopie der grundlegenden Metadaten für bessere Auffindbarkeit

## Systemanforderungen

- **IPFS-Daemon**: Lokaler oder Remote-IPFS-Knoten für die Interaktion mit dem IPFS-Netzwerk
- **WebTorrent-Unterstützung**: Für die Verarbeitung von BitTorrent-Streams in Webanwendungen
- **Speicherkapazität**: Für das Caching und Pinning von häufig verwendeten Inhalten

## Künftige Verbesserungen

1. **Filecoin-Integration**: Für langfristige Speichergarantien
2. **Dynamische Schwellenwertanpassung**: Basierend auf Netzwerk- und Speichermetriken
3. **Multi-Tier-Pinning**: Priorisierung von Inhalten basierend auf Relevanz und Nutzung
4. **Öffentliche Gateway-Integration**: Für bessere Zugänglichkeit ohne spezielle Software

## Fazit

Die hybride Speicherlösung von DeSci-Scholar bietet das Beste aus beiden Welten:

- IPFS sorgt für unveränderliche, verifizierbare Metadaten
- BitTorrent ermöglicht die effiziente Verteilung großer Forschungsdatensätze

Diese Architektur unterstützt die grundlegenden Ziele der dezentralen Wissenschaft: Transparenz, Reproduzierbarkeit und offenen Zugang zu Wissen.
