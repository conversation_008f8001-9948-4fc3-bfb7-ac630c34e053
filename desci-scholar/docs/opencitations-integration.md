# OpenCitations-Integration in DeSci-Scholar

Diese Dokumentation beschreibt die Integration von [OpenCitations](https://opencitations.net/) in das DeSci-Scholar-Projekt.

## Was ist OpenCitations?

OpenCitations ist eine unabhängige Infrastrukturorganisation, die offene bibliografische und Zitationsdaten bereitstellt. Sie bietet:

- Strukturierte, offene Zitationsdaten
- REST-APIs für den Zugriff auf Zitationsinformationen
- Eindeutige Identifikatoren für Zitationen (OCIs - Open Citation Identifiers)
- Semantische Web-Technologien für verknüpfte Daten

## Vorteile der Integration

Die Integration von OpenCitations in DeSci-Scholar bietet folgende Vorteile:

1. **Für die Zitationsanalyse**:
   - Umfassendere Zitationsdaten aus einer offenen, vertrauenswürdigen Quelle
   - Bessere Metriken für die Einflussberechnung
   - Genauere Zitationsnetzwerke

2. **Für die DOI-zu-NFT-Konvertierung**:
   - Anreicherung der NFT-Metadaten mit verifizierten Zitationsdaten
   - Eindeutige Identifikatoren (OCIs) für Zitationen
   - Verbesserte Vertrauenswürdigkeit der NFTs durch Verknüpfung mit einer anerkannten Quelle

3. **Allgemeine Vorteile**:
   - Unterstützung der Open-Science-Bewegung
   - Verbesserte Interoperabilität mit anderen wissenschaftlichen Systemen
   - Zugang zu strukturierten, offenen Zitationsdaten

## Konfiguration

Um die OpenCitations-Integration zu nutzen, müssen Sie folgende Umgebungsvariablen in Ihrer `.env`-Datei konfigurieren:

```
OPENCITATIONS_API_URL=https://opencitations.net/index/api/v1
OPENCITATIONS_ACCESS_TOKEN=your_access_token_here
```

Sie können einen API-Schlüssel von OpenCitations erhalten, indem Sie sich auf deren Website registrieren.

## Verwendung

### Zitationsanalyse

Die OpenCitations-Integration wird automatisch in der Zitationsanalyse verwendet. Der `CitationAnalysisAgent` ruft Daten von OpenCitations ab und kombiniert sie mit lokalen Daten, um umfassendere Zitationsmetriken zu erstellen.

```javascript
// Beispiel für die Verwendung der Zitationsanalyse mit OpenCitations
const agent = new CitationAnalysisAgent(options);
const result = await agent.analyzeCitations('10.1234/example-doi');

console.log(`Zitationen laut OpenCitations: ${result.openCitationsData.citationCount}`);
```

### NFT-Erstellung

Bei der Erstellung von NFTs für DOIs werden automatisch OpenCitations-Daten in die Metadaten aufgenommen, wenn verfügbar:

```javascript
// Beispiel für die Erstellung eines NFT mit OpenCitations-Daten
const agent = new CitationAnalysisAgent(options);
const result = await agent.createNFTForDOI('10.1234/example-doi');

console.log(`NFT erstellt: ${result.nftId}`);
console.log(`OpenCitations verifiziert: ${result.openCitationsVerified}`);
```

### Zitationsnetzwerke

Die Zitationsnetzwerke können mit OpenCitations-Daten angereichert werden:

```javascript
// Beispiel für die Erstellung eines Zitationsnetzwerks mit OpenCitations-Daten
const agent = new CitationAnalysisAgent(options);
const result = await agent.createCitationNetwork('10.1234/example-doi', {
  depth: 2,
  maxNodes: 100,
  useOpenCitations: true
});

console.log(`Netzwerk erstellt mit ${result.network.nodes.length} Knoten`);
```

## Datenbankschema

Die OpenCitations-Integration fügt folgende Spalten zu den Datenbanktabellen hinzu:

- `citation_relationships.oci`: Speichert den Open Citation Identifier
- `publication_metrics.opencitations_verified`: Gibt an, ob die Metriken durch OpenCitations verifiziert wurden
- `doi_nft_mapping.opencitations_verified`: Gibt an, ob das NFT mit OpenCitations-Daten angereichert wurde
- `citation_nft_links.oci`: Speichert den Open Citation Identifier für die Verknüpfung

Außerdem wird eine neue Tabelle `publication_external_data` erstellt, um externe Daten zu Publikationen zu speichern.

## Technische Details

Die Integration verwendet den `OpenCitationsService`, der folgende Hauptfunktionen bietet:

- `getCitations(doi)`: Holt eingehende Zitationen für eine DOI
- `getReferences(doi)`: Holt ausgehende Zitationen (Referenzen) für eine DOI
- `getCitationCount(doi)`: Holt die Anzahl der Zitationen für eine DOI
- `getMetadata(doi)`: Holt Metadaten für eine DOI
- `getAllDataForDOI(doi)`: Holt alle verfügbaren Daten für eine DOI
- `updateCitationDataForDOI(doi, databaseService)`: Aktualisiert die Zitationsdaten in der Datenbank

## Fehlerbehebung

Wenn Sie Probleme mit der OpenCitations-Integration haben, prüfen Sie Folgendes:

1. Stellen Sie sicher, dass Ihre API-Schlüssel korrekt konfiguriert sind
2. Prüfen Sie, ob die OpenCitations-API erreichbar ist
3. Überprüfen Sie die Logs auf Fehler im Zusammenhang mit OpenCitations
4. Stellen Sie sicher, dass die Datenbankmigration ausgeführt wurde

## Weitere Ressourcen

- [OpenCitations Website](https://opencitations.net/)
- [OpenCitations API-Dokumentation](https://opencitations.net/index/api/v1)
- [Open Citation Identifier (OCI) Spezifikation](https://opencitations.net/oci)# OpenCitations-Integration in DeSci-Scholar

Diese Dokumentation beschreibt die Integration von [OpenCitations](https://opencitations.net/) in das DeSci-Scholar-Projekt.

## Was ist OpenCitations?

OpenCitations ist eine unabhängige Infrastrukturorganisation, die offene bibliografische und Zitationsdaten bereitstellt. Sie bietet:

- Strukturierte, offene Zitationsdaten
- REST-APIs für den Zugriff auf Zitationsinformationen
- Eindeutige Identifikatoren für Zitationen (OCIs - Open Citation Identifiers)
- Semantische Web-Technologien für verknüpfte Daten

## Vorteile der Integration

Die Integration von OpenCitations in DeSci-Scholar bietet folgende Vorteile:

1. **Für die Zitationsanalyse**:
   - Umfassendere Zitationsdaten aus einer offenen, vertrauenswürdigen Quelle
   - Bessere Metriken für die Einflussberechnung
   - Genauere Zitationsnetzwerke

2. **Für die DOI-zu-NFT-Konvertierung**:
   - Anreicherung der NFT-Metadaten mit verifizierten Zitationsdaten
   - Eindeutige Identifikatoren (OCIs) für Zitationen
   - Verbesserte Vertrauenswürdigkeit der NFTs durch Verknüpfung mit einer anerkannten Quelle

3. **Allgemeine Vorteile**:
   - Unterstützung der Open-Science-Bewegung
   - Verbesserte Interoperabilität mit anderen wissenschaftlichen Systemen
   - Zugang zu strukturierten, offenen Zitationsdaten

## Konfiguration

Um die OpenCitations-Integration zu nutzen, müssen Sie folgende Umgebungsvariablen in Ihrer `.env`-Datei konfigurieren:

```
OPENCITATIONS_API_URL=https://opencitations.net/index/api/v1
OPENCITATIONS_ACCESS_TOKEN=your_access_token_here
```

Sie können einen API-Schlüssel von OpenCitations erhalten, indem Sie sich auf deren Website registrieren.

## Verwendung

### Zitationsanalyse

Die OpenCitations-Integration wird automatisch in der Zitationsanalyse verwendet. Der `CitationAnalysisAgent` ruft Daten von OpenCitations ab und kombiniert sie mit lokalen Daten, um umfassendere Zitationsmetriken zu erstellen.

```javascript
// Beispiel für die Verwendung der Zitationsanalyse mit OpenCitations
const agent = new CitationAnalysisAgent(options);
const result = await agent.analyzeCitations('10.1234/example-doi');

console.log(`Zitationen laut OpenCitations: ${result.openCitationsData.citationCount}`);
```

### NFT-Erstellung

Bei der Erstellung von NFTs für DOIs werden automatisch OpenCitations-Daten in die Metadaten aufgenommen, wenn verfügbar:

```javascript
// Beispiel für die Erstellung eines NFT mit OpenCitations-Daten
const agent = new CitationAnalysisAgent(options);
const result = await agent.createNFTForDOI('10.1234/example-doi');

console.log(`NFT erstellt: ${result.nftId}`);
console.log(`OpenCitations verifiziert: ${result.openCitationsVerified}`);
```

### Zitationsnetzwerke

Die Zitationsnetzwerke können mit OpenCitations-Daten angereichert werden:

```javascript
// Beispiel für die Erstellung eines Zitationsnetzwerks mit OpenCitations-Daten
const agent = new CitationAnalysisAgent(options);
const result = await agent.createCitationNetwork('10.1234/example-doi', {
  depth: 2,
  maxNodes: 100,
  useOpenCitations: true
});

console.log(`Netzwerk erstellt mit ${result.network.nodes.length} Knoten`);
```

## Datenbankschema

Die OpenCitations-Integration fügt folgende Spalten zu den Datenbanktabellen hinzu:

- `citation_relationships.oci`: Speichert den Open Citation Identifier
- `publication_metrics.opencitations_verified`: Gibt an, ob die Metriken durch OpenCitations verifiziert wurden
- `doi_nft_mapping.opencitations_verified`: Gibt an, ob das NFT mit OpenCitations-Daten angereichert wurde
- `citation_nft_links.oci`: Speichert den Open Citation Identifier für die Verknüpfung

Außerdem wird eine neue Tabelle `publication_external_data` erstellt, um externe Daten zu Publikationen zu speichern.

## Technische Details

Die Integration verwendet den `OpenCitationsService`, der folgende Hauptfunktionen bietet:

- `getCitations(doi)`: Holt eingehende Zitationen für eine DOI
- `getReferences(doi)`: Holt ausgehende Zitationen (Referenzen) für eine DOI
- `getCitationCount(doi)`: Holt die Anzahl der Zitationen für eine DOI
- `getMetadata(doi)`: Holt Metadaten für eine DOI
- `getAllDataForDOI(doi)`: Holt alle verfügbaren Daten für eine DOI
- `updateCitationDataForDOI(doi, databaseService)`: Aktualisiert die Zitationsdaten in der Datenbank

## Fehlerbehebung

Wenn Sie Probleme mit der OpenCitations-Integration haben, prüfen Sie Folgendes:

1. Stellen Sie sicher, dass Ihre API-Schlüssel korrekt konfiguriert sind
2. Prüfen Sie, ob die OpenCitations-API erreichbar ist
3. Überprüfen Sie die Logs auf Fehler im Zusammenhang mit OpenCitations
4. Stellen Sie sicher, dass die Datenbankmigration ausgeführt wurde

## Weitere Ressourcen

- [OpenCitations Website](https://opencitations.net/)
- [OpenCitations API-Dokumentation](https://opencitations.net/index/api/v1)
- [Open Citation Identifier (OCI) Spezifikation](https://opencitations.net/oci)# OpenCitations-Integration in DeSci-Scholar

Diese Dokumentation beschreibt die Integration von [OpenCitations](https://opencitations.net/) in das DeSci-Scholar-Projekt.

## Was ist OpenCitations?

OpenCitations ist eine unabhängige Infrastrukturorganisation, die offene bibliografische und Zitationsdaten bereitstellt. Sie bietet:

- Strukturierte, offene Zitationsdaten
- REST-APIs für den Zugriff auf Zitationsinformationen
- Eindeutige Identifikatoren für Zitationen (OCIs - Open Citation Identifiers)
- Semantische Web-Technologien für verknüpfte Daten

## Vorteile der Integration

Die Integration von OpenCitations in DeSci-Scholar bietet folgende Vorteile:

1. **Für die Zitationsanalyse**:
   - Umfassendere Zitationsdaten aus einer offenen, vertrauenswürdigen Quelle
   - Bessere Metriken für die Einflussberechnung
   - Genauere Zitationsnetzwerke

2. **Für die DOI-zu-NFT-Konvertierung**:
   - Anreicherung der NFT-Metadaten mit verifizierten Zitationsdaten
   - Eindeutige Identifikatoren (OCIs) für Zitationen
   - Verbesserte Vertrauenswürdigkeit der NFTs durch Verknüpfung mit einer anerkannten Quelle

3. **Allgemeine Vorteile**:
   - Unterstützung der Open-Science-Bewegung
   - Verbesserte Interoperabilität mit anderen wissenschaftlichen Systemen
   - Zugang zu strukturierten, offenen Zitationsdaten

## Konfiguration

Um die OpenCitations-Integration zu nutzen, müssen Sie folgende Umgebungsvariablen in Ihrer `.env`-Datei konfigurieren:

```
OPENCITATIONS_API_URL=https://opencitations.net/index/api/v1
OPENCITATIONS_ACCESS_TOKEN=your_access_token_here
```

Sie können einen API-Schlüssel von OpenCitations erhalten, indem Sie sich auf deren Website registrieren.

## Verwendung

### Zitationsanalyse

Die OpenCitations-Integration wird automatisch in der Zitationsanalyse verwendet. Der `CitationAnalysisAgent` ruft Daten von OpenCitations ab und kombiniert sie mit lokalen Daten, um umfassendere Zitationsmetriken zu erstellen.

```javascript
// Beispiel für die Verwendung der Zitationsanalyse mit OpenCitations
const agent = new CitationAnalysisAgent(options);
const result = await agent.analyzeCitations('10.1234/example-doi');

console.log(`Zitationen laut OpenCitations: ${result.openCitationsData.citationCount}`);
```

### NFT-Erstellung

Bei der Erstellung von NFTs für DOIs werden automatisch OpenCitations-Daten in die Metadaten aufgenommen, wenn verfügbar:

```javascript
// Beispiel für die Erstellung eines NFT mit OpenCitations-Daten
const agent = new CitationAnalysisAgent(options);
const result = await agent.createNFTForDOI('10.1234/example-doi');

console.log(`NFT erstellt: ${result.nftId}`);
console.log(`OpenCitations verifiziert: ${result.openCitationsVerified}`);
```

### Zitationsnetzwerke

Die Zitationsnetzwerke können mit OpenCitations-Daten angereichert werden:

```javascript
// Beispiel für die Erstellung eines Zitationsnetzwerks mit OpenCitations-Daten
const agent = new CitationAnalysisAgent(options);
const result = await agent.createCitationNetwork('10.1234/example-doi', {
  depth: 2,
  maxNodes: 100,
  useOpenCitations: true
});

console.log(`Netzwerk erstellt mit ${result.network.nodes.length} Knoten`);
```

## Datenbankschema

Die OpenCitations-Integration fügt folgende Spalten zu den Datenbanktabellen hinzu:

- `citation_relationships.oci`: Speichert den Open Citation Identifier
- `publication_metrics.opencitations_verified`: Gibt an, ob die Metriken durch OpenCitations verifiziert wurden
- `doi_nft_mapping.opencitations_verified`: Gibt an, ob das NFT mit OpenCitations-Daten angereichert wurde
- `citation_nft_links.oci`: Speichert den Open Citation Identifier für die Verknüpfung

Außerdem wird eine neue Tabelle `publication_external_data` erstellt, um externe Daten zu Publikationen zu speichern.

## Technische Details

Die Integration verwendet den `OpenCitationsService`, der folgende Hauptfunktionen bietet:

- `getCitations(doi)`: Holt eingehende Zitationen für eine DOI
- `getReferences(doi)`: Holt ausgehende Zitationen (Referenzen) für eine DOI
- `getCitationCount(doi)`: Holt die Anzahl der Zitationen für eine DOI
- `getMetadata(doi)`: Holt Metadaten für eine DOI
- `getAllDataForDOI(doi)`: Holt alle verfügbaren Daten für eine DOI
- `updateCitationDataForDOI(doi, databaseService)`: Aktualisiert die Zitationsdaten in der Datenbank

## Fehlerbehebung

Wenn Sie Probleme mit der OpenCitations-Integration haben, prüfen Sie Folgendes:

1. Stellen Sie sicher, dass Ihre API-Schlüssel korrekt konfiguriert sind
2. Prüfen Sie, ob die OpenCitations-API erreichbar ist
3. Überprüfen Sie die Logs auf Fehler im Zusammenhang mit OpenCitations
4. Stellen Sie sicher, dass die Datenbankmigration ausgeführt wurde

## Weitere Ressourcen

- [OpenCitations Website](https://opencitations.net/)
- [OpenCitations API-Dokumentation](https://opencitations.net/index/api/v1)
- [Open Citation Identifier (OCI) Spezifikation](https://opencitations.net/oci)# OpenCitations-Integration in DeSci-Scholar

Diese Dokumentation beschreibt die Integration von [OpenCitations](https://opencitations.net/) in das DeSci-Scholar-Projekt.

## Was ist OpenCitations?

OpenCitations ist eine unabhängige Infrastrukturorganisation, die offene bibliografische und Zitationsdaten bereitstellt. Sie bietet:

- Strukturierte, offene Zitationsdaten
- REST-APIs für den Zugriff auf Zitationsinformationen
- Eindeutige Identifikatoren für Zitationen (OCIs - Open Citation Identifiers)
- Semantische Web-Technologien für verknüpfte Daten

## Vorteile der Integration

Die Integration von OpenCitations in DeSci-Scholar bietet folgende Vorteile:

1. **Für die Zitationsanalyse**:
   - Umfassendere Zitationsdaten aus einer offenen, vertrauenswürdigen Quelle
   - Bessere Metriken für die Einflussberechnung
   - Genauere Zitationsnetzwerke

2. **Für die DOI-zu-NFT-Konvertierung**:
   - Anreicherung der NFT-Metadaten mit verifizierten Zitationsdaten
   - Eindeutige Identifikatoren (OCIs) für Zitationen
   - Verbesserte Vertrauenswürdigkeit der NFTs durch Verknüpfung mit einer anerkannten Quelle

3. **Allgemeine Vorteile**:
   - Unterstützung der Open-Science-Bewegung
   - Verbesserte Interoperabilität mit anderen wissenschaftlichen Systemen
   - Zugang zu strukturierten, offenen Zitationsdaten

## Konfiguration

Um die OpenCitations-Integration zu nutzen, müssen Sie folgende Umgebungsvariablen in Ihrer `.env`-Datei konfigurieren:

```
OPENCITATIONS_API_URL=https://opencitations.net/index/api/v1
OPENCITATIONS_ACCESS_TOKEN=your_access_token_here
```

Sie können einen API-Schlüssel von OpenCitations erhalten, indem Sie sich auf deren Website registrieren.

## Verwendung

### Zitationsanalyse

Die OpenCitations-Integration wird automatisch in der Zitationsanalyse verwendet. Der `CitationAnalysisAgent` ruft Daten von OpenCitations ab und kombiniert sie mit lokalen Daten, um umfassendere Zitationsmetriken zu erstellen.

```javascript
// Beispiel für die Verwendung der Zitationsanalyse mit OpenCitations
const agent = new CitationAnalysisAgent(options);
const result = await agent.analyzeCitations('10.1234/example-doi');

console.log(`Zitationen laut OpenCitations: ${result.openCitationsData.citationCount}`);
```

### NFT-Erstellung

Bei der Erstellung von NFTs für DOIs werden automatisch OpenCitations-Daten in die Metadaten aufgenommen, wenn verfügbar:

```javascript
// Beispiel für die Erstellung eines NFT mit OpenCitations-Daten
const agent = new CitationAnalysisAgent(options);
const result = await agent.createNFTForDOI('10.1234/example-doi');

console.log(`NFT erstellt: ${result.nftId}`);
console.log(`OpenCitations verifiziert: ${result.openCitationsVerified}`);
```

### Zitationsnetzwerke

Die Zitationsnetzwerke können mit OpenCitations-Daten angereichert werden:

```javascript
// Beispiel für die Erstellung eines Zitationsnetzwerks mit OpenCitations-Daten
const agent = new CitationAnalysisAgent(options);
const result = await agent.createCitationNetwork('10.1234/example-doi', {
  depth: 2,
  maxNodes: 100,
  useOpenCitations: true
});

console.log(`Netzwerk erstellt mit ${result.network.nodes.length} Knoten`);
```

## Datenbankschema

Die OpenCitations-Integration fügt folgende Spalten zu den Datenbanktabellen hinzu:

- `citation_relationships.oci`: Speichert den Open Citation Identifier
- `publication_metrics.opencitations_verified`: Gibt an, ob die Metriken durch OpenCitations verifiziert wurden
- `doi_nft_mapping.opencitations_verified`: Gibt an, ob das NFT mit OpenCitations-Daten angereichert wurde
- `citation_nft_links.oci`: Speichert den Open Citation Identifier für die Verknüpfung

Außerdem wird eine neue Tabelle `publication_external_data` erstellt, um externe Daten zu Publikationen zu speichern.

## Technische Details

Die Integration verwendet den `OpenCitationsService`, der folgende Hauptfunktionen bietet:

- `getCitations(doi)`: Holt eingehende Zitationen für eine DOI
- `getReferences(doi)`: Holt ausgehende Zitationen (Referenzen) für eine DOI
- `getCitationCount(doi)`: Holt die Anzahl der Zitationen für eine DOI
- `getMetadata(doi)`: Holt Metadaten für eine DOI
- `getAllDataForDOI(doi)`: Holt alle verfügbaren Daten für eine DOI
- `updateCitationDataForDOI(doi, databaseService)`: Aktualisiert die Zitationsdaten in der Datenbank

## Fehlerbehebung

Wenn Sie Probleme mit der OpenCitations-Integration haben, prüfen Sie Folgendes:

1. Stellen Sie sicher, dass Ihre API-Schlüssel korrekt konfiguriert sind
2. Prüfen Sie, ob die OpenCitations-API erreichbar ist
3. Überprüfen Sie die Logs auf Fehler im Zusammenhang mit OpenCitations
4. Stellen Sie sicher, dass die Datenbankmigration ausgeführt wurde

## Weitere Ressourcen

- [OpenCitations Website](https://opencitations.net/)
- [OpenCitations API-Dokumentation](https://opencitations.net/index/api/v1)
- [Open Citation Identifier (OCI) Spezifikation](https://opencitations.net/oci)# OpenCitations-Integration in DeSci-Scholar

Diese Dokumentation beschreibt die Integration von [OpenCitations](https://opencitations.net/) in das DeSci-Scholar-Projekt.

## Was ist OpenCitations?

OpenCitations ist eine unabhängige Infrastrukturorganisation, die offene bibliografische und Zitationsdaten bereitstellt. Sie bietet:

- Strukturierte, offene Zitationsdaten
- REST-APIs für den Zugriff auf Zitationsinformationen
- Eindeutige Identifikatoren für Zitationen (OCIs - Open Citation Identifiers)
- Semantische Web-Technologien für verknüpfte Daten

## Vorteile der Integration

Die Integration von OpenCitations in DeSci-Scholar bietet folgende Vorteile:

1. **Für die Zitationsanalyse**:
   - Umfassendere Zitationsdaten aus einer offenen, vertrauenswürdigen Quelle
   - Bessere Metriken für die Einflussberechnung
   - Genauere Zitationsnetzwerke

2. **Für die DOI-zu-NFT-Konvertierung**:
   - Anreicherung der NFT-Metadaten mit verifizierten Zitationsdaten
   - Eindeutige Identifikatoren (OCIs) für Zitationen
   - Verbesserte Vertrauenswürdigkeit der NFTs durch Verknüpfung mit einer anerkannten Quelle

3. **Allgemeine Vorteile**:
   - Unterstützung der Open-Science-Bewegung
   - Verbesserte Interoperabilität mit anderen wissenschaftlichen Systemen
   - Zugang zu strukturierten, offenen Zitationsdaten

## Konfiguration

Um die OpenCitations-Integration zu nutzen, müssen Sie folgende Umgebungsvariablen in Ihrer `.env`-Datei konfigurieren:

```
OPENCITATIONS_API_URL=https://opencitations.net/index/api/v1
OPENCITATIONS_ACCESS_TOKEN=your_access_token_here
```

Sie können einen API-Schlüssel von OpenCitations erhalten, indem Sie sich auf deren Website registrieren.

## Verwendung

### Zitationsanalyse

Die OpenCitations-Integration wird automatisch in der Zitationsanalyse verwendet. Der `CitationAnalysisAgent` ruft Daten von OpenCitations ab und kombiniert sie mit lokalen Daten, um umfassendere Zitationsmetriken zu erstellen.

```javascript
// Beispiel für die Verwendung der Zitationsanalyse mit OpenCitations
const agent = new CitationAnalysisAgent(options);
const result = await agent.analyzeCitations('10.1234/example-doi');

console.log(`Zitationen laut OpenCitations: ${result.openCitationsData.citationCount}`);
```

### NFT-Erstellung

Bei der Erstellung von NFTs für DOIs werden automatisch OpenCitations-Daten in die Metadaten aufgenommen, wenn verfügbar:

```javascript
// Beispiel für die Erstellung eines NFT mit OpenCitations-Daten
const agent = new CitationAnalysisAgent(options);
const result = await agent.createNFTForDOI('10.1234/example-doi');

console.log(`NFT erstellt: ${result.nftId}`);
console.log(`OpenCitations verifiziert: ${result.openCitationsVerified}`);
```

### Zitationsnetzwerke

Die Zitationsnetzwerke können mit OpenCitations-Daten angereichert werden:

```javascript
// Beispiel für die Erstellung eines Zitationsnetzwerks mit OpenCitations-Daten
const agent = new CitationAnalysisAgent(options);
const result = await agent.createCitationNetwork('10.1234/example-doi', {
  depth: 2,
  maxNodes: 100,
  useOpenCitations: true
});

console.log(`Netzwerk erstellt mit ${result.network.nodes.length} Knoten`);
```

## Datenbankschema

Die OpenCitations-Integration fügt folgende Spalten zu den Datenbanktabellen hinzu:

- `citation_relationships.oci`: Speichert den Open Citation Identifier
- `publication_metrics.opencitations_verified`: Gibt an, ob die Metriken durch OpenCitations verifiziert wurden
- `doi_nft_mapping.opencitations_verified`: Gibt an, ob das NFT mit OpenCitations-Daten angereichert wurde
- `citation_nft_links.oci`: Speichert den Open Citation Identifier für die Verknüpfung

Außerdem wird eine neue Tabelle `publication_external_data` erstellt, um externe Daten zu Publikationen zu speichern.

## Technische Details

Die Integration verwendet den `OpenCitationsService`, der folgende Hauptfunktionen bietet:

- `getCitations(doi)`: Holt eingehende Zitationen für eine DOI
- `getReferences(doi)`: Holt ausgehende Zitationen (Referenzen) für eine DOI
- `getCitationCount(doi)`: Holt die Anzahl der Zitationen für eine DOI
- `getMetadata(doi)`: Holt Metadaten für eine DOI
- `getAllDataForDOI(doi)`: Holt alle verfügbaren Daten für eine DOI
- `updateCitationDataForDOI(doi, databaseService)`: Aktualisiert die Zitationsdaten in der Datenbank

## Fehlerbehebung

Wenn Sie Probleme mit der OpenCitations-Integration haben, prüfen Sie Folgendes:

1. Stellen Sie sicher, dass Ihre API-Schlüssel korrekt konfiguriert sind
2. Prüfen Sie, ob die OpenCitations-API erreichbar ist
3. Überprüfen Sie die Logs auf Fehler im Zusammenhang mit OpenCitations
4. Stellen Sie sicher, dass die Datenbankmigration ausgeführt wurde

## Weitere Ressourcen

- [OpenCitations Website](https://opencitations.net/)
- [OpenCitations API-Dokumentation](https://opencitations.net/index/api/v1)
- [Open Citation Identifier (OCI) Spezifikation](https://opencitations.net/oci)# OpenCitations-Integration in DeSci-Scholar

Diese Dokumentation beschreibt die Integration von [OpenCitations](https://opencitations.net/) in das DeSci-Scholar-Projekt.

## Was ist OpenCitations?

OpenCitations ist eine unabhängige Infrastrukturorganisation, die offene bibliografische und Zitationsdaten bereitstellt. Sie bietet:

- Strukturierte, offene Zitationsdaten
- REST-APIs für den Zugriff auf Zitationsinformationen
- Eindeutige Identifikatoren für Zitationen (OCIs - Open Citation Identifiers)
- Semantische Web-Technologien für verknüpfte Daten

## Vorteile der Integration

Die Integration von OpenCitations in DeSci-Scholar bietet folgende Vorteile:

1. **Für die Zitationsanalyse**:
   - Umfassendere Zitationsdaten aus einer offenen, vertrauenswürdigen Quelle
   - Bessere Metriken für die Einflussberechnung
   - Genauere Zitationsnetzwerke

2. **Für die DOI-zu-NFT-Konvertierung**:
   - Anreicherung der NFT-Metadaten mit verifizierten Zitationsdaten
   - Eindeutige Identifikatoren (OCIs) für Zitationen
   - Verbesserte Vertrauenswürdigkeit der NFTs durch Verknüpfung mit einer anerkannten Quelle

3. **Allgemeine Vorteile**:
   - Unterstützung der Open-Science-Bewegung
   - Verbesserte Interoperabilität mit anderen wissenschaftlichen Systemen
   - Zugang zu strukturierten, offenen Zitationsdaten

## Konfiguration

Um die OpenCitations-Integration zu nutzen, müssen Sie folgende Umgebungsvariablen in Ihrer `.env`-Datei konfigurieren:

```
OPENCITATIONS_API_URL=https://opencitations.net/index/api/v1
OPENCITATIONS_ACCESS_TOKEN=your_access_token_here
```

Sie können einen API-Schlüssel von OpenCitations erhalten, indem Sie sich auf deren Website registrieren.

## Verwendung

### Zitationsanalyse

Die OpenCitations-Integration wird automatisch in der Zitationsanalyse verwendet. Der `CitationAnalysisAgent` ruft Daten von OpenCitations ab und kombiniert sie mit lokalen Daten, um umfassendere Zitationsmetriken zu erstellen.

```javascript
// Beispiel für die Verwendung der Zitationsanalyse mit OpenCitations
const agent = new CitationAnalysisAgent(options);
const result = await agent.analyzeCitations('10.1234/example-doi');

console.log(`Zitationen laut OpenCitations: ${result.openCitationsData.citationCount}`);
```

### NFT-Erstellung

Bei der Erstellung von NFTs für DOIs werden automatisch OpenCitations-Daten in die Metadaten aufgenommen, wenn verfügbar:

```javascript
// Beispiel für die Erstellung eines NFT mit OpenCitations-Daten
const agent = new CitationAnalysisAgent(options);
const result = await agent.createNFTForDOI('10.1234/example-doi');

console.log(`NFT erstellt: ${result.nftId}`);
console.log(`OpenCitations verifiziert: ${result.openCitationsVerified}`);
```

### Zitationsnetzwerke

Die Zitationsnetzwerke können mit OpenCitations-Daten angereichert werden:

```javascript
// Beispiel für die Erstellung eines Zitationsnetzwerks mit OpenCitations-Daten
const agent = new CitationAnalysisAgent(options);
const result = await agent.createCitationNetwork('10.1234/example-doi', {
  depth: 2,
  maxNodes: 100,
  useOpenCitations: true
});

console.log(`Netzwerk erstellt mit ${result.network.nodes.length} Knoten`);
```

## Datenbankschema

Die OpenCitations-Integration fügt folgende Spalten zu den Datenbanktabellen hinzu:

- `citation_relationships.oci`: Speichert den Open Citation Identifier
- `publication_metrics.opencitations_verified`: Gibt an, ob die Metriken durch OpenCitations verifiziert wurden
- `doi_nft_mapping.opencitations_verified`: Gibt an, ob das NFT mit OpenCitations-Daten angereichert wurde
- `citation_nft_links.oci`: Speichert den Open Citation Identifier für die Verknüpfung

Außerdem wird eine neue Tabelle `publication_external_data` erstellt, um externe Daten zu Publikationen zu speichern.

## Technische Details

Die Integration verwendet den `OpenCitationsService`, der folgende Hauptfunktionen bietet:

- `getCitations(doi)`: Holt eingehende Zitationen für eine DOI
- `getReferences(doi)`: Holt ausgehende Zitationen (Referenzen) für eine DOI
- `getCitationCount(doi)`: Holt die Anzahl der Zitationen für eine DOI
- `getMetadata(doi)`: Holt Metadaten für eine DOI
- `getAllDataForDOI(doi)`: Holt alle verfügbaren Daten für eine DOI
- `updateCitationDataForDOI(doi, databaseService)`: Aktualisiert die Zitationsdaten in der Datenbank

## Fehlerbehebung

Wenn Sie Probleme mit der OpenCitations-Integration haben, prüfen Sie Folgendes:

1. Stellen Sie sicher, dass Ihre API-Schlüssel korrekt konfiguriert sind
2. Prüfen Sie, ob die OpenCitations-API erreichbar ist
3. Überprüfen Sie die Logs auf Fehler im Zusammenhang mit OpenCitations
4. Stellen Sie sicher, dass die Datenbankmigration ausgeführt wurde

## Weitere Ressourcen

- [OpenCitations Website](https://opencitations.net/)
- [OpenCitations API-Dokumentation](https://opencitations.net/index/api/v1)
- [Open Citation Identifier (OCI) Spezifikation](https://opencitations.net/oci)# OpenCitations-Integration in DeSci-Scholar

Diese Dokumentation beschreibt die Integration von [OpenCitations](https://opencitations.net/) in das DeSci-Scholar-Projekt.

## Was ist OpenCitations?

OpenCitations ist eine unabhängige Infrastrukturorganisation, die offene bibliografische und Zitationsdaten bereitstellt. Sie bietet:

- Strukturierte, offene Zitationsdaten
- REST-APIs für den Zugriff auf Zitationsinformationen
- Eindeutige Identifikatoren für Zitationen (OCIs - Open Citation Identifiers)
- Semantische Web-Technologien für verknüpfte Daten

## Vorteile der Integration

Die Integration von OpenCitations in DeSci-Scholar bietet folgende Vorteile:

1. **Für die Zitationsanalyse**:
   - Umfassendere Zitationsdaten aus einer offenen, vertrauenswürdigen Quelle
   - Bessere Metriken für die Einflussberechnung
   - Genauere Zitationsnetzwerke

2. **Für die DOI-zu-NFT-Konvertierung**:
   - Anreicherung der NFT-Metadaten mit verifizierten Zitationsdaten
   - Eindeutige Identifikatoren (OCIs) für Zitationen
   - Verbesserte Vertrauenswürdigkeit der NFTs durch Verknüpfung mit einer anerkannten Quelle

3. **Allgemeine Vorteile**:
   - Unterstützung der Open-Science-Bewegung
   - Verbesserte Interoperabilität mit anderen wissenschaftlichen Systemen
   - Zugang zu strukturierten, offenen Zitationsdaten

## Konfiguration

Um die OpenCitations-Integration zu nutzen, müssen Sie folgende Umgebungsvariablen in Ihrer `.env`-Datei konfigurieren:

```
OPENCITATIONS_API_URL=https://opencitations.net/index/api/v1
OPENCITATIONS_ACCESS_TOKEN=your_access_token_here
```

Sie können einen API-Schlüssel von OpenCitations erhalten, indem Sie sich auf deren Website registrieren.

## Verwendung

### Zitationsanalyse

Die OpenCitations-Integration wird automatisch in der Zitationsanalyse verwendet. Der `CitationAnalysisAgent` ruft Daten von OpenCitations ab und kombiniert sie mit lokalen Daten, um umfassendere Zitationsmetriken zu erstellen.

```javascript
// Beispiel für die Verwendung der Zitationsanalyse mit OpenCitations
const agent = new CitationAnalysisAgent(options);
const result = await agent.analyzeCitations('10.1234/example-doi');

console.log(`Zitationen laut OpenCitations: ${result.openCitationsData.citationCount}`);
```

### NFT-Erstellung

Bei der Erstellung von NFTs für DOIs werden automatisch OpenCitations-Daten in die Metadaten aufgenommen, wenn verfügbar:

```javascript
// Beispiel für die Erstellung eines NFT mit OpenCitations-Daten
const agent = new CitationAnalysisAgent(options);
const result = await agent.createNFTForDOI('10.1234/example-doi');

console.log(`NFT erstellt: ${result.nftId}`);
console.log(`OpenCitations verifiziert: ${result.openCitationsVerified}`);
```

### Zitationsnetzwerke

Die Zitationsnetzwerke können mit OpenCitations-Daten angereichert werden:

```javascript
// Beispiel für die Erstellung eines Zitationsnetzwerks mit OpenCitations-Daten
const agent = new CitationAnalysisAgent(options);
const result = await agent.createCitationNetwork('10.1234/example-doi', {
  depth: 2,
  maxNodes: 100,
  useOpenCitations: true
});

console.log(`Netzwerk erstellt mit ${result.network.nodes.length} Knoten`);
```

## Datenbankschema

Die OpenCitations-Integration fügt folgende Spalten zu den Datenbanktabellen hinzu:

- `citation_relationships.oci`: Speichert den Open Citation Identifier
- `publication_metrics.opencitations_verified`: Gibt an, ob die Metriken durch OpenCitations verifiziert wurden
- `doi_nft_mapping.opencitations_verified`: Gibt an, ob das NFT mit OpenCitations-Daten angereichert wurde
- `citation_nft_links.oci`: Speichert den Open Citation Identifier für die Verknüpfung

Außerdem wird eine neue Tabelle `publication_external_data` erstellt, um externe Daten zu Publikationen zu speichern.

## Technische Details

Die Integration verwendet den `OpenCitationsService`, der folgende Hauptfunktionen bietet:

- `getCitations(doi)`: Holt eingehende Zitationen für eine DOI
- `getReferences(doi)`: Holt ausgehende Zitationen (Referenzen) für eine DOI
- `getCitationCount(doi)`: Holt die Anzahl der Zitationen für eine DOI
- `getMetadata(doi)`: Holt Metadaten für eine DOI
- `getAllDataForDOI(doi)`: Holt alle verfügbaren Daten für eine DOI
- `updateCitationDataForDOI(doi, databaseService)`: Aktualisiert die Zitationsdaten in der Datenbank

## Fehlerbehebung

Wenn Sie Probleme mit der OpenCitations-Integration haben, prüfen Sie Folgendes:

1. Stellen Sie sicher, dass Ihre API-Schlüssel korrekt konfiguriert sind
2. Prüfen Sie, ob die OpenCitations-API erreichbar ist
3. Überprüfen Sie die Logs auf Fehler im Zusammenhang mit OpenCitations
4. Stellen Sie sicher, dass die Datenbankmigration ausgeführt wurde

## Weitere Ressourcen

- [OpenCitations Website](https://opencitations.net/)
- [OpenCitations API-Dokumentation](https://opencitations.net/index/api/v1)
- [Open Citation Identifier (OCI) Spezifikation](https://opencitations.net/oci)# OpenCitations-Integration in DeSci-Scholar

Diese Dokumentation beschreibt die Integration von [OpenCitations](https://opencitations.net/) in das DeSci-Scholar-Projekt.

## Was ist OpenCitations?

OpenCitations ist eine unabhängige Infrastrukturorganisation, die offene bibliografische und Zitationsdaten bereitstellt. Sie bietet:

- Strukturierte, offene Zitationsdaten
- REST-APIs für den Zugriff auf Zitationsinformationen
- Eindeutige Identifikatoren für Zitationen (OCIs - Open Citation Identifiers)
- Semantische Web-Technologien für verknüpfte Daten

## Vorteile der Integration

Die Integration von OpenCitations in DeSci-Scholar bietet folgende Vorteile:

1. **Für die Zitationsanalyse**:
   - Umfassendere Zitationsdaten aus einer offenen, vertrauenswürdigen Quelle
   - Bessere Metriken für die Einflussberechnung
   - Genauere Zitationsnetzwerke

2. **Für die DOI-zu-NFT-Konvertierung**:
   - Anreicherung der NFT-Metadaten mit verifizierten Zitationsdaten
   - Eindeutige Identifikatoren (OCIs) für Zitationen
   - Verbesserte Vertrauenswürdigkeit der NFTs durch Verknüpfung mit einer anerkannten Quelle

3. **Allgemeine Vorteile**:
   - Unterstützung der Open-Science-Bewegung
   - Verbesserte Interoperabilität mit anderen wissenschaftlichen Systemen
   - Zugang zu strukturierten, offenen Zitationsdaten

## Konfiguration

Um die OpenCitations-Integration zu nutzen, müssen Sie folgende Umgebungsvariablen in Ihrer `.env`-Datei konfigurieren:

```
OPENCITATIONS_API_URL=https://opencitations.net/index/api/v1
OPENCITATIONS_ACCESS_TOKEN=your_access_token_here
```

Sie können einen API-Schlüssel von OpenCitations erhalten, indem Sie sich auf deren Website registrieren.

## Verwendung

### Zitationsanalyse

Die OpenCitations-Integration wird automatisch in der Zitationsanalyse verwendet. Der `CitationAnalysisAgent` ruft Daten von OpenCitations ab und kombiniert sie mit lokalen Daten, um umfassendere Zitationsmetriken zu erstellen.

```javascript
// Beispiel für die Verwendung der Zitationsanalyse mit OpenCitations
const agent = new CitationAnalysisAgent(options);
const result = await agent.analyzeCitations('10.1234/example-doi');

console.log(`Zitationen laut OpenCitations: ${result.openCitationsData.citationCount}`);
```

### NFT-Erstellung

Bei der Erstellung von NFTs für DOIs werden automatisch OpenCitations-Daten in die Metadaten aufgenommen, wenn verfügbar:

```javascript
// Beispiel für die Erstellung eines NFT mit OpenCitations-Daten
const agent = new CitationAnalysisAgent(options);
const result = await agent.createNFTForDOI('10.1234/example-doi');

console.log(`NFT erstellt: ${result.nftId}`);
console.log(`OpenCitations verifiziert: ${result.openCitationsVerified}`);
```

### Zitationsnetzwerke

Die Zitationsnetzwerke können mit OpenCitations-Daten angereichert werden:

```javascript
// Beispiel für die Erstellung eines Zitationsnetzwerks mit OpenCitations-Daten
const agent = new CitationAnalysisAgent(options);
const result = await agent.createCitationNetwork('10.1234/example-doi', {
  depth: 2,
  maxNodes: 100,
  useOpenCitations: true
});

console.log(`Netzwerk erstellt mit ${result.network.nodes.length} Knoten`);
```

## Datenbankschema

Die OpenCitations-Integration fügt folgende Spalten zu den Datenbanktabellen hinzu:

- `citation_relationships.oci`: Speichert den Open Citation Identifier
- `publication_metrics.opencitations_verified`: Gibt an, ob die Metriken durch OpenCitations verifiziert wurden
- `doi_nft_mapping.opencitations_verified`: Gibt an, ob das NFT mit OpenCitations-Daten angereichert wurde
- `citation_nft_links.oci`: Speichert den Open Citation Identifier für die Verknüpfung

Außerdem wird eine neue Tabelle `publication_external_data` erstellt, um externe Daten zu Publikationen zu speichern.

## Technische Details

Die Integration verwendet den `OpenCitationsService`, der folgende Hauptfunktionen bietet:

- `getCitations(doi)`: Holt eingehende Zitationen für eine DOI
- `getReferences(doi)`: Holt ausgehende Zitationen (Referenzen) für eine DOI
- `getCitationCount(doi)`: Holt die Anzahl der Zitationen für eine DOI
- `getMetadata(doi)`: Holt Metadaten für eine DOI
- `getAllDataForDOI(doi)`: Holt alle verfügbaren Daten für eine DOI
- `updateCitationDataForDOI(doi, databaseService)`: Aktualisiert die Zitationsdaten in der Datenbank

## Fehlerbehebung

Wenn Sie Probleme mit der OpenCitations-Integration haben, prüfen Sie Folgendes:

1. Stellen Sie sicher, dass Ihre API-Schlüssel korrekt konfiguriert sind
2. Prüfen Sie, ob die OpenCitations-API erreichbar ist
3. Überprüfen Sie die Logs auf Fehler im Zusammenhang mit OpenCitations
4. Stellen Sie sicher, dass die Datenbankmigration ausgeführt wurde

## Weitere Ressourcen

- [OpenCitations Website](https://opencitations.net/)
- [OpenCitations API-Dokumentation](https://opencitations.net/index/api/v1)
- [Open Citation Identifier (OCI) Spezifikation](https://opencitations.net/oci)Diese Dokumentation beschreibt die Integration von [OpenCitations](https://opencitations.net/) in das DeSci-Scholar-Projekt.

## Was ist OpenCitations?

OpenCitations ist eine unabhängige Infrastrukturorganisation, die offene bibliografische und Zitationsdaten bereitstellt. Sie bietet:

- Strukturierte, offene Zitationsdaten
- REST-APIs für den Zugriff auf Zitationsinformationen
- Eindeutige Identifikatoren für Zitationen (OCIs - Open Citation Identifiers)
- Semantische Web-Technologien für verknüpfte Daten

## Vorteile der Integration

Die Integration von OpenCitations in DeSci-Scholar bietet folgende Vorteile:

1. **Für die Zitationsanalyse**:
   - Umfassendere Zitationsdaten aus einer offenen, vertrauenswürdigen Quelle
   - Bessere Metriken für die Einflussberechnung
   - Genauere Zitationsnetzwerke

2. **Für die DOI-zu-NFT-Konvertierung**:
   - Anreicherung der NFT-Metadaten mit verifizierten Zitationsdaten
   - Eindeutige Identifikatoren (OCIs) für Zitationen
   - Verbesserte Vertrauenswürdigkeit der NFTs durch Verknüpfung mit einer anerkannten Quelle

3. **Allgemeine Vorteile**:
   - Unterstützung der Open-Science-Bewegung
   - Verbesserte Interoperabilität mit anderen wissenschaftlichen Systemen
   - Zugang zu strukturierten, offenen Zitationsdaten

## Konfiguration

Um die OpenCitations-Integration zu nutzen, müssen Sie folgende Umgebungsvariablen in Ihrer `.env`-Datei konfigurieren:

```
OPENCITATIONS_API_URL=https://opencitations.net/index/api/v1
OPENCITATIONS_ACCESS_TOKEN=your_access_token_here
```

Sie können einen API-Schlüssel von OpenCitations erhalten, indem Sie sich auf deren Website registrieren.

## Verwendung

### Zitationsanalyse

Die OpenCitations-Integration wird automatisch in der Zitationsanalyse verwendet. Der `CitationAnalysisAgent` ruft Daten von OpenCitations ab und kombiniert sie mit lokalen Daten, um umfassendere Zitationsmetriken zu erstellen.

```javascript
// Beispiel für die Verwendung der Zitationsanalyse mit OpenCitations
const agent = new CitationAnalysisAgent(options);
const result = await agent.analyzeCitations('10.1234/example-doi');

console.log(`Zitationen laut OpenCitations: ${result.openCitationsData.citationCount}`);
```

### NFT-Erstellung

Bei der Erstellung von NFTs für DOIs werden automatisch OpenCitations-Daten in die Metadaten aufgenommen, wenn verfügbar:

```javascript
// Beispiel für die Erstellung eines NFT mit OpenCitations-Daten
const agent = new CitationAnalysisAgent(options);
const result = await agent.createNFTForDOI('10.1234/example-doi');

console.log(`NFT erstellt: ${result.nftId}`);
console.log(`OpenCitations verifiziert: ${result.openCitationsVerified}`);
```

### Zitationsnetzwerke

Die Zitationsnetzwerke können mit OpenCitations-Daten angereichert werden:

```javascript
// Beispiel für die Erstellung eines Zitationsnetzwerks mit OpenCitations-Daten
const agent = new CitationAnalysisAgent(options);
const result = await agent.createCitationNetwork('10.1234/example-doi', {
  depth: 2,
  maxNodes: 100,
  useOpenCitations: true
});

console.log(`Netzwerk erstellt mit ${result.network.nodes.length} Knoten`);
```

## Datenbankschema

Die OpenCitations-Integration fügt folgende Spalten zu den Datenbanktabellen hinzu:

- `citation_relationships.oci`: Speichert den Open Citation Identifier
- `publication_metrics.opencitations_verified`: Gibt an, ob die Metriken durch OpenCitations verifiziert wurden
- `doi_nft_mapping.opencitations_verified`: Gibt an, ob das NFT mit OpenCitations-Daten angereichert wurde
- `citation_nft_links.oci`: Speichert den Open Citation Identifier für die Verknüpfung

Außerdem wird eine neue Tabelle `publication_external_data` erstellt, um externe Daten zu Publikationen zu speichern.

## Technische Details

Die Integration verwendet den `OpenCitationsService`, der folgende Hauptfunktionen bietet:

- `getCitations(doi)`: Holt eingehende Zitationen für eine DOI
- `getReferences(doi)`: Holt ausgehende Zitationen (Referenzen) für eine DOI
- `getCitationCount(doi)`: Holt die Anzahl der Zitationen für eine DOI
- `getMetadata(doi)`: Holt Metadaten für eine DOI
- `getAllDataForDOI(doi)`: Holt alle verfügbaren Daten für eine DOI
- `updateCitationDataForDOI(doi, databaseService)`: Aktualisiert die Zitationsdaten in der Datenbank

## Fehlerbehebung

Wenn Sie Probleme mit der OpenCitations-Integration haben, prüfen Sie Folgendes:

1. Stellen Sie sicher, dass Ihre API-Schlüssel korrekt konfiguriert sind
2. Prüfen Sie, ob die OpenCitations-API erreichbar ist
3. Überprüfen Sie die Logs auf Fehler im Zusammenhang mit OpenCitations
4. Stellen Sie sicher, dass die Datenbankmigration ausgeführt wurde

## Weitere Ressourcen

- [OpenCitations Website](https://opencitations.net/)
- [OpenCitations API-Dokumentation](https://opencitations.net/index/api/v1)
- [Open Citation Identifier (OCI) Spezifikation](https://opencitations.net/oci)- [Open Citation Identifier (OCI) Spezifikation](https://opencitations.net/oci)Diese Dokumentation beschreibt die Integration von [OpenCitations](https://opencitations.net/) in das DeSci-Scholar-Projekt.

## Was ist OpenCitations?

OpenCitations ist eine unabhängige Infrastrukturorganisation, die offene bibliografische und Zitationsdaten bereitstellt. Sie bietet:

- Strukturierte, offene Zitationsdaten
- REST-APIs für den Zugriff auf Zitationsinformationen
- Eindeutige Identifikatoren für Zitationen (OCIs - Open Citation Identifiers)
- Semantische Web-Technologien für verknüpfte Daten

## Vorteile der Integration

Die Integration von OpenCitations in DeSci-Scholar bietet folgende Vorteile:

1. **Für die Zitationsanalyse**:
   - Umfassendere Zitationsdaten aus einer offenen, vertrauenswürdigen Quelle
   - Bessere Metriken für die Einflussberechnung
   - Genauere Zitationsnetzwerke

2. **Für die DOI-zu-NFT-Konvertierung**:
   - Anreicherung der NFT-Metadaten mit verifizierten Zitationsdaten
   - Eindeutige Identifikatoren (OCIs) für Zitationen
   - Verbesserte Vertrauenswürdigkeit der NFTs durch Verknüpfung mit einer anerkannten Quelle

3. **Allgemeine Vorteile**:
   - Unterstützung der Open-Science-Bewegung
   - Verbesserte Interoperabilität mit anderen wissenschaftlichen Systemen
   - Zugang zu strukturierten, offenen Zitationsdaten

## Konfiguration

Um die OpenCitations-Integration zu nutzen, müssen Sie folgende Umgebungsvariablen in Ihrer `.env`-Datei konfigurieren:

```
OPENCITATIONS_API_URL=https://opencitations.net/index/api/v1
OPENCITATIONS_ACCESS_TOKEN=your_access_token_here
```

Sie können einen API-Schlüssel von OpenCitations erhalten, indem Sie sich auf deren Website registrieren.

## Verwendung

### Zitationsanalyse

Die OpenCitations-Integration wird automatisch in der Zitationsanalyse verwendet. Der `CitationAnalysisAgent` ruft Daten von OpenCitations ab und kombiniert sie mit lokalen Daten, um umfassendere Zitationsmetriken zu erstellen.

```javascript
// Beispiel für die Verwendung der Zitationsanalyse mit OpenCitations
const agent = new CitationAnalysisAgent(options);
const result = await agent.analyzeCitations('10.1234/example-doi');

console.log(`Zitationen laut OpenCitations: ${result.openCitationsData.citationCount}`);
```

### NFT-Erstellung

Bei der Erstellung von NFTs für DOIs werden automatisch OpenCitations-Daten in die Metadaten aufgenommen, wenn verfügbar:

```javascript
// Beispiel für die Erstellung eines NFT mit OpenCitations-Daten
const agent = new CitationAnalysisAgent(options);
const result = await agent.createNFTForDOI('10.1234/example-doi');

console.log(`NFT erstellt: ${result.nftId}`);
console.log(`OpenCitations verifiziert: ${result.openCitationsVerified}`);
```

### Zitationsnetzwerke

Die Zitationsnetzwerke können mit OpenCitations-Daten angereichert werden:

```javascript
// Beispiel für die Erstellung eines Zitationsnetzwerks mit OpenCitations-Daten
const agent = new CitationAnalysisAgent(options);
const result = await agent.createCitationNetwork('10.1234/example-doi', {
  depth: 2,
  maxNodes: 100,
  useOpenCitations: true
});

console.log(`Netzwerk erstellt mit ${result.network.nodes.length} Knoten`);
```

## Datenbankschema

Die OpenCitations-Integration fügt folgende Spalten zu den Datenbanktabellen hinzu:

- `citation_relationships.oci`: Speichert den Open Citation Identifier
- `publication_metrics.opencitations_verified`: Gibt an, ob die Metriken durch OpenCitations verifiziert wurden
- `doi_nft_mapping.opencitations_verified`: Gibt an, ob das NFT mit OpenCitations-Daten angereichert wurde
- `citation_nft_links.oci`: Speichert den Open Citation Identifier für die Verknüpfung

Außerdem wird eine neue Tabelle `publication_external_data` erstellt, um externe Daten zu Publikationen zu speichern.

## Technische Details

Die Integration verwendet den `OpenCitationsService`, der folgende Hauptfunktionen bietet:

- `getCitations(doi)`: Holt eingehende Zitationen für eine DOI
- `getReferences(doi)`: Holt ausgehende Zitationen (Referenzen) für eine DOI
- `getCitationCount(doi)`: Holt die Anzahl der Zitationen für eine DOI
- `getMetadata(doi)`: Holt Metadaten für eine DOI
- `getAllDataForDOI(doi)`: Holt alle verfügbaren Daten für eine DOI
- `updateCitationDataForDOI(doi, databaseService)`: Aktualisiert die Zitationsdaten in der Datenbank

## Fehlerbehebung

Wenn Sie Probleme mit der OpenCitations-Integration haben, prüfen Sie Folgendes:

1. Stellen Sie sicher, dass Ihre API-Schlüssel korrekt konfiguriert sind
2. Prüfen Sie, ob die OpenCitations-API erreichbar ist
3. Überprüfen Sie die Logs auf Fehler im Zusammenhang mit OpenCitations
4. Stellen Sie sicher, dass die Datenbankmigration ausgeführt wurde

## Weitere Ressourcen

- [OpenCitations Website](https://opencitations.net/)
- [OpenCitations API-Dokumentation](https://opencitations.net/index/api/v1)
- [Open Citation Identifier (OCI) Spezifikation](https://opencitations.net/oci)- [Open Citation Identifier (OCI) Spezifikation](https://opencitations.net/oci)- [Open Citation Identifier (OCI) Spezifikation](https://opencitations.net/oci)- [Open Citation Identifier (OCI) Spezifikation](https://opencitations.net/oci)Diese Dokumentation beschreibt die Integration von [OpenCitations](https://opencitations.net/) in das DeSci-Scholar-Projekt.

## Was ist OpenCitations?

OpenCitations ist eine unabhängige Infrastrukturorganisation, die offene bibliografische und Zitationsdaten bereitstellt. Sie bietet:

- Strukturierte, offene Zitationsdaten
- REST-APIs für den Zugriff auf Zitationsinformationen
- Eindeutige Identifikatoren für Zitationen (OCIs - Open Citation Identifiers)
- Semantische Web-Technologien für verknüpfte Daten

## Vorteile der Integration

Die Integration von OpenCitations in DeSci-Scholar bietet folgende Vorteile:

1. **Für die Zitationsanalyse**:
   - Umfassendere Zitationsdaten aus einer offenen, vertrauenswürdigen Quelle
   - Bessere Metriken für die Einflussberechnung
   - Genauere Zitationsnetzwerke

2. **Für die DOI-zu-NFT-Konvertierung**:
   - Anreicherung der NFT-Metadaten mit verifizierten Zitationsdaten
   - Eindeutige Identifikatoren (OCIs) für Zitationen
   - Verbesserte Vertrauenswürdigkeit der NFTs durch Verknüpfung mit einer anerkannten Quelle

3. **Allgemeine Vorteile**:
   - Unterstützung der Open-Science-Bewegung
   - Verbesserte Interoperabilität mit anderen wissenschaftlichen Systemen
   - Zugang zu strukturierten, offenen Zitationsdaten

## Konfiguration

Um die OpenCitations-Integration zu nutzen, müssen Sie folgende Umgebungsvariablen in Ihrer `.env`-Datei konfigurieren:

```
OPENCITATIONS_API_URL=https://opencitations.net/index/api/v1
OPENCITATIONS_ACCESS_TOKEN=your_access_token_here
```

Sie können einen API-Schlüssel von OpenCitations erhalten, indem Sie sich auf deren Website registrieren.

## Verwendung

### Zitationsanalyse

Die OpenCitations-Integration wird automatisch in der Zitationsanalyse verwendet. Der `CitationAnalysisAgent` ruft Daten von OpenCitations ab und kombiniert sie mit lokalen Daten, um umfassendere Zitationsmetriken zu erstellen.

```javascript
// Beispiel für die Verwendung der Zitationsanalyse mit OpenCitations
const agent = new CitationAnalysisAgent(options);
const result = await agent.analyzeCitations('10.1234/example-doi');

console.log(`Zitationen laut OpenCitations: ${result.openCitationsData.citationCount}`);
```

### NFT-Erstellung

Bei der Erstellung von NFTs für DOIs werden automatisch OpenCitations-Daten in die Metadaten aufgenommen, wenn verfügbar:

```javascript
// Beispiel für die Erstellung eines NFT mit OpenCitations-Daten
const agent = new CitationAnalysisAgent(options);
const result = await agent.createNFTForDOI('10.1234/example-doi');

console.log(`NFT erstellt: ${result.nftId}`);
console.log(`OpenCitations verifiziert: ${result.openCitationsVerified}`);
```

### Zitationsnetzwerke

Die Zitationsnetzwerke können mit OpenCitations-Daten angereichert werden:

```javascript
// Beispiel für die Erstellung eines Zitationsnetzwerks mit OpenCitations-Daten
const agent = new CitationAnalysisAgent(options);
const result = await agent.createCitationNetwork('10.1234/example-doi', {
  depth: 2,
  maxNodes: 100,
  useOpenCitations: true
});

console.log(`Netzwerk erstellt mit ${result.network.nodes.length} Knoten`);
```

## Datenbankschema

Die OpenCitations-Integration fügt folgende Spalten zu den Datenbanktabellen hinzu:

- `citation_relationships.oci`: Speichert den Open Citation Identifier
- `publication_metrics.opencitations_verified`: Gibt an, ob die Metriken durch OpenCitations verifiziert wurden
- `doi_nft_mapping.opencitations_verified`: Gibt an, ob das NFT mit OpenCitations-Daten angereichert wurde
- `citation_nft_links.oci`: Speichert den Open Citation Identifier für die Verknüpfung

Außerdem wird eine neue Tabelle `publication_external_data` erstellt, um externe Daten zu Publikationen zu speichern.

## Technische Details

Die Integration verwendet den `OpenCitationsService`, der folgende Hauptfunktionen bietet:

- `getCitations(doi)`: Holt eingehende Zitationen für eine DOI
- `getReferences(doi)`: Holt ausgehende Zitationen (Referenzen) für eine DOI
- `getCitationCount(doi)`: Holt die Anzahl der Zitationen für eine DOI
- `getMetadata(doi)`: Holt Metadaten für eine DOI
- `getAllDataForDOI(doi)`: Holt alle verfügbaren Daten für eine DOI
- `updateCitationDataForDOI(doi, databaseService)`: Aktualisiert die Zitationsdaten in der Datenbank

## Fehlerbehebung

Wenn Sie Probleme mit der OpenCitations-Integration haben, prüfen Sie Folgendes:

1. Stellen Sie sicher, dass Ihre API-Schlüssel korrekt konfiguriert sind
2. Prüfen Sie, ob die OpenCitations-API erreichbar ist
3. Überprüfen Sie die Logs auf Fehler im Zusammenhang mit OpenCitations
4. Stellen Sie sicher, dass die Datenbankmigration ausgeführt wurde

## Weitere Ressourcen

- [OpenCitations Website](https://opencitations.net/)
- [OpenCitations API-Dokumentation](https://opencitations.net/index/api/v1)
- [Open Citation Identifier (OCI) Spezifikation](https://opencitations.net/oci)- [Open Citation Identifier (OCI) Spezifikation](https://opencitations.net/oci)- [OpenCitations API-Dokumentation](https://opencitations.net/index/api/v1)
- [Open Citation Identifier (OCI) Spezifikation](https://opencitations.net/oci)- [Open Citation Identifier (OCI) Spezifikation](https://opencitations.net/oci)Diese Dokumentation beschreibt die Integration von [OpenCitations](https://opencitations.net/) in das DeSci-Scholar-Projekt.

## Was ist OpenCitations?

OpenCitations ist eine unabhängige Infrastrukturorganisation, die offene bibliografische und Zitationsdaten bereitstellt. Sie bietet:

- Strukturierte, offene Zitationsdaten
- REST-APIs für den Zugriff auf Zitationsinformationen
- Eindeutige Identifikatoren für Zitationen (OCIs - Open Citation Identifiers)
- Semantische Web-Technologien für verknüpfte Daten

## Vorteile der Integration

Die Integration von OpenCitations in DeSci-Scholar bietet folgende Vorteile:

1. **Für die Zitationsanalyse**:
   - Umfassendere Zitationsdaten aus einer offenen, vertrauenswürdigen Quelle
   - Bessere Metriken für die Einflussberechnung
   - Genauere Zitationsnetzwerke

2. **Für die DOI-zu-NFT-Konvertierung**:
   - Anreicherung der NFT-Metadaten mit verifizierten Zitationsdaten
   - Eindeutige Identifikatoren (OCIs) für Zitationen
   - Verbesserte Vertrauenswürdigkeit der NFTs durch Verknüpfung mit einer anerkannten Quelle

3. **Allgemeine Vorteile**:
   - Unterstützung der Open-Science-Bewegung
   - Verbesserte Interoperabilität mit anderen wissenschaftlichen Systemen
   - Zugang zu strukturierten, offenen Zitationsdaten

## Konfiguration

Um die OpenCitations-Integration zu nutzen, müssen Sie folgende Umgebungsvariablen in Ihrer `.env`-Datei konfigurieren:

```
OPENCITATIONS_API_URL=https://opencitations.net/index/api/v1
OPENCITATIONS_ACCESS_TOKEN=your_access_token_here
```

Sie können einen API-Schlüssel von OpenCitations erhalten, indem Sie sich auf deren Website registrieren.

## Verwendung

### Zitationsanalyse

Die OpenCitations-Integration wird automatisch in der Zitationsanalyse verwendet. Der `CitationAnalysisAgent` ruft Daten von OpenCitations ab und kombiniert sie mit lokalen Daten, um umfassendere Zitationsmetriken zu erstellen.

```javascript
// Beispiel für die Verwendung der Zitationsanalyse mit OpenCitations
const agent = new CitationAnalysisAgent(options);
const result = await agent.analyzeCitations('10.1234/example-doi');

console.log(`Zitationen laut OpenCitations: ${result.openCitationsData.citationCount}`);
```

### NFT-Erstellung

Bei der Erstellung von NFTs für DOIs werden automatisch OpenCitations-Daten in die Metadaten aufgenommen, wenn verfügbar:

```javascript
// Beispiel für die Erstellung eines NFT mit OpenCitations-Daten
const agent = new CitationAnalysisAgent(options);
const result = await agent.createNFTForDOI('10.1234/example-doi');

console.log(`NFT erstellt: ${result.nftId}`);
console.log(`OpenCitations verifiziert: ${result.openCitationsVerified}`);
```

### Zitationsnetzwerke

Die Zitationsnetzwerke können mit OpenCitations-Daten angereichert werden:

```javascript
// Beispiel für die Erstellung eines Zitationsnetzwerks mit OpenCitations-Daten
const agent = new CitationAnalysisAgent(options);
const result = await agent.createCitationNetwork('10.1234/example-doi', {
  depth: 2,
  maxNodes: 100,
  useOpenCitations: true
});

console.log(`Netzwerk erstellt mit ${result.network.nodes.length} Knoten`);
```

## Datenbankschema

Die OpenCitations-Integration fügt folgende Spalten zu den Datenbanktabellen hinzu:

- `citation_relationships.oci`: Speichert den Open Citation Identifier
- `publication_metrics.opencitations_verified`: Gibt an, ob die Metriken durch OpenCitations verifiziert wurden
- `doi_nft_mapping.opencitations_verified`: Gibt an, ob das NFT mit OpenCitations-Daten angereichert wurde
- `citation_nft_links.oci`: Speichert den Open Citation Identifier für die Verknüpfung

Außerdem wird eine neue Tabelle `publication_external_data` erstellt, um externe Daten zu Publikationen zu speichern.

## Technische Details

Die Integration verwendet den `OpenCitationsService`, der folgende Hauptfunktionen bietet:

- `getCitations(doi)`: Holt eingehende Zitationen für eine DOI
- `getReferences(doi)`: Holt ausgehende Zitationen (Referenzen) für eine DOI
- `getCitationCount(doi)`: Holt die Anzahl der Zitationen für eine DOI
- `getMetadata(doi)`: Holt Metadaten für eine DOI
- `getAllDataForDOI(doi)`: Holt alle verfügbaren Daten für eine DOI
- `updateCitationDataForDOI(doi, databaseService)`: Aktualisiert die Zitationsdaten in der Datenbank

## Fehlerbehebung

Wenn Sie Probleme mit der OpenCitations-Integration haben, prüfen Sie Folgendes:

1. Stellen Sie sicher, dass Ihre API-Schlüssel korrekt konfiguriert sind
2. Prüfen Sie, ob die OpenCitations-API erreichbar ist
3. Überprüfen Sie die Logs auf Fehler im Zusammenhang mit OpenCitations
4. Stellen Sie sicher, dass die Datenbankmigration ausgeführt wurde

## Weitere Ressourcen

- [OpenCitations Website](https://opencitations.net/)
- [OpenCitations API-Dokumentation](https://opencitations.net/index/api/v1)
- [Open Citation Identifier (OCI) Spezifikation](https://opencitations.net/oci)- [Open Citation Identifier (OCI) Spezifikation](https://opencitations.net/oci)- [Open Citation Identifier (OCI) Spezifikation](https://opencitations.net/oci)- [Open Citation Identifier (OCI) Spezifikation](https://opencitations.net/oci)- [OpenCitations API-Dokumentation](https://opencitations.net/index/api/v1)
- [Open Citation Identifier (OCI) Spezifikation](https://opencitations.net/oci)- [Open Citation Identifier (OCI) Spezifikation](https://opencitations.net/oci)Diese Dokumentation beschreibt die Integration von [OpenCitations](https://opencitations.net/) in das DeSci-Scholar-Projekt.

## Was ist OpenCitations?

OpenCitations ist eine unabhängige Infrastrukturorganisation, die offene bibliografische und Zitationsdaten bereitstellt. Sie bietet:

- Strukturierte, offene Zitationsdaten
- REST-APIs für den Zugriff auf Zitationsinformationen
- Eindeutige Identifikatoren für Zitationen (OCIs - Open Citation Identifiers)
- Semantische Web-Technologien für verknüpfte Daten

## Vorteile der Integration

Die Integration von OpenCitations in DeSci-Scholar bietet folgende Vorteile:

1. **Für die Zitationsanalyse**:
   - Umfassendere Zitationsdaten aus einer offenen, vertrauenswürdigen Quelle
   - Bessere Metriken für die Einflussberechnung
   - Genauere Zitationsnetzwerke

2. **Für die DOI-zu-NFT-Konvertierung**:
   - Anreicherung der NFT-Metadaten mit verifizierten Zitationsdaten
   - Eindeutige Identifikatoren (OCIs) für Zitationen
   - Verbesserte Vertrauenswürdigkeit der NFTs durch Verknüpfung mit einer anerkannten Quelle

3. **Allgemeine Vorteile**:
   - Unterstützung der Open-Science-Bewegung
   - Verbesserte Interoperabilität mit anderen wissenschaftlichen Systemen
   - Zugang zu strukturierten, offenen Zitationsdaten

## Konfiguration

Um die OpenCitations-Integration zu nutzen, müssen Sie folgende Umgebungsvariablen in Ihrer `.env`-Datei konfigurieren:

```
OPENCITATIONS_API_URL=https://opencitations.net/index/api/v1
OPENCITATIONS_ACCESS_TOKEN=your_access_token_here
```

Sie können einen API-Schlüssel von OpenCitations erhalten, indem Sie sich auf deren Website registrieren.

## Verwendung

### Zitationsanalyse

Die OpenCitations-Integration wird automatisch in der Zitationsanalyse verwendet. Der `CitationAnalysisAgent` ruft Daten von OpenCitations ab und kombiniert sie mit lokalen Daten, um umfassendere Zitationsmetriken zu erstellen.

```javascript
// Beispiel für die Verwendung der Zitationsanalyse mit OpenCitations
const agent = new CitationAnalysisAgent(options);
const result = await agent.analyzeCitations('10.1234/example-doi');

console.log(`Zitationen laut OpenCitations: ${result.openCitationsData.citationCount}`);
```

### NFT-Erstellung

Bei der Erstellung von NFTs für DOIs werden automatisch OpenCitations-Daten in die Metadaten aufgenommen, wenn verfügbar:

```javascript
// Beispiel für die Erstellung eines NFT mit OpenCitations-Daten
const agent = new CitationAnalysisAgent(options);
const result = await agent.createNFTForDOI('10.1234/example-doi');

console.log(`NFT erstellt: ${result.nftId}`);
console.log(`OpenCitations verifiziert: ${result.openCitationsVerified}`);
```

### Zitationsnetzwerke

Die Zitationsnetzwerke können mit OpenCitations-Daten angereichert werden:

```javascript
// Beispiel für die Erstellung eines Zitationsnetzwerks mit OpenCitations-Daten
const agent = new CitationAnalysisAgent(options);
const result = await agent.createCitationNetwork('10.1234/example-doi', {
  depth: 2,
  maxNodes: 100,
  useOpenCitations: true
});

console.log(`Netzwerk erstellt mit ${result.network.nodes.length} Knoten`);
```

## Datenbankschema

Die OpenCitations-Integration fügt folgende Spalten zu den Datenbanktabellen hinzu:

- `citation_relationships.oci`: Speichert den Open Citation Identifier
- `publication_metrics.opencitations_verified`: Gibt an, ob die Metriken durch OpenCitations verifiziert wurden
- `doi_nft_mapping.opencitations_verified`: Gibt an, ob das NFT mit OpenCitations-Daten angereichert wurde
- `citation_nft_links.oci`: Speichert den Open Citation Identifier für die Verknüpfung

Außerdem wird eine neue Tabelle `publication_external_data` erstellt, um externe Daten zu Publikationen zu speichern.

## Technische Details

Die Integration verwendet den `OpenCitationsService`, der folgende Hauptfunktionen bietet:

- `getCitations(doi)`: Holt eingehende Zitationen für eine DOI
- `getReferences(doi)`: Holt ausgehende Zitationen (Referenzen) für eine DOI
- `getCitationCount(doi)`: Holt die Anzahl der Zitationen für eine DOI
- `getMetadata(doi)`: Holt Metadaten für eine DOI
- `getAllDataForDOI(doi)`: Holt alle verfügbaren Daten für eine DOI
- `updateCitationDataForDOI(doi, databaseService)`: Aktualisiert die Zitationsdaten in der Datenbank

## Fehlerbehebung

Wenn Sie Probleme mit der OpenCitations-Integration haben, prüfen Sie Folgendes:

1. Stellen Sie sicher, dass Ihre API-Schlüssel korrekt konfiguriert sind
2. Prüfen Sie, ob die OpenCitations-API erreichbar ist
3. Überprüfen Sie die Logs auf Fehler im Zusammenhang mit OpenCitations
4. Stellen Sie sicher, dass die Datenbankmigration ausgeführt wurde

## Weitere Ressourcen

- [OpenCitations Website](https://opencitations.net/)
- [OpenCitations API-Dokumentation](https://opencitations.net/index/api/v1)
- [Open Citation Identifier (OCI) Spezifikation](https://opencitations.net/oci)- [Open Citation Identifier (OCI) Spezifikation](https://opencitations.net/oci)