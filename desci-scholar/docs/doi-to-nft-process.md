# DOI-zu-NFT-Konvertierungsprozess in DeSci-Scholar

Dieses Dokument beschreibt den detaillierten technischen Prozess der Konvertierung von Digital Object Identifiers (DOIs) zu Non-Fungible Tokens (NFTs) in DeSci-Scholar.

## Übersicht

Der DOI-zu-NFT-Konvertierungsprozess ist die Kernfunktionalität von DeSci-Scholar und umfasst mehrere Schritte, von der Eingabe eines DOI bis zur Erstellung eines verifizierbaren NFT auf der Blockchain.

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│  DOI-Eingabe │────▶│ Metadaten-  │────▶│ Dezentrale  │────▶│  NFT-       │
│  & Validierung│     │ Anreicherung│     │ Speicherung │     │  Erstellung │
└─────────────┘     └─────────────┘     └─────────────┘     └─────────────┘
                                                                    │
┌─────────────┐     ┌─────────────┐     ┌─────────────┐            │
│ Verifizierung│◀────│ Blockchain- │◀────│ Zertifikat- │◀───────────┘
│ & Nachweis   │     │ Registrierung│     │ Generierung │
└─────────────┘     └─────────────┘     └─────────────┘
```

## Detaillierter Prozess

### 1. DOI-Eingabe und Validierung

#### Eingabemethoden
- Manuelle Eingabe eines DOI
- Upload einer Publikation mit DOI-Extraktion
- API-basierte Übermittlung

#### Validierungsprozess
- Überprüfung des DOI-Formats (10.xxxx/xxxxx)
- Abfrage des DOI bei offiziellen Registrierungsstellen (Crossref, DataCite)
- Extraktion grundlegender Metadaten:
  - Titel
  - Autoren
  - Veröffentlichungsdatum
  - Zeitschrift/Verlag
  - Abstrakt
  - Lizenzinformationen

#### Implementierung
```javascript
async function validateDOI(doi) {
  try {
    // Überprüfe DOI-Format
    if (!isValidDOIFormat(doi)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Abfrage bei Crossref
    const crossrefData = await fetchFromCrossref(doi);
    if (crossrefData.success) {
      return {
        success: true,
        source: 'crossref',
        metadata: extractMetadata(crossrefData)
      };
    }
    
    // Abfrage bei DataCite, falls Crossref keine Ergebnisse liefert
    const dataciteData = await fetchFromDatacite(doi);
    if (dataciteData.success) {
      return {
        success: true,
        source: 'datacite',
        metadata: extractMetadata(dataciteData)
      };
    }
    
    throw new Error('DOI nicht gefunden');
  } catch (error) {
    logger.error(`DOI-Validierungsfehler: ${error.message}`, { doi });
    return {
      success: false,
      error: error.message
    };
  }
}
```

### 2. Metadaten-Anreicherung

#### Datenquellen
- **OpenCitations**: Zitationsdaten und -beziehungen
- **ORCID**: Autorenidentifikatoren und -profile
- **Semantic Scholar**: Zusätzliche akademische Metadaten
- **Unpaywall**: Open-Access-Verfügbarkeit

#### Anreicherungsprozess
- Abfrage von Zitationsdaten (eingehend und ausgehend)
- Verknüpfung mit Autorenidentifikatoren
- Extraktion von Schlüsselwörtern und Themen
- Identifikation verwandter Arbeiten
- Ermittlung von Impact-Metriken

#### Implementierung
```javascript
async function enrichMetadata(doi, baseMetadata) {
  try {
    // Anreicherung mit OpenCitations-Daten
    const citationData = await fetchOpenCitationsData(doi);
    
    // Anreicherung mit ORCID-Daten für Autoren
    const authorsWithORCID = await enrichAuthorsWithORCID(baseMetadata.authors);
    
    // Anreicherung mit Semantic Scholar-Daten
    const semanticScholarData = await fetchSemanticScholarData(doi);
    
    // Anreicherung mit Unpaywall-Daten für Open-Access-Status
    const openAccessData = await fetchUnpaywallData(doi);
    
    // Kombiniere alle Metadaten
    const enrichedMetadata = {
      ...baseMetadata,
      citations: {
        incoming: citationData.citations || [],
        outgoing: citationData.references || [],
        count: citationData.citationCount || 0
      },
      authors: authorsWithORCID,
      topics: semanticScholarData.topics || [],
      openAccess: openAccessData.isOpenAccess || false,
      openAccessUrl: openAccessData.bestOpenAccessUrl || null,
      enrichmentTimestamp: new Date().toISOString()
    };
    
    return {
      success: true,
      metadata: enrichedMetadata
    };
  } catch (error) {
    logger.error(`Metadaten-Anreicherungsfehler: ${error.message}`, { doi });
    // Gib Basis-Metadaten zurück, auch wenn Anreicherung fehlschlägt
    return {
      success: false,
      metadata: baseMetadata,
      error: error.message
    };
  }
}
```

### 3. Dezentrale Speicherung

#### Speicherstrategien
- **IPFS**: Primäre Speicherung für Metadaten und kleinere Dokumente
- **BitTorrent**: Speicherung für größere Datensätze und Zusatzmaterialien
- **Redundante Speicherung**: Mehrfache Pinning-Dienste für IPFS

#### Speicherprozess
- Konvertierung der Metadaten in ein standardisiertes JSON-Format
- Erstellung eines Metadaten-Pakets mit allen angereicherten Informationen
- Speicherung des Metadaten-Pakets in IPFS
- Bei verfügbarem Volltext: Speicherung in IPFS oder BitTorrent
- Generierung persistenter Links und Content-Identifikatoren

#### Implementierung
```javascript
async function storeInDecentralizedStorage(doi, enrichedMetadata, fullTextContent = null) {
  try {
    // Erstelle standardisiertes Metadaten-Paket
    const metadataPackage = createStandardizedMetadataPackage(doi, enrichedMetadata);
    
    // Speichere Metadaten in IPFS
    const metadataIPFSResult = await storeInIPFS(
      JSON.stringify(metadataPackage),
      `${doi.replace('/', '_')}_metadata.json`
    );
    
    let fullTextResult = null;
    
    // Speichere Volltext, falls verfügbar
    if (fullTextContent) {
      // Wähle Speichermethode basierend auf Dateigröße
      if (fullTextContent.length > LARGE_FILE_THRESHOLD) {
        fullTextResult = await storeInBitTorrent(
          fullTextContent,
          `${doi.replace('/', '_')}_fulltext.pdf`
        );
      } else {
        fullTextResult = await storeInIPFS(
          fullTextContent,
          `${doi.replace('/', '_')}_fulltext.pdf`
        );
      }
    }
    
    // Pinne IPFS-Inhalte bei mehreren Diensten für Redundanz
    if (metadataIPFSResult.success) {
      await pinIPFSContent(metadataIPFSResult.cid);
    }
    
    if (fullTextResult && fullTextResult.protocol === 'ipfs' && fullTextResult.success) {
      await pinIPFSContent(fullTextResult.cid);
    }
    
    return {
      success: true,
      metadata: {
        protocol: 'ipfs',
        cid: metadataIPFSResult.cid,
        uri: `ipfs://${metadataIPFSResult.cid}`
      },
      fullText: fullTextResult ? {
        protocol: fullTextResult.protocol,
        identifier: fullTextResult.protocol === 'ipfs' ? fullTextResult.cid : fullTextResult.magnetLink,
        uri: fullTextResult.protocol === 'ipfs' ? 
          `ipfs://${fullTextResult.cid}` : 
          fullTextResult.magnetLink
      } : null
    };
  } catch (error) {
    logger.error(`Dezentrale Speicherungsfehler: ${error.message}`, { doi });
    return {
      success: false,
      error: error.message
    };
  }
}
```

### 4. NFT-Erstellung

#### NFT-Metadaten
- DOI und grundlegende bibliografische Informationen
- Verweise auf dezentral gespeicherte Metadaten und Inhalte
- Zitationsinformationen und Impact-Metriken
- Provenienz- und Authentizitätsnachweise
- Lizenz- und Urheberrechtsinformationen

#### NFT-Erstellungsprozess
- Generierung eines eindeutigen Token-Identifikators
- Erstellung der NFT-Metadaten nach ERC-721-Standard
- Einbettung kryptografischer Nachweise
- Prägung des NFT auf der Blockchain
- Verknüpfung mit dem Wallet des Urhebers oder der Institution

#### Implementierung
```javascript
async function createNFT(doi, storageResult, enrichedMetadata, ownerAddress) {
  try {
    // Erstelle NFT-Metadaten nach ERC-721-Standard
    const nftMetadata = {
      name: `${enrichedMetadata.title} (${doi})`,
      description: enrichedMetadata.abstract || 'Keine Zusammenfassung verfügbar',
      image: generateThumbnailUrl(doi, enrichedMetadata),
      external_url: `https://doi.org/${doi}`,
      attributes: [
        { trait_type: 'Publication Type', value: enrichedMetadata.type || 'Article' },
        { trait_type: 'Publication Date', value: enrichedMetadata.published },
        { trait_type: 'Citation Count', value: enrichedMetadata.citations.count },
        { trait_type: 'Open Access', value: enrichedMetadata.openAccess ? 'Yes' : 'No' },
        { trait_type: 'Authors Count', value: enrichedMetadata.authors.length }
      ],
      properties: {
        doi: doi,
        authors: enrichedMetadata.authors.map(author => ({
          name: author.name,
          orcid: author.orcid || null
        })),
        journal: enrichedMetadata.journal,
        publisher: enrichedMetadata.publisher,
        license: enrichedMetadata.license,
        metadata_uri: storageResult.metadata.uri,
        fulltext_uri: storageResult.fullText ? storageResult.fullText.uri : null,
        citations: {
          count: enrichedMetadata.citations.count,
          data_uri: `ipfs://${storageResult.metadata.cid}#citations`
        },
        timestamp: new Date().toISOString(),
        verification: {
          method: 'DOI-Verification',
          timestamp: new Date().toISOString(),
          verifier: 'DeSci-Scholar'
        }
      }
    };
    
    // Speichere NFT-Metadaten in IPFS
    const nftMetadataIPFS = await storeInIPFS(
      JSON.stringify(nftMetadata),
      `${doi.replace('/', '_')}_nft_metadata.json`
    );
    
    // Präge NFT auf der Blockchain
    const mintResult = await mintNFTOnBlockchain(
      ownerAddress,
      nftMetadataIPFS.cid,
      doi
    );
    
    return {
      success: true,
      tokenId: mintResult.tokenId,
      transactionHash: mintResult.transactionHash,
      ownerAddress: ownerAddress,
      metadataUri: `ipfs://${nftMetadataIPFS.cid}`,
      blockchainExplorerUrl: `${BLOCKCHAIN_EXPLORER_URL}/token/${NFT_CONTRACT_ADDRESS}?a=${mintResult.tokenId}`
    };
  } catch (error) {
    logger.error(`NFT-Erstellungsfehler: ${error.message}`, { doi });
    return {
      success: false,
      error: error.message
    };
  }
}
```

### 5. Zertifikat-Generierung

#### Zertifikatsinhalte
- Verifizierbare Informationen zur Publikation
- Blockchain-Transaktionsdetails
- Zeitstempel und kryptografische Nachweise
- QR-Code für schnelle Verifizierung
- Visuelle Darstellung für einfache Nutzung

#### Generierungsprozess
- Erstellung eines standardisierten Zertifikatsformats
- Einbettung kryptografischer Nachweise
- Generierung eines QR-Codes für die Verifizierungs-URL
- Erstellung einer PDF- oder HTML-Version des Zertifikats

#### Implementierung
```javascript
async function generateCertificate(doi, nftResult, enrichedMetadata) {
  try {
    // Erstelle Verifizierungs-URL
    const verificationUrl = `https://desci-scholar.org/verify/${nftResult.tokenId}`;
    
    // Generiere QR-Code
    const qrCodeImage = await generateQRCode(verificationUrl);
    
    // Erstelle Zertifikatsdaten
    const certificateData = {
      title: 'DeSci-Scholar Authentizitätszertifikat',
      publicationTitle: enrichedMetadata.title,
      doi: doi,
      authors: enrichedMetadata.authors.map(a => a.name).join(', '),
      publicationDate: enrichedMetadata.published,
      verificationTimestamp: new Date().toISOString(),
      blockchain: {
        network: BLOCKCHAIN_NETWORK,
        contractAddress: NFT_CONTRACT_ADDRESS,
        tokenId: nftResult.tokenId,
        transactionHash: nftResult.transactionHash
      },
      verificationUrl: verificationUrl,
      qrCodeImage: qrCodeImage,
      metadataUri: nftResult.metadataUri
    };
    
    // Generiere PDF-Zertifikat
    const pdfCertificate = await generatePDFCertificate(certificateData);
    
    // Speichere Zertifikat in IPFS
    const certificateIPFS = await storeInIPFS(
      pdfCertificate,
      `${doi.replace('/', '_')}_certificate.pdf`
    );
    
    return {
      success: true,
      certificateUri: `ipfs://${certificateIPFS.cid}`,
      verificationUrl: verificationUrl,
      pdfBuffer: pdfCertificate
    };
  } catch (error) {
    logger.error(`Zertifikat-Generierungsfehler: ${error.message}`, { doi });
    return {
      success: false,
      error: error.message
    };
  }
}
```

### 6. Blockchain-Registrierung

#### Registrierungsdaten
- Token-ID und Metadaten-URI
- Zeitstempel und Verifizierungsinformationen
- Zitationsbeziehungen (falls verfügbar)
- Urheberrechts- und Lizenzinformationen

#### Registrierungsprozess
- Interaktion mit dem Smart Contract
- Aufzeichnung der Metadaten-URI und des DOI
- Verknüpfung mit dem Wallet des Urhebers
- Aufzeichnung von Zeitstempel und Verifizierungsinformationen

#### Implementierung
```javascript
async function registerOnBlockchain(doi, nftResult, certificateResult) {
  try {
    // Bereite Registrierungsdaten vor
    const registrationData = {
      doi: doi,
      tokenId: nftResult.tokenId,
      metadataUri: nftResult.metadataUri,
      certificateUri: certificateResult.certificateUri,
      timestamp: Math.floor(Date.now() / 1000),
      ownerAddress: nftResult.ownerAddress
    };
    
    // Registriere in der Blockchain über Smart Contract
    const registrationResult = await doiRegistryContract.registerDOI(
      doi,
      nftResult.tokenId,
      nftResult.metadataUri,
      certificateResult.certificateUri
    );
    
    // Speichere Registrierungsinformationen in der Datenbank
    await databaseService.saveRegistration({
      doi: doi,
      tokenId: nftResult.tokenId,
      transactionHash: registrationResult.transactionHash,
      blockNumber: registrationResult.blockNumber,
      timestamp: registrationData.timestamp,
      ownerAddress: nftResult.ownerAddress
    });
    
    return {
      success: true,
      transactionHash: registrationResult.transactionHash,
      blockNumber: registrationResult.blockNumber,
      registrationTimestamp: registrationData.timestamp
    };
  } catch (error) {
    logger.error(`Blockchain-Registrierungsfehler: ${error.message}`, { doi });
    return {
      success: false,
      error: error.message
    };
  }
}
```

### 7. Verifizierung und Nachweis

#### Verifizierungsmethoden
- Öffentliche Verifizierungs-Webseite
- API-Endpunkt für automatisierte Verifizierung
- Blockchain-Explorer-Integration

#### Verifizierungsprozess
- Eingabe eines DOI oder einer Token-ID
- Abfrage der Blockchain-Registrierung
- Überprüfung der Metadaten und des Zertifikats
- Anzeige des Verifizierungsergebnisses mit Details

#### Implementierung
```javascript
async function verifyDOINFT(identifier) {
  try {
    let doi, tokenId;
    
    // Bestimme, ob die Eingabe ein DOI oder eine Token-ID ist
    if (isValidDOIFormat(identifier)) {
      doi = identifier;
      // Suche Token-ID für diesen DOI
      const registrationData = await databaseService.getRegistrationByDOI(doi);
      if (!registrationData) {
        throw new Error('Keine Registrierung für diesen DOI gefunden');
      }
      tokenId = registrationData.tokenId;
    } else if (isValidTokenId(identifier)) {
      tokenId = identifier;
      // Suche DOI für diese Token-ID
      const registrationData = await databaseService.getRegistrationByTokenId(tokenId);
      if (!registrationData) {
        throw new Error('Keine Registrierung für diese Token-ID gefunden');
      }
      doi = registrationData.doi;
    } else {
      throw new Error('Ungültiges Format. Bitte geben Sie einen DOI oder eine Token-ID ein');
    }
    
    // Hole Blockchain-Daten
    const onChainData = await doiRegistryContract.getDOIData(tokenId);
    
    // Hole NFT-Metadaten
    const nftMetadata = await fetchIPFSJson(onChainData.metadataUri.replace('ipfs://', ''));
    
    // Hole Zertifikat
    const certificateUri = onChainData.certificateUri;
    
    // Überprüfe DOI bei offiziellen Quellen
    const doiValidation = await validateDOI(doi);
    
    return {
      success: true,
      verified: onChainData.doi === doi && doiValidation.success,
      doi: doi,
      tokenId: tokenId,
      ownerAddress: onChainData.owner,
      registrationTimestamp: new Date(onChainData.timestamp * 1000).toISOString(),
      transactionHash: onChainData.transactionHash,
      blockNumber: onChainData.blockNumber,
      metadataUri: onChainData.metadataUri,
      certificateUri: certificateUri,
      publicationData: {
        title: nftMetadata.name,
        authors: nftMetadata.properties.authors,
        publicationDate: nftMetadata.attributes.find(a => a.trait_type === 'Publication Date')?.value,
        journal: nftMetadata.properties.journal,
        publisher: nftMetadata.properties.publisher
      }
    };
  } catch (error) {
    logger.error(`Verifizierungsfehler: ${error.message}`, { identifier });
    return {
      success: false,
      error: error.message
    };
  }
}
```

## Gesamtprozess-Integration

Der vollständige DOI-zu-NFT-Konvertierungsprozess wird durch einen orchestrierten Workflow zusammengeführt:

```javascript
async function convertDOItoNFT(doi, ownerAddress) {
  try {
    // 1. DOI validieren
    logger.info(`Starte DOI-zu-NFT-Konvertierung für ${doi}`);
    const validationResult = await validateDOI(doi);
    if (!validationResult.success) {
      throw new Error(`DOI-Validierung fehlgeschlagen: ${validationResult.error}`);
    }
    
    // 2. Metadaten anreichern
    const enrichmentResult = await enrichMetadata(doi, validationResult.metadata);
    const enrichedMetadata = enrichmentResult.success ? 
      enrichmentResult.metadata : validationResult.metadata;
    
    // 3. In dezentraler Speicherung ablegen
    const storageResult = await storeInDecentralizedStorage(doi, enrichedMetadata);
    if (!storageResult.success) {
      throw new Error(`Dezentrale Speicherung fehlgeschlagen: ${storageResult.error}`);
    }
    
    // 4. NFT erstellen
    const nftResult = await createNFT(doi, storageResult, enrichedMetadata, ownerAddress);
    if (!nftResult.success) {
      throw new Error(`NFT-Erstellung fehlgeschlagen: ${nftResult.error}`);
    }
    
    // 5. Zertifikat generieren
    const certificateResult = await generateCertificate(doi, nftResult, enrichedMetadata);
    if (!certificateResult.success) {
      throw new Error(`Zertifikat-Generierung fehlgeschlagen: ${certificateResult.error}`);
    }
    
    // 6. In Blockchain registrieren
    const registrationResult = await registerOnBlockchain(doi, nftResult, certificateResult);
    if (!registrationResult.success) {
      throw new Error(`Blockchain-Registrierung fehlgeschlagen: ${registrationResult.error}`);
    }
    
    // 7. Erfolgreiche Konvertierung zurückgeben
    return {
      success: true,
      doi: doi,
      tokenId: nftResult.tokenId,
      ownerAddress: ownerAddress,
      transactionHash: registrationResult.transactionHash,
      metadataUri: nftResult.metadataUri,
      certificateUri: certificateResult.certificateUri,
      verificationUrl: certificateResult.verificationUrl
    };
  } catch (error) {
    logger.error(`DOI-zu-NFT-Konvertierungsfehler: ${error.message}`, { doi });
    return {
      success: false,
      doi: doi,
      error: error.message
    };
  }
}
```

## Sicherheits- und Datenschutzaspekte

### Sicherheitsmaßnahmen
- Kryptografische Verifizierung aller Transaktionen
- Mehrfache Validierung von DOIs und Metadaten
- Redundante Speicherung kritischer Daten
- Regelmäßige Sicherheitsaudits der Smart Contracts

### Datenschutzaspekte
- Transparente Offenlegung aller gespeicherten Daten
- Einhaltung relevanter Datenschutzbestimmungen
- Optionale Anonymisierung sensibler Informationen
- Klare Berechtigungsstrukturen für Datenänderungen

## Fazit

Der DOI-zu-NFT-Konvertierungsprozess in DeSci-Scholar bietet eine robuste, transparente und dezentralisierte Methode zur Authentifizierung und Verifizierung wissenschaftlicher Publikationen. Durch die Kombination etablierter wissenschaftlicher Identifikatoren (DOIs) mit moderner Blockchain-Technologie schafft DeSci-Scholar eine Brücke zwischen traditionellen wissenschaftlichen Infrastrukturen und innovativen dezentralen Systemen.# DOI-zu-NFT-Konvertierungsprozess in DeSci-Scholar

Dieses Dokument beschreibt den detaillierten technischen Prozess der Konvertierung von Digital Object Identifiers (DOIs) zu Non-Fungible Tokens (NFTs) in DeSci-Scholar.

## Übersicht

Der DOI-zu-NFT-Konvertierungsprozess ist die Kernfunktionalität von DeSci-Scholar und umfasst mehrere Schritte, von der Eingabe eines DOI bis zur Erstellung eines verifizierbaren NFT auf der Blockchain.

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│  DOI-Eingabe │────▶│ Metadaten-  │────▶│ Dezentrale  │────▶│  NFT-       │
│  & Validierung│     │ Anreicherung│     │ Speicherung │     │  Erstellung │
└─────────────┘     └─────────────┘     └─────────────┘     └─────────────┘
                                                                    │
┌─────────────┐     ┌─────────────┐     ┌─────────────┐            │
│ Verifizierung│◀────│ Blockchain- │◀────│ Zertifikat- │◀───────────┘
│ & Nachweis   │     │ Registrierung│     │ Generierung │
└─────────────┘     └─────────────┘     └─────────────┘
```

## Detaillierter Prozess

### 1. DOI-Eingabe und Validierung

#### Eingabemethoden
- Manuelle Eingabe eines DOI
- Upload einer Publikation mit DOI-Extraktion
- API-basierte Übermittlung

#### Validierungsprozess
- Überprüfung des DOI-Formats (10.xxxx/xxxxx)
- Abfrage des DOI bei offiziellen Registrierungsstellen (Crossref, DataCite)
- Extraktion grundlegender Metadaten:
  - Titel
  - Autoren
  - Veröffentlichungsdatum
  - Zeitschrift/Verlag
  - Abstrakt
  - Lizenzinformationen

#### Implementierung
```javascript
async function validateDOI(doi) {
  try {
    // Überprüfe DOI-Format
    if (!isValidDOIFormat(doi)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Abfrage bei Crossref
    const crossrefData = await fetchFromCrossref(doi);
    if (crossrefData.success) {
      return {
        success: true,
        source: 'crossref',
        metadata: extractMetadata(crossrefData)
      };
    }
    
    // Abfrage bei DataCite, falls Crossref keine Ergebnisse liefert
    const dataciteData = await fetchFromDatacite(doi);
    if (dataciteData.success) {
      return {
        success: true,
        source: 'datacite',
        metadata: extractMetadata(dataciteData)
      };
    }
    
    throw new Error('DOI nicht gefunden');
  } catch (error) {
    logger.error(`DOI-Validierungsfehler: ${error.message}`, { doi });
    return {
      success: false,
      error: error.message
    };
  }
}
```

### 2. Metadaten-Anreicherung

#### Datenquellen
- **OpenCitations**: Zitationsdaten und -beziehungen
- **ORCID**: Autorenidentifikatoren und -profile
- **Semantic Scholar**: Zusätzliche akademische Metadaten
- **Unpaywall**: Open-Access-Verfügbarkeit

#### Anreicherungsprozess
- Abfrage von Zitationsdaten (eingehend und ausgehend)
- Verknüpfung mit Autorenidentifikatoren
- Extraktion von Schlüsselwörtern und Themen
- Identifikation verwandter Arbeiten
- Ermittlung von Impact-Metriken

#### Implementierung
```javascript
async function enrichMetadata(doi, baseMetadata) {
  try {
    // Anreicherung mit OpenCitations-Daten
    const citationData = await fetchOpenCitationsData(doi);
    
    // Anreicherung mit ORCID-Daten für Autoren
    const authorsWithORCID = await enrichAuthorsWithORCID(baseMetadata.authors);
    
    // Anreicherung mit Semantic Scholar-Daten
    const semanticScholarData = await fetchSemanticScholarData(doi);
    
    // Anreicherung mit Unpaywall-Daten für Open-Access-Status
    const openAccessData = await fetchUnpaywallData(doi);
    
    // Kombiniere alle Metadaten
    const enrichedMetadata = {
      ...baseMetadata,
      citations: {
        incoming: citationData.citations || [],
        outgoing: citationData.references || [],
        count: citationData.citationCount || 0
      },
      authors: authorsWithORCID,
      topics: semanticScholarData.topics || [],
      openAccess: openAccessData.isOpenAccess || false,
      openAccessUrl: openAccessData.bestOpenAccessUrl || null,
      enrichmentTimestamp: new Date().toISOString()
    };
    
    return {
      success: true,
      metadata: enrichedMetadata
    };
  } catch (error) {
    logger.error(`Metadaten-Anreicherungsfehler: ${error.message}`, { doi });
    // Gib Basis-Metadaten zurück, auch wenn Anreicherung fehlschlägt
    return {
      success: false,
      metadata: baseMetadata,
      error: error.message
    };
  }
}
```

### 3. Dezentrale Speicherung

#### Speicherstrategien
- **IPFS**: Primäre Speicherung für Metadaten und kleinere Dokumente
- **BitTorrent**: Speicherung für größere Datensätze und Zusatzmaterialien
- **Redundante Speicherung**: Mehrfache Pinning-Dienste für IPFS

#### Speicherprozess
- Konvertierung der Metadaten in ein standardisiertes JSON-Format
- Erstellung eines Metadaten-Pakets mit allen angereicherten Informationen
- Speicherung des Metadaten-Pakets in IPFS
- Bei verfügbarem Volltext: Speicherung in IPFS oder BitTorrent
- Generierung persistenter Links und Content-Identifikatoren

#### Implementierung
```javascript
async function storeInDecentralizedStorage(doi, enrichedMetadata, fullTextContent = null) {
  try {
    // Erstelle standardisiertes Metadaten-Paket
    const metadataPackage = createStandardizedMetadataPackage(doi, enrichedMetadata);
    
    // Speichere Metadaten in IPFS
    const metadataIPFSResult = await storeInIPFS(
      JSON.stringify(metadataPackage),
      `${doi.replace('/', '_')}_metadata.json`
    );
    
    let fullTextResult = null;
    
    // Speichere Volltext, falls verfügbar
    if (fullTextContent) {
      // Wähle Speichermethode basierend auf Dateigröße
      if (fullTextContent.length > LARGE_FILE_THRESHOLD) {
        fullTextResult = await storeInBitTorrent(
          fullTextContent,
          `${doi.replace('/', '_')}_fulltext.pdf`
        );
      } else {
        fullTextResult = await storeInIPFS(
          fullTextContent,
          `${doi.replace('/', '_')}_fulltext.pdf`
        );
      }
    }
    
    // Pinne IPFS-Inhalte bei mehreren Diensten für Redundanz
    if (metadataIPFSResult.success) {
      await pinIPFSContent(metadataIPFSResult.cid);
    }
    
    if (fullTextResult && fullTextResult.protocol === 'ipfs' && fullTextResult.success) {
      await pinIPFSContent(fullTextResult.cid);
    }
    
    return {
      success: true,
      metadata: {
        protocol: 'ipfs',
        cid: metadataIPFSResult.cid,
        uri: `ipfs://${metadataIPFSResult.cid}`
      },
      fullText: fullTextResult ? {
        protocol: fullTextResult.protocol,
        identifier: fullTextResult.protocol === 'ipfs' ? fullTextResult.cid : fullTextResult.magnetLink,
        uri: fullTextResult.protocol === 'ipfs' ? 
          `ipfs://${fullTextResult.cid}` : 
          fullTextResult.magnetLink
      } : null
    };
  } catch (error) {
    logger.error(`Dezentrale Speicherungsfehler: ${error.message}`, { doi });
    return {
      success: false,
      error: error.message
    };
  }
}
```

### 4. NFT-Erstellung

#### NFT-Metadaten
- DOI und grundlegende bibliografische Informationen
- Verweise auf dezentral gespeicherte Metadaten und Inhalte
- Zitationsinformationen und Impact-Metriken
- Provenienz- und Authentizitätsnachweise
- Lizenz- und Urheberrechtsinformationen

#### NFT-Erstellungsprozess
- Generierung eines eindeutigen Token-Identifikators
- Erstellung der NFT-Metadaten nach ERC-721-Standard
- Einbettung kryptografischer Nachweise
- Prägung des NFT auf der Blockchain
- Verknüpfung mit dem Wallet des Urhebers oder der Institution

#### Implementierung
```javascript
async function createNFT(doi, storageResult, enrichedMetadata, ownerAddress) {
  try {
    // Erstelle NFT-Metadaten nach ERC-721-Standard
    const nftMetadata = {
      name: `${enrichedMetadata.title} (${doi})`,
      description: enrichedMetadata.abstract || 'Keine Zusammenfassung verfügbar',
      image: generateThumbnailUrl(doi, enrichedMetadata),
      external_url: `https://doi.org/${doi}`,
      attributes: [
        { trait_type: 'Publication Type', value: enrichedMetadata.type || 'Article' },
        { trait_type: 'Publication Date', value: enrichedMetadata.published },
        { trait_type: 'Citation Count', value: enrichedMetadata.citations.count },
        { trait_type: 'Open Access', value: enrichedMetadata.openAccess ? 'Yes' : 'No' },
        { trait_type: 'Authors Count', value: enrichedMetadata.authors.length }
      ],
      properties: {
        doi: doi,
        authors: enrichedMetadata.authors.map(author => ({
          name: author.name,
          orcid: author.orcid || null
        })),
        journal: enrichedMetadata.journal,
        publisher: enrichedMetadata.publisher,
        license: enrichedMetadata.license,
        metadata_uri: storageResult.metadata.uri,
        fulltext_uri: storageResult.fullText ? storageResult.fullText.uri : null,
        citations: {
          count: enrichedMetadata.citations.count,
          data_uri: `ipfs://${storageResult.metadata.cid}#citations`
        },
        timestamp: new Date().toISOString(),
        verification: {
          method: 'DOI-Verification',
          timestamp: new Date().toISOString(),
          verifier: 'DeSci-Scholar'
        }
      }
    };
    
    // Speichere NFT-Metadaten in IPFS
    const nftMetadataIPFS = await storeInIPFS(
      JSON.stringify(nftMetadata),
      `${doi.replace('/', '_')}_nft_metadata.json`
    );
    
    // Präge NFT auf der Blockchain
    const mintResult = await mintNFTOnBlockchain(
      ownerAddress,
      nftMetadataIPFS.cid,
      doi
    );
    
    return {
      success: true,
      tokenId: mintResult.tokenId,
      transactionHash: mintResult.transactionHash,
      ownerAddress: ownerAddress,
      metadataUri: `ipfs://${nftMetadataIPFS.cid}`,
      blockchainExplorerUrl: `${BLOCKCHAIN_EXPLORER_URL}/token/${NFT_CONTRACT_ADDRESS}?a=${mintResult.tokenId}`
    };
  } catch (error) {
    logger.error(`NFT-Erstellungsfehler: ${error.message}`, { doi });
    return {
      success: false,
      error: error.message
    };
  }
}
```

### 5. Zertifikat-Generierung

#### Zertifikatsinhalte
- Verifizierbare Informationen zur Publikation
- Blockchain-Transaktionsdetails
- Zeitstempel und kryptografische Nachweise
- QR-Code für schnelle Verifizierung
- Visuelle Darstellung für einfache Nutzung

#### Generierungsprozess
- Erstellung eines standardisierten Zertifikatsformats
- Einbettung kryptografischer Nachweise
- Generierung eines QR-Codes für die Verifizierungs-URL
- Erstellung einer PDF- oder HTML-Version des Zertifikats

#### Implementierung
```javascript
async function generateCertificate(doi, nftResult, enrichedMetadata) {
  try {
    // Erstelle Verifizierungs-URL
    const verificationUrl = `https://desci-scholar.org/verify/${nftResult.tokenId}`;
    
    // Generiere QR-Code
    const qrCodeImage = await generateQRCode(verificationUrl);
    
    // Erstelle Zertifikatsdaten
    const certificateData = {
      title: 'DeSci-Scholar Authentizitätszertifikat',
      publicationTitle: enrichedMetadata.title,
      doi: doi,
      authors: enrichedMetadata.authors.map(a => a.name).join(', '),
      publicationDate: enrichedMetadata.published,
      verificationTimestamp: new Date().toISOString(),
      blockchain: {
        network: BLOCKCHAIN_NETWORK,
        contractAddress: NFT_CONTRACT_ADDRESS,
        tokenId: nftResult.tokenId,
        transactionHash: nftResult.transactionHash
      },
      verificationUrl: verificationUrl,
      qrCodeImage: qrCodeImage,
      metadataUri: nftResult.metadataUri
    };
    
    // Generiere PDF-Zertifikat
    const pdfCertificate = await generatePDFCertificate(certificateData);
    
    // Speichere Zertifikat in IPFS
    const certificateIPFS = await storeInIPFS(
      pdfCertificate,
      `${doi.replace('/', '_')}_certificate.pdf`
    );
    
    return {
      success: true,
      certificateUri: `ipfs://${certificateIPFS.cid}`,
      verificationUrl: verificationUrl,
      pdfBuffer: pdfCertificate
    };
  } catch (error) {
    logger.error(`Zertifikat-Generierungsfehler: ${error.message}`, { doi });
    return {
      success: false,
      error: error.message
    };
  }
}
```

### 6. Blockchain-Registrierung

#### Registrierungsdaten
- Token-ID und Metadaten-URI
- Zeitstempel und Verifizierungsinformationen
- Zitationsbeziehungen (falls verfügbar)
- Urheberrechts- und Lizenzinformationen

#### Registrierungsprozess
- Interaktion mit dem Smart Contract
- Aufzeichnung der Metadaten-URI und des DOI
- Verknüpfung mit dem Wallet des Urhebers
- Aufzeichnung von Zeitstempel und Verifizierungsinformationen

#### Implementierung
```javascript
async function registerOnBlockchain(doi, nftResult, certificateResult) {
  try {
    // Bereite Registrierungsdaten vor
    const registrationData = {
      doi: doi,
      tokenId: nftResult.tokenId,
      metadataUri: nftResult.metadataUri,
      certificateUri: certificateResult.certificateUri,
      timestamp: Math.floor(Date.now() / 1000),
      ownerAddress: nftResult.ownerAddress
    };
    
    // Registriere in der Blockchain über Smart Contract
    const registrationResult = await doiRegistryContract.registerDOI(
      doi,
      nftResult.tokenId,
      nftResult.metadataUri,
      certificateResult.certificateUri
    );
    
    // Speichere Registrierungsinformationen in der Datenbank
    await databaseService.saveRegistration({
      doi: doi,
      tokenId: nftResult.tokenId,
      transactionHash: registrationResult.transactionHash,
      blockNumber: registrationResult.blockNumber,
      timestamp: registrationData.timestamp,
      ownerAddress: nftResult.ownerAddress
    });
    
    return {
      success: true,
      transactionHash: registrationResult.transactionHash,
      blockNumber: registrationResult.blockNumber,
      registrationTimestamp: registrationData.timestamp
    };
  } catch (error) {
    logger.error(`Blockchain-Registrierungsfehler: ${error.message}`, { doi });
    return {
      success: false,
      error: error.message
    };
  }
}
```

### 7. Verifizierung und Nachweis

#### Verifizierungsmethoden
- Öffentliche Verifizierungs-Webseite
- API-Endpunkt für automatisierte Verifizierung
- Blockchain-Explorer-Integration

#### Verifizierungsprozess
- Eingabe eines DOI oder einer Token-ID
- Abfrage der Blockchain-Registrierung
- Überprüfung der Metadaten und des Zertifikats
- Anzeige des Verifizierungsergebnisses mit Details

#### Implementierung
```javascript
async function verifyDOINFT(identifier) {
  try {
    let doi, tokenId;
    
    // Bestimme, ob die Eingabe ein DOI oder eine Token-ID ist
    if (isValidDOIFormat(identifier)) {
      doi = identifier;
      // Suche Token-ID für diesen DOI
      const registrationData = await databaseService.getRegistrationByDOI(doi);
      if (!registrationData) {
        throw new Error('Keine Registrierung für diesen DOI gefunden');
      }
      tokenId = registrationData.tokenId;
    } else if (isValidTokenId(identifier)) {
      tokenId = identifier;
      // Suche DOI für diese Token-ID
      const registrationData = await databaseService.getRegistrationByTokenId(tokenId);
      if (!registrationData) {
        throw new Error('Keine Registrierung für diese Token-ID gefunden');
      }
      doi = registrationData.doi;
    } else {
      throw new Error('Ungültiges Format. Bitte geben Sie einen DOI oder eine Token-ID ein');
    }
    
    // Hole Blockchain-Daten
    const onChainData = await doiRegistryContract.getDOIData(tokenId);
    
    // Hole NFT-Metadaten
    const nftMetadata = await fetchIPFSJson(onChainData.metadataUri.replace('ipfs://', ''));
    
    // Hole Zertifikat
    const certificateUri = onChainData.certificateUri;
    
    // Überprüfe DOI bei offiziellen Quellen
    const doiValidation = await validateDOI(doi);
    
    return {
      success: true,
      verified: onChainData.doi === doi && doiValidation.success,
      doi: doi,
      tokenId: tokenId,
      ownerAddress: onChainData.owner,
      registrationTimestamp: new Date(onChainData.timestamp * 1000).toISOString(),
      transactionHash: onChainData.transactionHash,
      blockNumber: onChainData.blockNumber,
      metadataUri: onChainData.metadataUri,
      certificateUri: certificateUri,
      publicationData: {
        title: nftMetadata.name,
        authors: nftMetadata.properties.authors,
        publicationDate: nftMetadata.attributes.find(a => a.trait_type === 'Publication Date')?.value,
        journal: nftMetadata.properties.journal,
        publisher: nftMetadata.properties.publisher
      }
    };
  } catch (error) {
    logger.error(`Verifizierungsfehler: ${error.message}`, { identifier });
    return {
      success: false,
      error: error.message
    };
  }
}
```

## Gesamtprozess-Integration

Der vollständige DOI-zu-NFT-Konvertierungsprozess wird durch einen orchestrierten Workflow zusammengeführt:

```javascript
async function convertDOItoNFT(doi, ownerAddress) {
  try {
    // 1. DOI validieren
    logger.info(`Starte DOI-zu-NFT-Konvertierung für ${doi}`);
    const validationResult = await validateDOI(doi);
    if (!validationResult.success) {
      throw new Error(`DOI-Validierung fehlgeschlagen: ${validationResult.error}`);
    }
    
    // 2. Metadaten anreichern
    const enrichmentResult = await enrichMetadata(doi, validationResult.metadata);
    const enrichedMetadata = enrichmentResult.success ? 
      enrichmentResult.metadata : validationResult.metadata;
    
    // 3. In dezentraler Speicherung ablegen
    const storageResult = await storeInDecentralizedStorage(doi, enrichedMetadata);
    if (!storageResult.success) {
      throw new Error(`Dezentrale Speicherung fehlgeschlagen: ${storageResult.error}`);
    }
    
    // 4. NFT erstellen
    const nftResult = await createNFT(doi, storageResult, enrichedMetadata, ownerAddress);
    if (!nftResult.success) {
      throw new Error(`NFT-Erstellung fehlgeschlagen: ${nftResult.error}`);
    }
    
    // 5. Zertifikat generieren
    const certificateResult = await generateCertificate(doi, nftResult, enrichedMetadata);
    if (!certificateResult.success) {
      throw new Error(`Zertifikat-Generierung fehlgeschlagen: ${certificateResult.error}`);
    }
    
    // 6. In Blockchain registrieren
    const registrationResult = await registerOnBlockchain(doi, nftResult, certificateResult);
    if (!registrationResult.success) {
      throw new Error(`Blockchain-Registrierung fehlgeschlagen: ${registrationResult.error}`);
    }
    
    // 7. Erfolgreiche Konvertierung zurückgeben
    return {
      success: true,
      doi: doi,
      tokenId: nftResult.tokenId,
      ownerAddress: ownerAddress,
      transactionHash: registrationResult.transactionHash,
      metadataUri: nftResult.metadataUri,
      certificateUri: certificateResult.certificateUri,
      verificationUrl: certificateResult.verificationUrl
    };
  } catch (error) {
    logger.error(`DOI-zu-NFT-Konvertierungsfehler: ${error.message}`, { doi });
    return {
      success: false,
      doi: doi,
      error: error.message
    };
  }
}
```

## Sicherheits- und Datenschutzaspekte

### Sicherheitsmaßnahmen
- Kryptografische Verifizierung aller Transaktionen
- Mehrfache Validierung von DOIs und Metadaten
- Redundante Speicherung kritischer Daten
- Regelmäßige Sicherheitsaudits der Smart Contracts

### Datenschutzaspekte
- Transparente Offenlegung aller gespeicherten Daten
- Einhaltung relevanter Datenschutzbestimmungen
- Optionale Anonymisierung sensibler Informationen
- Klare Berechtigungsstrukturen für Datenänderungen

## Fazit

Der DOI-zu-NFT-Konvertierungsprozess in DeSci-Scholar bietet eine robuste, transparente und dezentralisierte Methode zur Authentifizierung und Verifizierung wissenschaftlicher Publikationen. Durch die Kombination etablierter wissenschaftlicher Identifikatoren (DOIs) mit moderner Blockchain-Technologie schafft DeSci-Scholar eine Brücke zwischen traditionellen wissenschaftlichen Infrastrukturen und innovativen dezentralen Systemen.# DOI-zu-NFT-Konvertierungsprozess in DeSci-Scholar

Dieses Dokument beschreibt den detaillierten technischen Prozess der Konvertierung von Digital Object Identifiers (DOIs) zu Non-Fungible Tokens (NFTs) in DeSci-Scholar.

## Übersicht

Der DOI-zu-NFT-Konvertierungsprozess ist die Kernfunktionalität von DeSci-Scholar und umfasst mehrere Schritte, von der Eingabe eines DOI bis zur Erstellung eines verifizierbaren NFT auf der Blockchain.

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│  DOI-Eingabe │────▶│ Metadaten-  │────▶│ Dezentrale  │────▶│  NFT-       │
│  & Validierung│     │ Anreicherung│     │ Speicherung │     │  Erstellung │
└─────────────┘     └─────────────┘     └─────────────┘     └─────────────┘
                                                                    │
┌─────────────┐     ┌─────────────┐     ┌─────────────┐            │
│ Verifizierung│◀────│ Blockchain- │◀────│ Zertifikat- │◀───────────┘
│ & Nachweis   │     │ Registrierung│     │ Generierung │
└─────────────┘     └─────────────┘     └─────────────┘
```

## Detaillierter Prozess

### 1. DOI-Eingabe und Validierung

#### Eingabemethoden
- Manuelle Eingabe eines DOI
- Upload einer Publikation mit DOI-Extraktion
- API-basierte Übermittlung

#### Validierungsprozess
- Überprüfung des DOI-Formats (10.xxxx/xxxxx)
- Abfrage des DOI bei offiziellen Registrierungsstellen (Crossref, DataCite)
- Extraktion grundlegender Metadaten:
  - Titel
  - Autoren
  - Veröffentlichungsdatum
  - Zeitschrift/Verlag
  - Abstrakt
  - Lizenzinformationen

#### Implementierung
```javascript
async function validateDOI(doi) {
  try {
    // Überprüfe DOI-Format
    if (!isValidDOIFormat(doi)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Abfrage bei Crossref
    const crossrefData = await fetchFromCrossref(doi);
    if (crossrefData.success) {
      return {
        success: true,
        source: 'crossref',
        metadata: extractMetadata(crossrefData)
      };
    }
    
    // Abfrage bei DataCite, falls Crossref keine Ergebnisse liefert
    const dataciteData = await fetchFromDatacite(doi);
    if (dataciteData.success) {
      return {
        success: true,
        source: 'datacite',
        metadata: extractMetadata(dataciteData)
      };
    }
    
    throw new Error('DOI nicht gefunden');
  } catch (error) {
    logger.error(`DOI-Validierungsfehler: ${error.message}`, { doi });
    return {
      success: false,
      error: error.message
    };
  }
}
```

### 2. Metadaten-Anreicherung

#### Datenquellen
- **OpenCitations**: Zitationsdaten und -beziehungen
- **ORCID**: Autorenidentifikatoren und -profile
- **Semantic Scholar**: Zusätzliche akademische Metadaten
- **Unpaywall**: Open-Access-Verfügbarkeit

#### Anreicherungsprozess
- Abfrage von Zitationsdaten (eingehend und ausgehend)
- Verknüpfung mit Autorenidentifikatoren
- Extraktion von Schlüsselwörtern und Themen
- Identifikation verwandter Arbeiten
- Ermittlung von Impact-Metriken

#### Implementierung
```javascript
async function enrichMetadata(doi, baseMetadata) {
  try {
    // Anreicherung mit OpenCitations-Daten
    const citationData = await fetchOpenCitationsData(doi);
    
    // Anreicherung mit ORCID-Daten für Autoren
    const authorsWithORCID = await enrichAuthorsWithORCID(baseMetadata.authors);
    
    // Anreicherung mit Semantic Scholar-Daten
    const semanticScholarData = await fetchSemanticScholarData(doi);
    
    // Anreicherung mit Unpaywall-Daten für Open-Access-Status
    const openAccessData = await fetchUnpaywallData(doi);
    
    // Kombiniere alle Metadaten
    const enrichedMetadata = {
      ...baseMetadata,
      citations: {
        incoming: citationData.citations || [],
        outgoing: citationData.references || [],
        count: citationData.citationCount || 0
      },
      authors: authorsWithORCID,
      topics: semanticScholarData.topics || [],
      openAccess: openAccessData.isOpenAccess || false,
      openAccessUrl: openAccessData.bestOpenAccessUrl || null,
      enrichmentTimestamp: new Date().toISOString()
    };
    
    return {
      success: true,
      metadata: enrichedMetadata
    };
  } catch (error) {
    logger.error(`Metadaten-Anreicherungsfehler: ${error.message}`, { doi });
    // Gib Basis-Metadaten zurück, auch wenn Anreicherung fehlschlägt
    return {
      success: false,
      metadata: baseMetadata,
      error: error.message
    };
  }
}
```

### 3. Dezentrale Speicherung

#### Speicherstrategien
- **IPFS**: Primäre Speicherung für Metadaten und kleinere Dokumente
- **BitTorrent**: Speicherung für größere Datensätze und Zusatzmaterialien
- **Redundante Speicherung**: Mehrfache Pinning-Dienste für IPFS

#### Speicherprozess
- Konvertierung der Metadaten in ein standardisiertes JSON-Format
- Erstellung eines Metadaten-Pakets mit allen angereicherten Informationen
- Speicherung des Metadaten-Pakets in IPFS
- Bei verfügbarem Volltext: Speicherung in IPFS oder BitTorrent
- Generierung persistenter Links und Content-Identifikatoren

#### Implementierung
```javascript
async function storeInDecentralizedStorage(doi, enrichedMetadata, fullTextContent = null) {
  try {
    // Erstelle standardisiertes Metadaten-Paket
    const metadataPackage = createStandardizedMetadataPackage(doi, enrichedMetadata);
    
    // Speichere Metadaten in IPFS
    const metadataIPFSResult = await storeInIPFS(
      JSON.stringify(metadataPackage),
      `${doi.replace('/', '_')}_metadata.json`
    );
    
    let fullTextResult = null;
    
    // Speichere Volltext, falls verfügbar
    if (fullTextContent) {
      // Wähle Speichermethode basierend auf Dateigröße
      if (fullTextContent.length > LARGE_FILE_THRESHOLD) {
        fullTextResult = await storeInBitTorrent(
          fullTextContent,
          `${doi.replace('/', '_')}_fulltext.pdf`
        );
      } else {
        fullTextResult = await storeInIPFS(
          fullTextContent,
          `${doi.replace('/', '_')}_fulltext.pdf`
        );
      }
    }
    
    // Pinne IPFS-Inhalte bei mehreren Diensten für Redundanz
    if (metadataIPFSResult.success) {
      await pinIPFSContent(metadataIPFSResult.cid);
    }
    
    if (fullTextResult && fullTextResult.protocol === 'ipfs' && fullTextResult.success) {
      await pinIPFSContent(fullTextResult.cid);
    }
    
    return {
      success: true,
      metadata: {
        protocol: 'ipfs',
        cid: metadataIPFSResult.cid,
        uri: `ipfs://${metadataIPFSResult.cid}`
      },
      fullText: fullTextResult ? {
        protocol: fullTextResult.protocol,
        identifier: fullTextResult.protocol === 'ipfs' ? fullTextResult.cid : fullTextResult.magnetLink,
        uri: fullTextResult.protocol === 'ipfs' ? 
          `ipfs://${fullTextResult.cid}` : 
          fullTextResult.magnetLink
      } : null
    };
  } catch (error) {
    logger.error(`Dezentrale Speicherungsfehler: ${error.message}`, { doi });
    return {
      success: false,
      error: error.message
    };
  }
}
```

### 4. NFT-Erstellung

#### NFT-Metadaten
- DOI und grundlegende bibliografische Informationen
- Verweise auf dezentral gespeicherte Metadaten und Inhalte
- Zitationsinformationen und Impact-Metriken
- Provenienz- und Authentizitätsnachweise
- Lizenz- und Urheberrechtsinformationen

#### NFT-Erstellungsprozess
- Generierung eines eindeutigen Token-Identifikators
- Erstellung der NFT-Metadaten nach ERC-721-Standard
- Einbettung kryptografischer Nachweise
- Prägung des NFT auf der Blockchain
- Verknüpfung mit dem Wallet des Urhebers oder der Institution

#### Implementierung
```javascript
async function createNFT(doi, storageResult, enrichedMetadata, ownerAddress) {
  try {
    // Erstelle NFT-Metadaten nach ERC-721-Standard
    const nftMetadata = {
      name: `${enrichedMetadata.title} (${doi})`,
      description: enrichedMetadata.abstract || 'Keine Zusammenfassung verfügbar',
      image: generateThumbnailUrl(doi, enrichedMetadata),
      external_url: `https://doi.org/${doi}`,
      attributes: [
        { trait_type: 'Publication Type', value: enrichedMetadata.type || 'Article' },
        { trait_type: 'Publication Date', value: enrichedMetadata.published },
        { trait_type: 'Citation Count', value: enrichedMetadata.citations.count },
        { trait_type: 'Open Access', value: enrichedMetadata.openAccess ? 'Yes' : 'No' },
        { trait_type: 'Authors Count', value: enrichedMetadata.authors.length }
      ],
      properties: {
        doi: doi,
        authors: enrichedMetadata.authors.map(author => ({
          name: author.name,
          orcid: author.orcid || null
        })),
        journal: enrichedMetadata.journal,
        publisher: enrichedMetadata.publisher,
        license: enrichedMetadata.license,
        metadata_uri: storageResult.metadata.uri,
        fulltext_uri: storageResult.fullText ? storageResult.fullText.uri : null,
        citations: {
          count: enrichedMetadata.citations.count,
          data_uri: `ipfs://${storageResult.metadata.cid}#citations`
        },
        timestamp: new Date().toISOString(),
        verification: {
          method: 'DOI-Verification',
          timestamp: new Date().toISOString(),
          verifier: 'DeSci-Scholar'
        }
      }
    };
    
    // Speichere NFT-Metadaten in IPFS
    const nftMetadataIPFS = await storeInIPFS(
      JSON.stringify(nftMetadata),
      `${doi.replace('/', '_')}_nft_metadata.json`
    );
    
    // Präge NFT auf der Blockchain
    const mintResult = await mintNFTOnBlockchain(
      ownerAddress,
      nftMetadataIPFS.cid,
      doi
    );
    
    return {
      success: true,
      tokenId: mintResult.tokenId,
      transactionHash: mintResult.transactionHash,
      ownerAddress: ownerAddress,
      metadataUri: `ipfs://${nftMetadataIPFS.cid}`,
      blockchainExplorerUrl: `${BLOCKCHAIN_EXPLORER_URL}/token/${NFT_CONTRACT_ADDRESS}?a=${mintResult.tokenId}`
    };
  } catch (error) {
    logger.error(`NFT-Erstellungsfehler: ${error.message}`, { doi });
    return {
      success: false,
      error: error.message
    };
  }
}
```

### 5. Zertifikat-Generierung

#### Zertifikatsinhalte
- Verifizierbare Informationen zur Publikation
- Blockchain-Transaktionsdetails
- Zeitstempel und kryptografische Nachweise
- QR-Code für schnelle Verifizierung
- Visuelle Darstellung für einfache Nutzung

#### Generierungsprozess
- Erstellung eines standardisierten Zertifikatsformats
- Einbettung kryptografischer Nachweise
- Generierung eines QR-Codes für die Verifizierungs-URL
- Erstellung einer PDF- oder HTML-Version des Zertifikats

#### Implementierung
```javascript
async function generateCertificate(doi, nftResult, enrichedMetadata) {
  try {
    // Erstelle Verifizierungs-URL
    const verificationUrl = `https://desci-scholar.org/verify/${nftResult.tokenId}`;
    
    // Generiere QR-Code
    const qrCodeImage = await generateQRCode(verificationUrl);
    
    // Erstelle Zertifikatsdaten
    const certificateData = {
      title: 'DeSci-Scholar Authentizitätszertifikat',
      publicationTitle: enrichedMetadata.title,
      doi: doi,
      authors: enrichedMetadata.authors.map(a => a.name).join(', '),
      publicationDate: enrichedMetadata.published,
      verificationTimestamp: new Date().toISOString(),
      blockchain: {
        network: BLOCKCHAIN_NETWORK,
        contractAddress: NFT_CONTRACT_ADDRESS,
        tokenId: nftResult.tokenId,
        transactionHash: nftResult.transactionHash
      },
      verificationUrl: verificationUrl,
      qrCodeImage: qrCodeImage,
      metadataUri: nftResult.metadataUri
    };
    
    // Generiere PDF-Zertifikat
    const pdfCertificate = await generatePDFCertificate(certificateData);
    
    // Speichere Zertifikat in IPFS
    const certificateIPFS = await storeInIPFS(
      pdfCertificate,
      `${doi.replace('/', '_')}_certificate.pdf`
    );
    
    return {
      success: true,
      certificateUri: `ipfs://${certificateIPFS.cid}`,
      verificationUrl: verificationUrl,
      pdfBuffer: pdfCertificate
    };
  } catch (error) {
    logger.error(`Zertifikat-Generierungsfehler: ${error.message}`, { doi });
    return {
      success: false,
      error: error.message
    };
  }
}
```

### 6. Blockchain-Registrierung

#### Registrierungsdaten
- Token-ID und Metadaten-URI
- Zeitstempel und Verifizierungsinformationen
- Zitationsbeziehungen (falls verfügbar)
- Urheberrechts- und Lizenzinformationen

#### Registrierungsprozess
- Interaktion mit dem Smart Contract
- Aufzeichnung der Metadaten-URI und des DOI
- Verknüpfung mit dem Wallet des Urhebers
- Aufzeichnung von Zeitstempel und Verifizierungsinformationen

#### Implementierung
```javascript
async function registerOnBlockchain(doi, nftResult, certificateResult) {
  try {
    // Bereite Registrierungsdaten vor
    const registrationData = {
      doi: doi,
      tokenId: nftResult.tokenId,
      metadataUri: nftResult.metadataUri,
      certificateUri: certificateResult.certificateUri,
      timestamp: Math.floor(Date.now() / 1000),
      ownerAddress: nftResult.ownerAddress
    };
    
    // Registriere in der Blockchain über Smart Contract
    const registrationResult = await doiRegistryContract.registerDOI(
      doi,
      nftResult.tokenId,
      nftResult.metadataUri,
      certificateResult.certificateUri
    );
    
    // Speichere Registrierungsinformationen in der Datenbank
    await databaseService.saveRegistration({
      doi: doi,
      tokenId: nftResult.tokenId,
      transactionHash: registrationResult.transactionHash,
      blockNumber: registrationResult.blockNumber,
      timestamp: registrationData.timestamp,
      ownerAddress: nftResult.ownerAddress
    });
    
    return {
      success: true,
      transactionHash: registrationResult.transactionHash,
      blockNumber: registrationResult.blockNumber,
      registrationTimestamp: registrationData.timestamp
    };
  } catch (error) {
    logger.error(`Blockchain-Registrierungsfehler: ${error.message}`, { doi });
    return {
      success: false,
      error: error.message
    };
  }
}
```

### 7. Verifizierung und Nachweis

#### Verifizierungsmethoden
- Öffentliche Verifizierungs-Webseite
- API-Endpunkt für automatisierte Verifizierung
- Blockchain-Explorer-Integration

#### Verifizierungsprozess
- Eingabe eines DOI oder einer Token-ID
- Abfrage der Blockchain-Registrierung
- Überprüfung der Metadaten und des Zertifikats
- Anzeige des Verifizierungsergebnisses mit Details

#### Implementierung
```javascript
async function verifyDOINFT(identifier) {
  try {
    let doi, tokenId;
    
    // Bestimme, ob die Eingabe ein DOI oder eine Token-ID ist
    if (isValidDOIFormat(identifier)) {
      doi = identifier;
      // Suche Token-ID für diesen DOI
      const registrationData = await databaseService.getRegistrationByDOI(doi);
      if (!registrationData) {
        throw new Error('Keine Registrierung für diesen DOI gefunden');
      }
      tokenId = registrationData.tokenId;
    } else if (isValidTokenId(identifier)) {
      tokenId = identifier;
      // Suche DOI für diese Token-ID
      const registrationData = await databaseService.getRegistrationByTokenId(tokenId);
      if (!registrationData) {
        throw new Error('Keine Registrierung für diese Token-ID gefunden');
      }
      doi = registrationData.doi;
    } else {
      throw new Error('Ungültiges Format. Bitte geben Sie einen DOI oder eine Token-ID ein');
    }
    
    // Hole Blockchain-Daten
    const onChainData = await doiRegistryContract.getDOIData(tokenId);
    
    // Hole NFT-Metadaten
    const nftMetadata = await fetchIPFSJson(onChainData.metadataUri.replace('ipfs://', ''));
    
    // Hole Zertifikat
    const certificateUri = onChainData.certificateUri;
    
    // Überprüfe DOI bei offiziellen Quellen
    const doiValidation = await validateDOI(doi);
    
    return {
      success: true,
      verified: onChainData.doi === doi && doiValidation.success,
      doi: doi,
      tokenId: tokenId,
      ownerAddress: onChainData.owner,
      registrationTimestamp: new Date(onChainData.timestamp * 1000).toISOString(),
      transactionHash: onChainData.transactionHash,
      blockNumber: onChainData.blockNumber,
      metadataUri: onChainData.metadataUri,
      certificateUri: certificateUri,
      publicationData: {
        title: nftMetadata.name,
        authors: nftMetadata.properties.authors,
        publicationDate: nftMetadata.attributes.find(a => a.trait_type === 'Publication Date')?.value,
        journal: nftMetadata.properties.journal,
        publisher: nftMetadata.properties.publisher
      }
    };
  } catch (error) {
    logger.error(`Verifizierungsfehler: ${error.message}`, { identifier });
    return {
      success: false,
      error: error.message
    };
  }
}
```

## Gesamtprozess-Integration

Der vollständige DOI-zu-NFT-Konvertierungsprozess wird durch einen orchestrierten Workflow zusammengeführt:

```javascript
async function convertDOItoNFT(doi, ownerAddress) {
  try {
    // 1. DOI validieren
    logger.info(`Starte DOI-zu-NFT-Konvertierung für ${doi}`);
    const validationResult = await validateDOI(doi);
    if (!validationResult.success) {
      throw new Error(`DOI-Validierung fehlgeschlagen: ${validationResult.error}`);
    }
    
    // 2. Metadaten anreichern
    const enrichmentResult = await enrichMetadata(doi, validationResult.metadata);
    const enrichedMetadata = enrichmentResult.success ? 
      enrichmentResult.metadata : validationResult.metadata;
    
    // 3. In dezentraler Speicherung ablegen
    const storageResult = await storeInDecentralizedStorage(doi, enrichedMetadata);
    if (!storageResult.success) {
      throw new Error(`Dezentrale Speicherung fehlgeschlagen: ${storageResult.error}`);
    }
    
    // 4. NFT erstellen
    const nftResult = await createNFT(doi, storageResult, enrichedMetadata, ownerAddress);
    if (!nftResult.success) {
      throw new Error(`NFT-Erstellung fehlgeschlagen: ${nftResult.error}`);
    }
    
    // 5. Zertifikat generieren
    const certificateResult = await generateCertificate(doi, nftResult, enrichedMetadata);
    if (!certificateResult.success) {
      throw new Error(`Zertifikat-Generierung fehlgeschlagen: ${certificateResult.error}`);
    }
    
    // 6. In Blockchain registrieren
    const registrationResult = await registerOnBlockchain(doi, nftResult, certificateResult);
    if (!registrationResult.success) {
      throw new Error(`Blockchain-Registrierung fehlgeschlagen: ${registrationResult.error}`);
    }
    
    // 7. Erfolgreiche Konvertierung zurückgeben
    return {
      success: true,
      doi: doi,
      tokenId: nftResult.tokenId,
      ownerAddress: ownerAddress,
      transactionHash: registrationResult.transactionHash,
      metadataUri: nftResult.metadataUri,
      certificateUri: certificateResult.certificateUri,
      verificationUrl: certificateResult.verificationUrl
    };
  } catch (error) {
    logger.error(`DOI-zu-NFT-Konvertierungsfehler: ${error.message}`, { doi });
    return {
      success: false,
      doi: doi,
      error: error.message
    };
  }
}
```

## Sicherheits- und Datenschutzaspekte

### Sicherheitsmaßnahmen
- Kryptografische Verifizierung aller Transaktionen
- Mehrfache Validierung von DOIs und Metadaten
- Redundante Speicherung kritischer Daten
- Regelmäßige Sicherheitsaudits der Smart Contracts

### Datenschutzaspekte
- Transparente Offenlegung aller gespeicherten Daten
- Einhaltung relevanter Datenschutzbestimmungen
- Optionale Anonymisierung sensibler Informationen
- Klare Berechtigungsstrukturen für Datenänderungen

## Fazit

Der DOI-zu-NFT-Konvertierungsprozess in DeSci-Scholar bietet eine robuste, transparente und dezentralisierte Methode zur Authentifizierung und Verifizierung wissenschaftlicher Publikationen. Durch die Kombination etablierter wissenschaftlicher Identifikatoren (DOIs) mit moderner Blockchain-Technologie schafft DeSci-Scholar eine Brücke zwischen traditionellen wissenschaftlichen Infrastrukturen und innovativen dezentralen Systemen.# DOI-zu-NFT-Konvertierungsprozess in DeSci-Scholar

Dieses Dokument beschreibt den detaillierten technischen Prozess der Konvertierung von Digital Object Identifiers (DOIs) zu Non-Fungible Tokens (NFTs) in DeSci-Scholar.

## Übersicht

Der DOI-zu-NFT-Konvertierungsprozess ist die Kernfunktionalität von DeSci-Scholar und umfasst mehrere Schritte, von der Eingabe eines DOI bis zur Erstellung eines verifizierbaren NFT auf der Blockchain.

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│  DOI-Eingabe │────▶│ Metadaten-  │────▶│ Dezentrale  │────▶│  NFT-       │
│  & Validierung│     │ Anreicherung│     │ Speicherung │     │  Erstellung │
└─────────────┘     └─────────────┘     └─────────────┘     └─────────────┘
                                                                    │
┌─────────────┐     ┌─────────────┐     ┌─────────────┐            │
│ Verifizierung│◀────│ Blockchain- │◀────│ Zertifikat- │◀───────────┘
│ & Nachweis   │     │ Registrierung│     │ Generierung │
└─────────────┘     └─────────────┘     └─────────────┘
```

## Detaillierter Prozess

### 1. DOI-Eingabe und Validierung

#### Eingabemethoden
- Manuelle Eingabe eines DOI
- Upload einer Publikation mit DOI-Extraktion
- API-basierte Übermittlung

#### Validierungsprozess
- Überprüfung des DOI-Formats (10.xxxx/xxxxx)
- Abfrage des DOI bei offiziellen Registrierungsstellen (Crossref, DataCite)
- Extraktion grundlegender Metadaten:
  - Titel
  - Autoren
  - Veröffentlichungsdatum
  - Zeitschrift/Verlag
  - Abstrakt
  - Lizenzinformationen

#### Implementierung
```javascript
async function validateDOI(doi) {
  try {
    // Überprüfe DOI-Format
    if (!isValidDOIFormat(doi)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Abfrage bei Crossref
    const crossrefData = await fetchFromCrossref(doi);
    if (crossrefData.success) {
      return {
        success: true,
        source: 'crossref',
        metadata: extractMetadata(crossrefData)
      };
    }
    
    // Abfrage bei DataCite, falls Crossref keine Ergebnisse liefert
    const dataciteData = await fetchFromDatacite(doi);
    if (dataciteData.success) {
      return {
        success: true,
        source: 'datacite',
        metadata: extractMetadata(dataciteData)
      };
    }
    
    throw new Error('DOI nicht gefunden');
  } catch (error) {
    logger.error(`DOI-Validierungsfehler: ${error.message}`, { doi });
    return {
      success: false,
      error: error.message
    };
  }
}
```

### 2. Metadaten-Anreicherung

#### Datenquellen
- **OpenCitations**: Zitationsdaten und -beziehungen
- **ORCID**: Autorenidentifikatoren und -profile
- **Semantic Scholar**: Zusätzliche akademische Metadaten
- **Unpaywall**: Open-Access-Verfügbarkeit

#### Anreicherungsprozess
- Abfrage von Zitationsdaten (eingehend und ausgehend)
- Verknüpfung mit Autorenidentifikatoren
- Extraktion von Schlüsselwörtern und Themen
- Identifikation verwandter Arbeiten
- Ermittlung von Impact-Metriken

#### Implementierung
```javascript
async function enrichMetadata(doi, baseMetadata) {
  try {
    // Anreicherung mit OpenCitations-Daten
    const citationData = await fetchOpenCitationsData(doi);
    
    // Anreicherung mit ORCID-Daten für Autoren
    const authorsWithORCID = await enrichAuthorsWithORCID(baseMetadata.authors);
    
    // Anreicherung mit Semantic Scholar-Daten
    const semanticScholarData = await fetchSemanticScholarData(doi);
    
    // Anreicherung mit Unpaywall-Daten für Open-Access-Status
    const openAccessData = await fetchUnpaywallData(doi);
    
    // Kombiniere alle Metadaten
    const enrichedMetadata = {
      ...baseMetadata,
      citations: {
        incoming: citationData.citations || [],
        outgoing: citationData.references || [],
        count: citationData.citationCount || 0
      },
      authors: authorsWithORCID,
      topics: semanticScholarData.topics || [],
      openAccess: openAccessData.isOpenAccess || false,
      openAccessUrl: openAccessData.bestOpenAccessUrl || null,
      enrichmentTimestamp: new Date().toISOString()
    };
    
    return {
      success: true,
      metadata: enrichedMetadata
    };
  } catch (error) {
    logger.error(`Metadaten-Anreicherungsfehler: ${error.message}`, { doi });
    // Gib Basis-Metadaten zurück, auch wenn Anreicherung fehlschlägt
    return {
      success: false,
      metadata: baseMetadata,
      error: error.message
    };
  }
}
```

### 3. Dezentrale Speicherung

#### Speicherstrategien
- **IPFS**: Primäre Speicherung für Metadaten und kleinere Dokumente
- **BitTorrent**: Speicherung für größere Datensätze und Zusatzmaterialien
- **Redundante Speicherung**: Mehrfache Pinning-Dienste für IPFS

#### Speicherprozess
- Konvertierung der Metadaten in ein standardisiertes JSON-Format
- Erstellung eines Metadaten-Pakets mit allen angereicherten Informationen
- Speicherung des Metadaten-Pakets in IPFS
- Bei verfügbarem Volltext: Speicherung in IPFS oder BitTorrent
- Generierung persistenter Links und Content-Identifikatoren

#### Implementierung
```javascript
async function storeInDecentralizedStorage(doi, enrichedMetadata, fullTextContent = null) {
  try {
    // Erstelle standardisiertes Metadaten-Paket
    const metadataPackage = createStandardizedMetadataPackage(doi, enrichedMetadata);
    
    // Speichere Metadaten in IPFS
    const metadataIPFSResult = await storeInIPFS(
      JSON.stringify(metadataPackage),
      `${doi.replace('/', '_')}_metadata.json`
    );
    
    let fullTextResult = null;
    
    // Speichere Volltext, falls verfügbar
    if (fullTextContent) {
      // Wähle Speichermethode basierend auf Dateigröße
      if (fullTextContent.length > LARGE_FILE_THRESHOLD) {
        fullTextResult = await storeInBitTorrent(
          fullTextContent,
          `${doi.replace('/', '_')}_fulltext.pdf`
        );
      } else {
        fullTextResult = await storeInIPFS(
          fullTextContent,
          `${doi.replace('/', '_')}_fulltext.pdf`
        );
      }
    }
    
    // Pinne IPFS-Inhalte bei mehreren Diensten für Redundanz
    if (metadataIPFSResult.success) {
      await pinIPFSContent(metadataIPFSResult.cid);
    }
    
    if (fullTextResult && fullTextResult.protocol === 'ipfs' && fullTextResult.success) {
      await pinIPFSContent(fullTextResult.cid);
    }
    
    return {
      success: true,
      metadata: {
        protocol: 'ipfs',
        cid: metadataIPFSResult.cid,
        uri: `ipfs://${metadataIPFSResult.cid}`
      },
      fullText: fullTextResult ? {
        protocol: fullTextResult.protocol,
        identifier: fullTextResult.protocol === 'ipfs' ? fullTextResult.cid : fullTextResult.magnetLink,
        uri: fullTextResult.protocol === 'ipfs' ? 
          `ipfs://${fullTextResult.cid}` : 
          fullTextResult.magnetLink
      } : null
    };
  } catch (error) {
    logger.error(`Dezentrale Speicherungsfehler: ${error.message}`, { doi });
    return {
      success: false,
      error: error.message
    };
  }
}
```

### 4. NFT-Erstellung

#### NFT-Metadaten
- DOI und grundlegende bibliografische Informationen
- Verweise auf dezentral gespeicherte Metadaten und Inhalte
- Zitationsinformationen und Impact-Metriken
- Provenienz- und Authentizitätsnachweise
- Lizenz- und Urheberrechtsinformationen

#### NFT-Erstellungsprozess
- Generierung eines eindeutigen Token-Identifikators
- Erstellung der NFT-Metadaten nach ERC-721-Standard
- Einbettung kryptografischer Nachweise
- Prägung des NFT auf der Blockchain
- Verknüpfung mit dem Wallet des Urhebers oder der Institution

#### Implementierung
```javascript
async function createNFT(doi, storageResult, enrichedMetadata, ownerAddress) {
  try {
    // Erstelle NFT-Metadaten nach ERC-721-Standard
    const nftMetadata = {
      name: `${enrichedMetadata.title} (${doi})`,
      description: enrichedMetadata.abstract || 'Keine Zusammenfassung verfügbar',
      image: generateThumbnailUrl(doi, enrichedMetadata),
      external_url: `https://doi.org/${doi}`,
      attributes: [
        { trait_type: 'Publication Type', value: enrichedMetadata.type || 'Article' },
        { trait_type: 'Publication Date', value: enrichedMetadata.published },
        { trait_type: 'Citation Count', value: enrichedMetadata.citations.count },
        { trait_type: 'Open Access', value: enrichedMetadata.openAccess ? 'Yes' : 'No' },
        { trait_type: 'Authors Count', value: enrichedMetadata.authors.length }
      ],
      properties: {
        doi: doi,
        authors: enrichedMetadata.authors.map(author => ({
          name: author.name,
          orcid: author.orcid || null
        })),
        journal: enrichedMetadata.journal,
        publisher: enrichedMetadata.publisher,
        license: enrichedMetadata.license,
        metadata_uri: storageResult.metadata.uri,
        fulltext_uri: storageResult.fullText ? storageResult.fullText.uri : null,
        citations: {
          count: enrichedMetadata.citations.count,
          data_uri: `ipfs://${storageResult.metadata.cid}#citations`
        },
        timestamp: new Date().toISOString(),
        verification: {
          method: 'DOI-Verification',
          timestamp: new Date().toISOString(),
          verifier: 'DeSci-Scholar'
        }
      }
    };
    
    // Speichere NFT-Metadaten in IPFS
    const nftMetadataIPFS = await storeInIPFS(
      JSON.stringify(nftMetadata),
      `${doi.replace('/', '_')}_nft_metadata.json`
    );
    
    // Präge NFT auf der Blockchain
    const mintResult = await mintNFTOnBlockchain(
      ownerAddress,
      nftMetadataIPFS.cid,
      doi
    );
    
    return {
      success: true,
      tokenId: mintResult.tokenId,
      transactionHash: mintResult.transactionHash,
      ownerAddress: ownerAddress,
      metadataUri: `ipfs://${nftMetadataIPFS.cid}`,
      blockchainExplorerUrl: `${BLOCKCHAIN_EXPLORER_URL}/token/${NFT_CONTRACT_ADDRESS}?a=${mintResult.tokenId}`
    };
  } catch (error) {
    logger.error(`NFT-Erstellungsfehler: ${error.message}`, { doi });
    return {
      success: false,
      error: error.message
    };
  }
}
```

### 5. Zertifikat-Generierung

#### Zertifikatsinhalte
- Verifizierbare Informationen zur Publikation
- Blockchain-Transaktionsdetails
- Zeitstempel und kryptografische Nachweise
- QR-Code für schnelle Verifizierung
- Visuelle Darstellung für einfache Nutzung

#### Generierungsprozess
- Erstellung eines standardisierten Zertifikatsformats
- Einbettung kryptografischer Nachweise
- Generierung eines QR-Codes für die Verifizierungs-URL
- Erstellung einer PDF- oder HTML-Version des Zertifikats

#### Implementierung
```javascript
async function generateCertificate(doi, nftResult, enrichedMetadata) {
  try {
    // Erstelle Verifizierungs-URL
    const verificationUrl = `https://desci-scholar.org/verify/${nftResult.tokenId}`;
    
    // Generiere QR-Code
    const qrCodeImage = await generateQRCode(verificationUrl);
    
    // Erstelle Zertifikatsdaten
    const certificateData = {
      title: 'DeSci-Scholar Authentizitätszertifikat',
      publicationTitle: enrichedMetadata.title,
      doi: doi,
      authors: enrichedMetadata.authors.map(a => a.name).join(', '),
      publicationDate: enrichedMetadata.published,
      verificationTimestamp: new Date().toISOString(),
      blockchain: {
        network: BLOCKCHAIN_NETWORK,
        contractAddress: NFT_CONTRACT_ADDRESS,
        tokenId: nftResult.tokenId,
        transactionHash: nftResult.transactionHash
      },
      verificationUrl: verificationUrl,
      qrCodeImage: qrCodeImage,
      metadataUri: nftResult.metadataUri
    };
    
    // Generiere PDF-Zertifikat
    const pdfCertificate = await generatePDFCertificate(certificateData);
    
    // Speichere Zertifikat in IPFS
    const certificateIPFS = await storeInIPFS(
      pdfCertificate,
      `${doi.replace('/', '_')}_certificate.pdf`
    );
    
    return {
      success: true,
      certificateUri: `ipfs://${certificateIPFS.cid}`,
      verificationUrl: verificationUrl,
      pdfBuffer: pdfCertificate
    };
  } catch (error) {
    logger.error(`Zertifikat-Generierungsfehler: ${error.message}`, { doi });
    return {
      success: false,
      error: error.message
    };
  }
}
```

### 6. Blockchain-Registrierung

#### Registrierungsdaten
- Token-ID und Metadaten-URI
- Zeitstempel und Verifizierungsinformationen
- Zitationsbeziehungen (falls verfügbar)
- Urheberrechts- und Lizenzinformationen

#### Registrierungsprozess
- Interaktion mit dem Smart Contract
- Aufzeichnung der Metadaten-URI und des DOI
- Verknüpfung mit dem Wallet des Urhebers
- Aufzeichnung von Zeitstempel und Verifizierungsinformationen

#### Implementierung
```javascript
async function registerOnBlockchain(doi, nftResult, certificateResult) {
  try {
    // Bereite Registrierungsdaten vor
    const registrationData = {
      doi: doi,
      tokenId: nftResult.tokenId,
      metadataUri: nftResult.metadataUri,
      certificateUri: certificateResult.certificateUri,
      timestamp: Math.floor(Date.now() / 1000),
      ownerAddress: nftResult.ownerAddress
    };
    
    // Registriere in der Blockchain über Smart Contract
    const registrationResult = await doiRegistryContract.registerDOI(
      doi,
      nftResult.tokenId,
      nftResult.metadataUri,
      certificateResult.certificateUri
    );
    
    // Speichere Registrierungsinformationen in der Datenbank
    await databaseService.saveRegistration({
      doi: doi,
      tokenId: nftResult.tokenId,
      transactionHash: registrationResult.transactionHash,
      blockNumber: registrationResult.blockNumber,
      timestamp: registrationData.timestamp,
      ownerAddress: nftResult.ownerAddress
    });
    
    return {
      success: true,
      transactionHash: registrationResult.transactionHash,
      blockNumber: registrationResult.blockNumber,
      registrationTimestamp: registrationData.timestamp
    };
  } catch (error) {
    logger.error(`Blockchain-Registrierungsfehler: ${error.message}`, { doi });
    return {
      success: false,
      error: error.message
    };
  }
}
```

### 7. Verifizierung und Nachweis

#### Verifizierungsmethoden
- Öffentliche Verifizierungs-Webseite
- API-Endpunkt für automatisierte Verifizierung
- Blockchain-Explorer-Integration

#### Verifizierungsprozess
- Eingabe eines DOI oder einer Token-ID
- Abfrage der Blockchain-Registrierung
- Überprüfung der Metadaten und des Zertifikats
- Anzeige des Verifizierungsergebnisses mit Details

#### Implementierung
```javascript
async function verifyDOINFT(identifier) {
  try {
    let doi, tokenId;
    
    // Bestimme, ob die Eingabe ein DOI oder eine Token-ID ist
    if (isValidDOIFormat(identifier)) {
      doi = identifier;
      // Suche Token-ID für diesen DOI
      const registrationData = await databaseService.getRegistrationByDOI(doi);
      if (!registrationData) {
        throw new Error('Keine Registrierung für diesen DOI gefunden');
      }
      tokenId = registrationData.tokenId;
    } else if (isValidTokenId(identifier)) {
      tokenId = identifier;
      // Suche DOI für diese Token-ID
      const registrationData = await databaseService.getRegistrationByTokenId(tokenId);
      if (!registrationData) {
        throw new Error('Keine Registrierung für diese Token-ID gefunden');
      }
      doi = registrationData.doi;
    } else {
      throw new Error('Ungültiges Format. Bitte geben Sie einen DOI oder eine Token-ID ein');
    }
    
    // Hole Blockchain-Daten
    const onChainData = await doiRegistryContract.getDOIData(tokenId);
    
    // Hole NFT-Metadaten
    const nftMetadata = await fetchIPFSJson(onChainData.metadataUri.replace('ipfs://', ''));
    
    // Hole Zertifikat
    const certificateUri = onChainData.certificateUri;
    
    // Überprüfe DOI bei offiziellen Quellen
    const doiValidation = await validateDOI(doi);
    
    return {
      success: true,
      verified: onChainData.doi === doi && doiValidation.success,
      doi: doi,
      tokenId: tokenId,
      ownerAddress: onChainData.owner,
      registrationTimestamp: new Date(onChainData.timestamp * 1000).toISOString(),
      transactionHash: onChainData.transactionHash,
      blockNumber: onChainData.blockNumber,
      metadataUri: onChainData.metadataUri,
      certificateUri: certificateUri,
      publicationData: {
        title: nftMetadata.name,
        authors: nftMetadata.properties.authors,
        publicationDate: nftMetadata.attributes.find(a => a.trait_type === 'Publication Date')?.value,
        journal: nftMetadata.properties.journal,
        publisher: nftMetadata.properties.publisher
      }
    };
  } catch (error) {
    logger.error(`Verifizierungsfehler: ${error.message}`, { identifier });
    return {
      success: false,
      error: error.message
    };
  }
}
```

## Gesamtprozess-Integration

Der vollständige DOI-zu-NFT-Konvertierungsprozess wird durch einen orchestrierten Workflow zusammengeführt:

```javascript
async function convertDOItoNFT(doi, ownerAddress) {
  try {
    // 1. DOI validieren
    logger.info(`Starte DOI-zu-NFT-Konvertierung für ${doi}`);
    const validationResult = await validateDOI(doi);
    if (!validationResult.success) {
      throw new Error(`DOI-Validierung fehlgeschlagen: ${validationResult.error}`);
    }
    
    // 2. Metadaten anreichern
    const enrichmentResult = await enrichMetadata(doi, validationResult.metadata);
    const enrichedMetadata = enrichmentResult.success ? 
      enrichmentResult.metadata : validationResult.metadata;
    
    // 3. In dezentraler Speicherung ablegen
    const storageResult = await storeInDecentralizedStorage(doi, enrichedMetadata);
    if (!storageResult.success) {
      throw new Error(`Dezentrale Speicherung fehlgeschlagen: ${storageResult.error}`);
    }
    
    // 4. NFT erstellen
    const nftResult = await createNFT(doi, storageResult, enrichedMetadata, ownerAddress);
    if (!nftResult.success) {
      throw new Error(`NFT-Erstellung fehlgeschlagen: ${nftResult.error}`);
    }
    
    // 5. Zertifikat generieren
    const certificateResult = await generateCertificate(doi, nftResult, enrichedMetadata);
    if (!certificateResult.success) {
      throw new Error(`Zertifikat-Generierung fehlgeschlagen: ${certificateResult.error}`);
    }
    
    // 6. In Blockchain registrieren
    const registrationResult = await registerOnBlockchain(doi, nftResult, certificateResult);
    if (!registrationResult.success) {
      throw new Error(`Blockchain-Registrierung fehlgeschlagen: ${registrationResult.error}`);
    }
    
    // 7. Erfolgreiche Konvertierung zurückgeben
    return {
      success: true,
      doi: doi,
      tokenId: nftResult.tokenId,
      ownerAddress: ownerAddress,
      transactionHash: registrationResult.transactionHash,
      metadataUri: nftResult.metadataUri,
      certificateUri: certificateResult.certificateUri,
      verificationUrl: certificateResult.verificationUrl
    };
  } catch (error) {
    logger.error(`DOI-zu-NFT-Konvertierungsfehler: ${error.message}`, { doi });
    return {
      success: false,
      doi: doi,
      error: error.message
    };
  }
}
```

## Sicherheits- und Datenschutzaspekte

### Sicherheitsmaßnahmen
- Kryptografische Verifizierung aller Transaktionen
- Mehrfache Validierung von DOIs und Metadaten
- Redundante Speicherung kritischer Daten
- Regelmäßige Sicherheitsaudits der Smart Contracts

### Datenschutzaspekte
- Transparente Offenlegung aller gespeicherten Daten
- Einhaltung relevanter Datenschutzbestimmungen
- Optionale Anonymisierung sensibler Informationen
- Klare Berechtigungsstrukturen für Datenänderungen

## Fazit

Der DOI-zu-NFT-Konvertierungsprozess in DeSci-Scholar bietet eine robuste, transparente und dezentralisierte Methode zur Authentifizierung und Verifizierung wissenschaftlicher Publikationen. Durch die Kombination etablierter wissenschaftlicher Identifikatoren (DOIs) mit moderner Blockchain-Technologie schafft DeSci-Scholar eine Brücke zwischen traditionellen wissenschaftlichen Infrastrukturen und innovativen dezentralen Systemen.Dieses Dokument beschreibt den detaillierten technischen Prozess der Konvertierung von Digital Object Identifiers (DOIs) zu Non-Fungible Tokens (NFTs) in DeSci-Scholar.

## Übersicht

Der DOI-zu-NFT-Konvertierungsprozess ist die Kernfunktionalität von DeSci-Scholar und umfasst mehrere Schritte, von der Eingabe eines DOI bis zur Erstellung eines verifizierbaren NFT auf der Blockchain.

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│  DOI-Eingabe │────▶│ Metadaten-  │────▶│ Dezentrale  │────▶│  NFT-       │
│  & Validierung│     │ Anreicherung│     │ Speicherung │     │  Erstellung │
└─────────────┘     └─────────────┘     └─────────────┘     └─────────────┘
                                                                    │
┌─────────────┐     ┌─────────────┐     ┌─────────────┐            │
│ Verifizierung│◀────│ Blockchain- │◀────│ Zertifikat- │◀───────────┘
│ & Nachweis   │     │ Registrierung│     │ Generierung │
└─────────────┘     └─────────────┘     └─────────────┘
```

## Detaillierter Prozess

### 1. DOI-Eingabe und Validierung

#### Eingabemethoden
- Manuelle Eingabe eines DOI
- Upload einer Publikation mit DOI-Extraktion
- API-basierte Übermittlung

#### Validierungsprozess
- Überprüfung des DOI-Formats (10.xxxx/xxxxx)
- Abfrage des DOI bei offiziellen Registrierungsstellen (Crossref, DataCite)
- Extraktion grundlegender Metadaten:
  - Titel
  - Autoren
  - Veröffentlichungsdatum
  - Zeitschrift/Verlag
  - Abstrakt
  - Lizenzinformationen

#### Implementierung
```javascript
async function validateDOI(doi) {
  try {
    // Überprüfe DOI-Format
    if (!isValidDOIFormat(doi)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Abfrage bei Crossref
    const crossrefData = await fetchFromCrossref(doi);
    if (crossrefData.success) {
      return {
        success: true,
        source: 'crossref',
        metadata: extractMetadata(crossrefData)
      };
    }
    
    // Abfrage bei DataCite, falls Crossref keine Ergebnisse liefert
    const dataciteData = await fetchFromDatacite(doi);
    if (dataciteData.success) {
      return {
        success: true,
        source: 'datacite',
        metadata: extractMetadata(dataciteData)
      };
    }
    
    throw new Error('DOI nicht gefunden');
  } catch (error) {
    logger.error(`DOI-Validierungsfehler: ${error.message}`, { doi });
    return {
      success: false,
      error: error.message
    };
  }
}
```

### 2. Metadaten-Anreicherung

#### Datenquellen
- **OpenCitations**: Zitationsdaten und -beziehungen
- **ORCID**: Autorenidentifikatoren und -profile
- **Semantic Scholar**: Zusätzliche akademische Metadaten
- **Unpaywall**: Open-Access-Verfügbarkeit

#### Anreicherungsprozess
- Abfrage von Zitationsdaten (eingehend und ausgehend)
- Verknüpfung mit Autorenidentifikatoren
- Extraktion von Schlüsselwörtern und Themen
- Identifikation verwandter Arbeiten
- Ermittlung von Impact-Metriken

#### Implementierung
```javascript
async function enrichMetadata(doi, baseMetadata) {
  try {
    // Anreicherung mit OpenCitations-Daten
    const citationData = await fetchOpenCitationsData(doi);
    
    // Anreicherung mit ORCID-Daten für Autoren
    const authorsWithORCID = await enrichAuthorsWithORCID(baseMetadata.authors);
    
    // Anreicherung mit Semantic Scholar-Daten
    const semanticScholarData = await fetchSemanticScholarData(doi);
    
    // Anreicherung mit Unpaywall-Daten für Open-Access-Status
    const openAccessData = await fetchUnpaywallData(doi);
    
    // Kombiniere alle Metadaten
    const enrichedMetadata = {
      ...baseMetadata,
      citations: {
        incoming: citationData.citations || [],
        outgoing: citationData.references || [],
        count: citationData.citationCount || 0
      },
      authors: authorsWithORCID,
      topics: semanticScholarData.topics || [],
      openAccess: openAccessData.isOpenAccess || false,
      openAccessUrl: openAccessData.bestOpenAccessUrl || null,
      enrichmentTimestamp: new Date().toISOString()
    };
    
    return {
      success: true,
      metadata: enrichedMetadata
    };
  } catch (error) {
    logger.error(`Metadaten-Anreicherungsfehler: ${error.message}`, { doi });
    // Gib Basis-Metadaten zurück, auch wenn Anreicherung fehlschlägt
    return {
      success: false,
      metadata: baseMetadata,
      error: error.message
    };
  }
}
```

### 3. Dezentrale Speicherung

#### Speicherstrategien
- **IPFS**: Primäre Speicherung für Metadaten und kleinere Dokumente
- **BitTorrent**: Speicherung für größere Datensätze und Zusatzmaterialien
- **Redundante Speicherung**: Mehrfache Pinning-Dienste für IPFS

#### Speicherprozess
- Konvertierung der Metadaten in ein standardisiertes JSON-Format
- Erstellung eines Metadaten-Pakets mit allen angereicherten Informationen
- Speicherung des Metadaten-Pakets in IPFS
- Bei verfügbarem Volltext: Speicherung in IPFS oder BitTorrent
- Generierung persistenter Links und Content-Identifikatoren

#### Implementierung
```javascript
async function storeInDecentralizedStorage(doi, enrichedMetadata, fullTextContent = null) {
  try {
    // Erstelle standardisiertes Metadaten-Paket
    const metadataPackage = createStandardizedMetadataPackage(doi, enrichedMetadata);
    
    // Speichere Metadaten in IPFS
    const metadataIPFSResult = await storeInIPFS(
      JSON.stringify(metadataPackage),
      `${doi.replace('/', '_')}_metadata.json`
    );
    
    let fullTextResult = null;
    
    // Speichere Volltext, falls verfügbar
    if (fullTextContent) {
      // Wähle Speichermethode basierend auf Dateigröße
      if (fullTextContent.length > LARGE_FILE_THRESHOLD) {
        fullTextResult = await storeInBitTorrent(
          fullTextContent,
          `${doi.replace('/', '_')}_fulltext.pdf`
        );
      } else {
        fullTextResult = await storeInIPFS(
          fullTextContent,
          `${doi.replace('/', '_')}_fulltext.pdf`
        );
      }
    }
    
    // Pinne IPFS-Inhalte bei mehreren Diensten für Redundanz
    if (metadataIPFSResult.success) {
      await pinIPFSContent(metadataIPFSResult.cid);
    }
    
    if (fullTextResult && fullTextResult.protocol === 'ipfs' && fullTextResult.success) {
      await pinIPFSContent(fullTextResult.cid);
    }
    
    return {
      success: true,
      metadata: {
        protocol: 'ipfs',
        cid: metadataIPFSResult.cid,
        uri: `ipfs://${metadataIPFSResult.cid}`
      },
      fullText: fullTextResult ? {
        protocol: fullTextResult.protocol,
        identifier: fullTextResult.protocol === 'ipfs' ? fullTextResult.cid : fullTextResult.magnetLink,
        uri: fullTextResult.protocol === 'ipfs' ? 
          `ipfs://${fullTextResult.cid}` : 
          fullTextResult.magnetLink
      } : null
    };
  } catch (error) {
    logger.error(`Dezentrale Speicherungsfehler: ${error.message}`, { doi });
    return {
      success: false,
      error: error.message
    };
  }
}
```

### 4. NFT-Erstellung

#### NFT-Metadaten
- DOI und grundlegende bibliografische Informationen
- Verweise auf dezentral gespeicherte Metadaten und Inhalte
- Zitationsinformationen und Impact-Metriken
- Provenienz- und Authentizitätsnachweise
- Lizenz- und Urheberrechtsinformationen

#### NFT-Erstellungsprozess
- Generierung eines eindeutigen Token-Identifikators
- Erstellung der NFT-Metadaten nach ERC-721-Standard
- Einbettung kryptografischer Nachweise
- Prägung des NFT auf der Blockchain
- Verknüpfung mit dem Wallet des Urhebers oder der Institution

#### Implementierung
```javascript
async function createNFT(doi, storageResult, enrichedMetadata, ownerAddress) {
  try {
    // Erstelle NFT-Metadaten nach ERC-721-Standard
    const nftMetadata = {
      name: `${enrichedMetadata.title} (${doi})`,
      description: enrichedMetadata.abstract || 'Keine Zusammenfassung verfügbar',
      image: generateThumbnailUrl(doi, enrichedMetadata),
      external_url: `https://doi.org/${doi}`,
      attributes: [
        { trait_type: 'Publication Type', value: enrichedMetadata.type || 'Article' },
        { trait_type: 'Publication Date', value: enrichedMetadata.published },
        { trait_type: 'Citation Count', value: enrichedMetadata.citations.count },
        { trait_type: 'Open Access', value: enrichedMetadata.openAccess ? 'Yes' : 'No' },
        { trait_type: 'Authors Count', value: enrichedMetadata.authors.length }
      ],
      properties: {
        doi: doi,
        authors: enrichedMetadata.authors.map(author => ({
          name: author.name,
          orcid: author.orcid || null
        })),
        journal: enrichedMetadata.journal,
        publisher: enrichedMetadata.publisher,
        license: enrichedMetadata.license,
        metadata_uri: storageResult.metadata.uri,
        fulltext_uri: storageResult.fullText ? storageResult.fullText.uri : null,
        citations: {
          count: enrichedMetadata.citations.count,
          data_uri: `ipfs://${storageResult.metadata.cid}#citations`
        },
        timestamp: new Date().toISOString(),
        verification: {
          method: 'DOI-Verification',
          timestamp: new Date().toISOString(),
          verifier: 'DeSci-Scholar'
        }
      }
    };
    
    // Speichere NFT-Metadaten in IPFS
    const nftMetadataIPFS = await storeInIPFS(
      JSON.stringify(nftMetadata),
      `${doi.replace('/', '_')}_nft_metadata.json`
    );
    
    // Präge NFT auf der Blockchain
    const mintResult = await mintNFTOnBlockchain(
      ownerAddress,
      nftMetadataIPFS.cid,
      doi
    );
    
    return {
      success: true,
      tokenId: mintResult.tokenId,
      transactionHash: mintResult.transactionHash,
      ownerAddress: ownerAddress,
      metadataUri: `ipfs://${nftMetadataIPFS.cid}`,
      blockchainExplorerUrl: `${BLOCKCHAIN_EXPLORER_URL}/token/${NFT_CONTRACT_ADDRESS}?a=${mintResult.tokenId}`
    };
  } catch (error) {
    logger.error(`NFT-Erstellungsfehler: ${error.message}`, { doi });
    return {
      success: false,
      error: error.message
    };
  }
}
```

### 5. Zertifikat-Generierung

#### Zertifikatsinhalte
- Verifizierbare Informationen zur Publikation
- Blockchain-Transaktionsdetails
- Zeitstempel und kryptografische Nachweise
- QR-Code für schnelle Verifizierung
- Visuelle Darstellung für einfache Nutzung

#### Generierungsprozess
- Erstellung eines standardisierten Zertifikatsformats
- Einbettung kryptografischer Nachweise
- Generierung eines QR-Codes für die Verifizierungs-URL
- Erstellung einer PDF- oder HTML-Version des Zertifikats

#### Implementierung
```javascript
async function generateCertificate(doi, nftResult, enrichedMetadata) {
  try {
    // Erstelle Verifizierungs-URL
    const verificationUrl = `https://desci-scholar.org/verify/${nftResult.tokenId}`;
    
    // Generiere QR-Code
    const qrCodeImage = await generateQRCode(verificationUrl);
    
    // Erstelle Zertifikatsdaten
    const certificateData = {
      title: 'DeSci-Scholar Authentizitätszertifikat',
      publicationTitle: enrichedMetadata.title,
      doi: doi,
      authors: enrichedMetadata.authors.map(a => a.name).join(', '),
      publicationDate: enrichedMetadata.published,
      verificationTimestamp: new Date().toISOString(),
      blockchain: {
        network: BLOCKCHAIN_NETWORK,
        contractAddress: NFT_CONTRACT_ADDRESS,
        tokenId: nftResult.tokenId,
        transactionHash: nftResult.transactionHash
      },
      verificationUrl: verificationUrl,
      qrCodeImage: qrCodeImage,
      metadataUri: nftResult.metadataUri
    };
    
    // Generiere PDF-Zertifikat
    const pdfCertificate = await generatePDFCertificate(certificateData);
    
    // Speichere Zertifikat in IPFS
    const certificateIPFS = await storeInIPFS(
      pdfCertificate,
      `${doi.replace('/', '_')}_certificate.pdf`
    );
    
    return {
      success: true,
      certificateUri: `ipfs://${certificateIPFS.cid}`,
      verificationUrl: verificationUrl,
      pdfBuffer: pdfCertificate
    };
  } catch (error) {
    logger.error(`Zertifikat-Generierungsfehler: ${error.message}`, { doi });
    return {
      success: false,
      error: error.message
    };
  }
}
```

### 6. Blockchain-Registrierung

#### Registrierungsdaten
- Token-ID und Metadaten-URI
- Zeitstempel und Verifizierungsinformationen
- Zitationsbeziehungen (falls verfügbar)
- Urheberrechts- und Lizenzinformationen

#### Registrierungsprozess
- Interaktion mit dem Smart Contract
- Aufzeichnung der Metadaten-URI und des DOI
- Verknüpfung mit dem Wallet des Urhebers
- Aufzeichnung von Zeitstempel und Verifizierungsinformationen

#### Implementierung
```javascript
async function registerOnBlockchain(doi, nftResult, certificateResult) {
  try {
    // Bereite Registrierungsdaten vor
    const registrationData = {
      doi: doi,
      tokenId: nftResult.tokenId,
      metadataUri: nftResult.metadataUri,
      certificateUri: certificateResult.certificateUri,
      timestamp: Math.floor(Date.now() / 1000),
      ownerAddress: nftResult.ownerAddress
    };
    
    // Registriere in der Blockchain über Smart Contract
    const registrationResult = await doiRegistryContract.registerDOI(
      doi,
      nftResult.tokenId,
      nftResult.metadataUri,
      certificateResult.certificateUri
    );
    
    // Speichere Registrierungsinformationen in der Datenbank
    await databaseService.saveRegistration({
      doi: doi,
      tokenId: nftResult.tokenId,
      transactionHash: registrationResult.transactionHash,
      blockNumber: registrationResult.blockNumber,
      timestamp: registrationData.timestamp,
      ownerAddress: nftResult.ownerAddress
    });
    
    return {
      success: true,
      transactionHash: registrationResult.transactionHash,
      blockNumber: registrationResult.blockNumber,
      registrationTimestamp: registrationData.timestamp
    };
  } catch (error) {
    logger.error(`Blockchain-Registrierungsfehler: ${error.message}`, { doi });
    return {
      success: false,
      error: error.message
    };
  }
}
```

### 7. Verifizierung und Nachweis

#### Verifizierungsmethoden
- Öffentliche Verifizierungs-Webseite
- API-Endpunkt für automatisierte Verifizierung
- Blockchain-Explorer-Integration

#### Verifizierungsprozess
- Eingabe eines DOI oder einer Token-ID
- Abfrage der Blockchain-Registrierung
- Überprüfung der Metadaten und des Zertifikats
- Anzeige des Verifizierungsergebnisses mit Details

#### Implementierung
```javascript
async function verifyDOINFT(identifier) {
  try {
    let doi, tokenId;
    
    // Bestimme, ob die Eingabe ein DOI oder eine Token-ID ist
    if (isValidDOIFormat(identifier)) {
      doi = identifier;
      // Suche Token-ID für diesen DOI
      const registrationData = await databaseService.getRegistrationByDOI(doi);
      if (!registrationData) {
        throw new Error('Keine Registrierung für diesen DOI gefunden');
      }
      tokenId = registrationData.tokenId;
    } else if (isValidTokenId(identifier)) {
      tokenId = identifier;
      // Suche DOI für diese Token-ID
      const registrationData = await databaseService.getRegistrationByTokenId(tokenId);
      if (!registrationData) {
        throw new Error('Keine Registrierung für diese Token-ID gefunden');
      }
      doi = registrationData.doi;
    } else {
      throw new Error('Ungültiges Format. Bitte geben Sie einen DOI oder eine Token-ID ein');
    }
    
    // Hole Blockchain-Daten
    const onChainData = await doiRegistryContract.getDOIData(tokenId);
    
    // Hole NFT-Metadaten
    const nftMetadata = await fetchIPFSJson(onChainData.metadataUri.replace('ipfs://', ''));
    
    // Hole Zertifikat
    const certificateUri = onChainData.certificateUri;
    
    // Überprüfe DOI bei offiziellen Quellen
    const doiValidation = await validateDOI(doi);
    
    return {
      success: true,
      verified: onChainData.doi === doi && doiValidation.success,
      doi: doi,
      tokenId: tokenId,
      ownerAddress: onChainData.owner,
      registrationTimestamp: new Date(onChainData.timestamp * 1000).toISOString(),
      transactionHash: onChainData.transactionHash,
      blockNumber: onChainData.blockNumber,
      metadataUri: onChainData.metadataUri,
      certificateUri: certificateUri,
      publicationData: {
        title: nftMetadata.name,
        authors: nftMetadata.properties.authors,
        publicationDate: nftMetadata.attributes.find(a => a.trait_type === 'Publication Date')?.value,
        journal: nftMetadata.properties.journal,
        publisher: nftMetadata.properties.publisher
      }
    };
  } catch (error) {
    logger.error(`Verifizierungsfehler: ${error.message}`, { identifier });
    return {
      success: false,
      error: error.message
    };
  }
}
```

## Gesamtprozess-Integration

Der vollständige DOI-zu-NFT-Konvertierungsprozess wird durch einen orchestrierten Workflow zusammengeführt:

```javascript
async function convertDOItoNFT(doi, ownerAddress) {
  try {
    // 1. DOI validieren
    logger.info(`Starte DOI-zu-NFT-Konvertierung für ${doi}`);
    const validationResult = await validateDOI(doi);
    if (!validationResult.success) {
      throw new Error(`DOI-Validierung fehlgeschlagen: ${validationResult.error}`);
    }
    
    // 2. Metadaten anreichern
    const enrichmentResult = await enrichMetadata(doi, validationResult.metadata);
    const enrichedMetadata = enrichmentResult.success ? 
      enrichmentResult.metadata : validationResult.metadata;
    
    // 3. In dezentraler Speicherung ablegen
    const storageResult = await storeInDecentralizedStorage(doi, enrichedMetadata);
    if (!storageResult.success) {
      throw new Error(`Dezentrale Speicherung fehlgeschlagen: ${storageResult.error}`);
    }
    
    // 4. NFT erstellen
    const nftResult = await createNFT(doi, storageResult, enrichedMetadata, ownerAddress);
    if (!nftResult.success) {
      throw new Error(`NFT-Erstellung fehlgeschlagen: ${nftResult.error}`);
    }
    
    // 5. Zertifikat generieren
    const certificateResult = await generateCertificate(doi, nftResult, enrichedMetadata);
    if (!certificateResult.success) {
      throw new Error(`Zertifikat-Generierung fehlgeschlagen: ${certificateResult.error}`);
    }
    
    // 6. In Blockchain registrieren
    const registrationResult = await registerOnBlockchain(doi, nftResult, certificateResult);
    if (!registrationResult.success) {
      throw new Error(`Blockchain-Registrierung fehlgeschlagen: ${registrationResult.error}`);
    }
    
    // 7. Erfolgreiche Konvertierung zurückgeben
    return {
      success: true,
      doi: doi,
      tokenId: nftResult.tokenId,
      ownerAddress: ownerAddress,
      transactionHash: registrationResult.transactionHash,
      metadataUri: nftResult.metadataUri,
      certificateUri: certificateResult.certificateUri,
      verificationUrl: certificateResult.verificationUrl
    };
  } catch (error) {
    logger.error(`DOI-zu-NFT-Konvertierungsfehler: ${error.message}`, { doi });
    return {
      success: false,
      doi: doi,
      error: error.message
    };
  }
}
```

## Sicherheits- und Datenschutzaspekte

### Sicherheitsmaßnahmen
- Kryptografische Verifizierung aller Transaktionen
- Mehrfache Validierung von DOIs und Metadaten
- Redundante Speicherung kritischer Daten
- Regelmäßige Sicherheitsaudits der Smart Contracts

### Datenschutzaspekte
- Transparente Offenlegung aller gespeicherten Daten
- Einhaltung relevanter Datenschutzbestimmungen
- Optionale Anonymisierung sensibler Informationen
- Klare Berechtigungsstrukturen für Datenänderungen

## Fazit

Der DOI-zu-NFT-Konvertierungsprozess in DeSci-Scholar bietet eine robuste, transparente und dezentralisierte Methode zur Authentifizierung und Verifizierung wissenschaftlicher Publikationen. Durch die Kombination etablierter wissenschaftlicher Identifikatoren (DOIs) mit moderner Blockchain-Technologie schafft DeSci-Scholar eine Brücke zwischen traditionellen wissenschaftlichen Infrastrukturen und innovativen dezentralen Systemen.