# arXiv-Integration für DeSci-Scholar

Diese Dokumentation beschreibt die Integration von arXiv in DeSci-Scholar, die es ermöglicht, wissenschaftliche Publikationen von arXiv zu suchen, zu importieren und als NFTs zu tokenisieren.

## Überblick

Die arXiv-Integration bietet folgende Funktionen:

- Suche nach wissenschaftlichen Publikationen in arXiv
- <PERSON>b<PERSON><PERSON> von Publikationsdetails anhand der arXiv-ID
- Import von arXiv-Publikationen in DeSci-Scholar
- Erstellung von NFTs für arXiv-Publikationen mit DOIs
- Abruf der neuesten Publikationen in bestimmten Kategorien
- Statistiken zur arXiv-Integration

## API-Endpunkte

### Suche nach Publikationen

**Anfrage**
```
GET /api/arxiv/search
```

**Query-Parameter**
- `query` - Allgemeiner Suchbegriff
- `title` - Suche im Titel
- `author` - Suche nach Autor
- `category` - Suche nach Kategorie (z.B. "physics", "cs.AI")
- `id` - Suche nach arXiv-ID
- `maxResults` - <PERSON><PERSON> (Standard: 10)
- `start` - <PERSON>position für Paginierung (Standard: 0)
- `sortBy` - Sortierkriterium ("relevance", "lastUpdatedDate", "submittedDate")
- `sortOrder` - Sortierreihenfolge ("ascending", "descending")

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalResults": 42,
  "startIndex": 0,
  "itemsPerPage": 10,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikationsdetails abrufen

**Anfrage**
```
GET /api/arxiv/paper/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Antwort (200 OK)**
```json
{
  "success": true,
  "paper": {
    "id": "2304.12345",
    "title": "Anwendung von KI in der Medizin",
    "abstract": "Diese Studie untersucht...",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "categories": ["cs.AI", "cs.LG"],
    "primaryCategory": "cs.AI",
    "published": "2023-04-15T14:22:10Z",
    "updated": "2023-04-16T09:15:30Z",
    "doi": "10.12345/example.doi",
    "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
    "comment": "20 pages, 8 figures",
    "pdfUrl": "https://arxiv.org/pdf/2304.12345",
    "arxivUrl": "https://arxiv.org/abs/2304.12345"
  }
}
```

### Neueste Publikationen in einer Kategorie abrufen

**Anfrage**
```
GET /api/arxiv/recent/:category
```

**Parameter**
- `category` - arXiv-Kategorie (z.B. "cs.AI", "physics")

**Query-Parameter**
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 30)

**Antwort (200 OK)**
```json
{
  "success": true,
  "category": "cs.AI",
  "categoryName": "Künstliche Intelligenz",
  "totalResults": 42,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikation importieren

**Anfrage**
```
POST /api/arxiv/import/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "keywords": ["Künstliche Intelligenz", "Machine Learning"],
    "publishedDate": "2023-04-15T14:22:10Z",
    "doi": "10.12345/example.doi",
    "license": "arXiv non-exclusive license to distribute",
    "status": "published",
    "arxivMetadata": {
      "id": "2304.12345",
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "updated": "2023-04-16T09:15:30Z",
      "comment": "20 pages, 8 figures",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "url": "https://arxiv.org/abs/2304.12345",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345"
    },
    "storage": {
      "ipfs": "QmXa12S5sdf87gh4j56k7l...",
      "bittorrent": "a1b2c3d4e5f6...",
      "timestamp": "2023-05-10T08:12:45Z"
    }
  },
  "message": "Publikation erfolgreich importiert"
}
```

### Mehrere Publikationen importieren

**Anfrage**
```
POST /api/arxiv/import
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 42,
  "imported": 5,
  "failed": 0,
  "publications": [
    {
      "id": "pub-789012",
      "title": "Quantum Computing Applications",
      "authors": ["Dr. John Doe", "Prof. Jane Smith"],
      "abstract": "This study explores...",
      "keywords": ["Quantum Computing", "Quantum Physics"],
      "publishedDate": "2023-04-15T14:22:10Z",
      "doi": "10.12345/example.doi",
      "license": "arXiv non-exclusive license to distribute",
      "status": "published",
      "arxivMetadata": {
        "id": "2304.12345",
        "categories": ["quant-ph", "cs.ET"],
        "primaryCategory": "quant-ph",
        "updated": "2023-04-16T09:15:30Z",
        "comment": "15 pages, 5 figures",
        "journalRef": "Journal of Quantum Computing, Vol. 10, pp. 45-60",
        "url": "https://arxiv.org/abs/2304.12345",
        "pdfUrl": "https://arxiv.org/pdf/2304.12345"
      }
    },
    // Weitere Publikationen...
  ]
}
```

### NFT für eine Publikation erstellen

**Anfrage**
```
POST /api/arxiv/nft/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Body**
```json
{
  "recipient": "0x1234567890abcdef1234567890abcdef12345678",
  "royaltyPercentage": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "doi": "10.12345/example.doi"
  },
  "nft": {
    "tokenId": "42",
    "metadata": {
      "name": "Anwendung von KI in der Medizin",
      "description": "Diese Studie untersucht...",
      "external_url": "https://arxiv.org/abs/2304.12345",
      "attributes": [
        { "trait_type": "Type", "value": "Scientific Paper" },
        { "trait_type": "Source", "value": "arXiv" },
        { "trait_type": "arXiv ID", "value": "2304.12345" },
        { "trait_type": "DOI", "value": "10.12345/example.doi" },
        { "trait_type": "Category", "value": "cs.AI" },
        { "trait_type": "Authors", "value": "Dr. Maria Schmidt, Prof. Hans Müller" },
        { "trait_type": "Publication Date", "value": "2023-04-15" }
      ]
    }
  },
  "tokenId": "42",
  "transactionHash": "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
  "message": "NFT erfolgreich erstellt"
}
```

### NFTs für mehrere Publikationen erstellen

**Anfrage**
```
POST /api/arxiv/nft
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5,
  "nftOptions": {
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 5,
  "withDOI": 3,
  "createdNFTs": 3,
  "failed": 0,
  "nfts": [
    {
      "publication": {
        "id": "pub-789012",
        "title": "Quantum Computing Applications",
        "authors": ["Dr. John Doe", "Prof. Jane Smith"],
        "abstract": "This study explores...",
        "doi": "10.12345/example.doi"
      },
      "nft": {
        "tokenId": "42",
        "metadata": {
          "name": "Quantum Computing Applications",
          "description": "This study explores...",
          "external_url": "https://arxiv.org/abs/2304.12345",
          "attributes": [
            { "trait_type": "Type", "value": "Scientific Paper" },
            { "trait_type": "Source", "value": "arXiv" },
            { "trait_type": "arXiv ID", "value": "2304.12345" },
            { "trait_type": "DOI", "value": "10.12345/example.doi" },
            { "trait_type": "Category", "value": "quant-ph" },
            { "trait_type": "Authors", "value": "Dr. John Doe, Prof. Jane Smith" },
            { "trait_type": "Publication Date", "value": "2023-04-15" }
          ]
        }
      },
      "tokenId": "42"
    },
    // Weitere NFTs...
  ]
}
```

### Statistiken abrufen

**Anfrage**
```
GET /api/arxiv/stats
```

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "stats": {
    "importedPapers": 42,
    "createdNFTs": 15,
    "failedImports": 3,
    "papersByCategory": {
      "cs.AI": 12,
      "quant-ph": 8,
      "physics": 22
    },
    "apiStats": {
      "totalRequests": 150,
      "successfulRequests": 147,
      "failedRequests": 3,
      "retries": 5,
      "requestsByEndpoint": {
        "query": 150
      },
      "successRate": "98.00%"
    },
    "successRate": "92.86%"
  }
}
```

## arXiv-Kategorien

Die arXiv-Integration unterstützt alle arXiv-Kategorien, darunter:

- **Physik**: astro-ph, cond-mat, gr-qc, hep-ex, hep-lat, hep-ph, hep-th, math-ph, nlin, nucl-ex, nucl-th, physics, quant-ph
- **Mathematik**: math
- **Informatik**: cs.AI, cs.AR, cs.CC, cs.CE, cs.CG, cs.CL, cs.CR, cs.CV, cs.CY, cs.DB, cs.DC, cs.DL, cs.DM, cs.DS, cs.ET, cs.FL, cs.GL, cs.GR, cs.GT, cs.HC, cs.IR, cs.IT, cs.LG, cs.LO, cs.MA, cs.MM, cs.MS, cs.NA, cs.NE, cs.NI, cs.OS, cs.PF, cs.PL, cs.RO, cs.SC, cs.SD, cs.SE, cs.SI, cs.SY
- **Quantitative Biologie**: q-bio
- **Quantitative Finanzen**: q-fin
- **Statistik**: stat
- **Elektrotechnik und Systemwissenschaft**: eess
- **Wirtschaftswissenschaften**: econ

Eine vollständige Liste der Kategorien und Unterkategorien finden Sie auf der [arXiv-Taxonomie-Seite](https://arxiv.org/category_taxonomy).

## Nutzungshinweise

Bei der Verwendung der arXiv-Integration sind folgende Punkte zu beachten:

1. **API-Limits**: Die arXiv API hat Ratenbegrenzungen. Es wird empfohlen, nicht mehr als eine Anfrage alle 3 Sekunden zu stellen.

2. **Urheberrecht**: Die meisten arXiv-Publikationen stehen unter der "arXiv non-exclusive license to distribute", die keine Weiterverbreitung erlaubt. Stellen Sie sicher, dass Sie die Urheberrechte respektieren.

3. **DOIs und NFTs**: Nur Publikationen mit DOIs können als NFTs tokenisiert werden. Nicht alle arXiv-Publikationen haben DOIs.

4. **Metadaten**: Die arXiv API liefert nur Metadaten, keinen Volltext. Der Volltext kann über die PDF-URL abgerufen werden, aber dies ist nicht Teil der API-Integration.

5. **Kategorien**: Bei der Suche nach Kategorien können sowohl Hauptkategorien (z.B. "physics") als auch Unterkategorien (z.B. "cs.AI") verwendet werden.

## Beispiele

### Curl-Beispiel: Suche nach Publikationen

```bash
curl -X GET "http://localhost:3000/api/arxiv/search?query=quantum+computing&category=quant-ph&maxResults=5"
```

### Curl-Beispiel: Publikation importieren

```bash
curl -X POST "http://localhost:3000/api/arxiv/import/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..."
```

### Curl-Beispiel: NFT erstellen

```bash
curl -X POST "http://localhost:3000/api/arxiv/nft/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..." \
  -H "Content-Type: application/json" \
  -d '{
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }'
```# arXiv-Integration für DeSci-Scholar

Diese Dokumentation beschreibt die Integration von arXiv in DeSci-Scholar, die es ermöglicht, wissenschaftliche Publikationen von arXiv zu suchen, zu importieren und als NFTs zu tokenisieren.

## Überblick

Die arXiv-Integration bietet folgende Funktionen:

- Suche nach wissenschaftlichen Publikationen in arXiv
- Abruf von Publikationsdetails anhand der arXiv-ID
- Import von arXiv-Publikationen in DeSci-Scholar
- Erstellung von NFTs für arXiv-Publikationen mit DOIs
- Abruf der neuesten Publikationen in bestimmten Kategorien
- Statistiken zur arXiv-Integration

## API-Endpunkte

### Suche nach Publikationen

**Anfrage**
```
GET /api/arxiv/search
```

**Query-Parameter**
- `query` - Allgemeiner Suchbegriff
- `title` - Suche im Titel
- `author` - Suche nach Autor
- `category` - Suche nach Kategorie (z.B. "physics", "cs.AI")
- `id` - Suche nach arXiv-ID
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 10)
- `start` - Startposition für Paginierung (Standard: 0)
- `sortBy` - Sortierkriterium ("relevance", "lastUpdatedDate", "submittedDate")
- `sortOrder` - Sortierreihenfolge ("ascending", "descending")

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalResults": 42,
  "startIndex": 0,
  "itemsPerPage": 10,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikationsdetails abrufen

**Anfrage**
```
GET /api/arxiv/paper/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Antwort (200 OK)**
```json
{
  "success": true,
  "paper": {
    "id": "2304.12345",
    "title": "Anwendung von KI in der Medizin",
    "abstract": "Diese Studie untersucht...",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "categories": ["cs.AI", "cs.LG"],
    "primaryCategory": "cs.AI",
    "published": "2023-04-15T14:22:10Z",
    "updated": "2023-04-16T09:15:30Z",
    "doi": "10.12345/example.doi",
    "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
    "comment": "20 pages, 8 figures",
    "pdfUrl": "https://arxiv.org/pdf/2304.12345",
    "arxivUrl": "https://arxiv.org/abs/2304.12345"
  }
}
```

### Neueste Publikationen in einer Kategorie abrufen

**Anfrage**
```
GET /api/arxiv/recent/:category
```

**Parameter**
- `category` - arXiv-Kategorie (z.B. "cs.AI", "physics")

**Query-Parameter**
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 30)

**Antwort (200 OK)**
```json
{
  "success": true,
  "category": "cs.AI",
  "categoryName": "Künstliche Intelligenz",
  "totalResults": 42,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikation importieren

**Anfrage**
```
POST /api/arxiv/import/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "keywords": ["Künstliche Intelligenz", "Machine Learning"],
    "publishedDate": "2023-04-15T14:22:10Z",
    "doi": "10.12345/example.doi",
    "license": "arXiv non-exclusive license to distribute",
    "status": "published",
    "arxivMetadata": {
      "id": "2304.12345",
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "updated": "2023-04-16T09:15:30Z",
      "comment": "20 pages, 8 figures",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "url": "https://arxiv.org/abs/2304.12345",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345"
    },
    "storage": {
      "ipfs": "QmXa12S5sdf87gh4j56k7l...",
      "bittorrent": "a1b2c3d4e5f6...",
      "timestamp": "2023-05-10T08:12:45Z"
    }
  },
  "message": "Publikation erfolgreich importiert"
}
```

### Mehrere Publikationen importieren

**Anfrage**
```
POST /api/arxiv/import
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 42,
  "imported": 5,
  "failed": 0,
  "publications": [
    {
      "id": "pub-789012",
      "title": "Quantum Computing Applications",
      "authors": ["Dr. John Doe", "Prof. Jane Smith"],
      "abstract": "This study explores...",
      "keywords": ["Quantum Computing", "Quantum Physics"],
      "publishedDate": "2023-04-15T14:22:10Z",
      "doi": "10.12345/example.doi",
      "license": "arXiv non-exclusive license to distribute",
      "status": "published",
      "arxivMetadata": {
        "id": "2304.12345",
        "categories": ["quant-ph", "cs.ET"],
        "primaryCategory": "quant-ph",
        "updated": "2023-04-16T09:15:30Z",
        "comment": "15 pages, 5 figures",
        "journalRef": "Journal of Quantum Computing, Vol. 10, pp. 45-60",
        "url": "https://arxiv.org/abs/2304.12345",
        "pdfUrl": "https://arxiv.org/pdf/2304.12345"
      }
    },
    // Weitere Publikationen...
  ]
}
```

### NFT für eine Publikation erstellen

**Anfrage**
```
POST /api/arxiv/nft/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Body**
```json
{
  "recipient": "0x1234567890abcdef1234567890abcdef12345678",
  "royaltyPercentage": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "doi": "10.12345/example.doi"
  },
  "nft": {
    "tokenId": "42",
    "metadata": {
      "name": "Anwendung von KI in der Medizin",
      "description": "Diese Studie untersucht...",
      "external_url": "https://arxiv.org/abs/2304.12345",
      "attributes": [
        { "trait_type": "Type", "value": "Scientific Paper" },
        { "trait_type": "Source", "value": "arXiv" },
        { "trait_type": "arXiv ID", "value": "2304.12345" },
        { "trait_type": "DOI", "value": "10.12345/example.doi" },
        { "trait_type": "Category", "value": "cs.AI" },
        { "trait_type": "Authors", "value": "Dr. Maria Schmidt, Prof. Hans Müller" },
        { "trait_type": "Publication Date", "value": "2023-04-15" }
      ]
    }
  },
  "tokenId": "42",
  "transactionHash": "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
  "message": "NFT erfolgreich erstellt"
}
```

### NFTs für mehrere Publikationen erstellen

**Anfrage**
```
POST /api/arxiv/nft
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5,
  "nftOptions": {
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 5,
  "withDOI": 3,
  "createdNFTs": 3,
  "failed": 0,
  "nfts": [
    {
      "publication": {
        "id": "pub-789012",
        "title": "Quantum Computing Applications",
        "authors": ["Dr. John Doe", "Prof. Jane Smith"],
        "abstract": "This study explores...",
        "doi": "10.12345/example.doi"
      },
      "nft": {
        "tokenId": "42",
        "metadata": {
          "name": "Quantum Computing Applications",
          "description": "This study explores...",
          "external_url": "https://arxiv.org/abs/2304.12345",
          "attributes": [
            { "trait_type": "Type", "value": "Scientific Paper" },
            { "trait_type": "Source", "value": "arXiv" },
            { "trait_type": "arXiv ID", "value": "2304.12345" },
            { "trait_type": "DOI", "value": "10.12345/example.doi" },
            { "trait_type": "Category", "value": "quant-ph" },
            { "trait_type": "Authors", "value": "Dr. John Doe, Prof. Jane Smith" },
            { "trait_type": "Publication Date", "value": "2023-04-15" }
          ]
        }
      },
      "tokenId": "42"
    },
    // Weitere NFTs...
  ]
}
```

### Statistiken abrufen

**Anfrage**
```
GET /api/arxiv/stats
```

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "stats": {
    "importedPapers": 42,
    "createdNFTs": 15,
    "failedImports": 3,
    "papersByCategory": {
      "cs.AI": 12,
      "quant-ph": 8,
      "physics": 22
    },
    "apiStats": {
      "totalRequests": 150,
      "successfulRequests": 147,
      "failedRequests": 3,
      "retries": 5,
      "requestsByEndpoint": {
        "query": 150
      },
      "successRate": "98.00%"
    },
    "successRate": "92.86%"
  }
}
```

## arXiv-Kategorien

Die arXiv-Integration unterstützt alle arXiv-Kategorien, darunter:

- **Physik**: astro-ph, cond-mat, gr-qc, hep-ex, hep-lat, hep-ph, hep-th, math-ph, nlin, nucl-ex, nucl-th, physics, quant-ph
- **Mathematik**: math
- **Informatik**: cs.AI, cs.AR, cs.CC, cs.CE, cs.CG, cs.CL, cs.CR, cs.CV, cs.CY, cs.DB, cs.DC, cs.DL, cs.DM, cs.DS, cs.ET, cs.FL, cs.GL, cs.GR, cs.GT, cs.HC, cs.IR, cs.IT, cs.LG, cs.LO, cs.MA, cs.MM, cs.MS, cs.NA, cs.NE, cs.NI, cs.OS, cs.PF, cs.PL, cs.RO, cs.SC, cs.SD, cs.SE, cs.SI, cs.SY
- **Quantitative Biologie**: q-bio
- **Quantitative Finanzen**: q-fin
- **Statistik**: stat
- **Elektrotechnik und Systemwissenschaft**: eess
- **Wirtschaftswissenschaften**: econ

Eine vollständige Liste der Kategorien und Unterkategorien finden Sie auf der [arXiv-Taxonomie-Seite](https://arxiv.org/category_taxonomy).

## Nutzungshinweise

Bei der Verwendung der arXiv-Integration sind folgende Punkte zu beachten:

1. **API-Limits**: Die arXiv API hat Ratenbegrenzungen. Es wird empfohlen, nicht mehr als eine Anfrage alle 3 Sekunden zu stellen.

2. **Urheberrecht**: Die meisten arXiv-Publikationen stehen unter der "arXiv non-exclusive license to distribute", die keine Weiterverbreitung erlaubt. Stellen Sie sicher, dass Sie die Urheberrechte respektieren.

3. **DOIs und NFTs**: Nur Publikationen mit DOIs können als NFTs tokenisiert werden. Nicht alle arXiv-Publikationen haben DOIs.

4. **Metadaten**: Die arXiv API liefert nur Metadaten, keinen Volltext. Der Volltext kann über die PDF-URL abgerufen werden, aber dies ist nicht Teil der API-Integration.

5. **Kategorien**: Bei der Suche nach Kategorien können sowohl Hauptkategorien (z.B. "physics") als auch Unterkategorien (z.B. "cs.AI") verwendet werden.

## Beispiele

### Curl-Beispiel: Suche nach Publikationen

```bash
curl -X GET "http://localhost:3000/api/arxiv/search?query=quantum+computing&category=quant-ph&maxResults=5"
```

### Curl-Beispiel: Publikation importieren

```bash
curl -X POST "http://localhost:3000/api/arxiv/import/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..."
```

### Curl-Beispiel: NFT erstellen

```bash
curl -X POST "http://localhost:3000/api/arxiv/nft/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..." \
  -H "Content-Type: application/json" \
  -d '{
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }'
```# arXiv-Integration für DeSci-Scholar

Diese Dokumentation beschreibt die Integration von arXiv in DeSci-Scholar, die es ermöglicht, wissenschaftliche Publikationen von arXiv zu suchen, zu importieren und als NFTs zu tokenisieren.

## Überblick

Die arXiv-Integration bietet folgende Funktionen:

- Suche nach wissenschaftlichen Publikationen in arXiv
- Abruf von Publikationsdetails anhand der arXiv-ID
- Import von arXiv-Publikationen in DeSci-Scholar
- Erstellung von NFTs für arXiv-Publikationen mit DOIs
- Abruf der neuesten Publikationen in bestimmten Kategorien
- Statistiken zur arXiv-Integration

## API-Endpunkte

### Suche nach Publikationen

**Anfrage**
```
GET /api/arxiv/search
```

**Query-Parameter**
- `query` - Allgemeiner Suchbegriff
- `title` - Suche im Titel
- `author` - Suche nach Autor
- `category` - Suche nach Kategorie (z.B. "physics", "cs.AI")
- `id` - Suche nach arXiv-ID
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 10)
- `start` - Startposition für Paginierung (Standard: 0)
- `sortBy` - Sortierkriterium ("relevance", "lastUpdatedDate", "submittedDate")
- `sortOrder` - Sortierreihenfolge ("ascending", "descending")

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalResults": 42,
  "startIndex": 0,
  "itemsPerPage": 10,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikationsdetails abrufen

**Anfrage**
```
GET /api/arxiv/paper/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Antwort (200 OK)**
```json
{
  "success": true,
  "paper": {
    "id": "2304.12345",
    "title": "Anwendung von KI in der Medizin",
    "abstract": "Diese Studie untersucht...",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "categories": ["cs.AI", "cs.LG"],
    "primaryCategory": "cs.AI",
    "published": "2023-04-15T14:22:10Z",
    "updated": "2023-04-16T09:15:30Z",
    "doi": "10.12345/example.doi",
    "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
    "comment": "20 pages, 8 figures",
    "pdfUrl": "https://arxiv.org/pdf/2304.12345",
    "arxivUrl": "https://arxiv.org/abs/2304.12345"
  }
}
```

### Neueste Publikationen in einer Kategorie abrufen

**Anfrage**
```
GET /api/arxiv/recent/:category
```

**Parameter**
- `category` - arXiv-Kategorie (z.B. "cs.AI", "physics")

**Query-Parameter**
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 30)

**Antwort (200 OK)**
```json
{
  "success": true,
  "category": "cs.AI",
  "categoryName": "Künstliche Intelligenz",
  "totalResults": 42,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikation importieren

**Anfrage**
```
POST /api/arxiv/import/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "keywords": ["Künstliche Intelligenz", "Machine Learning"],
    "publishedDate": "2023-04-15T14:22:10Z",
    "doi": "10.12345/example.doi",
    "license": "arXiv non-exclusive license to distribute",
    "status": "published",
    "arxivMetadata": {
      "id": "2304.12345",
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "updated": "2023-04-16T09:15:30Z",
      "comment": "20 pages, 8 figures",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "url": "https://arxiv.org/abs/2304.12345",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345"
    },
    "storage": {
      "ipfs": "QmXa12S5sdf87gh4j56k7l...",
      "bittorrent": "a1b2c3d4e5f6...",
      "timestamp": "2023-05-10T08:12:45Z"
    }
  },
  "message": "Publikation erfolgreich importiert"
}
```

### Mehrere Publikationen importieren

**Anfrage**
```
POST /api/arxiv/import
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 42,
  "imported": 5,
  "failed": 0,
  "publications": [
    {
      "id": "pub-789012",
      "title": "Quantum Computing Applications",
      "authors": ["Dr. John Doe", "Prof. Jane Smith"],
      "abstract": "This study explores...",
      "keywords": ["Quantum Computing", "Quantum Physics"],
      "publishedDate": "2023-04-15T14:22:10Z",
      "doi": "10.12345/example.doi",
      "license": "arXiv non-exclusive license to distribute",
      "status": "published",
      "arxivMetadata": {
        "id": "2304.12345",
        "categories": ["quant-ph", "cs.ET"],
        "primaryCategory": "quant-ph",
        "updated": "2023-04-16T09:15:30Z",
        "comment": "15 pages, 5 figures",
        "journalRef": "Journal of Quantum Computing, Vol. 10, pp. 45-60",
        "url": "https://arxiv.org/abs/2304.12345",
        "pdfUrl": "https://arxiv.org/pdf/2304.12345"
      }
    },
    // Weitere Publikationen...
  ]
}
```

### NFT für eine Publikation erstellen

**Anfrage**
```
POST /api/arxiv/nft/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Body**
```json
{
  "recipient": "0x1234567890abcdef1234567890abcdef12345678",
  "royaltyPercentage": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "doi": "10.12345/example.doi"
  },
  "nft": {
    "tokenId": "42",
    "metadata": {
      "name": "Anwendung von KI in der Medizin",
      "description": "Diese Studie untersucht...",
      "external_url": "https://arxiv.org/abs/2304.12345",
      "attributes": [
        { "trait_type": "Type", "value": "Scientific Paper" },
        { "trait_type": "Source", "value": "arXiv" },
        { "trait_type": "arXiv ID", "value": "2304.12345" },
        { "trait_type": "DOI", "value": "10.12345/example.doi" },
        { "trait_type": "Category", "value": "cs.AI" },
        { "trait_type": "Authors", "value": "Dr. Maria Schmidt, Prof. Hans Müller" },
        { "trait_type": "Publication Date", "value": "2023-04-15" }
      ]
    }
  },
  "tokenId": "42",
  "transactionHash": "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
  "message": "NFT erfolgreich erstellt"
}
```

### NFTs für mehrere Publikationen erstellen

**Anfrage**
```
POST /api/arxiv/nft
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5,
  "nftOptions": {
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 5,
  "withDOI": 3,
  "createdNFTs": 3,
  "failed": 0,
  "nfts": [
    {
      "publication": {
        "id": "pub-789012",
        "title": "Quantum Computing Applications",
        "authors": ["Dr. John Doe", "Prof. Jane Smith"],
        "abstract": "This study explores...",
        "doi": "10.12345/example.doi"
      },
      "nft": {
        "tokenId": "42",
        "metadata": {
          "name": "Quantum Computing Applications",
          "description": "This study explores...",
          "external_url": "https://arxiv.org/abs/2304.12345",
          "attributes": [
            { "trait_type": "Type", "value": "Scientific Paper" },
            { "trait_type": "Source", "value": "arXiv" },
            { "trait_type": "arXiv ID", "value": "2304.12345" },
            { "trait_type": "DOI", "value": "10.12345/example.doi" },
            { "trait_type": "Category", "value": "quant-ph" },
            { "trait_type": "Authors", "value": "Dr. John Doe, Prof. Jane Smith" },
            { "trait_type": "Publication Date", "value": "2023-04-15" }
          ]
        }
      },
      "tokenId": "42"
    },
    // Weitere NFTs...
  ]
}
```

### Statistiken abrufen

**Anfrage**
```
GET /api/arxiv/stats
```

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "stats": {
    "importedPapers": 42,
    "createdNFTs": 15,
    "failedImports": 3,
    "papersByCategory": {
      "cs.AI": 12,
      "quant-ph": 8,
      "physics": 22
    },
    "apiStats": {
      "totalRequests": 150,
      "successfulRequests": 147,
      "failedRequests": 3,
      "retries": 5,
      "requestsByEndpoint": {
        "query": 150
      },
      "successRate": "98.00%"
    },
    "successRate": "92.86%"
  }
}
```

## arXiv-Kategorien

Die arXiv-Integration unterstützt alle arXiv-Kategorien, darunter:

- **Physik**: astro-ph, cond-mat, gr-qc, hep-ex, hep-lat, hep-ph, hep-th, math-ph, nlin, nucl-ex, nucl-th, physics, quant-ph
- **Mathematik**: math
- **Informatik**: cs.AI, cs.AR, cs.CC, cs.CE, cs.CG, cs.CL, cs.CR, cs.CV, cs.CY, cs.DB, cs.DC, cs.DL, cs.DM, cs.DS, cs.ET, cs.FL, cs.GL, cs.GR, cs.GT, cs.HC, cs.IR, cs.IT, cs.LG, cs.LO, cs.MA, cs.MM, cs.MS, cs.NA, cs.NE, cs.NI, cs.OS, cs.PF, cs.PL, cs.RO, cs.SC, cs.SD, cs.SE, cs.SI, cs.SY
- **Quantitative Biologie**: q-bio
- **Quantitative Finanzen**: q-fin
- **Statistik**: stat
- **Elektrotechnik und Systemwissenschaft**: eess
- **Wirtschaftswissenschaften**: econ

Eine vollständige Liste der Kategorien und Unterkategorien finden Sie auf der [arXiv-Taxonomie-Seite](https://arxiv.org/category_taxonomy).

## Nutzungshinweise

Bei der Verwendung der arXiv-Integration sind folgende Punkte zu beachten:

1. **API-Limits**: Die arXiv API hat Ratenbegrenzungen. Es wird empfohlen, nicht mehr als eine Anfrage alle 3 Sekunden zu stellen.

2. **Urheberrecht**: Die meisten arXiv-Publikationen stehen unter der "arXiv non-exclusive license to distribute", die keine Weiterverbreitung erlaubt. Stellen Sie sicher, dass Sie die Urheberrechte respektieren.

3. **DOIs und NFTs**: Nur Publikationen mit DOIs können als NFTs tokenisiert werden. Nicht alle arXiv-Publikationen haben DOIs.

4. **Metadaten**: Die arXiv API liefert nur Metadaten, keinen Volltext. Der Volltext kann über die PDF-URL abgerufen werden, aber dies ist nicht Teil der API-Integration.

5. **Kategorien**: Bei der Suche nach Kategorien können sowohl Hauptkategorien (z.B. "physics") als auch Unterkategorien (z.B. "cs.AI") verwendet werden.

## Beispiele

### Curl-Beispiel: Suche nach Publikationen

```bash
curl -X GET "http://localhost:3000/api/arxiv/search?query=quantum+computing&category=quant-ph&maxResults=5"
```

### Curl-Beispiel: Publikation importieren

```bash
curl -X POST "http://localhost:3000/api/arxiv/import/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..."
```

### Curl-Beispiel: NFT erstellen

```bash
curl -X POST "http://localhost:3000/api/arxiv/nft/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..." \
  -H "Content-Type: application/json" \
  -d '{
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }'
```# arXiv-Integration für DeSci-Scholar

Diese Dokumentation beschreibt die Integration von arXiv in DeSci-Scholar, die es ermöglicht, wissenschaftliche Publikationen von arXiv zu suchen, zu importieren und als NFTs zu tokenisieren.

## Überblick

Die arXiv-Integration bietet folgende Funktionen:

- Suche nach wissenschaftlichen Publikationen in arXiv
- Abruf von Publikationsdetails anhand der arXiv-ID
- Import von arXiv-Publikationen in DeSci-Scholar
- Erstellung von NFTs für arXiv-Publikationen mit DOIs
- Abruf der neuesten Publikationen in bestimmten Kategorien
- Statistiken zur arXiv-Integration

## API-Endpunkte

### Suche nach Publikationen

**Anfrage**
```
GET /api/arxiv/search
```

**Query-Parameter**
- `query` - Allgemeiner Suchbegriff
- `title` - Suche im Titel
- `author` - Suche nach Autor
- `category` - Suche nach Kategorie (z.B. "physics", "cs.AI")
- `id` - Suche nach arXiv-ID
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 10)
- `start` - Startposition für Paginierung (Standard: 0)
- `sortBy` - Sortierkriterium ("relevance", "lastUpdatedDate", "submittedDate")
- `sortOrder` - Sortierreihenfolge ("ascending", "descending")

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalResults": 42,
  "startIndex": 0,
  "itemsPerPage": 10,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikationsdetails abrufen

**Anfrage**
```
GET /api/arxiv/paper/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Antwort (200 OK)**
```json
{
  "success": true,
  "paper": {
    "id": "2304.12345",
    "title": "Anwendung von KI in der Medizin",
    "abstract": "Diese Studie untersucht...",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "categories": ["cs.AI", "cs.LG"],
    "primaryCategory": "cs.AI",
    "published": "2023-04-15T14:22:10Z",
    "updated": "2023-04-16T09:15:30Z",
    "doi": "10.12345/example.doi",
    "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
    "comment": "20 pages, 8 figures",
    "pdfUrl": "https://arxiv.org/pdf/2304.12345",
    "arxivUrl": "https://arxiv.org/abs/2304.12345"
  }
}
```

### Neueste Publikationen in einer Kategorie abrufen

**Anfrage**
```
GET /api/arxiv/recent/:category
```

**Parameter**
- `category` - arXiv-Kategorie (z.B. "cs.AI", "physics")

**Query-Parameter**
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 30)

**Antwort (200 OK)**
```json
{
  "success": true,
  "category": "cs.AI",
  "categoryName": "Künstliche Intelligenz",
  "totalResults": 42,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikation importieren

**Anfrage**
```
POST /api/arxiv/import/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "keywords": ["Künstliche Intelligenz", "Machine Learning"],
    "publishedDate": "2023-04-15T14:22:10Z",
    "doi": "10.12345/example.doi",
    "license": "arXiv non-exclusive license to distribute",
    "status": "published",
    "arxivMetadata": {
      "id": "2304.12345",
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "updated": "2023-04-16T09:15:30Z",
      "comment": "20 pages, 8 figures",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "url": "https://arxiv.org/abs/2304.12345",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345"
    },
    "storage": {
      "ipfs": "QmXa12S5sdf87gh4j56k7l...",
      "bittorrent": "a1b2c3d4e5f6...",
      "timestamp": "2023-05-10T08:12:45Z"
    }
  },
  "message": "Publikation erfolgreich importiert"
}
```

### Mehrere Publikationen importieren

**Anfrage**
```
POST /api/arxiv/import
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 42,
  "imported": 5,
  "failed": 0,
  "publications": [
    {
      "id": "pub-789012",
      "title": "Quantum Computing Applications",
      "authors": ["Dr. John Doe", "Prof. Jane Smith"],
      "abstract": "This study explores...",
      "keywords": ["Quantum Computing", "Quantum Physics"],
      "publishedDate": "2023-04-15T14:22:10Z",
      "doi": "10.12345/example.doi",
      "license": "arXiv non-exclusive license to distribute",
      "status": "published",
      "arxivMetadata": {
        "id": "2304.12345",
        "categories": ["quant-ph", "cs.ET"],
        "primaryCategory": "quant-ph",
        "updated": "2023-04-16T09:15:30Z",
        "comment": "15 pages, 5 figures",
        "journalRef": "Journal of Quantum Computing, Vol. 10, pp. 45-60",
        "url": "https://arxiv.org/abs/2304.12345",
        "pdfUrl": "https://arxiv.org/pdf/2304.12345"
      }
    },
    // Weitere Publikationen...
  ]
}
```

### NFT für eine Publikation erstellen

**Anfrage**
```
POST /api/arxiv/nft/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Body**
```json
{
  "recipient": "0x1234567890abcdef1234567890abcdef12345678",
  "royaltyPercentage": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "doi": "10.12345/example.doi"
  },
  "nft": {
    "tokenId": "42",
    "metadata": {
      "name": "Anwendung von KI in der Medizin",
      "description": "Diese Studie untersucht...",
      "external_url": "https://arxiv.org/abs/2304.12345",
      "attributes": [
        { "trait_type": "Type", "value": "Scientific Paper" },
        { "trait_type": "Source", "value": "arXiv" },
        { "trait_type": "arXiv ID", "value": "2304.12345" },
        { "trait_type": "DOI", "value": "10.12345/example.doi" },
        { "trait_type": "Category", "value": "cs.AI" },
        { "trait_type": "Authors", "value": "Dr. Maria Schmidt, Prof. Hans Müller" },
        { "trait_type": "Publication Date", "value": "2023-04-15" }
      ]
    }
  },
  "tokenId": "42",
  "transactionHash": "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
  "message": "NFT erfolgreich erstellt"
}
```

### NFTs für mehrere Publikationen erstellen

**Anfrage**
```
POST /api/arxiv/nft
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5,
  "nftOptions": {
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 5,
  "withDOI": 3,
  "createdNFTs": 3,
  "failed": 0,
  "nfts": [
    {
      "publication": {
        "id": "pub-789012",
        "title": "Quantum Computing Applications",
        "authors": ["Dr. John Doe", "Prof. Jane Smith"],
        "abstract": "This study explores...",
        "doi": "10.12345/example.doi"
      },
      "nft": {
        "tokenId": "42",
        "metadata": {
          "name": "Quantum Computing Applications",
          "description": "This study explores...",
          "external_url": "https://arxiv.org/abs/2304.12345",
          "attributes": [
            { "trait_type": "Type", "value": "Scientific Paper" },
            { "trait_type": "Source", "value": "arXiv" },
            { "trait_type": "arXiv ID", "value": "2304.12345" },
            { "trait_type": "DOI", "value": "10.12345/example.doi" },
            { "trait_type": "Category", "value": "quant-ph" },
            { "trait_type": "Authors", "value": "Dr. John Doe, Prof. Jane Smith" },
            { "trait_type": "Publication Date", "value": "2023-04-15" }
          ]
        }
      },
      "tokenId": "42"
    },
    // Weitere NFTs...
  ]
}
```

### Statistiken abrufen

**Anfrage**
```
GET /api/arxiv/stats
```

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "stats": {
    "importedPapers": 42,
    "createdNFTs": 15,
    "failedImports": 3,
    "papersByCategory": {
      "cs.AI": 12,
      "quant-ph": 8,
      "physics": 22
    },
    "apiStats": {
      "totalRequests": 150,
      "successfulRequests": 147,
      "failedRequests": 3,
      "retries": 5,
      "requestsByEndpoint": {
        "query": 150
      },
      "successRate": "98.00%"
    },
    "successRate": "92.86%"
  }
}
```

## arXiv-Kategorien

Die arXiv-Integration unterstützt alle arXiv-Kategorien, darunter:

- **Physik**: astro-ph, cond-mat, gr-qc, hep-ex, hep-lat, hep-ph, hep-th, math-ph, nlin, nucl-ex, nucl-th, physics, quant-ph
- **Mathematik**: math
- **Informatik**: cs.AI, cs.AR, cs.CC, cs.CE, cs.CG, cs.CL, cs.CR, cs.CV, cs.CY, cs.DB, cs.DC, cs.DL, cs.DM, cs.DS, cs.ET, cs.FL, cs.GL, cs.GR, cs.GT, cs.HC, cs.IR, cs.IT, cs.LG, cs.LO, cs.MA, cs.MM, cs.MS, cs.NA, cs.NE, cs.NI, cs.OS, cs.PF, cs.PL, cs.RO, cs.SC, cs.SD, cs.SE, cs.SI, cs.SY
- **Quantitative Biologie**: q-bio
- **Quantitative Finanzen**: q-fin
- **Statistik**: stat
- **Elektrotechnik und Systemwissenschaft**: eess
- **Wirtschaftswissenschaften**: econ

Eine vollständige Liste der Kategorien und Unterkategorien finden Sie auf der [arXiv-Taxonomie-Seite](https://arxiv.org/category_taxonomy).

## Nutzungshinweise

Bei der Verwendung der arXiv-Integration sind folgende Punkte zu beachten:

1. **API-Limits**: Die arXiv API hat Ratenbegrenzungen. Es wird empfohlen, nicht mehr als eine Anfrage alle 3 Sekunden zu stellen.

2. **Urheberrecht**: Die meisten arXiv-Publikationen stehen unter der "arXiv non-exclusive license to distribute", die keine Weiterverbreitung erlaubt. Stellen Sie sicher, dass Sie die Urheberrechte respektieren.

3. **DOIs und NFTs**: Nur Publikationen mit DOIs können als NFTs tokenisiert werden. Nicht alle arXiv-Publikationen haben DOIs.

4. **Metadaten**: Die arXiv API liefert nur Metadaten, keinen Volltext. Der Volltext kann über die PDF-URL abgerufen werden, aber dies ist nicht Teil der API-Integration.

5. **Kategorien**: Bei der Suche nach Kategorien können sowohl Hauptkategorien (z.B. "physics") als auch Unterkategorien (z.B. "cs.AI") verwendet werden.

## Beispiele

### Curl-Beispiel: Suche nach Publikationen

```bash
curl -X GET "http://localhost:3000/api/arxiv/search?query=quantum+computing&category=quant-ph&maxResults=5"
```

### Curl-Beispiel: Publikation importieren

```bash
curl -X POST "http://localhost:3000/api/arxiv/import/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..."
```

### Curl-Beispiel: NFT erstellen

```bash
curl -X POST "http://localhost:3000/api/arxiv/nft/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..." \
  -H "Content-Type: application/json" \
  -d '{
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }'
```# arXiv-Integration für DeSci-Scholar

Diese Dokumentation beschreibt die Integration von arXiv in DeSci-Scholar, die es ermöglicht, wissenschaftliche Publikationen von arXiv zu suchen, zu importieren und als NFTs zu tokenisieren.

## Überblick

Die arXiv-Integration bietet folgende Funktionen:

- Suche nach wissenschaftlichen Publikationen in arXiv
- Abruf von Publikationsdetails anhand der arXiv-ID
- Import von arXiv-Publikationen in DeSci-Scholar
- Erstellung von NFTs für arXiv-Publikationen mit DOIs
- Abruf der neuesten Publikationen in bestimmten Kategorien
- Statistiken zur arXiv-Integration

## API-Endpunkte

### Suche nach Publikationen

**Anfrage**
```
GET /api/arxiv/search
```

**Query-Parameter**
- `query` - Allgemeiner Suchbegriff
- `title` - Suche im Titel
- `author` - Suche nach Autor
- `category` - Suche nach Kategorie (z.B. "physics", "cs.AI")
- `id` - Suche nach arXiv-ID
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 10)
- `start` - Startposition für Paginierung (Standard: 0)
- `sortBy` - Sortierkriterium ("relevance", "lastUpdatedDate", "submittedDate")
- `sortOrder` - Sortierreihenfolge ("ascending", "descending")

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalResults": 42,
  "startIndex": 0,
  "itemsPerPage": 10,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikationsdetails abrufen

**Anfrage**
```
GET /api/arxiv/paper/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Antwort (200 OK)**
```json
{
  "success": true,
  "paper": {
    "id": "2304.12345",
    "title": "Anwendung von KI in der Medizin",
    "abstract": "Diese Studie untersucht...",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "categories": ["cs.AI", "cs.LG"],
    "primaryCategory": "cs.AI",
    "published": "2023-04-15T14:22:10Z",
    "updated": "2023-04-16T09:15:30Z",
    "doi": "10.12345/example.doi",
    "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
    "comment": "20 pages, 8 figures",
    "pdfUrl": "https://arxiv.org/pdf/2304.12345",
    "arxivUrl": "https://arxiv.org/abs/2304.12345"
  }
}
```

### Neueste Publikationen in einer Kategorie abrufen

**Anfrage**
```
GET /api/arxiv/recent/:category
```

**Parameter**
- `category` - arXiv-Kategorie (z.B. "cs.AI", "physics")

**Query-Parameter**
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 30)

**Antwort (200 OK)**
```json
{
  "success": true,
  "category": "cs.AI",
  "categoryName": "Künstliche Intelligenz",
  "totalResults": 42,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikation importieren

**Anfrage**
```
POST /api/arxiv/import/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "keywords": ["Künstliche Intelligenz", "Machine Learning"],
    "publishedDate": "2023-04-15T14:22:10Z",
    "doi": "10.12345/example.doi",
    "license": "arXiv non-exclusive license to distribute",
    "status": "published",
    "arxivMetadata": {
      "id": "2304.12345",
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "updated": "2023-04-16T09:15:30Z",
      "comment": "20 pages, 8 figures",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "url": "https://arxiv.org/abs/2304.12345",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345"
    },
    "storage": {
      "ipfs": "QmXa12S5sdf87gh4j56k7l...",
      "bittorrent": "a1b2c3d4e5f6...",
      "timestamp": "2023-05-10T08:12:45Z"
    }
  },
  "message": "Publikation erfolgreich importiert"
}
```

### Mehrere Publikationen importieren

**Anfrage**
```
POST /api/arxiv/import
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 42,
  "imported": 5,
  "failed": 0,
  "publications": [
    {
      "id": "pub-789012",
      "title": "Quantum Computing Applications",
      "authors": ["Dr. John Doe", "Prof. Jane Smith"],
      "abstract": "This study explores...",
      "keywords": ["Quantum Computing", "Quantum Physics"],
      "publishedDate": "2023-04-15T14:22:10Z",
      "doi": "10.12345/example.doi",
      "license": "arXiv non-exclusive license to distribute",
      "status": "published",
      "arxivMetadata": {
        "id": "2304.12345",
        "categories": ["quant-ph", "cs.ET"],
        "primaryCategory": "quant-ph",
        "updated": "2023-04-16T09:15:30Z",
        "comment": "15 pages, 5 figures",
        "journalRef": "Journal of Quantum Computing, Vol. 10, pp. 45-60",
        "url": "https://arxiv.org/abs/2304.12345",
        "pdfUrl": "https://arxiv.org/pdf/2304.12345"
      }
    },
    // Weitere Publikationen...
  ]
}
```

### NFT für eine Publikation erstellen

**Anfrage**
```
POST /api/arxiv/nft/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Body**
```json
{
  "recipient": "0x1234567890abcdef1234567890abcdef12345678",
  "royaltyPercentage": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "doi": "10.12345/example.doi"
  },
  "nft": {
    "tokenId": "42",
    "metadata": {
      "name": "Anwendung von KI in der Medizin",
      "description": "Diese Studie untersucht...",
      "external_url": "https://arxiv.org/abs/2304.12345",
      "attributes": [
        { "trait_type": "Type", "value": "Scientific Paper" },
        { "trait_type": "Source", "value": "arXiv" },
        { "trait_type": "arXiv ID", "value": "2304.12345" },
        { "trait_type": "DOI", "value": "10.12345/example.doi" },
        { "trait_type": "Category", "value": "cs.AI" },
        { "trait_type": "Authors", "value": "Dr. Maria Schmidt, Prof. Hans Müller" },
        { "trait_type": "Publication Date", "value": "2023-04-15" }
      ]
    }
  },
  "tokenId": "42",
  "transactionHash": "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
  "message": "NFT erfolgreich erstellt"
}
```

### NFTs für mehrere Publikationen erstellen

**Anfrage**
```
POST /api/arxiv/nft
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5,
  "nftOptions": {
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 5,
  "withDOI": 3,
  "createdNFTs": 3,
  "failed": 0,
  "nfts": [
    {
      "publication": {
        "id": "pub-789012",
        "title": "Quantum Computing Applications",
        "authors": ["Dr. John Doe", "Prof. Jane Smith"],
        "abstract": "This study explores...",
        "doi": "10.12345/example.doi"
      },
      "nft": {
        "tokenId": "42",
        "metadata": {
          "name": "Quantum Computing Applications",
          "description": "This study explores...",
          "external_url": "https://arxiv.org/abs/2304.12345",
          "attributes": [
            { "trait_type": "Type", "value": "Scientific Paper" },
            { "trait_type": "Source", "value": "arXiv" },
            { "trait_type": "arXiv ID", "value": "2304.12345" },
            { "trait_type": "DOI", "value": "10.12345/example.doi" },
            { "trait_type": "Category", "value": "quant-ph" },
            { "trait_type": "Authors", "value": "Dr. John Doe, Prof. Jane Smith" },
            { "trait_type": "Publication Date", "value": "2023-04-15" }
          ]
        }
      },
      "tokenId": "42"
    },
    // Weitere NFTs...
  ]
}
```

### Statistiken abrufen

**Anfrage**
```
GET /api/arxiv/stats
```

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "stats": {
    "importedPapers": 42,
    "createdNFTs": 15,
    "failedImports": 3,
    "papersByCategory": {
      "cs.AI": 12,
      "quant-ph": 8,
      "physics": 22
    },
    "apiStats": {
      "totalRequests": 150,
      "successfulRequests": 147,
      "failedRequests": 3,
      "retries": 5,
      "requestsByEndpoint": {
        "query": 150
      },
      "successRate": "98.00%"
    },
    "successRate": "92.86%"
  }
}
```

## arXiv-Kategorien

Die arXiv-Integration unterstützt alle arXiv-Kategorien, darunter:

- **Physik**: astro-ph, cond-mat, gr-qc, hep-ex, hep-lat, hep-ph, hep-th, math-ph, nlin, nucl-ex, nucl-th, physics, quant-ph
- **Mathematik**: math
- **Informatik**: cs.AI, cs.AR, cs.CC, cs.CE, cs.CG, cs.CL, cs.CR, cs.CV, cs.CY, cs.DB, cs.DC, cs.DL, cs.DM, cs.DS, cs.ET, cs.FL, cs.GL, cs.GR, cs.GT, cs.HC, cs.IR, cs.IT, cs.LG, cs.LO, cs.MA, cs.MM, cs.MS, cs.NA, cs.NE, cs.NI, cs.OS, cs.PF, cs.PL, cs.RO, cs.SC, cs.SD, cs.SE, cs.SI, cs.SY
- **Quantitative Biologie**: q-bio
- **Quantitative Finanzen**: q-fin
- **Statistik**: stat
- **Elektrotechnik und Systemwissenschaft**: eess
- **Wirtschaftswissenschaften**: econ

Eine vollständige Liste der Kategorien und Unterkategorien finden Sie auf der [arXiv-Taxonomie-Seite](https://arxiv.org/category_taxonomy).

## Nutzungshinweise

Bei der Verwendung der arXiv-Integration sind folgende Punkte zu beachten:

1. **API-Limits**: Die arXiv API hat Ratenbegrenzungen. Es wird empfohlen, nicht mehr als eine Anfrage alle 3 Sekunden zu stellen.

2. **Urheberrecht**: Die meisten arXiv-Publikationen stehen unter der "arXiv non-exclusive license to distribute", die keine Weiterverbreitung erlaubt. Stellen Sie sicher, dass Sie die Urheberrechte respektieren.

3. **DOIs und NFTs**: Nur Publikationen mit DOIs können als NFTs tokenisiert werden. Nicht alle arXiv-Publikationen haben DOIs.

4. **Metadaten**: Die arXiv API liefert nur Metadaten, keinen Volltext. Der Volltext kann über die PDF-URL abgerufen werden, aber dies ist nicht Teil der API-Integration.

5. **Kategorien**: Bei der Suche nach Kategorien können sowohl Hauptkategorien (z.B. "physics") als auch Unterkategorien (z.B. "cs.AI") verwendet werden.

## Beispiele

### Curl-Beispiel: Suche nach Publikationen

```bash
curl -X GET "http://localhost:3000/api/arxiv/search?query=quantum+computing&category=quant-ph&maxResults=5"
```

### Curl-Beispiel: Publikation importieren

```bash
curl -X POST "http://localhost:3000/api/arxiv/import/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..."
```

### Curl-Beispiel: NFT erstellen

```bash
curl -X POST "http://localhost:3000/api/arxiv/nft/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..." \
  -H "Content-Type: application/json" \
  -d '{
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }'
```# arXiv-Integration für DeSci-Scholar

Diese Dokumentation beschreibt die Integration von arXiv in DeSci-Scholar, die es ermöglicht, wissenschaftliche Publikationen von arXiv zu suchen, zu importieren und als NFTs zu tokenisieren.

## Überblick

Die arXiv-Integration bietet folgende Funktionen:

- Suche nach wissenschaftlichen Publikationen in arXiv
- Abruf von Publikationsdetails anhand der arXiv-ID
- Import von arXiv-Publikationen in DeSci-Scholar
- Erstellung von NFTs für arXiv-Publikationen mit DOIs
- Abruf der neuesten Publikationen in bestimmten Kategorien
- Statistiken zur arXiv-Integration

## API-Endpunkte

### Suche nach Publikationen

**Anfrage**
```
GET /api/arxiv/search
```

**Query-Parameter**
- `query` - Allgemeiner Suchbegriff
- `title` - Suche im Titel
- `author` - Suche nach Autor
- `category` - Suche nach Kategorie (z.B. "physics", "cs.AI")
- `id` - Suche nach arXiv-ID
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 10)
- `start` - Startposition für Paginierung (Standard: 0)
- `sortBy` - Sortierkriterium ("relevance", "lastUpdatedDate", "submittedDate")
- `sortOrder` - Sortierreihenfolge ("ascending", "descending")

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalResults": 42,
  "startIndex": 0,
  "itemsPerPage": 10,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikationsdetails abrufen

**Anfrage**
```
GET /api/arxiv/paper/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Antwort (200 OK)**
```json
{
  "success": true,
  "paper": {
    "id": "2304.12345",
    "title": "Anwendung von KI in der Medizin",
    "abstract": "Diese Studie untersucht...",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "categories": ["cs.AI", "cs.LG"],
    "primaryCategory": "cs.AI",
    "published": "2023-04-15T14:22:10Z",
    "updated": "2023-04-16T09:15:30Z",
    "doi": "10.12345/example.doi",
    "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
    "comment": "20 pages, 8 figures",
    "pdfUrl": "https://arxiv.org/pdf/2304.12345",
    "arxivUrl": "https://arxiv.org/abs/2304.12345"
  }
}
```

### Neueste Publikationen in einer Kategorie abrufen

**Anfrage**
```
GET /api/arxiv/recent/:category
```

**Parameter**
- `category` - arXiv-Kategorie (z.B. "cs.AI", "physics")

**Query-Parameter**
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 30)

**Antwort (200 OK)**
```json
{
  "success": true,
  "category": "cs.AI",
  "categoryName": "Künstliche Intelligenz",
  "totalResults": 42,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikation importieren

**Anfrage**
```
POST /api/arxiv/import/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "keywords": ["Künstliche Intelligenz", "Machine Learning"],
    "publishedDate": "2023-04-15T14:22:10Z",
    "doi": "10.12345/example.doi",
    "license": "arXiv non-exclusive license to distribute",
    "status": "published",
    "arxivMetadata": {
      "id": "2304.12345",
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "updated": "2023-04-16T09:15:30Z",
      "comment": "20 pages, 8 figures",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "url": "https://arxiv.org/abs/2304.12345",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345"
    },
    "storage": {
      "ipfs": "QmXa12S5sdf87gh4j56k7l...",
      "bittorrent": "a1b2c3d4e5f6...",
      "timestamp": "2023-05-10T08:12:45Z"
    }
  },
  "message": "Publikation erfolgreich importiert"
}
```

### Mehrere Publikationen importieren

**Anfrage**
```
POST /api/arxiv/import
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 42,
  "imported": 5,
  "failed": 0,
  "publications": [
    {
      "id": "pub-789012",
      "title": "Quantum Computing Applications",
      "authors": ["Dr. John Doe", "Prof. Jane Smith"],
      "abstract": "This study explores...",
      "keywords": ["Quantum Computing", "Quantum Physics"],
      "publishedDate": "2023-04-15T14:22:10Z",
      "doi": "10.12345/example.doi",
      "license": "arXiv non-exclusive license to distribute",
      "status": "published",
      "arxivMetadata": {
        "id": "2304.12345",
        "categories": ["quant-ph", "cs.ET"],
        "primaryCategory": "quant-ph",
        "updated": "2023-04-16T09:15:30Z",
        "comment": "15 pages, 5 figures",
        "journalRef": "Journal of Quantum Computing, Vol. 10, pp. 45-60",
        "url": "https://arxiv.org/abs/2304.12345",
        "pdfUrl": "https://arxiv.org/pdf/2304.12345"
      }
    },
    // Weitere Publikationen...
  ]
}
```

### NFT für eine Publikation erstellen

**Anfrage**
```
POST /api/arxiv/nft/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Body**
```json
{
  "recipient": "0x1234567890abcdef1234567890abcdef12345678",
  "royaltyPercentage": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "doi": "10.12345/example.doi"
  },
  "nft": {
    "tokenId": "42",
    "metadata": {
      "name": "Anwendung von KI in der Medizin",
      "description": "Diese Studie untersucht...",
      "external_url": "https://arxiv.org/abs/2304.12345",
      "attributes": [
        { "trait_type": "Type", "value": "Scientific Paper" },
        { "trait_type": "Source", "value": "arXiv" },
        { "trait_type": "arXiv ID", "value": "2304.12345" },
        { "trait_type": "DOI", "value": "10.12345/example.doi" },
        { "trait_type": "Category", "value": "cs.AI" },
        { "trait_type": "Authors", "value": "Dr. Maria Schmidt, Prof. Hans Müller" },
        { "trait_type": "Publication Date", "value": "2023-04-15" }
      ]
    }
  },
  "tokenId": "42",
  "transactionHash": "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
  "message": "NFT erfolgreich erstellt"
}
```

### NFTs für mehrere Publikationen erstellen

**Anfrage**
```
POST /api/arxiv/nft
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5,
  "nftOptions": {
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 5,
  "withDOI": 3,
  "createdNFTs": 3,
  "failed": 0,
  "nfts": [
    {
      "publication": {
        "id": "pub-789012",
        "title": "Quantum Computing Applications",
        "authors": ["Dr. John Doe", "Prof. Jane Smith"],
        "abstract": "This study explores...",
        "doi": "10.12345/example.doi"
      },
      "nft": {
        "tokenId": "42",
        "metadata": {
          "name": "Quantum Computing Applications",
          "description": "This study explores...",
          "external_url": "https://arxiv.org/abs/2304.12345",
          "attributes": [
            { "trait_type": "Type", "value": "Scientific Paper" },
            { "trait_type": "Source", "value": "arXiv" },
            { "trait_type": "arXiv ID", "value": "2304.12345" },
            { "trait_type": "DOI", "value": "10.12345/example.doi" },
            { "trait_type": "Category", "value": "quant-ph" },
            { "trait_type": "Authors", "value": "Dr. John Doe, Prof. Jane Smith" },
            { "trait_type": "Publication Date", "value": "2023-04-15" }
          ]
        }
      },
      "tokenId": "42"
    },
    // Weitere NFTs...
  ]
}
```

### Statistiken abrufen

**Anfrage**
```
GET /api/arxiv/stats
```

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "stats": {
    "importedPapers": 42,
    "createdNFTs": 15,
    "failedImports": 3,
    "papersByCategory": {
      "cs.AI": 12,
      "quant-ph": 8,
      "physics": 22
    },
    "apiStats": {
      "totalRequests": 150,
      "successfulRequests": 147,
      "failedRequests": 3,
      "retries": 5,
      "requestsByEndpoint": {
        "query": 150
      },
      "successRate": "98.00%"
    },
    "successRate": "92.86%"
  }
}
```

## arXiv-Kategorien

Die arXiv-Integration unterstützt alle arXiv-Kategorien, darunter:

- **Physik**: astro-ph, cond-mat, gr-qc, hep-ex, hep-lat, hep-ph, hep-th, math-ph, nlin, nucl-ex, nucl-th, physics, quant-ph
- **Mathematik**: math
- **Informatik**: cs.AI, cs.AR, cs.CC, cs.CE, cs.CG, cs.CL, cs.CR, cs.CV, cs.CY, cs.DB, cs.DC, cs.DL, cs.DM, cs.DS, cs.ET, cs.FL, cs.GL, cs.GR, cs.GT, cs.HC, cs.IR, cs.IT, cs.LG, cs.LO, cs.MA, cs.MM, cs.MS, cs.NA, cs.NE, cs.NI, cs.OS, cs.PF, cs.PL, cs.RO, cs.SC, cs.SD, cs.SE, cs.SI, cs.SY
- **Quantitative Biologie**: q-bio
- **Quantitative Finanzen**: q-fin
- **Statistik**: stat
- **Elektrotechnik und Systemwissenschaft**: eess
- **Wirtschaftswissenschaften**: econ

Eine vollständige Liste der Kategorien und Unterkategorien finden Sie auf der [arXiv-Taxonomie-Seite](https://arxiv.org/category_taxonomy).

## Nutzungshinweise

Bei der Verwendung der arXiv-Integration sind folgende Punkte zu beachten:

1. **API-Limits**: Die arXiv API hat Ratenbegrenzungen. Es wird empfohlen, nicht mehr als eine Anfrage alle 3 Sekunden zu stellen.

2. **Urheberrecht**: Die meisten arXiv-Publikationen stehen unter der "arXiv non-exclusive license to distribute", die keine Weiterverbreitung erlaubt. Stellen Sie sicher, dass Sie die Urheberrechte respektieren.

3. **DOIs und NFTs**: Nur Publikationen mit DOIs können als NFTs tokenisiert werden. Nicht alle arXiv-Publikationen haben DOIs.

4. **Metadaten**: Die arXiv API liefert nur Metadaten, keinen Volltext. Der Volltext kann über die PDF-URL abgerufen werden, aber dies ist nicht Teil der API-Integration.

5. **Kategorien**: Bei der Suche nach Kategorien können sowohl Hauptkategorien (z.B. "physics") als auch Unterkategorien (z.B. "cs.AI") verwendet werden.

## Beispiele

### Curl-Beispiel: Suche nach Publikationen

```bash
curl -X GET "http://localhost:3000/api/arxiv/search?query=quantum+computing&category=quant-ph&maxResults=5"
```

### Curl-Beispiel: Publikation importieren

```bash
curl -X POST "http://localhost:3000/api/arxiv/import/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..."
```

### Curl-Beispiel: NFT erstellen

```bash
curl -X POST "http://localhost:3000/api/arxiv/nft/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..." \
  -H "Content-Type: application/json" \
  -d '{
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }'
```# arXiv-Integration für DeSci-Scholar

Diese Dokumentation beschreibt die Integration von arXiv in DeSci-Scholar, die es ermöglicht, wissenschaftliche Publikationen von arXiv zu suchen, zu importieren und als NFTs zu tokenisieren.

## Überblick

Die arXiv-Integration bietet folgende Funktionen:

- Suche nach wissenschaftlichen Publikationen in arXiv
- Abruf von Publikationsdetails anhand der arXiv-ID
- Import von arXiv-Publikationen in DeSci-Scholar
- Erstellung von NFTs für arXiv-Publikationen mit DOIs
- Abruf der neuesten Publikationen in bestimmten Kategorien
- Statistiken zur arXiv-Integration

## API-Endpunkte

### Suche nach Publikationen

**Anfrage**
```
GET /api/arxiv/search
```

**Query-Parameter**
- `query` - Allgemeiner Suchbegriff
- `title` - Suche im Titel
- `author` - Suche nach Autor
- `category` - Suche nach Kategorie (z.B. "physics", "cs.AI")
- `id` - Suche nach arXiv-ID
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 10)
- `start` - Startposition für Paginierung (Standard: 0)
- `sortBy` - Sortierkriterium ("relevance", "lastUpdatedDate", "submittedDate")
- `sortOrder` - Sortierreihenfolge ("ascending", "descending")

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalResults": 42,
  "startIndex": 0,
  "itemsPerPage": 10,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikationsdetails abrufen

**Anfrage**
```
GET /api/arxiv/paper/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Antwort (200 OK)**
```json
{
  "success": true,
  "paper": {
    "id": "2304.12345",
    "title": "Anwendung von KI in der Medizin",
    "abstract": "Diese Studie untersucht...",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "categories": ["cs.AI", "cs.LG"],
    "primaryCategory": "cs.AI",
    "published": "2023-04-15T14:22:10Z",
    "updated": "2023-04-16T09:15:30Z",
    "doi": "10.12345/example.doi",
    "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
    "comment": "20 pages, 8 figures",
    "pdfUrl": "https://arxiv.org/pdf/2304.12345",
    "arxivUrl": "https://arxiv.org/abs/2304.12345"
  }
}
```

### Neueste Publikationen in einer Kategorie abrufen

**Anfrage**
```
GET /api/arxiv/recent/:category
```

**Parameter**
- `category` - arXiv-Kategorie (z.B. "cs.AI", "physics")

**Query-Parameter**
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 30)

**Antwort (200 OK)**
```json
{
  "success": true,
  "category": "cs.AI",
  "categoryName": "Künstliche Intelligenz",
  "totalResults": 42,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikation importieren

**Anfrage**
```
POST /api/arxiv/import/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "keywords": ["Künstliche Intelligenz", "Machine Learning"],
    "publishedDate": "2023-04-15T14:22:10Z",
    "doi": "10.12345/example.doi",
    "license": "arXiv non-exclusive license to distribute",
    "status": "published",
    "arxivMetadata": {
      "id": "2304.12345",
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "updated": "2023-04-16T09:15:30Z",
      "comment": "20 pages, 8 figures",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "url": "https://arxiv.org/abs/2304.12345",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345"
    },
    "storage": {
      "ipfs": "QmXa12S5sdf87gh4j56k7l...",
      "bittorrent": "a1b2c3d4e5f6...",
      "timestamp": "2023-05-10T08:12:45Z"
    }
  },
  "message": "Publikation erfolgreich importiert"
}
```

### Mehrere Publikationen importieren

**Anfrage**
```
POST /api/arxiv/import
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 42,
  "imported": 5,
  "failed": 0,
  "publications": [
    {
      "id": "pub-789012",
      "title": "Quantum Computing Applications",
      "authors": ["Dr. John Doe", "Prof. Jane Smith"],
      "abstract": "This study explores...",
      "keywords": ["Quantum Computing", "Quantum Physics"],
      "publishedDate": "2023-04-15T14:22:10Z",
      "doi": "10.12345/example.doi",
      "license": "arXiv non-exclusive license to distribute",
      "status": "published",
      "arxivMetadata": {
        "id": "2304.12345",
        "categories": ["quant-ph", "cs.ET"],
        "primaryCategory": "quant-ph",
        "updated": "2023-04-16T09:15:30Z",
        "comment": "15 pages, 5 figures",
        "journalRef": "Journal of Quantum Computing, Vol. 10, pp. 45-60",
        "url": "https://arxiv.org/abs/2304.12345",
        "pdfUrl": "https://arxiv.org/pdf/2304.12345"
      }
    },
    // Weitere Publikationen...
  ]
}
```

### NFT für eine Publikation erstellen

**Anfrage**
```
POST /api/arxiv/nft/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Body**
```json
{
  "recipient": "0x1234567890abcdef1234567890abcdef12345678",
  "royaltyPercentage": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "doi": "10.12345/example.doi"
  },
  "nft": {
    "tokenId": "42",
    "metadata": {
      "name": "Anwendung von KI in der Medizin",
      "description": "Diese Studie untersucht...",
      "external_url": "https://arxiv.org/abs/2304.12345",
      "attributes": [
        { "trait_type": "Type", "value": "Scientific Paper" },
        { "trait_type": "Source", "value": "arXiv" },
        { "trait_type": "arXiv ID", "value": "2304.12345" },
        { "trait_type": "DOI", "value": "10.12345/example.doi" },
        { "trait_type": "Category", "value": "cs.AI" },
        { "trait_type": "Authors", "value": "Dr. Maria Schmidt, Prof. Hans Müller" },
        { "trait_type": "Publication Date", "value": "2023-04-15" }
      ]
    }
  },
  "tokenId": "42",
  "transactionHash": "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
  "message": "NFT erfolgreich erstellt"
}
```

### NFTs für mehrere Publikationen erstellen

**Anfrage**
```
POST /api/arxiv/nft
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5,
  "nftOptions": {
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 5,
  "withDOI": 3,
  "createdNFTs": 3,
  "failed": 0,
  "nfts": [
    {
      "publication": {
        "id": "pub-789012",
        "title": "Quantum Computing Applications",
        "authors": ["Dr. John Doe", "Prof. Jane Smith"],
        "abstract": "This study explores...",
        "doi": "10.12345/example.doi"
      },
      "nft": {
        "tokenId": "42",
        "metadata": {
          "name": "Quantum Computing Applications",
          "description": "This study explores...",
          "external_url": "https://arxiv.org/abs/2304.12345",
          "attributes": [
            { "trait_type": "Type", "value": "Scientific Paper" },
            { "trait_type": "Source", "value": "arXiv" },
            { "trait_type": "arXiv ID", "value": "2304.12345" },
            { "trait_type": "DOI", "value": "10.12345/example.doi" },
            { "trait_type": "Category", "value": "quant-ph" },
            { "trait_type": "Authors", "value": "Dr. John Doe, Prof. Jane Smith" },
            { "trait_type": "Publication Date", "value": "2023-04-15" }
          ]
        }
      },
      "tokenId": "42"
    },
    // Weitere NFTs...
  ]
}
```

### Statistiken abrufen

**Anfrage**
```
GET /api/arxiv/stats
```

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "stats": {
    "importedPapers": 42,
    "createdNFTs": 15,
    "failedImports": 3,
    "papersByCategory": {
      "cs.AI": 12,
      "quant-ph": 8,
      "physics": 22
    },
    "apiStats": {
      "totalRequests": 150,
      "successfulRequests": 147,
      "failedRequests": 3,
      "retries": 5,
      "requestsByEndpoint": {
        "query": 150
      },
      "successRate": "98.00%"
    },
    "successRate": "92.86%"
  }
}
```

## arXiv-Kategorien

Die arXiv-Integration unterstützt alle arXiv-Kategorien, darunter:

- **Physik**: astro-ph, cond-mat, gr-qc, hep-ex, hep-lat, hep-ph, hep-th, math-ph, nlin, nucl-ex, nucl-th, physics, quant-ph
- **Mathematik**: math
- **Informatik**: cs.AI, cs.AR, cs.CC, cs.CE, cs.CG, cs.CL, cs.CR, cs.CV, cs.CY, cs.DB, cs.DC, cs.DL, cs.DM, cs.DS, cs.ET, cs.FL, cs.GL, cs.GR, cs.GT, cs.HC, cs.IR, cs.IT, cs.LG, cs.LO, cs.MA, cs.MM, cs.MS, cs.NA, cs.NE, cs.NI, cs.OS, cs.PF, cs.PL, cs.RO, cs.SC, cs.SD, cs.SE, cs.SI, cs.SY
- **Quantitative Biologie**: q-bio
- **Quantitative Finanzen**: q-fin
- **Statistik**: stat
- **Elektrotechnik und Systemwissenschaft**: eess
- **Wirtschaftswissenschaften**: econ

Eine vollständige Liste der Kategorien und Unterkategorien finden Sie auf der [arXiv-Taxonomie-Seite](https://arxiv.org/category_taxonomy).

## Nutzungshinweise

Bei der Verwendung der arXiv-Integration sind folgende Punkte zu beachten:

1. **API-Limits**: Die arXiv API hat Ratenbegrenzungen. Es wird empfohlen, nicht mehr als eine Anfrage alle 3 Sekunden zu stellen.

2. **Urheberrecht**: Die meisten arXiv-Publikationen stehen unter der "arXiv non-exclusive license to distribute", die keine Weiterverbreitung erlaubt. Stellen Sie sicher, dass Sie die Urheberrechte respektieren.

3. **DOIs und NFTs**: Nur Publikationen mit DOIs können als NFTs tokenisiert werden. Nicht alle arXiv-Publikationen haben DOIs.

4. **Metadaten**: Die arXiv API liefert nur Metadaten, keinen Volltext. Der Volltext kann über die PDF-URL abgerufen werden, aber dies ist nicht Teil der API-Integration.

5. **Kategorien**: Bei der Suche nach Kategorien können sowohl Hauptkategorien (z.B. "physics") als auch Unterkategorien (z.B. "cs.AI") verwendet werden.

## Beispiele

### Curl-Beispiel: Suche nach Publikationen

```bash
curl -X GET "http://localhost:3000/api/arxiv/search?query=quantum+computing&category=quant-ph&maxResults=5"
```

### Curl-Beispiel: Publikation importieren

```bash
curl -X POST "http://localhost:3000/api/arxiv/import/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..."
```

### Curl-Beispiel: NFT erstellen

```bash
curl -X POST "http://localhost:3000/api/arxiv/nft/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..." \
  -H "Content-Type: application/json" \
  -d '{
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }'
```# arXiv-Integration für DeSci-Scholar

Diese Dokumentation beschreibt die Integration von arXiv in DeSci-Scholar, die es ermöglicht, wissenschaftliche Publikationen von arXiv zu suchen, zu importieren und als NFTs zu tokenisieren.

## Überblick

Die arXiv-Integration bietet folgende Funktionen:

- Suche nach wissenschaftlichen Publikationen in arXiv
- Abruf von Publikationsdetails anhand der arXiv-ID
- Import von arXiv-Publikationen in DeSci-Scholar
- Erstellung von NFTs für arXiv-Publikationen mit DOIs
- Abruf der neuesten Publikationen in bestimmten Kategorien
- Statistiken zur arXiv-Integration

## API-Endpunkte

### Suche nach Publikationen

**Anfrage**
```
GET /api/arxiv/search
```

**Query-Parameter**
- `query` - Allgemeiner Suchbegriff
- `title` - Suche im Titel
- `author` - Suche nach Autor
- `category` - Suche nach Kategorie (z.B. "physics", "cs.AI")
- `id` - Suche nach arXiv-ID
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 10)
- `start` - Startposition für Paginierung (Standard: 0)
- `sortBy` - Sortierkriterium ("relevance", "lastUpdatedDate", "submittedDate")
- `sortOrder` - Sortierreihenfolge ("ascending", "descending")

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalResults": 42,
  "startIndex": 0,
  "itemsPerPage": 10,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikationsdetails abrufen

**Anfrage**
```
GET /api/arxiv/paper/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Antwort (200 OK)**
```json
{
  "success": true,
  "paper": {
    "id": "2304.12345",
    "title": "Anwendung von KI in der Medizin",
    "abstract": "Diese Studie untersucht...",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "categories": ["cs.AI", "cs.LG"],
    "primaryCategory": "cs.AI",
    "published": "2023-04-15T14:22:10Z",
    "updated": "2023-04-16T09:15:30Z",
    "doi": "10.12345/example.doi",
    "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
    "comment": "20 pages, 8 figures",
    "pdfUrl": "https://arxiv.org/pdf/2304.12345",
    "arxivUrl": "https://arxiv.org/abs/2304.12345"
  }
}
```

### Neueste Publikationen in einer Kategorie abrufen

**Anfrage**
```
GET /api/arxiv/recent/:category
```

**Parameter**
- `category` - arXiv-Kategorie (z.B. "cs.AI", "physics")

**Query-Parameter**
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 30)

**Antwort (200 OK)**
```json
{
  "success": true,
  "category": "cs.AI",
  "categoryName": "Künstliche Intelligenz",
  "totalResults": 42,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikation importieren

**Anfrage**
```
POST /api/arxiv/import/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "keywords": ["Künstliche Intelligenz", "Machine Learning"],
    "publishedDate": "2023-04-15T14:22:10Z",
    "doi": "10.12345/example.doi",
    "license": "arXiv non-exclusive license to distribute",
    "status": "published",
    "arxivMetadata": {
      "id": "2304.12345",
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "updated": "2023-04-16T09:15:30Z",
      "comment": "20 pages, 8 figures",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "url": "https://arxiv.org/abs/2304.12345",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345"
    },
    "storage": {
      "ipfs": "QmXa12S5sdf87gh4j56k7l...",
      "bittorrent": "a1b2c3d4e5f6...",
      "timestamp": "2023-05-10T08:12:45Z"
    }
  },
  "message": "Publikation erfolgreich importiert"
}
```

### Mehrere Publikationen importieren

**Anfrage**
```
POST /api/arxiv/import
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 42,
  "imported": 5,
  "failed": 0,
  "publications": [
    {
      "id": "pub-789012",
      "title": "Quantum Computing Applications",
      "authors": ["Dr. John Doe", "Prof. Jane Smith"],
      "abstract": "This study explores...",
      "keywords": ["Quantum Computing", "Quantum Physics"],
      "publishedDate": "2023-04-15T14:22:10Z",
      "doi": "10.12345/example.doi",
      "license": "arXiv non-exclusive license to distribute",
      "status": "published",
      "arxivMetadata": {
        "id": "2304.12345",
        "categories": ["quant-ph", "cs.ET"],
        "primaryCategory": "quant-ph",
        "updated": "2023-04-16T09:15:30Z",
        "comment": "15 pages, 5 figures",
        "journalRef": "Journal of Quantum Computing, Vol. 10, pp. 45-60",
        "url": "https://arxiv.org/abs/2304.12345",
        "pdfUrl": "https://arxiv.org/pdf/2304.12345"
      }
    },
    // Weitere Publikationen...
  ]
}
```

### NFT für eine Publikation erstellen

**Anfrage**
```
POST /api/arxiv/nft/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Body**
```json
{
  "recipient": "0x1234567890abcdef1234567890abcdef12345678",
  "royaltyPercentage": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "doi": "10.12345/example.doi"
  },
  "nft": {
    "tokenId": "42",
    "metadata": {
      "name": "Anwendung von KI in der Medizin",
      "description": "Diese Studie untersucht...",
      "external_url": "https://arxiv.org/abs/2304.12345",
      "attributes": [
        { "trait_type": "Type", "value": "Scientific Paper" },
        { "trait_type": "Source", "value": "arXiv" },
        { "trait_type": "arXiv ID", "value": "2304.12345" },
        { "trait_type": "DOI", "value": "10.12345/example.doi" },
        { "trait_type": "Category", "value": "cs.AI" },
        { "trait_type": "Authors", "value": "Dr. Maria Schmidt, Prof. Hans Müller" },
        { "trait_type": "Publication Date", "value": "2023-04-15" }
      ]
    }
  },
  "tokenId": "42",
  "transactionHash": "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
  "message": "NFT erfolgreich erstellt"
}
```

### NFTs für mehrere Publikationen erstellen

**Anfrage**
```
POST /api/arxiv/nft
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5,
  "nftOptions": {
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 5,
  "withDOI": 3,
  "createdNFTs": 3,
  "failed": 0,
  "nfts": [
    {
      "publication": {
        "id": "pub-789012",
        "title": "Quantum Computing Applications",
        "authors": ["Dr. John Doe", "Prof. Jane Smith"],
        "abstract": "This study explores...",
        "doi": "10.12345/example.doi"
      },
      "nft": {
        "tokenId": "42",
        "metadata": {
          "name": "Quantum Computing Applications",
          "description": "This study explores...",
          "external_url": "https://arxiv.org/abs/2304.12345",
          "attributes": [
            { "trait_type": "Type", "value": "Scientific Paper" },
            { "trait_type": "Source", "value": "arXiv" },
            { "trait_type": "arXiv ID", "value": "2304.12345" },
            { "trait_type": "DOI", "value": "10.12345/example.doi" },
            { "trait_type": "Category", "value": "quant-ph" },
            { "trait_type": "Authors", "value": "Dr. John Doe, Prof. Jane Smith" },
            { "trait_type": "Publication Date", "value": "2023-04-15" }
          ]
        }
      },
      "tokenId": "42"
    },
    // Weitere NFTs...
  ]
}
```

### Statistiken abrufen

**Anfrage**
```
GET /api/arxiv/stats
```

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "stats": {
    "importedPapers": 42,
    "createdNFTs": 15,
    "failedImports": 3,
    "papersByCategory": {
      "cs.AI": 12,
      "quant-ph": 8,
      "physics": 22
    },
    "apiStats": {
      "totalRequests": 150,
      "successfulRequests": 147,
      "failedRequests": 3,
      "retries": 5,
      "requestsByEndpoint": {
        "query": 150
      },
      "successRate": "98.00%"
    },
    "successRate": "92.86%"
  }
}
```

## arXiv-Kategorien

Die arXiv-Integration unterstützt alle arXiv-Kategorien, darunter:

- **Physik**: astro-ph, cond-mat, gr-qc, hep-ex, hep-lat, hep-ph, hep-th, math-ph, nlin, nucl-ex, nucl-th, physics, quant-ph
- **Mathematik**: math
- **Informatik**: cs.AI, cs.AR, cs.CC, cs.CE, cs.CG, cs.CL, cs.CR, cs.CV, cs.CY, cs.DB, cs.DC, cs.DL, cs.DM, cs.DS, cs.ET, cs.FL, cs.GL, cs.GR, cs.GT, cs.HC, cs.IR, cs.IT, cs.LG, cs.LO, cs.MA, cs.MM, cs.MS, cs.NA, cs.NE, cs.NI, cs.OS, cs.PF, cs.PL, cs.RO, cs.SC, cs.SD, cs.SE, cs.SI, cs.SY
- **Quantitative Biologie**: q-bio
- **Quantitative Finanzen**: q-fin
- **Statistik**: stat
- **Elektrotechnik und Systemwissenschaft**: eess
- **Wirtschaftswissenschaften**: econ

Eine vollständige Liste der Kategorien und Unterkategorien finden Sie auf der [arXiv-Taxonomie-Seite](https://arxiv.org/category_taxonomy).

## Nutzungshinweise

Bei der Verwendung der arXiv-Integration sind folgende Punkte zu beachten:

1. **API-Limits**: Die arXiv API hat Ratenbegrenzungen. Es wird empfohlen, nicht mehr als eine Anfrage alle 3 Sekunden zu stellen.

2. **Urheberrecht**: Die meisten arXiv-Publikationen stehen unter der "arXiv non-exclusive license to distribute", die keine Weiterverbreitung erlaubt. Stellen Sie sicher, dass Sie die Urheberrechte respektieren.

3. **DOIs und NFTs**: Nur Publikationen mit DOIs können als NFTs tokenisiert werden. Nicht alle arXiv-Publikationen haben DOIs.

4. **Metadaten**: Die arXiv API liefert nur Metadaten, keinen Volltext. Der Volltext kann über die PDF-URL abgerufen werden, aber dies ist nicht Teil der API-Integration.

5. **Kategorien**: Bei der Suche nach Kategorien können sowohl Hauptkategorien (z.B. "physics") als auch Unterkategorien (z.B. "cs.AI") verwendet werden.

## Beispiele

### Curl-Beispiel: Suche nach Publikationen

```bash
curl -X GET "http://localhost:3000/api/arxiv/search?query=quantum+computing&category=quant-ph&maxResults=5"
```

### Curl-Beispiel: Publikation importieren

```bash
curl -X POST "http://localhost:3000/api/arxiv/import/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..."
```

### Curl-Beispiel: NFT erstellen

```bash
curl -X POST "http://localhost:3000/api/arxiv/nft/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..." \
  -H "Content-Type: application/json" \
  -d '{
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }'
```# arXiv-Integration für DeSci-Scholar

Diese Dokumentation beschreibt die Integration von arXiv in DeSci-Scholar, die es ermöglicht, wissenschaftliche Publikationen von arXiv zu suchen, zu importieren und als NFTs zu tokenisieren.

## Überblick

Die arXiv-Integration bietet folgende Funktionen:

- Suche nach wissenschaftlichen Publikationen in arXiv
- Abruf von Publikationsdetails anhand der arXiv-ID
- Import von arXiv-Publikationen in DeSci-Scholar
- Erstellung von NFTs für arXiv-Publikationen mit DOIs
- Abruf der neuesten Publikationen in bestimmten Kategorien
- Statistiken zur arXiv-Integration

## API-Endpunkte

### Suche nach Publikationen

**Anfrage**
```
GET /api/arxiv/search
```

**Query-Parameter**
- `query` - Allgemeiner Suchbegriff
- `title` - Suche im Titel
- `author` - Suche nach Autor
- `category` - Suche nach Kategorie (z.B. "physics", "cs.AI")
- `id` - Suche nach arXiv-ID
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 10)
- `start` - Startposition für Paginierung (Standard: 0)
- `sortBy` - Sortierkriterium ("relevance", "lastUpdatedDate", "submittedDate")
- `sortOrder` - Sortierreihenfolge ("ascending", "descending")

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalResults": 42,
  "startIndex": 0,
  "itemsPerPage": 10,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikationsdetails abrufen

**Anfrage**
```
GET /api/arxiv/paper/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Antwort (200 OK)**
```json
{
  "success": true,
  "paper": {
    "id": "2304.12345",
    "title": "Anwendung von KI in der Medizin",
    "abstract": "Diese Studie untersucht...",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "categories": ["cs.AI", "cs.LG"],
    "primaryCategory": "cs.AI",
    "published": "2023-04-15T14:22:10Z",
    "updated": "2023-04-16T09:15:30Z",
    "doi": "10.12345/example.doi",
    "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
    "comment": "20 pages, 8 figures",
    "pdfUrl": "https://arxiv.org/pdf/2304.12345",
    "arxivUrl": "https://arxiv.org/abs/2304.12345"
  }
}
```

### Neueste Publikationen in einer Kategorie abrufen

**Anfrage**
```
GET /api/arxiv/recent/:category
```

**Parameter**
- `category` - arXiv-Kategorie (z.B. "cs.AI", "physics")

**Query-Parameter**
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 30)

**Antwort (200 OK)**
```json
{
  "success": true,
  "category": "cs.AI",
  "categoryName": "Künstliche Intelligenz",
  "totalResults": 42,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikation importieren

**Anfrage**
```
POST /api/arxiv/import/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "keywords": ["Künstliche Intelligenz", "Machine Learning"],
    "publishedDate": "2023-04-15T14:22:10Z",
    "doi": "10.12345/example.doi",
    "license": "arXiv non-exclusive license to distribute",
    "status": "published",
    "arxivMetadata": {
      "id": "2304.12345",
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "updated": "2023-04-16T09:15:30Z",
      "comment": "20 pages, 8 figures",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "url": "https://arxiv.org/abs/2304.12345",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345"
    },
    "storage": {
      "ipfs": "QmXa12S5sdf87gh4j56k7l...",
      "bittorrent": "a1b2c3d4e5f6...",
      "timestamp": "2023-05-10T08:12:45Z"
    }
  },
  "message": "Publikation erfolgreich importiert"
}
```

### Mehrere Publikationen importieren

**Anfrage**
```
POST /api/arxiv/import
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 42,
  "imported": 5,
  "failed": 0,
  "publications": [
    {
      "id": "pub-789012",
      "title": "Quantum Computing Applications",
      "authors": ["Dr. John Doe", "Prof. Jane Smith"],
      "abstract": "This study explores...",
      "keywords": ["Quantum Computing", "Quantum Physics"],
      "publishedDate": "2023-04-15T14:22:10Z",
      "doi": "10.12345/example.doi",
      "license": "arXiv non-exclusive license to distribute",
      "status": "published",
      "arxivMetadata": {
        "id": "2304.12345",
        "categories": ["quant-ph", "cs.ET"],
        "primaryCategory": "quant-ph",
        "updated": "2023-04-16T09:15:30Z",
        "comment": "15 pages, 5 figures",
        "journalRef": "Journal of Quantum Computing, Vol. 10, pp. 45-60",
        "url": "https://arxiv.org/abs/2304.12345",
        "pdfUrl": "https://arxiv.org/pdf/2304.12345"
      }
    },
    // Weitere Publikationen...
  ]
}
```

### NFT für eine Publikation erstellen

**Anfrage**
```
POST /api/arxiv/nft/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Body**
```json
{
  "recipient": "0x1234567890abcdef1234567890abcdef12345678",
  "royaltyPercentage": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "doi": "10.12345/example.doi"
  },
  "nft": {
    "tokenId": "42",
    "metadata": {
      "name": "Anwendung von KI in der Medizin",
      "description": "Diese Studie untersucht...",
      "external_url": "https://arxiv.org/abs/2304.12345",
      "attributes": [
        { "trait_type": "Type", "value": "Scientific Paper" },
        { "trait_type": "Source", "value": "arXiv" },
        { "trait_type": "arXiv ID", "value": "2304.12345" },
        { "trait_type": "DOI", "value": "10.12345/example.doi" },
        { "trait_type": "Category", "value": "cs.AI" },
        { "trait_type": "Authors", "value": "Dr. Maria Schmidt, Prof. Hans Müller" },
        { "trait_type": "Publication Date", "value": "2023-04-15" }
      ]
    }
  },
  "tokenId": "42",
  "transactionHash": "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
  "message": "NFT erfolgreich erstellt"
}
```

### NFTs für mehrere Publikationen erstellen

**Anfrage**
```
POST /api/arxiv/nft
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5,
  "nftOptions": {
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 5,
  "withDOI": 3,
  "createdNFTs": 3,
  "failed": 0,
  "nfts": [
    {
      "publication": {
        "id": "pub-789012",
        "title": "Quantum Computing Applications",
        "authors": ["Dr. John Doe", "Prof. Jane Smith"],
        "abstract": "This study explores...",
        "doi": "10.12345/example.doi"
      },
      "nft": {
        "tokenId": "42",
        "metadata": {
          "name": "Quantum Computing Applications",
          "description": "This study explores...",
          "external_url": "https://arxiv.org/abs/2304.12345",
          "attributes": [
            { "trait_type": "Type", "value": "Scientific Paper" },
            { "trait_type": "Source", "value": "arXiv" },
            { "trait_type": "arXiv ID", "value": "2304.12345" },
            { "trait_type": "DOI", "value": "10.12345/example.doi" },
            { "trait_type": "Category", "value": "quant-ph" },
            { "trait_type": "Authors", "value": "Dr. John Doe, Prof. Jane Smith" },
            { "trait_type": "Publication Date", "value": "2023-04-15" }
          ]
        }
      },
      "tokenId": "42"
    },
    // Weitere NFTs...
  ]
}
```

### Statistiken abrufen

**Anfrage**
```
GET /api/arxiv/stats
```

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "stats": {
    "importedPapers": 42,
    "createdNFTs": 15,
    "failedImports": 3,
    "papersByCategory": {
      "cs.AI": 12,
      "quant-ph": 8,
      "physics": 22
    },
    "apiStats": {
      "totalRequests": 150,
      "successfulRequests": 147,
      "failedRequests": 3,
      "retries": 5,
      "requestsByEndpoint": {
        "query": 150
      },
      "successRate": "98.00%"
    },
    "successRate": "92.86%"
  }
}
```

## arXiv-Kategorien

Die arXiv-Integration unterstützt alle arXiv-Kategorien, darunter:

- **Physik**: astro-ph, cond-mat, gr-qc, hep-ex, hep-lat, hep-ph, hep-th, math-ph, nlin, nucl-ex, nucl-th, physics, quant-ph
- **Mathematik**: math
- **Informatik**: cs.AI, cs.AR, cs.CC, cs.CE, cs.CG, cs.CL, cs.CR, cs.CV, cs.CY, cs.DB, cs.DC, cs.DL, cs.DM, cs.DS, cs.ET, cs.FL, cs.GL, cs.GR, cs.GT, cs.HC, cs.IR, cs.IT, cs.LG, cs.LO, cs.MA, cs.MM, cs.MS, cs.NA, cs.NE, cs.NI, cs.OS, cs.PF, cs.PL, cs.RO, cs.SC, cs.SD, cs.SE, cs.SI, cs.SY
- **Quantitative Biologie**: q-bio
- **Quantitative Finanzen**: q-fin
- **Statistik**: stat
- **Elektrotechnik und Systemwissenschaft**: eess
- **Wirtschaftswissenschaften**: econ

Eine vollständige Liste der Kategorien und Unterkategorien finden Sie auf der [arXiv-Taxonomie-Seite](https://arxiv.org/category_taxonomy).

## Nutzungshinweise

Bei der Verwendung der arXiv-Integration sind folgende Punkte zu beachten:

1. **API-Limits**: Die arXiv API hat Ratenbegrenzungen. Es wird empfohlen, nicht mehr als eine Anfrage alle 3 Sekunden zu stellen.

2. **Urheberrecht**: Die meisten arXiv-Publikationen stehen unter der "arXiv non-exclusive license to distribute", die keine Weiterverbreitung erlaubt. Stellen Sie sicher, dass Sie die Urheberrechte respektieren.

3. **DOIs und NFTs**: Nur Publikationen mit DOIs können als NFTs tokenisiert werden. Nicht alle arXiv-Publikationen haben DOIs.

4. **Metadaten**: Die arXiv API liefert nur Metadaten, keinen Volltext. Der Volltext kann über die PDF-URL abgerufen werden, aber dies ist nicht Teil der API-Integration.

5. **Kategorien**: Bei der Suche nach Kategorien können sowohl Hauptkategorien (z.B. "physics") als auch Unterkategorien (z.B. "cs.AI") verwendet werden.

## Beispiele

### Curl-Beispiel: Suche nach Publikationen

```bash
curl -X GET "http://localhost:3000/api/arxiv/search?query=quantum+computing&category=quant-ph&maxResults=5"
```

### Curl-Beispiel: Publikation importieren

```bash
curl -X POST "http://localhost:3000/api/arxiv/import/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..."
```

### Curl-Beispiel: NFT erstellen

```bash
curl -X POST "http://localhost:3000/api/arxiv/nft/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..." \
  -H "Content-Type: application/json" \
  -d '{
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }'
```# arXiv-Integration für DeSci-Scholar

Diese Dokumentation beschreibt die Integration von arXiv in DeSci-Scholar, die es ermöglicht, wissenschaftliche Publikationen von arXiv zu suchen, zu importieren und als NFTs zu tokenisieren.

## Überblick

Die arXiv-Integration bietet folgende Funktionen:

- Suche nach wissenschaftlichen Publikationen in arXiv
- Abruf von Publikationsdetails anhand der arXiv-ID
- Import von arXiv-Publikationen in DeSci-Scholar
- Erstellung von NFTs für arXiv-Publikationen mit DOIs
- Abruf der neuesten Publikationen in bestimmten Kategorien
- Statistiken zur arXiv-Integration

## API-Endpunkte

### Suche nach Publikationen

**Anfrage**
```
GET /api/arxiv/search
```

**Query-Parameter**
- `query` - Allgemeiner Suchbegriff
- `title` - Suche im Titel
- `author` - Suche nach Autor
- `category` - Suche nach Kategorie (z.B. "physics", "cs.AI")
- `id` - Suche nach arXiv-ID
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 10)
- `start` - Startposition für Paginierung (Standard: 0)
- `sortBy` - Sortierkriterium ("relevance", "lastUpdatedDate", "submittedDate")
- `sortOrder` - Sortierreihenfolge ("ascending", "descending")

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalResults": 42,
  "startIndex": 0,
  "itemsPerPage": 10,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikationsdetails abrufen

**Anfrage**
```
GET /api/arxiv/paper/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Antwort (200 OK)**
```json
{
  "success": true,
  "paper": {
    "id": "2304.12345",
    "title": "Anwendung von KI in der Medizin",
    "abstract": "Diese Studie untersucht...",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "categories": ["cs.AI", "cs.LG"],
    "primaryCategory": "cs.AI",
    "published": "2023-04-15T14:22:10Z",
    "updated": "2023-04-16T09:15:30Z",
    "doi": "10.12345/example.doi",
    "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
    "comment": "20 pages, 8 figures",
    "pdfUrl": "https://arxiv.org/pdf/2304.12345",
    "arxivUrl": "https://arxiv.org/abs/2304.12345"
  }
}
```

### Neueste Publikationen in einer Kategorie abrufen

**Anfrage**
```
GET /api/arxiv/recent/:category
```

**Parameter**
- `category` - arXiv-Kategorie (z.B. "cs.AI", "physics")

**Query-Parameter**
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 30)

**Antwort (200 OK)**
```json
{
  "success": true,
  "category": "cs.AI",
  "categoryName": "Künstliche Intelligenz",
  "totalResults": 42,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikation importieren

**Anfrage**
```
POST /api/arxiv/import/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "keywords": ["Künstliche Intelligenz", "Machine Learning"],
    "publishedDate": "2023-04-15T14:22:10Z",
    "doi": "10.12345/example.doi",
    "license": "arXiv non-exclusive license to distribute",
    "status": "published",
    "arxivMetadata": {
      "id": "2304.12345",
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "updated": "2023-04-16T09:15:30Z",
      "comment": "20 pages, 8 figures",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "url": "https://arxiv.org/abs/2304.12345",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345"
    },
    "storage": {
      "ipfs": "QmXa12S5sdf87gh4j56k7l...",
      "bittorrent": "a1b2c3d4e5f6...",
      "timestamp": "2023-05-10T08:12:45Z"
    }
  },
  "message": "Publikation erfolgreich importiert"
}
```

### Mehrere Publikationen importieren

**Anfrage**
```
POST /api/arxiv/import
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 42,
  "imported": 5,
  "failed": 0,
  "publications": [
    {
      "id": "pub-789012",
      "title": "Quantum Computing Applications",
      "authors": ["Dr. John Doe", "Prof. Jane Smith"],
      "abstract": "This study explores...",
      "keywords": ["Quantum Computing", "Quantum Physics"],
      "publishedDate": "2023-04-15T14:22:10Z",
      "doi": "10.12345/example.doi",
      "license": "arXiv non-exclusive license to distribute",
      "status": "published",
      "arxivMetadata": {
        "id": "2304.12345",
        "categories": ["quant-ph", "cs.ET"],
        "primaryCategory": "quant-ph",
        "updated": "2023-04-16T09:15:30Z",
        "comment": "15 pages, 5 figures",
        "journalRef": "Journal of Quantum Computing, Vol. 10, pp. 45-60",
        "url": "https://arxiv.org/abs/2304.12345",
        "pdfUrl": "https://arxiv.org/pdf/2304.12345"
      }
    },
    // Weitere Publikationen...
  ]
}
```

### NFT für eine Publikation erstellen

**Anfrage**
```
POST /api/arxiv/nft/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Body**
```json
{
  "recipient": "0x1234567890abcdef1234567890abcdef12345678",
  "royaltyPercentage": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "doi": "10.12345/example.doi"
  },
  "nft": {
    "tokenId": "42",
    "metadata": {
      "name": "Anwendung von KI in der Medizin",
      "description": "Diese Studie untersucht...",
      "external_url": "https://arxiv.org/abs/2304.12345",
      "attributes": [
        { "trait_type": "Type", "value": "Scientific Paper" },
        { "trait_type": "Source", "value": "arXiv" },
        { "trait_type": "arXiv ID", "value": "2304.12345" },
        { "trait_type": "DOI", "value": "10.12345/example.doi" },
        { "trait_type": "Category", "value": "cs.AI" },
        { "trait_type": "Authors", "value": "Dr. Maria Schmidt, Prof. Hans Müller" },
        { "trait_type": "Publication Date", "value": "2023-04-15" }
      ]
    }
  },
  "tokenId": "42",
  "transactionHash": "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
  "message": "NFT erfolgreich erstellt"
}
```

### NFTs für mehrere Publikationen erstellen

**Anfrage**
```
POST /api/arxiv/nft
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5,
  "nftOptions": {
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 5,
  "withDOI": 3,
  "createdNFTs": 3,
  "failed": 0,
  "nfts": [
    {
      "publication": {
        "id": "pub-789012",
        "title": "Quantum Computing Applications",
        "authors": ["Dr. John Doe", "Prof. Jane Smith"],
        "abstract": "This study explores...",
        "doi": "10.12345/example.doi"
      },
      "nft": {
        "tokenId": "42",
        "metadata": {
          "name": "Quantum Computing Applications",
          "description": "This study explores...",
          "external_url": "https://arxiv.org/abs/2304.12345",
          "attributes": [
            { "trait_type": "Type", "value": "Scientific Paper" },
            { "trait_type": "Source", "value": "arXiv" },
            { "trait_type": "arXiv ID", "value": "2304.12345" },
            { "trait_type": "DOI", "value": "10.12345/example.doi" },
            { "trait_type": "Category", "value": "quant-ph" },
            { "trait_type": "Authors", "value": "Dr. John Doe, Prof. Jane Smith" },
            { "trait_type": "Publication Date", "value": "2023-04-15" }
          ]
        }
      },
      "tokenId": "42"
    },
    // Weitere NFTs...
  ]
}
```

### Statistiken abrufen

**Anfrage**
```
GET /api/arxiv/stats
```

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "stats": {
    "importedPapers": 42,
    "createdNFTs": 15,
    "failedImports": 3,
    "papersByCategory": {
      "cs.AI": 12,
      "quant-ph": 8,
      "physics": 22
    },
    "apiStats": {
      "totalRequests": 150,
      "successfulRequests": 147,
      "failedRequests": 3,
      "retries": 5,
      "requestsByEndpoint": {
        "query": 150
      },
      "successRate": "98.00%"
    },
    "successRate": "92.86%"
  }
}
```

## arXiv-Kategorien

Die arXiv-Integration unterstützt alle arXiv-Kategorien, darunter:

- **Physik**: astro-ph, cond-mat, gr-qc, hep-ex, hep-lat, hep-ph, hep-th, math-ph, nlin, nucl-ex, nucl-th, physics, quant-ph
- **Mathematik**: math
- **Informatik**: cs.AI, cs.AR, cs.CC, cs.CE, cs.CG, cs.CL, cs.CR, cs.CV, cs.CY, cs.DB, cs.DC, cs.DL, cs.DM, cs.DS, cs.ET, cs.FL, cs.GL, cs.GR, cs.GT, cs.HC, cs.IR, cs.IT, cs.LG, cs.LO, cs.MA, cs.MM, cs.MS, cs.NA, cs.NE, cs.NI, cs.OS, cs.PF, cs.PL, cs.RO, cs.SC, cs.SD, cs.SE, cs.SI, cs.SY
- **Quantitative Biologie**: q-bio
- **Quantitative Finanzen**: q-fin
- **Statistik**: stat
- **Elektrotechnik und Systemwissenschaft**: eess
- **Wirtschaftswissenschaften**: econ

Eine vollständige Liste der Kategorien und Unterkategorien finden Sie auf der [arXiv-Taxonomie-Seite](https://arxiv.org/category_taxonomy).

## Nutzungshinweise

Bei der Verwendung der arXiv-Integration sind folgende Punkte zu beachten:

1. **API-Limits**: Die arXiv API hat Ratenbegrenzungen. Es wird empfohlen, nicht mehr als eine Anfrage alle 3 Sekunden zu stellen.

2. **Urheberrecht**: Die meisten arXiv-Publikationen stehen unter der "arXiv non-exclusive license to distribute", die keine Weiterverbreitung erlaubt. Stellen Sie sicher, dass Sie die Urheberrechte respektieren.

3. **DOIs und NFTs**: Nur Publikationen mit DOIs können als NFTs tokenisiert werden. Nicht alle arXiv-Publikationen haben DOIs.

4. **Metadaten**: Die arXiv API liefert nur Metadaten, keinen Volltext. Der Volltext kann über die PDF-URL abgerufen werden, aber dies ist nicht Teil der API-Integration.

5. **Kategorien**: Bei der Suche nach Kategorien können sowohl Hauptkategorien (z.B. "physics") als auch Unterkategorien (z.B. "cs.AI") verwendet werden.

## Beispiele

### Curl-Beispiel: Suche nach Publikationen

```bash
curl -X GET "http://localhost:3000/api/arxiv/search?query=quantum+computing&category=quant-ph&maxResults=5"
```

### Curl-Beispiel: Publikation importieren

```bash
curl -X POST "http://localhost:3000/api/arxiv/import/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..."
```

### Curl-Beispiel: NFT erstellen

```bash
curl -X POST "http://localhost:3000/api/arxiv/nft/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..." \
  -H "Content-Type: application/json" \
  -d '{
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }'
```# arXiv-Integration für DeSci-Scholar

Diese Dokumentation beschreibt die Integration von arXiv in DeSci-Scholar, die es ermöglicht, wissenschaftliche Publikationen von arXiv zu suchen, zu importieren und als NFTs zu tokenisieren.

## Überblick

Die arXiv-Integration bietet folgende Funktionen:

- Suche nach wissenschaftlichen Publikationen in arXiv
- Abruf von Publikationsdetails anhand der arXiv-ID
- Import von arXiv-Publikationen in DeSci-Scholar
- Erstellung von NFTs für arXiv-Publikationen mit DOIs
- Abruf der neuesten Publikationen in bestimmten Kategorien
- Statistiken zur arXiv-Integration

## API-Endpunkte

### Suche nach Publikationen

**Anfrage**
```
GET /api/arxiv/search
```

**Query-Parameter**
- `query` - Allgemeiner Suchbegriff
- `title` - Suche im Titel
- `author` - Suche nach Autor
- `category` - Suche nach Kategorie (z.B. "physics", "cs.AI")
- `id` - Suche nach arXiv-ID
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 10)
- `start` - Startposition für Paginierung (Standard: 0)
- `sortBy` - Sortierkriterium ("relevance", "lastUpdatedDate", "submittedDate")
- `sortOrder` - Sortierreihenfolge ("ascending", "descending")

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalResults": 42,
  "startIndex": 0,
  "itemsPerPage": 10,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikationsdetails abrufen

**Anfrage**
```
GET /api/arxiv/paper/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Antwort (200 OK)**
```json
{
  "success": true,
  "paper": {
    "id": "2304.12345",
    "title": "Anwendung von KI in der Medizin",
    "abstract": "Diese Studie untersucht...",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "categories": ["cs.AI", "cs.LG"],
    "primaryCategory": "cs.AI",
    "published": "2023-04-15T14:22:10Z",
    "updated": "2023-04-16T09:15:30Z",
    "doi": "10.12345/example.doi",
    "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
    "comment": "20 pages, 8 figures",
    "pdfUrl": "https://arxiv.org/pdf/2304.12345",
    "arxivUrl": "https://arxiv.org/abs/2304.12345"
  }
}
```

### Neueste Publikationen in einer Kategorie abrufen

**Anfrage**
```
GET /api/arxiv/recent/:category
```

**Parameter**
- `category` - arXiv-Kategorie (z.B. "cs.AI", "physics")

**Query-Parameter**
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 30)

**Antwort (200 OK)**
```json
{
  "success": true,
  "category": "cs.AI",
  "categoryName": "Künstliche Intelligenz",
  "totalResults": 42,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikation importieren

**Anfrage**
```
POST /api/arxiv/import/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "keywords": ["Künstliche Intelligenz", "Machine Learning"],
    "publishedDate": "2023-04-15T14:22:10Z",
    "doi": "10.12345/example.doi",
    "license": "arXiv non-exclusive license to distribute",
    "status": "published",
    "arxivMetadata": {
      "id": "2304.12345",
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "updated": "2023-04-16T09:15:30Z",
      "comment": "20 pages, 8 figures",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "url": "https://arxiv.org/abs/2304.12345",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345"
    },
    "storage": {
      "ipfs": "QmXa12S5sdf87gh4j56k7l...",
      "bittorrent": "a1b2c3d4e5f6...",
      "timestamp": "2023-05-10T08:12:45Z"
    }
  },
  "message": "Publikation erfolgreich importiert"
}
```

### Mehrere Publikationen importieren

**Anfrage**
```
POST /api/arxiv/import
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 42,
  "imported": 5,
  "failed": 0,
  "publications": [
    {
      "id": "pub-789012",
      "title": "Quantum Computing Applications",
      "authors": ["Dr. John Doe", "Prof. Jane Smith"],
      "abstract": "This study explores...",
      "keywords": ["Quantum Computing", "Quantum Physics"],
      "publishedDate": "2023-04-15T14:22:10Z",
      "doi": "10.12345/example.doi",
      "license": "arXiv non-exclusive license to distribute",
      "status": "published",
      "arxivMetadata": {
        "id": "2304.12345",
        "categories": ["quant-ph", "cs.ET"],
        "primaryCategory": "quant-ph",
        "updated": "2023-04-16T09:15:30Z",
        "comment": "15 pages, 5 figures",
        "journalRef": "Journal of Quantum Computing, Vol. 10, pp. 45-60",
        "url": "https://arxiv.org/abs/2304.12345",
        "pdfUrl": "https://arxiv.org/pdf/2304.12345"
      }
    },
    // Weitere Publikationen...
  ]
}
```

### NFT für eine Publikation erstellen

**Anfrage**
```
POST /api/arxiv/nft/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Body**
```json
{
  "recipient": "0x1234567890abcdef1234567890abcdef12345678",
  "royaltyPercentage": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "doi": "10.12345/example.doi"
  },
  "nft": {
    "tokenId": "42",
    "metadata": {
      "name": "Anwendung von KI in der Medizin",
      "description": "Diese Studie untersucht...",
      "external_url": "https://arxiv.org/abs/2304.12345",
      "attributes": [
        { "trait_type": "Type", "value": "Scientific Paper" },
        { "trait_type": "Source", "value": "arXiv" },
        { "trait_type": "arXiv ID", "value": "2304.12345" },
        { "trait_type": "DOI", "value": "10.12345/example.doi" },
        { "trait_type": "Category", "value": "cs.AI" },
        { "trait_type": "Authors", "value": "Dr. Maria Schmidt, Prof. Hans Müller" },
        { "trait_type": "Publication Date", "value": "2023-04-15" }
      ]
    }
  },
  "tokenId": "42",
  "transactionHash": "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
  "message": "NFT erfolgreich erstellt"
}
```

### NFTs für mehrere Publikationen erstellen

**Anfrage**
```
POST /api/arxiv/nft
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5,
  "nftOptions": {
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 5,
  "withDOI": 3,
  "createdNFTs": 3,
  "failed": 0,
  "nfts": [
    {
      "publication": {
        "id": "pub-789012",
        "title": "Quantum Computing Applications",
        "authors": ["Dr. John Doe", "Prof. Jane Smith"],
        "abstract": "This study explores...",
        "doi": "10.12345/example.doi"
      },
      "nft": {
        "tokenId": "42",
        "metadata": {
          "name": "Quantum Computing Applications",
          "description": "This study explores...",
          "external_url": "https://arxiv.org/abs/2304.12345",
          "attributes": [
            { "trait_type": "Type", "value": "Scientific Paper" },
            { "trait_type": "Source", "value": "arXiv" },
            { "trait_type": "arXiv ID", "value": "2304.12345" },
            { "trait_type": "DOI", "value": "10.12345/example.doi" },
            { "trait_type": "Category", "value": "quant-ph" },
            { "trait_type": "Authors", "value": "Dr. John Doe, Prof. Jane Smith" },
            { "trait_type": "Publication Date", "value": "2023-04-15" }
          ]
        }
      },
      "tokenId": "42"
    },
    // Weitere NFTs...
  ]
}
```

### Statistiken abrufen

**Anfrage**
```
GET /api/arxiv/stats
```

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "stats": {
    "importedPapers": 42,
    "createdNFTs": 15,
    "failedImports": 3,
    "papersByCategory": {
      "cs.AI": 12,
      "quant-ph": 8,
      "physics": 22
    },
    "apiStats": {
      "totalRequests": 150,
      "successfulRequests": 147,
      "failedRequests": 3,
      "retries": 5,
      "requestsByEndpoint": {
        "query": 150
      },
      "successRate": "98.00%"
    },
    "successRate": "92.86%"
  }
}
```

## arXiv-Kategorien

Die arXiv-Integration unterstützt alle arXiv-Kategorien, darunter:

- **Physik**: astro-ph, cond-mat, gr-qc, hep-ex, hep-lat, hep-ph, hep-th, math-ph, nlin, nucl-ex, nucl-th, physics, quant-ph
- **Mathematik**: math
- **Informatik**: cs.AI, cs.AR, cs.CC, cs.CE, cs.CG, cs.CL, cs.CR, cs.CV, cs.CY, cs.DB, cs.DC, cs.DL, cs.DM, cs.DS, cs.ET, cs.FL, cs.GL, cs.GR, cs.GT, cs.HC, cs.IR, cs.IT, cs.LG, cs.LO, cs.MA, cs.MM, cs.MS, cs.NA, cs.NE, cs.NI, cs.OS, cs.PF, cs.PL, cs.RO, cs.SC, cs.SD, cs.SE, cs.SI, cs.SY
- **Quantitative Biologie**: q-bio
- **Quantitative Finanzen**: q-fin
- **Statistik**: stat
- **Elektrotechnik und Systemwissenschaft**: eess
- **Wirtschaftswissenschaften**: econ

Eine vollständige Liste der Kategorien und Unterkategorien finden Sie auf der [arXiv-Taxonomie-Seite](https://arxiv.org/category_taxonomy).

## Nutzungshinweise

Bei der Verwendung der arXiv-Integration sind folgende Punkte zu beachten:

1. **API-Limits**: Die arXiv API hat Ratenbegrenzungen. Es wird empfohlen, nicht mehr als eine Anfrage alle 3 Sekunden zu stellen.

2. **Urheberrecht**: Die meisten arXiv-Publikationen stehen unter der "arXiv non-exclusive license to distribute", die keine Weiterverbreitung erlaubt. Stellen Sie sicher, dass Sie die Urheberrechte respektieren.

3. **DOIs und NFTs**: Nur Publikationen mit DOIs können als NFTs tokenisiert werden. Nicht alle arXiv-Publikationen haben DOIs.

4. **Metadaten**: Die arXiv API liefert nur Metadaten, keinen Volltext. Der Volltext kann über die PDF-URL abgerufen werden, aber dies ist nicht Teil der API-Integration.

5. **Kategorien**: Bei der Suche nach Kategorien können sowohl Hauptkategorien (z.B. "physics") als auch Unterkategorien (z.B. "cs.AI") verwendet werden.

## Beispiele

### Curl-Beispiel: Suche nach Publikationen

```bash
curl -X GET "http://localhost:3000/api/arxiv/search?query=quantum+computing&category=quant-ph&maxResults=5"
```

### Curl-Beispiel: Publikation importieren

```bash
curl -X POST "http://localhost:3000/api/arxiv/import/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..."
```

### Curl-Beispiel: NFT erstellen

```bash
curl -X POST "http://localhost:3000/api/arxiv/nft/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..." \
  -H "Content-Type: application/json" \
  -d '{
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }'
```# arXiv-Integration für DeSci-Scholar

Diese Dokumentation beschreibt die Integration von arXiv in DeSci-Scholar, die es ermöglicht, wissenschaftliche Publikationen von arXiv zu suchen, zu importieren und als NFTs zu tokenisieren.

## Überblick

Die arXiv-Integration bietet folgende Funktionen:

- Suche nach wissenschaftlichen Publikationen in arXiv
- Abruf von Publikationsdetails anhand der arXiv-ID
- Import von arXiv-Publikationen in DeSci-Scholar
- Erstellung von NFTs für arXiv-Publikationen mit DOIs
- Abruf der neuesten Publikationen in bestimmten Kategorien
- Statistiken zur arXiv-Integration

## API-Endpunkte

### Suche nach Publikationen

**Anfrage**
```
GET /api/arxiv/search
```

**Query-Parameter**
- `query` - Allgemeiner Suchbegriff
- `title` - Suche im Titel
- `author` - Suche nach Autor
- `category` - Suche nach Kategorie (z.B. "physics", "cs.AI")
- `id` - Suche nach arXiv-ID
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 10)
- `start` - Startposition für Paginierung (Standard: 0)
- `sortBy` - Sortierkriterium ("relevance", "lastUpdatedDate", "submittedDate")
- `sortOrder` - Sortierreihenfolge ("ascending", "descending")

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalResults": 42,
  "startIndex": 0,
  "itemsPerPage": 10,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikationsdetails abrufen

**Anfrage**
```
GET /api/arxiv/paper/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Antwort (200 OK)**
```json
{
  "success": true,
  "paper": {
    "id": "2304.12345",
    "title": "Anwendung von KI in der Medizin",
    "abstract": "Diese Studie untersucht...",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "categories": ["cs.AI", "cs.LG"],
    "primaryCategory": "cs.AI",
    "published": "2023-04-15T14:22:10Z",
    "updated": "2023-04-16T09:15:30Z",
    "doi": "10.12345/example.doi",
    "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
    "comment": "20 pages, 8 figures",
    "pdfUrl": "https://arxiv.org/pdf/2304.12345",
    "arxivUrl": "https://arxiv.org/abs/2304.12345"
  }
}
```

### Neueste Publikationen in einer Kategorie abrufen

**Anfrage**
```
GET /api/arxiv/recent/:category
```

**Parameter**
- `category` - arXiv-Kategorie (z.B. "cs.AI", "physics")

**Query-Parameter**
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 30)

**Antwort (200 OK)**
```json
{
  "success": true,
  "category": "cs.AI",
  "categoryName": "Künstliche Intelligenz",
  "totalResults": 42,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikation importieren

**Anfrage**
```
POST /api/arxiv/import/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "keywords": ["Künstliche Intelligenz", "Machine Learning"],
    "publishedDate": "2023-04-15T14:22:10Z",
    "doi": "10.12345/example.doi",
    "license": "arXiv non-exclusive license to distribute",
    "status": "published",
    "arxivMetadata": {
      "id": "2304.12345",
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "updated": "2023-04-16T09:15:30Z",
      "comment": "20 pages, 8 figures",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "url": "https://arxiv.org/abs/2304.12345",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345"
    },
    "storage": {
      "ipfs": "QmXa12S5sdf87gh4j56k7l...",
      "bittorrent": "a1b2c3d4e5f6...",
      "timestamp": "2023-05-10T08:12:45Z"
    }
  },
  "message": "Publikation erfolgreich importiert"
}
```

### Mehrere Publikationen importieren

**Anfrage**
```
POST /api/arxiv/import
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 42,
  "imported": 5,
  "failed": 0,
  "publications": [
    {
      "id": "pub-789012",
      "title": "Quantum Computing Applications",
      "authors": ["Dr. John Doe", "Prof. Jane Smith"],
      "abstract": "This study explores...",
      "keywords": ["Quantum Computing", "Quantum Physics"],
      "publishedDate": "2023-04-15T14:22:10Z",
      "doi": "10.12345/example.doi",
      "license": "arXiv non-exclusive license to distribute",
      "status": "published",
      "arxivMetadata": {
        "id": "2304.12345",
        "categories": ["quant-ph", "cs.ET"],
        "primaryCategory": "quant-ph",
        "updated": "2023-04-16T09:15:30Z",
        "comment": "15 pages, 5 figures",
        "journalRef": "Journal of Quantum Computing, Vol. 10, pp. 45-60",
        "url": "https://arxiv.org/abs/2304.12345",
        "pdfUrl": "https://arxiv.org/pdf/2304.12345"
      }
    },
    // Weitere Publikationen...
  ]
}
```

### NFT für eine Publikation erstellen

**Anfrage**
```
POST /api/arxiv/nft/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Body**
```json
{
  "recipient": "0x1234567890abcdef1234567890abcdef12345678",
  "royaltyPercentage": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "doi": "10.12345/example.doi"
  },
  "nft": {
    "tokenId": "42",
    "metadata": {
      "name": "Anwendung von KI in der Medizin",
      "description": "Diese Studie untersucht...",
      "external_url": "https://arxiv.org/abs/2304.12345",
      "attributes": [
        { "trait_type": "Type", "value": "Scientific Paper" },
        { "trait_type": "Source", "value": "arXiv" },
        { "trait_type": "arXiv ID", "value": "2304.12345" },
        { "trait_type": "DOI", "value": "10.12345/example.doi" },
        { "trait_type": "Category", "value": "cs.AI" },
        { "trait_type": "Authors", "value": "Dr. Maria Schmidt, Prof. Hans Müller" },
        { "trait_type": "Publication Date", "value": "2023-04-15" }
      ]
    }
  },
  "tokenId": "42",
  "transactionHash": "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
  "message": "NFT erfolgreich erstellt"
}
```

### NFTs für mehrere Publikationen erstellen

**Anfrage**
```
POST /api/arxiv/nft
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5,
  "nftOptions": {
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 5,
  "withDOI": 3,
  "createdNFTs": 3,
  "failed": 0,
  "nfts": [
    {
      "publication": {
        "id": "pub-789012",
        "title": "Quantum Computing Applications",
        "authors": ["Dr. John Doe", "Prof. Jane Smith"],
        "abstract": "This study explores...",
        "doi": "10.12345/example.doi"
      },
      "nft": {
        "tokenId": "42",
        "metadata": {
          "name": "Quantum Computing Applications",
          "description": "This study explores...",
          "external_url": "https://arxiv.org/abs/2304.12345",
          "attributes": [
            { "trait_type": "Type", "value": "Scientific Paper" },
            { "trait_type": "Source", "value": "arXiv" },
            { "trait_type": "arXiv ID", "value": "2304.12345" },
            { "trait_type": "DOI", "value": "10.12345/example.doi" },
            { "trait_type": "Category", "value": "quant-ph" },
            { "trait_type": "Authors", "value": "Dr. John Doe, Prof. Jane Smith" },
            { "trait_type": "Publication Date", "value": "2023-04-15" }
          ]
        }
      },
      "tokenId": "42"
    },
    // Weitere NFTs...
  ]
}
```

### Statistiken abrufen

**Anfrage**
```
GET /api/arxiv/stats
```

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "stats": {
    "importedPapers": 42,
    "createdNFTs": 15,
    "failedImports": 3,
    "papersByCategory": {
      "cs.AI": 12,
      "quant-ph": 8,
      "physics": 22
    },
    "apiStats": {
      "totalRequests": 150,
      "successfulRequests": 147,
      "failedRequests": 3,
      "retries": 5,
      "requestsByEndpoint": {
        "query": 150
      },
      "successRate": "98.00%"
    },
    "successRate": "92.86%"
  }
}
```

## arXiv-Kategorien

Die arXiv-Integration unterstützt alle arXiv-Kategorien, darunter:

- **Physik**: astro-ph, cond-mat, gr-qc, hep-ex, hep-lat, hep-ph, hep-th, math-ph, nlin, nucl-ex, nucl-th, physics, quant-ph
- **Mathematik**: math
- **Informatik**: cs.AI, cs.AR, cs.CC, cs.CE, cs.CG, cs.CL, cs.CR, cs.CV, cs.CY, cs.DB, cs.DC, cs.DL, cs.DM, cs.DS, cs.ET, cs.FL, cs.GL, cs.GR, cs.GT, cs.HC, cs.IR, cs.IT, cs.LG, cs.LO, cs.MA, cs.MM, cs.MS, cs.NA, cs.NE, cs.NI, cs.OS, cs.PF, cs.PL, cs.RO, cs.SC, cs.SD, cs.SE, cs.SI, cs.SY
- **Quantitative Biologie**: q-bio
- **Quantitative Finanzen**: q-fin
- **Statistik**: stat
- **Elektrotechnik und Systemwissenschaft**: eess
- **Wirtschaftswissenschaften**: econ

Eine vollständige Liste der Kategorien und Unterkategorien finden Sie auf der [arXiv-Taxonomie-Seite](https://arxiv.org/category_taxonomy).

## Nutzungshinweise

Bei der Verwendung der arXiv-Integration sind folgende Punkte zu beachten:

1. **API-Limits**: Die arXiv API hat Ratenbegrenzungen. Es wird empfohlen, nicht mehr als eine Anfrage alle 3 Sekunden zu stellen.

2. **Urheberrecht**: Die meisten arXiv-Publikationen stehen unter der "arXiv non-exclusive license to distribute", die keine Weiterverbreitung erlaubt. Stellen Sie sicher, dass Sie die Urheberrechte respektieren.

3. **DOIs und NFTs**: Nur Publikationen mit DOIs können als NFTs tokenisiert werden. Nicht alle arXiv-Publikationen haben DOIs.

4. **Metadaten**: Die arXiv API liefert nur Metadaten, keinen Volltext. Der Volltext kann über die PDF-URL abgerufen werden, aber dies ist nicht Teil der API-Integration.

5. **Kategorien**: Bei der Suche nach Kategorien können sowohl Hauptkategorien (z.B. "physics") als auch Unterkategorien (z.B. "cs.AI") verwendet werden.

## Beispiele

### Curl-Beispiel: Suche nach Publikationen

```bash
curl -X GET "http://localhost:3000/api/arxiv/search?query=quantum+computing&category=quant-ph&maxResults=5"
```

### Curl-Beispiel: Publikation importieren

```bash
curl -X POST "http://localhost:3000/api/arxiv/import/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..."
```

### Curl-Beispiel: NFT erstellen

```bash
curl -X POST "http://localhost:3000/api/arxiv/nft/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..." \
  -H "Content-Type: application/json" \
  -d '{
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }'
```# arXiv-Integration für DeSci-Scholar

Diese Dokumentation beschreibt die Integration von arXiv in DeSci-Scholar, die es ermöglicht, wissenschaftliche Publikationen von arXiv zu suchen, zu importieren und als NFTs zu tokenisieren.

## Überblick

Die arXiv-Integration bietet folgende Funktionen:

- Suche nach wissenschaftlichen Publikationen in arXiv
- Abruf von Publikationsdetails anhand der arXiv-ID
- Import von arXiv-Publikationen in DeSci-Scholar
- Erstellung von NFTs für arXiv-Publikationen mit DOIs
- Abruf der neuesten Publikationen in bestimmten Kategorien
- Statistiken zur arXiv-Integration

## API-Endpunkte

### Suche nach Publikationen

**Anfrage**
```
GET /api/arxiv/search
```

**Query-Parameter**
- `query` - Allgemeiner Suchbegriff
- `title` - Suche im Titel
- `author` - Suche nach Autor
- `category` - Suche nach Kategorie (z.B. "physics", "cs.AI")
- `id` - Suche nach arXiv-ID
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 10)
- `start` - Startposition für Paginierung (Standard: 0)
- `sortBy` - Sortierkriterium ("relevance", "lastUpdatedDate", "submittedDate")
- `sortOrder` - Sortierreihenfolge ("ascending", "descending")

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalResults": 42,
  "startIndex": 0,
  "itemsPerPage": 10,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikationsdetails abrufen

**Anfrage**
```
GET /api/arxiv/paper/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Antwort (200 OK)**
```json
{
  "success": true,
  "paper": {
    "id": "2304.12345",
    "title": "Anwendung von KI in der Medizin",
    "abstract": "Diese Studie untersucht...",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "categories": ["cs.AI", "cs.LG"],
    "primaryCategory": "cs.AI",
    "published": "2023-04-15T14:22:10Z",
    "updated": "2023-04-16T09:15:30Z",
    "doi": "10.12345/example.doi",
    "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
    "comment": "20 pages, 8 figures",
    "pdfUrl": "https://arxiv.org/pdf/2304.12345",
    "arxivUrl": "https://arxiv.org/abs/2304.12345"
  }
}
```

### Neueste Publikationen in einer Kategorie abrufen

**Anfrage**
```
GET /api/arxiv/recent/:category
```

**Parameter**
- `category` - arXiv-Kategorie (z.B. "cs.AI", "physics")

**Query-Parameter**
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 30)

**Antwort (200 OK)**
```json
{
  "success": true,
  "category": "cs.AI",
  "categoryName": "Künstliche Intelligenz",
  "totalResults": 42,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikation importieren

**Anfrage**
```
POST /api/arxiv/import/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "keywords": ["Künstliche Intelligenz", "Machine Learning"],
    "publishedDate": "2023-04-15T14:22:10Z",
    "doi": "10.12345/example.doi",
    "license": "arXiv non-exclusive license to distribute",
    "status": "published",
    "arxivMetadata": {
      "id": "2304.12345",
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "updated": "2023-04-16T09:15:30Z",
      "comment": "20 pages, 8 figures",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "url": "https://arxiv.org/abs/2304.12345",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345"
    },
    "storage": {
      "ipfs": "QmXa12S5sdf87gh4j56k7l...",
      "bittorrent": "a1b2c3d4e5f6...",
      "timestamp": "2023-05-10T08:12:45Z"
    }
  },
  "message": "Publikation erfolgreich importiert"
}
```

### Mehrere Publikationen importieren

**Anfrage**
```
POST /api/arxiv/import
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 42,
  "imported": 5,
  "failed": 0,
  "publications": [
    {
      "id": "pub-789012",
      "title": "Quantum Computing Applications",
      "authors": ["Dr. John Doe", "Prof. Jane Smith"],
      "abstract": "This study explores...",
      "keywords": ["Quantum Computing", "Quantum Physics"],
      "publishedDate": "2023-04-15T14:22:10Z",
      "doi": "10.12345/example.doi",
      "license": "arXiv non-exclusive license to distribute",
      "status": "published",
      "arxivMetadata": {
        "id": "2304.12345",
        "categories": ["quant-ph", "cs.ET"],
        "primaryCategory": "quant-ph",
        "updated": "2023-04-16T09:15:30Z",
        "comment": "15 pages, 5 figures",
        "journalRef": "Journal of Quantum Computing, Vol. 10, pp. 45-60",
        "url": "https://arxiv.org/abs/2304.12345",
        "pdfUrl": "https://arxiv.org/pdf/2304.12345"
      }
    },
    // Weitere Publikationen...
  ]
}
```

### NFT für eine Publikation erstellen

**Anfrage**
```
POST /api/arxiv/nft/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Body**
```json
{
  "recipient": "0x1234567890abcdef1234567890abcdef12345678",
  "royaltyPercentage": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "doi": "10.12345/example.doi"
  },
  "nft": {
    "tokenId": "42",
    "metadata": {
      "name": "Anwendung von KI in der Medizin",
      "description": "Diese Studie untersucht...",
      "external_url": "https://arxiv.org/abs/2304.12345",
      "attributes": [
        { "trait_type": "Type", "value": "Scientific Paper" },
        { "trait_type": "Source", "value": "arXiv" },
        { "trait_type": "arXiv ID", "value": "2304.12345" },
        { "trait_type": "DOI", "value": "10.12345/example.doi" },
        { "trait_type": "Category", "value": "cs.AI" },
        { "trait_type": "Authors", "value": "Dr. Maria Schmidt, Prof. Hans Müller" },
        { "trait_type": "Publication Date", "value": "2023-04-15" }
      ]
    }
  },
  "tokenId": "42",
  "transactionHash": "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
  "message": "NFT erfolgreich erstellt"
}
```

### NFTs für mehrere Publikationen erstellen

**Anfrage**
```
POST /api/arxiv/nft
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5,
  "nftOptions": {
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 5,
  "withDOI": 3,
  "createdNFTs": 3,
  "failed": 0,
  "nfts": [
    {
      "publication": {
        "id": "pub-789012",
        "title": "Quantum Computing Applications",
        "authors": ["Dr. John Doe", "Prof. Jane Smith"],
        "abstract": "This study explores...",
        "doi": "10.12345/example.doi"
      },
      "nft": {
        "tokenId": "42",
        "metadata": {
          "name": "Quantum Computing Applications",
          "description": "This study explores...",
          "external_url": "https://arxiv.org/abs/2304.12345",
          "attributes": [
            { "trait_type": "Type", "value": "Scientific Paper" },
            { "trait_type": "Source", "value": "arXiv" },
            { "trait_type": "arXiv ID", "value": "2304.12345" },
            { "trait_type": "DOI", "value": "10.12345/example.doi" },
            { "trait_type": "Category", "value": "quant-ph" },
            { "trait_type": "Authors", "value": "Dr. John Doe, Prof. Jane Smith" },
            { "trait_type": "Publication Date", "value": "2023-04-15" }
          ]
        }
      },
      "tokenId": "42"
    },
    // Weitere NFTs...
  ]
}
```

### Statistiken abrufen

**Anfrage**
```
GET /api/arxiv/stats
```

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "stats": {
    "importedPapers": 42,
    "createdNFTs": 15,
    "failedImports": 3,
    "papersByCategory": {
      "cs.AI": 12,
      "quant-ph": 8,
      "physics": 22
    },
    "apiStats": {
      "totalRequests": 150,
      "successfulRequests": 147,
      "failedRequests": 3,
      "retries": 5,
      "requestsByEndpoint": {
        "query": 150
      },
      "successRate": "98.00%"
    },
    "successRate": "92.86%"
  }
}
```

## arXiv-Kategorien

Die arXiv-Integration unterstützt alle arXiv-Kategorien, darunter:

- **Physik**: astro-ph, cond-mat, gr-qc, hep-ex, hep-lat, hep-ph, hep-th, math-ph, nlin, nucl-ex, nucl-th, physics, quant-ph
- **Mathematik**: math
- **Informatik**: cs.AI, cs.AR, cs.CC, cs.CE, cs.CG, cs.CL, cs.CR, cs.CV, cs.CY, cs.DB, cs.DC, cs.DL, cs.DM, cs.DS, cs.ET, cs.FL, cs.GL, cs.GR, cs.GT, cs.HC, cs.IR, cs.IT, cs.LG, cs.LO, cs.MA, cs.MM, cs.MS, cs.NA, cs.NE, cs.NI, cs.OS, cs.PF, cs.PL, cs.RO, cs.SC, cs.SD, cs.SE, cs.SI, cs.SY
- **Quantitative Biologie**: q-bio
- **Quantitative Finanzen**: q-fin
- **Statistik**: stat
- **Elektrotechnik und Systemwissenschaft**: eess
- **Wirtschaftswissenschaften**: econ

Eine vollständige Liste der Kategorien und Unterkategorien finden Sie auf der [arXiv-Taxonomie-Seite](https://arxiv.org/category_taxonomy).

## Nutzungshinweise

Bei der Verwendung der arXiv-Integration sind folgende Punkte zu beachten:

1. **API-Limits**: Die arXiv API hat Ratenbegrenzungen. Es wird empfohlen, nicht mehr als eine Anfrage alle 3 Sekunden zu stellen.

2. **Urheberrecht**: Die meisten arXiv-Publikationen stehen unter der "arXiv non-exclusive license to distribute", die keine Weiterverbreitung erlaubt. Stellen Sie sicher, dass Sie die Urheberrechte respektieren.

3. **DOIs und NFTs**: Nur Publikationen mit DOIs können als NFTs tokenisiert werden. Nicht alle arXiv-Publikationen haben DOIs.

4. **Metadaten**: Die arXiv API liefert nur Metadaten, keinen Volltext. Der Volltext kann über die PDF-URL abgerufen werden, aber dies ist nicht Teil der API-Integration.

5. **Kategorien**: Bei der Suche nach Kategorien können sowohl Hauptkategorien (z.B. "physics") als auch Unterkategorien (z.B. "cs.AI") verwendet werden.

## Beispiele

### Curl-Beispiel: Suche nach Publikationen

```bash
curl -X GET "http://localhost:3000/api/arxiv/search?query=quantum+computing&category=quant-ph&maxResults=5"
```

### Curl-Beispiel: Publikation importieren

```bash
curl -X POST "http://localhost:3000/api/arxiv/import/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..."
```

### Curl-Beispiel: NFT erstellen

```bash
curl -X POST "http://localhost:3000/api/arxiv/nft/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..." \
  -H "Content-Type: application/json" \
  -d '{
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }'
```# arXiv-Integration für DeSci-Scholar

Diese Dokumentation beschreibt die Integration von arXiv in DeSci-Scholar, die es ermöglicht, wissenschaftliche Publikationen von arXiv zu suchen, zu importieren und als NFTs zu tokenisieren.

## Überblick

Die arXiv-Integration bietet folgende Funktionen:

- Suche nach wissenschaftlichen Publikationen in arXiv
- Abruf von Publikationsdetails anhand der arXiv-ID
- Import von arXiv-Publikationen in DeSci-Scholar
- Erstellung von NFTs für arXiv-Publikationen mit DOIs
- Abruf der neuesten Publikationen in bestimmten Kategorien
- Statistiken zur arXiv-Integration

## API-Endpunkte

### Suche nach Publikationen

**Anfrage**
```
GET /api/arxiv/search
```

**Query-Parameter**
- `query` - Allgemeiner Suchbegriff
- `title` - Suche im Titel
- `author` - Suche nach Autor
- `category` - Suche nach Kategorie (z.B. "physics", "cs.AI")
- `id` - Suche nach arXiv-ID
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 10)
- `start` - Startposition für Paginierung (Standard: 0)
- `sortBy` - Sortierkriterium ("relevance", "lastUpdatedDate", "submittedDate")
- `sortOrder` - Sortierreihenfolge ("ascending", "descending")

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalResults": 42,
  "startIndex": 0,
  "itemsPerPage": 10,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikationsdetails abrufen

**Anfrage**
```
GET /api/arxiv/paper/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Antwort (200 OK)**
```json
{
  "success": true,
  "paper": {
    "id": "2304.12345",
    "title": "Anwendung von KI in der Medizin",
    "abstract": "Diese Studie untersucht...",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "categories": ["cs.AI", "cs.LG"],
    "primaryCategory": "cs.AI",
    "published": "2023-04-15T14:22:10Z",
    "updated": "2023-04-16T09:15:30Z",
    "doi": "10.12345/example.doi",
    "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
    "comment": "20 pages, 8 figures",
    "pdfUrl": "https://arxiv.org/pdf/2304.12345",
    "arxivUrl": "https://arxiv.org/abs/2304.12345"
  }
}
```

### Neueste Publikationen in einer Kategorie abrufen

**Anfrage**
```
GET /api/arxiv/recent/:category
```

**Parameter**
- `category` - arXiv-Kategorie (z.B. "cs.AI", "physics")

**Query-Parameter**
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 30)

**Antwort (200 OK)**
```json
{
  "success": true,
  "category": "cs.AI",
  "categoryName": "Künstliche Intelligenz",
  "totalResults": 42,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikation importieren

**Anfrage**
```
POST /api/arxiv/import/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "keywords": ["Künstliche Intelligenz", "Machine Learning"],
    "publishedDate": "2023-04-15T14:22:10Z",
    "doi": "10.12345/example.doi",
    "license": "arXiv non-exclusive license to distribute",
    "status": "published",
    "arxivMetadata": {
      "id": "2304.12345",
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "updated": "2023-04-16T09:15:30Z",
      "comment": "20 pages, 8 figures",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "url": "https://arxiv.org/abs/2304.12345",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345"
    },
    "storage": {
      "ipfs": "QmXa12S5sdf87gh4j56k7l...",
      "bittorrent": "a1b2c3d4e5f6...",
      "timestamp": "2023-05-10T08:12:45Z"
    }
  },
  "message": "Publikation erfolgreich importiert"
}
```

### Mehrere Publikationen importieren

**Anfrage**
```
POST /api/arxiv/import
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 42,
  "imported": 5,
  "failed": 0,
  "publications": [
    {
      "id": "pub-789012",
      "title": "Quantum Computing Applications",
      "authors": ["Dr. John Doe", "Prof. Jane Smith"],
      "abstract": "This study explores...",
      "keywords": ["Quantum Computing", "Quantum Physics"],
      "publishedDate": "2023-04-15T14:22:10Z",
      "doi": "10.12345/example.doi",
      "license": "arXiv non-exclusive license to distribute",
      "status": "published",
      "arxivMetadata": {
        "id": "2304.12345",
        "categories": ["quant-ph", "cs.ET"],
        "primaryCategory": "quant-ph",
        "updated": "2023-04-16T09:15:30Z",
        "comment": "15 pages, 5 figures",
        "journalRef": "Journal of Quantum Computing, Vol. 10, pp. 45-60",
        "url": "https://arxiv.org/abs/2304.12345",
        "pdfUrl": "https://arxiv.org/pdf/2304.12345"
      }
    },
    // Weitere Publikationen...
  ]
}
```

### NFT für eine Publikation erstellen

**Anfrage**
```
POST /api/arxiv/nft/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Body**
```json
{
  "recipient": "0x1234567890abcdef1234567890abcdef12345678",
  "royaltyPercentage": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "doi": "10.12345/example.doi"
  },
  "nft": {
    "tokenId": "42",
    "metadata": {
      "name": "Anwendung von KI in der Medizin",
      "description": "Diese Studie untersucht...",
      "external_url": "https://arxiv.org/abs/2304.12345",
      "attributes": [
        { "trait_type": "Type", "value": "Scientific Paper" },
        { "trait_type": "Source", "value": "arXiv" },
        { "trait_type": "arXiv ID", "value": "2304.12345" },
        { "trait_type": "DOI", "value": "10.12345/example.doi" },
        { "trait_type": "Category", "value": "cs.AI" },
        { "trait_type": "Authors", "value": "Dr. Maria Schmidt, Prof. Hans Müller" },
        { "trait_type": "Publication Date", "value": "2023-04-15" }
      ]
    }
  },
  "tokenId": "42",
  "transactionHash": "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
  "message": "NFT erfolgreich erstellt"
}
```

### NFTs für mehrere Publikationen erstellen

**Anfrage**
```
POST /api/arxiv/nft
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5,
  "nftOptions": {
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 5,
  "withDOI": 3,
  "createdNFTs": 3,
  "failed": 0,
  "nfts": [
    {
      "publication": {
        "id": "pub-789012",
        "title": "Quantum Computing Applications",
        "authors": ["Dr. John Doe", "Prof. Jane Smith"],
        "abstract": "This study explores...",
        "doi": "10.12345/example.doi"
      },
      "nft": {
        "tokenId": "42",
        "metadata": {
          "name": "Quantum Computing Applications",
          "description": "This study explores...",
          "external_url": "https://arxiv.org/abs/2304.12345",
          "attributes": [
            { "trait_type": "Type", "value": "Scientific Paper" },
            { "trait_type": "Source", "value": "arXiv" },
            { "trait_type": "arXiv ID", "value": "2304.12345" },
            { "trait_type": "DOI", "value": "10.12345/example.doi" },
            { "trait_type": "Category", "value": "quant-ph" },
            { "trait_type": "Authors", "value": "Dr. John Doe, Prof. Jane Smith" },
            { "trait_type": "Publication Date", "value": "2023-04-15" }
          ]
        }
      },
      "tokenId": "42"
    },
    // Weitere NFTs...
  ]
}
```

### Statistiken abrufen

**Anfrage**
```
GET /api/arxiv/stats
```

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "stats": {
    "importedPapers": 42,
    "createdNFTs": 15,
    "failedImports": 3,
    "papersByCategory": {
      "cs.AI": 12,
      "quant-ph": 8,
      "physics": 22
    },
    "apiStats": {
      "totalRequests": 150,
      "successfulRequests": 147,
      "failedRequests": 3,
      "retries": 5,
      "requestsByEndpoint": {
        "query": 150
      },
      "successRate": "98.00%"
    },
    "successRate": "92.86%"
  }
}
```

## arXiv-Kategorien

Die arXiv-Integration unterstützt alle arXiv-Kategorien, darunter:

- **Physik**: astro-ph, cond-mat, gr-qc, hep-ex, hep-lat, hep-ph, hep-th, math-ph, nlin, nucl-ex, nucl-th, physics, quant-ph
- **Mathematik**: math
- **Informatik**: cs.AI, cs.AR, cs.CC, cs.CE, cs.CG, cs.CL, cs.CR, cs.CV, cs.CY, cs.DB, cs.DC, cs.DL, cs.DM, cs.DS, cs.ET, cs.FL, cs.GL, cs.GR, cs.GT, cs.HC, cs.IR, cs.IT, cs.LG, cs.LO, cs.MA, cs.MM, cs.MS, cs.NA, cs.NE, cs.NI, cs.OS, cs.PF, cs.PL, cs.RO, cs.SC, cs.SD, cs.SE, cs.SI, cs.SY
- **Quantitative Biologie**: q-bio
- **Quantitative Finanzen**: q-fin
- **Statistik**: stat
- **Elektrotechnik und Systemwissenschaft**: eess
- **Wirtschaftswissenschaften**: econ

Eine vollständige Liste der Kategorien und Unterkategorien finden Sie auf der [arXiv-Taxonomie-Seite](https://arxiv.org/category_taxonomy).

## Nutzungshinweise

Bei der Verwendung der arXiv-Integration sind folgende Punkte zu beachten:

1. **API-Limits**: Die arXiv API hat Ratenbegrenzungen. Es wird empfohlen, nicht mehr als eine Anfrage alle 3 Sekunden zu stellen.

2. **Urheberrecht**: Die meisten arXiv-Publikationen stehen unter der "arXiv non-exclusive license to distribute", die keine Weiterverbreitung erlaubt. Stellen Sie sicher, dass Sie die Urheberrechte respektieren.

3. **DOIs und NFTs**: Nur Publikationen mit DOIs können als NFTs tokenisiert werden. Nicht alle arXiv-Publikationen haben DOIs.

4. **Metadaten**: Die arXiv API liefert nur Metadaten, keinen Volltext. Der Volltext kann über die PDF-URL abgerufen werden, aber dies ist nicht Teil der API-Integration.

5. **Kategorien**: Bei der Suche nach Kategorien können sowohl Hauptkategorien (z.B. "physics") als auch Unterkategorien (z.B. "cs.AI") verwendet werden.

## Beispiele

### Curl-Beispiel: Suche nach Publikationen

```bash
curl -X GET "http://localhost:3000/api/arxiv/search?query=quantum+computing&category=quant-ph&maxResults=5"
```

### Curl-Beispiel: Publikation importieren

```bash
curl -X POST "http://localhost:3000/api/arxiv/import/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..."
```

### Curl-Beispiel: NFT erstellen

```bash
curl -X POST "http://localhost:3000/api/arxiv/nft/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..." \
  -H "Content-Type: application/json" \
  -d '{
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }'
```# arXiv-Integration für DeSci-Scholar

Diese Dokumentation beschreibt die Integration von arXiv in DeSci-Scholar, die es ermöglicht, wissenschaftliche Publikationen von arXiv zu suchen, zu importieren und als NFTs zu tokenisieren.

## Überblick

Die arXiv-Integration bietet folgende Funktionen:

- Suche nach wissenschaftlichen Publikationen in arXiv
- Abruf von Publikationsdetails anhand der arXiv-ID
- Import von arXiv-Publikationen in DeSci-Scholar
- Erstellung von NFTs für arXiv-Publikationen mit DOIs
- Abruf der neuesten Publikationen in bestimmten Kategorien
- Statistiken zur arXiv-Integration

## API-Endpunkte

### Suche nach Publikationen

**Anfrage**
```
GET /api/arxiv/search
```

**Query-Parameter**
- `query` - Allgemeiner Suchbegriff
- `title` - Suche im Titel
- `author` - Suche nach Autor
- `category` - Suche nach Kategorie (z.B. "physics", "cs.AI")
- `id` - Suche nach arXiv-ID
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 10)
- `start` - Startposition für Paginierung (Standard: 0)
- `sortBy` - Sortierkriterium ("relevance", "lastUpdatedDate", "submittedDate")
- `sortOrder` - Sortierreihenfolge ("ascending", "descending")

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalResults": 42,
  "startIndex": 0,
  "itemsPerPage": 10,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikationsdetails abrufen

**Anfrage**
```
GET /api/arxiv/paper/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Antwort (200 OK)**
```json
{
  "success": true,
  "paper": {
    "id": "2304.12345",
    "title": "Anwendung von KI in der Medizin",
    "abstract": "Diese Studie untersucht...",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "categories": ["cs.AI", "cs.LG"],
    "primaryCategory": "cs.AI",
    "published": "2023-04-15T14:22:10Z",
    "updated": "2023-04-16T09:15:30Z",
    "doi": "10.12345/example.doi",
    "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
    "comment": "20 pages, 8 figures",
    "pdfUrl": "https://arxiv.org/pdf/2304.12345",
    "arxivUrl": "https://arxiv.org/abs/2304.12345"
  }
}
```

### Neueste Publikationen in einer Kategorie abrufen

**Anfrage**
```
GET /api/arxiv/recent/:category
```

**Parameter**
- `category` - arXiv-Kategorie (z.B. "cs.AI", "physics")

**Query-Parameter**
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 30)

**Antwort (200 OK)**
```json
{
  "success": true,
  "category": "cs.AI",
  "categoryName": "Künstliche Intelligenz",
  "totalResults": 42,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikation importieren

**Anfrage**
```
POST /api/arxiv/import/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "keywords": ["Künstliche Intelligenz", "Machine Learning"],
    "publishedDate": "2023-04-15T14:22:10Z",
    "doi": "10.12345/example.doi",
    "license": "arXiv non-exclusive license to distribute",
    "status": "published",
    "arxivMetadata": {
      "id": "2304.12345",
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "updated": "2023-04-16T09:15:30Z",
      "comment": "20 pages, 8 figures",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "url": "https://arxiv.org/abs/2304.12345",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345"
    },
    "storage": {
      "ipfs": "QmXa12S5sdf87gh4j56k7l...",
      "bittorrent": "a1b2c3d4e5f6...",
      "timestamp": "2023-05-10T08:12:45Z"
    }
  },
  "message": "Publikation erfolgreich importiert"
}
```

### Mehrere Publikationen importieren

**Anfrage**
```
POST /api/arxiv/import
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 42,
  "imported": 5,
  "failed": 0,
  "publications": [
    {
      "id": "pub-789012",
      "title": "Quantum Computing Applications",
      "authors": ["Dr. John Doe", "Prof. Jane Smith"],
      "abstract": "This study explores...",
      "keywords": ["Quantum Computing", "Quantum Physics"],
      "publishedDate": "2023-04-15T14:22:10Z",
      "doi": "10.12345/example.doi",
      "license": "arXiv non-exclusive license to distribute",
      "status": "published",
      "arxivMetadata": {
        "id": "2304.12345",
        "categories": ["quant-ph", "cs.ET"],
        "primaryCategory": "quant-ph",
        "updated": "2023-04-16T09:15:30Z",
        "comment": "15 pages, 5 figures",
        "journalRef": "Journal of Quantum Computing, Vol. 10, pp. 45-60",
        "url": "https://arxiv.org/abs/2304.12345",
        "pdfUrl": "https://arxiv.org/pdf/2304.12345"
      }
    },
    // Weitere Publikationen...
  ]
}
```

### NFT für eine Publikation erstellen

**Anfrage**
```
POST /api/arxiv/nft/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Body**
```json
{
  "recipient": "0x1234567890abcdef1234567890abcdef12345678",
  "royaltyPercentage": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "doi": "10.12345/example.doi"
  },
  "nft": {
    "tokenId": "42",
    "metadata": {
      "name": "Anwendung von KI in der Medizin",
      "description": "Diese Studie untersucht...",
      "external_url": "https://arxiv.org/abs/2304.12345",
      "attributes": [
        { "trait_type": "Type", "value": "Scientific Paper" },
        { "trait_type": "Source", "value": "arXiv" },
        { "trait_type": "arXiv ID", "value": "2304.12345" },
        { "trait_type": "DOI", "value": "10.12345/example.doi" },
        { "trait_type": "Category", "value": "cs.AI" },
        { "trait_type": "Authors", "value": "Dr. Maria Schmidt, Prof. Hans Müller" },
        { "trait_type": "Publication Date", "value": "2023-04-15" }
      ]
    }
  },
  "tokenId": "42",
  "transactionHash": "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
  "message": "NFT erfolgreich erstellt"
}
```

### NFTs für mehrere Publikationen erstellen

**Anfrage**
```
POST /api/arxiv/nft
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5,
  "nftOptions": {
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 5,
  "withDOI": 3,
  "createdNFTs": 3,
  "failed": 0,
  "nfts": [
    {
      "publication": {
        "id": "pub-789012",
        "title": "Quantum Computing Applications",
        "authors": ["Dr. John Doe", "Prof. Jane Smith"],
        "abstract": "This study explores...",
        "doi": "10.12345/example.doi"
      },
      "nft": {
        "tokenId": "42",
        "metadata": {
          "name": "Quantum Computing Applications",
          "description": "This study explores...",
          "external_url": "https://arxiv.org/abs/2304.12345",
          "attributes": [
            { "trait_type": "Type", "value": "Scientific Paper" },
            { "trait_type": "Source", "value": "arXiv" },
            { "trait_type": "arXiv ID", "value": "2304.12345" },
            { "trait_type": "DOI", "value": "10.12345/example.doi" },
            { "trait_type": "Category", "value": "quant-ph" },
            { "trait_type": "Authors", "value": "Dr. John Doe, Prof. Jane Smith" },
            { "trait_type": "Publication Date", "value": "2023-04-15" }
          ]
        }
      },
      "tokenId": "42"
    },
    // Weitere NFTs...
  ]
}
```

### Statistiken abrufen

**Anfrage**
```
GET /api/arxiv/stats
```

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "stats": {
    "importedPapers": 42,
    "createdNFTs": 15,
    "failedImports": 3,
    "papersByCategory": {
      "cs.AI": 12,
      "quant-ph": 8,
      "physics": 22
    },
    "apiStats": {
      "totalRequests": 150,
      "successfulRequests": 147,
      "failedRequests": 3,
      "retries": 5,
      "requestsByEndpoint": {
        "query": 150
      },
      "successRate": "98.00%"
    },
    "successRate": "92.86%"
  }
}
```

## arXiv-Kategorien

Die arXiv-Integration unterstützt alle arXiv-Kategorien, darunter:

- **Physik**: astro-ph, cond-mat, gr-qc, hep-ex, hep-lat, hep-ph, hep-th, math-ph, nlin, nucl-ex, nucl-th, physics, quant-ph
- **Mathematik**: math
- **Informatik**: cs.AI, cs.AR, cs.CC, cs.CE, cs.CG, cs.CL, cs.CR, cs.CV, cs.CY, cs.DB, cs.DC, cs.DL, cs.DM, cs.DS, cs.ET, cs.FL, cs.GL, cs.GR, cs.GT, cs.HC, cs.IR, cs.IT, cs.LG, cs.LO, cs.MA, cs.MM, cs.MS, cs.NA, cs.NE, cs.NI, cs.OS, cs.PF, cs.PL, cs.RO, cs.SC, cs.SD, cs.SE, cs.SI, cs.SY
- **Quantitative Biologie**: q-bio
- **Quantitative Finanzen**: q-fin
- **Statistik**: stat
- **Elektrotechnik und Systemwissenschaft**: eess
- **Wirtschaftswissenschaften**: econ

Eine vollständige Liste der Kategorien und Unterkategorien finden Sie auf der [arXiv-Taxonomie-Seite](https://arxiv.org/category_taxonomy).

## Nutzungshinweise

Bei der Verwendung der arXiv-Integration sind folgende Punkte zu beachten:

1. **API-Limits**: Die arXiv API hat Ratenbegrenzungen. Es wird empfohlen, nicht mehr als eine Anfrage alle 3 Sekunden zu stellen.

2. **Urheberrecht**: Die meisten arXiv-Publikationen stehen unter der "arXiv non-exclusive license to distribute", die keine Weiterverbreitung erlaubt. Stellen Sie sicher, dass Sie die Urheberrechte respektieren.

3. **DOIs und NFTs**: Nur Publikationen mit DOIs können als NFTs tokenisiert werden. Nicht alle arXiv-Publikationen haben DOIs.

4. **Metadaten**: Die arXiv API liefert nur Metadaten, keinen Volltext. Der Volltext kann über die PDF-URL abgerufen werden, aber dies ist nicht Teil der API-Integration.

5. **Kategorien**: Bei der Suche nach Kategorien können sowohl Hauptkategorien (z.B. "physics") als auch Unterkategorien (z.B. "cs.AI") verwendet werden.

## Beispiele

### Curl-Beispiel: Suche nach Publikationen

```bash
curl -X GET "http://localhost:3000/api/arxiv/search?query=quantum+computing&category=quant-ph&maxResults=5"
```

### Curl-Beispiel: Publikation importieren

```bash
curl -X POST "http://localhost:3000/api/arxiv/import/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..."
```

### Curl-Beispiel: NFT erstellen

```bash
curl -X POST "http://localhost:3000/api/arxiv/nft/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..." \
  -H "Content-Type: application/json" \
  -d '{
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }'
```
## Überblick

Die arXiv-Integration bietet folgende Funktionen:

- Suche nach wissenschaftlichen Publikationen in arXiv
- Abruf von Publikationsdetails anhand der arXiv-ID
- Import von arXiv-Publikationen in DeSci-Scholar
- Erstellung von NFTs für arXiv-Publikationen mit DOIs
- Abruf der neuesten Publikationen in bestimmten Kategorien
- Statistiken zur arXiv-Integration

## API-Endpunkte

### Suche nach Publikationen

**Anfrage**
```
GET /api/arxiv/search
```

**Query-Parameter**
- `query` - Allgemeiner Suchbegriff
- `title` - Suche im Titel
- `author` - Suche nach Autor
- `category` - Suche nach Kategorie (z.B. "physics", "cs.AI")
- `id` - Suche nach arXiv-ID
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 10)
- `start` - Startposition für Paginierung (Standard: 0)
- `sortBy` - Sortierkriterium ("relevance", "lastUpdatedDate", "submittedDate")
- `sortOrder` - Sortierreihenfolge ("ascending", "descending")

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalResults": 42,
  "startIndex": 0,
  "itemsPerPage": 10,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikationsdetails abrufen

**Anfrage**
```
GET /api/arxiv/paper/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Antwort (200 OK)**
```json
{
  "success": true,
  "paper": {
    "id": "2304.12345",
    "title": "Anwendung von KI in der Medizin",
    "abstract": "Diese Studie untersucht...",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "categories": ["cs.AI", "cs.LG"],
    "primaryCategory": "cs.AI",
    "published": "2023-04-15T14:22:10Z",
    "updated": "2023-04-16T09:15:30Z",
    "doi": "10.12345/example.doi",
    "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
    "comment": "20 pages, 8 figures",
    "pdfUrl": "https://arxiv.org/pdf/2304.12345",
    "arxivUrl": "https://arxiv.org/abs/2304.12345"
  }
}
```

### Neueste Publikationen in einer Kategorie abrufen

**Anfrage**
```
GET /api/arxiv/recent/:category
```

**Parameter**
- `category` - arXiv-Kategorie (z.B. "cs.AI", "physics")

**Query-Parameter**
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 30)

**Antwort (200 OK)**
```json
{
  "success": true,
  "category": "cs.AI",
  "categoryName": "Künstliche Intelligenz",
  "totalResults": 42,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikation importieren

**Anfrage**
```
POST /api/arxiv/import/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "keywords": ["Künstliche Intelligenz", "Machine Learning"],
    "publishedDate": "2023-04-15T14:22:10Z",
    "doi": "10.12345/example.doi",
    "license": "arXiv non-exclusive license to distribute",
    "status": "published",
    "arxivMetadata": {
      "id": "2304.12345",
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "updated": "2023-04-16T09:15:30Z",
      "comment": "20 pages, 8 figures",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "url": "https://arxiv.org/abs/2304.12345",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345"
    },
    "storage": {
      "ipfs": "QmXa12S5sdf87gh4j56k7l...",
      "bittorrent": "a1b2c3d4e5f6...",
      "timestamp": "2023-05-10T08:12:45Z"
    }
  },
  "message": "Publikation erfolgreich importiert"
}
```

### Mehrere Publikationen importieren

**Anfrage**
```
POST /api/arxiv/import
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 42,
  "imported": 5,
  "failed": 0,
  "publications": [
    {
      "id": "pub-789012",
      "title": "Quantum Computing Applications",
      "authors": ["Dr. John Doe", "Prof. Jane Smith"],
      "abstract": "This study explores...",
      "keywords": ["Quantum Computing", "Quantum Physics"],
      "publishedDate": "2023-04-15T14:22:10Z",
      "doi": "10.12345/example.doi",
      "license": "arXiv non-exclusive license to distribute",
      "status": "published",
      "arxivMetadata": {
        "id": "2304.12345",
        "categories": ["quant-ph", "cs.ET"],
        "primaryCategory": "quant-ph",
        "updated": "2023-04-16T09:15:30Z",
        "comment": "15 pages, 5 figures",
        "journalRef": "Journal of Quantum Computing, Vol. 10, pp. 45-60",
        "url": "https://arxiv.org/abs/2304.12345",
        "pdfUrl": "https://arxiv.org/pdf/2304.12345"
      }
    },
    // Weitere Publikationen...
  ]
}
```

### NFT für eine Publikation erstellen

**Anfrage**
```
POST /api/arxiv/nft/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Body**
```json
{
  "recipient": "0x1234567890abcdef1234567890abcdef12345678",
  "royaltyPercentage": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "doi": "10.12345/example.doi"
  },
  "nft": {
    "tokenId": "42",
    "metadata": {
      "name": "Anwendung von KI in der Medizin",
      "description": "Diese Studie untersucht...",
      "external_url": "https://arxiv.org/abs/2304.12345",
      "attributes": [
        { "trait_type": "Type", "value": "Scientific Paper" },
        { "trait_type": "Source", "value": "arXiv" },
        { "trait_type": "arXiv ID", "value": "2304.12345" },
        { "trait_type": "DOI", "value": "10.12345/example.doi" },
        { "trait_type": "Category", "value": "cs.AI" },
        { "trait_type": "Authors", "value": "Dr. Maria Schmidt, Prof. Hans Müller" },
        { "trait_type": "Publication Date", "value": "2023-04-15" }
      ]
    }
  },
  "tokenId": "42",
  "transactionHash": "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
  "message": "NFT erfolgreich erstellt"
}
```

### NFTs für mehrere Publikationen erstellen

**Anfrage**
```
POST /api/arxiv/nft
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5,
  "nftOptions": {
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 5,
  "withDOI": 3,
  "createdNFTs": 3,
  "failed": 0,
  "nfts": [
    {
      "publication": {
        "id": "pub-789012",
        "title": "Quantum Computing Applications",
        "authors": ["Dr. John Doe", "Prof. Jane Smith"],
        "abstract": "This study explores...",
        "doi": "10.12345/example.doi"
      },
      "nft": {
        "tokenId": "42",
        "metadata": {
          "name": "Quantum Computing Applications",
          "description": "This study explores...",
          "external_url": "https://arxiv.org/abs/2304.12345",
          "attributes": [
            { "trait_type": "Type", "value": "Scientific Paper" },
            { "trait_type": "Source", "value": "arXiv" },
            { "trait_type": "arXiv ID", "value": "2304.12345" },
            { "trait_type": "DOI", "value": "10.12345/example.doi" },
            { "trait_type": "Category", "value": "quant-ph" },
            { "trait_type": "Authors", "value": "Dr. John Doe, Prof. Jane Smith" },
            { "trait_type": "Publication Date", "value": "2023-04-15" }
          ]
        }
      },
      "tokenId": "42"
    },
    // Weitere NFTs...
  ]
}
```

### Statistiken abrufen

**Anfrage**
```
GET /api/arxiv/stats
```

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "stats": {
    "importedPapers": 42,
    "createdNFTs": 15,
    "failedImports": 3,
    "papersByCategory": {
      "cs.AI": 12,
      "quant-ph": 8,
      "physics": 22
    },
    "apiStats": {
      "totalRequests": 150,
      "successfulRequests": 147,
      "failedRequests": 3,
      "retries": 5,
      "requestsByEndpoint": {
        "query": 150
      },
      "successRate": "98.00%"
    },
    "successRate": "92.86%"
  }
}
```

## arXiv-Kategorien

Die arXiv-Integration unterstützt alle arXiv-Kategorien, darunter:

- **Physik**: astro-ph, cond-mat, gr-qc, hep-ex, hep-lat, hep-ph, hep-th, math-ph, nlin, nucl-ex, nucl-th, physics, quant-ph
- **Mathematik**: math
- **Informatik**: cs.AI, cs.AR, cs.CC, cs.CE, cs.CG, cs.CL, cs.CR, cs.CV, cs.CY, cs.DB, cs.DC, cs.DL, cs.DM, cs.DS, cs.ET, cs.FL, cs.GL, cs.GR, cs.GT, cs.HC, cs.IR, cs.IT, cs.LG, cs.LO, cs.MA, cs.MM, cs.MS, cs.NA, cs.NE, cs.NI, cs.OS, cs.PF, cs.PL, cs.RO, cs.SC, cs.SD, cs.SE, cs.SI, cs.SY
- **Quantitative Biologie**: q-bio
- **Quantitative Finanzen**: q-fin
- **Statistik**: stat
- **Elektrotechnik und Systemwissenschaft**: eess
- **Wirtschaftswissenschaften**: econ

Eine vollständige Liste der Kategorien und Unterkategorien finden Sie auf der [arXiv-Taxonomie-Seite](https://arxiv.org/category_taxonomy).

## Nutzungshinweise

Bei der Verwendung der arXiv-Integration sind folgende Punkte zu beachten:

1. **API-Limits**: Die arXiv API hat Ratenbegrenzungen. Es wird empfohlen, nicht mehr als eine Anfrage alle 3 Sekunden zu stellen.

2. **Urheberrecht**: Die meisten arXiv-Publikationen stehen unter der "arXiv non-exclusive license to distribute", die keine Weiterverbreitung erlaubt. Stellen Sie sicher, dass Sie die Urheberrechte respektieren.

3. **DOIs und NFTs**: Nur Publikationen mit DOIs können als NFTs tokenisiert werden. Nicht alle arXiv-Publikationen haben DOIs.

4. **Metadaten**: Die arXiv API liefert nur Metadaten, keinen Volltext. Der Volltext kann über die PDF-URL abgerufen werden, aber dies ist nicht Teil der API-Integration.

5. **Kategorien**: Bei der Suche nach Kategorien können sowohl Hauptkategorien (z.B. "physics") als auch Unterkategorien (z.B. "cs.AI") verwendet werden.

## Beispiele

### Curl-Beispiel: Suche nach Publikationen

```bash
curl -X GET "http://localhost:3000/api/arxiv/search?query=quantum+computing&category=quant-ph&maxResults=5"
```

### Curl-Beispiel: Publikation importieren

```bash
curl -X POST "http://localhost:3000/api/arxiv/import/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..."
```

### Curl-Beispiel: NFT erstellen

```bash
curl -X POST "http://localhost:3000/api/arxiv/nft/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..." \
  -H "Content-Type: application/json" \
  -d '{
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }'
```
## Überblick

Die arXiv-Integration bietet folgende Funktionen:

- Suche nach wissenschaftlichen Publikationen in arXiv
- Abruf von Publikationsdetails anhand der arXiv-ID
- Import von arXiv-Publikationen in DeSci-Scholar
- Erstellung von NFTs für arXiv-Publikationen mit DOIs
- Abruf der neuesten Publikationen in bestimmten Kategorien
- Statistiken zur arXiv-Integration

## API-Endpunkte

### Suche nach Publikationen

**Anfrage**
```
GET /api/arxiv/search
```

**Query-Parameter**
- `query` - Allgemeiner Suchbegriff
- `title` - Suche im Titel
- `author` - Suche nach Autor
- `category` - Suche nach Kategorie (z.B. "physics", "cs.AI")
- `id` - Suche nach arXiv-ID
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 10)
- `start` - Startposition für Paginierung (Standard: 0)
- `sortBy` - Sortierkriterium ("relevance", "lastUpdatedDate", "submittedDate")
- `sortOrder` - Sortierreihenfolge ("ascending", "descending")

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalResults": 42,
  "startIndex": 0,
  "itemsPerPage": 10,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikationsdetails abrufen

**Anfrage**
```
GET /api/arxiv/paper/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Antwort (200 OK)**
```json
{
  "success": true,
  "paper": {
    "id": "2304.12345",
    "title": "Anwendung von KI in der Medizin",
    "abstract": "Diese Studie untersucht...",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "categories": ["cs.AI", "cs.LG"],
    "primaryCategory": "cs.AI",
    "published": "2023-04-15T14:22:10Z",
    "updated": "2023-04-16T09:15:30Z",
    "doi": "10.12345/example.doi",
    "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
    "comment": "20 pages, 8 figures",
    "pdfUrl": "https://arxiv.org/pdf/2304.12345",
    "arxivUrl": "https://arxiv.org/abs/2304.12345"
  }
}
```

### Neueste Publikationen in einer Kategorie abrufen

**Anfrage**
```
GET /api/arxiv/recent/:category
```

**Parameter**
- `category` - arXiv-Kategorie (z.B. "cs.AI", "physics")

**Query-Parameter**
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 30)

**Antwort (200 OK)**
```json
{
  "success": true,
  "category": "cs.AI",
  "categoryName": "Künstliche Intelligenz",
  "totalResults": 42,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikation importieren

**Anfrage**
```
POST /api/arxiv/import/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "keywords": ["Künstliche Intelligenz", "Machine Learning"],
    "publishedDate": "2023-04-15T14:22:10Z",
    "doi": "10.12345/example.doi",
    "license": "arXiv non-exclusive license to distribute",
    "status": "published",
    "arxivMetadata": {
      "id": "2304.12345",
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "updated": "2023-04-16T09:15:30Z",
      "comment": "20 pages, 8 figures",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "url": "https://arxiv.org/abs/2304.12345",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345"
    },
    "storage": {
      "ipfs": "QmXa12S5sdf87gh4j56k7l...",
      "bittorrent": "a1b2c3d4e5f6...",
      "timestamp": "2023-05-10T08:12:45Z"
    }
  },
  "message": "Publikation erfolgreich importiert"
}
```

### Mehrere Publikationen importieren

**Anfrage**
```
POST /api/arxiv/import
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 42,
  "imported": 5,
  "failed": 0,
  "publications": [
    {
      "id": "pub-789012",
      "title": "Quantum Computing Applications",
      "authors": ["Dr. John Doe", "Prof. Jane Smith"],
      "abstract": "This study explores...",
      "keywords": ["Quantum Computing", "Quantum Physics"],
      "publishedDate": "2023-04-15T14:22:10Z",
      "doi": "10.12345/example.doi",
      "license": "arXiv non-exclusive license to distribute",
      "status": "published",
      "arxivMetadata": {
        "id": "2304.12345",
        "categories": ["quant-ph", "cs.ET"],
        "primaryCategory": "quant-ph",
        "updated": "2023-04-16T09:15:30Z",
        "comment": "15 pages, 5 figures",
        "journalRef": "Journal of Quantum Computing, Vol. 10, pp. 45-60",
        "url": "https://arxiv.org/abs/2304.12345",
        "pdfUrl": "https://arxiv.org/pdf/2304.12345"
      }
    },
    // Weitere Publikationen...
  ]
}
```

### NFT für eine Publikation erstellen

**Anfrage**
```
POST /api/arxiv/nft/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Body**
```json
{
  "recipient": "0x1234567890abcdef1234567890abcdef12345678",
  "royaltyPercentage": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "doi": "10.12345/example.doi"
  },
  "nft": {
    "tokenId": "42",
    "metadata": {
      "name": "Anwendung von KI in der Medizin",
      "description": "Diese Studie untersucht...",
      "external_url": "https://arxiv.org/abs/2304.12345",
      "attributes": [
        { "trait_type": "Type", "value": "Scientific Paper" },
        { "trait_type": "Source", "value": "arXiv" },
        { "trait_type": "arXiv ID", "value": "2304.12345" },
        { "trait_type": "DOI", "value": "10.12345/example.doi" },
        { "trait_type": "Category", "value": "cs.AI" },
        { "trait_type": "Authors", "value": "Dr. Maria Schmidt, Prof. Hans Müller" },
        { "trait_type": "Publication Date", "value": "2023-04-15" }
      ]
    }
  },
  "tokenId": "42",
  "transactionHash": "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
  "message": "NFT erfolgreich erstellt"
}
```

### NFTs für mehrere Publikationen erstellen

**Anfrage**
```
POST /api/arxiv/nft
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5,
  "nftOptions": {
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 5,
  "withDOI": 3,
  "createdNFTs": 3,
  "failed": 0,
  "nfts": [
    {
      "publication": {
        "id": "pub-789012",
        "title": "Quantum Computing Applications",
        "authors": ["Dr. John Doe", "Prof. Jane Smith"],
        "abstract": "This study explores...",
        "doi": "10.12345/example.doi"
      },
      "nft": {
        "tokenId": "42",
        "metadata": {
          "name": "Quantum Computing Applications",
          "description": "This study explores...",
          "external_url": "https://arxiv.org/abs/2304.12345",
          "attributes": [
            { "trait_type": "Type", "value": "Scientific Paper" },
            { "trait_type": "Source", "value": "arXiv" },
            { "trait_type": "arXiv ID", "value": "2304.12345" },
            { "trait_type": "DOI", "value": "10.12345/example.doi" },
            { "trait_type": "Category", "value": "quant-ph" },
            { "trait_type": "Authors", "value": "Dr. John Doe, Prof. Jane Smith" },
            { "trait_type": "Publication Date", "value": "2023-04-15" }
          ]
        }
      },
      "tokenId": "42"
    },
    // Weitere NFTs...
  ]
}
```

### Statistiken abrufen

**Anfrage**
```
GET /api/arxiv/stats
```

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "stats": {
    "importedPapers": 42,
    "createdNFTs": 15,
    "failedImports": 3,
    "papersByCategory": {
      "cs.AI": 12,
      "quant-ph": 8,
      "physics": 22
    },
    "apiStats": {
      "totalRequests": 150,
      "successfulRequests": 147,
      "failedRequests": 3,
      "retries": 5,
      "requestsByEndpoint": {
        "query": 150
      },
      "successRate": "98.00%"
    },
    "successRate": "92.86%"
  }
}
```

## arXiv-Kategorien

Die arXiv-Integration unterstützt alle arXiv-Kategorien, darunter:

- **Physik**: astro-ph, cond-mat, gr-qc, hep-ex, hep-lat, hep-ph, hep-th, math-ph, nlin, nucl-ex, nucl-th, physics, quant-ph
- **Mathematik**: math
- **Informatik**: cs.AI, cs.AR, cs.CC, cs.CE, cs.CG, cs.CL, cs.CR, cs.CV, cs.CY, cs.DB, cs.DC, cs.DL, cs.DM, cs.DS, cs.ET, cs.FL, cs.GL, cs.GR, cs.GT, cs.HC, cs.IR, cs.IT, cs.LG, cs.LO, cs.MA, cs.MM, cs.MS, cs.NA, cs.NE, cs.NI, cs.OS, cs.PF, cs.PL, cs.RO, cs.SC, cs.SD, cs.SE, cs.SI, cs.SY
- **Quantitative Biologie**: q-bio
- **Quantitative Finanzen**: q-fin
- **Statistik**: stat
- **Elektrotechnik und Systemwissenschaft**: eess
- **Wirtschaftswissenschaften**: econ

Eine vollständige Liste der Kategorien und Unterkategorien finden Sie auf der [arXiv-Taxonomie-Seite](https://arxiv.org/category_taxonomy).

## Nutzungshinweise

Bei der Verwendung der arXiv-Integration sind folgende Punkte zu beachten:

1. **API-Limits**: Die arXiv API hat Ratenbegrenzungen. Es wird empfohlen, nicht mehr als eine Anfrage alle 3 Sekunden zu stellen.

2. **Urheberrecht**: Die meisten arXiv-Publikationen stehen unter der "arXiv non-exclusive license to distribute", die keine Weiterverbreitung erlaubt. Stellen Sie sicher, dass Sie die Urheberrechte respektieren.

3. **DOIs und NFTs**: Nur Publikationen mit DOIs können als NFTs tokenisiert werden. Nicht alle arXiv-Publikationen haben DOIs.

4. **Metadaten**: Die arXiv API liefert nur Metadaten, keinen Volltext. Der Volltext kann über die PDF-URL abgerufen werden, aber dies ist nicht Teil der API-Integration.

5. **Kategorien**: Bei der Suche nach Kategorien können sowohl Hauptkategorien (z.B. "physics") als auch Unterkategorien (z.B. "cs.AI") verwendet werden.

## Beispiele

### Curl-Beispiel: Suche nach Publikationen

```bash
curl -X GET "http://localhost:3000/api/arxiv/search?query=quantum+computing&category=quant-ph&maxResults=5"
```

### Curl-Beispiel: Publikation importieren

```bash
curl -X POST "http://localhost:3000/api/arxiv/import/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..."
```

### Curl-Beispiel: NFT erstellen

```bash
curl -X POST "http://localhost:3000/api/arxiv/nft/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..." \
  -H "Content-Type: application/json" \
  -d '{
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }'
```  }'
```
Diese Dokumentation beschreibt die Integration von arXiv in DeSci-Scholar, die es ermöglicht, wissenschaftliche Publikationen von arXiv zu suchen, zu importieren und als NFTs zu tokenisieren.

## Überblick

Die arXiv-Integration bietet folgende Funktionen:

- Suche nach wissenschaftlichen Publikationen in arXiv
- Abruf von Publikationsdetails anhand der arXiv-ID
- Import von arXiv-Publikationen in DeSci-Scholar
- Erstellung von NFTs für arXiv-Publikationen mit DOIs
- Abruf der neuesten Publikationen in bestimmten Kategorien
- Statistiken zur arXiv-Integration

## API-Endpunkte

### Suche nach Publikationen

**Anfrage**
```
GET /api/arxiv/search
```

**Query-Parameter**
- `query` - Allgemeiner Suchbegriff
- `title` - Suche im Titel
- `author` - Suche nach Autor
- `category` - Suche nach Kategorie (z.B. "physics", "cs.AI")
- `id` - Suche nach arXiv-ID
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 10)
- `start` - Startposition für Paginierung (Standard: 0)
- `sortBy` - Sortierkriterium ("relevance", "lastUpdatedDate", "submittedDate")
- `sortOrder` - Sortierreihenfolge ("ascending", "descending")

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalResults": 42,
  "startIndex": 0,
  "itemsPerPage": 10,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikationsdetails abrufen

**Anfrage**
```
GET /api/arxiv/paper/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Antwort (200 OK)**
```json
{
  "success": true,
  "paper": {
    "id": "2304.12345",
    "title": "Anwendung von KI in der Medizin",
    "abstract": "Diese Studie untersucht...",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "categories": ["cs.AI", "cs.LG"],
    "primaryCategory": "cs.AI",
    "published": "2023-04-15T14:22:10Z",
    "updated": "2023-04-16T09:15:30Z",
    "doi": "10.12345/example.doi",
    "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
    "comment": "20 pages, 8 figures",
    "pdfUrl": "https://arxiv.org/pdf/2304.12345",
    "arxivUrl": "https://arxiv.org/abs/2304.12345"
  }
}
```

### Neueste Publikationen in einer Kategorie abrufen

**Anfrage**
```
GET /api/arxiv/recent/:category
```

**Parameter**
- `category` - arXiv-Kategorie (z.B. "cs.AI", "physics")

**Query-Parameter**
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 30)

**Antwort (200 OK)**
```json
{
  "success": true,
  "category": "cs.AI",
  "categoryName": "Künstliche Intelligenz",
  "totalResults": 42,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikation importieren

**Anfrage**
```
POST /api/arxiv/import/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "keywords": ["Künstliche Intelligenz", "Machine Learning"],
    "publishedDate": "2023-04-15T14:22:10Z",
    "doi": "10.12345/example.doi",
    "license": "arXiv non-exclusive license to distribute",
    "status": "published",
    "arxivMetadata": {
      "id": "2304.12345",
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "updated": "2023-04-16T09:15:30Z",
      "comment": "20 pages, 8 figures",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "url": "https://arxiv.org/abs/2304.12345",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345"
    },
    "storage": {
      "ipfs": "QmXa12S5sdf87gh4j56k7l...",
      "bittorrent": "a1b2c3d4e5f6...",
      "timestamp": "2023-05-10T08:12:45Z"
    }
  },
  "message": "Publikation erfolgreich importiert"
}
```

### Mehrere Publikationen importieren

**Anfrage**
```
POST /api/arxiv/import
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 42,
  "imported": 5,
  "failed": 0,
  "publications": [
    {
      "id": "pub-789012",
      "title": "Quantum Computing Applications",
      "authors": ["Dr. John Doe", "Prof. Jane Smith"],
      "abstract": "This study explores...",
      "keywords": ["Quantum Computing", "Quantum Physics"],
      "publishedDate": "2023-04-15T14:22:10Z",
      "doi": "10.12345/example.doi",
      "license": "arXiv non-exclusive license to distribute",
      "status": "published",
      "arxivMetadata": {
        "id": "2304.12345",
        "categories": ["quant-ph", "cs.ET"],
        "primaryCategory": "quant-ph",
        "updated": "2023-04-16T09:15:30Z",
        "comment": "15 pages, 5 figures",
        "journalRef": "Journal of Quantum Computing, Vol. 10, pp. 45-60",
        "url": "https://arxiv.org/abs/2304.12345",
        "pdfUrl": "https://arxiv.org/pdf/2304.12345"
      }
    },
    // Weitere Publikationen...
  ]
}
```

### NFT für eine Publikation erstellen

**Anfrage**
```
POST /api/arxiv/nft/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Body**
```json
{
  "recipient": "0x1234567890abcdef1234567890abcdef12345678",
  "royaltyPercentage": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "doi": "10.12345/example.doi"
  },
  "nft": {
    "tokenId": "42",
    "metadata": {
      "name": "Anwendung von KI in der Medizin",
      "description": "Diese Studie untersucht...",
      "external_url": "https://arxiv.org/abs/2304.12345",
      "attributes": [
        { "trait_type": "Type", "value": "Scientific Paper" },
        { "trait_type": "Source", "value": "arXiv" },
        { "trait_type": "arXiv ID", "value": "2304.12345" },
        { "trait_type": "DOI", "value": "10.12345/example.doi" },
        { "trait_type": "Category", "value": "cs.AI" },
        { "trait_type": "Authors", "value": "Dr. Maria Schmidt, Prof. Hans Müller" },
        { "trait_type": "Publication Date", "value": "2023-04-15" }
      ]
    }
  },
  "tokenId": "42",
  "transactionHash": "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
  "message": "NFT erfolgreich erstellt"
}
```

### NFTs für mehrere Publikationen erstellen

**Anfrage**
```
POST /api/arxiv/nft
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5,
  "nftOptions": {
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 5,
  "withDOI": 3,
  "createdNFTs": 3,
  "failed": 0,
  "nfts": [
    {
      "publication": {
        "id": "pub-789012",
        "title": "Quantum Computing Applications",
        "authors": ["Dr. John Doe", "Prof. Jane Smith"],
        "abstract": "This study explores...",
        "doi": "10.12345/example.doi"
      },
      "nft": {
        "tokenId": "42",
        "metadata": {
          "name": "Quantum Computing Applications",
          "description": "This study explores...",
          "external_url": "https://arxiv.org/abs/2304.12345",
          "attributes": [
            { "trait_type": "Type", "value": "Scientific Paper" },
            { "trait_type": "Source", "value": "arXiv" },
            { "trait_type": "arXiv ID", "value": "2304.12345" },
            { "trait_type": "DOI", "value": "10.12345/example.doi" },
            { "trait_type": "Category", "value": "quant-ph" },
            { "trait_type": "Authors", "value": "Dr. John Doe, Prof. Jane Smith" },
            { "trait_type": "Publication Date", "value": "2023-04-15" }
          ]
        }
      },
      "tokenId": "42"
    },
    // Weitere NFTs...
  ]
}
```

### Statistiken abrufen

**Anfrage**
```
GET /api/arxiv/stats
```

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "stats": {
    "importedPapers": 42,
    "createdNFTs": 15,
    "failedImports": 3,
    "papersByCategory": {
      "cs.AI": 12,
      "quant-ph": 8,
      "physics": 22
    },
    "apiStats": {
      "totalRequests": 150,
      "successfulRequests": 147,
      "failedRequests": 3,
      "retries": 5,
      "requestsByEndpoint": {
        "query": 150
      },
      "successRate": "98.00%"
    },
    "successRate": "92.86%"
  }
}
```

## arXiv-Kategorien

Die arXiv-Integration unterstützt alle arXiv-Kategorien, darunter:

- **Physik**: astro-ph, cond-mat, gr-qc, hep-ex, hep-lat, hep-ph, hep-th, math-ph, nlin, nucl-ex, nucl-th, physics, quant-ph
- **Mathematik**: math
- **Informatik**: cs.AI, cs.AR, cs.CC, cs.CE, cs.CG, cs.CL, cs.CR, cs.CV, cs.CY, cs.DB, cs.DC, cs.DL, cs.DM, cs.DS, cs.ET, cs.FL, cs.GL, cs.GR, cs.GT, cs.HC, cs.IR, cs.IT, cs.LG, cs.LO, cs.MA, cs.MM, cs.MS, cs.NA, cs.NE, cs.NI, cs.OS, cs.PF, cs.PL, cs.RO, cs.SC, cs.SD, cs.SE, cs.SI, cs.SY
- **Quantitative Biologie**: q-bio
- **Quantitative Finanzen**: q-fin
- **Statistik**: stat
- **Elektrotechnik und Systemwissenschaft**: eess
- **Wirtschaftswissenschaften**: econ

Eine vollständige Liste der Kategorien und Unterkategorien finden Sie auf der [arXiv-Taxonomie-Seite](https://arxiv.org/category_taxonomy).

## Nutzungshinweise

Bei der Verwendung der arXiv-Integration sind folgende Punkte zu beachten:

1. **API-Limits**: Die arXiv API hat Ratenbegrenzungen. Es wird empfohlen, nicht mehr als eine Anfrage alle 3 Sekunden zu stellen.

2. **Urheberrecht**: Die meisten arXiv-Publikationen stehen unter der "arXiv non-exclusive license to distribute", die keine Weiterverbreitung erlaubt. Stellen Sie sicher, dass Sie die Urheberrechte respektieren.

3. **DOIs und NFTs**: Nur Publikationen mit DOIs können als NFTs tokenisiert werden. Nicht alle arXiv-Publikationen haben DOIs.

4. **Metadaten**: Die arXiv API liefert nur Metadaten, keinen Volltext. Der Volltext kann über die PDF-URL abgerufen werden, aber dies ist nicht Teil der API-Integration.

5. **Kategorien**: Bei der Suche nach Kategorien können sowohl Hauptkategorien (z.B. "physics") als auch Unterkategorien (z.B. "cs.AI") verwendet werden.

## Beispiele

### Curl-Beispiel: Suche nach Publikationen

```bash
curl -X GET "http://localhost:3000/api/arxiv/search?query=quantum+computing&category=quant-ph&maxResults=5"
```

### Curl-Beispiel: Publikation importieren

```bash
curl -X POST "http://localhost:3000/api/arxiv/import/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..."
```

### Curl-Beispiel: NFT erstellen

```bash
curl -X POST "http://localhost:3000/api/arxiv/nft/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..." \
  -H "Content-Type: application/json" \
  -d '{
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }'
```  }'
```  }'
```  }'
``````
Die arXiv-Integration bietet folgende Funktionen:

- Suche nach wissenschaftlichen Publikationen in arXiv
- Abruf von Publikationsdetails anhand der arXiv-ID
- Import von arXiv-Publikationen in DeSci-Scholar
- Erstellung von NFTs für arXiv-Publikationen mit DOIs
- Abruf der neuesten Publikationen in bestimmten Kategorien
- Statistiken zur arXiv-Integration

## API-Endpunkte

### Suche nach Publikationen

**Anfrage**
```
GET /api/arxiv/search
```

**Query-Parameter**
- `query` - Allgemeiner Suchbegriff
- `title` - Suche im Titel
- `author` - Suche nach Autor
- `category` - Suche nach Kategorie (z.B. "physics", "cs.AI")
- `id` - Suche nach arXiv-ID
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 10)
- `start` - Startposition für Paginierung (Standard: 0)
- `sortBy` - Sortierkriterium ("relevance", "lastUpdatedDate", "submittedDate")
- `sortOrder` - Sortierreihenfolge ("ascending", "descending")

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalResults": 42,
  "startIndex": 0,
  "itemsPerPage": 10,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikationsdetails abrufen

**Anfrage**
```
GET /api/arxiv/paper/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Antwort (200 OK)**
```json
{
  "success": true,
  "paper": {
    "id": "2304.12345",
    "title": "Anwendung von KI in der Medizin",
    "abstract": "Diese Studie untersucht...",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "categories": ["cs.AI", "cs.LG"],
    "primaryCategory": "cs.AI",
    "published": "2023-04-15T14:22:10Z",
    "updated": "2023-04-16T09:15:30Z",
    "doi": "10.12345/example.doi",
    "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
    "comment": "20 pages, 8 figures",
    "pdfUrl": "https://arxiv.org/pdf/2304.12345",
    "arxivUrl": "https://arxiv.org/abs/2304.12345"
  }
}
```

### Neueste Publikationen in einer Kategorie abrufen

**Anfrage**
```
GET /api/arxiv/recent/:category
```

**Parameter**
- `category` - arXiv-Kategorie (z.B. "cs.AI", "physics")

**Query-Parameter**
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 30)

**Antwort (200 OK)**
```json
{
  "success": true,
  "category": "cs.AI",
  "categoryName": "Künstliche Intelligenz",
  "totalResults": 42,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikation importieren

**Anfrage**
```
POST /api/arxiv/import/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "keywords": ["Künstliche Intelligenz", "Machine Learning"],
    "publishedDate": "2023-04-15T14:22:10Z",
    "doi": "10.12345/example.doi",
    "license": "arXiv non-exclusive license to distribute",
    "status": "published",
    "arxivMetadata": {
      "id": "2304.12345",
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "updated": "2023-04-16T09:15:30Z",
      "comment": "20 pages, 8 figures",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "url": "https://arxiv.org/abs/2304.12345",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345"
    },
    "storage": {
      "ipfs": "QmXa12S5sdf87gh4j56k7l...",
      "bittorrent": "a1b2c3d4e5f6...",
      "timestamp": "2023-05-10T08:12:45Z"
    }
  },
  "message": "Publikation erfolgreich importiert"
}
```

### Mehrere Publikationen importieren

**Anfrage**
```
POST /api/arxiv/import
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 42,
  "imported": 5,
  "failed": 0,
  "publications": [
    {
      "id": "pub-789012",
      "title": "Quantum Computing Applications",
      "authors": ["Dr. John Doe", "Prof. Jane Smith"],
      "abstract": "This study explores...",
      "keywords": ["Quantum Computing", "Quantum Physics"],
      "publishedDate": "2023-04-15T14:22:10Z",
      "doi": "10.12345/example.doi",
      "license": "arXiv non-exclusive license to distribute",
      "status": "published",
      "arxivMetadata": {
        "id": "2304.12345",
        "categories": ["quant-ph", "cs.ET"],
        "primaryCategory": "quant-ph",
        "updated": "2023-04-16T09:15:30Z",
        "comment": "15 pages, 5 figures",
        "journalRef": "Journal of Quantum Computing, Vol. 10, pp. 45-60",
        "url": "https://arxiv.org/abs/2304.12345",
        "pdfUrl": "https://arxiv.org/pdf/2304.12345"
      }
    },
    // Weitere Publikationen...
  ]
}
```

### NFT für eine Publikation erstellen

**Anfrage**
```
POST /api/arxiv/nft/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Body**
```json
{
  "recipient": "0x1234567890abcdef1234567890abcdef12345678",
  "royaltyPercentage": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "doi": "10.12345/example.doi"
  },
  "nft": {
    "tokenId": "42",
    "metadata": {
      "name": "Anwendung von KI in der Medizin",
      "description": "Diese Studie untersucht...",
      "external_url": "https://arxiv.org/abs/2304.12345",
      "attributes": [
        { "trait_type": "Type", "value": "Scientific Paper" },
        { "trait_type": "Source", "value": "arXiv" },
        { "trait_type": "arXiv ID", "value": "2304.12345" },
        { "trait_type": "DOI", "value": "10.12345/example.doi" },
        { "trait_type": "Category", "value": "cs.AI" },
        { "trait_type": "Authors", "value": "Dr. Maria Schmidt, Prof. Hans Müller" },
        { "trait_type": "Publication Date", "value": "2023-04-15" }
      ]
    }
  },
  "tokenId": "42",
  "transactionHash": "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
  "message": "NFT erfolgreich erstellt"
}
```

### NFTs für mehrere Publikationen erstellen

**Anfrage**
```
POST /api/arxiv/nft
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5,
  "nftOptions": {
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 5,
  "withDOI": 3,
  "createdNFTs": 3,
  "failed": 0,
  "nfts": [
    {
      "publication": {
        "id": "pub-789012",
        "title": "Quantum Computing Applications",
        "authors": ["Dr. John Doe", "Prof. Jane Smith"],
        "abstract": "This study explores...",
        "doi": "10.12345/example.doi"
      },
      "nft": {
        "tokenId": "42",
        "metadata": {
          "name": "Quantum Computing Applications",
          "description": "This study explores...",
          "external_url": "https://arxiv.org/abs/2304.12345",
          "attributes": [
            { "trait_type": "Type", "value": "Scientific Paper" },
            { "trait_type": "Source", "value": "arXiv" },
            { "trait_type": "arXiv ID", "value": "2304.12345" },
            { "trait_type": "DOI", "value": "10.12345/example.doi" },
            { "trait_type": "Category", "value": "quant-ph" },
            { "trait_type": "Authors", "value": "Dr. John Doe, Prof. Jane Smith" },
            { "trait_type": "Publication Date", "value": "2023-04-15" }
          ]
        }
      },
      "tokenId": "42"
    },
    // Weitere NFTs...
  ]
}
```

### Statistiken abrufen

**Anfrage**
```
GET /api/arxiv/stats
```

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "stats": {
    "importedPapers": 42,
    "createdNFTs": 15,
    "failedImports": 3,
    "papersByCategory": {
      "cs.AI": 12,
      "quant-ph": 8,
      "physics": 22
    },
    "apiStats": {
      "totalRequests": 150,
      "successfulRequests": 147,
      "failedRequests": 3,
      "retries": 5,
      "requestsByEndpoint": {
        "query": 150
      },
      "successRate": "98.00%"
    },
    "successRate": "92.86%"
  }
}
```

## arXiv-Kategorien

Die arXiv-Integration unterstützt alle arXiv-Kategorien, darunter:

- **Physik**: astro-ph, cond-mat, gr-qc, hep-ex, hep-lat, hep-ph, hep-th, math-ph, nlin, nucl-ex, nucl-th, physics, quant-ph
- **Mathematik**: math
- **Informatik**: cs.AI, cs.AR, cs.CC, cs.CE, cs.CG, cs.CL, cs.CR, cs.CV, cs.CY, cs.DB, cs.DC, cs.DL, cs.DM, cs.DS, cs.ET, cs.FL, cs.GL, cs.GR, cs.GT, cs.HC, cs.IR, cs.IT, cs.LG, cs.LO, cs.MA, cs.MM, cs.MS, cs.NA, cs.NE, cs.NI, cs.OS, cs.PF, cs.PL, cs.RO, cs.SC, cs.SD, cs.SE, cs.SI, cs.SY
- **Quantitative Biologie**: q-bio
- **Quantitative Finanzen**: q-fin
- **Statistik**: stat
- **Elektrotechnik und Systemwissenschaft**: eess
- **Wirtschaftswissenschaften**: econ

Eine vollständige Liste der Kategorien und Unterkategorien finden Sie auf der [arXiv-Taxonomie-Seite](https://arxiv.org/category_taxonomy).

## Nutzungshinweise

Bei der Verwendung der arXiv-Integration sind folgende Punkte zu beachten:

1. **API-Limits**: Die arXiv API hat Ratenbegrenzungen. Es wird empfohlen, nicht mehr als eine Anfrage alle 3 Sekunden zu stellen.

2. **Urheberrecht**: Die meisten arXiv-Publikationen stehen unter der "arXiv non-exclusive license to distribute", die keine Weiterverbreitung erlaubt. Stellen Sie sicher, dass Sie die Urheberrechte respektieren.

3. **DOIs und NFTs**: Nur Publikationen mit DOIs können als NFTs tokenisiert werden. Nicht alle arXiv-Publikationen haben DOIs.

4. **Metadaten**: Die arXiv API liefert nur Metadaten, keinen Volltext. Der Volltext kann über die PDF-URL abgerufen werden, aber dies ist nicht Teil der API-Integration.

5. **Kategorien**: Bei der Suche nach Kategorien können sowohl Hauptkategorien (z.B. "physics") als auch Unterkategorien (z.B. "cs.AI") verwendet werden.

## Beispiele

### Curl-Beispiel: Suche nach Publikationen

```bash
curl -X GET "http://localhost:3000/api/arxiv/search?query=quantum+computing&category=quant-ph&maxResults=5"
```

### Curl-Beispiel: Publikation importieren

```bash
curl -X POST "http://localhost:3000/api/arxiv/import/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..."
```

### Curl-Beispiel: NFT erstellen

```bash
curl -X POST "http://localhost:3000/api/arxiv/nft/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..." \
  -H "Content-Type: application/json" \
  -d '{
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }'
```  }'
``````  }'
```  }'
``````
Die arXiv-Integration bietet folgende Funktionen:

- Suche nach wissenschaftlichen Publikationen in arXiv
- Abruf von Publikationsdetails anhand der arXiv-ID
- Import von arXiv-Publikationen in DeSci-Scholar
- Erstellung von NFTs für arXiv-Publikationen mit DOIs
- Abruf der neuesten Publikationen in bestimmten Kategorien
- Statistiken zur arXiv-Integration

## API-Endpunkte

### Suche nach Publikationen

**Anfrage**
```
GET /api/arxiv/search
```

**Query-Parameter**
- `query` - Allgemeiner Suchbegriff
- `title` - Suche im Titel
- `author` - Suche nach Autor
- `category` - Suche nach Kategorie (z.B. "physics", "cs.AI")
- `id` - Suche nach arXiv-ID
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 10)
- `start` - Startposition für Paginierung (Standard: 0)
- `sortBy` - Sortierkriterium ("relevance", "lastUpdatedDate", "submittedDate")
- `sortOrder` - Sortierreihenfolge ("ascending", "descending")

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalResults": 42,
  "startIndex": 0,
  "itemsPerPage": 10,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikationsdetails abrufen

**Anfrage**
```
GET /api/arxiv/paper/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Antwort (200 OK)**
```json
{
  "success": true,
  "paper": {
    "id": "2304.12345",
    "title": "Anwendung von KI in der Medizin",
    "abstract": "Diese Studie untersucht...",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "categories": ["cs.AI", "cs.LG"],
    "primaryCategory": "cs.AI",
    "published": "2023-04-15T14:22:10Z",
    "updated": "2023-04-16T09:15:30Z",
    "doi": "10.12345/example.doi",
    "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
    "comment": "20 pages, 8 figures",
    "pdfUrl": "https://arxiv.org/pdf/2304.12345",
    "arxivUrl": "https://arxiv.org/abs/2304.12345"
  }
}
```

### Neueste Publikationen in einer Kategorie abrufen

**Anfrage**
```
GET /api/arxiv/recent/:category
```

**Parameter**
- `category` - arXiv-Kategorie (z.B. "cs.AI", "physics")

**Query-Parameter**
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 30)

**Antwort (200 OK)**
```json
{
  "success": true,
  "category": "cs.AI",
  "categoryName": "Künstliche Intelligenz",
  "totalResults": 42,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikation importieren

**Anfrage**
```
POST /api/arxiv/import/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "keywords": ["Künstliche Intelligenz", "Machine Learning"],
    "publishedDate": "2023-04-15T14:22:10Z",
    "doi": "10.12345/example.doi",
    "license": "arXiv non-exclusive license to distribute",
    "status": "published",
    "arxivMetadata": {
      "id": "2304.12345",
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "updated": "2023-04-16T09:15:30Z",
      "comment": "20 pages, 8 figures",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "url": "https://arxiv.org/abs/2304.12345",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345"
    },
    "storage": {
      "ipfs": "QmXa12S5sdf87gh4j56k7l...",
      "bittorrent": "a1b2c3d4e5f6...",
      "timestamp": "2023-05-10T08:12:45Z"
    }
  },
  "message": "Publikation erfolgreich importiert"
}
```

### Mehrere Publikationen importieren

**Anfrage**
```
POST /api/arxiv/import
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 42,
  "imported": 5,
  "failed": 0,
  "publications": [
    {
      "id": "pub-789012",
      "title": "Quantum Computing Applications",
      "authors": ["Dr. John Doe", "Prof. Jane Smith"],
      "abstract": "This study explores...",
      "keywords": ["Quantum Computing", "Quantum Physics"],
      "publishedDate": "2023-04-15T14:22:10Z",
      "doi": "10.12345/example.doi",
      "license": "arXiv non-exclusive license to distribute",
      "status": "published",
      "arxivMetadata": {
        "id": "2304.12345",
        "categories": ["quant-ph", "cs.ET"],
        "primaryCategory": "quant-ph",
        "updated": "2023-04-16T09:15:30Z",
        "comment": "15 pages, 5 figures",
        "journalRef": "Journal of Quantum Computing, Vol. 10, pp. 45-60",
        "url": "https://arxiv.org/abs/2304.12345",
        "pdfUrl": "https://arxiv.org/pdf/2304.12345"
      }
    },
    // Weitere Publikationen...
  ]
}
```

### NFT für eine Publikation erstellen

**Anfrage**
```
POST /api/arxiv/nft/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Body**
```json
{
  "recipient": "0x1234567890abcdef1234567890abcdef12345678",
  "royaltyPercentage": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "doi": "10.12345/example.doi"
  },
  "nft": {
    "tokenId": "42",
    "metadata": {
      "name": "Anwendung von KI in der Medizin",
      "description": "Diese Studie untersucht...",
      "external_url": "https://arxiv.org/abs/2304.12345",
      "attributes": [
        { "trait_type": "Type", "value": "Scientific Paper" },
        { "trait_type": "Source", "value": "arXiv" },
        { "trait_type": "arXiv ID", "value": "2304.12345" },
        { "trait_type": "DOI", "value": "10.12345/example.doi" },
        { "trait_type": "Category", "value": "cs.AI" },
        { "trait_type": "Authors", "value": "Dr. Maria Schmidt, Prof. Hans Müller" },
        { "trait_type": "Publication Date", "value": "2023-04-15" }
      ]
    }
  },
  "tokenId": "42",
  "transactionHash": "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
  "message": "NFT erfolgreich erstellt"
}
```

### NFTs für mehrere Publikationen erstellen

**Anfrage**
```
POST /api/arxiv/nft
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5,
  "nftOptions": {
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 5,
  "withDOI": 3,
  "createdNFTs": 3,
  "failed": 0,
  "nfts": [
    {
      "publication": {
        "id": "pub-789012",
        "title": "Quantum Computing Applications",
        "authors": ["Dr. John Doe", "Prof. Jane Smith"],
        "abstract": "This study explores...",
        "doi": "10.12345/example.doi"
      },
      "nft": {
        "tokenId": "42",
        "metadata": {
          "name": "Quantum Computing Applications",
          "description": "This study explores...",
          "external_url": "https://arxiv.org/abs/2304.12345",
          "attributes": [
            { "trait_type": "Type", "value": "Scientific Paper" },
            { "trait_type": "Source", "value": "arXiv" },
            { "trait_type": "arXiv ID", "value": "2304.12345" },
            { "trait_type": "DOI", "value": "10.12345/example.doi" },
            { "trait_type": "Category", "value": "quant-ph" },
            { "trait_type": "Authors", "value": "Dr. John Doe, Prof. Jane Smith" },
            { "trait_type": "Publication Date", "value": "2023-04-15" }
          ]
        }
      },
      "tokenId": "42"
    },
    // Weitere NFTs...
  ]
}
```

### Statistiken abrufen

**Anfrage**
```
GET /api/arxiv/stats
```

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "stats": {
    "importedPapers": 42,
    "createdNFTs": 15,
    "failedImports": 3,
    "papersByCategory": {
      "cs.AI": 12,
      "quant-ph": 8,
      "physics": 22
    },
    "apiStats": {
      "totalRequests": 150,
      "successfulRequests": 147,
      "failedRequests": 3,
      "retries": 5,
      "requestsByEndpoint": {
        "query": 150
      },
      "successRate": "98.00%"
    },
    "successRate": "92.86%"
  }
}
```

## arXiv-Kategorien

Die arXiv-Integration unterstützt alle arXiv-Kategorien, darunter:

- **Physik**: astro-ph, cond-mat, gr-qc, hep-ex, hep-lat, hep-ph, hep-th, math-ph, nlin, nucl-ex, nucl-th, physics, quant-ph
- **Mathematik**: math
- **Informatik**: cs.AI, cs.AR, cs.CC, cs.CE, cs.CG, cs.CL, cs.CR, cs.CV, cs.CY, cs.DB, cs.DC, cs.DL, cs.DM, cs.DS, cs.ET, cs.FL, cs.GL, cs.GR, cs.GT, cs.HC, cs.IR, cs.IT, cs.LG, cs.LO, cs.MA, cs.MM, cs.MS, cs.NA, cs.NE, cs.NI, cs.OS, cs.PF, cs.PL, cs.RO, cs.SC, cs.SD, cs.SE, cs.SI, cs.SY
- **Quantitative Biologie**: q-bio
- **Quantitative Finanzen**: q-fin
- **Statistik**: stat
- **Elektrotechnik und Systemwissenschaft**: eess
- **Wirtschaftswissenschaften**: econ

Eine vollständige Liste der Kategorien und Unterkategorien finden Sie auf der [arXiv-Taxonomie-Seite](https://arxiv.org/category_taxonomy).

## Nutzungshinweise

Bei der Verwendung der arXiv-Integration sind folgende Punkte zu beachten:

1. **API-Limits**: Die arXiv API hat Ratenbegrenzungen. Es wird empfohlen, nicht mehr als eine Anfrage alle 3 Sekunden zu stellen.

2. **Urheberrecht**: Die meisten arXiv-Publikationen stehen unter der "arXiv non-exclusive license to distribute", die keine Weiterverbreitung erlaubt. Stellen Sie sicher, dass Sie die Urheberrechte respektieren.

3. **DOIs und NFTs**: Nur Publikationen mit DOIs können als NFTs tokenisiert werden. Nicht alle arXiv-Publikationen haben DOIs.

4. **Metadaten**: Die arXiv API liefert nur Metadaten, keinen Volltext. Der Volltext kann über die PDF-URL abgerufen werden, aber dies ist nicht Teil der API-Integration.

5. **Kategorien**: Bei der Suche nach Kategorien können sowohl Hauptkategorien (z.B. "physics") als auch Unterkategorien (z.B. "cs.AI") verwendet werden.

## Beispiele

### Curl-Beispiel: Suche nach Publikationen

```bash
curl -X GET "http://localhost:3000/api/arxiv/search?query=quantum+computing&category=quant-ph&maxResults=5"
```

### Curl-Beispiel: Publikation importieren

```bash
curl -X POST "http://localhost:3000/api/arxiv/import/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..."
```

### Curl-Beispiel: NFT erstellen

```bash
curl -X POST "http://localhost:3000/api/arxiv/nft/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..." \
  -H "Content-Type: application/json" \
  -d '{
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }'
```  }'
````````````  }'
```  }'
``````
Die arXiv-Integration bietet folgende Funktionen:

- Suche nach wissenschaftlichen Publikationen in arXiv
- Abruf von Publikationsdetails anhand der arXiv-ID
- Import von arXiv-Publikationen in DeSci-Scholar
- Erstellung von NFTs für arXiv-Publikationen mit DOIs
- Abruf der neuesten Publikationen in bestimmten Kategorien
- Statistiken zur arXiv-Integration

## API-Endpunkte

### Suche nach Publikationen

**Anfrage**
```
GET /api/arxiv/search
```

**Query-Parameter**
- `query` - Allgemeiner Suchbegriff
- `title` - Suche im Titel
- `author` - Suche nach Autor
- `category` - Suche nach Kategorie (z.B. "physics", "cs.AI")
- `id` - Suche nach arXiv-ID
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 10)
- `start` - Startposition für Paginierung (Standard: 0)
- `sortBy` - Sortierkriterium ("relevance", "lastUpdatedDate", "submittedDate")
- `sortOrder` - Sortierreihenfolge ("ascending", "descending")

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalResults": 42,
  "startIndex": 0,
  "itemsPerPage": 10,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikationsdetails abrufen

**Anfrage**
```
GET /api/arxiv/paper/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Antwort (200 OK)**
```json
{
  "success": true,
  "paper": {
    "id": "2304.12345",
    "title": "Anwendung von KI in der Medizin",
    "abstract": "Diese Studie untersucht...",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "categories": ["cs.AI", "cs.LG"],
    "primaryCategory": "cs.AI",
    "published": "2023-04-15T14:22:10Z",
    "updated": "2023-04-16T09:15:30Z",
    "doi": "10.12345/example.doi",
    "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
    "comment": "20 pages, 8 figures",
    "pdfUrl": "https://arxiv.org/pdf/2304.12345",
    "arxivUrl": "https://arxiv.org/abs/2304.12345"
  }
}
```

### Neueste Publikationen in einer Kategorie abrufen

**Anfrage**
```
GET /api/arxiv/recent/:category
```

**Parameter**
- `category` - arXiv-Kategorie (z.B. "cs.AI", "physics")

**Query-Parameter**
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 30)

**Antwort (200 OK)**
```json
{
  "success": true,
  "category": "cs.AI",
  "categoryName": "Künstliche Intelligenz",
  "totalResults": 42,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikation importieren

**Anfrage**
```
POST /api/arxiv/import/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "keywords": ["Künstliche Intelligenz", "Machine Learning"],
    "publishedDate": "2023-04-15T14:22:10Z",
    "doi": "10.12345/example.doi",
    "license": "arXiv non-exclusive license to distribute",
    "status": "published",
    "arxivMetadata": {
      "id": "2304.12345",
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "updated": "2023-04-16T09:15:30Z",
      "comment": "20 pages, 8 figures",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "url": "https://arxiv.org/abs/2304.12345",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345"
    },
    "storage": {
      "ipfs": "QmXa12S5sdf87gh4j56k7l...",
      "bittorrent": "a1b2c3d4e5f6...",
      "timestamp": "2023-05-10T08:12:45Z"
    }
  },
  "message": "Publikation erfolgreich importiert"
}
```

### Mehrere Publikationen importieren

**Anfrage**
```
POST /api/arxiv/import
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 42,
  "imported": 5,
  "failed": 0,
  "publications": [
    {
      "id": "pub-789012",
      "title": "Quantum Computing Applications",
      "authors": ["Dr. John Doe", "Prof. Jane Smith"],
      "abstract": "This study explores...",
      "keywords": ["Quantum Computing", "Quantum Physics"],
      "publishedDate": "2023-04-15T14:22:10Z",
      "doi": "10.12345/example.doi",
      "license": "arXiv non-exclusive license to distribute",
      "status": "published",
      "arxivMetadata": {
        "id": "2304.12345",
        "categories": ["quant-ph", "cs.ET"],
        "primaryCategory": "quant-ph",
        "updated": "2023-04-16T09:15:30Z",
        "comment": "15 pages, 5 figures",
        "journalRef": "Journal of Quantum Computing, Vol. 10, pp. 45-60",
        "url": "https://arxiv.org/abs/2304.12345",
        "pdfUrl": "https://arxiv.org/pdf/2304.12345"
      }
    },
    // Weitere Publikationen...
  ]
}
```

### NFT für eine Publikation erstellen

**Anfrage**
```
POST /api/arxiv/nft/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Body**
```json
{
  "recipient": "0x1234567890abcdef1234567890abcdef12345678",
  "royaltyPercentage": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "doi": "10.12345/example.doi"
  },
  "nft": {
    "tokenId": "42",
    "metadata": {
      "name": "Anwendung von KI in der Medizin",
      "description": "Diese Studie untersucht...",
      "external_url": "https://arxiv.org/abs/2304.12345",
      "attributes": [
        { "trait_type": "Type", "value": "Scientific Paper" },
        { "trait_type": "Source", "value": "arXiv" },
        { "trait_type": "arXiv ID", "value": "2304.12345" },
        { "trait_type": "DOI", "value": "10.12345/example.doi" },
        { "trait_type": "Category", "value": "cs.AI" },
        { "trait_type": "Authors", "value": "Dr. Maria Schmidt, Prof. Hans Müller" },
        { "trait_type": "Publication Date", "value": "2023-04-15" }
      ]
    }
  },
  "tokenId": "42",
  "transactionHash": "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
  "message": "NFT erfolgreich erstellt"
}
```

### NFTs für mehrere Publikationen erstellen

**Anfrage**
```
POST /api/arxiv/nft
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5,
  "nftOptions": {
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 5,
  "withDOI": 3,
  "createdNFTs": 3,
  "failed": 0,
  "nfts": [
    {
      "publication": {
        "id": "pub-789012",
        "title": "Quantum Computing Applications",
        "authors": ["Dr. John Doe", "Prof. Jane Smith"],
        "abstract": "This study explores...",
        "doi": "10.12345/example.doi"
      },
      "nft": {
        "tokenId": "42",
        "metadata": {
          "name": "Quantum Computing Applications",
          "description": "This study explores...",
          "external_url": "https://arxiv.org/abs/2304.12345",
          "attributes": [
            { "trait_type": "Type", "value": "Scientific Paper" },
            { "trait_type": "Source", "value": "arXiv" },
            { "trait_type": "arXiv ID", "value": "2304.12345" },
            { "trait_type": "DOI", "value": "10.12345/example.doi" },
            { "trait_type": "Category", "value": "quant-ph" },
            { "trait_type": "Authors", "value": "Dr. John Doe, Prof. Jane Smith" },
            { "trait_type": "Publication Date", "value": "2023-04-15" }
          ]
        }
      },
      "tokenId": "42"
    },
    // Weitere NFTs...
  ]
}
```

### Statistiken abrufen

**Anfrage**
```
GET /api/arxiv/stats
```

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "stats": {
    "importedPapers": 42,
    "createdNFTs": 15,
    "failedImports": 3,
    "papersByCategory": {
      "cs.AI": 12,
      "quant-ph": 8,
      "physics": 22
    },
    "apiStats": {
      "totalRequests": 150,
      "successfulRequests": 147,
      "failedRequests": 3,
      "retries": 5,
      "requestsByEndpoint": {
        "query": 150
      },
      "successRate": "98.00%"
    },
    "successRate": "92.86%"
  }
}
```

## arXiv-Kategorien

Die arXiv-Integration unterstützt alle arXiv-Kategorien, darunter:

- **Physik**: astro-ph, cond-mat, gr-qc, hep-ex, hep-lat, hep-ph, hep-th, math-ph, nlin, nucl-ex, nucl-th, physics, quant-ph
- **Mathematik**: math
- **Informatik**: cs.AI, cs.AR, cs.CC, cs.CE, cs.CG, cs.CL, cs.CR, cs.CV, cs.CY, cs.DB, cs.DC, cs.DL, cs.DM, cs.DS, cs.ET, cs.FL, cs.GL, cs.GR, cs.GT, cs.HC, cs.IR, cs.IT, cs.LG, cs.LO, cs.MA, cs.MM, cs.MS, cs.NA, cs.NE, cs.NI, cs.OS, cs.PF, cs.PL, cs.RO, cs.SC, cs.SD, cs.SE, cs.SI, cs.SY
- **Quantitative Biologie**: q-bio
- **Quantitative Finanzen**: q-fin
- **Statistik**: stat
- **Elektrotechnik und Systemwissenschaft**: eess
- **Wirtschaftswissenschaften**: econ

Eine vollständige Liste der Kategorien und Unterkategorien finden Sie auf der [arXiv-Taxonomie-Seite](https://arxiv.org/category_taxonomy).

## Nutzungshinweise

Bei der Verwendung der arXiv-Integration sind folgende Punkte zu beachten:

1. **API-Limits**: Die arXiv API hat Ratenbegrenzungen. Es wird empfohlen, nicht mehr als eine Anfrage alle 3 Sekunden zu stellen.

2. **Urheberrecht**: Die meisten arXiv-Publikationen stehen unter der "arXiv non-exclusive license to distribute", die keine Weiterverbreitung erlaubt. Stellen Sie sicher, dass Sie die Urheberrechte respektieren.

3. **DOIs und NFTs**: Nur Publikationen mit DOIs können als NFTs tokenisiert werden. Nicht alle arXiv-Publikationen haben DOIs.

4. **Metadaten**: Die arXiv API liefert nur Metadaten, keinen Volltext. Der Volltext kann über die PDF-URL abgerufen werden, aber dies ist nicht Teil der API-Integration.

5. **Kategorien**: Bei der Suche nach Kategorien können sowohl Hauptkategorien (z.B. "physics") als auch Unterkategorien (z.B. "cs.AI") verwendet werden.

## Beispiele

### Curl-Beispiel: Suche nach Publikationen

```bash
curl -X GET "http://localhost:3000/api/arxiv/search?query=quantum+computing&category=quant-ph&maxResults=5"
```

### Curl-Beispiel: Publikation importieren

```bash
curl -X POST "http://localhost:3000/api/arxiv/import/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..."
```

### Curl-Beispiel: NFT erstellen

```bash
curl -X POST "http://localhost:3000/api/arxiv/nft/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..." \
  -H "Content-Type: application/json" \
  -d '{
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }'
```  }'
```````````````  }'
```  }'
``````
Die arXiv-Integration bietet folgende Funktionen:

- Suche nach wissenschaftlichen Publikationen in arXiv
- Abruf von Publikationsdetails anhand der arXiv-ID
- Import von arXiv-Publikationen in DeSci-Scholar
- Erstellung von NFTs für arXiv-Publikationen mit DOIs
- Abruf der neuesten Publikationen in bestimmten Kategorien
- Statistiken zur arXiv-Integration

## API-Endpunkte

### Suche nach Publikationen

**Anfrage**
```
GET /api/arxiv/search
```

**Query-Parameter**
- `query` - Allgemeiner Suchbegriff
- `title` - Suche im Titel
- `author` - Suche nach Autor
- `category` - Suche nach Kategorie (z.B. "physics", "cs.AI")
- `id` - Suche nach arXiv-ID
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 10)
- `start` - Startposition für Paginierung (Standard: 0)
- `sortBy` - Sortierkriterium ("relevance", "lastUpdatedDate", "submittedDate")
- `sortOrder` - Sortierreihenfolge ("ascending", "descending")

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalResults": 42,
  "startIndex": 0,
  "itemsPerPage": 10,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikationsdetails abrufen

**Anfrage**
```
GET /api/arxiv/paper/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Antwort (200 OK)**
```json
{
  "success": true,
  "paper": {
    "id": "2304.12345",
    "title": "Anwendung von KI in der Medizin",
    "abstract": "Diese Studie untersucht...",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "categories": ["cs.AI", "cs.LG"],
    "primaryCategory": "cs.AI",
    "published": "2023-04-15T14:22:10Z",
    "updated": "2023-04-16T09:15:30Z",
    "doi": "10.12345/example.doi",
    "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
    "comment": "20 pages, 8 figures",
    "pdfUrl": "https://arxiv.org/pdf/2304.12345",
    "arxivUrl": "https://arxiv.org/abs/2304.12345"
  }
}
```

### Neueste Publikationen in einer Kategorie abrufen

**Anfrage**
```
GET /api/arxiv/recent/:category
```

**Parameter**
- `category` - arXiv-Kategorie (z.B. "cs.AI", "physics")

**Query-Parameter**
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 30)

**Antwort (200 OK)**
```json
{
  "success": true,
  "category": "cs.AI",
  "categoryName": "Künstliche Intelligenz",
  "totalResults": 42,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikation importieren

**Anfrage**
```
POST /api/arxiv/import/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "keywords": ["Künstliche Intelligenz", "Machine Learning"],
    "publishedDate": "2023-04-15T14:22:10Z",
    "doi": "10.12345/example.doi",
    "license": "arXiv non-exclusive license to distribute",
    "status": "published",
    "arxivMetadata": {
      "id": "2304.12345",
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "updated": "2023-04-16T09:15:30Z",
      "comment": "20 pages, 8 figures",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "url": "https://arxiv.org/abs/2304.12345",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345"
    },
    "storage": {
      "ipfs": "QmXa12S5sdf87gh4j56k7l...",
      "bittorrent": "a1b2c3d4e5f6...",
      "timestamp": "2023-05-10T08:12:45Z"
    }
  },
  "message": "Publikation erfolgreich importiert"
}
```

### Mehrere Publikationen importieren

**Anfrage**
```
POST /api/arxiv/import
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 42,
  "imported": 5,
  "failed": 0,
  "publications": [
    {
      "id": "pub-789012",
      "title": "Quantum Computing Applications",
      "authors": ["Dr. John Doe", "Prof. Jane Smith"],
      "abstract": "This study explores...",
      "keywords": ["Quantum Computing", "Quantum Physics"],
      "publishedDate": "2023-04-15T14:22:10Z",
      "doi": "10.12345/example.doi",
      "license": "arXiv non-exclusive license to distribute",
      "status": "published",
      "arxivMetadata": {
        "id": "2304.12345",
        "categories": ["quant-ph", "cs.ET"],
        "primaryCategory": "quant-ph",
        "updated": "2023-04-16T09:15:30Z",
        "comment": "15 pages, 5 figures",
        "journalRef": "Journal of Quantum Computing, Vol. 10, pp. 45-60",
        "url": "https://arxiv.org/abs/2304.12345",
        "pdfUrl": "https://arxiv.org/pdf/2304.12345"
      }
    },
    // Weitere Publikationen...
  ]
}
```

### NFT für eine Publikation erstellen

**Anfrage**
```
POST /api/arxiv/nft/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Body**
```json
{
  "recipient": "0x1234567890abcdef1234567890abcdef12345678",
  "royaltyPercentage": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "doi": "10.12345/example.doi"
  },
  "nft": {
    "tokenId": "42",
    "metadata": {
      "name": "Anwendung von KI in der Medizin",
      "description": "Diese Studie untersucht...",
      "external_url": "https://arxiv.org/abs/2304.12345",
      "attributes": [
        { "trait_type": "Type", "value": "Scientific Paper" },
        { "trait_type": "Source", "value": "arXiv" },
        { "trait_type": "arXiv ID", "value": "2304.12345" },
        { "trait_type": "DOI", "value": "10.12345/example.doi" },
        { "trait_type": "Category", "value": "cs.AI" },
        { "trait_type": "Authors", "value": "Dr. Maria Schmidt, Prof. Hans Müller" },
        { "trait_type": "Publication Date", "value": "2023-04-15" }
      ]
    }
  },
  "tokenId": "42",
  "transactionHash": "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
  "message": "NFT erfolgreich erstellt"
}
```

### NFTs für mehrere Publikationen erstellen

**Anfrage**
```
POST /api/arxiv/nft
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5,
  "nftOptions": {
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 5,
  "withDOI": 3,
  "createdNFTs": 3,
  "failed": 0,
  "nfts": [
    {
      "publication": {
        "id": "pub-789012",
        "title": "Quantum Computing Applications",
        "authors": ["Dr. John Doe", "Prof. Jane Smith"],
        "abstract": "This study explores...",
        "doi": "10.12345/example.doi"
      },
      "nft": {
        "tokenId": "42",
        "metadata": {
          "name": "Quantum Computing Applications",
          "description": "This study explores...",
          "external_url": "https://arxiv.org/abs/2304.12345",
          "attributes": [
            { "trait_type": "Type", "value": "Scientific Paper" },
            { "trait_type": "Source", "value": "arXiv" },
            { "trait_type": "arXiv ID", "value": "2304.12345" },
            { "trait_type": "DOI", "value": "10.12345/example.doi" },
            { "trait_type": "Category", "value": "quant-ph" },
            { "trait_type": "Authors", "value": "Dr. John Doe, Prof. Jane Smith" },
            { "trait_type": "Publication Date", "value": "2023-04-15" }
          ]
        }
      },
      "tokenId": "42"
    },
    // Weitere NFTs...
  ]
}
```

### Statistiken abrufen

**Anfrage**
```
GET /api/arxiv/stats
```

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "stats": {
    "importedPapers": 42,
    "createdNFTs": 15,
    "failedImports": 3,
    "papersByCategory": {
      "cs.AI": 12,
      "quant-ph": 8,
      "physics": 22
    },
    "apiStats": {
      "totalRequests": 150,
      "successfulRequests": 147,
      "failedRequests": 3,
      "retries": 5,
      "requestsByEndpoint": {
        "query": 150
      },
      "successRate": "98.00%"
    },
    "successRate": "92.86%"
  }
}
```

## arXiv-Kategorien

Die arXiv-Integration unterstützt alle arXiv-Kategorien, darunter:

- **Physik**: astro-ph, cond-mat, gr-qc, hep-ex, hep-lat, hep-ph, hep-th, math-ph, nlin, nucl-ex, nucl-th, physics, quant-ph
- **Mathematik**: math
- **Informatik**: cs.AI, cs.AR, cs.CC, cs.CE, cs.CG, cs.CL, cs.CR, cs.CV, cs.CY, cs.DB, cs.DC, cs.DL, cs.DM, cs.DS, cs.ET, cs.FL, cs.GL, cs.GR, cs.GT, cs.HC, cs.IR, cs.IT, cs.LG, cs.LO, cs.MA, cs.MM, cs.MS, cs.NA, cs.NE, cs.NI, cs.OS, cs.PF, cs.PL, cs.RO, cs.SC, cs.SD, cs.SE, cs.SI, cs.SY
- **Quantitative Biologie**: q-bio
- **Quantitative Finanzen**: q-fin
- **Statistik**: stat
- **Elektrotechnik und Systemwissenschaft**: eess
- **Wirtschaftswissenschaften**: econ

Eine vollständige Liste der Kategorien und Unterkategorien finden Sie auf der [arXiv-Taxonomie-Seite](https://arxiv.org/category_taxonomy).

## Nutzungshinweise

Bei der Verwendung der arXiv-Integration sind folgende Punkte zu beachten:

1. **API-Limits**: Die arXiv API hat Ratenbegrenzungen. Es wird empfohlen, nicht mehr als eine Anfrage alle 3 Sekunden zu stellen.

2. **Urheberrecht**: Die meisten arXiv-Publikationen stehen unter der "arXiv non-exclusive license to distribute", die keine Weiterverbreitung erlaubt. Stellen Sie sicher, dass Sie die Urheberrechte respektieren.

3. **DOIs und NFTs**: Nur Publikationen mit DOIs können als NFTs tokenisiert werden. Nicht alle arXiv-Publikationen haben DOIs.

4. **Metadaten**: Die arXiv API liefert nur Metadaten, keinen Volltext. Der Volltext kann über die PDF-URL abgerufen werden, aber dies ist nicht Teil der API-Integration.

5. **Kategorien**: Bei der Suche nach Kategorien können sowohl Hauptkategorien (z.B. "physics") als auch Unterkategorien (z.B. "cs.AI") verwendet werden.

## Beispiele

### Curl-Beispiel: Suche nach Publikationen

```bash
curl -X GET "http://localhost:3000/api/arxiv/search?query=quantum+computing&category=quant-ph&maxResults=5"
```

### Curl-Beispiel: Publikation importieren

```bash
curl -X POST "http://localhost:3000/api/arxiv/import/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..."
```

### Curl-Beispiel: NFT erstellen

```bash
curl -X POST "http://localhost:3000/api/arxiv/nft/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..." \
  -H "Content-Type: application/json" \
  -d '{
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }'
```  }'
``````  }'
```````````````  }'
```  }'
``````
Die arXiv-Integration bietet folgende Funktionen:

- Suche nach wissenschaftlichen Publikationen in arXiv
- Abruf von Publikationsdetails anhand der arXiv-ID
- Import von arXiv-Publikationen in DeSci-Scholar
- Erstellung von NFTs für arXiv-Publikationen mit DOIs
- Abruf der neuesten Publikationen in bestimmten Kategorien
- Statistiken zur arXiv-Integration

## API-Endpunkte

### Suche nach Publikationen

**Anfrage**
```
GET /api/arxiv/search
```

**Query-Parameter**
- `query` - Allgemeiner Suchbegriff
- `title` - Suche im Titel
- `author` - Suche nach Autor
- `category` - Suche nach Kategorie (z.B. "physics", "cs.AI")
- `id` - Suche nach arXiv-ID
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 10)
- `start` - Startposition für Paginierung (Standard: 0)
- `sortBy` - Sortierkriterium ("relevance", "lastUpdatedDate", "submittedDate")
- `sortOrder` - Sortierreihenfolge ("ascending", "descending")

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalResults": 42,
  "startIndex": 0,
  "itemsPerPage": 10,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikationsdetails abrufen

**Anfrage**
```
GET /api/arxiv/paper/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Antwort (200 OK)**
```json
{
  "success": true,
  "paper": {
    "id": "2304.12345",
    "title": "Anwendung von KI in der Medizin",
    "abstract": "Diese Studie untersucht...",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "categories": ["cs.AI", "cs.LG"],
    "primaryCategory": "cs.AI",
    "published": "2023-04-15T14:22:10Z",
    "updated": "2023-04-16T09:15:30Z",
    "doi": "10.12345/example.doi",
    "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
    "comment": "20 pages, 8 figures",
    "pdfUrl": "https://arxiv.org/pdf/2304.12345",
    "arxivUrl": "https://arxiv.org/abs/2304.12345"
  }
}
```

### Neueste Publikationen in einer Kategorie abrufen

**Anfrage**
```
GET /api/arxiv/recent/:category
```

**Parameter**
- `category` - arXiv-Kategorie (z.B. "cs.AI", "physics")

**Query-Parameter**
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 30)

**Antwort (200 OK)**
```json
{
  "success": true,
  "category": "cs.AI",
  "categoryName": "Künstliche Intelligenz",
  "totalResults": 42,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikation importieren

**Anfrage**
```
POST /api/arxiv/import/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "keywords": ["Künstliche Intelligenz", "Machine Learning"],
    "publishedDate": "2023-04-15T14:22:10Z",
    "doi": "10.12345/example.doi",
    "license": "arXiv non-exclusive license to distribute",
    "status": "published",
    "arxivMetadata": {
      "id": "2304.12345",
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "updated": "2023-04-16T09:15:30Z",
      "comment": "20 pages, 8 figures",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "url": "https://arxiv.org/abs/2304.12345",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345"
    },
    "storage": {
      "ipfs": "QmXa12S5sdf87gh4j56k7l...",
      "bittorrent": "a1b2c3d4e5f6...",
      "timestamp": "2023-05-10T08:12:45Z"
    }
  },
  "message": "Publikation erfolgreich importiert"
}
```

### Mehrere Publikationen importieren

**Anfrage**
```
POST /api/arxiv/import
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 42,
  "imported": 5,
  "failed": 0,
  "publications": [
    {
      "id": "pub-789012",
      "title": "Quantum Computing Applications",
      "authors": ["Dr. John Doe", "Prof. Jane Smith"],
      "abstract": "This study explores...",
      "keywords": ["Quantum Computing", "Quantum Physics"],
      "publishedDate": "2023-04-15T14:22:10Z",
      "doi": "10.12345/example.doi",
      "license": "arXiv non-exclusive license to distribute",
      "status": "published",
      "arxivMetadata": {
        "id": "2304.12345",
        "categories": ["quant-ph", "cs.ET"],
        "primaryCategory": "quant-ph",
        "updated": "2023-04-16T09:15:30Z",
        "comment": "15 pages, 5 figures",
        "journalRef": "Journal of Quantum Computing, Vol. 10, pp. 45-60",
        "url": "https://arxiv.org/abs/2304.12345",
        "pdfUrl": "https://arxiv.org/pdf/2304.12345"
      }
    },
    // Weitere Publikationen...
  ]
}
```

### NFT für eine Publikation erstellen

**Anfrage**
```
POST /api/arxiv/nft/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Body**
```json
{
  "recipient": "0x1234567890abcdef1234567890abcdef12345678",
  "royaltyPercentage": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "doi": "10.12345/example.doi"
  },
  "nft": {
    "tokenId": "42",
    "metadata": {
      "name": "Anwendung von KI in der Medizin",
      "description": "Diese Studie untersucht...",
      "external_url": "https://arxiv.org/abs/2304.12345",
      "attributes": [
        { "trait_type": "Type", "value": "Scientific Paper" },
        { "trait_type": "Source", "value": "arXiv" },
        { "trait_type": "arXiv ID", "value": "2304.12345" },
        { "trait_type": "DOI", "value": "10.12345/example.doi" },
        { "trait_type": "Category", "value": "cs.AI" },
        { "trait_type": "Authors", "value": "Dr. Maria Schmidt, Prof. Hans Müller" },
        { "trait_type": "Publication Date", "value": "2023-04-15" }
      ]
    }
  },
  "tokenId": "42",
  "transactionHash": "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
  "message": "NFT erfolgreich erstellt"
}
```

### NFTs für mehrere Publikationen erstellen

**Anfrage**
```
POST /api/arxiv/nft
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5,
  "nftOptions": {
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 5,
  "withDOI": 3,
  "createdNFTs": 3,
  "failed": 0,
  "nfts": [
    {
      "publication": {
        "id": "pub-789012",
        "title": "Quantum Computing Applications",
        "authors": ["Dr. John Doe", "Prof. Jane Smith"],
        "abstract": "This study explores...",
        "doi": "10.12345/example.doi"
      },
      "nft": {
        "tokenId": "42",
        "metadata": {
          "name": "Quantum Computing Applications",
          "description": "This study explores...",
          "external_url": "https://arxiv.org/abs/2304.12345",
          "attributes": [
            { "trait_type": "Type", "value": "Scientific Paper" },
            { "trait_type": "Source", "value": "arXiv" },
            { "trait_type": "arXiv ID", "value": "2304.12345" },
            { "trait_type": "DOI", "value": "10.12345/example.doi" },
            { "trait_type": "Category", "value": "quant-ph" },
            { "trait_type": "Authors", "value": "Dr. John Doe, Prof. Jane Smith" },
            { "trait_type": "Publication Date", "value": "2023-04-15" }
          ]
        }
      },
      "tokenId": "42"
    },
    // Weitere NFTs...
  ]
}
```

### Statistiken abrufen

**Anfrage**
```
GET /api/arxiv/stats
```

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "stats": {
    "importedPapers": 42,
    "createdNFTs": 15,
    "failedImports": 3,
    "papersByCategory": {
      "cs.AI": 12,
      "quant-ph": 8,
      "physics": 22
    },
    "apiStats": {
      "totalRequests": 150,
      "successfulRequests": 147,
      "failedRequests": 3,
      "retries": 5,
      "requestsByEndpoint": {
        "query": 150
      },
      "successRate": "98.00%"
    },
    "successRate": "92.86%"
  }
}
```

## arXiv-Kategorien

Die arXiv-Integration unterstützt alle arXiv-Kategorien, darunter:

- **Physik**: astro-ph, cond-mat, gr-qc, hep-ex, hep-lat, hep-ph, hep-th, math-ph, nlin, nucl-ex, nucl-th, physics, quant-ph
- **Mathematik**: math
- **Informatik**: cs.AI, cs.AR, cs.CC, cs.CE, cs.CG, cs.CL, cs.CR, cs.CV, cs.CY, cs.DB, cs.DC, cs.DL, cs.DM, cs.DS, cs.ET, cs.FL, cs.GL, cs.GR, cs.GT, cs.HC, cs.IR, cs.IT, cs.LG, cs.LO, cs.MA, cs.MM, cs.MS, cs.NA, cs.NE, cs.NI, cs.OS, cs.PF, cs.PL, cs.RO, cs.SC, cs.SD, cs.SE, cs.SI, cs.SY
- **Quantitative Biologie**: q-bio
- **Quantitative Finanzen**: q-fin
- **Statistik**: stat
- **Elektrotechnik und Systemwissenschaft**: eess
- **Wirtschaftswissenschaften**: econ

Eine vollständige Liste der Kategorien und Unterkategorien finden Sie auf der [arXiv-Taxonomie-Seite](https://arxiv.org/category_taxonomy).

## Nutzungshinweise

Bei der Verwendung der arXiv-Integration sind folgende Punkte zu beachten:

1. **API-Limits**: Die arXiv API hat Ratenbegrenzungen. Es wird empfohlen, nicht mehr als eine Anfrage alle 3 Sekunden zu stellen.

2. **Urheberrecht**: Die meisten arXiv-Publikationen stehen unter der "arXiv non-exclusive license to distribute", die keine Weiterverbreitung erlaubt. Stellen Sie sicher, dass Sie die Urheberrechte respektieren.

3. **DOIs und NFTs**: Nur Publikationen mit DOIs können als NFTs tokenisiert werden. Nicht alle arXiv-Publikationen haben DOIs.

4. **Metadaten**: Die arXiv API liefert nur Metadaten, keinen Volltext. Der Volltext kann über die PDF-URL abgerufen werden, aber dies ist nicht Teil der API-Integration.

5. **Kategorien**: Bei der Suche nach Kategorien können sowohl Hauptkategorien (z.B. "physics") als auch Unterkategorien (z.B. "cs.AI") verwendet werden.

## Beispiele

### Curl-Beispiel: Suche nach Publikationen

```bash
curl -X GET "http://localhost:3000/api/arxiv/search?query=quantum+computing&category=quant-ph&maxResults=5"
```

### Curl-Beispiel: Publikation importieren

```bash
curl -X POST "http://localhost:3000/api/arxiv/import/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..."
```

### Curl-Beispiel: NFT erstellen

```bash
curl -X POST "http://localhost:3000/api/arxiv/nft/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..." \
  -H "Content-Type: application/json" \
  -d '{
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }'
```  }'
``````  }'
```````````````  }'
```  }'
``````
Die arXiv-Integration bietet folgende Funktionen:

- Suche nach wissenschaftlichen Publikationen in arXiv
- Abruf von Publikationsdetails anhand der arXiv-ID
- Import von arXiv-Publikationen in DeSci-Scholar
- Erstellung von NFTs für arXiv-Publikationen mit DOIs
- Abruf der neuesten Publikationen in bestimmten Kategorien
- Statistiken zur arXiv-Integration

## API-Endpunkte

### Suche nach Publikationen

**Anfrage**
```
GET /api/arxiv/search
```

**Query-Parameter**
- `query` - Allgemeiner Suchbegriff
- `title` - Suche im Titel
- `author` - Suche nach Autor
- `category` - Suche nach Kategorie (z.B. "physics", "cs.AI")
- `id` - Suche nach arXiv-ID
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 10)
- `start` - Startposition für Paginierung (Standard: 0)
- `sortBy` - Sortierkriterium ("relevance", "lastUpdatedDate", "submittedDate")
- `sortOrder` - Sortierreihenfolge ("ascending", "descending")

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalResults": 42,
  "startIndex": 0,
  "itemsPerPage": 10,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikationsdetails abrufen

**Anfrage**
```
GET /api/arxiv/paper/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Antwort (200 OK)**
```json
{
  "success": true,
  "paper": {
    "id": "2304.12345",
    "title": "Anwendung von KI in der Medizin",
    "abstract": "Diese Studie untersucht...",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "categories": ["cs.AI", "cs.LG"],
    "primaryCategory": "cs.AI",
    "published": "2023-04-15T14:22:10Z",
    "updated": "2023-04-16T09:15:30Z",
    "doi": "10.12345/example.doi",
    "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
    "comment": "20 pages, 8 figures",
    "pdfUrl": "https://arxiv.org/pdf/2304.12345",
    "arxivUrl": "https://arxiv.org/abs/2304.12345"
  }
}
```

### Neueste Publikationen in einer Kategorie abrufen

**Anfrage**
```
GET /api/arxiv/recent/:category
```

**Parameter**
- `category` - arXiv-Kategorie (z.B. "cs.AI", "physics")

**Query-Parameter**
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 30)

**Antwort (200 OK)**
```json
{
  "success": true,
  "category": "cs.AI",
  "categoryName": "Künstliche Intelligenz",
  "totalResults": 42,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikation importieren

**Anfrage**
```
POST /api/arxiv/import/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "keywords": ["Künstliche Intelligenz", "Machine Learning"],
    "publishedDate": "2023-04-15T14:22:10Z",
    "doi": "10.12345/example.doi",
    "license": "arXiv non-exclusive license to distribute",
    "status": "published",
    "arxivMetadata": {
      "id": "2304.12345",
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "updated": "2023-04-16T09:15:30Z",
      "comment": "20 pages, 8 figures",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "url": "https://arxiv.org/abs/2304.12345",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345"
    },
    "storage": {
      "ipfs": "QmXa12S5sdf87gh4j56k7l...",
      "bittorrent": "a1b2c3d4e5f6...",
      "timestamp": "2023-05-10T08:12:45Z"
    }
  },
  "message": "Publikation erfolgreich importiert"
}
```

### Mehrere Publikationen importieren

**Anfrage**
```
POST /api/arxiv/import
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 42,
  "imported": 5,
  "failed": 0,
  "publications": [
    {
      "id": "pub-789012",
      "title": "Quantum Computing Applications",
      "authors": ["Dr. John Doe", "Prof. Jane Smith"],
      "abstract": "This study explores...",
      "keywords": ["Quantum Computing", "Quantum Physics"],
      "publishedDate": "2023-04-15T14:22:10Z",
      "doi": "10.12345/example.doi",
      "license": "arXiv non-exclusive license to distribute",
      "status": "published",
      "arxivMetadata": {
        "id": "2304.12345",
        "categories": ["quant-ph", "cs.ET"],
        "primaryCategory": "quant-ph",
        "updated": "2023-04-16T09:15:30Z",
        "comment": "15 pages, 5 figures",
        "journalRef": "Journal of Quantum Computing, Vol. 10, pp. 45-60",
        "url": "https://arxiv.org/abs/2304.12345",
        "pdfUrl": "https://arxiv.org/pdf/2304.12345"
      }
    },
    // Weitere Publikationen...
  ]
}
```

### NFT für eine Publikation erstellen

**Anfrage**
```
POST /api/arxiv/nft/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Body**
```json
{
  "recipient": "0x1234567890abcdef1234567890abcdef12345678",
  "royaltyPercentage": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "doi": "10.12345/example.doi"
  },
  "nft": {
    "tokenId": "42",
    "metadata": {
      "name": "Anwendung von KI in der Medizin",
      "description": "Diese Studie untersucht...",
      "external_url": "https://arxiv.org/abs/2304.12345",
      "attributes": [
        { "trait_type": "Type", "value": "Scientific Paper" },
        { "trait_type": "Source", "value": "arXiv" },
        { "trait_type": "arXiv ID", "value": "2304.12345" },
        { "trait_type": "DOI", "value": "10.12345/example.doi" },
        { "trait_type": "Category", "value": "cs.AI" },
        { "trait_type": "Authors", "value": "Dr. Maria Schmidt, Prof. Hans Müller" },
        { "trait_type": "Publication Date", "value": "2023-04-15" }
      ]
    }
  },
  "tokenId": "42",
  "transactionHash": "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
  "message": "NFT erfolgreich erstellt"
}
```

### NFTs für mehrere Publikationen erstellen

**Anfrage**
```
POST /api/arxiv/nft
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5,
  "nftOptions": {
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 5,
  "withDOI": 3,
  "createdNFTs": 3,
  "failed": 0,
  "nfts": [
    {
      "publication": {
        "id": "pub-789012",
        "title": "Quantum Computing Applications",
        "authors": ["Dr. John Doe", "Prof. Jane Smith"],
        "abstract": "This study explores...",
        "doi": "10.12345/example.doi"
      },
      "nft": {
        "tokenId": "42",
        "metadata": {
          "name": "Quantum Computing Applications",
          "description": "This study explores...",
          "external_url": "https://arxiv.org/abs/2304.12345",
          "attributes": [
            { "trait_type": "Type", "value": "Scientific Paper" },
            { "trait_type": "Source", "value": "arXiv" },
            { "trait_type": "arXiv ID", "value": "2304.12345" },
            { "trait_type": "DOI", "value": "10.12345/example.doi" },
            { "trait_type": "Category", "value": "quant-ph" },
            { "trait_type": "Authors", "value": "Dr. John Doe, Prof. Jane Smith" },
            { "trait_type": "Publication Date", "value": "2023-04-15" }
          ]
        }
      },
      "tokenId": "42"
    },
    // Weitere NFTs...
  ]
}
```

### Statistiken abrufen

**Anfrage**
```
GET /api/arxiv/stats
```

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "stats": {
    "importedPapers": 42,
    "createdNFTs": 15,
    "failedImports": 3,
    "papersByCategory": {
      "cs.AI": 12,
      "quant-ph": 8,
      "physics": 22
    },
    "apiStats": {
      "totalRequests": 150,
      "successfulRequests": 147,
      "failedRequests": 3,
      "retries": 5,
      "requestsByEndpoint": {
        "query": 150
      },
      "successRate": "98.00%"
    },
    "successRate": "92.86%"
  }
}
```

## arXiv-Kategorien

Die arXiv-Integration unterstützt alle arXiv-Kategorien, darunter:

- **Physik**: astro-ph, cond-mat, gr-qc, hep-ex, hep-lat, hep-ph, hep-th, math-ph, nlin, nucl-ex, nucl-th, physics, quant-ph
- **Mathematik**: math
- **Informatik**: cs.AI, cs.AR, cs.CC, cs.CE, cs.CG, cs.CL, cs.CR, cs.CV, cs.CY, cs.DB, cs.DC, cs.DL, cs.DM, cs.DS, cs.ET, cs.FL, cs.GL, cs.GR, cs.GT, cs.HC, cs.IR, cs.IT, cs.LG, cs.LO, cs.MA, cs.MM, cs.MS, cs.NA, cs.NE, cs.NI, cs.OS, cs.PF, cs.PL, cs.RO, cs.SC, cs.SD, cs.SE, cs.SI, cs.SY
- **Quantitative Biologie**: q-bio
- **Quantitative Finanzen**: q-fin
- **Statistik**: stat
- **Elektrotechnik und Systemwissenschaft**: eess
- **Wirtschaftswissenschaften**: econ

Eine vollständige Liste der Kategorien und Unterkategorien finden Sie auf der [arXiv-Taxonomie-Seite](https://arxiv.org/category_taxonomy).

## Nutzungshinweise

Bei der Verwendung der arXiv-Integration sind folgende Punkte zu beachten:

1. **API-Limits**: Die arXiv API hat Ratenbegrenzungen. Es wird empfohlen, nicht mehr als eine Anfrage alle 3 Sekunden zu stellen.

2. **Urheberrecht**: Die meisten arXiv-Publikationen stehen unter der "arXiv non-exclusive license to distribute", die keine Weiterverbreitung erlaubt. Stellen Sie sicher, dass Sie die Urheberrechte respektieren.

3. **DOIs und NFTs**: Nur Publikationen mit DOIs können als NFTs tokenisiert werden. Nicht alle arXiv-Publikationen haben DOIs.

4. **Metadaten**: Die arXiv API liefert nur Metadaten, keinen Volltext. Der Volltext kann über die PDF-URL abgerufen werden, aber dies ist nicht Teil der API-Integration.

5. **Kategorien**: Bei der Suche nach Kategorien können sowohl Hauptkategorien (z.B. "physics") als auch Unterkategorien (z.B. "cs.AI") verwendet werden.

## Beispiele

### Curl-Beispiel: Suche nach Publikationen

```bash
curl -X GET "http://localhost:3000/api/arxiv/search?query=quantum+computing&category=quant-ph&maxResults=5"
```

### Curl-Beispiel: Publikation importieren

```bash
curl -X POST "http://localhost:3000/api/arxiv/import/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..."
```

### Curl-Beispiel: NFT erstellen

```bash
curl -X POST "http://localhost:3000/api/arxiv/nft/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..." \
  -H "Content-Type: application/json" \
  -d '{
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }'
```  }'
``````  }'
``````  }'
````````````  }'
```  }'
``````
Die arXiv-Integration bietet folgende Funktionen:

- Suche nach wissenschaftlichen Publikationen in arXiv
- Abruf von Publikationsdetails anhand der arXiv-ID
- Import von arXiv-Publikationen in DeSci-Scholar
- Erstellung von NFTs für arXiv-Publikationen mit DOIs
- Abruf der neuesten Publikationen in bestimmten Kategorien
- Statistiken zur arXiv-Integration

## API-Endpunkte

### Suche nach Publikationen

**Anfrage**
```
GET /api/arxiv/search
```

**Query-Parameter**
- `query` - Allgemeiner Suchbegriff
- `title` - Suche im Titel
- `author` - Suche nach Autor
- `category` - Suche nach Kategorie (z.B. "physics", "cs.AI")
- `id` - Suche nach arXiv-ID
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 10)
- `start` - Startposition für Paginierung (Standard: 0)
- `sortBy` - Sortierkriterium ("relevance", "lastUpdatedDate", "submittedDate")
- `sortOrder` - Sortierreihenfolge ("ascending", "descending")

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalResults": 42,
  "startIndex": 0,
  "itemsPerPage": 10,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikationsdetails abrufen

**Anfrage**
```
GET /api/arxiv/paper/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Antwort (200 OK)**
```json
{
  "success": true,
  "paper": {
    "id": "2304.12345",
    "title": "Anwendung von KI in der Medizin",
    "abstract": "Diese Studie untersucht...",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "categories": ["cs.AI", "cs.LG"],
    "primaryCategory": "cs.AI",
    "published": "2023-04-15T14:22:10Z",
    "updated": "2023-04-16T09:15:30Z",
    "doi": "10.12345/example.doi",
    "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
    "comment": "20 pages, 8 figures",
    "pdfUrl": "https://arxiv.org/pdf/2304.12345",
    "arxivUrl": "https://arxiv.org/abs/2304.12345"
  }
}
```

### Neueste Publikationen in einer Kategorie abrufen

**Anfrage**
```
GET /api/arxiv/recent/:category
```

**Parameter**
- `category` - arXiv-Kategorie (z.B. "cs.AI", "physics")

**Query-Parameter**
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 30)

**Antwort (200 OK)**
```json
{
  "success": true,
  "category": "cs.AI",
  "categoryName": "Künstliche Intelligenz",
  "totalResults": 42,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikation importieren

**Anfrage**
```
POST /api/arxiv/import/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "keywords": ["Künstliche Intelligenz", "Machine Learning"],
    "publishedDate": "2023-04-15T14:22:10Z",
    "doi": "10.12345/example.doi",
    "license": "arXiv non-exclusive license to distribute",
    "status": "published",
    "arxivMetadata": {
      "id": "2304.12345",
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "updated": "2023-04-16T09:15:30Z",
      "comment": "20 pages, 8 figures",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "url": "https://arxiv.org/abs/2304.12345",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345"
    },
    "storage": {
      "ipfs": "QmXa12S5sdf87gh4j56k7l...",
      "bittorrent": "a1b2c3d4e5f6...",
      "timestamp": "2023-05-10T08:12:45Z"
    }
  },
  "message": "Publikation erfolgreich importiert"
}
```

### Mehrere Publikationen importieren

**Anfrage**
```
POST /api/arxiv/import
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 42,
  "imported": 5,
  "failed": 0,
  "publications": [
    {
      "id": "pub-789012",
      "title": "Quantum Computing Applications",
      "authors": ["Dr. John Doe", "Prof. Jane Smith"],
      "abstract": "This study explores...",
      "keywords": ["Quantum Computing", "Quantum Physics"],
      "publishedDate": "2023-04-15T14:22:10Z",
      "doi": "10.12345/example.doi",
      "license": "arXiv non-exclusive license to distribute",
      "status": "published",
      "arxivMetadata": {
        "id": "2304.12345",
        "categories": ["quant-ph", "cs.ET"],
        "primaryCategory": "quant-ph",
        "updated": "2023-04-16T09:15:30Z",
        "comment": "15 pages, 5 figures",
        "journalRef": "Journal of Quantum Computing, Vol. 10, pp. 45-60",
        "url": "https://arxiv.org/abs/2304.12345",
        "pdfUrl": "https://arxiv.org/pdf/2304.12345"
      }
    },
    // Weitere Publikationen...
  ]
}
```

### NFT für eine Publikation erstellen

**Anfrage**
```
POST /api/arxiv/nft/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Body**
```json
{
  "recipient": "0x1234567890abcdef1234567890abcdef12345678",
  "royaltyPercentage": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "doi": "10.12345/example.doi"
  },
  "nft": {
    "tokenId": "42",
    "metadata": {
      "name": "Anwendung von KI in der Medizin",
      "description": "Diese Studie untersucht...",
      "external_url": "https://arxiv.org/abs/2304.12345",
      "attributes": [
        { "trait_type": "Type", "value": "Scientific Paper" },
        { "trait_type": "Source", "value": "arXiv" },
        { "trait_type": "arXiv ID", "value": "2304.12345" },
        { "trait_type": "DOI", "value": "10.12345/example.doi" },
        { "trait_type": "Category", "value": "cs.AI" },
        { "trait_type": "Authors", "value": "Dr. Maria Schmidt, Prof. Hans Müller" },
        { "trait_type": "Publication Date", "value": "2023-04-15" }
      ]
    }
  },
  "tokenId": "42",
  "transactionHash": "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
  "message": "NFT erfolgreich erstellt"
}
```

### NFTs für mehrere Publikationen erstellen

**Anfrage**
```
POST /api/arxiv/nft
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5,
  "nftOptions": {
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 5,
  "withDOI": 3,
  "createdNFTs": 3,
  "failed": 0,
  "nfts": [
    {
      "publication": {
        "id": "pub-789012",
        "title": "Quantum Computing Applications",
        "authors": ["Dr. John Doe", "Prof. Jane Smith"],
        "abstract": "This study explores...",
        "doi": "10.12345/example.doi"
      },
      "nft": {
        "tokenId": "42",
        "metadata": {
          "name": "Quantum Computing Applications",
          "description": "This study explores...",
          "external_url": "https://arxiv.org/abs/2304.12345",
          "attributes": [
            { "trait_type": "Type", "value": "Scientific Paper" },
            { "trait_type": "Source", "value": "arXiv" },
            { "trait_type": "arXiv ID", "value": "2304.12345" },
            { "trait_type": "DOI", "value": "10.12345/example.doi" },
            { "trait_type": "Category", "value": "quant-ph" },
            { "trait_type": "Authors", "value": "Dr. John Doe, Prof. Jane Smith" },
            { "trait_type": "Publication Date", "value": "2023-04-15" }
          ]
        }
      },
      "tokenId": "42"
    },
    // Weitere NFTs...
  ]
}
```

### Statistiken abrufen

**Anfrage**
```
GET /api/arxiv/stats
```

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "stats": {
    "importedPapers": 42,
    "createdNFTs": 15,
    "failedImports": 3,
    "papersByCategory": {
      "cs.AI": 12,
      "quant-ph": 8,
      "physics": 22
    },
    "apiStats": {
      "totalRequests": 150,
      "successfulRequests": 147,
      "failedRequests": 3,
      "retries": 5,
      "requestsByEndpoint": {
        "query": 150
      },
      "successRate": "98.00%"
    },
    "successRate": "92.86%"
  }
}
```

## arXiv-Kategorien

Die arXiv-Integration unterstützt alle arXiv-Kategorien, darunter:

- **Physik**: astro-ph, cond-mat, gr-qc, hep-ex, hep-lat, hep-ph, hep-th, math-ph, nlin, nucl-ex, nucl-th, physics, quant-ph
- **Mathematik**: math
- **Informatik**: cs.AI, cs.AR, cs.CC, cs.CE, cs.CG, cs.CL, cs.CR, cs.CV, cs.CY, cs.DB, cs.DC, cs.DL, cs.DM, cs.DS, cs.ET, cs.FL, cs.GL, cs.GR, cs.GT, cs.HC, cs.IR, cs.IT, cs.LG, cs.LO, cs.MA, cs.MM, cs.MS, cs.NA, cs.NE, cs.NI, cs.OS, cs.PF, cs.PL, cs.RO, cs.SC, cs.SD, cs.SE, cs.SI, cs.SY
- **Quantitative Biologie**: q-bio
- **Quantitative Finanzen**: q-fin
- **Statistik**: stat
- **Elektrotechnik und Systemwissenschaft**: eess
- **Wirtschaftswissenschaften**: econ

Eine vollständige Liste der Kategorien und Unterkategorien finden Sie auf der [arXiv-Taxonomie-Seite](https://arxiv.org/category_taxonomy).

## Nutzungshinweise

Bei der Verwendung der arXiv-Integration sind folgende Punkte zu beachten:

1. **API-Limits**: Die arXiv API hat Ratenbegrenzungen. Es wird empfohlen, nicht mehr als eine Anfrage alle 3 Sekunden zu stellen.

2. **Urheberrecht**: Die meisten arXiv-Publikationen stehen unter der "arXiv non-exclusive license to distribute", die keine Weiterverbreitung erlaubt. Stellen Sie sicher, dass Sie die Urheberrechte respektieren.

3. **DOIs und NFTs**: Nur Publikationen mit DOIs können als NFTs tokenisiert werden. Nicht alle arXiv-Publikationen haben DOIs.

4. **Metadaten**: Die arXiv API liefert nur Metadaten, keinen Volltext. Der Volltext kann über die PDF-URL abgerufen werden, aber dies ist nicht Teil der API-Integration.

5. **Kategorien**: Bei der Suche nach Kategorien können sowohl Hauptkategorien (z.B. "physics") als auch Unterkategorien (z.B. "cs.AI") verwendet werden.

## Beispiele

### Curl-Beispiel: Suche nach Publikationen

```bash
curl -X GET "http://localhost:3000/api/arxiv/search?query=quantum+computing&category=quant-ph&maxResults=5"
```

### Curl-Beispiel: Publikation importieren

```bash
curl -X POST "http://localhost:3000/api/arxiv/import/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..."
```

### Curl-Beispiel: NFT erstellen

```bash
curl -X POST "http://localhost:3000/api/arxiv/nft/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..." \
  -H "Content-Type: application/json" \
  -d '{
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }'
```  }'
``````````````````  }'
```  }'
``````
Die arXiv-Integration bietet folgende Funktionen:

- Suche nach wissenschaftlichen Publikationen in arXiv
- Abruf von Publikationsdetails anhand der arXiv-ID
- Import von arXiv-Publikationen in DeSci-Scholar
- Erstellung von NFTs für arXiv-Publikationen mit DOIs
- Abruf der neuesten Publikationen in bestimmten Kategorien
- Statistiken zur arXiv-Integration

## API-Endpunkte

### Suche nach Publikationen

**Anfrage**
```
GET /api/arxiv/search
```

**Query-Parameter**
- `query` - Allgemeiner Suchbegriff
- `title` - Suche im Titel
- `author` - Suche nach Autor
- `category` - Suche nach Kategorie (z.B. "physics", "cs.AI")
- `id` - Suche nach arXiv-ID
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 10)
- `start` - Startposition für Paginierung (Standard: 0)
- `sortBy` - Sortierkriterium ("relevance", "lastUpdatedDate", "submittedDate")
- `sortOrder` - Sortierreihenfolge ("ascending", "descending")

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalResults": 42,
  "startIndex": 0,
  "itemsPerPage": 10,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikationsdetails abrufen

**Anfrage**
```
GET /api/arxiv/paper/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Antwort (200 OK)**
```json
{
  "success": true,
  "paper": {
    "id": "2304.12345",
    "title": "Anwendung von KI in der Medizin",
    "abstract": "Diese Studie untersucht...",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "categories": ["cs.AI", "cs.LG"],
    "primaryCategory": "cs.AI",
    "published": "2023-04-15T14:22:10Z",
    "updated": "2023-04-16T09:15:30Z",
    "doi": "10.12345/example.doi",
    "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
    "comment": "20 pages, 8 figures",
    "pdfUrl": "https://arxiv.org/pdf/2304.12345",
    "arxivUrl": "https://arxiv.org/abs/2304.12345"
  }
}
```

### Neueste Publikationen in einer Kategorie abrufen

**Anfrage**
```
GET /api/arxiv/recent/:category
```

**Parameter**
- `category` - arXiv-Kategorie (z.B. "cs.AI", "physics")

**Query-Parameter**
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 30)

**Antwort (200 OK)**
```json
{
  "success": true,
  "category": "cs.AI",
  "categoryName": "Künstliche Intelligenz",
  "totalResults": 42,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikation importieren

**Anfrage**
```
POST /api/arxiv/import/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "keywords": ["Künstliche Intelligenz", "Machine Learning"],
    "publishedDate": "2023-04-15T14:22:10Z",
    "doi": "10.12345/example.doi",
    "license": "arXiv non-exclusive license to distribute",
    "status": "published",
    "arxivMetadata": {
      "id": "2304.12345",
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "updated": "2023-04-16T09:15:30Z",
      "comment": "20 pages, 8 figures",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "url": "https://arxiv.org/abs/2304.12345",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345"
    },
    "storage": {
      "ipfs": "QmXa12S5sdf87gh4j56k7l...",
      "bittorrent": "a1b2c3d4e5f6...",
      "timestamp": "2023-05-10T08:12:45Z"
    }
  },
  "message": "Publikation erfolgreich importiert"
}
```

### Mehrere Publikationen importieren

**Anfrage**
```
POST /api/arxiv/import
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 42,
  "imported": 5,
  "failed": 0,
  "publications": [
    {
      "id": "pub-789012",
      "title": "Quantum Computing Applications",
      "authors": ["Dr. John Doe", "Prof. Jane Smith"],
      "abstract": "This study explores...",
      "keywords": ["Quantum Computing", "Quantum Physics"],
      "publishedDate": "2023-04-15T14:22:10Z",
      "doi": "10.12345/example.doi",
      "license": "arXiv non-exclusive license to distribute",
      "status": "published",
      "arxivMetadata": {
        "id": "2304.12345",
        "categories": ["quant-ph", "cs.ET"],
        "primaryCategory": "quant-ph",
        "updated": "2023-04-16T09:15:30Z",
        "comment": "15 pages, 5 figures",
        "journalRef": "Journal of Quantum Computing, Vol. 10, pp. 45-60",
        "url": "https://arxiv.org/abs/2304.12345",
        "pdfUrl": "https://arxiv.org/pdf/2304.12345"
      }
    },
    // Weitere Publikationen...
  ]
}
```

### NFT für eine Publikation erstellen

**Anfrage**
```
POST /api/arxiv/nft/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Body**
```json
{
  "recipient": "0x1234567890abcdef1234567890abcdef12345678",
  "royaltyPercentage": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "doi": "10.12345/example.doi"
  },
  "nft": {
    "tokenId": "42",
    "metadata": {
      "name": "Anwendung von KI in der Medizin",
      "description": "Diese Studie untersucht...",
      "external_url": "https://arxiv.org/abs/2304.12345",
      "attributes": [
        { "trait_type": "Type", "value": "Scientific Paper" },
        { "trait_type": "Source", "value": "arXiv" },
        { "trait_type": "arXiv ID", "value": "2304.12345" },
        { "trait_type": "DOI", "value": "10.12345/example.doi" },
        { "trait_type": "Category", "value": "cs.AI" },
        { "trait_type": "Authors", "value": "Dr. Maria Schmidt, Prof. Hans Müller" },
        { "trait_type": "Publication Date", "value": "2023-04-15" }
      ]
    }
  },
  "tokenId": "42",
  "transactionHash": "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
  "message": "NFT erfolgreich erstellt"
}
```

### NFTs für mehrere Publikationen erstellen

**Anfrage**
```
POST /api/arxiv/nft
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5,
  "nftOptions": {
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 5,
  "withDOI": 3,
  "createdNFTs": 3,
  "failed": 0,
  "nfts": [
    {
      "publication": {
        "id": "pub-789012",
        "title": "Quantum Computing Applications",
        "authors": ["Dr. John Doe", "Prof. Jane Smith"],
        "abstract": "This study explores...",
        "doi": "10.12345/example.doi"
      },
      "nft": {
        "tokenId": "42",
        "metadata": {
          "name": "Quantum Computing Applications",
          "description": "This study explores...",
          "external_url": "https://arxiv.org/abs/2304.12345",
          "attributes": [
            { "trait_type": "Type", "value": "Scientific Paper" },
            { "trait_type": "Source", "value": "arXiv" },
            { "trait_type": "arXiv ID", "value": "2304.12345" },
            { "trait_type": "DOI", "value": "10.12345/example.doi" },
            { "trait_type": "Category", "value": "quant-ph" },
            { "trait_type": "Authors", "value": "Dr. John Doe, Prof. Jane Smith" },
            { "trait_type": "Publication Date", "value": "2023-04-15" }
          ]
        }
      },
      "tokenId": "42"
    },
    // Weitere NFTs...
  ]
}
```

### Statistiken abrufen

**Anfrage**
```
GET /api/arxiv/stats
```

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "stats": {
    "importedPapers": 42,
    "createdNFTs": 15,
    "failedImports": 3,
    "papersByCategory": {
      "cs.AI": 12,
      "quant-ph": 8,
      "physics": 22
    },
    "apiStats": {
      "totalRequests": 150,
      "successfulRequests": 147,
      "failedRequests": 3,
      "retries": 5,
      "requestsByEndpoint": {
        "query": 150
      },
      "successRate": "98.00%"
    },
    "successRate": "92.86%"
  }
}
```

## arXiv-Kategorien

Die arXiv-Integration unterstützt alle arXiv-Kategorien, darunter:

- **Physik**: astro-ph, cond-mat, gr-qc, hep-ex, hep-lat, hep-ph, hep-th, math-ph, nlin, nucl-ex, nucl-th, physics, quant-ph
- **Mathematik**: math
- **Informatik**: cs.AI, cs.AR, cs.CC, cs.CE, cs.CG, cs.CL, cs.CR, cs.CV, cs.CY, cs.DB, cs.DC, cs.DL, cs.DM, cs.DS, cs.ET, cs.FL, cs.GL, cs.GR, cs.GT, cs.HC, cs.IR, cs.IT, cs.LG, cs.LO, cs.MA, cs.MM, cs.MS, cs.NA, cs.NE, cs.NI, cs.OS, cs.PF, cs.PL, cs.RO, cs.SC, cs.SD, cs.SE, cs.SI, cs.SY
- **Quantitative Biologie**: q-bio
- **Quantitative Finanzen**: q-fin
- **Statistik**: stat
- **Elektrotechnik und Systemwissenschaft**: eess
- **Wirtschaftswissenschaften**: econ

Eine vollständige Liste der Kategorien und Unterkategorien finden Sie auf der [arXiv-Taxonomie-Seite](https://arxiv.org/category_taxonomy).

## Nutzungshinweise

Bei der Verwendung der arXiv-Integration sind folgende Punkte zu beachten:

1. **API-Limits**: Die arXiv API hat Ratenbegrenzungen. Es wird empfohlen, nicht mehr als eine Anfrage alle 3 Sekunden zu stellen.

2. **Urheberrecht**: Die meisten arXiv-Publikationen stehen unter der "arXiv non-exclusive license to distribute", die keine Weiterverbreitung erlaubt. Stellen Sie sicher, dass Sie die Urheberrechte respektieren.

3. **DOIs und NFTs**: Nur Publikationen mit DOIs können als NFTs tokenisiert werden. Nicht alle arXiv-Publikationen haben DOIs.

4. **Metadaten**: Die arXiv API liefert nur Metadaten, keinen Volltext. Der Volltext kann über die PDF-URL abgerufen werden, aber dies ist nicht Teil der API-Integration.

5. **Kategorien**: Bei der Suche nach Kategorien können sowohl Hauptkategorien (z.B. "physics") als auch Unterkategorien (z.B. "cs.AI") verwendet werden.

## Beispiele

### Curl-Beispiel: Suche nach Publikationen

```bash
curl -X GET "http://localhost:3000/api/arxiv/search?query=quantum+computing&category=quant-ph&maxResults=5"
```

### Curl-Beispiel: Publikation importieren

```bash
curl -X POST "http://localhost:3000/api/arxiv/import/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..."
```

### Curl-Beispiel: NFT erstellen

```bash
curl -X POST "http://localhost:3000/api/arxiv/nft/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..." \
  -H "Content-Type: application/json" \
  -d '{
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }'
```  }'
```````````````  }'
````````````  }'
```  }'
``````
Die arXiv-Integration bietet folgende Funktionen:

- Suche nach wissenschaftlichen Publikationen in arXiv
- Abruf von Publikationsdetails anhand der arXiv-ID
- Import von arXiv-Publikationen in DeSci-Scholar
- Erstellung von NFTs für arXiv-Publikationen mit DOIs
- Abruf der neuesten Publikationen in bestimmten Kategorien
- Statistiken zur arXiv-Integration

## API-Endpunkte

### Suche nach Publikationen

**Anfrage**
```
GET /api/arxiv/search
```

**Query-Parameter**
- `query` - Allgemeiner Suchbegriff
- `title` - Suche im Titel
- `author` - Suche nach Autor
- `category` - Suche nach Kategorie (z.B. "physics", "cs.AI")
- `id` - Suche nach arXiv-ID
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 10)
- `start` - Startposition für Paginierung (Standard: 0)
- `sortBy` - Sortierkriterium ("relevance", "lastUpdatedDate", "submittedDate")
- `sortOrder` - Sortierreihenfolge ("ascending", "descending")

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalResults": 42,
  "startIndex": 0,
  "itemsPerPage": 10,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikationsdetails abrufen

**Anfrage**
```
GET /api/arxiv/paper/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Antwort (200 OK)**
```json
{
  "success": true,
  "paper": {
    "id": "2304.12345",
    "title": "Anwendung von KI in der Medizin",
    "abstract": "Diese Studie untersucht...",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "categories": ["cs.AI", "cs.LG"],
    "primaryCategory": "cs.AI",
    "published": "2023-04-15T14:22:10Z",
    "updated": "2023-04-16T09:15:30Z",
    "doi": "10.12345/example.doi",
    "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
    "comment": "20 pages, 8 figures",
    "pdfUrl": "https://arxiv.org/pdf/2304.12345",
    "arxivUrl": "https://arxiv.org/abs/2304.12345"
  }
}
```

### Neueste Publikationen in einer Kategorie abrufen

**Anfrage**
```
GET /api/arxiv/recent/:category
```

**Parameter**
- `category` - arXiv-Kategorie (z.B. "cs.AI", "physics")

**Query-Parameter**
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 30)

**Antwort (200 OK)**
```json
{
  "success": true,
  "category": "cs.AI",
  "categoryName": "Künstliche Intelligenz",
  "totalResults": 42,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikation importieren

**Anfrage**
```
POST /api/arxiv/import/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "keywords": ["Künstliche Intelligenz", "Machine Learning"],
    "publishedDate": "2023-04-15T14:22:10Z",
    "doi": "10.12345/example.doi",
    "license": "arXiv non-exclusive license to distribute",
    "status": "published",
    "arxivMetadata": {
      "id": "2304.12345",
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "updated": "2023-04-16T09:15:30Z",
      "comment": "20 pages, 8 figures",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "url": "https://arxiv.org/abs/2304.12345",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345"
    },
    "storage": {
      "ipfs": "QmXa12S5sdf87gh4j56k7l...",
      "bittorrent": "a1b2c3d4e5f6...",
      "timestamp": "2023-05-10T08:12:45Z"
    }
  },
  "message": "Publikation erfolgreich importiert"
}
```

### Mehrere Publikationen importieren

**Anfrage**
```
POST /api/arxiv/import
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 42,
  "imported": 5,
  "failed": 0,
  "publications": [
    {
      "id": "pub-789012",
      "title": "Quantum Computing Applications",
      "authors": ["Dr. John Doe", "Prof. Jane Smith"],
      "abstract": "This study explores...",
      "keywords": ["Quantum Computing", "Quantum Physics"],
      "publishedDate": "2023-04-15T14:22:10Z",
      "doi": "10.12345/example.doi",
      "license": "arXiv non-exclusive license to distribute",
      "status": "published",
      "arxivMetadata": {
        "id": "2304.12345",
        "categories": ["quant-ph", "cs.ET"],
        "primaryCategory": "quant-ph",
        "updated": "2023-04-16T09:15:30Z",
        "comment": "15 pages, 5 figures",
        "journalRef": "Journal of Quantum Computing, Vol. 10, pp. 45-60",
        "url": "https://arxiv.org/abs/2304.12345",
        "pdfUrl": "https://arxiv.org/pdf/2304.12345"
      }
    },
    // Weitere Publikationen...
  ]
}
```

### NFT für eine Publikation erstellen

**Anfrage**
```
POST /api/arxiv/nft/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Body**
```json
{
  "recipient": "0x1234567890abcdef1234567890abcdef12345678",
  "royaltyPercentage": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "doi": "10.12345/example.doi"
  },
  "nft": {
    "tokenId": "42",
    "metadata": {
      "name": "Anwendung von KI in der Medizin",
      "description": "Diese Studie untersucht...",
      "external_url": "https://arxiv.org/abs/2304.12345",
      "attributes": [
        { "trait_type": "Type", "value": "Scientific Paper" },
        { "trait_type": "Source", "value": "arXiv" },
        { "trait_type": "arXiv ID", "value": "2304.12345" },
        { "trait_type": "DOI", "value": "10.12345/example.doi" },
        { "trait_type": "Category", "value": "cs.AI" },
        { "trait_type": "Authors", "value": "Dr. Maria Schmidt, Prof. Hans Müller" },
        { "trait_type": "Publication Date", "value": "2023-04-15" }
      ]
    }
  },
  "tokenId": "42",
  "transactionHash": "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
  "message": "NFT erfolgreich erstellt"
}
```

### NFTs für mehrere Publikationen erstellen

**Anfrage**
```
POST /api/arxiv/nft
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5,
  "nftOptions": {
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 5,
  "withDOI": 3,
  "createdNFTs": 3,
  "failed": 0,
  "nfts": [
    {
      "publication": {
        "id": "pub-789012",
        "title": "Quantum Computing Applications",
        "authors": ["Dr. John Doe", "Prof. Jane Smith"],
        "abstract": "This study explores...",
        "doi": "10.12345/example.doi"
      },
      "nft": {
        "tokenId": "42",
        "metadata": {
          "name": "Quantum Computing Applications",
          "description": "This study explores...",
          "external_url": "https://arxiv.org/abs/2304.12345",
          "attributes": [
            { "trait_type": "Type", "value": "Scientific Paper" },
            { "trait_type": "Source", "value": "arXiv" },
            { "trait_type": "arXiv ID", "value": "2304.12345" },
            { "trait_type": "DOI", "value": "10.12345/example.doi" },
            { "trait_type": "Category", "value": "quant-ph" },
            { "trait_type": "Authors", "value": "Dr. John Doe, Prof. Jane Smith" },
            { "trait_type": "Publication Date", "value": "2023-04-15" }
          ]
        }
      },
      "tokenId": "42"
    },
    // Weitere NFTs...
  ]
}
```

### Statistiken abrufen

**Anfrage**
```
GET /api/arxiv/stats
```

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "stats": {
    "importedPapers": 42,
    "createdNFTs": 15,
    "failedImports": 3,
    "papersByCategory": {
      "cs.AI": 12,
      "quant-ph": 8,
      "physics": 22
    },
    "apiStats": {
      "totalRequests": 150,
      "successfulRequests": 147,
      "failedRequests": 3,
      "retries": 5,
      "requestsByEndpoint": {
        "query": 150
      },
      "successRate": "98.00%"
    },
    "successRate": "92.86%"
  }
}
```

## arXiv-Kategorien

Die arXiv-Integration unterstützt alle arXiv-Kategorien, darunter:

- **Physik**: astro-ph, cond-mat, gr-qc, hep-ex, hep-lat, hep-ph, hep-th, math-ph, nlin, nucl-ex, nucl-th, physics, quant-ph
- **Mathematik**: math
- **Informatik**: cs.AI, cs.AR, cs.CC, cs.CE, cs.CG, cs.CL, cs.CR, cs.CV, cs.CY, cs.DB, cs.DC, cs.DL, cs.DM, cs.DS, cs.ET, cs.FL, cs.GL, cs.GR, cs.GT, cs.HC, cs.IR, cs.IT, cs.LG, cs.LO, cs.MA, cs.MM, cs.MS, cs.NA, cs.NE, cs.NI, cs.OS, cs.PF, cs.PL, cs.RO, cs.SC, cs.SD, cs.SE, cs.SI, cs.SY
- **Quantitative Biologie**: q-bio
- **Quantitative Finanzen**: q-fin
- **Statistik**: stat
- **Elektrotechnik und Systemwissenschaft**: eess
- **Wirtschaftswissenschaften**: econ

Eine vollständige Liste der Kategorien und Unterkategorien finden Sie auf der [arXiv-Taxonomie-Seite](https://arxiv.org/category_taxonomy).

## Nutzungshinweise

Bei der Verwendung der arXiv-Integration sind folgende Punkte zu beachten:

1. **API-Limits**: Die arXiv API hat Ratenbegrenzungen. Es wird empfohlen, nicht mehr als eine Anfrage alle 3 Sekunden zu stellen.

2. **Urheberrecht**: Die meisten arXiv-Publikationen stehen unter der "arXiv non-exclusive license to distribute", die keine Weiterverbreitung erlaubt. Stellen Sie sicher, dass Sie die Urheberrechte respektieren.

3. **DOIs und NFTs**: Nur Publikationen mit DOIs können als NFTs tokenisiert werden. Nicht alle arXiv-Publikationen haben DOIs.

4. **Metadaten**: Die arXiv API liefert nur Metadaten, keinen Volltext. Der Volltext kann über die PDF-URL abgerufen werden, aber dies ist nicht Teil der API-Integration.

5. **Kategorien**: Bei der Suche nach Kategorien können sowohl Hauptkategorien (z.B. "physics") als auch Unterkategorien (z.B. "cs.AI") verwendet werden.

## Beispiele

### Curl-Beispiel: Suche nach Publikationen

```bash
curl -X GET "http://localhost:3000/api/arxiv/search?query=quantum+computing&category=quant-ph&maxResults=5"
```

### Curl-Beispiel: Publikation importieren

```bash
curl -X POST "http://localhost:3000/api/arxiv/import/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..."
```

### Curl-Beispiel: NFT erstellen

```bash
curl -X POST "http://localhost:3000/api/arxiv/nft/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..." \
  -H "Content-Type: application/json" \
  -d '{
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }'
```  }'
``````````````````  }'
```  }'
``````
Die arXiv-Integration bietet folgende Funktionen:

- Suche nach wissenschaftlichen Publikationen in arXiv
- Abruf von Publikationsdetails anhand der arXiv-ID
- Import von arXiv-Publikationen in DeSci-Scholar
- Erstellung von NFTs für arXiv-Publikationen mit DOIs
- Abruf der neuesten Publikationen in bestimmten Kategorien
- Statistiken zur arXiv-Integration

## API-Endpunkte

### Suche nach Publikationen

**Anfrage**
```
GET /api/arxiv/search
```

**Query-Parameter**
- `query` - Allgemeiner Suchbegriff
- `title` - Suche im Titel
- `author` - Suche nach Autor
- `category` - Suche nach Kategorie (z.B. "physics", "cs.AI")
- `id` - Suche nach arXiv-ID
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 10)
- `start` - Startposition für Paginierung (Standard: 0)
- `sortBy` - Sortierkriterium ("relevance", "lastUpdatedDate", "submittedDate")
- `sortOrder` - Sortierreihenfolge ("ascending", "descending")

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalResults": 42,
  "startIndex": 0,
  "itemsPerPage": 10,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikationsdetails abrufen

**Anfrage**
```
GET /api/arxiv/paper/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Antwort (200 OK)**
```json
{
  "success": true,
  "paper": {
    "id": "2304.12345",
    "title": "Anwendung von KI in der Medizin",
    "abstract": "Diese Studie untersucht...",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "categories": ["cs.AI", "cs.LG"],
    "primaryCategory": "cs.AI",
    "published": "2023-04-15T14:22:10Z",
    "updated": "2023-04-16T09:15:30Z",
    "doi": "10.12345/example.doi",
    "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
    "comment": "20 pages, 8 figures",
    "pdfUrl": "https://arxiv.org/pdf/2304.12345",
    "arxivUrl": "https://arxiv.org/abs/2304.12345"
  }
}
```

### Neueste Publikationen in einer Kategorie abrufen

**Anfrage**
```
GET /api/arxiv/recent/:category
```

**Parameter**
- `category` - arXiv-Kategorie (z.B. "cs.AI", "physics")

**Query-Parameter**
- `maxResults` - Maximale Anzahl von Ergebnissen (Standard: 30)

**Antwort (200 OK)**
```json
{
  "success": true,
  "category": "cs.AI",
  "categoryName": "Künstliche Intelligenz",
  "totalResults": 42,
  "papers": [
    {
      "id": "2304.12345",
      "title": "Anwendung von KI in der Medizin",
      "abstract": "Diese Studie untersucht...",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "published": "2023-04-15T14:22:10Z",
      "updated": "2023-04-16T09:15:30Z",
      "doi": "10.12345/example.doi",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "comment": "20 pages, 8 figures",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345",
      "arxivUrl": "https://arxiv.org/abs/2304.12345"
    },
    // Weitere Publikationen...
  ]
}
```

### Publikation importieren

**Anfrage**
```
POST /api/arxiv/import/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "keywords": ["Künstliche Intelligenz", "Machine Learning"],
    "publishedDate": "2023-04-15T14:22:10Z",
    "doi": "10.12345/example.doi",
    "license": "arXiv non-exclusive license to distribute",
    "status": "published",
    "arxivMetadata": {
      "id": "2304.12345",
      "categories": ["cs.AI", "cs.LG"],
      "primaryCategory": "cs.AI",
      "updated": "2023-04-16T09:15:30Z",
      "comment": "20 pages, 8 figures",
      "journalRef": "Journal of AI in Medicine, Vol. 42, pp. 123-145",
      "url": "https://arxiv.org/abs/2304.12345",
      "pdfUrl": "https://arxiv.org/pdf/2304.12345"
    },
    "storage": {
      "ipfs": "QmXa12S5sdf87gh4j56k7l...",
      "bittorrent": "a1b2c3d4e5f6...",
      "timestamp": "2023-05-10T08:12:45Z"
    }
  },
  "message": "Publikation erfolgreich importiert"
}
```

### Mehrere Publikationen importieren

**Anfrage**
```
POST /api/arxiv/import
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 42,
  "imported": 5,
  "failed": 0,
  "publications": [
    {
      "id": "pub-789012",
      "title": "Quantum Computing Applications",
      "authors": ["Dr. John Doe", "Prof. Jane Smith"],
      "abstract": "This study explores...",
      "keywords": ["Quantum Computing", "Quantum Physics"],
      "publishedDate": "2023-04-15T14:22:10Z",
      "doi": "10.12345/example.doi",
      "license": "arXiv non-exclusive license to distribute",
      "status": "published",
      "arxivMetadata": {
        "id": "2304.12345",
        "categories": ["quant-ph", "cs.ET"],
        "primaryCategory": "quant-ph",
        "updated": "2023-04-16T09:15:30Z",
        "comment": "15 pages, 5 figures",
        "journalRef": "Journal of Quantum Computing, Vol. 10, pp. 45-60",
        "url": "https://arxiv.org/abs/2304.12345",
        "pdfUrl": "https://arxiv.org/pdf/2304.12345"
      }
    },
    // Weitere Publikationen...
  ]
}
```

### NFT für eine Publikation erstellen

**Anfrage**
```
POST /api/arxiv/nft/:id
```

**Parameter**
- `id` - arXiv-ID der Publikation

**Authentifizierung erforderlich**

**Body**
```json
{
  "recipient": "0x1234567890abcdef1234567890abcdef12345678",
  "royaltyPercentage": 5
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "doi": "10.12345/example.doi"
  },
  "nft": {
    "tokenId": "42",
    "metadata": {
      "name": "Anwendung von KI in der Medizin",
      "description": "Diese Studie untersucht...",
      "external_url": "https://arxiv.org/abs/2304.12345",
      "attributes": [
        { "trait_type": "Type", "value": "Scientific Paper" },
        { "trait_type": "Source", "value": "arXiv" },
        { "trait_type": "arXiv ID", "value": "2304.12345" },
        { "trait_type": "DOI", "value": "10.12345/example.doi" },
        { "trait_type": "Category", "value": "cs.AI" },
        { "trait_type": "Authors", "value": "Dr. Maria Schmidt, Prof. Hans Müller" },
        { "trait_type": "Publication Date", "value": "2023-04-15" }
      ]
    }
  },
  "tokenId": "42",
  "transactionHash": "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
  "message": "NFT erfolgreich erstellt"
}
```

### NFTs für mehrere Publikationen erstellen

**Anfrage**
```
POST /api/arxiv/nft
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "query": "quantum computing",
  "category": "quant-ph",
  "limit": 5,
  "nftOptions": {
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "totalFound": 5,
  "withDOI": 3,
  "createdNFTs": 3,
  "failed": 0,
  "nfts": [
    {
      "publication": {
        "id": "pub-789012",
        "title": "Quantum Computing Applications",
        "authors": ["Dr. John Doe", "Prof. Jane Smith"],
        "abstract": "This study explores...",
        "doi": "10.12345/example.doi"
      },
      "nft": {
        "tokenId": "42",
        "metadata": {
          "name": "Quantum Computing Applications",
          "description": "This study explores...",
          "external_url": "https://arxiv.org/abs/2304.12345",
          "attributes": [
            { "trait_type": "Type", "value": "Scientific Paper" },
            { "trait_type": "Source", "value": "arXiv" },
            { "trait_type": "arXiv ID", "value": "2304.12345" },
            { "trait_type": "DOI", "value": "10.12345/example.doi" },
            { "trait_type": "Category", "value": "quant-ph" },
            { "trait_type": "Authors", "value": "Dr. John Doe, Prof. Jane Smith" },
            { "trait_type": "Publication Date", "value": "2023-04-15" }
          ]
        }
      },
      "tokenId": "42"
    },
    // Weitere NFTs...
  ]
}
```

### Statistiken abrufen

**Anfrage**
```
GET /api/arxiv/stats
```

**Authentifizierung erforderlich**

**Antwort (200 OK)**
```json
{
  "success": true,
  "stats": {
    "importedPapers": 42,
    "createdNFTs": 15,
    "failedImports": 3,
    "papersByCategory": {
      "cs.AI": 12,
      "quant-ph": 8,
      "physics": 22
    },
    "apiStats": {
      "totalRequests": 150,
      "successfulRequests": 147,
      "failedRequests": 3,
      "retries": 5,
      "requestsByEndpoint": {
        "query": 150
      },
      "successRate": "98.00%"
    },
    "successRate": "92.86%"
  }
}
```

## arXiv-Kategorien

Die arXiv-Integration unterstützt alle arXiv-Kategorien, darunter:

- **Physik**: astro-ph, cond-mat, gr-qc, hep-ex, hep-lat, hep-ph, hep-th, math-ph, nlin, nucl-ex, nucl-th, physics, quant-ph
- **Mathematik**: math
- **Informatik**: cs.AI, cs.AR, cs.CC, cs.CE, cs.CG, cs.CL, cs.CR, cs.CV, cs.CY, cs.DB, cs.DC, cs.DL, cs.DM, cs.DS, cs.ET, cs.FL, cs.GL, cs.GR, cs.GT, cs.HC, cs.IR, cs.IT, cs.LG, cs.LO, cs.MA, cs.MM, cs.MS, cs.NA, cs.NE, cs.NI, cs.OS, cs.PF, cs.PL, cs.RO, cs.SC, cs.SD, cs.SE, cs.SI, cs.SY
- **Quantitative Biologie**: q-bio
- **Quantitative Finanzen**: q-fin
- **Statistik**: stat
- **Elektrotechnik und Systemwissenschaft**: eess
- **Wirtschaftswissenschaften**: econ

Eine vollständige Liste der Kategorien und Unterkategorien finden Sie auf der [arXiv-Taxonomie-Seite](https://arxiv.org/category_taxonomy).

## Nutzungshinweise

Bei der Verwendung der arXiv-Integration sind folgende Punkte zu beachten:

1. **API-Limits**: Die arXiv API hat Ratenbegrenzungen. Es wird empfohlen, nicht mehr als eine Anfrage alle 3 Sekunden zu stellen.

2. **Urheberrecht**: Die meisten arXiv-Publikationen stehen unter der "arXiv non-exclusive license to distribute", die keine Weiterverbreitung erlaubt. Stellen Sie sicher, dass Sie die Urheberrechte respektieren.

3. **DOIs und NFTs**: Nur Publikationen mit DOIs können als NFTs tokenisiert werden. Nicht alle arXiv-Publikationen haben DOIs.

4. **Metadaten**: Die arXiv API liefert nur Metadaten, keinen Volltext. Der Volltext kann über die PDF-URL abgerufen werden, aber dies ist nicht Teil der API-Integration.

5. **Kategorien**: Bei der Suche nach Kategorien können sowohl Hauptkategorien (z.B. "physics") als auch Unterkategorien (z.B. "cs.AI") verwendet werden.

## Beispiele

### Curl-Beispiel: Suche nach Publikationen

```bash
curl -X GET "http://localhost:3000/api/arxiv/search?query=quantum+computing&category=quant-ph&maxResults=5"
```

### Curl-Beispiel: Publikation importieren

```bash
curl -X POST "http://localhost:3000/api/arxiv/import/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..."
```

### Curl-Beispiel: NFT erstellen

```bash
curl -X POST "http://localhost:3000/api/arxiv/nft/2304.12345" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..." \
  -H "Content-Type: application/json" \
  -d '{
    "recipient": "0x1234567890abcdef1234567890abcdef12345678",
    "royaltyPercentage": 5
  }'
```  }'
```