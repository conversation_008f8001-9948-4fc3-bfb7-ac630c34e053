version: '3.8'

services:
  # MongoDB
  mongodb:
    image: mongo:5.0
    container_name: desci-scholar-mongodb
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password
      - MONGO_INITDB_DATABASE=desci-scholar
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js:ro
    restart: unless-stopped
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongosh localhost:27017/desci-scholar --quiet
      interval: 10s
      timeout: 10s
      retries: 5
      start_period: 40s

  # MongoDB Express (Web-basierte MongoDB-Admin-Oberfläche)
  mongo-express:
    image: mongo-express
    container_name: desci-scholar-mongo-express
    environment:
      - ME_CONFIG_MONGODB_ADMINUSERNAME=admin
      - ME_CONFIG_MONGODB_ADMINPASSWORD=password
      - ME_CONFIG_MONGODB_SERVER=mongodb
      - ME_CONFIG_BASICAUTH_USERNAME=admin
      - ME_CONFIG_BASICAUTH_PASSWORD=password
    ports:
      - "8081:8081"
    depends_on:
      - mongodb
    restart: unless-stopped

volumes:
  mongodb_data: 