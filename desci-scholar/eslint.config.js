import eslintRecommended from '@eslint/js';
import eslint<PERSON>onfig<PERSON>rettier from 'eslint-config-prettier';
import typescriptParser from '@typescript-eslint/parser';

export default [
  eslintRecommended.configs.recommended,
  eslintConfigPrettier,
  {
    languageOptions: {
      parser: typescriptParser,
      parserOptions: {
        sourceType: 'module',
        ecmaVersion: 2022,
        jsx: true,
      },
      globals: {
        es2022: true,
        jest: true,
        node: true,
        console: true,
        Buffer: true,
        __dirname: true,
        __filename: true,
        exports: true,
        module: true,
        require: true,
        process: true,
        setTimeout: true,
        setInterval: true,
        clearTimeout: true,
        clearInterval: true,
        setImmediate: true,
        clearImmediate: true,
        window: true,
        document: true,
        localStorage: true,
        fetch: true,
        URLSearchParams: true,
        URL: true,
        crypto: true,
        fs: true,
        navigator: true,
        alert: true,
        Blob: true,
        AbortController: true,
      },
    },
    rules: {
      'no-console': 'off',
      'no-unused-vars': ['error', { argsIgnorePattern: '^_' }],
      'prefer-const': 'error',
      'quotes': ['warn', 'single', { avoidEscape: true }],
      'semi': ['error', 'always'],
    },
  },
];
