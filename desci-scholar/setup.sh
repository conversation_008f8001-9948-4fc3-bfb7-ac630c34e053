#!/bin/bash

# DeSci-Scholar Einrichtungsskript
# Dieses Skript installiert alle Abhängigkeiten und richtet die Umgebung für DeSci-Scholar ein

echo "🧪 DeSci-Scholar - Setupskript 🧪"
echo "=================================="
echo

# Prüfe, ob Node.js installiert ist
if ! command -v node &> /dev/null; then
    echo "❌ Node.js ist nicht installiert. Bitte installiere Node.js (Version ≥ 16)."
    echo "   Download: https://nodejs.org/"
    exit 1
fi

# Prüfe Node.js-Version
NODE_VERSION=$(node -v | cut -d'v' -f2)
NODE_MAJOR_VERSION=$(echo $NODE_VERSION | cut -d'.' -f1)

if [ $NODE_MAJOR_VERSION -lt 16 ]; then
    echo "❌ Node.js Version ist zu alt: $NODE_VERSION"
    echo "   DeSci-Scholar ben<PERSON>tigt <PERSON>.js Version 16 oder höher."
    exit 1
fi

echo "✅ Node.js Version: $NODE_VERSION"

# Prüfe, ob npm installiert ist
if ! command -v npm &> /dev/null; then
    echo "❌ npm ist nicht installiert."
    exit 1
fi

echo "✅ npm Version: $(npm -v)"

# Erstelle .env Datei, falls nicht vorhanden
if [ ! -f .env ]; then
    echo "📝 Erstelle .env Datei aus Vorlage..."
    cp .env.example .env
    echo "✅ .env erstellt. Bitte passe die Werte in der .env-Datei an, falls nötig."
else
    echo "📝 .env existiert bereits."
fi

# Installiere Abhängigkeiten
echo "📦 Installiere Abhängigkeiten..."
npm install

# Erstelle notwendige Verzeichnisse
echo "📁 Erstelle notwendige Verzeichnisse..."
mkdir -p uploads downloads logs

# Setze Berechtigungen für Ausführungsdateien
chmod +x setup.sh

echo
echo "✅ DeSci-Scholar wurde erfolgreich eingerichtet!"
echo
echo "Starte den Entwicklungsserver mit:"
echo "npm run dev"
echo
echo "Oder führe die Beispiele aus:"
echo "npm run example:storage"
echo "npm run example:publication"
echo "npm run example:controller"
echo
echo "Weitere Informationen findest du in der README.md"
echo "==================================" 