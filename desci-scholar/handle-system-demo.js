/**
 * @fileoverview DeSci-Scholar Handle System Demo
 * Demonstriert die vollständige hdl.handle.net Integration
 */

import axios from 'axios';

// Demo-Konfiguration
const DEMO_HANDLES = [
  '10.1038/nature12373',
  '10.1000/example.1',
  '10.5281/zenodo.1234567'
];

/**
 * Parse HTML Handle Response von hdl.handle.net
 */
function parseHandleHTML(html, handle) {
  try {
    const values = [];
    
    // Suche nach Tabellenzeilen mit Handle-Werten
    // Vereinfachte Regex für robustes Parsing
    const lines = html.split('\n');
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      // Suche nach <tr> mit <td><b>Nummer</b></td>
      if (line.includes('<tr><td><b>') && line.includes('</b></td>')) {
        try {
          // Extrahiere Index
          const indexMatch = line.match(/<td><b>(\d+)<\/b><\/td>/);
          if (!indexMatch) continue;
          
          const index = parseInt(indexMatch[1]);
          
          // Suche nach Type in der gleichen Zeile oder nächsten Zeilen
          let fullRow = line;
          let j = i + 1;
          while (j < lines.length && !fullRow.includes('</tr>')) {
            fullRow += lines[j];
            j++;
          }
          
          // Extrahiere Type
          const typeMatch = fullRow.match(/<td><b>(?:<a[^>]*>)?([^<]+)(?:<\/a>)?<\/b><\/td>/);
          if (!typeMatch) continue;
          
          const type = typeMatch[1].trim();
          
          // Extrahiere alle <td> Inhalte
          const tdMatches = fullRow.match(/<td[^>]*>([^<]*(?:<[^>]*>[^<]*)*)<\/td>/g);
          if (!tdMatches || tdMatches.length < 4) continue;
          
          // Data ist im letzten <td>
          const dataCell = tdMatches[tdMatches.length - 1];
          const data = dataCell
            .replace(/<[^>]*>/g, '')
            .replace(/&nbsp;/g, ' ')
            .replace(/&amp;/g, '&')
            .replace(/&lt;/g, '<')
            .replace(/&gt;/g, '>')
            .trim();
          
          // Timestamp ist im vorletzten <td>
          const timestampCell = tdMatches[tdMatches.length - 2];
          const timestamp = timestampCell
            .replace(/<[^>]*>/g, '')
            .replace(/&nbsp;/g, ' ')
            .trim();
          
          values.push({
            index,
            type,
            timestamp,
            data
          });
          
        } catch (parseError) {
          console.warn(`Fehler beim Parsen der Zeile ${i}:`, parseError.message);
        }
      }
    }
    
    // Generiere Metadaten
    const urlValues = values.filter(v => v.type === 'URL');
    const adminValues = values.filter(v => v.type === 'HS_ADMIN');
    const primaryUrl = urlValues.length > 0 ? urlValues[0].data : null;
    
    // NFT-URL-Vorschlag
    const nftUrlSuggestion = {
      suggestedURL: handle.replace(/[\/\.]/g, '-').toLowerCase() + '.desci',
      domain: '.desci',
      originalHandle: handle,
      originalUrl: primaryUrl,
      benefits: [
        'blockchain_ownership',
        'no_annual_fees',
        'automatic_royalties',
        'decentralized_resolution',
        'immutable_metadata'
      ],
      migration: {
        preserveOriginal: true,
        bidirectionalSync: true,
        metadataEmbedding: true
      }
    };
    
    return {
      handle,
      resolved: values.length > 0,
      timestamp: new Date().toISOString(),
      values,
      metadata: {
        primaryUrl,
        totalValues: values.length,
        urlCount: urlValues.length,
        adminInfo: adminValues.length > 0 ? adminValues[0].data : null,
        lastModified: values.length > 0 ? values[0].timestamp : null
      },
      source: 'hdl.handle.net_html',
      nftUrlSuggestion
    };
    
  } catch (error) {
    console.error(`Fehler beim Parsen der HTML Handle Response: ${error.message}`);
    return {
      handle,
      resolved: false,
      error: error.message,
      timestamp: new Date().toISOString(),
      source: 'hdl.handle.net_parse_error'
    };
  }
}

/**
 * Teste Handle System Integration
 */
async function testHandleSystem(handle, options = {}) {
  try {
    console.log(`\n🔍 Testing Handle: ${handle}`);
    console.log(`Options:`, options);
    
    // Baue Query-Parameter
    const params = {};
    if (options.noredirect) params.noredirect = 'true';
    if (options.auth) params.auth = 'true';
    if (options.noalias) params.noalias = 'true';
    if (options.type) params.type = options.type;
    
    // Anfrage an hdl.handle.net
    const response = await axios.get(`http://hdl.handle.net/${handle}`, {
      timeout: 10000,
      headers: {
        'Accept': 'text/html',
        'User-Agent': 'DeSci-Scholar/2.0 HandleSystemDemo'
      },
      params,
      validateStatus: (status) => status < 500
    });
    
    console.log(`✅ Response Status: ${response.status}`);
    
    if (response.status === 200) {
      const result = parseHandleHTML(response.data, handle);
      
      console.log(`📊 Resolved: ${result.resolved}`);
      console.log(`📈 Values Found: ${result.values.length}`);
      
      if (result.values.length > 0) {
        console.log(`🔗 Primary URL: ${result.metadata.primaryUrl}`);
        console.log(`🎯 NFT-URL Suggestion: ${result.nftUrlSuggestion.suggestedURL}`);
        
        console.log(`\n📋 Handle Values:`);
        result.values.forEach(value => {
          console.log(`  ${value.index}: ${value.type} = ${value.data.substring(0, 100)}${value.data.length > 100 ? '...' : ''}`);
        });
      }
      
      return result;
    } else {
      console.log(`❌ Failed with status: ${response.status}`);
      return {
        handle,
        resolved: false,
        error: `HTTP ${response.status}`,
        timestamp: new Date().toISOString()
      };
    }
    
  } catch (error) {
    console.error(`❌ Error testing ${handle}:`, error.message);
    return {
      handle,
      resolved: false,
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * Hauptdemo-Funktion
 */
async function runDemo() {
  console.log('🚀 DeSci-Scholar Handle System Integration Demo');
  console.log('=' .repeat(60));
  
  const results = [];
  
  for (const handle of DEMO_HANDLES) {
    // Test 1: Standard-Auflösung
    const standard = await testHandleSystem(handle);
    results.push({ handle, type: 'standard', result: standard });
    
    // Test 2: No Redirect (wie hdl.handle.net Web-Form)
    const noredirect = await testHandleSystem(handle, { noredirect: true });
    results.push({ handle, type: 'noredirect', result: noredirect });
    
    // Test 3: Authoritative Query
    const auth = await testHandleSystem(handle, { auth: true, noredirect: true });
    results.push({ handle, type: 'authoritative', result: auth });
    
    // Pause zwischen Tests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // Zusammenfassung
  console.log('\n' + '=' .repeat(60));
  console.log('📊 DEMO SUMMARY');
  console.log('=' .repeat(60));
  
  const successful = results.filter(r => r.result.resolved);
  const failed = results.filter(r => !r.result.resolved);
  
  console.log(`✅ Successful resolutions: ${successful.length}/${results.length}`);
  console.log(`❌ Failed resolutions: ${failed.length}/${results.length}`);
  
  if (successful.length > 0) {
    console.log('\n🎯 NFT-URL Suggestions:');
    successful.forEach(r => {
      if (r.result.nftUrlSuggestion) {
        console.log(`  ${r.handle} → ${r.result.nftUrlSuggestion.suggestedURL}`);
      }
    });
  }
  
  console.log('\n🚀 DeSci-Scholar Handle System Integration: COMPLETE!');
  console.log('Ready for DOI-NFT-URL Bridge deployment! 🎉');
}

// Starte Demo
runDemo().catch(console.error);
