/**
 * Einfacher DeSci-Gate Server - DOI zu NFT Bridge
 */

import http from 'http';
import https from 'https';
import url from 'url';

const PORT = 3011;

// Handle System Integration
function checkHandleSystem(doi) {
  return new Promise((resolve) => {
    const handleUrl = `http://hdl.handle.net/${doi}`;

    const req = http.get(handleUrl, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        const resolved = data.includes('Handle Values for:') || res.statusCode === 302;
        resolve({
          success: true,
          resolved,
          handleUrl,
          statusCode: res.statusCode,
          redirectLocation: res.headers.location || null
        });
      });
    });

    req.on('error', (error) => {
      resolve({
        success: false,
        error: error.message,
        handleUrl
      });
    });

    req.setTimeout(10000, () => {
      req.destroy();
      resolve({
        success: false,
        error: 'Timeout',
        handleUrl
      });
    });
  });
}

// DOI zu NFT Transformation mit Handle System
async function doiToNft(doi) {
  const nftUrl = doi.replace(/[\/\.]/g, '-').toLowerCase() + '.desci';
  const hash = Buffer.from(doi + Date.now()).toString('base64').substring(0, 16);

  // Handle System prüfen
  const handleResult = await checkHandleSystem(doi);

  return {
    success: true,
    doi,
    nftUrl,
    hash,
    transformation: `${doi} → ${nftUrl}`,
    timestamp: new Date().toISOString(),

    // Handle System Integration
    handleSystem: handleResult,

    // Bridge Info
    bridge: {
      service: 'DeSci-Gate',
      version: '1.0.0',
      inspiration: {
        handleSystem: 'http://hdl.handle.net (1995)',
        signFirst: 'https://www.signfirst.com (2005)'
      },
      innovation: 'DOI-URLs → NFT-URLs (2024)'
    }
  };
}

// HTML Interface
const htmlInterface = `
<!DOCTYPE html>
<html>
<head>
    <title>DeSci-Gate: DOI zu NFT Bridge</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #fff; color: #000; }
        .container { max-width: 800px; margin: 0 auto; }
        h1 { border-bottom: 3px solid #000; padding-bottom: 10px; }
        .hero { background: #000; color: #fff; padding: 20px; margin: 20px 0; text-align: center; }
        .demo { background: #f0f0f0; padding: 20px; border: 2px solid #000; margin: 20px 0; }
        .btn { background: #000; color: #fff; padding: 10px 20px; border: none; cursor: pointer; margin: 5px; }
        .btn:hover { background: #333; }
        #result { background: #fff; border: 2px solid #000; padding: 20px; margin: 20px 0; font-family: monospace; white-space: pre-wrap; }
        .system { background: #f8f8f8; padding: 15px; margin: 10px 0; border-left: 3px solid #000; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 DeSci-Gate: DOI zu NFT Bridge</h1>
        
        <div class="hero">
            <h2>Die erste DOI-zu-NFT-URL Bridge der Welt</h2>
            <p>Inspiriert von SignFirst.com (2005) und Handle System (1995)</p>
        </div>
        
        <div class="demo">
            <h3>🔄 DOI → NFT Transformation</h3>
            <p>Testen Sie die Bridge:</p>
            
            <button class="btn" onclick="testDoi('10.1038/nature12373')">🧬 Nature Paper</button>
            <button class="btn" onclick="testDoi('10.1000/example.1')">📄 Example DOI</button>
            <button class="btn" onclick="testDoi('10.5281/zenodo.1234567')">📊 Zenodo Data</button>
        </div>
        
        <div class="system">
            <h4>🔗 Handle System Integration</h4>
            <p><strong>http://hdl.handle.net</strong> - Persistente Identifikatoren seit 1995</p>
        </div>
        
        <div class="system">
            <h4>🎯 SignFirst Inspiration</h4>
            <p><strong>https://www.signfirst.com</strong> - Hash-basierte Beweise seit 2005</p>
        </div>
        
        <div class="system">
            <h4>✨ DeSci-Gate Innovation</h4>
            <p><strong>DOI-URLs → NFT-URLs</strong> - Blockchain-Ownership für Wissenschaftler</p>
        </div>
        
        <div id="result">Klicken Sie einen Button zum Testen...</div>
    </div>

    <script>
        async function testDoi(doi) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = 'Transforming DOI: ' + doi + '...';
            
            try {
                const response = await fetch('/api/bridge?doi=' + encodeURIComponent(doi));
                const data = await response.json();
                resultDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                resultDiv.textContent = 'Error: ' + error.message;
            }
        }
    </script>
</body>
</html>
`;

// Server
const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const query = parsedUrl.query;
  
  // CORS Headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
  
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }
  
  // Routes
  if (path === '/') {
    res.writeHead(200, { 'Content-Type': 'text/html' });
    res.end(htmlInterface);
  }
  else if (path === '/api/bridge') {
    const doi = query.doi;

    if (!doi) {
      res.writeHead(400, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        error: 'DOI parameter required',
        usage: '/api/bridge?doi=10.1038/nature12373'
      }));
      return;
    }

    // Async DOI-NFT Bridge
    doiToNft(doi).then(result => {
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify(result, null, 2));
    }).catch(error => {
      res.writeHead(500, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        error: 'Bridge creation failed',
        message: error.message,
        doi
      }));
    });
  }
  else if (path === '/health') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      status: 'healthy',
      service: 'DeSci-Gate Simple Server',
      timestamp: new Date().toISOString()
    }));
  }
  else {
    res.writeHead(404, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      error: 'Not found',
      availableEndpoints: ['/', '/api/bridge', '/health']
    }));
  }
});

server.listen(PORT, () => {
  console.log(`🚀 DeSci-Gate Server läuft auf Port ${PORT}`);
  console.log(`🌐 Web-Interface: http://localhost:${PORT}`);
  console.log(`🔗 API: http://localhost:${PORT}/api/bridge?doi=10.1038/nature12373`);
  console.log(`✅ FUNKTIONIERT: DOI → NFT Bridge!`);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\\n🛑 Server wird beendet...');
  server.close(() => {
    console.log('✅ Server erfolgreich beendet');
    process.exit(0);
  });
});
