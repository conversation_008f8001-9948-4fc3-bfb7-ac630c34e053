/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  distDir: '.next',
  pageExtensions: ['js', 'jsx', 'ts', 'tsx'],
  
  // In Next.js 12+ sollte für benutzerdefinierte Pfade wie folgt konfiguriert werden
  // Hinweis: Die Angabe von Verzeichnissen im 'dir'-Objekt wird nicht unterstützt
  experimental: {
    // Falls alternative Verzeichnisse für Pages benötigt werden
    // Anmerkung: In der aktuellen Projektstruktur nutzen wir das Standard-pages-Verzeichnis
  },
  
  // Benutzerdefinierte Webpack-Konfiguration
  webpack: (config, { isServer }) => {
    // Hier können Sie Webpack-Konfigurationen anpassen
    return config;
  },
  
  // Richtiges Setzen der Umgebungsvariablen für den Client
  publicRuntimeConfig: {
    // Nur zur Laufzeit verfügbare Konfigurationen
    apiUrl: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000',
  },
  
  // Serverseiting verfügbare Konfigurationen
  serverRuntimeConfig: {
    // Serverseiting verfügbare Konfigurationen
    polkadotNodeUrl: process.env.POLKADOT_NODE_URL,
  }
}

module.exports = nextConfig
