// MongoDB Initialisierungsskript
// Erstellt Benutzer und Datenbank für DeSci-Scholar

// Verbindung zur Admin-Datenbank herstellen
db = db.getSiblingDB('admin');

// Prü<PERSON>, ob der desci-scholar <PERSON><PERSON>er bereits existiert
var userExists = db.getUser('desci-user');

if (!userExists) {
  // Erstelle Benutzer für die Anwendung mit Lese- und Schreibrechten
  db.createUser({
    user: 'desci-user',
    pwd: 'desci-password',
    roles: [
      {
        role: 'readWrite',
        db: 'desci-scholar'
      },
      {
        role: 'readWrite', 
        db: 'desci-scholar-test'
      }
    ]
  });
  
  print('Benutzer "desci-user" erfolgreich erstellt');
} else {
  print('Benutzer "desci-user" existiert bereits');
}

// Wechsle zur Anwendungsdatenbank
db = db.getSiblingDB('desci-scholar');

// Erstelle Sammlungen mit Validierung
db.createCollection('publications', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['title', 'authors', 'status', 'storageIds'],
      properties: {
        title: {
          bsonType: 'string',
          description: 'Titel der Publikation'
        },
        authors: {
          bsonType: 'array',
          description: 'Liste der Autoren',
          items: {
            bsonType: 'string'
          }
        },
        abstract: {
          bsonType: 'string',
          description: 'Kurzzusammenfassung'
        },
        keywords: {
          bsonType: 'array',
          description: 'Schlüsselwörter',
          items: {
            bsonType: 'string'
          }
        },
        status: {
          bsonType: 'string',
          description: 'Status der Publikation (draft, published, archived)',
          enum: ['draft', 'published', 'archived']
        },
        storageIds: {
          bsonType: 'object',
          description: 'Storage-IDs für verschiedene Protokolle'
        },
        doi: {
          bsonType: 'string',
          description: 'Digital Object Identifier'
        },
        version: {
          bsonType: 'string',
          description: 'Version der Publikation'
        }
      }
    }
  }
});

db.createCollection('users', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['username', 'email'],
      properties: {
        username: {
          bsonType: 'string',
          description: 'Benutzername'
        },
        email: {
          bsonType: 'string',
          description: 'E-Mail-Adresse'
        },
        fullName: {
          bsonType: 'string',
          description: 'Vollständiger Name'
        },
        roles: {
          bsonType: 'array',
          description: 'Benutzerrollen',
          items: {
            bsonType: 'string'
          }
        }
      }
    }
  }
});

db.createCollection('citations');
db.createCollection('stats');

// Indizes erstellen
db.publications.createIndex({ "title": "text", "abstract": "text", "keywords": "text" });
db.publications.createIndex({ "authors": 1 });
db.publications.createIndex({ "status": 1 });
db.publications.createIndex({ "doi": 1 }, { unique: true, sparse: true });
db.publications.createIndex({ "storageIds.ipfs": 1 }, { sparse: true });
db.publications.createIndex({ "storageIds.bittorrent": 1 }, { sparse: true });

db.users.createIndex({ "email": 1 }, { unique: true });
db.users.createIndex({ "username": 1 }, { unique: true });

db.citations.createIndex({ "sourceId": 1 });
db.citations.createIndex({ "targetId": 1 });

print('Sammlungen und Indizes für DeSci-Scholar erfolgreich erstellt');

// Testdatenbank vorbereiten
db = db.getSiblingDB('desci-scholar-test');

db.createCollection('publications');
db.createCollection('users');
db.createCollection('citations');
db.createCollection('stats');

// Gleiche Indizes für die Testdatenbank erstellen
db.publications.createIndex({ "title": "text", "abstract": "text", "keywords": "text" });
db.publications.createIndex({ "authors": 1 });
db.publications.createIndex({ "status": 1 });
db.publications.createIndex({ "doi": 1 }, { unique: true, sparse: true });
db.publications.createIndex({ "storageIds.ipfs": 1 }, { sparse: true });
db.publications.createIndex({ "storageIds.bittorrent": 1 }, { sparse: true });

db.users.createIndex({ "email": 1 }, { unique: true });
db.users.createIndex({ "username": 1 }, { unique: true });

db.citations.createIndex({ "sourceId": 1 });
db.citations.createIndex({ "targetId": 1 });

print('Testdatenbank für DeSci-Scholar erfolgreich vorbereitet'); 