# Beitragsrichtlinien für DeSci-Scholar

Vielen Dank für Ihr Interesse, zu DeSci-Scholar beizutragen! Als Open-Source- und Open-Science-Projekt leben wir von der Beteiligung der Community. Diese Richtlinien sollen Ihnen helfen, effektiv zum Projekt beizutragen.

## Open-Science-Prinzipien

DeSci-Scholar ist der Open-Science-Bewegung verpflichtet. Wir glauben an:

- **Transparenz**: Alle Aspekte des wissenschaftlichen Prozesses sollten offen und nachvollziehbar sein
- **Offenen Zugang**: Wissenschaftliche Erkenntnisse sollten frei zugänglich sein
- **Offene Daten**: Forschungsdaten sollten geteilt und wiederverwendbar sein
- **Offene Methoden**: Forschungsmethoden sollten transparent und reproduzierbar sein
- **Offene Infrastruktur**: Wissenschaftliche Infrastruktur sollte offen und gemeinschaftlich entwickelt werden

Bitte beachten Sie diese Prinzipien bei Ihren Beiträgen zum Projekt.

## Wie Sie beitragen können

Es gibt viele Möglichkeiten, zu DeSci-Scholar beizutragen:

1. **Code-Beiträge**: Implementierung neuer Funktionen oder Behebung von Fehlern
2. **Dokumentation**: Verbesserung oder Erweiterung der Dokumentation
3. **Tests**: Schreiben von Tests zur Verbesserung der Codequalität
4. **Fehlerberichte**: Melden von Fehlern oder Problemen
5. **Funktionsanfragen**: Vorschläge für neue Funktionen oder Verbesserungen
6. **Übersetzungen**: Übersetzung der Dokumentation oder der Benutzeroberfläche
7. **Community-Unterstützung**: Beantwortung von Fragen in Foren oder Diskussionen

## Entwicklungsprozess

### 1. Fork und Clone

1. Erstellen Sie einen Fork des Repositories auf GitHub
2. Klonen Sie Ihren Fork auf Ihren lokalen Computer:
   ```
   git clone https://github.com/IHR_BENUTZERNAME/desci-scholar.git
   cd desci-scholar
   ```
3. Fügen Sie das Upstream-Repository hinzu:
   ```
   git remote add upstream https://github.com/desci-labs/desci-scholar.git
   ```

### 2. Branches

1. Halten Sie Ihren `main`-Branch synchron mit dem Upstream:
   ```
   git checkout main
   git pull upstream main
   ```
2. Erstellen Sie einen neuen Branch für Ihre Änderungen:
   ```
   git checkout -b feature/ihre-feature-beschreibung
   ```

### 3. Entwicklung

1. Implementieren Sie Ihre Änderungen
2. Stellen Sie sicher, dass Ihr Code den Codierungsrichtlinien entspricht
3. Schreiben Sie Tests für Ihre Änderungen
4. Stellen Sie sicher, dass alle Tests bestehen:
   ```
   npm test
   ```

### 4. Commit und Push

1. Committen Sie Ihre Änderungen mit aussagekräftigen Commit-Nachrichten:
   ```
   git commit -m "Kurze Beschreibung der Änderungen"
   ```
2. Pushen Sie Ihre Änderungen zu Ihrem Fork:
   ```
   git push origin feature/ihre-feature-beschreibung
   ```

### 5. Pull Request

1. Erstellen Sie einen Pull Request von Ihrem Branch zu `desci-labs/desci-scholar:main`
2. Beschreiben Sie Ihre Änderungen ausführlich im Pull Request
3. Verlinken Sie relevante Issues
4. Warten Sie auf Feedback und reagieren Sie auf Kommentare

## Codierungsrichtlinien

- Folgen Sie dem bestehenden Codierungsstil
- Schreiben Sie klaren, lesbaren und gut dokumentierten Code
- Kommentieren Sie komplexe Logik
- Verwenden Sie aussagekräftige Variablen- und Funktionsnamen
- Halten Sie Funktionen kurz und fokussiert

## Commit-Nachrichten

Bitte verwenden Sie klare und aussagekräftige Commit-Nachrichten. Folgen Sie diesem Format:

```
Kurze Zusammenfassung (max. 50 Zeichen)

Ausführlichere Erklärung, wenn nötig. Umbruch nach etwa 72 Zeichen.
Erklären Sie das Problem, das dieser Commit löst. Konzentrieren Sie
sich darauf, warum die Änderung notwendig ist, nicht wie sie
implementiert wurde.

Wenn es relevante Issues gibt, erwähnen Sie diese am Ende:
Fixes #123
```

## Lizenz

Durch das Einreichen eines Beitrags erklären Sie sich damit einverstanden, dass Ihr Beitrag unter der [MIT-Lizenz](LICENSE) veröffentlicht wird, die für das Projekt gilt.

## Verhaltenskodex

Wir erwarten von allen Teilnehmern, dass sie unseren [Verhaltenskodex](CODE_OF_CONDUCT.md) einhalten. Bitte lesen Sie ihn, bevor Sie beitragen.

## Fragen?

Wenn Sie Fragen haben oder Hilfe benötigen, können Sie:

- Ein Issue auf GitHub erstellen
- Eine Diskussion im Discussions-Bereich starten
- Uns per E-Mail kontaktieren: [<EMAIL>](mailto:<EMAIL>)

Vielen Dank für Ihre Beiträge zu DeSci-Scholar!# Beitragsrichtlinien für DeSci-Scholar

Vielen Dank für Ihr Interesse, zu DeSci-Scholar beizutragen! Als Open-Source- und Open-Science-Projekt leben wir von der Beteiligung der Community. Diese Richtlinien sollen Ihnen helfen, effektiv zum Projekt beizutragen.

## Open-Science-Prinzipien

DeSci-Scholar ist der Open-Science-Bewegung verpflichtet. Wir glauben an:

- **Transparenz**: Alle Aspekte des wissenschaftlichen Prozesses sollten offen und nachvollziehbar sein
- **Offenen Zugang**: Wissenschaftliche Erkenntnisse sollten frei zugänglich sein
- **Offene Daten**: Forschungsdaten sollten geteilt und wiederverwendbar sein
- **Offene Methoden**: Forschungsmethoden sollten transparent und reproduzierbar sein
- **Offene Infrastruktur**: Wissenschaftliche Infrastruktur sollte offen und gemeinschaftlich entwickelt werden

Bitte beachten Sie diese Prinzipien bei Ihren Beiträgen zum Projekt.

## Wie Sie beitragen können

Es gibt viele Möglichkeiten, zu DeSci-Scholar beizutragen:

1. **Code-Beiträge**: Implementierung neuer Funktionen oder Behebung von Fehlern
2. **Dokumentation**: Verbesserung oder Erweiterung der Dokumentation
3. **Tests**: Schreiben von Tests zur Verbesserung der Codequalität
4. **Fehlerberichte**: Melden von Fehlern oder Problemen
5. **Funktionsanfragen**: Vorschläge für neue Funktionen oder Verbesserungen
6. **Übersetzungen**: Übersetzung der Dokumentation oder der Benutzeroberfläche
7. **Community-Unterstützung**: Beantwortung von Fragen in Foren oder Diskussionen

## Entwicklungsprozess

### 1. Fork und Clone

1. Erstellen Sie einen Fork des Repositories auf GitHub
2. Klonen Sie Ihren Fork auf Ihren lokalen Computer:
   ```
   git clone https://github.com/IHR_BENUTZERNAME/desci-scholar.git
   cd desci-scholar
   ```
3. Fügen Sie das Upstream-Repository hinzu:
   ```
   git remote add upstream https://github.com/desci-labs/desci-scholar.git
   ```

### 2. Branches

1. Halten Sie Ihren `main`-Branch synchron mit dem Upstream:
   ```
   git checkout main
   git pull upstream main
   ```
2. Erstellen Sie einen neuen Branch für Ihre Änderungen:
   ```
   git checkout -b feature/ihre-feature-beschreibung
   ```

### 3. Entwicklung

1. Implementieren Sie Ihre Änderungen
2. Stellen Sie sicher, dass Ihr Code den Codierungsrichtlinien entspricht
3. Schreiben Sie Tests für Ihre Änderungen
4. Stellen Sie sicher, dass alle Tests bestehen:
   ```
   npm test
   ```

### 4. Commit und Push

1. Committen Sie Ihre Änderungen mit aussagekräftigen Commit-Nachrichten:
   ```
   git commit -m "Kurze Beschreibung der Änderungen"
   ```
2. Pushen Sie Ihre Änderungen zu Ihrem Fork:
   ```
   git push origin feature/ihre-feature-beschreibung
   ```

### 5. Pull Request

1. Erstellen Sie einen Pull Request von Ihrem Branch zu `desci-labs/desci-scholar:main`
2. Beschreiben Sie Ihre Änderungen ausführlich im Pull Request
3. Verlinken Sie relevante Issues
4. Warten Sie auf Feedback und reagieren Sie auf Kommentare

## Codierungsrichtlinien

- Folgen Sie dem bestehenden Codierungsstil
- Schreiben Sie klaren, lesbaren und gut dokumentierten Code
- Kommentieren Sie komplexe Logik
- Verwenden Sie aussagekräftige Variablen- und Funktionsnamen
- Halten Sie Funktionen kurz und fokussiert

## Commit-Nachrichten

Bitte verwenden Sie klare und aussagekräftige Commit-Nachrichten. Folgen Sie diesem Format:

```
Kurze Zusammenfassung (max. 50 Zeichen)

Ausführlichere Erklärung, wenn nötig. Umbruch nach etwa 72 Zeichen.
Erklären Sie das Problem, das dieser Commit löst. Konzentrieren Sie
sich darauf, warum die Änderung notwendig ist, nicht wie sie
implementiert wurde.

Wenn es relevante Issues gibt, erwähnen Sie diese am Ende:
Fixes #123
```

## Lizenz

Durch das Einreichen eines Beitrags erklären Sie sich damit einverstanden, dass Ihr Beitrag unter der [MIT-Lizenz](LICENSE) veröffentlicht wird, die für das Projekt gilt.

## Verhaltenskodex

Wir erwarten von allen Teilnehmern, dass sie unseren [Verhaltenskodex](CODE_OF_CONDUCT.md) einhalten. Bitte lesen Sie ihn, bevor Sie beitragen.

## Fragen?

Wenn Sie Fragen haben oder Hilfe benötigen, können Sie:

- Ein Issue auf GitHub erstellen
- Eine Diskussion im Discussions-Bereich starten
- Uns per E-Mail kontaktieren: [<EMAIL>](mailto:<EMAIL>)

Vielen Dank für Ihre Beiträge zu DeSci-Scholar!# Beitragsrichtlinien für DeSci-Scholar

Vielen Dank für Ihr Interesse, zu DeSci-Scholar beizutragen! Als Open-Source- und Open-Science-Projekt leben wir von der Beteiligung der Community. Diese Richtlinien sollen Ihnen helfen, effektiv zum Projekt beizutragen.

## Open-Science-Prinzipien

DeSci-Scholar ist der Open-Science-Bewegung verpflichtet. Wir glauben an:

- **Transparenz**: Alle Aspekte des wissenschaftlichen Prozesses sollten offen und nachvollziehbar sein
- **Offenen Zugang**: Wissenschaftliche Erkenntnisse sollten frei zugänglich sein
- **Offene Daten**: Forschungsdaten sollten geteilt und wiederverwendbar sein
- **Offene Methoden**: Forschungsmethoden sollten transparent und reproduzierbar sein
- **Offene Infrastruktur**: Wissenschaftliche Infrastruktur sollte offen und gemeinschaftlich entwickelt werden

Bitte beachten Sie diese Prinzipien bei Ihren Beiträgen zum Projekt.

## Wie Sie beitragen können

Es gibt viele Möglichkeiten, zu DeSci-Scholar beizutragen:

1. **Code-Beiträge**: Implementierung neuer Funktionen oder Behebung von Fehlern
2. **Dokumentation**: Verbesserung oder Erweiterung der Dokumentation
3. **Tests**: Schreiben von Tests zur Verbesserung der Codequalität
4. **Fehlerberichte**: Melden von Fehlern oder Problemen
5. **Funktionsanfragen**: Vorschläge für neue Funktionen oder Verbesserungen
6. **Übersetzungen**: Übersetzung der Dokumentation oder der Benutzeroberfläche
7. **Community-Unterstützung**: Beantwortung von Fragen in Foren oder Diskussionen

## Entwicklungsprozess

### 1. Fork und Clone

1. Erstellen Sie einen Fork des Repositories auf GitHub
2. Klonen Sie Ihren Fork auf Ihren lokalen Computer:
   ```
   git clone https://github.com/IHR_BENUTZERNAME/desci-scholar.git
   cd desci-scholar
   ```
3. Fügen Sie das Upstream-Repository hinzu:
   ```
   git remote add upstream https://github.com/desci-labs/desci-scholar.git
   ```

### 2. Branches

1. Halten Sie Ihren `main`-Branch synchron mit dem Upstream:
   ```
   git checkout main
   git pull upstream main
   ```
2. Erstellen Sie einen neuen Branch für Ihre Änderungen:
   ```
   git checkout -b feature/ihre-feature-beschreibung
   ```

### 3. Entwicklung

1. Implementieren Sie Ihre Änderungen
2. Stellen Sie sicher, dass Ihr Code den Codierungsrichtlinien entspricht
3. Schreiben Sie Tests für Ihre Änderungen
4. Stellen Sie sicher, dass alle Tests bestehen:
   ```
   npm test
   ```

### 4. Commit und Push

1. Committen Sie Ihre Änderungen mit aussagekräftigen Commit-Nachrichten:
   ```
   git commit -m "Kurze Beschreibung der Änderungen"
   ```
2. Pushen Sie Ihre Änderungen zu Ihrem Fork:
   ```
   git push origin feature/ihre-feature-beschreibung
   ```

### 5. Pull Request

1. Erstellen Sie einen Pull Request von Ihrem Branch zu `desci-labs/desci-scholar:main`
2. Beschreiben Sie Ihre Änderungen ausführlich im Pull Request
3. Verlinken Sie relevante Issues
4. Warten Sie auf Feedback und reagieren Sie auf Kommentare

## Codierungsrichtlinien

- Folgen Sie dem bestehenden Codierungsstil
- Schreiben Sie klaren, lesbaren und gut dokumentierten Code
- Kommentieren Sie komplexe Logik
- Verwenden Sie aussagekräftige Variablen- und Funktionsnamen
- Halten Sie Funktionen kurz und fokussiert

## Commit-Nachrichten

Bitte verwenden Sie klare und aussagekräftige Commit-Nachrichten. Folgen Sie diesem Format:

```
Kurze Zusammenfassung (max. 50 Zeichen)

Ausführlichere Erklärung, wenn nötig. Umbruch nach etwa 72 Zeichen.
Erklären Sie das Problem, das dieser Commit löst. Konzentrieren Sie
sich darauf, warum die Änderung notwendig ist, nicht wie sie
implementiert wurde.

Wenn es relevante Issues gibt, erwähnen Sie diese am Ende:
Fixes #123
```

## Lizenz

Durch das Einreichen eines Beitrags erklären Sie sich damit einverstanden, dass Ihr Beitrag unter der [MIT-Lizenz](LICENSE) veröffentlicht wird, die für das Projekt gilt.

## Verhaltenskodex

Wir erwarten von allen Teilnehmern, dass sie unseren [Verhaltenskodex](CODE_OF_CONDUCT.md) einhalten. Bitte lesen Sie ihn, bevor Sie beitragen.

## Fragen?

Wenn Sie Fragen haben oder Hilfe benötigen, können Sie:

- Ein Issue auf GitHub erstellen
- Eine Diskussion im Discussions-Bereich starten
- Uns per E-Mail kontaktieren: [<EMAIL>](mailto:<EMAIL>)

Vielen Dank für Ihre Beiträge zu DeSci-Scholar!# Beitragsrichtlinien für DeSci-Scholar

Vielen Dank für Ihr Interesse, zu DeSci-Scholar beizutragen! Als Open-Source- und Open-Science-Projekt leben wir von der Beteiligung der Community. Diese Richtlinien sollen Ihnen helfen, effektiv zum Projekt beizutragen.

## Open-Science-Prinzipien

DeSci-Scholar ist der Open-Science-Bewegung verpflichtet. Wir glauben an:

- **Transparenz**: Alle Aspekte des wissenschaftlichen Prozesses sollten offen und nachvollziehbar sein
- **Offenen Zugang**: Wissenschaftliche Erkenntnisse sollten frei zugänglich sein
- **Offene Daten**: Forschungsdaten sollten geteilt und wiederverwendbar sein
- **Offene Methoden**: Forschungsmethoden sollten transparent und reproduzierbar sein
- **Offene Infrastruktur**: Wissenschaftliche Infrastruktur sollte offen und gemeinschaftlich entwickelt werden

Bitte beachten Sie diese Prinzipien bei Ihren Beiträgen zum Projekt.

## Wie Sie beitragen können

Es gibt viele Möglichkeiten, zu DeSci-Scholar beizutragen:

1. **Code-Beiträge**: Implementierung neuer Funktionen oder Behebung von Fehlern
2. **Dokumentation**: Verbesserung oder Erweiterung der Dokumentation
3. **Tests**: Schreiben von Tests zur Verbesserung der Codequalität
4. **Fehlerberichte**: Melden von Fehlern oder Problemen
5. **Funktionsanfragen**: Vorschläge für neue Funktionen oder Verbesserungen
6. **Übersetzungen**: Übersetzung der Dokumentation oder der Benutzeroberfläche
7. **Community-Unterstützung**: Beantwortung von Fragen in Foren oder Diskussionen

## Entwicklungsprozess

### 1. Fork und Clone

1. Erstellen Sie einen Fork des Repositories auf GitHub
2. Klonen Sie Ihren Fork auf Ihren lokalen Computer:
   ```
   git clone https://github.com/IHR_BENUTZERNAME/desci-scholar.git
   cd desci-scholar
   ```
3. Fügen Sie das Upstream-Repository hinzu:
   ```
   git remote add upstream https://github.com/desci-labs/desci-scholar.git
   ```

### 2. Branches

1. Halten Sie Ihren `main`-Branch synchron mit dem Upstream:
   ```
   git checkout main
   git pull upstream main
   ```
2. Erstellen Sie einen neuen Branch für Ihre Änderungen:
   ```
   git checkout -b feature/ihre-feature-beschreibung
   ```

### 3. Entwicklung

1. Implementieren Sie Ihre Änderungen
2. Stellen Sie sicher, dass Ihr Code den Codierungsrichtlinien entspricht
3. Schreiben Sie Tests für Ihre Änderungen
4. Stellen Sie sicher, dass alle Tests bestehen:
   ```
   npm test
   ```

### 4. Commit und Push

1. Committen Sie Ihre Änderungen mit aussagekräftigen Commit-Nachrichten:
   ```
   git commit -m "Kurze Beschreibung der Änderungen"
   ```
2. Pushen Sie Ihre Änderungen zu Ihrem Fork:
   ```
   git push origin feature/ihre-feature-beschreibung
   ```

### 5. Pull Request

1. Erstellen Sie einen Pull Request von Ihrem Branch zu `desci-labs/desci-scholar:main`
2. Beschreiben Sie Ihre Änderungen ausführlich im Pull Request
3. Verlinken Sie relevante Issues
4. Warten Sie auf Feedback und reagieren Sie auf Kommentare

## Codierungsrichtlinien

- Folgen Sie dem bestehenden Codierungsstil
- Schreiben Sie klaren, lesbaren und gut dokumentierten Code
- Kommentieren Sie komplexe Logik
- Verwenden Sie aussagekräftige Variablen- und Funktionsnamen
- Halten Sie Funktionen kurz und fokussiert

## Commit-Nachrichten

Bitte verwenden Sie klare und aussagekräftige Commit-Nachrichten. Folgen Sie diesem Format:

```
Kurze Zusammenfassung (max. 50 Zeichen)

Ausführlichere Erklärung, wenn nötig. Umbruch nach etwa 72 Zeichen.
Erklären Sie das Problem, das dieser Commit löst. Konzentrieren Sie
sich darauf, warum die Änderung notwendig ist, nicht wie sie
implementiert wurde.

Wenn es relevante Issues gibt, erwähnen Sie diese am Ende:
Fixes #123
```

## Lizenz

Durch das Einreichen eines Beitrags erklären Sie sich damit einverstanden, dass Ihr Beitrag unter der [MIT-Lizenz](LICENSE) veröffentlicht wird, die für das Projekt gilt.

## Verhaltenskodex

Wir erwarten von allen Teilnehmern, dass sie unseren [Verhaltenskodex](CODE_OF_CONDUCT.md) einhalten. Bitte lesen Sie ihn, bevor Sie beitragen.

## Fragen?

Wenn Sie Fragen haben oder Hilfe benötigen, können Sie:

- Ein Issue auf GitHub erstellen
- Eine Diskussion im Discussions-Bereich starten
- Uns per E-Mail kontaktieren: [<EMAIL>](mailto:<EMAIL>)

Vielen Dank für Ihre Beiträge zu DeSci-Scholar!# Beitragsrichtlinien für DeSci-Scholar

Vielen Dank für Ihr Interesse, zu DeSci-Scholar beizutragen! Als Open-Source- und Open-Science-Projekt leben wir von der Beteiligung der Community. Diese Richtlinien sollen Ihnen helfen, effektiv zum Projekt beizutragen.

## Open-Science-Prinzipien

DeSci-Scholar ist der Open-Science-Bewegung verpflichtet. Wir glauben an:

- **Transparenz**: Alle Aspekte des wissenschaftlichen Prozesses sollten offen und nachvollziehbar sein
- **Offenen Zugang**: Wissenschaftliche Erkenntnisse sollten frei zugänglich sein
- **Offene Daten**: Forschungsdaten sollten geteilt und wiederverwendbar sein
- **Offene Methoden**: Forschungsmethoden sollten transparent und reproduzierbar sein
- **Offene Infrastruktur**: Wissenschaftliche Infrastruktur sollte offen und gemeinschaftlich entwickelt werden

Bitte beachten Sie diese Prinzipien bei Ihren Beiträgen zum Projekt.

## Wie Sie beitragen können

Es gibt viele Möglichkeiten, zu DeSci-Scholar beizutragen:

1. **Code-Beiträge**: Implementierung neuer Funktionen oder Behebung von Fehlern
2. **Dokumentation**: Verbesserung oder Erweiterung der Dokumentation
3. **Tests**: Schreiben von Tests zur Verbesserung der Codequalität
4. **Fehlerberichte**: Melden von Fehlern oder Problemen
5. **Funktionsanfragen**: Vorschläge für neue Funktionen oder Verbesserungen
6. **Übersetzungen**: Übersetzung der Dokumentation oder der Benutzeroberfläche
7. **Community-Unterstützung**: Beantwortung von Fragen in Foren oder Diskussionen

## Entwicklungsprozess

### 1. Fork und Clone

1. Erstellen Sie einen Fork des Repositories auf GitHub
2. Klonen Sie Ihren Fork auf Ihren lokalen Computer:
   ```
   git clone https://github.com/IHR_BENUTZERNAME/desci-scholar.git
   cd desci-scholar
   ```
3. Fügen Sie das Upstream-Repository hinzu:
   ```
   git remote add upstream https://github.com/desci-labs/desci-scholar.git
   ```

### 2. Branches

1. Halten Sie Ihren `main`-Branch synchron mit dem Upstream:
   ```
   git checkout main
   git pull upstream main
   ```
2. Erstellen Sie einen neuen Branch für Ihre Änderungen:
   ```
   git checkout -b feature/ihre-feature-beschreibung
   ```

### 3. Entwicklung

1. Implementieren Sie Ihre Änderungen
2. Stellen Sie sicher, dass Ihr Code den Codierungsrichtlinien entspricht
3. Schreiben Sie Tests für Ihre Änderungen
4. Stellen Sie sicher, dass alle Tests bestehen:
   ```
   npm test
   ```

### 4. Commit und Push

1. Committen Sie Ihre Änderungen mit aussagekräftigen Commit-Nachrichten:
   ```
   git commit -m "Kurze Beschreibung der Änderungen"
   ```
2. Pushen Sie Ihre Änderungen zu Ihrem Fork:
   ```
   git push origin feature/ihre-feature-beschreibung
   ```

### 5. Pull Request

1. Erstellen Sie einen Pull Request von Ihrem Branch zu `desci-labs/desci-scholar:main`
2. Beschreiben Sie Ihre Änderungen ausführlich im Pull Request
3. Verlinken Sie relevante Issues
4. Warten Sie auf Feedback und reagieren Sie auf Kommentare

## Codierungsrichtlinien

- Folgen Sie dem bestehenden Codierungsstil
- Schreiben Sie klaren, lesbaren und gut dokumentierten Code
- Kommentieren Sie komplexe Logik
- Verwenden Sie aussagekräftige Variablen- und Funktionsnamen
- Halten Sie Funktionen kurz und fokussiert

## Commit-Nachrichten

Bitte verwenden Sie klare und aussagekräftige Commit-Nachrichten. Folgen Sie diesem Format:

```
Kurze Zusammenfassung (max. 50 Zeichen)

Ausführlichere Erklärung, wenn nötig. Umbruch nach etwa 72 Zeichen.
Erklären Sie das Problem, das dieser Commit löst. Konzentrieren Sie
sich darauf, warum die Änderung notwendig ist, nicht wie sie
implementiert wurde.

Wenn es relevante Issues gibt, erwähnen Sie diese am Ende:
Fixes #123
```

## Lizenz

Durch das Einreichen eines Beitrags erklären Sie sich damit einverstanden, dass Ihr Beitrag unter der [MIT-Lizenz](LICENSE) veröffentlicht wird, die für das Projekt gilt.

## Verhaltenskodex

Wir erwarten von allen Teilnehmern, dass sie unseren [Verhaltenskodex](CODE_OF_CONDUCT.md) einhalten. Bitte lesen Sie ihn, bevor Sie beitragen.

## Fragen?

Wenn Sie Fragen haben oder Hilfe benötigen, können Sie:

- Ein Issue auf GitHub erstellen
- Eine Diskussion im Discussions-Bereich starten
- Uns per E-Mail kontaktieren: [<EMAIL>](mailto:<EMAIL>)

Vielen Dank für Ihre Beiträge zu DeSci-Scholar!# Beitragsrichtlinien für DeSci-Scholar

Vielen Dank für Ihr Interesse, zu DeSci-Scholar beizutragen! Als Open-Source- und Open-Science-Projekt leben wir von der Beteiligung der Community. Diese Richtlinien sollen Ihnen helfen, effektiv zum Projekt beizutragen.

## Open-Science-Prinzipien

DeSci-Scholar ist der Open-Science-Bewegung verpflichtet. Wir glauben an:

- **Transparenz**: Alle Aspekte des wissenschaftlichen Prozesses sollten offen und nachvollziehbar sein
- **Offenen Zugang**: Wissenschaftliche Erkenntnisse sollten frei zugänglich sein
- **Offene Daten**: Forschungsdaten sollten geteilt und wiederverwendbar sein
- **Offene Methoden**: Forschungsmethoden sollten transparent und reproduzierbar sein
- **Offene Infrastruktur**: Wissenschaftliche Infrastruktur sollte offen und gemeinschaftlich entwickelt werden

Bitte beachten Sie diese Prinzipien bei Ihren Beiträgen zum Projekt.

## Wie Sie beitragen können

Es gibt viele Möglichkeiten, zu DeSci-Scholar beizutragen:

1. **Code-Beiträge**: Implementierung neuer Funktionen oder Behebung von Fehlern
2. **Dokumentation**: Verbesserung oder Erweiterung der Dokumentation
3. **Tests**: Schreiben von Tests zur Verbesserung der Codequalität
4. **Fehlerberichte**: Melden von Fehlern oder Problemen
5. **Funktionsanfragen**: Vorschläge für neue Funktionen oder Verbesserungen
6. **Übersetzungen**: Übersetzung der Dokumentation oder der Benutzeroberfläche
7. **Community-Unterstützung**: Beantwortung von Fragen in Foren oder Diskussionen

## Entwicklungsprozess

### 1. Fork und Clone

1. Erstellen Sie einen Fork des Repositories auf GitHub
2. Klonen Sie Ihren Fork auf Ihren lokalen Computer:
   ```
   git clone https://github.com/IHR_BENUTZERNAME/desci-scholar.git
   cd desci-scholar
   ```
3. Fügen Sie das Upstream-Repository hinzu:
   ```
   git remote add upstream https://github.com/desci-labs/desci-scholar.git
   ```

### 2. Branches

1. Halten Sie Ihren `main`-Branch synchron mit dem Upstream:
   ```
   git checkout main
   git pull upstream main
   ```
2. Erstellen Sie einen neuen Branch für Ihre Änderungen:
   ```
   git checkout -b feature/ihre-feature-beschreibung
   ```

### 3. Entwicklung

1. Implementieren Sie Ihre Änderungen
2. Stellen Sie sicher, dass Ihr Code den Codierungsrichtlinien entspricht
3. Schreiben Sie Tests für Ihre Änderungen
4. Stellen Sie sicher, dass alle Tests bestehen:
   ```
   npm test
   ```

### 4. Commit und Push

1. Committen Sie Ihre Änderungen mit aussagekräftigen Commit-Nachrichten:
   ```
   git commit -m "Kurze Beschreibung der Änderungen"
   ```
2. Pushen Sie Ihre Änderungen zu Ihrem Fork:
   ```
   git push origin feature/ihre-feature-beschreibung
   ```

### 5. Pull Request

1. Erstellen Sie einen Pull Request von Ihrem Branch zu `desci-labs/desci-scholar:main`
2. Beschreiben Sie Ihre Änderungen ausführlich im Pull Request
3. Verlinken Sie relevante Issues
4. Warten Sie auf Feedback und reagieren Sie auf Kommentare

## Codierungsrichtlinien

- Folgen Sie dem bestehenden Codierungsstil
- Schreiben Sie klaren, lesbaren und gut dokumentierten Code
- Kommentieren Sie komplexe Logik
- Verwenden Sie aussagekräftige Variablen- und Funktionsnamen
- Halten Sie Funktionen kurz und fokussiert

## Commit-Nachrichten

Bitte verwenden Sie klare und aussagekräftige Commit-Nachrichten. Folgen Sie diesem Format:

```
Kurze Zusammenfassung (max. 50 Zeichen)

Ausführlichere Erklärung, wenn nötig. Umbruch nach etwa 72 Zeichen.
Erklären Sie das Problem, das dieser Commit löst. Konzentrieren Sie
sich darauf, warum die Änderung notwendig ist, nicht wie sie
implementiert wurde.

Wenn es relevante Issues gibt, erwähnen Sie diese am Ende:
Fixes #123
```

## Lizenz

Durch das Einreichen eines Beitrags erklären Sie sich damit einverstanden, dass Ihr Beitrag unter der [MIT-Lizenz](LICENSE) veröffentlicht wird, die für das Projekt gilt.

## Verhaltenskodex

Wir erwarten von allen Teilnehmern, dass sie unseren [Verhaltenskodex](CODE_OF_CONDUCT.md) einhalten. Bitte lesen Sie ihn, bevor Sie beitragen.

## Fragen?

Wenn Sie Fragen haben oder Hilfe benötigen, können Sie:

- Ein Issue auf GitHub erstellen
- Eine Diskussion im Discussions-Bereich starten
- Uns per E-Mail kontaktieren: [<EMAIL>](mailto:<EMAIL>)

Vielen Dank für Ihre Beiträge zu DeSci-Scholar!# Beitragsrichtlinien für DeSci-Scholar

Vielen Dank für Ihr Interesse, zu DeSci-Scholar beizutragen! Als Open-Source- und Open-Science-Projekt leben wir von der Beteiligung der Community. Diese Richtlinien sollen Ihnen helfen, effektiv zum Projekt beizutragen.

## Open-Science-Prinzipien

DeSci-Scholar ist der Open-Science-Bewegung verpflichtet. Wir glauben an:

- **Transparenz**: Alle Aspekte des wissenschaftlichen Prozesses sollten offen und nachvollziehbar sein
- **Offenen Zugang**: Wissenschaftliche Erkenntnisse sollten frei zugänglich sein
- **Offene Daten**: Forschungsdaten sollten geteilt und wiederverwendbar sein
- **Offene Methoden**: Forschungsmethoden sollten transparent und reproduzierbar sein
- **Offene Infrastruktur**: Wissenschaftliche Infrastruktur sollte offen und gemeinschaftlich entwickelt werden

Bitte beachten Sie diese Prinzipien bei Ihren Beiträgen zum Projekt.

## Wie Sie beitragen können

Es gibt viele Möglichkeiten, zu DeSci-Scholar beizutragen:

1. **Code-Beiträge**: Implementierung neuer Funktionen oder Behebung von Fehlern
2. **Dokumentation**: Verbesserung oder Erweiterung der Dokumentation
3. **Tests**: Schreiben von Tests zur Verbesserung der Codequalität
4. **Fehlerberichte**: Melden von Fehlern oder Problemen
5. **Funktionsanfragen**: Vorschläge für neue Funktionen oder Verbesserungen
6. **Übersetzungen**: Übersetzung der Dokumentation oder der Benutzeroberfläche
7. **Community-Unterstützung**: Beantwortung von Fragen in Foren oder Diskussionen

## Entwicklungsprozess

### 1. Fork und Clone

1. Erstellen Sie einen Fork des Repositories auf GitHub
2. Klonen Sie Ihren Fork auf Ihren lokalen Computer:
   ```
   git clone https://github.com/IHR_BENUTZERNAME/desci-scholar.git
   cd desci-scholar
   ```
3. Fügen Sie das Upstream-Repository hinzu:
   ```
   git remote add upstream https://github.com/desci-labs/desci-scholar.git
   ```

### 2. Branches

1. Halten Sie Ihren `main`-Branch synchron mit dem Upstream:
   ```
   git checkout main
   git pull upstream main
   ```
2. Erstellen Sie einen neuen Branch für Ihre Änderungen:
   ```
   git checkout -b feature/ihre-feature-beschreibung
   ```

### 3. Entwicklung

1. Implementieren Sie Ihre Änderungen
2. Stellen Sie sicher, dass Ihr Code den Codierungsrichtlinien entspricht
3. Schreiben Sie Tests für Ihre Änderungen
4. Stellen Sie sicher, dass alle Tests bestehen:
   ```
   npm test
   ```

### 4. Commit und Push

1. Committen Sie Ihre Änderungen mit aussagekräftigen Commit-Nachrichten:
   ```
   git commit -m "Kurze Beschreibung der Änderungen"
   ```
2. Pushen Sie Ihre Änderungen zu Ihrem Fork:
   ```
   git push origin feature/ihre-feature-beschreibung
   ```

### 5. Pull Request

1. Erstellen Sie einen Pull Request von Ihrem Branch zu `desci-labs/desci-scholar:main`
2. Beschreiben Sie Ihre Änderungen ausführlich im Pull Request
3. Verlinken Sie relevante Issues
4. Warten Sie auf Feedback und reagieren Sie auf Kommentare

## Codierungsrichtlinien

- Folgen Sie dem bestehenden Codierungsstil
- Schreiben Sie klaren, lesbaren und gut dokumentierten Code
- Kommentieren Sie komplexe Logik
- Verwenden Sie aussagekräftige Variablen- und Funktionsnamen
- Halten Sie Funktionen kurz und fokussiert

## Commit-Nachrichten

Bitte verwenden Sie klare und aussagekräftige Commit-Nachrichten. Folgen Sie diesem Format:

```
Kurze Zusammenfassung (max. 50 Zeichen)

Ausführlichere Erklärung, wenn nötig. Umbruch nach etwa 72 Zeichen.
Erklären Sie das Problem, das dieser Commit löst. Konzentrieren Sie
sich darauf, warum die Änderung notwendig ist, nicht wie sie
implementiert wurde.

Wenn es relevante Issues gibt, erwähnen Sie diese am Ende:
Fixes #123
```

## Lizenz

Durch das Einreichen eines Beitrags erklären Sie sich damit einverstanden, dass Ihr Beitrag unter der [MIT-Lizenz](LICENSE) veröffentlicht wird, die für das Projekt gilt.

## Verhaltenskodex

Wir erwarten von allen Teilnehmern, dass sie unseren [Verhaltenskodex](CODE_OF_CONDUCT.md) einhalten. Bitte lesen Sie ihn, bevor Sie beitragen.

## Fragen?

Wenn Sie Fragen haben oder Hilfe benötigen, können Sie:

- Ein Issue auf GitHub erstellen
- Eine Diskussion im Discussions-Bereich starten
- Uns per E-Mail kontaktieren: [<EMAIL>](mailto:<EMAIL>)

Vielen Dank für Ihre Beiträge zu DeSci-Scholar!# Beitragsrichtlinien für DeSci-Scholar

Vielen Dank für Ihr Interesse, zu DeSci-Scholar beizutragen! Als Open-Source- und Open-Science-Projekt leben wir von der Beteiligung der Community. Diese Richtlinien sollen Ihnen helfen, effektiv zum Projekt beizutragen.

## Open-Science-Prinzipien

DeSci-Scholar ist der Open-Science-Bewegung verpflichtet. Wir glauben an:

- **Transparenz**: Alle Aspekte des wissenschaftlichen Prozesses sollten offen und nachvollziehbar sein
- **Offenen Zugang**: Wissenschaftliche Erkenntnisse sollten frei zugänglich sein
- **Offene Daten**: Forschungsdaten sollten geteilt und wiederverwendbar sein
- **Offene Methoden**: Forschungsmethoden sollten transparent und reproduzierbar sein
- **Offene Infrastruktur**: Wissenschaftliche Infrastruktur sollte offen und gemeinschaftlich entwickelt werden

Bitte beachten Sie diese Prinzipien bei Ihren Beiträgen zum Projekt.

## Wie Sie beitragen können

Es gibt viele Möglichkeiten, zu DeSci-Scholar beizutragen:

1. **Code-Beiträge**: Implementierung neuer Funktionen oder Behebung von Fehlern
2. **Dokumentation**: Verbesserung oder Erweiterung der Dokumentation
3. **Tests**: Schreiben von Tests zur Verbesserung der Codequalität
4. **Fehlerberichte**: Melden von Fehlern oder Problemen
5. **Funktionsanfragen**: Vorschläge für neue Funktionen oder Verbesserungen
6. **Übersetzungen**: Übersetzung der Dokumentation oder der Benutzeroberfläche
7. **Community-Unterstützung**: Beantwortung von Fragen in Foren oder Diskussionen

## Entwicklungsprozess

### 1. Fork und Clone

1. Erstellen Sie einen Fork des Repositories auf GitHub
2. Klonen Sie Ihren Fork auf Ihren lokalen Computer:
   ```
   git clone https://github.com/IHR_BENUTZERNAME/desci-scholar.git
   cd desci-scholar
   ```
3. Fügen Sie das Upstream-Repository hinzu:
   ```
   git remote add upstream https://github.com/desci-labs/desci-scholar.git
   ```

### 2. Branches

1. Halten Sie Ihren `main`-Branch synchron mit dem Upstream:
   ```
   git checkout main
   git pull upstream main
   ```
2. Erstellen Sie einen neuen Branch für Ihre Änderungen:
   ```
   git checkout -b feature/ihre-feature-beschreibung
   ```

### 3. Entwicklung

1. Implementieren Sie Ihre Änderungen
2. Stellen Sie sicher, dass Ihr Code den Codierungsrichtlinien entspricht
3. Schreiben Sie Tests für Ihre Änderungen
4. Stellen Sie sicher, dass alle Tests bestehen:
   ```
   npm test
   ```

### 4. Commit und Push

1. Committen Sie Ihre Änderungen mit aussagekräftigen Commit-Nachrichten:
   ```
   git commit -m "Kurze Beschreibung der Änderungen"
   ```
2. Pushen Sie Ihre Änderungen zu Ihrem Fork:
   ```
   git push origin feature/ihre-feature-beschreibung
   ```

### 5. Pull Request

1. Erstellen Sie einen Pull Request von Ihrem Branch zu `desci-labs/desci-scholar:main`
2. Beschreiben Sie Ihre Änderungen ausführlich im Pull Request
3. Verlinken Sie relevante Issues
4. Warten Sie auf Feedback und reagieren Sie auf Kommentare

## Codierungsrichtlinien

- Folgen Sie dem bestehenden Codierungsstil
- Schreiben Sie klaren, lesbaren und gut dokumentierten Code
- Kommentieren Sie komplexe Logik
- Verwenden Sie aussagekräftige Variablen- und Funktionsnamen
- Halten Sie Funktionen kurz und fokussiert

## Commit-Nachrichten

Bitte verwenden Sie klare und aussagekräftige Commit-Nachrichten. Folgen Sie diesem Format:

```
Kurze Zusammenfassung (max. 50 Zeichen)

Ausführlichere Erklärung, wenn nötig. Umbruch nach etwa 72 Zeichen.
Erklären Sie das Problem, das dieser Commit löst. Konzentrieren Sie
sich darauf, warum die Änderung notwendig ist, nicht wie sie
implementiert wurde.

Wenn es relevante Issues gibt, erwähnen Sie diese am Ende:
Fixes #123
```

## Lizenz

Durch das Einreichen eines Beitrags erklären Sie sich damit einverstanden, dass Ihr Beitrag unter der [MIT-Lizenz](LICENSE) veröffentlicht wird, die für das Projekt gilt.

## Verhaltenskodex

Wir erwarten von allen Teilnehmern, dass sie unseren [Verhaltenskodex](CODE_OF_CONDUCT.md) einhalten. Bitte lesen Sie ihn, bevor Sie beitragen.

## Fragen?

Wenn Sie Fragen haben oder Hilfe benötigen, können Sie:

- Ein Issue auf GitHub erstellen
- Eine Diskussion im Discussions-Bereich starten
- Uns per E-Mail kontaktieren: [<EMAIL>](mailto:<EMAIL>)

Vielen Dank für Ihre Beiträge zu DeSci-Scholar!# Beitragsrichtlinien für DeSci-Scholar

Vielen Dank für Ihr Interesse, zu DeSci-Scholar beizutragen! Als Open-Source- und Open-Science-Projekt leben wir von der Beteiligung der Community. Diese Richtlinien sollen Ihnen helfen, effektiv zum Projekt beizutragen.

## Open-Science-Prinzipien

DeSci-Scholar ist der Open-Science-Bewegung verpflichtet. Wir glauben an:

- **Transparenz**: Alle Aspekte des wissenschaftlichen Prozesses sollten offen und nachvollziehbar sein
- **Offenen Zugang**: Wissenschaftliche Erkenntnisse sollten frei zugänglich sein
- **Offene Daten**: Forschungsdaten sollten geteilt und wiederverwendbar sein
- **Offene Methoden**: Forschungsmethoden sollten transparent und reproduzierbar sein
- **Offene Infrastruktur**: Wissenschaftliche Infrastruktur sollte offen und gemeinschaftlich entwickelt werden

Bitte beachten Sie diese Prinzipien bei Ihren Beiträgen zum Projekt.

## Wie Sie beitragen können

Es gibt viele Möglichkeiten, zu DeSci-Scholar beizutragen:

1. **Code-Beiträge**: Implementierung neuer Funktionen oder Behebung von Fehlern
2. **Dokumentation**: Verbesserung oder Erweiterung der Dokumentation
3. **Tests**: Schreiben von Tests zur Verbesserung der Codequalität
4. **Fehlerberichte**: Melden von Fehlern oder Problemen
5. **Funktionsanfragen**: Vorschläge für neue Funktionen oder Verbesserungen
6. **Übersetzungen**: Übersetzung der Dokumentation oder der Benutzeroberfläche
7. **Community-Unterstützung**: Beantwortung von Fragen in Foren oder Diskussionen

## Entwicklungsprozess

### 1. Fork und Clone

1. Erstellen Sie einen Fork des Repositories auf GitHub
2. Klonen Sie Ihren Fork auf Ihren lokalen Computer:
   ```
   git clone https://github.com/IHR_BENUTZERNAME/desci-scholar.git
   cd desci-scholar
   ```
3. Fügen Sie das Upstream-Repository hinzu:
   ```
   git remote add upstream https://github.com/desci-labs/desci-scholar.git
   ```

### 2. Branches

1. Halten Sie Ihren `main`-Branch synchron mit dem Upstream:
   ```
   git checkout main
   git pull upstream main
   ```
2. Erstellen Sie einen neuen Branch für Ihre Änderungen:
   ```
   git checkout -b feature/ihre-feature-beschreibung
   ```

### 3. Entwicklung

1. Implementieren Sie Ihre Änderungen
2. Stellen Sie sicher, dass Ihr Code den Codierungsrichtlinien entspricht
3. Schreiben Sie Tests für Ihre Änderungen
4. Stellen Sie sicher, dass alle Tests bestehen:
   ```
   npm test
   ```

### 4. Commit und Push

1. Committen Sie Ihre Änderungen mit aussagekräftigen Commit-Nachrichten:
   ```
   git commit -m "Kurze Beschreibung der Änderungen"
   ```
2. Pushen Sie Ihre Änderungen zu Ihrem Fork:
   ```
   git push origin feature/ihre-feature-beschreibung
   ```

### 5. Pull Request

1. Erstellen Sie einen Pull Request von Ihrem Branch zu `desci-labs/desci-scholar:main`
2. Beschreiben Sie Ihre Änderungen ausführlich im Pull Request
3. Verlinken Sie relevante Issues
4. Warten Sie auf Feedback und reagieren Sie auf Kommentare

## Codierungsrichtlinien

- Folgen Sie dem bestehenden Codierungsstil
- Schreiben Sie klaren, lesbaren und gut dokumentierten Code
- Kommentieren Sie komplexe Logik
- Verwenden Sie aussagekräftige Variablen- und Funktionsnamen
- Halten Sie Funktionen kurz und fokussiert

## Commit-Nachrichten

Bitte verwenden Sie klare und aussagekräftige Commit-Nachrichten. Folgen Sie diesem Format:

```
Kurze Zusammenfassung (max. 50 Zeichen)

Ausführlichere Erklärung, wenn nötig. Umbruch nach etwa 72 Zeichen.
Erklären Sie das Problem, das dieser Commit löst. Konzentrieren Sie
sich darauf, warum die Änderung notwendig ist, nicht wie sie
implementiert wurde.

Wenn es relevante Issues gibt, erwähnen Sie diese am Ende:
Fixes #123
```

## Lizenz

Durch das Einreichen eines Beitrags erklären Sie sich damit einverstanden, dass Ihr Beitrag unter der [MIT-Lizenz](LICENSE) veröffentlicht wird, die für das Projekt gilt.

## Verhaltenskodex

Wir erwarten von allen Teilnehmern, dass sie unseren [Verhaltenskodex](CODE_OF_CONDUCT.md) einhalten. Bitte lesen Sie ihn, bevor Sie beitragen.

## Fragen?

Wenn Sie Fragen haben oder Hilfe benötigen, können Sie:

- Ein Issue auf GitHub erstellen
- Eine Diskussion im Discussions-Bereich starten
- Uns per E-Mail kontaktieren: [<EMAIL>](mailto:<EMAIL>)

Vielen Dank für Ihre Beiträge zu DeSci-Scholar!# Beitragsrichtlinien für DeSci-Scholar

Vielen Dank für Ihr Interesse, zu DeSci-Scholar beizutragen! Als Open-Source- und Open-Science-Projekt leben wir von der Beteiligung der Community. Diese Richtlinien sollen Ihnen helfen, effektiv zum Projekt beizutragen.

## Open-Science-Prinzipien

DeSci-Scholar ist der Open-Science-Bewegung verpflichtet. Wir glauben an:

- **Transparenz**: Alle Aspekte des wissenschaftlichen Prozesses sollten offen und nachvollziehbar sein
- **Offenen Zugang**: Wissenschaftliche Erkenntnisse sollten frei zugänglich sein
- **Offene Daten**: Forschungsdaten sollten geteilt und wiederverwendbar sein
- **Offene Methoden**: Forschungsmethoden sollten transparent und reproduzierbar sein
- **Offene Infrastruktur**: Wissenschaftliche Infrastruktur sollte offen und gemeinschaftlich entwickelt werden

Bitte beachten Sie diese Prinzipien bei Ihren Beiträgen zum Projekt.

## Wie Sie beitragen können

Es gibt viele Möglichkeiten, zu DeSci-Scholar beizutragen:

1. **Code-Beiträge**: Implementierung neuer Funktionen oder Behebung von Fehlern
2. **Dokumentation**: Verbesserung oder Erweiterung der Dokumentation
3. **Tests**: Schreiben von Tests zur Verbesserung der Codequalität
4. **Fehlerberichte**: Melden von Fehlern oder Problemen
5. **Funktionsanfragen**: Vorschläge für neue Funktionen oder Verbesserungen
6. **Übersetzungen**: Übersetzung der Dokumentation oder der Benutzeroberfläche
7. **Community-Unterstützung**: Beantwortung von Fragen in Foren oder Diskussionen

## Entwicklungsprozess

### 1. Fork und Clone

1. Erstellen Sie einen Fork des Repositories auf GitHub
2. Klonen Sie Ihren Fork auf Ihren lokalen Computer:
   ```
   git clone https://github.com/IHR_BENUTZERNAME/desci-scholar.git
   cd desci-scholar
   ```
3. Fügen Sie das Upstream-Repository hinzu:
   ```
   git remote add upstream https://github.com/desci-labs/desci-scholar.git
   ```

### 2. Branches

1. Halten Sie Ihren `main`-Branch synchron mit dem Upstream:
   ```
   git checkout main
   git pull upstream main
   ```
2. Erstellen Sie einen neuen Branch für Ihre Änderungen:
   ```
   git checkout -b feature/ihre-feature-beschreibung
   ```

### 3. Entwicklung

1. Implementieren Sie Ihre Änderungen
2. Stellen Sie sicher, dass Ihr Code den Codierungsrichtlinien entspricht
3. Schreiben Sie Tests für Ihre Änderungen
4. Stellen Sie sicher, dass alle Tests bestehen:
   ```
   npm test
   ```

### 4. Commit und Push

1. Committen Sie Ihre Änderungen mit aussagekräftigen Commit-Nachrichten:
   ```
   git commit -m "Kurze Beschreibung der Änderungen"
   ```
2. Pushen Sie Ihre Änderungen zu Ihrem Fork:
   ```
   git push origin feature/ihre-feature-beschreibung
   ```

### 5. Pull Request

1. Erstellen Sie einen Pull Request von Ihrem Branch zu `desci-labs/desci-scholar:main`
2. Beschreiben Sie Ihre Änderungen ausführlich im Pull Request
3. Verlinken Sie relevante Issues
4. Warten Sie auf Feedback und reagieren Sie auf Kommentare

## Codierungsrichtlinien

- Folgen Sie dem bestehenden Codierungsstil
- Schreiben Sie klaren, lesbaren und gut dokumentierten Code
- Kommentieren Sie komplexe Logik
- Verwenden Sie aussagekräftige Variablen- und Funktionsnamen
- Halten Sie Funktionen kurz und fokussiert

## Commit-Nachrichten

Bitte verwenden Sie klare und aussagekräftige Commit-Nachrichten. Folgen Sie diesem Format:

```
Kurze Zusammenfassung (max. 50 Zeichen)

Ausführlichere Erklärung, wenn nötig. Umbruch nach etwa 72 Zeichen.
Erklären Sie das Problem, das dieser Commit löst. Konzentrieren Sie
sich darauf, warum die Änderung notwendig ist, nicht wie sie
implementiert wurde.

Wenn es relevante Issues gibt, erwähnen Sie diese am Ende:
Fixes #123
```

## Lizenz

Durch das Einreichen eines Beitrags erklären Sie sich damit einverstanden, dass Ihr Beitrag unter der [MIT-Lizenz](LICENSE) veröffentlicht wird, die für das Projekt gilt.

## Verhaltenskodex

Wir erwarten von allen Teilnehmern, dass sie unseren [Verhaltenskodex](CODE_OF_CONDUCT.md) einhalten. Bitte lesen Sie ihn, bevor Sie beitragen.

## Fragen?

Wenn Sie Fragen haben oder Hilfe benötigen, können Sie:

- Ein Issue auf GitHub erstellen
- Eine Diskussion im Discussions-Bereich starten
- Uns per E-Mail kontaktieren: [<EMAIL>](mailto:<EMAIL>)

Vielen Dank für Ihre Beiträge zu DeSci-Scholar!# Beitragsrichtlinien für DeSci-Scholar

Vielen Dank für Ihr Interesse, zu DeSci-Scholar beizutragen! Als Open-Source- und Open-Science-Projekt leben wir von der Beteiligung der Community. Diese Richtlinien sollen Ihnen helfen, effektiv zum Projekt beizutragen.

## Open-Science-Prinzipien

DeSci-Scholar ist der Open-Science-Bewegung verpflichtet. Wir glauben an:

- **Transparenz**: Alle Aspekte des wissenschaftlichen Prozesses sollten offen und nachvollziehbar sein
- **Offenen Zugang**: Wissenschaftliche Erkenntnisse sollten frei zugänglich sein
- **Offene Daten**: Forschungsdaten sollten geteilt und wiederverwendbar sein
- **Offene Methoden**: Forschungsmethoden sollten transparent und reproduzierbar sein
- **Offene Infrastruktur**: Wissenschaftliche Infrastruktur sollte offen und gemeinschaftlich entwickelt werden

Bitte beachten Sie diese Prinzipien bei Ihren Beiträgen zum Projekt.

## Wie Sie beitragen können

Es gibt viele Möglichkeiten, zu DeSci-Scholar beizutragen:

1. **Code-Beiträge**: Implementierung neuer Funktionen oder Behebung von Fehlern
2. **Dokumentation**: Verbesserung oder Erweiterung der Dokumentation
3. **Tests**: Schreiben von Tests zur Verbesserung der Codequalität
4. **Fehlerberichte**: Melden von Fehlern oder Problemen
5. **Funktionsanfragen**: Vorschläge für neue Funktionen oder Verbesserungen
6. **Übersetzungen**: Übersetzung der Dokumentation oder der Benutzeroberfläche
7. **Community-Unterstützung**: Beantwortung von Fragen in Foren oder Diskussionen

## Entwicklungsprozess

### 1. Fork und Clone

1. Erstellen Sie einen Fork des Repositories auf GitHub
2. Klonen Sie Ihren Fork auf Ihren lokalen Computer:
   ```
   git clone https://github.com/IHR_BENUTZERNAME/desci-scholar.git
   cd desci-scholar
   ```
3. Fügen Sie das Upstream-Repository hinzu:
   ```
   git remote add upstream https://github.com/desci-labs/desci-scholar.git
   ```

### 2. Branches

1. Halten Sie Ihren `main`-Branch synchron mit dem Upstream:
   ```
   git checkout main
   git pull upstream main
   ```
2. Erstellen Sie einen neuen Branch für Ihre Änderungen:
   ```
   git checkout -b feature/ihre-feature-beschreibung
   ```

### 3. Entwicklung

1. Implementieren Sie Ihre Änderungen
2. Stellen Sie sicher, dass Ihr Code den Codierungsrichtlinien entspricht
3. Schreiben Sie Tests für Ihre Änderungen
4. Stellen Sie sicher, dass alle Tests bestehen:
   ```
   npm test
   ```

### 4. Commit und Push

1. Committen Sie Ihre Änderungen mit aussagekräftigen Commit-Nachrichten:
   ```
   git commit -m "Kurze Beschreibung der Änderungen"
   ```
2. Pushen Sie Ihre Änderungen zu Ihrem Fork:
   ```
   git push origin feature/ihre-feature-beschreibung
   ```

### 5. Pull Request

1. Erstellen Sie einen Pull Request von Ihrem Branch zu `desci-labs/desci-scholar:main`
2. Beschreiben Sie Ihre Änderungen ausführlich im Pull Request
3. Verlinken Sie relevante Issues
4. Warten Sie auf Feedback und reagieren Sie auf Kommentare

## Codierungsrichtlinien

- Folgen Sie dem bestehenden Codierungsstil
- Schreiben Sie klaren, lesbaren und gut dokumentierten Code
- Kommentieren Sie komplexe Logik
- Verwenden Sie aussagekräftige Variablen- und Funktionsnamen
- Halten Sie Funktionen kurz und fokussiert

## Commit-Nachrichten

Bitte verwenden Sie klare und aussagekräftige Commit-Nachrichten. Folgen Sie diesem Format:

```
Kurze Zusammenfassung (max. 50 Zeichen)

Ausführlichere Erklärung, wenn nötig. Umbruch nach etwa 72 Zeichen.
Erklären Sie das Problem, das dieser Commit löst. Konzentrieren Sie
sich darauf, warum die Änderung notwendig ist, nicht wie sie
implementiert wurde.

Wenn es relevante Issues gibt, erwähnen Sie diese am Ende:
Fixes #123
```

## Lizenz

Durch das Einreichen eines Beitrags erklären Sie sich damit einverstanden, dass Ihr Beitrag unter der [MIT-Lizenz](LICENSE) veröffentlicht wird, die für das Projekt gilt.

## Verhaltenskodex

Wir erwarten von allen Teilnehmern, dass sie unseren [Verhaltenskodex](CODE_OF_CONDUCT.md) einhalten. Bitte lesen Sie ihn, bevor Sie beitragen.

## Fragen?

Wenn Sie Fragen haben oder Hilfe benötigen, können Sie:

- Ein Issue auf GitHub erstellen
- Eine Diskussion im Discussions-Bereich starten
- Uns per E-Mail kontaktieren: [<EMAIL>](mailto:<EMAIL>)

Vielen Dank für Ihre Beiträge zu DeSci-Scholar!# Beitragsrichtlinien für DeSci-Scholar

Vielen Dank für Ihr Interesse, zu DeSci-Scholar beizutragen! Als Open-Source- und Open-Science-Projekt leben wir von der Beteiligung der Community. Diese Richtlinien sollen Ihnen helfen, effektiv zum Projekt beizutragen.

## Open-Science-Prinzipien

DeSci-Scholar ist der Open-Science-Bewegung verpflichtet. Wir glauben an:

- **Transparenz**: Alle Aspekte des wissenschaftlichen Prozesses sollten offen und nachvollziehbar sein
- **Offenen Zugang**: Wissenschaftliche Erkenntnisse sollten frei zugänglich sein
- **Offene Daten**: Forschungsdaten sollten geteilt und wiederverwendbar sein
- **Offene Methoden**: Forschungsmethoden sollten transparent und reproduzierbar sein
- **Offene Infrastruktur**: Wissenschaftliche Infrastruktur sollte offen und gemeinschaftlich entwickelt werden

Bitte beachten Sie diese Prinzipien bei Ihren Beiträgen zum Projekt.

## Wie Sie beitragen können

Es gibt viele Möglichkeiten, zu DeSci-Scholar beizutragen:

1. **Code-Beiträge**: Implementierung neuer Funktionen oder Behebung von Fehlern
2. **Dokumentation**: Verbesserung oder Erweiterung der Dokumentation
3. **Tests**: Schreiben von Tests zur Verbesserung der Codequalität
4. **Fehlerberichte**: Melden von Fehlern oder Problemen
5. **Funktionsanfragen**: Vorschläge für neue Funktionen oder Verbesserungen
6. **Übersetzungen**: Übersetzung der Dokumentation oder der Benutzeroberfläche
7. **Community-Unterstützung**: Beantwortung von Fragen in Foren oder Diskussionen

## Entwicklungsprozess

### 1. Fork und Clone

1. Erstellen Sie einen Fork des Repositories auf GitHub
2. Klonen Sie Ihren Fork auf Ihren lokalen Computer:
   ```
   git clone https://github.com/IHR_BENUTZERNAME/desci-scholar.git
   cd desci-scholar
   ```
3. Fügen Sie das Upstream-Repository hinzu:
   ```
   git remote add upstream https://github.com/desci-labs/desci-scholar.git
   ```

### 2. Branches

1. Halten Sie Ihren `main`-Branch synchron mit dem Upstream:
   ```
   git checkout main
   git pull upstream main
   ```
2. Erstellen Sie einen neuen Branch für Ihre Änderungen:
   ```
   git checkout -b feature/ihre-feature-beschreibung
   ```

### 3. Entwicklung

1. Implementieren Sie Ihre Änderungen
2. Stellen Sie sicher, dass Ihr Code den Codierungsrichtlinien entspricht
3. Schreiben Sie Tests für Ihre Änderungen
4. Stellen Sie sicher, dass alle Tests bestehen:
   ```
   npm test
   ```

### 4. Commit und Push

1. Committen Sie Ihre Änderungen mit aussagekräftigen Commit-Nachrichten:
   ```
   git commit -m "Kurze Beschreibung der Änderungen"
   ```
2. Pushen Sie Ihre Änderungen zu Ihrem Fork:
   ```
   git push origin feature/ihre-feature-beschreibung
   ```

### 5. Pull Request

1. Erstellen Sie einen Pull Request von Ihrem Branch zu `desci-labs/desci-scholar:main`
2. Beschreiben Sie Ihre Änderungen ausführlich im Pull Request
3. Verlinken Sie relevante Issues
4. Warten Sie auf Feedback und reagieren Sie auf Kommentare

## Codierungsrichtlinien

- Folgen Sie dem bestehenden Codierungsstil
- Schreiben Sie klaren, lesbaren und gut dokumentierten Code
- Kommentieren Sie komplexe Logik
- Verwenden Sie aussagekräftige Variablen- und Funktionsnamen
- Halten Sie Funktionen kurz und fokussiert

## Commit-Nachrichten

Bitte verwenden Sie klare und aussagekräftige Commit-Nachrichten. Folgen Sie diesem Format:

```
Kurze Zusammenfassung (max. 50 Zeichen)

Ausführlichere Erklärung, wenn nötig. Umbruch nach etwa 72 Zeichen.
Erklären Sie das Problem, das dieser Commit löst. Konzentrieren Sie
sich darauf, warum die Änderung notwendig ist, nicht wie sie
implementiert wurde.

Wenn es relevante Issues gibt, erwähnen Sie diese am Ende:
Fixes #123
```

## Lizenz

Durch das Einreichen eines Beitrags erklären Sie sich damit einverstanden, dass Ihr Beitrag unter der [MIT-Lizenz](LICENSE) veröffentlicht wird, die für das Projekt gilt.

## Verhaltenskodex

Wir erwarten von allen Teilnehmern, dass sie unseren [Verhaltenskodex](CODE_OF_CONDUCT.md) einhalten. Bitte lesen Sie ihn, bevor Sie beitragen.

## Fragen?

Wenn Sie Fragen haben oder Hilfe benötigen, können Sie:

- Ein Issue auf GitHub erstellen
- Eine Diskussion im Discussions-Bereich starten
- Uns per E-Mail kontaktieren: [<EMAIL>](mailto:<EMAIL>)

Vielen Dank für Ihre Beiträge zu DeSci-Scholar!# Beitragsrichtlinien für DeSci-Scholar

Vielen Dank für Ihr Interesse, zu DeSci-Scholar beizutragen! Als Open-Source- und Open-Science-Projekt leben wir von der Beteiligung der Community. Diese Richtlinien sollen Ihnen helfen, effektiv zum Projekt beizutragen.

## Open-Science-Prinzipien

DeSci-Scholar ist der Open-Science-Bewegung verpflichtet. Wir glauben an:

- **Transparenz**: Alle Aspekte des wissenschaftlichen Prozesses sollten offen und nachvollziehbar sein
- **Offenen Zugang**: Wissenschaftliche Erkenntnisse sollten frei zugänglich sein
- **Offene Daten**: Forschungsdaten sollten geteilt und wiederverwendbar sein
- **Offene Methoden**: Forschungsmethoden sollten transparent und reproduzierbar sein
- **Offene Infrastruktur**: Wissenschaftliche Infrastruktur sollte offen und gemeinschaftlich entwickelt werden

Bitte beachten Sie diese Prinzipien bei Ihren Beiträgen zum Projekt.

## Wie Sie beitragen können

Es gibt viele Möglichkeiten, zu DeSci-Scholar beizutragen:

1. **Code-Beiträge**: Implementierung neuer Funktionen oder Behebung von Fehlern
2. **Dokumentation**: Verbesserung oder Erweiterung der Dokumentation
3. **Tests**: Schreiben von Tests zur Verbesserung der Codequalität
4. **Fehlerberichte**: Melden von Fehlern oder Problemen
5. **Funktionsanfragen**: Vorschläge für neue Funktionen oder Verbesserungen
6. **Übersetzungen**: Übersetzung der Dokumentation oder der Benutzeroberfläche
7. **Community-Unterstützung**: Beantwortung von Fragen in Foren oder Diskussionen

## Entwicklungsprozess

### 1. Fork und Clone

1. Erstellen Sie einen Fork des Repositories auf GitHub
2. Klonen Sie Ihren Fork auf Ihren lokalen Computer:
   ```
   git clone https://github.com/IHR_BENUTZERNAME/desci-scholar.git
   cd desci-scholar
   ```
3. Fügen Sie das Upstream-Repository hinzu:
   ```
   git remote add upstream https://github.com/desci-labs/desci-scholar.git
   ```

### 2. Branches

1. Halten Sie Ihren `main`-Branch synchron mit dem Upstream:
   ```
   git checkout main
   git pull upstream main
   ```
2. Erstellen Sie einen neuen Branch für Ihre Änderungen:
   ```
   git checkout -b feature/ihre-feature-beschreibung
   ```

### 3. Entwicklung

1. Implementieren Sie Ihre Änderungen
2. Stellen Sie sicher, dass Ihr Code den Codierungsrichtlinien entspricht
3. Schreiben Sie Tests für Ihre Änderungen
4. Stellen Sie sicher, dass alle Tests bestehen:
   ```
   npm test
   ```

### 4. Commit und Push

1. Committen Sie Ihre Änderungen mit aussagekräftigen Commit-Nachrichten:
   ```
   git commit -m "Kurze Beschreibung der Änderungen"
   ```
2. Pushen Sie Ihre Änderungen zu Ihrem Fork:
   ```
   git push origin feature/ihre-feature-beschreibung
   ```

### 5. Pull Request

1. Erstellen Sie einen Pull Request von Ihrem Branch zu `desci-labs/desci-scholar:main`
2. Beschreiben Sie Ihre Änderungen ausführlich im Pull Request
3. Verlinken Sie relevante Issues
4. Warten Sie auf Feedback und reagieren Sie auf Kommentare

## Codierungsrichtlinien

- Folgen Sie dem bestehenden Codierungsstil
- Schreiben Sie klaren, lesbaren und gut dokumentierten Code
- Kommentieren Sie komplexe Logik
- Verwenden Sie aussagekräftige Variablen- und Funktionsnamen
- Halten Sie Funktionen kurz und fokussiert

## Commit-Nachrichten

Bitte verwenden Sie klare und aussagekräftige Commit-Nachrichten. Folgen Sie diesem Format:

```
Kurze Zusammenfassung (max. 50 Zeichen)

Ausführlichere Erklärung, wenn nötig. Umbruch nach etwa 72 Zeichen.
Erklären Sie das Problem, das dieser Commit löst. Konzentrieren Sie
sich darauf, warum die Änderung notwendig ist, nicht wie sie
implementiert wurde.

Wenn es relevante Issues gibt, erwähnen Sie diese am Ende:
Fixes #123
```

## Lizenz

Durch das Einreichen eines Beitrags erklären Sie sich damit einverstanden, dass Ihr Beitrag unter der [MIT-Lizenz](LICENSE) veröffentlicht wird, die für das Projekt gilt.

## Verhaltenskodex

Wir erwarten von allen Teilnehmern, dass sie unseren [Verhaltenskodex](CODE_OF_CONDUCT.md) einhalten. Bitte lesen Sie ihn, bevor Sie beitragen.

## Fragen?

Wenn Sie Fragen haben oder Hilfe benötigen, können Sie:

- Ein Issue auf GitHub erstellen
- Eine Diskussion im Discussions-Bereich starten
- Uns per E-Mail kontaktieren: [<EMAIL>](mailto:<EMAIL>)

Vielen Dank für Ihre Beiträge zu DeSci-Scholar!# Beitragsrichtlinien für DeSci-Scholar

Vielen Dank für Ihr Interesse, zu DeSci-Scholar beizutragen! Als Open-Source- und Open-Science-Projekt leben wir von der Beteiligung der Community. Diese Richtlinien sollen Ihnen helfen, effektiv zum Projekt beizutragen.

## Open-Science-Prinzipien

DeSci-Scholar ist der Open-Science-Bewegung verpflichtet. Wir glauben an:

- **Transparenz**: Alle Aspekte des wissenschaftlichen Prozesses sollten offen und nachvollziehbar sein
- **Offenen Zugang**: Wissenschaftliche Erkenntnisse sollten frei zugänglich sein
- **Offene Daten**: Forschungsdaten sollten geteilt und wiederverwendbar sein
- **Offene Methoden**: Forschungsmethoden sollten transparent und reproduzierbar sein
- **Offene Infrastruktur**: Wissenschaftliche Infrastruktur sollte offen und gemeinschaftlich entwickelt werden

Bitte beachten Sie diese Prinzipien bei Ihren Beiträgen zum Projekt.

## Wie Sie beitragen können

Es gibt viele Möglichkeiten, zu DeSci-Scholar beizutragen:

1. **Code-Beiträge**: Implementierung neuer Funktionen oder Behebung von Fehlern
2. **Dokumentation**: Verbesserung oder Erweiterung der Dokumentation
3. **Tests**: Schreiben von Tests zur Verbesserung der Codequalität
4. **Fehlerberichte**: Melden von Fehlern oder Problemen
5. **Funktionsanfragen**: Vorschläge für neue Funktionen oder Verbesserungen
6. **Übersetzungen**: Übersetzung der Dokumentation oder der Benutzeroberfläche
7. **Community-Unterstützung**: Beantwortung von Fragen in Foren oder Diskussionen

## Entwicklungsprozess

### 1. Fork und Clone

1. Erstellen Sie einen Fork des Repositories auf GitHub
2. Klonen Sie Ihren Fork auf Ihren lokalen Computer:
   ```
   git clone https://github.com/IHR_BENUTZERNAME/desci-scholar.git
   cd desci-scholar
   ```
3. Fügen Sie das Upstream-Repository hinzu:
   ```
   git remote add upstream https://github.com/desci-labs/desci-scholar.git
   ```

### 2. Branches

1. Halten Sie Ihren `main`-Branch synchron mit dem Upstream:
   ```
   git checkout main
   git pull upstream main
   ```
2. Erstellen Sie einen neuen Branch für Ihre Änderungen:
   ```
   git checkout -b feature/ihre-feature-beschreibung
   ```

### 3. Entwicklung

1. Implementieren Sie Ihre Änderungen
2. Stellen Sie sicher, dass Ihr Code den Codierungsrichtlinien entspricht
3. Schreiben Sie Tests für Ihre Änderungen
4. Stellen Sie sicher, dass alle Tests bestehen:
   ```
   npm test
   ```

### 4. Commit und Push

1. Committen Sie Ihre Änderungen mit aussagekräftigen Commit-Nachrichten:
   ```
   git commit -m "Kurze Beschreibung der Änderungen"
   ```
2. Pushen Sie Ihre Änderungen zu Ihrem Fork:
   ```
   git push origin feature/ihre-feature-beschreibung
   ```

### 5. Pull Request

1. Erstellen Sie einen Pull Request von Ihrem Branch zu `desci-labs/desci-scholar:main`
2. Beschreiben Sie Ihre Änderungen ausführlich im Pull Request
3. Verlinken Sie relevante Issues
4. Warten Sie auf Feedback und reagieren Sie auf Kommentare

## Codierungsrichtlinien

- Folgen Sie dem bestehenden Codierungsstil
- Schreiben Sie klaren, lesbaren und gut dokumentierten Code
- Kommentieren Sie komplexe Logik
- Verwenden Sie aussagekräftige Variablen- und Funktionsnamen
- Halten Sie Funktionen kurz und fokussiert

## Commit-Nachrichten

Bitte verwenden Sie klare und aussagekräftige Commit-Nachrichten. Folgen Sie diesem Format:

```
Kurze Zusammenfassung (max. 50 Zeichen)

Ausführlichere Erklärung, wenn nötig. Umbruch nach etwa 72 Zeichen.
Erklären Sie das Problem, das dieser Commit löst. Konzentrieren Sie
sich darauf, warum die Änderung notwendig ist, nicht wie sie
implementiert wurde.

Wenn es relevante Issues gibt, erwähnen Sie diese am Ende:
Fixes #123
```

## Lizenz

Durch das Einreichen eines Beitrags erklären Sie sich damit einverstanden, dass Ihr Beitrag unter der [MIT-Lizenz](LICENSE) veröffentlicht wird, die für das Projekt gilt.

## Verhaltenskodex

Wir erwarten von allen Teilnehmern, dass sie unseren [Verhaltenskodex](CODE_OF_CONDUCT.md) einhalten. Bitte lesen Sie ihn, bevor Sie beitragen.

## Fragen?

Wenn Sie Fragen haben oder Hilfe benötigen, können Sie:

- Ein Issue auf GitHub erstellen
- Eine Diskussion im Discussions-Bereich starten
- Uns per E-Mail kontaktieren: [<EMAIL>](mailto:<EMAIL>)

Vielen Dank für Ihre Beiträge zu DeSci-Scholar!# Beitragsrichtlinien für DeSci-Scholar

Vielen Dank für Ihr Interesse, zu DeSci-Scholar beizutragen! Als Open-Source- und Open-Science-Projekt leben wir von der Beteiligung der Community. Diese Richtlinien sollen Ihnen helfen, effektiv zum Projekt beizutragen.

## Open-Science-Prinzipien

DeSci-Scholar ist der Open-Science-Bewegung verpflichtet. Wir glauben an:

- **Transparenz**: Alle Aspekte des wissenschaftlichen Prozesses sollten offen und nachvollziehbar sein
- **Offenen Zugang**: Wissenschaftliche Erkenntnisse sollten frei zugänglich sein
- **Offene Daten**: Forschungsdaten sollten geteilt und wiederverwendbar sein
- **Offene Methoden**: Forschungsmethoden sollten transparent und reproduzierbar sein
- **Offene Infrastruktur**: Wissenschaftliche Infrastruktur sollte offen und gemeinschaftlich entwickelt werden

Bitte beachten Sie diese Prinzipien bei Ihren Beiträgen zum Projekt.

## Wie Sie beitragen können

Es gibt viele Möglichkeiten, zu DeSci-Scholar beizutragen:

1. **Code-Beiträge**: Implementierung neuer Funktionen oder Behebung von Fehlern
2. **Dokumentation**: Verbesserung oder Erweiterung der Dokumentation
3. **Tests**: Schreiben von Tests zur Verbesserung der Codequalität
4. **Fehlerberichte**: Melden von Fehlern oder Problemen
5. **Funktionsanfragen**: Vorschläge für neue Funktionen oder Verbesserungen
6. **Übersetzungen**: Übersetzung der Dokumentation oder der Benutzeroberfläche
7. **Community-Unterstützung**: Beantwortung von Fragen in Foren oder Diskussionen

## Entwicklungsprozess

### 1. Fork und Clone

1. Erstellen Sie einen Fork des Repositories auf GitHub
2. Klonen Sie Ihren Fork auf Ihren lokalen Computer:
   ```
   git clone https://github.com/IHR_BENUTZERNAME/desci-scholar.git
   cd desci-scholar
   ```
3. Fügen Sie das Upstream-Repository hinzu:
   ```
   git remote add upstream https://github.com/desci-labs/desci-scholar.git
   ```

### 2. Branches

1. Halten Sie Ihren `main`-Branch synchron mit dem Upstream:
   ```
   git checkout main
   git pull upstream main
   ```
2. Erstellen Sie einen neuen Branch für Ihre Änderungen:
   ```
   git checkout -b feature/ihre-feature-beschreibung
   ```

### 3. Entwicklung

1. Implementieren Sie Ihre Änderungen
2. Stellen Sie sicher, dass Ihr Code den Codierungsrichtlinien entspricht
3. Schreiben Sie Tests für Ihre Änderungen
4. Stellen Sie sicher, dass alle Tests bestehen:
   ```
   npm test
   ```

### 4. Commit und Push

1. Committen Sie Ihre Änderungen mit aussagekräftigen Commit-Nachrichten:
   ```
   git commit -m "Kurze Beschreibung der Änderungen"
   ```
2. Pushen Sie Ihre Änderungen zu Ihrem Fork:
   ```
   git push origin feature/ihre-feature-beschreibung
   ```

### 5. Pull Request

1. Erstellen Sie einen Pull Request von Ihrem Branch zu `desci-labs/desci-scholar:main`
2. Beschreiben Sie Ihre Änderungen ausführlich im Pull Request
3. Verlinken Sie relevante Issues
4. Warten Sie auf Feedback und reagieren Sie auf Kommentare

## Codierungsrichtlinien

- Folgen Sie dem bestehenden Codierungsstil
- Schreiben Sie klaren, lesbaren und gut dokumentierten Code
- Kommentieren Sie komplexe Logik
- Verwenden Sie aussagekräftige Variablen- und Funktionsnamen
- Halten Sie Funktionen kurz und fokussiert

## Commit-Nachrichten

Bitte verwenden Sie klare und aussagekräftige Commit-Nachrichten. Folgen Sie diesem Format:

```
Kurze Zusammenfassung (max. 50 Zeichen)

Ausführlichere Erklärung, wenn nötig. Umbruch nach etwa 72 Zeichen.
Erklären Sie das Problem, das dieser Commit löst. Konzentrieren Sie
sich darauf, warum die Änderung notwendig ist, nicht wie sie
implementiert wurde.

Wenn es relevante Issues gibt, erwähnen Sie diese am Ende:
Fixes #123
```

## Lizenz

Durch das Einreichen eines Beitrags erklären Sie sich damit einverstanden, dass Ihr Beitrag unter der [MIT-Lizenz](LICENSE) veröffentlicht wird, die für das Projekt gilt.

## Verhaltenskodex

Wir erwarten von allen Teilnehmern, dass sie unseren [Verhaltenskodex](CODE_OF_CONDUCT.md) einhalten. Bitte lesen Sie ihn, bevor Sie beitragen.

## Fragen?

Wenn Sie Fragen haben oder Hilfe benötigen, können Sie:

- Ein Issue auf GitHub erstellen
- Eine Diskussion im Discussions-Bereich starten
- Uns per E-Mail kontaktieren: [<EMAIL>](mailto:<EMAIL>)

Vielen Dank für Ihre Beiträge zu DeSci-Scholar!# Beitragsrichtlinien für DeSci-Scholar

Vielen Dank für Ihr Interesse, zu DeSci-Scholar beizutragen! Als Open-Source- und Open-Science-Projekt leben wir von der Beteiligung der Community. Diese Richtlinien sollen Ihnen helfen, effektiv zum Projekt beizutragen.

## Open-Science-Prinzipien

DeSci-Scholar ist der Open-Science-Bewegung verpflichtet. Wir glauben an:

- **Transparenz**: Alle Aspekte des wissenschaftlichen Prozesses sollten offen und nachvollziehbar sein
- **Offenen Zugang**: Wissenschaftliche Erkenntnisse sollten frei zugänglich sein
- **Offene Daten**: Forschungsdaten sollten geteilt und wiederverwendbar sein
- **Offene Methoden**: Forschungsmethoden sollten transparent und reproduzierbar sein
- **Offene Infrastruktur**: Wissenschaftliche Infrastruktur sollte offen und gemeinschaftlich entwickelt werden

Bitte beachten Sie diese Prinzipien bei Ihren Beiträgen zum Projekt.

## Wie Sie beitragen können

Es gibt viele Möglichkeiten, zu DeSci-Scholar beizutragen:

1. **Code-Beiträge**: Implementierung neuer Funktionen oder Behebung von Fehlern
2. **Dokumentation**: Verbesserung oder Erweiterung der Dokumentation
3. **Tests**: Schreiben von Tests zur Verbesserung der Codequalität
4. **Fehlerberichte**: Melden von Fehlern oder Problemen
5. **Funktionsanfragen**: Vorschläge für neue Funktionen oder Verbesserungen
6. **Übersetzungen**: Übersetzung der Dokumentation oder der Benutzeroberfläche
7. **Community-Unterstützung**: Beantwortung von Fragen in Foren oder Diskussionen

## Entwicklungsprozess

### 1. Fork und Clone

1. Erstellen Sie einen Fork des Repositories auf GitHub
2. Klonen Sie Ihren Fork auf Ihren lokalen Computer:
   ```
   git clone https://github.com/IHR_BENUTZERNAME/desci-scholar.git
   cd desci-scholar
   ```
3. Fügen Sie das Upstream-Repository hinzu:
   ```
   git remote add upstream https://github.com/desci-labs/desci-scholar.git
   ```

### 2. Branches

1. Halten Sie Ihren `main`-Branch synchron mit dem Upstream:
   ```
   git checkout main
   git pull upstream main
   ```
2. Erstellen Sie einen neuen Branch für Ihre Änderungen:
   ```
   git checkout -b feature/ihre-feature-beschreibung
   ```

### 3. Entwicklung

1. Implementieren Sie Ihre Änderungen
2. Stellen Sie sicher, dass Ihr Code den Codierungsrichtlinien entspricht
3. Schreiben Sie Tests für Ihre Änderungen
4. Stellen Sie sicher, dass alle Tests bestehen:
   ```
   npm test
   ```

### 4. Commit und Push

1. Committen Sie Ihre Änderungen mit aussagekräftigen Commit-Nachrichten:
   ```
   git commit -m "Kurze Beschreibung der Änderungen"
   ```
2. Pushen Sie Ihre Änderungen zu Ihrem Fork:
   ```
   git push origin feature/ihre-feature-beschreibung
   ```

### 5. Pull Request

1. Erstellen Sie einen Pull Request von Ihrem Branch zu `desci-labs/desci-scholar:main`
2. Beschreiben Sie Ihre Änderungen ausführlich im Pull Request
3. Verlinken Sie relevante Issues
4. Warten Sie auf Feedback und reagieren Sie auf Kommentare

## Codierungsrichtlinien

- Folgen Sie dem bestehenden Codierungsstil
- Schreiben Sie klaren, lesbaren und gut dokumentierten Code
- Kommentieren Sie komplexe Logik
- Verwenden Sie aussagekräftige Variablen- und Funktionsnamen
- Halten Sie Funktionen kurz und fokussiert

## Commit-Nachrichten

Bitte verwenden Sie klare und aussagekräftige Commit-Nachrichten. Folgen Sie diesem Format:

```
Kurze Zusammenfassung (max. 50 Zeichen)

Ausführlichere Erklärung, wenn nötig. Umbruch nach etwa 72 Zeichen.
Erklären Sie das Problem, das dieser Commit löst. Konzentrieren Sie
sich darauf, warum die Änderung notwendig ist, nicht wie sie
implementiert wurde.

Wenn es relevante Issues gibt, erwähnen Sie diese am Ende:
Fixes #123
```

## Lizenz

Durch das Einreichen eines Beitrags erklären Sie sich damit einverstanden, dass Ihr Beitrag unter der [MIT-Lizenz](LICENSE) veröffentlicht wird, die für das Projekt gilt.

## Verhaltenskodex

Wir erwarten von allen Teilnehmern, dass sie unseren [Verhaltenskodex](CODE_OF_CONDUCT.md) einhalten. Bitte lesen Sie ihn, bevor Sie beitragen.

## Fragen?

Wenn Sie Fragen haben oder Hilfe benötigen, können Sie:

- Ein Issue auf GitHub erstellen
- Eine Diskussion im Discussions-Bereich starten
- Uns per E-Mail kontaktieren: [<EMAIL>](mailto:<EMAIL>)

Vielen Dank für Ihre Beiträge zu DeSci-Scholar!# Beitragsrichtlinien für DeSci-Scholar

Vielen Dank für Ihr Interesse, zu DeSci-Scholar beizutragen! Als Open-Source- und Open-Science-Projekt leben wir von der Beteiligung der Community. Diese Richtlinien sollen Ihnen helfen, effektiv zum Projekt beizutragen.

## Open-Science-Prinzipien

DeSci-Scholar ist der Open-Science-Bewegung verpflichtet. Wir glauben an:

- **Transparenz**: Alle Aspekte des wissenschaftlichen Prozesses sollten offen und nachvollziehbar sein
- **Offenen Zugang**: Wissenschaftliche Erkenntnisse sollten frei zugänglich sein
- **Offene Daten**: Forschungsdaten sollten geteilt und wiederverwendbar sein
- **Offene Methoden**: Forschungsmethoden sollten transparent und reproduzierbar sein
- **Offene Infrastruktur**: Wissenschaftliche Infrastruktur sollte offen und gemeinschaftlich entwickelt werden

Bitte beachten Sie diese Prinzipien bei Ihren Beiträgen zum Projekt.

## Wie Sie beitragen können

Es gibt viele Möglichkeiten, zu DeSci-Scholar beizutragen:

1. **Code-Beiträge**: Implementierung neuer Funktionen oder Behebung von Fehlern
2. **Dokumentation**: Verbesserung oder Erweiterung der Dokumentation
3. **Tests**: Schreiben von Tests zur Verbesserung der Codequalität
4. **Fehlerberichte**: Melden von Fehlern oder Problemen
5. **Funktionsanfragen**: Vorschläge für neue Funktionen oder Verbesserungen
6. **Übersetzungen**: Übersetzung der Dokumentation oder der Benutzeroberfläche
7. **Community-Unterstützung**: Beantwortung von Fragen in Foren oder Diskussionen

## Entwicklungsprozess

### 1. Fork und Clone

1. Erstellen Sie einen Fork des Repositories auf GitHub
2. Klonen Sie Ihren Fork auf Ihren lokalen Computer:
   ```
   git clone https://github.com/IHR_BENUTZERNAME/desci-scholar.git
   cd desci-scholar
   ```
3. Fügen Sie das Upstream-Repository hinzu:
   ```
   git remote add upstream https://github.com/desci-labs/desci-scholar.git
   ```

### 2. Branches

1. Halten Sie Ihren `main`-Branch synchron mit dem Upstream:
   ```
   git checkout main
   git pull upstream main
   ```
2. Erstellen Sie einen neuen Branch für Ihre Änderungen:
   ```
   git checkout -b feature/ihre-feature-beschreibung
   ```

### 3. Entwicklung

1. Implementieren Sie Ihre Änderungen
2. Stellen Sie sicher, dass Ihr Code den Codierungsrichtlinien entspricht
3. Schreiben Sie Tests für Ihre Änderungen
4. Stellen Sie sicher, dass alle Tests bestehen:
   ```
   npm test
   ```

### 4. Commit und Push

1. Committen Sie Ihre Änderungen mit aussagekräftigen Commit-Nachrichten:
   ```
   git commit -m "Kurze Beschreibung der Änderungen"
   ```
2. Pushen Sie Ihre Änderungen zu Ihrem Fork:
   ```
   git push origin feature/ihre-feature-beschreibung
   ```

### 5. Pull Request

1. Erstellen Sie einen Pull Request von Ihrem Branch zu `desci-labs/desci-scholar:main`
2. Beschreiben Sie Ihre Änderungen ausführlich im Pull Request
3. Verlinken Sie relevante Issues
4. Warten Sie auf Feedback und reagieren Sie auf Kommentare

## Codierungsrichtlinien

- Folgen Sie dem bestehenden Codierungsstil
- Schreiben Sie klaren, lesbaren und gut dokumentierten Code
- Kommentieren Sie komplexe Logik
- Verwenden Sie aussagekräftige Variablen- und Funktionsnamen
- Halten Sie Funktionen kurz und fokussiert

## Commit-Nachrichten

Bitte verwenden Sie klare und aussagekräftige Commit-Nachrichten. Folgen Sie diesem Format:

```
Kurze Zusammenfassung (max. 50 Zeichen)

Ausführlichere Erklärung, wenn nötig. Umbruch nach etwa 72 Zeichen.
Erklären Sie das Problem, das dieser Commit löst. Konzentrieren Sie
sich darauf, warum die Änderung notwendig ist, nicht wie sie
implementiert wurde.

Wenn es relevante Issues gibt, erwähnen Sie diese am Ende:
Fixes #123
```

## Lizenz

Durch das Einreichen eines Beitrags erklären Sie sich damit einverstanden, dass Ihr Beitrag unter der [MIT-Lizenz](LICENSE) veröffentlicht wird, die für das Projekt gilt.

## Verhaltenskodex

Wir erwarten von allen Teilnehmern, dass sie unseren [Verhaltenskodex](CODE_OF_CONDUCT.md) einhalten. Bitte lesen Sie ihn, bevor Sie beitragen.

## Fragen?

Wenn Sie Fragen haben oder Hilfe benötigen, können Sie:

- Ein Issue auf GitHub erstellen
- Eine Diskussion im Discussions-Bereich starten
- Uns per E-Mail kontaktieren: [<EMAIL>](mailto:<EMAIL>)

Vielen Dank für Ihre Beiträge zu DeSci-Scholar!# Beitragsrichtlinien für DeSci-Scholar

Vielen Dank für Ihr Interesse, zu DeSci-Scholar beizutragen! Als Open-Source- und Open-Science-Projekt leben wir von der Beteiligung der Community. Diese Richtlinien sollen Ihnen helfen, effektiv zum Projekt beizutragen.

## Open-Science-Prinzipien

DeSci-Scholar ist der Open-Science-Bewegung verpflichtet. Wir glauben an:

- **Transparenz**: Alle Aspekte des wissenschaftlichen Prozesses sollten offen und nachvollziehbar sein
- **Offenen Zugang**: Wissenschaftliche Erkenntnisse sollten frei zugänglich sein
- **Offene Daten**: Forschungsdaten sollten geteilt und wiederverwendbar sein
- **Offene Methoden**: Forschungsmethoden sollten transparent und reproduzierbar sein
- **Offene Infrastruktur**: Wissenschaftliche Infrastruktur sollte offen und gemeinschaftlich entwickelt werden

Bitte beachten Sie diese Prinzipien bei Ihren Beiträgen zum Projekt.

## Wie Sie beitragen können

Es gibt viele Möglichkeiten, zu DeSci-Scholar beizutragen:

1. **Code-Beiträge**: Implementierung neuer Funktionen oder Behebung von Fehlern
2. **Dokumentation**: Verbesserung oder Erweiterung der Dokumentation
3. **Tests**: Schreiben von Tests zur Verbesserung der Codequalität
4. **Fehlerberichte**: Melden von Fehlern oder Problemen
5. **Funktionsanfragen**: Vorschläge für neue Funktionen oder Verbesserungen
6. **Übersetzungen**: Übersetzung der Dokumentation oder der Benutzeroberfläche
7. **Community-Unterstützung**: Beantwortung von Fragen in Foren oder Diskussionen

## Entwicklungsprozess

### 1. Fork und Clone

1. Erstellen Sie einen Fork des Repositories auf GitHub
2. Klonen Sie Ihren Fork auf Ihren lokalen Computer:
   ```
   git clone https://github.com/IHR_BENUTZERNAME/desci-scholar.git
   cd desci-scholar
   ```
3. Fügen Sie das Upstream-Repository hinzu:
   ```
   git remote add upstream https://github.com/desci-labs/desci-scholar.git
   ```

### 2. Branches

1. Halten Sie Ihren `main`-Branch synchron mit dem Upstream:
   ```
   git checkout main
   git pull upstream main
   ```
2. Erstellen Sie einen neuen Branch für Ihre Änderungen:
   ```
   git checkout -b feature/ihre-feature-beschreibung
   ```

### 3. Entwicklung

1. Implementieren Sie Ihre Änderungen
2. Stellen Sie sicher, dass Ihr Code den Codierungsrichtlinien entspricht
3. Schreiben Sie Tests für Ihre Änderungen
4. Stellen Sie sicher, dass alle Tests bestehen:
   ```
   npm test
   ```

### 4. Commit und Push

1. Committen Sie Ihre Änderungen mit aussagekräftigen Commit-Nachrichten:
   ```
   git commit -m "Kurze Beschreibung der Änderungen"
   ```
2. Pushen Sie Ihre Änderungen zu Ihrem Fork:
   ```
   git push origin feature/ihre-feature-beschreibung
   ```

### 5. Pull Request

1. Erstellen Sie einen Pull Request von Ihrem Branch zu `desci-labs/desci-scholar:main`
2. Beschreiben Sie Ihre Änderungen ausführlich im Pull Request
3. Verlinken Sie relevante Issues
4. Warten Sie auf Feedback und reagieren Sie auf Kommentare

## Codierungsrichtlinien

- Folgen Sie dem bestehenden Codierungsstil
- Schreiben Sie klaren, lesbaren und gut dokumentierten Code
- Kommentieren Sie komplexe Logik
- Verwenden Sie aussagekräftige Variablen- und Funktionsnamen
- Halten Sie Funktionen kurz und fokussiert

## Commit-Nachrichten

Bitte verwenden Sie klare und aussagekräftige Commit-Nachrichten. Folgen Sie diesem Format:

```
Kurze Zusammenfassung (max. 50 Zeichen)

Ausführlichere Erklärung, wenn nötig. Umbruch nach etwa 72 Zeichen.
Erklären Sie das Problem, das dieser Commit löst. Konzentrieren Sie
sich darauf, warum die Änderung notwendig ist, nicht wie sie
implementiert wurde.

Wenn es relevante Issues gibt, erwähnen Sie diese am Ende:
Fixes #123
```

## Lizenz

Durch das Einreichen eines Beitrags erklären Sie sich damit einverstanden, dass Ihr Beitrag unter der [MIT-Lizenz](LICENSE) veröffentlicht wird, die für das Projekt gilt.

## Verhaltenskodex

Wir erwarten von allen Teilnehmern, dass sie unseren [Verhaltenskodex](CODE_OF_CONDUCT.md) einhalten. Bitte lesen Sie ihn, bevor Sie beitragen.

## Fragen?

Wenn Sie Fragen haben oder Hilfe benötigen, können Sie:

- Ein Issue auf GitHub erstellen
- Eine Diskussion im Discussions-Bereich starten
- Uns per E-Mail kontaktieren: [<EMAIL>](mailto:<EMAIL>)

Vielen Dank für Ihre Beiträge zu DeSci-Scholar!# Beitragsrichtlinien für DeSci-Scholar

Vielen Dank für Ihr Interesse, zu DeSci-Scholar beizutragen! Als Open-Source- und Open-Science-Projekt leben wir von der Beteiligung der Community. Diese Richtlinien sollen Ihnen helfen, effektiv zum Projekt beizutragen.

## Open-Science-Prinzipien

DeSci-Scholar ist der Open-Science-Bewegung verpflichtet. Wir glauben an:

- **Transparenz**: Alle Aspekte des wissenschaftlichen Prozesses sollten offen und nachvollziehbar sein
- **Offenen Zugang**: Wissenschaftliche Erkenntnisse sollten frei zugänglich sein
- **Offene Daten**: Forschungsdaten sollten geteilt und wiederverwendbar sein
- **Offene Methoden**: Forschungsmethoden sollten transparent und reproduzierbar sein
- **Offene Infrastruktur**: Wissenschaftliche Infrastruktur sollte offen und gemeinschaftlich entwickelt werden

Bitte beachten Sie diese Prinzipien bei Ihren Beiträgen zum Projekt.

## Wie Sie beitragen können

Es gibt viele Möglichkeiten, zu DeSci-Scholar beizutragen:

1. **Code-Beiträge**: Implementierung neuer Funktionen oder Behebung von Fehlern
2. **Dokumentation**: Verbesserung oder Erweiterung der Dokumentation
3. **Tests**: Schreiben von Tests zur Verbesserung der Codequalität
4. **Fehlerberichte**: Melden von Fehlern oder Problemen
5. **Funktionsanfragen**: Vorschläge für neue Funktionen oder Verbesserungen
6. **Übersetzungen**: Übersetzung der Dokumentation oder der Benutzeroberfläche
7. **Community-Unterstützung**: Beantwortung von Fragen in Foren oder Diskussionen

## Entwicklungsprozess

### 1. Fork und Clone

1. Erstellen Sie einen Fork des Repositories auf GitHub
2. Klonen Sie Ihren Fork auf Ihren lokalen Computer:
   ```
   git clone https://github.com/IHR_BENUTZERNAME/desci-scholar.git
   cd desci-scholar
   ```
3. Fügen Sie das Upstream-Repository hinzu:
   ```
   git remote add upstream https://github.com/desci-labs/desci-scholar.git
   ```

### 2. Branches

1. Halten Sie Ihren `main`-Branch synchron mit dem Upstream:
   ```
   git checkout main
   git pull upstream main
   ```
2. Erstellen Sie einen neuen Branch für Ihre Änderungen:
   ```
   git checkout -b feature/ihre-feature-beschreibung
   ```

### 3. Entwicklung

1. Implementieren Sie Ihre Änderungen
2. Stellen Sie sicher, dass Ihr Code den Codierungsrichtlinien entspricht
3. Schreiben Sie Tests für Ihre Änderungen
4. Stellen Sie sicher, dass alle Tests bestehen:
   ```
   npm test
   ```

### 4. Commit und Push

1. Committen Sie Ihre Änderungen mit aussagekräftigen Commit-Nachrichten:
   ```
   git commit -m "Kurze Beschreibung der Änderungen"
   ```
2. Pushen Sie Ihre Änderungen zu Ihrem Fork:
   ```
   git push origin feature/ihre-feature-beschreibung
   ```

### 5. Pull Request

1. Erstellen Sie einen Pull Request von Ihrem Branch zu `desci-labs/desci-scholar:main`
2. Beschreiben Sie Ihre Änderungen ausführlich im Pull Request
3. Verlinken Sie relevante Issues
4. Warten Sie auf Feedback und reagieren Sie auf Kommentare

## Codierungsrichtlinien

- Folgen Sie dem bestehenden Codierungsstil
- Schreiben Sie klaren, lesbaren und gut dokumentierten Code
- Kommentieren Sie komplexe Logik
- Verwenden Sie aussagekräftige Variablen- und Funktionsnamen
- Halten Sie Funktionen kurz und fokussiert

## Commit-Nachrichten

Bitte verwenden Sie klare und aussagekräftige Commit-Nachrichten. Folgen Sie diesem Format:

```
Kurze Zusammenfassung (max. 50 Zeichen)

Ausführlichere Erklärung, wenn nötig. Umbruch nach etwa 72 Zeichen.
Erklären Sie das Problem, das dieser Commit löst. Konzentrieren Sie
sich darauf, warum die Änderung notwendig ist, nicht wie sie
implementiert wurde.

Wenn es relevante Issues gibt, erwähnen Sie diese am Ende:
Fixes #123
```

## Lizenz

Durch das Einreichen eines Beitrags erklären Sie sich damit einverstanden, dass Ihr Beitrag unter der [MIT-Lizenz](LICENSE) veröffentlicht wird, die für das Projekt gilt.

## Verhaltenskodex

Wir erwarten von allen Teilnehmern, dass sie unseren [Verhaltenskodex](CODE_OF_CONDUCT.md) einhalten. Bitte lesen Sie ihn, bevor Sie beitragen.

## Fragen?

Wenn Sie Fragen haben oder Hilfe benötigen, können Sie:

- Ein Issue auf GitHub erstellen
- Eine Diskussion im Discussions-Bereich starten
- Uns per E-Mail kontaktieren: [<EMAIL>](mailto:<EMAIL>)

Vielen Dank für Ihre Beiträge zu DeSci-Scholar!# Beitragsrichtlinien für DeSci-Scholar

Vielen Dank für Ihr Interesse, zu DeSci-Scholar beizutragen! Als Open-Source- und Open-Science-Projekt leben wir von der Beteiligung der Community. Diese Richtlinien sollen Ihnen helfen, effektiv zum Projekt beizutragen.

## Open-Science-Prinzipien

DeSci-Scholar ist der Open-Science-Bewegung verpflichtet. Wir glauben an:

- **Transparenz**: Alle Aspekte des wissenschaftlichen Prozesses sollten offen und nachvollziehbar sein
- **Offenen Zugang**: Wissenschaftliche Erkenntnisse sollten frei zugänglich sein
- **Offene Daten**: Forschungsdaten sollten geteilt und wiederverwendbar sein
- **Offene Methoden**: Forschungsmethoden sollten transparent und reproduzierbar sein
- **Offene Infrastruktur**: Wissenschaftliche Infrastruktur sollte offen und gemeinschaftlich entwickelt werden

Bitte beachten Sie diese Prinzipien bei Ihren Beiträgen zum Projekt.

## Wie Sie beitragen können

Es gibt viele Möglichkeiten, zu DeSci-Scholar beizutragen:

1. **Code-Beiträge**: Implementierung neuer Funktionen oder Behebung von Fehlern
2. **Dokumentation**: Verbesserung oder Erweiterung der Dokumentation
3. **Tests**: Schreiben von Tests zur Verbesserung der Codequalität
4. **Fehlerberichte**: Melden von Fehlern oder Problemen
5. **Funktionsanfragen**: Vorschläge für neue Funktionen oder Verbesserungen
6. **Übersetzungen**: Übersetzung der Dokumentation oder der Benutzeroberfläche
7. **Community-Unterstützung**: Beantwortung von Fragen in Foren oder Diskussionen

## Entwicklungsprozess

### 1. Fork und Clone

1. Erstellen Sie einen Fork des Repositories auf GitHub
2. Klonen Sie Ihren Fork auf Ihren lokalen Computer:
   ```
   git clone https://github.com/IHR_BENUTZERNAME/desci-scholar.git
   cd desci-scholar
   ```
3. Fügen Sie das Upstream-Repository hinzu:
   ```
   git remote add upstream https://github.com/desci-labs/desci-scholar.git
   ```

### 2. Branches

1. Halten Sie Ihren `main`-Branch synchron mit dem Upstream:
   ```
   git checkout main
   git pull upstream main
   ```
2. Erstellen Sie einen neuen Branch für Ihre Änderungen:
   ```
   git checkout -b feature/ihre-feature-beschreibung
   ```

### 3. Entwicklung

1. Implementieren Sie Ihre Änderungen
2. Stellen Sie sicher, dass Ihr Code den Codierungsrichtlinien entspricht
3. Schreiben Sie Tests für Ihre Änderungen
4. Stellen Sie sicher, dass alle Tests bestehen:
   ```
   npm test
   ```

### 4. Commit und Push

1. Committen Sie Ihre Änderungen mit aussagekräftigen Commit-Nachrichten:
   ```
   git commit -m "Kurze Beschreibung der Änderungen"
   ```
2. Pushen Sie Ihre Änderungen zu Ihrem Fork:
   ```
   git push origin feature/ihre-feature-beschreibung
   ```

### 5. Pull Request

1. Erstellen Sie einen Pull Request von Ihrem Branch zu `desci-labs/desci-scholar:main`
2. Beschreiben Sie Ihre Änderungen ausführlich im Pull Request
3. Verlinken Sie relevante Issues
4. Warten Sie auf Feedback und reagieren Sie auf Kommentare

## Codierungsrichtlinien

- Folgen Sie dem bestehenden Codierungsstil
- Schreiben Sie klaren, lesbaren und gut dokumentierten Code
- Kommentieren Sie komplexe Logik
- Verwenden Sie aussagekräftige Variablen- und Funktionsnamen
- Halten Sie Funktionen kurz und fokussiert

## Commit-Nachrichten

Bitte verwenden Sie klare und aussagekräftige Commit-Nachrichten. Folgen Sie diesem Format:

```
Kurze Zusammenfassung (max. 50 Zeichen)

Ausführlichere Erklärung, wenn nötig. Umbruch nach etwa 72 Zeichen.
Erklären Sie das Problem, das dieser Commit löst. Konzentrieren Sie
sich darauf, warum die Änderung notwendig ist, nicht wie sie
implementiert wurde.

Wenn es relevante Issues gibt, erwähnen Sie diese am Ende:
Fixes #123
```

## Lizenz

Durch das Einreichen eines Beitrags erklären Sie sich damit einverstanden, dass Ihr Beitrag unter der [MIT-Lizenz](LICENSE) veröffentlicht wird, die für das Projekt gilt.

## Verhaltenskodex

Wir erwarten von allen Teilnehmern, dass sie unseren [Verhaltenskodex](CODE_OF_CONDUCT.md) einhalten. Bitte lesen Sie ihn, bevor Sie beitragen.

## Fragen?

Wenn Sie Fragen haben oder Hilfe benötigen, können Sie:

- Ein Issue auf GitHub erstellen
- Eine Diskussion im Discussions-Bereich starten
- Uns per E-Mail kontaktieren: [<EMAIL>](mailto:<EMAIL>)

Vielen Dank für Ihre Beiträge zu DeSci-Scholar!# Beitragsrichtlinien für DeSci-Scholar

Vielen Dank für Ihr Interesse, zu DeSci-Scholar beizutragen! Als Open-Source- und Open-Science-Projekt leben wir von der Beteiligung der Community. Diese Richtlinien sollen Ihnen helfen, effektiv zum Projekt beizutragen.

## Open-Science-Prinzipien

DeSci-Scholar ist der Open-Science-Bewegung verpflichtet. Wir glauben an:

- **Transparenz**: Alle Aspekte des wissenschaftlichen Prozesses sollten offen und nachvollziehbar sein
- **Offenen Zugang**: Wissenschaftliche Erkenntnisse sollten frei zugänglich sein
- **Offene Daten**: Forschungsdaten sollten geteilt und wiederverwendbar sein
- **Offene Methoden**: Forschungsmethoden sollten transparent und reproduzierbar sein
- **Offene Infrastruktur**: Wissenschaftliche Infrastruktur sollte offen und gemeinschaftlich entwickelt werden

Bitte beachten Sie diese Prinzipien bei Ihren Beiträgen zum Projekt.

## Wie Sie beitragen können

Es gibt viele Möglichkeiten, zu DeSci-Scholar beizutragen:

1. **Code-Beiträge**: Implementierung neuer Funktionen oder Behebung von Fehlern
2. **Dokumentation**: Verbesserung oder Erweiterung der Dokumentation
3. **Tests**: Schreiben von Tests zur Verbesserung der Codequalität
4. **Fehlerberichte**: Melden von Fehlern oder Problemen
5. **Funktionsanfragen**: Vorschläge für neue Funktionen oder Verbesserungen
6. **Übersetzungen**: Übersetzung der Dokumentation oder der Benutzeroberfläche
7. **Community-Unterstützung**: Beantwortung von Fragen in Foren oder Diskussionen

## Entwicklungsprozess

### 1. Fork und Clone

1. Erstellen Sie einen Fork des Repositories auf GitHub
2. Klonen Sie Ihren Fork auf Ihren lokalen Computer:
   ```
   git clone https://github.com/IHR_BENUTZERNAME/desci-scholar.git
   cd desci-scholar
   ```
3. Fügen Sie das Upstream-Repository hinzu:
   ```
   git remote add upstream https://github.com/desci-labs/desci-scholar.git
   ```

### 2. Branches

1. Halten Sie Ihren `main`-Branch synchron mit dem Upstream:
   ```
   git checkout main
   git pull upstream main
   ```
2. Erstellen Sie einen neuen Branch für Ihre Änderungen:
   ```
   git checkout -b feature/ihre-feature-beschreibung
   ```

### 3. Entwicklung

1. Implementieren Sie Ihre Änderungen
2. Stellen Sie sicher, dass Ihr Code den Codierungsrichtlinien entspricht
3. Schreiben Sie Tests für Ihre Änderungen
4. Stellen Sie sicher, dass alle Tests bestehen:
   ```
   npm test
   ```

### 4. Commit und Push

1. Committen Sie Ihre Änderungen mit aussagekräftigen Commit-Nachrichten:
   ```
   git commit -m "Kurze Beschreibung der Änderungen"
   ```
2. Pushen Sie Ihre Änderungen zu Ihrem Fork:
   ```
   git push origin feature/ihre-feature-beschreibung
   ```

### 5. Pull Request

1. Erstellen Sie einen Pull Request von Ihrem Branch zu `desci-labs/desci-scholar:main`
2. Beschreiben Sie Ihre Änderungen ausführlich im Pull Request
3. Verlinken Sie relevante Issues
4. Warten Sie auf Feedback und reagieren Sie auf Kommentare

## Codierungsrichtlinien

- Folgen Sie dem bestehenden Codierungsstil
- Schreiben Sie klaren, lesbaren und gut dokumentierten Code
- Kommentieren Sie komplexe Logik
- Verwenden Sie aussagekräftige Variablen- und Funktionsnamen
- Halten Sie Funktionen kurz und fokussiert

## Commit-Nachrichten

Bitte verwenden Sie klare und aussagekräftige Commit-Nachrichten. Folgen Sie diesem Format:

```
Kurze Zusammenfassung (max. 50 Zeichen)

Ausführlichere Erklärung, wenn nötig. Umbruch nach etwa 72 Zeichen.
Erklären Sie das Problem, das dieser Commit löst. Konzentrieren Sie
sich darauf, warum die Änderung notwendig ist, nicht wie sie
implementiert wurde.

Wenn es relevante Issues gibt, erwähnen Sie diese am Ende:
Fixes #123
```

## Lizenz

Durch das Einreichen eines Beitrags erklären Sie sich damit einverstanden, dass Ihr Beitrag unter der [MIT-Lizenz](LICENSE) veröffentlicht wird, die für das Projekt gilt.

## Verhaltenskodex

Wir erwarten von allen Teilnehmern, dass sie unseren [Verhaltenskodex](CODE_OF_CONDUCT.md) einhalten. Bitte lesen Sie ihn, bevor Sie beitragen.

## Fragen?

Wenn Sie Fragen haben oder Hilfe benötigen, können Sie:

- Ein Issue auf GitHub erstellen
- Eine Diskussion im Discussions-Bereich starten
- Uns per E-Mail kontaktieren: [<EMAIL>](mailto:<EMAIL>)

Vielen Dank für Ihre Beiträge zu DeSci-Scholar!# Beitragsrichtlinien für DeSci-Scholar

Vielen Dank für Ihr Interesse, zu DeSci-Scholar beizutragen! Als Open-Source- und Open-Science-Projekt leben wir von der Beteiligung der Community. Diese Richtlinien sollen Ihnen helfen, effektiv zum Projekt beizutragen.

## Open-Science-Prinzipien

DeSci-Scholar ist der Open-Science-Bewegung verpflichtet. Wir glauben an:

- **Transparenz**: Alle Aspekte des wissenschaftlichen Prozesses sollten offen und nachvollziehbar sein
- **Offenen Zugang**: Wissenschaftliche Erkenntnisse sollten frei zugänglich sein
- **Offene Daten**: Forschungsdaten sollten geteilt und wiederverwendbar sein
- **Offene Methoden**: Forschungsmethoden sollten transparent und reproduzierbar sein
- **Offene Infrastruktur**: Wissenschaftliche Infrastruktur sollte offen und gemeinschaftlich entwickelt werden

Bitte beachten Sie diese Prinzipien bei Ihren Beiträgen zum Projekt.

## Wie Sie beitragen können

Es gibt viele Möglichkeiten, zu DeSci-Scholar beizutragen:

1. **Code-Beiträge**: Implementierung neuer Funktionen oder Behebung von Fehlern
2. **Dokumentation**: Verbesserung oder Erweiterung der Dokumentation
3. **Tests**: Schreiben von Tests zur Verbesserung der Codequalität
4. **Fehlerberichte**: Melden von Fehlern oder Problemen
5. **Funktionsanfragen**: Vorschläge für neue Funktionen oder Verbesserungen
6. **Übersetzungen**: Übersetzung der Dokumentation oder der Benutzeroberfläche
7. **Community-Unterstützung**: Beantwortung von Fragen in Foren oder Diskussionen

## Entwicklungsprozess

### 1. Fork und Clone

1. Erstellen Sie einen Fork des Repositories auf GitHub
2. Klonen Sie Ihren Fork auf Ihren lokalen Computer:
   ```
   git clone https://github.com/IHR_BENUTZERNAME/desci-scholar.git
   cd desci-scholar
   ```
3. Fügen Sie das Upstream-Repository hinzu:
   ```
   git remote add upstream https://github.com/desci-labs/desci-scholar.git
   ```

### 2. Branches

1. Halten Sie Ihren `main`-Branch synchron mit dem Upstream:
   ```
   git checkout main
   git pull upstream main
   ```
2. Erstellen Sie einen neuen Branch für Ihre Änderungen:
   ```
   git checkout -b feature/ihre-feature-beschreibung
   ```

### 3. Entwicklung

1. Implementieren Sie Ihre Änderungen
2. Stellen Sie sicher, dass Ihr Code den Codierungsrichtlinien entspricht
3. Schreiben Sie Tests für Ihre Änderungen
4. Stellen Sie sicher, dass alle Tests bestehen:
   ```
   npm test
   ```

### 4. Commit und Push

1. Committen Sie Ihre Änderungen mit aussagekräftigen Commit-Nachrichten:
   ```
   git commit -m "Kurze Beschreibung der Änderungen"
   ```
2. Pushen Sie Ihre Änderungen zu Ihrem Fork:
   ```
   git push origin feature/ihre-feature-beschreibung
   ```

### 5. Pull Request

1. Erstellen Sie einen Pull Request von Ihrem Branch zu `desci-labs/desci-scholar:main`
2. Beschreiben Sie Ihre Änderungen ausführlich im Pull Request
3. Verlinken Sie relevante Issues
4. Warten Sie auf Feedback und reagieren Sie auf Kommentare

## Codierungsrichtlinien

- Folgen Sie dem bestehenden Codierungsstil
- Schreiben Sie klaren, lesbaren und gut dokumentierten Code
- Kommentieren Sie komplexe Logik
- Verwenden Sie aussagekräftige Variablen- und Funktionsnamen
- Halten Sie Funktionen kurz und fokussiert

## Commit-Nachrichten

Bitte verwenden Sie klare und aussagekräftige Commit-Nachrichten. Folgen Sie diesem Format:

```
Kurze Zusammenfassung (max. 50 Zeichen)

Ausführlichere Erklärung, wenn nötig. Umbruch nach etwa 72 Zeichen.
Erklären Sie das Problem, das dieser Commit löst. Konzentrieren Sie
sich darauf, warum die Änderung notwendig ist, nicht wie sie
implementiert wurde.

Wenn es relevante Issues gibt, erwähnen Sie diese am Ende:
Fixes #123
```

## Lizenz

Durch das Einreichen eines Beitrags erklären Sie sich damit einverstanden, dass Ihr Beitrag unter der [MIT-Lizenz](LICENSE) veröffentlicht wird, die für das Projekt gilt.

## Verhaltenskodex

Wir erwarten von allen Teilnehmern, dass sie unseren [Verhaltenskodex](CODE_OF_CONDUCT.md) einhalten. Bitte lesen Sie ihn, bevor Sie beitragen.

## Fragen?

Wenn Sie Fragen haben oder Hilfe benötigen, können Sie:

- Ein Issue auf GitHub erstellen
- Eine Diskussion im Discussions-Bereich starten
- Uns per E-Mail kontaktieren: [<EMAIL>](mailto:<EMAIL>)

Vielen Dank für Ihre Beiträge zu DeSci-Scholar!# Beitragsrichtlinien für DeSci-Scholar

Vielen Dank für Ihr Interesse, zu DeSci-Scholar beizutragen! Als Open-Source- und Open-Science-Projekt leben wir von der Beteiligung der Community. Diese Richtlinien sollen Ihnen helfen, effektiv zum Projekt beizutragen.

## Open-Science-Prinzipien

DeSci-Scholar ist der Open-Science-Bewegung verpflichtet. Wir glauben an:

- **Transparenz**: Alle Aspekte des wissenschaftlichen Prozesses sollten offen und nachvollziehbar sein
- **Offenen Zugang**: Wissenschaftliche Erkenntnisse sollten frei zugänglich sein
- **Offene Daten**: Forschungsdaten sollten geteilt und wiederverwendbar sein
- **Offene Methoden**: Forschungsmethoden sollten transparent und reproduzierbar sein
- **Offene Infrastruktur**: Wissenschaftliche Infrastruktur sollte offen und gemeinschaftlich entwickelt werden

Bitte beachten Sie diese Prinzipien bei Ihren Beiträgen zum Projekt.

## Wie Sie beitragen können

Es gibt viele Möglichkeiten, zu DeSci-Scholar beizutragen:

1. **Code-Beiträge**: Implementierung neuer Funktionen oder Behebung von Fehlern
2. **Dokumentation**: Verbesserung oder Erweiterung der Dokumentation
3. **Tests**: Schreiben von Tests zur Verbesserung der Codequalität
4. **Fehlerberichte**: Melden von Fehlern oder Problemen
5. **Funktionsanfragen**: Vorschläge für neue Funktionen oder Verbesserungen
6. **Übersetzungen**: Übersetzung der Dokumentation oder der Benutzeroberfläche
7. **Community-Unterstützung**: Beantwortung von Fragen in Foren oder Diskussionen

## Entwicklungsprozess

### 1. Fork und Clone

1. Erstellen Sie einen Fork des Repositories auf GitHub
2. Klonen Sie Ihren Fork auf Ihren lokalen Computer:
   ```
   git clone https://github.com/IHR_BENUTZERNAME/desci-scholar.git
   cd desci-scholar
   ```
3. Fügen Sie das Upstream-Repository hinzu:
   ```
   git remote add upstream https://github.com/desci-labs/desci-scholar.git
   ```

### 2. Branches

1. Halten Sie Ihren `main`-Branch synchron mit dem Upstream:
   ```
   git checkout main
   git pull upstream main
   ```
2. Erstellen Sie einen neuen Branch für Ihre Änderungen:
   ```
   git checkout -b feature/ihre-feature-beschreibung
   ```

### 3. Entwicklung

1. Implementieren Sie Ihre Änderungen
2. Stellen Sie sicher, dass Ihr Code den Codierungsrichtlinien entspricht
3. Schreiben Sie Tests für Ihre Änderungen
4. Stellen Sie sicher, dass alle Tests bestehen:
   ```
   npm test
   ```

### 4. Commit und Push

1. Committen Sie Ihre Änderungen mit aussagekräftigen Commit-Nachrichten:
   ```
   git commit -m "Kurze Beschreibung der Änderungen"
   ```
2. Pushen Sie Ihre Änderungen zu Ihrem Fork:
   ```
   git push origin feature/ihre-feature-beschreibung
   ```

### 5. Pull Request

1. Erstellen Sie einen Pull Request von Ihrem Branch zu `desci-labs/desci-scholar:main`
2. Beschreiben Sie Ihre Änderungen ausführlich im Pull Request
3. Verlinken Sie relevante Issues
4. Warten Sie auf Feedback und reagieren Sie auf Kommentare

## Codierungsrichtlinien

- Folgen Sie dem bestehenden Codierungsstil
- Schreiben Sie klaren, lesbaren und gut dokumentierten Code
- Kommentieren Sie komplexe Logik
- Verwenden Sie aussagekräftige Variablen- und Funktionsnamen
- Halten Sie Funktionen kurz und fokussiert

## Commit-Nachrichten

Bitte verwenden Sie klare und aussagekräftige Commit-Nachrichten. Folgen Sie diesem Format:

```
Kurze Zusammenfassung (max. 50 Zeichen)

Ausführlichere Erklärung, wenn nötig. Umbruch nach etwa 72 Zeichen.
Erklären Sie das Problem, das dieser Commit löst. Konzentrieren Sie
sich darauf, warum die Änderung notwendig ist, nicht wie sie
implementiert wurde.

Wenn es relevante Issues gibt, erwähnen Sie diese am Ende:
Fixes #123
```

## Lizenz

Durch das Einreichen eines Beitrags erklären Sie sich damit einverstanden, dass Ihr Beitrag unter der [MIT-Lizenz](LICENSE) veröffentlicht wird, die für das Projekt gilt.

## Verhaltenskodex

Wir erwarten von allen Teilnehmern, dass sie unseren [Verhaltenskodex](CODE_OF_CONDUCT.md) einhalten. Bitte lesen Sie ihn, bevor Sie beitragen.

## Fragen?

Wenn Sie Fragen haben oder Hilfe benötigen, können Sie:

- Ein Issue auf GitHub erstellen
- Eine Diskussion im Discussions-Bereich starten
- Uns per E-Mail kontaktieren: [<EMAIL>](mailto:<EMAIL>)

Vielen Dank für Ihre Beiträge zu DeSci-Scholar!# Beitragsrichtlinien für DeSci-Scholar

Vielen Dank für Ihr Interesse, zu DeSci-Scholar beizutragen! Als Open-Source- und Open-Science-Projekt leben wir von der Beteiligung der Community. Diese Richtlinien sollen Ihnen helfen, effektiv zum Projekt beizutragen.

## Open-Science-Prinzipien

DeSci-Scholar ist der Open-Science-Bewegung verpflichtet. Wir glauben an:

- **Transparenz**: Alle Aspekte des wissenschaftlichen Prozesses sollten offen und nachvollziehbar sein
- **Offenen Zugang**: Wissenschaftliche Erkenntnisse sollten frei zugänglich sein
- **Offene Daten**: Forschungsdaten sollten geteilt und wiederverwendbar sein
- **Offene Methoden**: Forschungsmethoden sollten transparent und reproduzierbar sein
- **Offene Infrastruktur**: Wissenschaftliche Infrastruktur sollte offen und gemeinschaftlich entwickelt werden

Bitte beachten Sie diese Prinzipien bei Ihren Beiträgen zum Projekt.

## Wie Sie beitragen können

Es gibt viele Möglichkeiten, zu DeSci-Scholar beizutragen:

1. **Code-Beiträge**: Implementierung neuer Funktionen oder Behebung von Fehlern
2. **Dokumentation**: Verbesserung oder Erweiterung der Dokumentation
3. **Tests**: Schreiben von Tests zur Verbesserung der Codequalität
4. **Fehlerberichte**: Melden von Fehlern oder Problemen
5. **Funktionsanfragen**: Vorschläge für neue Funktionen oder Verbesserungen
6. **Übersetzungen**: Übersetzung der Dokumentation oder der Benutzeroberfläche
7. **Community-Unterstützung**: Beantwortung von Fragen in Foren oder Diskussionen

## Entwicklungsprozess

### 1. Fork und Clone

1. Erstellen Sie einen Fork des Repositories auf GitHub
2. Klonen Sie Ihren Fork auf Ihren lokalen Computer:
   ```
   git clone https://github.com/IHR_BENUTZERNAME/desci-scholar.git
   cd desci-scholar
   ```
3. Fügen Sie das Upstream-Repository hinzu:
   ```
   git remote add upstream https://github.com/desci-labs/desci-scholar.git
   ```

### 2. Branches

1. Halten Sie Ihren `main`-Branch synchron mit dem Upstream:
   ```
   git checkout main
   git pull upstream main
   ```
2. Erstellen Sie einen neuen Branch für Ihre Änderungen:
   ```
   git checkout -b feature/ihre-feature-beschreibung
   ```

### 3. Entwicklung

1. Implementieren Sie Ihre Änderungen
2. Stellen Sie sicher, dass Ihr Code den Codierungsrichtlinien entspricht
3. Schreiben Sie Tests für Ihre Änderungen
4. Stellen Sie sicher, dass alle Tests bestehen:
   ```
   npm test
   ```

### 4. Commit und Push

1. Committen Sie Ihre Änderungen mit aussagekräftigen Commit-Nachrichten:
   ```
   git commit -m "Kurze Beschreibung der Änderungen"
   ```
2. Pushen Sie Ihre Änderungen zu Ihrem Fork:
   ```
   git push origin feature/ihre-feature-beschreibung
   ```

### 5. Pull Request

1. Erstellen Sie einen Pull Request von Ihrem Branch zu `desci-labs/desci-scholar:main`
2. Beschreiben Sie Ihre Änderungen ausführlich im Pull Request
3. Verlinken Sie relevante Issues
4. Warten Sie auf Feedback und reagieren Sie auf Kommentare

## Codierungsrichtlinien

- Folgen Sie dem bestehenden Codierungsstil
- Schreiben Sie klaren, lesbaren und gut dokumentierten Code
- Kommentieren Sie komplexe Logik
- Verwenden Sie aussagekräftige Variablen- und Funktionsnamen
- Halten Sie Funktionen kurz und fokussiert

## Commit-Nachrichten

Bitte verwenden Sie klare und aussagekräftige Commit-Nachrichten. Folgen Sie diesem Format:

```
Kurze Zusammenfassung (max. 50 Zeichen)

Ausführlichere Erklärung, wenn nötig. Umbruch nach etwa 72 Zeichen.
Erklären Sie das Problem, das dieser Commit löst. Konzentrieren Sie
sich darauf, warum die Änderung notwendig ist, nicht wie sie
implementiert wurde.

Wenn es relevante Issues gibt, erwähnen Sie diese am Ende:
Fixes #123
```

## Lizenz

Durch das Einreichen eines Beitrags erklären Sie sich damit einverstanden, dass Ihr Beitrag unter der [MIT-Lizenz](LICENSE) veröffentlicht wird, die für das Projekt gilt.

## Verhaltenskodex

Wir erwarten von allen Teilnehmern, dass sie unseren [Verhaltenskodex](CODE_OF_CONDUCT.md) einhalten. Bitte lesen Sie ihn, bevor Sie beitragen.

## Fragen?

Wenn Sie Fragen haben oder Hilfe benötigen, können Sie:

- Ein Issue auf GitHub erstellen
- Eine Diskussion im Discussions-Bereich starten
- Uns per E-Mail kontaktieren: [<EMAIL>](mailto:<EMAIL>)

Vielen Dank für Ihre Beiträge zu DeSci-Scholar!# Beitragsrichtlinien für DeSci-Scholar

Vielen Dank für Ihr Interesse, zu DeSci-Scholar beizutragen! Als Open-Source- und Open-Science-Projekt leben wir von der Beteiligung der Community. Diese Richtlinien sollen Ihnen helfen, effektiv zum Projekt beizutragen.

## Open-Science-Prinzipien

DeSci-Scholar ist der Open-Science-Bewegung verpflichtet. Wir glauben an:

- **Transparenz**: Alle Aspekte des wissenschaftlichen Prozesses sollten offen und nachvollziehbar sein
- **Offenen Zugang**: Wissenschaftliche Erkenntnisse sollten frei zugänglich sein
- **Offene Daten**: Forschungsdaten sollten geteilt und wiederverwendbar sein
- **Offene Methoden**: Forschungsmethoden sollten transparent und reproduzierbar sein
- **Offene Infrastruktur**: Wissenschaftliche Infrastruktur sollte offen und gemeinschaftlich entwickelt werden

Bitte beachten Sie diese Prinzipien bei Ihren Beiträgen zum Projekt.

## Wie Sie beitragen können

Es gibt viele Möglichkeiten, zu DeSci-Scholar beizutragen:

1. **Code-Beiträge**: Implementierung neuer Funktionen oder Behebung von Fehlern
2. **Dokumentation**: Verbesserung oder Erweiterung der Dokumentation
3. **Tests**: Schreiben von Tests zur Verbesserung der Codequalität
4. **Fehlerberichte**: Melden von Fehlern oder Problemen
5. **Funktionsanfragen**: Vorschläge für neue Funktionen oder Verbesserungen
6. **Übersetzungen**: Übersetzung der Dokumentation oder der Benutzeroberfläche
7. **Community-Unterstützung**: Beantwortung von Fragen in Foren oder Diskussionen

## Entwicklungsprozess

### 1. Fork und Clone

1. Erstellen Sie einen Fork des Repositories auf GitHub
2. Klonen Sie Ihren Fork auf Ihren lokalen Computer:
   ```
   git clone https://github.com/IHR_BENUTZERNAME/desci-scholar.git
   cd desci-scholar
   ```
3. Fügen Sie das Upstream-Repository hinzu:
   ```
   git remote add upstream https://github.com/desci-labs/desci-scholar.git
   ```

### 2. Branches

1. Halten Sie Ihren `main`-Branch synchron mit dem Upstream:
   ```
   git checkout main
   git pull upstream main
   ```
2. Erstellen Sie einen neuen Branch für Ihre Änderungen:
   ```
   git checkout -b feature/ihre-feature-beschreibung
   ```

### 3. Entwicklung

1. Implementieren Sie Ihre Änderungen
2. Stellen Sie sicher, dass Ihr Code den Codierungsrichtlinien entspricht
3. Schreiben Sie Tests für Ihre Änderungen
4. Stellen Sie sicher, dass alle Tests bestehen:
   ```
   npm test
   ```

### 4. Commit und Push

1. Committen Sie Ihre Änderungen mit aussagekräftigen Commit-Nachrichten:
   ```
   git commit -m "Kurze Beschreibung der Änderungen"
   ```
2. Pushen Sie Ihre Änderungen zu Ihrem Fork:
   ```
   git push origin feature/ihre-feature-beschreibung
   ```

### 5. Pull Request

1. Erstellen Sie einen Pull Request von Ihrem Branch zu `desci-labs/desci-scholar:main`
2. Beschreiben Sie Ihre Änderungen ausführlich im Pull Request
3. Verlinken Sie relevante Issues
4. Warten Sie auf Feedback und reagieren Sie auf Kommentare

## Codierungsrichtlinien

- Folgen Sie dem bestehenden Codierungsstil
- Schreiben Sie klaren, lesbaren und gut dokumentierten Code
- Kommentieren Sie komplexe Logik
- Verwenden Sie aussagekräftige Variablen- und Funktionsnamen
- Halten Sie Funktionen kurz und fokussiert

## Commit-Nachrichten

Bitte verwenden Sie klare und aussagekräftige Commit-Nachrichten. Folgen Sie diesem Format:

```
Kurze Zusammenfassung (max. 50 Zeichen)

Ausführlichere Erklärung, wenn nötig. Umbruch nach etwa 72 Zeichen.
Erklären Sie das Problem, das dieser Commit löst. Konzentrieren Sie
sich darauf, warum die Änderung notwendig ist, nicht wie sie
implementiert wurde.

Wenn es relevante Issues gibt, erwähnen Sie diese am Ende:
Fixes #123
```

## Lizenz

Durch das Einreichen eines Beitrags erklären Sie sich damit einverstanden, dass Ihr Beitrag unter der [MIT-Lizenz](LICENSE) veröffentlicht wird, die für das Projekt gilt.

## Verhaltenskodex

Wir erwarten von allen Teilnehmern, dass sie unseren [Verhaltenskodex](CODE_OF_CONDUCT.md) einhalten. Bitte lesen Sie ihn, bevor Sie beitragen.

## Fragen?

Wenn Sie Fragen haben oder Hilfe benötigen, können Sie:

- Ein Issue auf GitHub erstellen
- Eine Diskussion im Discussions-Bereich starten
- Uns per E-Mail kontaktieren: [<EMAIL>](mailto:<EMAIL>)

Vielen Dank für Ihre Beiträge zu DeSci-Scholar!# Beitragsrichtlinien für DeSci-Scholar

Vielen Dank für Ihr Interesse, zu DeSci-Scholar beizutragen! Als Open-Source- und Open-Science-Projekt leben wir von der Beteiligung der Community. Diese Richtlinien sollen Ihnen helfen, effektiv zum Projekt beizutragen.

## Open-Science-Prinzipien

DeSci-Scholar ist der Open-Science-Bewegung verpflichtet. Wir glauben an:

- **Transparenz**: Alle Aspekte des wissenschaftlichen Prozesses sollten offen und nachvollziehbar sein
- **Offenen Zugang**: Wissenschaftliche Erkenntnisse sollten frei zugänglich sein
- **Offene Daten**: Forschungsdaten sollten geteilt und wiederverwendbar sein
- **Offene Methoden**: Forschungsmethoden sollten transparent und reproduzierbar sein
- **Offene Infrastruktur**: Wissenschaftliche Infrastruktur sollte offen und gemeinschaftlich entwickelt werden

Bitte beachten Sie diese Prinzipien bei Ihren Beiträgen zum Projekt.

## Wie Sie beitragen können

Es gibt viele Möglichkeiten, zu DeSci-Scholar beizutragen:

1. **Code-Beiträge**: Implementierung neuer Funktionen oder Behebung von Fehlern
2. **Dokumentation**: Verbesserung oder Erweiterung der Dokumentation
3. **Tests**: Schreiben von Tests zur Verbesserung der Codequalität
4. **Fehlerberichte**: Melden von Fehlern oder Problemen
5. **Funktionsanfragen**: Vorschläge für neue Funktionen oder Verbesserungen
6. **Übersetzungen**: Übersetzung der Dokumentation oder der Benutzeroberfläche
7. **Community-Unterstützung**: Beantwortung von Fragen in Foren oder Diskussionen

## Entwicklungsprozess

### 1. Fork und Clone

1. Erstellen Sie einen Fork des Repositories auf GitHub
2. Klonen Sie Ihren Fork auf Ihren lokalen Computer:
   ```
   git clone https://github.com/IHR_BENUTZERNAME/desci-scholar.git
   cd desci-scholar
   ```
3. Fügen Sie das Upstream-Repository hinzu:
   ```
   git remote add upstream https://github.com/desci-labs/desci-scholar.git
   ```

### 2. Branches

1. Halten Sie Ihren `main`-Branch synchron mit dem Upstream:
   ```
   git checkout main
   git pull upstream main
   ```
2. Erstellen Sie einen neuen Branch für Ihre Änderungen:
   ```
   git checkout -b feature/ihre-feature-beschreibung
   ```

### 3. Entwicklung

1. Implementieren Sie Ihre Änderungen
2. Stellen Sie sicher, dass Ihr Code den Codierungsrichtlinien entspricht
3. Schreiben Sie Tests für Ihre Änderungen
4. Stellen Sie sicher, dass alle Tests bestehen:
   ```
   npm test
   ```

### 4. Commit und Push

1. Committen Sie Ihre Änderungen mit aussagekräftigen Commit-Nachrichten:
   ```
   git commit -m "Kurze Beschreibung der Änderungen"
   ```
2. Pushen Sie Ihre Änderungen zu Ihrem Fork:
   ```
   git push origin feature/ihre-feature-beschreibung
   ```

### 5. Pull Request

1. Erstellen Sie einen Pull Request von Ihrem Branch zu `desci-labs/desci-scholar:main`
2. Beschreiben Sie Ihre Änderungen ausführlich im Pull Request
3. Verlinken Sie relevante Issues
4. Warten Sie auf Feedback und reagieren Sie auf Kommentare

## Codierungsrichtlinien

- Folgen Sie dem bestehenden Codierungsstil
- Schreiben Sie klaren, lesbaren und gut dokumentierten Code
- Kommentieren Sie komplexe Logik
- Verwenden Sie aussagekräftige Variablen- und Funktionsnamen
- Halten Sie Funktionen kurz und fokussiert

## Commit-Nachrichten

Bitte verwenden Sie klare und aussagekräftige Commit-Nachrichten. Folgen Sie diesem Format:

```
Kurze Zusammenfassung (max. 50 Zeichen)

Ausführlichere Erklärung, wenn nötig. Umbruch nach etwa 72 Zeichen.
Erklären Sie das Problem, das dieser Commit löst. Konzentrieren Sie
sich darauf, warum die Änderung notwendig ist, nicht wie sie
implementiert wurde.

Wenn es relevante Issues gibt, erwähnen Sie diese am Ende:
Fixes #123
```

## Lizenz

Durch das Einreichen eines Beitrags erklären Sie sich damit einverstanden, dass Ihr Beitrag unter der [MIT-Lizenz](LICENSE) veröffentlicht wird, die für das Projekt gilt.

## Verhaltenskodex

Wir erwarten von allen Teilnehmern, dass sie unseren [Verhaltenskodex](CODE_OF_CONDUCT.md) einhalten. Bitte lesen Sie ihn, bevor Sie beitragen.

## Fragen?

Wenn Sie Fragen haben oder Hilfe benötigen, können Sie:

- Ein Issue auf GitHub erstellen
- Eine Diskussion im Discussions-Bereich starten
- Uns per E-Mail kontaktieren: [<EMAIL>](mailto:<EMAIL>)

Vielen Dank für Ihre Beiträge zu DeSci-Scholar!# Beitragsrichtlinien für DeSci-Scholar

Vielen Dank für Ihr Interesse, zu DeSci-Scholar beizutragen! Als Open-Source- und Open-Science-Projekt leben wir von der Beteiligung der Community. Diese Richtlinien sollen Ihnen helfen, effektiv zum Projekt beizutragen.

## Open-Science-Prinzipien

DeSci-Scholar ist der Open-Science-Bewegung verpflichtet. Wir glauben an:

- **Transparenz**: Alle Aspekte des wissenschaftlichen Prozesses sollten offen und nachvollziehbar sein
- **Offenen Zugang**: Wissenschaftliche Erkenntnisse sollten frei zugänglich sein
- **Offene Daten**: Forschungsdaten sollten geteilt und wiederverwendbar sein
- **Offene Methoden**: Forschungsmethoden sollten transparent und reproduzierbar sein
- **Offene Infrastruktur**: Wissenschaftliche Infrastruktur sollte offen und gemeinschaftlich entwickelt werden

Bitte beachten Sie diese Prinzipien bei Ihren Beiträgen zum Projekt.

## Wie Sie beitragen können

Es gibt viele Möglichkeiten, zu DeSci-Scholar beizutragen:

1. **Code-Beiträge**: Implementierung neuer Funktionen oder Behebung von Fehlern
2. **Dokumentation**: Verbesserung oder Erweiterung der Dokumentation
3. **Tests**: Schreiben von Tests zur Verbesserung der Codequalität
4. **Fehlerberichte**: Melden von Fehlern oder Problemen
5. **Funktionsanfragen**: Vorschläge für neue Funktionen oder Verbesserungen
6. **Übersetzungen**: Übersetzung der Dokumentation oder der Benutzeroberfläche
7. **Community-Unterstützung**: Beantwortung von Fragen in Foren oder Diskussionen

## Entwicklungsprozess

### 1. Fork und Clone

1. Erstellen Sie einen Fork des Repositories auf GitHub
2. Klonen Sie Ihren Fork auf Ihren lokalen Computer:
   ```
   git clone https://github.com/IHR_BENUTZERNAME/desci-scholar.git
   cd desci-scholar
   ```
3. Fügen Sie das Upstream-Repository hinzu:
   ```
   git remote add upstream https://github.com/desci-labs/desci-scholar.git
   ```

### 2. Branches

1. Halten Sie Ihren `main`-Branch synchron mit dem Upstream:
   ```
   git checkout main
   git pull upstream main
   ```
2. Erstellen Sie einen neuen Branch für Ihre Änderungen:
   ```
   git checkout -b feature/ihre-feature-beschreibung
   ```

### 3. Entwicklung

1. Implementieren Sie Ihre Änderungen
2. Stellen Sie sicher, dass Ihr Code den Codierungsrichtlinien entspricht
3. Schreiben Sie Tests für Ihre Änderungen
4. Stellen Sie sicher, dass alle Tests bestehen:
   ```
   npm test
   ```

### 4. Commit und Push

1. Committen Sie Ihre Änderungen mit aussagekräftigen Commit-Nachrichten:
   ```
   git commit -m "Kurze Beschreibung der Änderungen"
   ```
2. Pushen Sie Ihre Änderungen zu Ihrem Fork:
   ```
   git push origin feature/ihre-feature-beschreibung
   ```

### 5. Pull Request

1. Erstellen Sie einen Pull Request von Ihrem Branch zu `desci-labs/desci-scholar:main`
2. Beschreiben Sie Ihre Änderungen ausführlich im Pull Request
3. Verlinken Sie relevante Issues
4. Warten Sie auf Feedback und reagieren Sie auf Kommentare

## Codierungsrichtlinien

- Folgen Sie dem bestehenden Codierungsstil
- Schreiben Sie klaren, lesbaren und gut dokumentierten Code
- Kommentieren Sie komplexe Logik
- Verwenden Sie aussagekräftige Variablen- und Funktionsnamen
- Halten Sie Funktionen kurz und fokussiert

## Commit-Nachrichten

Bitte verwenden Sie klare und aussagekräftige Commit-Nachrichten. Folgen Sie diesem Format:

```
Kurze Zusammenfassung (max. 50 Zeichen)

Ausführlichere Erklärung, wenn nötig. Umbruch nach etwa 72 Zeichen.
Erklären Sie das Problem, das dieser Commit löst. Konzentrieren Sie
sich darauf, warum die Änderung notwendig ist, nicht wie sie
implementiert wurde.

Wenn es relevante Issues gibt, erwähnen Sie diese am Ende:
Fixes #123
```

## Lizenz

Durch das Einreichen eines Beitrags erklären Sie sich damit einverstanden, dass Ihr Beitrag unter der [MIT-Lizenz](LICENSE) veröffentlicht wird, die für das Projekt gilt.

## Verhaltenskodex

Wir erwarten von allen Teilnehmern, dass sie unseren [Verhaltenskodex](CODE_OF_CONDUCT.md) einhalten. Bitte lesen Sie ihn, bevor Sie beitragen.

## Fragen?

Wenn Sie Fragen haben oder Hilfe benötigen, können Sie:

- Ein Issue auf GitHub erstellen
- Eine Diskussion im Discussions-Bereich starten
- Uns per E-Mail kontaktieren: [<EMAIL>](mailto:<EMAIL>)

Vielen Dank für Ihre Beiträge zu DeSci-Scholar!# Beitragsrichtlinien für DeSci-Scholar

Vielen Dank für Ihr Interesse, zu DeSci-Scholar beizutragen! Als Open-Source- und Open-Science-Projekt leben wir von der Beteiligung der Community. Diese Richtlinien sollen Ihnen helfen, effektiv zum Projekt beizutragen.

## Open-Science-Prinzipien

DeSci-Scholar ist der Open-Science-Bewegung verpflichtet. Wir glauben an:

- **Transparenz**: Alle Aspekte des wissenschaftlichen Prozesses sollten offen und nachvollziehbar sein
- **Offenen Zugang**: Wissenschaftliche Erkenntnisse sollten frei zugänglich sein
- **Offene Daten**: Forschungsdaten sollten geteilt und wiederverwendbar sein
- **Offene Methoden**: Forschungsmethoden sollten transparent und reproduzierbar sein
- **Offene Infrastruktur**: Wissenschaftliche Infrastruktur sollte offen und gemeinschaftlich entwickelt werden

Bitte beachten Sie diese Prinzipien bei Ihren Beiträgen zum Projekt.

## Wie Sie beitragen können

Es gibt viele Möglichkeiten, zu DeSci-Scholar beizutragen:

1. **Code-Beiträge**: Implementierung neuer Funktionen oder Behebung von Fehlern
2. **Dokumentation**: Verbesserung oder Erweiterung der Dokumentation
3. **Tests**: Schreiben von Tests zur Verbesserung der Codequalität
4. **Fehlerberichte**: Melden von Fehlern oder Problemen
5. **Funktionsanfragen**: Vorschläge für neue Funktionen oder Verbesserungen
6. **Übersetzungen**: Übersetzung der Dokumentation oder der Benutzeroberfläche
7. **Community-Unterstützung**: Beantwortung von Fragen in Foren oder Diskussionen

## Entwicklungsprozess

### 1. Fork und Clone

1. Erstellen Sie einen Fork des Repositories auf GitHub
2. Klonen Sie Ihren Fork auf Ihren lokalen Computer:
   ```
   git clone https://github.com/IHR_BENUTZERNAME/desci-scholar.git
   cd desci-scholar
   ```
3. Fügen Sie das Upstream-Repository hinzu:
   ```
   git remote add upstream https://github.com/desci-labs/desci-scholar.git
   ```

### 2. Branches

1. Halten Sie Ihren `main`-Branch synchron mit dem Upstream:
   ```
   git checkout main
   git pull upstream main
   ```
2. Erstellen Sie einen neuen Branch für Ihre Änderungen:
   ```
   git checkout -b feature/ihre-feature-beschreibung
   ```

### 3. Entwicklung

1. Implementieren Sie Ihre Änderungen
2. Stellen Sie sicher, dass Ihr Code den Codierungsrichtlinien entspricht
3. Schreiben Sie Tests für Ihre Änderungen
4. Stellen Sie sicher, dass alle Tests bestehen:
   ```
   npm test
   ```

### 4. Commit und Push

1. Committen Sie Ihre Änderungen mit aussagekräftigen Commit-Nachrichten:
   ```
   git commit -m "Kurze Beschreibung der Änderungen"
   ```
2. Pushen Sie Ihre Änderungen zu Ihrem Fork:
   ```
   git push origin feature/ihre-feature-beschreibung
   ```

### 5. Pull Request

1. Erstellen Sie einen Pull Request von Ihrem Branch zu `desci-labs/desci-scholar:main`
2. Beschreiben Sie Ihre Änderungen ausführlich im Pull Request
3. Verlinken Sie relevante Issues
4. Warten Sie auf Feedback und reagieren Sie auf Kommentare

## Codierungsrichtlinien

- Folgen Sie dem bestehenden Codierungsstil
- Schreiben Sie klaren, lesbaren und gut dokumentierten Code
- Kommentieren Sie komplexe Logik
- Verwenden Sie aussagekräftige Variablen- und Funktionsnamen
- Halten Sie Funktionen kurz und fokussiert

## Commit-Nachrichten

Bitte verwenden Sie klare und aussagekräftige Commit-Nachrichten. Folgen Sie diesem Format:

```
Kurze Zusammenfassung (max. 50 Zeichen)

Ausführlichere Erklärung, wenn nötig. Umbruch nach etwa 72 Zeichen.
Erklären Sie das Problem, das dieser Commit löst. Konzentrieren Sie
sich darauf, warum die Änderung notwendig ist, nicht wie sie
implementiert wurde.

Wenn es relevante Issues gibt, erwähnen Sie diese am Ende:
Fixes #123
```

## Lizenz

Durch das Einreichen eines Beitrags erklären Sie sich damit einverstanden, dass Ihr Beitrag unter der [MIT-Lizenz](LICENSE) veröffentlicht wird, die für das Projekt gilt.

## Verhaltenskodex

Wir erwarten von allen Teilnehmern, dass sie unseren [Verhaltenskodex](CODE_OF_CONDUCT.md) einhalten. Bitte lesen Sie ihn, bevor Sie beitragen.

## Fragen?

Wenn Sie Fragen haben oder Hilfe benötigen, können Sie:

- Ein Issue auf GitHub erstellen
- Eine Diskussion im Discussions-Bereich starten
- Uns per E-Mail kontaktieren: [<EMAIL>](mailto:<EMAIL>)

Vielen Dank für Ihre Beiträge zu DeSci-Scholar!# Beitragsrichtlinien für DeSci-Scholar

Vielen Dank für Ihr Interesse, zu DeSci-Scholar beizutragen! Als Open-Source- und Open-Science-Projekt leben wir von der Beteiligung der Community. Diese Richtlinien sollen Ihnen helfen, effektiv zum Projekt beizutragen.

## Open-Science-Prinzipien

DeSci-Scholar ist der Open-Science-Bewegung verpflichtet. Wir glauben an:

- **Transparenz**: Alle Aspekte des wissenschaftlichen Prozesses sollten offen und nachvollziehbar sein
- **Offenen Zugang**: Wissenschaftliche Erkenntnisse sollten frei zugänglich sein
- **Offene Daten**: Forschungsdaten sollten geteilt und wiederverwendbar sein
- **Offene Methoden**: Forschungsmethoden sollten transparent und reproduzierbar sein
- **Offene Infrastruktur**: Wissenschaftliche Infrastruktur sollte offen und gemeinschaftlich entwickelt werden

Bitte beachten Sie diese Prinzipien bei Ihren Beiträgen zum Projekt.

## Wie Sie beitragen können

Es gibt viele Möglichkeiten, zu DeSci-Scholar beizutragen:

1. **Code-Beiträge**: Implementierung neuer Funktionen oder Behebung von Fehlern
2. **Dokumentation**: Verbesserung oder Erweiterung der Dokumentation
3. **Tests**: Schreiben von Tests zur Verbesserung der Codequalität
4. **Fehlerberichte**: Melden von Fehlern oder Problemen
5. **Funktionsanfragen**: Vorschläge für neue Funktionen oder Verbesserungen
6. **Übersetzungen**: Übersetzung der Dokumentation oder der Benutzeroberfläche
7. **Community-Unterstützung**: Beantwortung von Fragen in Foren oder Diskussionen

## Entwicklungsprozess

### 1. Fork und Clone

1. Erstellen Sie einen Fork des Repositories auf GitHub
2. Klonen Sie Ihren Fork auf Ihren lokalen Computer:
   ```
   git clone https://github.com/IHR_BENUTZERNAME/desci-scholar.git
   cd desci-scholar
   ```
3. Fügen Sie das Upstream-Repository hinzu:
   ```
   git remote add upstream https://github.com/desci-labs/desci-scholar.git
   ```

### 2. Branches

1. Halten Sie Ihren `main`-Branch synchron mit dem Upstream:
   ```
   git checkout main
   git pull upstream main
   ```
2. Erstellen Sie einen neuen Branch für Ihre Änderungen:
   ```
   git checkout -b feature/ihre-feature-beschreibung
   ```

### 3. Entwicklung

1. Implementieren Sie Ihre Änderungen
2. Stellen Sie sicher, dass Ihr Code den Codierungsrichtlinien entspricht
3. Schreiben Sie Tests für Ihre Änderungen
4. Stellen Sie sicher, dass alle Tests bestehen:
   ```
   npm test
   ```

### 4. Commit und Push

1. Committen Sie Ihre Änderungen mit aussagekräftigen Commit-Nachrichten:
   ```
   git commit -m "Kurze Beschreibung der Änderungen"
   ```
2. Pushen Sie Ihre Änderungen zu Ihrem Fork:
   ```
   git push origin feature/ihre-feature-beschreibung
   ```

### 5. Pull Request

1. Erstellen Sie einen Pull Request von Ihrem Branch zu `desci-labs/desci-scholar:main`
2. Beschreiben Sie Ihre Änderungen ausführlich im Pull Request
3. Verlinken Sie relevante Issues
4. Warten Sie auf Feedback und reagieren Sie auf Kommentare

## Codierungsrichtlinien

- Folgen Sie dem bestehenden Codierungsstil
- Schreiben Sie klaren, lesbaren und gut dokumentierten Code
- Kommentieren Sie komplexe Logik
- Verwenden Sie aussagekräftige Variablen- und Funktionsnamen
- Halten Sie Funktionen kurz und fokussiert

## Commit-Nachrichten

Bitte verwenden Sie klare und aussagekräftige Commit-Nachrichten. Folgen Sie diesem Format:

```
Kurze Zusammenfassung (max. 50 Zeichen)

Ausführlichere Erklärung, wenn nötig. Umbruch nach etwa 72 Zeichen.
Erklären Sie das Problem, das dieser Commit löst. Konzentrieren Sie
sich darauf, warum die Änderung notwendig ist, nicht wie sie
implementiert wurde.

Wenn es relevante Issues gibt, erwähnen Sie diese am Ende:
Fixes #123
```

## Lizenz

Durch das Einreichen eines Beitrags erklären Sie sich damit einverstanden, dass Ihr Beitrag unter der [MIT-Lizenz](LICENSE) veröffentlicht wird, die für das Projekt gilt.

## Verhaltenskodex

Wir erwarten von allen Teilnehmern, dass sie unseren [Verhaltenskodex](CODE_OF_CONDUCT.md) einhalten. Bitte lesen Sie ihn, bevor Sie beitragen.

## Fragen?

Wenn Sie Fragen haben oder Hilfe benötigen, können Sie:

- Ein Issue auf GitHub erstellen
- Eine Diskussion im Discussions-Bereich starten
- Uns per E-Mail kontaktieren: [<EMAIL>](mailto:<EMAIL>)

Vielen Dank für Ihre Beiträge zu DeSci-Scholar!# Beitragsrichtlinien für DeSci-Scholar

Vielen Dank für Ihr Interesse, zu DeSci-Scholar beizutragen! Als Open-Source- und Open-Science-Projekt leben wir von der Beteiligung der Community. Diese Richtlinien sollen Ihnen helfen, effektiv zum Projekt beizutragen.

## Open-Science-Prinzipien

DeSci-Scholar ist der Open-Science-Bewegung verpflichtet. Wir glauben an:

- **Transparenz**: Alle Aspekte des wissenschaftlichen Prozesses sollten offen und nachvollziehbar sein
- **Offenen Zugang**: Wissenschaftliche Erkenntnisse sollten frei zugänglich sein
- **Offene Daten**: Forschungsdaten sollten geteilt und wiederverwendbar sein
- **Offene Methoden**: Forschungsmethoden sollten transparent und reproduzierbar sein
- **Offene Infrastruktur**: Wissenschaftliche Infrastruktur sollte offen und gemeinschaftlich entwickelt werden

Bitte beachten Sie diese Prinzipien bei Ihren Beiträgen zum Projekt.

## Wie Sie beitragen können

Es gibt viele Möglichkeiten, zu DeSci-Scholar beizutragen:

1. **Code-Beiträge**: Implementierung neuer Funktionen oder Behebung von Fehlern
2. **Dokumentation**: Verbesserung oder Erweiterung der Dokumentation
3. **Tests**: Schreiben von Tests zur Verbesserung der Codequalität
4. **Fehlerberichte**: Melden von Fehlern oder Problemen
5. **Funktionsanfragen**: Vorschläge für neue Funktionen oder Verbesserungen
6. **Übersetzungen**: Übersetzung der Dokumentation oder der Benutzeroberfläche
7. **Community-Unterstützung**: Beantwortung von Fragen in Foren oder Diskussionen

## Entwicklungsprozess

### 1. Fork und Clone

1. Erstellen Sie einen Fork des Repositories auf GitHub
2. Klonen Sie Ihren Fork auf Ihren lokalen Computer:
   ```
   git clone https://github.com/IHR_BENUTZERNAME/desci-scholar.git
   cd desci-scholar
   ```
3. Fügen Sie das Upstream-Repository hinzu:
   ```
   git remote add upstream https://github.com/desci-labs/desci-scholar.git
   ```

### 2. Branches

1. Halten Sie Ihren `main`-Branch synchron mit dem Upstream:
   ```
   git checkout main
   git pull upstream main
   ```
2. Erstellen Sie einen neuen Branch für Ihre Änderungen:
   ```
   git checkout -b feature/ihre-feature-beschreibung
   ```

### 3. Entwicklung

1. Implementieren Sie Ihre Änderungen
2. Stellen Sie sicher, dass Ihr Code den Codierungsrichtlinien entspricht
3. Schreiben Sie Tests für Ihre Änderungen
4. Stellen Sie sicher, dass alle Tests bestehen:
   ```
   npm test
   ```

### 4. Commit und Push

1. Committen Sie Ihre Änderungen mit aussagekräftigen Commit-Nachrichten:
   ```
   git commit -m "Kurze Beschreibung der Änderungen"
   ```
2. Pushen Sie Ihre Änderungen zu Ihrem Fork:
   ```
   git push origin feature/ihre-feature-beschreibung
   ```

### 5. Pull Request

1. Erstellen Sie einen Pull Request von Ihrem Branch zu `desci-labs/desci-scholar:main`
2. Beschreiben Sie Ihre Änderungen ausführlich im Pull Request
3. Verlinken Sie relevante Issues
4. Warten Sie auf Feedback und reagieren Sie auf Kommentare

## Codierungsrichtlinien

- Folgen Sie dem bestehenden Codierungsstil
- Schreiben Sie klaren, lesbaren und gut dokumentierten Code
- Kommentieren Sie komplexe Logik
- Verwenden Sie aussagekräftige Variablen- und Funktionsnamen
- Halten Sie Funktionen kurz und fokussiert

## Commit-Nachrichten

Bitte verwenden Sie klare und aussagekräftige Commit-Nachrichten. Folgen Sie diesem Format:

```
Kurze Zusammenfassung (max. 50 Zeichen)

Ausführlichere Erklärung, wenn nötig. Umbruch nach etwa 72 Zeichen.
Erklären Sie das Problem, das dieser Commit löst. Konzentrieren Sie
sich darauf, warum die Änderung notwendig ist, nicht wie sie
implementiert wurde.

Wenn es relevante Issues gibt, erwähnen Sie diese am Ende:
Fixes #123
```

## Lizenz

Durch das Einreichen eines Beitrags erklären Sie sich damit einverstanden, dass Ihr Beitrag unter der [MIT-Lizenz](LICENSE) veröffentlicht wird, die für das Projekt gilt.

## Verhaltenskodex

Wir erwarten von allen Teilnehmern, dass sie unseren [Verhaltenskodex](CODE_OF_CONDUCT.md) einhalten. Bitte lesen Sie ihn, bevor Sie beitragen.

## Fragen?

Wenn Sie Fragen haben oder Hilfe benötigen, können Sie:

- Ein Issue auf GitHub erstellen
- Eine Diskussion im Discussions-Bereich starten
- Uns per E-Mail kontaktieren: [<EMAIL>](mailto:<EMAIL>)

Vielen Dank für Ihre Beiträge zu DeSci-Scholar!# Beitragsrichtlinien für DeSci-Scholar

Vielen Dank für Ihr Interesse, zu DeSci-Scholar beizutragen! Als Open-Source- und Open-Science-Projekt leben wir von der Beteiligung der Community. Diese Richtlinien sollen Ihnen helfen, effektiv zum Projekt beizutragen.

## Open-Science-Prinzipien

DeSci-Scholar ist der Open-Science-Bewegung verpflichtet. Wir glauben an:

- **Transparenz**: Alle Aspekte des wissenschaftlichen Prozesses sollten offen und nachvollziehbar sein
- **Offenen Zugang**: Wissenschaftliche Erkenntnisse sollten frei zugänglich sein
- **Offene Daten**: Forschungsdaten sollten geteilt und wiederverwendbar sein
- **Offene Methoden**: Forschungsmethoden sollten transparent und reproduzierbar sein
- **Offene Infrastruktur**: Wissenschaftliche Infrastruktur sollte offen und gemeinschaftlich entwickelt werden

Bitte beachten Sie diese Prinzipien bei Ihren Beiträgen zum Projekt.

## Wie Sie beitragen können

Es gibt viele Möglichkeiten, zu DeSci-Scholar beizutragen:

1. **Code-Beiträge**: Implementierung neuer Funktionen oder Behebung von Fehlern
2. **Dokumentation**: Verbesserung oder Erweiterung der Dokumentation
3. **Tests**: Schreiben von Tests zur Verbesserung der Codequalität
4. **Fehlerberichte**: Melden von Fehlern oder Problemen
5. **Funktionsanfragen**: Vorschläge für neue Funktionen oder Verbesserungen
6. **Übersetzungen**: Übersetzung der Dokumentation oder der Benutzeroberfläche
7. **Community-Unterstützung**: Beantwortung von Fragen in Foren oder Diskussionen

## Entwicklungsprozess

### 1. Fork und Clone

1. Erstellen Sie einen Fork des Repositories auf GitHub
2. Klonen Sie Ihren Fork auf Ihren lokalen Computer:
   ```
   git clone https://github.com/IHR_BENUTZERNAME/desci-scholar.git
   cd desci-scholar
   ```
3. Fügen Sie das Upstream-Repository hinzu:
   ```
   git remote add upstream https://github.com/desci-labs/desci-scholar.git
   ```

### 2. Branches

1. Halten Sie Ihren `main`-Branch synchron mit dem Upstream:
   ```
   git checkout main
   git pull upstream main
   ```
2. Erstellen Sie einen neuen Branch für Ihre Änderungen:
   ```
   git checkout -b feature/ihre-feature-beschreibung
   ```

### 3. Entwicklung

1. Implementieren Sie Ihre Änderungen
2. Stellen Sie sicher, dass Ihr Code den Codierungsrichtlinien entspricht
3. Schreiben Sie Tests für Ihre Änderungen
4. Stellen Sie sicher, dass alle Tests bestehen:
   ```
   npm test
   ```

### 4. Commit und Push

1. Committen Sie Ihre Änderungen mit aussagekräftigen Commit-Nachrichten:
   ```
   git commit -m "Kurze Beschreibung der Änderungen"
   ```
2. Pushen Sie Ihre Änderungen zu Ihrem Fork:
   ```
   git push origin feature/ihre-feature-beschreibung
   ```

### 5. Pull Request

1. Erstellen Sie einen Pull Request von Ihrem Branch zu `desci-labs/desci-scholar:main`
2. Beschreiben Sie Ihre Änderungen ausführlich im Pull Request
3. Verlinken Sie relevante Issues
4. Warten Sie auf Feedback und reagieren Sie auf Kommentare

## Codierungsrichtlinien

- Folgen Sie dem bestehenden Codierungsstil
- Schreiben Sie klaren, lesbaren und gut dokumentierten Code
- Kommentieren Sie komplexe Logik
- Verwenden Sie aussagekräftige Variablen- und Funktionsnamen
- Halten Sie Funktionen kurz und fokussiert

## Commit-Nachrichten

Bitte verwenden Sie klare und aussagekräftige Commit-Nachrichten. Folgen Sie diesem Format:

```
Kurze Zusammenfassung (max. 50 Zeichen)

Ausführlichere Erklärung, wenn nötig. Umbruch nach etwa 72 Zeichen.
Erklären Sie das Problem, das dieser Commit löst. Konzentrieren Sie
sich darauf, warum die Änderung notwendig ist, nicht wie sie
implementiert wurde.

Wenn es relevante Issues gibt, erwähnen Sie diese am Ende:
Fixes #123
```

## Lizenz

Durch das Einreichen eines Beitrags erklären Sie sich damit einverstanden, dass Ihr Beitrag unter der [MIT-Lizenz](LICENSE) veröffentlicht wird, die für das Projekt gilt.

## Verhaltenskodex

Wir erwarten von allen Teilnehmern, dass sie unseren [Verhaltenskodex](CODE_OF_CONDUCT.md) einhalten. Bitte lesen Sie ihn, bevor Sie beitragen.

## Fragen?

Wenn Sie Fragen haben oder Hilfe benötigen, können Sie:

- Ein Issue auf GitHub erstellen
- Eine Diskussion im Discussions-Bereich starten
- Uns per E-Mail kontaktieren: [<EMAIL>](mailto:<EMAIL>)

Vielen Dank für Ihre Beiträge zu DeSci-Scholar!# Beitragsrichtlinien für DeSci-Scholar

Vielen Dank für Ihr Interesse, zu DeSci-Scholar beizutragen! Als Open-Source- und Open-Science-Projekt leben wir von der Beteiligung der Community. Diese Richtlinien sollen Ihnen helfen, effektiv zum Projekt beizutragen.

## Open-Science-Prinzipien

DeSci-Scholar ist der Open-Science-Bewegung verpflichtet. Wir glauben an:

- **Transparenz**: Alle Aspekte des wissenschaftlichen Prozesses sollten offen und nachvollziehbar sein
- **Offenen Zugang**: Wissenschaftliche Erkenntnisse sollten frei zugänglich sein
- **Offene Daten**: Forschungsdaten sollten geteilt und wiederverwendbar sein
- **Offene Methoden**: Forschungsmethoden sollten transparent und reproduzierbar sein
- **Offene Infrastruktur**: Wissenschaftliche Infrastruktur sollte offen und gemeinschaftlich entwickelt werden

Bitte beachten Sie diese Prinzipien bei Ihren Beiträgen zum Projekt.

## Wie Sie beitragen können

Es gibt viele Möglichkeiten, zu DeSci-Scholar beizutragen:

1. **Code-Beiträge**: Implementierung neuer Funktionen oder Behebung von Fehlern
2. **Dokumentation**: Verbesserung oder Erweiterung der Dokumentation
3. **Tests**: Schreiben von Tests zur Verbesserung der Codequalität
4. **Fehlerberichte**: Melden von Fehlern oder Problemen
5. **Funktionsanfragen**: Vorschläge für neue Funktionen oder Verbesserungen
6. **Übersetzungen**: Übersetzung der Dokumentation oder der Benutzeroberfläche
7. **Community-Unterstützung**: Beantwortung von Fragen in Foren oder Diskussionen

## Entwicklungsprozess

### 1. Fork und Clone

1. Erstellen Sie einen Fork des Repositories auf GitHub
2. Klonen Sie Ihren Fork auf Ihren lokalen Computer:
   ```
   git clone https://github.com/IHR_BENUTZERNAME/desci-scholar.git
   cd desci-scholar
   ```
3. Fügen Sie das Upstream-Repository hinzu:
   ```
   git remote add upstream https://github.com/desci-labs/desci-scholar.git
   ```

### 2. Branches

1. Halten Sie Ihren `main`-Branch synchron mit dem Upstream:
   ```
   git checkout main
   git pull upstream main
   ```
2. Erstellen Sie einen neuen Branch für Ihre Änderungen:
   ```
   git checkout -b feature/ihre-feature-beschreibung
   ```

### 3. Entwicklung

1. Implementieren Sie Ihre Änderungen
2. Stellen Sie sicher, dass Ihr Code den Codierungsrichtlinien entspricht
3. Schreiben Sie Tests für Ihre Änderungen
4. Stellen Sie sicher, dass alle Tests bestehen:
   ```
   npm test
   ```

### 4. Commit und Push

1. Committen Sie Ihre Änderungen mit aussagekräftigen Commit-Nachrichten:
   ```
   git commit -m "Kurze Beschreibung der Änderungen"
   ```
2. Pushen Sie Ihre Änderungen zu Ihrem Fork:
   ```
   git push origin feature/ihre-feature-beschreibung
   ```

### 5. Pull Request

1. Erstellen Sie einen Pull Request von Ihrem Branch zu `desci-labs/desci-scholar:main`
2. Beschreiben Sie Ihre Änderungen ausführlich im Pull Request
3. Verlinken Sie relevante Issues
4. Warten Sie auf Feedback und reagieren Sie auf Kommentare

## Codierungsrichtlinien

- Folgen Sie dem bestehenden Codierungsstil
- Schreiben Sie klaren, lesbaren und gut dokumentierten Code
- Kommentieren Sie komplexe Logik
- Verwenden Sie aussagekräftige Variablen- und Funktionsnamen
- Halten Sie Funktionen kurz und fokussiert

## Commit-Nachrichten

Bitte verwenden Sie klare und aussagekräftige Commit-Nachrichten. Folgen Sie diesem Format:

```
Kurze Zusammenfassung (max. 50 Zeichen)

Ausführlichere Erklärung, wenn nötig. Umbruch nach etwa 72 Zeichen.
Erklären Sie das Problem, das dieser Commit löst. Konzentrieren Sie
sich darauf, warum die Änderung notwendig ist, nicht wie sie
implementiert wurde.

Wenn es relevante Issues gibt, erwähnen Sie diese am Ende:
Fixes #123
```

## Lizenz

Durch das Einreichen eines Beitrags erklären Sie sich damit einverstanden, dass Ihr Beitrag unter der [MIT-Lizenz](LICENSE) veröffentlicht wird, die für das Projekt gilt.

## Verhaltenskodex

Wir erwarten von allen Teilnehmern, dass sie unseren [Verhaltenskodex](CODE_OF_CONDUCT.md) einhalten. Bitte lesen Sie ihn, bevor Sie beitragen.

## Fragen?

Wenn Sie Fragen haben oder Hilfe benötigen, können Sie:

- Ein Issue auf GitHub erstellen
- Eine Diskussion im Discussions-Bereich starten
- Uns per E-Mail kontaktieren: [<EMAIL>](mailto:<EMAIL>)

Vielen Dank für Ihre Beiträge zu DeSci-Scholar!# Beitragsrichtlinien für DeSci-Scholar

Vielen Dank für Ihr Interesse, zu DeSci-Scholar beizutragen! Als Open-Source- und Open-Science-Projekt leben wir von der Beteiligung der Community. Diese Richtlinien sollen Ihnen helfen, effektiv zum Projekt beizutragen.

## Open-Science-Prinzipien

DeSci-Scholar ist der Open-Science-Bewegung verpflichtet. Wir glauben an:

- **Transparenz**: Alle Aspekte des wissenschaftlichen Prozesses sollten offen und nachvollziehbar sein
- **Offenen Zugang**: Wissenschaftliche Erkenntnisse sollten frei zugänglich sein
- **Offene Daten**: Forschungsdaten sollten geteilt und wiederverwendbar sein
- **Offene Methoden**: Forschungsmethoden sollten transparent und reproduzierbar sein
- **Offene Infrastruktur**: Wissenschaftliche Infrastruktur sollte offen und gemeinschaftlich entwickelt werden

Bitte beachten Sie diese Prinzipien bei Ihren Beiträgen zum Projekt.

## Wie Sie beitragen können

Es gibt viele Möglichkeiten, zu DeSci-Scholar beizutragen:

1. **Code-Beiträge**: Implementierung neuer Funktionen oder Behebung von Fehlern
2. **Dokumentation**: Verbesserung oder Erweiterung der Dokumentation
3. **Tests**: Schreiben von Tests zur Verbesserung der Codequalität
4. **Fehlerberichte**: Melden von Fehlern oder Problemen
5. **Funktionsanfragen**: Vorschläge für neue Funktionen oder Verbesserungen
6. **Übersetzungen**: Übersetzung der Dokumentation oder der Benutzeroberfläche
7. **Community-Unterstützung**: Beantwortung von Fragen in Foren oder Diskussionen

## Entwicklungsprozess

### 1. Fork und Clone

1. Erstellen Sie einen Fork des Repositories auf GitHub
2. Klonen Sie Ihren Fork auf Ihren lokalen Computer:
   ```
   git clone https://github.com/IHR_BENUTZERNAME/desci-scholar.git
   cd desci-scholar
   ```
3. Fügen Sie das Upstream-Repository hinzu:
   ```
   git remote add upstream https://github.com/desci-labs/desci-scholar.git
   ```

### 2. Branches

1. Halten Sie Ihren `main`-Branch synchron mit dem Upstream:
   ```
   git checkout main
   git pull upstream main
   ```
2. Erstellen Sie einen neuen Branch für Ihre Änderungen:
   ```
   git checkout -b feature/ihre-feature-beschreibung
   ```

### 3. Entwicklung

1. Implementieren Sie Ihre Änderungen
2. Stellen Sie sicher, dass Ihr Code den Codierungsrichtlinien entspricht
3. Schreiben Sie Tests für Ihre Änderungen
4. Stellen Sie sicher, dass alle Tests bestehen:
   ```
   npm test
   ```

### 4. Commit und Push

1. Committen Sie Ihre Änderungen mit aussagekräftigen Commit-Nachrichten:
   ```
   git commit -m "Kurze Beschreibung der Änderungen"
   ```
2. Pushen Sie Ihre Änderungen zu Ihrem Fork:
   ```
   git push origin feature/ihre-feature-beschreibung
   ```

### 5. Pull Request

1. Erstellen Sie einen Pull Request von Ihrem Branch zu `desci-labs/desci-scholar:main`
2. Beschreiben Sie Ihre Änderungen ausführlich im Pull Request
3. Verlinken Sie relevante Issues
4. Warten Sie auf Feedback und reagieren Sie auf Kommentare

## Codierungsrichtlinien

- Folgen Sie dem bestehenden Codierungsstil
- Schreiben Sie klaren, lesbaren und gut dokumentierten Code
- Kommentieren Sie komplexe Logik
- Verwenden Sie aussagekräftige Variablen- und Funktionsnamen
- Halten Sie Funktionen kurz und fokussiert

## Commit-Nachrichten

Bitte verwenden Sie klare und aussagekräftige Commit-Nachrichten. Folgen Sie diesem Format:

```
Kurze Zusammenfassung (max. 50 Zeichen)

Ausführlichere Erklärung, wenn nötig. Umbruch nach etwa 72 Zeichen.
Erklären Sie das Problem, das dieser Commit löst. Konzentrieren Sie
sich darauf, warum die Änderung notwendig ist, nicht wie sie
implementiert wurde.

Wenn es relevante Issues gibt, erwähnen Sie diese am Ende:
Fixes #123
```

## Lizenz

Durch das Einreichen eines Beitrags erklären Sie sich damit einverstanden, dass Ihr Beitrag unter der [MIT-Lizenz](LICENSE) veröffentlicht wird, die für das Projekt gilt.

## Verhaltenskodex

Wir erwarten von allen Teilnehmern, dass sie unseren [Verhaltenskodex](CODE_OF_CONDUCT.md) einhalten. Bitte lesen Sie ihn, bevor Sie beitragen.

## Fragen?

Wenn Sie Fragen haben oder Hilfe benötigen, können Sie:

- Ein Issue auf GitHub erstellen
- Eine Diskussion im Discussions-Bereich starten
- Uns per E-Mail kontaktieren: [<EMAIL>](mailto:<EMAIL>)

Vielen Dank für Ihre Beiträge zu DeSci-Scholar!# Beitragsrichtlinien für DeSci-Scholar

Vielen Dank für Ihr Interesse, zu DeSci-Scholar beizutragen! Als Open-Source- und Open-Science-Projekt leben wir von der Beteiligung der Community. Diese Richtlinien sollen Ihnen helfen, effektiv zum Projekt beizutragen.

## Open-Science-Prinzipien

DeSci-Scholar ist der Open-Science-Bewegung verpflichtet. Wir glauben an:

- **Transparenz**: Alle Aspekte des wissenschaftlichen Prozesses sollten offen und nachvollziehbar sein
- **Offenen Zugang**: Wissenschaftliche Erkenntnisse sollten frei zugänglich sein
- **Offene Daten**: Forschungsdaten sollten geteilt und wiederverwendbar sein
- **Offene Methoden**: Forschungsmethoden sollten transparent und reproduzierbar sein
- **Offene Infrastruktur**: Wissenschaftliche Infrastruktur sollte offen und gemeinschaftlich entwickelt werden

Bitte beachten Sie diese Prinzipien bei Ihren Beiträgen zum Projekt.

## Wie Sie beitragen können

Es gibt viele Möglichkeiten, zu DeSci-Scholar beizutragen:

1. **Code-Beiträge**: Implementierung neuer Funktionen oder Behebung von Fehlern
2. **Dokumentation**: Verbesserung oder Erweiterung der Dokumentation
3. **Tests**: Schreiben von Tests zur Verbesserung der Codequalität
4. **Fehlerberichte**: Melden von Fehlern oder Problemen
5. **Funktionsanfragen**: Vorschläge für neue Funktionen oder Verbesserungen
6. **Übersetzungen**: Übersetzung der Dokumentation oder der Benutzeroberfläche
7. **Community-Unterstützung**: Beantwortung von Fragen in Foren oder Diskussionen

## Entwicklungsprozess

### 1. Fork und Clone

1. Erstellen Sie einen Fork des Repositories auf GitHub
2. Klonen Sie Ihren Fork auf Ihren lokalen Computer:
   ```
   git clone https://github.com/IHR_BENUTZERNAME/desci-scholar.git
   cd desci-scholar
   ```
3. Fügen Sie das Upstream-Repository hinzu:
   ```
   git remote add upstream https://github.com/desci-labs/desci-scholar.git
   ```

### 2. Branches

1. Halten Sie Ihren `main`-Branch synchron mit dem Upstream:
   ```
   git checkout main
   git pull upstream main
   ```
2. Erstellen Sie einen neuen Branch für Ihre Änderungen:
   ```
   git checkout -b feature/ihre-feature-beschreibung
   ```

### 3. Entwicklung

1. Implementieren Sie Ihre Änderungen
2. Stellen Sie sicher, dass Ihr Code den Codierungsrichtlinien entspricht
3. Schreiben Sie Tests für Ihre Änderungen
4. Stellen Sie sicher, dass alle Tests bestehen:
   ```
   npm test
   ```

### 4. Commit und Push

1. Committen Sie Ihre Änderungen mit aussagekräftigen Commit-Nachrichten:
   ```
   git commit -m "Kurze Beschreibung der Änderungen"
   ```
2. Pushen Sie Ihre Änderungen zu Ihrem Fork:
   ```
   git push origin feature/ihre-feature-beschreibung
   ```

### 5. Pull Request

1. Erstellen Sie einen Pull Request von Ihrem Branch zu `desci-labs/desci-scholar:main`
2. Beschreiben Sie Ihre Änderungen ausführlich im Pull Request
3. Verlinken Sie relevante Issues
4. Warten Sie auf Feedback und reagieren Sie auf Kommentare

## Codierungsrichtlinien

- Folgen Sie dem bestehenden Codierungsstil
- Schreiben Sie klaren, lesbaren und gut dokumentierten Code
- Kommentieren Sie komplexe Logik
- Verwenden Sie aussagekräftige Variablen- und Funktionsnamen
- Halten Sie Funktionen kurz und fokussiert

## Commit-Nachrichten

Bitte verwenden Sie klare und aussagekräftige Commit-Nachrichten. Folgen Sie diesem Format:

```
Kurze Zusammenfassung (max. 50 Zeichen)

Ausführlichere Erklärung, wenn nötig. Umbruch nach etwa 72 Zeichen.
Erklären Sie das Problem, das dieser Commit löst. Konzentrieren Sie
sich darauf, warum die Änderung notwendig ist, nicht wie sie
implementiert wurde.

Wenn es relevante Issues gibt, erwähnen Sie diese am Ende:
Fixes #123
```

## Lizenz

Durch das Einreichen eines Beitrags erklären Sie sich damit einverstanden, dass Ihr Beitrag unter der [MIT-Lizenz](LICENSE) veröffentlicht wird, die für das Projekt gilt.

## Verhaltenskodex

Wir erwarten von allen Teilnehmern, dass sie unseren [Verhaltenskodex](CODE_OF_CONDUCT.md) einhalten. Bitte lesen Sie ihn, bevor Sie beitragen.

## Fragen?

Wenn Sie Fragen haben oder Hilfe benötigen, können Sie:

- Ein Issue auf GitHub erstellen
- Eine Diskussion im Discussions-Bereich starten
- Uns per E-Mail kontaktieren: [<EMAIL>](mailto:<EMAIL>)

Vielen Dank für Ihre Beiträge zu DeSci-Scholar!# Beitragsrichtlinien für DeSci-Scholar

Vielen Dank für Ihr Interesse, zu DeSci-Scholar beizutragen! Als Open-Source- und Open-Science-Projekt leben wir von der Beteiligung der Community. Diese Richtlinien sollen Ihnen helfen, effektiv zum Projekt beizutragen.

## Open-Science-Prinzipien

DeSci-Scholar ist der Open-Science-Bewegung verpflichtet. Wir glauben an:

- **Transparenz**: Alle Aspekte des wissenschaftlichen Prozesses sollten offen und nachvollziehbar sein
- **Offenen Zugang**: Wissenschaftliche Erkenntnisse sollten frei zugänglich sein
- **Offene Daten**: Forschungsdaten sollten geteilt und wiederverwendbar sein
- **Offene Methoden**: Forschungsmethoden sollten transparent und reproduzierbar sein
- **Offene Infrastruktur**: Wissenschaftliche Infrastruktur sollte offen und gemeinschaftlich entwickelt werden

Bitte beachten Sie diese Prinzipien bei Ihren Beiträgen zum Projekt.

## Wie Sie beitragen können

Es gibt viele Möglichkeiten, zu DeSci-Scholar beizutragen:

1. **Code-Beiträge**: Implementierung neuer Funktionen oder Behebung von Fehlern
2. **Dokumentation**: Verbesserung oder Erweiterung der Dokumentation
3. **Tests**: Schreiben von Tests zur Verbesserung der Codequalität
4. **Fehlerberichte**: Melden von Fehlern oder Problemen
5. **Funktionsanfragen**: Vorschläge für neue Funktionen oder Verbesserungen
6. **Übersetzungen**: Übersetzung der Dokumentation oder der Benutzeroberfläche
7. **Community-Unterstützung**: Beantwortung von Fragen in Foren oder Diskussionen

## Entwicklungsprozess

### 1. Fork und Clone

1. Erstellen Sie einen Fork des Repositories auf GitHub
2. Klonen Sie Ihren Fork auf Ihren lokalen Computer:
   ```
   git clone https://github.com/IHR_BENUTZERNAME/desci-scholar.git
   cd desci-scholar
   ```
3. Fügen Sie das Upstream-Repository hinzu:
   ```
   git remote add upstream https://github.com/desci-labs/desci-scholar.git
   ```

### 2. Branches

1. Halten Sie Ihren `main`-Branch synchron mit dem Upstream:
   ```
   git checkout main
   git pull upstream main
   ```
2. Erstellen Sie einen neuen Branch für Ihre Änderungen:
   ```
   git checkout -b feature/ihre-feature-beschreibung
   ```

### 3. Entwicklung

1. Implementieren Sie Ihre Änderungen
2. Stellen Sie sicher, dass Ihr Code den Codierungsrichtlinien entspricht
3. Schreiben Sie Tests für Ihre Änderungen
4. Stellen Sie sicher, dass alle Tests bestehen:
   ```
   npm test
   ```

### 4. Commit und Push

1. Committen Sie Ihre Änderungen mit aussagekräftigen Commit-Nachrichten:
   ```
   git commit -m "Kurze Beschreibung der Änderungen"
   ```
2. Pushen Sie Ihre Änderungen zu Ihrem Fork:
   ```
   git push origin feature/ihre-feature-beschreibung
   ```

### 5. Pull Request

1. Erstellen Sie einen Pull Request von Ihrem Branch zu `desci-labs/desci-scholar:main`
2. Beschreiben Sie Ihre Änderungen ausführlich im Pull Request
3. Verlinken Sie relevante Issues
4. Warten Sie auf Feedback und reagieren Sie auf Kommentare

## Codierungsrichtlinien

- Folgen Sie dem bestehenden Codierungsstil
- Schreiben Sie klaren, lesbaren und gut dokumentierten Code
- Kommentieren Sie komplexe Logik
- Verwenden Sie aussagekräftige Variablen- und Funktionsnamen
- Halten Sie Funktionen kurz und fokussiert

## Commit-Nachrichten

Bitte verwenden Sie klare und aussagekräftige Commit-Nachrichten. Folgen Sie diesem Format:

```
Kurze Zusammenfassung (max. 50 Zeichen)

Ausführlichere Erklärung, wenn nötig. Umbruch nach etwa 72 Zeichen.
Erklären Sie das Problem, das dieser Commit löst. Konzentrieren Sie
sich darauf, warum die Änderung notwendig ist, nicht wie sie
implementiert wurde.

Wenn es relevante Issues gibt, erwähnen Sie diese am Ende:
Fixes #123
```

## Lizenz

Durch das Einreichen eines Beitrags erklären Sie sich damit einverstanden, dass Ihr Beitrag unter der [MIT-Lizenz](LICENSE) veröffentlicht wird, die für das Projekt gilt.

## Verhaltenskodex

Wir erwarten von allen Teilnehmern, dass sie unseren [Verhaltenskodex](CODE_OF_CONDUCT.md) einhalten. Bitte lesen Sie ihn, bevor Sie beitragen.

## Fragen?

Wenn Sie Fragen haben oder Hilfe benötigen, können Sie:

- Ein Issue auf GitHub erstellen
- Eine Diskussion im Discussions-Bereich starten
- Uns per E-Mail kontaktieren: [<EMAIL>](mailto:<EMAIL>)

Vielen Dank für Ihre Beiträge zu DeSci-Scholar!# Beitragsrichtlinien für DeSci-Scholar

Vielen Dank für Ihr Interesse, zu DeSci-Scholar beizutragen! Als Open-Source- und Open-Science-Projekt leben wir von der Beteiligung der Community. Diese Richtlinien sollen Ihnen helfen, effektiv zum Projekt beizutragen.

## Open-Science-Prinzipien

DeSci-Scholar ist der Open-Science-Bewegung verpflichtet. Wir glauben an:

- **Transparenz**: Alle Aspekte des wissenschaftlichen Prozesses sollten offen und nachvollziehbar sein
- **Offenen Zugang**: Wissenschaftliche Erkenntnisse sollten frei zugänglich sein
- **Offene Daten**: Forschungsdaten sollten geteilt und wiederverwendbar sein
- **Offene Methoden**: Forschungsmethoden sollten transparent und reproduzierbar sein
- **Offene Infrastruktur**: Wissenschaftliche Infrastruktur sollte offen und gemeinschaftlich entwickelt werden

Bitte beachten Sie diese Prinzipien bei Ihren Beiträgen zum Projekt.

## Wie Sie beitragen können

Es gibt viele Möglichkeiten, zu DeSci-Scholar beizutragen:

1. **Code-Beiträge**: Implementierung neuer Funktionen oder Behebung von Fehlern
2. **Dokumentation**: Verbesserung oder Erweiterung der Dokumentation
3. **Tests**: Schreiben von Tests zur Verbesserung der Codequalität
4. **Fehlerberichte**: Melden von Fehlern oder Problemen
5. **Funktionsanfragen**: Vorschläge für neue Funktionen oder Verbesserungen
6. **Übersetzungen**: Übersetzung der Dokumentation oder der Benutzeroberfläche
7. **Community-Unterstützung**: Beantwortung von Fragen in Foren oder Diskussionen

## Entwicklungsprozess

### 1. Fork und Clone

1. Erstellen Sie einen Fork des Repositories auf GitHub
2. Klonen Sie Ihren Fork auf Ihren lokalen Computer:
   ```
   git clone https://github.com/IHR_BENUTZERNAME/desci-scholar.git
   cd desci-scholar
   ```
3. Fügen Sie das Upstream-Repository hinzu:
   ```
   git remote add upstream https://github.com/desci-labs/desci-scholar.git
   ```

### 2. Branches

1. Halten Sie Ihren `main`-Branch synchron mit dem Upstream:
   ```
   git checkout main
   git pull upstream main
   ```
2. Erstellen Sie einen neuen Branch für Ihre Änderungen:
   ```
   git checkout -b feature/ihre-feature-beschreibung
   ```

### 3. Entwicklung

1. Implementieren Sie Ihre Änderungen
2. Stellen Sie sicher, dass Ihr Code den Codierungsrichtlinien entspricht
3. Schreiben Sie Tests für Ihre Änderungen
4. Stellen Sie sicher, dass alle Tests bestehen:
   ```
   npm test
   ```

### 4. Commit und Push

1. Committen Sie Ihre Änderungen mit aussagekräftigen Commit-Nachrichten:
   ```
   git commit -m "Kurze Beschreibung der Änderungen"
   ```
2. Pushen Sie Ihre Änderungen zu Ihrem Fork:
   ```
   git push origin feature/ihre-feature-beschreibung
   ```

### 5. Pull Request

1. Erstellen Sie einen Pull Request von Ihrem Branch zu `desci-labs/desci-scholar:main`
2. Beschreiben Sie Ihre Änderungen ausführlich im Pull Request
3. Verlinken Sie relevante Issues
4. Warten Sie auf Feedback und reagieren Sie auf Kommentare

## Codierungsrichtlinien

- Folgen Sie dem bestehenden Codierungsstil
- Schreiben Sie klaren, lesbaren und gut dokumentierten Code
- Kommentieren Sie komplexe Logik
- Verwenden Sie aussagekräftige Variablen- und Funktionsnamen
- Halten Sie Funktionen kurz und fokussiert

## Commit-Nachrichten

Bitte verwenden Sie klare und aussagekräftige Commit-Nachrichten. Folgen Sie diesem Format:

```
Kurze Zusammenfassung (max. 50 Zeichen)

Ausführlichere Erklärung, wenn nötig. Umbruch nach etwa 72 Zeichen.
Erklären Sie das Problem, das dieser Commit löst. Konzentrieren Sie
sich darauf, warum die Änderung notwendig ist, nicht wie sie
implementiert wurde.

Wenn es relevante Issues gibt, erwähnen Sie diese am Ende:
Fixes #123
```

## Lizenz

Durch das Einreichen eines Beitrags erklären Sie sich damit einverstanden, dass Ihr Beitrag unter der [MIT-Lizenz](LICENSE) veröffentlicht wird, die für das Projekt gilt.

## Verhaltenskodex

Wir erwarten von allen Teilnehmern, dass sie unseren [Verhaltenskodex](CODE_OF_CONDUCT.md) einhalten. Bitte lesen Sie ihn, bevor Sie beitragen.

## Fragen?

Wenn Sie Fragen haben oder Hilfe benötigen, können Sie:

- Ein Issue auf GitHub erstellen
- Eine Diskussion im Discussions-Bereich starten
- Uns per E-Mail kontaktieren: [<EMAIL>](mailto:<EMAIL>)

Vielen Dank für Ihre Beiträge zu DeSci-Scholar!# Beitragsrichtlinien für DeSci-Scholar

Vielen Dank für Ihr Interesse, zu DeSci-Scholar beizutragen! Als Open-Source- und Open-Science-Projekt leben wir von der Beteiligung der Community. Diese Richtlinien sollen Ihnen helfen, effektiv zum Projekt beizutragen.

## Open-Science-Prinzipien

DeSci-Scholar ist der Open-Science-Bewegung verpflichtet. Wir glauben an:

- **Transparenz**: Alle Aspekte des wissenschaftlichen Prozesses sollten offen und nachvollziehbar sein
- **Offenen Zugang**: Wissenschaftliche Erkenntnisse sollten frei zugänglich sein
- **Offene Daten**: Forschungsdaten sollten geteilt und wiederverwendbar sein
- **Offene Methoden**: Forschungsmethoden sollten transparent und reproduzierbar sein
- **Offene Infrastruktur**: Wissenschaftliche Infrastruktur sollte offen und gemeinschaftlich entwickelt werden

Bitte beachten Sie diese Prinzipien bei Ihren Beiträgen zum Projekt.

## Wie Sie beitragen können

Es gibt viele Möglichkeiten, zu DeSci-Scholar beizutragen:

1. **Code-Beiträge**: Implementierung neuer Funktionen oder Behebung von Fehlern
2. **Dokumentation**: Verbesserung oder Erweiterung der Dokumentation
3. **Tests**: Schreiben von Tests zur Verbesserung der Codequalität
4. **Fehlerberichte**: Melden von Fehlern oder Problemen
5. **Funktionsanfragen**: Vorschläge für neue Funktionen oder Verbesserungen
6. **Übersetzungen**: Übersetzung der Dokumentation oder der Benutzeroberfläche
7. **Community-Unterstützung**: Beantwortung von Fragen in Foren oder Diskussionen

## Entwicklungsprozess

### 1. Fork und Clone

1. Erstellen Sie einen Fork des Repositories auf GitHub
2. Klonen Sie Ihren Fork auf Ihren lokalen Computer:
   ```
   git clone https://github.com/IHR_BENUTZERNAME/desci-scholar.git
   cd desci-scholar
   ```
3. Fügen Sie das Upstream-Repository hinzu:
   ```
   git remote add upstream https://github.com/desci-labs/desci-scholar.git
   ```

### 2. Branches

1. Halten Sie Ihren `main`-Branch synchron mit dem Upstream:
   ```
   git checkout main
   git pull upstream main
   ```
2. Erstellen Sie einen neuen Branch für Ihre Änderungen:
   ```
   git checkout -b feature/ihre-feature-beschreibung
   ```

### 3. Entwicklung

1. Implementieren Sie Ihre Änderungen
2. Stellen Sie sicher, dass Ihr Code den Codierungsrichtlinien entspricht
3. Schreiben Sie Tests für Ihre Änderungen
4. Stellen Sie sicher, dass alle Tests bestehen:
   ```
   npm test
   ```

### 4. Commit und Push

1. Committen Sie Ihre Änderungen mit aussagekräftigen Commit-Nachrichten:
   ```
   git commit -m "Kurze Beschreibung der Änderungen"
   ```
2. Pushen Sie Ihre Änderungen zu Ihrem Fork:
   ```
   git push origin feature/ihre-feature-beschreibung
   ```

### 5. Pull Request

1. Erstellen Sie einen Pull Request von Ihrem Branch zu `desci-labs/desci-scholar:main`
2. Beschreiben Sie Ihre Änderungen ausführlich im Pull Request
3. Verlinken Sie relevante Issues
4. Warten Sie auf Feedback und reagieren Sie auf Kommentare

## Codierungsrichtlinien

- Folgen Sie dem bestehenden Codierungsstil
- Schreiben Sie klaren, lesbaren und gut dokumentierten Code
- Kommentieren Sie komplexe Logik
- Verwenden Sie aussagekräftige Variablen- und Funktionsnamen
- Halten Sie Funktionen kurz und fokussiert

## Commit-Nachrichten

Bitte verwenden Sie klare und aussagekräftige Commit-Nachrichten. Folgen Sie diesem Format:

```
Kurze Zusammenfassung (max. 50 Zeichen)

Ausführlichere Erklärung, wenn nötig. Umbruch nach etwa 72 Zeichen.
Erklären Sie das Problem, das dieser Commit löst. Konzentrieren Sie
sich darauf, warum die Änderung notwendig ist, nicht wie sie
implementiert wurde.

Wenn es relevante Issues gibt, erwähnen Sie diese am Ende:
Fixes #123
```

## Lizenz

Durch das Einreichen eines Beitrags erklären Sie sich damit einverstanden, dass Ihr Beitrag unter der [MIT-Lizenz](LICENSE) veröffentlicht wird, die für das Projekt gilt.

## Verhaltenskodex

Wir erwarten von allen Teilnehmern, dass sie unseren [Verhaltenskodex](CODE_OF_CONDUCT.md) einhalten. Bitte lesen Sie ihn, bevor Sie beitragen.

## Fragen?

Wenn Sie Fragen haben oder Hilfe benötigen, können Sie:

- Ein Issue auf GitHub erstellen
- Eine Diskussion im Discussions-Bereich starten
- Uns per E-Mail kontaktieren: [<EMAIL>](mailto:<EMAIL>)

Vielen Dank für Ihre Beiträge zu DeSci-Scholar!# Beitragsrichtlinien für DeSci-Scholar

Vielen Dank für Ihr Interesse, zu DeSci-Scholar beizutragen! Als Open-Source- und Open-Science-Projekt leben wir von der Beteiligung der Community. Diese Richtlinien sollen Ihnen helfen, effektiv zum Projekt beizutragen.

## Open-Science-Prinzipien

DeSci-Scholar ist der Open-Science-Bewegung verpflichtet. Wir glauben an:

- **Transparenz**: Alle Aspekte des wissenschaftlichen Prozesses sollten offen und nachvollziehbar sein
- **Offenen Zugang**: Wissenschaftliche Erkenntnisse sollten frei zugänglich sein
- **Offene Daten**: Forschungsdaten sollten geteilt und wiederverwendbar sein
- **Offene Methoden**: Forschungsmethoden sollten transparent und reproduzierbar sein
- **Offene Infrastruktur**: Wissenschaftliche Infrastruktur sollte offen und gemeinschaftlich entwickelt werden

Bitte beachten Sie diese Prinzipien bei Ihren Beiträgen zum Projekt.

## Wie Sie beitragen können

Es gibt viele Möglichkeiten, zu DeSci-Scholar beizutragen:

1. **Code-Beiträge**: Implementierung neuer Funktionen oder Behebung von Fehlern
2. **Dokumentation**: Verbesserung oder Erweiterung der Dokumentation
3. **Tests**: Schreiben von Tests zur Verbesserung der Codequalität
4. **Fehlerberichte**: Melden von Fehlern oder Problemen
5. **Funktionsanfragen**: Vorschläge für neue Funktionen oder Verbesserungen
6. **Übersetzungen**: Übersetzung der Dokumentation oder der Benutzeroberfläche
7. **Community-Unterstützung**: Beantwortung von Fragen in Foren oder Diskussionen

## Entwicklungsprozess

### 1. Fork und Clone

1. Erstellen Sie einen Fork des Repositories auf GitHub
2. Klonen Sie Ihren Fork auf Ihren lokalen Computer:
   ```
   git clone https://github.com/IHR_BENUTZERNAME/desci-scholar.git
   cd desci-scholar
   ```
3. Fügen Sie das Upstream-Repository hinzu:
   ```
   git remote add upstream https://github.com/desci-labs/desci-scholar.git
   ```

### 2. Branches

1. Halten Sie Ihren `main`-Branch synchron mit dem Upstream:
   ```
   git checkout main
   git pull upstream main
   ```
2. Erstellen Sie einen neuen Branch für Ihre Änderungen:
   ```
   git checkout -b feature/ihre-feature-beschreibung
   ```

### 3. Entwicklung

1. Implementieren Sie Ihre Änderungen
2. Stellen Sie sicher, dass Ihr Code den Codierungsrichtlinien entspricht
3. Schreiben Sie Tests für Ihre Änderungen
4. Stellen Sie sicher, dass alle Tests bestehen:
   ```
   npm test
   ```

### 4. Commit und Push

1. Committen Sie Ihre Änderungen mit aussagekräftigen Commit-Nachrichten:
   ```
   git commit -m "Kurze Beschreibung der Änderungen"
   ```
2. Pushen Sie Ihre Änderungen zu Ihrem Fork:
   ```
   git push origin feature/ihre-feature-beschreibung
   ```

### 5. Pull Request

1. Erstellen Sie einen Pull Request von Ihrem Branch zu `desci-labs/desci-scholar:main`
2. Beschreiben Sie Ihre Änderungen ausführlich im Pull Request
3. Verlinken Sie relevante Issues
4. Warten Sie auf Feedback und reagieren Sie auf Kommentare

## Codierungsrichtlinien

- Folgen Sie dem bestehenden Codierungsstil
- Schreiben Sie klaren, lesbaren und gut dokumentierten Code
- Kommentieren Sie komplexe Logik
- Verwenden Sie aussagekräftige Variablen- und Funktionsnamen
- Halten Sie Funktionen kurz und fokussiert

## Commit-Nachrichten

Bitte verwenden Sie klare und aussagekräftige Commit-Nachrichten. Folgen Sie diesem Format:

```
Kurze Zusammenfassung (max. 50 Zeichen)

Ausführlichere Erklärung, wenn nötig. Umbruch nach etwa 72 Zeichen.
Erklären Sie das Problem, das dieser Commit löst. Konzentrieren Sie
sich darauf, warum die Änderung notwendig ist, nicht wie sie
implementiert wurde.

Wenn es relevante Issues gibt, erwähnen Sie diese am Ende:
Fixes #123
```

## Lizenz

Durch das Einreichen eines Beitrags erklären Sie sich damit einverstanden, dass Ihr Beitrag unter der [MIT-Lizenz](LICENSE) veröffentlicht wird, die für das Projekt gilt.

## Verhaltenskodex

Wir erwarten von allen Teilnehmern, dass sie unseren [Verhaltenskodex](CODE_OF_CONDUCT.md) einhalten. Bitte lesen Sie ihn, bevor Sie beitragen.

## Fragen?

Wenn Sie Fragen haben oder Hilfe benötigen, können Sie:

- Ein Issue auf GitHub erstellen
- Eine Diskussion im Discussions-Bereich starten
- Uns per E-Mail kontaktieren: [<EMAIL>](mailto:<EMAIL>)

Vielen Dank für Ihre Beiträge zu DeSci-Scholar!# Beitragsrichtlinien für DeSci-Scholar

Vielen Dank für Ihr Interesse, zu DeSci-Scholar beizutragen! Als Open-Source- und Open-Science-Projekt leben wir von der Beteiligung der Community. Diese Richtlinien sollen Ihnen helfen, effektiv zum Projekt beizutragen.

## Open-Science-Prinzipien

DeSci-Scholar ist der Open-Science-Bewegung verpflichtet. Wir glauben an:

- **Transparenz**: Alle Aspekte des wissenschaftlichen Prozesses sollten offen und nachvollziehbar sein
- **Offenen Zugang**: Wissenschaftliche Erkenntnisse sollten frei zugänglich sein
- **Offene Daten**: Forschungsdaten sollten geteilt und wiederverwendbar sein
- **Offene Methoden**: Forschungsmethoden sollten transparent und reproduzierbar sein
- **Offene Infrastruktur**: Wissenschaftliche Infrastruktur sollte offen und gemeinschaftlich entwickelt werden

Bitte beachten Sie diese Prinzipien bei Ihren Beiträgen zum Projekt.

## Wie Sie beitragen können

Es gibt viele Möglichkeiten, zu DeSci-Scholar beizutragen:

1. **Code-Beiträge**: Implementierung neuer Funktionen oder Behebung von Fehlern
2. **Dokumentation**: Verbesserung oder Erweiterung der Dokumentation
3. **Tests**: Schreiben von Tests zur Verbesserung der Codequalität
4. **Fehlerberichte**: Melden von Fehlern oder Problemen
5. **Funktionsanfragen**: Vorschläge für neue Funktionen oder Verbesserungen
6. **Übersetzungen**: Übersetzung der Dokumentation oder der Benutzeroberfläche
7. **Community-Unterstützung**: Beantwortung von Fragen in Foren oder Diskussionen

## Entwicklungsprozess

### 1. Fork und Clone

1. Erstellen Sie einen Fork des Repositories auf GitHub
2. Klonen Sie Ihren Fork auf Ihren lokalen Computer:
   ```
   git clone https://github.com/IHR_BENUTZERNAME/desci-scholar.git
   cd desci-scholar
   ```
3. Fügen Sie das Upstream-Repository hinzu:
   ```
   git remote add upstream https://github.com/desci-labs/desci-scholar.git
   ```

### 2. Branches

1. Halten Sie Ihren `main`-Branch synchron mit dem Upstream:
   ```
   git checkout main
   git pull upstream main
   ```
2. Erstellen Sie einen neuen Branch für Ihre Änderungen:
   ```
   git checkout -b feature/ihre-feature-beschreibung
   ```

### 3. Entwicklung

1. Implementieren Sie Ihre Änderungen
2. Stellen Sie sicher, dass Ihr Code den Codierungsrichtlinien entspricht
3. Schreiben Sie Tests für Ihre Änderungen
4. Stellen Sie sicher, dass alle Tests bestehen:
   ```
   npm test
   ```

### 4. Commit und Push

1. Committen Sie Ihre Änderungen mit aussagekräftigen Commit-Nachrichten:
   ```
   git commit -m "Kurze Beschreibung der Änderungen"
   ```
2. Pushen Sie Ihre Änderungen zu Ihrem Fork:
   ```
   git push origin feature/ihre-feature-beschreibung
   ```

### 5. Pull Request

1. Erstellen Sie einen Pull Request von Ihrem Branch zu `desci-labs/desci-scholar:main`
2. Beschreiben Sie Ihre Änderungen ausführlich im Pull Request
3. Verlinken Sie relevante Issues
4. Warten Sie auf Feedback und reagieren Sie auf Kommentare

## Codierungsrichtlinien

- Folgen Sie dem bestehenden Codierungsstil
- Schreiben Sie klaren, lesbaren und gut dokumentierten Code
- Kommentieren Sie komplexe Logik
- Verwenden Sie aussagekräftige Variablen- und Funktionsnamen
- Halten Sie Funktionen kurz und fokussiert

## Commit-Nachrichten

Bitte verwenden Sie klare und aussagekräftige Commit-Nachrichten. Folgen Sie diesem Format:

```
Kurze Zusammenfassung (max. 50 Zeichen)

Ausführlichere Erklärung, wenn nötig. Umbruch nach etwa 72 Zeichen.
Erklären Sie das Problem, das dieser Commit löst. Konzentrieren Sie
sich darauf, warum die Änderung notwendig ist, nicht wie sie
implementiert wurde.

Wenn es relevante Issues gibt, erwähnen Sie diese am Ende:
Fixes #123
```

## Lizenz

Durch das Einreichen eines Beitrags erklären Sie sich damit einverstanden, dass Ihr Beitrag unter der [MIT-Lizenz](LICENSE) veröffentlicht wird, die für das Projekt gilt.

## Verhaltenskodex

Wir erwarten von allen Teilnehmern, dass sie unseren [Verhaltenskodex](CODE_OF_CONDUCT.md) einhalten. Bitte lesen Sie ihn, bevor Sie beitragen.

## Fragen?

Wenn Sie Fragen haben oder Hilfe benötigen, können Sie:

- Ein Issue auf GitHub erstellen
- Eine Diskussion im Discussions-Bereich starten
- Uns per E-Mail kontaktieren: [<EMAIL>](mailto:<EMAIL>)

Vielen Dank für Ihre Beiträge zu DeSci-Scholar!# Beitragsrichtlinien für DeSci-Scholar

Vielen Dank für Ihr Interesse, zu DeSci-Scholar beizutragen! Als Open-Source- und Open-Science-Projekt leben wir von der Beteiligung der Community. Diese Richtlinien sollen Ihnen helfen, effektiv zum Projekt beizutragen.

## Open-Science-Prinzipien

DeSci-Scholar ist der Open-Science-Bewegung verpflichtet. Wir glauben an:

- **Transparenz**: Alle Aspekte des wissenschaftlichen Prozesses sollten offen und nachvollziehbar sein
- **Offenen Zugang**: Wissenschaftliche Erkenntnisse sollten frei zugänglich sein
- **Offene Daten**: Forschungsdaten sollten geteilt und wiederverwendbar sein
- **Offene Methoden**: Forschungsmethoden sollten transparent und reproduzierbar sein
- **Offene Infrastruktur**: Wissenschaftliche Infrastruktur sollte offen und gemeinschaftlich entwickelt werden

Bitte beachten Sie diese Prinzipien bei Ihren Beiträgen zum Projekt.

## Wie Sie beitragen können

Es gibt viele Möglichkeiten, zu DeSci-Scholar beizutragen:

1. **Code-Beiträge**: Implementierung neuer Funktionen oder Behebung von Fehlern
2. **Dokumentation**: Verbesserung oder Erweiterung der Dokumentation
3. **Tests**: Schreiben von Tests zur Verbesserung der Codequalität
4. **Fehlerberichte**: Melden von Fehlern oder Problemen
5. **Funktionsanfragen**: Vorschläge für neue Funktionen oder Verbesserungen
6. **Übersetzungen**: Übersetzung der Dokumentation oder der Benutzeroberfläche
7. **Community-Unterstützung**: Beantwortung von Fragen in Foren oder Diskussionen

## Entwicklungsprozess

### 1. Fork und Clone

1. Erstellen Sie einen Fork des Repositories auf GitHub
2. Klonen Sie Ihren Fork auf Ihren lokalen Computer:
   ```
   git clone https://github.com/IHR_BENUTZERNAME/desci-scholar.git
   cd desci-scholar
   ```
3. Fügen Sie das Upstream-Repository hinzu:
   ```
   git remote add upstream https://github.com/desci-labs/desci-scholar.git
   ```

### 2. Branches

1. Halten Sie Ihren `main`-Branch synchron mit dem Upstream:
   ```
   git checkout main
   git pull upstream main
   ```
2. Erstellen Sie einen neuen Branch für Ihre Änderungen:
   ```
   git checkout -b feature/ihre-feature-beschreibung
   ```

### 3. Entwicklung

1. Implementieren Sie Ihre Änderungen
2. Stellen Sie sicher, dass Ihr Code den Codierungsrichtlinien entspricht
3. Schreiben Sie Tests für Ihre Änderungen
4. Stellen Sie sicher, dass alle Tests bestehen:
   ```
   npm test
   ```

### 4. Commit und Push

1. Committen Sie Ihre Änderungen mit aussagekräftigen Commit-Nachrichten:
   ```
   git commit -m "Kurze Beschreibung der Änderungen"
   ```
2. Pushen Sie Ihre Änderungen zu Ihrem Fork:
   ```
   git push origin feature/ihre-feature-beschreibung
   ```

### 5. Pull Request

1. Erstellen Sie einen Pull Request von Ihrem Branch zu `desci-labs/desci-scholar:main`
2. Beschreiben Sie Ihre Änderungen ausführlich im Pull Request
3. Verlinken Sie relevante Issues
4. Warten Sie auf Feedback und reagieren Sie auf Kommentare

## Codierungsrichtlinien

- Folgen Sie dem bestehenden Codierungsstil
- Schreiben Sie klaren, lesbaren und gut dokumentierten Code
- Kommentieren Sie komplexe Logik
- Verwenden Sie aussagekräftige Variablen- und Funktionsnamen
- Halten Sie Funktionen kurz und fokussiert

## Commit-Nachrichten

Bitte verwenden Sie klare und aussagekräftige Commit-Nachrichten. Folgen Sie diesem Format:

```
Kurze Zusammenfassung (max. 50 Zeichen)

Ausführlichere Erklärung, wenn nötig. Umbruch nach etwa 72 Zeichen.
Erklären Sie das Problem, das dieser Commit löst. Konzentrieren Sie
sich darauf, warum die Änderung notwendig ist, nicht wie sie
implementiert wurde.

Wenn es relevante Issues gibt, erwähnen Sie diese am Ende:
Fixes #123
```

## Lizenz

Durch das Einreichen eines Beitrags erklären Sie sich damit einverstanden, dass Ihr Beitrag unter der [MIT-Lizenz](LICENSE) veröffentlicht wird, die für das Projekt gilt.

## Verhaltenskodex

Wir erwarten von allen Teilnehmern, dass sie unseren [Verhaltenskodex](CODE_OF_CONDUCT.md) einhalten. Bitte lesen Sie ihn, bevor Sie beitragen.

## Fragen?

Wenn Sie Fragen haben oder Hilfe benötigen, können Sie:

- Ein Issue auf GitHub erstellen
- Eine Diskussion im Discussions-Bereich starten
- Uns per E-Mail kontaktieren: [<EMAIL>](mailto:<EMAIL>)

Vielen Dank für Ihre Beiträge zu DeSci-Scholar!# Beitragsrichtlinien für DeSci-Scholar

Vielen Dank für Ihr Interesse, zu DeSci-Scholar beizutragen! Als Open-Source- und Open-Science-Projekt leben wir von der Beteiligung der Community. Diese Richtlinien sollen Ihnen helfen, effektiv zum Projekt beizutragen.

## Open-Science-Prinzipien

DeSci-Scholar ist der Open-Science-Bewegung verpflichtet. Wir glauben an:

- **Transparenz**: Alle Aspekte des wissenschaftlichen Prozesses sollten offen und nachvollziehbar sein
- **Offenen Zugang**: Wissenschaftliche Erkenntnisse sollten frei zugänglich sein
- **Offene Daten**: Forschungsdaten sollten geteilt und wiederverwendbar sein
- **Offene Methoden**: Forschungsmethoden sollten transparent und reproduzierbar sein
- **Offene Infrastruktur**: Wissenschaftliche Infrastruktur sollte offen und gemeinschaftlich entwickelt werden

Bitte beachten Sie diese Prinzipien bei Ihren Beiträgen zum Projekt.

## Wie Sie beitragen können

Es gibt viele Möglichkeiten, zu DeSci-Scholar beizutragen:

1. **Code-Beiträge**: Implementierung neuer Funktionen oder Behebung von Fehlern
2. **Dokumentation**: Verbesserung oder Erweiterung der Dokumentation
3. **Tests**: Schreiben von Tests zur Verbesserung der Codequalität
4. **Fehlerberichte**: Melden von Fehlern oder Problemen
5. **Funktionsanfragen**: Vorschläge für neue Funktionen oder Verbesserungen
6. **Übersetzungen**: Übersetzung der Dokumentation oder der Benutzeroberfläche
7. **Community-Unterstützung**: Beantwortung von Fragen in Foren oder Diskussionen

## Entwicklungsprozess

### 1. Fork und Clone

1. Erstellen Sie einen Fork des Repositories auf GitHub
2. Klonen Sie Ihren Fork auf Ihren lokalen Computer:
   ```
   git clone https://github.com/IHR_BENUTZERNAME/desci-scholar.git
   cd desci-scholar
   ```
3. Fügen Sie das Upstream-Repository hinzu:
   ```
   git remote add upstream https://github.com/desci-labs/desci-scholar.git
   ```

### 2. Branches

1. Halten Sie Ihren `main`-Branch synchron mit dem Upstream:
   ```
   git checkout main
   git pull upstream main
   ```
2. Erstellen Sie einen neuen Branch für Ihre Änderungen:
   ```
   git checkout -b feature/ihre-feature-beschreibung
   ```

### 3. Entwicklung

1. Implementieren Sie Ihre Änderungen
2. Stellen Sie sicher, dass Ihr Code den Codierungsrichtlinien entspricht
3. Schreiben Sie Tests für Ihre Änderungen
4. Stellen Sie sicher, dass alle Tests bestehen:
   ```
   npm test
   ```

### 4. Commit und Push

1. Committen Sie Ihre Änderungen mit aussagekräftigen Commit-Nachrichten:
   ```
   git commit -m "Kurze Beschreibung der Änderungen"
   ```
2. Pushen Sie Ihre Änderungen zu Ihrem Fork:
   ```
   git push origin feature/ihre-feature-beschreibung
   ```

### 5. Pull Request

1. Erstellen Sie einen Pull Request von Ihrem Branch zu `desci-labs/desci-scholar:main`
2. Beschreiben Sie Ihre Änderungen ausführlich im Pull Request
3. Verlinken Sie relevante Issues
4. Warten Sie auf Feedback und reagieren Sie auf Kommentare

## Codierungsrichtlinien

- Folgen Sie dem bestehenden Codierungsstil
- Schreiben Sie klaren, lesbaren und gut dokumentierten Code
- Kommentieren Sie komplexe Logik
- Verwenden Sie aussagekräftige Variablen- und Funktionsnamen
- Halten Sie Funktionen kurz und fokussiert

## Commit-Nachrichten

Bitte verwenden Sie klare und aussagekräftige Commit-Nachrichten. Folgen Sie diesem Format:

```
Kurze Zusammenfassung (max. 50 Zeichen)

Ausführlichere Erklärung, wenn nötig. Umbruch nach etwa 72 Zeichen.
Erklären Sie das Problem, das dieser Commit löst. Konzentrieren Sie
sich darauf, warum die Änderung notwendig ist, nicht wie sie
implementiert wurde.

Wenn es relevante Issues gibt, erwähnen Sie diese am Ende:
Fixes #123
```

## Lizenz

Durch das Einreichen eines Beitrags erklären Sie sich damit einverstanden, dass Ihr Beitrag unter der [MIT-Lizenz](LICENSE) veröffentlicht wird, die für das Projekt gilt.

## Verhaltenskodex

Wir erwarten von allen Teilnehmern, dass sie unseren [Verhaltenskodex](CODE_OF_CONDUCT.md) einhalten. Bitte lesen Sie ihn, bevor Sie beitragen.

## Fragen?

Wenn Sie Fragen haben oder Hilfe benötigen, können Sie:

- Ein Issue auf GitHub erstellen
- Eine Diskussion im Discussions-Bereich starten
- Uns per E-Mail kontaktieren: [<EMAIL>](mailto:<EMAIL>)

Vielen Dank für Ihre Beiträge zu DeSci-Scholar!