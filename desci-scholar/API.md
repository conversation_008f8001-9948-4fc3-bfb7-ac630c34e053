# DeSci-Scholar API-Dokumentation

Diese Dokumentation beschreibt die REST-API-Endpunkte des DeSci-Scholar Projekts für die Verwaltung wissenschaftlicher Publikationen mit dezentralisierter Speicherung.

## Basis-URL

```
http://localhost:3000/api
```

## Authentifizierung

Die API verwendet JWT (JSON Web Tokens) für die Authentifizierung. Für geschützte Endpunkte muss ein gültiges Token im Authorization-Header übergeben werden:

```
Authorization: Bearer <token>
```

## Publikationen

### Publikationen abrufen

**Anfrage**
```
GET /publications
```

**Query-Parameter**
- `keywords` - Kommagetrennte List<PERSON> von Schlüsselwörtern
- `authors` - Kommagetrennte Liste von Autoren
- `title` - Titel oder Teil des Titels
- `doi` - DOI-Identifier
- `status` - Status der Publikation (draft, published)
- `limit` - An<PERSON><PERSON> der Ergebnisse (Standard: 20)
- `offset` - Offset für Paginierung (Standard: 0)

**Antwort (200 OK)**
```json
{
  "success": true,
  "publications": [
    {
      "id": "pub-123456",
      "title": "Anwendung von KI in der Medizin",
      "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
      "abstract": "Diese Studie untersucht...",
      "keywords": ["KI", "Medizin", "Maschinelles Lernen"],
      "doi": "10.12345/desci.123456",
      "status": "published",
      "publishedDate": "2023-04-15T14:22:10Z"
    },
    // Weitere Publikationen...
  ],
  "total": 42,
  "limit": 20,
  "offset": 0
}
```

### Einzelne Publikation abrufen

**Anfrage**
```
GET /publications/:id
```

**Query-Parameter**
- `content` - Volltext der Publikation einschließen (true/false)

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-123456",
    "title": "Anwendung von KI in der Medizin",
    "authors": ["Dr. Maria Schmidt", "Prof. Hans Müller"],
    "abstract": "Diese Studie untersucht...",
    "content": "Der vollständige Inhalt der Publikation...",
    "keywords": ["KI", "Medizin", "Maschinelles Lernen"],
    "doi": "10.12345/desci.123456",
    "license": "CC-BY-4.0",
    "status": "published",
    "publishedDate": "2023-04-15T14:22:10Z",
    "files": [
      {
        "id": "file-789012",
        "name": "forschungsdaten.pdf",
        "type": "application/pdf",
        "size": 2547852
      }
    ],
    "versions": [
      {
        "id": "pub-123456-v1",
        "createdAt": "2023-03-20T09:15:32Z"
      }
    ],
    "storageInfo": {
      "protocol": "BOTH",
      "ipfs": {
        "cid": "QmXa12S5sdf87gh4j56k7l...",
        "url": "https://ipfs.io/ipfs/QmXa12S5sdf87gh4j56k7l..."
      },
      "bittorrent": {
        "infoHash": "a1b2c3d4e5f6...",
        "magnetLink": "magnet:?xt=urn:btih:a1b2c3d4e5f6..."
      }
    }
  }
}
```

**Antwort (404 Not Found)**
```json
{
  "success": false,
  "message": "Publikation nicht gefunden"
}
```

### Neue Publikation erstellen

**Anfrage**
```
POST /publications
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "title": "Neue Entwicklungen in der Quantencomputer-Forschung",
  "authors": ["Dr. Thomas Weber", "Dr. Lisa Bauer"],
  "abstract": "Diese Arbeit präsentiert neue Ansätze...",
  "content": "Der vollständige Inhalt der Publikation...",
  "keywords": ["Quantencomputer", "Quantenphysik", "Quantenbits"],
  "license": "MIT"
}
```

**Antwort (201 Created)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-789012",
    "title": "Neue Entwicklungen in der Quantencomputer-Forschung",
    "status": "draft",
    "createdAt": "2023-05-10T08:12:45Z"
  },
  "message": "Publikation erfolgreich erstellt"
}
```

### Publikation aktualisieren

**Anfrage**
```
PUT /publications/:id
```

**Authentifizierung erforderlich**

**Query-Parameter**
- `newVersion` - Neue Version erstellen (true/false, Standard: false)

**Body**
```json
{
  "title": "Aktualisierter Titel",
  "abstract": "Aktualisierte Zusammenfassung...",
  "keywords": ["Neue", "Schlüsselwörter"]
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-123456",
    "title": "Aktualisierter Titel",
    "version": "Aktualisierte Version oder neue Versionsnummer, falls newVersion=true",
    "updatedAt": "2023-05-11T14:30:22Z"
  },
  "message": "Publikation erfolgreich aktualisiert"
}
```

### Publikation veröffentlichen

**Anfrage**
```
POST /publications/:id/publish
```

**Authentifizierung erforderlich**

**Body**
```json
{
  "registerDOI": true
}
```

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-123456",
    "title": "Anwendung von KI in der Medizin",
    "status": "published",
    "publishedDate": "2023-05-12T09:45:12Z",
    "doi": "10.12345/desci.123456"
  },
  "message": "Publikation erfolgreich veröffentlicht"
}
```

### Datei zu Publikation hinzufügen

**Anfrage**
```
POST /publications/:id/file
```

**Authentifizierung erforderlich**

**Content-Type**: `multipart/form-data`

**Form-Felder**
- `file` - Die hochzuladende Datei

**Antwort (200 OK)**
```json
{
  "success": true,
  "file": {
    "id": "file-789012",
    "name": "forschungsdaten.pdf",
    "type": "application/pdf",
    "size": 2547852,
    "storageInfo": {
      "protocol": "BOTH",
      "ipfs": {
        "cid": "QmXa12S5sdf87gh4j56k7l...",
        "url": "https://ipfs.io/ipfs/QmXa12S5sdf87gh4j56k7l..."
      },
      "bittorrent": {
        "infoHash": "a1b2c3d4e5f6...",
        "magnetLink": "magnet:?xt=urn:btih:a1b2c3d4e5f6..."
      }
    }
  },
  "message": "Datei erfolgreich hochgeladen"
}
```

### Datei einer Publikation herunterladen

**Anfrage**
```
GET /publications/:id/file
```

**Antwort**: Die Datei wird als Download gesendet mit entsprechenden Content-Type- und Content-Disposition-Headern.

### Zitation einer Publikation generieren

**Anfrage**
```
GET /publications/:id/cite
```

**Query-Parameter**
- `format` - Zitationsformat (apa, mla, chicago, harvard, bibtex, ris, endnote)

**Antwort (200 OK)**
```json
{
  "success": true,
  "publication": {
    "id": "pub-123456",
    "title": "Anwendung von KI in der Medizin"
  },
  "format": "bibtex",
  "citation": "@article{pub-123456,\n  author = {Dr. Maria Schmidt and Prof. Hans Müller},\n  title = {Anwendung von KI in der Medizin},\n  journal = {DeSci Scholar},\n  year = {2023},\n  doi = {10.12345/desci.123456}\n}"
}
```

## Fehlerbehandlung

Alle API-Endpunkte verwenden Standard-HTTP-Statuscodes und folgendes Antwortformat für Fehler:

```json
{
  "success": false,
  "message": "Beschreibung des Fehlers"
}
```

Typische Statuscodes:
- `400 Bad Request` - Ungültige Anfrage (z.B. fehlende oder ungültige Parameter)
- `401 Unauthorized` - Fehlende oder ungültige Authentifizierung
- `403 Forbidden` - Keine Berechtigung für diese Aktion
- `404 Not Found` - Ressource nicht gefunden
- `500 Internal Server Error` - Serverfehler

## Beispiele

### Curl-Beispiel: Publikationen abrufen

```bash
curl -X GET "http://localhost:3000/api/publications?keywords=KI,Medizin&limit=5"
```

### Curl-Beispiel: Neue Publikation erstellen

```bash
curl -X POST "http://localhost:3000/api/publications" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..." \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Neue wissenschaftliche Erkenntnisse",
    "authors": ["Dr. Max Mustermann"],
    "abstract": "Zusammenfassung der Forschung..."
  }'
```

### Curl-Beispiel: Datei hochladen

```bash
curl -X POST "http://localhost:3000/api/publications/pub-123456/file" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5..." \
  -F "file=@/pfad/zur/datei.pdf"
``` 