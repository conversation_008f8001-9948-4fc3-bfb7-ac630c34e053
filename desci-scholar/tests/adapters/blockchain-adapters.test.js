/**
 * @fileoverview Tests für die Blockchain-Adapter
 * 
 * Diese Tests überprüfen die Funktionalität der Blockchain-Adapter.
 */

import { expect } from 'chai';
import { initializeAdapterSystem } from '../../src/core/adapter/index.js';
import { blockchainAdapterConfig } from '../../src/config/adapters.js';

describe('Blockchain-Adapter-Tests', () => {
  let adapterSystem;
  let registry;
  
  before(async () => {
    // Initialisiere das Adapter-System
    adapterSystem = await initializeAdapterSystem({
      registry: {
        adapterPaths: [
          './src/services/blockchain'
        ],
        autoDiscovery: true,
        defaultAdapters: {
          blockchain: 'polkadot'
        }
      }
    });
    
    registry = adapterSystem.registry;
  });
  
  describe('Adapter-Registry', () => {
    it('sollte alle Blockchain-Adapter registrieren', () => {
      const adapters = registry.getAllAdapters();
      expect(adapters).to.have.property('blockchain');
      expect(adapters.blockchain).to.be.an('array');
      expect(adapters.blockchain.length).to.be.at.least(1);
    });
    
    it('sollte Polkadot als Standard-Blockchain-Adapter haben', () => {
      const defaultAdapter = registry.getDefaultAdapter('blockchain');
      expect(defaultAdapter).to.equal('polkadot');
    });
  });
  
  describe('Polkadot-Adapter', () => {
    let polkadotAdapter;
    
    before(async () => {
      // Hole den Polkadot-Adapter
      polkadotAdapter = registry.getAdapter('blockchain', 'polkadot', {
        provider: 'wss://rpc.polkadot.io',
        network: 'polkadot'
      });
    });
    
    it('sollte eine Instanz des Polkadot-Adapters zurückgeben', () => {
      expect(polkadotAdapter).to.exist;
      expect(polkadotAdapter.constructor.name).to.equal('PolkadotAdapter');
    });
    
    it('sollte die richtigen Capabilities haben', () => {
      const capabilities = polkadotAdapter.getCapabilities();
      expect(capabilities).to.be.an('array');
      expect(capabilities).to.include('sendTransaction');
    });
    
    it('sollte initialisiert werden können', async () => {
      // Mock für die Initialisierung, um keine echte Verbindung herzustellen
      polkadotAdapter.connect = async () => true;
      
      const result = await polkadotAdapter.initialize();
      expect(result).to.be.true;
      expect(polkadotAdapter.isInitialized()).to.be.true;
    });
  });
  
  describe('Ethereum-Adapter', () => {
    let ethereumAdapter;
    
    before(async () => {
      // Hole den Ethereum-Adapter
      ethereumAdapter = registry.getAdapter('blockchain', 'ethereum', {
        provider: 'https://mainnet.infura.io/v3/your-api-key',
        network: 'mainnet'
      });
    });
    
    it('sollte eine Instanz des Ethereum-Adapters zurückgeben', () => {
      expect(ethereumAdapter).to.exist;
      expect(ethereumAdapter.constructor.name).to.equal('EthereumAdapter');
    });
    
    it('sollte die richtigen Capabilities haben', () => {
      const capabilities = ethereumAdapter.getCapabilities();
      expect(capabilities).to.be.an('array');
      expect(capabilities).to.include('mintNFT');
    });
    
    it('sollte initialisiert werden können', async () => {
      // Mock für die Initialisierung, um keine echte Verbindung herzustellen
      ethereumAdapter.connect = async () => true;
      
      const result = await ethereumAdapter.initialize();
      expect(result).to.be.true;
      expect(ethereumAdapter.isInitialized()).to.be.true;
    });
  });
  
  describe('Polygon-Adapter', () => {
    let polygonAdapter;
    
    before(async () => {
      // Hole den Polygon-Adapter
      polygonAdapter = registry.getAdapter('blockchain', 'polygon', {
        provider: 'https://polygon-rpc.com',
        network: 'mainnet'
      });
    });
    
    it('sollte eine Instanz des Polygon-Adapters zurückgeben', () => {
      expect(polygonAdapter).to.exist;
      expect(polygonAdapter.constructor.name).to.equal('PolygonAdapter');
    });
    
    it('sollte die richtigen Capabilities haben', () => {
      const capabilities = polygonAdapter.getCapabilities();
      expect(capabilities).to.be.an('array');
      expect(capabilities).to.include('mintNFT');
    });
    
    it('sollte initialisiert werden können', async () => {
      // Mock für die Initialisierung, um keine echte Verbindung herzustellen
      polygonAdapter.connect = async () => true;
      
      const result = await polygonAdapter.initialize();
      expect(result).to.be.true;
      expect(polygonAdapter.isInitialized()).to.be.true;
    });
  });
  
  describe('Optimism-Adapter', () => {
    let optimismAdapter;
    
    before(async () => {
      // Hole den Optimism-Adapter
      optimismAdapter = registry.getAdapter('blockchain', 'optimism', {
        provider: 'https://mainnet.optimism.io',
        network: 'mainnet'
      });
    });
    
    it('sollte eine Instanz des Optimism-Adapters zurückgeben', () => {
      expect(optimismAdapter).to.exist;
      expect(optimismAdapter.constructor.name).to.equal('OptimismAdapter');
    });
    
    it('sollte die richtigen Capabilities haben', () => {
      const capabilities = optimismAdapter.getCapabilities();
      expect(capabilities).to.be.an('array');
      expect(capabilities).to.include('mintNFT');
    });
    
    it('sollte initialisiert werden können', async () => {
      // Mock für die Initialisierung, um keine echte Verbindung herzustellen
      optimismAdapter.connect = async () => true;
      
      const result = await optimismAdapter.initialize();
      expect(result).to.be.true;
      expect(optimismAdapter.isInitialized()).to.be.true;
    });
  });
  
  describe('Solana-Adapter', () => {
    let solanaAdapter;
    
    before(async () => {
      // Hole den Solana-Adapter
      solanaAdapter = registry.getAdapter('blockchain', 'solana', {
        endpoint: 'https://api.mainnet-beta.solana.com',
        network: 'mainnet-beta'
      });
    });
    
    it('sollte eine Instanz des Solana-Adapters zurückgeben', () => {
      expect(solanaAdapter).to.exist;
      expect(solanaAdapter.constructor.name).to.equal('SolanaAdapter');
    });
    
    it('sollte die richtigen Capabilities haben', () => {
      const capabilities = solanaAdapter.getCapabilities();
      expect(capabilities).to.be.an('array');
      expect(capabilities).to.include('mintNFT');
    });
    
    it('sollte initialisiert werden können', async () => {
      // Mock für die Initialisierung, um keine echte Verbindung herzustellen
      solanaAdapter.connect = async () => true;
      
      const result = await solanaAdapter.initialize();
      expect(result).to.be.true;
      expect(solanaAdapter.isInitialized()).to.be.true;
    });
  });
});
