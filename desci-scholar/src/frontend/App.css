/**
 * App.css
 * 
 * Styling für die Hauptanwendung und Layout-Komponenten
 */

/* CSS-Variablen für das gesamte Projekt */
:root {
  --primary: #007bff;
  --primary-dark: #0069d9;
  --primary-light: rgba(0, 123, 255, 0.1);
  --secondary: #6c757d;
  --success: #28a745;
  --info: #17a2b8;
  --warning: #ffc107;
  --danger: #dc3545;
  --light: #f8f9fa;
  --dark: #343a40;
  
  --text-primary: #212529;
  --text-secondary: #6c757d;
  --text-muted: #adb5bd;
  
  --bg-light: #f8f9fa;
  --bg-dark: #343a40;
  --card-bg: #ffffff;
  
  --border-color: #dee2e6;
  --border-radius: 0.25rem;
  
  --font-family-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
  --font-family-mono: SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
  
  --icon-bg: rgba(0, 123, 255, 0.1);
  --activity-bar-bg: #e9ecef;
  --badge-bg: #f8f9fa;
  
  --primary-color: #007bff;
  --primary-color-dark: #0069d9;
  --primary-color-light: rgba(0, 123, 255, 0.1);
  --secondary-color: #6c757d;
  --secondary-color-light: #e9ecef;
  --secondary-color-dark: #495057;
  
  --text-color-primary: #212529;
  --text-color-secondary: #6c757d;
  --text-color-tertiary: #adb5bd;
  
  --bg-color: #f8f9fa;
  --card-bg-color: #ffffff;
  
  --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  --card-radius: 0.5rem;
  
  --error-color: #dc3545;
  --success-color: #28a745;
  --warning-color: #ffc107;
  --info-color: #17a2b8;
}

/* Allgemeine Styling-Regeln */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-family-sans);
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--bg-light);
}

a {
  color: var(--primary);
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

ul {
  list-style-type: none;
}

/* App Container und Layout */
.app {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* Header Styling */
.app-header {
  background-color: white;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  padding: 1rem 0;
}

.app-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.app-logo h1 {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary);
  margin: 0;
}

.app-nav ul {
  display: flex;
  gap: 1.5rem;
}

.app-nav-link {
  color: var(--text-secondary);
  font-weight: 500;
  padding: 0.5rem 0;
  position: relative;
}

.app-nav-link:hover {
  color: var(--primary);
  text-decoration: none;
}

.app-nav-link.active {
  color: var(--primary);
}

.app-nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: var(--primary);
}

.app-login-button {
  background-color: var(--primary);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.app-login-button:hover {
  background-color: var(--primary-dark);
}

/* Main Content */
.app-main {
  flex: 1;
  background-color: var(--bg-light);
}

/* Footer Styling */
.app-footer {
  background-color: var(--bg-dark);
  color: white;
  padding: 3rem 0 0;
  margin-top: 2rem;
}

.app-footer-content {
  display: flex;
  flex-wrap: wrap;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  gap: 2rem;
  margin-bottom: 2rem;
}

.app-footer-info {
  flex: 1;
  min-width: 250px;
}

.app-footer-info h3 {
  font-size: 1.25rem;
  margin-bottom: 1rem;
  color: white;
}

.app-footer-info p {
  color: rgba(255, 255, 255, 0.7);
  max-width: 300px;
}

.app-footer-links {
  flex: 2;
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
}

.app-footer-section {
  flex: 1;
  min-width: 150px;
}

.app-footer-section h4 {
  font-size: 1rem;
  margin-bottom: 1rem;
  color: white;
}

.app-footer-section ul li {
  margin-bottom: 0.5rem;
}

.app-footer-section a {
  color: rgba(255, 255, 255, 0.7);
  transition: color 0.2s;
}

.app-footer-section a:hover {
  color: white;
  text-decoration: none;
}

.app-footer-copyright {
  background-color: rgba(0, 0, 0, 0.2);
  padding: 1rem 0;
  text-align: center;
}

.app-footer-copyright p {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.875rem;
}

/* Media Queries */
@media (max-width: 768px) {
  .app-header-content {
    flex-direction: column;
    gap: 1rem;
    padding: 0.5rem 1rem;
  }
  
  .app-nav ul {
    gap: 1rem;
  }
  
  .app-footer-content {
    flex-direction: column;
    gap: 2rem;
  }
  
  .app-footer-info,
  .app-footer-links {
    width: 100%;
  }
}

@media (max-width: 576px) {
  .app-nav ul {
    flex-wrap: wrap;
    justify-content: center;
  }
}

/* Hauptinhalt - Text-Buttons und Platzhalterinhalt */
.text-button {
  background: none;
  border: none;
  color: var(--primary);
  font-weight: 500;
  cursor: pointer;
  padding: 0;
  display: inline;
  text-decoration: underline;
  font-size: inherit;
}

.text-button:hover {
  color: var(--primary-dark);
}

.placeholder-content {
  max-width: 800px;
  margin: 3rem auto;
  text-align: center;
  padding: 2rem;
  background-color: var(--card-bg-color);
  border-radius: var(--card-radius);
  box-shadow: var(--card-shadow);
}

.placeholder-content h2 {
  margin-bottom: 1rem;
  color: var(--text-color-primary);
}

.placeholder-content p {
  margin-bottom: 1rem;
  color: var(--text-color-secondary);
} 