/**
 * stats-client.js
 * 
 * Funktionen zum Abrufen von Statistikdaten von den API-Endpunkten
 */

import axios from 'axios';
import { API_BASE_URL } from '../config';

// Axios-Instance mit Basiskonfiguration
const apiClient = axios.create({
  baseURL: `${API_BASE_URL}/stats`,
  headers: {
    'Content-Type': 'application/json'
  }
});

/**
 * Fügt das Authentifizierungstoken zu Anfragen hinzu, falls vorhanden
 */
const addAuthToken = () => {
  const token = localStorage.getItem('auth_token');
  if (token) {
    return {
      headers: {
        Authorization: `Bearer ${token}`
      }
    };
  }
  return {};
};

/**
 * Ruft allgemeine Plattformstatistiken ab
 * @returns {Promise<Object>} Gesamtstatistiken
 */
export const fetchTotalStats = async () => {
  try {
    const response = await apiClient.get('/publications', addAuthToken());
    return response.data.data;
  } catch (error) {
    console.error('<PERSON><PERSON> beim Abrufen der Gesamtstatistiken:', error);
    throw error;
  }
};

/**
 * Ruft Speicherstatistiken ab
 * @returns {Promise<Object>} Speicherstatistiken
 */
export const fetchStorageStats = async () => {
  try {
    const response = await apiClient.get('/storage', addAuthToken());
    return response.data.data;
  } catch (error) {
    console.error('Fehler beim Abrufen der Speicherstatistiken:', error);
    throw error;
  }
};

/**
 * Ruft Benutzerstatistiken ab (erfordert Admin-Rechte)
 * @returns {Promise<Object>} Benutzerstatistiken
 */
export const fetchUserStats = async () => {
  try {
    const response = await apiClient.get('/users', addAuthToken());
    return response.data.data;
  } catch (error) {
    console.error('Fehler beim Abrufen der Benutzerstatistiken:', error);
    throw error;
  }
};

/**
 * Ruft Download-Statistiken für einen bestimmten Zeitraum ab
 * @param {Object} options Optionen für die Anfrage
 * @param {string} options.startDate Startdatum (ISO-Format)
 * @param {string} options.endDate Enddatum (ISO-Format)
 * @param {string} options.groupBy Gruppierung (day, week, month)
 * @returns {Promise<Array>} Download-Statistiken
 */
export const fetchDownloadStats = async ({ startDate, endDate, groupBy = 'day' } = {}) => {
  try {
    const params = {};
    if (startDate) params.start = startDate;
    if (endDate) params.end = endDate;
    if (groupBy) params.groupBy = groupBy;
    
    const response = await apiClient.get('/downloads', { 
      ...addAuthToken(),
      params
    });
    
    return response.data.data;
  } catch (error) {
    console.error('Fehler beim Abrufen der Download-Statistiken:', error);
    throw error;
  }
};

/**
 * Ruft Trend-Publikationen basierend auf Aktivität ab
 * @param {Object} options Optionen für die Anfrage
 * @param {number} options.period Zeitraum in Tagen
 * @param {number} options.limit Maximale Anzahl von Ergebnissen
 * @returns {Promise<Array>} Trend-Publikationen
 */
export const fetchTrendingPublications = async ({ period, limit = 10 } = {}) => {
  try {
    const params = {};
    if (period) params.period = period;
    if (limit) params.limit = limit;
    
    const response = await apiClient.get('/trending', { 
      ...addAuthToken(),
      params
    });
    
    return response.data.data;
  } catch (error) {
    console.error('Fehler beim Abrufen der Trend-Publikationen:', error);
    throw error;
  }
};

/**
 * Ruft detaillierte Statistiken für eine Publikation ab
 * @param {string} publicationId ID der Publikation
 * @returns {Promise<Object>} Detaillierte Publikationsstatistiken
 */
export const fetchPublicationStats = async (publicationId) => {
  if (!publicationId) {
    throw new Error('Publikations-ID ist erforderlich');
  }
  
  try {
    const response = await apiClient.get(`/publication/${publicationId}`, addAuthToken());
    return response.data.data;
  } catch (error) {
    console.error(`Fehler beim Abrufen der Statistiken für Publikation ${publicationId}:`, error);
    throw error;
  }
};

/**
 * Zeichnet einen Zugriff auf eine Publikation auf
 * @param {Object} accessData Zugriffsdaten
 * @param {string} accessData.publicationId ID der Publikation
 * @param {string} accessData.type Zugriffstyp (view, download, citation)
 * @param {Object} accessData.metadata Weitere Metadaten
 * @returns {Promise<Object>} Antwort der API
 */
export const recordAccess = async ({ publicationId, type, metadata = {} }) => {
  if (!publicationId || !type) {
    throw new Error('Publikations-ID und Zugriffstyp sind erforderlich');
  }
  
  // Validiere den Zugriffstyp
  const validTypes = ['view', 'download', 'citation'];
  if (!validTypes.includes(type)) {
    throw new Error(`Ungültiger Zugriffstyp. Erlaubte Werte: ${validTypes.join(', ')}`);
  }
  
  try {
    const response = await apiClient.post('/record', {
      publicationId,
      type,
      metadata
    }, addAuthToken());
    
    return response.data;
  } catch (error) {
    console.error('Fehler beim Aufzeichnen des Zugriffs:', error);
    throw error;
  }
};

/**
 * Ruft eigene Publikationsstatistiken ab (erfordert Authentifizierung)
 * @returns {Promise<Object>} Eigene Publikationsstatistiken
 */
export const fetchUserPublicationStats = async () => {
  try {
    const response = await apiClient.get('/user/publications', addAuthToken());
    return response.data.data;
  } catch (error) {
    console.error('Fehler beim Abrufen der Benutzer-Publikationsstatistiken:', error);
    throw error;
  }
};

/**
 * Ruft geografische Verteilungsdaten für einen Zeitraum ab
 * @param {Object} options Optionen für die Anfrage 
 * @param {string} options.startDate Startdatum (ISO-Format)
 * @param {string} options.endDate Enddatum (ISO-Format)
 * @param {number} options.limit Maximale Anzahl von Ländern
 * @returns {Promise<Array>} Geografische Verteilungsdaten
 */
export const fetchGeographicDistribution = async ({ startDate, endDate, limit = 10 } = {}) => {
  try {
    const params = { limit };
    if (startDate) params.start = startDate;
    if (endDate) params.end = endDate;
    
    const response = await apiClient.get('/geographic', { 
      ...addAuthToken(),
      params 
    });
    
    return response.data.data;
  } catch (error) {
    console.error('Fehler beim Abrufen der geografischen Verteilung:', error);
    throw error;
  }
}; 