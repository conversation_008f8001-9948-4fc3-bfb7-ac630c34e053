/**
 * config.js
 * 
 * Konfigurationseinstellungen für das Frontend
 */

// API-Basis-URL mit Fallback
export const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000/api';

// Frontend-Konfiguration
export const CONFIG = {
  // Allgemeine Einstellungen
  appName: 'DeSci-Scholar',
  
  // Authentifizierung
  auth: {
    tokenStorageKey: 'auth_token',
    refreshTokenStorageKey: 'refresh_token',
    tokenExpiryKey: 'token_expiry'
  },
  
  // Dateiupload-Einstellungen
  upload: {
    maxFileSize: process.env.REACT_APP_MAX_FILE_SIZE || 100 * 1024 * 1024, // 100 MB
    allowedFileTypes: [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
      'text/csv',
      'application/zip',
      'application/x-zip-compressed'
    ]
  },
  
  // Statistik-Einstellungen
  stats: {
    defaultTimeRange: 'month',
    refreshInterval: 300000, // 5 Minuten
    chartColors: [
      '#4285F4', // Blau
      '#34A853', // Grün
      '#FBBC05', // Gelb
      '#EA4335', // Rot
      '#8AB4F8', // Hellblau
      '#137333', // Dunkelgrün
      '#F6C142', // Dunkelgelb
      '#C5221F'  // Dunkelrot
    ]
  },
  
  // Feature-Flags
  features: {
    enableRealTimeStats: process.env.REACT_APP_ENABLE_REALTIME_STATS === 'true',
    enableAdvancedCharts: process.env.REACT_APP_ENABLE_ADVANCED_CHARTS === 'true',
    enableGeolocation: true,
    enableStorageInsights: true
  }
}; 