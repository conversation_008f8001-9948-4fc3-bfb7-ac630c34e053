.container {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  padding: 24px;
  width: 100%;
  max-width: 480px;
  margin: 0 auto;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-top: 0;
  margin-bottom: 24px;
  color: #1a1a1a;
  text-align: center;
}

.walletOptions {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.noWallet {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
}

.noWallet p {
  margin-top: 0;
  margin-bottom: 16px;
  color: #333;
}

.walletLinks {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.walletLink {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  color: #333;
  text-decoration: none;
  transition: all 0.2s ease;
}

.walletLink:hover {
  border-color: #e6007a;
  background-color: rgba(230, 0, 122, 0.05);
  transform: translateY(-2px);
}

.walletIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
}

.walletLink:hover .walletIcon {
  color: #e6007a;
}

.connectOptions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.connectButton {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 14px 20px;
  background-color: #fff;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  color: #333;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.connectButton:hover:not(:disabled) {
  border-color: #e6007a;
  background-color: rgba(230, 0, 122, 0.05);
}

.connectButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  border-color: #e0e0e0;
}

.buttonIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
}

.connectButton:hover:not(:disabled) .buttonIcon {
  color: #e6007a;
}

.error {
  background-color: #ffe6e6;
  color: #d03030;
  padding: 12px;
  border-radius: 6px;
  font-size: 0.9rem;
  text-align: center;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 16px;
}

.spinner {
  width: 30px;
  height: 30px;
  border: 3px solid rgba(230, 0, 122, 0.3);
  border-radius: 50%;
  border-top-color: #e6007a;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.connectedWallet {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.accountInfo {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 20px;
}

.accountHeader {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.walletBadge {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: rgba(230, 0, 122, 0.1);
  border-radius: 20px;
  padding: 4px 12px;
  color: #e6007a;
  font-size: 0.8rem;
  font-weight: 500;
  width: fit-content;
}

.accountName {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
}

.addressContainer {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 8px 12px;
}

.address {
  font-family: monospace;
  font-size: 0.9rem;
  color: #555;
}

.copyButton {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.copyButton:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: #333;
}

.disconnectButton {
  padding: 12px;
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  color: #666;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.disconnectButton:hover {
  background-color: #ebebeb;
  color: #333;
}

@media (max-width: 768px) {
  .container {
    padding: 20px;
    max-width: 100%;
  }
}
