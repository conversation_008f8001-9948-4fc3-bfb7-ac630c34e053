.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.header {
  text-align: center;
  margin-bottom: 3rem;
  position: relative;
}

.title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  background: linear-gradient(90deg, #e6007a 0%, #6e45e3 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subtitle {
  font-size: 1.1rem;
  color: #555;
  max-width: 700px;
  margin: 0 auto;
}

.main {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 3rem;
}

.serviceSelector {
  background-color: #fff;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.serviceSelector h2 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  color: #333;
}

.radioGroup {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.radioOption {
  display: flex;
  align-items: flex-start;
  padding: 1rem;
  border: 2px solid #eee;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.radioOption:hover {
  border-color: #6e45e3;
}

.radioOption input {
  margin-top: 0.25rem;
  margin-right: 1rem;
  accent-color: #6e45e3;
}

.radioContent {
  flex: 1;
  display: flex;
  justify-content: space-between;
}

.serviceName {
  display: flex;
  flex-direction: column;
  font-weight: 600;
  color: #333;
}

.servicePeriod {
  font-size: 0.8rem;
  font-weight: normal;
  color: #777;
  margin-top: 0.25rem;
}

.servicePrice {
  font-size: 1.25rem;
  font-weight: 700;
  color: #6e45e3;
}

.infoSection {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  margin-bottom: 3rem;
}

.infoItem {
  background-color: #f9f7ff;
  padding: 1.5rem;
  border-radius: 12px;
}

.infoItem h3 {
  margin-top: 0;
  color: #6e45e3;
  font-size: 1.25rem;
  margin-bottom: 1rem;
}

.infoItem p {
  color: #555;
  line-height: 1.5;
  margin: 0;
}

.footer {
  text-align: center;
  padding-top: 2rem;
  border-top: 1px solid #eee;
  color: #777;
  font-size: 0.9rem;
}

/* Wallet Badge Styles */
.walletBadge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  background-color: #f9f7ff;
  border: 1px solid #e0e0e0;
  border-radius: 20px;
  font-size: 0.85rem;
  color: #555;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.walletIcon {
  display: flex;
  align-items: center;
  margin-right: 0.5rem;
  color: #6e45e3;
}

.walletSource {
  font-weight: 600;
  margin-right: 0.5rem;
}

.walletAddress {
  font-family: monospace;
  background-color: rgba(110, 69, 227, 0.1);
  padding: 0.1rem 0.4rem;
  border-radius: 4px;
  color: #6e45e3;
}

.paymentSection {
  /* Styling applied from PaymentOptions component */
}

@media (max-width: 768px) {
  .main {
    grid-template-columns: 1fr;
  }
  
  .infoSection {
    grid-template-columns: 1fr;
  }
  
  .walletBadge {
    position: relative;
    top: auto;
    right: auto;
    margin: 1rem auto 0;
    display: inline-flex;
  }
}
