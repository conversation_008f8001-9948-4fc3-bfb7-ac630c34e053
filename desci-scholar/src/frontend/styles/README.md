# DeSci-Scholar: Puristischer Minimalismus

Dieses Design-System definiert ein streng puristisches Design für die DeSci-Scholar-Plattform, das durch eine minimalistische Schwarz-Weiß-Palette mit gezielten Grautönen für Hierarchie und Lesbarkeit gekennzeichnet ist.

## Designprinzipien

1. **Puristische Reduktion**: Beschränkung auf Schwarz, Weiß und wenige gezielte Grauabstufungen
2. **Typografie als Struktur**: Die Typografie als primäres strukturierendes Element
3. **Minimale Linien**: Dezente horizontale und vertikale Linien als Trenner
4. **Großzügiger Weißraum**: Bewusster Einsatz von Leerraum für visuelle Klarheit
5. **Klare Hierarchie**: Nuancierter Einsatz von Grautönen für visuelle Abstufungen

## Farbpalette

- **<PERSON><PERSON><PERSON> (#000000)**: <PERSON><PERSON><PERSON>, Überschriften und interaktive Elemente
- **Weiß (#FFFFFF)**: <PERSON><PERSON><PERSON>gründe
- **Dunkelgrau (#222222)**: F<PERSON>r F<PERSON>ß<PERSON> in längeren Abschnitten
- **Mittelgrau (#444444, #666666)**: Für Sekundärtexte und Hinweise
- **Hellgrau (#dddddd)**: Für subtile Trennlinien und Strukturelemente

## Typografische Hierarchie

- **Überschriften**: Großbuchstaben mit Letter-Spacing, schwarz auf weiß
- **Text**: Hierarchische Abstufungen durch Schriftfarben
- **Hervorhebungen**: Durch Schriftstärke, Größe und gezielte Farbabstufungen

## Komponenten

### Buttons
- Keine visuellen Hintergründe
- Schwarzer Text mit minimaler Unterstreichung
- Textumwandlung in Großbuchstaben
- Hover-Effekt durch Transparenz

### Container und Karten
- Keine Schatten oder visuelle Container
- Dezente horizontale Linien in Hellgrau als Trenner
- Inhalte primär durch Weißraum strukturiert

### Badges und Tags
- Keine Hintergründe oder Umrandungen
- Vertikale Striche als visuelle Markierungen
- Text in Schwarz für optimale Lesbarkeit

### Navigation
- Typografie in Großbuchstaben
- Hervorhebung aktiver Elemente durch Schriftstärke

## Implementation

Diese puristische Ästhetik wurde durch folgende Maßnahmen erreicht:

1. **Minimale Farbpalette**:
   - Dominanz von Schwarz und Weiß
   - Gezielte Grautöne nur für visuelle Hierarchie
   - Keine Farbverläufe oder Transparenzen

2. **Vereinfachung der visuellen Sprache**:
   - Horizontale und vertikale Linien in Hellgrau
   - Typografische Hierarchie durch Schriftstärken und dezente Farbabstufungen
   - Struktur primär durch Weißraum und Abstände

3. **Optimierte Lesbarkeit**:
   - Starke Kontraste für primäre Informationen
   - Dezente Grauabstufungen für Sekundärinformationen
   - Großzügige Abstände und klare Hierarchie

## Angepasste Komponenten

- **LiteratureExportButton**: Puristische Gestaltung mit minimalen Grautönen für Strukturierung
- **PublicationDetailPage**: Klare visuelle Hierarchie mit gezielten Farbabstufungen
- **Allgemeine UI-Elemente**: Durch das `black-white-theme.css` auf das Wesentliche reduziert

## Inspiration

Dieses puristische Design orientiert sich an klassischen typografischen Prinzipien und dem Konzept der gezielten Reduktion. Es setzt bewusst auf wenige, aber wirkungsvolle visuelle Mittel, um eine optimale Lesbarkeit und klare Informationshierarchie zu schaffen, ohne visuelle Ablenkungen zu erzeugen.

```javascript
import './App.css';
import './styles/black-white-theme.css';
```

## Anpassungen für bestehende Komponenten

Bestehende Komponenten wurden angepasst, um dem neuen Design-System zu entsprechen, insbesondere:

- LiteratureExportButton
- PublicationDetailPage
- StatsDashboard

## Prinzipien

1. **Klarheit**: Klare visuelle Hierarchie und Fokus auf den Inhalt
2. **Minimalismus**: Reduzierte visuelle Komplexität
3. **Konsistenz**: Einheitliche Anwendung des Designs in der gesamten Anwendung
4. **Lesbarkeit**: Optimierte Typografie für wissenschaftliche Inhalte

## Inspiration

Das Design ist inspiriert von der OpenAlex-Plattform (https://openalex.org), die ein ähnliches Schwarz-Weiß-Design verwendet, um wissenschaftliche Informationen übersichtlich darzustellen. 