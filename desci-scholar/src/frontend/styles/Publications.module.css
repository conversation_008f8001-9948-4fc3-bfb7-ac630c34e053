.container {
  padding: 0 1rem;
  max-width: 1200px;
  margin: 0 auto;
}

.main {
  min-height: 100vh;
  padding: 2rem 0;
}

.title {
  font-size: 2.5rem;
  margin-bottom: 2rem;
  color: #333;
  text-align: center;
}

.searchContainer {
  margin-bottom: 2rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.searchInput {
  width: 100%;
  padding: 1rem;
  font-size: 1rem;
  border: 1px solid #ddd;
  border-radius: 0.5rem;
  outline: none;
  transition: border-color 0.3s ease;
}

.searchInput:focus {
  border-color: #0070f3;
}

.results {
  text-align: center;
  color: #666;
  margin-bottom: 2rem;
}

.publicationGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
}

.publicationCard {
  padding: 1.5rem;
  border: 1px solid #eaeaea;
  border-radius: 0.5rem;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  background-color: white;
}

.publicationCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.publicationLink {
  text-decoration: none;
  color: inherit;
}

.publicationTitle {
  font-size: 1.25rem;
  margin-bottom: 0.75rem;
  color: #0070f3;
}

.authors {
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
  color: #333;
}

.journalInfo {
  font-size: 0.85rem;
  margin-bottom: 0.5rem;
  color: #666;
}

.journal {
  font-style: italic;
}

.date {
  margin-left: 0.5rem;
}

.doi {
  font-size: 0.85rem;
  margin-bottom: 1rem;
  color: #666;
}

.doi a {
  color: #0070f3;
  text-decoration: none;
}

.doi a:hover {
  text-decoration: underline;
}

.abstract {
  font-size: 0.9rem;
  margin-bottom: 1.5rem;
  color: #666;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.viewButton {
  display: inline-block;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  color: #0070f3;
  border: 1px solid #0070f3;
  border-radius: 0.25rem;
  text-decoration: none;
  transition: background-color 0.2s ease, color 0.2s ease;
}

.viewButton:hover {
  background-color: #0070f3;
  color: white;
}

.loading {
  text-align: center;
  padding: 4rem 0;
  color: #666;
  font-size: 1.2rem;
}
