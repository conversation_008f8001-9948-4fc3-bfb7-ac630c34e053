.paymentContainer {
  max-width: 500px;
  margin: 0 auto;
  padding: 1.5rem;
  border-radius: 12px;
  background-color: #fff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.title {
  text-align: center;
  margin-bottom: 1.5rem;
  color: #1e1e1e;
  font-size: 1.5rem;
}

.amountDisplay {
  text-align: center;
  margin-bottom: 1rem;
}

.amount {
  font-size: 2rem;
  font-weight: 700;
  color: #6e45e3;
}

.currency {
  font-size: 1.2rem;
  margin-left: 0.3rem;
  color: #666;
}

.description {
  text-align: center;
  margin-bottom: 2rem;
  color: #555;
}

.methods {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  justify-content: center;
}

.method {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  border-radius: 8px;
  border: 2px solid #e0e0e0;
  cursor: pointer;
  transition: all 0.2s ease;
}

.method:hover {
  border-color: #6e45e3;
}

.active {
  border-color: #6e45e3;
  background-color: rgba(110, 69, 227, 0.05);
}

.methodIcon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 48px;
  height: 48px;
  border-radius: 8px;
  background-color: rgba(110, 69, 227, 0.1);
  color: #6e45e3;
}

.paymentButton {
  width: 100%;
  padding: 1rem;
  border: none;
  border-radius: 8px;
  background-color: #6e45e3;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.paymentButton:hover {
  background-color: #5a36c9;
}

.paymentButton:disabled {
  background-color: #a893e9;
  cursor: not-allowed;
}

.error {
  margin: 1rem 0;
  padding: 0.75rem;
  border-radius: 8px;
  background-color: #ffeded;
  color: #d32f2f;
  border: 1px solid #f5c2c2;
}

.cryptoPayment {
  margin-bottom: 1.5rem;
  padding: 1rem;
  border-radius: 8px;
  background-color: #f9f7ff;
}

.conversionRate {
  text-align: center;
  margin-bottom: 1rem;
  font-size: 0.9rem;
  color: #666;
}

.generateButton {
  width: 100%;
  padding: 0.75rem;
  border: none;
  border-radius: 8px;
  background-color: #6e45e3;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.generateButton:hover {
  background-color: #5a36c9;
}

.generateButton:disabled {
  background-color: #a893e9;
  cursor: not-allowed;
}

.qrCodeContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 1rem 0;
}

.qrCode {
  width: 200px;
  height: 200px;
  margin-bottom: 0.5rem;
}

.secureNote {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 1.5rem;
  color: #666;
  font-size: 0.8rem;
}

.walletConnect {
  margin-top: 1rem;
}

.connectPrompt {
  margin-bottom: 1rem;
  text-align: center;
  color: #333;
}

.orDivider {
  position: relative;
  text-align: center;
  margin: 1.5rem 0;
}

.orDivider::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background-color: #e0e0e0;
}

.orDivider span {
  position: relative;
  background-color: #f9f7ff;
  padding: 0 1rem;
  color: #777;
  font-size: 0.9rem;
}

.connectedWalletInfo {
  margin: 1rem 0;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  background-color: white;
}

.walletInfo {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #f0f0f0;
}

.walletLabel {
  font-weight: 600;
  color: #333;
  margin-right: 0.5rem;
}

.walletAddress {
  font-family: monospace;
  padding: 0.3rem 0.5rem;
  background-color: #f5f5f5;
  border-radius: 4px;
  font-size: 0.9rem;
}

.transactionStatus {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  margin: 1rem 0;
  padding: 1rem;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.statusPending {
  background-color: #f9f7ff;
  color: #6e45e3;
}

.statusSuccess {
  background-color: #edf7ed;
  color: #2e7d32;
}

.statusFailed {
  background-color: #ffeded;
  color: #d32f2f;
}

.statusSpinner {
  width: 20px;
  height: 20px;
  border: 2px solid currentColor;
  border-right-color: transparent;
  border-radius: 50%;
  animation: spinner 0.8s linear infinite;
}

.statusIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}

@keyframes spinner {
  to {
    transform: rotate(360deg);
  }
}

@media (max-width: 768px) {
  .paymentContainer {
    padding: 24px;
    max-width: 100%;
  }
  
  .methods {
    flex-direction: column;
  }
  
  .method {
    flex-direction: row;
    gap: 12px;
    justify-content: flex-start;
  }
  
  .methodIcon {
    margin-bottom: 0;
  }
}
