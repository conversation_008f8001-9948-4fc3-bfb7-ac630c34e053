/* Main container */
.container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  color: #333;
}

/* Header styles */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.logo {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logo h1 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(90deg, #4f39fa, #da62c4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.nav {
  display: flex;
  gap: 2rem;
}

.nav a {
  text-decoration: none;
  color: #555;
  font-weight: 500;
  transition: color 0.2s ease;
}

.nav a:hover {
  color: #4f39fa;
}

.wallet {
  display: flex;
  align-items: center;
}

.connectButton {
  background: linear-gradient(90deg, #4f39fa, #da62c4);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 30px;
  font-weight: 600;
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.connectButton:hover {
  opacity: 0.9;
}

.connected {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: #f5f5f5;
  padding: 0.5rem 1rem;
  border-radius: 30px;
}

.dot {
  width: 10px;
  height: 10px;
  background-color: #10b981;
  border-radius: 50%;
}

.address {
  font-family: monospace;
  font-size: 0.9rem;
}

/* Main content */
.main {
  flex: 1;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

/* Hero section */
.hero {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4rem;
  gap: 2rem;
}

.heroContent {
  flex: 1;
}

.heroContent h1 {
  font-size: 2.5rem;
  font-weight: 800;
  background: linear-gradient(90deg, #4f39fa, #da62c4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 1.5rem;
}

.heroContent p {
  font-size: 1.2rem;
  line-height: 1.6;
  color: #555;
  margin-bottom: 2rem;
}

.heroCta {
  display: flex;
  gap: 1rem;
}

.primaryButton {
  display: inline-block;
  background: linear-gradient(90deg, #4f39fa, #da62c4);
  color: white;
  padding: 0.8rem 1.5rem;
  border-radius: 30px;
  font-weight: 600;
  text-decoration: none;
  transition: opacity 0.2s ease;
}

.primaryButton:hover {
  opacity: 0.9;
}

.secondaryButton {
  display: inline-block;
  background-color: transparent;
  color: #4f39fa;
  padding: 0.8rem 1.5rem;
  border-radius: 30px;
  border: 2px solid #4f39fa;
  font-weight: 600;
  text-decoration: none;
  transition: background-color 0.2s ease, color 0.2s ease;
}

.secondaryButton:hover {
  background-color: #4f39fa;
  color: white;
}

.heroImage {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.heroImage img {
  max-width: 100%;
  height: auto;
}

/* Features section */
.features {
  margin-bottom: 4rem;
}

.features h2 {
  font-size: 2rem;
  text-align: center;
  margin-bottom: 2rem;
  color: #333;
}

.featureGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
}

.featureCard {
  background-color: #fff;
  border-radius: 10px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.featureCard:hover {
  transform: translateY(-5px);
}

.featureIcon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.featureCard h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #333;
}

.featureCard p {
  color: #666;
  line-height: 1.6;
}

/* Featured content sections */
.featured {
  margin-bottom: 4rem;
}

.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.sectionHeader h2 {
  font-size: 1.8rem;
  color: #333;
}

.viewAllLink {
  color: #4f39fa;
  text-decoration: none;
  font-weight: 600;
  transition: opacity 0.2s ease;
}

.viewAllLink:hover {
  opacity: 0.8;
}

.featuredPublications, .featuredPatents {
  margin-bottom: 3rem;
}

.publicationGrid, .patentGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
}

.publicationCard, .patentCard {
  background-color: #fff;
  border-radius: 10px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.publicationCard:hover, .patentCard:hover {
  transform: translateY(-5px);
}

.publicationCard h3, .patentCard h3 {
  font-size: 1.3rem;
  margin-bottom: 0.8rem;
  color: #333;
}

.authors, .inventors {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.5rem;
}

.journal, .patentInfo, .dates {
  font-size: 0.9rem;
  color: #888;
  margin-bottom: 0.5rem;
}

.doi {
  font-family: monospace;
  font-size: 0.9rem;
  color: #4f39fa;
  margin-bottom: 0.8rem;
}

.abstract {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 1rem;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.cardLink {
  display: inline-block;
  color: #4f39fa;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.9rem;
  transition: opacity 0.2s ease;
}

.cardLink:hover {
  opacity: 0.8;
}

.loading {
  text-align: center;
  padding: 2rem;
  color: #888;
}

/* Stats section */
.stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.statCard {
  background-color: #f9f9f9;
  border-radius: 10px;
  padding: 2rem;
  text-align: center;
}

.statCard h3 {
  font-size: 2.5rem;
  color: #4f39fa;
  margin-bottom: 0.5rem;
}

.statCard p {
  color: #666;
  font-size: 1rem;
}

/* Partners section */
.partners {
  margin-bottom: 4rem;
  text-align: center;
}

.partners h2 {
  font-size: 2rem;
  margin-bottom: 2rem;
  color: #333;
}

.partnerLogos {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 3rem;
}

.partnerLogo {
  width: 150px;
  height: 80px;
  background-color: #f5f5f5;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: #888;
}

/* Footer styles */
.footer {
  background-color: #f9f9f9;
  padding: 4rem 2rem 2rem;
  margin-top: auto;
}

.footerGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.footerSection h3 {
  font-size: 1.2rem;
  margin-bottom: 1.5rem;
  color: #333;
}

.footerSection p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.footerSection ul {
  list-style: none;
  padding: 0;
}

.footerSection ul li {
  margin-bottom: 0.8rem;
}

.footerSection ul li a {
  color: #666;
  text-decoration: none;
  transition: color 0.2s ease;
}

.footerSection ul li a:hover {
  color: #4f39fa;
}

.socialLinks {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.socialLinks a {
  color: #4f39fa;
  text-decoration: none;
  transition: opacity 0.2s ease;
}

.socialLinks a:hover {
  opacity: 0.8;
}

.copyright {
  text-align: center;
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 1px solid #eee;
  color: #888;
  font-size: 0.9rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .hero {
    flex-direction: column;
  }
  
  .heroContent h1 {
    font-size: 2rem;
  }
  
  .nav {
    display: none;
  }
  
  .header {
    padding: 1rem;
  }
  
  .featureGrid, .publicationGrid, .patentGrid {
    grid-template-columns: 1fr;
  }
}
