.container {
  padding: 0 1rem;
  max-width: 1000px;
  margin: 0 auto;
}

.main {
  min-height: 100vh;
  padding: 2rem 0;
}

.backLink {
  margin-bottom: 2rem;
}

.backLink a {
  color: #0070f3;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  font-size: 1rem;
}

.backLink a:hover {
  text-decoration: underline;
}

.publication {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  padding: 2rem;
}

.title {
  font-size: 2.2rem;
  margin-bottom: 1.5rem;
  color: #333;
  line-height: 1.3;
}

.metadata {
  margin-bottom: 2.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #eaeaea;
}

.authors {
  font-size: 1.1rem;
  margin-bottom: 0.75rem;
  color: #444;
}

.journalInfo {
  font-size: 1rem;
  margin-bottom: 0.75rem;
  color: #666;
}

.journal {
  font-style: italic;
  margin-right: 0.5rem;
}

.date {
  margin-left: 0.5rem;
}

.doi {
  font-size: 0.95rem;
  color: #666;
}

.doi a {
  color: #0070f3;
  text-decoration: none;
}

.doi a:hover {
  text-decoration: underline;
}

.abstract, .keywords, .links, .funding {
  margin-bottom: 2.5rem;
}

.abstract h2, .keywords h2, .links h2, .funding h2 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #333;
}

.abstract p {
  font-size: 1.05rem;
  line-height: 1.6;
  color: #444;
}

.keywords ul {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  padding: 0;
  list-style: none;
}

.keywords li {
  padding: 0.4rem 0.8rem;
  background-color: #f0f7ff;
  border-radius: 2rem;
  font-size: 0.9rem;
  color: #0070f3;
}

.accessLinks {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.accessButton {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  background-color: #0070f3;
  color: white;
  text-decoration: none;
  border-radius: 0.25rem;
  font-size: 1rem;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.accessButton:hover {
  background-color: #0060df;
}

.funding p {
  font-size: 1rem;
  line-height: 1.6;
  color: #666;
  font-style: italic;
}

.loading, .error {
  text-align: center;
  padding: 4rem 0;
  color: #666;
  font-size: 1.2rem;
}

.error {
  color: #d32f2f;
}
