/* Reset CSS */
html,
body {
  padding: 0;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

* {
  box-sizing: border-box;
}

/* Global styles */
:root {
  --primary-color: #4f39fa;
  --secondary-color: #da62c4;
  --text-color: #333;
  --text-light: #666;
  --text-lighter: #888;
  --background-color: #fff;
  --background-light: #f9f9f9;
  --border-color: #eee;
  --box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
}

body {
  line-height: 1.6;
  color: var(--text-color);
  background-color: var(--background-light);
}

a {
  color: var(--primary-color);
  text-decoration: none;
}

button {
  cursor: pointer;
}

h1, h2, h3, h4, h5, h6 {
  margin-top: 0;
  font-weight: 700;
  line-height: 1.2;
}

p {
  margin-top: 0;
}

img {
  max-width: 100%;
  height: auto;
}

/* Utility classes */
.gradient-text {
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.section {
  margin-bottom: 4rem;
}

.card {
  background-color: var(--background-color);
  border-radius: 10px;
  box-shadow: var(--box-shadow);
  padding: 1.5rem;
}

.btn {
  display: inline-block;
  padding: 0.8rem 1.5rem;
  border-radius: 30px;
  font-weight: 600;
  text-align: center;
  transition: all 0.2s ease;
}

.btn-primary {
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  color: white;
  border: none;
}

.btn-primary:hover {
  opacity: 0.9;
}

.btn-secondary {
  background-color: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
  background-color: var(--primary-color);
  color: white;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

/* Responsive utilities */
.hide-mobile {
  display: block;
}

.show-mobile {
  display: none;
}

@media (max-width: 768px) {
  .hide-mobile {
    display: none;
  }
  
  .show-mobile {
    display: block;
  }
  
  .container {
    padding: 0 1rem;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #aaa;
}
