/**
 * black-white-theme.css
 * 
 * Puristisches Schwarz-Weiß-Theme mit minimalen Grautönen
 * Nur gezielte Abstufungen für Hierarchie und Lesbarkeit
 */

:root {
  /* Reduzierte Farbpalette: <PERSON><PERSON><PERSON>, Weiß und minimale Grautöne */
  --primary: #000000;
  --primary-dark: #000000;
  --primary-light: #000000;
  --secondary: #000000;
  --success: #000000;
  --info: #000000;
  --warning: #000000;
  --danger: #000000;
  --light: #ffffff;
  --dark: #000000;
  
  /* Gezielte Textfarben */
  --text-primary: #000000;
  --text-secondary: #222222;
  --text-muted: #666666;
  
  /* Hintergrundfarben: Weiß und minimale Abstufung */
  --bg-light: #ffffff;
  --bg-dark: #000000;
  --card-bg: #ffffff;
  
  /* Rahmenfarben: Abstufungen für visuelle Hierarchie */
  --border-color: #dddddd;
  
  /* Icon-Hintergründe */
  --icon-bg: transparent;
  --activity-bar-bg: #ffffff;
  --badge-bg: #ffffff;
  
  /* Spezifische Farbvariablen */
  --primary-color: #000000;
  --primary-color-dark: #000000;
  --primary-color-light: #000000;
  --secondary-color: #000000;
  --secondary-color-light: #ffffff;
  --secondary-color-dark: #000000;
  
  /* Text-Farben: Minimale Abstufung für Hierarchie */
  --text-color-primary: #000000;
  --text-color-secondary: #222222;
  --text-color-tertiary: #666666;
  
  /* Hintergrundfarben */
  --bg-color: #ffffff;
  --card-bg-color: #ffffff;
  
  /* Funktionale Farben */
  --error-color: #000000;
  --success-color: #000000;
  --warning-color: #000000;
  --info-color: #000000;
  
  /* Keine Schatten */
  --card-shadow: none;
}

/* Grundlegende Typografie */
body {
  font-family: -apple-system, BlinkMacSystemFont, sans-serif !important;
  line-height: 1.5 !important;
  font-size: 16px !important;
  color: #000000 !important;
}

h1, h2, h3, h4, h5, h6 {
  font-weight: 500 !important;
  margin-bottom: 1rem !important;
}

h1 { font-size: 1.75rem !important; }
h2 { font-size: 1.5rem !important; }
h3 { font-size: 1.25rem !important; }
h4 { font-size: 1rem !important; }

/* Karten und Container mit minimalistischer Gestaltung */
.card, 
div[class*="card"],
div[class*="container"],
.publication-info-card,
.stats-card {
  box-shadow: none !important;
  border: none !important;
  border-bottom: 1px solid #dddddd !important;
  border-radius: 0 !important;
  padding: 1.5rem 0 !important;
  background-color: transparent !important;
}

/* Puristische Buttons */
button, 
.btn-primary, 
.btn-secondary, 
.app-login-button,
button[class*="primary"],
button[class*="secondary"] {
  background: none !important;
  color: #000000 !important;
  border: none !important;
  border-bottom: 1px solid #000000 !important;
  border-radius: 0 !important;
  padding: 0.5rem 0 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.05em !important;
  font-size: 0.875rem !important;
  transition: opacity 0.2s !important;
}

button:hover, 
.btn-primary:hover, 
.btn-secondary:hover,
.app-login-button:hover,
button[class*="primary"]:hover,
button[class*="secondary"]:hover {
  opacity: 0.7 !important;
  background-color: transparent !important;
}

.btn-secondary.active {
  font-weight: bold !important;
}

/* Reduzierte Navigation */
.app-header {
  background-color: #ffffff !important;
  border-bottom: 1px solid #dddddd !important;
  box-shadow: none !important;
  padding: 1.5rem 0 !important;
}

.app-nav-link {
  color: #000000 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.05em !important;
  font-size: 0.875rem !important;
}

.app-nav-link.active {
  font-weight: bold !important;
}

.app-nav-link.active::after {
  display: none !important;
}

/* Vereinfachter Footer */
.app-footer {
  background-color: #ffffff !important;
  border-top: 1px solid #dddddd !important;
  color: #000000 !important;
}

.app-footer-copyright {
  background-color: transparent !important;
}

.app-footer-copyright p {
  color: #666666 !important;
}

/* Vereinfachung der Badges und Tags */
.publication-tag,
span[class*="badge"],
.recommended-badge,
.detected-badge {
  background-color: transparent !important;
  color: #000000 !important;
  border: none !important;
  border-left: 2px solid #000000 !important;
  border-radius: 0 !important;
  padding: 0 0.5rem !important;
  margin: 0.25rem 0.5rem 0.25rem 0 !important;
}

/* Links in Schwarz mit minimalistischer Unterstreichung */
a {
  color: #000000 !important;
  text-decoration: none !important;
  border-bottom: 1px solid transparent !important;
  transition: border-color 0.2s !important;
}

a:hover {
  text-decoration: none !important;
  border-bottom-color: #000000 !important;
}

/* Vereinfachte Tabellen */
table {
  border-collapse: collapse !important;
  width: 100% !important;
}

table th {
  border-bottom: 1px solid #000000 !important;
  font-weight: 500 !important;
  text-align: left !important;
  padding: 0.75rem 0 !important;
}

table td {
  border-bottom: 1px solid #dddddd !important;
  padding: 0.75rem 0 !important;
}

/* Minimalistischer Literature Export Button */
.literature-export-button {
  background: none !important;
  color: #000000 !important;
  border: none !important;
  border-bottom: 1px solid #000000 !important;
  border-radius: 0 !important;
  padding: 0.5rem 0 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.05em !important;
}

.literature-export-button:hover {
  opacity: 0.7 !important;
  background-color: transparent !important;
}

.literature-export-dropdown {
  border: none !important;
  border-left: 1px solid #dddddd !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  padding: 1rem 0 !important;
}

.literature-export-item {
  color: #000000 !important;
}

.literature-export-item:hover {
  background-color: transparent !important;
  opacity: 0.7 !important;
}

.literature-export-item-icon {
  background-color: transparent !important;
  color: #000000 !important;
}

.zotero-icon {
  background-color: transparent !important;
  color: #000000 !important;
  font-weight: bold !important;
}

.literature-export-group:not(:last-child) {
  border-bottom: 1px solid #dddddd !important;
}

.literature-export-info {
  background-color: transparent !important;
  border-top: 1px solid #dddddd !important;
}

.literature-export-hint {
  border-top: 1px solid #dddddd !important;
}

/* Minimalistischere Charts und Grafiken */
path[stroke], 
line[stroke] {
  stroke: #000000 !important;
  stroke-width: 1px !important;
}

rect[fill],
circle[fill],
path[fill] {
  fill: #000000 !important;
}

text {
  fill: #000000 !important;
  font-size: 0.75rem !important;
}

/* Minimalistischer Spinner */
.loading-spinner {
  border: 1px solid #eeeeee !important;
  border-top: 1px solid #000000 !important;
  border-radius: 50% !important;
}

/* Großzügigere Layouts */
.publication-detail-container {
  max-width: 900px !important;
  margin: 0 auto !important;
  padding: 3rem 1rem !important;
}

.publication-detail-content {
  display: grid !important;
  grid-template-columns: 1fr !important;
  gap: 3rem !important;
}

@media (min-width: 768px) {
  .publication-detail-content {
    grid-template-columns: 1fr 2fr !important;
  }
} 