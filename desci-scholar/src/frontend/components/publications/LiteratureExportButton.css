/**
 * LiteratureExportButton.css
 * 
 * Puristisches Styling mit minimalen Grautönen für die LiteratureExportButton-Komponente
 */

.literature-export-container {
  position: relative;
  display: inline-block;
}

.literature-export-button {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 0;
  background: none;
  color: #000000;
  border: none;
  border-bottom: 1px solid #000000;
  border-radius: 0;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: 500;
  cursor: pointer;
  transition: opacity 0.2s;
}

.literature-export-button:hover {
  opacity: 0.7;
  background: none;
}

.literature-export-icon {
  font-size: 1rem;
}

.literature-export-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 1rem;
  background-color: white;
  border: none;
  border-left: 1px solid #dddddd;
  border-radius: 0;
  box-shadow: none;
  z-index: 20;
  min-width: 250px;
  overflow: hidden;
  padding: 1rem 0;
}

.literature-export-group {
  padding: 1rem 0;
}

.literature-export-group:not(:last-child) {
  border-bottom: 1px solid #dddddd;
}

.literature-export-group h4 {
  font-size: 0.75rem;
  font-weight: 500;
  color: #000000;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 0 1rem 0.5rem;
  margin: 0;
}

.literature-export-item {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0.5rem 1rem;
  text-align: left;
  background: none;
  border: none;
  font-size: 0.875rem;
  color: #000000;
  cursor: pointer;
  transition: opacity 0.2s;
  gap: 0.75rem;
  position: relative;
}

.literature-export-item:hover {
  background: none;
  opacity: 0.7;
}

.literature-export-item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: none;
  color: #000000;
  font-size: 0.75rem;
  font-weight: 700;
}

.zotero-icon {
  background: none;
  color: #000000;
  font-family: serif;
  font-weight: bold;
}

.recommended-badge {
  position: absolute;
  right: 1rem;
  font-size: 0.7rem;
  background: none;
  color: #000000;
  padding: 0;
  border-left: 2px solid #000000;
  padding-left: 0.5rem;
  font-weight: 500;
}

.literature-export-info {
  padding: 0.75rem 1rem;
  font-size: 0.75rem;
  color: #666666;
  background: none;
  border-top: 1px solid #dddddd;
}

.literature-export-info p {
  margin: 0;
  line-height: 1.4;
  position: relative;
}

.literature-export-info p:not(:last-child) {
  margin-bottom: 0.5rem;
}

.detected-badge {
  display: inline-block;
  margin-left: 0.5rem;
  font-size: 0.7rem;
  background: none;
  color: #000000;
  padding: 0;
  border-left: 2px solid #000000;
  padding-left: 0.5rem;
  font-weight: 500;
  vertical-align: middle;
}

.literature-export-hint {
  color: #444444;
  padding-top: 0.5rem;
  margin-top: 0.5rem;
  border-top: 1px solid #dddddd;
  font-style: italic;
}

@media (max-width: 768px) {
  .literature-export-button {
    padding: 0.375rem 0;
    font-size: 0.8125rem;
  }
  
  .literature-export-dropdown {
    right: 0;
    left: auto;
    min-width: 220px;
  }
} 