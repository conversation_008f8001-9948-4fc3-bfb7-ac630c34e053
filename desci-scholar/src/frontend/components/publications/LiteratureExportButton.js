/**
 * LiteratureExportButton.js
 * 
 * Komponente zum Exportieren von Publikationsmetadaten in verschiedenen
 * bibliographischen Formaten für Literaturverwaltungsprogramme
 */

import React, { useState, useEffect } from 'react';
import { useReferenceManagerDetection } from '../../utils/reference-manager-detection';
import { checkZoteroAvailability } from '../../utils/zotero-integration';
import './LiteratureExportButton.css';

/**
 * Erstellt eine BibTeX-Darstellung einer Publikation
 * 
 * @param {Object} publication Die Publikationsdaten
 * @returns {string} BibTeX-Formatierte Publikationsdaten
 */
const generateBibTeX = (publication) => {
  // BibTeX-Entry-Typ basierend auf Publikationstyp bestimmen
  const entryType = publication.type === 'book' ? 'book' : 
                    publication.type === 'conference' ? 'inproceedings' : 'article';
  
  // BibTeX-Key erstellen (Erstautor + Jahr + <PERSON><PERSON><PERSON> Titelwort)
  const firstAuthor = publication.authors && publication.authors.length > 0 
    ? publication.authors[0].split(' ').pop().replace(/[^a-zA-Z]/g, '') 
    : 'Unknown';
  const year = publication.publishedAt 
    ? new Date(publication.publishedAt).getFullYear() 
    : new Date().getFullYear();
  const firstTitleWord = publication.title 
    ? publication.title.split(' ')[0].replace(/[^a-zA-Z]/g, '') 
    : 'Doc';
  const bibKey = `${firstAuthor}${year}${firstTitleWord}`;
  
  // BibTeX-Eintrag erstellen
  let bibtex = `@${entryType}{${bibKey},\n`;
  
  // Pflichtfelder
  bibtex += `  title = {${publication.title || ''}},\n`;
  
  // Autoren formatieren
  if (publication.authors && publication.authors.length > 0) {
    bibtex += `  author = {${publication.authors.join(' and ')}},\n`;
  }
  
  // Jahr
  if (publication.publishedAt) {
    bibtex += `  year = {${year}},\n`;
  }
  
  // Weitere Felder je nach Verfügbarkeit hinzufügen
  if (publication.journal) {
    bibtex += `  journal = {${publication.journal}},\n`;
  }
  
  if (publication.volume) {
    bibtex += `  volume = {${publication.volume}},\n`;
  }
  
  if (publication.issue) {
    bibtex += `  number = {${publication.issue}},\n`;
  }
  
  if (publication.pages) {
    bibtex += `  pages = {${publication.pages}},\n`;
  }
  
  if (publication.doi) {
    bibtex += `  doi = {${publication.doi}},\n`;
  }
  
  if (publication.abstract) {
    bibtex += `  abstract = {${publication.abstract}},\n`;
  }
  
  if (publication.keywords && publication.keywords.length > 0) {
    bibtex += `  keywords = {${publication.keywords.join(', ')}},\n`;
  }
  
  // URL zur Publikation
  bibtex += `  url = {${window.location.origin}/publications/${publication.id}},\n`;
  
  // BibTeX-Eintrag abschließen (letztes Komma entfernen und Klammer schließen)
  bibtex = bibtex.slice(0, -2) + '\n}';
  
  return bibtex;
};

/**
 * Erstellt eine RIS-Darstellung einer Publikation (für EndNote, Mendeley, etc.)
 * 
 * @param {Object} publication Die Publikationsdaten
 * @returns {string} RIS-Formatierte Publikationsdaten
 */
const generateRIS = (publication) => {
  // RIS-Typ basierend auf Publikationstyp bestimmen
  const risType = publication.type === 'book' ? 'BOOK' : 
                  publication.type === 'conference' ? 'CONF' : 'JOUR';
  
  let ris = `TY  - ${risType}\n`;
  
  // Titel
  ris += `TI  - ${publication.title || ''}\n`;
  
  // Autoren
  if (publication.authors && publication.authors.length > 0) {
    publication.authors.forEach(author => {
      ris += `AU  - ${author}\n`;
    });
  }
  
  // Datum
  if (publication.publishedAt) {
    const date = new Date(publication.publishedAt);
    ris += `PY  - ${date.getFullYear()}\n`;
    ris += `DA  - ${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()}\n`;
  }
  
  // Weitere Felder je nach Verfügbarkeit hinzufügen
  if (publication.journal) {
    ris += `JO  - ${publication.journal}\n`;
  }
  
  if (publication.volume) {
    ris += `VL  - ${publication.volume}\n`;
  }
  
  if (publication.issue) {
    ris += `IS  - ${publication.issue}\n`;
  }
  
  if (publication.pages) {
    ris += `SP  - ${publication.pages.split('-')[0]}\n`;
    if (publication.pages.includes('-')) {
      ris += `EP  - ${publication.pages.split('-')[1]}\n`;
    }
  }
  
  if (publication.doi) {
    ris += `DO  - ${publication.doi}\n`;
  }
  
  if (publication.abstract) {
    ris += `AB  - ${publication.abstract}\n`;
  }
  
  if (publication.keywords && publication.keywords.length > 0) {
    publication.keywords.forEach(keyword => {
      ris += `KW  - ${keyword}\n`;
    });
  }
  
  // URL zur Publikation
  ris += `UR  - ${window.location.origin}/publications/${publication.id}\n`;
  
  // RIS-Eintrag abschließen
  ris += 'ER  - \n';
  
  return ris;
};

/**
 * Erstellt eine CSL-JSON-Darstellung einer Publikation (für Zotero)
 * 
 * @param {Object} publication Die Publikationsdaten
 * @returns {Object} CSL-JSON-Objekt
 */
const generateCSLJSON = (publication) => {
  // CSL-Typ basierend auf Publikationstyp bestimmen
  const cslType = publication.type === 'book' ? 'book' : 
                  publication.type === 'conference' ? 'paper-conference' : 'article-journal';
  
  // Autoren formatieren
  const authors = publication.authors && publication.authors.length > 0 
    ? publication.authors.map(author => {
        const nameParts = author.split(' ');
        return {
          family: nameParts.pop(),
          given: nameParts.join(' ')
        };
      }) 
    : [];
  
  // Publikationsdatum
  const issued = publication.publishedAt 
    ? {
        'date-parts': [
          [new Date(publication.publishedAt).getFullYear()]
        ]
      } 
    : undefined;
  
  // CSL-JSON erstellen
  const cslJson = {
    id: publication.id,
    type: cslType,
    title: publication.title || '',
    author: authors,
    issued: issued,
    'container-title': publication.journal || '',
    volume: publication.volume || '',
    issue: publication.issue || '',
    page: publication.pages || '',
    DOI: publication.doi || '',
    URL: `${window.location.origin}/publications/${publication.id}`,
    abstract: publication.abstract || '',
    keyword: publication.keywords || []
  };
  
  return cslJson;
};

/**
 * Erstellt eine Datei und initiiert den Download
 * 
 * @param {string} content Dateiinhalt
 * @param {string} filename Dateiname
 * @param {string} type MIME-Typ der Datei
 */
const downloadFile = (content, filename, type) => {
  const blob = new Blob([content], { type });
  const url = URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.setAttribute('href', url);
  link.setAttribute('download', filename);
  link.style.display = 'none';
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

/**
 * Öffnet Zotero Connector für den Import (wenn installiert)
 * 
 * @param {Object} publication Die Publikationsdaten
 */
const exportToZotero = (publication) => {
  // CSL-JSON generieren
  const cslJson = generateCSLJSON(publication);
  
  // Prüfen, ob Zotero Connector API verfügbar ist
  if (window.Zotero && window.Zotero.Connector) {
    // Zotero Connector verwenden, wenn verfügbar
    window.Zotero.Connector.saveItem(cslJson);
  } else {
    // Fallback: Zotero Web-API über einen neuen Tab öffnen
    // Zotero Web-API akzeptiert nur bestimmte Arten von Metadaten
    const zoteroImportUrl = `https://www.zotero.org/save?url=${encodeURIComponent(window.location.href)}`;
    window.open(zoteroImportUrl, '_blank');
  }
};

/**
 * LiteratureExportButton Komponente
 * 
 * @param {Object} props Komponenten-Props
 * @param {Object} props.publication Die Publikationsdaten
 * @param {string} props.label Text für den Hauptbutton
 * @returns {JSX.Element}
 */
const LiteratureExportButton = ({ 
  publication, 
  label = 'Zitieren/Exportieren' 
}) => {
  const [showDropdown, setShowDropdown] = useState(false);
  const [zoteroAvailable, setZoteroAvailable] = useState(false);
  const { detectedManagers } = useReferenceManagerDetection();
  
  // Prüfen, ob Zotero verfügbar ist
  useEffect(() => {
    const checkZotero = async () => {
      const available = await checkZoteroAvailability();
      setZoteroAvailable(available);
    };
    
    checkZotero();
  }, []);
  
  const toggleDropdown = () => {
    setShowDropdown(prev => !prev);
  };
  
  const handleExportBibTeX = () => {
    if (!publication) return;
    
    const bibtex = generateBibTeX(publication);
    downloadFile(bibtex, `${publication.id}.bib`, 'application/x-bibtex');
    setShowDropdown(false);
  };
  
  const handleExportRIS = () => {
    if (!publication) return;
    
    const ris = generateRIS(publication);
    downloadFile(ris, `${publication.id}.ris`, 'application/x-research-info-systems');
    setShowDropdown(false);
  };
  
  const handleExportToZotero = () => {
    if (!publication) return;
    
    exportToZotero(publication);
    setShowDropdown(false);
  };
  
  // Abbrechen bei fehlenden Daten
  if (!publication) {
    return null;
  }
  
  return (
    <div className="literature-export-container">
      <button 
        className="literature-export-button"
        onClick={toggleDropdown}
        aria-haspopup="true"
        aria-expanded={showDropdown}
      >
        <span className="literature-export-icon">📄</span> {label}
      </button>
      
      {showDropdown && (
        <div className="literature-export-dropdown">
          {(zoteroAvailable || detectedManagers.zotero) && (
            <div className="literature-export-group">
              <h4>Direkter Export</h4>
              <button 
                className="literature-export-item"
                onClick={handleExportToZotero}
              >
                <span className="literature-export-item-icon zotero-icon">Z</span>
                Nach Zotero exportieren
              </button>
            </div>
          )}
          
          <div className="literature-export-group">
            <h4>Format herunterladen</h4>
            <button 
              className="literature-export-item"
              onClick={handleExportBibTeX}
            >
              <span className="literature-export-item-icon">B</span>
              BibTeX (.bib)
              {!detectedManagers.zotero && !detectedManagers.mendeley && !detectedManagers.endnote && (
                <span className="recommended-badge">Empfohlen</span>
              )}
            </button>
            <button 
              className="literature-export-item"
              onClick={handleExportRIS}
            >
              <span className="literature-export-item-icon">R</span>
              RIS-Format (.ris)
              {(detectedManagers.mendeley || detectedManagers.endnote) && !detectedManagers.zotero && (
                <span className="recommended-badge">Empfohlen</span>
              )}
            </button>
          </div>
          
          <div className="literature-export-info">
            <p>
              BibTeX: JabRef, BibDesk, LaTeX<br />
              RIS: EndNote, Mendeley, Bookends
              {detectedManagers.mendeley && <span className="detected-badge">Mendeley erkannt</span>}
              {detectedManagers.endnote && <span className="detected-badge">EndNote erkannt</span>}
            </p>
            {zoteroAvailable && !detectedManagers.zotero && (
              <p className="literature-export-hint">
                <strong>Tipp:</strong> Drücken Sie das Zotero-Symbol in der Browser-Symbolleiste, um diese Seite direkt zu speichern.
              </p>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default LiteratureExportButton; 