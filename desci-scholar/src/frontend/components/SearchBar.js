import React, { useState, useEffect, useRef } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSearch, faTimes, faSpinner } from '@fortawesome/free-solid-svg-icons';
import debounce from 'lodash/debounce';

import './SearchBar.css';

/**
 * Suchleisten-Komponente im Stil von OpenAlex
 * @param {Object} props - Komponenten-Properties
 * @param {Function} props.onSearch - Callback-Funktion für die Suche
 * @param {Function} props.onSuggestionSelected - Callback für ausgewählte Vorschläge
 * @param {Function} props.fetchSuggestions - Funktion zum Abrufen von Vorschlägen
 * @param {boolean} props.loading - Flag für Ladestatus
 * @param {string} props.placeholder - Placeholder-Text
 */
const SearchBar = ({ 
  onSearch, 
  onSuggestionSelected, 
  fetchSuggestions, 
  loading = false,
  placeholder = 'Suche nach Publikationen, Autoren, Konzepten...'
}) => {
  const [query, setQuery] = useState('');
  const [suggestions, setSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [focusedSuggestion, setFocusedSuggestion] = useState(-1);
  const inputRef = useRef(null);
  const suggestionsRef = useRef(null);

  // Debounced Funktion zum Abrufen von Vorschlägen
  const debouncedFetchSuggestions = useRef(
    debounce(async (searchTerm) => {
      if (searchTerm.length < 2) {
        setSuggestions([]);
        return;
      }
      
      try {
        const results = await fetchSuggestions(searchTerm);
        setSuggestions(results);
        setShowSuggestions(true);
      } catch (error) {
        console.error('Fehler beim Abrufen von Vorschlägen:', error);
        setSuggestions([]);
      }
    }, 300)
  ).current;

  // Vorschläge aktualisieren, wenn sich die Abfrage ändert
  useEffect(() => {
    if (query) {
      debouncedFetchSuggestions(query);
    } else {
      setSuggestions([]);
      setShowSuggestions(false);
    }
    
    return () => {
      debouncedFetchSuggestions.cancel();
    };
  }, [query, debouncedFetchSuggestions]);

  // Klick-Handler außerhalb der Komponente
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        suggestionsRef.current && 
        !suggestionsRef.current.contains(event.target) &&
        !inputRef.current.contains(event.target)
      ) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Handler für das Absenden der Suche
  const handleSubmit = (e) => {
    e.preventDefault();
    setShowSuggestions(false);
    if (onSearch && query.trim()) {
      onSearch(query);
    }
  };

  // Handler für das Löschen der Suche
  const handleClear = () => {
    setQuery('');
    setSuggestions([]);
    setShowSuggestions(false);
    setFocusedSuggestion(-1);
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  // Handler für Tastatureingaben
  const handleKeyDown = (e) => {
    // Pfeil nach unten
    if (e.key === 'ArrowDown') {
      e.preventDefault();
      if (suggestions.length > 0) {
        setFocusedSuggestion(prev => 
          prev < suggestions.length - 1 ? prev + 1 : prev
        );
        setShowSuggestions(true);
      }
    }
    // Pfeil nach oben
    else if (e.key === 'ArrowUp') {
      e.preventDefault();
      if (suggestions.length > 0) {
        setFocusedSuggestion(prev => 
          prev > 0 ? prev - 1 : 0
        );
        setShowSuggestions(true);
      }
    }
    // Enter-Taste
    else if (e.key === 'Enter') {
      if (focusedSuggestion >= 0 && suggestions[focusedSuggestion]) {
        e.preventDefault();
        handleSuggestionClick(suggestions[focusedSuggestion]);
      }
    }
    // Escape-Taste
    else if (e.key === 'Escape') {
      setShowSuggestions(false);
      setFocusedSuggestion(-1);
    }
  };

  // Handler für Klick auf einen Vorschlag
  const handleSuggestionClick = (suggestion) => {
    setQuery(suggestion.title || suggestion.name || suggestion.text);
    setShowSuggestions(false);
    setFocusedSuggestion(-1);
    
    if (onSuggestionSelected) {
      onSuggestionSelected(suggestion);
    }
  };

  return (
    <div className="search-bar-container">
      <form onSubmit={handleSubmit} className="search-form">
        <div className="search-input-container">
          <div className="search-icon">
            <FontAwesomeIcon icon={faSearch} />
          </div>
          
          <input
            ref={inputRef}
            type="text"
            className="search-input"
            placeholder={placeholder}
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onFocus={() => query && setSuggestions.length > 0 && setShowSuggestions(true)}
            onKeyDown={handleKeyDown}
            aria-label="Suchfeld"
          />
          
          {loading && (
            <div className="spinner-icon">
              <FontAwesomeIcon icon={faSpinner} spin />
            </div>
          )}
          
          {query && !loading && (
            <button
              type="button"
              className="clear-button"
              onClick={handleClear}
              aria-label="Suche löschen"
            >
              <FontAwesomeIcon icon={faTimes} />
            </button>
          )}
          
          <button
            type="submit"
            className="search-button"
            disabled={!query.trim() || loading}
            aria-label="Suche starten"
          >
            Suchen
          </button>
        </div>
        
        {showSuggestions && suggestions.length > 0 && (
          <div className="suggestions-container" ref={suggestionsRef}>
            <ul className="suggestions-list">
              {suggestions.map((suggestion, index) => (
                <li
                  key={suggestion.id || index}
                  className={`suggestion-item ${focusedSuggestion === index ? 'focused' : ''}`}
                  onClick={() => handleSuggestionClick(suggestion)}
                >
                  <div className="suggestion-icon">
                    {/* Icon basierend auf Typ */}
                    <FontAwesomeIcon 
                      icon={
                        suggestion.type === 'author' ? 'user' :
                        suggestion.type === 'concept' ? 'lightbulb' :
                        'file-alt'
                      } 
                    />
                  </div>
                  <div className="suggestion-content">
                    <div className="suggestion-title">
                      {suggestion.title || suggestion.name || suggestion.text}
                    </div>
                    {suggestion.description && (
                      <div className="suggestion-description">
                        {suggestion.description}
                      </div>
                    )}
                    {suggestion.type && (
                      <div className="suggestion-type">
                        {suggestion.type}
                      </div>
                    )}
                  </div>
                </li>
              ))}
            </ul>
          </div>
        )}
      </form>
    </div>
  );
};

export default SearchBar; 