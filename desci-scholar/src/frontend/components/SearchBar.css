/* SearchBar.css - Styling im OpenAlex-Design */

.search-bar-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  padding: 1rem;
}

.search-form {
  position: relative;
  width: 100%;
}

.search-input-container {
  display: flex;
  align-items: center;
  position: relative;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background-color: #fff;
  overflow: hidden;
  transition: box-shadow 0.3s ease;
}

.search-input-container:hover,
.search-input-container:focus-within {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.search-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 0.75rem;
  color: #626980;
  font-size: 1rem;
}

.search-input {
  flex: 1;
  padding: 1rem 0.75rem;
  border: none;
  outline: none;
  font-size: 1rem;
  color: #34364a;
  background: transparent;
  line-height: 1.5;
}

.search-input::placeholder {
  color: #a3a6b9;
}

.spinner-icon,
.clear-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 0.75rem;
  border: none;
  background: transparent;
  color: #626980;
  cursor: pointer;
  font-size: 1rem;
}

.clear-button:hover {
  color: #34364a;
}

.search-button {
  background-color: #2c4dd0;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  cursor: pointer;
  font-weight: 500;
  font-size: 0.9rem;
  transition: background-color 0.2s ease;
}

.search-button:hover {
  background-color: #1e38a3;
}

.search-button:disabled {
  background-color: #a3a6b9;
  cursor: not-allowed;
}

/* Vorschläge */
.suggestions-container {
  position: absolute;
  top: calc(100% + 4px);
  left: 0;
  right: 0;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 10;
  max-height: 400px;
  overflow-y: auto;
}

.suggestions-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.suggestion-item {
  display: flex;
  align-items: flex-start;
  padding: 0.75rem 1rem;
  cursor: pointer;
  border-bottom: 1px solid #f0f2f5;
  transition: background-color 0.2s ease;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item:hover,
.suggestion-item.focused {
  background-color: #f6f8fa;
}

.suggestion-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #2c4dd0;
  margin-right: 0.75rem;
  width: 24px;
  height: 24px;
}

.suggestion-content {
  flex: 1;
}

.suggestion-title {
  font-weight: 500;
  color: #34364a;
  margin-bottom: 0.25rem;
}

.suggestion-description {
  font-size: 0.85rem;
  color: #626980;
  margin-bottom: 0.25rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 500px;
}

.suggestion-type {
  font-size: 0.75rem;
  color: #a3a6b9;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Responsive Anpassungen */
@media (max-width: 768px) {
  .search-button {
    padding: 0.75rem 1rem;
  }
  
  .suggestion-description {
    max-width: 300px;
  }
}

@media (max-width: 480px) {
  .search-input-container {
    flex-wrap: wrap;
  }
  
  .search-input {
    width: calc(100% - 100px);
    order: 2;
  }
  
  .search-icon {
    order: 1;
  }
  
  .spinner-icon,
  .clear-button {
    order: 3;
  }
  
  .search-button {
    width: 100%;
    order: 4;
    margin-top: 0.5rem;
    border-radius: 4px;
  }
  
  .suggestion-description {
    max-width: 200px;
  }
} 