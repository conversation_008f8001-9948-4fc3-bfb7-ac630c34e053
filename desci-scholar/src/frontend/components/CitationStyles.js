import React, { useState } from 'react';
import { generateCitation } from '../utils/citation';

const CitationStyles = ({ publicationData }) => {
  const [selectedFormat, setSelectedFormat] = useState('BibTeX');
  const [showCitation, setShowCitation] = useState(false);

  const citationFormats = [
    'EndNote XML',
    'BibTeX',
    'RIS',
    'MLA',
    'APA',
    'ISO 690'
  ];

  const handleFormatChange = (format) => {
    setSelectedFormat(format);
    setShowCitation(true);
  };

  const getCitation = () => {
    try {
      return generateCitation(selectedFormat, publicationData);
    } catch (error) {
      console.error('Error generating citation:', error);
      return 'Citation format not supported';
    }
  };

  const copyToClipboard = () => {
    const citation = getCitation();
    navigator.clipboard.writeText(citation)
      .then(() => {
        alert('Citation copied to clipboard!');
      })
      .catch((err) => {
        console.error('Failed to copy citation:', err);
        alert('Failed to copy citation. Please try again.');
      });
  };

  return (
    <div className="citation-container">
      <h3>Cite this Publication</h3>
      <div className="citation-formats">
        {citationFormats.map((format) => (
          <button
            key={format}
            className={`format-button ${selectedFormat === format ? 'active' : ''}`}
            onClick={() => handleFormatChange(format)}
          >
            {format}
          </button>
        ))}
      </div>
      {showCitation && (
        <div className="citation-display">
          <pre>{getCitation()}</pre>
          <button className="copy-button" onClick={copyToClipboard}>
            Copy to Clipboard
          </button>
        </div>
      )}
      <style jsx>{`
        .citation-container {
          margin: 2rem 0;
          padding: 1.5rem;
          border: 1px solid #eaeaea;
          border-radius: 0.5rem;
          background-color: #f9f9f9;
        }
        
        h3 {
          margin-top: 0;
          color: #333;
        }
        
        .citation-formats {
          display: flex;
          flex-wrap: wrap;
          gap: 0.5rem;
          margin-bottom: 1rem;
        }
        
        .format-button {
          padding: 0.5rem 1rem;
          background-color: #ffffff;
          border: 1px solid #dddddd;
          border-radius: 0.25rem;
          cursor: pointer;
          font-size: 0.875rem;
          transition: all 0.2s ease;
        }
        
        .format-button:hover {
          background-color: #f0f0f0;
        }
        
        .format-button.active {
          background-color: #0070f3;
          color: white;
          border-color: #0070f3;
        }
        
        .citation-display {
          margin-top: 1rem;
          background-color: #fff;
          border: 1px solid #ddd;
          border-radius: 0.25rem;
          padding: 1rem;
          position: relative;
        }
        
        pre {
          white-space: pre-wrap;
          word-wrap: break-word;
          margin: 0;
          padding: 0;
          font-family: monospace;
          font-size: 0.875rem;
          line-height: 1.5;
          max-height: 200px;
          overflow-y: auto;
        }
        
        .copy-button {
          position: absolute;
          top: 0.5rem;
          right: 0.5rem;
          padding: 0.25rem 0.5rem;
          background-color: #f0f0f0;
          border: 1px solid #ddd;
          border-radius: 0.25rem;
          cursor: pointer;
          font-size: 0.75rem;
          transition: all 0.2s ease;
        }
        
        .copy-button:hover {
          background-color: #e0e0e0;
        }
      `}</style>
    </div>
  );
};

export default CitationStyles;
