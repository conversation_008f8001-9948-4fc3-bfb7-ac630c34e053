import React, { useState, useEffect } from 'react';
import styles from '../styles/PolkadotWalletConnect.module.css';
import * as polkadotUtilCrypto from '@polkadot/util-crypto';

/**
 * PolkadotWalletConnect component for connecting to various Polkadot wallets
 * @param {Object} props Component properties
 * @param {Function} props.onConnect Callback function when wallet is connected
 * @returns {JSX.Element} Component JSX
 */
const PolkadotWalletConnect = ({ onConnect }) => {
  const [connected, setConnected] = useState(false);
  const [account, setAccount] = useState(null);
  const [walletInstalled, setWalletInstalled] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Check if Polkadot wallets are installed
  useEffect(() => {
    const checkWalletExtensions = () => {
      // Check for Polkadot.js extension
      const hasPolkadotJs = window.injectedWeb3 && window.injectedWeb3['polkadot-js'];

      // Check for SubWallet
      const hasSubWallet = window.injectedWeb3 && window.injectedWeb3.subwallet;

      // Check for Talisman
      const hasTalisman = window.injectedWeb3 && window.injectedWeb3.talisman;

      setWalletInstalled(hasPolkadotJs || hasSubWallet || hasTalisman);
    };

    // When the component mounts, check for wallet extensions
    checkWalletExtensions();
  }, []);

  /**
   * Connect to Polkadot.js extension
   */
  const connectPolkadotJs = async () => {
    setLoading(true);
    setError(null);

    try {
      // Ensure the extension is installed
      if (!window.injectedWeb3 || !window.injectedWeb3['polkadot-js']) {
        throw new Error('Polkadot.js extension not found. Please install it first.');
      }

      // Get the extension
      const extension = window.injectedWeb3['polkadot-js'];

      // Enable the extension
      const injected = await extension.enable('DeSci-Scholar');

      // Get accounts
      const accounts = await injected.accounts.get();

      if (accounts.length === 0) {
        throw new Error('No accounts found. Please create an account in the Polkadot.js extension.');
      }

      // Use the first account
      const firstAccount = accounts[0];

      setAccount({
        address: firstAccount.address,
        name: firstAccount.name,
        source: 'polkadot-js'
      });

      setConnected(true);

      // Call the onConnect callback
      if (onConnect) {
        onConnect({
          address: firstAccount.address,
          name: firstAccount.name,
          source: 'polkadot-js',
          signer: injected.signer
        });
      }
    } catch (err) {
      console.error('Error connecting to Polkadot.js extension:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Connect to SubWallet extension
   */
  const connectSubWallet = async () => {
    setLoading(true);
    setError(null);

    try {
      // Ensure the extension is installed
      if (!window.injectedWeb3 || !window.injectedWeb3.subwallet) {
        throw new Error('SubWallet extension not found. Please install it first.');
      }

      // Get the extension
      const extension = window.injectedWeb3.subwallet;

      // Enable the extension
      const injected = await extension.enable('DeSci-Scholar');

      // Get accounts
      const accounts = await injected.accounts.get();

      if (accounts.length === 0) {
        throw new Error('No accounts found. Please create an account in the SubWallet extension.');
      }

      // Use the first account
      const firstAccount = accounts[0];

      setAccount({
        address: firstAccount.address,
        name: firstAccount.name,
        source: 'subwallet'
      });

      setConnected(true);

      // Call the onConnect callback
      if (onConnect) {
        onConnect({
          address: firstAccount.address,
          name: firstAccount.name,
          source: 'subwallet',
          signer: injected.signer
        });
      }
    } catch (err) {
      console.error('Error connecting to SubWallet extension:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Connect to Talisman extension
   */
  const connectTalisman = async () => {
    setLoading(true);
    setError(null);

    try {
      // Ensure the extension is installed
      if (!window.injectedWeb3 || !window.injectedWeb3.talisman) {
        throw new Error('Talisman extension not found. Please install it first.');
      }

      // Get the extension
      const extension = window.injectedWeb3.talisman;

      // Enable the extension
      const injected = await extension.enable('DeSci-Scholar');

      // Get accounts
      const accounts = await injected.accounts.get();

      if (accounts.length === 0) {
        throw new Error('No accounts found. Please create an account in the Talisman extension.');
      }

      // Use the first account
      const firstAccount = accounts[0];

      setAccount({
        address: firstAccount.address,
        name: firstAccount.name,
        source: 'talisman'
      });

      setConnected(true);

      // Call the onConnect callback
      if (onConnect) {
        onConnect({
          address: firstAccount.address,
          name: firstAccount.name,
          source: 'talisman',
          signer: injected.signer
        });
      }
    } catch (err) {
      console.error('Error connecting to Talisman extension:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Disconnect the wallet
   */
  const disconnectWallet = () => {
    setConnected(false);
    setAccount(null);

    // Call the onConnect callback with null
    if (onConnect) {
      onConnect(null);
    }
  };

  return (
    <div className={styles.container}>
      <h2 className={styles.title}>Polkadot Wallet</h2>

      {!connected ? (
        <div className={styles.walletOptions}>
          {!walletInstalled ? (
            <div className={styles.noWallet}>
              <p>No compatible wallet extensions detected. Bitte installieren Sie eine der folgenden:</p>
              <div className={styles.walletLinks}>
                <a
                  href="https://polkadot.js.org/extension/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className={styles.walletLink}
                >
                  <div className={styles.walletIcon}>
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
                      <circle cx="12" cy="12" r="5" stroke="currentColor" strokeWidth="2"/>
                      <path d="M8 12L10.5 14.5L16 9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                  <span>Polkadot.js</span>
                </a>
                <a
                  href="https://subwallet.app/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className={styles.walletLink}
                >
                  <div className={styles.walletIcon}>
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <rect x="3" y="6" width="18" height="12" rx="2" stroke="currentColor" strokeWidth="2" />
                      <path d="M16 14C16 15.1046 15.1046 16 14 16H10C8.89543 16 8 15.1046 8 14V10C8 8.89543 8.89543 8 10 8H14C15.1046 8 16 8.89543 16 10V14Z" stroke="currentColor" strokeWidth="2" />
                    </svg>
                  </div>
                  <span>SubWallet</span>
                </a>
                <a
                  href="https://www.talisman.xyz/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className={styles.walletLink}
                >
                  <div className={styles.walletIcon}>
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" strokeWidth="2" />
                      <path d="M8 12L10.5 14.5L16 9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                  </div>
                  <span>Talisman</span>
                </a>
              </div>
            </div>
          ) : (
            <div>
              <button className={styles.connectButton} onClick={connectPolkadotJs} disabled={loading}>
                {loading ? 'Connecting...' : 'Connect Polkadot.js'}
              </button>
              <button className={styles.connectButton} onClick={connectSubWallet} disabled={loading}>
                {loading ? 'Connecting...' : 'Connect SubWallet'}
              </button>
              <button className={styles.connectButton} onClick={connectTalisman} disabled={loading}>
                {loading ? 'Connecting...' : 'Connect Talisman'}
              </button>
            </div>
          )}

          {error && (
            <div className={styles.error}>
              {error}
            </div>
          )}

          {loading && (
            <div className={styles.loading}>
              <div className={styles.spinner}></div>
              <span>Connecting to wallet...</span>
            </div>
          )}
        </div>
      ) : (
        <div className={styles.connectedWallet}>
          <div className={styles.accountInfo}>
            <div className={styles.accountHeader}>
              <div className={styles.walletBadge}>
                {account.source === 'polkadot-js' && (
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
                    <circle cx="12" cy="12" r="5" stroke="currentColor" strokeWidth="2"/>
                  </svg>
                )}
                {account.source === 'subwallet' && (
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="3" y="6" width="18" height="12" rx="2" stroke="currentColor" strokeWidth="2"/>
                    <path d="M16 14C16 15.1046 15.1046 16 14 16H10C8.89543 16 8 15.1046 8 14V10C8 8.89543 8.89543 8 10 8H14C15.1046 8 16 8.89543 16 10V14Z" stroke="currentColor" strokeWidth="2"/>
                  </svg>
                )}
                {account.source === 'talisman' && (
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" strokeWidth="2"/>
                    <path d="M8 12L10.5 14.5L16 9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                )}
              </div>
              <div className={styles.accountDetails}>
                <div className={styles.accountName}>
                  {account.name}
                </div>
                <div className={styles.accountAddress}>
                  Adresse: {account.address}
                </div>
              </div>
              <button className={styles.disconnectButton} onClick={disconnectWallet}>
                Disconnect
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PolkadotWalletConnect;
