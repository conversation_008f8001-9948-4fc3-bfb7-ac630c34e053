import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import styles from '../styles/PaymentOptions.module.css';
import Image from 'next/image';
import { useWallet } from '../context/WalletContext';
import PolkadotWalletConnect from './PolkadotWalletConnect';

/**
 * PaymentOptions component for selecting and processing payments
 * @param {Object} props Component properties
 * @param {number} props.amount Amount to pay
 * @param {string} props.currency Currency code (default: USD)
 * @param {string} props.description Payment description
 * @param {Function} props.onPaymentComplete Callback when payment is complete
 * @returns {JSX.Element} PaymentOptions component
 */
const PaymentOptions = ({ amount, currency = 'USD', description, onPaymentComplete }) => {
  const router = useRouter();
  const { wallet, formatAddress } = useWallet();
  const [paymentMethod, setPaymentMethod] = useState('credit-card');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [dotAmount, setDotAmount] = useState(null);
  const [qrCode, setQrCode] = useState(null);
  const [transactionStatus, setTransactionStatus] = useState(null);
  const [retryCount, setRetryCount] = useState(0);
  const [successMessage, setSuccessMessage] = useState(null);
  
  // Validiere Props beim Laden der Komponente
  useEffect(() => {
    // Validiere den Betrag
    if (typeof amount !== 'number' || isNaN(amount) || amount <= 0) {
      setError('Ungültiger Zahlungsbetrag. Bitte geben Sie einen positiven Wert ein.');
      return;
    }
    
    // Validiere die Währung
    const supportedCurrencies = ['USD', 'EUR', 'DOT', 'KSM'];
    if (!supportedCurrencies.includes(currency)) {
      setError(`Nicht unterstützte Währung: ${currency}. Unterstützte Währungen: ${supportedCurrencies.join(', ')}`);
      return;
    }
    
    // Validiere Beschreibung
    if (!description || description.trim() === '') {
      setError('Zahlungsbeschreibung fehlt. Bitte geben Sie eine Beschreibung an.');
      return;
    }
    
    // Alles ist gültig, leere Fehlermeldungen
    setError(null);
  }, [amount, currency, description]);
  
  // Fetch DOT equivalent amount
  useEffect(() => {
    const fetchDotAmount = async () => {
      if (!amount || amount <= 0) return;
      
      try {
        setLoading(true);
        // Verwende einen Timeout um fehlgeschlagene Anfragen zu beenden
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 Sekunden Timeout
        
        // API-Aufruf mit besserer Fehlerbehandlung
        const response = await fetch(
          `/api/payments/convert?amount=${amount}&from=${currency}&to=DOT`,
          { signal: controller.signal }
        );
        
        clearTimeout(timeoutId);
        
        if (!response.ok) {
          throw new Error(`Serverfehler: ${response.status} ${response.statusText}`);
        }
        
        const data = await response.json();
        
        if (!data.convertedAmount) {
          throw new Error('Ungültige Antwort vom Server: Konvertierter Betrag fehlt');
        }
        
        setDotAmount(data.convertedAmount);
        setLoading(false);
      } catch (err) {
        console.error('Fehler beim Abrufen des DOT-Umrechnungskurses:', err);
        
        // Fallback-Berechnung mit einer Fehlermeldung
        setDotAmount((amount / 25).toFixed(6));
        setError(`Konnte aktuellen DOT-Kurs nicht abrufen. Verwende geschätzten Kurs (1 DOT ≈ 25 USD).${err.message ? ` Fehler: ${err.message}` : ''}`);
        setLoading(false);
        
        // Automatisch erneut versuchen mit begrenzter Anzahl von Versuchen
        if (retryCount < 3) {
          setTimeout(() => {
            setRetryCount(prevCount => prevCount + 1);
          }, 2000); // Nach 2 Sekunden erneut versuchen
        }
      }
    };
    
    fetchDotAmount();
  }, [amount, currency, retryCount]);
  
  // Handles payment method selection
  const handleMethodChange = (method) => {
    setPaymentMethod(method);
    setError(null);
    setTransactionStatus(null);
    setSuccessMessage(null);
    
    // Generate QR code if Polkadot selected and no wallet is connected
    if (method === 'polkadot' && !wallet && dotAmount) {
      generateQRCode();
    }
  };
  
  // Generate QR code for Polkadot payment
  const generateQRCode = async () => {
    try {
      setLoading(true);
      setError(null);
      
      if (!dotAmount || dotAmount <= 0) {
        throw new Error('Ungültiger DOT-Betrag. Bitte versuchen Sie es erneut.');
      }
      
      // API-Aufruf mit besserer Fehlerbehandlung
      const response = await fetch('/api/payments/polkadot/qrcode', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: dotAmount,
          description: description || 'DeSci-Scholar Zahlung',
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          `QR-Code konnte nicht generiert werden. Serverfehler: ${response.status} ${
            errorData.error || response.statusText
          }`
        );
      }
      
      const data = await response.json();
      
      if (!data.qrCodeUrl) {
        throw new Error('Server hat keinen QR-Code zurückgegeben');
      }
      
      setQrCode(data.qrCodeUrl);
      setSuccessMessage('QR-Code erfolgreich generiert. Bitte scannen Sie ihn mit Ihrer Polkadot Wallet.');
    } catch (err) {
      setError(err.message || 'Fehler bei der QR-Code-Generierung');
      console.error('Fehler bei der QR-Code-Generierung:', err);
    } finally {
      setLoading(false);
    }
  };
  
  // Process direct Polkadot payment using connected wallet
  const handlePolkadotDirectPayment = async () => {
    if (!wallet || !wallet.signer) {
      setError('Wallet nicht verbunden oder fehlender Signer. Bitte verbinden Sie Ihre Wallet erneut.');
      return;
    }
    
    if (!dotAmount || parseFloat(dotAmount) <= 0) {
      setError('Ungültiger DOT-Betrag. Bitte versuchen Sie es erneut.');
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      setTransactionStatus('vorbereiten');
      
      // Get the platform address to send payment to
      const addressResponse = await fetch('/api/payments/polkadot/address');
      
      if (!addressResponse.ok) {
        throw new Error(`Konnte Plattformadresse nicht abrufen: ${addressResponse.status} ${addressResponse.statusText}`);
      }
      
      const addressData = await addressResponse.json();
      
      if (!addressData.address) {
        throw new Error('Ungültige Antwort vom Server: Plattformadresse fehlt');
      }
      
      const platformAddress = addressData.address;
      setTransactionStatus('erstellen');
      
      // Create transaction on the server
      const createTxResponse = await fetch('/api/payments/polkadot/create-transaction', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fromAddress: wallet.address,
          amount: dotAmount,
          description: description || 'DeSci-Scholar Zahlung',
        }),
      });
      
      if (!createTxResponse.ok) {
        const errorData = await createTxResponse.json().catch(() => ({}));
        throw new Error(
          `Transaktion konnte nicht erstellt werden: ${errorData.error || createTxResponse.statusText}`
        );
      }
      
      const createTxData = await createTxResponse.json();
      
      if (!createTxData.success || !createTxData.txData) {
        throw new Error('Ungültige Antwort vom Server: Transaktionsdaten fehlen');
      }
      
      setTransactionStatus('signieren');
      
      // Sign and submit the transaction using the wallet's signer
      try {
        // In einer echten Implementierung würde hier die polkadot.js-API verwendet
        // Dies ist nur ein Beispiel, wie es implementiert werden könnte
        // Hier sollte eine reale Integration mit der gewählten Blockchain erfolgen
        const payload = {
          address: wallet.address,
          blockHash: '0x1234...', // Dies würde in einer echten Implementierung dynamisch sein
          method: createTxData.txData.extrinsicData.method,
          section: createTxData.txData.extrinsicData.section,
          args: [platformAddress, parseFloat(dotAmount) * 1e12],
        };
        
        // Mock für die Signatur in der Demo-Version
        // In einer echten Implementierung würde hier die Transaktion signiert werden
        setTransactionStatus('übertragen');
        
        // Simuliere erfolgreiche Transaktion nach kurzer Wartezeit
        setTimeout(() => {
          setTransactionStatus('erfolgreich');
          setSuccessMessage('Zahlung erfolgreich! Transaktion wurde an die Blockchain übermittelt.');
          
          // Rufe den onPaymentComplete-Callback auf
            if (onPaymentComplete) {
              onPaymentComplete({
                method: 'polkadot',
                amount: dotAmount,
                currency: 'DOT',
              transactionId: 'tx-' + Date.now(),
              timestamp: new Date().toISOString(),
              });
            }
            
            setLoading(false);
          }, 2000);
      } catch (signingError) {
        console.error('Fehler beim Signieren der Transaktion:', signingError);
        setTransactionStatus('fehlgeschlagen');
        throw new Error(`Fehler beim Signieren der Transaktion: ${signingError.message}`);
      }
    } catch (err) {
      console.error('Fehler bei der Polkadot-Zahlung:', err);
      setError(err.message || 'Fehler bei der Verarbeitung der Zahlung');
      setTransactionStatus('fehlgeschlagen');
      setLoading(false);
    }
  };
  
  // Process Stripe payment
  const handleStripePayment = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // This would call your backend API in production
      const response = await fetch('/api/payments/stripe/create-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount,
          currency: currency.toLowerCase(),
          description,
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to create payment session');
      }
      
      const data = await response.json();
      
      // Redirect to Stripe checkout
      window.location.href = data.redirectUrl;
    } catch (err) {
      setError(err.message);
      console.error('Error processing Stripe payment:', err);
      setLoading(false);
    }
  };
  
  // Process PayPal payment
  const handlePayPalPayment = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // This would call your backend API in production
      const response = await fetch('/api/payments/paypal/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount,
          currency,
          description,
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to create PayPal payment');
      }
      
      const data = await response.json();
      
      // Redirect to PayPal
      window.location.href = data.approvalUrl;
    } catch (err) {
      setError(err.message);
      console.error('Error processing PayPal payment:', err);
      setLoading(false);
    }
  };
  
  // Process payment based on selected method
  const processPayment = () => {
    switch (paymentMethod) {
      case 'credit-card':
        handleStripePayment();
        break;
      case 'paypal':
        handlePayPalPayment();
        break;
      case 'polkadot':
        if (wallet) {
          handlePolkadotDirectPayment();
        } else {
          // Payment happens via QR code scan
          // We could implement polling for confirmation here
        }
        break;
      default:
        setError('Invalid payment method');
    }
  };
  
  // Render transaction status message
  const renderTransactionStatus = () => {
    if (!transactionStatus) return null;
    
    let statusMessage = '';
    let statusClass = styles.statusPending;
    
    switch (transactionStatus) {
      case 'preparing':
        statusMessage = 'Preparing transaction...';
        break;
      case 'confirming':
        statusMessage = 'Confirming details...';
        break;
      case 'signing':
        statusMessage = 'Waiting for you to sign the transaction in your wallet...';
        break;
      case 'broadcasting':
        statusMessage = 'Broadcasting transaction to the blockchain...';
        break;
      case 'confirmed':
        statusMessage = 'Transaction confirmed! Payment complete.';
        statusClass = styles.statusSuccess;
        break;
      case 'failed':
        statusMessage = 'Transaction failed. Please try again.';
        statusClass = styles.statusFailed;
        break;
      default:
        statusMessage = 'Processing transaction...';
    }
    
    return (
      <div className={`${styles.transactionStatus} ${statusClass}`}>
        {transactionStatus !== 'confirmed' && (
          <div className={styles.statusSpinner}></div>
        )}
        {transactionStatus === 'confirmed' && (
          <div className={styles.statusIcon}>
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M16.6667 5L7.50001 14.1667L3.33334 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </div>
        )}
        <span>{statusMessage}</span>
      </div>
    );
  };
  
  // Handle wallet connection
  const handleWalletConnect = (walletData) => {
    // The WalletContext will handle storing the connection
    if (walletData) {
      // If wallet was just connected, clear any existing QR code
      setQrCode(null);
    }
  };
  
  return (
    <div className={styles.paymentContainer}>
      <h2 className={styles.title}>Payment Options</h2>
      
      <div className={styles.amountDisplay}>
        <span className={styles.amount}>{amount.toFixed(2)}</span>
        <span className={styles.currency}>{currency}</span>
      </div>
      
      <p className={styles.description}>{description}</p>
      
      {/* Fehleranzeige mit Benutzerfreundlichen Meldungen */}
      {error && (
        <div className={styles.errorMessage}>
          <p>{error}</p>
          <button 
            className={styles.retryButton} 
            onClick={() => {
              setError(null);
              setRetryCount(0);
              // Je nach Fehlertyp unterschiedliche Aktionen durchführen
              if (error.includes('DOT-Kurs')) {
                // Erneut versuchen, den Kurs abzurufen
                setRetryCount(prev => prev + 1);
              } else if (error.includes('QR-Code')) {
                // QR-Code neu generieren
                generateQRCode();
              } else if (error.includes('Wallet')) {
                // Komponente neu rendern, um Wallet-Verbindung zu versuchen
                setPaymentMethod(paymentMethod);
              }
            }}
          >
            Erneut versuchen
          </button>
        </div>
      )}
      
      {/* Erfolgsmeldung */}
      {successMessage && !error && (
        <div className={styles.successMessage}>
          <p>{successMessage}</p>
        </div>
      )}
      
      <div className={styles.methods}>
        <div 
          className={`${styles.method} ${paymentMethod === 'credit-card' ? styles.active : ''}`}
          onClick={() => handleMethodChange('credit-card')}
        >
          <div className={styles.methodIcon}>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect x="2" y="5" width="20" height="14" rx="2" stroke="currentColor" strokeWidth="2"/>
              <path d="M2 10H22" stroke="currentColor" strokeWidth="2"/>
              <path d="M6 15H10" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
            </svg>
          </div>
          <span>Credit Card</span>
        </div>
        
        <div 
          className={`${styles.method} ${paymentMethod === 'paypal' ? styles.active : ''}`}
          onClick={() => handleMethodChange('paypal')}
        >
          <div className={styles.methodIcon}>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M6.5 7C6.5 5.89543 7.39543 5 8.5 5H15.5C16.6046 5 17.5 5.89543 17.5 7V10C17.5 11.1046 16.6046 12 15.5 12H11L8.5 15V12C7.39543 12 6.5 11.1046 6.5 10V7Z" stroke="currentColor" strokeWidth="2"/>
              <path d="M10.5 12V16C10.5 17.1046 11.3954 18 12.5 18H16L18.5 21V18C19.6046 18 20.5 17.1046 20.5 16V13C20.5 11.8954 19.6046 11 18.5 11H17.5" stroke="currentColor" strokeWidth="2"/>
            </svg>
          </div>
          <span>PayPal</span>
        </div>
        
        <div 
          className={`${styles.method} ${paymentMethod === 'polkadot' ? styles.active : ''}`}
          onClick={() => handleMethodChange('polkadot')}
        >
          <div className={styles.methodIcon}>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
              <circle cx="12" cy="12" r="5" stroke="currentColor" strokeWidth="2"/>
              <path d="M12 2V7" stroke="currentColor" strokeWidth="2"/>
              <path d="M12 17V22" stroke="currentColor" strokeWidth="2"/>
              <path d="M2 12H7" stroke="currentColor" strokeWidth="2"/>
              <path d="M17 12H22" stroke="currentColor" strokeWidth="2"/>
            </svg>
          </div>
          <span>Polkadot (DOT)</span>
        </div>
      </div>
      
      {paymentMethod === 'polkadot' && (
        <div className={styles.cryptoPayment}>
          <div className={styles.conversionRate}>
            <p>Estimated cost: <strong>{dotAmount || '...'} DOT</strong></p>
          </div>
          
          {!wallet ? (
            <div className={styles.walletConnect}>
              <p className={styles.connectPrompt}>Connect your Polkadot wallet to pay directly:</p>
              <PolkadotWalletConnect onConnect={handleWalletConnect} />
              
              <div className={styles.orDivider}>
                <span>OR</span>
              </div>
              
              {qrCode ? (
                <div className={styles.qrCodeContainer}>
                  <img 
                    src={qrCode} 
                    alt="Polkadot payment QR code" 
                    className={styles.qrCode}
                  />
                  <p>Scan this QR code with your Polkadot wallet app</p>
                </div>
              ) : (
                <button 
                  className={styles.generateButton}
                  onClick={generateQRCode}
                  disabled={loading}
                >
                  {loading ? 'Generating...' : 'Generate Payment QR Code'}
                </button>
              )}
            </div>
          ) : (
            <div className={styles.connectedWalletInfo}>
              <div className={styles.walletInfo}>
                <span className={styles.walletLabel}>Connected Wallet:</span>
                <span className={styles.walletAddress}>{formatAddress(wallet.address)}</span>
              </div>
              
              {renderTransactionStatus()}
              
              {!transactionStatus && (
                <button 
                  className={styles.paymentButton}
                  onClick={processPayment}
                  disabled={loading}
                >
                  {loading ? 'Processing...' : `Pay ${dotAmount} DOT`}
                </button>
              )}
            </div>
          )}
        </div>
      )}
      
      {paymentMethod !== 'polkadot' && (
        <button 
          className={styles.paymentButton}
          onClick={processPayment}
          disabled={loading}
        >
          {loading ? 'Processing...' : `Pay with ${paymentMethod === 'credit-card' ? 'Card' : 'PayPal'}`}
        </button>
      )}
      
      <div className={styles.secureNote}>
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M4 7V5C4 2.79086 5.79086 1 8 1C10.2091 1 12 2.79086 12 5V7" stroke="currentColor" strokeWidth="1.5"/>
          <rect x="3" y="7" width="10" height="8" rx="1" stroke="currentColor" strokeWidth="1.5"/>
          <circle cx="8" cy="11" r="1" fill="currentColor"/>
        </svg>
        <span>All payments are secure and encrypted</span>
      </div>
    </div>
  );
};

export default PaymentOptions;
