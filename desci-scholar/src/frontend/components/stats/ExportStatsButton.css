/**
 * ExportStatsButton.css
 * 
 * Styling für die ExportStatsButton-Komponente
 */

.export-stats-container {
  position: relative;
  display: inline-block;
}

.export-stats-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: var(--primary, #007bff);
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.export-stats-button:hover {
  background-color: var(--primary-dark, #0069d9);
}

.export-icon {
  font-size: 1rem;
}

.export-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 0.25rem;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 10;
  min-width: 180px;
  overflow: hidden;
}

.export-dropdown-item {
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  text-align: left;
  background: none;
  border: none;
  font-size: 0.875rem;
  color: var(--text-primary, #212529);
  cursor: pointer;
  transition: background-color 0.15s ease;
}

.export-dropdown-item:hover {
  background-color: var(--bg-light, #f8f9fa);
}

.export-dropdown-item:not(:last-child) {
  border-bottom: 1px solid var(--border-color, #e9ecef);
}

@media (max-width: 768px) {
  .export-stats-button {
    padding: 0.375rem 0.75rem;
    font-size: 0.8125rem;
  }
  
  .export-dropdown {
    right: 0;
    left: auto;
  }
} 