/**
 * StatsDashboard.js
 * 
 * Haupt-Dashboard-Komponente zur Anzeige von Statistiken
 */

import React, { useState, useEffect, useCallback } from 'react';
import StatsSummaryCard from './StatsSummaryCard';
import TimeSeriesChart from './TimeSeriesChart';
import TrendingPublicationsList from './TrendingPublicationsList';
import GeographicDistributionChart from './GeographicDistributionChart';
import ExportStatsButton from './ExportStatsButton';
import { calculateDateRange } from '../../utils/date-utils';
import { 
  fetchTotalStats,
  fetchDownloadStats,
  fetchTrendingPublications,
  fetchStorageStats,
  fetchGeographicDistribution
} from '../../api/stats-client';
import './StatsDashboard.css';

/**
 * StatsDashboard Komponente
 * 
 * @param {Object} props Komponenten-Props
 * @param {boolean} props.showTrending Ob die Trending-Publikationen angezeigt werden sollen
 * @param {boolean} props.showDownloads Ob die Download-Diagramme angezeigt werden sollen
 * @param {boolean} props.showStorage Ob die Speicherstatistiken angezeigt werden sollen
 * @param {string} props.defaultTimeRange Standard-Zeitraum ('7d', '30d', etc.)
 * @param {Function} props.onPublicationClick Callback-Funktion wenn Publikation angeklickt wird
 * @returns {JSX.Element}
 */
const StatsDashboard = ({
  showTrending = true,
  showDownloads = true,
  showStorage = true,
  defaultTimeRange = '7d',
  onPublicationClick
}) => {
  // State für Daten und UI-Status
  const [timeRange, setTimeRange] = useState(defaultTimeRange);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [totalStats, setTotalStats] = useState(null);
  const [downloadStats, setDownloadStats] = useState([]);
  const [trendingPublications, setTrendingPublications] = useState([]);
  const [storageStats, setStorageStats] = useState(null);
  const [geoDistribution, setGeoDistribution] = useState([]);

  /**
   * Berechnet die Periode basierend auf dem Zeitraum
   * 
   * @param {string} range Zeitraum-Code
   * @returns {number} Periode in Tagen
   */
  const calculatePeriod = (range) => {
    switch (range) {
      case '7d': return 7;
      case '30d': return 30;
      case '90d': return 90;
      case '365d': return 365;
      default: return 30;
    }
  };

  /**
   * Lädt alle notwendigen Statistikdaten
   */
  const loadStatisticsData = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Zeitraum berechnen
      const period = calculatePeriod(timeRange);
      const { startDate, endDate } = calculateDateRange(timeRange);
      
      // Parallel Daten laden
      const [
        totalStatsData,
        downloadStatsData,
        trendingData,
        storageStatsData,
        geoDistributionData
      ] = await Promise.all([
        fetchTotalStats(),
        showDownloads ? fetchDownloadStats({ 
          startDate, 
          endDate, 
          groupBy: period > 90 ? 'month' : period > 30 ? 'week' : 'day' 
        }) : Promise.resolve([]),
        showTrending ? fetchTrendingPublications({ period, limit: 5 }) : Promise.resolve([]),
        showStorage ? fetchStorageStats() : Promise.resolve(null),
        fetchGeographicDistribution({ startDate, endDate, limit: 10 })
      ]);
      
      // Daten im State speichern
      setTotalStats(totalStatsData);
      setDownloadStats(downloadStatsData);
      setTrendingPublications(trendingData);
      setStorageStats(storageStatsData);
      setGeoDistribution(geoDistributionData);
    } catch (err) {
      console.error('Fehler beim Laden der Statistikdaten:', err);
      setError('Die Statistikdaten konnten nicht geladen werden. Bitte versuchen Sie es später erneut.');
    } finally {
      setLoading(false);
    }
  }, [timeRange, showDownloads, showTrending, showStorage]);

  // Daten laden, wenn sich der Zeitraum ändert
  useEffect(() => {
    loadStatisticsData();
  }, [loadStatisticsData]);

  /**
   * Handler für Änderung des Zeitraums
   */
  const handleTimeRangeChange = (e) => {
    setTimeRange(e.target.value);
  };

  /**
   * Bereitet alle Daten für den Export vor
   * 
   * @returns {Object} Exportierbare Daten
   */
  const getExportData = () => {
    return {
      metadaten: {
        zeitraum: timeRange,
        startDatum: calculateDateRange(timeRange).startDate,
        endDatum: calculateDateRange(timeRange).endDate,
        exportiertAm: new Date().toISOString()
      },
      gesamtstatistiken: totalStats,
      downloadVerlauf: downloadStats,
      trendingPublikationen: trendingPublications,
      speicherstatistiken: storageStats,
      geografischeVerteilung: geoDistribution
    };
  };

  // Zeige Ladezustand
  if (loading) {
    return (
      <div className="stats-dashboard">
        <div className="stats-dashboard-loading">
          <div className="stats-loading-spinner"></div>
          <p>Statistiken werden geladen...</p>
        </div>
      </div>
    );
  }

  // Zeige Fehlermeldung
  if (error) {
    return (
      <div className="stats-dashboard">
        <div className="stats-dashboard-error">
          <h3>Ein Fehler ist aufgetreten</h3>
          <p>{error}</p>
          <button 
            className="stats-retry-button"
            onClick={loadStatisticsData}
          >
            Erneut versuchen
          </button>
        </div>
      </div>
    );
  }

  // Zeige Meldung wenn keine Daten vorhanden
  if (!totalStats) {
    return (
      <div className="stats-dashboard">
        <div className="stats-no-data">
          <h3>Keine Statistikdaten verfügbar</h3>
          <p>Es sind noch keine Statistikdaten für die Plattform vorhanden.</p>
        </div>
      </div>
    );
  }

  // Zeitraumbezeichnung für Anzeige
  const periodLabel = {
    '7d': 'letzte 7 Tage',
    '30d': 'letzte 30 Tage',
    '90d': 'letzte 90 Tage',
    '365d': 'letztes Jahr'
  }[timeRange] || 'letzte 30 Tage';

  return (
    <div className="stats-dashboard">
      <div className="stats-dashboard-header">
        <h2>Statistik-Dashboard</h2>
        
        <div className="stats-dashboard-actions">
          <div className="stats-time-range-selector">
            <label htmlFor="timeRange">Zeitraum:</label>
            <select 
              id="timeRange" 
              value={timeRange} 
              onChange={handleTimeRangeChange}
            >
              <option value="7d">Letzte 7 Tage</option>
              <option value="30d">Letzte 30 Tage</option>
              <option value="90d">Letzte 90 Tage</option>
              <option value="365d">Letztes Jahr</option>
            </select>
          </div>
          
          <ExportStatsButton 
            data={getExportData()}
            filename={`desci-scholar-statistiken-${timeRange}`}
            label="Daten exportieren"
          />
        </div>
      </div>
      
      {/* Übersichtskarten */}
      <div className="stats-card-grid">
        <StatsSummaryCard 
          title="Publikationen"
          value={totalStats.publicationCount}
          icon="file-text"
          color="#4285F4"
        />
        <StatsSummaryCard 
          title="Ansichten"
          value={totalStats.views}
          icon="eye"
          color="#34A853"
        />
        <StatsSummaryCard 
          title="Downloads"
          value={totalStats.downloads}
          icon="download"
          color="#FBBC05"
        />
        {showStorage && storageStats && (
          <StatsSummaryCard 
            title="Gespeicherte Dateien"
            value={storageStats.storageSize}
            suffix="MB"
            icon="disk"
            color="#EA4335"
          />
        )}
      </div>
      
      {/* Zeitreihen-Diagramme */}
      {showDownloads && downloadStats.length > 0 && (
        <div className="stats-chart-container">
          <TimeSeriesChart 
            data={downloadStats}
            title={`Downloads (${periodLabel})`}
            label="Downloads"
            color="#4285F4"
            yAxisTitle="Anzahl Downloads"
          />
        </div>
      )}
      
      {/* Geographische Verteilung */}
      {geoDistribution.length > 0 && (
        <div className="stats-chart-container">
          <GeographicDistributionChart 
            data={geoDistribution}
            title={`Geografische Verteilung (${periodLabel})`}
            valueLabel="Zugriffe"
          />
        </div>
      )}
      
      {/* Trend-Publikationen */}
      {showTrending && trendingPublications.length > 0 && (
        <div className="stats-trending-container">
          <TrendingPublicationsList 
            publications={trendingPublications} 
            title="Trending Publikationen"
            period={periodLabel}
            onPublicationClick={onPublicationClick}
          />
        </div>
      )}
    </div>
  );
};

export default StatsDashboard; 