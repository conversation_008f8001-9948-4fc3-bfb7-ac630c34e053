/**
 * PublicationStatsDetail.css
 * 
 * Styling für die PublicationStatsDetail-Komponente
 */

.publication-stats-detail {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 1.5rem;
}

.publication-stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.publication-stats-header h2 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary, #212529);
}

.publication-stats-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.publication-time-range-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.publication-time-range-selector label {
  font-size: 0.875rem;
  color: var(--text-secondary, #6c757d);
}

.publication-time-range-selector select {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  border-radius: 4px;
  border: 1px solid var(--border-color, #ced4da);
  background-color: #fff;
  min-width: 150px;
}

.publication-stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.publication-chart-container {
  margin-bottom: 1.5rem;
}

.publication-stats-loading,
.publication-stats-error,
.publication-stats-no-data {
  padding: 2rem;
  text-align: center;
  background-color: var(--card-bg, #ffffff);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin: 1rem 0;
}

.publication-stats-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  color: var(--text-secondary, #6c757d);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 123, 255, 0.1);
  border-radius: 50%;
  border-top-color: var(--primary, #007bff);
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.publication-stats-error {
  color: var(--danger, #dc3545);
  background-color: rgba(220, 53, 69, 0.1);
}

.publication-stats-no-data {
  color: var(--text-secondary, #6c757d);
}

.publication-referrers {
  background-color: var(--card-bg, #ffffff);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  padding: 1.25rem;
  margin-bottom: 1.5rem;
}

.publication-referrers h3 {
  margin: 0 0 1rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary, #212529);
}

.referrers-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.referrer-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.referrer-name {
  font-size: 0.9375rem;
  color: var(--text-primary, #212529);
  display: flex;
  align-items: center;
}

.referrer-rank {
  display: inline-block;
  font-weight: 600;
  margin-right: 0.5rem;
  min-width: 1.5rem;
  color: var(--primary, #007bff);
}

.referrer-value {
  font-size: 0.8125rem;
  color: var(--text-secondary, #6c757d);
  margin-left: 2rem;
}

.referrer-bar-container {
  height: 6px;
  background-color: var(--activity-bar-bg, #e9ecef);
  border-radius: 3px;
  overflow: hidden;
  margin-top: 0.25rem;
}

.referrer-bar {
  height: 100%;
  background: linear-gradient(90deg, 
    var(--primary-light, rgba(0, 123, 255, 0.3)) 0%, 
    var(--primary, #007bff) 100%);
  border-radius: 3px;
}

@media (max-width: 768px) {
  .publication-stats-detail {
    padding: 1rem;
  }
  
  .publication-stats-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .publication-stats-actions {
    width: 100%;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
  
  .publication-time-range-selector {
    width: 100%;
  }
  
  .publication-time-range-selector select {
    flex-grow: 1;
  }
  
  .publication-stats-cards {
    grid-template-columns: 1fr;
  }
} 