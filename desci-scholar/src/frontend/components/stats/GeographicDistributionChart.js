/**
 * GeographicDistributionChart.js
 * 
 * Komponente zur Visualisierung der geografischen Verteilung 
 * von Zugriffen und Downloads als Balkendiagramm
 */

import React, { useMemo } from 'react';
import { Bar } from 'react-chartjs-2';
import { 
  Chart as ChartJS, 
  CategoryScale, 
  LinearScale, 
  BarElement, 
  Title, 
  Tooltip, 
  Legend 
} from 'chart.js';
import './GeographicDistributionChart.css';

// ChartJS-Komponenten registrieren
ChartJS.register(
  CategoryScale, 
  LinearScale, 
  BarElement, 
  Title, 
  Tooltip, 
  Legend
);

/**
 * GeographicDistributionChart Komponente
 * 
 * @param {Object} props Komponenten-Props
 * @param {Array} props.data Array mit Länder-Datenpunkten [{country: 'DE', value: 123, countryName: 'Deutschland'}]
 * @param {string} props.title Titel des Diagramms
 * @param {boolean} props.horizontal Ob das Balkendiagramm horizontal oder vertikal angezeigt werden soll
 * @param {number} props.height Höhe des Diagramms in Pixel
 * @param {string} props.valueLabel Beschriftung für die Werte (z.B. 'Zugriffe')
 * @returns {JSX.Element}
 */
const GeographicDistributionChart = ({
  data = [],
  title = 'Geografische Verteilung',
  horizontal = true,
  height = 350,
  valueLabel = 'Zugriffe'
}) => {
  // Farbpalette für die Länder
  const colors = [
    'rgba(54, 162, 235, 0.8)',
    'rgba(75, 192, 192, 0.8)',
    'rgba(255, 206, 86, 0.8)',
    'rgba(255, 99, 132, 0.8)',
    'rgba(153, 102, 255, 0.8)',
    'rgba(255, 159, 64, 0.8)',
    'rgba(199, 199, 199, 0.8)',
    'rgba(83, 102, 255, 0.8)',
    'rgba(40, 167, 69, 0.8)',
    'rgba(220, 53, 69, 0.8)'
  ];

  // Daten für Chart.js aufbereiten
  const chartData = useMemo(() => {
    if (!data || data.length === 0) {
      return {
        labels: [],
        datasets: [{
          data: []
        }]
      };
    }

    // Daten nach Wert sortieren (absteigend)
    const sortedData = [...data].sort((a, b) => b.value - a.value);
    
    // Labels (Länder) und Werte extrahieren
    const labels = sortedData.map(item => item.countryName || item.country);
    const values = sortedData.map(item => item.value);
    const backgroundColors = sortedData.map((_, index) => colors[index % colors.length]);
    
    return {
      labels,
      datasets: [
        {
          label: valueLabel,
          data: values,
          backgroundColor: backgroundColors,
          borderColor: backgroundColors.map(color => color.replace('0.8', '1')),
          borderWidth: 1,
        },
      ],
    };
  }, [data, valueLabel, colors]);

  // Chart.js Optionen konfigurieren
  const options = useMemo(() => ({
    responsive: true,
    maintainAspectRatio: false,
    indexAxis: horizontal ? 'y' : 'x',
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: !!title,
        text: title,
        font: {
          size: 16,
          weight: 'bold'
        },
        padding: {
          bottom: 15
        }
      },
      tooltip: {
        callbacks: {
          label: (context) => {
            return `${valueLabel}: ${context.parsed[horizontal ? 'x' : 'y'].toLocaleString('de-DE')}`;
          }
        }
      }
    },
    scales: {
      [horizontal ? 'x' : 'y']: {
        beginAtZero: true,
        ticks: {
          callback: (value) => value.toLocaleString('de-DE')
        }
      }
    },
  }), [title, horizontal, valueLabel]);

  // Fallback für leere Daten
  if (!data || data.length === 0) {
    return (
      <div className="geo-chart-empty">
        <p>Keine Daten zur geografischen Verteilung verfügbar.</p>
      </div>
    );
  }

  return (
    <div className="geo-distribution-chart" style={{ height }}>
      <Bar data={chartData} options={options} />
    </div>
  );
};

export default GeographicDistributionChart; 