/**
 * TimeSeriesChart.css
 * 
 * Styling für die TimeSeriesChart-Komponente
 */

.time-series-chart {
  padding: 1rem;
  border-radius: 8px;
  background-color: var(--card-bg, #ffffff);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 1.5rem;
  width: 100%;
  position: relative;
}

.time-series-chart canvas {
  width: 100% !important;
}

.time-series-chart-empty {
  padding: 2rem;
  border-radius: 8px;
  background-color: var(--card-bg, #ffffff);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  text-align: center;
  color: var(--text-secondary, #6c757d);
  height: 250px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.time-series-chart-empty p {
  margin: 0;
  font-size: 0.95rem;
}

@media (max-width: 768px) {
  .time-series-chart {
    padding: 0.75rem;
    overflow-x: auto;
  }
} 