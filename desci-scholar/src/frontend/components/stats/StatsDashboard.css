/**
 * StatsDashboard.css
 * 
 * Styling für die StatsDashboard-Komponente
 */

.stats-dashboard {
  padding: 1.5rem;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.stats-dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.stats-dashboard-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary, #212529);
}

.stats-dashboard-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stats-time-range-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.stats-time-range-selector label {
  font-size: 0.875rem;
  color: var(--text-secondary, #6c757d);
}

.stats-time-range-selector select {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  border-radius: 4px;
  border: 1px solid var(--border-color, #ced4da);
  background-color: #fff;
  min-width: 150px;
}

.stats-card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.stats-chart-container, 
.stats-trending-container {
  margin-bottom: 1.5rem;
}

.stats-dashboard-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  color: var(--text-secondary, #6c757d);
}

.stats-loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 123, 255, 0.1);
  border-radius: 50%;
  border-top-color: var(--primary, #007bff);
  animation: stats-spin 1s ease-in-out infinite;
  margin-bottom: 1rem;
}

@keyframes stats-spin {
  to {
    transform: rotate(360deg);
  }
}

.stats-dashboard-error {
  padding: 2rem;
  text-align: center;
  background-color: rgba(220, 53, 69, 0.1);
  border-radius: 8px;
  color: var(--danger, #dc3545);
  margin: 2rem auto;
  max-width: 600px;
}

.stats-retry-button {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background-color: var(--primary, #007bff);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background-color 0.2s;
}

.stats-retry-button:hover {
  background-color: var(--primary-dark, #0069d9);
}

.stats-no-data {
  padding: 3rem;
  text-align: center;
  background-color: var(--card-bg, #ffffff);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  color: var(--text-secondary, #6c757d);
  margin: 1rem 0;
}

@media (max-width: 768px) {
  .stats-dashboard {
    padding: 1rem;
  }
  
  .stats-dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .stats-dashboard-actions {
    width: 100%;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
  
  .stats-time-range-selector {
    width: 100%;
  }
  
  .stats-time-range-selector select {
    flex-grow: 1;
  }
  
  .stats-card-grid {
    grid-template-columns: 1fr;
  }
} 