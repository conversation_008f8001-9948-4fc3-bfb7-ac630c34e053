/**
 * StatsSummaryCard.js
 * 
 * Komponente zur Anzeige einer einzelnen Statistikzahl mit Icon und Titel
 */

import React from 'react';
import './StatsSummaryCard.css';

// Icon-Mapping
const ICONS = {
  'file-text': (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M14 2V8H20" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M16 13H8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M16 17H8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M10 9H9H8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  ),
  'eye': (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M1 12C1 12 5 4 12 4C19 4 23 12 23 12C23 12 19 20 12 20C5 20 1 12 1 12Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  ),
  'download': (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M7 10L12 15L17 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M12 15V3" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  ),
  'bar-chart': (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M18 20V10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M12 20V4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M6 20V14" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  ),
  'disk': (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M19.5 12.5725C19.5 11.9607 19.4829 11.8915 19.4327 11.7906C19.2888 11.5026 19.0978 11.2562 18.8639 11.0666C18.753 10.9768 18.6766 10.9611 18 10.9611H15C14.3234 10.9611 14.247 10.9768 14.1361 11.0666C13.9022 11.2562 13.7112 11.5026 13.5673 11.7906C13.5171 11.8915 13.5 11.9607 13.5 12.5725V21H19.5V12.5725Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M10.5 21V3.42751C10.5 2.81565 10.4829 2.74648 10.4327 2.64564C10.2888 2.35765 10.0978 2.11125 9.86394 1.92162C9.75296 1.83185 9.67664 1.81616 9 1.81616H6C5.32336 1.81616 5.24703 1.83185 5.13606 1.92162C4.90216 2.11125 4.71116 2.35765 4.56732 2.64564C4.51709 2.74648 4.5 2.81565 4.5 3.42751V21H10.5Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M22.5 21H1.5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  ),
  'user': (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M12 11C14.2091 11 16 9.20914 16 7C16 4.79086 14.2091 3 12 3C9.79086 3 8 4.79086 8 7C8 9.20914 9.79086 11 12 11Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  ),
  'calendar': (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M19 4H5C3.89543 4 3 4.89543 3 6V20C3 21.1046 3.89543 22 5 22H19C20.1046 22 21 21.1046 21 20V6C21 4.89543 20.1046 4 19 4Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M16 2V6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M8 2V6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M3 10H21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  )
};

/**
 * Komponente für eine einzelne Statistik-Übersichtskarte
 * 
 * @param {Object} props Komponenten-Props
 * @param {string} props.title Titel der Statistik
 * @param {number|string} props.value Wert der Statistik
 * @param {string} props.icon Name des zu verwendenden Icons
 * @param {string} props.suffix Suffix für den Wert (z.B. "MB", "%")
 * @param {Object} props.change Veränderung gegenüber dem Vorwert
 * @param {string} props.changeType "increase" oder "decrease" zur Festlegung der Farbe
 * @param {string} props.color Benutzerdefinierte Farbe für das Icon
 * @returns {JSX.Element}
 */
const StatsSummaryCard = ({ 
  title, 
  value, 
  icon = 'bar-chart', 
  suffix = '',
  change, 
  changeType,
  color 
}) => {
  // Hilfsfunktion zum Formatieren großer Zahlen
  const formatNumber = (num) => {
    if (typeof num !== 'number') return num;
    
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'k';
    }
    
    return num.toString();
  };
  
  // Veränderungspfeil und Farbe bestimmen
  const renderChange = () => {
    if (!change) return null;
    
    const changeClass = changeType === 'increase' ? 'stats-change-up' : 'stats-change-down';
    const arrow = changeType === 'increase' ? '↑' : '↓';
    
    return (
      <div className={`stats-card-change ${changeClass}`}>
        {arrow} {change}%
      </div>
    );
  };
  
  return (
    <div className="stats-summary-card">
      <div className="stats-card-icon" style={{ color: color || 'var(--primary-color)' }}>
        {ICONS[icon] || ICONS['bar-chart']}
      </div>
      
      <div className="stats-card-content">
        <h3 className="stats-card-title">{title}</h3>
        
        <div className="stats-card-value-container">
          <div className="stats-card-value">
            {formatNumber(value)}
            {suffix && <span className="stats-card-suffix">{suffix}</span>}
          </div>
          {renderChange()}
        </div>
      </div>
    </div>
  );
};

export default StatsSummaryCard; 