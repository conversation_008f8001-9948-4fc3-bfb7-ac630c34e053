/**
 * TrendingPublicationsList.css
 * 
 * Styling für die TrendingPublicationsList-Komponente
 */

.trending-publications-container {
  background-color: var(--card-bg, #ffffff);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  padding: 1.25rem;
  margin-bottom: 1.5rem;
}

.trending-publications-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.25rem;
  border-bottom: 1px solid var(--border-color, #e9ecef);
  padding-bottom: 0.75rem;
}

.trending-publications-header h3 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary, #212529);
}

.trending-period {
  font-size: 0.875rem;
  color: var(--text-secondary, #6c757d);
  background-color: var(--badge-bg, #f8f9fa);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.trending-publications-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.trending-publication-item {
  display: flex;
  align-items: flex-start;
  padding: 0.75rem;
  border-radius: 6px;
  background-color: var(--item-bg, #f8f9fa);
  cursor: pointer;
  transition: transform 0.15s ease, box-shadow 0.15s ease;
}

.trending-publication-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.trending-rank {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 28px;
  height: 28px;
  background-color: var(--primary, #007bff);
  color: white;
  border-radius: 50%;
  font-weight: 600;
  margin-right: 1rem;
  flex-shrink: 0;
  font-size: 0.875rem;
}

.trending-publication-info {
  flex: 1;
  min-width: 0; /* Für Text-Overflow */
}

.trending-publication-title {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary, #212529);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.trending-publication-meta {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.75rem;
  font-size: 0.8125rem;
  color: var(--text-secondary, #6c757d);
}

.trending-author {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.trending-stats {
  display: flex;
  gap: 1.25rem;
  margin-bottom: 0.75rem;
}

.trending-stat-item {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  font-size: 0.8125rem;
}

.trending-stat-icon {
  font-size: 0.875rem;
}

.trending-stat-value {
  font-weight: 500;
}

.trending-activity-bar-container {
  position: relative;
  height: 6px;
  background-color: var(--activity-bar-bg, #e9ecef);
  border-radius: 3px;
  overflow: hidden;
  margin-top: 0.5rem;
}

.trending-activity-bar {
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.trending-activity-label {
  position: absolute;
  right: 0;
  top: -18px;
  font-size: 0.75rem;
  color: var(--text-secondary, #6c757d);
}

.trending-publications-empty {
  padding: 2rem 0;
  text-align: center;
  color: var(--text-secondary, #6c757d);
  font-size: 0.9375rem;
}

@media (max-width: 768px) {
  .trending-publications-container {
    padding: 1rem;
  }
  
  .trending-publication-meta {
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .trending-stats {
    gap: 0.75rem;
  }
} 