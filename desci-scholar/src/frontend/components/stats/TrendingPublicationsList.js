/**
 * TrendingPublicationsList.js
 * 
 * Komponente zur Anzeige von Trending-Publikationen mit Aktivitätsstatistiken
 */

import React from 'react';
import './TrendingPublicationsList.css';

/**
 * Formatiert eine Zahl mit Präfix und Suffix
 * 
 * @param {number} value Der zu formatierende Wert
 * @param {string} prefix Optionales Präfix
 * @param {string} suffix Optionales Suffix
 * @returns {string} Formatierter Wert
 */
const formatValue = (value, prefix = '', suffix = '') => {
  if (typeof value !== 'number') return value;
  return `${prefix}${value.toLocaleString('de-DE')}${suffix}`;
};

/**
 * Hilfsfunktion zur Generierung eines Gradientenbalkens basierend auf dem Aktivitätswert
 * 
 * @param {number} activity Aktivitätswert (0-100)
 * @returns {Object} Style-Objekt für den Gradienten
 */
const getActivityBarStyle = (activity) => {
  const normalizedActivity = Math.min(Math.max(activity, 0), 100);
  return {
    width: `${normalizedActivity}%`,
    background: `linear-gradient(90deg, 
      var(--primary-light, rgba(0, 123, 255, 0.2)) 0%, 
      var(--primary, #007bff) 100%)`
  };
};

/**
 * Komponente zur Anzeige von Trending-Publikationen mit Aktivitätsstatistiken
 * 
 * @param {Object} props Komponenten-Props
 * @param {Array} props.publications Liste von Publikationen mit Aktivitätsdaten
 * @param {string} props.title Titel der Komponente
 * @param {string} props.period Zeitraum der Daten (z.B. "7 Tage")
 * @param {Function} props.onPublicationClick Callback-Funktion wenn Publikation angeklickt wird
 * @returns {JSX.Element}
 */
const TrendingPublicationsList = ({ 
  publications = [], 
  title = 'Trending Publikationen', 
  period = '7 Tage',
  onPublicationClick 
}) => {
  // Falls keine Daten vorhanden sind
  if (!publications || publications.length === 0) {
    return (
      <div className="trending-publications-container">
        <div className="trending-publications-header">
          <h3>{title}</h3>
          <span className="trending-period">{period}</span>
        </div>
        <div className="trending-publications-empty">
          <p>Keine Daten zu Trending-Publikationen verfügbar.</p>
        </div>
      </div>
    );
  }

  const handlePublicationClick = (publication) => {
    if (onPublicationClick && typeof onPublicationClick === 'function') {
      onPublicationClick(publication);
    }
  };

  return (
    <div className="trending-publications-container">
      <div className="trending-publications-header">
        <h3>{title}</h3>
        <span className="trending-period">{period}</span>
      </div>
      
      <div className="trending-publications-list">
        {publications.map((publication, index) => (
          <div 
            key={publication.id || index} 
            className="trending-publication-item"
            onClick={() => handlePublicationClick(publication)}
          >
            <div className="trending-rank">{index + 1}</div>
            
            <div className="trending-publication-info">
              <h4 className="trending-publication-title">{publication.title}</h4>
              
              <div className="trending-publication-meta">
                <span className="trending-author">{publication.authors?.join(', ') || 'Unbekannter Autor'}</span>
                <span className="trending-date">
                  {new Date(publication.publishedAt || publication.date).toLocaleDateString('de-DE')}
                </span>
              </div>
              
              <div className="trending-stats">
                <div className="trending-stat-item">
                  <span className="trending-stat-icon">👁️</span>
                  <span className="trending-stat-value">{formatValue(publication.views || 0)}</span>
                </div>
                
                <div className="trending-stat-item">
                  <span className="trending-stat-icon">⬇️</span>
                  <span className="trending-stat-value">{formatValue(publication.downloads || 0)}</span>
                </div>
                
                <div className="trending-stat-item">
                  <span className="trending-stat-icon">📑</span>
                  <span className="trending-stat-value">{formatValue(publication.citations || 0)}</span>
                </div>
              </div>
              
              <div className="trending-activity-bar-container">
                <div 
                  className="trending-activity-bar" 
                  style={getActivityBarStyle(publication.activityScore || 0)}
                />
                <span className="trending-activity-label">
                  Aktivität: {publication.activityScore?.toFixed(0) || 0}%
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default TrendingPublicationsList; 