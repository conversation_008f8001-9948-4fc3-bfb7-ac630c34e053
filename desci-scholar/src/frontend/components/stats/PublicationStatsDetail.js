/**
 * PublicationStatsDetail.js
 * 
 * Komponente zur Anzeige detaillierter Statistiken für eine einzelne Publikation
 */

import React, { useState, useEffect } from 'react';
import StatsSummaryCard from './StatsSummaryCard';
import TimeSeriesChart from './TimeSeriesChart';
import GeographicDistributionChart from './GeographicDistributionChart';
import ExportStatsButton from './ExportStatsButton';
import { calculateDateRange } from '../../utils/date-utils';
import { fetchPublicationStats } from '../../api/stats-client';
import './PublicationStatsDetail.css';

/**
 * PublicationStatsDetail Komponente
 * 
 * @param {Object} props Komponenten-Props
 * @param {string} props.publicationId ID der Publikation
 * @param {string} props.defaultTimeRange Standard-Zeitraum ('7d', '30d', etc.)
 * @returns {JSX.Element}
 */
const PublicationStatsDetail = ({ 
  publicationId, 
  defaultTimeRange = '30d'
}) => {
  const [timeRange, setTimeRange] = useState(defaultTimeRange);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [stats, setStats] = useState(null);
  
  useEffect(() => {
    if (!publicationId) {
      setError('Keine Publikations-ID angegeben');
      setLoading(false);
      return;
    }
    
    const loadPublicationStats = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const { startDate, endDate } = calculateDateRange(timeRange);
        const statsData = await fetchPublicationStats(publicationId, { startDate, endDate });
        setStats(statsData);
      } catch (err) {
        console.error('Fehler beim Laden der Publikationsstatistiken:', err);
        setError('Die Statistikdaten für diese Publikation konnten nicht geladen werden.');
      } finally {
        setLoading(false);
      }
    };
    
    loadPublicationStats();
  }, [publicationId, timeRange]);
  
  const handleTimeRangeChange = (e) => {
    setTimeRange(e.target.value);
  };
  
  // Exportdaten vorbereiten
  const getExportData = () => {
    if (!stats) return null;
    
    return {
      metadaten: {
        publikationId: publicationId,
        zeitraum: timeRange,
        exportiertAm: new Date().toISOString()
      },
      ...stats
    };
  };
  
  // Zeige Ladezustand
  if (loading) {
    return (
      <div className="publication-stats-detail">
        <div className="publication-stats-loading">
          <div className="loading-spinner"></div>
          <p>Statistiken werden geladen...</p>
        </div>
      </div>
    );
  }
  
  // Zeige Fehlermeldung
  if (error) {
    return (
      <div className="publication-stats-detail">
        <div className="publication-stats-error">
          <h3>Ein Fehler ist aufgetreten</h3>
          <p>{error}</p>
        </div>
      </div>
    );
  }
  
  // Zeige Meldung wenn keine Daten vorhanden
  if (!stats) {
    return (
      <div className="publication-stats-detail">
        <div className="publication-stats-no-data">
          <h3>Keine Statistikdaten verfügbar</h3>
          <p>Es sind noch keine Statistikdaten für diese Publikation vorhanden.</p>
        </div>
      </div>
    );
  }
  
  // Zeitraumbezeichnung für Anzeige
  const periodLabel = {
    '7d': 'letzte 7 Tage',
    '30d': 'letzte 30 Tage',
    '90d': 'letzte 90 Tage',
    '365d': 'letztes Jahr'
  }[timeRange] || 'letzte 30 Tage';
  
  return (
    <div className="publication-stats-detail">
      <div className="publication-stats-header">
        <h2>Statistiken für "{stats.title}"</h2>
        
        <div className="publication-stats-actions">
          <div className="publication-time-range-selector">
            <label htmlFor="publicationTimeRange">Zeitraum:</label>
            <select 
              id="publicationTimeRange" 
              value={timeRange} 
              onChange={handleTimeRangeChange}
            >
              <option value="7d">Letzte 7 Tage</option>
              <option value="30d">Letzte 30 Tage</option>
              <option value="90d">Letzte 90 Tage</option>
              <option value="365d">Letztes Jahr</option>
              <option value="all">Alle Zeit</option>
            </select>
          </div>
          
          <ExportStatsButton 
            data={getExportData()}
            filename={`publikation-${publicationId}-statistiken`}
            label="Exportieren"
          />
        </div>
      </div>
      
      {/* Übersichtskarten */}
      <div className="publication-stats-cards">
        <StatsSummaryCard 
          title="Ansichten"
          value={stats.views}
          icon="eye"
          color="#34A853"
          change={stats.viewsTrend}
          changeType={stats.viewsTrend > 0 ? 'increase' : 'decrease'}
        />
        
        <StatsSummaryCard 
          title="Downloads"
          value={stats.downloads}
          icon="download"
          color="#FBBC05"
          change={stats.downloadsTrend}
          changeType={stats.downloadsTrend > 0 ? 'increase' : 'decrease'}
        />
        
        <StatsSummaryCard 
          title="Zitierungen"
          value={stats.citations}
          icon="file-text"
          color="#4285F4"
          change={stats.citationsTrend}
          changeType={stats.citationsTrend > 0 ? 'increase' : 'decrease'}
        />
        
        <StatsSummaryCard 
          title="Aktivitätsrang"
          value={stats.activityRank || 'N/A'}
          suffix={stats.activityRank ? '.' : ''}
          icon="bar-chart"
          color="#EA4335"
        />
      </div>
      
      {/* Zeitreihen-Diagramme */}
      {stats.viewsOverTime && stats.viewsOverTime.length > 0 && (
        <div className="publication-chart-container">
          <TimeSeriesChart 
            data={stats.viewsOverTime}
            title={`Ansichten (${periodLabel})`}
            label="Ansichten"
            color="#34A853"
            yAxisTitle="Anzahl Ansichten"
          />
        </div>
      )}
      
      {stats.downloadsOverTime && stats.downloadsOverTime.length > 0 && (
        <div className="publication-chart-container">
          <TimeSeriesChart 
            data={stats.downloadsOverTime}
            title={`Downloads (${periodLabel})`}
            label="Downloads"
            color="#FBBC05"
            yAxisTitle="Anzahl Downloads"
          />
        </div>
      )}
      
      {/* Geografische Verteilung */}
      {stats.geoDistribution && stats.geoDistribution.length > 0 && (
        <div className="publication-chart-container">
          <GeographicDistributionChart 
            data={stats.geoDistribution}
            title={`Geografische Verteilung (${periodLabel})`}
            valueLabel="Zugriffe"
          />
        </div>
      )}
      
      {/* Referrer / Verweisquellen */}
      {stats.referrers && stats.referrers.length > 0 && (
        <div className="publication-referrers">
          <h3>Top Verweisquellen</h3>
          <div className="referrers-list">
            {stats.referrers.map((referrer, index) => (
              <div key={index} className="referrer-item">
                <div className="referrer-name">
                  <span className="referrer-rank">{index + 1}.</span>
                  {referrer.source}
                </div>
                <div className="referrer-value">{referrer.count} Zugriffe</div>
                <div className="referrer-bar-container">
                  <div 
                    className="referrer-bar" 
                    style={{ 
                      width: `${(referrer.count / stats.referrers[0].count) * 100}%` 
                    }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default PublicationStatsDetail; 