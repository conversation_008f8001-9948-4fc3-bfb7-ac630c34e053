/**
 * ExportStatsButton.js
 * 
 * Komponente zum Exportieren von Statistikdaten in verschiedenen Formaten
 */

import React, { useState } from 'react';
import './ExportStatsButton.css';

/**
 * Exportiert Daten als CSV-Datei
 * 
 * @param {Array|Object} data Die zu exportierenden Daten
 * @param {string} filename Der Dateiname ohne Erweiterung
 * @returns {void}
 */
const exportAsCSV = (data, filename) => {
  // Bei Objekten in ein Array umwandeln
  const dataArray = Array.isArray(data) ? data : [data];
  
  // Sicherstellen, dass Daten vorhanden sind
  if (!dataArray.length) return;
  
  // Spaltenüberschriften aus dem ersten Objekt extrahieren
  const headers = Object.keys(dataArray[0]);
  
  // CSV-Inhalt erstellen
  let csvContent = headers.join(',') + '\n';
  
  dataArray.forEach(item => {
    const row = headers.map(header => {
      // Wert aus dem Objekt extrahieren
      let value = item[header];
      
      // Wert formatieren
      if (value === null || value === undefined) {
        value = '';
      } else if (typeof value === 'object') {
        // Objekte und Arrays in JSON-Strings umwandeln
        value = JSON.stringify(value);
      }
      
      // Wenn der Wert Kommas oder Anführungszeichen enthält, in Anführungszeichen einschließen
      if (String(value).includes(',') || String(value).includes('"')) {
        value = `"${String(value).replace(/"/g, '""')}"`;
      }
      
      return value;
    }).join(',');
    
    csvContent += row + '\n';
  });
  
  // Blob erstellen und Download auslösen
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const url = URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.setAttribute('href', url);
  link.setAttribute('download', `${filename}.csv`);
  link.style.display = 'none';
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

/**
 * Exportiert Daten als JSON-Datei
 * 
 * @param {Array|Object} data Die zu exportierenden Daten
 * @param {string} filename Der Dateiname ohne Erweiterung
 * @returns {void}
 */
const exportAsJSON = (data, filename) => {
  const jsonContent = JSON.stringify(data, null, 2);
  const blob = new Blob([jsonContent], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.setAttribute('href', url);
  link.setAttribute('download', `${filename}.json`);
  link.style.display = 'none';
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

/**
 * ExportStatsButton Komponente
 * 
 * @param {Object} props Komponenten-Props
 * @param {Array|Object} props.data Die zu exportierenden Daten
 * @param {string} props.filename Der Dateiname ohne Erweiterung
 * @param {string} props.label Text für den Hauptbutton
 * @returns {JSX.Element}
 */
const ExportStatsButton = ({ 
  data, 
  filename = 'stats-export', 
  label = 'Exportieren' 
}) => {
  const [showDropdown, setShowDropdown] = useState(false);
  
  const toggleDropdown = () => {
    setShowDropdown(prev => !prev);
  };
  
  const handleExportCSV = () => {
    exportAsCSV(data, filename);
    setShowDropdown(false);
  };
  
  const handleExportJSON = () => {
    exportAsJSON(data, filename);
    setShowDropdown(false);
  };
  
  // Abbrechen bei fehlenden Daten
  if (!data || (Array.isArray(data) && data.length === 0)) {
    return null;
  }
  
  return (
    <div className="export-stats-container">
      <button 
        className="export-stats-button"
        onClick={toggleDropdown}
        aria-haspopup="true"
        aria-expanded={showDropdown}
      >
        <span className="export-icon">⬇️</span> {label}
      </button>
      
      {showDropdown && (
        <div className="export-dropdown">
          <button 
            className="export-dropdown-item"
            onClick={handleExportCSV}
          >
            Als CSV exportieren
          </button>
          <button 
            className="export-dropdown-item"
            onClick={handleExportJSON}
          >
            Als JSON exportieren
          </button>
        </div>
      )}
    </div>
  );
};

export default ExportStatsButton; 