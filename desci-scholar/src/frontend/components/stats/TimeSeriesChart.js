/**
 * TimeSeriesChart.js
 * 
 * Komponente zur Darstellung von Zeitreihendaten als Liniendiagramm
 * Verwendet React-ChartJS-2 für die Rendering-Funktionalität
 */

import React, { useMemo } from 'react';
import { Line } from 'react-chartjs-2';
import { 
  Chart as ChartJS, 
  CategoryScale, 
  LinearScale, 
  PointElement, 
  LineElement, 
  Title, 
  Tooltip, 
  Legend,
  Filler
} from 'chart.js';
import './TimeSeriesChart.css';

// ChartJS-Komponenten registrieren
ChartJS.register(
  CategoryScale, 
  LinearScale, 
  PointElement, 
  LineElement, 
  Title, 
  Tooltip, 
  Legend,
  Filler
);

/**
 * Zeitreihendiagramm-Komponente für die Darstellung von Statistikdaten über einen Zeitraum
 * 
 * @param {Object} props Komponenten-Props
 * @param {Array} props.data Array mit Datenpunkten im Format [{date: 'YYYY-MM-DD', value: number}]
 * @param {string} props.title Titel des Diagramms
 * @param {string} props.label Label für die Datenreihe
 * @param {string} props.color Hauptfarbe für die Datenreihe (als CSS-Farbe)
 * @param {boolean} props.fill Ob der Bereich unter der Linie gefüllt werden soll
 * @param {boolean} props.showLegend Ob die Legende angezeigt werden soll
 * @param {string} props.valuePrefix Präfix für die Werte (z.B. "$")
 * @param {string} props.valueSuffix Suffix für die Werte (z.B. "MB")
 * @param {number} props.height Höhe des Diagramms in Pixel
 * @param {string} props.yAxisTitle Beschriftung der Y-Achse
 * @returns {JSX.Element}
 */
const TimeSeriesChart = ({
  data = [],
  title = '',
  label = '',
  color = 'rgba(53, 162, 235, 1)',
  fill = true,
  showLegend = false,
  valuePrefix = '',
  valueSuffix = '',
  height = 300,
  yAxisTitle = ''
}) => {
  // Daten für Chart.js aufbereiten
  const chartData = useMemo(() => {
    if (!data || data.length === 0) {
      return {
        labels: [],
        datasets: [{
          data: []
        }]
      };
    }

    // Daten sortieren nach Datum
    const sortedData = [...data].sort((a, b) => new Date(a.date) - new Date(b.date));
    
    // Labels (X-Achse) und Werte (Y-Achse) extrahieren
    const labels = sortedData.map(item => {
      // Formatierung des Datums für die X-Achse
      const date = new Date(item.date);
      return date.toLocaleDateString('de-DE', { 
        day: '2-digit', 
        month: '2-digit',
        year: '2-digit'
      });
    });
    
    const values = sortedData.map(item => item.value);
    
    // Farben mit Transparenz für den Füllbereich
    const fillColor = color.replace('1)', '0.2)');
    
    return {
      labels,
      datasets: [
        {
          label,
          data: values,
          borderColor: color,
          backgroundColor: fill ? fillColor : 'transparent',
          tension: 0.4,
          fill,
          pointRadius: 3,
          pointHoverRadius: 5,
          pointBackgroundColor: color,
          pointBorderColor: '#fff',
          pointBorderWidth: 1.5,
        },
      ],
    };
  }, [data, label, color, fill]);

  // Chart.js Optionen konfigurieren
  const options = useMemo(() => ({
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: showLegend,
        position: 'top',
      },
      title: {
        display: !!title,
        text: title,
        font: {
          size: 16,
          weight: 'bold'
        },
        padding: {
          bottom: 15
        }
      },
      tooltip: {
        callbacks: {
          label: (context) => {
            let label = context.dataset.label || '';
            if (label) {
              label += ': ';
            }
            if (context.parsed.y !== null) {
              label += `${valuePrefix}${context.parsed.y.toLocaleString('de-DE')}${valueSuffix}`;
            }
            return label;
          }
        }
      }
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        ticks: {
          maxRotation: 45,
          minRotation: 45
        }
      },
      y: {
        beginAtZero: true,
        title: {
          display: !!yAxisTitle,
          text: yAxisTitle,
          font: {
            weight: 'bold'
          }
        },
        ticks: {
          callback: (value) => `${valuePrefix}${value.toLocaleString('de-DE')}${valueSuffix}`
        }
      }
    },
    interaction: {
      mode: 'index',
      intersect: false,
    },
  }), [title, showLegend, valuePrefix, valueSuffix, yAxisTitle]);

  // Fallback für leere Daten
  if (!data || data.length < 2) {
    return (
      <div className="time-series-chart-empty">
        <p>Keine ausreichenden Daten für die Anzeige eines Diagramms verfügbar.</p>
      </div>
    );
  }

  return (
    <div className="time-series-chart" style={{ height }}>
      <Line data={chartData} options={options} />
    </div>
  );
};

export default TimeSeriesChart; 