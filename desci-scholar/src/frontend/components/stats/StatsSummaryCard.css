/**
 * StatsSummaryCard.css
 * 
 * Styling für die StatsSummaryCard-Komponente
 */

.stats-summary-card {
  background-color: var(--card-bg, #ffffff);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  padding: 1.25rem;
  display: flex;
  align-items: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  height: 100%;
  min-height: 120px;
}

.stats-summary-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.stats-card-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 48px;
  height: 48px;
  background-color: var(--icon-bg, rgba(0, 123, 255, 0.1));
  border-radius: 12px;
  margin-right: 1rem;
  flex-shrink: 0;
}

.stats-card-content {
  flex: 1;
}

.stats-card-title {
  font-size: 0.875rem;
  color: var(--text-secondary, #6c757d);
  margin: 0 0 0.5rem 0;
  font-weight: 500;
}

.stats-card-value-container {
  display: flex;
  align-items: baseline;
  justify-content: space-between;
}

.stats-card-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary, #212529);
  display: flex;
  align-items: baseline;
}

.stats-card-suffix {
  font-size: 0.875rem;
  color: var(--text-secondary, #6c757d);
  margin-left: 0.25rem;
}

.stats-card-change {
  font-size: 0.8125rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  display: flex;
  align-items: center;
}

.stats-change-up {
  background-color: rgba(40, 167, 69, 0.1);
  color: var(--success, #28a745);
}

.stats-change-down {
  background-color: rgba(220, 53, 69, 0.1);
  color: var(--danger, #dc3545);
}

@media (max-width: 768px) {
  .stats-summary-card {
    padding: 1rem;
    min-height: 100px;
  }
  
  .stats-card-icon {
    width: 40px;
    height: 40px;
  }
  
  .stats-card-value {
    font-size: 1.25rem;
  }
} 