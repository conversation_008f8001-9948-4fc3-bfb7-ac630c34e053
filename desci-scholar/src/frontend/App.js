/**
 * App.js
 * 
 * Hauptkomponente für die DeSci-Scholar-Frontend-Anwendung
 */

import React, { useState } from 'react';
import StatsPage from './pages/StatsPage';
import PublicationDetailPage from './pages/PublicationDetailPage';
import './App.css';
// Schwarz-Weiß-Theme importieren (nach App.css, damit es Vorrang hat)
import './styles/black-white-theme.css';

// Einfache Routing-Konstanten
const ROUTES = {
  HOME: 'home',
  PUBLICATIONS: 'publications',
  PUBLICATION_DETAIL: 'publication-detail',
  STATS: 'stats',
  ABOUT: 'about'
};

/**
 * App-Hauptkomponente
 * Implementiert ein einfaches Routing und die globale Layout-Struktur
 * 
 * In einer vollständigen Anwendung würde hier React Router oder eine
 * andere Routing-Bibliothek verwendet werden.
 * 
 * @returns {JSX.Element}
 */
const App = () => {
  // Aktive Route speichern (einfaches Routing ohne React Router)
  const [activeRoute, setActiveRoute] = useState(ROUTES.STATS);

  // Navigation zu einer Seite
  const navigateTo = (route) => {
    setActiveRoute(route);
  };

  // Rendert den Hauptinhalt basierend auf der aktiven Route
  const renderMainContent = () => {
    switch (activeRoute) {
      case ROUTES.PUBLICATION_DETAIL:
        return <PublicationDetailPage />;
      case ROUTES.STATS:
        return <StatsPage />;
      default:
        return (
          <div className="placeholder-content">
            <h2>Seite wird entwickelt</h2>
            <p>Diese Seite ist noch in der Entwicklung.</p>
            <p>Bitte besuchen Sie die <button 
              onClick={() => navigateTo(ROUTES.STATS)} 
              className="text-button"
            >
              Statistikseite
            </button> oder die <button 
              onClick={() => navigateTo(ROUTES.PUBLICATION_DETAIL)} 
              className="text-button"
            >
              Publikationsdetailseite
            </button> für eine Vorschau.</p>
          </div>
        );
    }
  };

  return (
    <div className="app">
      <header className="app-header">
        <div className="app-header-content">
          <div className="app-logo">
            <h1 onClick={() => navigateTo(ROUTES.HOME)} style={{ cursor: 'pointer' }}>DeSci-Scholar</h1>
          </div>
          <nav className="app-nav">
            <ul>
              <li>
                <a 
                  href="#" 
                  className={`app-nav-link ${activeRoute === ROUTES.HOME ? 'active' : ''}`}
                  onClick={(e) => { e.preventDefault(); navigateTo(ROUTES.HOME); }}
                >
                  Startseite
                </a>
              </li>
              <li>
                <a 
                  href="#" 
                  className={`app-nav-link ${activeRoute === ROUTES.PUBLICATIONS || activeRoute === ROUTES.PUBLICATION_DETAIL ? 'active' : ''}`}
                  onClick={(e) => { e.preventDefault(); navigateTo(ROUTES.PUBLICATIONS); }}
                >
                  Publikationen
                </a>
              </li>
              <li>
                <a 
                  href="#" 
                  className={`app-nav-link ${activeRoute === ROUTES.STATS ? 'active' : ''}`}
                  onClick={(e) => { e.preventDefault(); navigateTo(ROUTES.STATS); }}
                >
                  Statistiken
                </a>
              </li>
              <li>
                <a 
                  href="#" 
                  className={`app-nav-link ${activeRoute === ROUTES.ABOUT ? 'active' : ''}`}
                  onClick={(e) => { e.preventDefault(); navigateTo(ROUTES.ABOUT); }}
                >
                  Über uns
                </a>
              </li>
            </ul>
          </nav>
          <div className="app-auth">
            <button className="app-login-button">Anmelden</button>
          </div>
        </div>
      </header>
      
      <main className="app-main">
        {renderMainContent()}
      </main>
      
      <footer className="app-footer">
        <div className="app-footer-content">
          <div className="app-footer-info">
            <h3>DeSci-Scholar</h3>
            <p>Eine dezentrale Plattform für wissenschaftliche Publikationen.</p>
          </div>
          
          <div className="app-footer-links">
            <div className="app-footer-section">
              <h4>Plattform</h4>
              <ul>
                <li><a href="#" onClick={(e) => { e.preventDefault(); navigateTo(ROUTES.HOME); }}>Entdecken</a></li>
                <li><a href="#" onClick={(e) => { e.preventDefault(); navigateTo(ROUTES.PUBLICATIONS); }}>Veröffentlichen</a></li>
                <li><a href="#" onClick={(e) => { e.preventDefault(); navigateTo(ROUTES.STATS); }}>Statistiken</a></li>
                <li><a href="#">API</a></li>
              </ul>
            </div>
            
            <div className="app-footer-section">
              <h4>Gemeinschaft</h4>
              <ul>
                <li><a href="#">Forum</a></li>
                <li><a href="#">Entwickler</a></li>
                <li><a href="#">Beitragen</a></li>
              </ul>
            </div>
            
            <div className="app-footer-section">
              <h4>Rechtliches</h4>
              <ul>
                <li><a href="#">Nutzungsbedingungen</a></li>
                <li><a href="#">Datenschutzerklärung</a></li>
                <li><a href="#">Impressum</a></li>
              </ul>
            </div>
          </div>
        </div>
        
        <div className="app-footer-copyright">
          <p>&copy; {new Date().getFullYear()} DeSci-Scholar. Alle Rechte vorbehalten.</p>
        </div>
      </footer>
    </div>
  );
};

export default App; 