import React, { createContext, useState, useContext, useEffect, useCallback } from 'react';

// Create the wallet context
const WalletContext = createContext();

/**
 * WalletProvider component to manage wallet connection state across the application
 * @param {Object} props Component properties with children
 * @returns {JSX.Element} Provider component
 */
export const WalletProvider = ({ children }) => {
  const [wallet, setWallet] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [availableWallets, setAvailableWallets] = useState([]);
  
  // Konstanten für localStorage-Schlüssel und Timeout-Werte definieren
  const LOCAL_STORAGE_KEY = 'desci_wallet_connection';
  const WALLET_RECONNECT_TIMEOUT = 500; // ms
  const WALLET_REQUEST_TIMEOUT = 5000; // ms
  
  /**
   * Speichert die Wallet-Verbindungsdaten im localStorage
   * @param {Object} walletData Wallet-Daten zum Speichern
   */
  const saveWalletData = useCallback((walletData) => {
    if (!walletData) {
      localStorage.removeItem(LOCAL_STORAGE_KEY);
      return;
    }
    
    try {
      const saveData = {
        address: walletData.address,
        source: walletData.source,
        timestamp: Date.now(),
      };
      localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(saveData));
    } catch (err) {
      console.error('Fehler beim Speichern der Wallet-Daten:', err);
    }
  }, []);
  
  /**
   * Überprüft die verfügbaren Wallet-Erweiterungen im Browser
   */
  const checkAvailableWallets = useCallback(() => {
    const wallets = [];
    
    if (typeof window === 'undefined' || !window.injectedWeb3) {
      return wallets;
    }
    
    // Bekannte Polkadot-Erweiterungen und deren Anzeigenamen
    const knownExtensions = {
      'polkadot-js': 'Polkadot.js',
      'subwallet-js': 'SubWallet',
      'talisman': 'Talisman',
      'fearless': 'Fearless Wallet',
      'nova': 'Nova Wallet',
      'math-wallet': 'Math Wallet',
    };
    
    // Verfügbare Erweiterungen sammeln
    Object.keys(window.injectedWeb3).forEach(key => {
      wallets.push({
        id: key,
        name: knownExtensions[key] || key,
        extension: window.injectedWeb3[key],
      });
    });
    
    setAvailableWallets(wallets);
    return wallets;
  }, []);
  
  // Überprüft, ob eine Wallet zuvor verbunden war
  useEffect(() => {
    const checkSavedWallet = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const savedWalletData = localStorage.getItem(LOCAL_STORAGE_KEY);
        
        if (!savedWalletData) {
          setLoading(false);
          return;
        }
        
        let walletData;
        try {
          walletData = JSON.parse(savedWalletData);
        } catch (parseError) {
          console.error('Ungültige gespeicherte Wallet-Daten:', parseError);
          localStorage.removeItem(LOCAL_STORAGE_KEY);
          setLoading(false);
          return;
        }
        
        // Überprüfen Sie, ob die Daten vor mehr als 24 Stunden gespeichert wurden
        const currentTime = Date.now();
        const savedTime = walletData.timestamp || 0;
        const oneDayMs = 24 * 60 * 60 * 1000;
        
        if (currentTime - savedTime > oneDayMs) {
          console.log('Gespeicherte Wallet-Daten sind abgelaufen');
          localStorage.removeItem(LOCAL_STORAGE_KEY);
          setLoading(false);
          return;
        }
        
        // Überprüfe, ob die verfügbaren Wallets geladen wurden
        const wallets = checkAvailableWallets();
        
        // Überprüfe, ob die Wallet-Erweiterung noch verfügbar ist
        const matchingWallet = wallets.find(w => w.id === walletData.source);
        
        if (!matchingWallet) {
          console.log(`Wallet-Erweiterung '${walletData.source}' nicht mehr verfügbar`);
          localStorage.removeItem(LOCAL_STORAGE_KEY);
          setLoading(false);
          return;
        }
        
        try {
          // Versuche, die Verbindung wiederherzustellen
          const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Zeitüberschreitung bei der Wallet-Verbindung')), WALLET_REQUEST_TIMEOUT);
          });
          
          const connectionPromise = async () => {
            const extension = matchingWallet.extension;
              const injected = await extension.enable('DeSci-Scholar');
              const accounts = await injected.accounts.get();
              
            // Finde das zuvor verbundene Konto
              const matchingAccount = accounts.find(acc => acc.address === walletData.address);
              
              if (matchingAccount) {
              return {
                  address: matchingAccount.address,
                name: matchingAccount.name || matchingAccount.meta?.name || 'Unbekannt',
                  source: walletData.source,
                signer: injected.signer,
                extension: matchingWallet.name
              };
              } else {
              throw new Error('Konto nicht mehr in der Wallet verfügbar');
            }
          };
          
          // Race zwischen Verbindung und Timeout
          const reconnectedWallet = await Promise.race([connectionPromise(), timeoutPromise]);
          setWallet(reconnectedWallet);
          saveWalletData(reconnectedWallet);
        } catch (err) {
          console.warn('Fehler bei der Wiederverbindung mit der Wallet:', err.message);
          localStorage.removeItem(LOCAL_STORAGE_KEY);
        }
      } catch (err) {
        console.error('Fehler beim Überprüfen der gespeicherten Wallet:', err);
        setError('Gespeicherte Wallet konnte nicht wiederhergestellt werden');
        localStorage.removeItem(LOCAL_STORAGE_KEY);
      } finally {
        setLoading(false);
      }
    };
    
    // Überprüfe Wallet-Erweiterungen
    if (typeof window !== 'undefined') {
      // Fügt eine Verzögerung hinzu, um sicherzustellen, dass injectedWeb3 vollständig geladen ist
      setTimeout(() => {
        checkSavedWallet();
      }, WALLET_RECONNECT_TIMEOUT);
    } else {
      setLoading(false);
    }
  }, [checkAvailableWallets, saveWalletData]);
  
  // Connect wallet function
  const connectWallet = useCallback(async (source) => {
    try {
      setLoading(true);
      setError(null);
      
      // Stelle sicher, dass Wallets überprüft wurden
      const wallets = checkAvailableWallets();
      
      // Finde die angegebene Wallet
      const selectedWallet = wallets.find(w => w.id === source);
      
      if (!selectedWallet) {
        throw new Error(`Wallet-Erweiterung '${source}' nicht gefunden`);
      }
      
      // Verbinde mit der Wallet
      const extension = selectedWallet.extension;
      const injected = await extension.enable('DeSci-Scholar');
      const accounts = await injected.accounts.get();
      
      if (accounts.length === 0) {
        throw new Error(`Keine Konten in der ${selectedWallet.name}-Wallet gefunden`);
      }
      
      // Verwende das erste Konto
      const account = accounts[0];
      
      const walletData = {
        address: account.address,
        name: account.name || account.meta?.name || 'Unbekannt',
        source: source,
        signer: injected.signer,
        extension: selectedWallet.name
      };
      
      setWallet(walletData);
      saveWalletData(walletData);
      
      return walletData;
    } catch (err) {
      console.error('Fehler beim Verbinden mit der Wallet:', err);
      setError(err.message || 'Fehler beim Verbinden mit der Wallet');
      setWallet(null);
      saveWalletData(null);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [checkAvailableWallets, saveWalletData]);
  
  // Disconnect wallet function
  const disconnectWallet = useCallback(() => {
    setWallet(null);
    saveWalletData(null);
    setError(null);
  }, [saveWalletData]);
  
  // Format a Polkadot address
  const formatAddress = useCallback((address) => {
    if (!address || typeof address !== 'string' || address.length < 10) {
      return address || '';
    }
    return `${address.slice(0, 6)}...${address.slice(-6)}`;
  }, []);
  
  // Check if the wallet is connected
  const isWalletConnected = useCallback(() => {
    return !!wallet && !!wallet.address;
  }, [wallet]);
  
  // Get all available wallets
  const getAvailableWallets = useCallback(() => {
    return availableWallets;
  }, [availableWallets]);
  
  return (
    <WalletContext.Provider 
      value={{ 
        wallet, 
        connectWallet, 
        disconnectWallet, 
        formatAddress,
        loading,
        error,
        isWalletConnected,
        getAvailableWallets,
      }}
    >
      {children}
    </WalletContext.Provider>
  );
};

/**
 * Custom hook to use the wallet context
 * @returns {Object} Wallet context value
 */
export const useWallet = () => useContext(WalletContext);

export default WalletContext;
