/**
 * date-utils.js
 * 
 * Hilfsfunktionen für Datumsoperationen im Frontend
 */

/**
 * Gibt ein Array mit Zeitraum-Optionen zurück
 * 
 * @returns {Array} Array mit Zeitraum-Optionen für Dropdowns
 */
export const getTimeRangeOptions = () => [
  { value: '7d', label: 'Letzte 7 Tage' },
  { value: '30d', label: 'Letzte 30 Tage' },
  { value: '90d', label: 'Letzte 90 Tage' },
  { value: '365d', label: 'Letztes Jahr' }
];

/**
 * Formatiert ein Datum in ein lokalisiertes Format
 * 
 * @param {string|Date} date Datum als String oder Date-Objekt
 * @param {Object} options Formatierungsoptionen für Intl.DateTimeFormat
 * @returns {string} Formatiertes Datum
 */
export const formatDate = (date, options = {}) => {
  if (!date) return '';
  
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  // Überprüfen, ob es ein gültiges Datum ist
  if (isNaN(dateObj.getTime())) {
    return '';
  }
  
  const defaultOptions = { 
    day: '2-digit', 
    month: '2-digit', 
    year: 'numeric' 
  };
  
  return dateObj.toLocaleDateString('de-DE', { ...defaultOptions, ...options });
};

/**
 * Berechnet den relativen Zeitraum vom heutigen Datum
 * 
 * @param {string} timeRange Zeitraumcode ('7d', '30d', '90d', '365d')
 * @returns {Object} Objekt mit startDate und endDate als ISO-Strings (YYYY-MM-DD)
 */
export const calculateDateRange = (timeRange) => {
  const now = new Date();
  const startDate = new Date();
  
  switch (timeRange) {
    case '7d':
      startDate.setDate(now.getDate() - 7);
      break;
    case '30d':
      startDate.setDate(now.getDate() - 30);
      break;
    case '90d':
      startDate.setDate(now.getDate() - 90);
      break;
    case '365d':
      startDate.setDate(now.getDate() - 365);
      break;
    default:
      startDate.setDate(now.getDate() - 30);
  }
  
  return {
    startDate: startDate.toISOString().split('T')[0],
    endDate: now.toISOString().split('T')[0]
  };
};

/**
 * Konvertiert eine Liste von Datenpunkten in ein Format für Zeitreihendiagramme
 * 
 * @param {Array} data Rohdaten mit Datums- und Wertattributen
 * @param {string} dateKey Name des Attributs, das das Datum enthält
 * @param {string} valueKey Name des Attributs, das den Wert enthält
 * @returns {Array} Formatierte Daten für Zeitreihendiagramme
 */
export const formatTimeSeriesData = (data, dateKey = 'date', valueKey = 'value') => {
  if (!data || !Array.isArray(data) || data.length === 0) {
    return [];
  }
  
  return data.map(item => ({
    date: item[dateKey],
    value: item[valueKey]
  }));
};

/**
 * Erstellt ein Array mit allen Tagen zwischen Start- und Enddatum
 * Nützlich, um Lücken in Zeitreihendaten zu füllen
 * 
 * @param {string} startDate Startdatum als ISO-String (YYYY-MM-DD)
 * @param {string} endDate Enddatum als ISO-String (YYYY-MM-DD)
 * @returns {Array} Array mit allen Tagen im Format YYYY-MM-DD
 */
export const generateDateRange = (startDate, endDate) => {
  const start = new Date(startDate);
  const end = new Date(endDate);
  const dateArray = [];
  
  const currentDate = new Date(start);
  
  while (currentDate <= end) {
    dateArray.push(currentDate.toISOString().split('T')[0]);
    currentDate.setDate(currentDate.getDate() + 1);
  }
  
  return dateArray;
};

/**
 * Füllt fehlende Tage in Zeitreihendaten mit Nullwerten
 * 
 * @param {Array} data Rohdaten mit date und value Eigenschaften
 * @param {string} startDate Startdatum als ISO-String (YYYY-MM-DD)
 * @param {string} endDate Enddatum als ISO-String (YYYY-MM-DD)
 * @returns {Array} Vollständige Zeitreihe ohne Lücken
 */
export const fillMissingDates = (data, startDate, endDate) => {
  const dateRange = generateDateRange(startDate, endDate);
  const dataMap = new Map();
  
  // Vorhandene Daten in eine Map konvertieren
  data.forEach(item => {
    dataMap.set(item.date, item.value);
  });
  
  // Vollständige Zeitreihe erstellen
  return dateRange.map(date => ({
    date,
    value: dataMap.has(date) ? dataMap.get(date) : 0
  }));
}; 