/**
 * reference-manager-detection.js
 * 
 * Hilfsfunktionen zur Erkennung von Literaturverwaltungsprogrammen
 * im Browser des Nutzers, um die Integration zu verbessern.
 */

import React from 'react';

/**
 * Erkannte Referenzmanager
 * @typedef {Object} DetectedReferenceManagers
 * @property {boolean} zotero - Ob Zotero erkannt wurde
 * @property {boolean} mendeley - Ob Mendeley erkannt wurde
 * @property {boolean} endnote - Ob EndNote Web erkannt wurde
 */

/**
 * Prüft, ob Zotero im Browser des Nutzers installiert ist
 * 
 * @returns {Promise<boolean>} True, wenn Zotero erkannt wurde
 */
const detectZotero = () => {
  return new Promise(resolve => {
    // Prüfen, ob Zotero-Connector bereits geladen ist
    if (window.Zotero && window.Zotero.Connector) {
      return resolve(true);
    }
    
    // Event-Listener für Zotero-Nachrichten einrichten
    const messageListener = (event) => {
      if (event.data && typeof event.data === 'object' && event.data.zoteroCheck) {
        window.removeEventListener('message', messageListener);
        resolve(true);
      }
    };
    
    window.addEventListener('message', messageListener);
    
    // Zotero-Connector ansprechen
    window.postMessage({ zoteroCheck: true }, '*');
    
    // Timeout nach 500ms
    setTimeout(() => {
      window.removeEventListener('message', messageListener);
      resolve(false);
    }, 500);
  });
};

/**
 * Prüft, ob Mendeley im Browser des Nutzers installiert ist
 * 
 * @returns {Promise<boolean>} True, wenn Mendeley Importer erkannt wurde
 */
const detectMendeley = () => {
  return new Promise(resolve => {
    // Prüfen, ob Mendeley-Objekt vorhanden ist
    if (window._mendeley) {
      return resolve(true);
    }
    
    // Test-Element hinzufügen, das von Mendeley erkannt werden könnte
    const testElement = document.createElement('div');
    testElement.id = 'mendeley-test';
    testElement.style.display = 'none';
    document.body.appendChild(testElement);
    
    // Timeout zum Erkennen von Änderungen durch die Mendeley-Erweiterung
    setTimeout(() => {
      const hasBeenModified = testElement.getAttribute('data-mendeley') ||
                              testElement.classList.contains('mendeley');
      
      document.body.removeChild(testElement);
      resolve(hasBeenModified);
    }, 300);
  });
};

/**
 * Prüft, ob EndNote im Browser des Nutzers installiert ist
 * (vereinfacht, da keine direkte API verfügbar ist)
 * 
 * @returns {Promise<boolean>} True, wenn EndNote-bezogene Elemente erkannt wurden
 */
const detectEndNote = () => {
  return Promise.resolve(
    !!document.querySelector('script[src*="endnote"]') ||
    !!document.querySelector('link[href*="endnote"]')
  );
};

/**
 * Erkennt installierte Literaturverwaltungsprogramme im Browser
 * 
 * @returns {Promise<DetectedReferenceManagers>} Objekt mit erkannten Programmen
 */
export const detectReferenceManagers = async () => {
  // Parallel alle Erkennungen ausführen
  const [zotero, mendeley, endnote] = await Promise.all([
    detectZotero(),
    detectMendeley(),
    detectEndNote()
  ]);
  
  return {
    zotero,
    mendeley,
    endnote
  };
};

/**
 * Gibt die beste Export-Option basierend auf den installierten 
 * Literaturverwaltungsprogrammen zurück
 * 
 * @param {DetectedReferenceManagers} detectedManagers Die erkannten Programme
 * @returns {string} Empfohlenes Export-Format ('zotero', 'bibtex', 'ris')
 */
export const getRecommendedExportFormat = (detectedManagers) => {
  if (detectedManagers.zotero) {
    return 'zotero';
  } else if (detectedManagers.mendeley || detectedManagers.endnote) {
    return 'ris';
  } else {
    return 'bibtex'; // Standard-Format
  }
};

/**
 * Hook, der installierte Literaturverwaltungsprogramme erkennt
 * 
 * @returns {Object} Erkannte Programme und empfohlenes Format
 */
export const useReferenceManagerDetection = () => {
  const [detectedManagers, setDetectedManagers] = React.useState({
    zotero: false,
    mendeley: false,
    endnote: false
  });
  
  const [recommendedFormat, setRecommendedFormat] = React.useState('bibtex');
  
  React.useEffect(() => {
    // Beim ersten Rendern Erkennung durchführen
    detectReferenceManagers().then(managers => {
      setDetectedManagers(managers);
      setRecommendedFormat(getRecommendedExportFormat(managers));
    });
  }, []);
  
  return {
    detectedManagers,
    recommendedFormat
  };
}; 