/**
 * zotero-integration.js
 * 
 * Hilfsfunktionen zur Integration von Zotero und anderen Literaturverwaltungsprogrammen
 */

/**
 * Lädt das Zotero Web Translator Skript dynamisch
 * 
 * Dies ermöglicht die Kommunikation mit der Zotero Browser-Erweiterung,
 * falls diese installiert ist.
 */
export const loadZoteroTranslator = () => {
  // Prüfen, ob Zotero bereits geladen ist
  if (window.Zotero && window.Zotero.Connector) {
    return Promise.resolve(true);
  }
  
  return new Promise((resolve) => {
    // Skript-Element erstellen
    const script = document.createElement('script');
    script.src = 'https://www.zotero.org/bookmarklet/translator.js';
    script.async = true;
    script.id = 'zotero-translator-script';
    
    // Event-Handler für erfolgreichen und fehlgeschlagenen Load
    script.onload = () => {
      console.log('Zotero Translator Script geladen');
      resolve(true);
    };
    
    script.onerror = () => {
      console.warn('Zotero Translator Script konnte nicht geladen werden');
      resolve(false);
    };
    
    // Skript zum Dokument hinzufügen
    document.head.appendChild(script);
  });
};

/**
 * Stellt Metadaten für den automatischen Import durch Zotero und andere
 * Literaturverwaltungs-Tools bereit
 * 
 * @param {Object} publication Publikationsdaten
 */
export const addPublicationMetaTags = (publication) => {
  if (!publication) return;
  
  // Bestehende Metadaten-Tags entfernen
  document.querySelectorAll('meta[name^="citation_"]').forEach(el => el.remove());
  
  // Hilfsfunktion zum Erstellen und Hinzufügen von Meta-Tags
  const addMetaTag = (name, content) => {
    if (content) {
      const meta = document.createElement('meta');
      meta.name = name;
      meta.content = content;
      document.head.appendChild(meta);
    }
  };
  
  // Einfache Metadaten
  addMetaTag('citation_title', publication.title);
  addMetaTag('citation_journal_title', publication.journal);
  addMetaTag('citation_volume', publication.volume);
  addMetaTag('citation_issue', publication.issue);
  addMetaTag('citation_pages', publication.pages);
  addMetaTag('citation_doi', publication.doi);
  
  // URL
  addMetaTag('citation_public_url', `${window.location.origin}/publications/${publication.id}`);
  
  // Datum formatieren und hinzufügen
  if (publication.publishedAt) {
    const date = new Date(publication.publishedAt);
    addMetaTag('citation_publication_date', `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()}`);
    addMetaTag('citation_year', date.getFullYear().toString());
  }
  
  // Autorenliste - jeder Autor bekommt ein eigenes Meta-Tag
  if (publication.authors && publication.authors.length > 0) {
    publication.authors.forEach(author => {
      addMetaTag('citation_author', author);
    });
  }
  
  // Schlüsselwörter als einzelnes Tag mit Kommas
  if (publication.keywords && publication.keywords.length > 0) {
    addMetaTag('citation_keywords', publication.keywords.join(', '));
  }
  
  // Für MODS-, COinS-Format und andere maschinenlesbare Formate könnte
  // bei Bedarf weitere Informationen hinzugefügt werden
};

/**
 * Fügt COinS (ContextObjects in Spans) für OpenURL-Unterstützung hinzu
 * Dies ermöglicht die Erkennung durch Tools wie Zotero und andere
 * 
 * @param {Object} publication Publikationsdaten
 * @param {HTMLElement} container Element, zu dem der Span hinzugefügt werden soll
 */
export const addCOinSTag = (publication, container) => {
  if (!publication || !container) return;
  
  // Vorhandene COinS-Tags entfernen
  container.querySelectorAll('span.Z3988').forEach(el => el.remove());
  
  // OpenURL Context Object erstellen
  // Wir verwenden ein Objekt mit einfachen Schlüsseln und behandeln
  // Punkte in den Schlüsseln beim Erstellen des URL-Strings
  const ctx = {
    url_ver: 'Z39.88-2004',
    ctx_ver: 'Z39.88-2004',
    rft_val_fmt: 'info:ofi/fmt:kev:mtx:journal',
    rft_genre: publication.type === 'book' ? 'book' : 'article',
    rft_atitle: publication.title,
    rft_jtitle: publication.journal,
    rft_volume: publication.volume,
    rft_issue: publication.issue,
    rft_pages: publication.pages,
    rft_date: publication.publishedAt ? new Date(publication.publishedAt).getFullYear() : '',
    rft_id_doi: publication.doi ? `info:doi:${publication.doi}` : '',
    rft_id_url: `${window.location.origin}/publications/${publication.id}`
  };
  
  // Autoren hinzufügen
  if (publication.authors && publication.authors.length > 0) {
    // Ersten Autor als primär nehmen
    const firstAuthor = publication.authors[0].split(' ');
    ctx.rft_aulast = firstAuthor.pop(); // Nachname
    ctx.rft_aufirst = firstAuthor.join(' '); // Vorname(n)
    
    // Alle Autoren als Array speichern
    ctx.rft_au = publication.authors;
  }
  
  // OpenURL-String erstellen mit Ersetzung der Schlüssel
  const openUrlStr = Object.entries(ctx)
    .filter(([_, value]) => value) // Leere Werte filtern
    .flatMap(([key, value]) => {
      // Array-Werte (wie Autoren) in mehrere Einträge aufteilen
      if (Array.isArray(value)) {
        return value.map(v => {
          // Schlüssel mit Unterstrich in Punkte konvertieren (rft_au -> rft.au)
          const formattedKey = key.replace(/_/g, '.');
          return `${encodeURIComponent(formattedKey)}=${encodeURIComponent(v)}`;
        });
      }
      
      // Einzelne Werte
      const formattedKey = key.replace(/_/g, '.');
      return [`${encodeURIComponent(formattedKey)}=${encodeURIComponent(value)}`];
    })
    .join('&');
  
  // Span-Element erstellen und hinzufügen
  const span = document.createElement('span');
  span.className = 'Z3988';
  span.title = openUrlStr;
  span.style.display = 'none'; // Unsichtbar machen
  container.appendChild(span);
};

/**
 * Prüft, ob Zotero-Erweiterung installiert ist
 * 
 * @returns {Promise<boolean>} True, wenn Zotero verfügbar ist
 */
export const checkZoteroAvailability = async () => {
  // Zotero Translator Skript laden
  await loadZoteroTranslator();
  
  // Prüfen, ob Zotero nach dem Laden verfügbar ist
  return !!(window.Zotero && window.Zotero.Connector);
};

// Beim App-Start Zotero-Skript laden
if (typeof window !== 'undefined') {
  loadZoteroTranslator().then(isLoaded => {
    if (isLoaded) {
      console.log('Zotero-Integration bereit');
    }
  });
} 