// Utility functions to generate citations in various formats

/**
 * Generate a citation in the specified format
 * @param {string} format Citation format name
 * @param {Object} data Publication data
 * @returns {string} Formatted citation
 * @throws {Error} If format is unsupported or data is invalid
 */
export function generateCitation(format, data) {
  // Validate input data
  if (!data) {
    throw new Error('Keine Publikationsdaten angegeben');
  }
  
  // Prüfen auf erforderliche Felder
  const requiredFields = ['title', 'authors', 'year', 'doi'];
  const missingFields = requiredFields.filter(field => !data[field]);
  
  if (missingFields.length > 0) {
    throw new Error(`Fehlende erforderliche Felder: ${missingFields.join(', ')}`);
  }
  
  // Erzeugen des Zitats im angegebenen Format
  switch (format) {
    case 'EndNote XML':
      return generateEndNoteXML(data);
    case 'BibTeX':
      return generateBibTeX(data);
    case 'RIS':
      return generateRIS(data);
    case 'MLA':
      return generateMLA(data);
    case 'APA':
      return generateAPA(data);
    case 'ISO 690':
      return generateISO690(data);
    default:
      throw new Error(`Nicht unterstütztes Zitierformat: ${format}`);
  }
}

/**
 * Parse an author name into components, handling international names
 * @param {string} author Full author name
 * @returns {Object} Name components: firstName, lastName
 */
function parseAuthorName(author) {
  if (!author || typeof author !== 'string') {
    return { firstName: '', lastName: 'Unbekannt' };
  }
  
  // Entferne zusätzliche Leerzeichen und Satzzeichen
  author = author.trim().replace(/\s+/g, ' ');
  
  // Prüfen auf spezielle Formatierungen
  if (author.includes(',')) {
    // Format: "Nachname, Vorname"
    const parts = author.split(',');
    return {
      lastName: parts[0].trim(),
      firstName: parts.slice(1).join(',').trim()
    };
  }
  
  // Standard-Parsing: Letztes Wort als Nachname, Rest als Vorname
  const parts = author.split(' ');
  
  if (parts.length === 1) {
    return {
      firstName: '',
      lastName: parts[0]
    };
  }
  
  return {
    lastName: parts.pop(),
    firstName: parts.join(' ')
  };
}

/**
 * Generate EndNote XML citation
 * @param {Object} data Publication data
 * @returns {string} EndNote XML citation
 */
function generateEndNoteXML(data) {
  // Vollständige EndNote XML-Implementierung
  return `<?xml version="1.0" encoding="UTF-8"?>
<xml>
  <records>
    <record>
      <ref-type name="Journal Article">17</ref-type>
      <contributors>
        <authors>
          ${data.authors.map(author => `<author>${author}</author>`).join('\n          ')}
        </authors>
      </contributors>
      <titles>
        <title>${escapeXml(data.title)}</title>
        <secondary-title>${escapeXml(data.journal || '')}</secondary-title>
      </titles>
      <periodical>
        <full-title>${escapeXml(data.journal || '')}</full-title>
      </periodical>
      <dates>
        <year>${data.year}</year>
      </dates>
      <urls>
        <related-urls>
          <url>https://doi.org/${data.doi}</url>
        </related-urls>
      </urls>
      <electronic-resource-num>${data.doi}</electronic-resource-num>
    </record>
  </records>
</xml>`;
}

/**
 * Escape XML special characters
 * @param {string} str Input string
 * @returns {string} XML escaped string
 */
function escapeXml(str) {
  if (!str) return '';
  return str
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&apos;');
}

/**
 * Generate BibTeX citation
 * @param {Object} data Publication data
 * @returns {string} BibTeX citation
 */
function generateBibTeX(data) {
  // Namen korrekt für BibTeX formatieren
  const formatAuthors = authors => {
    return authors.map(author => {
      const { firstName, lastName } = parseAuthorName(author);
      return `${lastName}, ${firstName}`;
    }).join(' and ');
  };
  
  // Generiere einen BibTeX-Key auf Basis des Titels, ersten Autors und Jahres
  const generateKey = () => {
    if (data.id) return data.id;
    
    const firstAuthor = data.authors.length > 0 ? data.authors[0] : '';
    const { lastName } = parseAuthorName(firstAuthor);
    const firstWord = (data.title || '').split(' ')[0].toLowerCase();
    const sanitizedLastName = lastName.toLowerCase().replace(/[^a-z0-9]/g, '');
    
    return `${sanitizedLastName}${data.year}${firstWord}`;
  };
  
  return `@article{${generateKey()},
  title = {${data.title.replace(/}/g, '\\}').replace(/{/g, '\\{')}},
  author = {${formatAuthors(data.authors)}},
  journal = {${(data.journal || '').replace(/}/g, '\\}').replace(/{/g, '\\{')}},
  year = {${data.year}},
  doi = {${data.doi}}${data.volume ? `,\n  volume = {${data.volume}}` : ''}${data.number ? `,\n  number = {${data.number}}` : ''}${data.pages ? `,\n  pages = {${data.pages}}` : ''}
}`;
}

/**
 * Generate RIS citation
 * @param {Object} data Publication data
 * @returns {string} RIS citation
 */
function generateRIS(data) {
  // Korrekt formatierte RIS-Zitation
  const formatAuthors = authors => {
    return authors.map(author => {
      const { firstName, lastName } = parseAuthorName(author);
      return `AU  - ${lastName}, ${firstName}`;
    }).join('\n');
  };
  
  return `TY  - JOUR
TI  - ${data.title}
${formatAuthors(data.authors)}
JO  - ${data.journal || ''}
PY  - ${data.year}
DO  - ${data.doi}
UR  - https://doi.org/${data.doi}${data.volume ? `\nVL  - ${data.volume}` : ''}${data.number ? `\nIS  - ${data.number}` : ''}${data.pages ? `\nSP  - ${data.pages.split('-')[0]}\nEP  - ${data.pages.split('-')[1] || data.pages.split('-')[0]}` : ''}
ER  - `;
}

/**
 * Generate MLA citation
 * @param {Object} data Publication data
 * @returns {string} MLA citation
 */
function generateMLA(data) {
  // MLA 8. Edition-Formatierung
  const formatAuthors = authors => {
    if (authors.length === 0) {
      return 'N/A';
    }
    
    if (authors.length === 1) {
      const { firstName, lastName } = parseAuthorName(authors[0]);
      return `${lastName}, ${firstName}`;
    } else if (authors.length === 2) {
      const { firstName: firstName1, lastName: lastName1 } = parseAuthorName(authors[0]);
      const { firstName: firstName2, lastName: lastName2 } = parseAuthorName(authors[1]);
      return `${lastName1}, ${firstName1}, und ${firstName2} ${lastName2}`;
    } else {
      const { firstName, lastName } = parseAuthorName(authors[0]);
      return `${lastName}, ${firstName}, et al.`;
    }
  };
  
  return `${formatAuthors(data.authors)}. "${data.title}." ${data.journal ? `${data.journal}, ` : ''}${data.volume ? `Bd. ${data.volume}, ` : ''}${data.number ? `Nr. ${data.number}, ` : ''}${data.year}, ${data.pages ? `S. ${data.pages}, ` : ''}doi:${data.doi}.`;
}

/**
 * Generate APA citation
 * @param {Object} data Publication data
 * @returns {string} APA citation
 */
function generateAPA(data) {
  // APA 7. Edition-Formatierung
  const formatAuthors = authors => {
    if (authors.length === 0) {
      return 'N/A';
    }
    
    return authors.map((author, index) => {
      const { firstName, lastName } = parseAuthorName(author);
      const initials = firstName.split(' ').map(n => n.charAt(0).toUpperCase() + '.').join(' ');
      
      if (index === authors.length - 1 && index > 0) {
        return `& ${lastName}, ${initials}`;
      } else if (authors.length > 1 && index === authors.length - 2) {
        return `${lastName}, ${initials}`;
    } else {
        return `${lastName}, ${initials},`;
      }
    }).join(' ');
  };
  
  // Bei mehr als 20 Autoren sollten nur die ersten 19 und der letzte angezeigt werden
  let processedAuthors = [...data.authors];
  if (processedAuthors.length > 20) {
    processedAuthors = [
      ...processedAuthors.slice(0, 19),
      '...',
      processedAuthors[processedAuthors.length - 1]
    ];
  }
  
  return `${formatAuthors(processedAuthors)} (${data.year}). ${data.title}. ${data.journal ? `${data.journal}` : ''}${data.volume ? `, ${data.volume}` : ''}${data.number ? `(${data.number})` : ''}${data.pages ? `, ${data.pages}` : ''}. https://doi.org/${data.doi}`;
}

/**
 * Generate ISO 690 citation
 * @param {Object} data Publication data
 * @returns {string} ISO 690 citation
 */
function generateISO690(data) {
  // ISO 690-Formatierung
  const formatAuthors = authors => {
    return authors.map(author => {
      const { firstName, lastName } = parseAuthorName(author);
      return `${lastName.toUpperCase()}, ${firstName}`;
    }).join('; ');
  };
  
  return `${formatAuthors(data.authors)}. ${data.title}. ${data.journal ? `${data.journal}. ` : ''}${data.year}${data.volume ? `, ${data.volume}` : ''}${data.number ? `(${data.number})` : ''}${data.pages ? `: ${data.pages}` : ''}. ISSN ${data.issn || 'N/A'}. DOI ${data.doi}.`;
}
