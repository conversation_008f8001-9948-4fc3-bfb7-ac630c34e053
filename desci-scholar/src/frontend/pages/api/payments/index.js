/**
 * API routes for payment processing
 */

// In einer Produktionsumgebung würde dies durch eine echte Datenbankanbindung ersetzt
const paymentRecords = new Map();

/**
 * Main payment API handler
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
export default function handler(req, res) {
  const { method } = req;

  switch (method) {
    case 'GET':
      // GET request for payment information
      return getPaymentInfo(req, res);
    case 'POST':
      // POST request to create a payment
      return createPayment(req, res);
    default:
      res.setHeader('Allow', ['GET', 'POST']);
      res.status(405).json({ error: `Methode ${method} nicht erlaubt`, success: false });
  }
}

/**
 * Validiert Anfrageparameter
 * @param {Object} params - Parameter zur Validierung
 * @param {Array} required - Liste der erforderlichen Parameter
 * @returns {Object} Validierungsergebnis mit Status und Fehlermeldung
 */
function validateParams(params, required) {
  const missingParams = required.filter(param => !params[param]);
  
  if (missingParams.length > 0) {
    return {
      valid: false,
      error: `Fehlende erforderliche Parameter: ${missingParams.join(', ')}`
    };
  }
  
  return { valid: true };
}

/**
 * Generiert eine eindeutige Zahlungs-ID
 * @returns {string} Eindeutige Zahlungs-ID
 */
function generatePaymentId() {
  return 'pay_' + Date.now() + '_' + Math.random().toString(36).substring(2, 9);
}

/**
 * Get payment information
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
async function getPaymentInfo(req, res) {
  try {
    const { id } = req.query;
    
    // Validiere Parameter
    const validation = validateParams({ id }, ['id']);
    if (!validation.valid) {
      return res.status(400).json({ error: validation.error, success: false });
    }
    
    // Suche nach der Zahlung in der temporären Speicherung
    if (paymentRecords.has(id)) {
      const paymentRecord = paymentRecords.get(id);
      return res.status(200).json({ ...paymentRecord, success: true });
    }
    
    // In einer echten Implementierung würde hier die Datenbankabfrage stehen
    // Dies ist eine vereinfachte Implementierung für Demonstrationszwecke
    
    // Wenn keine passende Zahlung gefunden wurde
    res.status(404).json({ 
      error: `Zahlung mit ID ${id} nicht gefunden`, 
      success: false 
    });
  } catch (error) {
    console.error('Fehler beim Abrufen der Zahlungsinformationen:', error);
    res.status(500).json({ 
      error: 'Fehler beim Abrufen der Zahlungsinformationen', 
      success: false,
      message: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}

/**
 * Create a new payment
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
async function createPayment(req, res) {
  try {
    const { method, amount, currency, description } = req.body;
    
    // Validiere Parameter
    const validation = validateParams(
      { method, amount, currency, description },
      ['method', 'amount', 'currency']
    );
    
    if (!validation.valid) {
      return res.status(400).json({ error: validation.error, success: false });
    }
    
    // Validiere den Betrag
    if (isNaN(parseFloat(amount)) || parseFloat(amount) <= 0) {
      return res.status(400).json({ 
        error: 'Der Betrag muss eine positive Zahl sein', 
        success: false 
      });
    }
    
    // Validiere die Währung
    const supportedCurrencies = ['USD', 'EUR', 'DOT', 'KSM'];
    if (!supportedCurrencies.includes(currency)) {
      return res.status(400).json({
        error: `Nicht unterstützte Währung: ${currency}. Unterstützte Währungen: ${supportedCurrencies.join(', ')}`,
        success: false
      });
    }
    
    // Validiere die Zahlungsmethode
    const supportedMethods = ['polkadot', 'credit-card', 'bank-transfer'];
    if (!supportedMethods.includes(method)) {
      return res.status(400).json({
        error: `Nicht unterstützte Zahlungsmethode: ${method}. Unterstützte Methoden: ${supportedMethods.join(', ')}`,
        success: false
      });
    }
    
    // Generiere eine eindeutige Zahlungs-ID
    const paymentId = generatePaymentId();
    
    // Erstelle ein Zahlungsobjekt
    const newPayment = {
      paymentId,
      method,
      amount: parseFloat(amount),
      currency,
      description: description || 'DeSci-Scholar Zahlung',
      status: 'pending',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    // Speichere die Zahlung in der temporären Speicherung
    paymentRecords.set(paymentId, newPayment);
    
    // In einer echten Implementierung würde dies in eine Datenbank gespeichert
    // const paymentProcessor = new PaymentProcessor();
    // await paymentProcessor.init();
    // const result = await paymentProcessor.createPayment(newPayment);
    
    // Je nach Zahlungsmethode unterschiedliche Aktionen ausführen
    let paymentResponse;
    
    if (method === 'polkadot') {
      // Spezifische Aktionen für Polkadot-Zahlungen
      paymentResponse = {
        ...newPayment,
        nextStep: 'connect-wallet',
        redirectUrl: `/payments/polkadot/${paymentId}`
      };
    } else if (method === 'credit-card') {
      // Spezifische Aktionen für Kreditkartenzahlungen
      paymentResponse = {
        ...newPayment,
        nextStep: 'credit-card-form',
        redirectUrl: `/payments/credit-card/${paymentId}`
      };
    } else if (method === 'bank-transfer') {
      // Spezifische Aktionen für Banküberweisungen
      paymentResponse = {
        ...newPayment,
        nextStep: 'bank-transfer-details',
        redirectUrl: `/payments/bank-transfer/${paymentId}`,
        bankDetails: {
          accountHolder: 'DeSci Scholar GmbH',
          iban: '**********************',
          bic: 'COBADEFFXXX',
          reference: paymentId
        }
      };
    }
    
    res.status(201).json({ 
      ...paymentResponse, 
      success: true,
      message: 'Zahlung erfolgreich erstellt' 
    });
  } catch (error) {
    console.error('Fehler beim Erstellen der Zahlung:', error);
    res.status(500).json({ 
      error: 'Fehler beim Erstellen der Zahlung', 
      success: false,
      message: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}

/**
 * Update payment status
 * @param {string} paymentId - Payment ID
 * @param {string} status - New status
 * @returns {boolean} Success indicator
 */
export async function updatePaymentStatus(paymentId, status) {
  try {
    // Prüfe, ob die Zahlung existiert
    if (!paymentRecords.has(paymentId)) {
      return false;
    }
    
    // Hole die aktuelle Zahlung
    const payment = paymentRecords.get(paymentId);
    
    // Aktualisiere den Status
    payment.status = status;
    payment.updatedAt = new Date().toISOString();
    
    // Speichere die aktualisierte Zahlung
    paymentRecords.set(paymentId, payment);
    
    // In einer echten Implementierung würde dies in eine Datenbank gespeichert
    // const paymentProcessor = new PaymentProcessor();
    // await paymentProcessor.init();
    // return await paymentProcessor.updatePaymentStatus(paymentId, status);
    
    return true;
  } catch (error) {
    console.error('Fehler beim Aktualisieren des Zahlungsstatus:', error);
    return false;
  }
}
