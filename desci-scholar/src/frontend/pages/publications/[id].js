import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import styles from '../../styles/Publication.module.css';
import CitationStyles from '../../components/CitationStyles';

// Mock data for publications
const publicationsData = [
  {
    id: 'pub-1',
    title: 'Decentralized Science: The Future of Research',
    authors: ['Dr. <PERSON>', 'Prof. <PERSON>'],
    journal: 'Blockchain & Science',
    year: 2025,
    date: '2025-01-15',
    doi: '10.1234/desci.2025.001',
    abstract: 'This paper explores how blockchain technologies are transforming scientific research and publishing. We discuss the benefits of decentralization for scientific integrity, reproducibility, and access to knowledge. Furthermore, we propose a framework for implementing decentralized science platforms that integrate with existing academic infrastructures.',
    keywords: ['blockchain', 'decentralized science', 'scientific publishing', 'open access'],
    fulltext_url: 'https://example.com/papers/decentralized-science.pdf',
    ipfs_hash: 'Qm1a2b3c4d5e6f7g8h9i0j1k2l3m4n5o6p7q8r9s0t1u2v3w4x5y6z',
    funding: 'This research was supported by the Decentralized Science Foundation, Grant #DSF-2024-1234.'
  },
  {
    id: 'pub-2',
    title: 'Zero-Knowledge Proofs in Scientific Data Sharing',
    authors: ['Dr. Carlos Rodriguez', 'Dr. Diana Chen'],
    journal: 'Cryptography Today',
    year: 2024,
    date: '2024-11-20',
    doi: '10.5678/crypto.2024.042',
    abstract: 'A novel approach to sharing sensitive research data while preserving privacy through zero-knowledge proofs. This paper presents a framework that allows researchers to verify the validity of analyses on sensitive data without revealing the underlying data itself. We demonstrate the application of this approach in medical research, where patient privacy is paramount.',
    keywords: ['zero-knowledge proofs', 'privacy', 'data sharing', 'medical research'],
    fulltext_url: 'https://example.com/papers/zk-proofs-data-sharing.pdf',
    ipfs_hash: 'Qm9z8y7x6w5v4u3t2s1r0q9p8o7n6m5l4k3j2h1g0f9e8d7c6b5a',
    funding: 'Funded by the Privacy in Research Consortium, Grant #PRC-2024-0987.'
  },
  {
    id: 'pub-3',
    title: 'AI-Driven Analysis of Climate Research',
    authors: ['Prof. Emma Wilson', 'Dr. Frank Davis'],
    journal: 'Environmental Data Science',
    year: 2024,
    date: '2024-12-05',
    doi: '10.9101/envds.2024.112',
    abstract: 'Leveraging artificial intelligence to identify patterns in climate research data. This study uses deep learning techniques to analyze global climate datasets, identifying previously unrecognized correlations between climate variables. Our approach demonstrates how AI can accelerate climate science and contribute to more accurate climate change predictions.',
    keywords: ['artificial intelligence', 'climate research', 'deep learning', 'data analysis'],
    fulltext_url: 'https://example.com/papers/ai-climate-analysis.pdf',
    ipfs_hash: 'Qm1q2w3e4r5t6y7u8i9o0p1a2s3d4f5g6h7j8k9l0z1x2c3v4b',
    funding: 'This work was supported by the Climate Innovation Fund, Grant #CIF-2024-5678.'
  }
];

export default function PublicationDetail() {
  const router = useRouter();
  const { id } = router.query;
  const [publication, setPublication] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!id) return;

    // In a real app, this would be an API call
    const fetchPublication = async () => {
      try {
        // Simulate API call with a delay
        await new Promise(resolve => setTimeout(resolve, 500));
        
        const pub = publicationsData.find(p => p.id === id);
        if (pub) {
          setPublication(pub);
        } else {
          console.error('Publication not found');
        }
        setLoading(false);
      } catch (error) {
        console.error('Error fetching publication:', error);
        setLoading(false);
      }
    };

    fetchPublication();
  }, [id]);

  if (loading) return <div className={styles.loading}>Loading publication details...</div>;
  if (!publication) return <div className={styles.error}>Publication not found</div>;

  return (
    <div className={styles.container}>
      <Head>
        <title>{publication.title} | DeSci-Scholar</title>
        <meta name="description" content={publication.abstract.substring(0, 160)} />
      </Head>

      <main className={styles.main}>
        <div className={styles.backLink}>
          <Link href="/publications">← Back to Publications</Link>
        </div>

        <article className={styles.publication}>
          <h1 className={styles.title}>{publication.title}</h1>
          
          <div className={styles.metadata}>
            <p className={styles.authors}>{publication.authors.join(', ')}</p>
            <p className={styles.journalInfo}>
              <span className={styles.journal}>{publication.journal}</span> • 
              <span className={styles.date}>{publication.date}</span>
            </p>
            <p className={styles.doi}>DOI: <a href={`https://doi.org/${publication.doi}`} target="_blank" rel="noopener noreferrer">{publication.doi}</a></p>
          </div>

          <div className={styles.abstract}>
            <h2>Abstract</h2>
            <p>{publication.abstract}</p>
          </div>

          <div className={styles.keywords}>
            <h2>Keywords</h2>
            <ul>
              {publication.keywords.map((keyword, index) => (
                <li key={index}>{keyword}</li>
              ))}
            </ul>
          </div>

          <div className={styles.links}>
            <h2>Access</h2>
            <div className={styles.accessLinks}>
              <a href={publication.fulltext_url} target="_blank" rel="noopener noreferrer" className={styles.accessButton}>
                Full Text (PDF)
              </a>
              <a href={`https://ipfs.io/ipfs/${publication.ipfs_hash}`} target="_blank" rel="noopener noreferrer" className={styles.accessButton}>
                View on IPFS
              </a>
            </div>
          </div>

          <div className={styles.funding}>
            <h2>Funding</h2>
            <p>{publication.funding}</p>
          </div>

          {/* Citation Component */}
          <CitationStyles publicationData={publication} />
        </article>
      </main>
    </div>
  );
}
