import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import styles from '../../styles/Publications.module.css';

// Same mock data used in [id].js
const publicationsData = [
  {
    id: 'pub-1',
    title: 'Decentralized Science: The Future of Research',
    authors: ['Dr. <PERSON>', 'Prof. <PERSON>'],
    journal: 'Blockchain & Science',
    year: 2025,
    date: '2025-01-15',
    doi: '10.1234/desci.2025.001',
    abstract: 'This paper explores how blockchain technologies are transforming scientific research...'
  },
  {
    id: 'pub-2',
    title: 'Zero-Knowledge Proofs in Scientific Data Sharing',
    authors: ['Dr. <PERSON>', 'Dr. <PERSON>'],
    journal: 'Cryptography Today',
    year: 2024,
    date: '2024-11-20',
    doi: '10.5678/crypto.2024.042',
    abstract: 'A novel approach to sharing sensitive research data while preserving privacy...'
  },
  {
    id: 'pub-3',
    title: 'AI-Driven Analysis of Climate Research',
    authors: ['Prof<PERSON> <PERSON>', 'Dr. <PERSON>'],
    journal: 'Environmental Data Science',
    year: 2024,
    date: '2024-12-05',
    doi: '10.9101/envds.2024.112',
    abstract: 'Leveraging artificial intelligence to identify patterns in climate research data...'
  },
  {
    id: 'pub-4',
    title: 'Blockchain-Based Peer Review: A New Paradigm',
    authors: ['Dr. George Brown', 'Prof. Hannah Kim'],
    journal: 'Journal of Scientific Workflows',
    year: 2024,
    date: '2024-10-18',
    doi: '10.2468/jsw.2024.023',
    abstract: 'This paper presents a blockchain-based system for transparent and immutable peer review...'
  },
  {
    id: 'pub-5',
    title: 'Decentralized Identity for Researchers: Challenges and Solutions',
    authors: ['Dr. Igor Petrov', 'Dr. Julia Martinez'],
    journal: 'Digital Identity Research',
    year: 2024,
    date: '2024-09-22',
    doi: '10.3333/dir.2024.076',
    abstract: 'An exploration of the challenges and potential solutions for implementing decentralized identity...'
  },
  {
    id: 'pub-6',
    title: 'IPFS as a Solution for Long-term Scientific Data Storage',
    authors: ['Prof. Kevin Wong', 'Dr. Leila Hasan'],
    journal: 'Scientific Data Management',
    year: 2024,
    date: '2024-08-10',
    doi: '10.7890/sdm.2024.098',
    abstract: 'This study evaluates the effectiveness of IPFS as a solution for long-term scientific data storage...'
  }
];

export default function Publications() {
  const [publications, setPublications] = useState([]);
  const [filteredPublications, setFilteredPublications] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // In a real app, this would be an API call
    const fetchPublications = async () => {
      try {
        // Simulate API call with a delay
        await new Promise(resolve => setTimeout(resolve, 500));
        setPublications(publicationsData);
        setFilteredPublications(publicationsData);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching publications:', error);
        setLoading(false);
      }
    };

    fetchPublications();
  }, []);

  // Handle search and filtering
  const handleSearch = (e) => {
    const term = e.target.value.toLowerCase();
    setSearchTerm(term);
    
    if (term.trim() === '') {
      setFilteredPublications(publications);
      return;
    }
    
    const filtered = publications.filter(pub => 
      pub.title.toLowerCase().includes(term) ||
      pub.abstract.toLowerCase().includes(term) ||
      pub.authors.some(author => author.toLowerCase().includes(term)) ||
      pub.journal.toLowerCase().includes(term) ||
      pub.doi.toLowerCase().includes(term)
    );
    
    setFilteredPublications(filtered);
  };

  return (
    <div className={styles.container}>
      <Head>
        <title>Publications | DeSci-Scholar</title>
        <meta name="description" content="Browse all scientific publications on DeSci-Scholar" />
      </Head>

      <main className={styles.main}>
        <h1 className={styles.title}>Scientific Publications</h1>
        
        <div className={styles.searchContainer}>
          <input
            type="text"
            placeholder="Search publications by title, author, keyword..."
            value={searchTerm}
            onChange={handleSearch}
            className={styles.searchInput}
          />
        </div>

        {loading ? (
          <div className={styles.loading}>Loading publications...</div>
        ) : (
          <>
            <p className={styles.results}>
              Showing {filteredPublications.length} of {publications.length} publications
            </p>

            <div className={styles.publicationGrid}>
              {filteredPublications.map((pub) => (
                <div key={pub.id} className={styles.publicationCard}>
                  <Link href={`/publications/${pub.id}`}>
                    <a className={styles.publicationLink}>
                      <h2 className={styles.publicationTitle}>{pub.title}</h2>
                    </a>
                  </Link>
                  <p className={styles.authors}>{pub.authors.join(', ')}</p>
                  <p className={styles.journalInfo}>
                    <span className={styles.journal}>{pub.journal}</span> • 
                    <span className={styles.date}>{pub.date}</span>
                  </p>
                  <p className={styles.doi}>DOI: <a href={`https://doi.org/${pub.doi}`} target="_blank" rel="noopener noreferrer">{pub.doi}</a></p>
                  <p className={styles.abstract}>{pub.abstract}</p>
                  <Link href={`/publications/${pub.id}`}>
                    <a className={styles.viewButton}>View Details</a>
                  </Link>
                </div>
              ))}
            </div>
          </>
        )}
      </main>
    </div>
  );
}
