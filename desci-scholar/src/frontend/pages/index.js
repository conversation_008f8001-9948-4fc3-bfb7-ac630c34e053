import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import styles from '../styles/Home.module.css';

export default function Home() {
  const [isConnected, setIsConnected] = useState(false);
  const [walletAddress, setWalletAddress] = useState('');
  const [featuredPublications, setFeaturedPublications] = useState([]);
  const [featuredPatents, setFeaturedPatents] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check if wallet is connected
    const checkConnection = async () => {
      if (window.ethereum) {
        try {
          // Request account access
          const accounts = await window.ethereum.request({ method: 'eth_accounts' });
          if (accounts.length > 0) {
            setIsConnected(true);
            setWalletAddress(accounts[0]);
          }
        } catch (error) {
          console.error('Error connecting to wallet:', error);
        }
      }
    };

    // Load featured content
    const loadFeaturedContent = async () => {
      try {
        // In a real implementation, these would be API calls
        const publicationsResponse = await Promise.resolve([
          {
            id: 'pub-1',
            title: 'Decentralized Science: The Future of Research',
            authors: ['Dr. Alice Johnson', 'Prof. Bob Smith'],
            journal: 'Blockchain & Science',
            date: '2025-01-15',
            doi: '10.1234/desci.2025.001',
            abstract: 'This paper explores how blockchain technologies are transforming scientific research...'
          },
          {
            id: 'pub-2',
            title: 'Zero-Knowledge Proofs in Scientific Data Sharing',
            authors: ['Dr. Carlos Rodriguez', 'Dr. Diana Chen'],
            journal: 'Cryptography Today',
            date: '2024-11-20',
            doi: '10.5678/crypto.2024.042',
            abstract: 'A novel approach to sharing sensitive research data while preserving privacy...'
          },
          {
            id: 'pub-3',
            title: 'AI-Driven Analysis of Climate Research',
            authors: ['Prof. Emma Wilson', 'Dr. Frank Davis'],
            journal: 'Environmental Data Science',
            date: '2024-12-05',
            doi: '10.9101/envds.2024.112',
            abstract: 'Leveraging artificial intelligence to identify patterns in climate research data...'
          }
        ]);

        const patentsResponse = await Promise.resolve([
          {
            id: 'pat-1',
            title: 'Method for Secure Decentralized Identity in Research',
            inventors: ['Grace Brown', 'Henry Miller'],
            patentNumber: 'US11234567',
            filingDate: '2023-09-10',
            issuedDate: '2024-08-15',
            abstract: 'A method and system for creating and managing decentralized identities for researchers...'
          },
          {
            id: 'pat-2',
            title: 'System for Blockchain-Based Peer Review',
            inventors: ['Isabel Garcia', 'Jack Thompson'],
            patentNumber: 'US11345678',
            filingDate: '2023-10-22',
            issuedDate: '2024-09-30',
            abstract: 'A system that leverages blockchain technology to create transparent and immutable peer review records...'
          },
          {
            id: 'pat-3',
            title: 'Apparatus for IPFS-Based Scientific Data Storage',
            inventors: ['Kevin Lewis', 'Laura Martinez'],
            patentNumber: 'US11456789',
            filingDate: '2023-11-15',
            issuedDate: '2024-10-18',
            abstract: 'An apparatus for efficiently storing and retrieving large scientific datasets using IPFS...'
          }
        ]);

        setFeaturedPublications(publicationsResponse);
        setFeaturedPatents(patentsResponse);
        setLoading(false);
      } catch (error) {
        console.error('Error loading featured content:', error);
        setLoading(false);
      }
    };

    checkConnection();
    loadFeaturedContent();
  }, []);

  // Connect wallet function
  const connectWallet = async () => {
    if (window.ethereum) {
      try {
        // Request account access
        const accounts = await window.ethereum.request({ method: 'eth_requestAccounts' });
        setIsConnected(true);
        setWalletAddress(accounts[0]);
      } catch (error) {
        console.error('Error connecting to wallet:', error);
      }
    } else {
      alert('Please install MetaMask or another Ethereum wallet to use this feature.');
    }
  };

  return (
    <div className={styles.container}>
      <Head>
        <title>DeSci-Scholar | Decentralized Scientific Knowledge Platform</title>
        <meta name="description" content="Revolutionizing scientific knowledge and patent management with blockchain and AI" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <header className={styles.header}>
        <div className={styles.logo}>
          <img src="/logo.svg" alt="DeSci-Scholar Logo" height={40} />
          <h1>DeSci-Scholar</h1>
        </div>
        <nav className={styles.nav}>
          <Link href="/">Home</Link>
          <Link href="/publications">Publications</Link>
          <Link href="/patents">Patents</Link>
          <Link href="/datasets">Datasets</Link>
          <Link href="/about">About</Link>
        </nav>
        <div className={styles.wallet}>
          {isConnected ? (
            <div className={styles.connected}>
              <span className={styles.dot}></span>
              <span className={styles.address}>
                {walletAddress.slice(0, 6)}...{walletAddress.slice(-4)}
              </span>
            </div>
          ) : (
            <button className={styles.connectButton} onClick={connectWallet}>
              Connect Wallet
            </button>
          )}
        </div>
      </header>

      <main className={styles.main}>
        <section className={styles.hero}>
          <div className={styles.heroContent}>
            <h1>Revolutionizing Scientific Knowledge Management</h1>
            <p>
              DeSci-Scholar integrates blockchain, decentralized storage, and AI to create a
              transparent, efficient, and secure ecosystem for scientific publications and patents.
            </p>
            <div className={styles.heroCta}>
              <Link href="/publications/submit" className={styles.primaryButton}>
                Submit Research
              </Link>
              <Link href="/explore" className={styles.secondaryButton}>
                Explore Platform
              </Link>
            </div>
          </div>
          <div className={styles.heroImage}>
            <img src="/hero-image.svg" alt="DeSci-Scholar Platform Illustration" />
          </div>
        </section>

        <section className={styles.features}>
          <h2>Core Features</h2>
          <div className={styles.featureGrid}>
            <div className={styles.featureCard}>
              <div className={styles.featureIcon}>🔗</div>
              <h3>Blockchain Verification</h3>
              <p>Secure and immutable record of scientific contributions and patent ownership using Polkadot.</p>
            </div>
            <div className={styles.featureCard}>
              <div className={styles.featureIcon}>📊</div>
              <h3>Decentralized Storage</h3>
              <p>Reliable and distributed storage of research data and documents using IPFS and BitTorrent.</p>
            </div>
            <div className={styles.featureCard}>
              <div className={styles.featureIcon}>🤖</div>
              <h3>AI Integration</h3>
              <p>Smart agents for peer review, proposal evaluation, and intelligent collaboration matchmaking.</p>
            </div>
            <div className={styles.featureCard}>
              <div className={styles.featureIcon}>📝</div>
              <h3>Smart Contract Licensing</h3>
              <p>Automated execution of patent licensing agreements with transparent royalty tracking.</p>
            </div>
            <div className={styles.featureCard}>
              <div className={styles.featureIcon}>🔍</div>
              <h3>Citation Analysis</h3>
              <p>Advanced AI-powered analysis to measure academic and technological influence.</p>
            </div>
            <div className={styles.featureCard}>
              <div className={styles.featureIcon}>🔒</div>
              <h3>Decentralized Identity</h3>
              <p>Secure researcher authentication and selective disclosure using zero-knowledge proofs.</p>
            </div>
          </div>
        </section>

        <section className={styles.featured}>
          <div className={styles.featuredPublications}>
            <div className={styles.sectionHeader}>
              <h2>Featured Publications</h2>
              <Link href="/publications" className={styles.viewAllLink}>
                View All
              </Link>
            </div>
            {loading ? (
              <div className={styles.loading}>Loading publications...</div>
            ) : (
              <div className={styles.publicationGrid}>
                {featuredPublications.map((pub) => (
                  <div key={pub.id} className={styles.publicationCard}>
                    <h3>{pub.title}</h3>
                    <p className={styles.authors}>{pub.authors.join(', ')}</p>
                    <p className={styles.journal}>{pub.journal} • {pub.date}</p>
                    <p className={styles.doi}>DOI: {pub.doi}</p>
                    <p className={styles.abstract}>{pub.abstract}</p>
                    <Link href={`/publications/detail/${pub.id}`} className={styles.cardLink}>
                      View Details
                    </Link>
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className={styles.featuredPatents}>
            <div className={styles.sectionHeader}>
              <h2>Featured Patents</h2>
              <Link href="/patents" className={styles.viewAllLink}>
                View All
              </Link>
            </div>
            {loading ? (
              <div className={styles.loading}>Loading patents...</div>
            ) : (
              <div className={styles.patentGrid}>
                {featuredPatents.map((patent) => (
                  <div key={patent.id} className={styles.patentCard}>
                    <h3>{patent.title}</h3>
                    <p className={styles.inventors}>
                      <strong>Inventors:</strong> {patent.inventors.join(', ')}
                    </p>
                    <p className={styles.patentInfo}>
                      <strong>Patent:</strong> {patent.patentNumber}
                    </p>
                    <p className={styles.dates}>
                      Filed: {patent.filingDate} • Issued: {patent.issuedDate}
                    </p>
                    <p className={styles.abstract}>{patent.abstract}</p>
                    <Link href={`/patents/${patent.id}`} className={styles.cardLink}>
                      View Details
                    </Link>
                  </div>
                ))}
              </div>
            )}
          </div>
        </section>

        <section className={styles.stats}>
          <div className={styles.statCard}>
            <h3>5,000+</h3>
            <p>Scientific Publications</p>
          </div>
          <div className={styles.statCard}>
            <h3>2,500+</h3>
            <p>Registered Patents</p>
          </div>
          <div className={styles.statCard}>
            <h3>10,000+</h3>
            <p>Researchers</p>
          </div>
          <div className={styles.statCard}>
            <h3>1,000+</h3>
            <p>Institutions</p>
          </div>
        </section>

        <section className={styles.partners}>
          <h2>Our Partners</h2>
          <div className={styles.partnerLogos}>
            {/* Partner logos would be displayed here */}
            <div className={styles.partnerLogo}>DataCite</div>
            <div className={styles.partnerLogo}>Crossref</div>
            <div className={styles.partnerLogo}>IPFS</div>
            <div className={styles.partnerLogo}>Polkadot</div>
            <div className={styles.partnerLogo}>ResearchGate</div>
          </div>
        </section>
      </main>

      <footer className={styles.footer}>
        <div className={styles.footerGrid}>
          <div className={styles.footerSection}>
            <h3>DeSci-Scholar</h3>
            <p>
              Revolutionizing the management, authentication, and distribution of scientific knowledge
              and patent information using decentralized technologies and AI.
            </p>
          </div>
          <div className={styles.footerSection}>
            <h3>Quick Links</h3>
            <ul>
              <li><Link href="/">Home</Link></li>
              <li><Link href="/publications">Publications</Link></li>
              <li><Link href="/patents">Patents</Link></li>
              <li><Link href="/datasets">Datasets</Link></li>
              <li><Link href="/about">About</Link></li>
            </ul>
          </div>
          <div className={styles.footerSection}>
            <h3>Resources</h3>
            <ul>
              <li><Link href="/docs">Documentation</Link></li>
              <li><Link href="/api">API</Link></li>
              <li><Link href="/faq">FAQ</Link></li>
              <li><Link href="/tutorials">Tutorials</Link></li>
              <li><Link href="/community">Community</Link></li>
            </ul>
          </div>
          <div className={styles.footerSection}>
            <h3>Contact</h3>
            <p><EMAIL></p>
            <div className={styles.socialLinks}>
              {/* Social media links would go here */}
              <a href="https://twitter.com/desci_scholar" target="_blank" rel="noopener noreferrer">Twitter</a>
              <a href="https://github.com/desci-scholar" target="_blank" rel="noopener noreferrer">GitHub</a>
              <a href="https://discord.gg/desci-scholar" target="_blank" rel="noopener noreferrer">Discord</a>
            </div>
          </div>
        </div>
        <div className={styles.copyright}>
          &copy; {new Date().getFullYear()} DeSci-Scholar. All rights reserved.
        </div>
      </footer>
    </div>
  );
}
