/**
 * StatsPage.css
 * 
 * Styling für die StatsPage-Komponente
 */

.stats-page {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.stats-page-header {
  margin-bottom: 2rem;
  text-align: center;
}

.stats-page-header h1 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary, #212529);
  margin-bottom: 0.5rem;
}

.stats-page-description {
  font-size: 1rem;
  color: var(--text-secondary, #6c757d);
  max-width: 700px;
  margin: 0 auto;
}

.stats-tabs {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 2rem;
  border-bottom: 1px solid var(--border-color, #dee2e6);
  padding-bottom: 1px;
}

.stats-tab {
  padding: 0.75rem 1.25rem;
  font-size: 1rem;
  background: none;
  border: none;
  border-bottom: 3px solid transparent;
  color: var(--text-secondary, #6c757d);
  cursor: pointer;
  transition: all 0.2s ease;
}

.stats-tab:hover {
  color: var(--primary, #007bff);
}

.stats-tab-active {
  color: var(--primary, #007bff);
  border-bottom-color: var(--primary, #007bff);
  font-weight: 500;
}

.stats-content {
  min-height: 500px;
}

.stats-login-required {
  background-color: var(--card-bg, #ffffff);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  padding: 3rem;
  text-align: center;
  margin: 2rem auto;
  max-width: 600px;
}

.stats-login-required h2 {
  margin-bottom: 1rem;
  font-size: 1.5rem;
  color: var(--text-primary, #212529);
}

.stats-login-required p {
  margin-bottom: 1.5rem;
  color: var(--text-secondary, #6c757d);
}

.stats-auth-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.stats-auth-button {
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s, transform 0.1s;
}

.stats-login-button {
  background-color: var(--primary, #007bff);
  color: white;
  border: none;
}

.stats-login-button:hover {
  background-color: var(--primary-dark, #0069d9);
  transform: translateY(-2px);
}

.stats-register-button {
  background-color: white;
  color: var(--primary, #007bff);
  border: 1px solid var(--primary, #007bff);
}

.stats-register-button:hover {
  background-color: var(--primary-light, rgba(0, 123, 255, 0.1));
  transform: translateY(-2px);
}

@media (max-width: 768px) {
  .stats-page {
    padding: 1rem;
  }
  
  .stats-page-header h1 {
    font-size: 1.75rem;
  }
  
  .stats-tabs {
    flex-wrap: wrap;
  }
  
  .stats-tab {
    flex: 1;
    min-width: 120px;
    text-align: center;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
  }
  
  .stats-login-required {
    padding: 2rem 1rem;
  }
  
  .stats-auth-buttons {
    flex-direction: column;
    max-width: 250px;
    margin: 0 auto;
  }
} 