/**
 * StatsPage.js
 * 
 * Seite zur Anzeige des Statistik-Dashboards für DeSci-Scholar
 */

import React, { useState } from 'react';
import StatsDashboard from '../components/stats/StatsDashboard';
import './StatsPage.css';

/**
 * Statistik-Seite, die das StatsDashboard einbindet
 * 
 * @returns {JSX.Element}
 */
const StatsPage = () => {
  const [activeTab, setActiveTab] = useState('platform');
  
  /**
   * Behandelt Klicks auf eine Publikation aus der Trending-Liste
   * 
   * @param {Object} publication Die angeklickte Publikation
   */
  const handlePublicationClick = (publication) => {
    console.log('Publikation angeklickt:', publication);
    // Hier würde die Navigation zur Publikationsdetailseite erfolgen
    // z.B. navigate(`/publications/${publication.id}`);
  };
  
  return (
    <div className="stats-page">
      <header className="stats-page-header">
        <h1>Statistiken</h1>
        <p className="stats-page-description">
          Echtzeit-Einblicke in die Nutzung und Verbreitung wissenschaftlicher Publikationen 
          auf der DeSci-Scholar Plattform.
        </p>
      </header>
      
      <div className="stats-tabs">
        <button 
          className={`stats-tab ${activeTab === 'platform' ? 'stats-tab-active' : ''}`}
          onClick={() => setActiveTab('platform')}
        >
          Plattform-Übersicht
        </button>
        <button 
          className={`stats-tab ${activeTab === 'user' ? 'stats-tab-active' : ''}`}
          onClick={() => setActiveTab('user')}
        >
          Meine Publikationen
        </button>
        <button 
          className={`stats-tab ${activeTab === 'storage' ? 'stats-tab-active' : ''}`}
          onClick={() => setActiveTab('storage')}
        >
          Speichernutzung
        </button>
      </div>
      
      <div className="stats-content">
        {activeTab === 'platform' && (
          <StatsDashboard 
            showTrending={true}
            showDownloads={true}
            showStorage={true}
            defaultTimeRange="30d"
            onPublicationClick={handlePublicationClick}
          />
        )}
        
        {activeTab === 'user' && (
          <div className="stats-login-required">
            <h2>Meine Publikationen</h2>
            <p>
              Um Statistiken zu Ihren eigenen Publikationen einzusehen, 
              müssen Sie sich anmelden oder registrieren.
            </p>
            <div className="stats-auth-buttons">
              <button className="stats-auth-button stats-login-button">Anmelden</button>
              <button className="stats-auth-button stats-register-button">Registrieren</button>
            </div>
            {/* Hier könnte alternativ ein spezielles Dashboard für Benutzerstatistiken eingebunden werden */}
          </div>
        )}
        
        {activeTab === 'storage' && (
          <StatsDashboard 
            showTrending={false}
            showDownloads={false}
            showStorage={true}
            defaultTimeRange="90d"
          />
        )}
      </div>
    </div>
  );
};

export default StatsPage; 