import React, { useState } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import PaymentOptions from '../../components/PaymentOptions';
import styles from '../../styles/PaymentDemo.module.css';
import { useWallet } from '../../context/WalletContext';

/**
 * Demo page for payment functionality
 */
const PaymentDemo = () => {
  const router = useRouter();
  const { wallet, loading } = useWallet();
  const [selectedService, setSelectedService] = useState('premium-access');
  
  // Pricing options
  const services = {
    'premium-access': {
      name: 'Premium Research Access',
      price: 25.00,
      description: 'One month of premium access to all published research papers',
      period: 'month',
    },
    'dataset-download': {
      name: 'Complete Dataset Download',
      price: 49.99,
      description: 'One-time download of the complete research dataset including raw data',
      period: 'one-time',
    },
    'patent-license': {
      name: 'Patent License',
      price: 299.00,
      description: 'Non-exclusive license to use the patented technology for academic purposes',
      period: 'year',
    },
  };
  
  // Handle service selection
  const handleServiceChange = (e) => {
    setSelectedService(e.target.value);
  };
  
  // Handle payment completion
  const handlePaymentComplete = (result) => {
    console.log('Payment completed:', result);
    router.push('/payments/success');
  };
  
  const selectedServiceData = services[selectedService];
  
  return (
    <>
      <Head>
        <title>Payment Demo | DeSci-Scholar</title>
        <meta name="description" content="DeSci-Scholar Payment Integration Demo" />
      </Head>
      
      <div className={styles.container}>
        <header className={styles.header}>
          <h1 className={styles.title}>DeSci-Scholar Payment Demo</h1>
          <p className={styles.subtitle}>
            Demonstration of integrated payment processing capabilities with Polkadot, Stripe, and PayPal
          </p>
          
          {!loading && wallet && (
            <div className={styles.walletBadge}>
              <span className={styles.walletIcon}>
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect x="1" y="3" width="14" height="10" rx="2" stroke="currentColor" strokeWidth="1.5"/>
                  <path d="M10 8H14" stroke="currentColor" strokeWidth="1.5"/>
                  <circle cx="12" cy="8" r="1" fill="currentColor"/>
                </svg>
              </span>
              <span className={styles.walletSource}>{wallet.source}</span>
              <span className={styles.walletAddress}>
                {wallet.address.slice(0, 6)}...{wallet.address.slice(-6)}
              </span>
            </div>
          )}
        </header>
        
        <div className={styles.main}>
          <div className={styles.serviceSelector}>
            <h2>Select a Service</h2>
            
            <div className={styles.radioGroup}>
              {Object.entries(services).map(([id, service]) => (
                <label key={id} className={styles.radioOption}>
                  <input
                    type="radio"
                    name="service"
                    value={id}
                    checked={selectedService === id}
                    onChange={handleServiceChange}
                  />
                  <div className={styles.radioContent}>
                    <div className={styles.serviceName}>
                      {service.name}
                      <span className={styles.servicePeriod}>
                        {service.period === 'one-time' ? 'One-time payment' : `Per ${service.period}`}
                      </span>
                    </div>
                    <div className={styles.servicePrice}>
                      ${service.price.toFixed(2)}
                    </div>
                  </div>
                </label>
              ))}
            </div>
          </div>
          
          <div className={styles.paymentSection}>
            <PaymentOptions
              amount={selectedServiceData.price}
              currency="USD"
              description={selectedServiceData.description}
              onPaymentComplete={handlePaymentComplete}
            />
          </div>
        </div>
        
        <div className={styles.infoSection}>
          <div className={styles.infoItem}>
            <h3>Secure Payments</h3>
            <p>
              All payment information is encrypted and processed securely. 
              DeSci-Scholar never stores your payment details.
            </p>
          </div>
          
          <div className={styles.infoItem}>
            <h3>Blockchain Verification</h3>
            <p>
              All transactions are recorded on the blockchain for transparency 
              and permanent verification of payment history.
            </p>
          </div>
          
          <div className={styles.infoItem}>
            <h3>Multiple Options</h3>
            <p>
              Choose from traditional payment methods or pay directly with 
              cryptocurrency for maximum flexibility.
            </p>
          </div>
        </div>
        
        <footer className={styles.footer}>
          <p>
            This is a demonstration of the DeSci-Scholar payment integration. 
            No actual charges will be processed in this demo environment.
          </p>
        </footer>
      </div>
    </>
  );
};

export default PaymentDemo;
