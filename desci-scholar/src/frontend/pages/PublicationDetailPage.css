/**
 * PublicationDetailPage.css
 * 
 * Puristisches Styling mit minimalen Grautönen für die PublicationDetailPage-Komponente
 */

.publication-detail-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 3rem 1rem;
}

.publication-detail-header {
  margin-bottom: 3rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #dddddd;
}

.publication-detail-header h1 {
  font-size: 2rem;
  font-weight: 500;
  margin-bottom: 1rem;
  color: #000000;
}

.publication-authors {
  font-size: 1.125rem;
  color: #000000;
  margin-bottom: 0.5rem;
}

.publication-meta {
  font-size: 0.875rem;
  color: #666666;
  margin-bottom: 1.5rem;
}

.publication-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.publication-tag {
  background: none;
  color: #000000;
  padding: 0;
  border-left: 2px solid #000000;
  padding-left: 0.5rem;
  font-size: 0.75rem;
}

.publication-detail-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: 3rem;
}

.publication-sidebar {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.publication-info-card {
  background: none;
  padding: 0;
  margin-bottom: 2rem;
}

.publication-info-card h3 {
  font-size: 1.125rem;
  font-weight: 500;
  margin-bottom: 1.5rem;
  color: #000000;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
  font-size: 0.875rem;
  border-bottom: 1px solid #dddddd;
  padding-bottom: 0.5rem;
}

.info-label {
  color: #444444;
  font-weight: 500;
}

.info-value {
  color: #000000;
  max-width: 60%;
  text-align: right;
}

.publication-actions {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.publication-actions button {
  width: 100%;
}

.btn-primary {
  background: none;
  color: #000000;
  border: none;
  border-bottom: 1px solid #000000;
  border-radius: 0;
  padding: 0.5rem 0;
  font-weight: 500;
  cursor: pointer;
  transition: opacity 0.2s;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.btn-primary:hover {
  opacity: 0.7;
}

.btn-secondary {
  background: none;
  color: #000000;
  border: none;
  border-bottom: 1px solid #000000;
  border-radius: 0;
  padding: 0.5rem 0;
  font-weight: 500;
  cursor: pointer;
  transition: opacity 0.2s;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.btn-secondary:hover {
  opacity: 0.7;
}

.btn-secondary.active {
  font-weight: bold;
}

.publication-abstract {
  background: none;
  padding: 0;
  margin-bottom: 3rem;
}

.publication-abstract h2 {
  font-size: 1.25rem;
  font-weight: 500;
  margin-bottom: 1.5rem;
  color: #000000;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.publication-abstract p {
  line-height: 1.7;
  color: #222222;
}

.publication-detail-loading, 
.publication-not-found {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  text-align: center;
}

.loading-spinner {
  border: 1px solid #eeeeee;
  border-top: 1px solid #000000;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
  margin-bottom: 1.5rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.publication-not-found h2 {
  font-size: 1.5rem;
  font-weight: 500;
  margin-bottom: 1rem;
  color: #000000;
}

.publication-not-found p {
  color: #666666;
}

/* Anpassungen für Statistiken */
.publication-stats-section {
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 1px solid #dddddd;
}

.publication-stats-section h2 {
  font-size: 1.25rem;
  font-weight: 500;
  margin-bottom: 1.5rem;
  color: #000000;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

@media (min-width: 768px) {
  .publication-detail-content {
    grid-template-columns: 1fr 2fr;
  }
}

@media (max-width: 768px) {
  .publication-detail-header h1 {
    font-size: 1.75rem;
  }
  
  .publication-sidebar {
    order: 2;
  }
} 