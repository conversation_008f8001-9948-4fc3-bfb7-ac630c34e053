/**
 * PublicationDetailPage.js
 * 
 * Detailseite für eine einzelne wissenschaftliche Publikation
 */

import React, { useState, useEffect, useRef } from 'react';
import PublicationStatsDetail from '../components/stats/PublicationStatsDetail';
import LiteratureExportButton from '../components/publications/LiteratureExportButton';
import { addPublicationMetaTags, addCOinSTag } from '../utils/zotero-integration';
import './PublicationDetailPage.css';

/**
 * PublicationDetailPage Komponente
 * 
 * In einer vollständigen App würde diese Komponente die Publikations-ID
 * aus den URL-Parametern lesen, z.B. mit React Router.
 * 
 * @returns {JSX.Element}
 */
const PublicationDetailPage = () => {
  // Für Demozwecke verwenden wir eine fest codierte Publikations-ID
  const publicationId = 'demo-publication-123';
  
  const [publication, setPublication] = useState(null);
  const [loading, setLoading] = useState(true);
  const [showStats, setShowStats] = useState(false);
  const containerRef = useRef(null);
  
  // Simulierte Publikationsdaten laden
  useEffect(() => {
    // Diese Funktion würde normalerweise die Daten von der API laden
    const fetchPublicationData = async () => {
      setLoading(true);
      
      // Simuliere API-Aufruf mit Timeout
      await new Promise(resolve => setTimeout(resolve, 800));
      
      // Simulierte Publikationsdaten
      const demoPublication = {
        id: publicationId,
        title: 'Dezentrale Speicherlösungen für wissenschaftliche Daten',
        authors: ['Dr. Martina Müller', 'Prof. Thomas Weber'],
        publishedAt: '2023-06-15',
        abstract: 'Diese Arbeit untersucht verschiedene dezentrale Speicherlösungen für wissenschaftliche Daten unter Berücksichtigung von Sicherheit, Verfügbarkeit und Integrität. Es werden IPFS, BitTorrent und verwandte Technologien verglichen und ein neues Hybridmodell vorgeschlagen.',
        keywords: ['Dezentrale Speicherung', 'IPFS', 'BitTorrent', 'Wissenschaftliche Daten', 'Datensicherheit'],
        doi: '10.1234/desci.2023.123456',
        license: 'CC-BY-4.0',
        fileSize: 2.45, // MB
        fileType: 'PDF',
        journal: 'Journal für dezentrale Wissenschaft',
        volume: '4',
        issue: '2',
        pages: '123-145',
        type: 'article' // 'article', 'book', 'conference'
      };
      
      setPublication(demoPublication);
      setLoading(false);
    };
    
    fetchPublicationData();
  }, [publicationId]);
  
  // Meta-Tags für Zotero und andere Literaturverwaltungsprogramme hinzufügen
  useEffect(() => {
    if (publication) {
      // Meta-Tags zum <head> hinzufügen
      addPublicationMetaTags(publication);
      
      // COinS-Tag hinzufügen, sobald der Container verfügbar ist
      if (containerRef.current) {
        addCOinSTag(publication, containerRef.current);
      }
    }
    
    // Cleanup-Funktion
    return () => {
      // Meta-Tags entfernen, wenn Komponente unmounted wird
      document.querySelectorAll('meta[name^="citation_"]').forEach(el => el.remove());
    };
  }, [publication, containerRef.current]);
  
  const toggleStats = () => {
    setShowStats(prev => !prev);
  };
  
  if (loading) {
    return (
      <div className="publication-detail-container">
        <div className="publication-detail-loading">
          <div className="loading-spinner"></div>
          <p>Publikation wird geladen...</p>
        </div>
      </div>
    );
  }
  
  if (!publication) {
    return (
      <div className="publication-detail-container">
        <div className="publication-not-found">
          <h2>Publikation nicht gefunden</h2>
          <p>Die angeforderte Publikation konnte nicht gefunden werden.</p>
        </div>
      </div>
    );
  }
  
  return (
    <div className="publication-detail-container" ref={containerRef}>
      <div className="publication-detail-header">
        <h1>{publication.title}</h1>
        <p className="publication-authors">
          {publication.authors.join(', ')}
        </p>
        <p className="publication-meta">
          Veröffentlicht am {new Date(publication.publishedAt).toLocaleDateString('de-DE')} in {publication.journal}
        </p>
        <div className="publication-tags">
          {publication.keywords.map((keyword, index) => (
            <span key={index} className="publication-tag">{keyword}</span>
          ))}
        </div>
      </div>
      
      <div className="publication-detail-content">
        <div className="publication-sidebar">
          <div className="publication-info-card">
            <h3>Publikationsinformationen</h3>
            <div className="info-item">
              <span className="info-label">DOI:</span>
              <span className="info-value">{publication.doi}</span>
            </div>
            <div className="info-item">
              <span className="info-label">Lizenz:</span>
              <span className="info-value">{publication.license}</span>
            </div>
            <div className="info-item">
              <span className="info-label">Dateityp:</span>
              <span className="info-value">{publication.fileType}</span>
            </div>
            <div className="info-item">
              <span className="info-label">Dateigröße:</span>
              <span className="info-value">{publication.fileSize} MB</span>
            </div>
            <div className="info-item">
              <span className="info-label">Journal:</span>
              <span className="info-value">{publication.journal}</span>
            </div>
            <div className="info-item">
              <span className="info-label">Volume/Issue:</span>
              <span className="info-value">{publication.volume}/{publication.issue}</span>
            </div>
            <div className="info-item">
              <span className="info-label">Seiten:</span>
              <span className="info-value">{publication.pages}</span>
            </div>
          </div>
          
          <div className="publication-actions">
            <button className="btn-primary">Herunterladen</button>
            <LiteratureExportButton 
              publication={publication} 
              label="Zitieren/Exportieren"
            />
            <button className="btn-secondary">Teilen</button>
            <button 
              className={`btn-secondary ${showStats ? 'active' : ''}`}
              onClick={toggleStats}
            >
              {showStats ? 'Statistiken ausblenden' : 'Statistiken anzeigen'}
            </button>
          </div>
        </div>
        
        <div className="publication-main-content">
          <div className="publication-abstract">
            <h2>Abstract</h2>
            <p>{publication.abstract}</p>
          </div>
          
          {showStats && (
            <div className="publication-stats-section">
              <h2>Statistiken</h2>
              <PublicationStatsDetail 
                publicationId={publicationId}
                defaultTimeRange="30d"
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PublicationDetailPage; 