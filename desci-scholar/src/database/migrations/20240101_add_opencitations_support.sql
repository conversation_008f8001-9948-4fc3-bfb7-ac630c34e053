-- Migration zur Unterstützung von OpenCitations in DeSci-Scholar
-- <PERSON><PERSON><PERSON> für OpenCitations-Daten zu verschiedenen Tabellen hinzu

-- Füge eine Spalte für den OCI (Open Citation Identifier) zur citation_relationships-Tabelle hinzu
ALTER TABLE citation_relationships ADD COLUMN IF NOT EXISTS oci VARCHAR(255) COMMENT 'Open Citation Identifier von OpenCitations';

-- Füge eine Spalte für die OpenCitations-Verifizierung zur publication_metrics-Tabelle hinzu
ALTER TABLE publication_metrics ADD COLUMN IF NOT EXISTS opencitations_verified TINYINT(1) DEFAULT 0 COMMENT 'Ob die Metriken durch OpenCitations verifiziert wurden';

-- Füge eine Spalte für die OpenCitations-Verifizierung zur doi_nft_mapping-Tabelle hinzu
ALTER TABLE doi_nft_mapping ADD COLUMN IF NOT EXISTS opencitations_verified TINYINT(1) DEFAULT 0 COMMENT 'Ob das NFT mit OpenCitations-Daten angereichert wurde';

-- Füge eine Spalte für den OCI zur citation_nft_links-Tabelle hinzu
ALTER TABLE citation_nft_links ADD COLUMN IF NOT EXISTS oci VARCHAR(255) COMMENT 'Open Citation Identifier von OpenCitations';

-- Erstelle eine neue Tabelle für die Speicherung von externen Publikationsdaten
CREATE TABLE IF NOT EXISTS publication_external_data (
  id INT AUTO_INCREMENT PRIMARY KEY,
  doi VARCHAR(255) NOT NULL,
  source VARCHAR(50) NOT NULL COMMENT 'Quelle der externen Daten (z.B. opencitations, semanticscholar)',
  data JSON NOT NULL COMMENT 'Externe Daten im JSON-Format',
  created_at DATETIME NOT NULL,
  updated_at DATETIME NOT NULL,
  UNIQUE KEY unique_doi_source (doi, source)
) COMMENT 'Speichert externe Daten zu Publikationen';

-- Erstelle einen Index für die schnelle Suche nach DOIs
CREATE INDEX IF NOT EXISTS idx_publication_external_data_doi ON publication_external_data(doi);

-- Erstelle einen Index für die schnelle Suche nach OCIs in der citation_relationships-Tabelle
CREATE INDEX IF NOT EXISTS idx_citation_relationships_oci ON citation_relationships(oci);

-- Erstelle einen Index für die schnelle Suche nach OCIs in der citation_nft_links-Tabelle
CREATE INDEX IF NOT EXISTS idx_citation_nft_links_oci ON citation_nft_links(oci);-- Migration zur Unterstützung von OpenCitations in DeSci-Scholar
-- Fügt Spalten für OpenCitations-Daten zu verschiedenen Tabellen hinzu

-- Füge eine Spalte für den OCI (Open Citation Identifier) zur citation_relationships-Tabelle hinzu
ALTER TABLE citation_relationships ADD COLUMN IF NOT EXISTS oci VARCHAR(255) COMMENT 'Open Citation Identifier von OpenCitations';

-- Füge eine Spalte für die OpenCitations-Verifizierung zur publication_metrics-Tabelle hinzu
ALTER TABLE publication_metrics ADD COLUMN IF NOT EXISTS opencitations_verified TINYINT(1) DEFAULT 0 COMMENT 'Ob die Metriken durch OpenCitations verifiziert wurden';

-- Füge eine Spalte für die OpenCitations-Verifizierung zur doi_nft_mapping-Tabelle hinzu
ALTER TABLE doi_nft_mapping ADD COLUMN IF NOT EXISTS opencitations_verified TINYINT(1) DEFAULT 0 COMMENT 'Ob das NFT mit OpenCitations-Daten angereichert wurde';

-- Füge eine Spalte für den OCI zur citation_nft_links-Tabelle hinzu
ALTER TABLE citation_nft_links ADD COLUMN IF NOT EXISTS oci VARCHAR(255) COMMENT 'Open Citation Identifier von OpenCitations';

-- Erstelle eine neue Tabelle für die Speicherung von externen Publikationsdaten
CREATE TABLE IF NOT EXISTS publication_external_data (
  id INT AUTO_INCREMENT PRIMARY KEY,
  doi VARCHAR(255) NOT NULL,
  source VARCHAR(50) NOT NULL COMMENT 'Quelle der externen Daten (z.B. opencitations, semanticscholar)',
  data JSON NOT NULL COMMENT 'Externe Daten im JSON-Format',
  created_at DATETIME NOT NULL,
  updated_at DATETIME NOT NULL,
  UNIQUE KEY unique_doi_source (doi, source)
) COMMENT 'Speichert externe Daten zu Publikationen';

-- Erstelle einen Index für die schnelle Suche nach DOIs
CREATE INDEX IF NOT EXISTS idx_publication_external_data_doi ON publication_external_data(doi);

-- Erstelle einen Index für die schnelle Suche nach OCIs in der citation_relationships-Tabelle
CREATE INDEX IF NOT EXISTS idx_citation_relationships_oci ON citation_relationships(oci);

-- Erstelle einen Index für die schnelle Suche nach OCIs in der citation_nft_links-Tabelle
CREATE INDEX IF NOT EXISTS idx_citation_nft_links_oci ON citation_nft_links(oci);-- Migration zur Unterstützung von OpenCitations in DeSci-Scholar
-- Fügt Spalten für OpenCitations-Daten zu verschiedenen Tabellen hinzu

-- Füge eine Spalte für den OCI (Open Citation Identifier) zur citation_relationships-Tabelle hinzu
ALTER TABLE citation_relationships ADD COLUMN IF NOT EXISTS oci VARCHAR(255) COMMENT 'Open Citation Identifier von OpenCitations';

-- Füge eine Spalte für die OpenCitations-Verifizierung zur publication_metrics-Tabelle hinzu
ALTER TABLE publication_metrics ADD COLUMN IF NOT EXISTS opencitations_verified TINYINT(1) DEFAULT 0 COMMENT 'Ob die Metriken durch OpenCitations verifiziert wurden';

-- Füge eine Spalte für die OpenCitations-Verifizierung zur doi_nft_mapping-Tabelle hinzu
ALTER TABLE doi_nft_mapping ADD COLUMN IF NOT EXISTS opencitations_verified TINYINT(1) DEFAULT 0 COMMENT 'Ob das NFT mit OpenCitations-Daten angereichert wurde';

-- Füge eine Spalte für den OCI zur citation_nft_links-Tabelle hinzu
ALTER TABLE citation_nft_links ADD COLUMN IF NOT EXISTS oci VARCHAR(255) COMMENT 'Open Citation Identifier von OpenCitations';

-- Erstelle eine neue Tabelle für die Speicherung von externen Publikationsdaten
CREATE TABLE IF NOT EXISTS publication_external_data (
  id INT AUTO_INCREMENT PRIMARY KEY,
  doi VARCHAR(255) NOT NULL,
  source VARCHAR(50) NOT NULL COMMENT 'Quelle der externen Daten (z.B. opencitations, semanticscholar)',
  data JSON NOT NULL COMMENT 'Externe Daten im JSON-Format',
  created_at DATETIME NOT NULL,
  updated_at DATETIME NOT NULL,
  UNIQUE KEY unique_doi_source (doi, source)
) COMMENT 'Speichert externe Daten zu Publikationen';

-- Erstelle einen Index für die schnelle Suche nach DOIs
CREATE INDEX IF NOT EXISTS idx_publication_external_data_doi ON publication_external_data(doi);

-- Erstelle einen Index für die schnelle Suche nach OCIs in der citation_relationships-Tabelle
CREATE INDEX IF NOT EXISTS idx_citation_relationships_oci ON citation_relationships(oci);

-- Erstelle einen Index für die schnelle Suche nach OCIs in der citation_nft_links-Tabelle
CREATE INDEX IF NOT EXISTS idx_citation_nft_links_oci ON citation_nft_links(oci);-- Migration zur Unterstützung von OpenCitations in DeSci-Scholar
-- Fügt Spalten für OpenCitations-Daten zu verschiedenen Tabellen hinzu

-- Füge eine Spalte für den OCI (Open Citation Identifier) zur citation_relationships-Tabelle hinzu
ALTER TABLE citation_relationships ADD COLUMN IF NOT EXISTS oci VARCHAR(255) COMMENT 'Open Citation Identifier von OpenCitations';

-- Füge eine Spalte für die OpenCitations-Verifizierung zur publication_metrics-Tabelle hinzu
ALTER TABLE publication_metrics ADD COLUMN IF NOT EXISTS opencitations_verified TINYINT(1) DEFAULT 0 COMMENT 'Ob die Metriken durch OpenCitations verifiziert wurden';

-- Füge eine Spalte für die OpenCitations-Verifizierung zur doi_nft_mapping-Tabelle hinzu
ALTER TABLE doi_nft_mapping ADD COLUMN IF NOT EXISTS opencitations_verified TINYINT(1) DEFAULT 0 COMMENT 'Ob das NFT mit OpenCitations-Daten angereichert wurde';

-- Füge eine Spalte für den OCI zur citation_nft_links-Tabelle hinzu
ALTER TABLE citation_nft_links ADD COLUMN IF NOT EXISTS oci VARCHAR(255) COMMENT 'Open Citation Identifier von OpenCitations';

-- Erstelle eine neue Tabelle für die Speicherung von externen Publikationsdaten
CREATE TABLE IF NOT EXISTS publication_external_data (
  id INT AUTO_INCREMENT PRIMARY KEY,
  doi VARCHAR(255) NOT NULL,
  source VARCHAR(50) NOT NULL COMMENT 'Quelle der externen Daten (z.B. opencitations, semanticscholar)',
  data JSON NOT NULL COMMENT 'Externe Daten im JSON-Format',
  created_at DATETIME NOT NULL,
  updated_at DATETIME NOT NULL,
  UNIQUE KEY unique_doi_source (doi, source)
) COMMENT 'Speichert externe Daten zu Publikationen';

-- Erstelle einen Index für die schnelle Suche nach DOIs
CREATE INDEX IF NOT EXISTS idx_publication_external_data_doi ON publication_external_data(doi);

-- Erstelle einen Index für die schnelle Suche nach OCIs in der citation_relationships-Tabelle
CREATE INDEX IF NOT EXISTS idx_citation_relationships_oci ON citation_relationships(oci);

-- Erstelle einen Index für die schnelle Suche nach OCIs in der citation_nft_links-Tabelle
CREATE INDEX IF NOT EXISTS idx_citation_nft_links_oci ON citation_nft_links(oci);-- Migration zur Unterstützung von OpenCitations in DeSci-Scholar
-- Fügt Spalten für OpenCitations-Daten zu verschiedenen Tabellen hinzu

-- Füge eine Spalte für den OCI (Open Citation Identifier) zur citation_relationships-Tabelle hinzu
ALTER TABLE citation_relationships ADD COLUMN IF NOT EXISTS oci VARCHAR(255) COMMENT 'Open Citation Identifier von OpenCitations';

-- Füge eine Spalte für die OpenCitations-Verifizierung zur publication_metrics-Tabelle hinzu
ALTER TABLE publication_metrics ADD COLUMN IF NOT EXISTS opencitations_verified TINYINT(1) DEFAULT 0 COMMENT 'Ob die Metriken durch OpenCitations verifiziert wurden';

-- Füge eine Spalte für die OpenCitations-Verifizierung zur doi_nft_mapping-Tabelle hinzu
ALTER TABLE doi_nft_mapping ADD COLUMN IF NOT EXISTS opencitations_verified TINYINT(1) DEFAULT 0 COMMENT 'Ob das NFT mit OpenCitations-Daten angereichert wurde';

-- Füge eine Spalte für den OCI zur citation_nft_links-Tabelle hinzu
ALTER TABLE citation_nft_links ADD COLUMN IF NOT EXISTS oci VARCHAR(255) COMMENT 'Open Citation Identifier von OpenCitations';

-- Erstelle eine neue Tabelle für die Speicherung von externen Publikationsdaten
CREATE TABLE IF NOT EXISTS publication_external_data (
  id INT AUTO_INCREMENT PRIMARY KEY,
  doi VARCHAR(255) NOT NULL,
  source VARCHAR(50) NOT NULL COMMENT 'Quelle der externen Daten (z.B. opencitations, semanticscholar)',
  data JSON NOT NULL COMMENT 'Externe Daten im JSON-Format',
  created_at DATETIME NOT NULL,
  updated_at DATETIME NOT NULL,
  UNIQUE KEY unique_doi_source (doi, source)
) COMMENT 'Speichert externe Daten zu Publikationen';

-- Erstelle einen Index für die schnelle Suche nach DOIs
CREATE INDEX IF NOT EXISTS idx_publication_external_data_doi ON publication_external_data(doi);

-- Erstelle einen Index für die schnelle Suche nach OCIs in der citation_relationships-Tabelle
CREATE INDEX IF NOT EXISTS idx_citation_relationships_oci ON citation_relationships(oci);

-- Erstelle einen Index für die schnelle Suche nach OCIs in der citation_nft_links-Tabelle
CREATE INDEX IF NOT EXISTS idx_citation_nft_links_oci ON citation_nft_links(oci);-- Migration zur Unterstützung von OpenCitations in DeSci-Scholar
-- Fügt Spalten für OpenCitations-Daten zu verschiedenen Tabellen hinzu

-- Füge eine Spalte für den OCI (Open Citation Identifier) zur citation_relationships-Tabelle hinzu
ALTER TABLE citation_relationships ADD COLUMN IF NOT EXISTS oci VARCHAR(255) COMMENT 'Open Citation Identifier von OpenCitations';

-- Füge eine Spalte für die OpenCitations-Verifizierung zur publication_metrics-Tabelle hinzu
ALTER TABLE publication_metrics ADD COLUMN IF NOT EXISTS opencitations_verified TINYINT(1) DEFAULT 0 COMMENT 'Ob die Metriken durch OpenCitations verifiziert wurden';

-- Füge eine Spalte für die OpenCitations-Verifizierung zur doi_nft_mapping-Tabelle hinzu
ALTER TABLE doi_nft_mapping ADD COLUMN IF NOT EXISTS opencitations_verified TINYINT(1) DEFAULT 0 COMMENT 'Ob das NFT mit OpenCitations-Daten angereichert wurde';

-- Füge eine Spalte für den OCI zur citation_nft_links-Tabelle hinzu
ALTER TABLE citation_nft_links ADD COLUMN IF NOT EXISTS oci VARCHAR(255) COMMENT 'Open Citation Identifier von OpenCitations';

-- Erstelle eine neue Tabelle für die Speicherung von externen Publikationsdaten
CREATE TABLE IF NOT EXISTS publication_external_data (
  id INT AUTO_INCREMENT PRIMARY KEY,
  doi VARCHAR(255) NOT NULL,
  source VARCHAR(50) NOT NULL COMMENT 'Quelle der externen Daten (z.B. opencitations, semanticscholar)',
  data JSON NOT NULL COMMENT 'Externe Daten im JSON-Format',
  created_at DATETIME NOT NULL,
  updated_at DATETIME NOT NULL,
  UNIQUE KEY unique_doi_source (doi, source)
) COMMENT 'Speichert externe Daten zu Publikationen';

-- Erstelle einen Index für die schnelle Suche nach DOIs
CREATE INDEX IF NOT EXISTS idx_publication_external_data_doi ON publication_external_data(doi);

-- Erstelle einen Index für die schnelle Suche nach OCIs in der citation_relationships-Tabelle
CREATE INDEX IF NOT EXISTS idx_citation_relationships_oci ON citation_relationships(oci);

-- Erstelle einen Index für die schnelle Suche nach OCIs in der citation_nft_links-Tabelle
CREATE INDEX IF NOT EXISTS idx_citation_nft_links_oci ON citation_nft_links(oci);-- Fügt Spalten für OpenCitations-Daten zu verschiedenen Tabellen hinzu

-- Füge eine Spalte für den OCI (Open Citation Identifier) zur citation_relationships-Tabelle hinzu
ALTER TABLE citation_relationships ADD COLUMN IF NOT EXISTS oci VARCHAR(255) COMMENT 'Open Citation Identifier von OpenCitations';

-- Füge eine Spalte für die OpenCitations-Verifizierung zur publication_metrics-Tabelle hinzu
ALTER TABLE publication_metrics ADD COLUMN IF NOT EXISTS opencitations_verified TINYINT(1) DEFAULT 0 COMMENT 'Ob die Metriken durch OpenCitations verifiziert wurden';

-- Füge eine Spalte für die OpenCitations-Verifizierung zur doi_nft_mapping-Tabelle hinzu
ALTER TABLE doi_nft_mapping ADD COLUMN IF NOT EXISTS opencitations_verified TINYINT(1) DEFAULT 0 COMMENT 'Ob das NFT mit OpenCitations-Daten angereichert wurde';

-- Füge eine Spalte für den OCI zur citation_nft_links-Tabelle hinzu
ALTER TABLE citation_nft_links ADD COLUMN IF NOT EXISTS oci VARCHAR(255) COMMENT 'Open Citation Identifier von OpenCitations';

-- Erstelle eine neue Tabelle für die Speicherung von externen Publikationsdaten
CREATE TABLE IF NOT EXISTS publication_external_data (
  id INT AUTO_INCREMENT PRIMARY KEY,
  doi VARCHAR(255) NOT NULL,
  source VARCHAR(50) NOT NULL COMMENT 'Quelle der externen Daten (z.B. opencitations, semanticscholar)',
  data JSON NOT NULL COMMENT 'Externe Daten im JSON-Format',
  created_at DATETIME NOT NULL,
  updated_at DATETIME NOT NULL,
  UNIQUE KEY unique_doi_source (doi, source)
) COMMENT 'Speichert externe Daten zu Publikationen';

-- Erstelle einen Index für die schnelle Suche nach DOIs
CREATE INDEX IF NOT EXISTS idx_publication_external_data_doi ON publication_external_data(doi);

-- Erstelle einen Index für die schnelle Suche nach OCIs in der citation_relationships-Tabelle
CREATE INDEX IF NOT EXISTS idx_citation_relationships_oci ON citation_relationships(oci);

-- Erstelle einen Index für die schnelle Suche nach OCIs in der citation_nft_links-Tabelle
CREATE INDEX IF NOT EXISTS idx_citation_nft_links_oci ON citation_nft_links(oci);-- Fügt Spalten für OpenCitations-Daten zu verschiedenen Tabellen hinzu

-- Füge eine Spalte für den OCI (Open Citation Identifier) zur citation_relationships-Tabelle hinzu
ALTER TABLE citation_relationships ADD COLUMN IF NOT EXISTS oci VARCHAR(255) COMMENT 'Open Citation Identifier von OpenCitations';

-- Füge eine Spalte für die OpenCitations-Verifizierung zur publication_metrics-Tabelle hinzu
ALTER TABLE publication_metrics ADD COLUMN IF NOT EXISTS opencitations_verified TINYINT(1) DEFAULT 0 COMMENT 'Ob die Metriken durch OpenCitations verifiziert wurden';

-- Füge eine Spalte für die OpenCitations-Verifizierung zur doi_nft_mapping-Tabelle hinzu
ALTER TABLE doi_nft_mapping ADD COLUMN IF NOT EXISTS opencitations_verified TINYINT(1) DEFAULT 0 COMMENT 'Ob das NFT mit OpenCitations-Daten angereichert wurde';

-- Füge eine Spalte für den OCI zur citation_nft_links-Tabelle hinzu
ALTER TABLE citation_nft_links ADD COLUMN IF NOT EXISTS oci VARCHAR(255) COMMENT 'Open Citation Identifier von OpenCitations';

-- Erstelle eine neue Tabelle für die Speicherung von externen Publikationsdaten
CREATE TABLE IF NOT EXISTS publication_external_data (
  id INT AUTO_INCREMENT PRIMARY KEY,
  doi VARCHAR(255) NOT NULL,
  source VARCHAR(50) NOT NULL COMMENT 'Quelle der externen Daten (z.B. opencitations, semanticscholar)',
  data JSON NOT NULL COMMENT 'Externe Daten im JSON-Format',
  created_at DATETIME NOT NULL,
  updated_at DATETIME NOT NULL,
  UNIQUE KEY unique_doi_source (doi, source)
) COMMENT 'Speichert externe Daten zu Publikationen';

-- Erstelle einen Index für die schnelle Suche nach DOIs
CREATE INDEX IF NOT EXISTS idx_publication_external_data_doi ON publication_external_data(doi);

-- Erstelle einen Index für die schnelle Suche nach OCIs in der citation_relationships-Tabelle
CREATE INDEX IF NOT EXISTS idx_citation_relationships_oci ON citation_relationships(oci);

-- Erstelle einen Index für die schnelle Suche nach OCIs in der citation_nft_links-Tabelle
CREATE INDEX IF NOT EXISTS idx_citation_nft_links_oci ON citation_nft_links(oci);CREATE INDEX IF NOT EXISTS idx_citation_nft_links_oci ON citation_nft_links(oci);
-- Füge eine Spalte für den OCI (Open Citation Identifier) zur citation_relationships-Tabelle hinzu
ALTER TABLE citation_relationships ADD COLUMN IF NOT EXISTS oci VARCHAR(255) COMMENT 'Open Citation Identifier von OpenCitations';

-- Füge eine Spalte für die OpenCitations-Verifizierung zur publication_metrics-Tabelle hinzu
ALTER TABLE publication_metrics ADD COLUMN IF NOT EXISTS opencitations_verified TINYINT(1) DEFAULT 0 COMMENT 'Ob die Metriken durch OpenCitations verifiziert wurden';

-- Füge eine Spalte für die OpenCitations-Verifizierung zur doi_nft_mapping-Tabelle hinzu
ALTER TABLE doi_nft_mapping ADD COLUMN IF NOT EXISTS opencitations_verified TINYINT(1) DEFAULT 0 COMMENT 'Ob das NFT mit OpenCitations-Daten angereichert wurde';

-- Füge eine Spalte für den OCI zur citation_nft_links-Tabelle hinzu
ALTER TABLE citation_nft_links ADD COLUMN IF NOT EXISTS oci VARCHAR(255) COMMENT 'Open Citation Identifier von OpenCitations';

-- Erstelle eine neue Tabelle für die Speicherung von externen Publikationsdaten
CREATE TABLE IF NOT EXISTS publication_external_data (
  id INT AUTO_INCREMENT PRIMARY KEY,
  doi VARCHAR(255) NOT NULL,
  source VARCHAR(50) NOT NULL COMMENT 'Quelle der externen Daten (z.B. opencitations, semanticscholar)',
  data JSON NOT NULL COMMENT 'Externe Daten im JSON-Format',
  created_at DATETIME NOT NULL,
  updated_at DATETIME NOT NULL,
  UNIQUE KEY unique_doi_source (doi, source)
) COMMENT 'Speichert externe Daten zu Publikationen';

-- Erstelle einen Index für die schnelle Suche nach DOIs
CREATE INDEX IF NOT EXISTS idx_publication_external_data_doi ON publication_external_data(doi);

-- Erstelle einen Index für die schnelle Suche nach OCIs in der citation_relationships-Tabelle
CREATE INDEX IF NOT EXISTS idx_citation_relationships_oci ON citation_relationships(oci);

-- Erstelle einen Index für die schnelle Suche nach OCIs in der citation_nft_links-Tabelle
CREATE INDEX IF NOT EXISTS idx_citation_nft_links_oci ON citation_nft_links(oci);-- Erstelle einen Index für die schnelle Suche nach OCIs in der citation_nft_links-Tabelle
CREATE INDEX IF NOT EXISTS idx_citation_nft_links_oci ON citation_nft_links(oci);CREATE INDEX IF NOT EXISTS idx_citation_nft_links_oci ON citation_nft_links(oci);
-- Füge eine Spalte für den OCI (Open Citation Identifier) zur citation_relationships-Tabelle hinzu
ALTER TABLE citation_relationships ADD COLUMN IF NOT EXISTS oci VARCHAR(255) COMMENT 'Open Citation Identifier von OpenCitations';

-- Füge eine Spalte für die OpenCitations-Verifizierung zur publication_metrics-Tabelle hinzu
ALTER TABLE publication_metrics ADD COLUMN IF NOT EXISTS opencitations_verified TINYINT(1) DEFAULT 0 COMMENT 'Ob die Metriken durch OpenCitations verifiziert wurden';

-- Füge eine Spalte für die OpenCitations-Verifizierung zur doi_nft_mapping-Tabelle hinzu
ALTER TABLE doi_nft_mapping ADD COLUMN IF NOT EXISTS opencitations_verified TINYINT(1) DEFAULT 0 COMMENT 'Ob das NFT mit OpenCitations-Daten angereichert wurde';

-- Füge eine Spalte für den OCI zur citation_nft_links-Tabelle hinzu
ALTER TABLE citation_nft_links ADD COLUMN IF NOT EXISTS oci VARCHAR(255) COMMENT 'Open Citation Identifier von OpenCitations';

-- Erstelle eine neue Tabelle für die Speicherung von externen Publikationsdaten
CREATE TABLE IF NOT EXISTS publication_external_data (
  id INT AUTO_INCREMENT PRIMARY KEY,
  doi VARCHAR(255) NOT NULL,
  source VARCHAR(50) NOT NULL COMMENT 'Quelle der externen Daten (z.B. opencitations, semanticscholar)',
  data JSON NOT NULL COMMENT 'Externe Daten im JSON-Format',
  created_at DATETIME NOT NULL,
  updated_at DATETIME NOT NULL,
  UNIQUE KEY unique_doi_source (doi, source)
) COMMENT 'Speichert externe Daten zu Publikationen';

-- Erstelle einen Index für die schnelle Suche nach DOIs
CREATE INDEX IF NOT EXISTS idx_publication_external_data_doi ON publication_external_data(doi);

-- Erstelle einen Index für die schnelle Suche nach OCIs in der citation_relationships-Tabelle
CREATE INDEX IF NOT EXISTS idx_citation_relationships_oci ON citation_relationships(oci);

-- Erstelle einen Index für die schnelle Suche nach OCIs in der citation_nft_links-Tabelle
CREATE INDEX IF NOT EXISTS idx_citation_nft_links_oci ON citation_nft_links(oci);-- Erstelle einen Index für die schnelle Suche nach OCIs in der citation_nft_links-Tabelle
CREATE INDEX IF NOT EXISTS idx_citation_nft_links_oci ON citation_nft_links(oci);CREATE INDEX IF NOT EXISTS idx_citation_nft_links_oci ON citation_nft_links(oci);
-- Füge eine Spalte für den OCI (Open Citation Identifier) zur citation_relationships-Tabelle hinzu
ALTER TABLE citation_relationships ADD COLUMN IF NOT EXISTS oci VARCHAR(255) COMMENT 'Open Citation Identifier von OpenCitations';

-- Füge eine Spalte für die OpenCitations-Verifizierung zur publication_metrics-Tabelle hinzu
ALTER TABLE publication_metrics ADD COLUMN IF NOT EXISTS opencitations_verified TINYINT(1) DEFAULT 0 COMMENT 'Ob die Metriken durch OpenCitations verifiziert wurden';

-- Füge eine Spalte für die OpenCitations-Verifizierung zur doi_nft_mapping-Tabelle hinzu
ALTER TABLE doi_nft_mapping ADD COLUMN IF NOT EXISTS opencitations_verified TINYINT(1) DEFAULT 0 COMMENT 'Ob das NFT mit OpenCitations-Daten angereichert wurde';

-- Füge eine Spalte für den OCI zur citation_nft_links-Tabelle hinzu
ALTER TABLE citation_nft_links ADD COLUMN IF NOT EXISTS oci VARCHAR(255) COMMENT 'Open Citation Identifier von OpenCitations';

-- Erstelle eine neue Tabelle für die Speicherung von externen Publikationsdaten
CREATE TABLE IF NOT EXISTS publication_external_data (
  id INT AUTO_INCREMENT PRIMARY KEY,
  doi VARCHAR(255) NOT NULL,
  source VARCHAR(50) NOT NULL COMMENT 'Quelle der externen Daten (z.B. opencitations, semanticscholar)',
  data JSON NOT NULL COMMENT 'Externe Daten im JSON-Format',
  created_at DATETIME NOT NULL,
  updated_at DATETIME NOT NULL,
  UNIQUE KEY unique_doi_source (doi, source)
) COMMENT 'Speichert externe Daten zu Publikationen';

-- Erstelle einen Index für die schnelle Suche nach DOIs
CREATE INDEX IF NOT EXISTS idx_publication_external_data_doi ON publication_external_data(doi);

-- Erstelle einen Index für die schnelle Suche nach OCIs in der citation_relationships-Tabelle
CREATE INDEX IF NOT EXISTS idx_citation_relationships_oci ON citation_relationships(oci);

-- Erstelle einen Index für die schnelle Suche nach OCIs in der citation_nft_links-Tabelle
CREATE INDEX IF NOT EXISTS idx_citation_nft_links_oci ON citation_nft_links(oci);CREATE INDEX IF NOT EXISTS idx_citation_nft_links_oci ON citation_nft_links(oci);CREATE INDEX IF NOT EXISTS idx_citation_nft_links_oci ON citation_nft_links(oci);CREATE INDEX IF NOT EXISTS idx_citation_nft_links_oci ON citation_nft_links(oci);
-- Füge eine Spalte für den OCI (Open Citation Identifier) zur citation_relationships-Tabelle hinzu
ALTER TABLE citation_relationships ADD COLUMN IF NOT EXISTS oci VARCHAR(255) COMMENT 'Open Citation Identifier von OpenCitations';

-- Füge eine Spalte für die OpenCitations-Verifizierung zur publication_metrics-Tabelle hinzu
ALTER TABLE publication_metrics ADD COLUMN IF NOT EXISTS opencitations_verified TINYINT(1) DEFAULT 0 COMMENT 'Ob die Metriken durch OpenCitations verifiziert wurden';

-- Füge eine Spalte für die OpenCitations-Verifizierung zur doi_nft_mapping-Tabelle hinzu
ALTER TABLE doi_nft_mapping ADD COLUMN IF NOT EXISTS opencitations_verified TINYINT(1) DEFAULT 0 COMMENT 'Ob das NFT mit OpenCitations-Daten angereichert wurde';

-- Füge eine Spalte für den OCI zur citation_nft_links-Tabelle hinzu
ALTER TABLE citation_nft_links ADD COLUMN IF NOT EXISTS oci VARCHAR(255) COMMENT 'Open Citation Identifier von OpenCitations';

-- Erstelle eine neue Tabelle für die Speicherung von externen Publikationsdaten
CREATE TABLE IF NOT EXISTS publication_external_data (
  id INT AUTO_INCREMENT PRIMARY KEY,
  doi VARCHAR(255) NOT NULL,
  source VARCHAR(50) NOT NULL COMMENT 'Quelle der externen Daten (z.B. opencitations, semanticscholar)',
  data JSON NOT NULL COMMENT 'Externe Daten im JSON-Format',
  created_at DATETIME NOT NULL,
  updated_at DATETIME NOT NULL,
  UNIQUE KEY unique_doi_source (doi, source)
) COMMENT 'Speichert externe Daten zu Publikationen';

-- Erstelle einen Index für die schnelle Suche nach DOIs
CREATE INDEX IF NOT EXISTS idx_publication_external_data_doi ON publication_external_data(doi);

-- Erstelle einen Index für die schnelle Suche nach OCIs in der citation_relationships-Tabelle
CREATE INDEX IF NOT EXISTS idx_citation_relationships_oci ON citation_relationships(oci);

-- Erstelle einen Index für die schnelle Suche nach OCIs in der citation_nft_links-Tabelle
CREATE INDEX IF NOT EXISTS idx_citation_nft_links_oci ON citation_nft_links(oci);CREATE INDEX IF NOT EXISTS idx_citation_nft_links_oci ON citation_nft_links(oci);CREATE INDEX IF NOT EXISTS idx_citation_nft_links_oci ON citation_nft_links(oci);CREATE INDEX IF NOT EXISTS idx_citation_nft_links_oci ON citation_nft_links(oci);CREATE INDEX IF NOT EXISTS idx_citation_nft_links_oci ON citation_nft_links(oci);CREATE INDEX IF NOT EXISTS idx_citation_nft_links_oci ON citation_nft_links(oci);
-- Füge eine Spalte für den OCI (Open Citation Identifier) zur citation_relationships-Tabelle hinzu
ALTER TABLE citation_relationships ADD COLUMN IF NOT EXISTS oci VARCHAR(255) COMMENT 'Open Citation Identifier von OpenCitations';

-- Füge eine Spalte für die OpenCitations-Verifizierung zur publication_metrics-Tabelle hinzu
ALTER TABLE publication_metrics ADD COLUMN IF NOT EXISTS opencitations_verified TINYINT(1) DEFAULT 0 COMMENT 'Ob die Metriken durch OpenCitations verifiziert wurden';

-- Füge eine Spalte für die OpenCitations-Verifizierung zur doi_nft_mapping-Tabelle hinzu
ALTER TABLE doi_nft_mapping ADD COLUMN IF NOT EXISTS opencitations_verified TINYINT(1) DEFAULT 0 COMMENT 'Ob das NFT mit OpenCitations-Daten angereichert wurde';

-- Füge eine Spalte für den OCI zur citation_nft_links-Tabelle hinzu
ALTER TABLE citation_nft_links ADD COLUMN IF NOT EXISTS oci VARCHAR(255) COMMENT 'Open Citation Identifier von OpenCitations';

-- Erstelle eine neue Tabelle für die Speicherung von externen Publikationsdaten
CREATE TABLE IF NOT EXISTS publication_external_data (
  id INT AUTO_INCREMENT PRIMARY KEY,
  doi VARCHAR(255) NOT NULL,
  source VARCHAR(50) NOT NULL COMMENT 'Quelle der externen Daten (z.B. opencitations, semanticscholar)',
  data JSON NOT NULL COMMENT 'Externe Daten im JSON-Format',
  created_at DATETIME NOT NULL,
  updated_at DATETIME NOT NULL,
  UNIQUE KEY unique_doi_source (doi, source)
) COMMENT 'Speichert externe Daten zu Publikationen';

-- Erstelle einen Index für die schnelle Suche nach DOIs
CREATE INDEX IF NOT EXISTS idx_publication_external_data_doi ON publication_external_data(doi);

-- Erstelle einen Index für die schnelle Suche nach OCIs in der citation_relationships-Tabelle
CREATE INDEX IF NOT EXISTS idx_citation_relationships_oci ON citation_relationships(oci);

-- Erstelle einen Index für die schnelle Suche nach OCIs in der citation_nft_links-Tabelle
CREATE INDEX IF NOT EXISTS idx_citation_nft_links_oci ON citation_nft_links(oci);