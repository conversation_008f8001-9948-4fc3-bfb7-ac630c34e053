/**
 * DeSci-Scholar API-Server
 *
 * Haupteinstiegspunkt für den DeSci-Scholar API-Server.
 * Initialisiert alle erforderlichen Dienste und startet den Express-Server.
 *
 * Diese Version verwendet das neue Adapter-System für verbesserte Modularität.
 */

import dotenv from 'dotenv';
import createAPI from './api/index.js';
import DatabaseService from './db/DatabaseService.js';
import AuthService from './auth/AuthService.js';
import SearchService from './search/SearchService.js';
import EnhancedStorageService from './storage/EnhancedStorageService.js';
import PublicationController from './controllers/PublicationController.js';
import StatsService from './stats/StatsService.js';
// Adapter-System temporär deaktiviert bis alle Abhängigkeiten verfügbar sind
// import { initializeAdapterSystem } from './core/adapter/index.js';
// import { adapterConfig } from './config/adapters.js';
import { LoggerFactory } from './utils/logger.js';

// Logger erstellen
const logger = LoggerFactory.createLogger('Server');

// Umgebungsvariablen laden
dotenv.config();

/**
 * Initialisiert alle erforderlichen Dienste
 *
 * @returns {Promise<Object>} Initialisierte Dienste
 */
async function initializeServices() {
  try {
    // Konfigurationsvariablen aus Umgebungsvariablen oder Standardwerten
    const config = {
      port: process.env.PORT || 3000,
      mongoDbUri: process.env.MONGODB_URI || 'mongodb://localhost:27017/desci-scholar',
      jwtSecret: process.env.JWT_SECRET || 'entwicklung-jwt-geheimnis',
      ipfsGateway: process.env.IPFS_GATEWAY || 'https://ipfs.io',
      ipfsApiUrl: process.env.IPFS_API_URL || 'http://localhost:5001',
      bittorrentPath: process.env.BITTORRENT_PATH || './data/bittorrent',
      corsOrigin: process.env.CORS_ORIGIN || '*',
      isProduction: process.env.NODE_ENV === 'production'
    };

    // Adapter-System temporär deaktiviert
    logger.info('Adapter-System temporär deaktiviert...');
    const adapterSystem = { registry: { getAllAdapters: () => ({}) } };
    logger.info('Basis-System initialisiert');

    // Datenbankdienst initialisieren
    logger.info('Initialisiere Datenbankverbindung...');
    const databaseService = new DatabaseService({
      uri: config.mongoDbUri,
      useInMemory: process.env.USE_IN_MEMORY_DB === 'true'
    });

    await databaseService.connect();
    logger.info('Datenbankverbindung hergestellt');

    // Authentifizierungsdienst initialisieren
    logger.info('Initialisiere AuthService...');
    const authService = new AuthService({
      databaseService,
      jwtSecret: config.jwtSecret,
      tokenExpiresIn: parseInt(process.env.JWT_EXPIRES_IN || '86400', 10), // 24h
      refreshTokenExpiresIn: parseInt(process.env.REFRESH_TOKEN_EXPIRES_IN || '604800', 10) // 7 Tage
    });

    await authService.initialize();
    logger.info('AuthService initialisiert');

    // Speicherdienst initialisieren
    logger.info('Initialisiere EnhancedStorageService...');
    const storageService = new EnhancedStorageService({
      ipfs: {
        gateway: config.ipfsGateway,
        apiUrl: config.ipfsApiUrl
      },
      bittorrent: {
        downloadPath: config.bittorrentPath,
        trackers: process.env.BITTORRENT_TRACKERS
          ? process.env.BITTORRENT_TRACKERS.split(',')
          : undefined
      },
      metadataOptions: {
        compression: true,
        automaticPinning: true
      },
      largeFileThreshold: parseInt(process.env.LARGE_FILE_THRESHOLD || '10485760', 10), // 10 MB
      adapterRegistry: adapterSystem.registry
    });

    await storageService.initialize();
    logger.info('EnhancedStorageService initialisiert');

    // Suchdienst initialisieren
    logger.info('Initialisiere SearchService...');
    const searchService = new SearchService({
      databaseService,
      enableFulltext: process.env.ENABLE_FULLTEXT_SEARCH === 'true',
      maxResults: parseInt(process.env.MAX_SEARCH_RESULTS || '50', 10)
    });

    await searchService.initialize();
    logger.info('SearchService initialisiert');

    // PublicationController initialisieren
    logger.info('Initialisiere PublicationController...');
    const publicationController = new PublicationController({
      databaseService,
      storageService,
      searchService,
      adapterRegistry: adapterSystem.registry
    });

    await publicationController.initialize();
    logger.info('PublicationController initialisiert');

    // Statistikdienst initialisieren
    logger.info('Initialisiere StatsService...');
    const statsService = new StatsService({
      databaseService,
      storageService,
      config: {
        trendingPeriodDays: parseInt(process.env.TRENDING_PERIOD_DAYS || '30', 10),
        cacheTimeMs: parseInt(process.env.STATS_CACHE_TIME_MS || '900000', 10) // 15 Minuten Standard
      }
    });

    await statsService.initialize();
    logger.info('StatsService initialisiert');

    // Alle initialisierten Dienste zurückgeben
    return {
      config,
      services: {
        databaseService,
        authService,
        storageService,
        searchService,
        publicationController,
        statsService,
        adapterSystem
      }
    };
  } catch (error) {
    logger.error('Fehler bei der Initialisierung der Dienste:', {
      error: error.message,
      stack: error.stack
    });
    throw error;
  }
}

/**
 * Startet den API-Server
 */
async function startServer() {
  try {
    // Dienste initialisieren
    const { config, services } = await initializeServices();

    // API erstellen
    const app = createAPI({
      services,
      config: {
        corsOrigin: config.corsOrigin,
        isProduction: config.isProduction,
        enableRateLimit: config.isProduction
      }
    });

    // Server starten
    const port = config.port;
    app.listen(port, () => {
      logger.info(`DeSci-Scholar API-Server läuft auf Port ${port}`);
      logger.info(`API verfügbar unter http://localhost:${port}/api/v1`);

      if (!config.isProduction) {
        logger.info('\nEntwicklungsmodus aktiv');
        logger.info('MongoDB: ' + (config.mongoDbUri || 'In-Memory'));
        logger.info('IPFS Gateway: ' + config.ipfsGateway);

        // Verfügbare Adapter anzeigen
        const adapters = services.adapterSystem.registry.getAllAdapters();
        logger.info('Verfügbare Adapter:');
        Object.entries(adapters).forEach(([category, adapterList]) => {
          logger.info(`  ${category}:`);
          adapterList.forEach(adapter => {
            logger.info(`    - ${adapter.name}${adapter.isDefault ? ' (Standard)' : ''}`);
          });
        });
      }
    });

    // Graceful Shutdown
    setupGracefulShutdown(services);
  } catch (error) {
    logger.error('Fehler beim Starten des Servers:', {
      error: error.message,
      stack: error.stack
    });
    process.exit(1);
  }
}

/**
 * Konfiguriert Graceful Shutdown
 *
 * @param {Object} services Initialisierte Dienste
 */
function setupGracefulShutdown(services) {
  // SIGTERM Signal (Docker, Kubernetes, etc.)
  process.on('SIGTERM', async () => {
    logger.info('SIGTERM Signal empfangen. Beende Server...');
    await shutdownServices(services);
    process.exit(0);
  });

  // SIGINT Signal (Strg+C)
  process.on('SIGINT', async () => {
    logger.info('SIGINT Signal empfangen. Beende Server...');
    await shutdownServices(services);
    process.exit(0);
  });

  // Unbehandelte Exceptions
  process.on('uncaughtException', async (error) => {
    logger.error('Unbehandelte Exception:', {
      error: error.message,
      stack: error.stack
    });
    await shutdownServices(services);
    process.exit(1);
  });

  // Unbehandelte Promise-Rejections
  process.on('unhandledRejection', async (reason, promise) => {
    logger.error('Unbehandelte Promise-Rejection:', {
      reason: reason instanceof Error ? reason.message : String(reason),
      stack: reason instanceof Error ? reason.stack : undefined
    });
    await shutdownServices(services);
    process.exit(1);
  });
}

/**
 * Fährt alle Dienste ordnungsgemäß herunter
 *
 * @param {Object} services Initialisierte Dienste
 */
async function shutdownServices(services) {
  logger.info('Fahre Dienste herunter...');

  try {
    // Adapter-System herunterfahren
    if (services.adapterSystem) {
      logger.info('Fahre Adapter-System herunter...');
      // Fahre alle Adapter herunter
      const adapters = services.adapterSystem.registry.getAllAdapters();
      for (const [category, adapterList] of Object.entries(adapters)) {
        for (const adapter of adapterList) {
          try {
            const adapterInstance = services.adapterSystem.registry.getAdapter(category, adapter.name);
            if (adapterInstance && adapterInstance.isInitialized()) {
              logger.debug(`Fahre Adapter ${category}:${adapter.name} herunter...`);
              await adapterInstance.shutdown();
            }
          } catch (adapterError) {
            logger.warn(`Fehler beim Herunterfahren des Adapters ${category}:${adapter.name}`, {
              error: adapterError.message
            });
          }
        }
      }
    }

    // Speicherdienst herunterfahren
    if (services.storageService) {
      logger.info('Fahre StorageService herunter...');
      await services.storageService.shutdown();
    }

    // Datenbankverbindung trennen
    if (services.databaseService) {
      logger.info('Trenne Datenbankverbindung...');
      await services.databaseService.close();
    }

    logger.info('Alle Dienste wurden ordnungsgemäß heruntergefahren');
  } catch (error) {
    logger.error('Fehler beim Herunterfahren der Dienste:', {
      error: error.message,
      stack: error.stack
    });
  }
}

// Server starten
startServer().catch(error => {
  logger.error('Schwerwiegender Fehler beim Serverstart:', {
    error: error.message,
    stack: error.stack
  });
  process.exit(1);
});
