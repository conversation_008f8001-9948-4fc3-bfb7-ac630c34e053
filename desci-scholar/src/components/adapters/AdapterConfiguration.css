.adapter-configuration {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.configuration-header {
  margin-bottom: 2rem;
  text-align: center;
}

.configuration-header h2 {
  margin: 0 0 0.75rem 0;
  color: #333;
  font-size: 1.75rem;
}

.configuration-description {
  color: #666;
  max-width: 700px;
  margin: 0 auto;
  line-height: 1.5;
}

.configuration-content {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.simple-mode,
.advanced-mode {
  margin-bottom: 2rem;
}

.adapter-selectors {
  margin-bottom: 2rem;
}

.profile-section {
  margin-bottom: 2rem;
}

.mode-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e0e0e0;
}

.guided-setup-button,
.toggle-mode-button {
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.guided-setup-button {
  background-color: #4a6cf7;
  color: white;
}

.guided-setup-button:hover {
  background-color: #3a5ce5;
}

.toggle-mode-button {
  background-color: #f0f0f0;
  color: #333;
}

.toggle-mode-button:hover {
  background-color: #e0e0e0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .adapter-configuration {
    padding: 1rem;
  }
  
  .mode-actions {
    flex-direction: column;
    gap: 0.75rem;
  }
}
