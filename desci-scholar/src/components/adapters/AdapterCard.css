.adapter-card {
  background-color: #fff;
  border-radius: 6px;
  padding: 1.25rem;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;
  cursor: pointer;
  border: 1px solid #e0e0e0;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.adapter-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
  border-color: #d0d0d0;
}

.adapter-card.selected {
  border-color: #4a6cf7;
  box-shadow: 0 0 0 2px rgba(74, 108, 247, 0.2);
}

.adapter-card.default {
  border-color: #4a6cf7;
}

.adapter-card-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
  position: relative;
}

.adapter-icon {
  width: 32px;
  height: 32px;
  margin-right: 0.75rem;
  object-fit: contain;
}

.adapter-name {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

.default-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: #4a6cf7;
  color: white;
  font-size: 0.7rem;
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  font-weight: 500;
}

.adapter-description {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
  margin-bottom: 1rem;
  flex-grow: 1;
}

.adapter-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.adapter-tag {
  background-color: #f0f2f5;
  color: #666;
  font-size: 0.75rem;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
}

.adapter-card-footer {
  display: flex;
  justify-content: space-between;
  margin-top: auto;
}

.info-button {
  background: none;
  border: none;
  color: #4a6cf7;
  font-size: 0.85rem;
  cursor: pointer;
  padding: 0.4rem 0.6rem;
  border-radius: 4px;
}

.info-button:hover {
  background-color: rgba(74, 108, 247, 0.1);
}

.select-button {
  background-color: #f0f2f5;
  border: none;
  color: #333;
  font-size: 0.85rem;
  font-weight: 500;
  padding: 0.4rem 0.8rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.select-button:hover {
  background-color: #e0e2e5;
}

.select-button.selected {
  background-color: #4a6cf7;
  color: white;
}

.select-button.selected:hover {
  background-color: #3a5ce5;
}
