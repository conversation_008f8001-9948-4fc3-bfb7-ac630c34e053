.guided-setup {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.guided-setup-progress {
  display: flex;
  justify-content: space-between;
  margin-bottom: 2rem;
  position: relative;
}

.guided-setup-progress::before {
  content: '';
  position: absolute;
  top: 16px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #e0e0e0;
  z-index: 1;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  flex: 1;
}

.step-indicator {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #e0e0e0;
  color: #666;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 600;
  margin-bottom: 0.5rem;
  transition: all 0.3s ease;
}

.progress-step.active .step-indicator {
  background-color: #4a6cf7;
  color: white;
}

.progress-step.completed .step-indicator {
  background-color: #4caf50;
  color: white;
}

.step-label {
  font-size: 0.8rem;
  color: #666;
  text-align: center;
  max-width: 100px;
  transition: all 0.3s ease;
}

.progress-step.active .step-label {
  color: #333;
  font-weight: 500;
}

.guided-setup-content {
  min-height: 300px;
  margin-bottom: 2rem;
}

.step-info,
.step-choice,
.step-recommendation {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.step-info h2,
.step-choice h2,
.step-recommendation h2 {
  margin-top: 0;
  margin-bottom: 1rem;
  color: #333;
}

.step-info p,
.step-choice p,
.step-recommendation p {
  color: #666;
  line-height: 1.5;
  margin-bottom: 1.5rem;
}

.choice-options {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
  margin-top: 1.5rem;
}

.choice-option {
  background-color: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 1.25rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.choice-option:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.choice-option.selected {
  border-color: #4a6cf7;
  background-color: rgba(74, 108, 247, 0.05);
  box-shadow: 0 0 0 2px rgba(74, 108, 247, 0.2);
}

.choice-option h4 {
  margin: 0 0 0.5rem 0;
  color: #333;
}

.choice-option p {
  margin: 0;
  font-size: 0.9rem;
  color: #666;
}

.recommended-profile {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  margin-top: 1rem;
}

.recommended-profile h3 {
  margin: 0 0 0.75rem 0;
  color: #333;
}

.recommended-profile p {
  margin: 0 0 1rem 0;
  color: #666;
}

.profile-adapters {
  background-color: #fff;
  border-radius: 6px;
  padding: 1rem;
  border: 1px solid #e0e0e0;
}

.profile-adapter {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #f0f0f0;
}

.profile-adapter:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.adapter-category {
  font-weight: 500;
  color: #555;
}

.adapter-value {
  color: #4a6cf7;
  font-weight: 500;
}

.guided-setup-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 2rem;
}

.previous-button,
.next-button {
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.previous-button {
  background-color: #f0f0f0;
  color: #333;
  border: none;
}

.previous-button:hover {
  background-color: #e0e0e0;
}

.next-button {
  background-color: #4a6cf7;
  color: white;
  border: none;
}

.next-button:hover {
  background-color: #3a5ce5;
}

.next-button:disabled {
  background-color: #c5c5c5;
  cursor: not-allowed;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .guided-setup {
    padding: 1.5rem;
  }
  
  .guided-setup-progress {
    overflow-x: auto;
    padding-bottom: 1rem;
  }
  
  .step-label {
    font-size: 0.7rem;
    max-width: 80px;
  }
  
  .choice-options {
    grid-template-columns: 1fr;
  }
}
