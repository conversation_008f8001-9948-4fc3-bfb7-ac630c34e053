import React from 'react';
import PropTypes from 'prop-types';
import './AdapterCard.css';

/**
 * Karte zur Darstellung eines Adapters in der Auswahl
 * 
 * @component
 * @param {Object} props - Komponenteneigenschaften
 * @param {Object} props.adapter - Adapter-Konfiguration
 * @param {string} props.adapterKey - Schlüssel des Adapters
 * @param {boolean} props.isSelected - Ob der Adapter ausgewählt ist
 * @param {boolean} props.isDefault - Ob der Adapter der Standard ist
 * @param {Function} props.onClick - Callback bei Klick auf die Karte
 * @param {Function} props.onInfoClick - Callback bei Klick auf den Info-Button
 * @returns {JSX.Element} AdapterCard-Komponente
 */
const AdapterCard = ({ 
  adapter, 
  adapterKey, 
  isSelected, 
  isDefault, 
  onClick, 
  onInfoClick 
}) => {
  if (!adapter) return null;
  
  return (
    <div 
      className={`adapter-card ${isSelected ? 'selected' : ''} ${isDefault ? 'default' : ''}`}
      onClick={onClick}
    >
      <div className="adapter-card-header">
        {adapter.icon && (
          <img 
            src={adapter.icon} 
            alt={`${adapter.name} Icon`} 
            className="adapter-icon" 
          />
        )}
        <h4 className="adapter-name">{adapter.name}</h4>
        {isDefault && <span className="default-badge">Standard</span>}
      </div>
      
      <p className="adapter-description">{adapter.description}</p>
      
      {adapter.recommendedFor && adapter.recommendedFor.length > 0 && (
        <div className="adapter-tags">
          {adapter.recommendedFor.map((tag, index) => (
            <span key={index} className="adapter-tag">{tag}</span>
          ))}
        </div>
      )}
      
      <div className="adapter-card-footer">
        <button 
          className="info-button" 
          onClick={(e) => {
            e.stopPropagation();
            onInfoClick();
          }}
        >
          Mehr Info
        </button>
        
        <button 
          className={`select-button ${isSelected ? 'selected' : ''}`}
          onClick={onClick}
        >
          {isSelected ? 'Ausgewählt' : 'Auswählen'}
        </button>
      </div>
    </div>
  );
};

AdapterCard.propTypes = {
  adapter: PropTypes.shape({
    name: PropTypes.string.isRequired,
    description: PropTypes.string.isRequired,
    icon: PropTypes.string,
    recommendedFor: PropTypes.arrayOf(PropTypes.string)
  }).isRequired,
  adapterKey: PropTypes.string.isRequired,
  isSelected: PropTypes.bool,
  isDefault: PropTypes.bool,
  onClick: PropTypes.func.isRequired,
  onInfoClick: PropTypes.func.isRequired
};

export default AdapterCard;
