import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import AdapterSelector from './AdapterSelector';
import AdapterProfileSelector from './AdapterProfileSelector';
import GuidedSetup from './GuidedSetup';
import { adapterProfiles } from '../../config/ui-config';
import './AdapterConfiguration.css';

/**
 * Hauptkomponente für die Adapter-Konfiguration
 * 
 * @component
 * @param {Object} props - Komponenteneigenschaften
 * @param {Object} props.initialAdapters - Initiale Adapter-Auswahl
 * @param {Function} props.onConfigurationChange - Callback bei Änderung der Konfiguration
 * @param {string} props.experienceLevel - Erfahrungslevel des Nutzers
 * @returns {JSX.Element} AdapterConfiguration-Komponente
 */
const AdapterConfiguration = ({ 
  initialAdapters = {}, 
  onConfigurationChange,
  experienceLevel = 'intermediate'
}) => {
  // Zustand für ausgewählte Adapter
  const [selectedAdapters, setSelectedAdapters] = useState({
    blockchain: initialAdapters.blockchain || adapterProfiles.default.adapters.blockchain,
    storage: initialAdapters.storage || adapterProfiles.default.adapters.storage,
    wallet: initialAdapters.wallet || adapterProfiles.default.adapters.wallet
  });
  
  // Zustand für ausgewähltes Profil
  const [selectedProfile, setSelectedProfile] = useState('default');
  
  // Zustand für UI-Modus
  const [uiMode, setUiMode] = useState(experienceLevel === 'beginner' ? 'guided' : 'advanced');
  
  // Zustand für Guided Setup
  const [showGuidedSetup, setShowGuidedSetup] = useState(experienceLevel === 'beginner');
  
  // Effekt für Änderungen an der Adapter-Auswahl
  useEffect(() => {
    onConfigurationChange(selectedAdapters);
    
    // Prüfen, ob die aktuelle Auswahl einem Profil entspricht
    const matchingProfile = Object.entries(adapterProfiles).find(([_, profile]) => {
      const profileAdapters = profile.adapters;
      return (
        profileAdapters.blockchain === selectedAdapters.blockchain &&
        profileAdapters.storage === selectedAdapters.storage &&
        profileAdapters.wallet === selectedAdapters.wallet
      );
    });
    
    if (matchingProfile) {
      setSelectedProfile(matchingProfile[0]);
    } else {
      setSelectedProfile('custom');
    }
  }, [selectedAdapters, onConfigurationChange]);
  
  // Handler für Änderungen an einzelnen Adaptern
  const handleAdapterChange = (category, adapter) => {
    setSelectedAdapters({
      ...selectedAdapters,
      [category]: adapter
    });
  };
  
  // Handler für Änderungen am Profil
  const handleProfileChange = (profile) => {
    setSelectedProfile(profile);
  };
  
  // Handler für Änderungen an allen Adaptern
  const handleAdaptersChange = (adapters) => {
    setSelectedAdapters(adapters);
  };
  
  // Handler für Abschluss des Guided Setup
  const handleGuidedSetupComplete = (result) => {
    setSelectedProfile(result.profile);
    setSelectedAdapters(result.adapters);
    setShowGuidedSetup(false);
  };
  
  // Handler für Abbruch des Guided Setup
  const handleGuidedSetupCancel = () => {
    setShowGuidedSetup(false);
  };
  
  // UI-Modus wechseln
  const toggleUIMode = () => {
    setUiMode(uiMode === 'simple' ? 'advanced' : 'simple');
  };
  
  // Guided Setup starten
  const startGuidedSetup = () => {
    setShowGuidedSetup(true);
  };
  
  // Render-Funktion für den einfachen Modus
  const renderSimpleMode = () => (
    <div className="simple-mode">
      <AdapterProfileSelector
        selectedProfile={selectedProfile}
        onProfileChange={handleProfileChange}
        onAdaptersChange={handleAdaptersChange}
      />
      
      <div className="mode-actions">
        <button className="guided-setup-button" onClick={startGuidedSetup}>
          Konfigurationsassistent starten
        </button>
        <button className="toggle-mode-button" onClick={toggleUIMode}>
          Erweiterte Einstellungen anzeigen
        </button>
      </div>
    </div>
  );
  
  // Render-Funktion für den erweiterten Modus
  const renderAdvancedMode = () => (
    <div className="advanced-mode">
      <div className="adapter-selectors">
        <AdapterSelector
          category="blockchain"
          selectedAdapter={selectedAdapters.blockchain}
          onAdapterChange={(adapter) => handleAdapterChange('blockchain', adapter)}
          experienceLevel={experienceLevel}
        />
        
        <AdapterSelector
          category="storage"
          selectedAdapter={selectedAdapters.storage}
          onAdapterChange={(adapter) => handleAdapterChange('storage', adapter)}
          experienceLevel={experienceLevel}
        />
        
        <AdapterSelector
          category="wallet"
          selectedAdapter={selectedAdapters.wallet}
          onAdapterChange={(adapter) => handleAdapterChange('wallet', adapter)}
          experienceLevel={experienceLevel}
        />
      </div>
      
      <div className="profile-section">
        <AdapterProfileSelector
          selectedProfile={selectedProfile}
          onProfileChange={handleProfileChange}
          onAdaptersChange={handleAdaptersChange}
        />
      </div>
      
      <div className="mode-actions">
        <button className="guided-setup-button" onClick={startGuidedSetup}>
          Konfigurationsassistent starten
        </button>
        <button className="toggle-mode-button" onClick={toggleUIMode}>
          Vereinfachte Ansicht anzeigen
        </button>
      </div>
    </div>
  );
  
  return (
    <div className="adapter-configuration">
      <div className="configuration-header">
        <h2>Adapter-Konfiguration</h2>
        <p className="configuration-description">
          Konfigurieren Sie die Adapter für Ihre DeSci-Scholar-Instanz. 
          Sie können entweder ein vordefiniertes Profil wählen oder die Adapter einzeln anpassen.
        </p>
      </div>
      
      {showGuidedSetup ? (
        <GuidedSetup
          onComplete={handleGuidedSetupComplete}
          onCancel={handleGuidedSetupCancel}
        />
      ) : (
        <div className="configuration-content">
          {uiMode === 'simple' ? renderSimpleMode() : renderAdvancedMode()}
        </div>
      )}
    </div>
  );
};

AdapterConfiguration.propTypes = {
  initialAdapters: PropTypes.shape({
    blockchain: PropTypes.string,
    storage: PropTypes.string,
    wallet: PropTypes.string
  }),
  onConfigurationChange: PropTypes.func.isRequired,
  experienceLevel: PropTypes.oneOf(['beginner', 'intermediate', 'expert'])
};

export default AdapterConfiguration;
