import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { guidedSetupConfig, adapterProfiles } from '../../config/ui-config';
import './GuidedSetup.css';

/**
 * Konfigurationsassistent für die Adapter-Auswahl
 * 
 * @component
 * @param {Object} props - Komponenteneigenschaften
 * @param {Function} props.onComplete - Callback bei Abschluss des Assistenten
 * @param {Function} props.onCancel - Callback bei Abbruch des Assistenten
 * @returns {JSX.Element} GuidedSetup-Komponente
 */
const GuidedSetup = ({ onComplete, onCancel }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [answers, setAnswers] = useState({});
  const [recommendedProfile, setRecommendedProfile] = useState('default');
  
  const steps = guidedSetupConfig.steps;
  const currentStepData = steps[currentStep];
  
  // Empfohlenes Profil basierend auf den Antworten berechnen
  useEffect(() => {
    if (currentStepData && currentStepData.id === 'recommendation') {
      const { experience, useCase, dataSize, costSensitivity } = answers;
      
      // Logik zur Bestimmung des empfohlenen Profils
      let profile = 'default';
      
      if (experience === 'beginner') {
        // Anfänger bekommen immer das Standardprofil
        profile = 'default';
      } else {
        // Fortgeschrittene und Experten bekommen profilbasierte Empfehlungen
        const logicMap = guidedSetupConfig.recommendationLogic[experience];
        
        if (logicMap && typeof logicMap === 'object') {
          // Verschachtelte Logik für fortgeschrittene Nutzer
          if (logicMap[useCase] && 
              logicMap[useCase][dataSize] && 
              logicMap[useCase][dataSize][costSensitivity]) {
            profile = logicMap[useCase][dataSize][costSensitivity];
          }
        } else if (typeof logicMap === 'string') {
          // Direkte Zuweisung für einfache Fälle
          profile = logicMap;
        }
      }
      
      setRecommendedProfile(profile);
    }
  }, [currentStep, currentStepData, answers]);
  
  // Zum nächsten Schritt gehen
  const goToNextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      // Assistenten abschließen
      onComplete({
        profile: recommendedProfile,
        adapters: adapterProfiles[recommendedProfile]?.adapters || {},
        answers
      });
    }
  };
  
  // Zum vorherigen Schritt gehen
  const goToPreviousStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    } else {
      // Assistenten abbrechen
      onCancel();
    }
  };
  
  // Antwort speichern
  const saveAnswer = (questionId, answer) => {
    setAnswers({
      ...answers,
      [questionId]: answer
    });
  };
  
  // Render-Funktion für verschiedene Schritttypen
  const renderStepContent = () => {
    if (!currentStepData) return null;
    
    switch (currentStepData.type) {
      case 'info':
        return (
          <div className="step-info">
            <h2>{currentStepData.title}</h2>
            <p>{currentStepData.description}</p>
          </div>
        );
        
      case 'choice':
        return (
          <div className="step-choice">
            <h2>{currentStepData.title}</h2>
            <p>{currentStepData.description}</p>
            
            <div className="choice-options">
              {currentStepData.options.map(option => (
                <div 
                  key={option.id}
                  className={`choice-option ${answers[currentStepData.id] === option.id ? 'selected' : ''}`}
                  onClick={() => saveAnswer(currentStepData.id, option.id)}
                >
                  <h4>{option.label}</h4>
                  <p>{option.description}</p>
                </div>
              ))}
            </div>
          </div>
        );
        
      case 'recommendation':
        const profile = adapterProfiles[recommendedProfile];
        
        return (
          <div className="step-recommendation">
            <h2>{currentStepData.title}</h2>
            <p>{currentStepData.description}</p>
            
            <div className="recommended-profile">
              <h3>{profile.name}</h3>
              <p>{profile.description}</p>
              
              <div className="profile-adapters">
                {Object.entries(profile.adapters).map(([category, adapter]) => (
                  <div key={category} className="profile-adapter">
                    <span className="adapter-category">{category}:</span>
                    <span className="adapter-value">{adapter}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );
        
      default:
        return null;
    }
  };
  
  return (
    <div className="guided-setup">
      <div className="guided-setup-progress">
        {steps.map((step, index) => (
          <div 
            key={step.id}
            className={`progress-step ${index === currentStep ? 'active' : ''} ${index < currentStep ? 'completed' : ''}`}
          >
            <div className="step-indicator">{index + 1}</div>
            <span className="step-label">{step.title}</span>
          </div>
        ))}
      </div>
      
      <div className="guided-setup-content">
        {renderStepContent()}
      </div>
      
      <div className="guided-setup-actions">
        <button 
          className="previous-button"
          onClick={goToPreviousStep}
        >
          {currentStep === 0 ? 'Abbrechen' : 'Zurück'}
        </button>
        
        <button 
          className="next-button"
          onClick={goToNextStep}
          disabled={
            currentStepData.type === 'choice' && 
            !answers[currentStepData.id]
          }
        >
          {currentStep === steps.length - 1 ? 'Fertigstellen' : 'Weiter'}
        </button>
      </div>
    </div>
  );
};

GuidedSetup.propTypes = {
  onComplete: PropTypes.func.isRequired,
  onCancel: PropTypes.func.isRequired
};

export default GuidedSetup;
