import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { adapterUIConfig } from '../../config/ui-config';
import AdapterCard from './AdapterCard';
import AdapterDetails from './AdapterDetails';
import './AdapterSelector.css';

/**
 * Komponente zur Auswahl eines Adapters für eine bestimmte Kategorie
 * 
 * @component
 * @param {Object} props - Komponenteneigenschaften
 * @param {string} props.category - Adapter-Kategorie (blockchain, storage, wallet)
 * @param {string} props.selectedAdapter - Aktuell ausgewählter Adapter
 * @param {Function} props.onAdapterChange - Callback bei Änderung der Auswahl
 * @param {boolean} props.showDetails - Ob Details angezeigt werden sollen
 * @param {string} props.experienceLevel - Erfahrungslevel des Nutzers (beginner, intermediate, expert)
 * @returns {JSX.Element} AdapterSelector-Komponente
 */
const AdapterSelector = ({ 
  category, 
  selectedAdapter, 
  onAdapterChange, 
  showDetails = true,
  experienceLevel = 'intermediate'
}) => {
  const [selectedAdapterDetails, setSelectedAdapterDetails] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  
  // Konfiguration für die aktuelle Kategorie
  const categoryConfig = adapterUIConfig[category] || {};
  const adapters = categoryConfig.adapters || {};
  
  // Standardadapter verwenden, wenn keiner ausgewählt ist
  useEffect(() => {
    if (!selectedAdapter && categoryConfig.defaultAdapter) {
      onAdapterChange(categoryConfig.defaultAdapter);
    }
  }, [selectedAdapter, categoryConfig, onAdapterChange]);
  
  // Details des ausgewählten Adapters abrufen
  useEffect(() => {
    if (selectedAdapter && adapters[selectedAdapter]) {
      setSelectedAdapterDetails(adapters[selectedAdapter]);
    } else {
      setSelectedAdapterDetails(null);
    }
  }, [selectedAdapter, adapters]);
  
  // Adapter-Karten basierend auf Erfahrungslevel filtern
  const getFilteredAdapters = () => {
    // Für Anfänger nur den Standardadapter und maximal einen alternativen Adapter anzeigen
    if (experienceLevel === 'beginner') {
      const defaultAdapter = categoryConfig.defaultAdapter;
      const alternativeAdapter = Object.keys(adapters)
        .filter(key => key !== defaultAdapter)
        .sort(() => 0.5 - Math.random())[0]; // Zufälligen alternativen Adapter auswählen
      
      const filteredAdapters = {};
      if (defaultAdapter) filteredAdapters[defaultAdapter] = adapters[defaultAdapter];
      if (alternativeAdapter) filteredAdapters[alternativeAdapter] = adapters[alternativeAdapter];
      
      return filteredAdapters;
    }
    
    // Für fortgeschrittene Nutzer alle Adapter anzeigen
    return adapters;
  };
  
  const filteredAdapters = getFilteredAdapters();
  
  // Modal mit Details öffnen
  const openDetailsModal = (adapterKey) => {
    setSelectedAdapterDetails(adapters[adapterKey]);
    setIsModalOpen(true);
  };
  
  // Modal schließen
  const closeDetailsModal = () => {
    setIsModalOpen(false);
  };
  
  return (
    <div className="adapter-selector">
      <div className="adapter-selector-header">
        <h3>{categoryConfig.title}</h3>
        <p className="adapter-selector-description">{categoryConfig.description}</p>
        
        {/* Standardadapter-Info anzeigen */}
        {categoryConfig.defaultAdapter && (
          <div className="default-adapter-info">
            <span className="default-label">Standard:</span> {adapters[categoryConfig.defaultAdapter]?.name}
            <span className="default-reason">{categoryConfig.defaultReason}</span>
          </div>
        )}
      </div>
      
      <div className="adapter-cards">
        {Object.keys(filteredAdapters).map(adapterKey => (
          <AdapterCard
            key={adapterKey}
            adapter={adapters[adapterKey]}
            adapterKey={adapterKey}
            isSelected={selectedAdapter === adapterKey}
            isDefault={categoryConfig.defaultAdapter === adapterKey}
            onClick={() => onAdapterChange(adapterKey)}
            onInfoClick={() => openDetailsModal(adapterKey)}
          />
        ))}
      </div>
      
      {/* Details des ausgewählten Adapters anzeigen */}
      {showDetails && selectedAdapterDetails && (
        <div className="selected-adapter-details">
          <AdapterDetails adapter={selectedAdapterDetails} />
        </div>
      )}
      
      {/* Modal für detaillierte Informationen */}
      {isModalOpen && selectedAdapterDetails && (
        <div className="adapter-modal-overlay" onClick={closeDetailsModal}>
          <div className="adapter-modal-content" onClick={e => e.stopPropagation()}>
            <button className="modal-close-button" onClick={closeDetailsModal}>×</button>
            <h2>{selectedAdapterDetails.name}</h2>
            <p>{selectedAdapterDetails.description}</p>
            
            <div className="adapter-details-container">
              <AdapterDetails adapter={selectedAdapterDetails} expanded />
            </div>
            
            <div className="modal-actions">
              <button 
                className="select-adapter-button"
                onClick={() => {
                  onAdapterChange(Object.keys(adapters).find(key => 
                    adapters[key] === selectedAdapterDetails
                  ));
                  closeDetailsModal();
                }}
              >
                Diesen Adapter auswählen
              </button>
              <button className="cancel-button" onClick={closeDetailsModal}>
                Abbrechen
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

AdapterSelector.propTypes = {
  category: PropTypes.oneOf(['blockchain', 'storage', 'wallet']).isRequired,
  selectedAdapter: PropTypes.string,
  onAdapterChange: PropTypes.func.isRequired,
  showDetails: PropTypes.bool,
  experienceLevel: PropTypes.oneOf(['beginner', 'intermediate', 'expert'])
};

export default AdapterSelector;
