.adapter-selector {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.adapter-selector-header {
  margin-bottom: 1.5rem;
}

.adapter-selector-header h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
  color: #333;
}

.adapter-selector-description {
  margin: 0 0 1rem 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

.default-adapter-info {
  display: flex;
  align-items: center;
  font-size: 0.85rem;
  color: #666;
  margin-top: 0.5rem;
}

.default-label {
  font-weight: 600;
  margin-right: 0.5rem;
}

.default-reason {
  margin-left: 0.5rem;
  font-style: italic;
  color: #888;
}

.adapter-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.selected-adapter-details {
  margin-top: 1.5rem;
  padding: 1rem;
  background-color: #fff;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
}

/* Modal Styles */
.adapter-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.adapter-modal-content {
  background-color: #fff;
  border-radius: 8px;
  padding: 2rem;
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.modal-close-button {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
}

.modal-close-button:hover {
  color: #333;
}

.adapter-details-container {
  margin: 1.5rem 0;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1.5rem;
}

.select-adapter-button {
  padding: 0.6rem 1.2rem;
  background-color: #4a6cf7;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.select-adapter-button:hover {
  background-color: #3a5ce5;
}

.cancel-button {
  padding: 0.6rem 1.2rem;
  background-color: #f0f0f0;
  color: #333;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.cancel-button:hover {
  background-color: #e0e0e0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .adapter-cards {
    grid-template-columns: 1fr;
  }
  
  .adapter-modal-content {
    width: 95%;
    padding: 1.5rem;
  }
}
