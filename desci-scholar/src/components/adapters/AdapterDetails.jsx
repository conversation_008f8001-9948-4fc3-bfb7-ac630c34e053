import React from 'react';
import PropTypes from 'prop-types';
import './AdapterDetails.css';

/**
 * Komponente zur Anzeige detaillierter Informationen über einen Adapter
 * 
 * @component
 * @param {Object} props - Komponenteneigenschaften
 * @param {Object} props.adapter - Adapter-Konfiguration
 * @param {boolean} props.expanded - Ob erweiterte Details angezeigt werden sollen
 * @returns {JSX.Element} AdapterDetails-Komponente
 */
const AdapterDetails = ({ adapter, expanded = false }) => {
  if (!adapter) return null;
  
  return (
    <div className={`adapter-details ${expanded ? 'expanded' : ''}`}>
      {expanded && (
        <div className="adapter-header">
          {adapter.icon && (
            <img 
              src={adapter.icon} 
              alt={`${adapter.name} Icon`} 
              className="adapter-icon-large" 
            />
          )}
          <div>
            <h2>{adapter.name}</h2>
            <p className="adapter-description-large">{adapter.description}</p>
          </div>
        </div>
      )}
      
      <div className="adapter-details-content">
        {/* Vorteile */}
        {adapter.advantages && adapter.advantages.length > 0 && (
          <div className="adapter-advantages">
            <h4>Vorteile</h4>
            <ul>
              {adapter.advantages.map((advantage, index) => (
                <li key={index}>{advantage}</li>
              ))}
            </ul>
          </div>
        )}
        
        {/* Nachteile */}
        {adapter.disadvantages && adapter.disadvantages.length > 0 && (
          <div className="adapter-disadvantages">
            <h4>Nachteile</h4>
            <ul>
              {adapter.disadvantages.map((disadvantage, index) => (
                <li key={index}>{disadvantage}</li>
              ))}
            </ul>
          </div>
        )}
        
        {/* Empfohlen für */}
        {adapter.recommendedFor && adapter.recommendedFor.length > 0 && (
          <div className="adapter-recommended-for">
            <h4>Empfohlen für</h4>
            <div className="adapter-tags-large">
              {adapter.recommendedFor.map((tag, index) => (
                <span key={index} className="adapter-tag-large">{tag}</span>
              ))}
            </div>
          </div>
        )}
        
        {/* Erweiterte Informationen */}
        {expanded && adapter.additionalInfo && (
          <div className="adapter-additional-info">
            <h4>Weitere Informationen</h4>
            <p>{adapter.additionalInfo}</p>
          </div>
        )}
        
        {/* Externe Links */}
        {expanded && adapter.externalLinks && adapter.externalLinks.length > 0 && (
          <div className="adapter-external-links">
            <h4>Externe Links</h4>
            <ul>
              {adapter.externalLinks.map((link, index) => (
                <li key={index}>
                  <a 
                    href={link.url} 
                    target="_blank" 
                    rel="noopener noreferrer"
                  >
                    {link.title}
                  </a>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
};

AdapterDetails.propTypes = {
  adapter: PropTypes.shape({
    name: PropTypes.string.isRequired,
    description: PropTypes.string.isRequired,
    icon: PropTypes.string,
    advantages: PropTypes.arrayOf(PropTypes.string),
    disadvantages: PropTypes.arrayOf(PropTypes.string),
    recommendedFor: PropTypes.arrayOf(PropTypes.string),
    additionalInfo: PropTypes.string,
    externalLinks: PropTypes.arrayOf(PropTypes.shape({
      title: PropTypes.string.isRequired,
      url: PropTypes.string.isRequired
    }))
  }).isRequired,
  expanded: PropTypes.bool
};

export default AdapterDetails;
