.adapter-profile-selector {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.adapter-profile-selector h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
  color: #333;
}

.profile-selector-description {
  margin: 0 0 1.5rem 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

.profile-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.25rem;
}

.profile-card {
  background-color: #fff;
  border-radius: 6px;
  padding: 1.25rem;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  border: 1px solid #e0e0e0;
  transition: all 0.2s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.profile-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
  border-color: #d0d0d0;
}

.profile-card.selected {
  border-color: #4a6cf7;
  box-shadow: 0 0 0 2px rgba(74, 108, 247, 0.2);
}

.profile-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.75rem;
}

.profile-card-header h4 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

.default-profile-badge {
  background-color: #4a6cf7;
  color: white;
  font-size: 0.7rem;
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  font-weight: 500;
}

.profile-description {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
  margin-bottom: 1rem;
  flex-grow: 1;
}

.profile-adapters {
  margin-bottom: 1rem;
  background-color: #f8f9fa;
  padding: 0.75rem;
  border-radius: 4px;
}

.profile-adapter-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  font-size: 0.85rem;
}

.profile-adapter-item:last-child {
  margin-bottom: 0;
}

.adapter-category {
  color: #666;
  font-weight: 500;
}

.adapter-name {
  color: #333;
}

.profile-details {
  margin: 1rem 0;
  padding: 0.75rem;
  background-color: #f0f2f5;
  border-radius: 4px;
  font-size: 0.85rem;
  color: #555;
}

.profile-card-footer {
  display: flex;
  justify-content: space-between;
  margin-top: auto;
}

.profile-details-toggle {
  background: none;
  border: none;
  color: #4a6cf7;
  font-size: 0.85rem;
  cursor: pointer;
  padding: 0.4rem 0.6rem;
  border-radius: 4px;
}

.profile-details-toggle:hover {
  background-color: rgba(74, 108, 247, 0.1);
}

.select-profile-button {
  background-color: #f0f2f5;
  border: none;
  color: #333;
  font-size: 0.85rem;
  font-weight: 500;
  padding: 0.4rem 0.8rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.select-profile-button:hover {
  background-color: #e0e2e5;
}

.select-profile-button.selected {
  background-color: #4a6cf7;
  color: white;
}

.select-profile-button.selected:hover {
  background-color: #3a5ce5;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .profile-cards {
    grid-template-columns: 1fr;
  }
}
