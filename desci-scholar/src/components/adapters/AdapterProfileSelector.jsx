import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { adapterProfiles, adapterUIConfig } from '../../config/ui-config';
import './AdapterProfileSelector.css';

/**
 * Komponente zur Auswahl vordefinierter Adapter-Profile
 * 
 * @component
 * @param {Object} props - Komponenteneigenschaften
 * @param {string} props.selectedProfile - Aktuell ausgewähltes Profil
 * @param {Function} props.onProfileChange - Callback bei Änderung des Profils
 * @param {Function} props.onAdaptersChange - Callback bei Änderung der Adapter
 * @returns {JSX.Element} AdapterProfileSelector-Komponente
 */
const AdapterProfileSelector = ({ 
  selectedProfile, 
  onProfileChange, 
  onAdaptersChange 
}) => {
  const [expandedProfile, setExpandedProfile] = useState(null);
  
  // Profil auswählen
  const selectProfile = (profileKey) => {
    onProfileChange(profileKey);
    
    // Adapter entsprechend dem Profil aktualisieren
    if (adapterProfiles[profileKey] && adapterProfiles[profileKey].adapters) {
      onAdaptersChange(adapterProfiles[profileKey].adapters);
    }
  };
  
  // Profil-Details ein-/ausklappen
  const toggleProfileDetails = (profileKey) => {
    setExpandedProfile(expandedProfile === profileKey ? null : profileKey);
  };
  
  // Adapter-Namen für ein Profil abrufen
  const getAdapterNames = (profile) => {
    if (!profile || !profile.adapters) return {};
    
    const adapterNames = {};
    
    Object.entries(profile.adapters).forEach(([category, adapterKey]) => {
      const categoryConfig = adapterUIConfig[category];
      if (categoryConfig && categoryConfig.adapters && categoryConfig.adapters[adapterKey]) {
        adapterNames[category] = categoryConfig.adapters[adapterKey].name;
      } else {
        adapterNames[category] = adapterKey;
      }
    });
    
    return adapterNames;
  };
  
  return (
    <div className="adapter-profile-selector">
      <h3>Vordefinierte Profile</h3>
      <p className="profile-selector-description">
        Wählen Sie ein vordefiniertes Profil, um schnell eine optimierte Konfiguration für Ihren Anwendungsfall zu erhalten.
      </p>
      
      <div className="profile-cards">
        {Object.keys(adapterProfiles).map(profileKey => {
          const profile = adapterProfiles[profileKey];
          const isSelected = selectedProfile === profileKey;
          const isExpanded = expandedProfile === profileKey;
          const adapterNames = getAdapterNames(profile);
          
          return (
            <div 
              key={profileKey}
              className={`profile-card ${isSelected ? 'selected' : ''}`}
            >
              <div className="profile-card-header">
                <h4>{profile.name}</h4>
                {profileKey === 'default' && (
                  <span className="default-profile-badge">Empfohlen</span>
                )}
              </div>
              
              <p className="profile-description">{profile.description}</p>
              
              <div className="profile-adapters">
                {Object.entries(adapterNames).map(([category, name]) => (
                  <div key={category} className="profile-adapter-item">
                    <span className="adapter-category">{adapterUIConfig[category]?.title || category}:</span>
                    <span className="adapter-name">{name}</span>
                  </div>
                ))}
              </div>
              
              {isExpanded && profile.details && (
                <div className="profile-details">
                  <p>{profile.details}</p>
                </div>
              )}
              
              <div className="profile-card-footer">
                {profile.details && (
                  <button 
                    className="profile-details-toggle" 
                    onClick={() => toggleProfileDetails(profileKey)}
                  >
                    {isExpanded ? 'Weniger anzeigen' : 'Mehr anzeigen'}
                  </button>
                )}
                
                <button 
                  className={`select-profile-button ${isSelected ? 'selected' : ''}`}
                  onClick={() => selectProfile(profileKey)}
                >
                  {isSelected ? 'Ausgewählt' : 'Auswählen'}
                </button>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

AdapterProfileSelector.propTypes = {
  selectedProfile: PropTypes.string,
  onProfileChange: PropTypes.func.isRequired,
  onAdaptersChange: PropTypes.func.isRequired
};

export default AdapterProfileSelector;
