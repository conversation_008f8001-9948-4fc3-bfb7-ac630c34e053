.adapter-details {
  padding: 1rem 0;
}

.adapter-details.expanded {
  padding: 0;
}

.adapter-header {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
}

.adapter-icon-large {
  width: 48px;
  height: 48px;
  margin-right: 1rem;
  object-fit: contain;
}

.adapter-description-large {
  color: #666;
  margin-top: 0.5rem;
  line-height: 1.5;
}

.adapter-details-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.adapter-advantages,
.adapter-disadvantages,
.adapter-recommended-for,
.adapter-additional-info,
.adapter-external-links {
  margin-bottom: 1.5rem;
}

.adapter-advantages h4,
.adapter-disadvantages h4,
.adapter-recommended-for h4,
.adapter-additional-info h4,
.adapter-external-links h4 {
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
  color: #333;
}

.adapter-advantages ul,
.adapter-disadvantages ul,
.adapter-external-links ul {
  margin: 0;
  padding-left: 1.25rem;
}

.adapter-advantages li,
.adapter-disadvantages li,
.adapter-external-links li {
  margin-bottom: 0.5rem;
  color: #555;
  font-size: 0.9rem;
  line-height: 1.4;
}

.adapter-advantages li {
  color: #2e7d32;
}

.adapter-disadvantages li {
  color: #c62828;
}

.adapter-tags-large {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.adapter-tag-large {
  background-color: #f0f2f5;
  color: #555;
  font-size: 0.85rem;
  padding: 0.3rem 0.7rem;
  border-radius: 4px;
  display: inline-block;
}

.adapter-additional-info p {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
  margin: 0;
}

.adapter-external-links a {
  color: #4a6cf7;
  text-decoration: none;
}

.adapter-external-links a:hover {
  text-decoration: underline;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .adapter-details-content {
    grid-template-columns: 1fr;
  }
  
  .adapter-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .adapter-icon-large {
    margin-bottom: 1rem;
  }
}
