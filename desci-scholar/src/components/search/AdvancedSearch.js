import React, { useState } from 'react';
import axios from 'axios';

/**
 * <PERSON>rweiterte Suchkomponente für wissenschaftliche Publikationen
 * In<PERSON>iri<PERSON> von Scinapse.io für eine verbesserte Benutzererfahrung
 */
const AdvancedSearch = ({ onResultsFound, onSelectPaper }) => {
  // Suchparameter
  const [searchParams, setSearchParams] = useState({
    query: '',
    authors: '',
    year: '',
    venue: '',
    sortBy: 'relevance',
    sortOrder: 'desc'
  });

  // Suchstatus
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState(null);
  const [results, setResults] = useState(null);

  // Handler für Änderungen an den Suchparametern
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSearchParams(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Suchfunktion
  const handleSearch = async (e) => {
    e.preventDefault();
    
    // Prüfen, ob mindestens ein Suchkriterium angegeben wurde
    if (!searchParams.query && !searchParams.authors && !searchParams.year && !searchParams.venue) {
      setError('Bitte geben Sie mindestens ein Suchkriterium ein.');
      return;
    }
    
    setIsSearching(true);
    setError(null);
    
    try {
      // API-Anfrage mit den Suchparametern
      const response = await axios.get('/api/v1/semantic-scholar/advanced-search', {
        params: {
          ...searchParams,
          fields: 'title,authors,year,venue,url,externalIds,abstract,citationCount'
        }
      });
      
      setResults(response.data);
      
      // Callback für übergeordnete Komponente
      if (onResultsFound) {
        onResultsFound(response.data);
      }
    } catch (error) {
      console.error('Fehler bei der Suche:', error);
      setError(error.response?.data?.error || 'Ein Fehler ist bei der Suche aufgetreten.');
    } finally {
      setIsSearching(false);
    }
  };

  // Zurücksetzen der Suchparameter
  const handleReset = () => {
    setSearchParams({
      query: '',
      authors: '',
      year: '',
      venue: '',
      sortBy: 'relevance',
      sortOrder: 'desc'
    });
    setResults(null);
    setError(null);
  };

  return (
    <div className="advanced-search-container">
      <h2>Erweiterte Suche</h2>
      
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSearch} className="advanced-search-form">
        <div className="form-group">
          <label htmlFor="query">Suchbegriffe:</label>
          <input
            type="text"
            id="query"
            name="query"
            value={searchParams.query}
            onChange={handleInputChange}
            placeholder="z.B. quantum computing"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="authors">Autor(en):</label>
          <input
            type="text"
            id="authors"
            name="authors"
            value={searchParams.authors}
            onChange={handleInputChange}
            placeholder="z.B. John Smith"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="year">Jahr oder Jahresbereich:</label>
          <input
            type="text"
            id="year"
            name="year"
            value={searchParams.year}
            onChange={handleInputChange}
            placeholder="z.B. 2020 oder 2018-2021"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="venue">Journal/Konferenz:</label>
          <input
            type="text"
            id="venue"
            name="venue"
            value={searchParams.venue}
            onChange={handleInputChange}
            placeholder="z.B. Nature"
            className="form-control"
          />
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="sortBy">Sortieren nach:</label>
            <select
              id="sortBy"
              name="sortBy"
              value={searchParams.sortBy}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="relevance">Relevanz</option>
              <option value="year">Jahr</option>
              <option value="citationCount">Zitationen</option>
            </select>
          </div>
          
          <div className="form-group">
            <label htmlFor="sortOrder">Reihenfolge:</label>
            <select
              id="sortOrder"
              name="sortOrder"
              value={searchParams.sortOrder}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="desc">Absteigend</option>
              <option value="asc">Aufsteigend</option>
            </select>
          </div>
        </div>
        
        <div className="form-actions">
          <button 
            type="submit" 
            className="btn btn-primary" 
            disabled={isSearching}
          >
            {isSearching ? 'Suche läuft...' : 'Suchen'}
          </button>
          <button 
            type="button" 
            className="btn btn-secondary" 
            onClick={handleReset}
            disabled={isSearching}
          >
            Zurücksetzen
          </button>
        </div>
      </form>
      
      {results && (
        <div className="search-results">
          <h3>Suchergebnisse ({results.total})</h3>
          
          {results.data && results.data.length > 0 ? (
            <div className="results-list">
              {results.data.map((paper, index) => (
                <div key={paper.paperId || index} className="result-item">
                  <h4>{paper.title}</h4>
                  <div className="paper-meta">
                    <span className="paper-authors">
                      {paper.authors?.map(a => a.name).join(', ')}
                    </span>
                    <span className="paper-venue">
                      {paper.venue} ({paper.year})
                    </span>
                    {paper.citationCount !== undefined && (
                      <span className="paper-citations">
                        Zitationen: {paper.citationCount}
                      </span>
                    )}
                  </div>
                  {paper.abstract && (
                    <p className="paper-abstract">
                      {paper.abstract.length > 300 
                        ? `${paper.abstract.substring(0, 300)}...` 
                        : paper.abstract}
                    </p>
                  )}
                  <div className="paper-links">
                    {paper.url && (
                      <a 
                        href={paper.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        Zum Paper
                      </a>
                    )}
                    {paper.externalIds?.DOI && (
                      <a 
                        href={`https://doi.org/${paper.externalIds.DOI}`} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        DOI: {paper.externalIds?.DOI}
                      </a>
                    )}
                    <button
                      className="btn btn-sm btn-primary create-nft-btn"
                      onClick={() => {
                        // Funktion zur Auswahl des Papers für die NFT-Erstellung
                        if (onSelectPaper) {
                          onSelectPaper(paper);
                        } else {
                          alert(`NFT für DOI ${paper.externalIds?.DOI || 'unbekannt'} erstellen`);
                        }
                      }}
                    >
                      Als NFT erstellen
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="no-results">Keine Ergebnisse gefunden.</p>
          )}
        </div>
      )}
      
      {/* CSS für die Komponente */}
      <style jsx>{`
        .advanced-search-container {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        
        .advanced-search-form {
          background-color: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
        }
        
        .form-group {
          margin-bottom: 15px;
        }
        
        .form-row {
          display: flex;
          gap: 15px;
        }
        
        .form-control {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #ced4da;
          border-radius: 4px;
          font-size: 16px;
        }
        
        .form-actions {
          display: flex;
          gap: 10px;
          margin-top: 20px;
        }
        
        .btn {
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 16px;
        }
        
        .btn-primary {
          background-color: #007bff;
          color: white;
        }
        
        .btn-secondary {
          background-color: #6c757d;
          color: white;
        }
        
        .btn:disabled {
          opacity: 0.65;
          cursor: not-allowed;
        }
        
        .error-message {
          background-color: #f8d7da;
          color: #721c24;
          padding: 10px;
          border-radius: 4px;
          margin-bottom: 15px;
        }
        
        .search-results {
          margin-top: 30px;
        }
        
        .result-item {
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 15px;
          background-color: white;
        }
        
        .result-item h4 {
          margin-top: 0;
          color: #007bff;
        }
        
        .paper-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 15px;
          margin-bottom: 10px;
          font-size: 14px;
          color: #6c757d;
        }
        
        .paper-abstract {
          font-size: 14px;
          line-height: 1.5;
          color: #212529;
        }
        
        .paper-links {
          display: flex;
          gap: 15px;
          margin-top: 10px;
        }
        
        .paper-link {
          color: #007bff;
          text-decoration: none;
        }
        
        .paper-link:hover {
          text-decoration: underline;
        }
        
        .btn-sm {
          padding: 4px 8px;
          font-size: 14px;
        }
        
        .create-nft-btn {
          margin-left: auto;
        }
        
        .no-results {
          text-align: center;
          color: #6c757d;
          font-style: italic;
        }
      `}</style>
    </div>
  );
};

export default AdvancedSearch;import React, { useState } from 'react';
import axios from 'axios';

/**
 * Erweiterte Suchkomponente für wissenschaftliche Publikationen
 * Inspiriert von Scinapse.io für eine verbesserte Benutzererfahrung
 */
const AdvancedSearch = ({ onResultsFound, onSelectPaper }) => {
  // Suchparameter
  const [searchParams, setSearchParams] = useState({
    query: '',
    authors: '',
    year: '',
    venue: '',
    sortBy: 'relevance',
    sortOrder: 'desc'
  });

  // Suchstatus
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState(null);
  const [results, setResults] = useState(null);

  // Handler für Änderungen an den Suchparametern
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSearchParams(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Suchfunktion
  const handleSearch = async (e) => {
    e.preventDefault();
    
    // Prüfen, ob mindestens ein Suchkriterium angegeben wurde
    if (!searchParams.query && !searchParams.authors && !searchParams.year && !searchParams.venue) {
      setError('Bitte geben Sie mindestens ein Suchkriterium ein.');
      return;
    }
    
    setIsSearching(true);
    setError(null);
    
    try {
      // API-Anfrage mit den Suchparametern
      const response = await axios.get('/api/v1/semantic-scholar/advanced-search', {
        params: {
          ...searchParams,
          fields: 'title,authors,year,venue,url,externalIds,abstract,citationCount'
        }
      });
      
      setResults(response.data);
      
      // Callback für übergeordnete Komponente
      if (onResultsFound) {
        onResultsFound(response.data);
      }
    } catch (error) {
      console.error('Fehler bei der Suche:', error);
      setError(error.response?.data?.error || 'Ein Fehler ist bei der Suche aufgetreten.');
    } finally {
      setIsSearching(false);
    }
  };

  // Zurücksetzen der Suchparameter
  const handleReset = () => {
    setSearchParams({
      query: '',
      authors: '',
      year: '',
      venue: '',
      sortBy: 'relevance',
      sortOrder: 'desc'
    });
    setResults(null);
    setError(null);
  };

  return (
    <div className="advanced-search-container">
      <h2>Erweiterte Suche</h2>
      
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSearch} className="advanced-search-form">
        <div className="form-group">
          <label htmlFor="query">Suchbegriffe:</label>
          <input
            type="text"
            id="query"
            name="query"
            value={searchParams.query}
            onChange={handleInputChange}
            placeholder="z.B. quantum computing"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="authors">Autor(en):</label>
          <input
            type="text"
            id="authors"
            name="authors"
            value={searchParams.authors}
            onChange={handleInputChange}
            placeholder="z.B. John Smith"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="year">Jahr oder Jahresbereich:</label>
          <input
            type="text"
            id="year"
            name="year"
            value={searchParams.year}
            onChange={handleInputChange}
            placeholder="z.B. 2020 oder 2018-2021"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="venue">Journal/Konferenz:</label>
          <input
            type="text"
            id="venue"
            name="venue"
            value={searchParams.venue}
            onChange={handleInputChange}
            placeholder="z.B. Nature"
            className="form-control"
          />
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="sortBy">Sortieren nach:</label>
            <select
              id="sortBy"
              name="sortBy"
              value={searchParams.sortBy}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="relevance">Relevanz</option>
              <option value="year">Jahr</option>
              <option value="citationCount">Zitationen</option>
            </select>
          </div>
          
          <div className="form-group">
            <label htmlFor="sortOrder">Reihenfolge:</label>
            <select
              id="sortOrder"
              name="sortOrder"
              value={searchParams.sortOrder}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="desc">Absteigend</option>
              <option value="asc">Aufsteigend</option>
            </select>
          </div>
        </div>
        
        <div className="form-actions">
          <button 
            type="submit" 
            className="btn btn-primary" 
            disabled={isSearching}
          >
            {isSearching ? 'Suche läuft...' : 'Suchen'}
          </button>
          <button 
            type="button" 
            className="btn btn-secondary" 
            onClick={handleReset}
            disabled={isSearching}
          >
            Zurücksetzen
          </button>
        </div>
      </form>
      
      {results && (
        <div className="search-results">
          <h3>Suchergebnisse ({results.total})</h3>
          
          {results.data && results.data.length > 0 ? (
            <div className="results-list">
              {results.data.map((paper, index) => (
                <div key={paper.paperId || index} className="result-item">
                  <h4>{paper.title}</h4>
                  <div className="paper-meta">
                    <span className="paper-authors">
                      {paper.authors?.map(a => a.name).join(', ')}
                    </span>
                    <span className="paper-venue">
                      {paper.venue} ({paper.year})
                    </span>
                    {paper.citationCount !== undefined && (
                      <span className="paper-citations">
                        Zitationen: {paper.citationCount}
                      </span>
                    )}
                  </div>
                  {paper.abstract && (
                    <p className="paper-abstract">
                      {paper.abstract.length > 300 
                        ? `${paper.abstract.substring(0, 300)}...` 
                        : paper.abstract}
                    </p>
                  )}
                  <div className="paper-links">
                    {paper.url && (
                      <a 
                        href={paper.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        Zum Paper
                      </a>
                    )}
                    {paper.externalIds?.DOI && (
                      <a 
                        href={`https://doi.org/${paper.externalIds.DOI}`} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        DOI: {paper.externalIds?.DOI}
                      </a>
                    )}
                    <button
                      className="btn btn-sm btn-primary create-nft-btn"
                      onClick={() => {
                        // Funktion zur Auswahl des Papers für die NFT-Erstellung
                        if (onSelectPaper) {
                          onSelectPaper(paper);
                        } else {
                          alert(`NFT für DOI ${paper.externalIds?.DOI || 'unbekannt'} erstellen`);
                        }
                      }}
                    >
                      Als NFT erstellen
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="no-results">Keine Ergebnisse gefunden.</p>
          )}
        </div>
      )}
      
      {/* CSS für die Komponente */}
      <style jsx>{`
        .advanced-search-container {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        
        .advanced-search-form {
          background-color: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
        }
        
        .form-group {
          margin-bottom: 15px;
        }
        
        .form-row {
          display: flex;
          gap: 15px;
        }
        
        .form-control {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #ced4da;
          border-radius: 4px;
          font-size: 16px;
        }
        
        .form-actions {
          display: flex;
          gap: 10px;
          margin-top: 20px;
        }
        
        .btn {
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 16px;
        }
        
        .btn-primary {
          background-color: #007bff;
          color: white;
        }
        
        .btn-secondary {
          background-color: #6c757d;
          color: white;
        }
        
        .btn:disabled {
          opacity: 0.65;
          cursor: not-allowed;
        }
        
        .error-message {
          background-color: #f8d7da;
          color: #721c24;
          padding: 10px;
          border-radius: 4px;
          margin-bottom: 15px;
        }
        
        .search-results {
          margin-top: 30px;
        }
        
        .result-item {
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 15px;
          background-color: white;
        }
        
        .result-item h4 {
          margin-top: 0;
          color: #007bff;
        }
        
        .paper-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 15px;
          margin-bottom: 10px;
          font-size: 14px;
          color: #6c757d;
        }
        
        .paper-abstract {
          font-size: 14px;
          line-height: 1.5;
          color: #212529;
        }
        
        .paper-links {
          display: flex;
          gap: 15px;
          margin-top: 10px;
        }
        
        .paper-link {
          color: #007bff;
          text-decoration: none;
        }
        
        .paper-link:hover {
          text-decoration: underline;
        }
        
        .btn-sm {
          padding: 4px 8px;
          font-size: 14px;
        }
        
        .create-nft-btn {
          margin-left: auto;
        }
        
        .no-results {
          text-align: center;
          color: #6c757d;
          font-style: italic;
        }
      `}</style>
    </div>
  );
};

export default AdvancedSearch;import React, { useState } from 'react';
import axios from 'axios';

/**
 * Erweiterte Suchkomponente für wissenschaftliche Publikationen
 * Inspiriert von Scinapse.io für eine verbesserte Benutzererfahrung
 */
const AdvancedSearch = ({ onResultsFound, onSelectPaper }) => {
  // Suchparameter
  const [searchParams, setSearchParams] = useState({
    query: '',
    authors: '',
    year: '',
    venue: '',
    sortBy: 'relevance',
    sortOrder: 'desc'
  });

  // Suchstatus
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState(null);
  const [results, setResults] = useState(null);

  // Handler für Änderungen an den Suchparametern
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSearchParams(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Suchfunktion
  const handleSearch = async (e) => {
    e.preventDefault();
    
    // Prüfen, ob mindestens ein Suchkriterium angegeben wurde
    if (!searchParams.query && !searchParams.authors && !searchParams.year && !searchParams.venue) {
      setError('Bitte geben Sie mindestens ein Suchkriterium ein.');
      return;
    }
    
    setIsSearching(true);
    setError(null);
    
    try {
      // API-Anfrage mit den Suchparametern
      const response = await axios.get('/api/v1/semantic-scholar/advanced-search', {
        params: {
          ...searchParams,
          fields: 'title,authors,year,venue,url,externalIds,abstract,citationCount'
        }
      });
      
      setResults(response.data);
      
      // Callback für übergeordnete Komponente
      if (onResultsFound) {
        onResultsFound(response.data);
      }
    } catch (error) {
      console.error('Fehler bei der Suche:', error);
      setError(error.response?.data?.error || 'Ein Fehler ist bei der Suche aufgetreten.');
    } finally {
      setIsSearching(false);
    }
  };

  // Zurücksetzen der Suchparameter
  const handleReset = () => {
    setSearchParams({
      query: '',
      authors: '',
      year: '',
      venue: '',
      sortBy: 'relevance',
      sortOrder: 'desc'
    });
    setResults(null);
    setError(null);
  };

  return (
    <div className="advanced-search-container">
      <h2>Erweiterte Suche</h2>
      
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSearch} className="advanced-search-form">
        <div className="form-group">
          <label htmlFor="query">Suchbegriffe:</label>
          <input
            type="text"
            id="query"
            name="query"
            value={searchParams.query}
            onChange={handleInputChange}
            placeholder="z.B. quantum computing"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="authors">Autor(en):</label>
          <input
            type="text"
            id="authors"
            name="authors"
            value={searchParams.authors}
            onChange={handleInputChange}
            placeholder="z.B. John Smith"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="year">Jahr oder Jahresbereich:</label>
          <input
            type="text"
            id="year"
            name="year"
            value={searchParams.year}
            onChange={handleInputChange}
            placeholder="z.B. 2020 oder 2018-2021"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="venue">Journal/Konferenz:</label>
          <input
            type="text"
            id="venue"
            name="venue"
            value={searchParams.venue}
            onChange={handleInputChange}
            placeholder="z.B. Nature"
            className="form-control"
          />
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="sortBy">Sortieren nach:</label>
            <select
              id="sortBy"
              name="sortBy"
              value={searchParams.sortBy}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="relevance">Relevanz</option>
              <option value="year">Jahr</option>
              <option value="citationCount">Zitationen</option>
            </select>
          </div>
          
          <div className="form-group">
            <label htmlFor="sortOrder">Reihenfolge:</label>
            <select
              id="sortOrder"
              name="sortOrder"
              value={searchParams.sortOrder}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="desc">Absteigend</option>
              <option value="asc">Aufsteigend</option>
            </select>
          </div>
        </div>
        
        <div className="form-actions">
          <button 
            type="submit" 
            className="btn btn-primary" 
            disabled={isSearching}
          >
            {isSearching ? 'Suche läuft...' : 'Suchen'}
          </button>
          <button 
            type="button" 
            className="btn btn-secondary" 
            onClick={handleReset}
            disabled={isSearching}
          >
            Zurücksetzen
          </button>
        </div>
      </form>
      
      {results && (
        <div className="search-results">
          <h3>Suchergebnisse ({results.total})</h3>
          
          {results.data && results.data.length > 0 ? (
            <div className="results-list">
              {results.data.map((paper, index) => (
                <div key={paper.paperId || index} className="result-item">
                  <h4>{paper.title}</h4>
                  <div className="paper-meta">
                    <span className="paper-authors">
                      {paper.authors?.map(a => a.name).join(', ')}
                    </span>
                    <span className="paper-venue">
                      {paper.venue} ({paper.year})
                    </span>
                    {paper.citationCount !== undefined && (
                      <span className="paper-citations">
                        Zitationen: {paper.citationCount}
                      </span>
                    )}
                  </div>
                  {paper.abstract && (
                    <p className="paper-abstract">
                      {paper.abstract.length > 300 
                        ? `${paper.abstract.substring(0, 300)}...` 
                        : paper.abstract}
                    </p>
                  )}
                  <div className="paper-links">
                    {paper.url && (
                      <a 
                        href={paper.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        Zum Paper
                      </a>
                    )}
                    {paper.externalIds?.DOI && (
                      <a 
                        href={`https://doi.org/${paper.externalIds.DOI}`} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        DOI: {paper.externalIds?.DOI}
                      </a>
                    )}
                    <button
                      className="btn btn-sm btn-primary create-nft-btn"
                      onClick={() => {
                        // Funktion zur Auswahl des Papers für die NFT-Erstellung
                        if (onSelectPaper) {
                          onSelectPaper(paper);
                        } else {
                          alert(`NFT für DOI ${paper.externalIds?.DOI || 'unbekannt'} erstellen`);
                        }
                      }}
                    >
                      Als NFT erstellen
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="no-results">Keine Ergebnisse gefunden.</p>
          )}
        </div>
      )}
      
      {/* CSS für die Komponente */}
      <style jsx>{`
        .advanced-search-container {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        
        .advanced-search-form {
          background-color: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
        }
        
        .form-group {
          margin-bottom: 15px;
        }
        
        .form-row {
          display: flex;
          gap: 15px;
        }
        
        .form-control {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #ced4da;
          border-radius: 4px;
          font-size: 16px;
        }
        
        .form-actions {
          display: flex;
          gap: 10px;
          margin-top: 20px;
        }
        
        .btn {
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 16px;
        }
        
        .btn-primary {
          background-color: #007bff;
          color: white;
        }
        
        .btn-secondary {
          background-color: #6c757d;
          color: white;
        }
        
        .btn:disabled {
          opacity: 0.65;
          cursor: not-allowed;
        }
        
        .error-message {
          background-color: #f8d7da;
          color: #721c24;
          padding: 10px;
          border-radius: 4px;
          margin-bottom: 15px;
        }
        
        .search-results {
          margin-top: 30px;
        }
        
        .result-item {
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 15px;
          background-color: white;
        }
        
        .result-item h4 {
          margin-top: 0;
          color: #007bff;
        }
        
        .paper-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 15px;
          margin-bottom: 10px;
          font-size: 14px;
          color: #6c757d;
        }
        
        .paper-abstract {
          font-size: 14px;
          line-height: 1.5;
          color: #212529;
        }
        
        .paper-links {
          display: flex;
          gap: 15px;
          margin-top: 10px;
        }
        
        .paper-link {
          color: #007bff;
          text-decoration: none;
        }
        
        .paper-link:hover {
          text-decoration: underline;
        }
        
        .btn-sm {
          padding: 4px 8px;
          font-size: 14px;
        }
        
        .create-nft-btn {
          margin-left: auto;
        }
        
        .no-results {
          text-align: center;
          color: #6c757d;
          font-style: italic;
        }
      `}</style>
    </div>
  );
};

export default AdvancedSearch;import React, { useState } from 'react';
import axios from 'axios';

/**
 * Erweiterte Suchkomponente für wissenschaftliche Publikationen
 * Inspiriert von Scinapse.io für eine verbesserte Benutzererfahrung
 */
const AdvancedSearch = ({ onResultsFound, onSelectPaper }) => {
  // Suchparameter
  const [searchParams, setSearchParams] = useState({
    query: '',
    authors: '',
    year: '',
    venue: '',
    sortBy: 'relevance',
    sortOrder: 'desc'
  });

  // Suchstatus
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState(null);
  const [results, setResults] = useState(null);

  // Handler für Änderungen an den Suchparametern
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSearchParams(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Suchfunktion
  const handleSearch = async (e) => {
    e.preventDefault();
    
    // Prüfen, ob mindestens ein Suchkriterium angegeben wurde
    if (!searchParams.query && !searchParams.authors && !searchParams.year && !searchParams.venue) {
      setError('Bitte geben Sie mindestens ein Suchkriterium ein.');
      return;
    }
    
    setIsSearching(true);
    setError(null);
    
    try {
      // API-Anfrage mit den Suchparametern
      const response = await axios.get('/api/v1/semantic-scholar/advanced-search', {
        params: {
          ...searchParams,
          fields: 'title,authors,year,venue,url,externalIds,abstract,citationCount'
        }
      });
      
      setResults(response.data);
      
      // Callback für übergeordnete Komponente
      if (onResultsFound) {
        onResultsFound(response.data);
      }
    } catch (error) {
      console.error('Fehler bei der Suche:', error);
      setError(error.response?.data?.error || 'Ein Fehler ist bei der Suche aufgetreten.');
    } finally {
      setIsSearching(false);
    }
  };

  // Zurücksetzen der Suchparameter
  const handleReset = () => {
    setSearchParams({
      query: '',
      authors: '',
      year: '',
      venue: '',
      sortBy: 'relevance',
      sortOrder: 'desc'
    });
    setResults(null);
    setError(null);
  };

  return (
    <div className="advanced-search-container">
      <h2>Erweiterte Suche</h2>
      
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSearch} className="advanced-search-form">
        <div className="form-group">
          <label htmlFor="query">Suchbegriffe:</label>
          <input
            type="text"
            id="query"
            name="query"
            value={searchParams.query}
            onChange={handleInputChange}
            placeholder="z.B. quantum computing"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="authors">Autor(en):</label>
          <input
            type="text"
            id="authors"
            name="authors"
            value={searchParams.authors}
            onChange={handleInputChange}
            placeholder="z.B. John Smith"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="year">Jahr oder Jahresbereich:</label>
          <input
            type="text"
            id="year"
            name="year"
            value={searchParams.year}
            onChange={handleInputChange}
            placeholder="z.B. 2020 oder 2018-2021"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="venue">Journal/Konferenz:</label>
          <input
            type="text"
            id="venue"
            name="venue"
            value={searchParams.venue}
            onChange={handleInputChange}
            placeholder="z.B. Nature"
            className="form-control"
          />
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="sortBy">Sortieren nach:</label>
            <select
              id="sortBy"
              name="sortBy"
              value={searchParams.sortBy}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="relevance">Relevanz</option>
              <option value="year">Jahr</option>
              <option value="citationCount">Zitationen</option>
            </select>
          </div>
          
          <div className="form-group">
            <label htmlFor="sortOrder">Reihenfolge:</label>
            <select
              id="sortOrder"
              name="sortOrder"
              value={searchParams.sortOrder}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="desc">Absteigend</option>
              <option value="asc">Aufsteigend</option>
            </select>
          </div>
        </div>
        
        <div className="form-actions">
          <button 
            type="submit" 
            className="btn btn-primary" 
            disabled={isSearching}
          >
            {isSearching ? 'Suche läuft...' : 'Suchen'}
          </button>
          <button 
            type="button" 
            className="btn btn-secondary" 
            onClick={handleReset}
            disabled={isSearching}
          >
            Zurücksetzen
          </button>
        </div>
      </form>
      
      {results && (
        <div className="search-results">
          <h3>Suchergebnisse ({results.total})</h3>
          
          {results.data && results.data.length > 0 ? (
            <div className="results-list">
              {results.data.map((paper, index) => (
                <div key={paper.paperId || index} className="result-item">
                  <h4>{paper.title}</h4>
                  <div className="paper-meta">
                    <span className="paper-authors">
                      {paper.authors?.map(a => a.name).join(', ')}
                    </span>
                    <span className="paper-venue">
                      {paper.venue} ({paper.year})
                    </span>
                    {paper.citationCount !== undefined && (
                      <span className="paper-citations">
                        Zitationen: {paper.citationCount}
                      </span>
                    )}
                  </div>
                  {paper.abstract && (
                    <p className="paper-abstract">
                      {paper.abstract.length > 300 
                        ? `${paper.abstract.substring(0, 300)}...` 
                        : paper.abstract}
                    </p>
                  )}
                  <div className="paper-links">
                    {paper.url && (
                      <a 
                        href={paper.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        Zum Paper
                      </a>
                    )}
                    {paper.externalIds?.DOI && (
                      <a 
                        href={`https://doi.org/${paper.externalIds.DOI}`} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        DOI: {paper.externalIds?.DOI}
                      </a>
                    )}
                    <button
                      className="btn btn-sm btn-primary create-nft-btn"
                      onClick={() => {
                        // Funktion zur Auswahl des Papers für die NFT-Erstellung
                        if (onSelectPaper) {
                          onSelectPaper(paper);
                        } else {
                          alert(`NFT für DOI ${paper.externalIds?.DOI || 'unbekannt'} erstellen`);
                        }
                      }}
                    >
                      Als NFT erstellen
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="no-results">Keine Ergebnisse gefunden.</p>
          )}
        </div>
      )}
      
      {/* CSS für die Komponente */}
      <style jsx>{`
        .advanced-search-container {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        
        .advanced-search-form {
          background-color: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
        }
        
        .form-group {
          margin-bottom: 15px;
        }
        
        .form-row {
          display: flex;
          gap: 15px;
        }
        
        .form-control {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #ced4da;
          border-radius: 4px;
          font-size: 16px;
        }
        
        .form-actions {
          display: flex;
          gap: 10px;
          margin-top: 20px;
        }
        
        .btn {
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 16px;
        }
        
        .btn-primary {
          background-color: #007bff;
          color: white;
        }
        
        .btn-secondary {
          background-color: #6c757d;
          color: white;
        }
        
        .btn:disabled {
          opacity: 0.65;
          cursor: not-allowed;
        }
        
        .error-message {
          background-color: #f8d7da;
          color: #721c24;
          padding: 10px;
          border-radius: 4px;
          margin-bottom: 15px;
        }
        
        .search-results {
          margin-top: 30px;
        }
        
        .result-item {
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 15px;
          background-color: white;
        }
        
        .result-item h4 {
          margin-top: 0;
          color: #007bff;
        }
        
        .paper-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 15px;
          margin-bottom: 10px;
          font-size: 14px;
          color: #6c757d;
        }
        
        .paper-abstract {
          font-size: 14px;
          line-height: 1.5;
          color: #212529;
        }
        
        .paper-links {
          display: flex;
          gap: 15px;
          margin-top: 10px;
        }
        
        .paper-link {
          color: #007bff;
          text-decoration: none;
        }
        
        .paper-link:hover {
          text-decoration: underline;
        }
        
        .btn-sm {
          padding: 4px 8px;
          font-size: 14px;
        }
        
        .create-nft-btn {
          margin-left: auto;
        }
        
        .no-results {
          text-align: center;
          color: #6c757d;
          font-style: italic;
        }
      `}</style>
    </div>
  );
};

export default AdvancedSearch;import React, { useState } from 'react';
import axios from 'axios';

/**
 * Erweiterte Suchkomponente für wissenschaftliche Publikationen
 * Inspiriert von Scinapse.io für eine verbesserte Benutzererfahrung
 */
const AdvancedSearch = ({ onResultsFound, onSelectPaper }) => {
  // Suchparameter
  const [searchParams, setSearchParams] = useState({
    query: '',
    authors: '',
    year: '',
    venue: '',
    sortBy: 'relevance',
    sortOrder: 'desc'
  });

  // Suchstatus
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState(null);
  const [results, setResults] = useState(null);

  // Handler für Änderungen an den Suchparametern
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSearchParams(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Suchfunktion
  const handleSearch = async (e) => {
    e.preventDefault();
    
    // Prüfen, ob mindestens ein Suchkriterium angegeben wurde
    if (!searchParams.query && !searchParams.authors && !searchParams.year && !searchParams.venue) {
      setError('Bitte geben Sie mindestens ein Suchkriterium ein.');
      return;
    }
    
    setIsSearching(true);
    setError(null);
    
    try {
      // API-Anfrage mit den Suchparametern
      const response = await axios.get('/api/v1/semantic-scholar/advanced-search', {
        params: {
          ...searchParams,
          fields: 'title,authors,year,venue,url,externalIds,abstract,citationCount'
        }
      });
      
      setResults(response.data);
      
      // Callback für übergeordnete Komponente
      if (onResultsFound) {
        onResultsFound(response.data);
      }
    } catch (error) {
      console.error('Fehler bei der Suche:', error);
      setError(error.response?.data?.error || 'Ein Fehler ist bei der Suche aufgetreten.');
    } finally {
      setIsSearching(false);
    }
  };

  // Zurücksetzen der Suchparameter
  const handleReset = () => {
    setSearchParams({
      query: '',
      authors: '',
      year: '',
      venue: '',
      sortBy: 'relevance',
      sortOrder: 'desc'
    });
    setResults(null);
    setError(null);
  };

  return (
    <div className="advanced-search-container">
      <h2>Erweiterte Suche</h2>
      
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSearch} className="advanced-search-form">
        <div className="form-group">
          <label htmlFor="query">Suchbegriffe:</label>
          <input
            type="text"
            id="query"
            name="query"
            value={searchParams.query}
            onChange={handleInputChange}
            placeholder="z.B. quantum computing"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="authors">Autor(en):</label>
          <input
            type="text"
            id="authors"
            name="authors"
            value={searchParams.authors}
            onChange={handleInputChange}
            placeholder="z.B. John Smith"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="year">Jahr oder Jahresbereich:</label>
          <input
            type="text"
            id="year"
            name="year"
            value={searchParams.year}
            onChange={handleInputChange}
            placeholder="z.B. 2020 oder 2018-2021"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="venue">Journal/Konferenz:</label>
          <input
            type="text"
            id="venue"
            name="venue"
            value={searchParams.venue}
            onChange={handleInputChange}
            placeholder="z.B. Nature"
            className="form-control"
          />
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="sortBy">Sortieren nach:</label>
            <select
              id="sortBy"
              name="sortBy"
              value={searchParams.sortBy}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="relevance">Relevanz</option>
              <option value="year">Jahr</option>
              <option value="citationCount">Zitationen</option>
            </select>
          </div>
          
          <div className="form-group">
            <label htmlFor="sortOrder">Reihenfolge:</label>
            <select
              id="sortOrder"
              name="sortOrder"
              value={searchParams.sortOrder}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="desc">Absteigend</option>
              <option value="asc">Aufsteigend</option>
            </select>
          </div>
        </div>
        
        <div className="form-actions">
          <button 
            type="submit" 
            className="btn btn-primary" 
            disabled={isSearching}
          >
            {isSearching ? 'Suche läuft...' : 'Suchen'}
          </button>
          <button 
            type="button" 
            className="btn btn-secondary" 
            onClick={handleReset}
            disabled={isSearching}
          >
            Zurücksetzen
          </button>
        </div>
      </form>
      
      {results && (
        <div className="search-results">
          <h3>Suchergebnisse ({results.total})</h3>
          
          {results.data && results.data.length > 0 ? (
            <div className="results-list">
              {results.data.map((paper, index) => (
                <div key={paper.paperId || index} className="result-item">
                  <h4>{paper.title}</h4>
                  <div className="paper-meta">
                    <span className="paper-authors">
                      {paper.authors?.map(a => a.name).join(', ')}
                    </span>
                    <span className="paper-venue">
                      {paper.venue} ({paper.year})
                    </span>
                    {paper.citationCount !== undefined && (
                      <span className="paper-citations">
                        Zitationen: {paper.citationCount}
                      </span>
                    )}
                  </div>
                  {paper.abstract && (
                    <p className="paper-abstract">
                      {paper.abstract.length > 300 
                        ? `${paper.abstract.substring(0, 300)}...` 
                        : paper.abstract}
                    </p>
                  )}
                  <div className="paper-links">
                    {paper.url && (
                      <a 
                        href={paper.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        Zum Paper
                      </a>
                    )}
                    {paper.externalIds?.DOI && (
                      <a 
                        href={`https://doi.org/${paper.externalIds.DOI}`} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        DOI: {paper.externalIds?.DOI}
                      </a>
                    )}
                    <button
                      className="btn btn-sm btn-primary create-nft-btn"
                      onClick={() => {
                        // Funktion zur Auswahl des Papers für die NFT-Erstellung
                        if (onSelectPaper) {
                          onSelectPaper(paper);
                        } else {
                          alert(`NFT für DOI ${paper.externalIds?.DOI || 'unbekannt'} erstellen`);
                        }
                      }}
                    >
                      Als NFT erstellen
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="no-results">Keine Ergebnisse gefunden.</p>
          )}
        </div>
      )}
      
      {/* CSS für die Komponente */}
      <style jsx>{`
        .advanced-search-container {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        
        .advanced-search-form {
          background-color: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
        }
        
        .form-group {
          margin-bottom: 15px;
        }
        
        .form-row {
          display: flex;
          gap: 15px;
        }
        
        .form-control {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #ced4da;
          border-radius: 4px;
          font-size: 16px;
        }
        
        .form-actions {
          display: flex;
          gap: 10px;
          margin-top: 20px;
        }
        
        .btn {
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 16px;
        }
        
        .btn-primary {
          background-color: #007bff;
          color: white;
        }
        
        .btn-secondary {
          background-color: #6c757d;
          color: white;
        }
        
        .btn:disabled {
          opacity: 0.65;
          cursor: not-allowed;
        }
        
        .error-message {
          background-color: #f8d7da;
          color: #721c24;
          padding: 10px;
          border-radius: 4px;
          margin-bottom: 15px;
        }
        
        .search-results {
          margin-top: 30px;
        }
        
        .result-item {
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 15px;
          background-color: white;
        }
        
        .result-item h4 {
          margin-top: 0;
          color: #007bff;
        }
        
        .paper-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 15px;
          margin-bottom: 10px;
          font-size: 14px;
          color: #6c757d;
        }
        
        .paper-abstract {
          font-size: 14px;
          line-height: 1.5;
          color: #212529;
        }
        
        .paper-links {
          display: flex;
          gap: 15px;
          margin-top: 10px;
        }
        
        .paper-link {
          color: #007bff;
          text-decoration: none;
        }
        
        .paper-link:hover {
          text-decoration: underline;
        }
        
        .btn-sm {
          padding: 4px 8px;
          font-size: 14px;
        }
        
        .create-nft-btn {
          margin-left: auto;
        }
        
        .no-results {
          text-align: center;
          color: #6c757d;
          font-style: italic;
        }
      `}</style>
    </div>
  );
};

export default AdvancedSearch;import React, { useState } from 'react';
import axios from 'axios';

/**
 * Erweiterte Suchkomponente für wissenschaftliche Publikationen
 * Inspiriert von Scinapse.io für eine verbesserte Benutzererfahrung
 */
const AdvancedSearch = ({ onResultsFound, onSelectPaper }) => {
  // Suchparameter
  const [searchParams, setSearchParams] = useState({
    query: '',
    authors: '',
    year: '',
    venue: '',
    sortBy: 'relevance',
    sortOrder: 'desc'
  });

  // Suchstatus
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState(null);
  const [results, setResults] = useState(null);

  // Handler für Änderungen an den Suchparametern
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSearchParams(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Suchfunktion
  const handleSearch = async (e) => {
    e.preventDefault();
    
    // Prüfen, ob mindestens ein Suchkriterium angegeben wurde
    if (!searchParams.query && !searchParams.authors && !searchParams.year && !searchParams.venue) {
      setError('Bitte geben Sie mindestens ein Suchkriterium ein.');
      return;
    }
    
    setIsSearching(true);
    setError(null);
    
    try {
      // API-Anfrage mit den Suchparametern
      const response = await axios.get('/api/v1/semantic-scholar/advanced-search', {
        params: {
          ...searchParams,
          fields: 'title,authors,year,venue,url,externalIds,abstract,citationCount'
        }
      });
      
      setResults(response.data);
      
      // Callback für übergeordnete Komponente
      if (onResultsFound) {
        onResultsFound(response.data);
      }
    } catch (error) {
      console.error('Fehler bei der Suche:', error);
      setError(error.response?.data?.error || 'Ein Fehler ist bei der Suche aufgetreten.');
    } finally {
      setIsSearching(false);
    }
  };

  // Zurücksetzen der Suchparameter
  const handleReset = () => {
    setSearchParams({
      query: '',
      authors: '',
      year: '',
      venue: '',
      sortBy: 'relevance',
      sortOrder: 'desc'
    });
    setResults(null);
    setError(null);
  };

  return (
    <div className="advanced-search-container">
      <h2>Erweiterte Suche</h2>
      
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSearch} className="advanced-search-form">
        <div className="form-group">
          <label htmlFor="query">Suchbegriffe:</label>
          <input
            type="text"
            id="query"
            name="query"
            value={searchParams.query}
            onChange={handleInputChange}
            placeholder="z.B. quantum computing"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="authors">Autor(en):</label>
          <input
            type="text"
            id="authors"
            name="authors"
            value={searchParams.authors}
            onChange={handleInputChange}
            placeholder="z.B. John Smith"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="year">Jahr oder Jahresbereich:</label>
          <input
            type="text"
            id="year"
            name="year"
            value={searchParams.year}
            onChange={handleInputChange}
            placeholder="z.B. 2020 oder 2018-2021"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="venue">Journal/Konferenz:</label>
          <input
            type="text"
            id="venue"
            name="venue"
            value={searchParams.venue}
            onChange={handleInputChange}
            placeholder="z.B. Nature"
            className="form-control"
          />
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="sortBy">Sortieren nach:</label>
            <select
              id="sortBy"
              name="sortBy"
              value={searchParams.sortBy}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="relevance">Relevanz</option>
              <option value="year">Jahr</option>
              <option value="citationCount">Zitationen</option>
            </select>
          </div>
          
          <div className="form-group">
            <label htmlFor="sortOrder">Reihenfolge:</label>
            <select
              id="sortOrder"
              name="sortOrder"
              value={searchParams.sortOrder}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="desc">Absteigend</option>
              <option value="asc">Aufsteigend</option>
            </select>
          </div>
        </div>
        
        <div className="form-actions">
          <button 
            type="submit" 
            className="btn btn-primary" 
            disabled={isSearching}
          >
            {isSearching ? 'Suche läuft...' : 'Suchen'}
          </button>
          <button 
            type="button" 
            className="btn btn-secondary" 
            onClick={handleReset}
            disabled={isSearching}
          >
            Zurücksetzen
          </button>
        </div>
      </form>
      
      {results && (
        <div className="search-results">
          <h3>Suchergebnisse ({results.total})</h3>
          
          {results.data && results.data.length > 0 ? (
            <div className="results-list">
              {results.data.map((paper, index) => (
                <div key={paper.paperId || index} className="result-item">
                  <h4>{paper.title}</h4>
                  <div className="paper-meta">
                    <span className="paper-authors">
                      {paper.authors?.map(a => a.name).join(', ')}
                    </span>
                    <span className="paper-venue">
                      {paper.venue} ({paper.year})
                    </span>
                    {paper.citationCount !== undefined && (
                      <span className="paper-citations">
                        Zitationen: {paper.citationCount}
                      </span>
                    )}
                  </div>
                  {paper.abstract && (
                    <p className="paper-abstract">
                      {paper.abstract.length > 300 
                        ? `${paper.abstract.substring(0, 300)}...` 
                        : paper.abstract}
                    </p>
                  )}
                  <div className="paper-links">
                    {paper.url && (
                      <a 
                        href={paper.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        Zum Paper
                      </a>
                    )}
                    {paper.externalIds?.DOI && (
                      <a 
                        href={`https://doi.org/${paper.externalIds.DOI}`} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        DOI: {paper.externalIds?.DOI}
                      </a>
                    )}
                    <button
                      className="btn btn-sm btn-primary create-nft-btn"
                      onClick={() => {
                        // Funktion zur Auswahl des Papers für die NFT-Erstellung
                        if (onSelectPaper) {
                          onSelectPaper(paper);
                        } else {
                          alert(`NFT für DOI ${paper.externalIds?.DOI || 'unbekannt'} erstellen`);
                        }
                      }}
                    >
                      Als NFT erstellen
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="no-results">Keine Ergebnisse gefunden.</p>
          )}
        </div>
      )}
      
      {/* CSS für die Komponente */}
      <style jsx>{`
        .advanced-search-container {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        
        .advanced-search-form {
          background-color: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
        }
        
        .form-group {
          margin-bottom: 15px;
        }
        
        .form-row {
          display: flex;
          gap: 15px;
        }
        
        .form-control {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #ced4da;
          border-radius: 4px;
          font-size: 16px;
        }
        
        .form-actions {
          display: flex;
          gap: 10px;
          margin-top: 20px;
        }
        
        .btn {
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 16px;
        }
        
        .btn-primary {
          background-color: #007bff;
          color: white;
        }
        
        .btn-secondary {
          background-color: #6c757d;
          color: white;
        }
        
        .btn:disabled {
          opacity: 0.65;
          cursor: not-allowed;
        }
        
        .error-message {
          background-color: #f8d7da;
          color: #721c24;
          padding: 10px;
          border-radius: 4px;
          margin-bottom: 15px;
        }
        
        .search-results {
          margin-top: 30px;
        }
        
        .result-item {
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 15px;
          background-color: white;
        }
        
        .result-item h4 {
          margin-top: 0;
          color: #007bff;
        }
        
        .paper-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 15px;
          margin-bottom: 10px;
          font-size: 14px;
          color: #6c757d;
        }
        
        .paper-abstract {
          font-size: 14px;
          line-height: 1.5;
          color: #212529;
        }
        
        .paper-links {
          display: flex;
          gap: 15px;
          margin-top: 10px;
        }
        
        .paper-link {
          color: #007bff;
          text-decoration: none;
        }
        
        .paper-link:hover {
          text-decoration: underline;
        }
        
        .btn-sm {
          padding: 4px 8px;
          font-size: 14px;
        }
        
        .create-nft-btn {
          margin-left: auto;
        }
        
        .no-results {
          text-align: center;
          color: #6c757d;
          font-style: italic;
        }
      `}</style>
    </div>
  );
};

export default AdvancedSearch;import React, { useState } from 'react';
import axios from 'axios';

/**
 * Erweiterte Suchkomponente für wissenschaftliche Publikationen
 * Inspiriert von Scinapse.io für eine verbesserte Benutzererfahrung
 */
const AdvancedSearch = ({ onResultsFound, onSelectPaper }) => {
  // Suchparameter
  const [searchParams, setSearchParams] = useState({
    query: '',
    authors: '',
    year: '',
    venue: '',
    sortBy: 'relevance',
    sortOrder: 'desc'
  });

  // Suchstatus
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState(null);
  const [results, setResults] = useState(null);

  // Handler für Änderungen an den Suchparametern
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSearchParams(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Suchfunktion
  const handleSearch = async (e) => {
    e.preventDefault();
    
    // Prüfen, ob mindestens ein Suchkriterium angegeben wurde
    if (!searchParams.query && !searchParams.authors && !searchParams.year && !searchParams.venue) {
      setError('Bitte geben Sie mindestens ein Suchkriterium ein.');
      return;
    }
    
    setIsSearching(true);
    setError(null);
    
    try {
      // API-Anfrage mit den Suchparametern
      const response = await axios.get('/api/v1/semantic-scholar/advanced-search', {
        params: {
          ...searchParams,
          fields: 'title,authors,year,venue,url,externalIds,abstract,citationCount'
        }
      });
      
      setResults(response.data);
      
      // Callback für übergeordnete Komponente
      if (onResultsFound) {
        onResultsFound(response.data);
      }
    } catch (error) {
      console.error('Fehler bei der Suche:', error);
      setError(error.response?.data?.error || 'Ein Fehler ist bei der Suche aufgetreten.');
    } finally {
      setIsSearching(false);
    }
  };

  // Zurücksetzen der Suchparameter
  const handleReset = () => {
    setSearchParams({
      query: '',
      authors: '',
      year: '',
      venue: '',
      sortBy: 'relevance',
      sortOrder: 'desc'
    });
    setResults(null);
    setError(null);
  };

  return (
    <div className="advanced-search-container">
      <h2>Erweiterte Suche</h2>
      
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSearch} className="advanced-search-form">
        <div className="form-group">
          <label htmlFor="query">Suchbegriffe:</label>
          <input
            type="text"
            id="query"
            name="query"
            value={searchParams.query}
            onChange={handleInputChange}
            placeholder="z.B. quantum computing"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="authors">Autor(en):</label>
          <input
            type="text"
            id="authors"
            name="authors"
            value={searchParams.authors}
            onChange={handleInputChange}
            placeholder="z.B. John Smith"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="year">Jahr oder Jahresbereich:</label>
          <input
            type="text"
            id="year"
            name="year"
            value={searchParams.year}
            onChange={handleInputChange}
            placeholder="z.B. 2020 oder 2018-2021"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="venue">Journal/Konferenz:</label>
          <input
            type="text"
            id="venue"
            name="venue"
            value={searchParams.venue}
            onChange={handleInputChange}
            placeholder="z.B. Nature"
            className="form-control"
          />
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="sortBy">Sortieren nach:</label>
            <select
              id="sortBy"
              name="sortBy"
              value={searchParams.sortBy}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="relevance">Relevanz</option>
              <option value="year">Jahr</option>
              <option value="citationCount">Zitationen</option>
            </select>
          </div>
          
          <div className="form-group">
            <label htmlFor="sortOrder">Reihenfolge:</label>
            <select
              id="sortOrder"
              name="sortOrder"
              value={searchParams.sortOrder}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="desc">Absteigend</option>
              <option value="asc">Aufsteigend</option>
            </select>
          </div>
        </div>
        
        <div className="form-actions">
          <button 
            type="submit" 
            className="btn btn-primary" 
            disabled={isSearching}
          >
            {isSearching ? 'Suche läuft...' : 'Suchen'}
          </button>
          <button 
            type="button" 
            className="btn btn-secondary" 
            onClick={handleReset}
            disabled={isSearching}
          >
            Zurücksetzen
          </button>
        </div>
      </form>
      
      {results && (
        <div className="search-results">
          <h3>Suchergebnisse ({results.total})</h3>
          
          {results.data && results.data.length > 0 ? (
            <div className="results-list">
              {results.data.map((paper, index) => (
                <div key={paper.paperId || index} className="result-item">
                  <h4>{paper.title}</h4>
                  <div className="paper-meta">
                    <span className="paper-authors">
                      {paper.authors?.map(a => a.name).join(', ')}
                    </span>
                    <span className="paper-venue">
                      {paper.venue} ({paper.year})
                    </span>
                    {paper.citationCount !== undefined && (
                      <span className="paper-citations">
                        Zitationen: {paper.citationCount}
                      </span>
                    )}
                  </div>
                  {paper.abstract && (
                    <p className="paper-abstract">
                      {paper.abstract.length > 300 
                        ? `${paper.abstract.substring(0, 300)}...` 
                        : paper.abstract}
                    </p>
                  )}
                  <div className="paper-links">
                    {paper.url && (
                      <a 
                        href={paper.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        Zum Paper
                      </a>
                    )}
                    {paper.externalIds?.DOI && (
                      <a 
                        href={`https://doi.org/${paper.externalIds.DOI}`} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        DOI: {paper.externalIds?.DOI}
                      </a>
                    )}
                    <button
                      className="btn btn-sm btn-primary create-nft-btn"
                      onClick={() => {
                        // Funktion zur Auswahl des Papers für die NFT-Erstellung
                        if (onSelectPaper) {
                          onSelectPaper(paper);
                        } else {
                          alert(`NFT für DOI ${paper.externalIds?.DOI || 'unbekannt'} erstellen`);
                        }
                      }}
                    >
                      Als NFT erstellen
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="no-results">Keine Ergebnisse gefunden.</p>
          )}
        </div>
      )}
      
      {/* CSS für die Komponente */}
      <style jsx>{`
        .advanced-search-container {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        
        .advanced-search-form {
          background-color: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
        }
        
        .form-group {
          margin-bottom: 15px;
        }
        
        .form-row {
          display: flex;
          gap: 15px;
        }
        
        .form-control {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #ced4da;
          border-radius: 4px;
          font-size: 16px;
        }
        
        .form-actions {
          display: flex;
          gap: 10px;
          margin-top: 20px;
        }
        
        .btn {
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 16px;
        }
        
        .btn-primary {
          background-color: #007bff;
          color: white;
        }
        
        .btn-secondary {
          background-color: #6c757d;
          color: white;
        }
        
        .btn:disabled {
          opacity: 0.65;
          cursor: not-allowed;
        }
        
        .error-message {
          background-color: #f8d7da;
          color: #721c24;
          padding: 10px;
          border-radius: 4px;
          margin-bottom: 15px;
        }
        
        .search-results {
          margin-top: 30px;
        }
        
        .result-item {
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 15px;
          background-color: white;
        }
        
        .result-item h4 {
          margin-top: 0;
          color: #007bff;
        }
        
        .paper-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 15px;
          margin-bottom: 10px;
          font-size: 14px;
          color: #6c757d;
        }
        
        .paper-abstract {
          font-size: 14px;
          line-height: 1.5;
          color: #212529;
        }
        
        .paper-links {
          display: flex;
          gap: 15px;
          margin-top: 10px;
        }
        
        .paper-link {
          color: #007bff;
          text-decoration: none;
        }
        
        .paper-link:hover {
          text-decoration: underline;
        }
        
        .btn-sm {
          padding: 4px 8px;
          font-size: 14px;
        }
        
        .create-nft-btn {
          margin-left: auto;
        }
        
        .no-results {
          text-align: center;
          color: #6c757d;
          font-style: italic;
        }
      `}</style>
    </div>
  );
};

export default AdvancedSearch;import React, { useState } from 'react';
import axios from 'axios';

/**
 * Erweiterte Suchkomponente für wissenschaftliche Publikationen
 * Inspiriert von Scinapse.io für eine verbesserte Benutzererfahrung
 */
const AdvancedSearch = ({ onResultsFound, onSelectPaper }) => {
  // Suchparameter
  const [searchParams, setSearchParams] = useState({
    query: '',
    authors: '',
    year: '',
    venue: '',
    sortBy: 'relevance',
    sortOrder: 'desc'
  });

  // Suchstatus
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState(null);
  const [results, setResults] = useState(null);

  // Handler für Änderungen an den Suchparametern
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSearchParams(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Suchfunktion
  const handleSearch = async (e) => {
    e.preventDefault();
    
    // Prüfen, ob mindestens ein Suchkriterium angegeben wurde
    if (!searchParams.query && !searchParams.authors && !searchParams.year && !searchParams.venue) {
      setError('Bitte geben Sie mindestens ein Suchkriterium ein.');
      return;
    }
    
    setIsSearching(true);
    setError(null);
    
    try {
      // API-Anfrage mit den Suchparametern
      const response = await axios.get('/api/v1/semantic-scholar/advanced-search', {
        params: {
          ...searchParams,
          fields: 'title,authors,year,venue,url,externalIds,abstract,citationCount'
        }
      });
      
      setResults(response.data);
      
      // Callback für übergeordnete Komponente
      if (onResultsFound) {
        onResultsFound(response.data);
      }
    } catch (error) {
      console.error('Fehler bei der Suche:', error);
      setError(error.response?.data?.error || 'Ein Fehler ist bei der Suche aufgetreten.');
    } finally {
      setIsSearching(false);
    }
  };

  // Zurücksetzen der Suchparameter
  const handleReset = () => {
    setSearchParams({
      query: '',
      authors: '',
      year: '',
      venue: '',
      sortBy: 'relevance',
      sortOrder: 'desc'
    });
    setResults(null);
    setError(null);
  };

  return (
    <div className="advanced-search-container">
      <h2>Erweiterte Suche</h2>
      
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSearch} className="advanced-search-form">
        <div className="form-group">
          <label htmlFor="query">Suchbegriffe:</label>
          <input
            type="text"
            id="query"
            name="query"
            value={searchParams.query}
            onChange={handleInputChange}
            placeholder="z.B. quantum computing"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="authors">Autor(en):</label>
          <input
            type="text"
            id="authors"
            name="authors"
            value={searchParams.authors}
            onChange={handleInputChange}
            placeholder="z.B. John Smith"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="year">Jahr oder Jahresbereich:</label>
          <input
            type="text"
            id="year"
            name="year"
            value={searchParams.year}
            onChange={handleInputChange}
            placeholder="z.B. 2020 oder 2018-2021"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="venue">Journal/Konferenz:</label>
          <input
            type="text"
            id="venue"
            name="venue"
            value={searchParams.venue}
            onChange={handleInputChange}
            placeholder="z.B. Nature"
            className="form-control"
          />
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="sortBy">Sortieren nach:</label>
            <select
              id="sortBy"
              name="sortBy"
              value={searchParams.sortBy}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="relevance">Relevanz</option>
              <option value="year">Jahr</option>
              <option value="citationCount">Zitationen</option>
            </select>
          </div>
          
          <div className="form-group">
            <label htmlFor="sortOrder">Reihenfolge:</label>
            <select
              id="sortOrder"
              name="sortOrder"
              value={searchParams.sortOrder}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="desc">Absteigend</option>
              <option value="asc">Aufsteigend</option>
            </select>
          </div>
        </div>
        
        <div className="form-actions">
          <button 
            type="submit" 
            className="btn btn-primary" 
            disabled={isSearching}
          >
            {isSearching ? 'Suche läuft...' : 'Suchen'}
          </button>
          <button 
            type="button" 
            className="btn btn-secondary" 
            onClick={handleReset}
            disabled={isSearching}
          >
            Zurücksetzen
          </button>
        </div>
      </form>
      
      {results && (
        <div className="search-results">
          <h3>Suchergebnisse ({results.total})</h3>
          
          {results.data && results.data.length > 0 ? (
            <div className="results-list">
              {results.data.map((paper, index) => (
                <div key={paper.paperId || index} className="result-item">
                  <h4>{paper.title}</h4>
                  <div className="paper-meta">
                    <span className="paper-authors">
                      {paper.authors?.map(a => a.name).join(', ')}
                    </span>
                    <span className="paper-venue">
                      {paper.venue} ({paper.year})
                    </span>
                    {paper.citationCount !== undefined && (
                      <span className="paper-citations">
                        Zitationen: {paper.citationCount}
                      </span>
                    )}
                  </div>
                  {paper.abstract && (
                    <p className="paper-abstract">
                      {paper.abstract.length > 300 
                        ? `${paper.abstract.substring(0, 300)}...` 
                        : paper.abstract}
                    </p>
                  )}
                  <div className="paper-links">
                    {paper.url && (
                      <a 
                        href={paper.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        Zum Paper
                      </a>
                    )}
                    {paper.externalIds?.DOI && (
                      <a 
                        href={`https://doi.org/${paper.externalIds.DOI}`} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        DOI: {paper.externalIds?.DOI}
                      </a>
                    )}
                    <button
                      className="btn btn-sm btn-primary create-nft-btn"
                      onClick={() => {
                        // Funktion zur Auswahl des Papers für die NFT-Erstellung
                        if (onSelectPaper) {
                          onSelectPaper(paper);
                        } else {
                          alert(`NFT für DOI ${paper.externalIds?.DOI || 'unbekannt'} erstellen`);
                        }
                      }}
                    >
                      Als NFT erstellen
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="no-results">Keine Ergebnisse gefunden.</p>
          )}
        </div>
      )}
      
      {/* CSS für die Komponente */}
      <style jsx>{`
        .advanced-search-container {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        
        .advanced-search-form {
          background-color: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
        }
        
        .form-group {
          margin-bottom: 15px;
        }
        
        .form-row {
          display: flex;
          gap: 15px;
        }
        
        .form-control {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #ced4da;
          border-radius: 4px;
          font-size: 16px;
        }
        
        .form-actions {
          display: flex;
          gap: 10px;
          margin-top: 20px;
        }
        
        .btn {
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 16px;
        }
        
        .btn-primary {
          background-color: #007bff;
          color: white;
        }
        
        .btn-secondary {
          background-color: #6c757d;
          color: white;
        }
        
        .btn:disabled {
          opacity: 0.65;
          cursor: not-allowed;
        }
        
        .error-message {
          background-color: #f8d7da;
          color: #721c24;
          padding: 10px;
          border-radius: 4px;
          margin-bottom: 15px;
        }
        
        .search-results {
          margin-top: 30px;
        }
        
        .result-item {
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 15px;
          background-color: white;
        }
        
        .result-item h4 {
          margin-top: 0;
          color: #007bff;
        }
        
        .paper-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 15px;
          margin-bottom: 10px;
          font-size: 14px;
          color: #6c757d;
        }
        
        .paper-abstract {
          font-size: 14px;
          line-height: 1.5;
          color: #212529;
        }
        
        .paper-links {
          display: flex;
          gap: 15px;
          margin-top: 10px;
        }
        
        .paper-link {
          color: #007bff;
          text-decoration: none;
        }
        
        .paper-link:hover {
          text-decoration: underline;
        }
        
        .btn-sm {
          padding: 4px 8px;
          font-size: 14px;
        }
        
        .create-nft-btn {
          margin-left: auto;
        }
        
        .no-results {
          text-align: center;
          color: #6c757d;
          font-style: italic;
        }
      `}</style>
    </div>
  );
};

export default AdvancedSearch;import React, { useState } from 'react';
import axios from 'axios';

/**
 * Erweiterte Suchkomponente für wissenschaftliche Publikationen
 * Inspiriert von Scinapse.io für eine verbesserte Benutzererfahrung
 */
const AdvancedSearch = ({ onResultsFound, onSelectPaper }) => {
  // Suchparameter
  const [searchParams, setSearchParams] = useState({
    query: '',
    authors: '',
    year: '',
    venue: '',
    sortBy: 'relevance',
    sortOrder: 'desc'
  });

  // Suchstatus
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState(null);
  const [results, setResults] = useState(null);

  // Handler für Änderungen an den Suchparametern
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSearchParams(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Suchfunktion
  const handleSearch = async (e) => {
    e.preventDefault();
    
    // Prüfen, ob mindestens ein Suchkriterium angegeben wurde
    if (!searchParams.query && !searchParams.authors && !searchParams.year && !searchParams.venue) {
      setError('Bitte geben Sie mindestens ein Suchkriterium ein.');
      return;
    }
    
    setIsSearching(true);
    setError(null);
    
    try {
      // API-Anfrage mit den Suchparametern
      const response = await axios.get('/api/v1/semantic-scholar/advanced-search', {
        params: {
          ...searchParams,
          fields: 'title,authors,year,venue,url,externalIds,abstract,citationCount'
        }
      });
      
      setResults(response.data);
      
      // Callback für übergeordnete Komponente
      if (onResultsFound) {
        onResultsFound(response.data);
      }
    } catch (error) {
      console.error('Fehler bei der Suche:', error);
      setError(error.response?.data?.error || 'Ein Fehler ist bei der Suche aufgetreten.');
    } finally {
      setIsSearching(false);
    }
  };

  // Zurücksetzen der Suchparameter
  const handleReset = () => {
    setSearchParams({
      query: '',
      authors: '',
      year: '',
      venue: '',
      sortBy: 'relevance',
      sortOrder: 'desc'
    });
    setResults(null);
    setError(null);
  };

  return (
    <div className="advanced-search-container">
      <h2>Erweiterte Suche</h2>
      
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSearch} className="advanced-search-form">
        <div className="form-group">
          <label htmlFor="query">Suchbegriffe:</label>
          <input
            type="text"
            id="query"
            name="query"
            value={searchParams.query}
            onChange={handleInputChange}
            placeholder="z.B. quantum computing"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="authors">Autor(en):</label>
          <input
            type="text"
            id="authors"
            name="authors"
            value={searchParams.authors}
            onChange={handleInputChange}
            placeholder="z.B. John Smith"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="year">Jahr oder Jahresbereich:</label>
          <input
            type="text"
            id="year"
            name="year"
            value={searchParams.year}
            onChange={handleInputChange}
            placeholder="z.B. 2020 oder 2018-2021"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="venue">Journal/Konferenz:</label>
          <input
            type="text"
            id="venue"
            name="venue"
            value={searchParams.venue}
            onChange={handleInputChange}
            placeholder="z.B. Nature"
            className="form-control"
          />
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="sortBy">Sortieren nach:</label>
            <select
              id="sortBy"
              name="sortBy"
              value={searchParams.sortBy}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="relevance">Relevanz</option>
              <option value="year">Jahr</option>
              <option value="citationCount">Zitationen</option>
            </select>
          </div>
          
          <div className="form-group">
            <label htmlFor="sortOrder">Reihenfolge:</label>
            <select
              id="sortOrder"
              name="sortOrder"
              value={searchParams.sortOrder}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="desc">Absteigend</option>
              <option value="asc">Aufsteigend</option>
            </select>
          </div>
        </div>
        
        <div className="form-actions">
          <button 
            type="submit" 
            className="btn btn-primary" 
            disabled={isSearching}
          >
            {isSearching ? 'Suche läuft...' : 'Suchen'}
          </button>
          <button 
            type="button" 
            className="btn btn-secondary" 
            onClick={handleReset}
            disabled={isSearching}
          >
            Zurücksetzen
          </button>
        </div>
      </form>
      
      {results && (
        <div className="search-results">
          <h3>Suchergebnisse ({results.total})</h3>
          
          {results.data && results.data.length > 0 ? (
            <div className="results-list">
              {results.data.map((paper, index) => (
                <div key={paper.paperId || index} className="result-item">
                  <h4>{paper.title}</h4>
                  <div className="paper-meta">
                    <span className="paper-authors">
                      {paper.authors?.map(a => a.name).join(', ')}
                    </span>
                    <span className="paper-venue">
                      {paper.venue} ({paper.year})
                    </span>
                    {paper.citationCount !== undefined && (
                      <span className="paper-citations">
                        Zitationen: {paper.citationCount}
                      </span>
                    )}
                  </div>
                  {paper.abstract && (
                    <p className="paper-abstract">
                      {paper.abstract.length > 300 
                        ? `${paper.abstract.substring(0, 300)}...` 
                        : paper.abstract}
                    </p>
                  )}
                  <div className="paper-links">
                    {paper.url && (
                      <a 
                        href={paper.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        Zum Paper
                      </a>
                    )}
                    {paper.externalIds?.DOI && (
                      <a 
                        href={`https://doi.org/${paper.externalIds.DOI}`} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        DOI: {paper.externalIds?.DOI}
                      </a>
                    )}
                    <button
                      className="btn btn-sm btn-primary create-nft-btn"
                      onClick={() => {
                        // Funktion zur Auswahl des Papers für die NFT-Erstellung
                        if (onSelectPaper) {
                          onSelectPaper(paper);
                        } else {
                          alert(`NFT für DOI ${paper.externalIds?.DOI || 'unbekannt'} erstellen`);
                        }
                      }}
                    >
                      Als NFT erstellen
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="no-results">Keine Ergebnisse gefunden.</p>
          )}
        </div>
      )}
      
      {/* CSS für die Komponente */}
      <style jsx>{`
        .advanced-search-container {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        
        .advanced-search-form {
          background-color: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
        }
        
        .form-group {
          margin-bottom: 15px;
        }
        
        .form-row {
          display: flex;
          gap: 15px;
        }
        
        .form-control {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #ced4da;
          border-radius: 4px;
          font-size: 16px;
        }
        
        .form-actions {
          display: flex;
          gap: 10px;
          margin-top: 20px;
        }
        
        .btn {
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 16px;
        }
        
        .btn-primary {
          background-color: #007bff;
          color: white;
        }
        
        .btn-secondary {
          background-color: #6c757d;
          color: white;
        }
        
        .btn:disabled {
          opacity: 0.65;
          cursor: not-allowed;
        }
        
        .error-message {
          background-color: #f8d7da;
          color: #721c24;
          padding: 10px;
          border-radius: 4px;
          margin-bottom: 15px;
        }
        
        .search-results {
          margin-top: 30px;
        }
        
        .result-item {
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 15px;
          background-color: white;
        }
        
        .result-item h4 {
          margin-top: 0;
          color: #007bff;
        }
        
        .paper-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 15px;
          margin-bottom: 10px;
          font-size: 14px;
          color: #6c757d;
        }
        
        .paper-abstract {
          font-size: 14px;
          line-height: 1.5;
          color: #212529;
        }
        
        .paper-links {
          display: flex;
          gap: 15px;
          margin-top: 10px;
        }
        
        .paper-link {
          color: #007bff;
          text-decoration: none;
        }
        
        .paper-link:hover {
          text-decoration: underline;
        }
        
        .btn-sm {
          padding: 4px 8px;
          font-size: 14px;
        }
        
        .create-nft-btn {
          margin-left: auto;
        }
        
        .no-results {
          text-align: center;
          color: #6c757d;
          font-style: italic;
        }
      `}</style>
    </div>
  );
};

export default AdvancedSearch;import React, { useState } from 'react';
import axios from 'axios';

/**
 * Erweiterte Suchkomponente für wissenschaftliche Publikationen
 * Inspiriert von Scinapse.io für eine verbesserte Benutzererfahrung
 */
const AdvancedSearch = ({ onResultsFound, onSelectPaper }) => {
  // Suchparameter
  const [searchParams, setSearchParams] = useState({
    query: '',
    authors: '',
    year: '',
    venue: '',
    sortBy: 'relevance',
    sortOrder: 'desc'
  });

  // Suchstatus
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState(null);
  const [results, setResults] = useState(null);

  // Handler für Änderungen an den Suchparametern
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSearchParams(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Suchfunktion
  const handleSearch = async (e) => {
    e.preventDefault();
    
    // Prüfen, ob mindestens ein Suchkriterium angegeben wurde
    if (!searchParams.query && !searchParams.authors && !searchParams.year && !searchParams.venue) {
      setError('Bitte geben Sie mindestens ein Suchkriterium ein.');
      return;
    }
    
    setIsSearching(true);
    setError(null);
    
    try {
      // API-Anfrage mit den Suchparametern
      const response = await axios.get('/api/v1/semantic-scholar/advanced-search', {
        params: {
          ...searchParams,
          fields: 'title,authors,year,venue,url,externalIds,abstract,citationCount'
        }
      });
      
      setResults(response.data);
      
      // Callback für übergeordnete Komponente
      if (onResultsFound) {
        onResultsFound(response.data);
      }
    } catch (error) {
      console.error('Fehler bei der Suche:', error);
      setError(error.response?.data?.error || 'Ein Fehler ist bei der Suche aufgetreten.');
    } finally {
      setIsSearching(false);
    }
  };

  // Zurücksetzen der Suchparameter
  const handleReset = () => {
    setSearchParams({
      query: '',
      authors: '',
      year: '',
      venue: '',
      sortBy: 'relevance',
      sortOrder: 'desc'
    });
    setResults(null);
    setError(null);
  };

  return (
    <div className="advanced-search-container">
      <h2>Erweiterte Suche</h2>
      
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSearch} className="advanced-search-form">
        <div className="form-group">
          <label htmlFor="query">Suchbegriffe:</label>
          <input
            type="text"
            id="query"
            name="query"
            value={searchParams.query}
            onChange={handleInputChange}
            placeholder="z.B. quantum computing"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="authors">Autor(en):</label>
          <input
            type="text"
            id="authors"
            name="authors"
            value={searchParams.authors}
            onChange={handleInputChange}
            placeholder="z.B. John Smith"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="year">Jahr oder Jahresbereich:</label>
          <input
            type="text"
            id="year"
            name="year"
            value={searchParams.year}
            onChange={handleInputChange}
            placeholder="z.B. 2020 oder 2018-2021"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="venue">Journal/Konferenz:</label>
          <input
            type="text"
            id="venue"
            name="venue"
            value={searchParams.venue}
            onChange={handleInputChange}
            placeholder="z.B. Nature"
            className="form-control"
          />
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="sortBy">Sortieren nach:</label>
            <select
              id="sortBy"
              name="sortBy"
              value={searchParams.sortBy}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="relevance">Relevanz</option>
              <option value="year">Jahr</option>
              <option value="citationCount">Zitationen</option>
            </select>
          </div>
          
          <div className="form-group">
            <label htmlFor="sortOrder">Reihenfolge:</label>
            <select
              id="sortOrder"
              name="sortOrder"
              value={searchParams.sortOrder}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="desc">Absteigend</option>
              <option value="asc">Aufsteigend</option>
            </select>
          </div>
        </div>
        
        <div className="form-actions">
          <button 
            type="submit" 
            className="btn btn-primary" 
            disabled={isSearching}
          >
            {isSearching ? 'Suche läuft...' : 'Suchen'}
          </button>
          <button 
            type="button" 
            className="btn btn-secondary" 
            onClick={handleReset}
            disabled={isSearching}
          >
            Zurücksetzen
          </button>
        </div>
      </form>
      
      {results && (
        <div className="search-results">
          <h3>Suchergebnisse ({results.total})</h3>
          
          {results.data && results.data.length > 0 ? (
            <div className="results-list">
              {results.data.map((paper, index) => (
                <div key={paper.paperId || index} className="result-item">
                  <h4>{paper.title}</h4>
                  <div className="paper-meta">
                    <span className="paper-authors">
                      {paper.authors?.map(a => a.name).join(', ')}
                    </span>
                    <span className="paper-venue">
                      {paper.venue} ({paper.year})
                    </span>
                    {paper.citationCount !== undefined && (
                      <span className="paper-citations">
                        Zitationen: {paper.citationCount}
                      </span>
                    )}
                  </div>
                  {paper.abstract && (
                    <p className="paper-abstract">
                      {paper.abstract.length > 300 
                        ? `${paper.abstract.substring(0, 300)}...` 
                        : paper.abstract}
                    </p>
                  )}
                  <div className="paper-links">
                    {paper.url && (
                      <a 
                        href={paper.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        Zum Paper
                      </a>
                    )}
                    {paper.externalIds?.DOI && (
                      <a 
                        href={`https://doi.org/${paper.externalIds.DOI}`} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        DOI: {paper.externalIds?.DOI}
                      </a>
                    )}
                    <button
                      className="btn btn-sm btn-primary create-nft-btn"
                      onClick={() => {
                        // Funktion zur Auswahl des Papers für die NFT-Erstellung
                        if (onSelectPaper) {
                          onSelectPaper(paper);
                        } else {
                          alert(`NFT für DOI ${paper.externalIds?.DOI || 'unbekannt'} erstellen`);
                        }
                      }}
                    >
                      Als NFT erstellen
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="no-results">Keine Ergebnisse gefunden.</p>
          )}
        </div>
      )}
      
      {/* CSS für die Komponente */}
      <style jsx>{`
        .advanced-search-container {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        
        .advanced-search-form {
          background-color: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
        }
        
        .form-group {
          margin-bottom: 15px;
        }
        
        .form-row {
          display: flex;
          gap: 15px;
        }
        
        .form-control {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #ced4da;
          border-radius: 4px;
          font-size: 16px;
        }
        
        .form-actions {
          display: flex;
          gap: 10px;
          margin-top: 20px;
        }
        
        .btn {
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 16px;
        }
        
        .btn-primary {
          background-color: #007bff;
          color: white;
        }
        
        .btn-secondary {
          background-color: #6c757d;
          color: white;
        }
        
        .btn:disabled {
          opacity: 0.65;
          cursor: not-allowed;
        }
        
        .error-message {
          background-color: #f8d7da;
          color: #721c24;
          padding: 10px;
          border-radius: 4px;
          margin-bottom: 15px;
        }
        
        .search-results {
          margin-top: 30px;
        }
        
        .result-item {
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 15px;
          background-color: white;
        }
        
        .result-item h4 {
          margin-top: 0;
          color: #007bff;
        }
        
        .paper-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 15px;
          margin-bottom: 10px;
          font-size: 14px;
          color: #6c757d;
        }
        
        .paper-abstract {
          font-size: 14px;
          line-height: 1.5;
          color: #212529;
        }
        
        .paper-links {
          display: flex;
          gap: 15px;
          margin-top: 10px;
        }
        
        .paper-link {
          color: #007bff;
          text-decoration: none;
        }
        
        .paper-link:hover {
          text-decoration: underline;
        }
        
        .btn-sm {
          padding: 4px 8px;
          font-size: 14px;
        }
        
        .create-nft-btn {
          margin-left: auto;
        }
        
        .no-results {
          text-align: center;
          color: #6c757d;
          font-style: italic;
        }
      `}</style>
    </div>
  );
};

export default AdvancedSearch;import React, { useState } from 'react';
import axios from 'axios';

/**
 * Erweiterte Suchkomponente für wissenschaftliche Publikationen
 * Inspiriert von Scinapse.io für eine verbesserte Benutzererfahrung
 */
const AdvancedSearch = ({ onResultsFound, onSelectPaper }) => {
  // Suchparameter
  const [searchParams, setSearchParams] = useState({
    query: '',
    authors: '',
    year: '',
    venue: '',
    sortBy: 'relevance',
    sortOrder: 'desc'
  });

  // Suchstatus
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState(null);
  const [results, setResults] = useState(null);

  // Handler für Änderungen an den Suchparametern
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSearchParams(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Suchfunktion
  const handleSearch = async (e) => {
    e.preventDefault();
    
    // Prüfen, ob mindestens ein Suchkriterium angegeben wurde
    if (!searchParams.query && !searchParams.authors && !searchParams.year && !searchParams.venue) {
      setError('Bitte geben Sie mindestens ein Suchkriterium ein.');
      return;
    }
    
    setIsSearching(true);
    setError(null);
    
    try {
      // API-Anfrage mit den Suchparametern
      const response = await axios.get('/api/v1/semantic-scholar/advanced-search', {
        params: {
          ...searchParams,
          fields: 'title,authors,year,venue,url,externalIds,abstract,citationCount'
        }
      });
      
      setResults(response.data);
      
      // Callback für übergeordnete Komponente
      if (onResultsFound) {
        onResultsFound(response.data);
      }
    } catch (error) {
      console.error('Fehler bei der Suche:', error);
      setError(error.response?.data?.error || 'Ein Fehler ist bei der Suche aufgetreten.');
    } finally {
      setIsSearching(false);
    }
  };

  // Zurücksetzen der Suchparameter
  const handleReset = () => {
    setSearchParams({
      query: '',
      authors: '',
      year: '',
      venue: '',
      sortBy: 'relevance',
      sortOrder: 'desc'
    });
    setResults(null);
    setError(null);
  };

  return (
    <div className="advanced-search-container">
      <h2>Erweiterte Suche</h2>
      
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSearch} className="advanced-search-form">
        <div className="form-group">
          <label htmlFor="query">Suchbegriffe:</label>
          <input
            type="text"
            id="query"
            name="query"
            value={searchParams.query}
            onChange={handleInputChange}
            placeholder="z.B. quantum computing"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="authors">Autor(en):</label>
          <input
            type="text"
            id="authors"
            name="authors"
            value={searchParams.authors}
            onChange={handleInputChange}
            placeholder="z.B. John Smith"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="year">Jahr oder Jahresbereich:</label>
          <input
            type="text"
            id="year"
            name="year"
            value={searchParams.year}
            onChange={handleInputChange}
            placeholder="z.B. 2020 oder 2018-2021"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="venue">Journal/Konferenz:</label>
          <input
            type="text"
            id="venue"
            name="venue"
            value={searchParams.venue}
            onChange={handleInputChange}
            placeholder="z.B. Nature"
            className="form-control"
          />
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="sortBy">Sortieren nach:</label>
            <select
              id="sortBy"
              name="sortBy"
              value={searchParams.sortBy}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="relevance">Relevanz</option>
              <option value="year">Jahr</option>
              <option value="citationCount">Zitationen</option>
            </select>
          </div>
          
          <div className="form-group">
            <label htmlFor="sortOrder">Reihenfolge:</label>
            <select
              id="sortOrder"
              name="sortOrder"
              value={searchParams.sortOrder}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="desc">Absteigend</option>
              <option value="asc">Aufsteigend</option>
            </select>
          </div>
        </div>
        
        <div className="form-actions">
          <button 
            type="submit" 
            className="btn btn-primary" 
            disabled={isSearching}
          >
            {isSearching ? 'Suche läuft...' : 'Suchen'}
          </button>
          <button 
            type="button" 
            className="btn btn-secondary" 
            onClick={handleReset}
            disabled={isSearching}
          >
            Zurücksetzen
          </button>
        </div>
      </form>
      
      {results && (
        <div className="search-results">
          <h3>Suchergebnisse ({results.total})</h3>
          
          {results.data && results.data.length > 0 ? (
            <div className="results-list">
              {results.data.map((paper, index) => (
                <div key={paper.paperId || index} className="result-item">
                  <h4>{paper.title}</h4>
                  <div className="paper-meta">
                    <span className="paper-authors">
                      {paper.authors?.map(a => a.name).join(', ')}
                    </span>
                    <span className="paper-venue">
                      {paper.venue} ({paper.year})
                    </span>
                    {paper.citationCount !== undefined && (
                      <span className="paper-citations">
                        Zitationen: {paper.citationCount}
                      </span>
                    )}
                  </div>
                  {paper.abstract && (
                    <p className="paper-abstract">
                      {paper.abstract.length > 300 
                        ? `${paper.abstract.substring(0, 300)}...` 
                        : paper.abstract}
                    </p>
                  )}
                  <div className="paper-links">
                    {paper.url && (
                      <a 
                        href={paper.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        Zum Paper
                      </a>
                    )}
                    {paper.externalIds?.DOI && (
                      <a 
                        href={`https://doi.org/${paper.externalIds.DOI}`} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        DOI: {paper.externalIds?.DOI}
                      </a>
                    )}
                    <button
                      className="btn btn-sm btn-primary create-nft-btn"
                      onClick={() => {
                        // Funktion zur Auswahl des Papers für die NFT-Erstellung
                        if (onSelectPaper) {
                          onSelectPaper(paper);
                        } else {
                          alert(`NFT für DOI ${paper.externalIds?.DOI || 'unbekannt'} erstellen`);
                        }
                      }}
                    >
                      Als NFT erstellen
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="no-results">Keine Ergebnisse gefunden.</p>
          )}
        </div>
      )}
      
      {/* CSS für die Komponente */}
      <style jsx>{`
        .advanced-search-container {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        
        .advanced-search-form {
          background-color: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
        }
        
        .form-group {
          margin-bottom: 15px;
        }
        
        .form-row {
          display: flex;
          gap: 15px;
        }
        
        .form-control {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #ced4da;
          border-radius: 4px;
          font-size: 16px;
        }
        
        .form-actions {
          display: flex;
          gap: 10px;
          margin-top: 20px;
        }
        
        .btn {
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 16px;
        }
        
        .btn-primary {
          background-color: #007bff;
          color: white;
        }
        
        .btn-secondary {
          background-color: #6c757d;
          color: white;
        }
        
        .btn:disabled {
          opacity: 0.65;
          cursor: not-allowed;
        }
        
        .error-message {
          background-color: #f8d7da;
          color: #721c24;
          padding: 10px;
          border-radius: 4px;
          margin-bottom: 15px;
        }
        
        .search-results {
          margin-top: 30px;
        }
        
        .result-item {
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 15px;
          background-color: white;
        }
        
        .result-item h4 {
          margin-top: 0;
          color: #007bff;
        }
        
        .paper-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 15px;
          margin-bottom: 10px;
          font-size: 14px;
          color: #6c757d;
        }
        
        .paper-abstract {
          font-size: 14px;
          line-height: 1.5;
          color: #212529;
        }
        
        .paper-links {
          display: flex;
          gap: 15px;
          margin-top: 10px;
        }
        
        .paper-link {
          color: #007bff;
          text-decoration: none;
        }
        
        .paper-link:hover {
          text-decoration: underline;
        }
        
        .btn-sm {
          padding: 4px 8px;
          font-size: 14px;
        }
        
        .create-nft-btn {
          margin-left: auto;
        }
        
        .no-results {
          text-align: center;
          color: #6c757d;
          font-style: italic;
        }
      `}</style>
    </div>
  );
};

export default AdvancedSearch;import React, { useState } from 'react';
import axios from 'axios';

/**
 * Erweiterte Suchkomponente für wissenschaftliche Publikationen
 * Inspiriert von Scinapse.io für eine verbesserte Benutzererfahrung
 */
const AdvancedSearch = ({ onResultsFound, onSelectPaper }) => {
  // Suchparameter
  const [searchParams, setSearchParams] = useState({
    query: '',
    authors: '',
    year: '',
    venue: '',
    sortBy: 'relevance',
    sortOrder: 'desc'
  });

  // Suchstatus
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState(null);
  const [results, setResults] = useState(null);

  // Handler für Änderungen an den Suchparametern
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSearchParams(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Suchfunktion
  const handleSearch = async (e) => {
    e.preventDefault();
    
    // Prüfen, ob mindestens ein Suchkriterium angegeben wurde
    if (!searchParams.query && !searchParams.authors && !searchParams.year && !searchParams.venue) {
      setError('Bitte geben Sie mindestens ein Suchkriterium ein.');
      return;
    }
    
    setIsSearching(true);
    setError(null);
    
    try {
      // API-Anfrage mit den Suchparametern
      const response = await axios.get('/api/v1/semantic-scholar/advanced-search', {
        params: {
          ...searchParams,
          fields: 'title,authors,year,venue,url,externalIds,abstract,citationCount'
        }
      });
      
      setResults(response.data);
      
      // Callback für übergeordnete Komponente
      if (onResultsFound) {
        onResultsFound(response.data);
      }
    } catch (error) {
      console.error('Fehler bei der Suche:', error);
      setError(error.response?.data?.error || 'Ein Fehler ist bei der Suche aufgetreten.');
    } finally {
      setIsSearching(false);
    }
  };

  // Zurücksetzen der Suchparameter
  const handleReset = () => {
    setSearchParams({
      query: '',
      authors: '',
      year: '',
      venue: '',
      sortBy: 'relevance',
      sortOrder: 'desc'
    });
    setResults(null);
    setError(null);
  };

  return (
    <div className="advanced-search-container">
      <h2>Erweiterte Suche</h2>
      
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSearch} className="advanced-search-form">
        <div className="form-group">
          <label htmlFor="query">Suchbegriffe:</label>
          <input
            type="text"
            id="query"
            name="query"
            value={searchParams.query}
            onChange={handleInputChange}
            placeholder="z.B. quantum computing"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="authors">Autor(en):</label>
          <input
            type="text"
            id="authors"
            name="authors"
            value={searchParams.authors}
            onChange={handleInputChange}
            placeholder="z.B. John Smith"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="year">Jahr oder Jahresbereich:</label>
          <input
            type="text"
            id="year"
            name="year"
            value={searchParams.year}
            onChange={handleInputChange}
            placeholder="z.B. 2020 oder 2018-2021"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="venue">Journal/Konferenz:</label>
          <input
            type="text"
            id="venue"
            name="venue"
            value={searchParams.venue}
            onChange={handleInputChange}
            placeholder="z.B. Nature"
            className="form-control"
          />
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="sortBy">Sortieren nach:</label>
            <select
              id="sortBy"
              name="sortBy"
              value={searchParams.sortBy}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="relevance">Relevanz</option>
              <option value="year">Jahr</option>
              <option value="citationCount">Zitationen</option>
            </select>
          </div>
          
          <div className="form-group">
            <label htmlFor="sortOrder">Reihenfolge:</label>
            <select
              id="sortOrder"
              name="sortOrder"
              value={searchParams.sortOrder}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="desc">Absteigend</option>
              <option value="asc">Aufsteigend</option>
            </select>
          </div>
        </div>
        
        <div className="form-actions">
          <button 
            type="submit" 
            className="btn btn-primary" 
            disabled={isSearching}
          >
            {isSearching ? 'Suche läuft...' : 'Suchen'}
          </button>
          <button 
            type="button" 
            className="btn btn-secondary" 
            onClick={handleReset}
            disabled={isSearching}
          >
            Zurücksetzen
          </button>
        </div>
      </form>
      
      {results && (
        <div className="search-results">
          <h3>Suchergebnisse ({results.total})</h3>
          
          {results.data && results.data.length > 0 ? (
            <div className="results-list">
              {results.data.map((paper, index) => (
                <div key={paper.paperId || index} className="result-item">
                  <h4>{paper.title}</h4>
                  <div className="paper-meta">
                    <span className="paper-authors">
                      {paper.authors?.map(a => a.name).join(', ')}
                    </span>
                    <span className="paper-venue">
                      {paper.venue} ({paper.year})
                    </span>
                    {paper.citationCount !== undefined && (
                      <span className="paper-citations">
                        Zitationen: {paper.citationCount}
                      </span>
                    )}
                  </div>
                  {paper.abstract && (
                    <p className="paper-abstract">
                      {paper.abstract.length > 300 
                        ? `${paper.abstract.substring(0, 300)}...` 
                        : paper.abstract}
                    </p>
                  )}
                  <div className="paper-links">
                    {paper.url && (
                      <a 
                        href={paper.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        Zum Paper
                      </a>
                    )}
                    {paper.externalIds?.DOI && (
                      <a 
                        href={`https://doi.org/${paper.externalIds.DOI}`} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        DOI: {paper.externalIds?.DOI}
                      </a>
                    )}
                    <button
                      className="btn btn-sm btn-primary create-nft-btn"
                      onClick={() => {
                        // Funktion zur Auswahl des Papers für die NFT-Erstellung
                        if (onSelectPaper) {
                          onSelectPaper(paper);
                        } else {
                          alert(`NFT für DOI ${paper.externalIds?.DOI || 'unbekannt'} erstellen`);
                        }
                      }}
                    >
                      Als NFT erstellen
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="no-results">Keine Ergebnisse gefunden.</p>
          )}
        </div>
      )}
      
      {/* CSS für die Komponente */}
      <style jsx>{`
        .advanced-search-container {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        
        .advanced-search-form {
          background-color: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
        }
        
        .form-group {
          margin-bottom: 15px;
        }
        
        .form-row {
          display: flex;
          gap: 15px;
        }
        
        .form-control {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #ced4da;
          border-radius: 4px;
          font-size: 16px;
        }
        
        .form-actions {
          display: flex;
          gap: 10px;
          margin-top: 20px;
        }
        
        .btn {
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 16px;
        }
        
        .btn-primary {
          background-color: #007bff;
          color: white;
        }
        
        .btn-secondary {
          background-color: #6c757d;
          color: white;
        }
        
        .btn:disabled {
          opacity: 0.65;
          cursor: not-allowed;
        }
        
        .error-message {
          background-color: #f8d7da;
          color: #721c24;
          padding: 10px;
          border-radius: 4px;
          margin-bottom: 15px;
        }
        
        .search-results {
          margin-top: 30px;
        }
        
        .result-item {
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 15px;
          background-color: white;
        }
        
        .result-item h4 {
          margin-top: 0;
          color: #007bff;
        }
        
        .paper-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 15px;
          margin-bottom: 10px;
          font-size: 14px;
          color: #6c757d;
        }
        
        .paper-abstract {
          font-size: 14px;
          line-height: 1.5;
          color: #212529;
        }
        
        .paper-links {
          display: flex;
          gap: 15px;
          margin-top: 10px;
        }
        
        .paper-link {
          color: #007bff;
          text-decoration: none;
        }
        
        .paper-link:hover {
          text-decoration: underline;
        }
        
        .btn-sm {
          padding: 4px 8px;
          font-size: 14px;
        }
        
        .create-nft-btn {
          margin-left: auto;
        }
        
        .no-results {
          text-align: center;
          color: #6c757d;
          font-style: italic;
        }
      `}</style>
    </div>
  );
};

export default AdvancedSearch;import React, { useState } from 'react';
import axios from 'axios';

/**
 * Erweiterte Suchkomponente für wissenschaftliche Publikationen
 * Inspiriert von Scinapse.io für eine verbesserte Benutzererfahrung
 */
const AdvancedSearch = ({ onResultsFound, onSelectPaper }) => {
  // Suchparameter
  const [searchParams, setSearchParams] = useState({
    query: '',
    authors: '',
    year: '',
    venue: '',
    sortBy: 'relevance',
    sortOrder: 'desc'
  });

  // Suchstatus
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState(null);
  const [results, setResults] = useState(null);

  // Handler für Änderungen an den Suchparametern
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSearchParams(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Suchfunktion
  const handleSearch = async (e) => {
    e.preventDefault();
    
    // Prüfen, ob mindestens ein Suchkriterium angegeben wurde
    if (!searchParams.query && !searchParams.authors && !searchParams.year && !searchParams.venue) {
      setError('Bitte geben Sie mindestens ein Suchkriterium ein.');
      return;
    }
    
    setIsSearching(true);
    setError(null);
    
    try {
      // API-Anfrage mit den Suchparametern
      const response = await axios.get('/api/v1/semantic-scholar/advanced-search', {
        params: {
          ...searchParams,
          fields: 'title,authors,year,venue,url,externalIds,abstract,citationCount'
        }
      });
      
      setResults(response.data);
      
      // Callback für übergeordnete Komponente
      if (onResultsFound) {
        onResultsFound(response.data);
      }
    } catch (error) {
      console.error('Fehler bei der Suche:', error);
      setError(error.response?.data?.error || 'Ein Fehler ist bei der Suche aufgetreten.');
    } finally {
      setIsSearching(false);
    }
  };

  // Zurücksetzen der Suchparameter
  const handleReset = () => {
    setSearchParams({
      query: '',
      authors: '',
      year: '',
      venue: '',
      sortBy: 'relevance',
      sortOrder: 'desc'
    });
    setResults(null);
    setError(null);
  };

  return (
    <div className="advanced-search-container">
      <h2>Erweiterte Suche</h2>
      
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSearch} className="advanced-search-form">
        <div className="form-group">
          <label htmlFor="query">Suchbegriffe:</label>
          <input
            type="text"
            id="query"
            name="query"
            value={searchParams.query}
            onChange={handleInputChange}
            placeholder="z.B. quantum computing"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="authors">Autor(en):</label>
          <input
            type="text"
            id="authors"
            name="authors"
            value={searchParams.authors}
            onChange={handleInputChange}
            placeholder="z.B. John Smith"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="year">Jahr oder Jahresbereich:</label>
          <input
            type="text"
            id="year"
            name="year"
            value={searchParams.year}
            onChange={handleInputChange}
            placeholder="z.B. 2020 oder 2018-2021"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="venue">Journal/Konferenz:</label>
          <input
            type="text"
            id="venue"
            name="venue"
            value={searchParams.venue}
            onChange={handleInputChange}
            placeholder="z.B. Nature"
            className="form-control"
          />
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="sortBy">Sortieren nach:</label>
            <select
              id="sortBy"
              name="sortBy"
              value={searchParams.sortBy}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="relevance">Relevanz</option>
              <option value="year">Jahr</option>
              <option value="citationCount">Zitationen</option>
            </select>
          </div>
          
          <div className="form-group">
            <label htmlFor="sortOrder">Reihenfolge:</label>
            <select
              id="sortOrder"
              name="sortOrder"
              value={searchParams.sortOrder}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="desc">Absteigend</option>
              <option value="asc">Aufsteigend</option>
            </select>
          </div>
        </div>
        
        <div className="form-actions">
          <button 
            type="submit" 
            className="btn btn-primary" 
            disabled={isSearching}
          >
            {isSearching ? 'Suche läuft...' : 'Suchen'}
          </button>
          <button 
            type="button" 
            className="btn btn-secondary" 
            onClick={handleReset}
            disabled={isSearching}
          >
            Zurücksetzen
          </button>
        </div>
      </form>
      
      {results && (
        <div className="search-results">
          <h3>Suchergebnisse ({results.total})</h3>
          
          {results.data && results.data.length > 0 ? (
            <div className="results-list">
              {results.data.map((paper, index) => (
                <div key={paper.paperId || index} className="result-item">
                  <h4>{paper.title}</h4>
                  <div className="paper-meta">
                    <span className="paper-authors">
                      {paper.authors?.map(a => a.name).join(', ')}
                    </span>
                    <span className="paper-venue">
                      {paper.venue} ({paper.year})
                    </span>
                    {paper.citationCount !== undefined && (
                      <span className="paper-citations">
                        Zitationen: {paper.citationCount}
                      </span>
                    )}
                  </div>
                  {paper.abstract && (
                    <p className="paper-abstract">
                      {paper.abstract.length > 300 
                        ? `${paper.abstract.substring(0, 300)}...` 
                        : paper.abstract}
                    </p>
                  )}
                  <div className="paper-links">
                    {paper.url && (
                      <a 
                        href={paper.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        Zum Paper
                      </a>
                    )}
                    {paper.externalIds?.DOI && (
                      <a 
                        href={`https://doi.org/${paper.externalIds.DOI}`} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        DOI: {paper.externalIds?.DOI}
                      </a>
                    )}
                    <button
                      className="btn btn-sm btn-primary create-nft-btn"
                      onClick={() => {
                        // Funktion zur Auswahl des Papers für die NFT-Erstellung
                        if (onSelectPaper) {
                          onSelectPaper(paper);
                        } else {
                          alert(`NFT für DOI ${paper.externalIds?.DOI || 'unbekannt'} erstellen`);
                        }
                      }}
                    >
                      Als NFT erstellen
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="no-results">Keine Ergebnisse gefunden.</p>
          )}
        </div>
      )}
      
      {/* CSS für die Komponente */}
      <style jsx>{`
        .advanced-search-container {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        
        .advanced-search-form {
          background-color: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
        }
        
        .form-group {
          margin-bottom: 15px;
        }
        
        .form-row {
          display: flex;
          gap: 15px;
        }
        
        .form-control {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #ced4da;
          border-radius: 4px;
          font-size: 16px;
        }
        
        .form-actions {
          display: flex;
          gap: 10px;
          margin-top: 20px;
        }
        
        .btn {
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 16px;
        }
        
        .btn-primary {
          background-color: #007bff;
          color: white;
        }
        
        .btn-secondary {
          background-color: #6c757d;
          color: white;
        }
        
        .btn:disabled {
          opacity: 0.65;
          cursor: not-allowed;
        }
        
        .error-message {
          background-color: #f8d7da;
          color: #721c24;
          padding: 10px;
          border-radius: 4px;
          margin-bottom: 15px;
        }
        
        .search-results {
          margin-top: 30px;
        }
        
        .result-item {
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 15px;
          background-color: white;
        }
        
        .result-item h4 {
          margin-top: 0;
          color: #007bff;
        }
        
        .paper-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 15px;
          margin-bottom: 10px;
          font-size: 14px;
          color: #6c757d;
        }
        
        .paper-abstract {
          font-size: 14px;
          line-height: 1.5;
          color: #212529;
        }
        
        .paper-links {
          display: flex;
          gap: 15px;
          margin-top: 10px;
        }
        
        .paper-link {
          color: #007bff;
          text-decoration: none;
        }
        
        .paper-link:hover {
          text-decoration: underline;
        }
        
        .btn-sm {
          padding: 4px 8px;
          font-size: 14px;
        }
        
        .create-nft-btn {
          margin-left: auto;
        }
        
        .no-results {
          text-align: center;
          color: #6c757d;
          font-style: italic;
        }
      `}</style>
    </div>
  );
};

export default AdvancedSearch;import React, { useState } from 'react';
import axios from 'axios';

/**
 * Erweiterte Suchkomponente für wissenschaftliche Publikationen
 * Inspiriert von Scinapse.io für eine verbesserte Benutzererfahrung
 */
const AdvancedSearch = ({ onResultsFound, onSelectPaper }) => {
  // Suchparameter
  const [searchParams, setSearchParams] = useState({
    query: '',
    authors: '',
    year: '',
    venue: '',
    sortBy: 'relevance',
    sortOrder: 'desc'
  });

  // Suchstatus
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState(null);
  const [results, setResults] = useState(null);

  // Handler für Änderungen an den Suchparametern
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSearchParams(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Suchfunktion
  const handleSearch = async (e) => {
    e.preventDefault();
    
    // Prüfen, ob mindestens ein Suchkriterium angegeben wurde
    if (!searchParams.query && !searchParams.authors && !searchParams.year && !searchParams.venue) {
      setError('Bitte geben Sie mindestens ein Suchkriterium ein.');
      return;
    }
    
    setIsSearching(true);
    setError(null);
    
    try {
      // API-Anfrage mit den Suchparametern
      const response = await axios.get('/api/v1/semantic-scholar/advanced-search', {
        params: {
          ...searchParams,
          fields: 'title,authors,year,venue,url,externalIds,abstract,citationCount'
        }
      });
      
      setResults(response.data);
      
      // Callback für übergeordnete Komponente
      if (onResultsFound) {
        onResultsFound(response.data);
      }
    } catch (error) {
      console.error('Fehler bei der Suche:', error);
      setError(error.response?.data?.error || 'Ein Fehler ist bei der Suche aufgetreten.');
    } finally {
      setIsSearching(false);
    }
  };

  // Zurücksetzen der Suchparameter
  const handleReset = () => {
    setSearchParams({
      query: '',
      authors: '',
      year: '',
      venue: '',
      sortBy: 'relevance',
      sortOrder: 'desc'
    });
    setResults(null);
    setError(null);
  };

  return (
    <div className="advanced-search-container">
      <h2>Erweiterte Suche</h2>
      
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSearch} className="advanced-search-form">
        <div className="form-group">
          <label htmlFor="query">Suchbegriffe:</label>
          <input
            type="text"
            id="query"
            name="query"
            value={searchParams.query}
            onChange={handleInputChange}
            placeholder="z.B. quantum computing"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="authors">Autor(en):</label>
          <input
            type="text"
            id="authors"
            name="authors"
            value={searchParams.authors}
            onChange={handleInputChange}
            placeholder="z.B. John Smith"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="year">Jahr oder Jahresbereich:</label>
          <input
            type="text"
            id="year"
            name="year"
            value={searchParams.year}
            onChange={handleInputChange}
            placeholder="z.B. 2020 oder 2018-2021"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="venue">Journal/Konferenz:</label>
          <input
            type="text"
            id="venue"
            name="venue"
            value={searchParams.venue}
            onChange={handleInputChange}
            placeholder="z.B. Nature"
            className="form-control"
          />
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="sortBy">Sortieren nach:</label>
            <select
              id="sortBy"
              name="sortBy"
              value={searchParams.sortBy}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="relevance">Relevanz</option>
              <option value="year">Jahr</option>
              <option value="citationCount">Zitationen</option>
            </select>
          </div>
          
          <div className="form-group">
            <label htmlFor="sortOrder">Reihenfolge:</label>
            <select
              id="sortOrder"
              name="sortOrder"
              value={searchParams.sortOrder}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="desc">Absteigend</option>
              <option value="asc">Aufsteigend</option>
            </select>
          </div>
        </div>
        
        <div className="form-actions">
          <button 
            type="submit" 
            className="btn btn-primary" 
            disabled={isSearching}
          >
            {isSearching ? 'Suche läuft...' : 'Suchen'}
          </button>
          <button 
            type="button" 
            className="btn btn-secondary" 
            onClick={handleReset}
            disabled={isSearching}
          >
            Zurücksetzen
          </button>
        </div>
      </form>
      
      {results && (
        <div className="search-results">
          <h3>Suchergebnisse ({results.total})</h3>
          
          {results.data && results.data.length > 0 ? (
            <div className="results-list">
              {results.data.map((paper, index) => (
                <div key={paper.paperId || index} className="result-item">
                  <h4>{paper.title}</h4>
                  <div className="paper-meta">
                    <span className="paper-authors">
                      {paper.authors?.map(a => a.name).join(', ')}
                    </span>
                    <span className="paper-venue">
                      {paper.venue} ({paper.year})
                    </span>
                    {paper.citationCount !== undefined && (
                      <span className="paper-citations">
                        Zitationen: {paper.citationCount}
                      </span>
                    )}
                  </div>
                  {paper.abstract && (
                    <p className="paper-abstract">
                      {paper.abstract.length > 300 
                        ? `${paper.abstract.substring(0, 300)}...` 
                        : paper.abstract}
                    </p>
                  )}
                  <div className="paper-links">
                    {paper.url && (
                      <a 
                        href={paper.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        Zum Paper
                      </a>
                    )}
                    {paper.externalIds?.DOI && (
                      <a 
                        href={`https://doi.org/${paper.externalIds.DOI}`} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        DOI: {paper.externalIds?.DOI}
                      </a>
                    )}
                    <button
                      className="btn btn-sm btn-primary create-nft-btn"
                      onClick={() => {
                        // Funktion zur Auswahl des Papers für die NFT-Erstellung
                        if (onSelectPaper) {
                          onSelectPaper(paper);
                        } else {
                          alert(`NFT für DOI ${paper.externalIds?.DOI || 'unbekannt'} erstellen`);
                        }
                      }}
                    >
                      Als NFT erstellen
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="no-results">Keine Ergebnisse gefunden.</p>
          )}
        </div>
      )}
      
      {/* CSS für die Komponente */}
      <style jsx>{`
        .advanced-search-container {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        
        .advanced-search-form {
          background-color: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
        }
        
        .form-group {
          margin-bottom: 15px;
        }
        
        .form-row {
          display: flex;
          gap: 15px;
        }
        
        .form-control {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #ced4da;
          border-radius: 4px;
          font-size: 16px;
        }
        
        .form-actions {
          display: flex;
          gap: 10px;
          margin-top: 20px;
        }
        
        .btn {
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 16px;
        }
        
        .btn-primary {
          background-color: #007bff;
          color: white;
        }
        
        .btn-secondary {
          background-color: #6c757d;
          color: white;
        }
        
        .btn:disabled {
          opacity: 0.65;
          cursor: not-allowed;
        }
        
        .error-message {
          background-color: #f8d7da;
          color: #721c24;
          padding: 10px;
          border-radius: 4px;
          margin-bottom: 15px;
        }
        
        .search-results {
          margin-top: 30px;
        }
        
        .result-item {
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 15px;
          background-color: white;
        }
        
        .result-item h4 {
          margin-top: 0;
          color: #007bff;
        }
        
        .paper-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 15px;
          margin-bottom: 10px;
          font-size: 14px;
          color: #6c757d;
        }
        
        .paper-abstract {
          font-size: 14px;
          line-height: 1.5;
          color: #212529;
        }
        
        .paper-links {
          display: flex;
          gap: 15px;
          margin-top: 10px;
        }
        
        .paper-link {
          color: #007bff;
          text-decoration: none;
        }
        
        .paper-link:hover {
          text-decoration: underline;
        }
        
        .btn-sm {
          padding: 4px 8px;
          font-size: 14px;
        }
        
        .create-nft-btn {
          margin-left: auto;
        }
        
        .no-results {
          text-align: center;
          color: #6c757d;
          font-style: italic;
        }
      `}</style>
    </div>
  );
};

export default AdvancedSearch;import React, { useState } from 'react';
import axios from 'axios';

/**
 * Erweiterte Suchkomponente für wissenschaftliche Publikationen
 * Inspiriert von Scinapse.io für eine verbesserte Benutzererfahrung
 */
const AdvancedSearch = ({ onResultsFound, onSelectPaper }) => {
  // Suchparameter
  const [searchParams, setSearchParams] = useState({
    query: '',
    authors: '',
    year: '',
    venue: '',
    sortBy: 'relevance',
    sortOrder: 'desc'
  });

  // Suchstatus
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState(null);
  const [results, setResults] = useState(null);

  // Handler für Änderungen an den Suchparametern
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSearchParams(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Suchfunktion
  const handleSearch = async (e) => {
    e.preventDefault();
    
    // Prüfen, ob mindestens ein Suchkriterium angegeben wurde
    if (!searchParams.query && !searchParams.authors && !searchParams.year && !searchParams.venue) {
      setError('Bitte geben Sie mindestens ein Suchkriterium ein.');
      return;
    }
    
    setIsSearching(true);
    setError(null);
    
    try {
      // API-Anfrage mit den Suchparametern
      const response = await axios.get('/api/v1/semantic-scholar/advanced-search', {
        params: {
          ...searchParams,
          fields: 'title,authors,year,venue,url,externalIds,abstract,citationCount'
        }
      });
      
      setResults(response.data);
      
      // Callback für übergeordnete Komponente
      if (onResultsFound) {
        onResultsFound(response.data);
      }
    } catch (error) {
      console.error('Fehler bei der Suche:', error);
      setError(error.response?.data?.error || 'Ein Fehler ist bei der Suche aufgetreten.');
    } finally {
      setIsSearching(false);
    }
  };

  // Zurücksetzen der Suchparameter
  const handleReset = () => {
    setSearchParams({
      query: '',
      authors: '',
      year: '',
      venue: '',
      sortBy: 'relevance',
      sortOrder: 'desc'
    });
    setResults(null);
    setError(null);
  };

  return (
    <div className="advanced-search-container">
      <h2>Erweiterte Suche</h2>
      
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSearch} className="advanced-search-form">
        <div className="form-group">
          <label htmlFor="query">Suchbegriffe:</label>
          <input
            type="text"
            id="query"
            name="query"
            value={searchParams.query}
            onChange={handleInputChange}
            placeholder="z.B. quantum computing"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="authors">Autor(en):</label>
          <input
            type="text"
            id="authors"
            name="authors"
            value={searchParams.authors}
            onChange={handleInputChange}
            placeholder="z.B. John Smith"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="year">Jahr oder Jahresbereich:</label>
          <input
            type="text"
            id="year"
            name="year"
            value={searchParams.year}
            onChange={handleInputChange}
            placeholder="z.B. 2020 oder 2018-2021"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="venue">Journal/Konferenz:</label>
          <input
            type="text"
            id="venue"
            name="venue"
            value={searchParams.venue}
            onChange={handleInputChange}
            placeholder="z.B. Nature"
            className="form-control"
          />
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="sortBy">Sortieren nach:</label>
            <select
              id="sortBy"
              name="sortBy"
              value={searchParams.sortBy}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="relevance">Relevanz</option>
              <option value="year">Jahr</option>
              <option value="citationCount">Zitationen</option>
            </select>
          </div>
          
          <div className="form-group">
            <label htmlFor="sortOrder">Reihenfolge:</label>
            <select
              id="sortOrder"
              name="sortOrder"
              value={searchParams.sortOrder}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="desc">Absteigend</option>
              <option value="asc">Aufsteigend</option>
            </select>
          </div>
        </div>
        
        <div className="form-actions">
          <button 
            type="submit" 
            className="btn btn-primary" 
            disabled={isSearching}
          >
            {isSearching ? 'Suche läuft...' : 'Suchen'}
          </button>
          <button 
            type="button" 
            className="btn btn-secondary" 
            onClick={handleReset}
            disabled={isSearching}
          >
            Zurücksetzen
          </button>
        </div>
      </form>
      
      {results && (
        <div className="search-results">
          <h3>Suchergebnisse ({results.total})</h3>
          
          {results.data && results.data.length > 0 ? (
            <div className="results-list">
              {results.data.map((paper, index) => (
                <div key={paper.paperId || index} className="result-item">
                  <h4>{paper.title}</h4>
                  <div className="paper-meta">
                    <span className="paper-authors">
                      {paper.authors?.map(a => a.name).join(', ')}
                    </span>
                    <span className="paper-venue">
                      {paper.venue} ({paper.year})
                    </span>
                    {paper.citationCount !== undefined && (
                      <span className="paper-citations">
                        Zitationen: {paper.citationCount}
                      </span>
                    )}
                  </div>
                  {paper.abstract && (
                    <p className="paper-abstract">
                      {paper.abstract.length > 300 
                        ? `${paper.abstract.substring(0, 300)}...` 
                        : paper.abstract}
                    </p>
                  )}
                  <div className="paper-links">
                    {paper.url && (
                      <a 
                        href={paper.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        Zum Paper
                      </a>
                    )}
                    {paper.externalIds?.DOI && (
                      <a 
                        href={`https://doi.org/${paper.externalIds.DOI}`} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        DOI: {paper.externalIds?.DOI}
                      </a>
                    )}
                    <button
                      className="btn btn-sm btn-primary create-nft-btn"
                      onClick={() => {
                        // Funktion zur Auswahl des Papers für die NFT-Erstellung
                        if (onSelectPaper) {
                          onSelectPaper(paper);
                        } else {
                          alert(`NFT für DOI ${paper.externalIds?.DOI || 'unbekannt'} erstellen`);
                        }
                      }}
                    >
                      Als NFT erstellen
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="no-results">Keine Ergebnisse gefunden.</p>
          )}
        </div>
      )}
      
      {/* CSS für die Komponente */}
      <style jsx>{`
        .advanced-search-container {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        
        .advanced-search-form {
          background-color: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
        }
        
        .form-group {
          margin-bottom: 15px;
        }
        
        .form-row {
          display: flex;
          gap: 15px;
        }
        
        .form-control {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #ced4da;
          border-radius: 4px;
          font-size: 16px;
        }
        
        .form-actions {
          display: flex;
          gap: 10px;
          margin-top: 20px;
        }
        
        .btn {
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 16px;
        }
        
        .btn-primary {
          background-color: #007bff;
          color: white;
        }
        
        .btn-secondary {
          background-color: #6c757d;
          color: white;
        }
        
        .btn:disabled {
          opacity: 0.65;
          cursor: not-allowed;
        }
        
        .error-message {
          background-color: #f8d7da;
          color: #721c24;
          padding: 10px;
          border-radius: 4px;
          margin-bottom: 15px;
        }
        
        .search-results {
          margin-top: 30px;
        }
        
        .result-item {
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 15px;
          background-color: white;
        }
        
        .result-item h4 {
          margin-top: 0;
          color: #007bff;
        }
        
        .paper-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 15px;
          margin-bottom: 10px;
          font-size: 14px;
          color: #6c757d;
        }
        
        .paper-abstract {
          font-size: 14px;
          line-height: 1.5;
          color: #212529;
        }
        
        .paper-links {
          display: flex;
          gap: 15px;
          margin-top: 10px;
        }
        
        .paper-link {
          color: #007bff;
          text-decoration: none;
        }
        
        .paper-link:hover {
          text-decoration: underline;
        }
        
        .btn-sm {
          padding: 4px 8px;
          font-size: 14px;
        }
        
        .create-nft-btn {
          margin-left: auto;
        }
        
        .no-results {
          text-align: center;
          color: #6c757d;
          font-style: italic;
        }
      `}</style>
    </div>
  );
};

export default AdvancedSearch;import React, { useState } from 'react';
import axios from 'axios';

/**
 * Erweiterte Suchkomponente für wissenschaftliche Publikationen
 * Inspiriert von Scinapse.io für eine verbesserte Benutzererfahrung
 */
const AdvancedSearch = ({ onResultsFound, onSelectPaper }) => {
  // Suchparameter
  const [searchParams, setSearchParams] = useState({
    query: '',
    authors: '',
    year: '',
    venue: '',
    sortBy: 'relevance',
    sortOrder: 'desc'
  });

  // Suchstatus
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState(null);
  const [results, setResults] = useState(null);

  // Handler für Änderungen an den Suchparametern
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSearchParams(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Suchfunktion
  const handleSearch = async (e) => {
    e.preventDefault();
    
    // Prüfen, ob mindestens ein Suchkriterium angegeben wurde
    if (!searchParams.query && !searchParams.authors && !searchParams.year && !searchParams.venue) {
      setError('Bitte geben Sie mindestens ein Suchkriterium ein.');
      return;
    }
    
    setIsSearching(true);
    setError(null);
    
    try {
      // API-Anfrage mit den Suchparametern
      const response = await axios.get('/api/v1/semantic-scholar/advanced-search', {
        params: {
          ...searchParams,
          fields: 'title,authors,year,venue,url,externalIds,abstract,citationCount'
        }
      });
      
      setResults(response.data);
      
      // Callback für übergeordnete Komponente
      if (onResultsFound) {
        onResultsFound(response.data);
      }
    } catch (error) {
      console.error('Fehler bei der Suche:', error);
      setError(error.response?.data?.error || 'Ein Fehler ist bei der Suche aufgetreten.');
    } finally {
      setIsSearching(false);
    }
  };

  // Zurücksetzen der Suchparameter
  const handleReset = () => {
    setSearchParams({
      query: '',
      authors: '',
      year: '',
      venue: '',
      sortBy: 'relevance',
      sortOrder: 'desc'
    });
    setResults(null);
    setError(null);
  };

  return (
    <div className="advanced-search-container">
      <h2>Erweiterte Suche</h2>
      
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSearch} className="advanced-search-form">
        <div className="form-group">
          <label htmlFor="query">Suchbegriffe:</label>
          <input
            type="text"
            id="query"
            name="query"
            value={searchParams.query}
            onChange={handleInputChange}
            placeholder="z.B. quantum computing"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="authors">Autor(en):</label>
          <input
            type="text"
            id="authors"
            name="authors"
            value={searchParams.authors}
            onChange={handleInputChange}
            placeholder="z.B. John Smith"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="year">Jahr oder Jahresbereich:</label>
          <input
            type="text"
            id="year"
            name="year"
            value={searchParams.year}
            onChange={handleInputChange}
            placeholder="z.B. 2020 oder 2018-2021"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="venue">Journal/Konferenz:</label>
          <input
            type="text"
            id="venue"
            name="venue"
            value={searchParams.venue}
            onChange={handleInputChange}
            placeholder="z.B. Nature"
            className="form-control"
          />
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="sortBy">Sortieren nach:</label>
            <select
              id="sortBy"
              name="sortBy"
              value={searchParams.sortBy}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="relevance">Relevanz</option>
              <option value="year">Jahr</option>
              <option value="citationCount">Zitationen</option>
            </select>
          </div>
          
          <div className="form-group">
            <label htmlFor="sortOrder">Reihenfolge:</label>
            <select
              id="sortOrder"
              name="sortOrder"
              value={searchParams.sortOrder}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="desc">Absteigend</option>
              <option value="asc">Aufsteigend</option>
            </select>
          </div>
        </div>
        
        <div className="form-actions">
          <button 
            type="submit" 
            className="btn btn-primary" 
            disabled={isSearching}
          >
            {isSearching ? 'Suche läuft...' : 'Suchen'}
          </button>
          <button 
            type="button" 
            className="btn btn-secondary" 
            onClick={handleReset}
            disabled={isSearching}
          >
            Zurücksetzen
          </button>
        </div>
      </form>
      
      {results && (
        <div className="search-results">
          <h3>Suchergebnisse ({results.total})</h3>
          
          {results.data && results.data.length > 0 ? (
            <div className="results-list">
              {results.data.map((paper, index) => (
                <div key={paper.paperId || index} className="result-item">
                  <h4>{paper.title}</h4>
                  <div className="paper-meta">
                    <span className="paper-authors">
                      {paper.authors?.map(a => a.name).join(', ')}
                    </span>
                    <span className="paper-venue">
                      {paper.venue} ({paper.year})
                    </span>
                    {paper.citationCount !== undefined && (
                      <span className="paper-citations">
                        Zitationen: {paper.citationCount}
                      </span>
                    )}
                  </div>
                  {paper.abstract && (
                    <p className="paper-abstract">
                      {paper.abstract.length > 300 
                        ? `${paper.abstract.substring(0, 300)}...` 
                        : paper.abstract}
                    </p>
                  )}
                  <div className="paper-links">
                    {paper.url && (
                      <a 
                        href={paper.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        Zum Paper
                      </a>
                    )}
                    {paper.externalIds?.DOI && (
                      <a 
                        href={`https://doi.org/${paper.externalIds.DOI}`} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        DOI: {paper.externalIds?.DOI}
                      </a>
                    )}
                    <button
                      className="btn btn-sm btn-primary create-nft-btn"
                      onClick={() => {
                        // Funktion zur Auswahl des Papers für die NFT-Erstellung
                        if (onSelectPaper) {
                          onSelectPaper(paper);
                        } else {
                          alert(`NFT für DOI ${paper.externalIds?.DOI || 'unbekannt'} erstellen`);
                        }
                      }}
                    >
                      Als NFT erstellen
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="no-results">Keine Ergebnisse gefunden.</p>
          )}
        </div>
      )}
      
      {/* CSS für die Komponente */}
      <style jsx>{`
        .advanced-search-container {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        
        .advanced-search-form {
          background-color: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
        }
        
        .form-group {
          margin-bottom: 15px;
        }
        
        .form-row {
          display: flex;
          gap: 15px;
        }
        
        .form-control {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #ced4da;
          border-radius: 4px;
          font-size: 16px;
        }
        
        .form-actions {
          display: flex;
          gap: 10px;
          margin-top: 20px;
        }
        
        .btn {
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 16px;
        }
        
        .btn-primary {
          background-color: #007bff;
          color: white;
        }
        
        .btn-secondary {
          background-color: #6c757d;
          color: white;
        }
        
        .btn:disabled {
          opacity: 0.65;
          cursor: not-allowed;
        }
        
        .error-message {
          background-color: #f8d7da;
          color: #721c24;
          padding: 10px;
          border-radius: 4px;
          margin-bottom: 15px;
        }
        
        .search-results {
          margin-top: 30px;
        }
        
        .result-item {
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 15px;
          background-color: white;
        }
        
        .result-item h4 {
          margin-top: 0;
          color: #007bff;
        }
        
        .paper-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 15px;
          margin-bottom: 10px;
          font-size: 14px;
          color: #6c757d;
        }
        
        .paper-abstract {
          font-size: 14px;
          line-height: 1.5;
          color: #212529;
        }
        
        .paper-links {
          display: flex;
          gap: 15px;
          margin-top: 10px;
        }
        
        .paper-link {
          color: #007bff;
          text-decoration: none;
        }
        
        .paper-link:hover {
          text-decoration: underline;
        }
        
        .btn-sm {
          padding: 4px 8px;
          font-size: 14px;
        }
        
        .create-nft-btn {
          margin-left: auto;
        }
        
        .no-results {
          text-align: center;
          color: #6c757d;
          font-style: italic;
        }
      `}</style>
    </div>
  );
};

export default AdvancedSearch;import React, { useState } from 'react';
import axios from 'axios';

/**
 * Erweiterte Suchkomponente für wissenschaftliche Publikationen
 * Inspiriert von Scinapse.io für eine verbesserte Benutzererfahrung
 */
const AdvancedSearch = ({ onResultsFound, onSelectPaper }) => {
  // Suchparameter
  const [searchParams, setSearchParams] = useState({
    query: '',
    authors: '',
    year: '',
    venue: '',
    sortBy: 'relevance',
    sortOrder: 'desc'
  });

  // Suchstatus
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState(null);
  const [results, setResults] = useState(null);

  // Handler für Änderungen an den Suchparametern
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSearchParams(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Suchfunktion
  const handleSearch = async (e) => {
    e.preventDefault();
    
    // Prüfen, ob mindestens ein Suchkriterium angegeben wurde
    if (!searchParams.query && !searchParams.authors && !searchParams.year && !searchParams.venue) {
      setError('Bitte geben Sie mindestens ein Suchkriterium ein.');
      return;
    }
    
    setIsSearching(true);
    setError(null);
    
    try {
      // API-Anfrage mit den Suchparametern
      const response = await axios.get('/api/v1/semantic-scholar/advanced-search', {
        params: {
          ...searchParams,
          fields: 'title,authors,year,venue,url,externalIds,abstract,citationCount'
        }
      });
      
      setResults(response.data);
      
      // Callback für übergeordnete Komponente
      if (onResultsFound) {
        onResultsFound(response.data);
      }
    } catch (error) {
      console.error('Fehler bei der Suche:', error);
      setError(error.response?.data?.error || 'Ein Fehler ist bei der Suche aufgetreten.');
    } finally {
      setIsSearching(false);
    }
  };

  // Zurücksetzen der Suchparameter
  const handleReset = () => {
    setSearchParams({
      query: '',
      authors: '',
      year: '',
      venue: '',
      sortBy: 'relevance',
      sortOrder: 'desc'
    });
    setResults(null);
    setError(null);
  };

  return (
    <div className="advanced-search-container">
      <h2>Erweiterte Suche</h2>
      
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSearch} className="advanced-search-form">
        <div className="form-group">
          <label htmlFor="query">Suchbegriffe:</label>
          <input
            type="text"
            id="query"
            name="query"
            value={searchParams.query}
            onChange={handleInputChange}
            placeholder="z.B. quantum computing"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="authors">Autor(en):</label>
          <input
            type="text"
            id="authors"
            name="authors"
            value={searchParams.authors}
            onChange={handleInputChange}
            placeholder="z.B. John Smith"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="year">Jahr oder Jahresbereich:</label>
          <input
            type="text"
            id="year"
            name="year"
            value={searchParams.year}
            onChange={handleInputChange}
            placeholder="z.B. 2020 oder 2018-2021"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="venue">Journal/Konferenz:</label>
          <input
            type="text"
            id="venue"
            name="venue"
            value={searchParams.venue}
            onChange={handleInputChange}
            placeholder="z.B. Nature"
            className="form-control"
          />
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="sortBy">Sortieren nach:</label>
            <select
              id="sortBy"
              name="sortBy"
              value={searchParams.sortBy}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="relevance">Relevanz</option>
              <option value="year">Jahr</option>
              <option value="citationCount">Zitationen</option>
            </select>
          </div>
          
          <div className="form-group">
            <label htmlFor="sortOrder">Reihenfolge:</label>
            <select
              id="sortOrder"
              name="sortOrder"
              value={searchParams.sortOrder}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="desc">Absteigend</option>
              <option value="asc">Aufsteigend</option>
            </select>
          </div>
        </div>
        
        <div className="form-actions">
          <button 
            type="submit" 
            className="btn btn-primary" 
            disabled={isSearching}
          >
            {isSearching ? 'Suche läuft...' : 'Suchen'}
          </button>
          <button 
            type="button" 
            className="btn btn-secondary" 
            onClick={handleReset}
            disabled={isSearching}
          >
            Zurücksetzen
          </button>
        </div>
      </form>
      
      {results && (
        <div className="search-results">
          <h3>Suchergebnisse ({results.total})</h3>
          
          {results.data && results.data.length > 0 ? (
            <div className="results-list">
              {results.data.map((paper, index) => (
                <div key={paper.paperId || index} className="result-item">
                  <h4>{paper.title}</h4>
                  <div className="paper-meta">
                    <span className="paper-authors">
                      {paper.authors?.map(a => a.name).join(', ')}
                    </span>
                    <span className="paper-venue">
                      {paper.venue} ({paper.year})
                    </span>
                    {paper.citationCount !== undefined && (
                      <span className="paper-citations">
                        Zitationen: {paper.citationCount}
                      </span>
                    )}
                  </div>
                  {paper.abstract && (
                    <p className="paper-abstract">
                      {paper.abstract.length > 300 
                        ? `${paper.abstract.substring(0, 300)}...` 
                        : paper.abstract}
                    </p>
                  )}
                  <div className="paper-links">
                    {paper.url && (
                      <a 
                        href={paper.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        Zum Paper
                      </a>
                    )}
                    {paper.externalIds?.DOI && (
                      <a 
                        href={`https://doi.org/${paper.externalIds.DOI}`} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        DOI: {paper.externalIds?.DOI}
                      </a>
                    )}
                    <button
                      className="btn btn-sm btn-primary create-nft-btn"
                      onClick={() => {
                        // Funktion zur Auswahl des Papers für die NFT-Erstellung
                        if (onSelectPaper) {
                          onSelectPaper(paper);
                        } else {
                          alert(`NFT für DOI ${paper.externalIds?.DOI || 'unbekannt'} erstellen`);
                        }
                      }}
                    >
                      Als NFT erstellen
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="no-results">Keine Ergebnisse gefunden.</p>
          )}
        </div>
      )}
      
      {/* CSS für die Komponente */}
      <style jsx>{`
        .advanced-search-container {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        
        .advanced-search-form {
          background-color: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
        }
        
        .form-group {
          margin-bottom: 15px;
        }
        
        .form-row {
          display: flex;
          gap: 15px;
        }
        
        .form-control {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #ced4da;
          border-radius: 4px;
          font-size: 16px;
        }
        
        .form-actions {
          display: flex;
          gap: 10px;
          margin-top: 20px;
        }
        
        .btn {
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 16px;
        }
        
        .btn-primary {
          background-color: #007bff;
          color: white;
        }
        
        .btn-secondary {
          background-color: #6c757d;
          color: white;
        }
        
        .btn:disabled {
          opacity: 0.65;
          cursor: not-allowed;
        }
        
        .error-message {
          background-color: #f8d7da;
          color: #721c24;
          padding: 10px;
          border-radius: 4px;
          margin-bottom: 15px;
        }
        
        .search-results {
          margin-top: 30px;
        }
        
        .result-item {
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 15px;
          background-color: white;
        }
        
        .result-item h4 {
          margin-top: 0;
          color: #007bff;
        }
        
        .paper-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 15px;
          margin-bottom: 10px;
          font-size: 14px;
          color: #6c757d;
        }
        
        .paper-abstract {
          font-size: 14px;
          line-height: 1.5;
          color: #212529;
        }
        
        .paper-links {
          display: flex;
          gap: 15px;
          margin-top: 10px;
        }
        
        .paper-link {
          color: #007bff;
          text-decoration: none;
        }
        
        .paper-link:hover {
          text-decoration: underline;
        }
        
        .btn-sm {
          padding: 4px 8px;
          font-size: 14px;
        }
        
        .create-nft-btn {
          margin-left: auto;
        }
        
        .no-results {
          text-align: center;
          color: #6c757d;
          font-style: italic;
        }
      `}</style>
    </div>
  );
};

export default AdvancedSearch;import React, { useState } from 'react';
import axios from 'axios';

/**
 * Erweiterte Suchkomponente für wissenschaftliche Publikationen
 * Inspiriert von Scinapse.io für eine verbesserte Benutzererfahrung
 */
const AdvancedSearch = ({ onResultsFound, onSelectPaper }) => {
  // Suchparameter
  const [searchParams, setSearchParams] = useState({
    query: '',
    authors: '',
    year: '',
    venue: '',
    sortBy: 'relevance',
    sortOrder: 'desc'
  });

  // Suchstatus
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState(null);
  const [results, setResults] = useState(null);

  // Handler für Änderungen an den Suchparametern
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSearchParams(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Suchfunktion
  const handleSearch = async (e) => {
    e.preventDefault();
    
    // Prüfen, ob mindestens ein Suchkriterium angegeben wurde
    if (!searchParams.query && !searchParams.authors && !searchParams.year && !searchParams.venue) {
      setError('Bitte geben Sie mindestens ein Suchkriterium ein.');
      return;
    }
    
    setIsSearching(true);
    setError(null);
    
    try {
      // API-Anfrage mit den Suchparametern
      const response = await axios.get('/api/v1/semantic-scholar/advanced-search', {
        params: {
          ...searchParams,
          fields: 'title,authors,year,venue,url,externalIds,abstract,citationCount'
        }
      });
      
      setResults(response.data);
      
      // Callback für übergeordnete Komponente
      if (onResultsFound) {
        onResultsFound(response.data);
      }
    } catch (error) {
      console.error('Fehler bei der Suche:', error);
      setError(error.response?.data?.error || 'Ein Fehler ist bei der Suche aufgetreten.');
    } finally {
      setIsSearching(false);
    }
  };

  // Zurücksetzen der Suchparameter
  const handleReset = () => {
    setSearchParams({
      query: '',
      authors: '',
      year: '',
      venue: '',
      sortBy: 'relevance',
      sortOrder: 'desc'
    });
    setResults(null);
    setError(null);
  };

  return (
    <div className="advanced-search-container">
      <h2>Erweiterte Suche</h2>
      
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSearch} className="advanced-search-form">
        <div className="form-group">
          <label htmlFor="query">Suchbegriffe:</label>
          <input
            type="text"
            id="query"
            name="query"
            value={searchParams.query}
            onChange={handleInputChange}
            placeholder="z.B. quantum computing"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="authors">Autor(en):</label>
          <input
            type="text"
            id="authors"
            name="authors"
            value={searchParams.authors}
            onChange={handleInputChange}
            placeholder="z.B. John Smith"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="year">Jahr oder Jahresbereich:</label>
          <input
            type="text"
            id="year"
            name="year"
            value={searchParams.year}
            onChange={handleInputChange}
            placeholder="z.B. 2020 oder 2018-2021"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="venue">Journal/Konferenz:</label>
          <input
            type="text"
            id="venue"
            name="venue"
            value={searchParams.venue}
            onChange={handleInputChange}
            placeholder="z.B. Nature"
            className="form-control"
          />
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="sortBy">Sortieren nach:</label>
            <select
              id="sortBy"
              name="sortBy"
              value={searchParams.sortBy}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="relevance">Relevanz</option>
              <option value="year">Jahr</option>
              <option value="citationCount">Zitationen</option>
            </select>
          </div>
          
          <div className="form-group">
            <label htmlFor="sortOrder">Reihenfolge:</label>
            <select
              id="sortOrder"
              name="sortOrder"
              value={searchParams.sortOrder}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="desc">Absteigend</option>
              <option value="asc">Aufsteigend</option>
            </select>
          </div>
        </div>
        
        <div className="form-actions">
          <button 
            type="submit" 
            className="btn btn-primary" 
            disabled={isSearching}
          >
            {isSearching ? 'Suche läuft...' : 'Suchen'}
          </button>
          <button 
            type="button" 
            className="btn btn-secondary" 
            onClick={handleReset}
            disabled={isSearching}
          >
            Zurücksetzen
          </button>
        </div>
      </form>
      
      {results && (
        <div className="search-results">
          <h3>Suchergebnisse ({results.total})</h3>
          
          {results.data && results.data.length > 0 ? (
            <div className="results-list">
              {results.data.map((paper, index) => (
                <div key={paper.paperId || index} className="result-item">
                  <h4>{paper.title}</h4>
                  <div className="paper-meta">
                    <span className="paper-authors">
                      {paper.authors?.map(a => a.name).join(', ')}
                    </span>
                    <span className="paper-venue">
                      {paper.venue} ({paper.year})
                    </span>
                    {paper.citationCount !== undefined && (
                      <span className="paper-citations">
                        Zitationen: {paper.citationCount}
                      </span>
                    )}
                  </div>
                  {paper.abstract && (
                    <p className="paper-abstract">
                      {paper.abstract.length > 300 
                        ? `${paper.abstract.substring(0, 300)}...` 
                        : paper.abstract}
                    </p>
                  )}
                  <div className="paper-links">
                    {paper.url && (
                      <a 
                        href={paper.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        Zum Paper
                      </a>
                    )}
                    {paper.externalIds?.DOI && (
                      <a 
                        href={`https://doi.org/${paper.externalIds.DOI}`} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        DOI: {paper.externalIds?.DOI}
                      </a>
                    )}
                    <button
                      className="btn btn-sm btn-primary create-nft-btn"
                      onClick={() => {
                        // Funktion zur Auswahl des Papers für die NFT-Erstellung
                        if (onSelectPaper) {
                          onSelectPaper(paper);
                        } else {
                          alert(`NFT für DOI ${paper.externalIds?.DOI || 'unbekannt'} erstellen`);
                        }
                      }}
                    >
                      Als NFT erstellen
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="no-results">Keine Ergebnisse gefunden.</p>
          )}
        </div>
      )}
      
      {/* CSS für die Komponente */}
      <style jsx>{`
        .advanced-search-container {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        
        .advanced-search-form {
          background-color: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
        }
        
        .form-group {
          margin-bottom: 15px;
        }
        
        .form-row {
          display: flex;
          gap: 15px;
        }
        
        .form-control {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #ced4da;
          border-radius: 4px;
          font-size: 16px;
        }
        
        .form-actions {
          display: flex;
          gap: 10px;
          margin-top: 20px;
        }
        
        .btn {
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 16px;
        }
        
        .btn-primary {
          background-color: #007bff;
          color: white;
        }
        
        .btn-secondary {
          background-color: #6c757d;
          color: white;
        }
        
        .btn:disabled {
          opacity: 0.65;
          cursor: not-allowed;
        }
        
        .error-message {
          background-color: #f8d7da;
          color: #721c24;
          padding: 10px;
          border-radius: 4px;
          margin-bottom: 15px;
        }
        
        .search-results {
          margin-top: 30px;
        }
        
        .result-item {
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 15px;
          background-color: white;
        }
        
        .result-item h4 {
          margin-top: 0;
          color: #007bff;
        }
        
        .paper-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 15px;
          margin-bottom: 10px;
          font-size: 14px;
          color: #6c757d;
        }
        
        .paper-abstract {
          font-size: 14px;
          line-height: 1.5;
          color: #212529;
        }
        
        .paper-links {
          display: flex;
          gap: 15px;
          margin-top: 10px;
        }
        
        .paper-link {
          color: #007bff;
          text-decoration: none;
        }
        
        .paper-link:hover {
          text-decoration: underline;
        }
        
        .btn-sm {
          padding: 4px 8px;
          font-size: 14px;
        }
        
        .create-nft-btn {
          margin-left: auto;
        }
        
        .no-results {
          text-align: center;
          color: #6c757d;
          font-style: italic;
        }
      `}</style>
    </div>
  );
};

export default AdvancedSearch;import React, { useState } from 'react';
import axios from 'axios';

/**
 * Erweiterte Suchkomponente für wissenschaftliche Publikationen
 * Inspiriert von Scinapse.io für eine verbesserte Benutzererfahrung
 */
const AdvancedSearch = ({ onResultsFound, onSelectPaper }) => {
  // Suchparameter
  const [searchParams, setSearchParams] = useState({
    query: '',
    authors: '',
    year: '',
    venue: '',
    sortBy: 'relevance',
    sortOrder: 'desc'
  });

  // Suchstatus
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState(null);
  const [results, setResults] = useState(null);

  // Handler für Änderungen an den Suchparametern
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSearchParams(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Suchfunktion
  const handleSearch = async (e) => {
    e.preventDefault();
    
    // Prüfen, ob mindestens ein Suchkriterium angegeben wurde
    if (!searchParams.query && !searchParams.authors && !searchParams.year && !searchParams.venue) {
      setError('Bitte geben Sie mindestens ein Suchkriterium ein.');
      return;
    }
    
    setIsSearching(true);
    setError(null);
    
    try {
      // API-Anfrage mit den Suchparametern
      const response = await axios.get('/api/v1/semantic-scholar/advanced-search', {
        params: {
          ...searchParams,
          fields: 'title,authors,year,venue,url,externalIds,abstract,citationCount'
        }
      });
      
      setResults(response.data);
      
      // Callback für übergeordnete Komponente
      if (onResultsFound) {
        onResultsFound(response.data);
      }
    } catch (error) {
      console.error('Fehler bei der Suche:', error);
      setError(error.response?.data?.error || 'Ein Fehler ist bei der Suche aufgetreten.');
    } finally {
      setIsSearching(false);
    }
  };

  // Zurücksetzen der Suchparameter
  const handleReset = () => {
    setSearchParams({
      query: '',
      authors: '',
      year: '',
      venue: '',
      sortBy: 'relevance',
      sortOrder: 'desc'
    });
    setResults(null);
    setError(null);
  };

  return (
    <div className="advanced-search-container">
      <h2>Erweiterte Suche</h2>
      
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSearch} className="advanced-search-form">
        <div className="form-group">
          <label htmlFor="query">Suchbegriffe:</label>
          <input
            type="text"
            id="query"
            name="query"
            value={searchParams.query}
            onChange={handleInputChange}
            placeholder="z.B. quantum computing"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="authors">Autor(en):</label>
          <input
            type="text"
            id="authors"
            name="authors"
            value={searchParams.authors}
            onChange={handleInputChange}
            placeholder="z.B. John Smith"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="year">Jahr oder Jahresbereich:</label>
          <input
            type="text"
            id="year"
            name="year"
            value={searchParams.year}
            onChange={handleInputChange}
            placeholder="z.B. 2020 oder 2018-2021"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="venue">Journal/Konferenz:</label>
          <input
            type="text"
            id="venue"
            name="venue"
            value={searchParams.venue}
            onChange={handleInputChange}
            placeholder="z.B. Nature"
            className="form-control"
          />
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="sortBy">Sortieren nach:</label>
            <select
              id="sortBy"
              name="sortBy"
              value={searchParams.sortBy}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="relevance">Relevanz</option>
              <option value="year">Jahr</option>
              <option value="citationCount">Zitationen</option>
            </select>
          </div>
          
          <div className="form-group">
            <label htmlFor="sortOrder">Reihenfolge:</label>
            <select
              id="sortOrder"
              name="sortOrder"
              value={searchParams.sortOrder}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="desc">Absteigend</option>
              <option value="asc">Aufsteigend</option>
            </select>
          </div>
        </div>
        
        <div className="form-actions">
          <button 
            type="submit" 
            className="btn btn-primary" 
            disabled={isSearching}
          >
            {isSearching ? 'Suche läuft...' : 'Suchen'}
          </button>
          <button 
            type="button" 
            className="btn btn-secondary" 
            onClick={handleReset}
            disabled={isSearching}
          >
            Zurücksetzen
          </button>
        </div>
      </form>
      
      {results && (
        <div className="search-results">
          <h3>Suchergebnisse ({results.total})</h3>
          
          {results.data && results.data.length > 0 ? (
            <div className="results-list">
              {results.data.map((paper, index) => (
                <div key={paper.paperId || index} className="result-item">
                  <h4>{paper.title}</h4>
                  <div className="paper-meta">
                    <span className="paper-authors">
                      {paper.authors?.map(a => a.name).join(', ')}
                    </span>
                    <span className="paper-venue">
                      {paper.venue} ({paper.year})
                    </span>
                    {paper.citationCount !== undefined && (
                      <span className="paper-citations">
                        Zitationen: {paper.citationCount}
                      </span>
                    )}
                  </div>
                  {paper.abstract && (
                    <p className="paper-abstract">
                      {paper.abstract.length > 300 
                        ? `${paper.abstract.substring(0, 300)}...` 
                        : paper.abstract}
                    </p>
                  )}
                  <div className="paper-links">
                    {paper.url && (
                      <a 
                        href={paper.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        Zum Paper
                      </a>
                    )}
                    {paper.externalIds?.DOI && (
                      <a 
                        href={`https://doi.org/${paper.externalIds.DOI}`} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        DOI: {paper.externalIds?.DOI}
                      </a>
                    )}
                    <button
                      className="btn btn-sm btn-primary create-nft-btn"
                      onClick={() => {
                        // Funktion zur Auswahl des Papers für die NFT-Erstellung
                        if (onSelectPaper) {
                          onSelectPaper(paper);
                        } else {
                          alert(`NFT für DOI ${paper.externalIds?.DOI || 'unbekannt'} erstellen`);
                        }
                      }}
                    >
                      Als NFT erstellen
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="no-results">Keine Ergebnisse gefunden.</p>
          )}
        </div>
      )}
      
      {/* CSS für die Komponente */}
      <style jsx>{`
        .advanced-search-container {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        
        .advanced-search-form {
          background-color: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
        }
        
        .form-group {
          margin-bottom: 15px;
        }
        
        .form-row {
          display: flex;
          gap: 15px;
        }
        
        .form-control {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #ced4da;
          border-radius: 4px;
          font-size: 16px;
        }
        
        .form-actions {
          display: flex;
          gap: 10px;
          margin-top: 20px;
        }
        
        .btn {
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 16px;
        }
        
        .btn-primary {
          background-color: #007bff;
          color: white;
        }
        
        .btn-secondary {
          background-color: #6c757d;
          color: white;
        }
        
        .btn:disabled {
          opacity: 0.65;
          cursor: not-allowed;
        }
        
        .error-message {
          background-color: #f8d7da;
          color: #721c24;
          padding: 10px;
          border-radius: 4px;
          margin-bottom: 15px;
        }
        
        .search-results {
          margin-top: 30px;
        }
        
        .result-item {
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 15px;
          background-color: white;
        }
        
        .result-item h4 {
          margin-top: 0;
          color: #007bff;
        }
        
        .paper-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 15px;
          margin-bottom: 10px;
          font-size: 14px;
          color: #6c757d;
        }
        
        .paper-abstract {
          font-size: 14px;
          line-height: 1.5;
          color: #212529;
        }
        
        .paper-links {
          display: flex;
          gap: 15px;
          margin-top: 10px;
        }
        
        .paper-link {
          color: #007bff;
          text-decoration: none;
        }
        
        .paper-link:hover {
          text-decoration: underline;
        }
        
        .btn-sm {
          padding: 4px 8px;
          font-size: 14px;
        }
        
        .create-nft-btn {
          margin-left: auto;
        }
        
        .no-results {
          text-align: center;
          color: #6c757d;
          font-style: italic;
        }
      `}</style>
    </div>
  );
};

export default AdvancedSearch;import React, { useState } from 'react';
import axios from 'axios';

/**
 * Erweiterte Suchkomponente für wissenschaftliche Publikationen
 * Inspiriert von Scinapse.io für eine verbesserte Benutzererfahrung
 */
const AdvancedSearch = ({ onResultsFound, onSelectPaper }) => {
  // Suchparameter
  const [searchParams, setSearchParams] = useState({
    query: '',
    authors: '',
    year: '',
    venue: '',
    sortBy: 'relevance',
    sortOrder: 'desc'
  });

  // Suchstatus
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState(null);
  const [results, setResults] = useState(null);

  // Handler für Änderungen an den Suchparametern
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSearchParams(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Suchfunktion
  const handleSearch = async (e) => {
    e.preventDefault();
    
    // Prüfen, ob mindestens ein Suchkriterium angegeben wurde
    if (!searchParams.query && !searchParams.authors && !searchParams.year && !searchParams.venue) {
      setError('Bitte geben Sie mindestens ein Suchkriterium ein.');
      return;
    }
    
    setIsSearching(true);
    setError(null);
    
    try {
      // API-Anfrage mit den Suchparametern
      const response = await axios.get('/api/v1/semantic-scholar/advanced-search', {
        params: {
          ...searchParams,
          fields: 'title,authors,year,venue,url,externalIds,abstract,citationCount'
        }
      });
      
      setResults(response.data);
      
      // Callback für übergeordnete Komponente
      if (onResultsFound) {
        onResultsFound(response.data);
      }
    } catch (error) {
      console.error('Fehler bei der Suche:', error);
      setError(error.response?.data?.error || 'Ein Fehler ist bei der Suche aufgetreten.');
    } finally {
      setIsSearching(false);
    }
  };

  // Zurücksetzen der Suchparameter
  const handleReset = () => {
    setSearchParams({
      query: '',
      authors: '',
      year: '',
      venue: '',
      sortBy: 'relevance',
      sortOrder: 'desc'
    });
    setResults(null);
    setError(null);
  };

  return (
    <div className="advanced-search-container">
      <h2>Erweiterte Suche</h2>
      
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSearch} className="advanced-search-form">
        <div className="form-group">
          <label htmlFor="query">Suchbegriffe:</label>
          <input
            type="text"
            id="query"
            name="query"
            value={searchParams.query}
            onChange={handleInputChange}
            placeholder="z.B. quantum computing"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="authors">Autor(en):</label>
          <input
            type="text"
            id="authors"
            name="authors"
            value={searchParams.authors}
            onChange={handleInputChange}
            placeholder="z.B. John Smith"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="year">Jahr oder Jahresbereich:</label>
          <input
            type="text"
            id="year"
            name="year"
            value={searchParams.year}
            onChange={handleInputChange}
            placeholder="z.B. 2020 oder 2018-2021"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="venue">Journal/Konferenz:</label>
          <input
            type="text"
            id="venue"
            name="venue"
            value={searchParams.venue}
            onChange={handleInputChange}
            placeholder="z.B. Nature"
            className="form-control"
          />
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="sortBy">Sortieren nach:</label>
            <select
              id="sortBy"
              name="sortBy"
              value={searchParams.sortBy}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="relevance">Relevanz</option>
              <option value="year">Jahr</option>
              <option value="citationCount">Zitationen</option>
            </select>
          </div>
          
          <div className="form-group">
            <label htmlFor="sortOrder">Reihenfolge:</label>
            <select
              id="sortOrder"
              name="sortOrder"
              value={searchParams.sortOrder}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="desc">Absteigend</option>
              <option value="asc">Aufsteigend</option>
            </select>
          </div>
        </div>
        
        <div className="form-actions">
          <button 
            type="submit" 
            className="btn btn-primary" 
            disabled={isSearching}
          >
            {isSearching ? 'Suche läuft...' : 'Suchen'}
          </button>
          <button 
            type="button" 
            className="btn btn-secondary" 
            onClick={handleReset}
            disabled={isSearching}
          >
            Zurücksetzen
          </button>
        </div>
      </form>
      
      {results && (
        <div className="search-results">
          <h3>Suchergebnisse ({results.total})</h3>
          
          {results.data && results.data.length > 0 ? (
            <div className="results-list">
              {results.data.map((paper, index) => (
                <div key={paper.paperId || index} className="result-item">
                  <h4>{paper.title}</h4>
                  <div className="paper-meta">
                    <span className="paper-authors">
                      {paper.authors?.map(a => a.name).join(', ')}
                    </span>
                    <span className="paper-venue">
                      {paper.venue} ({paper.year})
                    </span>
                    {paper.citationCount !== undefined && (
                      <span className="paper-citations">
                        Zitationen: {paper.citationCount}
                      </span>
                    )}
                  </div>
                  {paper.abstract && (
                    <p className="paper-abstract">
                      {paper.abstract.length > 300 
                        ? `${paper.abstract.substring(0, 300)}...` 
                        : paper.abstract}
                    </p>
                  )}
                  <div className="paper-links">
                    {paper.url && (
                      <a 
                        href={paper.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        Zum Paper
                      </a>
                    )}
                    {paper.externalIds?.DOI && (
                      <a 
                        href={`https://doi.org/${paper.externalIds.DOI}`} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        DOI: {paper.externalIds?.DOI}
                      </a>
                    )}
                    <button
                      className="btn btn-sm btn-primary create-nft-btn"
                      onClick={() => {
                        // Funktion zur Auswahl des Papers für die NFT-Erstellung
                        if (onSelectPaper) {
                          onSelectPaper(paper);
                        } else {
                          alert(`NFT für DOI ${paper.externalIds?.DOI || 'unbekannt'} erstellen`);
                        }
                      }}
                    >
                      Als NFT erstellen
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="no-results">Keine Ergebnisse gefunden.</p>
          )}
        </div>
      )}
      
      {/* CSS für die Komponente */}
      <style jsx>{`
        .advanced-search-container {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        
        .advanced-search-form {
          background-color: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
        }
        
        .form-group {
          margin-bottom: 15px;
        }
        
        .form-row {
          display: flex;
          gap: 15px;
        }
        
        .form-control {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #ced4da;
          border-radius: 4px;
          font-size: 16px;
        }
        
        .form-actions {
          display: flex;
          gap: 10px;
          margin-top: 20px;
        }
        
        .btn {
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 16px;
        }
        
        .btn-primary {
          background-color: #007bff;
          color: white;
        }
        
        .btn-secondary {
          background-color: #6c757d;
          color: white;
        }
        
        .btn:disabled {
          opacity: 0.65;
          cursor: not-allowed;
        }
        
        .error-message {
          background-color: #f8d7da;
          color: #721c24;
          padding: 10px;
          border-radius: 4px;
          margin-bottom: 15px;
        }
        
        .search-results {
          margin-top: 30px;
        }
        
        .result-item {
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 15px;
          background-color: white;
        }
        
        .result-item h4 {
          margin-top: 0;
          color: #007bff;
        }
        
        .paper-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 15px;
          margin-bottom: 10px;
          font-size: 14px;
          color: #6c757d;
        }
        
        .paper-abstract {
          font-size: 14px;
          line-height: 1.5;
          color: #212529;
        }
        
        .paper-links {
          display: flex;
          gap: 15px;
          margin-top: 10px;
        }
        
        .paper-link {
          color: #007bff;
          text-decoration: none;
        }
        
        .paper-link:hover {
          text-decoration: underline;
        }
        
        .btn-sm {
          padding: 4px 8px;
          font-size: 14px;
        }
        
        .create-nft-btn {
          margin-left: auto;
        }
        
        .no-results {
          text-align: center;
          color: #6c757d;
          font-style: italic;
        }
      `}</style>
    </div>
  );
};

export default AdvancedSearch;import React, { useState } from 'react';
import axios from 'axios';

/**
 * Erweiterte Suchkomponente für wissenschaftliche Publikationen
 * Inspiriert von Scinapse.io für eine verbesserte Benutzererfahrung
 */
const AdvancedSearch = ({ onResultsFound, onSelectPaper }) => {
  // Suchparameter
  const [searchParams, setSearchParams] = useState({
    query: '',
    authors: '',
    year: '',
    venue: '',
    sortBy: 'relevance',
    sortOrder: 'desc'
  });

  // Suchstatus
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState(null);
  const [results, setResults] = useState(null);

  // Handler für Änderungen an den Suchparametern
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSearchParams(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Suchfunktion
  const handleSearch = async (e) => {
    e.preventDefault();
    
    // Prüfen, ob mindestens ein Suchkriterium angegeben wurde
    if (!searchParams.query && !searchParams.authors && !searchParams.year && !searchParams.venue) {
      setError('Bitte geben Sie mindestens ein Suchkriterium ein.');
      return;
    }
    
    setIsSearching(true);
    setError(null);
    
    try {
      // API-Anfrage mit den Suchparametern
      const response = await axios.get('/api/v1/semantic-scholar/advanced-search', {
        params: {
          ...searchParams,
          fields: 'title,authors,year,venue,url,externalIds,abstract,citationCount'
        }
      });
      
      setResults(response.data);
      
      // Callback für übergeordnete Komponente
      if (onResultsFound) {
        onResultsFound(response.data);
      }
    } catch (error) {
      console.error('Fehler bei der Suche:', error);
      setError(error.response?.data?.error || 'Ein Fehler ist bei der Suche aufgetreten.');
    } finally {
      setIsSearching(false);
    }
  };

  // Zurücksetzen der Suchparameter
  const handleReset = () => {
    setSearchParams({
      query: '',
      authors: '',
      year: '',
      venue: '',
      sortBy: 'relevance',
      sortOrder: 'desc'
    });
    setResults(null);
    setError(null);
  };

  return (
    <div className="advanced-search-container">
      <h2>Erweiterte Suche</h2>
      
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSearch} className="advanced-search-form">
        <div className="form-group">
          <label htmlFor="query">Suchbegriffe:</label>
          <input
            type="text"
            id="query"
            name="query"
            value={searchParams.query}
            onChange={handleInputChange}
            placeholder="z.B. quantum computing"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="authors">Autor(en):</label>
          <input
            type="text"
            id="authors"
            name="authors"
            value={searchParams.authors}
            onChange={handleInputChange}
            placeholder="z.B. John Smith"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="year">Jahr oder Jahresbereich:</label>
          <input
            type="text"
            id="year"
            name="year"
            value={searchParams.year}
            onChange={handleInputChange}
            placeholder="z.B. 2020 oder 2018-2021"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="venue">Journal/Konferenz:</label>
          <input
            type="text"
            id="venue"
            name="venue"
            value={searchParams.venue}
            onChange={handleInputChange}
            placeholder="z.B. Nature"
            className="form-control"
          />
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="sortBy">Sortieren nach:</label>
            <select
              id="sortBy"
              name="sortBy"
              value={searchParams.sortBy}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="relevance">Relevanz</option>
              <option value="year">Jahr</option>
              <option value="citationCount">Zitationen</option>
            </select>
          </div>
          
          <div className="form-group">
            <label htmlFor="sortOrder">Reihenfolge:</label>
            <select
              id="sortOrder"
              name="sortOrder"
              value={searchParams.sortOrder}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="desc">Absteigend</option>
              <option value="asc">Aufsteigend</option>
            </select>
          </div>
        </div>
        
        <div className="form-actions">
          <button 
            type="submit" 
            className="btn btn-primary" 
            disabled={isSearching}
          >
            {isSearching ? 'Suche läuft...' : 'Suchen'}
          </button>
          <button 
            type="button" 
            className="btn btn-secondary" 
            onClick={handleReset}
            disabled={isSearching}
          >
            Zurücksetzen
          </button>
        </div>
      </form>
      
      {results && (
        <div className="search-results">
          <h3>Suchergebnisse ({results.total})</h3>
          
          {results.data && results.data.length > 0 ? (
            <div className="results-list">
              {results.data.map((paper, index) => (
                <div key={paper.paperId || index} className="result-item">
                  <h4>{paper.title}</h4>
                  <div className="paper-meta">
                    <span className="paper-authors">
                      {paper.authors?.map(a => a.name).join(', ')}
                    </span>
                    <span className="paper-venue">
                      {paper.venue} ({paper.year})
                    </span>
                    {paper.citationCount !== undefined && (
                      <span className="paper-citations">
                        Zitationen: {paper.citationCount}
                      </span>
                    )}
                  </div>
                  {paper.abstract && (
                    <p className="paper-abstract">
                      {paper.abstract.length > 300 
                        ? `${paper.abstract.substring(0, 300)}...` 
                        : paper.abstract}
                    </p>
                  )}
                  <div className="paper-links">
                    {paper.url && (
                      <a 
                        href={paper.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        Zum Paper
                      </a>
                    )}
                    {paper.externalIds?.DOI && (
                      <a 
                        href={`https://doi.org/${paper.externalIds.DOI}`} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        DOI: {paper.externalIds?.DOI}
                      </a>
                    )}
                    <button
                      className="btn btn-sm btn-primary create-nft-btn"
                      onClick={() => {
                        // Funktion zur Auswahl des Papers für die NFT-Erstellung
                        if (onSelectPaper) {
                          onSelectPaper(paper);
                        } else {
                          alert(`NFT für DOI ${paper.externalIds?.DOI || 'unbekannt'} erstellen`);
                        }
                      }}
                    >
                      Als NFT erstellen
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="no-results">Keine Ergebnisse gefunden.</p>
          )}
        </div>
      )}
      
      {/* CSS für die Komponente */}
      <style jsx>{`
        .advanced-search-container {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        
        .advanced-search-form {
          background-color: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
        }
        
        .form-group {
          margin-bottom: 15px;
        }
        
        .form-row {
          display: flex;
          gap: 15px;
        }
        
        .form-control {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #ced4da;
          border-radius: 4px;
          font-size: 16px;
        }
        
        .form-actions {
          display: flex;
          gap: 10px;
          margin-top: 20px;
        }
        
        .btn {
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 16px;
        }
        
        .btn-primary {
          background-color: #007bff;
          color: white;
        }
        
        .btn-secondary {
          background-color: #6c757d;
          color: white;
        }
        
        .btn:disabled {
          opacity: 0.65;
          cursor: not-allowed;
        }
        
        .error-message {
          background-color: #f8d7da;
          color: #721c24;
          padding: 10px;
          border-radius: 4px;
          margin-bottom: 15px;
        }
        
        .search-results {
          margin-top: 30px;
        }
        
        .result-item {
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 15px;
          background-color: white;
        }
        
        .result-item h4 {
          margin-top: 0;
          color: #007bff;
        }
        
        .paper-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 15px;
          margin-bottom: 10px;
          font-size: 14px;
          color: #6c757d;
        }
        
        .paper-abstract {
          font-size: 14px;
          line-height: 1.5;
          color: #212529;
        }
        
        .paper-links {
          display: flex;
          gap: 15px;
          margin-top: 10px;
        }
        
        .paper-link {
          color: #007bff;
          text-decoration: none;
        }
        
        .paper-link:hover {
          text-decoration: underline;
        }
        
        .btn-sm {
          padding: 4px 8px;
          font-size: 14px;
        }
        
        .create-nft-btn {
          margin-left: auto;
        }
        
        .no-results {
          text-align: center;
          color: #6c757d;
          font-style: italic;
        }
      `}</style>
    </div>
  );
};

export default AdvancedSearch;import React, { useState } from 'react';
import axios from 'axios';

/**
 * Erweiterte Suchkomponente für wissenschaftliche Publikationen
 * Inspiriert von Scinapse.io für eine verbesserte Benutzererfahrung
 */
const AdvancedSearch = ({ onResultsFound, onSelectPaper }) => {
  // Suchparameter
  const [searchParams, setSearchParams] = useState({
    query: '',
    authors: '',
    year: '',
    venue: '',
    sortBy: 'relevance',
    sortOrder: 'desc'
  });

  // Suchstatus
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState(null);
  const [results, setResults] = useState(null);

  // Handler für Änderungen an den Suchparametern
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSearchParams(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Suchfunktion
  const handleSearch = async (e) => {
    e.preventDefault();
    
    // Prüfen, ob mindestens ein Suchkriterium angegeben wurde
    if (!searchParams.query && !searchParams.authors && !searchParams.year && !searchParams.venue) {
      setError('Bitte geben Sie mindestens ein Suchkriterium ein.');
      return;
    }
    
    setIsSearching(true);
    setError(null);
    
    try {
      // API-Anfrage mit den Suchparametern
      const response = await axios.get('/api/v1/semantic-scholar/advanced-search', {
        params: {
          ...searchParams,
          fields: 'title,authors,year,venue,url,externalIds,abstract,citationCount'
        }
      });
      
      setResults(response.data);
      
      // Callback für übergeordnete Komponente
      if (onResultsFound) {
        onResultsFound(response.data);
      }
    } catch (error) {
      console.error('Fehler bei der Suche:', error);
      setError(error.response?.data?.error || 'Ein Fehler ist bei der Suche aufgetreten.');
    } finally {
      setIsSearching(false);
    }
  };

  // Zurücksetzen der Suchparameter
  const handleReset = () => {
    setSearchParams({
      query: '',
      authors: '',
      year: '',
      venue: '',
      sortBy: 'relevance',
      sortOrder: 'desc'
    });
    setResults(null);
    setError(null);
  };

  return (
    <div className="advanced-search-container">
      <h2>Erweiterte Suche</h2>
      
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSearch} className="advanced-search-form">
        <div className="form-group">
          <label htmlFor="query">Suchbegriffe:</label>
          <input
            type="text"
            id="query"
            name="query"
            value={searchParams.query}
            onChange={handleInputChange}
            placeholder="z.B. quantum computing"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="authors">Autor(en):</label>
          <input
            type="text"
            id="authors"
            name="authors"
            value={searchParams.authors}
            onChange={handleInputChange}
            placeholder="z.B. John Smith"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="year">Jahr oder Jahresbereich:</label>
          <input
            type="text"
            id="year"
            name="year"
            value={searchParams.year}
            onChange={handleInputChange}
            placeholder="z.B. 2020 oder 2018-2021"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="venue">Journal/Konferenz:</label>
          <input
            type="text"
            id="venue"
            name="venue"
            value={searchParams.venue}
            onChange={handleInputChange}
            placeholder="z.B. Nature"
            className="form-control"
          />
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="sortBy">Sortieren nach:</label>
            <select
              id="sortBy"
              name="sortBy"
              value={searchParams.sortBy}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="relevance">Relevanz</option>
              <option value="year">Jahr</option>
              <option value="citationCount">Zitationen</option>
            </select>
          </div>
          
          <div className="form-group">
            <label htmlFor="sortOrder">Reihenfolge:</label>
            <select
              id="sortOrder"
              name="sortOrder"
              value={searchParams.sortOrder}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="desc">Absteigend</option>
              <option value="asc">Aufsteigend</option>
            </select>
          </div>
        </div>
        
        <div className="form-actions">
          <button 
            type="submit" 
            className="btn btn-primary" 
            disabled={isSearching}
          >
            {isSearching ? 'Suche läuft...' : 'Suchen'}
          </button>
          <button 
            type="button" 
            className="btn btn-secondary" 
            onClick={handleReset}
            disabled={isSearching}
          >
            Zurücksetzen
          </button>
        </div>
      </form>
      
      {results && (
        <div className="search-results">
          <h3>Suchergebnisse ({results.total})</h3>
          
          {results.data && results.data.length > 0 ? (
            <div className="results-list">
              {results.data.map((paper, index) => (
                <div key={paper.paperId || index} className="result-item">
                  <h4>{paper.title}</h4>
                  <div className="paper-meta">
                    <span className="paper-authors">
                      {paper.authors?.map(a => a.name).join(', ')}
                    </span>
                    <span className="paper-venue">
                      {paper.venue} ({paper.year})
                    </span>
                    {paper.citationCount !== undefined && (
                      <span className="paper-citations">
                        Zitationen: {paper.citationCount}
                      </span>
                    )}
                  </div>
                  {paper.abstract && (
                    <p className="paper-abstract">
                      {paper.abstract.length > 300 
                        ? `${paper.abstract.substring(0, 300)}...` 
                        : paper.abstract}
                    </p>
                  )}
                  <div className="paper-links">
                    {paper.url && (
                      <a 
                        href={paper.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        Zum Paper
                      </a>
                    )}
                    {paper.externalIds?.DOI && (
                      <a 
                        href={`https://doi.org/${paper.externalIds.DOI}`} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        DOI: {paper.externalIds?.DOI}
                      </a>
                    )}
                    <button
                      className="btn btn-sm btn-primary create-nft-btn"
                      onClick={() => {
                        // Funktion zur Auswahl des Papers für die NFT-Erstellung
                        if (onSelectPaper) {
                          onSelectPaper(paper);
                        } else {
                          alert(`NFT für DOI ${paper.externalIds?.DOI || 'unbekannt'} erstellen`);
                        }
                      }}
                    >
                      Als NFT erstellen
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="no-results">Keine Ergebnisse gefunden.</p>
          )}
        </div>
      )}
      
      {/* CSS für die Komponente */}
      <style jsx>{`
        .advanced-search-container {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        
        .advanced-search-form {
          background-color: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
        }
        
        .form-group {
          margin-bottom: 15px;
        }
        
        .form-row {
          display: flex;
          gap: 15px;
        }
        
        .form-control {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #ced4da;
          border-radius: 4px;
          font-size: 16px;
        }
        
        .form-actions {
          display: flex;
          gap: 10px;
          margin-top: 20px;
        }
        
        .btn {
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 16px;
        }
        
        .btn-primary {
          background-color: #007bff;
          color: white;
        }
        
        .btn-secondary {
          background-color: #6c757d;
          color: white;
        }
        
        .btn:disabled {
          opacity: 0.65;
          cursor: not-allowed;
        }
        
        .error-message {
          background-color: #f8d7da;
          color: #721c24;
          padding: 10px;
          border-radius: 4px;
          margin-bottom: 15px;
        }
        
        .search-results {
          margin-top: 30px;
        }
        
        .result-item {
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 15px;
          background-color: white;
        }
        
        .result-item h4 {
          margin-top: 0;
          color: #007bff;
        }
        
        .paper-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 15px;
          margin-bottom: 10px;
          font-size: 14px;
          color: #6c757d;
        }
        
        .paper-abstract {
          font-size: 14px;
          line-height: 1.5;
          color: #212529;
        }
        
        .paper-links {
          display: flex;
          gap: 15px;
          margin-top: 10px;
        }
        
        .paper-link {
          color: #007bff;
          text-decoration: none;
        }
        
        .paper-link:hover {
          text-decoration: underline;
        }
        
        .btn-sm {
          padding: 4px 8px;
          font-size: 14px;
        }
        
        .create-nft-btn {
          margin-left: auto;
        }
        
        .no-results {
          text-align: center;
          color: #6c757d;
          font-style: italic;
        }
      `}</style>
    </div>
  );
};

export default AdvancedSearch;import React, { useState } from 'react';
import axios from 'axios';

/**
 * Erweiterte Suchkomponente für wissenschaftliche Publikationen
 * Inspiriert von Scinapse.io für eine verbesserte Benutzererfahrung
 */
const AdvancedSearch = ({ onResultsFound, onSelectPaper }) => {
  // Suchparameter
  const [searchParams, setSearchParams] = useState({
    query: '',
    authors: '',
    year: '',
    venue: '',
    sortBy: 'relevance',
    sortOrder: 'desc'
  });

  // Suchstatus
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState(null);
  const [results, setResults] = useState(null);

  // Handler für Änderungen an den Suchparametern
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSearchParams(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Suchfunktion
  const handleSearch = async (e) => {
    e.preventDefault();
    
    // Prüfen, ob mindestens ein Suchkriterium angegeben wurde
    if (!searchParams.query && !searchParams.authors && !searchParams.year && !searchParams.venue) {
      setError('Bitte geben Sie mindestens ein Suchkriterium ein.');
      return;
    }
    
    setIsSearching(true);
    setError(null);
    
    try {
      // API-Anfrage mit den Suchparametern
      const response = await axios.get('/api/v1/semantic-scholar/advanced-search', {
        params: {
          ...searchParams,
          fields: 'title,authors,year,venue,url,externalIds,abstract,citationCount'
        }
      });
      
      setResults(response.data);
      
      // Callback für übergeordnete Komponente
      if (onResultsFound) {
        onResultsFound(response.data);
      }
    } catch (error) {
      console.error('Fehler bei der Suche:', error);
      setError(error.response?.data?.error || 'Ein Fehler ist bei der Suche aufgetreten.');
    } finally {
      setIsSearching(false);
    }
  };

  // Zurücksetzen der Suchparameter
  const handleReset = () => {
    setSearchParams({
      query: '',
      authors: '',
      year: '',
      venue: '',
      sortBy: 'relevance',
      sortOrder: 'desc'
    });
    setResults(null);
    setError(null);
  };

  return (
    <div className="advanced-search-container">
      <h2>Erweiterte Suche</h2>
      
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSearch} className="advanced-search-form">
        <div className="form-group">
          <label htmlFor="query">Suchbegriffe:</label>
          <input
            type="text"
            id="query"
            name="query"
            value={searchParams.query}
            onChange={handleInputChange}
            placeholder="z.B. quantum computing"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="authors">Autor(en):</label>
          <input
            type="text"
            id="authors"
            name="authors"
            value={searchParams.authors}
            onChange={handleInputChange}
            placeholder="z.B. John Smith"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="year">Jahr oder Jahresbereich:</label>
          <input
            type="text"
            id="year"
            name="year"
            value={searchParams.year}
            onChange={handleInputChange}
            placeholder="z.B. 2020 oder 2018-2021"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="venue">Journal/Konferenz:</label>
          <input
            type="text"
            id="venue"
            name="venue"
            value={searchParams.venue}
            onChange={handleInputChange}
            placeholder="z.B. Nature"
            className="form-control"
          />
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="sortBy">Sortieren nach:</label>
            <select
              id="sortBy"
              name="sortBy"
              value={searchParams.sortBy}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="relevance">Relevanz</option>
              <option value="year">Jahr</option>
              <option value="citationCount">Zitationen</option>
            </select>
          </div>
          
          <div className="form-group">
            <label htmlFor="sortOrder">Reihenfolge:</label>
            <select
              id="sortOrder"
              name="sortOrder"
              value={searchParams.sortOrder}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="desc">Absteigend</option>
              <option value="asc">Aufsteigend</option>
            </select>
          </div>
        </div>
        
        <div className="form-actions">
          <button 
            type="submit" 
            className="btn btn-primary" 
            disabled={isSearching}
          >
            {isSearching ? 'Suche läuft...' : 'Suchen'}
          </button>
          <button 
            type="button" 
            className="btn btn-secondary" 
            onClick={handleReset}
            disabled={isSearching}
          >
            Zurücksetzen
          </button>
        </div>
      </form>
      
      {results && (
        <div className="search-results">
          <h3>Suchergebnisse ({results.total})</h3>
          
          {results.data && results.data.length > 0 ? (
            <div className="results-list">
              {results.data.map((paper, index) => (
                <div key={paper.paperId || index} className="result-item">
                  <h4>{paper.title}</h4>
                  <div className="paper-meta">
                    <span className="paper-authors">
                      {paper.authors?.map(a => a.name).join(', ')}
                    </span>
                    <span className="paper-venue">
                      {paper.venue} ({paper.year})
                    </span>
                    {paper.citationCount !== undefined && (
                      <span className="paper-citations">
                        Zitationen: {paper.citationCount}
                      </span>
                    )}
                  </div>
                  {paper.abstract && (
                    <p className="paper-abstract">
                      {paper.abstract.length > 300 
                        ? `${paper.abstract.substring(0, 300)}...` 
                        : paper.abstract}
                    </p>
                  )}
                  <div className="paper-links">
                    {paper.url && (
                      <a 
                        href={paper.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        Zum Paper
                      </a>
                    )}
                    {paper.externalIds?.DOI && (
                      <a 
                        href={`https://doi.org/${paper.externalIds.DOI}`} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        DOI: {paper.externalIds?.DOI}
                      </a>
                    )}
                    <button
                      className="btn btn-sm btn-primary create-nft-btn"
                      onClick={() => {
                        // Funktion zur Auswahl des Papers für die NFT-Erstellung
                        if (onSelectPaper) {
                          onSelectPaper(paper);
                        } else {
                          alert(`NFT für DOI ${paper.externalIds?.DOI || 'unbekannt'} erstellen`);
                        }
                      }}
                    >
                      Als NFT erstellen
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="no-results">Keine Ergebnisse gefunden.</p>
          )}
        </div>
      )}
      
      {/* CSS für die Komponente */}
      <style jsx>{`
        .advanced-search-container {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        
        .advanced-search-form {
          background-color: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
        }
        
        .form-group {
          margin-bottom: 15px;
        }
        
        .form-row {
          display: flex;
          gap: 15px;
        }
        
        .form-control {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #ced4da;
          border-radius: 4px;
          font-size: 16px;
        }
        
        .form-actions {
          display: flex;
          gap: 10px;
          margin-top: 20px;
        }
        
        .btn {
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 16px;
        }
        
        .btn-primary {
          background-color: #007bff;
          color: white;
        }
        
        .btn-secondary {
          background-color: #6c757d;
          color: white;
        }
        
        .btn:disabled {
          opacity: 0.65;
          cursor: not-allowed;
        }
        
        .error-message {
          background-color: #f8d7da;
          color: #721c24;
          padding: 10px;
          border-radius: 4px;
          margin-bottom: 15px;
        }
        
        .search-results {
          margin-top: 30px;
        }
        
        .result-item {
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 15px;
          background-color: white;
        }
        
        .result-item h4 {
          margin-top: 0;
          color: #007bff;
        }
        
        .paper-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 15px;
          margin-bottom: 10px;
          font-size: 14px;
          color: #6c757d;
        }
        
        .paper-abstract {
          font-size: 14px;
          line-height: 1.5;
          color: #212529;
        }
        
        .paper-links {
          display: flex;
          gap: 15px;
          margin-top: 10px;
        }
        
        .paper-link {
          color: #007bff;
          text-decoration: none;
        }
        
        .paper-link:hover {
          text-decoration: underline;
        }
        
        .btn-sm {
          padding: 4px 8px;
          font-size: 14px;
        }
        
        .create-nft-btn {
          margin-left: auto;
        }
        
        .no-results {
          text-align: center;
          color: #6c757d;
          font-style: italic;
        }
      `}</style>
    </div>
  );
};

export default AdvancedSearch;import React, { useState } from 'react';
import axios from 'axios';

/**
 * Erweiterte Suchkomponente für wissenschaftliche Publikationen
 * Inspiriert von Scinapse.io für eine verbesserte Benutzererfahrung
 */
const AdvancedSearch = ({ onResultsFound, onSelectPaper }) => {
  // Suchparameter
  const [searchParams, setSearchParams] = useState({
    query: '',
    authors: '',
    year: '',
    venue: '',
    sortBy: 'relevance',
    sortOrder: 'desc'
  });

  // Suchstatus
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState(null);
  const [results, setResults] = useState(null);

  // Handler für Änderungen an den Suchparametern
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSearchParams(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Suchfunktion
  const handleSearch = async (e) => {
    e.preventDefault();
    
    // Prüfen, ob mindestens ein Suchkriterium angegeben wurde
    if (!searchParams.query && !searchParams.authors && !searchParams.year && !searchParams.venue) {
      setError('Bitte geben Sie mindestens ein Suchkriterium ein.');
      return;
    }
    
    setIsSearching(true);
    setError(null);
    
    try {
      // API-Anfrage mit den Suchparametern
      const response = await axios.get('/api/v1/semantic-scholar/advanced-search', {
        params: {
          ...searchParams,
          fields: 'title,authors,year,venue,url,externalIds,abstract,citationCount'
        }
      });
      
      setResults(response.data);
      
      // Callback für übergeordnete Komponente
      if (onResultsFound) {
        onResultsFound(response.data);
      }
    } catch (error) {
      console.error('Fehler bei der Suche:', error);
      setError(error.response?.data?.error || 'Ein Fehler ist bei der Suche aufgetreten.');
    } finally {
      setIsSearching(false);
    }
  };

  // Zurücksetzen der Suchparameter
  const handleReset = () => {
    setSearchParams({
      query: '',
      authors: '',
      year: '',
      venue: '',
      sortBy: 'relevance',
      sortOrder: 'desc'
    });
    setResults(null);
    setError(null);
  };

  return (
    <div className="advanced-search-container">
      <h2>Erweiterte Suche</h2>
      
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSearch} className="advanced-search-form">
        <div className="form-group">
          <label htmlFor="query">Suchbegriffe:</label>
          <input
            type="text"
            id="query"
            name="query"
            value={searchParams.query}
            onChange={handleInputChange}
            placeholder="z.B. quantum computing"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="authors">Autor(en):</label>
          <input
            type="text"
            id="authors"
            name="authors"
            value={searchParams.authors}
            onChange={handleInputChange}
            placeholder="z.B. John Smith"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="year">Jahr oder Jahresbereich:</label>
          <input
            type="text"
            id="year"
            name="year"
            value={searchParams.year}
            onChange={handleInputChange}
            placeholder="z.B. 2020 oder 2018-2021"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="venue">Journal/Konferenz:</label>
          <input
            type="text"
            id="venue"
            name="venue"
            value={searchParams.venue}
            onChange={handleInputChange}
            placeholder="z.B. Nature"
            className="form-control"
          />
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="sortBy">Sortieren nach:</label>
            <select
              id="sortBy"
              name="sortBy"
              value={searchParams.sortBy}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="relevance">Relevanz</option>
              <option value="year">Jahr</option>
              <option value="citationCount">Zitationen</option>
            </select>
          </div>
          
          <div className="form-group">
            <label htmlFor="sortOrder">Reihenfolge:</label>
            <select
              id="sortOrder"
              name="sortOrder"
              value={searchParams.sortOrder}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="desc">Absteigend</option>
              <option value="asc">Aufsteigend</option>
            </select>
          </div>
        </div>
        
        <div className="form-actions">
          <button 
            type="submit" 
            className="btn btn-primary" 
            disabled={isSearching}
          >
            {isSearching ? 'Suche läuft...' : 'Suchen'}
          </button>
          <button 
            type="button" 
            className="btn btn-secondary" 
            onClick={handleReset}
            disabled={isSearching}
          >
            Zurücksetzen
          </button>
        </div>
      </form>
      
      {results && (
        <div className="search-results">
          <h3>Suchergebnisse ({results.total})</h3>
          
          {results.data && results.data.length > 0 ? (
            <div className="results-list">
              {results.data.map((paper, index) => (
                <div key={paper.paperId || index} className="result-item">
                  <h4>{paper.title}</h4>
                  <div className="paper-meta">
                    <span className="paper-authors">
                      {paper.authors?.map(a => a.name).join(', ')}
                    </span>
                    <span className="paper-venue">
                      {paper.venue} ({paper.year})
                    </span>
                    {paper.citationCount !== undefined && (
                      <span className="paper-citations">
                        Zitationen: {paper.citationCount}
                      </span>
                    )}
                  </div>
                  {paper.abstract && (
                    <p className="paper-abstract">
                      {paper.abstract.length > 300 
                        ? `${paper.abstract.substring(0, 300)}...` 
                        : paper.abstract}
                    </p>
                  )}
                  <div className="paper-links">
                    {paper.url && (
                      <a 
                        href={paper.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        Zum Paper
                      </a>
                    )}
                    {paper.externalIds?.DOI && (
                      <a 
                        href={`https://doi.org/${paper.externalIds.DOI}`} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        DOI: {paper.externalIds?.DOI}
                      </a>
                    )}
                    <button
                      className="btn btn-sm btn-primary create-nft-btn"
                      onClick={() => {
                        // Funktion zur Auswahl des Papers für die NFT-Erstellung
                        if (onSelectPaper) {
                          onSelectPaper(paper);
                        } else {
                          alert(`NFT für DOI ${paper.externalIds?.DOI || 'unbekannt'} erstellen`);
                        }
                      }}
                    >
                      Als NFT erstellen
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="no-results">Keine Ergebnisse gefunden.</p>
          )}
        </div>
      )}
      
      {/* CSS für die Komponente */}
      <style jsx>{`
        .advanced-search-container {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        
        .advanced-search-form {
          background-color: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
        }
        
        .form-group {
          margin-bottom: 15px;
        }
        
        .form-row {
          display: flex;
          gap: 15px;
        }
        
        .form-control {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #ced4da;
          border-radius: 4px;
          font-size: 16px;
        }
        
        .form-actions {
          display: flex;
          gap: 10px;
          margin-top: 20px;
        }
        
        .btn {
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 16px;
        }
        
        .btn-primary {
          background-color: #007bff;
          color: white;
        }
        
        .btn-secondary {
          background-color: #6c757d;
          color: white;
        }
        
        .btn:disabled {
          opacity: 0.65;
          cursor: not-allowed;
        }
        
        .error-message {
          background-color: #f8d7da;
          color: #721c24;
          padding: 10px;
          border-radius: 4px;
          margin-bottom: 15px;
        }
        
        .search-results {
          margin-top: 30px;
        }
        
        .result-item {
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 15px;
          background-color: white;
        }
        
        .result-item h4 {
          margin-top: 0;
          color: #007bff;
        }
        
        .paper-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 15px;
          margin-bottom: 10px;
          font-size: 14px;
          color: #6c757d;
        }
        
        .paper-abstract {
          font-size: 14px;
          line-height: 1.5;
          color: #212529;
        }
        
        .paper-links {
          display: flex;
          gap: 15px;
          margin-top: 10px;
        }
        
        .paper-link {
          color: #007bff;
          text-decoration: none;
        }
        
        .paper-link:hover {
          text-decoration: underline;
        }
        
        .btn-sm {
          padding: 4px 8px;
          font-size: 14px;
        }
        
        .create-nft-btn {
          margin-left: auto;
        }
        
        .no-results {
          text-align: center;
          color: #6c757d;
          font-style: italic;
        }
      `}</style>
    </div>
  );
};

export default AdvancedSearch;import React, { useState } from 'react';
import axios from 'axios';

/**
 * Erweiterte Suchkomponente für wissenschaftliche Publikationen
 * Inspiriert von Scinapse.io für eine verbesserte Benutzererfahrung
 */
const AdvancedSearch = ({ onResultsFound, onSelectPaper }) => {
  // Suchparameter
  const [searchParams, setSearchParams] = useState({
    query: '',
    authors: '',
    year: '',
    venue: '',
    sortBy: 'relevance',
    sortOrder: 'desc'
  });

  // Suchstatus
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState(null);
  const [results, setResults] = useState(null);

  // Handler für Änderungen an den Suchparametern
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSearchParams(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Suchfunktion
  const handleSearch = async (e) => {
    e.preventDefault();
    
    // Prüfen, ob mindestens ein Suchkriterium angegeben wurde
    if (!searchParams.query && !searchParams.authors && !searchParams.year && !searchParams.venue) {
      setError('Bitte geben Sie mindestens ein Suchkriterium ein.');
      return;
    }
    
    setIsSearching(true);
    setError(null);
    
    try {
      // API-Anfrage mit den Suchparametern
      const response = await axios.get('/api/v1/semantic-scholar/advanced-search', {
        params: {
          ...searchParams,
          fields: 'title,authors,year,venue,url,externalIds,abstract,citationCount'
        }
      });
      
      setResults(response.data);
      
      // Callback für übergeordnete Komponente
      if (onResultsFound) {
        onResultsFound(response.data);
      }
    } catch (error) {
      console.error('Fehler bei der Suche:', error);
      setError(error.response?.data?.error || 'Ein Fehler ist bei der Suche aufgetreten.');
    } finally {
      setIsSearching(false);
    }
  };

  // Zurücksetzen der Suchparameter
  const handleReset = () => {
    setSearchParams({
      query: '',
      authors: '',
      year: '',
      venue: '',
      sortBy: 'relevance',
      sortOrder: 'desc'
    });
    setResults(null);
    setError(null);
  };

  return (
    <div className="advanced-search-container">
      <h2>Erweiterte Suche</h2>
      
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSearch} className="advanced-search-form">
        <div className="form-group">
          <label htmlFor="query">Suchbegriffe:</label>
          <input
            type="text"
            id="query"
            name="query"
            value={searchParams.query}
            onChange={handleInputChange}
            placeholder="z.B. quantum computing"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="authors">Autor(en):</label>
          <input
            type="text"
            id="authors"
            name="authors"
            value={searchParams.authors}
            onChange={handleInputChange}
            placeholder="z.B. John Smith"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="year">Jahr oder Jahresbereich:</label>
          <input
            type="text"
            id="year"
            name="year"
            value={searchParams.year}
            onChange={handleInputChange}
            placeholder="z.B. 2020 oder 2018-2021"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="venue">Journal/Konferenz:</label>
          <input
            type="text"
            id="venue"
            name="venue"
            value={searchParams.venue}
            onChange={handleInputChange}
            placeholder="z.B. Nature"
            className="form-control"
          />
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="sortBy">Sortieren nach:</label>
            <select
              id="sortBy"
              name="sortBy"
              value={searchParams.sortBy}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="relevance">Relevanz</option>
              <option value="year">Jahr</option>
              <option value="citationCount">Zitationen</option>
            </select>
          </div>
          
          <div className="form-group">
            <label htmlFor="sortOrder">Reihenfolge:</label>
            <select
              id="sortOrder"
              name="sortOrder"
              value={searchParams.sortOrder}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="desc">Absteigend</option>
              <option value="asc">Aufsteigend</option>
            </select>
          </div>
        </div>
        
        <div className="form-actions">
          <button 
            type="submit" 
            className="btn btn-primary" 
            disabled={isSearching}
          >
            {isSearching ? 'Suche läuft...' : 'Suchen'}
          </button>
          <button 
            type="button" 
            className="btn btn-secondary" 
            onClick={handleReset}
            disabled={isSearching}
          >
            Zurücksetzen
          </button>
        </div>
      </form>
      
      {results && (
        <div className="search-results">
          <h3>Suchergebnisse ({results.total})</h3>
          
          {results.data && results.data.length > 0 ? (
            <div className="results-list">
              {results.data.map((paper, index) => (
                <div key={paper.paperId || index} className="result-item">
                  <h4>{paper.title}</h4>
                  <div className="paper-meta">
                    <span className="paper-authors">
                      {paper.authors?.map(a => a.name).join(', ')}
                    </span>
                    <span className="paper-venue">
                      {paper.venue} ({paper.year})
                    </span>
                    {paper.citationCount !== undefined && (
                      <span className="paper-citations">
                        Zitationen: {paper.citationCount}
                      </span>
                    )}
                  </div>
                  {paper.abstract && (
                    <p className="paper-abstract">
                      {paper.abstract.length > 300 
                        ? `${paper.abstract.substring(0, 300)}...` 
                        : paper.abstract}
                    </p>
                  )}
                  <div className="paper-links">
                    {paper.url && (
                      <a 
                        href={paper.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        Zum Paper
                      </a>
                    )}
                    {paper.externalIds?.DOI && (
                      <a 
                        href={`https://doi.org/${paper.externalIds.DOI}`} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        DOI: {paper.externalIds?.DOI}
                      </a>
                    )}
                    <button
                      className="btn btn-sm btn-primary create-nft-btn"
                      onClick={() => {
                        // Funktion zur Auswahl des Papers für die NFT-Erstellung
                        if (onSelectPaper) {
                          onSelectPaper(paper);
                        } else {
                          alert(`NFT für DOI ${paper.externalIds?.DOI || 'unbekannt'} erstellen`);
                        }
                      }}
                    >
                      Als NFT erstellen
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="no-results">Keine Ergebnisse gefunden.</p>
          )}
        </div>
      )}
      
      {/* CSS für die Komponente */}
      <style jsx>{`
        .advanced-search-container {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        
        .advanced-search-form {
          background-color: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
        }
        
        .form-group {
          margin-bottom: 15px;
        }
        
        .form-row {
          display: flex;
          gap: 15px;
        }
        
        .form-control {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #ced4da;
          border-radius: 4px;
          font-size: 16px;
        }
        
        .form-actions {
          display: flex;
          gap: 10px;
          margin-top: 20px;
        }
        
        .btn {
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 16px;
        }
        
        .btn-primary {
          background-color: #007bff;
          color: white;
        }
        
        .btn-secondary {
          background-color: #6c757d;
          color: white;
        }
        
        .btn:disabled {
          opacity: 0.65;
          cursor: not-allowed;
        }
        
        .error-message {
          background-color: #f8d7da;
          color: #721c24;
          padding: 10px;
          border-radius: 4px;
          margin-bottom: 15px;
        }
        
        .search-results {
          margin-top: 30px;
        }
        
        .result-item {
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 15px;
          background-color: white;
        }
        
        .result-item h4 {
          margin-top: 0;
          color: #007bff;
        }
        
        .paper-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 15px;
          margin-bottom: 10px;
          font-size: 14px;
          color: #6c757d;
        }
        
        .paper-abstract {
          font-size: 14px;
          line-height: 1.5;
          color: #212529;
        }
        
        .paper-links {
          display: flex;
          gap: 15px;
          margin-top: 10px;
        }
        
        .paper-link {
          color: #007bff;
          text-decoration: none;
        }
        
        .paper-link:hover {
          text-decoration: underline;
        }
        
        .btn-sm {
          padding: 4px 8px;
          font-size: 14px;
        }
        
        .create-nft-btn {
          margin-left: auto;
        }
        
        .no-results {
          text-align: center;
          color: #6c757d;
          font-style: italic;
        }
      `}</style>
    </div>
  );
};

export default AdvancedSearch;import React, { useState } from 'react';
import axios from 'axios';

/**
 * Erweiterte Suchkomponente für wissenschaftliche Publikationen
 * Inspiriert von Scinapse.io für eine verbesserte Benutzererfahrung
 */
const AdvancedSearch = ({ onResultsFound, onSelectPaper }) => {
  // Suchparameter
  const [searchParams, setSearchParams] = useState({
    query: '',
    authors: '',
    year: '',
    venue: '',
    sortBy: 'relevance',
    sortOrder: 'desc'
  });

  // Suchstatus
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState(null);
  const [results, setResults] = useState(null);

  // Handler für Änderungen an den Suchparametern
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSearchParams(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Suchfunktion
  const handleSearch = async (e) => {
    e.preventDefault();
    
    // Prüfen, ob mindestens ein Suchkriterium angegeben wurde
    if (!searchParams.query && !searchParams.authors && !searchParams.year && !searchParams.venue) {
      setError('Bitte geben Sie mindestens ein Suchkriterium ein.');
      return;
    }
    
    setIsSearching(true);
    setError(null);
    
    try {
      // API-Anfrage mit den Suchparametern
      const response = await axios.get('/api/v1/semantic-scholar/advanced-search', {
        params: {
          ...searchParams,
          fields: 'title,authors,year,venue,url,externalIds,abstract,citationCount'
        }
      });
      
      setResults(response.data);
      
      // Callback für übergeordnete Komponente
      if (onResultsFound) {
        onResultsFound(response.data);
      }
    } catch (error) {
      console.error('Fehler bei der Suche:', error);
      setError(error.response?.data?.error || 'Ein Fehler ist bei der Suche aufgetreten.');
    } finally {
      setIsSearching(false);
    }
  };

  // Zurücksetzen der Suchparameter
  const handleReset = () => {
    setSearchParams({
      query: '',
      authors: '',
      year: '',
      venue: '',
      sortBy: 'relevance',
      sortOrder: 'desc'
    });
    setResults(null);
    setError(null);
  };

  return (
    <div className="advanced-search-container">
      <h2>Erweiterte Suche</h2>
      
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSearch} className="advanced-search-form">
        <div className="form-group">
          <label htmlFor="query">Suchbegriffe:</label>
          <input
            type="text"
            id="query"
            name="query"
            value={searchParams.query}
            onChange={handleInputChange}
            placeholder="z.B. quantum computing"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="authors">Autor(en):</label>
          <input
            type="text"
            id="authors"
            name="authors"
            value={searchParams.authors}
            onChange={handleInputChange}
            placeholder="z.B. John Smith"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="year">Jahr oder Jahresbereich:</label>
          <input
            type="text"
            id="year"
            name="year"
            value={searchParams.year}
            onChange={handleInputChange}
            placeholder="z.B. 2020 oder 2018-2021"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="venue">Journal/Konferenz:</label>
          <input
            type="text"
            id="venue"
            name="venue"
            value={searchParams.venue}
            onChange={handleInputChange}
            placeholder="z.B. Nature"
            className="form-control"
          />
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="sortBy">Sortieren nach:</label>
            <select
              id="sortBy"
              name="sortBy"
              value={searchParams.sortBy}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="relevance">Relevanz</option>
              <option value="year">Jahr</option>
              <option value="citationCount">Zitationen</option>
            </select>
          </div>
          
          <div className="form-group">
            <label htmlFor="sortOrder">Reihenfolge:</label>
            <select
              id="sortOrder"
              name="sortOrder"
              value={searchParams.sortOrder}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="desc">Absteigend</option>
              <option value="asc">Aufsteigend</option>
            </select>
          </div>
        </div>
        
        <div className="form-actions">
          <button 
            type="submit" 
            className="btn btn-primary" 
            disabled={isSearching}
          >
            {isSearching ? 'Suche läuft...' : 'Suchen'}
          </button>
          <button 
            type="button" 
            className="btn btn-secondary" 
            onClick={handleReset}
            disabled={isSearching}
          >
            Zurücksetzen
          </button>
        </div>
      </form>
      
      {results && (
        <div className="search-results">
          <h3>Suchergebnisse ({results.total})</h3>
          
          {results.data && results.data.length > 0 ? (
            <div className="results-list">
              {results.data.map((paper, index) => (
                <div key={paper.paperId || index} className="result-item">
                  <h4>{paper.title}</h4>
                  <div className="paper-meta">
                    <span className="paper-authors">
                      {paper.authors?.map(a => a.name).join(', ')}
                    </span>
                    <span className="paper-venue">
                      {paper.venue} ({paper.year})
                    </span>
                    {paper.citationCount !== undefined && (
                      <span className="paper-citations">
                        Zitationen: {paper.citationCount}
                      </span>
                    )}
                  </div>
                  {paper.abstract && (
                    <p className="paper-abstract">
                      {paper.abstract.length > 300 
                        ? `${paper.abstract.substring(0, 300)}...` 
                        : paper.abstract}
                    </p>
                  )}
                  <div className="paper-links">
                    {paper.url && (
                      <a 
                        href={paper.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        Zum Paper
                      </a>
                    )}
                    {paper.externalIds?.DOI && (
                      <a 
                        href={`https://doi.org/${paper.externalIds.DOI}`} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        DOI: {paper.externalIds?.DOI}
                      </a>
                    )}
                    <button
                      className="btn btn-sm btn-primary create-nft-btn"
                      onClick={() => {
                        // Funktion zur Auswahl des Papers für die NFT-Erstellung
                        if (onSelectPaper) {
                          onSelectPaper(paper);
                        } else {
                          alert(`NFT für DOI ${paper.externalIds?.DOI || 'unbekannt'} erstellen`);
                        }
                      }}
                    >
                      Als NFT erstellen
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="no-results">Keine Ergebnisse gefunden.</p>
          )}
        </div>
      )}
      
      {/* CSS für die Komponente */}
      <style jsx>{`
        .advanced-search-container {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        
        .advanced-search-form {
          background-color: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
        }
        
        .form-group {
          margin-bottom: 15px;
        }
        
        .form-row {
          display: flex;
          gap: 15px;
        }
        
        .form-control {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #ced4da;
          border-radius: 4px;
          font-size: 16px;
        }
        
        .form-actions {
          display: flex;
          gap: 10px;
          margin-top: 20px;
        }
        
        .btn {
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 16px;
        }
        
        .btn-primary {
          background-color: #007bff;
          color: white;
        }
        
        .btn-secondary {
          background-color: #6c757d;
          color: white;
        }
        
        .btn:disabled {
          opacity: 0.65;
          cursor: not-allowed;
        }
        
        .error-message {
          background-color: #f8d7da;
          color: #721c24;
          padding: 10px;
          border-radius: 4px;
          margin-bottom: 15px;
        }
        
        .search-results {
          margin-top: 30px;
        }
        
        .result-item {
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 15px;
          background-color: white;
        }
        
        .result-item h4 {
          margin-top: 0;
          color: #007bff;
        }
        
        .paper-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 15px;
          margin-bottom: 10px;
          font-size: 14px;
          color: #6c757d;
        }
        
        .paper-abstract {
          font-size: 14px;
          line-height: 1.5;
          color: #212529;
        }
        
        .paper-links {
          display: flex;
          gap: 15px;
          margin-top: 10px;
        }
        
        .paper-link {
          color: #007bff;
          text-decoration: none;
        }
        
        .paper-link:hover {
          text-decoration: underline;
        }
        
        .btn-sm {
          padding: 4px 8px;
          font-size: 14px;
        }
        
        .create-nft-btn {
          margin-left: auto;
        }
        
        .no-results {
          text-align: center;
          color: #6c757d;
          font-style: italic;
        }
      `}</style>
    </div>
  );
};

export default AdvancedSearch;import React, { useState } from 'react';
import axios from 'axios';

/**
 * Erweiterte Suchkomponente für wissenschaftliche Publikationen
 * Inspiriert von Scinapse.io für eine verbesserte Benutzererfahrung
 */
const AdvancedSearch = ({ onResultsFound, onSelectPaper }) => {
  // Suchparameter
  const [searchParams, setSearchParams] = useState({
    query: '',
    authors: '',
    year: '',
    venue: '',
    sortBy: 'relevance',
    sortOrder: 'desc'
  });

  // Suchstatus
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState(null);
  const [results, setResults] = useState(null);

  // Handler für Änderungen an den Suchparametern
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSearchParams(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Suchfunktion
  const handleSearch = async (e) => {
    e.preventDefault();
    
    // Prüfen, ob mindestens ein Suchkriterium angegeben wurde
    if (!searchParams.query && !searchParams.authors && !searchParams.year && !searchParams.venue) {
      setError('Bitte geben Sie mindestens ein Suchkriterium ein.');
      return;
    }
    
    setIsSearching(true);
    setError(null);
    
    try {
      // API-Anfrage mit den Suchparametern
      const response = await axios.get('/api/v1/semantic-scholar/advanced-search', {
        params: {
          ...searchParams,
          fields: 'title,authors,year,venue,url,externalIds,abstract,citationCount'
        }
      });
      
      setResults(response.data);
      
      // Callback für übergeordnete Komponente
      if (onResultsFound) {
        onResultsFound(response.data);
      }
    } catch (error) {
      console.error('Fehler bei der Suche:', error);
      setError(error.response?.data?.error || 'Ein Fehler ist bei der Suche aufgetreten.');
    } finally {
      setIsSearching(false);
    }
  };

  // Zurücksetzen der Suchparameter
  const handleReset = () => {
    setSearchParams({
      query: '',
      authors: '',
      year: '',
      venue: '',
      sortBy: 'relevance',
      sortOrder: 'desc'
    });
    setResults(null);
    setError(null);
  };

  return (
    <div className="advanced-search-container">
      <h2>Erweiterte Suche</h2>
      
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSearch} className="advanced-search-form">
        <div className="form-group">
          <label htmlFor="query">Suchbegriffe:</label>
          <input
            type="text"
            id="query"
            name="query"
            value={searchParams.query}
            onChange={handleInputChange}
            placeholder="z.B. quantum computing"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="authors">Autor(en):</label>
          <input
            type="text"
            id="authors"
            name="authors"
            value={searchParams.authors}
            onChange={handleInputChange}
            placeholder="z.B. John Smith"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="year">Jahr oder Jahresbereich:</label>
          <input
            type="text"
            id="year"
            name="year"
            value={searchParams.year}
            onChange={handleInputChange}
            placeholder="z.B. 2020 oder 2018-2021"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="venue">Journal/Konferenz:</label>
          <input
            type="text"
            id="venue"
            name="venue"
            value={searchParams.venue}
            onChange={handleInputChange}
            placeholder="z.B. Nature"
            className="form-control"
          />
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="sortBy">Sortieren nach:</label>
            <select
              id="sortBy"
              name="sortBy"
              value={searchParams.sortBy}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="relevance">Relevanz</option>
              <option value="year">Jahr</option>
              <option value="citationCount">Zitationen</option>
            </select>
          </div>
          
          <div className="form-group">
            <label htmlFor="sortOrder">Reihenfolge:</label>
            <select
              id="sortOrder"
              name="sortOrder"
              value={searchParams.sortOrder}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="desc">Absteigend</option>
              <option value="asc">Aufsteigend</option>
            </select>
          </div>
        </div>
        
        <div className="form-actions">
          <button 
            type="submit" 
            className="btn btn-primary" 
            disabled={isSearching}
          >
            {isSearching ? 'Suche läuft...' : 'Suchen'}
          </button>
          <button 
            type="button" 
            className="btn btn-secondary" 
            onClick={handleReset}
            disabled={isSearching}
          >
            Zurücksetzen
          </button>
        </div>
      </form>
      
      {results && (
        <div className="search-results">
          <h3>Suchergebnisse ({results.total})</h3>
          
          {results.data && results.data.length > 0 ? (
            <div className="results-list">
              {results.data.map((paper, index) => (
                <div key={paper.paperId || index} className="result-item">
                  <h4>{paper.title}</h4>
                  <div className="paper-meta">
                    <span className="paper-authors">
                      {paper.authors?.map(a => a.name).join(', ')}
                    </span>
                    <span className="paper-venue">
                      {paper.venue} ({paper.year})
                    </span>
                    {paper.citationCount !== undefined && (
                      <span className="paper-citations">
                        Zitationen: {paper.citationCount}
                      </span>
                    )}
                  </div>
                  {paper.abstract && (
                    <p className="paper-abstract">
                      {paper.abstract.length > 300 
                        ? `${paper.abstract.substring(0, 300)}...` 
                        : paper.abstract}
                    </p>
                  )}
                  <div className="paper-links">
                    {paper.url && (
                      <a 
                        href={paper.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        Zum Paper
                      </a>
                    )}
                    {paper.externalIds?.DOI && (
                      <a 
                        href={`https://doi.org/${paper.externalIds.DOI}`} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        DOI: {paper.externalIds?.DOI}
                      </a>
                    )}
                    <button
                      className="btn btn-sm btn-primary create-nft-btn"
                      onClick={() => {
                        // Funktion zur Auswahl des Papers für die NFT-Erstellung
                        if (onSelectPaper) {
                          onSelectPaper(paper);
                        } else {
                          alert(`NFT für DOI ${paper.externalIds?.DOI || 'unbekannt'} erstellen`);
                        }
                      }}
                    >
                      Als NFT erstellen
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="no-results">Keine Ergebnisse gefunden.</p>
          )}
        </div>
      )}
      
      {/* CSS für die Komponente */}
      <style jsx>{`
        .advanced-search-container {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        
        .advanced-search-form {
          background-color: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
        }
        
        .form-group {
          margin-bottom: 15px;
        }
        
        .form-row {
          display: flex;
          gap: 15px;
        }
        
        .form-control {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #ced4da;
          border-radius: 4px;
          font-size: 16px;
        }
        
        .form-actions {
          display: flex;
          gap: 10px;
          margin-top: 20px;
        }
        
        .btn {
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 16px;
        }
        
        .btn-primary {
          background-color: #007bff;
          color: white;
        }
        
        .btn-secondary {
          background-color: #6c757d;
          color: white;
        }
        
        .btn:disabled {
          opacity: 0.65;
          cursor: not-allowed;
        }
        
        .error-message {
          background-color: #f8d7da;
          color: #721c24;
          padding: 10px;
          border-radius: 4px;
          margin-bottom: 15px;
        }
        
        .search-results {
          margin-top: 30px;
        }
        
        .result-item {
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 15px;
          background-color: white;
        }
        
        .result-item h4 {
          margin-top: 0;
          color: #007bff;
        }
        
        .paper-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 15px;
          margin-bottom: 10px;
          font-size: 14px;
          color: #6c757d;
        }
        
        .paper-abstract {
          font-size: 14px;
          line-height: 1.5;
          color: #212529;
        }
        
        .paper-links {
          display: flex;
          gap: 15px;
          margin-top: 10px;
        }
        
        .paper-link {
          color: #007bff;
          text-decoration: none;
        }
        
        .paper-link:hover {
          text-decoration: underline;
        }
        
        .btn-sm {
          padding: 4px 8px;
          font-size: 14px;
        }
        
        .create-nft-btn {
          margin-left: auto;
        }
        
        .no-results {
          text-align: center;
          color: #6c757d;
          font-style: italic;
        }
      `}</style>
    </div>
  );
};

export default AdvancedSearch;import React, { useState } from 'react';
import axios from 'axios';

/**
 * Erweiterte Suchkomponente für wissenschaftliche Publikationen
 * Inspiriert von Scinapse.io für eine verbesserte Benutzererfahrung
 */
const AdvancedSearch = ({ onResultsFound, onSelectPaper }) => {
  // Suchparameter
  const [searchParams, setSearchParams] = useState({
    query: '',
    authors: '',
    year: '',
    venue: '',
    sortBy: 'relevance',
    sortOrder: 'desc'
  });

  // Suchstatus
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState(null);
  const [results, setResults] = useState(null);

  // Handler für Änderungen an den Suchparametern
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSearchParams(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Suchfunktion
  const handleSearch = async (e) => {
    e.preventDefault();
    
    // Prüfen, ob mindestens ein Suchkriterium angegeben wurde
    if (!searchParams.query && !searchParams.authors && !searchParams.year && !searchParams.venue) {
      setError('Bitte geben Sie mindestens ein Suchkriterium ein.');
      return;
    }
    
    setIsSearching(true);
    setError(null);
    
    try {
      // API-Anfrage mit den Suchparametern
      const response = await axios.get('/api/v1/semantic-scholar/advanced-search', {
        params: {
          ...searchParams,
          fields: 'title,authors,year,venue,url,externalIds,abstract,citationCount'
        }
      });
      
      setResults(response.data);
      
      // Callback für übergeordnete Komponente
      if (onResultsFound) {
        onResultsFound(response.data);
      }
    } catch (error) {
      console.error('Fehler bei der Suche:', error);
      setError(error.response?.data?.error || 'Ein Fehler ist bei der Suche aufgetreten.');
    } finally {
      setIsSearching(false);
    }
  };

  // Zurücksetzen der Suchparameter
  const handleReset = () => {
    setSearchParams({
      query: '',
      authors: '',
      year: '',
      venue: '',
      sortBy: 'relevance',
      sortOrder: 'desc'
    });
    setResults(null);
    setError(null);
  };

  return (
    <div className="advanced-search-container">
      <h2>Erweiterte Suche</h2>
      
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSearch} className="advanced-search-form">
        <div className="form-group">
          <label htmlFor="query">Suchbegriffe:</label>
          <input
            type="text"
            id="query"
            name="query"
            value={searchParams.query}
            onChange={handleInputChange}
            placeholder="z.B. quantum computing"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="authors">Autor(en):</label>
          <input
            type="text"
            id="authors"
            name="authors"
            value={searchParams.authors}
            onChange={handleInputChange}
            placeholder="z.B. John Smith"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="year">Jahr oder Jahresbereich:</label>
          <input
            type="text"
            id="year"
            name="year"
            value={searchParams.year}
            onChange={handleInputChange}
            placeholder="z.B. 2020 oder 2018-2021"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="venue">Journal/Konferenz:</label>
          <input
            type="text"
            id="venue"
            name="venue"
            value={searchParams.venue}
            onChange={handleInputChange}
            placeholder="z.B. Nature"
            className="form-control"
          />
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="sortBy">Sortieren nach:</label>
            <select
              id="sortBy"
              name="sortBy"
              value={searchParams.sortBy}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="relevance">Relevanz</option>
              <option value="year">Jahr</option>
              <option value="citationCount">Zitationen</option>
            </select>
          </div>
          
          <div className="form-group">
            <label htmlFor="sortOrder">Reihenfolge:</label>
            <select
              id="sortOrder"
              name="sortOrder"
              value={searchParams.sortOrder}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="desc">Absteigend</option>
              <option value="asc">Aufsteigend</option>
            </select>
          </div>
        </div>
        
        <div className="form-actions">
          <button 
            type="submit" 
            className="btn btn-primary" 
            disabled={isSearching}
          >
            {isSearching ? 'Suche läuft...' : 'Suchen'}
          </button>
          <button 
            type="button" 
            className="btn btn-secondary" 
            onClick={handleReset}
            disabled={isSearching}
          >
            Zurücksetzen
          </button>
        </div>
      </form>
      
      {results && (
        <div className="search-results">
          <h3>Suchergebnisse ({results.total})</h3>
          
          {results.data && results.data.length > 0 ? (
            <div className="results-list">
              {results.data.map((paper, index) => (
                <div key={paper.paperId || index} className="result-item">
                  <h4>{paper.title}</h4>
                  <div className="paper-meta">
                    <span className="paper-authors">
                      {paper.authors?.map(a => a.name).join(', ')}
                    </span>
                    <span className="paper-venue">
                      {paper.venue} ({paper.year})
                    </span>
                    {paper.citationCount !== undefined && (
                      <span className="paper-citations">
                        Zitationen: {paper.citationCount}
                      </span>
                    )}
                  </div>
                  {paper.abstract && (
                    <p className="paper-abstract">
                      {paper.abstract.length > 300 
                        ? `${paper.abstract.substring(0, 300)}...` 
                        : paper.abstract}
                    </p>
                  )}
                  <div className="paper-links">
                    {paper.url && (
                      <a 
                        href={paper.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        Zum Paper
                      </a>
                    )}
                    {paper.externalIds?.DOI && (
                      <a 
                        href={`https://doi.org/${paper.externalIds.DOI}`} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        DOI: {paper.externalIds?.DOI}
                      </a>
                    )}
                    <button
                      className="btn btn-sm btn-primary create-nft-btn"
                      onClick={() => {
                        // Funktion zur Auswahl des Papers für die NFT-Erstellung
                        if (onSelectPaper) {
                          onSelectPaper(paper);
                        } else {
                          alert(`NFT für DOI ${paper.externalIds?.DOI || 'unbekannt'} erstellen`);
                        }
                      }}
                    >
                      Als NFT erstellen
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="no-results">Keine Ergebnisse gefunden.</p>
          )}
        </div>
      )}
      
      {/* CSS für die Komponente */}
      <style jsx>{`
        .advanced-search-container {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        
        .advanced-search-form {
          background-color: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
        }
        
        .form-group {
          margin-bottom: 15px;
        }
        
        .form-row {
          display: flex;
          gap: 15px;
        }
        
        .form-control {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #ced4da;
          border-radius: 4px;
          font-size: 16px;
        }
        
        .form-actions {
          display: flex;
          gap: 10px;
          margin-top: 20px;
        }
        
        .btn {
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 16px;
        }
        
        .btn-primary {
          background-color: #007bff;
          color: white;
        }
        
        .btn-secondary {
          background-color: #6c757d;
          color: white;
        }
        
        .btn:disabled {
          opacity: 0.65;
          cursor: not-allowed;
        }
        
        .error-message {
          background-color: #f8d7da;
          color: #721c24;
          padding: 10px;
          border-radius: 4px;
          margin-bottom: 15px;
        }
        
        .search-results {
          margin-top: 30px;
        }
        
        .result-item {
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 15px;
          background-color: white;
        }
        
        .result-item h4 {
          margin-top: 0;
          color: #007bff;
        }
        
        .paper-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 15px;
          margin-bottom: 10px;
          font-size: 14px;
          color: #6c757d;
        }
        
        .paper-abstract {
          font-size: 14px;
          line-height: 1.5;
          color: #212529;
        }
        
        .paper-links {
          display: flex;
          gap: 15px;
          margin-top: 10px;
        }
        
        .paper-link {
          color: #007bff;
          text-decoration: none;
        }
        
        .paper-link:hover {
          text-decoration: underline;
        }
        
        .btn-sm {
          padding: 4px 8px;
          font-size: 14px;
        }
        
        .create-nft-btn {
          margin-left: auto;
        }
        
        .no-results {
          text-align: center;
          color: #6c757d;
          font-style: italic;
        }
      `}</style>
    </div>
  );
};

export default AdvancedSearch;import React, { useState } from 'react';
import axios from 'axios';

/**
 * Erweiterte Suchkomponente für wissenschaftliche Publikationen
 * Inspiriert von Scinapse.io für eine verbesserte Benutzererfahrung
 */
const AdvancedSearch = ({ onResultsFound, onSelectPaper }) => {
  // Suchparameter
  const [searchParams, setSearchParams] = useState({
    query: '',
    authors: '',
    year: '',
    venue: '',
    sortBy: 'relevance',
    sortOrder: 'desc'
  });

  // Suchstatus
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState(null);
  const [results, setResults] = useState(null);

  // Handler für Änderungen an den Suchparametern
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSearchParams(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Suchfunktion
  const handleSearch = async (e) => {
    e.preventDefault();
    
    // Prüfen, ob mindestens ein Suchkriterium angegeben wurde
    if (!searchParams.query && !searchParams.authors && !searchParams.year && !searchParams.venue) {
      setError('Bitte geben Sie mindestens ein Suchkriterium ein.');
      return;
    }
    
    setIsSearching(true);
    setError(null);
    
    try {
      // API-Anfrage mit den Suchparametern
      const response = await axios.get('/api/v1/semantic-scholar/advanced-search', {
        params: {
          ...searchParams,
          fields: 'title,authors,year,venue,url,externalIds,abstract,citationCount'
        }
      });
      
      setResults(response.data);
      
      // Callback für übergeordnete Komponente
      if (onResultsFound) {
        onResultsFound(response.data);
      }
    } catch (error) {
      console.error('Fehler bei der Suche:', error);
      setError(error.response?.data?.error || 'Ein Fehler ist bei der Suche aufgetreten.');
    } finally {
      setIsSearching(false);
    }
  };

  // Zurücksetzen der Suchparameter
  const handleReset = () => {
    setSearchParams({
      query: '',
      authors: '',
      year: '',
      venue: '',
      sortBy: 'relevance',
      sortOrder: 'desc'
    });
    setResults(null);
    setError(null);
  };

  return (
    <div className="advanced-search-container">
      <h2>Erweiterte Suche</h2>
      
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSearch} className="advanced-search-form">
        <div className="form-group">
          <label htmlFor="query">Suchbegriffe:</label>
          <input
            type="text"
            id="query"
            name="query"
            value={searchParams.query}
            onChange={handleInputChange}
            placeholder="z.B. quantum computing"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="authors">Autor(en):</label>
          <input
            type="text"
            id="authors"
            name="authors"
            value={searchParams.authors}
            onChange={handleInputChange}
            placeholder="z.B. John Smith"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="year">Jahr oder Jahresbereich:</label>
          <input
            type="text"
            id="year"
            name="year"
            value={searchParams.year}
            onChange={handleInputChange}
            placeholder="z.B. 2020 oder 2018-2021"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="venue">Journal/Konferenz:</label>
          <input
            type="text"
            id="venue"
            name="venue"
            value={searchParams.venue}
            onChange={handleInputChange}
            placeholder="z.B. Nature"
            className="form-control"
          />
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="sortBy">Sortieren nach:</label>
            <select
              id="sortBy"
              name="sortBy"
              value={searchParams.sortBy}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="relevance">Relevanz</option>
              <option value="year">Jahr</option>
              <option value="citationCount">Zitationen</option>
            </select>
          </div>
          
          <div className="form-group">
            <label htmlFor="sortOrder">Reihenfolge:</label>
            <select
              id="sortOrder"
              name="sortOrder"
              value={searchParams.sortOrder}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="desc">Absteigend</option>
              <option value="asc">Aufsteigend</option>
            </select>
          </div>
        </div>
        
        <div className="form-actions">
          <button 
            type="submit" 
            className="btn btn-primary" 
            disabled={isSearching}
          >
            {isSearching ? 'Suche läuft...' : 'Suchen'}
          </button>
          <button 
            type="button" 
            className="btn btn-secondary" 
            onClick={handleReset}
            disabled={isSearching}
          >
            Zurücksetzen
          </button>
        </div>
      </form>
      
      {results && (
        <div className="search-results">
          <h3>Suchergebnisse ({results.total})</h3>
          
          {results.data && results.data.length > 0 ? (
            <div className="results-list">
              {results.data.map((paper, index) => (
                <div key={paper.paperId || index} className="result-item">
                  <h4>{paper.title}</h4>
                  <div className="paper-meta">
                    <span className="paper-authors">
                      {paper.authors?.map(a => a.name).join(', ')}
                    </span>
                    <span className="paper-venue">
                      {paper.venue} ({paper.year})
                    </span>
                    {paper.citationCount !== undefined && (
                      <span className="paper-citations">
                        Zitationen: {paper.citationCount}
                      </span>
                    )}
                  </div>
                  {paper.abstract && (
                    <p className="paper-abstract">
                      {paper.abstract.length > 300 
                        ? `${paper.abstract.substring(0, 300)}...` 
                        : paper.abstract}
                    </p>
                  )}
                  <div className="paper-links">
                    {paper.url && (
                      <a 
                        href={paper.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        Zum Paper
                      </a>
                    )}
                    {paper.externalIds?.DOI && (
                      <a 
                        href={`https://doi.org/${paper.externalIds.DOI}`} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        DOI: {paper.externalIds?.DOI}
                      </a>
                    )}
                    <button
                      className="btn btn-sm btn-primary create-nft-btn"
                      onClick={() => {
                        // Funktion zur Auswahl des Papers für die NFT-Erstellung
                        if (onSelectPaper) {
                          onSelectPaper(paper);
                        } else {
                          alert(`NFT für DOI ${paper.externalIds?.DOI || 'unbekannt'} erstellen`);
                        }
                      }}
                    >
                      Als NFT erstellen
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="no-results">Keine Ergebnisse gefunden.</p>
          )}
        </div>
      )}
      
      {/* CSS für die Komponente */}
      <style jsx>{`
        .advanced-search-container {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        
        .advanced-search-form {
          background-color: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
        }
        
        .form-group {
          margin-bottom: 15px;
        }
        
        .form-row {
          display: flex;
          gap: 15px;
        }
        
        .form-control {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #ced4da;
          border-radius: 4px;
          font-size: 16px;
        }
        
        .form-actions {
          display: flex;
          gap: 10px;
          margin-top: 20px;
        }
        
        .btn {
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 16px;
        }
        
        .btn-primary {
          background-color: #007bff;
          color: white;
        }
        
        .btn-secondary {
          background-color: #6c757d;
          color: white;
        }
        
        .btn:disabled {
          opacity: 0.65;
          cursor: not-allowed;
        }
        
        .error-message {
          background-color: #f8d7da;
          color: #721c24;
          padding: 10px;
          border-radius: 4px;
          margin-bottom: 15px;
        }
        
        .search-results {
          margin-top: 30px;
        }
        
        .result-item {
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 15px;
          background-color: white;
        }
        
        .result-item h4 {
          margin-top: 0;
          color: #007bff;
        }
        
        .paper-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 15px;
          margin-bottom: 10px;
          font-size: 14px;
          color: #6c757d;
        }
        
        .paper-abstract {
          font-size: 14px;
          line-height: 1.5;
          color: #212529;
        }
        
        .paper-links {
          display: flex;
          gap: 15px;
          margin-top: 10px;
        }
        
        .paper-link {
          color: #007bff;
          text-decoration: none;
        }
        
        .paper-link:hover {
          text-decoration: underline;
        }
        
        .btn-sm {
          padding: 4px 8px;
          font-size: 14px;
        }
        
        .create-nft-btn {
          margin-left: auto;
        }
        
        .no-results {
          text-align: center;
          color: #6c757d;
          font-style: italic;
        }
      `}</style>
    </div>
  );
};

export default AdvancedSearch;import React, { useState } from 'react';
import axios from 'axios';

/**
 * Erweiterte Suchkomponente für wissenschaftliche Publikationen
 * Inspiriert von Scinapse.io für eine verbesserte Benutzererfahrung
 */
const AdvancedSearch = ({ onResultsFound, onSelectPaper }) => {
  // Suchparameter
  const [searchParams, setSearchParams] = useState({
    query: '',
    authors: '',
    year: '',
    venue: '',
    sortBy: 'relevance',
    sortOrder: 'desc'
  });

  // Suchstatus
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState(null);
  const [results, setResults] = useState(null);

  // Handler für Änderungen an den Suchparametern
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSearchParams(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Suchfunktion
  const handleSearch = async (e) => {
    e.preventDefault();
    
    // Prüfen, ob mindestens ein Suchkriterium angegeben wurde
    if (!searchParams.query && !searchParams.authors && !searchParams.year && !searchParams.venue) {
      setError('Bitte geben Sie mindestens ein Suchkriterium ein.');
      return;
    }
    
    setIsSearching(true);
    setError(null);
    
    try {
      // API-Anfrage mit den Suchparametern
      const response = await axios.get('/api/v1/semantic-scholar/advanced-search', {
        params: {
          ...searchParams,
          fields: 'title,authors,year,venue,url,externalIds,abstract,citationCount'
        }
      });
      
      setResults(response.data);
      
      // Callback für übergeordnete Komponente
      if (onResultsFound) {
        onResultsFound(response.data);
      }
    } catch (error) {
      console.error('Fehler bei der Suche:', error);
      setError(error.response?.data?.error || 'Ein Fehler ist bei der Suche aufgetreten.');
    } finally {
      setIsSearching(false);
    }
  };

  // Zurücksetzen der Suchparameter
  const handleReset = () => {
    setSearchParams({
      query: '',
      authors: '',
      year: '',
      venue: '',
      sortBy: 'relevance',
      sortOrder: 'desc'
    });
    setResults(null);
    setError(null);
  };

  return (
    <div className="advanced-search-container">
      <h2>Erweiterte Suche</h2>
      
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSearch} className="advanced-search-form">
        <div className="form-group">
          <label htmlFor="query">Suchbegriffe:</label>
          <input
            type="text"
            id="query"
            name="query"
            value={searchParams.query}
            onChange={handleInputChange}
            placeholder="z.B. quantum computing"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="authors">Autor(en):</label>
          <input
            type="text"
            id="authors"
            name="authors"
            value={searchParams.authors}
            onChange={handleInputChange}
            placeholder="z.B. John Smith"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="year">Jahr oder Jahresbereich:</label>
          <input
            type="text"
            id="year"
            name="year"
            value={searchParams.year}
            onChange={handleInputChange}
            placeholder="z.B. 2020 oder 2018-2021"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="venue">Journal/Konferenz:</label>
          <input
            type="text"
            id="venue"
            name="venue"
            value={searchParams.venue}
            onChange={handleInputChange}
            placeholder="z.B. Nature"
            className="form-control"
          />
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="sortBy">Sortieren nach:</label>
            <select
              id="sortBy"
              name="sortBy"
              value={searchParams.sortBy}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="relevance">Relevanz</option>
              <option value="year">Jahr</option>
              <option value="citationCount">Zitationen</option>
            </select>
          </div>
          
          <div className="form-group">
            <label htmlFor="sortOrder">Reihenfolge:</label>
            <select
              id="sortOrder"
              name="sortOrder"
              value={searchParams.sortOrder}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="desc">Absteigend</option>
              <option value="asc">Aufsteigend</option>
            </select>
          </div>
        </div>
        
        <div className="form-actions">
          <button 
            type="submit" 
            className="btn btn-primary" 
            disabled={isSearching}
          >
            {isSearching ? 'Suche läuft...' : 'Suchen'}
          </button>
          <button 
            type="button" 
            className="btn btn-secondary" 
            onClick={handleReset}
            disabled={isSearching}
          >
            Zurücksetzen
          </button>
        </div>
      </form>
      
      {results && (
        <div className="search-results">
          <h3>Suchergebnisse ({results.total})</h3>
          
          {results.data && results.data.length > 0 ? (
            <div className="results-list">
              {results.data.map((paper, index) => (
                <div key={paper.paperId || index} className="result-item">
                  <h4>{paper.title}</h4>
                  <div className="paper-meta">
                    <span className="paper-authors">
                      {paper.authors?.map(a => a.name).join(', ')}
                    </span>
                    <span className="paper-venue">
                      {paper.venue} ({paper.year})
                    </span>
                    {paper.citationCount !== undefined && (
                      <span className="paper-citations">
                        Zitationen: {paper.citationCount}
                      </span>
                    )}
                  </div>
                  {paper.abstract && (
                    <p className="paper-abstract">
                      {paper.abstract.length > 300 
                        ? `${paper.abstract.substring(0, 300)}...` 
                        : paper.abstract}
                    </p>
                  )}
                  <div className="paper-links">
                    {paper.url && (
                      <a 
                        href={paper.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        Zum Paper
                      </a>
                    )}
                    {paper.externalIds?.DOI && (
                      <a 
                        href={`https://doi.org/${paper.externalIds.DOI}`} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        DOI: {paper.externalIds?.DOI}
                      </a>
                    )}
                    <button
                      className="btn btn-sm btn-primary create-nft-btn"
                      onClick={() => {
                        // Funktion zur Auswahl des Papers für die NFT-Erstellung
                        if (onSelectPaper) {
                          onSelectPaper(paper);
                        } else {
                          alert(`NFT für DOI ${paper.externalIds?.DOI || 'unbekannt'} erstellen`);
                        }
                      }}
                    >
                      Als NFT erstellen
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="no-results">Keine Ergebnisse gefunden.</p>
          )}
        </div>
      )}
      
      {/* CSS für die Komponente */}
      <style jsx>{`
        .advanced-search-container {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        
        .advanced-search-form {
          background-color: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
        }
        
        .form-group {
          margin-bottom: 15px;
        }
        
        .form-row {
          display: flex;
          gap: 15px;
        }
        
        .form-control {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #ced4da;
          border-radius: 4px;
          font-size: 16px;
        }
        
        .form-actions {
          display: flex;
          gap: 10px;
          margin-top: 20px;
        }
        
        .btn {
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 16px;
        }
        
        .btn-primary {
          background-color: #007bff;
          color: white;
        }
        
        .btn-secondary {
          background-color: #6c757d;
          color: white;
        }
        
        .btn:disabled {
          opacity: 0.65;
          cursor: not-allowed;
        }
        
        .error-message {
          background-color: #f8d7da;
          color: #721c24;
          padding: 10px;
          border-radius: 4px;
          margin-bottom: 15px;
        }
        
        .search-results {
          margin-top: 30px;
        }
        
        .result-item {
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 15px;
          background-color: white;
        }
        
        .result-item h4 {
          margin-top: 0;
          color: #007bff;
        }
        
        .paper-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 15px;
          margin-bottom: 10px;
          font-size: 14px;
          color: #6c757d;
        }
        
        .paper-abstract {
          font-size: 14px;
          line-height: 1.5;
          color: #212529;
        }
        
        .paper-links {
          display: flex;
          gap: 15px;
          margin-top: 10px;
        }
        
        .paper-link {
          color: #007bff;
          text-decoration: none;
        }
        
        .paper-link:hover {
          text-decoration: underline;
        }
        
        .btn-sm {
          padding: 4px 8px;
          font-size: 14px;
        }
        
        .create-nft-btn {
          margin-left: auto;
        }
        
        .no-results {
          text-align: center;
          color: #6c757d;
          font-style: italic;
        }
      `}</style>
    </div>
  );
};

export default AdvancedSearch;import React, { useState } from 'react';
import axios from 'axios';

/**
 * Erweiterte Suchkomponente für wissenschaftliche Publikationen
 * Inspiriert von Scinapse.io für eine verbesserte Benutzererfahrung
 */
const AdvancedSearch = ({ onResultsFound, onSelectPaper }) => {
  // Suchparameter
  const [searchParams, setSearchParams] = useState({
    query: '',
    authors: '',
    year: '',
    venue: '',
    sortBy: 'relevance',
    sortOrder: 'desc'
  });

  // Suchstatus
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState(null);
  const [results, setResults] = useState(null);

  // Handler für Änderungen an den Suchparametern
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSearchParams(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Suchfunktion
  const handleSearch = async (e) => {
    e.preventDefault();
    
    // Prüfen, ob mindestens ein Suchkriterium angegeben wurde
    if (!searchParams.query && !searchParams.authors && !searchParams.year && !searchParams.venue) {
      setError('Bitte geben Sie mindestens ein Suchkriterium ein.');
      return;
    }
    
    setIsSearching(true);
    setError(null);
    
    try {
      // API-Anfrage mit den Suchparametern
      const response = await axios.get('/api/v1/semantic-scholar/advanced-search', {
        params: {
          ...searchParams,
          fields: 'title,authors,year,venue,url,externalIds,abstract,citationCount'
        }
      });
      
      setResults(response.data);
      
      // Callback für übergeordnete Komponente
      if (onResultsFound) {
        onResultsFound(response.data);
      }
    } catch (error) {
      console.error('Fehler bei der Suche:', error);
      setError(error.response?.data?.error || 'Ein Fehler ist bei der Suche aufgetreten.');
    } finally {
      setIsSearching(false);
    }
  };

  // Zurücksetzen der Suchparameter
  const handleReset = () => {
    setSearchParams({
      query: '',
      authors: '',
      year: '',
      venue: '',
      sortBy: 'relevance',
      sortOrder: 'desc'
    });
    setResults(null);
    setError(null);
  };

  return (
    <div className="advanced-search-container">
      <h2>Erweiterte Suche</h2>
      
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSearch} className="advanced-search-form">
        <div className="form-group">
          <label htmlFor="query">Suchbegriffe:</label>
          <input
            type="text"
            id="query"
            name="query"
            value={searchParams.query}
            onChange={handleInputChange}
            placeholder="z.B. quantum computing"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="authors">Autor(en):</label>
          <input
            type="text"
            id="authors"
            name="authors"
            value={searchParams.authors}
            onChange={handleInputChange}
            placeholder="z.B. John Smith"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="year">Jahr oder Jahresbereich:</label>
          <input
            type="text"
            id="year"
            name="year"
            value={searchParams.year}
            onChange={handleInputChange}
            placeholder="z.B. 2020 oder 2018-2021"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="venue">Journal/Konferenz:</label>
          <input
            type="text"
            id="venue"
            name="venue"
            value={searchParams.venue}
            onChange={handleInputChange}
            placeholder="z.B. Nature"
            className="form-control"
          />
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="sortBy">Sortieren nach:</label>
            <select
              id="sortBy"
              name="sortBy"
              value={searchParams.sortBy}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="relevance">Relevanz</option>
              <option value="year">Jahr</option>
              <option value="citationCount">Zitationen</option>
            </select>
          </div>
          
          <div className="form-group">
            <label htmlFor="sortOrder">Reihenfolge:</label>
            <select
              id="sortOrder"
              name="sortOrder"
              value={searchParams.sortOrder}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="desc">Absteigend</option>
              <option value="asc">Aufsteigend</option>
            </select>
          </div>
        </div>
        
        <div className="form-actions">
          <button 
            type="submit" 
            className="btn btn-primary" 
            disabled={isSearching}
          >
            {isSearching ? 'Suche läuft...' : 'Suchen'}
          </button>
          <button 
            type="button" 
            className="btn btn-secondary" 
            onClick={handleReset}
            disabled={isSearching}
          >
            Zurücksetzen
          </button>
        </div>
      </form>
      
      {results && (
        <div className="search-results">
          <h3>Suchergebnisse ({results.total})</h3>
          
          {results.data && results.data.length > 0 ? (
            <div className="results-list">
              {results.data.map((paper, index) => (
                <div key={paper.paperId || index} className="result-item">
                  <h4>{paper.title}</h4>
                  <div className="paper-meta">
                    <span className="paper-authors">
                      {paper.authors?.map(a => a.name).join(', ')}
                    </span>
                    <span className="paper-venue">
                      {paper.venue} ({paper.year})
                    </span>
                    {paper.citationCount !== undefined && (
                      <span className="paper-citations">
                        Zitationen: {paper.citationCount}
                      </span>
                    )}
                  </div>
                  {paper.abstract && (
                    <p className="paper-abstract">
                      {paper.abstract.length > 300 
                        ? `${paper.abstract.substring(0, 300)}...` 
                        : paper.abstract}
                    </p>
                  )}
                  <div className="paper-links">
                    {paper.url && (
                      <a 
                        href={paper.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        Zum Paper
                      </a>
                    )}
                    {paper.externalIds?.DOI && (
                      <a 
                        href={`https://doi.org/${paper.externalIds.DOI}`} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        DOI: {paper.externalIds?.DOI}
                      </a>
                    )}
                    <button
                      className="btn btn-sm btn-primary create-nft-btn"
                      onClick={() => {
                        // Funktion zur Auswahl des Papers für die NFT-Erstellung
                        if (onSelectPaper) {
                          onSelectPaper(paper);
                        } else {
                          alert(`NFT für DOI ${paper.externalIds?.DOI || 'unbekannt'} erstellen`);
                        }
                      }}
                    >
                      Als NFT erstellen
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="no-results">Keine Ergebnisse gefunden.</p>
          )}
        </div>
      )}
      
      {/* CSS für die Komponente */}
      <style jsx>{`
        .advanced-search-container {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        
        .advanced-search-form {
          background-color: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
        }
        
        .form-group {
          margin-bottom: 15px;
        }
        
        .form-row {
          display: flex;
          gap: 15px;
        }
        
        .form-control {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #ced4da;
          border-radius: 4px;
          font-size: 16px;
        }
        
        .form-actions {
          display: flex;
          gap: 10px;
          margin-top: 20px;
        }
        
        .btn {
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 16px;
        }
        
        .btn-primary {
          background-color: #007bff;
          color: white;
        }
        
        .btn-secondary {
          background-color: #6c757d;
          color: white;
        }
        
        .btn:disabled {
          opacity: 0.65;
          cursor: not-allowed;
        }
        
        .error-message {
          background-color: #f8d7da;
          color: #721c24;
          padding: 10px;
          border-radius: 4px;
          margin-bottom: 15px;
        }
        
        .search-results {
          margin-top: 30px;
        }
        
        .result-item {
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 15px;
          background-color: white;
        }
        
        .result-item h4 {
          margin-top: 0;
          color: #007bff;
        }
        
        .paper-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 15px;
          margin-bottom: 10px;
          font-size: 14px;
          color: #6c757d;
        }
        
        .paper-abstract {
          font-size: 14px;
          line-height: 1.5;
          color: #212529;
        }
        
        .paper-links {
          display: flex;
          gap: 15px;
          margin-top: 10px;
        }
        
        .paper-link {
          color: #007bff;
          text-decoration: none;
        }
        
        .paper-link:hover {
          text-decoration: underline;
        }
        
        .btn-sm {
          padding: 4px 8px;
          font-size: 14px;
        }
        
        .create-nft-btn {
          margin-left: auto;
        }
        
        .no-results {
          text-align: center;
          color: #6c757d;
          font-style: italic;
        }
      `}</style>
    </div>
  );
};

export default AdvancedSearch;import React, { useState } from 'react';
import axios from 'axios';

/**
 * Erweiterte Suchkomponente für wissenschaftliche Publikationen
 * Inspiriert von Scinapse.io für eine verbesserte Benutzererfahrung
 */
const AdvancedSearch = ({ onResultsFound, onSelectPaper }) => {
  // Suchparameter
  const [searchParams, setSearchParams] = useState({
    query: '',
    authors: '',
    year: '',
    venue: '',
    sortBy: 'relevance',
    sortOrder: 'desc'
  });

  // Suchstatus
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState(null);
  const [results, setResults] = useState(null);

  // Handler für Änderungen an den Suchparametern
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSearchParams(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Suchfunktion
  const handleSearch = async (e) => {
    e.preventDefault();
    
    // Prüfen, ob mindestens ein Suchkriterium angegeben wurde
    if (!searchParams.query && !searchParams.authors && !searchParams.year && !searchParams.venue) {
      setError('Bitte geben Sie mindestens ein Suchkriterium ein.');
      return;
    }
    
    setIsSearching(true);
    setError(null);
    
    try {
      // API-Anfrage mit den Suchparametern
      const response = await axios.get('/api/v1/semantic-scholar/advanced-search', {
        params: {
          ...searchParams,
          fields: 'title,authors,year,venue,url,externalIds,abstract,citationCount'
        }
      });
      
      setResults(response.data);
      
      // Callback für übergeordnete Komponente
      if (onResultsFound) {
        onResultsFound(response.data);
      }
    } catch (error) {
      console.error('Fehler bei der Suche:', error);
      setError(error.response?.data?.error || 'Ein Fehler ist bei der Suche aufgetreten.');
    } finally {
      setIsSearching(false);
    }
  };

  // Zurücksetzen der Suchparameter
  const handleReset = () => {
    setSearchParams({
      query: '',
      authors: '',
      year: '',
      venue: '',
      sortBy: 'relevance',
      sortOrder: 'desc'
    });
    setResults(null);
    setError(null);
  };

  return (
    <div className="advanced-search-container">
      <h2>Erweiterte Suche</h2>
      
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSearch} className="advanced-search-form">
        <div className="form-group">
          <label htmlFor="query">Suchbegriffe:</label>
          <input
            type="text"
            id="query"
            name="query"
            value={searchParams.query}
            onChange={handleInputChange}
            placeholder="z.B. quantum computing"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="authors">Autor(en):</label>
          <input
            type="text"
            id="authors"
            name="authors"
            value={searchParams.authors}
            onChange={handleInputChange}
            placeholder="z.B. John Smith"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="year">Jahr oder Jahresbereich:</label>
          <input
            type="text"
            id="year"
            name="year"
            value={searchParams.year}
            onChange={handleInputChange}
            placeholder="z.B. 2020 oder 2018-2021"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="venue">Journal/Konferenz:</label>
          <input
            type="text"
            id="venue"
            name="venue"
            value={searchParams.venue}
            onChange={handleInputChange}
            placeholder="z.B. Nature"
            className="form-control"
          />
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="sortBy">Sortieren nach:</label>
            <select
              id="sortBy"
              name="sortBy"
              value={searchParams.sortBy}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="relevance">Relevanz</option>
              <option value="year">Jahr</option>
              <option value="citationCount">Zitationen</option>
            </select>
          </div>
          
          <div className="form-group">
            <label htmlFor="sortOrder">Reihenfolge:</label>
            <select
              id="sortOrder"
              name="sortOrder"
              value={searchParams.sortOrder}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="desc">Absteigend</option>
              <option value="asc">Aufsteigend</option>
            </select>
          </div>
        </div>
        
        <div className="form-actions">
          <button 
            type="submit" 
            className="btn btn-primary" 
            disabled={isSearching}
          >
            {isSearching ? 'Suche läuft...' : 'Suchen'}
          </button>
          <button 
            type="button" 
            className="btn btn-secondary" 
            onClick={handleReset}
            disabled={isSearching}
          >
            Zurücksetzen
          </button>
        </div>
      </form>
      
      {results && (
        <div className="search-results">
          <h3>Suchergebnisse ({results.total})</h3>
          
          {results.data && results.data.length > 0 ? (
            <div className="results-list">
              {results.data.map((paper, index) => (
                <div key={paper.paperId || index} className="result-item">
                  <h4>{paper.title}</h4>
                  <div className="paper-meta">
                    <span className="paper-authors">
                      {paper.authors?.map(a => a.name).join(', ')}
                    </span>
                    <span className="paper-venue">
                      {paper.venue} ({paper.year})
                    </span>
                    {paper.citationCount !== undefined && (
                      <span className="paper-citations">
                        Zitationen: {paper.citationCount}
                      </span>
                    )}
                  </div>
                  {paper.abstract && (
                    <p className="paper-abstract">
                      {paper.abstract.length > 300 
                        ? `${paper.abstract.substring(0, 300)}...` 
                        : paper.abstract}
                    </p>
                  )}
                  <div className="paper-links">
                    {paper.url && (
                      <a 
                        href={paper.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        Zum Paper
                      </a>
                    )}
                    {paper.externalIds?.DOI && (
                      <a 
                        href={`https://doi.org/${paper.externalIds.DOI}`} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        DOI: {paper.externalIds?.DOI}
                      </a>
                    )}
                    <button
                      className="btn btn-sm btn-primary create-nft-btn"
                      onClick={() => {
                        // Funktion zur Auswahl des Papers für die NFT-Erstellung
                        if (onSelectPaper) {
                          onSelectPaper(paper);
                        } else {
                          alert(`NFT für DOI ${paper.externalIds?.DOI || 'unbekannt'} erstellen`);
                        }
                      }}
                    >
                      Als NFT erstellen
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="no-results">Keine Ergebnisse gefunden.</p>
          )}
        </div>
      )}
      
      {/* CSS für die Komponente */}
      <style jsx>{`
        .advanced-search-container {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        
        .advanced-search-form {
          background-color: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
        }
        
        .form-group {
          margin-bottom: 15px;
        }
        
        .form-row {
          display: flex;
          gap: 15px;
        }
        
        .form-control {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #ced4da;
          border-radius: 4px;
          font-size: 16px;
        }
        
        .form-actions {
          display: flex;
          gap: 10px;
          margin-top: 20px;
        }
        
        .btn {
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 16px;
        }
        
        .btn-primary {
          background-color: #007bff;
          color: white;
        }
        
        .btn-secondary {
          background-color: #6c757d;
          color: white;
        }
        
        .btn:disabled {
          opacity: 0.65;
          cursor: not-allowed;
        }
        
        .error-message {
          background-color: #f8d7da;
          color: #721c24;
          padding: 10px;
          border-radius: 4px;
          margin-bottom: 15px;
        }
        
        .search-results {
          margin-top: 30px;
        }
        
        .result-item {
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 15px;
          background-color: white;
        }
        
        .result-item h4 {
          margin-top: 0;
          color: #007bff;
        }
        
        .paper-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 15px;
          margin-bottom: 10px;
          font-size: 14px;
          color: #6c757d;
        }
        
        .paper-abstract {
          font-size: 14px;
          line-height: 1.5;
          color: #212529;
        }
        
        .paper-links {
          display: flex;
          gap: 15px;
          margin-top: 10px;
        }
        
        .paper-link {
          color: #007bff;
          text-decoration: none;
        }
        
        .paper-link:hover {
          text-decoration: underline;
        }
        
        .btn-sm {
          padding: 4px 8px;
          font-size: 14px;
        }
        
        .create-nft-btn {
          margin-left: auto;
        }
        
        .no-results {
          text-align: center;
          color: #6c757d;
          font-style: italic;
        }
      `}</style>
    </div>
  );
};

export default AdvancedSearch;import React, { useState } from 'react';
import axios from 'axios';

/**
 * Erweiterte Suchkomponente für wissenschaftliche Publikationen
 * Inspiriert von Scinapse.io für eine verbesserte Benutzererfahrung
 */
const AdvancedSearch = ({ onResultsFound, onSelectPaper }) => {
  // Suchparameter
  const [searchParams, setSearchParams] = useState({
    query: '',
    authors: '',
    year: '',
    venue: '',
    sortBy: 'relevance',
    sortOrder: 'desc'
  });

  // Suchstatus
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState(null);
  const [results, setResults] = useState(null);

  // Handler für Änderungen an den Suchparametern
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSearchParams(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Suchfunktion
  const handleSearch = async (e) => {
    e.preventDefault();
    
    // Prüfen, ob mindestens ein Suchkriterium angegeben wurde
    if (!searchParams.query && !searchParams.authors && !searchParams.year && !searchParams.venue) {
      setError('Bitte geben Sie mindestens ein Suchkriterium ein.');
      return;
    }
    
    setIsSearching(true);
    setError(null);
    
    try {
      // API-Anfrage mit den Suchparametern
      const response = await axios.get('/api/v1/semantic-scholar/advanced-search', {
        params: {
          ...searchParams,
          fields: 'title,authors,year,venue,url,externalIds,abstract,citationCount'
        }
      });
      
      setResults(response.data);
      
      // Callback für übergeordnete Komponente
      if (onResultsFound) {
        onResultsFound(response.data);
      }
    } catch (error) {
      console.error('Fehler bei der Suche:', error);
      setError(error.response?.data?.error || 'Ein Fehler ist bei der Suche aufgetreten.');
    } finally {
      setIsSearching(false);
    }
  };

  // Zurücksetzen der Suchparameter
  const handleReset = () => {
    setSearchParams({
      query: '',
      authors: '',
      year: '',
      venue: '',
      sortBy: 'relevance',
      sortOrder: 'desc'
    });
    setResults(null);
    setError(null);
  };

  return (
    <div className="advanced-search-container">
      <h2>Erweiterte Suche</h2>
      
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSearch} className="advanced-search-form">
        <div className="form-group">
          <label htmlFor="query">Suchbegriffe:</label>
          <input
            type="text"
            id="query"
            name="query"
            value={searchParams.query}
            onChange={handleInputChange}
            placeholder="z.B. quantum computing"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="authors">Autor(en):</label>
          <input
            type="text"
            id="authors"
            name="authors"
            value={searchParams.authors}
            onChange={handleInputChange}
            placeholder="z.B. John Smith"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="year">Jahr oder Jahresbereich:</label>
          <input
            type="text"
            id="year"
            name="year"
            value={searchParams.year}
            onChange={handleInputChange}
            placeholder="z.B. 2020 oder 2018-2021"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="venue">Journal/Konferenz:</label>
          <input
            type="text"
            id="venue"
            name="venue"
            value={searchParams.venue}
            onChange={handleInputChange}
            placeholder="z.B. Nature"
            className="form-control"
          />
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="sortBy">Sortieren nach:</label>
            <select
              id="sortBy"
              name="sortBy"
              value={searchParams.sortBy}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="relevance">Relevanz</option>
              <option value="year">Jahr</option>
              <option value="citationCount">Zitationen</option>
            </select>
          </div>
          
          <div className="form-group">
            <label htmlFor="sortOrder">Reihenfolge:</label>
            <select
              id="sortOrder"
              name="sortOrder"
              value={searchParams.sortOrder}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="desc">Absteigend</option>
              <option value="asc">Aufsteigend</option>
            </select>
          </div>
        </div>
        
        <div className="form-actions">
          <button 
            type="submit" 
            className="btn btn-primary" 
            disabled={isSearching}
          >
            {isSearching ? 'Suche läuft...' : 'Suchen'}
          </button>
          <button 
            type="button" 
            className="btn btn-secondary" 
            onClick={handleReset}
            disabled={isSearching}
          >
            Zurücksetzen
          </button>
        </div>
      </form>
      
      {results && (
        <div className="search-results">
          <h3>Suchergebnisse ({results.total})</h3>
          
          {results.data && results.data.length > 0 ? (
            <div className="results-list">
              {results.data.map((paper, index) => (
                <div key={paper.paperId || index} className="result-item">
                  <h4>{paper.title}</h4>
                  <div className="paper-meta">
                    <span className="paper-authors">
                      {paper.authors?.map(a => a.name).join(', ')}
                    </span>
                    <span className="paper-venue">
                      {paper.venue} ({paper.year})
                    </span>
                    {paper.citationCount !== undefined && (
                      <span className="paper-citations">
                        Zitationen: {paper.citationCount}
                      </span>
                    )}
                  </div>
                  {paper.abstract && (
                    <p className="paper-abstract">
                      {paper.abstract.length > 300 
                        ? `${paper.abstract.substring(0, 300)}...` 
                        : paper.abstract}
                    </p>
                  )}
                  <div className="paper-links">
                    {paper.url && (
                      <a 
                        href={paper.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        Zum Paper
                      </a>
                    )}
                    {paper.externalIds?.DOI && (
                      <a 
                        href={`https://doi.org/${paper.externalIds.DOI}`} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        DOI: {paper.externalIds?.DOI}
                      </a>
                    )}
                    <button
                      className="btn btn-sm btn-primary create-nft-btn"
                      onClick={() => {
                        // Funktion zur Auswahl des Papers für die NFT-Erstellung
                        if (onSelectPaper) {
                          onSelectPaper(paper);
                        } else {
                          alert(`NFT für DOI ${paper.externalIds?.DOI || 'unbekannt'} erstellen`);
                        }
                      }}
                    >
                      Als NFT erstellen
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="no-results">Keine Ergebnisse gefunden.</p>
          )}
        </div>
      )}
      
      {/* CSS für die Komponente */}
      <style jsx>{`
        .advanced-search-container {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        
        .advanced-search-form {
          background-color: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
        }
        
        .form-group {
          margin-bottom: 15px;
        }
        
        .form-row {
          display: flex;
          gap: 15px;
        }
        
        .form-control {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #ced4da;
          border-radius: 4px;
          font-size: 16px;
        }
        
        .form-actions {
          display: flex;
          gap: 10px;
          margin-top: 20px;
        }
        
        .btn {
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 16px;
        }
        
        .btn-primary {
          background-color: #007bff;
          color: white;
        }
        
        .btn-secondary {
          background-color: #6c757d;
          color: white;
        }
        
        .btn:disabled {
          opacity: 0.65;
          cursor: not-allowed;
        }
        
        .error-message {
          background-color: #f8d7da;
          color: #721c24;
          padding: 10px;
          border-radius: 4px;
          margin-bottom: 15px;
        }
        
        .search-results {
          margin-top: 30px;
        }
        
        .result-item {
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 15px;
          background-color: white;
        }
        
        .result-item h4 {
          margin-top: 0;
          color: #007bff;
        }
        
        .paper-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 15px;
          margin-bottom: 10px;
          font-size: 14px;
          color: #6c757d;
        }
        
        .paper-abstract {
          font-size: 14px;
          line-height: 1.5;
          color: #212529;
        }
        
        .paper-links {
          display: flex;
          gap: 15px;
          margin-top: 10px;
        }
        
        .paper-link {
          color: #007bff;
          text-decoration: none;
        }
        
        .paper-link:hover {
          text-decoration: underline;
        }
        
        .btn-sm {
          padding: 4px 8px;
          font-size: 14px;
        }
        
        .create-nft-btn {
          margin-left: auto;
        }
        
        .no-results {
          text-align: center;
          color: #6c757d;
          font-style: italic;
        }
      `}</style>
    </div>
  );
};

export default AdvancedSearch;import React, { useState } from 'react';
import axios from 'axios';

/**
 * Erweiterte Suchkomponente für wissenschaftliche Publikationen
 * Inspiriert von Scinapse.io für eine verbesserte Benutzererfahrung
 */
const AdvancedSearch = ({ onResultsFound, onSelectPaper }) => {
  // Suchparameter
  const [searchParams, setSearchParams] = useState({
    query: '',
    authors: '',
    year: '',
    venue: '',
    sortBy: 'relevance',
    sortOrder: 'desc'
  });

  // Suchstatus
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState(null);
  const [results, setResults] = useState(null);

  // Handler für Änderungen an den Suchparametern
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSearchParams(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Suchfunktion
  const handleSearch = async (e) => {
    e.preventDefault();
    
    // Prüfen, ob mindestens ein Suchkriterium angegeben wurde
    if (!searchParams.query && !searchParams.authors && !searchParams.year && !searchParams.venue) {
      setError('Bitte geben Sie mindestens ein Suchkriterium ein.');
      return;
    }
    
    setIsSearching(true);
    setError(null);
    
    try {
      // API-Anfrage mit den Suchparametern
      const response = await axios.get('/api/v1/semantic-scholar/advanced-search', {
        params: {
          ...searchParams,
          fields: 'title,authors,year,venue,url,externalIds,abstract,citationCount'
        }
      });
      
      setResults(response.data);
      
      // Callback für übergeordnete Komponente
      if (onResultsFound) {
        onResultsFound(response.data);
      }
    } catch (error) {
      console.error('Fehler bei der Suche:', error);
      setError(error.response?.data?.error || 'Ein Fehler ist bei der Suche aufgetreten.');
    } finally {
      setIsSearching(false);
    }
  };

  // Zurücksetzen der Suchparameter
  const handleReset = () => {
    setSearchParams({
      query: '',
      authors: '',
      year: '',
      venue: '',
      sortBy: 'relevance',
      sortOrder: 'desc'
    });
    setResults(null);
    setError(null);
  };

  return (
    <div className="advanced-search-container">
      <h2>Erweiterte Suche</h2>
      
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSearch} className="advanced-search-form">
        <div className="form-group">
          <label htmlFor="query">Suchbegriffe:</label>
          <input
            type="text"
            id="query"
            name="query"
            value={searchParams.query}
            onChange={handleInputChange}
            placeholder="z.B. quantum computing"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="authors">Autor(en):</label>
          <input
            type="text"
            id="authors"
            name="authors"
            value={searchParams.authors}
            onChange={handleInputChange}
            placeholder="z.B. John Smith"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="year">Jahr oder Jahresbereich:</label>
          <input
            type="text"
            id="year"
            name="year"
            value={searchParams.year}
            onChange={handleInputChange}
            placeholder="z.B. 2020 oder 2018-2021"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="venue">Journal/Konferenz:</label>
          <input
            type="text"
            id="venue"
            name="venue"
            value={searchParams.venue}
            onChange={handleInputChange}
            placeholder="z.B. Nature"
            className="form-control"
          />
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="sortBy">Sortieren nach:</label>
            <select
              id="sortBy"
              name="sortBy"
              value={searchParams.sortBy}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="relevance">Relevanz</option>
              <option value="year">Jahr</option>
              <option value="citationCount">Zitationen</option>
            </select>
          </div>
          
          <div className="form-group">
            <label htmlFor="sortOrder">Reihenfolge:</label>
            <select
              id="sortOrder"
              name="sortOrder"
              value={searchParams.sortOrder}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="desc">Absteigend</option>
              <option value="asc">Aufsteigend</option>
            </select>
          </div>
        </div>
        
        <div className="form-actions">
          <button 
            type="submit" 
            className="btn btn-primary" 
            disabled={isSearching}
          >
            {isSearching ? 'Suche läuft...' : 'Suchen'}
          </button>
          <button 
            type="button" 
            className="btn btn-secondary" 
            onClick={handleReset}
            disabled={isSearching}
          >
            Zurücksetzen
          </button>
        </div>
      </form>
      
      {results && (
        <div className="search-results">
          <h3>Suchergebnisse ({results.total})</h3>
          
          {results.data && results.data.length > 0 ? (
            <div className="results-list">
              {results.data.map((paper, index) => (
                <div key={paper.paperId || index} className="result-item">
                  <h4>{paper.title}</h4>
                  <div className="paper-meta">
                    <span className="paper-authors">
                      {paper.authors?.map(a => a.name).join(', ')}
                    </span>
                    <span className="paper-venue">
                      {paper.venue} ({paper.year})
                    </span>
                    {paper.citationCount !== undefined && (
                      <span className="paper-citations">
                        Zitationen: {paper.citationCount}
                      </span>
                    )}
                  </div>
                  {paper.abstract && (
                    <p className="paper-abstract">
                      {paper.abstract.length > 300 
                        ? `${paper.abstract.substring(0, 300)}...` 
                        : paper.abstract}
                    </p>
                  )}
                  <div className="paper-links">
                    {paper.url && (
                      <a 
                        href={paper.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        Zum Paper
                      </a>
                    )}
                    {paper.externalIds?.DOI && (
                      <a 
                        href={`https://doi.org/${paper.externalIds.DOI}`} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="paper-link"
                      >
                        DOI: {paper.externalIds?.DOI}
                      </a>
                    )}
                    <button
                      className="btn btn-sm btn-primary create-nft-btn"
                      onClick={() => {
                        // Funktion zur Auswahl des Papers für die NFT-Erstellung
                        if (onSelectPaper) {
                          onSelectPaper(paper);
                        } else {
                          alert(`NFT für DOI ${paper.externalIds?.DOI || 'unbekannt'} erstellen`);
                        }
                      }}
                    >
                      Als NFT erstellen
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="no-results">Keine Ergebnisse gefunden.</p>
          )}
        </div>
      )}
      
      {/* CSS für die Komponente */}
      <style jsx>{`
        .advanced-search-container {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        
        .advanced-search-form {
          background-color: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
        }
        
        .form-group {
          margin-bottom: 15px;
        }
        
        .form-row {
          display: flex;
          gap: 15px;
        }
        
        .form-control {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #ced4da;
          border-radius: 4px;
          font-size: 16px;
        }
        
        .form-actions {
          display: flex;
          gap: 10px;
          margin-top: 20px;
        }
        
        .btn {
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 16px;
        }
        
        .btn-primary {
          background-color: #007bff;
          color: white;
        }
        
        .btn-secondary {
          background-color: #6c757d;
          color: white;
        }
        
        .btn:disabled {
          opacity: 0.65;
          cursor: not-allowed;
        }
        
        .error-message {
          background-color: #f8d7da;
          color: #721c24;
          padding: 10px;
          border-radius: 4px;
          margin-bottom: 15px;
        }
        
        .search-results {
          margin-top: 30px;
        }
        
        .result-item {
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 15px;
          background-color: white;
        }
        
        .result-item h4 {
          margin-top: 0;
          color: #007bff;
        }
        
        .paper-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 15px;
          margin-bottom: 10px;
          font-size: 14px;
          color: #6c757d;
        }
        
        .paper-abstract {
          font-size: 14px;
          line-height: 1.5;
          color: #212529;
        }
        
        .paper-links {
          display: flex;
          gap: 15px;
          margin-top: 10px;
        }
        
        .paper-link {
          color: #007bff;
          text-decoration: none;
        }
        
        .paper-link:hover {
          text-decoration: underline;
        }
        
        .btn-sm {
          padding: 4px 8px;
          font-size: 14px;
        }
        
        .create-nft-btn {
          margin-left: auto;
        }
        
        .no-results {
          text-align: center;
          color: #6c757d;
          font-style: italic;
        }
      `}</style>
    </div>
  );
};

export default AdvancedSearch;