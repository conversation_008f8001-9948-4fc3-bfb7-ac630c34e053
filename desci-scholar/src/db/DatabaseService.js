/**
 * DatabaseService - Datenbankanbindung für DeSci-Scholar
 * Implementiert eine MongoDB-Integration für die Speicherung von Metadaten
 * und die Indizierung von Publikationen, während die eigentlichen Daten 
 * in dezentraler Speicherung (IPFS/BitTorrent) verbleiben.
 */

import { MongoClient, ObjectId } from 'mongodb';

class DatabaseService {
  /**
   * Erstellt eine neue Instanz des DatabaseService
   * @param {Object} config Konfigurationsobjekt
   * @param {string} config.uri MongoDB Verbindungs-URI
   * @param {string} config.dbName Name der Datenbank
   * @param {number} config.connectionTimeout Verbindungs-Timeout in Millisekunden
   */
  constructor(config = {}) {
    this.uri = config.uri || process.env.MONGO_URI || 'mongodb://localhost:27017';
    this.dbName = config.dbName || process.env.MONGO_DB_NAME || 'desci-scholar';
    this.connectionTimeout = config.connectionTimeout || 30000;
    
    this.client = null;
    this.db = null;
    this.collections = {};
    this._connected = false;
  }
  
  /**
   * Verbindet mit der Datenbank (Alias für initialize)
   * @returns {Promise<boolean>} Erfolg der Verbindung
   */
  async connect() {
    return this.initialize();
  }

  /**
   * Prüft, ob eine Verbindung zur Datenbank besteht
   * @returns {Promise<boolean>} Verbindungsstatus
   */
  async isConnected() {
    return this._connected && this.client && this.client.topology && this.client.topology.isConnected();
  }

  /**
   * Schließt die Datenbankverbindung
   * @returns {Promise<boolean>} Erfolg des Schließens
   */
  async close() {
    try {
      if (this.client) {
        await this.client.close();
        this._connected = false;
        console.log('Datenbankverbindung geschlossen');
      }
      return true;
    } catch (error) {
      console.error('Fehler beim Schließen der Datenbankverbindung:', error);
      return false;
    }
  }

  /**
   * Gibt eine Collection zurück
   * @param {string} name Name der Collection
   * @returns {Object} MongoDB Collection
   */
  getCollection(name) {
    if (!this.db) {
      throw new Error('Datenbank nicht initialisiert');
    }
    return this.db.collection(name);
  }

  /**
   * Initialisiert die Datenbankverbindung
   * @returns {Promise<boolean>} Erfolg der Initialisierung
   */
  async initialize() {
    try {
      if (this._connected) {
        console.log('Datenbankverbindung bereits hergestellt');
        return true;
      }

      console.log(`Verbinde mit MongoDB: ${this.uri}`);
      this.client = new MongoClient(this.uri, {
        connectTimeoutMS: this.connectionTimeout
      });

      await this.client.connect();
      this.db = this.client.db(this.dbName);

      // Initialisiere Sammlungen
      this.collections = {
        publications: this.db.collection('publications'),
        users: this.db.collection('users'),
        citations: this.db.collection('citations'),
        stats: this.db.collection('stats')
      };

      // Initialisiere Indizes für effiziente Suche
      await this._setupIndizes();

      this._connected = true;
      console.log(`Mit MongoDB verbunden: ${this.dbName}`);
      return true;
    } catch (error) {
      console.error('Fehler beim Verbinden mit der Datenbank:', error);
      return false;
    }
  }
  
  /**
   * Stellt sicher, dass eine Verbindung zur Datenbank besteht
   * @private
   */
  async _ensureConnected() {
    if (!this._connected) {
      const success = await this.initialize();
      if (!success) {
        throw new Error('Datenbankverbindung konnte nicht hergestellt werden');
      }
    }
  }
  
  /**
   * Richtet Datenbankindizes ein
   * @private
   */
  async _setupIndizes() {
    // Publikationsindizes
    await this.collections.publications.createIndexes([
      { key: { 'title': 'text', 'abstract': 'text', 'keywords': 'text' } },
      { key: { 'authors': 1 } },
      { key: { 'status': 1 } },
      { key: { 'doi': 1 }, unique: true, sparse: true },
      { key: { 'storageIds.ipfs': 1 }, sparse: true },
      { key: { 'storageIds.bittorrent': 1 }, sparse: true },
      { key: { 'createdAt': 1 } },
      { key: { 'updatedAt': 1 } }
    ]);
    
    // Benutzerindizes
    await this.collections.users.createIndexes([
      { key: { 'email': 1 }, unique: true },
      { key: { 'username': 1 }, unique: true }
    ]);
    
    // Zitationsindizes
    await this.collections.citations.createIndexes([
      { key: { 'sourceId': 1 } },
      { key: { 'targetId': 1 } }
    ]);
  }
  
  /**
   * Speichert Publikationsmetadaten in der Datenbank
   * @param {Object} publication Publikationsobjekt
   * @returns {Promise<Object>} Ergebnisobjekt mit ID
   */
  async savePublication(publication) {
    await this._ensureConnected();
    
    try {
      const now = new Date();
      const doc = {
        ...publication,
        _id: publication._id || new ObjectId(),
        createdAt: publication.createdAt || now,
        updatedAt: now
      };
      
      // Bereinige große Inhalte, die in dezentraler Speicherung gehalten werden
      if (doc.content && doc.storageIds) {
        delete doc.content;
      }
      
      // Format für Speicherung in MongoDB anpassen
      if (doc.storageIds) {
        doc.storageIds = {
          ipfs: publication.storage?.ipfs || publication.storageIds?.ipfs,
          bittorrent: publication.storage?.bittorrent || publication.storageIds?.bittorrent
        };
      }
      
      // _id entfernen, wenn es ein Quell-Objekt aus Publikationsmodell ist
      if (doc.storage) {
        delete doc.storage;
      }
      
      const result = await this.collections.publications.updateOne(
        { _id: doc._id },
        { $set: doc },
        { upsert: true }
      );
      
      return {
        success: true,
        id: doc._id.toString(),
        isNew: result.upsertedCount === 1,
        updated: result.modifiedCount === 1
      };
    } catch (error) {
      console.error('Fehler beim Speichern der Publikationsmetadaten:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * Ruft eine Publikation anhand ihrer ID ab
   * @param {string} id Publikations-ID
   * @returns {Promise<Object>} Publikationsobjekt oder null
   */
  async getPublication(id) {
    await this._ensureConnected();
    
    try {
      const objectId = typeof id === 'string' ? new ObjectId(id) : id;
      const publication = await this.collections.publications.findOne({ _id: objectId });
      
      if (!publication) {
        return {
          success: false,
          message: 'Publikation nicht gefunden'
        };
      }
      
      // Konvertiere MongoDB-ID zu String
      publication.id = publication._id.toString();
      
      return {
        success: true,
        publication
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Publikation:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * Sucht nach Publikationen basierend auf Kriterien
   * @param {Object} criteria Suchkriterien
   * @param {Object} options Optionen für die Suche (limit, offset, sort)
   * @returns {Promise<Object>} Ergebnisobjekt mit gefundenen Publikationen
   */
  async searchPublications(criteria = {}, options = {}) {
    await this._ensureConnected();
    
    try {
      const query = this._buildSearchQuery(criteria);
      const limit = options.limit || 20;
      const offset = options.offset || 0;
      const sort = options.sort || { updatedAt: -1 };
      
      const total = await this.collections.publications.countDocuments(query);
      
      const cursor = this.collections.publications
        .find(query)
        .sort(sort)
        .skip(offset)
        .limit(limit);
      
      const publications = await cursor.toArray();
      
      // Wandle _id in id um für konsistentes API-Format
      publications.forEach(pub => {
        pub.id = pub._id.toString();
      });
      
      return {
        success: true,
        publications,
        total,
        limit,
        offset
      };
    } catch (error) {
      console.error('Fehler bei der Publikationssuche:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * Baut eine MongoDB-Suchabfrage basierend auf den Kriterien
   * @param {Object} criteria Suchkriterien
   * @returns {Object} MongoDB-Query-Objekt
   * @private
   */
  _buildSearchQuery(criteria) {
    const query = {};
    
    if (criteria.id) {
      query._id = new ObjectId(criteria.id);
    }
    
    if (criteria.title) {
      query.title = { $regex: criteria.title, $options: 'i' };
    }
    
    if (criteria.authors && criteria.authors.length) {
      if (Array.isArray(criteria.authors)) {
        query.authors = { $in: criteria.authors.map(a => new RegExp(a, 'i')) };
      } else {
        query.authors = { $regex: criteria.authors, $options: 'i' };
      }
    }
    
    if (criteria.keywords && criteria.keywords.length) {
      if (Array.isArray(criteria.keywords)) {
        query.keywords = { $in: criteria.keywords.map(k => new RegExp(k, 'i')) };
      } else {
        query.keywords = { $regex: criteria.keywords, $options: 'i' };
      }
    }
    
    if (criteria.status) {
      query.status = criteria.status;
    }
    
    if (criteria.doi) {
      query.doi = criteria.doi;
    }
    
    if (criteria.dateRange) {
      query.publishedDate = {};
      
      if (criteria.dateRange.from) {
        query.publishedDate.$gte = new Date(criteria.dateRange.from);
      }
      
      if (criteria.dateRange.to) {
        query.publishedDate.$lte = new Date(criteria.dateRange.to);
      }
    }
    
    if (criteria.textSearch) {
      query.$text = { $search: criteria.textSearch };
    }
    
    if (criteria.storageId) {
      query.$or = [
        { 'storageIds.ipfs': criteria.storageId },
        { 'storageIds.bittorrent': criteria.storageId }
      ];
    }
    
    return query;
  }
  
  /**
   * Löscht eine Publikation aus der Datenbank
   * @param {string} id ID der zu löschenden Publikation
   * @returns {Promise<Object>} Ergebnisobjekt
   */
  async deletePublication(id) {
    await this._ensureConnected();
    
    try {
      const objectId = typeof id === 'string' ? new ObjectId(id) : id;
      const result = await this.collections.publications.deleteOne({ _id: objectId });
      
      return {
        success: result.deletedCount === 1,
        deleted: result.deletedCount === 1
      };
    } catch (error) {
      console.error('Fehler beim Löschen der Publikation:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * Speichert einen Benutzer in der Datenbank
   * @param {Object} user Benutzerobjekt
   * @returns {Promise<Object>} Ergebnisobjekt mit ID
   */
  async saveUser(user) {
    await this._ensureConnected();
    
    try {
      const now = new Date();
      const doc = {
        ...user,
        _id: user._id || new ObjectId(),
        createdAt: user.createdAt || now,
        updatedAt: now
      };
      
      // Überprüfe, ob E-Mail oder Benutzername bereits existieren
      if (!user._id) {
        const existingUser = await this.collections.users.findOne({
          $or: [
            { email: doc.email },
            { username: doc.username }
          ]
        });
        
        if (existingUser) {
          return {
            success: false,
            error: 'E-Mail oder Benutzername bereits vergeben'
          };
        }
      }
      
      const result = await this.collections.users.updateOne(
        { _id: doc._id },
        { $set: doc },
        { upsert: true }
      );
      
      return {
        success: true,
        id: doc._id.toString(),
        isNew: result.upsertedCount === 1,
        updated: result.modifiedCount === 1
      };
    } catch (error) {
      console.error('Fehler beim Speichern des Benutzers:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * Ruft einen Benutzer anhand seiner ID ab
   * @param {string} id Benutzer-ID
   * @returns {Promise<Object>} Benutzerobjekt oder null
   */
  async getUser(id) {
    await this._ensureConnected();
    
    try {
      const objectId = typeof id === 'string' ? new ObjectId(id) : id;
      const user = await this.collections.users.findOne({ _id: objectId });
      
      if (!user) {
        return {
          success: false,
          message: 'Benutzer nicht gefunden'
        };
      }
      
      // Konvertiere MongoDB-ID zu String
      user.id = user._id.toString();
      
      return {
        success: true,
        user
      };
    } catch (error) {
      console.error('Fehler beim Abrufen des Benutzers:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * Ruft einen Benutzer anhand seiner E-Mail-Adresse ab
   * @param {string} email E-Mail-Adresse
   * @returns {Promise<Object>} Benutzerobjekt oder null
   */
  async getUserByEmail(email) {
    await this._ensureConnected();
    
    try {
      const user = await this.collections.users.findOne({ email });
      
      if (!user) {
        return {
          success: false,
          message: 'Benutzer nicht gefunden'
        };
      }
      
      // Konvertiere MongoDB-ID zu String
      user.id = user._id.toString();
      
      return {
        success: true,
        user
      };
    } catch (error) {
      console.error('Fehler beim Abrufen des Benutzers:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * Speichert eine Zitation in der Datenbank
   * @param {Object} citation Zitationsobjekt
   * @returns {Promise<Object>} Ergebnisobjekt mit ID
   */
  async saveCitation(citation) {
    await this._ensureConnected();
    
    try {
      const now = new Date();
      const doc = {
        ...citation,
        _id: citation._id || new ObjectId(),
        createdAt: citation.createdAt || now,
        updatedAt: now
      };
      
      const result = await this.collections.citations.updateOne(
        { _id: doc._id },
        { $set: doc },
        { upsert: true }
      );
      
      return {
        success: true,
        id: doc._id.toString(),
        isNew: result.upsertedCount === 1,
        updated: result.modifiedCount === 1
      };
    } catch (error) {
      console.error('Fehler beim Speichern der Zitation:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * Ruft Zitationen für eine Publikation ab
   * @param {string} publicationId Publikations-ID
   * @returns {Promise<Object>} Ergebnisobjekt mit Zitationen
   */
  async getCitationsForPublication(publicationId) {
    await this._ensureConnected();
    
    try {
      const objectId = typeof publicationId === 'string' ? new ObjectId(publicationId) : publicationId;
      
      // Alle Zitationen abrufen, die diese Publikation zitieren
      const citedBy = await this.collections.citations
        .find({ targetId: objectId })
        .toArray();
      
      // Alle Zitationen abrufen, die von dieser Publikation zitiert werden
      const cites = await this.collections.citations
        .find({ sourceId: objectId })
        .toArray();
      
      return {
        success: true,
        citedBy,
        cites
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Zitationen:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * Aktualisiert die Statistiken für eine Publikation
   * @param {string} publicationId Publikations-ID
   * @param {string} action Aktion (view, download, cite)
   * @returns {Promise<Object>} Ergebnisobjekt
   */
  async updateStats(publicationId, action) {
    await this._ensureConnected();
    
    try {
      const objectId = typeof publicationId === 'string' ? new ObjectId(publicationId) : publicationId;
      const now = new Date();
      
      // Aktualisiere Gesamtstatistik
      await this.collections.stats.updateOne(
        { publicationId: objectId },
        {
          $inc: { [`${action}Count`]: 1 },
          $set: { [`last${action.charAt(0).toUpperCase() + action.slice(1)}At`]: now }
        },
        { upsert: true }
      );
      
      // Füge Eintrag im Verlauf hinzu
      await this.collections.stats.updateOne(
        { publicationId: objectId },
        {
          $push: {
            [`${action}History`]: {
              date: now,
              // Hier könnten weitere Informationen wie IP, Benutzer etc. hinzugefügt werden
            }
          }
        }
      );
      
      return {
        success: true,
        message: `Statistik für ${action} erfolgreich aktualisiert`
      };
    } catch (error) {
      console.error('Fehler beim Aktualisieren der Statistik:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * Ruft Statistiken für eine Publikation ab
   * @param {string} publicationId Publikations-ID
   * @returns {Promise<Object>} Statistikobjekt
   */
  async getStats(publicationId) {
    await this._ensureConnected();
    
    try {
      const objectId = typeof publicationId === 'string' ? new ObjectId(publicationId) : publicationId;
      const stats = await this.collections.stats.findOne({ publicationId: objectId });
      
      if (!stats) {
        return {
          success: true,
          stats: {
            viewCount: 0,
            downloadCount: 0,
            citeCount: 0
          }
        };
      }
      
      return {
        success: true,
        stats
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Statistik:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * Beendet die Datenbankverbindung
   */
  async close() {
    if (this.client) {
      try {
        await this.client.close();
        this._connected = false;
        console.log('Datenbankverbindung geschlossen');
      } catch (error) {
        console.error('Fehler beim Schließen der Datenbankverbindung:', error);
      }
    }
  }

  /**
   * Listet alle Sammlungen in der Datenbank auf
   * @returns {Promise<Array<string>>} Liste der Sammlungsnamen
   */
  async listCollections() {
    try {
      if (!this._connected) {
        await this._ensureConnected();
      }

      const collections = await this.db.listCollections().toArray();
      return collections.map(collection => collection.name);
    } catch (error) {
      console.error('Fehler beim Auflisten der Sammlungen:', error);
      return [];
    }
  }

  /**
   * Erstellt eine neue Sammlung in der Datenbank
   * @param {string} collectionName Name der zu erstellenden Sammlung
   * @returns {Promise<boolean>} us
   */
  isConnected() {
    return this._connected;
  }

  /**
   * Erstellt eine MongoDB ObjectId aus einem String
   * @param {string} id ID als String
   * @returns {ObjectId} MongoDB ObjectId
   */
  createObjectId(id) {
    return new ObjectId(id);
  }

  /**
   * Listet alle Sammlungen in der Datenbank auf
   * @returns {Promise<Array<string>>} Liste der Sammlungsnamen
   */
  async listCollections() {
    try {
      if (!this._connected) {
        await this._ensureConnected();
      }

      const collections = await this.db.listCollections().toArray();
      return collections.map(collection => collection.name);
    } catch (error) {
      console.error('Fehler beim Auflisten der Sammlungen:', error);
      return [];
    }
  }

  /**
   * Erstellt eine neue Sammlung in der Datenbank
   * @param {string} collectionName Name der zu erstellenden Sammlung
   * @returns {Promise<boolean>} Erfolg der Operation
   */
  async createCollection(collectionName) {
    try {
      if (!this._connected) {
        await this._ensureConnected();
      }

      await this.db.createCollection(collectionName);
      return true;
    } catch (error) {
      console.error(`Fehler beim Erstellen der Sammlung ${collectionName}:`, error);
      return false;
    }
  }

  /**
   * Erstellt einen Index für eine Sammlung
   * @param {string} collectionName Name der Sammlung
   * @param {Object} keys Schlüssel für den Index
   * @param {Object} [options] Optionen für den Index
   * @returns {Promise<string>} Name des erstellten Index
   */
  async createIndex(collectionName, keys, options = {}) {
    try {
      if (!this._connected) {
        await this._ensureConnected();
      }

      const collection = this.db.collection(collectionName);
      return await collection.createIndex(keys, options);
    } catch (error) {
      console.error(`Fehler beim Erstellen des Index für ${collectionName}:`, error);
      throw error;
    }
  }

  /**
   * Fügt ein Dokument in eine Sammlung ein
   * @param {string} collectionName Name der Sammlung
   * @param {Object} document Einzufügendes Dokument
   * @returns {Promise<Object>} Ergebnis der Einfügeoperation
   */
  async insertOne(collectionName, document) {
    try {
      if (!this._connected) {
        await this._ensureConnected();
      }

      const collection = this.db.collection(collectionName);
      return await collection.insertOne(document);
    } catch (error) {
      console.error(`Fehler beim Einfügen in ${collectionName}:`, error);
      throw error;
    }
  }

  /**
   * Aktualisiert ein Dokument in einer Sammlung
   * @param {string} collectionName Name der Sammlung
   * @param {Object} filter Filter für die zu aktualisierenden Dokumente
   * @param {Object} update Aktualisierungsoperation
   * @param {Object} [options] Optionen für die Aktualisierung
   * @returns {Promise<Object>} Ergebnis der Aktualisierungsoperation
   */
  async updateOne(collectionName, filter, update, options = {}) {
    try {
      if (!this._connected) {
        await this._ensureConnected();
      }

      const collection = this.db.collection(collectionName);
      return await collection.updateOne(filter, update, options);
    } catch (error) {
      console.error(`Fehler beim Aktualisieren in ${collectionName}:`, error);
      throw error;
    }
  }

  /**
   * Findet ein Dokument in einer Sammlung
   * @param {string} collectionName Name der Sammlung
   * @param {Object} filter Filter für die Suche
   * @param {Object} [options] Optionen für die Suche
   * @returns {Promise<Object|null>} Gefundenes Dokument oder null
   */
  async findOne(collectionName, filter, options = {}) {
    try {
      if (!this._connected) {
        await this._ensureConnected();
      }

      const collection = this.db.collection(collectionName);
      return await collection.findOne(filter, options);
    } catch (error) {
      console.error(`Fehler beim Suchen in ${collectionName}:`, error);
      throw error;
    }
  }

  /**
   * Zählt Dokumente in einer Sammlung
   * @param {string} collectionName Name der Sammlung
   * @param {Object} filter Filter für die zu zählenden Dokumente
   * @returns {Promise<number>} Anzahl der Dokumente
   */
  async countDocuments(collectionName, filter) {
    try {
      if (!this._connected) {
        await this._ensureConnected();
      }

      const collection = this.db.collection(collectionName);
      return await collection.countDocuments(filter);
    } catch (error) {
      console.error(`Fehler beim Zählen in ${collectionName}:`, error);
      throw error;
    }
  }

  /**
   * Führt eine Aggregationspipeline auf einer Sammlung aus
   * @param {string} collectionName Name der Sammlung
   * @param {Array} pipeline Aggregationspipeline
   * @param {Object} [options] Optionen für die Aggregation
   * @returns {Promise<Array>} Ergebnis der Aggregation
   */
  async aggregate(collectionName, pipeline, options = {}) {
    try {
      if (!this._connected) {
        await this._ensureConnected();
      }

      const collection = this.db.collection(collectionName);
      return await collection.aggregate(pipeline, options).toArray();
    } catch (error) {
      console.error(`Fehler bei der Aggregation in ${collectionName}:`, error);
      throw error;
    }
  }
}

export default DatabaseService; 