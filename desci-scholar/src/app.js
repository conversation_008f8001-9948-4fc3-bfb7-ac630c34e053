import ModuleRegistry from './config/moduleRegistry.js';
import { LoggerFactory } from './utils/logger.js';

// Manager und Services
import BlockchainManager from './services/blockchain/BlockchainManager.js';
import StorageManager from './services/storage/StorageManager.js';
import IdentityManager from './services/identity/IdentityManager.js';
import PaymentManager from './services/payment/PaymentManager.js';
import DoiNftService from './services/scientific/DoiNftService.js';
import AiDataExportService from './services/ai/AiDataExportService.js';
import ApiGateway from './api/ApiGateway.js';

// Adapter
import PolkadotAdapter from './services/blockchain/PolkadotAdapter.js';
import EthereumAdapter from './services/blockchain/EthereumAdapter.js';
import PolygonAdapter from './services/blockchain/PolygonAdapter.js';
import OptimismAdapter from './services/blockchain/OptimismAdapter.js';
import SolanaAdapter from './services/blockchain/SolanaAdapter.js';

const logger = LoggerFactory.createLogger('App');

/**
 * Hauptanwendung für DeSci-Scholar
 * Initialisiert alle Module und startet die Anwendung
 */
export async function startApp() {
  try {
    logger.info('Starte DeSci-Scholar Anwendung...');

    // Modul-Registry erstellen
    const registry = new ModuleRegistry();

    // Blockchain-Adapter registrieren
    logger.info('Registriere Blockchain-Adapter...');
    registry.registerAdapter('blockchain', 'polkadot', new PolkadotAdapter());
    registry.registerAdapter('blockchain', 'ethereum', new EthereumAdapter());
    registry.registerAdapter('blockchain', 'polygon', new PolygonAdapter());
    registry.registerAdapter('blockchain', 'optimism', new OptimismAdapter());
    registry.registerAdapter('blockchain', 'solana', new SolanaAdapter());

    // Standard-Adapter setzen
    registry.setDefaultAdapter('blockchain', 'polkadot');

    logger.info('DeSci-Scholar Anwendung erfolgreich gestartet');

    return {
      registry
    };
  } catch (error) {
    logger.error('Fehler beim Starten der Anwendung:', {
      error: error.message,
      stack: error.stack
    });
    throw error;
  }
}

// Anwendung starten, wenn die Datei direkt ausgeführt wird
if (import.meta.url === `file://${process.argv[1]}`) {
  startApp().catch(err => {
    logger.error('Fehler beim Starten der Anwendung:', {
      error: err.message,
      stack: err.stack
    });
    process.exit(1);
  });
}

export default { startApp };