/**
 * Hilfsfunktionen für Buffer-Operationen, Hashing und Dateiumwandlung
 * Unterstützt verschiedene Dateneingaben für dezentrale Speicherung
 */

import crypto from 'crypto';

/**
 * Konvertiert einen Buffer in das gewünschte Format
 * @param {Buffer} buffer Der zu konvertierende Buffer
 * @param {string} format Zielformat ('buffer', 'text', 'json', 'base64', 'hex')
 * @returns {Buffer|string|Object} Konvertierte Daten
 */
export function parseBuffer(buffer, format = 'buffer') {
  if (!Buffer.isBuffer(buffer)) {
    throw new Error('Eingabe ist kein Buffer');
  }
  
  switch (format.toLowerCase()) {
    case 'buffer':
      return buffer;
    
    case 'text':
    case 'string':
      return buffer.toString('utf-8');
    
    case 'json':
      try {
        return JSON.parse(buffer.toString('utf-8'));
      } catch (error) {
        throw new Error(`Konnte Buffer nicht als JSON parsen: ${error.message}`);
      }
    
    case 'base64':
      return buffer.toString('base64');
    
    case 'hex':
      return buffer.toString('hex');
    
    default:
      throw new Error(`Unbekanntes Format: ${format}`);
  }
}

/**
 * Konvertiert verschiedene Datentypen in einen Buffer
 * @param {Buffer|string|Object|Array} data - Zu konvertierender Datentyp
 * @returns {Buffer} Konvertierter Buffer
 */
export function toBuffer(data) {
  if (Buffer.isBuffer(data)) {
    return data;
  }
  
  if (typeof data === 'string') {
    return Buffer.from(data);
  }
  
  if (typeof data === 'object') {
    return Buffer.from(JSON.stringify(data));
  }
  
  throw new Error('Nicht unterstützter Datentyp für Buffer-Konvertierung');
}

/**
 * Überprüft, ob die Daten ein gültiges JSON-Format haben
 * @param {string|Buffer} data Die zu überprüfenden Daten
 * @returns {boolean} Gültigkeit
 */
export function isValidJSON(data) {
  try {
    if (Buffer.isBuffer(data)) {
      JSON.parse(data.toString('utf-8'));
    } else if (typeof data === 'string') {
      JSON.parse(data);
    } else {
      return false;
    }
    return true;
  } catch (error) {
    return false;
  }
}

/**
 * Berechnet die Größe eines Datensatzes in Bytes
 * @param {Buffer|string|Object} data - Daten, deren Größe berechnet werden soll
 * @returns {number} Größe in Bytes
 */
export function calculateSize(data) {
  return toBuffer(data).length;
}

/**
 * Berechnet den SHA-256-Hash eines Datensatzes
 * @param {Buffer|string|Object} data - Datensatz, dessen Hash berechnet werden soll
 * @returns {string} SHA-256-Hash als Hex-String
 */
export function sha256Hash(data) {
  const buffer = toBuffer(data);
  return crypto.createHash('sha256').update(buffer).digest('hex');
}

/**
 * Berechnet den MD5-Hash eines Datensatzes
 * @param {Buffer|string|Object} data - Datensatz, dessen Hash berechnet werden soll
 * @returns {string} MD5-Hash als Hex-String
 */
export function md5Hash(data) {
  const buffer = toBuffer(data);
  return crypto.createHash('md5').update(buffer).digest('hex');
}

/**
 * Konvertiert einen Buffer in eine Base64-Zeichenkette
 * @param {Buffer} buffer - Zu konvertierender Buffer
 * @returns {string} Base64-codierte Zeichenkette
 */
export function bufferToBase64(buffer) {
  return toBuffer(buffer).toString('base64');
}

/**
 * Konvertiert eine Base64-Zeichenkette zurück in einen Buffer
 * @param {string} base64 - Base64-codierte Zeichenkette
 * @returns {Buffer} Dekodierter Buffer
 */
export function base64ToBuffer(base64) {
  return Buffer.from(base64, 'base64');
}

/**
 * Zerlegt große Daten in Chunks für effizientere Verarbeitung
 * @param {Buffer} buffer Der zu zerlegende Buffer
 * @param {number} chunkSize Größe der Chunks in Bytes (Standard: 1MB)
 * @returns {Array<Buffer>} Array mit Buffer-Chunks
 */
export function splitBufferIntoChunks(buffer, chunkSize = 1024 * 1024) {
  if (!Buffer.isBuffer(buffer)) {
    throw new Error('Eingabe ist kein Buffer');
  }
  
  if (chunkSize <= 0) {
    throw new Error('Chunk-Größe muss positiv sein');
  }
  
  const chunks = [];
  
  for (let i = 0; i < buffer.length; i += chunkSize) {
    chunks.push(buffer.slice(i, i + chunkSize));
  }
  
  return chunks;
}

/**
 * Konvertiert einen String in einen UTF-8-Buffer
 * @param {string} str Der zu konvertierende String
 * @returns {Buffer} UTF-8-Buffer
 */
export function stringToUtf8Buffer(str) {
  if (typeof str !== 'string') {
    throw new Error('Eingabe ist kein String');
  }
  
  return Buffer.from(str, 'utf-8');
}

/**
 * Konvertiert einen Buffer in einen UTF-8-String
 * @param {Buffer} buffer Der zu konvertierende Buffer
 * @returns {string} UTF-8-String
 */
export function bufferToUtf8String(buffer) {
  if (!Buffer.isBuffer(buffer)) {
    throw new Error('Eingabe ist kein Buffer');
  }
  
  return buffer.toString('utf-8');
}

/**
 * Fügt mehrere Buffer zusammen
 * @param {Array<Buffer>} buffers Array von Buffern
 * @returns {Buffer} Zusammengeführter Buffer
 */
export function concatBuffers(buffers) {
  if (!Array.isArray(buffers) || !buffers.every(buf => Buffer.isBuffer(buf))) {
    throw new Error('Eingabe muss ein Array von Buffern sein');
  }
  
  return Buffer.concat(buffers);
}

/**
 * Erstellt einen zufälligen Buffer einer bestimmten Länge
 * @param {number} length - Länge des zu erstellenden Buffers in Bytes
 * @returns {Buffer} Zufälliger Buffer
 */
export function randomBuffer(length) {
  return crypto.randomBytes(length);
}

/**
 * Konvertiert einen Buffer in eine hexadezimale Zeichenkette
 * @param {Buffer} buffer - Zu konvertierender Buffer
 * @returns {string} Hexadezimale Zeichenkette
 */
export function bufferToHex(buffer) {
  return toBuffer(buffer).toString('hex');
}

/**
 * Konvertiert eine hexadezimale Zeichenkette zurück in einen Buffer
 * @param {string} hex - Hexadezimale Zeichenkette
 * @returns {Buffer} Konvertierter Buffer
 */
export function hexToBuffer(hex) {
  return Buffer.from(hex, 'hex');
}

/**
 * Vergleicht zwei Buffer auf Gleichheit
 * @param {Buffer} buffer1 - Erster Buffer
 * @param {Buffer} buffer2 - Zweiter Buffer
 * @returns {boolean} True, wenn die Buffer identisch sind
 */
export function compareBuffers(buffer1, buffer2) {
  const b1 = toBuffer(buffer1);
  const b2 = toBuffer(buffer2);
  
  if (b1.length !== b2.length) {
    return false;
  }
  
  return crypto.timingSafeEqual(b1, b2);
}

/**
 * Konvertiert einen Buffer in einen lesbaren String, wenn möglich
 * @param {Buffer} buffer - Zu konvertierender Buffer
 * @returns {string} String-Repräsentation des Buffers
 */
export function bufferToString(buffer) {
  const buf = toBuffer(buffer);
  
  // Prüfen, ob es sich um einen UTF-8-String handelt
  try {
    return buf.toString('utf8');
  } catch (e) {
    // Wenn keine UTF-8-Konvertierung möglich ist, als Hex zurückgeben
    return bufferToHex(buf);
  }
}

/**
 * Berechnet den Chunk-Hash eines Datenblocks nach dem IPFS-Standard
 * @param {Buffer} data - Datenblock
 * @returns {string} Multihash als Hex-String
 */
export function calculateMultihash(data) {
  // Einfache Implementierung - in Produktion würde man ipfs-unixfs verwenden
  const buffer = toBuffer(data);
  const hash = sha256Hash(buffer);
  // 0x12 = SHA-256, 0x20 = 32 Bytes Länge in der IPFS-Multihash-Spezifikation
  return `1220${hash}`;
}

/**
 * Kodiert einen String als URL-sicher
 * @param {string} str - Zu kodierender String
 * @returns {string} URL-sicherer String
 */
export function encodeURLSafe(str) {
  if (typeof str !== 'string') {
    str = String(str);
  }
  return encodeURIComponent(str).replace(/[!'()*]/g, c => `%${c.charCodeAt(0).toString(16).toUpperCase()}`);
}

/**
 * Dekodiert einen URL-sicheren String
 * @param {string} str - Zu dekodierender URL-sicherer String
 * @returns {string} Dekodierter String
 */
export function decodeURLSafe(str) {
  return decodeURIComponent(str);
} 