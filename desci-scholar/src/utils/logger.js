/**
 * @fileoverview Logger-Utility für DeSci-Scholar
 * 
 * Bietet eine einheitliche Schnittstelle für Logging in der Anwendung.
 * In einer Produktionsumgebung könnte dies durch eine robustere Lösung wie Winston ersetzt werden.
 */

/**
 * Einfacher Logger mit verschiedenen Log-Levels
 */
export const logger = {
  /**
   * Loggt eine Info-Nachricht
   * @param {string} message - Nachricht
   * @param {Object} [data] - Zusätzliche Daten
   */
  info: (message, data) => {
    console.log(`[INFO] ${message}`, data ? data : '');
  },
  
  /**
   * Loggt eine Warn-Nachricht
   * @param {string} message - Nachricht
   * @param {Object} [data] - Zusätzliche Daten
   */
  warn: (message, data) => {
    console.warn(`[WARN] ${message}`, data ? data : '');
  },
  
  /**
   * Loggt eine Error-Nachricht
   * @param {string} message - Nachricht
   * @param {Object} [data] - Zusätzliche Daten
   */
  error: (message, data) => {
    console.error(`[ERROR] ${message}`, data ? data : '');
  },
  
  /**
   * Loggt eine Debug-Nachricht (nur in Entwicklungsumgebung)
   * @param {string} message - Nachricht
   * @param {Object} [data] - Zusätzliche Daten
   */
  debug: (message, data) => {
    if (process.env.NODE_ENV !== 'production') {
      console.debug(`[DEBUG] ${message}`, data ? data : '');
    }
  }
};

/**
 * Logger-Factory für die Erstellung von benannten Loggern
 */
export const LoggerFactory = {
  /**
   * Erstellt einen benannten Logger
   * @param {string} name - Name des Loggers
   * @returns {Object} Logger-Instanz
   */
  createLogger: (name) => {
    return {
      /**
       * Loggt eine Info-Nachricht
       * @param {string} message - Nachricht
       * @param {Object} [data] - Zusätzliche Daten
       */
      info: (message, data) => {
        console.log(`[INFO] [${name}] ${message}`, data ? data : '');
      },
      
      /**
       * Loggt eine Warn-Nachricht
       * @param {string} message - Nachricht
       * @param {Object} [data] - Zusätzliche Daten
       */
      warn: (message, data) => {
        console.warn(`[WARN] [${name}] ${message}`, data ? data : '');
      },
      
      /**
       * Loggt eine Error-Nachricht
       * @param {string} message - Nachricht
       * @param {Object} [data] - Zusätzliche Daten
       */
      error: (message, data) => {
        console.error(`[ERROR] [${name}] ${message}`, data ? data : '');
      },
      
      /**
       * Loggt eine Debug-Nachricht (nur in Entwicklungsumgebung)
       * @param {string} message - Nachricht
       * @param {Object} [data] - Zusätzliche Daten
       */
      debug: (message, data) => {
        if (process.env.NODE_ENV !== 'production') {
          console.debug(`[DEBUG] [${name}] ${message}`, data ? data : '');
        }
      }
    };
  }
};

export default logger;
