/**
 * Text-Utilities für DeSci-Scholar
 * 
 * Stellt Funktionen für die Verarbeitung, Normalisierung und Analyse von Text bereit.
 */

/**
 * Normalisiert einen Text für die Suche und Indizierung
 * Entfernt Sonderzeichen, wandelt in Kleinbuchstaben um, entfernt überflüssige Leerzeichen
 * 
 * @param {string} text Zu normalisierender Text
 * @returns {string} Normalisierter Text
 */
export function normalizeText(text) {
  if (!text || typeof text !== 'string') {
    return '';
  }
  
  return text
    .toLowerCase()
    .replace(/[^\p{L}\p{N}\p{Z}]/gu, ' ') // Entferne alle Nicht-Buchstaben, Nicht-Zahlen
    .replace(/\s+/g, ' ')                 // Entferne mehrfache Leerzeichen
    .trim();                              // Entferne führende/nachfolgende Leerzeichen
}

/**
 * Prüft, ob ein Text in einem anderen enthalten ist (normalisiert)
 * 
 * @param {string} haystack Text, in dem gesucht wird
 * @param {string} needle Zu suchender Text
 * @returns {boolean} Ist enthalten?
 */
export function textContains(haystack, needle) {
  if (!haystack || !needle) return false;
  
  const normalizedHaystack = normalizeText(haystack);
  const normalizedNeedle = normalizeText(needle);
  
  return normalizedHaystack.includes(normalizedNeedle);
}

/**
 * Stopwörter in verschiedenen Sprachen
 * Wird verwendet, um häufige Wörter zu filtern, die für die Suche wenig relevant sind
 */
const STOPWORDS = {
  de: new Set([
    'aber', 'als', 'am', 'an', 'auch', 'auf', 'aus', 'bei', 'bin', 'bis', 'bist', 'da', 'damit', 'dann',
    'das', 'dass', 'dem', 'den', 'der', 'des', 'die', 'dies', 'ein', 'eine', 'einem', 'einen', 'einer',
    'eines', 'er', 'es', 'für', 'hatte', 'hatten', 'hattest', 'hattet', 'hier', 'ich', 'ihr', 'ihre',
    'im', 'in', 'ist', 'ja', 'kann', 'kannst', 'könnt', 'können', 'machen', 'mein', 'meine', 'mit',
    'nach', 'nicht', 'noch', 'nun', 'oder', 'seid', 'sein', 'seine', 'sich', 'sie', 'sind', 'so',
    'über', 'und', 'vom', 'von', 'vor', 'war', 'waren', 'warst', 'was', 'weg', 'wenn', 'werden',
    'wie', 'wieder', 'will', 'wir', 'wird', 'wirst', 'wo', 'zu', 'zum', 'zur'
  ]),
  en: new Set([
    'a', 'about', 'above', 'after', 'again', 'against', 'all', 'am', 'an', 'and', 'any', 'are', 'as',
    'at', 'be', 'because', 'been', 'before', 'being', 'below', 'between', 'both', 'but', 'by', 'can',
    'did', 'do', 'does', 'doing', 'down', 'during', 'each', 'few', 'for', 'from', 'further', 'had',
    'has', 'have', 'having', 'he', 'her', 'here', 'hers', 'herself', 'him', 'himself', 'his', 'how',
    'i', 'if', 'in', 'into', 'is', 'it', 'its', 'itself', 'me', 'more', 'most', 'my', 'myself', 'no',
    'nor', 'not', 'of', 'off', 'on', 'once', 'only', 'or', 'other', 'ought', 'our', 'ours', 'ourselves',
    'out', 'over', 'own', 'same', 'she', 'should', 'so', 'some', 'such', 'than', 'that', 'the', 'their',
    'theirs', 'them', 'themselves', 'then', 'there', 'these', 'they', 'this', 'those', 'through', 'to',
    'too', 'under', 'until', 'up', 'very', 'was', 'we', 'were', 'what', 'when', 'where', 'which', 'while',
    'who', 'whom', 'why', 'with', 'would', 'you', 'your', 'yours', 'yourself', 'yourselves'
  ])
};

/**
 * Komb. Set mit allen Stopwörtern für einfachen Zugriff
 */
const ALL_STOPWORDS = new Set([
  ...STOPWORDS.de,
  ...STOPWORDS.en
]);

/**
 * Prüft, ob ein Wort ein Stopwort ist
 * 
 * @param {string} word Zu prüfendes Wort
 * @param {string} lang Sprache (Standard: 'all')
 * @returns {boolean} Ist Stopwort?
 */
export function isStopword(word, lang = 'all') {
  if (!word) return false;
  
  const normalizedWord = word.toLowerCase().trim();
  
  if (lang === 'all') {
    return ALL_STOPWORDS.has(normalizedWord);
  }
  
  const langStopwords = STOPWORDS[lang];
  if (!langStopwords) {
    console.warn(`Unbekannte Sprache "${lang}" für Stopwörter, verwende alle Stopwörter`);
    return ALL_STOPWORDS.has(normalizedWord);
  }
  
  return langStopwords.has(normalizedWord);
}

/**
 * Extrahiert die relevantesten Schlüsselwörter aus einem Text
 * Nützlich für die Erstellung von Suchanfragen oder die Analyse von Text
 * 
 * @param {string} text Text, aus dem Schlüsselwörter extrahiert werden sollen
 * @param {Object} options Optionen für die Extraktion
 * @param {number} options.minWordLength Mindestlänge eines Wortes (Standard: 3)
 * @param {number} options.maxKeywords Maximale Anzahl von Schlüsselwörtern (Standard: 10)
 * @param {string} options.language Sprache für Stopwörter (Standard: 'all')
 * @returns {string[]} Extrahierte Schlüsselwörter
 */
export function extractKeywords(text, options = {}) {
  if (!text || typeof text !== 'string') {
    return [];
  }
  
  const {
    minWordLength = 3,
    maxKeywords = 10,
    language = 'all'
  } = options;
  
  // Text normalisieren und in Wörter aufteilen
  const words = normalizeText(text).split(/\s+/);
  
  // Wörter zählen (ohne Stopwörter und zu kurze Wörter)
  const wordCounts = words.reduce((counts, word) => {
    if (word.length < minWordLength || isStopword(word, language)) {
      return counts;
    }
    
    counts[word] = (counts[word] || 0) + 1;
    return counts;
  }, {});
  
  // Nach Häufigkeit sortieren und die häufigsten zurückgeben
  return Object.entries(wordCounts)
    .sort((a, b) => b[1] - a[1])
    .slice(0, maxKeywords)
    .map(([word]) => word);
}

/**
 * Berechnet die textuelle Ähnlichkeit zwischen zwei Texten
 * mit dem Jaccard-Ähnlichkeitskoeffizienten (Verhältnis gemeinsamer Wörter)
 * 
 * @param {string} text1 Erster Text
 * @param {string} text2 Zweiter Text
 * @param {Object} options Optionen
 * @returns {number} Ähnlichkeit (0-1)
 */
export function calculateTextSimilarity(text1, text2, options = {}) {
  if (!text1 || !text2) return 0;
  
  const {
    minWordLength = 3,
    language = 'all'
  } = options;
  
  // Beide Texte in Sets von Wörtern umwandeln (ohne Stopwörter und zu kurze Wörter)
  const words1 = new Set(
    normalizeText(text1)
      .split(/\s+/)
      .filter(word => word.length >= minWordLength && !isStopword(word, language))
  );
  
  const words2 = new Set(
    normalizeText(text2)
      .split(/\s+/)
      .filter(word => word.length >= minWordLength && !isStopword(word, language))
  );
  
  // Leere Mengen behandeln
  if (words1.size === 0 || words2.size === 0) return 0;
  
  // Schnittmenge und Vereinigungsmenge berechnen
  const intersection = new Set([...words1].filter(word => words2.has(word)));
  const union = new Set([...words1, ...words2]);
  
  // Jaccard-Ähnlichkeit: Größe der Schnittmenge geteilt durch Größe der Vereinigungsmenge
  return intersection.size / union.size;
}

/**
 * Erstellt eine Zusammenfassung eines Textes
 * 
 * @param {string} text Zu kürzender Text
 * @param {Object} options Optionen
 * @param {number} options.maxLength Maximale Länge (Standard: 200)
 * @param {boolean} options.addEllipsis Auslassungspunkte hinzufügen (Standard: true)
 * @returns {string} Gekürzte Version des Textes
 */
export function summarizeText(text, options = {}) {
  if (!text) return '';
  
  const {
    maxLength = 200,
    addEllipsis = true
  } = options;
  
  // Wenn der Text bereits kurz genug ist, gib ihn unverändert zurück
  if (text.length <= maxLength) {
    return text;
  }
  
  // Kürze den Text auf einen Satz oder eine bestimmte Länge
  let summary = text.substring(0, maxLength);
  
  // Versuche, am Ende eines Satzes abzuschneiden
  const sentenceEnd = summary.lastIndexOf('. ');
  if (sentenceEnd > maxLength * 0.5) {
    summary = summary.substring(0, sentenceEnd + 1);
  } else {
    // Andernfalls schneide am Ende eines Wortes ab
    const lastSpace = summary.lastIndexOf(' ');
    if (lastSpace > 0) {
      summary = summary.substring(0, lastSpace);
    }
  }
  
  // Füge Auslassungspunkte hinzu, wenn gewünscht
  if (addEllipsis && text.length > maxLength) {
    summary += '...';
  }
  
  return summary;
}

/**
 * Extrahiert Zitate und deren mögliche Quellen aus einem Text
 * z.B. für die automatische Erkennung von Zitationen
 * 
 * @param {string} text Text, aus dem Zitate extrahiert werden sollen
 * @returns {Array} Extrahierte Zitate und Quellen
 */
export function extractCitations(text) {
  if (!text) return [];
  
  const citations = [];
  
  // Verschiedene Zitationsformate erkennen
  
  // 1. Zitate in Anführungszeichen mit möglichen Quellen
  const quoteRegex = /"([^"]+)"\s*(?:\(([^)]+)\))?/g;
  let quoteMatch;
  while ((quoteMatch = quoteRegex.exec(text)) !== null) {
    citations.push({
      type: 'quote',
      text: quoteMatch[1],
      source: quoteMatch[2] || null
    });
  }
  
  // 2. Akademische Zitationen im Text (Autor, Jahr)
  const academicRegex = /\(([^,]+),\s*(\d{4})[^)]*\)/g;
  let academicMatch;
  while ((academicMatch = academicRegex.exec(text)) !== null) {
    citations.push({
      type: 'academic',
      author: academicMatch[1],
      year: academicMatch[2]
    });
  }
  
  // 3. DOI-Erwähnungen finden
  const doiRegex = /\b(10\.\d{4,}(?:\.\d+)*\/\S+[^,;\s])\b/g;
  let doiMatch;
  while ((doiMatch = doiRegex.exec(text)) !== null) {
    citations.push({
      type: 'doi',
      doi: doiMatch[1]
    });
  }
  
  return citations;
}

/**
 * Analysiert einen Text und gibt verschiedene Metriken zurück
 * z.B. Lesbarkeit, Wortanzahl, Satzanzahl
 * 
 * @param {string} text Zu analysierender Text
 * @returns {Object} Textmetriken
 */
export function analyzeText(text) {
  if (!text) {
    return {
      wordCount: 0,
      sentenceCount: 0,
      avgWordLength: 0,
      avgSentenceLength: 0,
      readingTimeMinutes: 0
    };
  }
  
  // Normalisiere den Text und erhalte alle Wörter
  const normalizedText = normalizeText(text);
  const words = normalizedText.split(/\s+/).filter(word => word.length > 0);
  
  // Sätze grob anhand von Satzzeichen erkennen
  const sentences = text.split(/[.!?]+/).filter(sentence => sentence.trim().length > 0);
  
  // Durchschnittliche Wortlänge berechnen
  const totalChars = words.reduce((sum, word) => sum + word.length, 0);
  const avgWordLength = words.length > 0 ? totalChars / words.length : 0;
  
  // Durchschnittliche Satzlänge (in Wörtern) berechnen
  const avgSentenceLength = sentences.length > 0 ? words.length / sentences.length : 0;
  
  // Lesezeit berechnen (durchschnittlich 200-250 Wörter pro Minute)
  const readingTimeMinutes = words.length / 200;
  
  return {
    wordCount: words.length,
    sentenceCount: sentences.length,
    avgWordLength: parseFloat(avgWordLength.toFixed(2)),
    avgSentenceLength: parseFloat(avgSentenceLength.toFixed(2)),
    readingTimeMinutes: parseFloat(readingTimeMinutes.toFixed(2))
  };
}

/**
 * Teilt einen Text in Sätze auf
 * 
 * @param {string} text Zu verarbeitender Text
 * @returns {string[]} Array von Sätzen
 */
export function splitSentences(text) {
  if (!text) return [];

  // Teile an Satzzeichen, berücksichtige aber spezielle Fälle wie Abkürzungen
  return text
    .replace(/([.?!])\s+(?=[A-Z])/g, '$1|')
    .split('|')
    .map(sentence => sentence.trim())
    .filter(sentence => sentence.length > 0);
}

/**
 * Prüft, ob ein Text einen bestimmten Suchbegriff enthält
 * @param {string} text - Zu durchsuchender Text
 * @param {string} searchTerm - Suchbegriff
 * @param {boolean} caseSensitive - Groß-/Kleinschreibung beachten
 * @returns {boolean} True, wenn der Suchbegriff gefunden wurde
 */
export function textContains(text, searchTerm, caseSensitive = false) {
  if (!text || !searchTerm) return false;

  const normalizedText = caseSensitive ? text : text.toLowerCase();
  const normalizedSearchTerm = caseSensitive ? searchTerm : searchTerm.toLowerCase();

  return normalizedText.includes(normalizedSearchTerm);
}