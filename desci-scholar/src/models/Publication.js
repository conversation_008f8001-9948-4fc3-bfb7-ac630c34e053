/**
 * Publication-Modell für die Verwaltung wissenschaftlicher Publikationen
 * Unterstützt dezentrale Speicherung über IPFS und BitTorrent
 */

import StorageService, { StorageProtocol } from '../storage/StorageService.js';
import { sha256Hash, toBuffer } from '../utils/buffer.js';

class Publication {
  /**
   * Konstruktor für eine wissenschaftliche Publikation
   * @param {Object} data Publikationsdaten
   * @param {string} data.title Titel der Publikation
   * @param {Array} data.authors Autoren der Publikation
   * @param {string} data.abstract Zusammenfassung der Publikation
   * @param {string} data.content Volltext der Publikation
   * @param {Array} data.keywords Schlüsselwörter der Publikation
   * @param {Date|string} data.publishedDate Veröffentlichungsdatum
   * @param {string} data.doi Digital Object Identifier (optional)
   * @param {string} data.license Lizenzinformationen (optional)
   * @param {Object} data.file Datei-Informationen (optional)
   * @param {Object} data.storage Speicherinformationen (optional)
   */
  constructor(data = {}) {
    this.title = data.title || '';
    this.authors = data.authors || [];
    this.abstract = data.abstract || '';
    this.content = data.content || '';
    this.keywords = data.keywords || [];
    this.publishedDate = data.publishedDate ? new Date(data.publishedDate) : new Date();
    this.doi = data.doi || '';
    this.license = data.license || 'CC BY 4.0';
    this.file = data.file || null;
    
    // Speicherinformationen (IPFS CID, BitTorrent Infohash, etc.)
    this.storage = data.storage || {
      ipfs: null,
      bittorrent: null,
      timestamp: null
    };
    
    // Metadaten für Zitationen und Verweise
    this.citations = data.citations || [];
    this.references = data.references || [];
    
    // Eindeutige ID basierend auf Titel und Autoren
    this.id = this._generateId();
    
    // Status der Publikation
    this.status = data.status || 'draft'; // draft, published, retracted
    
    // Versionshistorie
    this.version = data.version || '1.0.0';
    this.versionHistory = data.versionHistory || [];
  }
  
  /**
   * Generiert eine eindeutige ID für die Publikation
   * @returns {string} Eindeutige ID
   * @private
   */
  _generateId() {
    const idBase = `${this.title}-${this.authors.join('-')}-${this.publishedDate.toISOString()}`;
    return sha256Hash(idBase).substring(0, 16);
  }
  
  /**
   * Validiert die Publikationsdaten
   * @returns {Object} Validierungsergebnis
   */
  validate() {
    const errors = [];
    
    if (!this.title || this.title.trim().length === 0) {
      errors.push('Titel ist erforderlich');
    }
    
    if (!this.authors || this.authors.length === 0) {
      errors.push('Mindestens ein Autor ist erforderlich');
    }
    
    if (!this.abstract || this.abstract.trim().length === 0) {
      errors.push('Zusammenfassung ist erforderlich');
    }
    
    if (!this.content || this.content.trim().length === 0) {
      errors.push('Inhalt ist erforderlich');
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }
  
  /**
   * Konvertiert die Publikation in ein JSON-Objekt
   * @param {boolean} includeContent Volltext einschließen? (Standard: true)
   * @returns {Object} JSON-Objekt der Publikation
   */
  toJSON(includeContent = true) {
    const json = {
      id: this.id,
      title: this.title,
      authors: this.authors,
      abstract: this.abstract,
      keywords: this.keywords,
      publishedDate: this.publishedDate.toISOString(),
      doi: this.doi,
      license: this.license,
      file: this.file,
      storage: this.storage,
      citations: this.citations,
      references: this.references,
      status: this.status,
      version: this.version,
      versionHistory: this.versionHistory
    };
    
    if (includeContent) {
      json.content = this.content;
    }
    
    return json;
  }
  
  /**
   * Speichert die Publikation mit dem angegebenen Storage-Service
   * @param {StorageService} storageService Initialisierter Storage-Service
   * @param {Object} options Speicheroptionen
   * @param {StorageProtocol} options.protocol Zu verwendendes Protokoll
   * @param {boolean} options.includeContent Volltext einschließen? (Standard: true)
   * @returns {Promise<Object>} Speicherergebnis
   */
  async save(storageService, options = {}) {
    // Validiere die Publikation
    const validation = this.validate();
    if (!validation.valid) {
      throw new Error(`Ungültige Publikation: ${validation.errors.join(', ')}`);
    }
    
    // Standardoptionen
    const saveOptions = {
      protocol: StorageProtocol.BOTH,
      includeContent: true,
      ...options
    };
    
    try {
      // Erstelle JSON-Objekt
      const publicationData = this.toJSON(saveOptions.includeContent);
      
      // Speichere die Daten
      const result = await storageService.storeData(publicationData, {
        protocol: saveOptions.protocol,
        filename: `publication-${this.id}.json`,
        metadata: {
          type: 'publication',
          title: this.title,
          authors: this.authors.join(', '),
          doi: this.doi,
          version: this.version
        }
      });
      
      if (!result.success) {
        throw new Error(`Speicherfehler: ${result.error}`);
      }
      
      // Aktualisiere Speicherinformationen
      this.storage = {
        ipfs: result.urls.ipfs ? result.id.ipfs || result.id : null,
        bittorrent: result.urls.bittorrent ? result.id.bittorrent || null : null,
        timestamp: new Date().toISOString()
      };
      
      // Aktualisiere Versionsverlauf, wenn der Status "published" ist
      if (this.status === 'published') {
        this.versionHistory.push({
          version: this.version,
          timestamp: this.storage.timestamp,
          storage: { ...this.storage }
        });
      }
      
      return {
        success: true,
        id: this.id,
        storage: this.storage,
        urls: result.urls
      };
    } catch (error) {
      console.error('Fehler beim Speichern der Publikation:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * Speichert eine zusätzliche Datei für die Publikation
   * @param {Buffer|string} fileData Dateiinhalt
   * @param {Object} fileInfo Dateiinformationen
   * @param {string} fileInfo.name Dateiname
   * @param {string} fileInfo.type MIME-Typ der Datei
   * @param {number} fileInfo.size Dateigröße in Bytes
   * @param {StorageService} storageService Initialisierter Storage-Service
   * @param {StorageProtocol} protocol Zu verwendendes Protokoll
   * @returns {Promise<Object>} Speicherergebnis
   */
  async saveFile(fileData, fileInfo, storageService, protocol = StorageProtocol.BOTH) {
    try {
      // Konvertiere Daten in Buffer, falls nötig
      const buffer = toBuffer(fileData);
      
      // Speichere die Datei
      const result = await storageService.storeData(buffer, {
        protocol,
        filename: fileInfo.name,
        metadata: {
          type: 'publication-file',
          publicationId: this.id,
          fileType: fileInfo.type,
          fileSize: fileInfo.size,
          description: `Datei für Publikation: ${this.title}`
        }
      });
      
      if (!result.success) {
        throw new Error(`Dateispeicherfehler: ${result.error}`);
      }
      
      // Aktualisiere Dateiinformationen
      this.file = {
        name: fileInfo.name,
        type: fileInfo.type,
        size: fileInfo.size,
        storage: {
          ipfs: result.urls.ipfs ? result.id.ipfs || result.id : null,
          bittorrent: result.urls.bittorrent ? result.id.bittorrent || null : null,
          timestamp: new Date().toISOString()
        }
      };
      
      return {
        success: true,
        file: this.file,
        urls: result.urls
      };
    } catch (error) {
      console.error('Fehler beim Speichern der Publikationsdatei:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * Lädt eine Publikation aus dem Speicher
   * @param {string|Object} id IPFS-CID, BitTorrent-Infohash oder Objekt mit beiden
   * @param {StorageService} storageService Initialisierter Storage-Service
   * @param {StorageProtocol} protocol Zu verwendendes Protokoll
   * @returns {Promise<Publication>} Geladene Publikation
   */
  static async load(id, storageService, protocol = StorageProtocol.BOTH) {
    try {
      // Lade die Daten
      const result = await storageService.retrieveData(id, {
        protocol,
        format: 'json'
      });
      
      if (!result.success) {
        throw new Error(`Ladefehler: ${result.error}`);
      }
      
      // Erstelle eine neue Publikation aus den geladenen Daten
      return new Publication(result.data);
    } catch (error) {
      console.error('Fehler beim Laden der Publikation:', error);
      throw error;
    }
  }
  
  /**
   * Lädt eine Datei einer Publikation
   * @param {Object} fileStorage Speicherinformationen der Datei
   * @param {StorageService} storageService Initialisierter Storage-Service
   * @param {string} format Rückgabeformat ('buffer', 'text', etc.)
   * @returns {Promise<Object>} Geladene Datei
   */
  static async loadFile(fileStorage, storageService, format = 'buffer') {
    try {
      // Bestimme die zu verwendende ID
      let id;
      let protocol;
      
      if (fileStorage.ipfs) {
        id = fileStorage.ipfs;
        protocol = StorageProtocol.IPFS;
      } else if (fileStorage.bittorrent) {
        id = fileStorage.bittorrent;
        protocol = StorageProtocol.BITTORRENT;
      } else {
        throw new Error('Keine gültige Speicher-ID gefunden');
      }
      
      // Lade die Datei
      const result = await storageService.retrieveData(id, {
        protocol,
        format
      });
      
      if (!result.success) {
        throw new Error(`Dateiladefehler: ${result.error}`);
      }
      
      return {
        success: true,
        data: result.data,
        format,
        metadata: result.metadata
      };
    } catch (error) {
      console.error('Fehler beim Laden der Publikationsdatei:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * Veröffentlicht eine Publikation
   * @param {StorageService} storageService Initialisierter Storage-Service
   * @returns {Promise<Object>} Veröffentlichungsergebnis
   */
  async publish(storageService) {
    // Setze Status auf "published"
    this.status = 'published';
    
    // Speichere die aktualisierte Publikation
    return this.save(storageService);
  }
  
  /**
   * Erstellt eine neue Version der Publikation
   * @param {Object} updates Zu aktualisierende Felder
   * @returns {Publication} Neue Version der Publikation
   */
  createNewVersion(updates = {}) {
    // Speichere aktuelle Version in der Versionshistorie
    this.versionHistory.push({
      version: this.version,
      timestamp: new Date().toISOString(),
      storage: { ...this.storage }
    });
    
    // Inkrementiere Versionsnummer
    const versionParts = this.version.split('.');
    versionParts[versionParts.length - 1] = (parseInt(versionParts[versionParts.length - 1]) + 1).toString();
    this.version = versionParts.join('.');
    
    // Aktualisiere Felder
    Object.keys(updates).forEach(key => {
      if (this.hasOwnProperty(key)) {
        this[key] = updates[key];
      }
    });
    
    // Setze Speicherinformationen zurück
    this.storage = {
      ipfs: null,
      bittorrent: null,
      timestamp: null
    };
    
    return this;
  }
}

export default Publication;
