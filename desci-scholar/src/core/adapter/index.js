/**
 * @fileoverview Adapter-System für DeSci-Scholar
 * 
 * Dieses Modul exportiert alle Komponenten des Adapter-Systems.
 */

import { AdapterRegistry, getAdapterRegistry } from './AdapterRegistry.js';
import { Adapter<PERSON>oader, getAdapterLoader } from './AdapterLoader.js';
import { BaseAdapter } from './BaseAdapter.js';

/**
 * Initialisiert das Adapter-System
 * @param {Object} options - Konfigurationsoptionen
 * @returns {Promise<Object>} Initialisiertes Adapter-System
 */
export async function initializeAdapterSystem(options = {}) {
  // Erstelle und initialisiere die Registry
  const registry = getAdapterRegistry(options.registry || {});
  await registry.initialize();
  
  // Erstelle und initialisiere den Loader
  const loader = getAdapterLoader({
    ...options.loader,
    registry
  });
  await loader.initialize();
  
  // Lade alle Adapter
  await loader.loadAllAdapters();
  
  return {
    registry,
    loader
  };
}

export {
  AdapterRegistry,
  getAdapterRegistry,
  AdapterLoader,
  getAdapterLoader,
  BaseAdapter
};
