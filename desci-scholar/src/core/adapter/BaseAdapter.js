/**
 * @fileoverview Basis-Adapter-Klasse
 * 
 * Diese K<PERSON>e dient als Basis für alle Adapter im System.
 * Sie definiert die grundlegende Schnittstelle und Funktionalität, die alle Adapter implementieren sollten.
 */

import { LoggerFactory } from '../../utils/logger.js';

const logger = LoggerFactory.createLogger('BaseAdapter');

/**
 * Basis-Adapter-Klasse
 */
export class BaseAdapter {
  /**
   * Erstellt eine neue Instanz des BaseAdapter
   * @param {Object} options - Konfigurationsoptionen
   */
  constructor(options = {}) {
    this.options = {
      ...options
    };
    
    this.initialized = false;
    this.name = this.constructor.name;
    this.category = this._deriveCategory();
    
    // Erstelle einen Logger für diesen Adapter
    this.logger = LoggerFactory.createLogger(`${this.category}-${this.name}`);
    
    this.logger.debug(`${this.name} initialisiert`);
  }
  
  /**
   * Initialisiert den Adapter
   * @returns {Promise<boolean>} Initialisierungsstatus
   */
  async initialize() {
    if (this.initialized) {
      this.logger.debug(`${this.name} bereits initialisiert`);
      return true;
    }
    
    try {
      this.logger.debug(`Initialisiere ${this.name}`);
      
      // Hier können abgeleitete Klassen ihre Initialisierungslogik implementieren
      
      this.initialized = true;
      this.logger.debug(`${this.name} erfolgreich initialisiert`);
      return true;
    } catch (error) {
      this.logger.error(`Fehler bei der Initialisierung von ${this.name}`, {
        error: error.message,
        stack: error.stack
      });
      return false;
    }
  }
  
  /**
   * Fährt den Adapter herunter
   * @returns {Promise<boolean>} Herunterfahrstatus
   */
  async shutdown() {
    if (!this.initialized) {
      this.logger.debug(`${this.name} nicht initialisiert, kein Herunterfahren erforderlich`);
      return true;
    }
    
    try {
      this.logger.debug(`Fahre ${this.name} herunter`);
      
      // Hier können abgeleitete Klassen ihre Herunterfahrlogik implementieren
      
      this.initialized = false;
      this.logger.debug(`${this.name} erfolgreich heruntergefahren`);
      return true;
    } catch (error) {
      this.logger.error(`Fehler beim Herunterfahren von ${this.name}`, {
        error: error.message,
        stack: error.stack
      });
      return false;
    }
  }
  
  /**
   * Prüft, ob der Adapter initialisiert ist
   * @returns {boolean} Initialisierungsstatus
   */
  isInitialized() {
    return this.initialized;
  }
  
  /**
   * Gibt die Kategorie des Adapters zurück
   * @returns {string} Kategorie
   */
  getCategory() {
    return this.category;
  }
  
  /**
   * Gibt den Namen des Adapters zurück
   * @returns {string} Name
   */
  getName() {
    return this.name.replace(/Adapter$/, '');
  }
  
  /**
   * Gibt die Funktionen zurück, die dieser Adapter unterstützt
   * @returns {Array<string>} Unterstützte Funktionen
   */
  getCapabilities() {
    // Abgeleitete Klassen sollten diese Methode überschreiben
    return [];
  }
  
  /**
   * Prüft, ob der Adapter eine bestimmte Funktion unterstützt
   * @param {string} capability - Zu prüfende Funktion
   * @returns {boolean} Ob die Funktion unterstützt wird
   */
  supportsCapability(capability) {
    return this.getCapabilities().includes(capability);
  }
  
  /**
   * Leitet die Kategorie aus dem Klassennamen ab
   * @returns {string} Abgeleitete Kategorie
   * @private
   */
  _deriveCategory() {
    // Versuche, die Kategorie aus dem Klassennamen abzuleiten
    // z.B. StorageAdapter -> storage, BlockchainAdapter -> blockchain
    const name = this.constructor.name;
    
    // Spezielle Fälle
    if (name.includes('Storage') || name.includes('IPFS') || name.includes('Torrent')) {
      return 'storage';
    }
    
    if (name.includes('Blockchain') || name.includes('Ethereum') || name.includes('Polkadot')) {
      return 'blockchain';
    }
    
    if (name.includes('Wallet') || name.includes('MetaMask') || name.includes('Ledger')) {
      return 'wallet';
    }
    
    if (name.includes('Payment') || name.includes('Stripe') || name.includes('PayPal')) {
      return 'payment';
    }
    
    if (name.includes('AI') || name.includes('ML') || name.includes('Model')) {
      return 'ai';
    }
    
    // Allgemeiner Fall: Entferne "Adapter" und konvertiere zu Kleinbuchstaben
    return name.replace(/Adapter$/, '').toLowerCase();
  }
  
  /**
   * Gibt Metadaten über den Adapter zurück
   * @returns {Object} Adapter-Metadaten
   */
  getMetadata() {
    return {
      name: this.getName(),
      category: this.getCategory(),
      capabilities: this.getCapabilities(),
      initialized: this.initialized
    };
  }
  
  /**
   * Validiert Optionen gegen ein Schema
   * @param {Object} options - Zu validierende Optionen
   * @param {Object} schema - Validierungsschema
   * @returns {boolean} Validierungsergebnis
   * @protected
   */
  _validateOptions(options, schema) {
    // Einfache Schemavalidierung
    for (const [key, config] of Object.entries(schema)) {
      if (config.required && (options[key] === undefined || options[key] === null)) {
        this.logger.error(`Erforderliche Option ${key} fehlt`);
        return false;
      }
      
      if (options[key] !== undefined && config.type && typeof options[key] !== config.type) {
        this.logger.error(`Option ${key} hat falschen Typ, erwartet ${config.type}, erhalten ${typeof options[key]}`);
        return false;
      }
    }
    
    return true;
  }
}

export default BaseAdapter;
