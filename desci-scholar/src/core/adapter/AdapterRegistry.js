/**
 * @fileoverview Zentrale Registry für alle Adapter im System
 * 
 * Diese Klasse implementiert eine zentrale Registry für alle Adapter im System.
 * Sie ermöglicht die dynamische Registrierung, Erkennung und Auswahl von Adaptern.
 */

import { LoggerFactory } from '../../utils/logger.js';
import fs from 'fs/promises';
import path from 'path';

const logger = LoggerFactory.createLogger('AdapterRegistry');

/**
 * Zentrale Registry für alle Adapter im System
 */
export class AdapterRegistry {
  /**
   * Erstellt eine neue Instanz der AdapterRegistry
   * @param {Object} options - Konfigurationsoptionen
   */
  constructor(options = {}) {
    this.options = {
      adapterPaths: options.adapterPaths || [],
      autoDiscovery: options.autoDiscovery !== false,
      defaultAdapters: options.defaultAdapters || {},
      ...options
    };
    
    // Map für registrierte Adapter-Klassen
    this.adapterClasses = new Map();
    
    // Map für instanziierte Adapter
    this.adapterInstances = new Map();
    
    // Map für Standard-Adapter pro Kategorie
    this.defaultAdapters = new Map();
    
    // Initialisiere Standard-Adapter aus den Optionen
    Object.entries(this.options.defaultAdapters).forEach(([category, adapter]) => {
      this.defaultAdapters.set(category, adapter);
    });
    
    logger.info('AdapterRegistry initialisiert');
  }
  
  /**
   * Initialisiert die AdapterRegistry
   * @returns {Promise<boolean>} Initialisierungsstatus
   */
  async initialize() {
    try {
      logger.info('Initialisiere AdapterRegistry');
      
      // Führe automatische Erkennung durch, falls aktiviert
      if (this.options.autoDiscovery) {
        await this.discoverAdapters();
      }
      
      logger.info('AdapterRegistry erfolgreich initialisiert');
      return true;
    } catch (error) {
      logger.error('Fehler bei der Initialisierung der AdapterRegistry', {
        error: error.message,
        stack: error.stack
      });
      return false;
    }
  }
  
  /**
   * Registriert eine Adapter-Klasse
   * @param {string} category - Kategorie des Adapters (z.B. 'blockchain', 'storage')
   * @param {string} name - Name des Adapters
   * @param {Class} AdapterClass - Adapter-Klasse
   * @param {boolean} isDefault - Ob dieser Adapter der Standard für seine Kategorie ist
   * @returns {boolean} Registrierungsstatus
   */
  registerAdapterClass(category, name, AdapterClass, isDefault = false) {
    try {
      const key = `${category}:${name}`;
      
      // Prüfe, ob die Klasse bereits registriert ist
      if (this.adapterClasses.has(key)) {
        logger.warn(`Adapter-Klasse ${key} ist bereits registriert`);
        return false;
      }
      
      // Registriere die Klasse
      this.adapterClasses.set(key, AdapterClass);
      
      // Setze als Standard-Adapter, falls gewünscht
      if (isDefault) {
        this.defaultAdapters.set(category, name);
        logger.debug(`${name} als Standard-Adapter für ${category} gesetzt`);
      }
      
      logger.debug(`Adapter-Klasse ${key} erfolgreich registriert`);
      return true;
    } catch (error) {
      logger.error(`Fehler bei der Registrierung der Adapter-Klasse ${category}:${name}`, {
        error: error.message
      });
      return false;
    }
  }
  
  /**
   * Registriert eine Adapter-Instanz
   * @param {string} category - Kategorie des Adapters
   * @param {string} name - Name des Adapters
   * @param {Object} adapter - Adapter-Instanz
   * @param {boolean} isDefault - Ob dieser Adapter der Standard für seine Kategorie ist
   * @returns {boolean} Registrierungsstatus
   */
  registerAdapterInstance(category, name, adapter, isDefault = false) {
    try {
      const key = `${category}:${name}`;
      
      // Registriere die Instanz
      this.adapterInstances.set(key, adapter);
      
      // Setze als Standard-Adapter, falls gewünscht
      if (isDefault) {
        this.defaultAdapters.set(category, name);
        logger.debug(`${name} als Standard-Adapter für ${category} gesetzt`);
      }
      
      logger.debug(`Adapter-Instanz ${key} erfolgreich registriert`);
      return true;
    } catch (error) {
      logger.error(`Fehler bei der Registrierung der Adapter-Instanz ${category}:${name}`, {
        error: error.message
      });
      return false;
    }
  }
  
  /**
   * Gibt eine Adapter-Instanz zurück
   * @param {string} category - Kategorie des Adapters
   * @param {string} name - Name des Adapters (optional, verwendet Standard-Adapter wenn nicht angegeben)
   * @param {Object} options - Konfigurationsoptionen für den Adapter
   * @returns {Object} Adapter-Instanz
   */
  getAdapter(category, name, options = {}) {
    try {
      // Verwende Standard-Adapter, wenn kein Name angegeben ist
      if (!name) {
        name = this.defaultAdapters.get(category);
        if (!name) {
          throw new Error(`Kein Standard-Adapter für Kategorie ${category} definiert`);
        }
      }
      
      const key = `${category}:${name}`;
      
      // Prüfe, ob eine Instanz bereits existiert
      if (this.adapterInstances.has(key)) {
        return this.adapterInstances.get(key);
      }
      
      // Prüfe, ob die Klasse registriert ist
      if (!this.adapterClasses.has(key)) {
        throw new Error(`Adapter-Klasse ${key} nicht gefunden`);
      }
      
      // Erstelle eine neue Instanz
      const AdapterClass = this.adapterClasses.get(key);
      const adapter = new AdapterClass(options);
      
      // Registriere die Instanz
      this.adapterInstances.set(key, adapter);
      
      return adapter;
    } catch (error) {
      logger.error(`Fehler beim Abrufen des Adapters ${category}:${name}`, {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Entdeckt Adapter in den konfigurierten Pfaden
   * @returns {Promise<number>} Anzahl der entdeckten Adapter
   */
  async discoverAdapters() {
    try {
      logger.info('Starte automatische Adapter-Erkennung');
      
      let discoveredCount = 0;
      
      // Durchsuche alle konfigurierten Pfade
      for (const adapterPath of this.options.adapterPaths) {
        const files = await fs.readdir(adapterPath);
        
        // Filtere nach Adapter-Dateien
        const adapterFiles = files.filter(file => 
          file.endsWith('Adapter.js') || 
          file.endsWith('Adapter.mjs')
        );
        
        // Importiere und registriere jeden Adapter
        for (const file of adapterFiles) {
          try {
            const filePath = path.join(adapterPath, file);
            const module = await import(filePath);
            
            // Extrahiere Kategorie und Namen aus dem Dateinamen
            const baseName = path.basename(file, path.extname(file));
            const category = this._getCategoryFromPath(adapterPath);
            const name = baseName.replace(/Adapter$/, '').toLowerCase();
            
            // Finde die Adapter-Klasse im Modul
            const AdapterClass = this._findAdapterClassInModule(module, baseName);
            
            if (AdapterClass) {
              // Registriere die Adapter-Klasse
              const isDefault = this._isDefaultAdapter(category, name);
              this.registerAdapterClass(category, name, AdapterClass, isDefault);
              discoveredCount++;
            }
          } catch (importError) {
            logger.warn(`Fehler beim Importieren des Adapters ${file}`, {
              error: importError.message
            });
          }
        }
      }
      
      logger.info(`${discoveredCount} Adapter automatisch erkannt und registriert`);
      return discoveredCount;
    } catch (error) {
      logger.error('Fehler bei der automatischen Adapter-Erkennung', {
        error: error.message
      });
      return 0;
    }
  }
  
  /**
   * Extrahiert die Kategorie aus dem Pfad
   * @param {string} adapterPath - Pfad zum Adapter
   * @returns {string} Kategorie
   * @private
   */
  _getCategoryFromPath(adapterPath) {
    const parts = adapterPath.split(path.sep);
    return parts[parts.length - 1].toLowerCase();
  }
  
  /**
   * Findet die Adapter-Klasse in einem Modul
   * @param {Object} module - Importiertes Modul
   * @param {string} expectedName - Erwarteter Name der Klasse
   * @returns {Class|null} Gefundene Adapter-Klasse oder null
   * @private
   */
  _findAdapterClassInModule(module, expectedName) {
    // Prüfe auf direkte Export-Eigenschaft
    if (module[expectedName]) {
      return module[expectedName];
    }
    
    // Prüfe auf default export
    if (module.default && (
      module.default.name === expectedName || 
      expectedName.includes(module.default.name)
    )) {
      return module.default;
    }
    
    // Durchsuche alle Exports
    for (const [key, value] of Object.entries(module)) {
      if (typeof value === 'function' && (
        key === expectedName || 
        expectedName.includes(key)
      )) {
        return value;
      }
    }
    
    return null;
  }
  
  /**
   * Prüft, ob ein Adapter der Standard-Adapter für seine Kategorie ist
   * @param {string} category - Kategorie des Adapters
   * @param {string} name - Name des Adapters
   * @returns {boolean} Ob der Adapter der Standard ist
   * @private
   */
  _isDefaultAdapter(category, name) {
    // Prüfe, ob in den Optionen definiert
    if (this.options.defaultAdapters[category] === name) {
      return true;
    }
    
    // Wenn noch kein Standard definiert ist, setze den ersten als Standard
    if (!this.defaultAdapters.has(category)) {
      return true;
    }
    
    return false;
  }
  
  /**
   * Gibt alle registrierten Adapter zurück
   * @returns {Object} Registrierte Adapter nach Kategorie
   */
  getAllAdapters() {
    const result = {};
    
    // Gruppiere nach Kategorie
    for (const [key, AdapterClass] of this.adapterClasses.entries()) {
      const [category, name] = key.split(':');
      
      if (!result[category]) {
        result[category] = [];
      }
      
      result[category].push({
        name,
        isDefault: this.defaultAdapters.get(category) === name
      });
    }
    
    return result;
  }
  
  /**
   * Gibt den Standard-Adapter für eine Kategorie zurück
   * @param {string} category - Kategorie des Adapters
   * @returns {string|null} Name des Standard-Adapters oder null
   */
  getDefaultAdapter(category) {
    return this.defaultAdapters.get(category) || null;
  }
  
  /**
   * Setzt den Standard-Adapter für eine Kategorie
   * @param {string} category - Kategorie des Adapters
   * @param {string} name - Name des Adapters
   * @returns {boolean} Erfolgsstatus
   */
  setDefaultAdapter(category, name) {
    try {
      const key = `${category}:${name}`;
      
      // Prüfe, ob der Adapter existiert
      if (!this.adapterClasses.has(key) && !this.adapterInstances.has(key)) {
        throw new Error(`Adapter ${key} nicht gefunden`);
      }
      
      // Setze als Standard
      this.defaultAdapters.set(category, name);
      
      logger.info(`${name} als Standard-Adapter für ${category} gesetzt`);
      return true;
    } catch (error) {
      logger.error(`Fehler beim Setzen des Standard-Adapters ${category}:${name}`, {
        error: error.message
      });
      return false;
    }
  }
}

// Singleton-Instanz
let instance = null;

/**
 * Gibt die Singleton-Instanz der AdapterRegistry zurück
 * @param {Object} options - Konfigurationsoptionen (nur beim ersten Aufruf verwendet)
 * @returns {AdapterRegistry} AdapterRegistry-Instanz
 */
export function getAdapterRegistry(options = {}) {
  if (!instance) {
    instance = new AdapterRegistry(options);
  }
  return instance;
}

export default {
  AdapterRegistry,
  getAdapterRegistry
};
