/**
 * @fileoverview Dynamischer Adapter-Loader
 * 
 * Diese Klasse implementiert einen dynamischen Loader für Adapter.
 * Sie ermöglicht das Laden von Adaptern zur Laufzeit aus verschiedenen Quellen.
 */

import { LoggerFactory } from '../../utils/logger.js';
import fs from 'fs/promises';
import path from 'path';
import { getAdapterRegistry } from './AdapterRegistry.js';

const logger = LoggerFactory.createLogger('AdapterLoader');

/**
 * Dynamischer Adapter-Loader
 */
export class AdapterLoader {
  /**
   * Erstellt eine neue Instanz des AdapterLoader
   * @param {Object} options - Konfigurationsoptionen
   */
  constructor(options = {}) {
    this.options = {
      basePaths: options.basePaths || [
        path.resolve(process.cwd(), 'src/services'),
        path.resolve(process.cwd(), 'src/adapters'),
        path.resolve(process.cwd(), 'node_modules')
      ],
      adapterPattern: options.adapterPattern || '*Adapter.js',
      externalModules: options.externalModules || [],
      registry: options.registry || getAdapterRegistry(),
      ...options
    };
    
    logger.info('AdapterLoader initialisiert');
  }
  
  /**
   * Initialisiert den AdapterLoader
   * @returns {Promise<boolean>} Initialisierungsstatus
   */
  async initialize() {
    try {
      logger.info('Initialisiere AdapterLoader');
      
      // Stelle sicher, dass die Registry initialisiert ist
      if (!this.options.registry.initialized) {
        await this.options.registry.initialize();
      }
      
      logger.info('AdapterLoader erfolgreich initialisiert');
      return true;
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des AdapterLoader', {
        error: error.message,
        stack: error.stack
      });
      return false;
    }
  }
  
  /**
   * Lädt Adapter aus einem Verzeichnis
   * @param {string} directory - Verzeichnispfad
   * @param {string} category - Kategorie der Adapter
   * @returns {Promise<number>} Anzahl der geladenen Adapter
   */
  async loadAdaptersFromDirectory(directory, category) {
    try {
      logger.info(`Lade Adapter aus Verzeichnis: ${directory}, Kategorie: ${category}`);
      
      // Prüfe, ob das Verzeichnis existiert
      try {
        await fs.access(directory);
      } catch (accessError) {
        logger.warn(`Verzeichnis ${directory} existiert nicht oder ist nicht zugänglich`);
        return 0;
      }
      
      // Lese Verzeichnisinhalt
      const files = await fs.readdir(directory);
      
      // Filtere nach Adapter-Dateien
      const adapterFiles = files.filter(file => 
        file.endsWith('Adapter.js') || 
        file.endsWith('Adapter.mjs')
      );
      
      let loadedCount = 0;
      
      // Lade jeden Adapter
      for (const file of adapterFiles) {
        try {
          const filePath = path.join(directory, file);
          const module = await import(filePath);
          
          // Extrahiere Namen aus dem Dateinamen
          const baseName = path.basename(file, path.extname(file));
          const name = baseName.replace(/Adapter$/, '').toLowerCase();
          
          // Finde die Adapter-Klasse im Modul
          const AdapterClass = this._findAdapterClassInModule(module, baseName);
          
          if (AdapterClass) {
            // Registriere die Adapter-Klasse
            const isDefault = this._isFirstAdapterInCategory(category);
            this.options.registry.registerAdapterClass(category, name, AdapterClass, isDefault);
            loadedCount++;
            
            logger.debug(`Adapter ${category}:${name} erfolgreich geladen`);
          } else {
            logger.warn(`Keine Adapter-Klasse in ${file} gefunden`);
          }
        } catch (importError) {
          logger.warn(`Fehler beim Importieren des Adapters ${file}`, {
            error: importError.message
          });
        }
      }
      
      logger.info(`${loadedCount} Adapter aus ${directory} geladen`);
      return loadedCount;
    } catch (error) {
      logger.error(`Fehler beim Laden von Adaptern aus ${directory}`, {
        error: error.message
      });
      return 0;
    }
  }
  
  /**
   * Lädt Adapter aus einem npm-Modul
   * @param {string} moduleName - Name des npm-Moduls
   * @param {string} category - Kategorie der Adapter
   * @returns {Promise<number>} Anzahl der geladenen Adapter
   */
  async loadAdaptersFromModule(moduleName, category) {
    try {
      logger.info(`Lade Adapter aus Modul: ${moduleName}, Kategorie: ${category}`);
      
      // Importiere das Modul
      const module = await import(moduleName);
      
      let loadedCount = 0;
      
      // Durchsuche das Modul nach Adapter-Klassen
      for (const [key, value] of Object.entries(module)) {
        if (typeof value === 'function' && key.endsWith('Adapter')) {
          const name = key.replace(/Adapter$/, '').toLowerCase();
          
          // Registriere die Adapter-Klasse
          const isDefault = this._isFirstAdapterInCategory(category);
          this.options.registry.registerAdapterClass(category, name, value, isDefault);
          loadedCount++;
          
          logger.debug(`Adapter ${category}:${name} aus Modul ${moduleName} erfolgreich geladen`);
        }
      }
      
      logger.info(`${loadedCount} Adapter aus Modul ${moduleName} geladen`);
      return loadedCount;
    } catch (error) {
      logger.error(`Fehler beim Laden von Adaptern aus Modul ${moduleName}`, {
        error: error.message
      });
      return 0;
    }
  }
  
  /**
   * Lädt alle Adapter aus allen konfigurierten Quellen
   * @returns {Promise<number>} Anzahl der geladenen Adapter
   */
  async loadAllAdapters() {
    try {
      logger.info('Lade alle Adapter');
      
      let totalLoaded = 0;
      
      // Lade Adapter aus Verzeichnissen
      for (const basePath of this.options.basePaths) {
        // Suche nach Kategorie-Verzeichnissen
        try {
          const directories = await fs.readdir(basePath);
          
          for (const dir of directories) {
            const fullPath = path.join(basePath, dir);
            
            // Prüfe, ob es ein Verzeichnis ist
            const stats = await fs.stat(fullPath);
            if (stats.isDirectory()) {
              // Verwende den Verzeichnisnamen als Kategorie
              const category = dir.toLowerCase();
              const loaded = await this.loadAdaptersFromDirectory(fullPath, category);
              totalLoaded += loaded;
            }
          }
        } catch (dirError) {
          logger.warn(`Fehler beim Lesen des Verzeichnisses ${basePath}`, {
            error: dirError.message
          });
        }
      }
      
      // Lade Adapter aus externen Modulen
      for (const moduleConfig of this.options.externalModules) {
        const { name, category } = moduleConfig;
        const loaded = await this.loadAdaptersFromModule(name, category);
        totalLoaded += loaded;
      }
      
      logger.info(`Insgesamt ${totalLoaded} Adapter geladen`);
      return totalLoaded;
    } catch (error) {
      logger.error('Fehler beim Laden aller Adapter', {
        error: error.message
      });
      return 0;
    }
  }
  
  /**
   * Findet die Adapter-Klasse in einem Modul
   * @param {Object} module - Importiertes Modul
   * @param {string} expectedName - Erwarteter Name der Klasse
   * @returns {Class|null} Gefundene Adapter-Klasse oder null
   * @private
   */
  _findAdapterClassInModule(module, expectedName) {
    // Prüfe auf direkte Export-Eigenschaft
    if (module[expectedName]) {
      return module[expectedName];
    }
    
    // Prüfe auf default export
    if (module.default && (
      module.default.name === expectedName || 
      expectedName.includes(module.default.name)
    )) {
      return module.default;
    }
    
    // Durchsuche alle Exports
    for (const [key, value] of Object.entries(module)) {
      if (typeof value === 'function' && (
        key === expectedName || 
        expectedName.includes(key)
      )) {
        return value;
      }
    }
    
    return null;
  }
  
  /**
   * Prüft, ob dies der erste Adapter in einer Kategorie ist
   * @param {string} category - Kategorie
   * @returns {boolean} Ob dies der erste Adapter ist
   * @private
   */
  _isFirstAdapterInCategory(category) {
    return !this.options.registry.getDefaultAdapter(category);
  }
}

// Singleton-Instanz
let instance = null;

/**
 * Gibt die Singleton-Instanz des AdapterLoader zurück
 * @param {Object} options - Konfigurationsoptionen (nur beim ersten Aufruf verwendet)
 * @returns {AdapterLoader} AdapterLoader-Instanz
 */
export function getAdapterLoader(options = {}) {
  if (!instance) {
    instance = new AdapterLoader(options);
  }
  return instance;
}

export default {
  AdapterLoader,
  getAdapterLoader
};
