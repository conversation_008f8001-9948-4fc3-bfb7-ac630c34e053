/**
 * @fileoverview Zentrale Klasse für die Verarbeitung von DOIs zu NFTs
 * 
 * Diese Klasse implementiert den Kernprozess des DeSci-Scholar-Projekts:
 * Die Umwandlung von wissenschaftlichen Publikationen mit DOIs in NFTs.
 */

import { LoggerFactory } from '../blockchain/polkadot-improved/LoggerFactory.js';
import { StorageService } from '../storage/StorageService.js';
import { DoiService } from '../doi/DoiService.js';
import { NftService } from '../blockchain/NftService.js';
import { MetadataManager } from '../metadata/MetadataManager.js';

const logger = LoggerFactory.createLogger('DoiToNftProcessor');

class DoiToNftProcessor {
  constructor(options = {}) {
    const {
      storageService,
      doiService,
      nftService,
      metadataManager,
      publicationSourceManager
    } = options;
    
    this.storageService = storageService || new StorageService(options.storageConfig);
    this.doiService = doiService || new DoiService(options.doiConfig);
    this.nftService = nftService || new NftService(options.nftConfig);
    this.metadataManager = metadataManager || new MetadataManager(options.metadataConfig);
    this.publicationSourceManager = publicationSourceManager;
    
    this.initialized = false;
  }
  
  /**
   * Initialisiert alle erforderlichen Dienste
   */
  async initialize() {
    if (this.initialized) return true;
    
    try {
      await this.storageService.initialize();
      await this.doiService.initialize();
      await this.nftService.initialize();
      await this.metadataManager.initialize();
      
      if (this.publicationSourceManager) {
        await this.publicationSourceManager.initialize();
      }
      
      this.initialized = true;
      logger.info('DoiToNftProcessor erfolgreich initialisiert');
      return true;
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des DoiToNftProcessor', {
        error: error.message,
        stack: error.stack
      });
      return false;
    }
  }
  
  /**
   * Verarbeitet eine Publikation von DOI zu NFT
   * 
   * @param {Object} publication - Publikationsdaten
   * @param {Object} options - Verarbeitungsoptionen
   * @returns {Object} Ergebnis der Verarbeitung
   */
  async processPublication(publication, options = {}) {
    if (!this.initialized) await this.initialize();
    
    const {
      validateDoi = true,
      createDoi = false,
      mintNft = true,
      storeMetadata = true,
      storeFulltext = false,
      nftMetadata = {},
      royaltyPercentage = 0
    } = options;
    
    logger.info('Starte Verarbeitung von Publikation zu NFT', {
      publicationId: publication.id,
      doi: publication.doi,
      options
    });
    
    const result = {
      publication: publication,
      steps: {},
      success: false
    };
    
    try {
      // Schritt 1: DOI validieren oder erstellen
      if (publication.doi && validateDoi) {
        result.steps.doiValidation = await this.doiService.validateDoi(publication.doi);
      } else if (createDoi) {
        result.steps.doiCreation = await this.doiService.createDoi(publication);
        publication.doi = result.steps.doiCreation.doi;
      }
      
      // Schritt 2: Metadaten aufbereiten und speichern
      if (storeMetadata) {
        const enrichedMetadata = await this.metadataManager.enrichMetadata(publication);
        result.steps.metadataStorage = await this.storageService.storeMetadata(
          enrichedMetadata,
          { type: 'publication', id: publication.id }
        );
        publication.metadataUri = result.steps.metadataStorage.uri;
      }
      
      // Schritt 3: Volltext speichern (falls vorhanden und gewünscht)
      if (storeFulltext && publication.fulltext) {
        result.steps.fulltextStorage = await this.storageService.storeFile(
          publication.fulltext,
          { 
            filename: `${publication.id}.pdf`,
            metadata: { type: 'publication', id: publication.id }
          }
        );
        publication.fulltextUri = result.steps.fulltextStorage.uri;
      }
      
      // Schritt 4: NFT prägen
      if (mintNft) {
        const nftData = {
          title: publication.title,
          description: publication.abstract || publication.description,
          doi: publication.doi,
          metadataUri: publication.metadataUri,
          contentUri: publication.fulltextUri,
          creators: publication.authors.map(author => ({
            name: author.name,
            orcid: author.orcid
          })),
          ...nftMetadata
        };
        
        result.steps.nftMinting = await this.nftService.mintNft(nftData, {
          royaltyPercentage
        });
        
        publication.nftId = result.steps.nftMinting.tokenId;
        publication.nftContract = result.steps.nftMinting.contractAddress;
        publication.nftUri = result.steps.nftMinting.tokenUri;
      }
      
      // Erfolg!
      result.success = true;
      logger.info('Publikation erfolgreich zu NFT verarbeitet', {
        publicationId: publication.id,
        doi: publication.doi,
        nftId: publication.nftId
      });
      
      return result;
    } catch (error) {
      logger.error('Fehler bei der Verarbeitung von Publikation zu NFT', {
        error: error.message,
        stack: error.stack,
        publicationId: publication.id
      });
      
      result.error = {
        message: error.message,
        step: Object.keys(result.steps).pop() || 'unknown'
      };
      
      return result;
    }
  }
  
  /**
   * Verarbeitet eine Publikation anhand ihrer Quelle und ID
   * 
   * @param {string} source - Quellname (arxiv, zotero, etc.)
   * @param {string} id - Publikations-ID
   * @param {Object} options - Verarbeitungsoptionen
   */
  async processPublicationById(source, id, options = {}) {
    if (!this.publicationSourceManager) {
      throw new Error('PublicationSourceManager nicht konfiguriert');
    }
    
    const publication = await this.publicationSourceManager.getPublication(source, id);
    if (!publication) {
      throw new Error(`Publikation nicht gefunden: ${source}/${id}`);
    }
    
    return this.processPublication(publication, options);
  }
}

export default DoiToNftProcessor;
