const { EventEmitter } = require('events');

/**
 * IdentityManager - Verwaltet Benutzeridentitäten und Authentifizierung
 * Unterstützt sowohl traditionelle als auch dezentrale Identitäten (DIDs)
 */
class IdentityManager extends EventEmitter {
  constructor(options = {}) {
    super();
    this.options = {
      defaultAdapter: 'standard',
      ...options
    };
    
    this.adapters = new Map();
    this.currentUser = null;
  }
  
  /**
   * Registriert einen Identitäts-Adapter
   * @param {string} name - Name des Adapters
   * @param {object} adapter - Adapter-Instanz
   */
  registerAdapter(name, adapter) {
    if (this.adapters.has(name)) {
      throw new Error(`Identitäts-Adapter "${name}" ist bereits registriert`);
    }
    
    this.adapters.set(name, adapter);
    return this;
  }
  
  /**
   * Wählt den passenden Adapter basierend auf den Optionen aus
   * @param {object} options - Optionen für die Adapter-Auswahl
   * @returns {object} Der ausgewählte Adapter
   */
  selectAdapter(options = {}) {
    // Verwende den explizit angegebenen Adapter, falls vorhanden
    if (options.adapter && this.adapters.has(options.adapter)) {
      return this.adapters.get(options.adapter);
    }
    
    // Verwende den Standard-Adapter
    return this.adapters.get(this.options.defaultAdapter);
  }
  
  /**
   * Registriert einen neuen Benutzer
   * @param {object} userData - Benutzerdaten
   * @param {object} options - Optionen für die Registrierung
   * @returns {Promise<object>} Registrierungsergebnis
   */
  async register(userData, options = {}) {
    const adapter = this.selectAdapter(options);
    const result = await adapter.register(userData);
    
    this.emit('user:registered', { userId: result.userId });
    return result;
  }
  
  /**
   * Authentifiziert einen Benutzer
   * @param {object} credentials - Anmeldedaten
   * @param {object} options - Optionen für die Authentifizierung
   * @returns {Promise<object>} Authentifizierungsergebnis
   */
  async login(credentials, options = {}) {
    const adapter = this.selectAdapter(options);
    const result = await adapter.login(credentials);
    
    this.currentUser = result.user;
    this.emit('user:login', { userId: result.user.id });
    return result;
  }
  
  /**
   * Meldet den aktuellen Benutzer ab
   * @returns {Promise<boolean>} Abmeldeergebnis
   */
  async logout() {
    if (!this.currentUser) {
      return true;
    }
    
    const userId = this.currentUser.id;
    this.currentUser = null;
    this.emit('user:logout', { userId });
    return true;
  }
  
  /**
   * Prüft, ob ein Benutzer authentifiziert ist
   * @returns {boolean} Authentifizierungsstatus
   */
  isAuthenticated() {
    return !!this.currentUser;
  }
  
  /**
   * Gibt den aktuellen Benutzer zurück
   * @returns {object|null} Aktueller Benutzer
   */
  getCurrentUser() {
    return this.currentUser;
  }
  
  /**
   * Aktualisiert Benutzerdaten
   * @param {string} userId - Benutzer-ID
   * @param {object} updates - Zu aktualisierende Daten
   * @param {object} options - Optionen für die Aktualisierung
   * @returns {Promise<object>} Aktualisierungsergebnis
   */
  async updateUser(userId, updates, options = {}) {
    const adapter = this.selectAdapter(options);
    const result = await adapter.updateUser(userId, updates);
    
    if (this.currentUser && this.currentUser.id === userId) {
      this.currentUser = { ...this.currentUser, ...updates };
    }
    
    this.emit('user:updated', { userId });
    return result;
  }
}

module.exports = IdentityManager;