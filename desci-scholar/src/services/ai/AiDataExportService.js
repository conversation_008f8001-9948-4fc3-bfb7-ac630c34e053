/**
 * AiDataExportService - Exportiert strukturierte wissenschaftliche Daten für KI-Training
 * Kernfunktionalität für den USP als KI-Datenlieferant
 */
class AiDataExportService {
  constructor(options = {}) {
    this.options = {
      publicationService: null,
      citationService: null,
      ...options
    };
    
    if (!this.options.publicationService) {
      throw new Error('publicationService ist erforderlich');
    }
  }
  
  /**
   * Exportiert einen strukturierten Datensatz für KI-Training
   * @param {object} query - Abfrageparameter
   * @param {object} options - Exportoptionen
   * @returns {Promise<object>} Exportierter Datensatz
   */
  async exportDataset(query = {}, options = {}) {
    const {
      domain,
      startDate,
      endDate,
      limit = 1000,
      format = 'json',
      includeFullText = false,
      includeReferences = true,
      includeCitations = true
    } = options;
    
    // Publikationen basierend auf den Abfrageparametern abrufen
    const publications = await this.options.publicationService.findPublications({
      ...query,
      domain,
      startDate,
      endDate,
      limit
    });
    
    // Strukturierten Datensatz erstellen
    const dataset = {
      metadata: {
        exportDate: new Date().toISOString(),
        query,
        options,
        totalCount: publications.length
      },
      data: await Promise.all(publications.map(async (publication) => {
        const item = {
          id: publication.id,
          doi: publication.doi,
          title: publication.title,
          abstract: publication.abstract,
          authors: publication.authors,
          publicationDate: publication.publicationDate,
          journal: publication.journal,
          keywords: publication.keywords,
          categories: publication.categories
        };
        
        // Volltext hinzufügen, falls angefordert
        if (includeFullText && publication.fullTextAvailable) {
          item.fullText = await this.options.publicationService.getFullText(publication.id);
        }
        
        // Referenzen hinzufügen, falls angefordert
        if (includeReferences && this.options.citationService) {
          item.references = await this.options.citationService.getReferences(publication.id);
        }
        
        // Zitationen hinzufügen, falls angefordert
        if (includeCitations && this.options.citationService) {
          item.citations = await this.options.citationService.getCitations(publication.id);
        }
        
        return item;
      }))
    };
    
    // Datensatz im gewünschten Format zurückgeben
    if (format === 'jsonl') {
      return dataset.data.map(item => JSON.stringify(item)).join('\n');
    }
    
    return dataset;
  }
  
  /**
   * Erstellt einen domänenspezifischen Datensatz
   * @param {string} domain - Wissenschaftliche Domäne
   * @param {object} options - Optionen für den Datensatz
   * @returns {Promise<object>} Domänenspezifischer Datensatz
   */
  async createDomainSpecificDataset(domain, options = {}) {
    return this.exportDataset({ domain }, options);
  }
  
  /**
   * Erstellt einen Datensatz für ein bestimmtes Forschungsthema
   * @param {string} topic - Forschungsthema
   * @param {object} options - Optionen für den Datensatz
   * @returns {Promise<object>} Themenspezifischer Datensatz
   */
  async createTopicSpecificDataset(topic, options = {}) {
    return this.exportDataset({ 
      keywords: topic,
      titleContains: topic,
      abstractContains: topic
    }, options);
  }
  
  /**
   * Erstellt einen Datensatz mit den neuesten Publikationen
   * @param {object} options - Optionen für den Datensatz
   * @returns {Promise<object>} Datensatz mit neuesten Publikationen
   */
  async createLatestPublicationsDataset(options = {}) {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - 1);
    
    return this.exportDataset({}, {
      ...options,
      startDate,
      endDate,
      sortBy: 'publicationDate',
      sortOrder: 'desc'
    });
  }
}

module.exports = AiDataExportService;