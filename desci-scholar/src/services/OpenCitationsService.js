/**
 * @fileoverview Service für die Interaktion mit der OpenCitations API
 * 
 * Dieser Service ermöglicht den Zugriff auf die OpenCitations API, um Zitationsdaten,
 * Referenzen und Metadaten für wissenschaftliche Publikationen abzurufen.
 * OpenCitations ist eine unabhängige Infrastrukturorganisation, die offene
 * bibliografische und Zitationsdaten bereitstellt.
 */

import fetch from 'node-fetch';
import { logger } from '../utils/logger.js';

/**
 * Service für die Interaktion mit der OpenCitations API
 */
export class OpenCitationsService {
  /**
   * Erstellt eine neue Instanz des OpenCitationsService
   * 
   * @param {Object} options - Optionen für den Service
   * @param {string} [options.baseUrl] - Basis-URL der OpenCitations API
   * @param {string} [options.accessToken] - Zugriffstoken für die API
   * @param {number} [options.timeout] - Timeout für API-Anfragen in Millisekunden
   * @param {number} [options.maxRetries] - Maximale Anzahl von Wiederholungsversuchen
   */
  constructor(options = {}) {
    this.baseUrl = options.baseUrl || process.env.OPENCITATIONS_API_URL || 'https://opencitations.net/index/api/v1';
    this.accessToken = options.accessToken || process.env.OPENCITATIONS_ACCESS_TOKEN;
    this.timeout = options.timeout || 10000;
    this.maxRetries = options.maxRetries || 3;
    this.retryCount = 0;
  }
  
  /**
   * Initialisiert den Service
   */
  async initialize() {
    logger.info('OpenCitationsService wird initialisiert');
    
    // Prüfe, ob die API erreichbar ist
    try {
      const testResponse = await this._makeRequest('/citation-count/10.1108/jd-12-2013-0166');
      
      if (testResponse.success) {
        logger.info('OpenCitationsService erfolgreich initialisiert');
      } else {
        logger.warn('OpenCitationsService konnte nicht vollständig initialisiert werden', {
          error: testResponse.error
        });
      }
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des OpenCitationsService', {
        error: error.message
      });
    }
  }
  
  /**
   * Holt Zitationen für eine DOI (eingehende Zitationen)
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Zitationsdaten
   */
  async getCitations(doi) {
    return this._makeRequest(`/citations/${encodeURIComponent(doi)}`);
  }
  
  /**
   * Holt Referenzen für eine DOI (ausgehende Zitationen)
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Referenzdaten
   */
  async getReferences(doi) {
    return this._makeRequest(`/references/${encodeURIComponent(doi)}`);
  }
  
  /**
   * Holt die Anzahl der Zitationen für eine DOI
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<number>} Anzahl der Zitationen
   */
  async getCitationCount(doi) {
    const result = await this._makeRequest(`/citation-count/${encodeURIComponent(doi)}`);
    return result.success ? parseInt(result.data[0]?.count || 0) : 0;
  }
  
  /**
   * Holt Metadaten für eine DOI
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Metadaten
   */
  async getMetadata(doi) {
    return this._makeRequest(`/metadata/${encodeURIComponent(doi)}`);
  }
  
  /**
   * Holt Daten für eine spezifische Zitation (OCI)
   * 
   * @param {string} oci - Open Citation Identifier
   * @returns {Promise<Object>} Zitationsdaten
   */
  async getCitation(oci) {
    return this._makeRequest(`/citation/${encodeURIComponent(oci)}`);
  }
  
  /**
   * Holt alle verfügbaren Daten für eine DOI
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Alle verfügbaren Daten
   */
  async getAllDataForDOI(doi) {
    try {
      logger.info(`Hole alle OpenCitations-Daten für DOI ${doi}`);
      
      // Parallele Anfragen für bessere Performance
      const [citationsResult, referencesResult, citationCountResult, metadataResult] = await Promise.all([
        this.getCitations(doi),
        this.getReferences(doi),
        this.getCitationCount(doi),
        this.getMetadata(doi)
      ]);
      
      return {
        success: true,
        doi,
        citations: citationsResult.success ? citationsResult.data : [],
        references: referencesResult.success ? referencesResult.data : [],
        citationCount: citationCountResult,
        metadata: metadataResult.success ? metadataResult.data : null
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen aller OpenCitations-Daten', {
        doi,
        error: error.message
      });
      
      return {
        success: false,
        doi,
        error: error.message
      };
    }
  }
  
  /**
   * Aktualisiert die Zitationsdaten in der Datenbank mit OpenCitations-Daten
   * 
   * @param {string} doi - DOI der Publikation
   * @param {Object} databaseService - Datenbankdienst
   * @returns {Promise<Object>} Ergebnis der Aktualisierung
   */
  async updateCitationDataForDOI(doi, databaseService) {
    try {
      logger.info(`Aktualisiere Zitationsdaten für DOI ${doi} mit OpenCitations-Daten`);
      
      // Hole alle Daten von OpenCitations
      const openCitationsData = await this.getAllDataForDOI(doi);
      
      if (!openCitationsData.success) {
        return {
          success: false,
          doi,
          message: 'Fehler beim Abrufen von OpenCitations-Daten',
          error: openCitationsData.error
        };
      }
      
      // Aktualisiere die Zitationszählung in der Datenbank
      await databaseService.query(
        'UPDATE publication_metrics SET total_citations = ?, updated_at = NOW() WHERE doi = ? AND total_citations < ?',
        [openCitationsData.citationCount, doi, openCitationsData.citationCount]
      );
      
      // Speichere die eingehenden Zitationen in der Datenbank
      for (const citation of openCitationsData.citations) {
        // Prüfe, ob die Zitationsbeziehung bereits existiert
        const existingRelationship = await databaseService.query(
          'SELECT id FROM citation_relationships WHERE citing_doi = ? AND cited_doi = ?',
          [citation.citing, doi]
        );
        
        if (existingRelationship && existingRelationship.length > 0) {
          // Aktualisiere den OCI, falls er jetzt verfügbar ist
          await databaseService.query(
            'UPDATE citation_relationships SET oci = ?, updated_at = NOW() WHERE id = ?',
            [citation.oci, existingRelationship[0].id]
          );
        } else {
          // Füge die neue Beziehung hinzu
          await databaseService.query(
            'INSERT INTO citation_relationships (citing_doi, cited_doi, oci, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())',
            [citation.citing, doi, citation.oci]
          );
        }
      }
      
      // Speichere die ausgehenden Zitationen in der Datenbank
      for (const reference of openCitationsData.references) {
        // Prüfe, ob die Zitationsbeziehung bereits existiert
        const existingRelationship = await databaseService.query(
          'SELECT id FROM citation_relationships WHERE citing_doi = ? AND cited_doi = ?',
          [doi, reference.cited]
        );
        
        if (existingRelationship && existingRelationship.length > 0) {
          // Aktualisiere den OCI, falls er jetzt verfügbar ist
          await databaseService.query(
            'UPDATE citation_relationships SET oci = ?, updated_at = NOW() WHERE id = ?',
            [reference.oci, existingRelationship[0].id]
          );
        } else {
          // Füge die neue Beziehung hinzu
          await databaseService.query(
            'INSERT INTO citation_relationships (citing_doi, cited_doi, oci, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())',
            [doi, reference.cited, reference.oci]
          );
        }
      }
      
      // Speichere die OpenCitations-Metadaten
      if (openCitationsData.metadata) {
        await databaseService.query(
          'INSERT INTO publication_external_data (doi, source, data, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW()) ' +
          'ON DUPLICATE KEY UPDATE data = ?, updated_at = NOW()',
          [
            doi, 
            'opencitations', 
            JSON.stringify(openCitationsData.metadata),
            JSON.stringify(openCitationsData.metadata)
          ]
        );
      }
      
      return {
        success: true,
        doi,
        citationsUpdated: openCitationsData.citations.length,
        referencesUpdated: openCitationsData.references.length,
        citationCount: openCitationsData.citationCount
      };
    } catch (error) {
      logger.error('Fehler bei der Aktualisierung von Zitationsdaten mit OpenCitations', {
        doi,
        error: error.message
      });
      
      return {
        success: false,
        doi,
        error: error.message
      };
    }
  }
  
  /**
   * Führt eine Anfrage an die OpenCitations API aus
   * 
   * @private
   * @param {string} endpoint - API-Endpunkt
   * @returns {Promise<Object>} API-Antwort
   */
  async _makeRequest(endpoint) {
    try {
      const url = `${this.baseUrl}${endpoint}`;
      
      const headers = {
        'Accept': 'application/json'
      };
      
      if (this.accessToken) {
        headers['authorization'] = this.accessToken;
      }
      
      const response = await fetch(url, { 
        headers,
        timeout: this.timeout
      });
      
      if (!response.ok) {
        throw new Error(`OpenCitations API Fehler: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      
      // Setze Retry-Zähler zurück
      this.retryCount = 0;
      
      return {
        success: true,
        data
      };
    } catch (error) {
      // Versuche erneut, falls konfiguriert
      if (this.retryCount < this.maxRetries) {
        this.retryCount++;
        
        logger.warn('OpenCitationsService: Fehler bei der Anfrage, versuche erneut', {
          endpoint,
          error: error.message,
          retryCount: this.retryCount
        });
        
        // Warte exponentiell länger zwischen Wiederholungsversuchen
        await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, this.retryCount)));
        
        return this._makeRequest(endpoint);
      }
      
      // Setze Retry-Zähler zurück
      this.retryCount = 0;
      
      logger.error('OpenCitationsService: Fehler bei der Anfrage', {
        endpoint,
        error: error.message
      });
      
      return {
        success: false,
        error: error.message
      };
    }
  }
}

export default OpenCitationsService;/**
 * @fileoverview Service für die Interaktion mit der OpenCitations API
 * 
 * Dieser Service ermöglicht den Zugriff auf die OpenCitations API, um Zitationsdaten,
 * Referenzen und Metadaten für wissenschaftliche Publikationen abzurufen.
 * OpenCitations ist eine unabhängige Infrastrukturorganisation, die offene
 * bibliografische und Zitationsdaten bereitstellt.
 */

import fetch from 'node-fetch';
import { logger } from '../utils/logger.js';

/**
 * Service für die Interaktion mit der OpenCitations API
 */
export class OpenCitationsService {
  /**
   * Erstellt eine neue Instanz des OpenCitationsService
   * 
   * @param {Object} options - Optionen für den Service
   * @param {string} [options.baseUrl] - Basis-URL der OpenCitations API
   * @param {string} [options.accessToken] - Zugriffstoken für die API
   * @param {number} [options.timeout] - Timeout für API-Anfragen in Millisekunden
   * @param {number} [options.maxRetries] - Maximale Anzahl von Wiederholungsversuchen
   */
  constructor(options = {}) {
    this.baseUrl = options.baseUrl || process.env.OPENCITATIONS_API_URL || 'https://opencitations.net/index/api/v1';
    this.accessToken = options.accessToken || process.env.OPENCITATIONS_ACCESS_TOKEN;
    this.timeout = options.timeout || 10000;
    this.maxRetries = options.maxRetries || 3;
    this.retryCount = 0;
  }
  
  /**
   * Initialisiert den Service
   */
  async initialize() {
    logger.info('OpenCitationsService wird initialisiert');
    
    // Prüfe, ob die API erreichbar ist
    try {
      const testResponse = await this._makeRequest('/citation-count/10.1108/jd-12-2013-0166');
      
      if (testResponse.success) {
        logger.info('OpenCitationsService erfolgreich initialisiert');
      } else {
        logger.warn('OpenCitationsService konnte nicht vollständig initialisiert werden', {
          error: testResponse.error
        });
      }
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des OpenCitationsService', {
        error: error.message
      });
    }
  }
  
  /**
   * Holt Zitationen für eine DOI (eingehende Zitationen)
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Zitationsdaten
   */
  async getCitations(doi) {
    return this._makeRequest(`/citations/${encodeURIComponent(doi)}`);
  }
  
  /**
   * Holt Referenzen für eine DOI (ausgehende Zitationen)
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Referenzdaten
   */
  async getReferences(doi) {
    return this._makeRequest(`/references/${encodeURIComponent(doi)}`);
  }
  
  /**
   * Holt die Anzahl der Zitationen für eine DOI
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<number>} Anzahl der Zitationen
   */
  async getCitationCount(doi) {
    const result = await this._makeRequest(`/citation-count/${encodeURIComponent(doi)}`);
    return result.success ? parseInt(result.data[0]?.count || 0) : 0;
  }
  
  /**
   * Holt Metadaten für eine DOI
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Metadaten
   */
  async getMetadata(doi) {
    return this._makeRequest(`/metadata/${encodeURIComponent(doi)}`);
  }
  
  /**
   * Holt Daten für eine spezifische Zitation (OCI)
   * 
   * @param {string} oci - Open Citation Identifier
   * @returns {Promise<Object>} Zitationsdaten
   */
  async getCitation(oci) {
    return this._makeRequest(`/citation/${encodeURIComponent(oci)}`);
  }
  
  /**
   * Holt alle verfügbaren Daten für eine DOI
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Alle verfügbaren Daten
   */
  async getAllDataForDOI(doi) {
    try {
      logger.info(`Hole alle OpenCitations-Daten für DOI ${doi}`);
      
      // Parallele Anfragen für bessere Performance
      const [citationsResult, referencesResult, citationCountResult, metadataResult] = await Promise.all([
        this.getCitations(doi),
        this.getReferences(doi),
        this.getCitationCount(doi),
        this.getMetadata(doi)
      ]);
      
      return {
        success: true,
        doi,
        citations: citationsResult.success ? citationsResult.data : [],
        references: referencesResult.success ? referencesResult.data : [],
        citationCount: citationCountResult,
        metadata: metadataResult.success ? metadataResult.data : null
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen aller OpenCitations-Daten', {
        doi,
        error: error.message
      });
      
      return {
        success: false,
        doi,
        error: error.message
      };
    }
  }
  
  /**
   * Aktualisiert die Zitationsdaten in der Datenbank mit OpenCitations-Daten
   * 
   * @param {string} doi - DOI der Publikation
   * @param {Object} databaseService - Datenbankdienst
   * @returns {Promise<Object>} Ergebnis der Aktualisierung
   */
  async updateCitationDataForDOI(doi, databaseService) {
    try {
      logger.info(`Aktualisiere Zitationsdaten für DOI ${doi} mit OpenCitations-Daten`);
      
      // Hole alle Daten von OpenCitations
      const openCitationsData = await this.getAllDataForDOI(doi);
      
      if (!openCitationsData.success) {
        return {
          success: false,
          doi,
          message: 'Fehler beim Abrufen von OpenCitations-Daten',
          error: openCitationsData.error
        };
      }
      
      // Aktualisiere die Zitationszählung in der Datenbank
      await databaseService.query(
        'UPDATE publication_metrics SET total_citations = ?, updated_at = NOW() WHERE doi = ? AND total_citations < ?',
        [openCitationsData.citationCount, doi, openCitationsData.citationCount]
      );
      
      // Speichere die eingehenden Zitationen in der Datenbank
      for (const citation of openCitationsData.citations) {
        // Prüfe, ob die Zitationsbeziehung bereits existiert
        const existingRelationship = await databaseService.query(
          'SELECT id FROM citation_relationships WHERE citing_doi = ? AND cited_doi = ?',
          [citation.citing, doi]
        );
        
        if (existingRelationship && existingRelationship.length > 0) {
          // Aktualisiere den OCI, falls er jetzt verfügbar ist
          await databaseService.query(
            'UPDATE citation_relationships SET oci = ?, updated_at = NOW() WHERE id = ?',
            [citation.oci, existingRelationship[0].id]
          );
        } else {
          // Füge die neue Beziehung hinzu
          await databaseService.query(
            'INSERT INTO citation_relationships (citing_doi, cited_doi, oci, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())',
            [citation.citing, doi, citation.oci]
          );
        }
      }
      
      // Speichere die ausgehenden Zitationen in der Datenbank
      for (const reference of openCitationsData.references) {
        // Prüfe, ob die Zitationsbeziehung bereits existiert
        const existingRelationship = await databaseService.query(
          'SELECT id FROM citation_relationships WHERE citing_doi = ? AND cited_doi = ?',
          [doi, reference.cited]
        );
        
        if (existingRelationship && existingRelationship.length > 0) {
          // Aktualisiere den OCI, falls er jetzt verfügbar ist
          await databaseService.query(
            'UPDATE citation_relationships SET oci = ?, updated_at = NOW() WHERE id = ?',
            [reference.oci, existingRelationship[0].id]
          );
        } else {
          // Füge die neue Beziehung hinzu
          await databaseService.query(
            'INSERT INTO citation_relationships (citing_doi, cited_doi, oci, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())',
            [doi, reference.cited, reference.oci]
          );
        }
      }
      
      // Speichere die OpenCitations-Metadaten
      if (openCitationsData.metadata) {
        await databaseService.query(
          'INSERT INTO publication_external_data (doi, source, data, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW()) ' +
          'ON DUPLICATE KEY UPDATE data = ?, updated_at = NOW()',
          [
            doi, 
            'opencitations', 
            JSON.stringify(openCitationsData.metadata),
            JSON.stringify(openCitationsData.metadata)
          ]
        );
      }
      
      return {
        success: true,
        doi,
        citationsUpdated: openCitationsData.citations.length,
        referencesUpdated: openCitationsData.references.length,
        citationCount: openCitationsData.citationCount
      };
    } catch (error) {
      logger.error('Fehler bei der Aktualisierung von Zitationsdaten mit OpenCitations', {
        doi,
        error: error.message
      });
      
      return {
        success: false,
        doi,
        error: error.message
      };
    }
  }
  
  /**
   * Führt eine Anfrage an die OpenCitations API aus
   * 
   * @private
   * @param {string} endpoint - API-Endpunkt
   * @returns {Promise<Object>} API-Antwort
   */
  async _makeRequest(endpoint) {
    try {
      const url = `${this.baseUrl}${endpoint}`;
      
      const headers = {
        'Accept': 'application/json'
      };
      
      if (this.accessToken) {
        headers['authorization'] = this.accessToken;
      }
      
      const response = await fetch(url, { 
        headers,
        timeout: this.timeout
      });
      
      if (!response.ok) {
        throw new Error(`OpenCitations API Fehler: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      
      // Setze Retry-Zähler zurück
      this.retryCount = 0;
      
      return {
        success: true,
        data
      };
    } catch (error) {
      // Versuche erneut, falls konfiguriert
      if (this.retryCount < this.maxRetries) {
        this.retryCount++;
        
        logger.warn('OpenCitationsService: Fehler bei der Anfrage, versuche erneut', {
          endpoint,
          error: error.message,
          retryCount: this.retryCount
        });
        
        // Warte exponentiell länger zwischen Wiederholungsversuchen
        await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, this.retryCount)));
        
        return this._makeRequest(endpoint);
      }
      
      // Setze Retry-Zähler zurück
      this.retryCount = 0;
      
      logger.error('OpenCitationsService: Fehler bei der Anfrage', {
        endpoint,
        error: error.message
      });
      
      return {
        success: false,
        error: error.message
      };
    }
  }
}

export default OpenCitationsService;/**
 * @fileoverview Service für die Interaktion mit der OpenCitations API
 * 
 * Dieser Service ermöglicht den Zugriff auf die OpenCitations API, um Zitationsdaten,
 * Referenzen und Metadaten für wissenschaftliche Publikationen abzurufen.
 * OpenCitations ist eine unabhängige Infrastrukturorganisation, die offene
 * bibliografische und Zitationsdaten bereitstellt.
 */

import fetch from 'node-fetch';
import { logger } from '../utils/logger.js';

/**
 * Service für die Interaktion mit der OpenCitations API
 */
export class OpenCitationsService {
  /**
   * Erstellt eine neue Instanz des OpenCitationsService
   * 
   * @param {Object} options - Optionen für den Service
   * @param {string} [options.baseUrl] - Basis-URL der OpenCitations API
   * @param {string} [options.accessToken] - Zugriffstoken für die API
   * @param {number} [options.timeout] - Timeout für API-Anfragen in Millisekunden
   * @param {number} [options.maxRetries] - Maximale Anzahl von Wiederholungsversuchen
   */
  constructor(options = {}) {
    this.baseUrl = options.baseUrl || process.env.OPENCITATIONS_API_URL || 'https://opencitations.net/index/api/v1';
    this.accessToken = options.accessToken || process.env.OPENCITATIONS_ACCESS_TOKEN;
    this.timeout = options.timeout || 10000;
    this.maxRetries = options.maxRetries || 3;
    this.retryCount = 0;
  }
  
  /**
   * Initialisiert den Service
   */
  async initialize() {
    logger.info('OpenCitationsService wird initialisiert');
    
    // Prüfe, ob die API erreichbar ist
    try {
      const testResponse = await this._makeRequest('/citation-count/10.1108/jd-12-2013-0166');
      
      if (testResponse.success) {
        logger.info('OpenCitationsService erfolgreich initialisiert');
      } else {
        logger.warn('OpenCitationsService konnte nicht vollständig initialisiert werden', {
          error: testResponse.error
        });
      }
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des OpenCitationsService', {
        error: error.message
      });
    }
  }
  
  /**
   * Holt Zitationen für eine DOI (eingehende Zitationen)
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Zitationsdaten
   */
  async getCitations(doi) {
    return this._makeRequest(`/citations/${encodeURIComponent(doi)}`);
  }
  
  /**
   * Holt Referenzen für eine DOI (ausgehende Zitationen)
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Referenzdaten
   */
  async getReferences(doi) {
    return this._makeRequest(`/references/${encodeURIComponent(doi)}`);
  }
  
  /**
   * Holt die Anzahl der Zitationen für eine DOI
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<number>} Anzahl der Zitationen
   */
  async getCitationCount(doi) {
    const result = await this._makeRequest(`/citation-count/${encodeURIComponent(doi)}`);
    return result.success ? parseInt(result.data[0]?.count || 0) : 0;
  }
  
  /**
   * Holt Metadaten für eine DOI
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Metadaten
   */
  async getMetadata(doi) {
    return this._makeRequest(`/metadata/${encodeURIComponent(doi)}`);
  }
  
  /**
   * Holt Daten für eine spezifische Zitation (OCI)
   * 
   * @param {string} oci - Open Citation Identifier
   * @returns {Promise<Object>} Zitationsdaten
   */
  async getCitation(oci) {
    return this._makeRequest(`/citation/${encodeURIComponent(oci)}`);
  }
  
  /**
   * Holt alle verfügbaren Daten für eine DOI
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Alle verfügbaren Daten
   */
  async getAllDataForDOI(doi) {
    try {
      logger.info(`Hole alle OpenCitations-Daten für DOI ${doi}`);
      
      // Parallele Anfragen für bessere Performance
      const [citationsResult, referencesResult, citationCountResult, metadataResult] = await Promise.all([
        this.getCitations(doi),
        this.getReferences(doi),
        this.getCitationCount(doi),
        this.getMetadata(doi)
      ]);
      
      return {
        success: true,
        doi,
        citations: citationsResult.success ? citationsResult.data : [],
        references: referencesResult.success ? referencesResult.data : [],
        citationCount: citationCountResult,
        metadata: metadataResult.success ? metadataResult.data : null
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen aller OpenCitations-Daten', {
        doi,
        error: error.message
      });
      
      return {
        success: false,
        doi,
        error: error.message
      };
    }
  }
  
  /**
   * Aktualisiert die Zitationsdaten in der Datenbank mit OpenCitations-Daten
   * 
   * @param {string} doi - DOI der Publikation
   * @param {Object} databaseService - Datenbankdienst
   * @returns {Promise<Object>} Ergebnis der Aktualisierung
   */
  async updateCitationDataForDOI(doi, databaseService) {
    try {
      logger.info(`Aktualisiere Zitationsdaten für DOI ${doi} mit OpenCitations-Daten`);
      
      // Hole alle Daten von OpenCitations
      const openCitationsData = await this.getAllDataForDOI(doi);
      
      if (!openCitationsData.success) {
        return {
          success: false,
          doi,
          message: 'Fehler beim Abrufen von OpenCitations-Daten',
          error: openCitationsData.error
        };
      }
      
      // Aktualisiere die Zitationszählung in der Datenbank
      await databaseService.query(
        'UPDATE publication_metrics SET total_citations = ?, updated_at = NOW() WHERE doi = ? AND total_citations < ?',
        [openCitationsData.citationCount, doi, openCitationsData.citationCount]
      );
      
      // Speichere die eingehenden Zitationen in der Datenbank
      for (const citation of openCitationsData.citations) {
        // Prüfe, ob die Zitationsbeziehung bereits existiert
        const existingRelationship = await databaseService.query(
          'SELECT id FROM citation_relationships WHERE citing_doi = ? AND cited_doi = ?',
          [citation.citing, doi]
        );
        
        if (existingRelationship && existingRelationship.length > 0) {
          // Aktualisiere den OCI, falls er jetzt verfügbar ist
          await databaseService.query(
            'UPDATE citation_relationships SET oci = ?, updated_at = NOW() WHERE id = ?',
            [citation.oci, existingRelationship[0].id]
          );
        } else {
          // Füge die neue Beziehung hinzu
          await databaseService.query(
            'INSERT INTO citation_relationships (citing_doi, cited_doi, oci, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())',
            [citation.citing, doi, citation.oci]
          );
        }
      }
      
      // Speichere die ausgehenden Zitationen in der Datenbank
      for (const reference of openCitationsData.references) {
        // Prüfe, ob die Zitationsbeziehung bereits existiert
        const existingRelationship = await databaseService.query(
          'SELECT id FROM citation_relationships WHERE citing_doi = ? AND cited_doi = ?',
          [doi, reference.cited]
        );
        
        if (existingRelationship && existingRelationship.length > 0) {
          // Aktualisiere den OCI, falls er jetzt verfügbar ist
          await databaseService.query(
            'UPDATE citation_relationships SET oci = ?, updated_at = NOW() WHERE id = ?',
            [reference.oci, existingRelationship[0].id]
          );
        } else {
          // Füge die neue Beziehung hinzu
          await databaseService.query(
            'INSERT INTO citation_relationships (citing_doi, cited_doi, oci, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())',
            [doi, reference.cited, reference.oci]
          );
        }
      }
      
      // Speichere die OpenCitations-Metadaten
      if (openCitationsData.metadata) {
        await databaseService.query(
          'INSERT INTO publication_external_data (doi, source, data, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW()) ' +
          'ON DUPLICATE KEY UPDATE data = ?, updated_at = NOW()',
          [
            doi, 
            'opencitations', 
            JSON.stringify(openCitationsData.metadata),
            JSON.stringify(openCitationsData.metadata)
          ]
        );
      }
      
      return {
        success: true,
        doi,
        citationsUpdated: openCitationsData.citations.length,
        referencesUpdated: openCitationsData.references.length,
        citationCount: openCitationsData.citationCount
      };
    } catch (error) {
      logger.error('Fehler bei der Aktualisierung von Zitationsdaten mit OpenCitations', {
        doi,
        error: error.message
      });
      
      return {
        success: false,
        doi,
        error: error.message
      };
    }
  }
  
  /**
   * Führt eine Anfrage an die OpenCitations API aus
   * 
   * @private
   * @param {string} endpoint - API-Endpunkt
   * @returns {Promise<Object>} API-Antwort
   */
  async _makeRequest(endpoint) {
    try {
      const url = `${this.baseUrl}${endpoint}`;
      
      const headers = {
        'Accept': 'application/json'
      };
      
      if (this.accessToken) {
        headers['authorization'] = this.accessToken;
      }
      
      const response = await fetch(url, { 
        headers,
        timeout: this.timeout
      });
      
      if (!response.ok) {
        throw new Error(`OpenCitations API Fehler: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      
      // Setze Retry-Zähler zurück
      this.retryCount = 0;
      
      return {
        success: true,
        data
      };
    } catch (error) {
      // Versuche erneut, falls konfiguriert
      if (this.retryCount < this.maxRetries) {
        this.retryCount++;
        
        logger.warn('OpenCitationsService: Fehler bei der Anfrage, versuche erneut', {
          endpoint,
          error: error.message,
          retryCount: this.retryCount
        });
        
        // Warte exponentiell länger zwischen Wiederholungsversuchen
        await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, this.retryCount)));
        
        return this._makeRequest(endpoint);
      }
      
      // Setze Retry-Zähler zurück
      this.retryCount = 0;
      
      logger.error('OpenCitationsService: Fehler bei der Anfrage', {
        endpoint,
        error: error.message
      });
      
      return {
        success: false,
        error: error.message
      };
    }
  }
}

export default OpenCitationsService;/**
 * @fileoverview Service für die Interaktion mit der OpenCitations API
 * 
 * Dieser Service ermöglicht den Zugriff auf die OpenCitations API, um Zitationsdaten,
 * Referenzen und Metadaten für wissenschaftliche Publikationen abzurufen.
 * OpenCitations ist eine unabhängige Infrastrukturorganisation, die offene
 * bibliografische und Zitationsdaten bereitstellt.
 */

import fetch from 'node-fetch';
import { logger } from '../utils/logger.js';

/**
 * Service für die Interaktion mit der OpenCitations API
 */
export class OpenCitationsService {
  /**
   * Erstellt eine neue Instanz des OpenCitationsService
   * 
   * @param {Object} options - Optionen für den Service
   * @param {string} [options.baseUrl] - Basis-URL der OpenCitations API
   * @param {string} [options.accessToken] - Zugriffstoken für die API
   * @param {number} [options.timeout] - Timeout für API-Anfragen in Millisekunden
   * @param {number} [options.maxRetries] - Maximale Anzahl von Wiederholungsversuchen
   */
  constructor(options = {}) {
    this.baseUrl = options.baseUrl || process.env.OPENCITATIONS_API_URL || 'https://opencitations.net/index/api/v1';
    this.accessToken = options.accessToken || process.env.OPENCITATIONS_ACCESS_TOKEN;
    this.timeout = options.timeout || 10000;
    this.maxRetries = options.maxRetries || 3;
    this.retryCount = 0;
  }
  
  /**
   * Initialisiert den Service
   */
  async initialize() {
    logger.info('OpenCitationsService wird initialisiert');
    
    // Prüfe, ob die API erreichbar ist
    try {
      const testResponse = await this._makeRequest('/citation-count/10.1108/jd-12-2013-0166');
      
      if (testResponse.success) {
        logger.info('OpenCitationsService erfolgreich initialisiert');
      } else {
        logger.warn('OpenCitationsService konnte nicht vollständig initialisiert werden', {
          error: testResponse.error
        });
      }
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des OpenCitationsService', {
        error: error.message
      });
    }
  }
  
  /**
   * Holt Zitationen für eine DOI (eingehende Zitationen)
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Zitationsdaten
   */
  async getCitations(doi) {
    return this._makeRequest(`/citations/${encodeURIComponent(doi)}`);
  }
  
  /**
   * Holt Referenzen für eine DOI (ausgehende Zitationen)
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Referenzdaten
   */
  async getReferences(doi) {
    return this._makeRequest(`/references/${encodeURIComponent(doi)}`);
  }
  
  /**
   * Holt die Anzahl der Zitationen für eine DOI
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<number>} Anzahl der Zitationen
   */
  async getCitationCount(doi) {
    const result = await this._makeRequest(`/citation-count/${encodeURIComponent(doi)}`);
    return result.success ? parseInt(result.data[0]?.count || 0) : 0;
  }
  
  /**
   * Holt Metadaten für eine DOI
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Metadaten
   */
  async getMetadata(doi) {
    return this._makeRequest(`/metadata/${encodeURIComponent(doi)}`);
  }
  
  /**
   * Holt Daten für eine spezifische Zitation (OCI)
   * 
   * @param {string} oci - Open Citation Identifier
   * @returns {Promise<Object>} Zitationsdaten
   */
  async getCitation(oci) {
    return this._makeRequest(`/citation/${encodeURIComponent(oci)}`);
  }
  
  /**
   * Holt alle verfügbaren Daten für eine DOI
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Alle verfügbaren Daten
   */
  async getAllDataForDOI(doi) {
    try {
      logger.info(`Hole alle OpenCitations-Daten für DOI ${doi}`);
      
      // Parallele Anfragen für bessere Performance
      const [citationsResult, referencesResult, citationCountResult, metadataResult] = await Promise.all([
        this.getCitations(doi),
        this.getReferences(doi),
        this.getCitationCount(doi),
        this.getMetadata(doi)
      ]);
      
      return {
        success: true,
        doi,
        citations: citationsResult.success ? citationsResult.data : [],
        references: referencesResult.success ? referencesResult.data : [],
        citationCount: citationCountResult,
        metadata: metadataResult.success ? metadataResult.data : null
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen aller OpenCitations-Daten', {
        doi,
        error: error.message
      });
      
      return {
        success: false,
        doi,
        error: error.message
      };
    }
  }
  
  /**
   * Aktualisiert die Zitationsdaten in der Datenbank mit OpenCitations-Daten
   * 
   * @param {string} doi - DOI der Publikation
   * @param {Object} databaseService - Datenbankdienst
   * @returns {Promise<Object>} Ergebnis der Aktualisierung
   */
  async updateCitationDataForDOI(doi, databaseService) {
    try {
      logger.info(`Aktualisiere Zitationsdaten für DOI ${doi} mit OpenCitations-Daten`);
      
      // Hole alle Daten von OpenCitations
      const openCitationsData = await this.getAllDataForDOI(doi);
      
      if (!openCitationsData.success) {
        return {
          success: false,
          doi,
          message: 'Fehler beim Abrufen von OpenCitations-Daten',
          error: openCitationsData.error
        };
      }
      
      // Aktualisiere die Zitationszählung in der Datenbank
      await databaseService.query(
        'UPDATE publication_metrics SET total_citations = ?, updated_at = NOW() WHERE doi = ? AND total_citations < ?',
        [openCitationsData.citationCount, doi, openCitationsData.citationCount]
      );
      
      // Speichere die eingehenden Zitationen in der Datenbank
      for (const citation of openCitationsData.citations) {
        // Prüfe, ob die Zitationsbeziehung bereits existiert
        const existingRelationship = await databaseService.query(
          'SELECT id FROM citation_relationships WHERE citing_doi = ? AND cited_doi = ?',
          [citation.citing, doi]
        );
        
        if (existingRelationship && existingRelationship.length > 0) {
          // Aktualisiere den OCI, falls er jetzt verfügbar ist
          await databaseService.query(
            'UPDATE citation_relationships SET oci = ?, updated_at = NOW() WHERE id = ?',
            [citation.oci, existingRelationship[0].id]
          );
        } else {
          // Füge die neue Beziehung hinzu
          await databaseService.query(
            'INSERT INTO citation_relationships (citing_doi, cited_doi, oci, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())',
            [citation.citing, doi, citation.oci]
          );
        }
      }
      
      // Speichere die ausgehenden Zitationen in der Datenbank
      for (const reference of openCitationsData.references) {
        // Prüfe, ob die Zitationsbeziehung bereits existiert
        const existingRelationship = await databaseService.query(
          'SELECT id FROM citation_relationships WHERE citing_doi = ? AND cited_doi = ?',
          [doi, reference.cited]
        );
        
        if (existingRelationship && existingRelationship.length > 0) {
          // Aktualisiere den OCI, falls er jetzt verfügbar ist
          await databaseService.query(
            'UPDATE citation_relationships SET oci = ?, updated_at = NOW() WHERE id = ?',
            [reference.oci, existingRelationship[0].id]
          );
        } else {
          // Füge die neue Beziehung hinzu
          await databaseService.query(
            'INSERT INTO citation_relationships (citing_doi, cited_doi, oci, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())',
            [doi, reference.cited, reference.oci]
          );
        }
      }
      
      // Speichere die OpenCitations-Metadaten
      if (openCitationsData.metadata) {
        await databaseService.query(
          'INSERT INTO publication_external_data (doi, source, data, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW()) ' +
          'ON DUPLICATE KEY UPDATE data = ?, updated_at = NOW()',
          [
            doi, 
            'opencitations', 
            JSON.stringify(openCitationsData.metadata),
            JSON.stringify(openCitationsData.metadata)
          ]
        );
      }
      
      return {
        success: true,
        doi,
        citationsUpdated: openCitationsData.citations.length,
        referencesUpdated: openCitationsData.references.length,
        citationCount: openCitationsData.citationCount
      };
    } catch (error) {
      logger.error('Fehler bei der Aktualisierung von Zitationsdaten mit OpenCitations', {
        doi,
        error: error.message
      });
      
      return {
        success: false,
        doi,
        error: error.message
      };
    }
  }
  
  /**
   * Führt eine Anfrage an die OpenCitations API aus
   * 
   * @private
   * @param {string} endpoint - API-Endpunkt
   * @returns {Promise<Object>} API-Antwort
   */
  async _makeRequest(endpoint) {
    try {
      const url = `${this.baseUrl}${endpoint}`;
      
      const headers = {
        'Accept': 'application/json'
      };
      
      if (this.accessToken) {
        headers['authorization'] = this.accessToken;
      }
      
      const response = await fetch(url, { 
        headers,
        timeout: this.timeout
      });
      
      if (!response.ok) {
        throw new Error(`OpenCitations API Fehler: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      
      // Setze Retry-Zähler zurück
      this.retryCount = 0;
      
      return {
        success: true,
        data
      };
    } catch (error) {
      // Versuche erneut, falls konfiguriert
      if (this.retryCount < this.maxRetries) {
        this.retryCount++;
        
        logger.warn('OpenCitationsService: Fehler bei der Anfrage, versuche erneut', {
          endpoint,
          error: error.message,
          retryCount: this.retryCount
        });
        
        // Warte exponentiell länger zwischen Wiederholungsversuchen
        await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, this.retryCount)));
        
        return this._makeRequest(endpoint);
      }
      
      // Setze Retry-Zähler zurück
      this.retryCount = 0;
      
      logger.error('OpenCitationsService: Fehler bei der Anfrage', {
        endpoint,
        error: error.message
      });
      
      return {
        success: false,
        error: error.message
      };
    }
  }
}

export default OpenCitationsService;/**
 * @fileoverview Service für die Interaktion mit der OpenCitations API
 * 
 * Dieser Service ermöglicht den Zugriff auf die OpenCitations API, um Zitationsdaten,
 * Referenzen und Metadaten für wissenschaftliche Publikationen abzurufen.
 * OpenCitations ist eine unabhängige Infrastrukturorganisation, die offene
 * bibliografische und Zitationsdaten bereitstellt.
 */

import fetch from 'node-fetch';
import { logger } from '../utils/logger.js';

/**
 * Service für die Interaktion mit der OpenCitations API
 */
export class OpenCitationsService {
  /**
   * Erstellt eine neue Instanz des OpenCitationsService
   * 
   * @param {Object} options - Optionen für den Service
   * @param {string} [options.baseUrl] - Basis-URL der OpenCitations API
   * @param {string} [options.accessToken] - Zugriffstoken für die API
   * @param {number} [options.timeout] - Timeout für API-Anfragen in Millisekunden
   * @param {number} [options.maxRetries] - Maximale Anzahl von Wiederholungsversuchen
   */
  constructor(options = {}) {
    this.baseUrl = options.baseUrl || process.env.OPENCITATIONS_API_URL || 'https://opencitations.net/index/api/v1';
    this.accessToken = options.accessToken || process.env.OPENCITATIONS_ACCESS_TOKEN;
    this.timeout = options.timeout || 10000;
    this.maxRetries = options.maxRetries || 3;
    this.retryCount = 0;
  }
  
  /**
   * Initialisiert den Service
   */
  async initialize() {
    logger.info('OpenCitationsService wird initialisiert');
    
    // Prüfe, ob die API erreichbar ist
    try {
      const testResponse = await this._makeRequest('/citation-count/10.1108/jd-12-2013-0166');
      
      if (testResponse.success) {
        logger.info('OpenCitationsService erfolgreich initialisiert');
      } else {
        logger.warn('OpenCitationsService konnte nicht vollständig initialisiert werden', {
          error: testResponse.error
        });
      }
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des OpenCitationsService', {
        error: error.message
      });
    }
  }
  
  /**
   * Holt Zitationen für eine DOI (eingehende Zitationen)
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Zitationsdaten
   */
  async getCitations(doi) {
    return this._makeRequest(`/citations/${encodeURIComponent(doi)}`);
  }
  
  /**
   * Holt Referenzen für eine DOI (ausgehende Zitationen)
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Referenzdaten
   */
  async getReferences(doi) {
    return this._makeRequest(`/references/${encodeURIComponent(doi)}`);
  }
  
  /**
   * Holt die Anzahl der Zitationen für eine DOI
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<number>} Anzahl der Zitationen
   */
  async getCitationCount(doi) {
    const result = await this._makeRequest(`/citation-count/${encodeURIComponent(doi)}`);
    return result.success ? parseInt(result.data[0]?.count || 0) : 0;
  }
  
  /**
   * Holt Metadaten für eine DOI
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Metadaten
   */
  async getMetadata(doi) {
    return this._makeRequest(`/metadata/${encodeURIComponent(doi)}`);
  }
  
  /**
   * Holt Daten für eine spezifische Zitation (OCI)
   * 
   * @param {string} oci - Open Citation Identifier
   * @returns {Promise<Object>} Zitationsdaten
   */
  async getCitation(oci) {
    return this._makeRequest(`/citation/${encodeURIComponent(oci)}`);
  }
  
  /**
   * Holt alle verfügbaren Daten für eine DOI
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Alle verfügbaren Daten
   */
  async getAllDataForDOI(doi) {
    try {
      logger.info(`Hole alle OpenCitations-Daten für DOI ${doi}`);
      
      // Parallele Anfragen für bessere Performance
      const [citationsResult, referencesResult, citationCountResult, metadataResult] = await Promise.all([
        this.getCitations(doi),
        this.getReferences(doi),
        this.getCitationCount(doi),
        this.getMetadata(doi)
      ]);
      
      return {
        success: true,
        doi,
        citations: citationsResult.success ? citationsResult.data : [],
        references: referencesResult.success ? referencesResult.data : [],
        citationCount: citationCountResult,
        metadata: metadataResult.success ? metadataResult.data : null
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen aller OpenCitations-Daten', {
        doi,
        error: error.message
      });
      
      return {
        success: false,
        doi,
        error: error.message
      };
    }
  }
  
  /**
   * Aktualisiert die Zitationsdaten in der Datenbank mit OpenCitations-Daten
   * 
   * @param {string} doi - DOI der Publikation
   * @param {Object} databaseService - Datenbankdienst
   * @returns {Promise<Object>} Ergebnis der Aktualisierung
   */
  async updateCitationDataForDOI(doi, databaseService) {
    try {
      logger.info(`Aktualisiere Zitationsdaten für DOI ${doi} mit OpenCitations-Daten`);
      
      // Hole alle Daten von OpenCitations
      const openCitationsData = await this.getAllDataForDOI(doi);
      
      if (!openCitationsData.success) {
        return {
          success: false,
          doi,
          message: 'Fehler beim Abrufen von OpenCitations-Daten',
          error: openCitationsData.error
        };
      }
      
      // Aktualisiere die Zitationszählung in der Datenbank
      await databaseService.query(
        'UPDATE publication_metrics SET total_citations = ?, updated_at = NOW() WHERE doi = ? AND total_citations < ?',
        [openCitationsData.citationCount, doi, openCitationsData.citationCount]
      );
      
      // Speichere die eingehenden Zitationen in der Datenbank
      for (const citation of openCitationsData.citations) {
        // Prüfe, ob die Zitationsbeziehung bereits existiert
        const existingRelationship = await databaseService.query(
          'SELECT id FROM citation_relationships WHERE citing_doi = ? AND cited_doi = ?',
          [citation.citing, doi]
        );
        
        if (existingRelationship && existingRelationship.length > 0) {
          // Aktualisiere den OCI, falls er jetzt verfügbar ist
          await databaseService.query(
            'UPDATE citation_relationships SET oci = ?, updated_at = NOW() WHERE id = ?',
            [citation.oci, existingRelationship[0].id]
          );
        } else {
          // Füge die neue Beziehung hinzu
          await databaseService.query(
            'INSERT INTO citation_relationships (citing_doi, cited_doi, oci, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())',
            [citation.citing, doi, citation.oci]
          );
        }
      }
      
      // Speichere die ausgehenden Zitationen in der Datenbank
      for (const reference of openCitationsData.references) {
        // Prüfe, ob die Zitationsbeziehung bereits existiert
        const existingRelationship = await databaseService.query(
          'SELECT id FROM citation_relationships WHERE citing_doi = ? AND cited_doi = ?',
          [doi, reference.cited]
        );
        
        if (existingRelationship && existingRelationship.length > 0) {
          // Aktualisiere den OCI, falls er jetzt verfügbar ist
          await databaseService.query(
            'UPDATE citation_relationships SET oci = ?, updated_at = NOW() WHERE id = ?',
            [reference.oci, existingRelationship[0].id]
          );
        } else {
          // Füge die neue Beziehung hinzu
          await databaseService.query(
            'INSERT INTO citation_relationships (citing_doi, cited_doi, oci, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())',
            [doi, reference.cited, reference.oci]
          );
        }
      }
      
      // Speichere die OpenCitations-Metadaten
      if (openCitationsData.metadata) {
        await databaseService.query(
          'INSERT INTO publication_external_data (doi, source, data, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW()) ' +
          'ON DUPLICATE KEY UPDATE data = ?, updated_at = NOW()',
          [
            doi, 
            'opencitations', 
            JSON.stringify(openCitationsData.metadata),
            JSON.stringify(openCitationsData.metadata)
          ]
        );
      }
      
      return {
        success: true,
        doi,
        citationsUpdated: openCitationsData.citations.length,
        referencesUpdated: openCitationsData.references.length,
        citationCount: openCitationsData.citationCount
      };
    } catch (error) {
      logger.error('Fehler bei der Aktualisierung von Zitationsdaten mit OpenCitations', {
        doi,
        error: error.message
      });
      
      return {
        success: false,
        doi,
        error: error.message
      };
    }
  }
  
  /**
   * Führt eine Anfrage an die OpenCitations API aus
   * 
   * @private
   * @param {string} endpoint - API-Endpunkt
   * @returns {Promise<Object>} API-Antwort
   */
  async _makeRequest(endpoint) {
    try {
      const url = `${this.baseUrl}${endpoint}`;
      
      const headers = {
        'Accept': 'application/json'
      };
      
      if (this.accessToken) {
        headers['authorization'] = this.accessToken;
      }
      
      const response = await fetch(url, { 
        headers,
        timeout: this.timeout
      });
      
      if (!response.ok) {
        throw new Error(`OpenCitations API Fehler: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      
      // Setze Retry-Zähler zurück
      this.retryCount = 0;
      
      return {
        success: true,
        data
      };
    } catch (error) {
      // Versuche erneut, falls konfiguriert
      if (this.retryCount < this.maxRetries) {
        this.retryCount++;
        
        logger.warn('OpenCitationsService: Fehler bei der Anfrage, versuche erneut', {
          endpoint,
          error: error.message,
          retryCount: this.retryCount
        });
        
        // Warte exponentiell länger zwischen Wiederholungsversuchen
        await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, this.retryCount)));
        
        return this._makeRequest(endpoint);
      }
      
      // Setze Retry-Zähler zurück
      this.retryCount = 0;
      
      logger.error('OpenCitationsService: Fehler bei der Anfrage', {
        endpoint,
        error: error.message
      });
      
      return {
        success: false,
        error: error.message
      };
    }
  }
}

export default OpenCitationsService;/**
 * @fileoverview Service für die Interaktion mit der OpenCitations API
 * 
 * Dieser Service ermöglicht den Zugriff auf die OpenCitations API, um Zitationsdaten,
 * Referenzen und Metadaten für wissenschaftliche Publikationen abzurufen.
 * OpenCitations ist eine unabhängige Infrastrukturorganisation, die offene
 * bibliografische und Zitationsdaten bereitstellt.
 */

import fetch from 'node-fetch';
import { logger } from '../utils/logger.js';

/**
 * Service für die Interaktion mit der OpenCitations API
 */
export class OpenCitationsService {
  /**
   * Erstellt eine neue Instanz des OpenCitationsService
   * 
   * @param {Object} options - Optionen für den Service
   * @param {string} [options.baseUrl] - Basis-URL der OpenCitations API
   * @param {string} [options.accessToken] - Zugriffstoken für die API
   * @param {number} [options.timeout] - Timeout für API-Anfragen in Millisekunden
   * @param {number} [options.maxRetries] - Maximale Anzahl von Wiederholungsversuchen
   */
  constructor(options = {}) {
    this.baseUrl = options.baseUrl || process.env.OPENCITATIONS_API_URL || 'https://opencitations.net/index/api/v1';
    this.accessToken = options.accessToken || process.env.OPENCITATIONS_ACCESS_TOKEN;
    this.timeout = options.timeout || 10000;
    this.maxRetries = options.maxRetries || 3;
    this.retryCount = 0;
  }
  
  /**
   * Initialisiert den Service
   */
  async initialize() {
    logger.info('OpenCitationsService wird initialisiert');
    
    // Prüfe, ob die API erreichbar ist
    try {
      const testResponse = await this._makeRequest('/citation-count/10.1108/jd-12-2013-0166');
      
      if (testResponse.success) {
        logger.info('OpenCitationsService erfolgreich initialisiert');
      } else {
        logger.warn('OpenCitationsService konnte nicht vollständig initialisiert werden', {
          error: testResponse.error
        });
      }
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des OpenCitationsService', {
        error: error.message
      });
    }
  }
  
  /**
   * Holt Zitationen für eine DOI (eingehende Zitationen)
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Zitationsdaten
   */
  async getCitations(doi) {
    return this._makeRequest(`/citations/${encodeURIComponent(doi)}`);
  }
  
  /**
   * Holt Referenzen für eine DOI (ausgehende Zitationen)
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Referenzdaten
   */
  async getReferences(doi) {
    return this._makeRequest(`/references/${encodeURIComponent(doi)}`);
  }
  
  /**
   * Holt die Anzahl der Zitationen für eine DOI
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<number>} Anzahl der Zitationen
   */
  async getCitationCount(doi) {
    const result = await this._makeRequest(`/citation-count/${encodeURIComponent(doi)}`);
    return result.success ? parseInt(result.data[0]?.count || 0) : 0;
  }
  
  /**
   * Holt Metadaten für eine DOI
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Metadaten
   */
  async getMetadata(doi) {
    return this._makeRequest(`/metadata/${encodeURIComponent(doi)}`);
  }
  
  /**
   * Holt Daten für eine spezifische Zitation (OCI)
   * 
   * @param {string} oci - Open Citation Identifier
   * @returns {Promise<Object>} Zitationsdaten
   */
  async getCitation(oci) {
    return this._makeRequest(`/citation/${encodeURIComponent(oci)}`);
  }
  
  /**
   * Holt alle verfügbaren Daten für eine DOI
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Alle verfügbaren Daten
   */
  async getAllDataForDOI(doi) {
    try {
      logger.info(`Hole alle OpenCitations-Daten für DOI ${doi}`);
      
      // Parallele Anfragen für bessere Performance
      const [citationsResult, referencesResult, citationCountResult, metadataResult] = await Promise.all([
        this.getCitations(doi),
        this.getReferences(doi),
        this.getCitationCount(doi),
        this.getMetadata(doi)
      ]);
      
      return {
        success: true,
        doi,
        citations: citationsResult.success ? citationsResult.data : [],
        references: referencesResult.success ? referencesResult.data : [],
        citationCount: citationCountResult,
        metadata: metadataResult.success ? metadataResult.data : null
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen aller OpenCitations-Daten', {
        doi,
        error: error.message
      });
      
      return {
        success: false,
        doi,
        error: error.message
      };
    }
  }
  
  /**
   * Aktualisiert die Zitationsdaten in der Datenbank mit OpenCitations-Daten
   * 
   * @param {string} doi - DOI der Publikation
   * @param {Object} databaseService - Datenbankdienst
   * @returns {Promise<Object>} Ergebnis der Aktualisierung
   */
  async updateCitationDataForDOI(doi, databaseService) {
    try {
      logger.info(`Aktualisiere Zitationsdaten für DOI ${doi} mit OpenCitations-Daten`);
      
      // Hole alle Daten von OpenCitations
      const openCitationsData = await this.getAllDataForDOI(doi);
      
      if (!openCitationsData.success) {
        return {
          success: false,
          doi,
          message: 'Fehler beim Abrufen von OpenCitations-Daten',
          error: openCitationsData.error
        };
      }
      
      // Aktualisiere die Zitationszählung in der Datenbank
      await databaseService.query(
        'UPDATE publication_metrics SET total_citations = ?, updated_at = NOW() WHERE doi = ? AND total_citations < ?',
        [openCitationsData.citationCount, doi, openCitationsData.citationCount]
      );
      
      // Speichere die eingehenden Zitationen in der Datenbank
      for (const citation of openCitationsData.citations) {
        // Prüfe, ob die Zitationsbeziehung bereits existiert
        const existingRelationship = await databaseService.query(
          'SELECT id FROM citation_relationships WHERE citing_doi = ? AND cited_doi = ?',
          [citation.citing, doi]
        );
        
        if (existingRelationship && existingRelationship.length > 0) {
          // Aktualisiere den OCI, falls er jetzt verfügbar ist
          await databaseService.query(
            'UPDATE citation_relationships SET oci = ?, updated_at = NOW() WHERE id = ?',
            [citation.oci, existingRelationship[0].id]
          );
        } else {
          // Füge die neue Beziehung hinzu
          await databaseService.query(
            'INSERT INTO citation_relationships (citing_doi, cited_doi, oci, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())',
            [citation.citing, doi, citation.oci]
          );
        }
      }
      
      // Speichere die ausgehenden Zitationen in der Datenbank
      for (const reference of openCitationsData.references) {
        // Prüfe, ob die Zitationsbeziehung bereits existiert
        const existingRelationship = await databaseService.query(
          'SELECT id FROM citation_relationships WHERE citing_doi = ? AND cited_doi = ?',
          [doi, reference.cited]
        );
        
        if (existingRelationship && existingRelationship.length > 0) {
          // Aktualisiere den OCI, falls er jetzt verfügbar ist
          await databaseService.query(
            'UPDATE citation_relationships SET oci = ?, updated_at = NOW() WHERE id = ?',
            [reference.oci, existingRelationship[0].id]
          );
        } else {
          // Füge die neue Beziehung hinzu
          await databaseService.query(
            'INSERT INTO citation_relationships (citing_doi, cited_doi, oci, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())',
            [doi, reference.cited, reference.oci]
          );
        }
      }
      
      // Speichere die OpenCitations-Metadaten
      if (openCitationsData.metadata) {
        await databaseService.query(
          'INSERT INTO publication_external_data (doi, source, data, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW()) ' +
          'ON DUPLICATE KEY UPDATE data = ?, updated_at = NOW()',
          [
            doi, 
            'opencitations', 
            JSON.stringify(openCitationsData.metadata),
            JSON.stringify(openCitationsData.metadata)
          ]
        );
      }
      
      return {
        success: true,
        doi,
        citationsUpdated: openCitationsData.citations.length,
        referencesUpdated: openCitationsData.references.length,
        citationCount: openCitationsData.citationCount
      };
    } catch (error) {
      logger.error('Fehler bei der Aktualisierung von Zitationsdaten mit OpenCitations', {
        doi,
        error: error.message
      });
      
      return {
        success: false,
        doi,
        error: error.message
      };
    }
  }
  
  /**
   * Führt eine Anfrage an die OpenCitations API aus
   * 
   * @private
   * @param {string} endpoint - API-Endpunkt
   * @returns {Promise<Object>} API-Antwort
   */
  async _makeRequest(endpoint) {
    try {
      const url = `${this.baseUrl}${endpoint}`;
      
      const headers = {
        'Accept': 'application/json'
      };
      
      if (this.accessToken) {
        headers['authorization'] = this.accessToken;
      }
      
      const response = await fetch(url, { 
        headers,
        timeout: this.timeout
      });
      
      if (!response.ok) {
        throw new Error(`OpenCitations API Fehler: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      
      // Setze Retry-Zähler zurück
      this.retryCount = 0;
      
      return {
        success: true,
        data
      };
    } catch (error) {
      // Versuche erneut, falls konfiguriert
      if (this.retryCount < this.maxRetries) {
        this.retryCount++;
        
        logger.warn('OpenCitationsService: Fehler bei der Anfrage, versuche erneut', {
          endpoint,
          error: error.message,
          retryCount: this.retryCount
        });
        
        // Warte exponentiell länger zwischen Wiederholungsversuchen
        await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, this.retryCount)));
        
        return this._makeRequest(endpoint);
      }
      
      // Setze Retry-Zähler zurück
      this.retryCount = 0;
      
      logger.error('OpenCitationsService: Fehler bei der Anfrage', {
        endpoint,
        error: error.message
      });
      
      return {
        success: false,
        error: error.message
      };
    }
  }
}

export default OpenCitationsService; * @fileoverview Service für die Interaktion mit der OpenCitations API
 * 
 * Dieser Service ermöglicht den Zugriff auf die OpenCitations API, um Zitationsdaten,
 * Referenzen und Metadaten für wissenschaftliche Publikationen abzurufen.
 * OpenCitations ist eine unabhängige Infrastrukturorganisation, die offene
 * bibliografische und Zitationsdaten bereitstellt.
 */

import fetch from 'node-fetch';
import { logger } from '../utils/logger.js';

/**
 * Service für die Interaktion mit der OpenCitations API
 */
export class OpenCitationsService {
  /**
   * Erstellt eine neue Instanz des OpenCitationsService
   * 
   * @param {Object} options - Optionen für den Service
   * @param {string} [options.baseUrl] - Basis-URL der OpenCitations API
   * @param {string} [options.accessToken] - Zugriffstoken für die API
   * @param {number} [options.timeout] - Timeout für API-Anfragen in Millisekunden
   * @param {number} [options.maxRetries] - Maximale Anzahl von Wiederholungsversuchen
   */
  constructor(options = {}) {
    this.baseUrl = options.baseUrl || process.env.OPENCITATIONS_API_URL || 'https://opencitations.net/index/api/v1';
    this.accessToken = options.accessToken || process.env.OPENCITATIONS_ACCESS_TOKEN;
    this.timeout = options.timeout || 10000;
    this.maxRetries = options.maxRetries || 3;
    this.retryCount = 0;
  }
  
  /**
   * Initialisiert den Service
   */
  async initialize() {
    logger.info('OpenCitationsService wird initialisiert');
    
    // Prüfe, ob die API erreichbar ist
    try {
      const testResponse = await this._makeRequest('/citation-count/10.1108/jd-12-2013-0166');
      
      if (testResponse.success) {
        logger.info('OpenCitationsService erfolgreich initialisiert');
      } else {
        logger.warn('OpenCitationsService konnte nicht vollständig initialisiert werden', {
          error: testResponse.error
        });
      }
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des OpenCitationsService', {
        error: error.message
      });
    }
  }
  
  /**
   * Holt Zitationen für eine DOI (eingehende Zitationen)
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Zitationsdaten
   */
  async getCitations(doi) {
    return this._makeRequest(`/citations/${encodeURIComponent(doi)}`);
  }
  
  /**
   * Holt Referenzen für eine DOI (ausgehende Zitationen)
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Referenzdaten
   */
  async getReferences(doi) {
    return this._makeRequest(`/references/${encodeURIComponent(doi)}`);
  }
  
  /**
   * Holt die Anzahl der Zitationen für eine DOI
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<number>} Anzahl der Zitationen
   */
  async getCitationCount(doi) {
    const result = await this._makeRequest(`/citation-count/${encodeURIComponent(doi)}`);
    return result.success ? parseInt(result.data[0]?.count || 0) : 0;
  }
  
  /**
   * Holt Metadaten für eine DOI
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Metadaten
   */
  async getMetadata(doi) {
    return this._makeRequest(`/metadata/${encodeURIComponent(doi)}`);
  }
  
  /**
   * Holt Daten für eine spezifische Zitation (OCI)
   * 
   * @param {string} oci - Open Citation Identifier
   * @returns {Promise<Object>} Zitationsdaten
   */
  async getCitation(oci) {
    return this._makeRequest(`/citation/${encodeURIComponent(oci)}`);
  }
  
  /**
   * Holt alle verfügbaren Daten für eine DOI
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Alle verfügbaren Daten
   */
  async getAllDataForDOI(doi) {
    try {
      logger.info(`Hole alle OpenCitations-Daten für DOI ${doi}`);
      
      // Parallele Anfragen für bessere Performance
      const [citationsResult, referencesResult, citationCountResult, metadataResult] = await Promise.all([
        this.getCitations(doi),
        this.getReferences(doi),
        this.getCitationCount(doi),
        this.getMetadata(doi)
      ]);
      
      return {
        success: true,
        doi,
        citations: citationsResult.success ? citationsResult.data : [],
        references: referencesResult.success ? referencesResult.data : [],
        citationCount: citationCountResult,
        metadata: metadataResult.success ? metadataResult.data : null
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen aller OpenCitations-Daten', {
        doi,
        error: error.message
      });
      
      return {
        success: false,
        doi,
        error: error.message
      };
    }
  }
  
  /**
   * Aktualisiert die Zitationsdaten in der Datenbank mit OpenCitations-Daten
   * 
   * @param {string} doi - DOI der Publikation
   * @param {Object} databaseService - Datenbankdienst
   * @returns {Promise<Object>} Ergebnis der Aktualisierung
   */
  async updateCitationDataForDOI(doi, databaseService) {
    try {
      logger.info(`Aktualisiere Zitationsdaten für DOI ${doi} mit OpenCitations-Daten`);
      
      // Hole alle Daten von OpenCitations
      const openCitationsData = await this.getAllDataForDOI(doi);
      
      if (!openCitationsData.success) {
        return {
          success: false,
          doi,
          message: 'Fehler beim Abrufen von OpenCitations-Daten',
          error: openCitationsData.error
        };
      }
      
      // Aktualisiere die Zitationszählung in der Datenbank
      await databaseService.query(
        'UPDATE publication_metrics SET total_citations = ?, updated_at = NOW() WHERE doi = ? AND total_citations < ?',
        [openCitationsData.citationCount, doi, openCitationsData.citationCount]
      );
      
      // Speichere die eingehenden Zitationen in der Datenbank
      for (const citation of openCitationsData.citations) {
        // Prüfe, ob die Zitationsbeziehung bereits existiert
        const existingRelationship = await databaseService.query(
          'SELECT id FROM citation_relationships WHERE citing_doi = ? AND cited_doi = ?',
          [citation.citing, doi]
        );
        
        if (existingRelationship && existingRelationship.length > 0) {
          // Aktualisiere den OCI, falls er jetzt verfügbar ist
          await databaseService.query(
            'UPDATE citation_relationships SET oci = ?, updated_at = NOW() WHERE id = ?',
            [citation.oci, existingRelationship[0].id]
          );
        } else {
          // Füge die neue Beziehung hinzu
          await databaseService.query(
            'INSERT INTO citation_relationships (citing_doi, cited_doi, oci, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())',
            [citation.citing, doi, citation.oci]
          );
        }
      }
      
      // Speichere die ausgehenden Zitationen in der Datenbank
      for (const reference of openCitationsData.references) {
        // Prüfe, ob die Zitationsbeziehung bereits existiert
        const existingRelationship = await databaseService.query(
          'SELECT id FROM citation_relationships WHERE citing_doi = ? AND cited_doi = ?',
          [doi, reference.cited]
        );
        
        if (existingRelationship && existingRelationship.length > 0) {
          // Aktualisiere den OCI, falls er jetzt verfügbar ist
          await databaseService.query(
            'UPDATE citation_relationships SET oci = ?, updated_at = NOW() WHERE id = ?',
            [reference.oci, existingRelationship[0].id]
          );
        } else {
          // Füge die neue Beziehung hinzu
          await databaseService.query(
            'INSERT INTO citation_relationships (citing_doi, cited_doi, oci, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())',
            [doi, reference.cited, reference.oci]
          );
        }
      }
      
      // Speichere die OpenCitations-Metadaten
      if (openCitationsData.metadata) {
        await databaseService.query(
          'INSERT INTO publication_external_data (doi, source, data, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW()) ' +
          'ON DUPLICATE KEY UPDATE data = ?, updated_at = NOW()',
          [
            doi, 
            'opencitations', 
            JSON.stringify(openCitationsData.metadata),
            JSON.stringify(openCitationsData.metadata)
          ]
        );
      }
      
      return {
        success: true,
        doi,
        citationsUpdated: openCitationsData.citations.length,
        referencesUpdated: openCitationsData.references.length,
        citationCount: openCitationsData.citationCount
      };
    } catch (error) {
      logger.error('Fehler bei der Aktualisierung von Zitationsdaten mit OpenCitations', {
        doi,
        error: error.message
      });
      
      return {
        success: false,
        doi,
        error: error.message
      };
    }
  }
  
  /**
   * Führt eine Anfrage an die OpenCitations API aus
   * 
   * @private
   * @param {string} endpoint - API-Endpunkt
   * @returns {Promise<Object>} API-Antwort
   */
  async _makeRequest(endpoint) {
    try {
      const url = `${this.baseUrl}${endpoint}`;
      
      const headers = {
        'Accept': 'application/json'
      };
      
      if (this.accessToken) {
        headers['authorization'] = this.accessToken;
      }
      
      const response = await fetch(url, { 
        headers,
        timeout: this.timeout
      });
      
      if (!response.ok) {
        throw new Error(`OpenCitations API Fehler: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      
      // Setze Retry-Zähler zurück
      this.retryCount = 0;
      
      return {
        success: true,
        data
      };
    } catch (error) {
      // Versuche erneut, falls konfiguriert
      if (this.retryCount < this.maxRetries) {
        this.retryCount++;
        
        logger.warn('OpenCitationsService: Fehler bei der Anfrage, versuche erneut', {
          endpoint,
          error: error.message,
          retryCount: this.retryCount
        });
        
        // Warte exponentiell länger zwischen Wiederholungsversuchen
        await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, this.retryCount)));
        
        return this._makeRequest(endpoint);
      }
      
      // Setze Retry-Zähler zurück
      this.retryCount = 0;
      
      logger.error('OpenCitationsService: Fehler bei der Anfrage', {
        endpoint,
        error: error.message
      });
      
      return {
        success: false,
        error: error.message
      };
    }
  }
}

export default OpenCitationsService; * @fileoverview Service für die Interaktion mit der OpenCitations API
 * 
 * Dieser Service ermöglicht den Zugriff auf die OpenCitations API, um Zitationsdaten,
 * Referenzen und Metadaten für wissenschaftliche Publikationen abzurufen.
 * OpenCitations ist eine unabhängige Infrastrukturorganisation, die offene
 * bibliografische und Zitationsdaten bereitstellt.
 */

import fetch from 'node-fetch';
import { logger } from '../utils/logger.js';

/**
 * Service für die Interaktion mit der OpenCitations API
 */
export class OpenCitationsService {
  /**
   * Erstellt eine neue Instanz des OpenCitationsService
   * 
   * @param {Object} options - Optionen für den Service
   * @param {string} [options.baseUrl] - Basis-URL der OpenCitations API
   * @param {string} [options.accessToken] - Zugriffstoken für die API
   * @param {number} [options.timeout] - Timeout für API-Anfragen in Millisekunden
   * @param {number} [options.maxRetries] - Maximale Anzahl von Wiederholungsversuchen
   */
  constructor(options = {}) {
    this.baseUrl = options.baseUrl || process.env.OPENCITATIONS_API_URL || 'https://opencitations.net/index/api/v1';
    this.accessToken = options.accessToken || process.env.OPENCITATIONS_ACCESS_TOKEN;
    this.timeout = options.timeout || 10000;
    this.maxRetries = options.maxRetries || 3;
    this.retryCount = 0;
  }
  
  /**
   * Initialisiert den Service
   */
  async initialize() {
    logger.info('OpenCitationsService wird initialisiert');
    
    // Prüfe, ob die API erreichbar ist
    try {
      const testResponse = await this._makeRequest('/citation-count/10.1108/jd-12-2013-0166');
      
      if (testResponse.success) {
        logger.info('OpenCitationsService erfolgreich initialisiert');
      } else {
        logger.warn('OpenCitationsService konnte nicht vollständig initialisiert werden', {
          error: testResponse.error
        });
      }
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des OpenCitationsService', {
        error: error.message
      });
    }
  }
  
  /**
   * Holt Zitationen für eine DOI (eingehende Zitationen)
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Zitationsdaten
   */
  async getCitations(doi) {
    return this._makeRequest(`/citations/${encodeURIComponent(doi)}`);
  }
  
  /**
   * Holt Referenzen für eine DOI (ausgehende Zitationen)
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Referenzdaten
   */
  async getReferences(doi) {
    return this._makeRequest(`/references/${encodeURIComponent(doi)}`);
  }
  
  /**
   * Holt die Anzahl der Zitationen für eine DOI
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<number>} Anzahl der Zitationen
   */
  async getCitationCount(doi) {
    const result = await this._makeRequest(`/citation-count/${encodeURIComponent(doi)}`);
    return result.success ? parseInt(result.data[0]?.count || 0) : 0;
  }
  
  /**
   * Holt Metadaten für eine DOI
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Metadaten
   */
  async getMetadata(doi) {
    return this._makeRequest(`/metadata/${encodeURIComponent(doi)}`);
  }
  
  /**
   * Holt Daten für eine spezifische Zitation (OCI)
   * 
   * @param {string} oci - Open Citation Identifier
   * @returns {Promise<Object>} Zitationsdaten
   */
  async getCitation(oci) {
    return this._makeRequest(`/citation/${encodeURIComponent(oci)}`);
  }
  
  /**
   * Holt alle verfügbaren Daten für eine DOI
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Alle verfügbaren Daten
   */
  async getAllDataForDOI(doi) {
    try {
      logger.info(`Hole alle OpenCitations-Daten für DOI ${doi}`);
      
      // Parallele Anfragen für bessere Performance
      const [citationsResult, referencesResult, citationCountResult, metadataResult] = await Promise.all([
        this.getCitations(doi),
        this.getReferences(doi),
        this.getCitationCount(doi),
        this.getMetadata(doi)
      ]);
      
      return {
        success: true,
        doi,
        citations: citationsResult.success ? citationsResult.data : [],
        references: referencesResult.success ? referencesResult.data : [],
        citationCount: citationCountResult,
        metadata: metadataResult.success ? metadataResult.data : null
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen aller OpenCitations-Daten', {
        doi,
        error: error.message
      });
      
      return {
        success: false,
        doi,
        error: error.message
      };
    }
  }
  
  /**
   * Aktualisiert die Zitationsdaten in der Datenbank mit OpenCitations-Daten
   * 
   * @param {string} doi - DOI der Publikation
   * @param {Object} databaseService - Datenbankdienst
   * @returns {Promise<Object>} Ergebnis der Aktualisierung
   */
  async updateCitationDataForDOI(doi, databaseService) {
    try {
      logger.info(`Aktualisiere Zitationsdaten für DOI ${doi} mit OpenCitations-Daten`);
      
      // Hole alle Daten von OpenCitations
      const openCitationsData = await this.getAllDataForDOI(doi);
      
      if (!openCitationsData.success) {
        return {
          success: false,
          doi,
          message: 'Fehler beim Abrufen von OpenCitations-Daten',
          error: openCitationsData.error
        };
      }
      
      // Aktualisiere die Zitationszählung in der Datenbank
      await databaseService.query(
        'UPDATE publication_metrics SET total_citations = ?, updated_at = NOW() WHERE doi = ? AND total_citations < ?',
        [openCitationsData.citationCount, doi, openCitationsData.citationCount]
      );
      
      // Speichere die eingehenden Zitationen in der Datenbank
      for (const citation of openCitationsData.citations) {
        // Prüfe, ob die Zitationsbeziehung bereits existiert
        const existingRelationship = await databaseService.query(
          'SELECT id FROM citation_relationships WHERE citing_doi = ? AND cited_doi = ?',
          [citation.citing, doi]
        );
        
        if (existingRelationship && existingRelationship.length > 0) {
          // Aktualisiere den OCI, falls er jetzt verfügbar ist
          await databaseService.query(
            'UPDATE citation_relationships SET oci = ?, updated_at = NOW() WHERE id = ?',
            [citation.oci, existingRelationship[0].id]
          );
        } else {
          // Füge die neue Beziehung hinzu
          await databaseService.query(
            'INSERT INTO citation_relationships (citing_doi, cited_doi, oci, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())',
            [citation.citing, doi, citation.oci]
          );
        }
      }
      
      // Speichere die ausgehenden Zitationen in der Datenbank
      for (const reference of openCitationsData.references) {
        // Prüfe, ob die Zitationsbeziehung bereits existiert
        const existingRelationship = await databaseService.query(
          'SELECT id FROM citation_relationships WHERE citing_doi = ? AND cited_doi = ?',
          [doi, reference.cited]
        );
        
        if (existingRelationship && existingRelationship.length > 0) {
          // Aktualisiere den OCI, falls er jetzt verfügbar ist
          await databaseService.query(
            'UPDATE citation_relationships SET oci = ?, updated_at = NOW() WHERE id = ?',
            [reference.oci, existingRelationship[0].id]
          );
        } else {
          // Füge die neue Beziehung hinzu
          await databaseService.query(
            'INSERT INTO citation_relationships (citing_doi, cited_doi, oci, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())',
            [doi, reference.cited, reference.oci]
          );
        }
      }
      
      // Speichere die OpenCitations-Metadaten
      if (openCitationsData.metadata) {
        await databaseService.query(
          'INSERT INTO publication_external_data (doi, source, data, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW()) ' +
          'ON DUPLICATE KEY UPDATE data = ?, updated_at = NOW()',
          [
            doi, 
            'opencitations', 
            JSON.stringify(openCitationsData.metadata),
            JSON.stringify(openCitationsData.metadata)
          ]
        );
      }
      
      return {
        success: true,
        doi,
        citationsUpdated: openCitationsData.citations.length,
        referencesUpdated: openCitationsData.references.length,
        citationCount: openCitationsData.citationCount
      };
    } catch (error) {
      logger.error('Fehler bei der Aktualisierung von Zitationsdaten mit OpenCitations', {
        doi,
        error: error.message
      });
      
      return {
        success: false,
        doi,
        error: error.message
      };
    }
  }
  
  /**
   * Führt eine Anfrage an die OpenCitations API aus
   * 
   * @private
   * @param {string} endpoint - API-Endpunkt
   * @returns {Promise<Object>} API-Antwort
   */
  async _makeRequest(endpoint) {
    try {
      const url = `${this.baseUrl}${endpoint}`;
      
      const headers = {
        'Accept': 'application/json'
      };
      
      if (this.accessToken) {
        headers['authorization'] = this.accessToken;
      }
      
      const response = await fetch(url, { 
        headers,
        timeout: this.timeout
      });
      
      if (!response.ok) {
        throw new Error(`OpenCitations API Fehler: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      
      // Setze Retry-Zähler zurück
      this.retryCount = 0;
      
      return {
        success: true,
        data
      };
    } catch (error) {
      // Versuche erneut, falls konfiguriert
      if (this.retryCount < this.maxRetries) {
        this.retryCount++;
        
        logger.warn('OpenCitationsService: Fehler bei der Anfrage, versuche erneut', {
          endpoint,
          error: error.message,
          retryCount: this.retryCount
        });
        
        // Warte exponentiell länger zwischen Wiederholungsversuchen
        await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, this.retryCount)));
        
        return this._makeRequest(endpoint);
      }
      
      // Setze Retry-Zähler zurück
      this.retryCount = 0;
      
      logger.error('OpenCitationsService: Fehler bei der Anfrage', {
        endpoint,
        error: error.message
      });
      
      return {
        success: false,
        error: error.message
      };
    }
  }
}

export default OpenCitationsService;export default OpenCitationsService; * @fileoverview Service für die Interaktion mit der OpenCitations API
 * 
 * Dieser Service ermöglicht den Zugriff auf die OpenCitations API, um Zitationsdaten,
 * Referenzen und Metadaten für wissenschaftliche Publikationen abzurufen.
 * OpenCitations ist eine unabhängige Infrastrukturorganisation, die offene
 * bibliografische und Zitationsdaten bereitstellt.
 */

import fetch from 'node-fetch';
import { logger } from '../utils/logger.js';

/**
 * Service für die Interaktion mit der OpenCitations API
 */
export class OpenCitationsService {
  /**
   * Erstellt eine neue Instanz des OpenCitationsService
   * 
   * @param {Object} options - Optionen für den Service
   * @param {string} [options.baseUrl] - Basis-URL der OpenCitations API
   * @param {string} [options.accessToken] - Zugriffstoken für die API
   * @param {number} [options.timeout] - Timeout für API-Anfragen in Millisekunden
   * @param {number} [options.maxRetries] - Maximale Anzahl von Wiederholungsversuchen
   */
  constructor(options = {}) {
    this.baseUrl = options.baseUrl || process.env.OPENCITATIONS_API_URL || 'https://opencitations.net/index/api/v1';
    this.accessToken = options.accessToken || process.env.OPENCITATIONS_ACCESS_TOKEN;
    this.timeout = options.timeout || 10000;
    this.maxRetries = options.maxRetries || 3;
    this.retryCount = 0;
  }
  
  /**
   * Initialisiert den Service
   */
  async initialize() {
    logger.info('OpenCitationsService wird initialisiert');
    
    // Prüfe, ob die API erreichbar ist
    try {
      const testResponse = await this._makeRequest('/citation-count/10.1108/jd-12-2013-0166');
      
      if (testResponse.success) {
        logger.info('OpenCitationsService erfolgreich initialisiert');
      } else {
        logger.warn('OpenCitationsService konnte nicht vollständig initialisiert werden', {
          error: testResponse.error
        });
      }
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des OpenCitationsService', {
        error: error.message
      });
    }
  }
  
  /**
   * Holt Zitationen für eine DOI (eingehende Zitationen)
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Zitationsdaten
   */
  async getCitations(doi) {
    return this._makeRequest(`/citations/${encodeURIComponent(doi)}`);
  }
  
  /**
   * Holt Referenzen für eine DOI (ausgehende Zitationen)
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Referenzdaten
   */
  async getReferences(doi) {
    return this._makeRequest(`/references/${encodeURIComponent(doi)}`);
  }
  
  /**
   * Holt die Anzahl der Zitationen für eine DOI
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<number>} Anzahl der Zitationen
   */
  async getCitationCount(doi) {
    const result = await this._makeRequest(`/citation-count/${encodeURIComponent(doi)}`);
    return result.success ? parseInt(result.data[0]?.count || 0) : 0;
  }
  
  /**
   * Holt Metadaten für eine DOI
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Metadaten
   */
  async getMetadata(doi) {
    return this._makeRequest(`/metadata/${encodeURIComponent(doi)}`);
  }
  
  /**
   * Holt Daten für eine spezifische Zitation (OCI)
   * 
   * @param {string} oci - Open Citation Identifier
   * @returns {Promise<Object>} Zitationsdaten
   */
  async getCitation(oci) {
    return this._makeRequest(`/citation/${encodeURIComponent(oci)}`);
  }
  
  /**
   * Holt alle verfügbaren Daten für eine DOI
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Alle verfügbaren Daten
   */
  async getAllDataForDOI(doi) {
    try {
      logger.info(`Hole alle OpenCitations-Daten für DOI ${doi}`);
      
      // Parallele Anfragen für bessere Performance
      const [citationsResult, referencesResult, citationCountResult, metadataResult] = await Promise.all([
        this.getCitations(doi),
        this.getReferences(doi),
        this.getCitationCount(doi),
        this.getMetadata(doi)
      ]);
      
      return {
        success: true,
        doi,
        citations: citationsResult.success ? citationsResult.data : [],
        references: referencesResult.success ? referencesResult.data : [],
        citationCount: citationCountResult,
        metadata: metadataResult.success ? metadataResult.data : null
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen aller OpenCitations-Daten', {
        doi,
        error: error.message
      });
      
      return {
        success: false,
        doi,
        error: error.message
      };
    }
  }
  
  /**
   * Aktualisiert die Zitationsdaten in der Datenbank mit OpenCitations-Daten
   * 
   * @param {string} doi - DOI der Publikation
   * @param {Object} databaseService - Datenbankdienst
   * @returns {Promise<Object>} Ergebnis der Aktualisierung
   */
  async updateCitationDataForDOI(doi, databaseService) {
    try {
      logger.info(`Aktualisiere Zitationsdaten für DOI ${doi} mit OpenCitations-Daten`);
      
      // Hole alle Daten von OpenCitations
      const openCitationsData = await this.getAllDataForDOI(doi);
      
      if (!openCitationsData.success) {
        return {
          success: false,
          doi,
          message: 'Fehler beim Abrufen von OpenCitations-Daten',
          error: openCitationsData.error
        };
      }
      
      // Aktualisiere die Zitationszählung in der Datenbank
      await databaseService.query(
        'UPDATE publication_metrics SET total_citations = ?, updated_at = NOW() WHERE doi = ? AND total_citations < ?',
        [openCitationsData.citationCount, doi, openCitationsData.citationCount]
      );
      
      // Speichere die eingehenden Zitationen in der Datenbank
      for (const citation of openCitationsData.citations) {
        // Prüfe, ob die Zitationsbeziehung bereits existiert
        const existingRelationship = await databaseService.query(
          'SELECT id FROM citation_relationships WHERE citing_doi = ? AND cited_doi = ?',
          [citation.citing, doi]
        );
        
        if (existingRelationship && existingRelationship.length > 0) {
          // Aktualisiere den OCI, falls er jetzt verfügbar ist
          await databaseService.query(
            'UPDATE citation_relationships SET oci = ?, updated_at = NOW() WHERE id = ?',
            [citation.oci, existingRelationship[0].id]
          );
        } else {
          // Füge die neue Beziehung hinzu
          await databaseService.query(
            'INSERT INTO citation_relationships (citing_doi, cited_doi, oci, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())',
            [citation.citing, doi, citation.oci]
          );
        }
      }
      
      // Speichere die ausgehenden Zitationen in der Datenbank
      for (const reference of openCitationsData.references) {
        // Prüfe, ob die Zitationsbeziehung bereits existiert
        const existingRelationship = await databaseService.query(
          'SELECT id FROM citation_relationships WHERE citing_doi = ? AND cited_doi = ?',
          [doi, reference.cited]
        );
        
        if (existingRelationship && existingRelationship.length > 0) {
          // Aktualisiere den OCI, falls er jetzt verfügbar ist
          await databaseService.query(
            'UPDATE citation_relationships SET oci = ?, updated_at = NOW() WHERE id = ?',
            [reference.oci, existingRelationship[0].id]
          );
        } else {
          // Füge die neue Beziehung hinzu
          await databaseService.query(
            'INSERT INTO citation_relationships (citing_doi, cited_doi, oci, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())',
            [doi, reference.cited, reference.oci]
          );
        }
      }
      
      // Speichere die OpenCitations-Metadaten
      if (openCitationsData.metadata) {
        await databaseService.query(
          'INSERT INTO publication_external_data (doi, source, data, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW()) ' +
          'ON DUPLICATE KEY UPDATE data = ?, updated_at = NOW()',
          [
            doi, 
            'opencitations', 
            JSON.stringify(openCitationsData.metadata),
            JSON.stringify(openCitationsData.metadata)
          ]
        );
      }
      
      return {
        success: true,
        doi,
        citationsUpdated: openCitationsData.citations.length,
        referencesUpdated: openCitationsData.references.length,
        citationCount: openCitationsData.citationCount
      };
    } catch (error) {
      logger.error('Fehler bei der Aktualisierung von Zitationsdaten mit OpenCitations', {
        doi,
        error: error.message
      });
      
      return {
        success: false,
        doi,
        error: error.message
      };
    }
  }
  
  /**
   * Führt eine Anfrage an die OpenCitations API aus
   * 
   * @private
   * @param {string} endpoint - API-Endpunkt
   * @returns {Promise<Object>} API-Antwort
   */
  async _makeRequest(endpoint) {
    try {
      const url = `${this.baseUrl}${endpoint}`;
      
      const headers = {
        'Accept': 'application/json'
      };
      
      if (this.accessToken) {
        headers['authorization'] = this.accessToken;
      }
      
      const response = await fetch(url, { 
        headers,
        timeout: this.timeout
      });
      
      if (!response.ok) {
        throw new Error(`OpenCitations API Fehler: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      
      // Setze Retry-Zähler zurück
      this.retryCount = 0;
      
      return {
        success: true,
        data
      };
    } catch (error) {
      // Versuche erneut, falls konfiguriert
      if (this.retryCount < this.maxRetries) {
        this.retryCount++;
        
        logger.warn('OpenCitationsService: Fehler bei der Anfrage, versuche erneut', {
          endpoint,
          error: error.message,
          retryCount: this.retryCount
        });
        
        // Warte exponentiell länger zwischen Wiederholungsversuchen
        await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, this.retryCount)));
        
        return this._makeRequest(endpoint);
      }
      
      // Setze Retry-Zähler zurück
      this.retryCount = 0;
      
      logger.error('OpenCitationsService: Fehler bei der Anfrage', {
        endpoint,
        error: error.message
      });
      
      return {
        success: false,
        error: error.message
      };
    }
  }
}

export default OpenCitationsService; * 
 * Dieser Service ermöglicht den Zugriff auf die OpenCitations API, um Zitationsdaten,
 * Referenzen und Metadaten für wissenschaftliche Publikationen abzurufen.
 * OpenCitations ist eine unabhängige Infrastrukturorganisation, die offene
 * bibliografische und Zitationsdaten bereitstellt.
 */

import fetch from 'node-fetch';
import { logger } from '../utils/logger.js';

/**
 * Service für die Interaktion mit der OpenCitations API
 */
export class OpenCitationsService {
  /**
   * Erstellt eine neue Instanz des OpenCitationsService
   * 
   * @param {Object} options - Optionen für den Service
   * @param {string} [options.baseUrl] - Basis-URL der OpenCitations API
   * @param {string} [options.accessToken] - Zugriffstoken für die API
   * @param {number} [options.timeout] - Timeout für API-Anfragen in Millisekunden
   * @param {number} [options.maxRetries] - Maximale Anzahl von Wiederholungsversuchen
   */
  constructor(options = {}) {
    this.baseUrl = options.baseUrl || process.env.OPENCITATIONS_API_URL || 'https://opencitations.net/index/api/v1';
    this.accessToken = options.accessToken || process.env.OPENCITATIONS_ACCESS_TOKEN;
    this.timeout = options.timeout || 10000;
    this.maxRetries = options.maxRetries || 3;
    this.retryCount = 0;
  }
  
  /**
   * Initialisiert den Service
   */
  async initialize() {
    logger.info('OpenCitationsService wird initialisiert');
    
    // Prüfe, ob die API erreichbar ist
    try {
      const testResponse = await this._makeRequest('/citation-count/10.1108/jd-12-2013-0166');
      
      if (testResponse.success) {
        logger.info('OpenCitationsService erfolgreich initialisiert');
      } else {
        logger.warn('OpenCitationsService konnte nicht vollständig initialisiert werden', {
          error: testResponse.error
        });
      }
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des OpenCitationsService', {
        error: error.message
      });
    }
  }
  
  /**
   * Holt Zitationen für eine DOI (eingehende Zitationen)
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Zitationsdaten
   */
  async getCitations(doi) {
    return this._makeRequest(`/citations/${encodeURIComponent(doi)}`);
  }
  
  /**
   * Holt Referenzen für eine DOI (ausgehende Zitationen)
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Referenzdaten
   */
  async getReferences(doi) {
    return this._makeRequest(`/references/${encodeURIComponent(doi)}`);
  }
  
  /**
   * Holt die Anzahl der Zitationen für eine DOI
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<number>} Anzahl der Zitationen
   */
  async getCitationCount(doi) {
    const result = await this._makeRequest(`/citation-count/${encodeURIComponent(doi)}`);
    return result.success ? parseInt(result.data[0]?.count || 0) : 0;
  }
  
  /**
   * Holt Metadaten für eine DOI
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Metadaten
   */
  async getMetadata(doi) {
    return this._makeRequest(`/metadata/${encodeURIComponent(doi)}`);
  }
  
  /**
   * Holt Daten für eine spezifische Zitation (OCI)
   * 
   * @param {string} oci - Open Citation Identifier
   * @returns {Promise<Object>} Zitationsdaten
   */
  async getCitation(oci) {
    return this._makeRequest(`/citation/${encodeURIComponent(oci)}`);
  }
  
  /**
   * Holt alle verfügbaren Daten für eine DOI
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Alle verfügbaren Daten
   */
  async getAllDataForDOI(doi) {
    try {
      logger.info(`Hole alle OpenCitations-Daten für DOI ${doi}`);
      
      // Parallele Anfragen für bessere Performance
      const [citationsResult, referencesResult, citationCountResult, metadataResult] = await Promise.all([
        this.getCitations(doi),
        this.getReferences(doi),
        this.getCitationCount(doi),
        this.getMetadata(doi)
      ]);
      
      return {
        success: true,
        doi,
        citations: citationsResult.success ? citationsResult.data : [],
        references: referencesResult.success ? referencesResult.data : [],
        citationCount: citationCountResult,
        metadata: metadataResult.success ? metadataResult.data : null
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen aller OpenCitations-Daten', {
        doi,
        error: error.message
      });
      
      return {
        success: false,
        doi,
        error: error.message
      };
    }
  }
  
  /**
   * Aktualisiert die Zitationsdaten in der Datenbank mit OpenCitations-Daten
   * 
   * @param {string} doi - DOI der Publikation
   * @param {Object} databaseService - Datenbankdienst
   * @returns {Promise<Object>} Ergebnis der Aktualisierung
   */
  async updateCitationDataForDOI(doi, databaseService) {
    try {
      logger.info(`Aktualisiere Zitationsdaten für DOI ${doi} mit OpenCitations-Daten`);
      
      // Hole alle Daten von OpenCitations
      const openCitationsData = await this.getAllDataForDOI(doi);
      
      if (!openCitationsData.success) {
        return {
          success: false,
          doi,
          message: 'Fehler beim Abrufen von OpenCitations-Daten',
          error: openCitationsData.error
        };
      }
      
      // Aktualisiere die Zitationszählung in der Datenbank
      await databaseService.query(
        'UPDATE publication_metrics SET total_citations = ?, updated_at = NOW() WHERE doi = ? AND total_citations < ?',
        [openCitationsData.citationCount, doi, openCitationsData.citationCount]
      );
      
      // Speichere die eingehenden Zitationen in der Datenbank
      for (const citation of openCitationsData.citations) {
        // Prüfe, ob die Zitationsbeziehung bereits existiert
        const existingRelationship = await databaseService.query(
          'SELECT id FROM citation_relationships WHERE citing_doi = ? AND cited_doi = ?',
          [citation.citing, doi]
        );
        
        if (existingRelationship && existingRelationship.length > 0) {
          // Aktualisiere den OCI, falls er jetzt verfügbar ist
          await databaseService.query(
            'UPDATE citation_relationships SET oci = ?, updated_at = NOW() WHERE id = ?',
            [citation.oci, existingRelationship[0].id]
          );
        } else {
          // Füge die neue Beziehung hinzu
          await databaseService.query(
            'INSERT INTO citation_relationships (citing_doi, cited_doi, oci, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())',
            [citation.citing, doi, citation.oci]
          );
        }
      }
      
      // Speichere die ausgehenden Zitationen in der Datenbank
      for (const reference of openCitationsData.references) {
        // Prüfe, ob die Zitationsbeziehung bereits existiert
        const existingRelationship = await databaseService.query(
          'SELECT id FROM citation_relationships WHERE citing_doi = ? AND cited_doi = ?',
          [doi, reference.cited]
        );
        
        if (existingRelationship && existingRelationship.length > 0) {
          // Aktualisiere den OCI, falls er jetzt verfügbar ist
          await databaseService.query(
            'UPDATE citation_relationships SET oci = ?, updated_at = NOW() WHERE id = ?',
            [reference.oci, existingRelationship[0].id]
          );
        } else {
          // Füge die neue Beziehung hinzu
          await databaseService.query(
            'INSERT INTO citation_relationships (citing_doi, cited_doi, oci, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())',
            [doi, reference.cited, reference.oci]
          );
        }
      }
      
      // Speichere die OpenCitations-Metadaten
      if (openCitationsData.metadata) {
        await databaseService.query(
          'INSERT INTO publication_external_data (doi, source, data, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW()) ' +
          'ON DUPLICATE KEY UPDATE data = ?, updated_at = NOW()',
          [
            doi, 
            'opencitations', 
            JSON.stringify(openCitationsData.metadata),
            JSON.stringify(openCitationsData.metadata)
          ]
        );
      }
      
      return {
        success: true,
        doi,
        citationsUpdated: openCitationsData.citations.length,
        referencesUpdated: openCitationsData.references.length,
        citationCount: openCitationsData.citationCount
      };
    } catch (error) {
      logger.error('Fehler bei der Aktualisierung von Zitationsdaten mit OpenCitations', {
        doi,
        error: error.message
      });
      
      return {
        success: false,
        doi,
        error: error.message
      };
    }
  }
  
  /**
   * Führt eine Anfrage an die OpenCitations API aus
   * 
   * @private
   * @param {string} endpoint - API-Endpunkt
   * @returns {Promise<Object>} API-Antwort
   */
  async _makeRequest(endpoint) {
    try {
      const url = `${this.baseUrl}${endpoint}`;
      
      const headers = {
        'Accept': 'application/json'
      };
      
      if (this.accessToken) {
        headers['authorization'] = this.accessToken;
      }
      
      const response = await fetch(url, { 
        headers,
        timeout: this.timeout
      });
      
      if (!response.ok) {
        throw new Error(`OpenCitations API Fehler: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      
      // Setze Retry-Zähler zurück
      this.retryCount = 0;
      
      return {
        success: true,
        data
      };
    } catch (error) {
      // Versuche erneut, falls konfiguriert
      if (this.retryCount < this.maxRetries) {
        this.retryCount++;
        
        logger.warn('OpenCitationsService: Fehler bei der Anfrage, versuche erneut', {
          endpoint,
          error: error.message,
          retryCount: this.retryCount
        });
        
        // Warte exponentiell länger zwischen Wiederholungsversuchen
        await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, this.retryCount)));
        
        return this._makeRequest(endpoint);
      }
      
      // Setze Retry-Zähler zurück
      this.retryCount = 0;
      
      logger.error('OpenCitationsService: Fehler bei der Anfrage', {
        endpoint,
        error: error.message
      });
      
      return {
        success: false,
        error: error.message
      };
    }
  }
}

export default OpenCitationsService;export default OpenCitationsService;export default OpenCitationsService; * @fileoverview Service für die Interaktion mit der OpenCitations API
 * 
 * Dieser Service ermöglicht den Zugriff auf die OpenCitations API, um Zitationsdaten,
 * Referenzen und Metadaten für wissenschaftliche Publikationen abzurufen.
 * OpenCitations ist eine unabhängige Infrastrukturorganisation, die offene
 * bibliografische und Zitationsdaten bereitstellt.
 */

import fetch from 'node-fetch';
import { logger } from '../utils/logger.js';

/**
 * Service für die Interaktion mit der OpenCitations API
 */
export class OpenCitationsService {
  /**
   * Erstellt eine neue Instanz des OpenCitationsService
   * 
   * @param {Object} options - Optionen für den Service
   * @param {string} [options.baseUrl] - Basis-URL der OpenCitations API
   * @param {string} [options.accessToken] - Zugriffstoken für die API
   * @param {number} [options.timeout] - Timeout für API-Anfragen in Millisekunden
   * @param {number} [options.maxRetries] - Maximale Anzahl von Wiederholungsversuchen
   */
  constructor(options = {}) {
    this.baseUrl = options.baseUrl || process.env.OPENCITATIONS_API_URL || 'https://opencitations.net/index/api/v1';
    this.accessToken = options.accessToken || process.env.OPENCITATIONS_ACCESS_TOKEN;
    this.timeout = options.timeout || 10000;
    this.maxRetries = options.maxRetries || 3;
    this.retryCount = 0;
  }
  
  /**
   * Initialisiert den Service
   */
  async initialize() {
    logger.info('OpenCitationsService wird initialisiert');
    
    // Prüfe, ob die API erreichbar ist
    try {
      const testResponse = await this._makeRequest('/citation-count/10.1108/jd-12-2013-0166');
      
      if (testResponse.success) {
        logger.info('OpenCitationsService erfolgreich initialisiert');
      } else {
        logger.warn('OpenCitationsService konnte nicht vollständig initialisiert werden', {
          error: testResponse.error
        });
      }
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des OpenCitationsService', {
        error: error.message
      });
    }
  }
  
  /**
   * Holt Zitationen für eine DOI (eingehende Zitationen)
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Zitationsdaten
   */
  async getCitations(doi) {
    return this._makeRequest(`/citations/${encodeURIComponent(doi)}`);
  }
  
  /**
   * Holt Referenzen für eine DOI (ausgehende Zitationen)
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Referenzdaten
   */
  async getReferences(doi) {
    return this._makeRequest(`/references/${encodeURIComponent(doi)}`);
  }
  
  /**
   * Holt die Anzahl der Zitationen für eine DOI
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<number>} Anzahl der Zitationen
   */
  async getCitationCount(doi) {
    const result = await this._makeRequest(`/citation-count/${encodeURIComponent(doi)}`);
    return result.success ? parseInt(result.data[0]?.count || 0) : 0;
  }
  
  /**
   * Holt Metadaten für eine DOI
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Metadaten
   */
  async getMetadata(doi) {
    return this._makeRequest(`/metadata/${encodeURIComponent(doi)}`);
  }
  
  /**
   * Holt Daten für eine spezifische Zitation (OCI)
   * 
   * @param {string} oci - Open Citation Identifier
   * @returns {Promise<Object>} Zitationsdaten
   */
  async getCitation(oci) {
    return this._makeRequest(`/citation/${encodeURIComponent(oci)}`);
  }
  
  /**
   * Holt alle verfügbaren Daten für eine DOI
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Alle verfügbaren Daten
   */
  async getAllDataForDOI(doi) {
    try {
      logger.info(`Hole alle OpenCitations-Daten für DOI ${doi}`);
      
      // Parallele Anfragen für bessere Performance
      const [citationsResult, referencesResult, citationCountResult, metadataResult] = await Promise.all([
        this.getCitations(doi),
        this.getReferences(doi),
        this.getCitationCount(doi),
        this.getMetadata(doi)
      ]);
      
      return {
        success: true,
        doi,
        citations: citationsResult.success ? citationsResult.data : [],
        references: referencesResult.success ? referencesResult.data : [],
        citationCount: citationCountResult,
        metadata: metadataResult.success ? metadataResult.data : null
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen aller OpenCitations-Daten', {
        doi,
        error: error.message
      });
      
      return {
        success: false,
        doi,
        error: error.message
      };
    }
  }
  
  /**
   * Aktualisiert die Zitationsdaten in der Datenbank mit OpenCitations-Daten
   * 
   * @param {string} doi - DOI der Publikation
   * @param {Object} databaseService - Datenbankdienst
   * @returns {Promise<Object>} Ergebnis der Aktualisierung
   */
  async updateCitationDataForDOI(doi, databaseService) {
    try {
      logger.info(`Aktualisiere Zitationsdaten für DOI ${doi} mit OpenCitations-Daten`);
      
      // Hole alle Daten von OpenCitations
      const openCitationsData = await this.getAllDataForDOI(doi);
      
      if (!openCitationsData.success) {
        return {
          success: false,
          doi,
          message: 'Fehler beim Abrufen von OpenCitations-Daten',
          error: openCitationsData.error
        };
      }
      
      // Aktualisiere die Zitationszählung in der Datenbank
      await databaseService.query(
        'UPDATE publication_metrics SET total_citations = ?, updated_at = NOW() WHERE doi = ? AND total_citations < ?',
        [openCitationsData.citationCount, doi, openCitationsData.citationCount]
      );
      
      // Speichere die eingehenden Zitationen in der Datenbank
      for (const citation of openCitationsData.citations) {
        // Prüfe, ob die Zitationsbeziehung bereits existiert
        const existingRelationship = await databaseService.query(
          'SELECT id FROM citation_relationships WHERE citing_doi = ? AND cited_doi = ?',
          [citation.citing, doi]
        );
        
        if (existingRelationship && existingRelationship.length > 0) {
          // Aktualisiere den OCI, falls er jetzt verfügbar ist
          await databaseService.query(
            'UPDATE citation_relationships SET oci = ?, updated_at = NOW() WHERE id = ?',
            [citation.oci, existingRelationship[0].id]
          );
        } else {
          // Füge die neue Beziehung hinzu
          await databaseService.query(
            'INSERT INTO citation_relationships (citing_doi, cited_doi, oci, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())',
            [citation.citing, doi, citation.oci]
          );
        }
      }
      
      // Speichere die ausgehenden Zitationen in der Datenbank
      for (const reference of openCitationsData.references) {
        // Prüfe, ob die Zitationsbeziehung bereits existiert
        const existingRelationship = await databaseService.query(
          'SELECT id FROM citation_relationships WHERE citing_doi = ? AND cited_doi = ?',
          [doi, reference.cited]
        );
        
        if (existingRelationship && existingRelationship.length > 0) {
          // Aktualisiere den OCI, falls er jetzt verfügbar ist
          await databaseService.query(
            'UPDATE citation_relationships SET oci = ?, updated_at = NOW() WHERE id = ?',
            [reference.oci, existingRelationship[0].id]
          );
        } else {
          // Füge die neue Beziehung hinzu
          await databaseService.query(
            'INSERT INTO citation_relationships (citing_doi, cited_doi, oci, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())',
            [doi, reference.cited, reference.oci]
          );
        }
      }
      
      // Speichere die OpenCitations-Metadaten
      if (openCitationsData.metadata) {
        await databaseService.query(
          'INSERT INTO publication_external_data (doi, source, data, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW()) ' +
          'ON DUPLICATE KEY UPDATE data = ?, updated_at = NOW()',
          [
            doi, 
            'opencitations', 
            JSON.stringify(openCitationsData.metadata),
            JSON.stringify(openCitationsData.metadata)
          ]
        );
      }
      
      return {
        success: true,
        doi,
        citationsUpdated: openCitationsData.citations.length,
        referencesUpdated: openCitationsData.references.length,
        citationCount: openCitationsData.citationCount
      };
    } catch (error) {
      logger.error('Fehler bei der Aktualisierung von Zitationsdaten mit OpenCitations', {
        doi,
        error: error.message
      });
      
      return {
        success: false,
        doi,
        error: error.message
      };
    }
  }
  
  /**
   * Führt eine Anfrage an die OpenCitations API aus
   * 
   * @private
   * @param {string} endpoint - API-Endpunkt
   * @returns {Promise<Object>} API-Antwort
   */
  async _makeRequest(endpoint) {
    try {
      const url = `${this.baseUrl}${endpoint}`;
      
      const headers = {
        'Accept': 'application/json'
      };
      
      if (this.accessToken) {
        headers['authorization'] = this.accessToken;
      }
      
      const response = await fetch(url, { 
        headers,
        timeout: this.timeout
      });
      
      if (!response.ok) {
        throw new Error(`OpenCitations API Fehler: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      
      // Setze Retry-Zähler zurück
      this.retryCount = 0;
      
      return {
        success: true,
        data
      };
    } catch (error) {
      // Versuche erneut, falls konfiguriert
      if (this.retryCount < this.maxRetries) {
        this.retryCount++;
        
        logger.warn('OpenCitationsService: Fehler bei der Anfrage, versuche erneut', {
          endpoint,
          error: error.message,
          retryCount: this.retryCount
        });
        
        // Warte exponentiell länger zwischen Wiederholungsversuchen
        await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, this.retryCount)));
        
        return this._makeRequest(endpoint);
      }
      
      // Setze Retry-Zähler zurück
      this.retryCount = 0;
      
      logger.error('OpenCitationsService: Fehler bei der Anfrage', {
        endpoint,
        error: error.message
      });
      
      return {
        success: false,
        error: error.message
      };
    }
  }
}

export default OpenCitationsService;export default OpenCitationsService;
export default OpenCitationsService;export default OpenCitationsService; * Dieser Service ermöglicht den Zugriff auf die OpenCitations API, um Zitationsdaten,
 * Referenzen und Metadaten für wissenschaftliche Publikationen abzurufen.
 * OpenCitations ist eine unabhängige Infrastrukturorganisation, die offene
 * bibliografische und Zitationsdaten bereitstellt.
 */

import fetch from 'node-fetch';
import { logger } from '../utils/logger.js';

/**
 * Service für die Interaktion mit der OpenCitations API
 */
export class OpenCitationsService {
  /**
   * Erstellt eine neue Instanz des OpenCitationsService
   * 
   * @param {Object} options - Optionen für den Service
   * @param {string} [options.baseUrl] - Basis-URL der OpenCitations API
   * @param {string} [options.accessToken] - Zugriffstoken für die API
   * @param {number} [options.timeout] - Timeout für API-Anfragen in Millisekunden
   * @param {number} [options.maxRetries] - Maximale Anzahl von Wiederholungsversuchen
   */
  constructor(options = {}) {
    this.baseUrl = options.baseUrl || process.env.OPENCITATIONS_API_URL || 'https://opencitations.net/index/api/v1';
    this.accessToken = options.accessToken || process.env.OPENCITATIONS_ACCESS_TOKEN;
    this.timeout = options.timeout || 10000;
    this.maxRetries = options.maxRetries || 3;
    this.retryCount = 0;
  }
  
  /**
   * Initialisiert den Service
   */
  async initialize() {
    logger.info('OpenCitationsService wird initialisiert');
    
    // Prüfe, ob die API erreichbar ist
    try {
      const testResponse = await this._makeRequest('/citation-count/10.1108/jd-12-2013-0166');
      
      if (testResponse.success) {
        logger.info('OpenCitationsService erfolgreich initialisiert');
      } else {
        logger.warn('OpenCitationsService konnte nicht vollständig initialisiert werden', {
          error: testResponse.error
        });
      }
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des OpenCitationsService', {
        error: error.message
      });
    }
  }
  
  /**
   * Holt Zitationen für eine DOI (eingehende Zitationen)
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Zitationsdaten
   */
  async getCitations(doi) {
    return this._makeRequest(`/citations/${encodeURIComponent(doi)}`);
  }
  
  /**
   * Holt Referenzen für eine DOI (ausgehende Zitationen)
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Referenzdaten
   */
  async getReferences(doi) {
    return this._makeRequest(`/references/${encodeURIComponent(doi)}`);
  }
  
  /**
   * Holt die Anzahl der Zitationen für eine DOI
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<number>} Anzahl der Zitationen
   */
  async getCitationCount(doi) {
    const result = await this._makeRequest(`/citation-count/${encodeURIComponent(doi)}`);
    return result.success ? parseInt(result.data[0]?.count || 0) : 0;
  }
  
  /**
   * Holt Metadaten für eine DOI
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Metadaten
   */
  async getMetadata(doi) {
    return this._makeRequest(`/metadata/${encodeURIComponent(doi)}`);
  }
  
  /**
   * Holt Daten für eine spezifische Zitation (OCI)
   * 
   * @param {string} oci - Open Citation Identifier
   * @returns {Promise<Object>} Zitationsdaten
   */
  async getCitation(oci) {
    return this._makeRequest(`/citation/${encodeURIComponent(oci)}`);
  }
  
  /**
   * Holt alle verfügbaren Daten für eine DOI
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Alle verfügbaren Daten
   */
  async getAllDataForDOI(doi) {
    try {
      logger.info(`Hole alle OpenCitations-Daten für DOI ${doi}`);
      
      // Parallele Anfragen für bessere Performance
      const [citationsResult, referencesResult, citationCountResult, metadataResult] = await Promise.all([
        this.getCitations(doi),
        this.getReferences(doi),
        this.getCitationCount(doi),
        this.getMetadata(doi)
      ]);
      
      return {
        success: true,
        doi,
        citations: citationsResult.success ? citationsResult.data : [],
        references: referencesResult.success ? referencesResult.data : [],
        citationCount: citationCountResult,
        metadata: metadataResult.success ? metadataResult.data : null
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen aller OpenCitations-Daten', {
        doi,
        error: error.message
      });
      
      return {
        success: false,
        doi,
        error: error.message
      };
    }
  }
  
  /**
   * Aktualisiert die Zitationsdaten in der Datenbank mit OpenCitations-Daten
   * 
   * @param {string} doi - DOI der Publikation
   * @param {Object} databaseService - Datenbankdienst
   * @returns {Promise<Object>} Ergebnis der Aktualisierung
   */
  async updateCitationDataForDOI(doi, databaseService) {
    try {
      logger.info(`Aktualisiere Zitationsdaten für DOI ${doi} mit OpenCitations-Daten`);
      
      // Hole alle Daten von OpenCitations
      const openCitationsData = await this.getAllDataForDOI(doi);
      
      if (!openCitationsData.success) {
        return {
          success: false,
          doi,
          message: 'Fehler beim Abrufen von OpenCitations-Daten',
          error: openCitationsData.error
        };
      }
      
      // Aktualisiere die Zitationszählung in der Datenbank
      await databaseService.query(
        'UPDATE publication_metrics SET total_citations = ?, updated_at = NOW() WHERE doi = ? AND total_citations < ?',
        [openCitationsData.citationCount, doi, openCitationsData.citationCount]
      );
      
      // Speichere die eingehenden Zitationen in der Datenbank
      for (const citation of openCitationsData.citations) {
        // Prüfe, ob die Zitationsbeziehung bereits existiert
        const existingRelationship = await databaseService.query(
          'SELECT id FROM citation_relationships WHERE citing_doi = ? AND cited_doi = ?',
          [citation.citing, doi]
        );
        
        if (existingRelationship && existingRelationship.length > 0) {
          // Aktualisiere den OCI, falls er jetzt verfügbar ist
          await databaseService.query(
            'UPDATE citation_relationships SET oci = ?, updated_at = NOW() WHERE id = ?',
            [citation.oci, existingRelationship[0].id]
          );
        } else {
          // Füge die neue Beziehung hinzu
          await databaseService.query(
            'INSERT INTO citation_relationships (citing_doi, cited_doi, oci, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())',
            [citation.citing, doi, citation.oci]
          );
        }
      }
      
      // Speichere die ausgehenden Zitationen in der Datenbank
      for (const reference of openCitationsData.references) {
        // Prüfe, ob die Zitationsbeziehung bereits existiert
        const existingRelationship = await databaseService.query(
          'SELECT id FROM citation_relationships WHERE citing_doi = ? AND cited_doi = ?',
          [doi, reference.cited]
        );
        
        if (existingRelationship && existingRelationship.length > 0) {
          // Aktualisiere den OCI, falls er jetzt verfügbar ist
          await databaseService.query(
            'UPDATE citation_relationships SET oci = ?, updated_at = NOW() WHERE id = ?',
            [reference.oci, existingRelationship[0].id]
          );
        } else {
          // Füge die neue Beziehung hinzu
          await databaseService.query(
            'INSERT INTO citation_relationships (citing_doi, cited_doi, oci, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())',
            [doi, reference.cited, reference.oci]
          );
        }
      }
      
      // Speichere die OpenCitations-Metadaten
      if (openCitationsData.metadata) {
        await databaseService.query(
          'INSERT INTO publication_external_data (doi, source, data, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW()) ' +
          'ON DUPLICATE KEY UPDATE data = ?, updated_at = NOW()',
          [
            doi, 
            'opencitations', 
            JSON.stringify(openCitationsData.metadata),
            JSON.stringify(openCitationsData.metadata)
          ]
        );
      }
      
      return {
        success: true,
        doi,
        citationsUpdated: openCitationsData.citations.length,
        referencesUpdated: openCitationsData.references.length,
        citationCount: openCitationsData.citationCount
      };
    } catch (error) {
      logger.error('Fehler bei der Aktualisierung von Zitationsdaten mit OpenCitations', {
        doi,
        error: error.message
      });
      
      return {
        success: false,
        doi,
        error: error.message
      };
    }
  }
  
  /**
   * Führt eine Anfrage an die OpenCitations API aus
   * 
   * @private
   * @param {string} endpoint - API-Endpunkt
   * @returns {Promise<Object>} API-Antwort
   */
  async _makeRequest(endpoint) {
    try {
      const url = `${this.baseUrl}${endpoint}`;
      
      const headers = {
        'Accept': 'application/json'
      };
      
      if (this.accessToken) {
        headers['authorization'] = this.accessToken;
      }
      
      const response = await fetch(url, { 
        headers,
        timeout: this.timeout
      });
      
      if (!response.ok) {
        throw new Error(`OpenCitations API Fehler: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      
      // Setze Retry-Zähler zurück
      this.retryCount = 0;
      
      return {
        success: true,
        data
      };
    } catch (error) {
      // Versuche erneut, falls konfiguriert
      if (this.retryCount < this.maxRetries) {
        this.retryCount++;
        
        logger.warn('OpenCitationsService: Fehler bei der Anfrage, versuche erneut', {
          endpoint,
          error: error.message,
          retryCount: this.retryCount
        });
        
        // Warte exponentiell länger zwischen Wiederholungsversuchen
        await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, this.retryCount)));
        
        return this._makeRequest(endpoint);
      }
      
      // Setze Retry-Zähler zurück
      this.retryCount = 0;
      
      logger.error('OpenCitationsService: Fehler bei der Anfrage', {
        endpoint,
        error: error.message
      });
      
      return {
        success: false,
        error: error.message
      };
    }
  }
}

export default OpenCitationsService;export default OpenCitationsService;