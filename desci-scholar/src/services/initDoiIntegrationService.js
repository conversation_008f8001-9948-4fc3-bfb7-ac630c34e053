/**
 * @fileoverview Initialisierung des DoiIntegrationService
 * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;/**
 * @fileoverview Initialisierung des DoiIntegrationService
 * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;/**
 * @fileoverview Initialisierung des DoiIntegrationService
 * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;/**
 * @fileoverview Initialisierung des DoiIntegrationService
 * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;/**
 * @fileoverview Initialisierung des DoiIntegrationService
 * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;/**
 * @fileoverview Initialisierung des DoiIntegrationService
 * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;/**
 * @fileoverview Initialisierung des DoiIntegrationService
 * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;/**
 * @fileoverview Initialisierung des DoiIntegrationService
 * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;/**
 * @fileoverview Initialisierung des DoiIntegrationService
 * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;/**
 * @fileoverview Initialisierung des DoiIntegrationService
 * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;/**
 * @fileoverview Initialisierung des DoiIntegrationService
 * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;/**
 * @fileoverview Initialisierung des DoiIntegrationService
 * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;/**
 * @fileoverview Initialisierung des DoiIntegrationService
 * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;/**
 * @fileoverview Initialisierung des DoiIntegrationService
 * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;/**
 * @fileoverview Initialisierung des DoiIntegrationService
 * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;/**
 * @fileoverview Initialisierung des DoiIntegrationService
 * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;/**
 * @fileoverview Initialisierung des DoiIntegrationService
 * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;/**
 * @fileoverview Initialisierung des DoiIntegrationService
 * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;/**
 * @fileoverview Initialisierung des DoiIntegrationService
 * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;/**
 * @fileoverview Initialisierung des DoiIntegrationService
 * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;/**
 * @fileoverview Initialisierung des DoiIntegrationService
 * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;/**
 * @fileoverview Initialisierung des DoiIntegrationService
 * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;/**
 * @fileoverview Initialisierung des DoiIntegrationService
 * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;/**
 * @fileoverview Initialisierung des DoiIntegrationService
 * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;/**
 * @fileoverview Initialisierung des DoiIntegrationService
 * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;/**
 * @fileoverview Initialisierung des DoiIntegrationService
 * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;/**
 * @fileoverview Initialisierung des DoiIntegrationService
 * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;/**
 * @fileoverview Initialisierung des DoiIntegrationService
 * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;/**
 * @fileoverview Initialisierung des DoiIntegrationService
 * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;/**
 * @fileoverview Initialisierung des DoiIntegrationService
 * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;/**
 * @fileoverview Initialisierung des DoiIntegrationService
 * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;/**
 * @fileoverview Initialisierung des DoiIntegrationService
 * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;/**
 * @fileoverview Initialisierung des DoiIntegrationService
 * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;/**
 * @fileoverview Initialisierung des DoiIntegrationService
 * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;/**
 * @fileoverview Initialisierung des DoiIntegrationService
 * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService; * @fileoverview Initialisierung des DoiIntegrationService
 * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService; * @fileoverview Initialisierung des DoiIntegrationService
 * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService; * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService; * @fileoverview Initialisierung des DoiIntegrationService
 * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;export default initDoiIntegrationService; * @fileoverview Initialisierung des DoiIntegrationService
 * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService; * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService; * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService; * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;
export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService; * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService; * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;
export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService; * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;
export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService; * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;export default initDoiIntegrationService;
export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService; * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService; * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;
export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService; * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService; * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService; * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService; * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;
export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService; * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;export default initDoiIntegrationService;
export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService; * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService; * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;
export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService; * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService; * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;
export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService; * 
 * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;export default initDoiIntegrationService; * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
       stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;
export default initDoiIntegrationService;export default initDoiIntegrationService; * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;
export default initDoiIntegrationService;
export default initDoiIntegrationService;export default initDoiIntegrationService; * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;export default initDoiIntegrationService;
export default initDoiIntegrationService;export default initDoiIntegrationService; * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;
export default initDoiIntegrationService;export default initDoiIntegrationService; * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;
export default initDoiIntegrationService;export default initDoiIntegrationService; * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;
export default initDoiIntegrationService;export default initDoiIntegrationService; * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;
export default initDoiIntegrationService;export default initDoiIntegrationService; * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;export default initDoiIntegrationService;
export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;
export default initDoiIntegrationService;export default initDoiIntegrationService; * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;export default initDoiIntegrationService;
export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;export default initDoiIntegrationService;
export default initDoiIntegrationService;export default initDoiIntegrationService; * Diese Datei enthält die Funktion zur Initialisierung des DoiIntegrationService
 * mit allen erforderlichen Abhängigkeiten.
 */

import DoiIntegrationService from './DoiIntegrationService.js';
import DataCiteClient from '../api/integrations/DataCiteClient.js';
import CrossrefClient from '../api/integrations/CrossrefClient.js';
import EnhancedDoiNftContract from '../blockchain/contracts/EnhancedDoiNftContract.js';
import { logger } from '../utils/logger.js';
import doiNftConfig from '../config/doi-nft-config.js';

/**
 * Initialisiert den DoiIntegrationService mit allen erforderlichen Abhängigkeiten
 * @param {Object} options - Konfigurationsoptionen
 * @param {Object} options.nftService - NFT-Service-Instanz
 * @param {Object} options.ipfsManager - IPFS-Manager-Instanz
 * @param {Object} options.storageService - Storage-Service-Instanz
 * @param {Object} options.blockchainApi - Blockchain-API-Instanz
 * @returns {Promise<DoiIntegrationService>} Initialisierter DoiIntegrationService
 */
async function initDoiIntegrationService(options = {}) {
  try {
    const { nftService, ipfsManager, storageService, blockchainApi } = options;
    
    logger.info('Initialisiere DoiIntegrationService');
    
    // Initialisiere DataCite-Client
    const dataCiteClient = new DataCiteClient({
      baseUrl: doiNftConfig.dataCite.apiBaseUrl,
      apiKey: doiNftConfig.dataCite.apiKey,
      repositoryId: doiNftConfig.dataCite.repositoryId,
      timeout: doiNftConfig.dataCite.timeout,
      maxRetries: doiNftConfig.dataCite.maxRetries
    });
    
    // Initialisiere Crossref-Client
    const crossrefClient = new CrossrefClient({
      baseUrl: doiNftConfig.crossref.apiBaseUrl,
      email: doiNftConfig.crossref.email,
      token: doiNftConfig.crossref.token,
      timeout: doiNftConfig.crossref.timeout,
      maxRetries: doiNftConfig.crossref.maxRetries
    });
    
    // Initialisiere EnhancedDoiNftContract, falls konfiguriert
    let enhancedContract = null;
    if (doiNftConfig.nft.useEnhancedContract && blockchainApi && doiNftConfig.nft.contractAddress) {
      try {
        enhancedContract = new EnhancedDoiNftContract(
          blockchainApi,
          doiNftConfig.nft.contractAddress,
          {
            gasLimit: doiNftConfig.nft.gasLimit,
            maxFeePerGas: doiNftConfig.nft.maxFeePerGas
          }
        );
        
        logger.info('EnhancedDoiNftContract erfolgreich initialisiert', {
          contractAddress: doiNftConfig.nft.contractAddress
        });
      } catch (contractError) {
        logger.error('Fehler bei der Initialisierung des EnhancedDoiNftContract', {
          error: contractError.message,
          stack: contractError.stack,
          contractAddress: doiNftConfig.nft.contractAddress
        });
      }
    }
    
    // Erstelle DoiIntegrationService
    const doiIntegrationService = new DoiIntegrationService({
      dataCiteClient,
      crossrefClient,
      nftService,
      ipfsManager,
      storageService,
      enhancedContract
    });
    
    // Initialisiere den Service
    await doiIntegrationService.initialize();
    
    logger.info('DoiIntegrationService erfolgreich initialisiert');
    
    return doiIntegrationService;
  } catch (error) {
    logger.error('Fehler bei der Initialisierung des DoiIntegrationService', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

export default initDoiIntegrationService;export default initDoiIntegrationService;