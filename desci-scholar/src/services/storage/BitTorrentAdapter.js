/**
 * @fileoverview BitTorrent-Adapter für dezentrale Speicherung großer Dateien
 * 
 * Dieser Adapter implementiert das StorageAdapter-Interface für BitTorrent.
 * Er wird hauptsächlich für die Speicherung und Verteilung großer Dateien wie
 * Publikationen, Patente und Forschungsdatensätze verwendet.
 */

import fs from 'fs';
import path from 'path';
import crypto from 'crypto';
import { promisify } from 'util';
import WebTorrent from 'webtorrent';
import { StorageAdapter } from './StorageAdapter.js';
import { logger } from '../../utils/logger.js';

// Promisify fs-Funktionen
const mkdir = promisify(fs.mkdir);
const writeFile = promisify(fs.writeFile);
const readFile = promisify(fs.readFile);
const stat = promisify(fs.stat);
const unlink = promisify(fs.unlink);

/**
 * BitTorrent-Adapter für dezentrale Speicherung großer Dateien
 */
export class BitTorrentAdapter extends StorageAdapter {
  /**
   * Erstellt eine neue Instanz des BitTorrentAdapter
   * @param {Object} options - Konfigurationsoptionen
   * @param {string} options.trackers - Liste der Tracker-URLs
   * @param {string} options.seedTime - Zeit in Millisekunden, für die Dateien geseedet werden sollen
   * @param {string} options.tempDir - Temporäres Verzeichnis für Dateien
   * @param {boolean} options.persistentSeeding - Ob Dateien dauerhaft geseedet werden sollen
   * @param {Array<string>} options.webSeeds - Liste der WebSeed-URLs
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      trackers: options.trackers || [
        'udp://tracker.opentrackr.org:1337/announce',
        'udp://tracker.openbittorrent.com:6969/announce',
        'udp://open.stealth.si:80/announce',
        'udp://tracker.torrent.eu.org:451/announce',
        'udp://tracker.moeking.me:6969/announce'
      ],
      seedTime: options.seedTime || 24 * 60 * 60 * 1000, // 24 Stunden
      tempDir: options.tempDir || path.join(process.cwd(), 'temp', 'torrents'),
      persistentSeeding: options.persistentSeeding !== undefined ? options.persistentSeeding : false,
      webSeeds: options.webSeeds || [],
      ...options
    };
    
    this.client = null;
    this.torrents = new Map(); // Speichert aktive Torrents
    this.metadataDb = new Map(); // Speichert Metadaten zu Torrents
    
    logger.info('BitTorrentAdapter initialisiert');
  }
  
  /**
   * Initialisiert den BitTorrent-Client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      logger.info('Initialisiere BitTorrent-Client');
      
      // Erstelle das temporäre Verzeichnis, falls es nicht existiert
      await mkdir(this.options.tempDir, { recursive: true });
      
      // Initialisiere den WebTorrent-Client
      this.client = new WebTorrent();
      
      // Lade gespeicherte Metadaten
      await this.loadMetadata();
      
      // Seed gespeicherte Torrents, falls persistentSeeding aktiviert ist
      if (this.options.persistentSeeding) {
        await this.seedStoredTorrents();
      }
      
      logger.info('BitTorrent-Client erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des BitTorrent-Clients', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Lädt gespeicherte Metadaten
   * @returns {Promise<void>}
   */
  async loadMetadata() {
    try {
      const metadataPath = path.join(this.options.tempDir, 'metadata.json');
      
      // Prüfe, ob die Metadatendatei existiert
      try {
        await stat(metadataPath);
      } catch (error) {
        // Erstelle eine leere Metadatendatei, falls sie nicht existiert
        await writeFile(metadataPath, JSON.stringify({}));
        return;
      }
      
      // Lade die Metadaten
      const metadataJson = await readFile(metadataPath, 'utf8');
      const metadata = JSON.parse(metadataJson);
      
      // Fülle die Metadaten-Map
      for (const [infoHash, torrentData] of Object.entries(metadata)) {
        this.metadataDb.set(infoHash, torrentData);
      }
      
      logger.debug(`${this.metadataDb.size} Torrent-Metadaten geladen`);
    } catch (error) {
      logger.error('Fehler beim Laden der Torrent-Metadaten', {
        error: error.message
      });
      // Erstelle eine leere Metadaten-Map im Fehlerfall
      this.metadataDb = new Map();
    }
  }
  
  /**
   * Speichert Metadaten
   * @returns {Promise<void>}
   */
  async saveMetadata() {
    try {
      const metadataPath = path.join(this.options.tempDir, 'metadata.json');
      
      // Konvertiere die Map in ein Objekt
      const metadata = {};
      for (const [infoHash, torrentData] of this.metadataDb.entries()) {
        metadata[infoHash] = torrentData;
      }
      
      // Speichere die Metadaten
      await writeFile(metadataPath, JSON.stringify(metadata, null, 2));
      
      logger.debug(`${this.metadataDb.size} Torrent-Metadaten gespeichert`);
    } catch (error) {
      logger.error('Fehler beim Speichern der Torrent-Metadaten', {
        error: error.message
      });
    }
  }
  
  /**
   * Seed gespeicherte Torrents
   * @returns {Promise<void>}
   */
  async seedStoredTorrents() {
    try {
      logger.info('Starte Seeding gespeicherter Torrents');
      
      const torrentsToSeed = [];
      
      // Sammle alle Torrent-Dateien
      for (const [infoHash, metadata] of this.metadataDb.entries()) {
        const torrentPath = path.join(this.options.tempDir, `${infoHash}.torrent`);
        
        try {
          // Prüfe, ob die Torrent-Datei existiert
          await stat(torrentPath);
          torrentsToSeed.push({ path: torrentPath, metadata });
        } catch (error) {
          logger.warn(`Torrent-Datei für ${infoHash} nicht gefunden`);
        }
      }
      
      // Seed die Torrents
      for (const { path: torrentPath, metadata } of torrentsToSeed) {
        try {
          const torrent = await this.seedTorrentFile(torrentPath);
          this.torrents.set(torrent.infoHash, torrent);
          logger.debug(`Torrent ${torrent.infoHash} wird geseedet`);
        } catch (error) {
          logger.error(`Fehler beim Seeden von Torrent ${metadata.name}`, {
            error: error.message
          });
        }
      }
      
      logger.info(`${this.torrents.size} Torrents werden geseedet`);
    } catch (error) {
      logger.error('Fehler beim Seeden gespeicherter Torrents', {
        error: error.message
      });
    }
  }
  
  /**
   * Seed eine Torrent-Datei
   * @param {string} torrentPath - Pfad zur Torrent-Datei
   * @returns {Promise<Object>} Torrent-Objekt
   */
  async seedTorrentFile(torrentPath) {
    return new Promise((resolve, reject) => {
      this.client.add(torrentPath, { path: this.options.tempDir }, torrent => {
        resolve(torrent);
      }).on('error', error => {
        reject(error);
      });
    });
  }
  
  /**
   * Speichert eine Datei über BitTorrent
   * @param {Buffer|string} data - Zu speichernde Daten
   * @param {Object} options - Speicheroptionen
   * @param {string} options.filename - Dateiname
   * @param {boolean} options.seed - Ob die Datei geseedet werden soll
   * @param {Array<string>} options.trackers - Zusätzliche Tracker
   * @param {Array<string>} options.webSeeds - Zusätzliche WebSeeds
   * @param {Object} options.metadata - Zusätzliche Metadaten
   * @returns {Promise<Object>} BitTorrent-Speicherinformationen
   */
  async storeFile(data, options = {}) {
    try {
      logger.info('Speichere Datei über BitTorrent');
      
      // Generiere einen eindeutigen Dateinamen, falls keiner angegeben ist
      const filename = options.filename || `file-${crypto.randomBytes(8).toString('hex')}`;
      const filePath = path.join(this.options.tempDir, filename);
      
      // Schreibe die Datei auf die Festplatte
      await writeFile(filePath, data);
      
      // Erstelle ein Torrent
      const torrent = await this.createTorrent(filePath, {
        name: filename,
        comment: options.metadata?.description || 'DeSci-Scholar File',
        createdBy: 'DeSci-Scholar BitTorrentAdapter',
        private: false,
        announceList: [
          ...this.options.trackers,
          ...(options.trackers || [])
        ],
        urlList: [
          ...this.options.webSeeds,
          ...(options.webSeeds || [])
        ]
      });
      
      // Speichere die Torrent-Datei
      const torrentPath = path.join(this.options.tempDir, `${torrent.infoHash}.torrent`);
      await writeFile(torrentPath, torrent.torrentFile);
      
      // Speichere Metadaten
      const metadata = {
        name: filename,
        size: torrent.length,
        files: torrent.files.map(file => ({
          name: file.name,
          path: file.path,
          length: file.length
        })),
        createdAt: new Date().toISOString(),
        originalFilename: options.filename,
        contentType: options.metadata?.contentType,
        description: options.metadata?.description,
        customMetadata: options.metadata?.custom
      };
      
      this.metadataDb.set(torrent.infoHash, metadata);
      await this.saveMetadata();
      
      // Seed die Datei für die angegebene Zeit, falls gewünscht
      if (options.seed !== false) {
        setTimeout(() => {
          if (!this.options.persistentSeeding) {
            torrent.destroy();
            this.torrents.delete(torrent.infoHash);
            logger.debug(`Seeding für Torrent ${torrent.infoHash} beendet`);
          }
        }, options.seedTime || this.options.seedTime);
      } else {
        torrent.destroy();
        this.torrents.delete(torrent.infoHash);
      }
      
      logger.debug(`Datei über BitTorrent gespeichert mit InfoHash ${torrent.infoHash}`);
      
      // Lösche die temporäre Datei, falls sie nicht geseedet wird
      if (options.seed === false) {
        await unlink(filePath);
      }
      
      return {
        infoHash: torrent.infoHash,
        magnetUri: torrent.magnetURI,
        torrentFile: torrent.torrentFile,
        size: torrent.length,
        files: torrent.files.map(file => ({
          name: file.name,
          path: file.path,
          length: file.length
        })),
        trackers: torrent.announce,
        webSeeds: torrent.urlList
      };
    } catch (error) {
      logger.error('Fehler beim Speichern der Datei über BitTorrent', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Erstellt ein Torrent für eine Datei
   * @param {string} filePath - Pfad zur Datei
   * @param {Object} options - Torrent-Optionen
   * @returns {Promise<Object>} Torrent-Objekt
   */
  async createTorrent(filePath, options = {}) {
    return new Promise((resolve, reject) => {
      this.client.seed(filePath, options, torrent => {
        this.torrents.set(torrent.infoHash, torrent);
        resolve(torrent);
      }).on('error', error => {
        reject(error);
      });
    });
  }
  
  /**
   * Speichert JSON-Daten über BitTorrent
   * @param {Object} data - Zu speichernde JSON-Daten
   * @param {Object} options - Speicheroptionen
   * @returns {Promise<Object>} BitTorrent-Speicherinformationen
   */
  async storeJson(data, options = {}) {
    try {
      logger.info('Speichere JSON-Daten über BitTorrent');
      
      // Konvertiere die JSON-Daten in einen String
      const jsonString = JSON.stringify(data);
      
      // Speichere die JSON-Daten als Datei
      return await this.storeFile(Buffer.from(jsonString), {
        filename: options.filename || 'data.json',
        seed: options.seed,
        trackers: options.trackers,
        webSeeds: options.webSeeds,
        metadata: {
          ...options.metadata,
          contentType: 'application/json'
        }
      });
    } catch (error) {
      logger.error('Fehler beim Speichern der JSON-Daten über BitTorrent', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Datei über BitTorrent ab
   * @param {string} infoHash - InfoHash des Torrents
   * @param {Object} options - Abrufoptionen
   * @param {number} options.timeout - Timeout in Millisekunden
   * @param {boolean} options.stream - Ob die Datei als Stream zurückgegeben werden soll
   * @returns {Promise<Buffer|stream.Readable>} Abgerufene Daten oder Stream
   */
  async retrieveFile(infoHash, options = {}) {
    try {
      logger.info(`Rufe Datei mit InfoHash ${infoHash} über BitTorrent ab`);
      
      // Prüfe, ob der Torrent bereits geladen ist
      let torrent = this.torrents.get(infoHash);
      
      if (!torrent) {
        // Prüfe, ob eine Torrent-Datei existiert
        const torrentPath = path.join(this.options.tempDir, `${infoHash}.torrent`);
        
        try {
          await stat(torrentPath);
          torrent = await this.addTorrent(torrentPath);
        } catch (error) {
          // Versuche, den Torrent über den Magnet-Link hinzuzufügen
          const magnetUri = `magnet:?xt=urn:btih:${infoHash}`;
          torrent = await this.addTorrent(magnetUri);
        }
      }
      
      // Warte, bis der Torrent vollständig heruntergeladen ist
      if (!torrent.done) {
        await this.waitForTorrent(torrent, options.timeout);
      }
      
      // Hole die Hauptdatei des Torrents
      const file = torrent.files[0];
      
      if (options.stream) {
        // Gib einen Stream zurück
        return file.createReadStream();
      } else {
        // Lese die Datei in einen Buffer
        return new Promise((resolve, reject) => {
          const chunks = [];
          const stream = file.createReadStream();
          
          stream.on('data', chunk => {
            chunks.push(chunk);
          });
          
          stream.on('end', () => {
            resolve(Buffer.concat(chunks));
          });
          
          stream.on('error', error => {
            reject(error);
          });
        });
      }
    } catch (error) {
      logger.error('Fehler beim Abrufen der Datei über BitTorrent', {
        error: error.message,
        infoHash
      });
      throw error;
    }
  }
  
  /**
   * Fügt einen Torrent zum Client hinzu
   * @param {string} torrentId - Torrent-ID (Pfad, Magnet-URI, InfoHash)
   * @returns {Promise<Object>} Torrent-Objekt
   */
  async addTorrent(torrentId) {
    return new Promise((resolve, reject) => {
      this.client.add(torrentId, { path: this.options.tempDir }, torrent => {
        this.torrents.set(torrent.infoHash, torrent);
        resolve(torrent);
      }).on('error', error => {
        reject(error);
      });
    });
  }
  
  /**
   * Wartet, bis ein Torrent vollständig heruntergeladen ist
   * @param {Object} torrent - Torrent-Objekt
   * @param {number} timeout - Timeout in Millisekunden
   * @returns {Promise<void>}
   */
  async waitForTorrent(torrent, timeout = 60000) {
    return new Promise((resolve, reject) => {
      // Setze einen Timeout
      const timeoutId = setTimeout(() => {
        reject(new Error(`Timeout beim Herunterladen des Torrents ${torrent.infoHash}`));
      }, timeout);
      
      // Warte auf das 'done'-Ereignis
      if (torrent.done) {
        clearTimeout(timeoutId);
        resolve();
      } else {
        torrent.on('done', () => {
          clearTimeout(timeoutId);
          resolve();
        });
      }
    });
  }
  
  /**
   * Ruft JSON-Daten über BitTorrent ab
   * @param {string} infoHash - InfoHash des Torrents
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Object>} Abgerufene JSON-Daten
   */
  async retrieveJson(infoHash, options = {}) {
    try {
      logger.info(`Rufe JSON-Daten mit InfoHash ${infoHash} über BitTorrent ab`);
      
      // Hole die Datei
      const data = await this.retrieveFile(infoHash, options);
      
      // Konvertiere die Daten in ein JSON-Objekt
      const jsonData = JSON.parse(data.toString());
      
      logger.debug(`JSON-Daten mit InfoHash ${infoHash} erfolgreich abgerufen`);
      
      return jsonData;
    } catch (error) {
      logger.error('Fehler beim Abrufen der JSON-Daten über BitTorrent', {
        error: error.message,
        infoHash
      });
      throw error;
    }
  }
  
  /**
   * Prüft, ob ein Torrent existiert
   * @param {string} infoHash - InfoHash des Torrents
   * @param {Object} options - Prüfoptionen
   * @returns {Promise<boolean>} True, wenn der Torrent existiert
   */
  async exists(infoHash, options = {}) {
    try {
      logger.info(`Prüfe, ob Torrent mit InfoHash ${infoHash} existiert`);
      
      // Prüfe, ob der Torrent bereits geladen ist
      if (this.torrents.has(infoHash)) {
        return true;
      }
      
      // Prüfe, ob eine Torrent-Datei existiert
      const torrentPath = path.join(this.options.tempDir, `${infoHash}.torrent`);
      
      try {
        await stat(torrentPath);
        return true;
      } catch (error) {
        // Prüfe, ob Metadaten existieren
        return this.metadataDb.has(infoHash);
      }
    } catch (error) {
      logger.error('Fehler beim Prüfen der Existenz des Torrents', {
        error: error.message,
        infoHash
      });
      return false;
    }
  }
  
  /**
   * Löscht einen Torrent
   * @param {string} infoHash - InfoHash des Torrents
   * @param {Object} options - Löschoptionen
   * @param {boolean} options.removeFiles - Ob die Dateien gelöscht werden sollen
   * @returns {Promise<boolean>} True, wenn der Torrent erfolgreich gelöscht wurde
   */
  async delete(infoHash, options = {}) {
    try {
      logger.info(`Lösche Torrent mit InfoHash ${infoHash}`);
      
      // Prüfe, ob der Torrent geladen ist
      const torrent = this.torrents.get(infoHash);
      
      if (torrent) {
        // Zerstöre den Torrent
        await new Promise(resolve => {
          torrent.destroy({ destroyStore: options.removeFiles }, () => {
            resolve();
          });
        });
        
        this.torrents.delete(infoHash);
      }
      
      // Lösche die Torrent-Datei
      const torrentPath = path.join(this.options.tempDir, `${infoHash}.torrent`);
      
      try {
        await unlink(torrentPath);
      } catch (error) {
        logger.warn(`Torrent-Datei für ${infoHash} konnte nicht gelöscht werden`);
      }
      
      // Lösche die Metadaten
      this.metadataDb.delete(infoHash);
      await this.saveMetadata();
      
      logger.debug(`Torrent mit InfoHash ${infoHash} erfolgreich gelöscht`);
      
      return true;
    } catch (error) {
      logger.error('Fehler beim Löschen des Torrents', {
        error: error.message,
        infoHash
      });
      return false;
    }
  }
  
  /**
   * Generiert eine URL für den Zugriff auf die Datei
   * @param {string} infoHash - InfoHash des Torrents
   * @param {Object} options - URL-Optionen
   * @returns {Promise<string>} Magnet-URI für den Zugriff auf die Datei
   */
  async getAccessUrl(infoHash, options = {}) {
    try {
      logger.info(`Generiere Zugriffs-URL für Torrent mit InfoHash ${infoHash}`);
      
      // Prüfe, ob der Torrent geladen ist
      const torrent = this.torrents.get(infoHash);
      
      if (torrent) {
        return torrent.magnetURI;
      }
      
      // Prüfe, ob Metadaten existieren
      const metadata = this.metadataDb.get(infoHash);
      
      if (metadata) {
        // Erstelle einen Magnet-Link
        const trackers = this.options.trackers.map(tracker => `&tr=${encodeURIComponent(tracker)}`).join('');
        const magnetUri = `magnet:?xt=urn:btih:${infoHash}&dn=${encodeURIComponent(metadata.name)}${trackers}`;
        
        return magnetUri;
      }
      
      throw new Error(`Torrent mit InfoHash ${infoHash} nicht gefunden`);
    } catch (error) {
      logger.error('Fehler beim Generieren der Zugriffs-URL', {
        error: error.message,
        infoHash
      });
      throw error;
    }
  }
}/**
 * @fileoverview BitTorrent-Adapter für dezentrale Speicherung großer Dateien
 * 
 * Dieser Adapter implementiert das StorageAdapter-Interface für BitTorrent.
 * Er wird hauptsächlich für die Speicherung und Verteilung großer Dateien wie
 * Publikationen, Patente und Forschungsdatensätze verwendet.
 */

import fs from 'fs';
import path from 'path';
import crypto from 'crypto';
import { promisify } from 'util';
import WebTorrent from 'webtorrent';
import { StorageAdapter } from './StorageAdapter.js';
import { logger } from '../../utils/logger.js';

// Promisify fs-Funktionen
const mkdir = promisify(fs.mkdir);
const writeFile = promisify(fs.writeFile);
const readFile = promisify(fs.readFile);
const stat = promisify(fs.stat);
const unlink = promisify(fs.unlink);

/**
 * BitTorrent-Adapter für dezentrale Speicherung großer Dateien
 */
export class BitTorrentAdapter extends StorageAdapter {
  /**
   * Erstellt eine neue Instanz des BitTorrentAdapter
   * @param {Object} options - Konfigurationsoptionen
   * @param {string} options.trackers - Liste der Tracker-URLs
   * @param {string} options.seedTime - Zeit in Millisekunden, für die Dateien geseedet werden sollen
   * @param {string} options.tempDir - Temporäres Verzeichnis für Dateien
   * @param {boolean} options.persistentSeeding - Ob Dateien dauerhaft geseedet werden sollen
   * @param {Array<string>} options.webSeeds - Liste der WebSeed-URLs
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      trackers: options.trackers || [
        'udp://tracker.opentrackr.org:1337/announce',
        'udp://tracker.openbittorrent.com:6969/announce',
        'udp://open.stealth.si:80/announce',
        'udp://tracker.torrent.eu.org:451/announce',
        'udp://tracker.moeking.me:6969/announce'
      ],
      seedTime: options.seedTime || 24 * 60 * 60 * 1000, // 24 Stunden
      tempDir: options.tempDir || path.join(process.cwd(), 'temp', 'torrents'),
      persistentSeeding: options.persistentSeeding !== undefined ? options.persistentSeeding : false,
      webSeeds: options.webSeeds || [],
      ...options
    };
    
    this.client = null;
    this.torrents = new Map(); // Speichert aktive Torrents
    this.metadataDb = new Map(); // Speichert Metadaten zu Torrents
    
    logger.info('BitTorrentAdapter initialisiert');
  }
  
  /**
   * Initialisiert den BitTorrent-Client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      logger.info('Initialisiere BitTorrent-Client');
      
      // Erstelle das temporäre Verzeichnis, falls es nicht existiert
      await mkdir(this.options.tempDir, { recursive: true });
      
      // Initialisiere den WebTorrent-Client
      this.client = new WebTorrent();
      
      // Lade gespeicherte Metadaten
      await this.loadMetadata();
      
      // Seed gespeicherte Torrents, falls persistentSeeding aktiviert ist
      if (this.options.persistentSeeding) {
        await this.seedStoredTorrents();
      }
      
      logger.info('BitTorrent-Client erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des BitTorrent-Clients', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Lädt gespeicherte Metadaten
   * @returns {Promise<void>}
   */
  async loadMetadata() {
    try {
      const metadataPath = path.join(this.options.tempDir, 'metadata.json');
      
      // Prüfe, ob die Metadatendatei existiert
      try {
        await stat(metadataPath);
      } catch (error) {
        // Erstelle eine leere Metadatendatei, falls sie nicht existiert
        await writeFile(metadataPath, JSON.stringify({}));
        return;
      }
      
      // Lade die Metadaten
      const metadataJson = await readFile(metadataPath, 'utf8');
      const metadata = JSON.parse(metadataJson);
      
      // Fülle die Metadaten-Map
      for (const [infoHash, torrentData] of Object.entries(metadata)) {
        this.metadataDb.set(infoHash, torrentData);
      }
      
      logger.debug(`${this.metadataDb.size} Torrent-Metadaten geladen`);
    } catch (error) {
      logger.error('Fehler beim Laden der Torrent-Metadaten', {
        error: error.message
      });
      // Erstelle eine leere Metadaten-Map im Fehlerfall
      this.metadataDb = new Map();
    }
  }
  
  /**
   * Speichert Metadaten
   * @returns {Promise<void>}
   */
  async saveMetadata() {
    try {
      const metadataPath = path.join(this.options.tempDir, 'metadata.json');
      
      // Konvertiere die Map in ein Objekt
      const metadata = {};
      for (const [infoHash, torrentData] of this.metadataDb.entries()) {
        metadata[infoHash] = torrentData;
      }
      
      // Speichere die Metadaten
      await writeFile(metadataPath, JSON.stringify(metadata, null, 2));
      
      logger.debug(`${this.metadataDb.size} Torrent-Metadaten gespeichert`);
    } catch (error) {
      logger.error('Fehler beim Speichern der Torrent-Metadaten', {
        error: error.message
      });
    }
  }
  
  /**
   * Seed gespeicherte Torrents
   * @returns {Promise<void>}
   */
  async seedStoredTorrents() {
    try {
      logger.info('Starte Seeding gespeicherter Torrents');
      
      const torrentsToSeed = [];
      
      // Sammle alle Torrent-Dateien
      for (const [infoHash, metadata] of this.metadataDb.entries()) {
        const torrentPath = path.join(this.options.tempDir, `${infoHash}.torrent`);
        
        try {
          // Prüfe, ob die Torrent-Datei existiert
          await stat(torrentPath);
          torrentsToSeed.push({ path: torrentPath, metadata });
        } catch (error) {
          logger.warn(`Torrent-Datei für ${infoHash} nicht gefunden`);
        }
      }
      
      // Seed die Torrents
      for (const { path: torrentPath, metadata } of torrentsToSeed) {
        try {
          const torrent = await this.seedTorrentFile(torrentPath);
          this.torrents.set(torrent.infoHash, torrent);
          logger.debug(`Torrent ${torrent.infoHash} wird geseedet`);
        } catch (error) {
          logger.error(`Fehler beim Seeden von Torrent ${metadata.name}`, {
            error: error.message
          });
        }
      }
      
      logger.info(`${this.torrents.size} Torrents werden geseedet`);
    } catch (error) {
      logger.error('Fehler beim Seeden gespeicherter Torrents', {
        error: error.message
      });
    }
  }
  
  /**
   * Seed eine Torrent-Datei
   * @param {string} torrentPath - Pfad zur Torrent-Datei
   * @returns {Promise<Object>} Torrent-Objekt
   */
  async seedTorrentFile(torrentPath) {
    return new Promise((resolve, reject) => {
      this.client.add(torrentPath, { path: this.options.tempDir }, torrent => {
        resolve(torrent);
      }).on('error', error => {
        reject(error);
      });
    });
  }
  
  /**
   * Speichert eine Datei über BitTorrent
   * @param {Buffer|string} data - Zu speichernde Daten
   * @param {Object} options - Speicheroptionen
   * @param {string} options.filename - Dateiname
   * @param {boolean} options.seed - Ob die Datei geseedet werden soll
   * @param {Array<string>} options.trackers - Zusätzliche Tracker
   * @param {Array<string>} options.webSeeds - Zusätzliche WebSeeds
   * @param {Object} options.metadata - Zusätzliche Metadaten
   * @returns {Promise<Object>} BitTorrent-Speicherinformationen
   */
  async storeFile(data, options = {}) {
    try {
      logger.info('Speichere Datei über BitTorrent');
      
      // Generiere einen eindeutigen Dateinamen, falls keiner angegeben ist
      const filename = options.filename || `file-${crypto.randomBytes(8).toString('hex')}`;
      const filePath = path.join(this.options.tempDir, filename);
      
      // Schreibe die Datei auf die Festplatte
      await writeFile(filePath, data);
      
      // Erstelle ein Torrent
      const torrent = await this.createTorrent(filePath, {
        name: filename,
        comment: options.metadata?.description || 'DeSci-Scholar File',
        createdBy: 'DeSci-Scholar BitTorrentAdapter',
        private: false,
        announceList: [
          ...this.options.trackers,
          ...(options.trackers || [])
        ],
        urlList: [
          ...this.options.webSeeds,
          ...(options.webSeeds || [])
        ]
      });
      
      // Speichere die Torrent-Datei
      const torrentPath = path.join(this.options.tempDir, `${torrent.infoHash}.torrent`);
      await writeFile(torrentPath, torrent.torrentFile);
      
      // Speichere Metadaten
      const metadata = {
        name: filename,
        size: torrent.length,
        files: torrent.files.map(file => ({
          name: file.name,
          path: file.path,
          length: file.length
        })),
        createdAt: new Date().toISOString(),
        originalFilename: options.filename,
        contentType: options.metadata?.contentType,
        description: options.metadata?.description,
        customMetadata: options.metadata?.custom
      };
      
      this.metadataDb.set(torrent.infoHash, metadata);
      await this.saveMetadata();
      
      // Seed die Datei für die angegebene Zeit, falls gewünscht
      if (options.seed !== false) {
        setTimeout(() => {
          if (!this.options.persistentSeeding) {
            torrent.destroy();
            this.torrents.delete(torrent.infoHash);
            logger.debug(`Seeding für Torrent ${torrent.infoHash} beendet`);
          }
        }, options.seedTime || this.options.seedTime);
      } else {
        torrent.destroy();
        this.torrents.delete(torrent.infoHash);
      }
      
      logger.debug(`Datei über BitTorrent gespeichert mit InfoHash ${torrent.infoHash}`);
      
      // Lösche die temporäre Datei, falls sie nicht geseedet wird
      if (options.seed === false) {
        await unlink(filePath);
      }
      
      return {
        infoHash: torrent.infoHash,
        magnetUri: torrent.magnetURI,
        torrentFile: torrent.torrentFile,
        size: torrent.length,
        files: torrent.files.map(file => ({
          name: file.name,
          path: file.path,
          length: file.length
        })),
        trackers: torrent.announce,
        webSeeds: torrent.urlList
      };
    } catch (error) {
      logger.error('Fehler beim Speichern der Datei über BitTorrent', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Erstellt ein Torrent für eine Datei
   * @param {string} filePath - Pfad zur Datei
   * @param {Object} options - Torrent-Optionen
   * @returns {Promise<Object>} Torrent-Objekt
   */
  async createTorrent(filePath, options = {}) {
    return new Promise((resolve, reject) => {
      this.client.seed(filePath, options, torrent => {
        this.torrents.set(torrent.infoHash, torrent);
        resolve(torrent);
      }).on('error', error => {
        reject(error);
      });
    });
  }
  
  /**
   * Speichert JSON-Daten über BitTorrent
   * @param {Object} data - Zu speichernde JSON-Daten
   * @param {Object} options - Speicheroptionen
   * @returns {Promise<Object>} BitTorrent-Speicherinformationen
   */
  async storeJson(data, options = {}) {
    try {
      logger.info('Speichere JSON-Daten über BitTorrent');
      
      // Konvertiere die JSON-Daten in einen String
      const jsonString = JSON.stringify(data);
      
      // Speichere die JSON-Daten als Datei
      return await this.storeFile(Buffer.from(jsonString), {
        filename: options.filename || 'data.json',
        seed: options.seed,
        trackers: options.trackers,
        webSeeds: options.webSeeds,
        metadata: {
          ...options.metadata,
          contentType: 'application/json'
        }
      });
    } catch (error) {
      logger.error('Fehler beim Speichern der JSON-Daten über BitTorrent', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Datei über BitTorrent ab
   * @param {string} infoHash - InfoHash des Torrents
   * @param {Object} options - Abrufoptionen
   * @param {number} options.timeout - Timeout in Millisekunden
   * @param {boolean} options.stream - Ob die Datei als Stream zurückgegeben werden soll
   * @returns {Promise<Buffer|stream.Readable>} Abgerufene Daten oder Stream
   */
  async retrieveFile(infoHash, options = {}) {
    try {
      logger.info(`Rufe Datei mit InfoHash ${infoHash} über BitTorrent ab`);
      
      // Prüfe, ob der Torrent bereits geladen ist
      let torrent = this.torrents.get(infoHash);
      
      if (!torrent) {
        // Prüfe, ob eine Torrent-Datei existiert
        const torrentPath = path.join(this.options.tempDir, `${infoHash}.torrent`);
        
        try {
          await stat(torrentPath);
          torrent = await this.addTorrent(torrentPath);
        } catch (error) {
          // Versuche, den Torrent über den Magnet-Link hinzuzufügen
          const magnetUri = `magnet:?xt=urn:btih:${infoHash}`;
          torrent = await this.addTorrent(magnetUri);
        }
      }
      
      // Warte, bis der Torrent vollständig heruntergeladen ist
      if (!torrent.done) {
        await this.waitForTorrent(torrent, options.timeout);
      }
      
      // Hole die Hauptdatei des Torrents
      const file = torrent.files[0];
      
      if (options.stream) {
        // Gib einen Stream zurück
        return file.createReadStream();
      } else {
        // Lese die Datei in einen Buffer
        return new Promise((resolve, reject) => {
          const chunks = [];
          const stream = file.createReadStream();
          
          stream.on('data', chunk => {
            chunks.push(chunk);
          });
          
          stream.on('end', () => {
            resolve(Buffer.concat(chunks));
          });
          
          stream.on('error', error => {
            reject(error);
          });
        });
      }
    } catch (error) {
      logger.error('Fehler beim Abrufen der Datei über BitTorrent', {
        error: error.message,
        infoHash
      });
      throw error;
    }
  }
  
  /**
   * Fügt einen Torrent zum Client hinzu
   * @param {string} torrentId - Torrent-ID (Pfad, Magnet-URI, InfoHash)
   * @returns {Promise<Object>} Torrent-Objekt
   */
  async addTorrent(torrentId) {
    return new Promise((resolve, reject) => {
      this.client.add(torrentId, { path: this.options.tempDir }, torrent => {
        this.torrents.set(torrent.infoHash, torrent);
        resolve(torrent);
      }).on('error', error => {
        reject(error);
      });
    });
  }
  
  /**
   * Wartet, bis ein Torrent vollständig heruntergeladen ist
   * @param {Object} torrent - Torrent-Objekt
   * @param {number} timeout - Timeout in Millisekunden
   * @returns {Promise<void>}
   */
  async waitForTorrent(torrent, timeout = 60000) {
    return new Promise((resolve, reject) => {
      // Setze einen Timeout
      const timeoutId = setTimeout(() => {
        reject(new Error(`Timeout beim Herunterladen des Torrents ${torrent.infoHash}`));
      }, timeout);
      
      // Warte auf das 'done'-Ereignis
      if (torrent.done) {
        clearTimeout(timeoutId);
        resolve();
      } else {
        torrent.on('done', () => {
          clearTimeout(timeoutId);
          resolve();
        });
      }
    });
  }
  
  /**
   * Ruft JSON-Daten über BitTorrent ab
   * @param {string} infoHash - InfoHash des Torrents
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Object>} Abgerufene JSON-Daten
   */
  async retrieveJson(infoHash, options = {}) {
    try {
      logger.info(`Rufe JSON-Daten mit InfoHash ${infoHash} über BitTorrent ab`);
      
      // Hole die Datei
      const data = await this.retrieveFile(infoHash, options);
      
      // Konvertiere die Daten in ein JSON-Objekt
      const jsonData = JSON.parse(data.toString());
      
      logger.debug(`JSON-Daten mit InfoHash ${infoHash} erfolgreich abgerufen`);
      
      return jsonData;
    } catch (error) {
      logger.error('Fehler beim Abrufen der JSON-Daten über BitTorrent', {
        error: error.message,
        infoHash
      });
      throw error;
    }
  }
  
  /**
   * Prüft, ob ein Torrent existiert
   * @param {string} infoHash - InfoHash des Torrents
   * @param {Object} options - Prüfoptionen
   * @returns {Promise<boolean>} True, wenn der Torrent existiert
   */
  async exists(infoHash, options = {}) {
    try {
      logger.info(`Prüfe, ob Torrent mit InfoHash ${infoHash} existiert`);
      
      // Prüfe, ob der Torrent bereits geladen ist
      if (this.torrents.has(infoHash)) {
        return true;
      }
      
      // Prüfe, ob eine Torrent-Datei existiert
      const torrentPath = path.join(this.options.tempDir, `${infoHash}.torrent`);
      
      try {
        await stat(torrentPath);
        return true;
      } catch (error) {
        // Prüfe, ob Metadaten existieren
        return this.metadataDb.has(infoHash);
      }
    } catch (error) {
      logger.error('Fehler beim Prüfen der Existenz des Torrents', {
        error: error.message,
        infoHash
      });
      return false;
    }
  }
  
  /**
   * Löscht einen Torrent
   * @param {string} infoHash - InfoHash des Torrents
   * @param {Object} options - Löschoptionen
   * @param {boolean} options.removeFiles - Ob die Dateien gelöscht werden sollen
   * @returns {Promise<boolean>} True, wenn der Torrent erfolgreich gelöscht wurde
   */
  async delete(infoHash, options = {}) {
    try {
      logger.info(`Lösche Torrent mit InfoHash ${infoHash}`);
      
      // Prüfe, ob der Torrent geladen ist
      const torrent = this.torrents.get(infoHash);
      
      if (torrent) {
        // Zerstöre den Torrent
        await new Promise(resolve => {
          torrent.destroy({ destroyStore: options.removeFiles }, () => {
            resolve();
          });
        });
        
        this.torrents.delete(infoHash);
      }
      
      // Lösche die Torrent-Datei
      const torrentPath = path.join(this.options.tempDir, `${infoHash}.torrent`);
      
      try {
        await unlink(torrentPath);
      } catch (error) {
        logger.warn(`Torrent-Datei für ${infoHash} konnte nicht gelöscht werden`);
      }
      
      // Lösche die Metadaten
      this.metadataDb.delete(infoHash);
      await this.saveMetadata();
      
      logger.debug(`Torrent mit InfoHash ${infoHash} erfolgreich gelöscht`);
      
      return true;
    } catch (error) {
      logger.error('Fehler beim Löschen des Torrents', {
        error: error.message,
        infoHash
      });
      return false;
    }
  }
  
  /**
   * Generiert eine URL für den Zugriff auf die Datei
   * @param {string} infoHash - InfoHash des Torrents
   * @param {Object} options - URL-Optionen
   * @returns {Promise<string>} Magnet-URI für den Zugriff auf die Datei
   */
  async getAccessUrl(infoHash, options = {}) {
    try {
      logger.info(`Generiere Zugriffs-URL für Torrent mit InfoHash ${infoHash}`);
      
      // Prüfe, ob der Torrent geladen ist
      const torrent = this.torrents.get(infoHash);
      
      if (torrent) {
        return torrent.magnetURI;
      }
      
      // Prüfe, ob Metadaten existieren
      const metadata = this.metadataDb.get(infoHash);
      
      if (metadata) {
        // Erstelle einen Magnet-Link
        const trackers = this.options.trackers.map(tracker => `&tr=${encodeURIComponent(tracker)}`).join('');
        const magnetUri = `magnet:?xt=urn:btih:${infoHash}&dn=${encodeURIComponent(metadata.name)}${trackers}`;
        
        return magnetUri;
      }
      
      throw new Error(`Torrent mit InfoHash ${infoHash} nicht gefunden`);
    } catch (error) {
      logger.error('Fehler beim Generieren der Zugriffs-URL', {
        error: error.message,
        infoHash
      });
      throw error;
    }
  }
}/**
 * @fileoverview BitTorrent-Adapter für dezentrale Speicherung großer Dateien
 * 
 * Dieser Adapter implementiert das StorageAdapter-Interface für BitTorrent.
 * Er wird hauptsächlich für die Speicherung und Verteilung großer Dateien wie
 * Publikationen, Patente und Forschungsdatensätze verwendet.
 */

import fs from 'fs';
import path from 'path';
import crypto from 'crypto';
import { promisify } from 'util';
import WebTorrent from 'webtorrent';
import { StorageAdapter } from './StorageAdapter.js';
import { logger } from '../../utils/logger.js';

// Promisify fs-Funktionen
const mkdir = promisify(fs.mkdir);
const writeFile = promisify(fs.writeFile);
const readFile = promisify(fs.readFile);
const stat = promisify(fs.stat);
const unlink = promisify(fs.unlink);

/**
 * BitTorrent-Adapter für dezentrale Speicherung großer Dateien
 */
export class BitTorrentAdapter extends StorageAdapter {
  /**
   * Erstellt eine neue Instanz des BitTorrentAdapter
   * @param {Object} options - Konfigurationsoptionen
   * @param {string} options.trackers - Liste der Tracker-URLs
   * @param {string} options.seedTime - Zeit in Millisekunden, für die Dateien geseedet werden sollen
   * @param {string} options.tempDir - Temporäres Verzeichnis für Dateien
   * @param {boolean} options.persistentSeeding - Ob Dateien dauerhaft geseedet werden sollen
   * @param {Array<string>} options.webSeeds - Liste der WebSeed-URLs
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      trackers: options.trackers || [
        'udp://tracker.opentrackr.org:1337/announce',
        'udp://tracker.openbittorrent.com:6969/announce',
        'udp://open.stealth.si:80/announce',
        'udp://tracker.torrent.eu.org:451/announce',
        'udp://tracker.moeking.me:6969/announce'
      ],
      seedTime: options.seedTime || 24 * 60 * 60 * 1000, // 24 Stunden
      tempDir: options.tempDir || path.join(process.cwd(), 'temp', 'torrents'),
      persistentSeeding: options.persistentSeeding !== undefined ? options.persistentSeeding : false,
      webSeeds: options.webSeeds || [],
      ...options
    };
    
    this.client = null;
    this.torrents = new Map(); // Speichert aktive Torrents
    this.metadataDb = new Map(); // Speichert Metadaten zu Torrents
    
    logger.info('BitTorrentAdapter initialisiert');
  }
  
  /**
   * Initialisiert den BitTorrent-Client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      logger.info('Initialisiere BitTorrent-Client');
      
      // Erstelle das temporäre Verzeichnis, falls es nicht existiert
      await mkdir(this.options.tempDir, { recursive: true });
      
      // Initialisiere den WebTorrent-Client
      this.client = new WebTorrent();
      
      // Lade gespeicherte Metadaten
      await this.loadMetadata();
      
      // Seed gespeicherte Torrents, falls persistentSeeding aktiviert ist
      if (this.options.persistentSeeding) {
        await this.seedStoredTorrents();
      }
      
      logger.info('BitTorrent-Client erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des BitTorrent-Clients', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Lädt gespeicherte Metadaten
   * @returns {Promise<void>}
   */
  async loadMetadata() {
    try {
      const metadataPath = path.join(this.options.tempDir, 'metadata.json');
      
      // Prüfe, ob die Metadatendatei existiert
      try {
        await stat(metadataPath);
      } catch (error) {
        // Erstelle eine leere Metadatendatei, falls sie nicht existiert
        await writeFile(metadataPath, JSON.stringify({}));
        return;
      }
      
      // Lade die Metadaten
      const metadataJson = await readFile(metadataPath, 'utf8');
      const metadata = JSON.parse(metadataJson);
      
      // Fülle die Metadaten-Map
      for (const [infoHash, torrentData] of Object.entries(metadata)) {
        this.metadataDb.set(infoHash, torrentData);
      }
      
      logger.debug(`${this.metadataDb.size} Torrent-Metadaten geladen`);
    } catch (error) {
      logger.error('Fehler beim Laden der Torrent-Metadaten', {
        error: error.message
      });
      // Erstelle eine leere Metadaten-Map im Fehlerfall
      this.metadataDb = new Map();
    }
  }
  
  /**
   * Speichert Metadaten
   * @returns {Promise<void>}
   */
  async saveMetadata() {
    try {
      const metadataPath = path.join(this.options.tempDir, 'metadata.json');
      
      // Konvertiere die Map in ein Objekt
      const metadata = {};
      for (const [infoHash, torrentData] of this.metadataDb.entries()) {
        metadata[infoHash] = torrentData;
      }
      
      // Speichere die Metadaten
      await writeFile(metadataPath, JSON.stringify(metadata, null, 2));
      
      logger.debug(`${this.metadataDb.size} Torrent-Metadaten gespeichert`);
    } catch (error) {
      logger.error('Fehler beim Speichern der Torrent-Metadaten', {
        error: error.message
      });
    }
  }
  
  /**
   * Seed gespeicherte Torrents
   * @returns {Promise<void>}
   */
  async seedStoredTorrents() {
    try {
      logger.info('Starte Seeding gespeicherter Torrents');
      
      const torrentsToSeed = [];
      
      // Sammle alle Torrent-Dateien
      for (const [infoHash, metadata] of this.metadataDb.entries()) {
        const torrentPath = path.join(this.options.tempDir, `${infoHash}.torrent`);
        
        try {
          // Prüfe, ob die Torrent-Datei existiert
          await stat(torrentPath);
          torrentsToSeed.push({ path: torrentPath, metadata });
        } catch (error) {
          logger.warn(`Torrent-Datei für ${infoHash} nicht gefunden`);
        }
      }
      
      // Seed die Torrents
      for (const { path: torrentPath, metadata } of torrentsToSeed) {
        try {
          const torrent = await this.seedTorrentFile(torrentPath);
          this.torrents.set(torrent.infoHash, torrent);
          logger.debug(`Torrent ${torrent.infoHash} wird geseedet`);
        } catch (error) {
          logger.error(`Fehler beim Seeden von Torrent ${metadata.name}`, {
            error: error.message
          });
        }
      }
      
      logger.info(`${this.torrents.size} Torrents werden geseedet`);
    } catch (error) {
      logger.error('Fehler beim Seeden gespeicherter Torrents', {
        error: error.message
      });
    }
  }
  
  /**
   * Seed eine Torrent-Datei
   * @param {string} torrentPath - Pfad zur Torrent-Datei
   * @returns {Promise<Object>} Torrent-Objekt
   */
  async seedTorrentFile(torrentPath) {
    return new Promise((resolve, reject) => {
      this.client.add(torrentPath, { path: this.options.tempDir }, torrent => {
        resolve(torrent);
      }).on('error', error => {
        reject(error);
      });
    });
  }
  
  /**
   * Speichert eine Datei über BitTorrent
   * @param {Buffer|string} data - Zu speichernde Daten
   * @param {Object} options - Speicheroptionen
   * @param {string} options.filename - Dateiname
   * @param {boolean} options.seed - Ob die Datei geseedet werden soll
   * @param {Array<string>} options.trackers - Zusätzliche Tracker
   * @param {Array<string>} options.webSeeds - Zusätzliche WebSeeds
   * @param {Object} options.metadata - Zusätzliche Metadaten
   * @returns {Promise<Object>} BitTorrent-Speicherinformationen
   */
  async storeFile(data, options = {}) {
    try {
      logger.info('Speichere Datei über BitTorrent');
      
      // Generiere einen eindeutigen Dateinamen, falls keiner angegeben ist
      const filename = options.filename || `file-${crypto.randomBytes(8).toString('hex')}`;
      const filePath = path.join(this.options.tempDir, filename);
      
      // Schreibe die Datei auf die Festplatte
      await writeFile(filePath, data);
      
      // Erstelle ein Torrent
      const torrent = await this.createTorrent(filePath, {
        name: filename,
        comment: options.metadata?.description || 'DeSci-Scholar File',
        createdBy: 'DeSci-Scholar BitTorrentAdapter',
        private: false,
        announceList: [
          ...this.options.trackers,
          ...(options.trackers || [])
        ],
        urlList: [
          ...this.options.webSeeds,
          ...(options.webSeeds || [])
        ]
      });
      
      // Speichere die Torrent-Datei
      const torrentPath = path.join(this.options.tempDir, `${torrent.infoHash}.torrent`);
      await writeFile(torrentPath, torrent.torrentFile);
      
      // Speichere Metadaten
      const metadata = {
        name: filename,
        size: torrent.length,
        files: torrent.files.map(file => ({
          name: file.name,
          path: file.path,
          length: file.length
        })),
        createdAt: new Date().toISOString(),
        originalFilename: options.filename,
        contentType: options.metadata?.contentType,
        description: options.metadata?.description,
        customMetadata: options.metadata?.custom
      };
      
      this.metadataDb.set(torrent.infoHash, metadata);
      await this.saveMetadata();
      
      // Seed die Datei für die angegebene Zeit, falls gewünscht
      if (options.seed !== false) {
        setTimeout(() => {
          if (!this.options.persistentSeeding) {
            torrent.destroy();
            this.torrents.delete(torrent.infoHash);
            logger.debug(`Seeding für Torrent ${torrent.infoHash} beendet`);
          }
        }, options.seedTime || this.options.seedTime);
      } else {
        torrent.destroy();
        this.torrents.delete(torrent.infoHash);
      }
      
      logger.debug(`Datei über BitTorrent gespeichert mit InfoHash ${torrent.infoHash}`);
      
      // Lösche die temporäre Datei, falls sie nicht geseedet wird
      if (options.seed === false) {
        await unlink(filePath);
      }
      
      return {
        infoHash: torrent.infoHash,
        magnetUri: torrent.magnetURI,
        torrentFile: torrent.torrentFile,
        size: torrent.length,
        files: torrent.files.map(file => ({
          name: file.name,
          path: file.path,
          length: file.length
        })),
        trackers: torrent.announce,
        webSeeds: torrent.urlList
      };
    } catch (error) {
      logger.error('Fehler beim Speichern der Datei über BitTorrent', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Erstellt ein Torrent für eine Datei
   * @param {string} filePath - Pfad zur Datei
   * @param {Object} options - Torrent-Optionen
   * @returns {Promise<Object>} Torrent-Objekt
   */
  async createTorrent(filePath, options = {}) {
    return new Promise((resolve, reject) => {
      this.client.seed(filePath, options, torrent => {
        this.torrents.set(torrent.infoHash, torrent);
        resolve(torrent);
      }).on('error', error => {
        reject(error);
      });
    });
  }
  
  /**
   * Speichert JSON-Daten über BitTorrent
   * @param {Object} data - Zu speichernde JSON-Daten
   * @param {Object} options - Speicheroptionen
   * @returns {Promise<Object>} BitTorrent-Speicherinformationen
   */
  async storeJson(data, options = {}) {
    try {
      logger.info('Speichere JSON-Daten über BitTorrent');
      
      // Konvertiere die JSON-Daten in einen String
      const jsonString = JSON.stringify(data);
      
      // Speichere die JSON-Daten als Datei
      return await this.storeFile(Buffer.from(jsonString), {
        filename: options.filename || 'data.json',
        seed: options.seed,
        trackers: options.trackers,
        webSeeds: options.webSeeds,
        metadata: {
          ...options.metadata,
          contentType: 'application/json'
        }
      });
    } catch (error) {
      logger.error('Fehler beim Speichern der JSON-Daten über BitTorrent', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Datei über BitTorrent ab
   * @param {string} infoHash - InfoHash des Torrents
   * @param {Object} options - Abrufoptionen
   * @param {number} options.timeout - Timeout in Millisekunden
   * @param {boolean} options.stream - Ob die Datei als Stream zurückgegeben werden soll
   * @returns {Promise<Buffer|stream.Readable>} Abgerufene Daten oder Stream
   */
  async retrieveFile(infoHash, options = {}) {
    try {
      logger.info(`Rufe Datei mit InfoHash ${infoHash} über BitTorrent ab`);
      
      // Prüfe, ob der Torrent bereits geladen ist
      let torrent = this.torrents.get(infoHash);
      
      if (!torrent) {
        // Prüfe, ob eine Torrent-Datei existiert
        const torrentPath = path.join(this.options.tempDir, `${infoHash}.torrent`);
        
        try {
          await stat(torrentPath);
          torrent = await this.addTorrent(torrentPath);
        } catch (error) {
          // Versuche, den Torrent über den Magnet-Link hinzuzufügen
          const magnetUri = `magnet:?xt=urn:btih:${infoHash}`;
          torrent = await this.addTorrent(magnetUri);
        }
      }
      
      // Warte, bis der Torrent vollständig heruntergeladen ist
      if (!torrent.done) {
        await this.waitForTorrent(torrent, options.timeout);
      }
      
      // Hole die Hauptdatei des Torrents
      const file = torrent.files[0];
      
      if (options.stream) {
        // Gib einen Stream zurück
        return file.createReadStream();
      } else {
        // Lese die Datei in einen Buffer
        return new Promise((resolve, reject) => {
          const chunks = [];
          const stream = file.createReadStream();
          
          stream.on('data', chunk => {
            chunks.push(chunk);
          });
          
          stream.on('end', () => {
            resolve(Buffer.concat(chunks));
          });
          
          stream.on('error', error => {
            reject(error);
          });
        });
      }
    } catch (error) {
      logger.error('Fehler beim Abrufen der Datei über BitTorrent', {
        error: error.message,
        infoHash
      });
      throw error;
    }
  }
  
  /**
   * Fügt einen Torrent zum Client hinzu
   * @param {string} torrentId - Torrent-ID (Pfad, Magnet-URI, InfoHash)
   * @returns {Promise<Object>} Torrent-Objekt
   */
  async addTorrent(torrentId) {
    return new Promise((resolve, reject) => {
      this.client.add(torrentId, { path: this.options.tempDir }, torrent => {
        this.torrents.set(torrent.infoHash, torrent);
        resolve(torrent);
      }).on('error', error => {
        reject(error);
      });
    });
  }
  
  /**
   * Wartet, bis ein Torrent vollständig heruntergeladen ist
   * @param {Object} torrent - Torrent-Objekt
   * @param {number} timeout - Timeout in Millisekunden
   * @returns {Promise<void>}
   */
  async waitForTorrent(torrent, timeout = 60000) {
    return new Promise((resolve, reject) => {
      // Setze einen Timeout
      const timeoutId = setTimeout(() => {
        reject(new Error(`Timeout beim Herunterladen des Torrents ${torrent.infoHash}`));
      }, timeout);
      
      // Warte auf das 'done'-Ereignis
      if (torrent.done) {
        clearTimeout(timeoutId);
        resolve();
      } else {
        torrent.on('done', () => {
          clearTimeout(timeoutId);
          resolve();
        });
      }
    });
  }
  
  /**
   * Ruft JSON-Daten über BitTorrent ab
   * @param {string} infoHash - InfoHash des Torrents
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Object>} Abgerufene JSON-Daten
   */
  async retrieveJson(infoHash, options = {}) {
    try {
      logger.info(`Rufe JSON-Daten mit InfoHash ${infoHash} über BitTorrent ab`);
      
      // Hole die Datei
      const data = await this.retrieveFile(infoHash, options);
      
      // Konvertiere die Daten in ein JSON-Objekt
      const jsonData = JSON.parse(data.toString());
      
      logger.debug(`JSON-Daten mit InfoHash ${infoHash} erfolgreich abgerufen`);
      
      return jsonData;
    } catch (error) {
      logger.error('Fehler beim Abrufen der JSON-Daten über BitTorrent', {
        error: error.message,
        infoHash
      });
      throw error;
    }
  }
  
  /**
   * Prüft, ob ein Torrent existiert
   * @param {string} infoHash - InfoHash des Torrents
   * @param {Object} options - Prüfoptionen
   * @returns {Promise<boolean>} True, wenn der Torrent existiert
   */
  async exists(infoHash, options = {}) {
    try {
      logger.info(`Prüfe, ob Torrent mit InfoHash ${infoHash} existiert`);
      
      // Prüfe, ob der Torrent bereits geladen ist
      if (this.torrents.has(infoHash)) {
        return true;
      }
      
      // Prüfe, ob eine Torrent-Datei existiert
      const torrentPath = path.join(this.options.tempDir, `${infoHash}.torrent`);
      
      try {
        await stat(torrentPath);
        return true;
      } catch (error) {
        // Prüfe, ob Metadaten existieren
        return this.metadataDb.has(infoHash);
      }
    } catch (error) {
      logger.error('Fehler beim Prüfen der Existenz des Torrents', {
        error: error.message,
        infoHash
      });
      return false;
    }
  }
  
  /**
   * Löscht einen Torrent
   * @param {string} infoHash - InfoHash des Torrents
   * @param {Object} options - Löschoptionen
   * @param {boolean} options.removeFiles - Ob die Dateien gelöscht werden sollen
   * @returns {Promise<boolean>} True, wenn der Torrent erfolgreich gelöscht wurde
   */
  async delete(infoHash, options = {}) {
    try {
      logger.info(`Lösche Torrent mit InfoHash ${infoHash}`);
      
      // Prüfe, ob der Torrent geladen ist
      const torrent = this.torrents.get(infoHash);
      
      if (torrent) {
        // Zerstöre den Torrent
        await new Promise(resolve => {
          torrent.destroy({ destroyStore: options.removeFiles }, () => {
            resolve();
          });
        });
        
        this.torrents.delete(infoHash);
      }
      
      // Lösche die Torrent-Datei
      const torrentPath = path.join(this.options.tempDir, `${infoHash}.torrent`);
      
      try {
        await unlink(torrentPath);
      } catch (error) {
        logger.warn(`Torrent-Datei für ${infoHash} konnte nicht gelöscht werden`);
      }
      
      // Lösche die Metadaten
      this.metadataDb.delete(infoHash);
      await this.saveMetadata();
      
      logger.debug(`Torrent mit InfoHash ${infoHash} erfolgreich gelöscht`);
      
      return true;
    } catch (error) {
      logger.error('Fehler beim Löschen des Torrents', {
        error: error.message,
        infoHash
      });
      return false;
    }
  }
  
  /**
   * Generiert eine URL für den Zugriff auf die Datei
   * @param {string} infoHash - InfoHash des Torrents
   * @param {Object} options - URL-Optionen
   * @returns {Promise<string>} Magnet-URI für den Zugriff auf die Datei
   */
  async getAccessUrl(infoHash, options = {}) {
    try {
      logger.info(`Generiere Zugriffs-URL für Torrent mit InfoHash ${infoHash}`);
      
      // Prüfe, ob der Torrent geladen ist
      const torrent = this.torrents.get(infoHash);
      
      if (torrent) {
        return torrent.magnetURI;
      }
      
      // Prüfe, ob Metadaten existieren
      const metadata = this.metadataDb.get(infoHash);
      
      if (metadata) {
        // Erstelle einen Magnet-Link
        const trackers = this.options.trackers.map(tracker => `&tr=${encodeURIComponent(tracker)}`).join('');
        const magnetUri = `magnet:?xt=urn:btih:${infoHash}&dn=${encodeURIComponent(metadata.name)}${trackers}`;
        
        return magnetUri;
      }
      
      throw new Error(`Torrent mit InfoHash ${infoHash} nicht gefunden`);
    } catch (error) {
      logger.error('Fehler beim Generieren der Zugriffs-URL', {
        error: error.message,
        infoHash
      });
      throw error;
    }
  }
}/**
 * @fileoverview BitTorrent-Adapter für dezentrale Speicherung großer Dateien
 * 
 * Dieser Adapter implementiert das StorageAdapter-Interface für BitTorrent.
 * Er wird hauptsächlich für die Speicherung und Verteilung großer Dateien wie
 * Publikationen, Patente und Forschungsdatensätze verwendet.
 */

import fs from 'fs';
import path from 'path';
import crypto from 'crypto';
import { promisify } from 'util';
import WebTorrent from 'webtorrent';
import { StorageAdapter } from './StorageAdapter.js';
import { logger } from '../../utils/logger.js';

// Promisify fs-Funktionen
const mkdir = promisify(fs.mkdir);
const writeFile = promisify(fs.writeFile);
const readFile = promisify(fs.readFile);
const stat = promisify(fs.stat);
const unlink = promisify(fs.unlink);

/**
 * BitTorrent-Adapter für dezentrale Speicherung großer Dateien
 */
export class BitTorrentAdapter extends StorageAdapter {
  /**
   * Erstellt eine neue Instanz des BitTorrentAdapter
   * @param {Object} options - Konfigurationsoptionen
   * @param {string} options.trackers - Liste der Tracker-URLs
   * @param {string} options.seedTime - Zeit in Millisekunden, für die Dateien geseedet werden sollen
   * @param {string} options.tempDir - Temporäres Verzeichnis für Dateien
   * @param {boolean} options.persistentSeeding - Ob Dateien dauerhaft geseedet werden sollen
   * @param {Array<string>} options.webSeeds - Liste der WebSeed-URLs
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      trackers: options.trackers || [
        'udp://tracker.opentrackr.org:1337/announce',
        'udp://tracker.openbittorrent.com:6969/announce',
        'udp://open.stealth.si:80/announce',
        'udp://tracker.torrent.eu.org:451/announce',
        'udp://tracker.moeking.me:6969/announce'
      ],
      seedTime: options.seedTime || 24 * 60 * 60 * 1000, // 24 Stunden
      tempDir: options.tempDir || path.join(process.cwd(), 'temp', 'torrents'),
      persistentSeeding: options.persistentSeeding !== undefined ? options.persistentSeeding : false,
      webSeeds: options.webSeeds || [],
      ...options
    };
    
    this.client = null;
    this.torrents = new Map(); // Speichert aktive Torrents
    this.metadataDb = new Map(); // Speichert Metadaten zu Torrents
    
    logger.info('BitTorrentAdapter initialisiert');
  }
  
  /**
   * Initialisiert den BitTorrent-Client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      logger.info('Initialisiere BitTorrent-Client');
      
      // Erstelle das temporäre Verzeichnis, falls es nicht existiert
      await mkdir(this.options.tempDir, { recursive: true });
      
      // Initialisiere den WebTorrent-Client
      this.client = new WebTorrent();
      
      // Lade gespeicherte Metadaten
      await this.loadMetadata();
      
      // Seed gespeicherte Torrents, falls persistentSeeding aktiviert ist
      if (this.options.persistentSeeding) {
        await this.seedStoredTorrents();
      }
      
      logger.info('BitTorrent-Client erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des BitTorrent-Clients', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Lädt gespeicherte Metadaten
   * @returns {Promise<void>}
   */
  async loadMetadata() {
    try {
      const metadataPath = path.join(this.options.tempDir, 'metadata.json');
      
      // Prüfe, ob die Metadatendatei existiert
      try {
        await stat(metadataPath);
      } catch (error) {
        // Erstelle eine leere Metadatendatei, falls sie nicht existiert
        await writeFile(metadataPath, JSON.stringify({}));
        return;
      }
      
      // Lade die Metadaten
      const metadataJson = await readFile(metadataPath, 'utf8');
      const metadata = JSON.parse(metadataJson);
      
      // Fülle die Metadaten-Map
      for (const [infoHash, torrentData] of Object.entries(metadata)) {
        this.metadataDb.set(infoHash, torrentData);
      }
      
      logger.debug(`${this.metadataDb.size} Torrent-Metadaten geladen`);
    } catch (error) {
      logger.error('Fehler beim Laden der Torrent-Metadaten', {
        error: error.message
      });
      // Erstelle eine leere Metadaten-Map im Fehlerfall
      this.metadataDb = new Map();
    }
  }
  
  /**
   * Speichert Metadaten
   * @returns {Promise<void>}
   */
  async saveMetadata() {
    try {
      const metadataPath = path.join(this.options.tempDir, 'metadata.json');
      
      // Konvertiere die Map in ein Objekt
      const metadata = {};
      for (const [infoHash, torrentData] of this.metadataDb.entries()) {
        metadata[infoHash] = torrentData;
      }
      
      // Speichere die Metadaten
      await writeFile(metadataPath, JSON.stringify(metadata, null, 2));
      
      logger.debug(`${this.metadataDb.size} Torrent-Metadaten gespeichert`);
    } catch (error) {
      logger.error('Fehler beim Speichern der Torrent-Metadaten', {
        error: error.message
      });
    }
  }
  
  /**
   * Seed gespeicherte Torrents
   * @returns {Promise<void>}
   */
  async seedStoredTorrents() {
    try {
      logger.info('Starte Seeding gespeicherter Torrents');
      
      const torrentsToSeed = [];
      
      // Sammle alle Torrent-Dateien
      for (const [infoHash, metadata] of this.metadataDb.entries()) {
        const torrentPath = path.join(this.options.tempDir, `${infoHash}.torrent`);
        
        try {
          // Prüfe, ob die Torrent-Datei existiert
          await stat(torrentPath);
          torrentsToSeed.push({ path: torrentPath, metadata });
        } catch (error) {
          logger.warn(`Torrent-Datei für ${infoHash} nicht gefunden`);
        }
      }
      
      // Seed die Torrents
      for (const { path: torrentPath, metadata } of torrentsToSeed) {
        try {
          const torrent = await this.seedTorrentFile(torrentPath);
          this.torrents.set(torrent.infoHash, torrent);
          logger.debug(`Torrent ${torrent.infoHash} wird geseedet`);
        } catch (error) {
          logger.error(`Fehler beim Seeden von Torrent ${metadata.name}`, {
            error: error.message
          });
        }
      }
      
      logger.info(`${this.torrents.size} Torrents werden geseedet`);
    } catch (error) {
      logger.error('Fehler beim Seeden gespeicherter Torrents', {
        error: error.message
      });
    }
  }
  
  /**
   * Seed eine Torrent-Datei
   * @param {string} torrentPath - Pfad zur Torrent-Datei
   * @returns {Promise<Object>} Torrent-Objekt
   */
  async seedTorrentFile(torrentPath) {
    return new Promise((resolve, reject) => {
      this.client.add(torrentPath, { path: this.options.tempDir }, torrent => {
        resolve(torrent);
      }).on('error', error => {
        reject(error);
      });
    });
  }
  
  /**
   * Speichert eine Datei über BitTorrent
   * @param {Buffer|string} data - Zu speichernde Daten
   * @param {Object} options - Speicheroptionen
   * @param {string} options.filename - Dateiname
   * @param {boolean} options.seed - Ob die Datei geseedet werden soll
   * @param {Array<string>} options.trackers - Zusätzliche Tracker
   * @param {Array<string>} options.webSeeds - Zusätzliche WebSeeds
   * @param {Object} options.metadata - Zusätzliche Metadaten
   * @returns {Promise<Object>} BitTorrent-Speicherinformationen
   */
  async storeFile(data, options = {}) {
    try {
      logger.info('Speichere Datei über BitTorrent');
      
      // Generiere einen eindeutigen Dateinamen, falls keiner angegeben ist
      const filename = options.filename || `file-${crypto.randomBytes(8).toString('hex')}`;
      const filePath = path.join(this.options.tempDir, filename);
      
      // Schreibe die Datei auf die Festplatte
      await writeFile(filePath, data);
      
      // Erstelle ein Torrent
      const torrent = await this.createTorrent(filePath, {
        name: filename,
        comment: options.metadata?.description || 'DeSci-Scholar File',
        createdBy: 'DeSci-Scholar BitTorrentAdapter',
        private: false,
        announceList: [
          ...this.options.trackers,
          ...(options.trackers || [])
        ],
        urlList: [
          ...this.options.webSeeds,
          ...(options.webSeeds || [])
        ]
      });
      
      // Speichere die Torrent-Datei
      const torrentPath = path.join(this.options.tempDir, `${torrent.infoHash}.torrent`);
      await writeFile(torrentPath, torrent.torrentFile);
      
      // Speichere Metadaten
      const metadata = {
        name: filename,
        size: torrent.length,
        files: torrent.files.map(file => ({
          name: file.name,
          path: file.path,
          length: file.length
        })),
        createdAt: new Date().toISOString(),
        originalFilename: options.filename,
        contentType: options.metadata?.contentType,
        description: options.metadata?.description,
        customMetadata: options.metadata?.custom
      };
      
      this.metadataDb.set(torrent.infoHash, metadata);
      await this.saveMetadata();
      
      // Seed die Datei für die angegebene Zeit, falls gewünscht
      if (options.seed !== false) {
        setTimeout(() => {
          if (!this.options.persistentSeeding) {
            torrent.destroy();
            this.torrents.delete(torrent.infoHash);
            logger.debug(`Seeding für Torrent ${torrent.infoHash} beendet`);
          }
        }, options.seedTime || this.options.seedTime);
      } else {
        torrent.destroy();
        this.torrents.delete(torrent.infoHash);
      }
      
      logger.debug(`Datei über BitTorrent gespeichert mit InfoHash ${torrent.infoHash}`);
      
      // Lösche die temporäre Datei, falls sie nicht geseedet wird
      if (options.seed === false) {
        await unlink(filePath);
      }
      
      return {
        infoHash: torrent.infoHash,
        magnetUri: torrent.magnetURI,
        torrentFile: torrent.torrentFile,
        size: torrent.length,
        files: torrent.files.map(file => ({
          name: file.name,
          path: file.path,
          length: file.length
        })),
        trackers: torrent.announce,
        webSeeds: torrent.urlList
      };
    } catch (error) {
      logger.error('Fehler beim Speichern der Datei über BitTorrent', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Erstellt ein Torrent für eine Datei
   * @param {string} filePath - Pfad zur Datei
   * @param {Object} options - Torrent-Optionen
   * @returns {Promise<Object>} Torrent-Objekt
   */
  async createTorrent(filePath, options = {}) {
    return new Promise((resolve, reject) => {
      this.client.seed(filePath, options, torrent => {
        this.torrents.set(torrent.infoHash, torrent);
        resolve(torrent);
      }).on('error', error => {
        reject(error);
      });
    });
  }
  
  /**
   * Speichert JSON-Daten über BitTorrent
   * @param {Object} data - Zu speichernde JSON-Daten
   * @param {Object} options - Speicheroptionen
   * @returns {Promise<Object>} BitTorrent-Speicherinformationen
   */
  async storeJson(data, options = {}) {
    try {
      logger.info('Speichere JSON-Daten über BitTorrent');
      
      // Konvertiere die JSON-Daten in einen String
      const jsonString = JSON.stringify(data);
      
      // Speichere die JSON-Daten als Datei
      return await this.storeFile(Buffer.from(jsonString), {
        filename: options.filename || 'data.json',
        seed: options.seed,
        trackers: options.trackers,
        webSeeds: options.webSeeds,
        metadata: {
          ...options.metadata,
          contentType: 'application/json'
        }
      });
    } catch (error) {
      logger.error('Fehler beim Speichern der JSON-Daten über BitTorrent', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Datei über BitTorrent ab
   * @param {string} infoHash - InfoHash des Torrents
   * @param {Object} options - Abrufoptionen
   * @param {number} options.timeout - Timeout in Millisekunden
   * @param {boolean} options.stream - Ob die Datei als Stream zurückgegeben werden soll
   * @returns {Promise<Buffer|stream.Readable>} Abgerufene Daten oder Stream
   */
  async retrieveFile(infoHash, options = {}) {
    try {
      logger.info(`Rufe Datei mit InfoHash ${infoHash} über BitTorrent ab`);
      
      // Prüfe, ob der Torrent bereits geladen ist
      let torrent = this.torrents.get(infoHash);
      
      if (!torrent) {
        // Prüfe, ob eine Torrent-Datei existiert
        const torrentPath = path.join(this.options.tempDir, `${infoHash}.torrent`);
        
        try {
          await stat(torrentPath);
          torrent = await this.addTorrent(torrentPath);
        } catch (error) {
          // Versuche, den Torrent über den Magnet-Link hinzuzufügen
          const magnetUri = `magnet:?xt=urn:btih:${infoHash}`;
          torrent = await this.addTorrent(magnetUri);
        }
      }
      
      // Warte, bis der Torrent vollständig heruntergeladen ist
      if (!torrent.done) {
        await this.waitForTorrent(torrent, options.timeout);
      }
      
      // Hole die Hauptdatei des Torrents
      const file = torrent.files[0];
      
      if (options.stream) {
        // Gib einen Stream zurück
        return file.createReadStream();
      } else {
        // Lese die Datei in einen Buffer
        return new Promise((resolve, reject) => {
          const chunks = [];
          const stream = file.createReadStream();
          
          stream.on('data', chunk => {
            chunks.push(chunk);
          });
          
          stream.on('end', () => {
            resolve(Buffer.concat(chunks));
          });
          
          stream.on('error', error => {
            reject(error);
          });
        });
      }
    } catch (error) {
      logger.error('Fehler beim Abrufen der Datei über BitTorrent', {
        error: error.message,
        infoHash
      });
      throw error;
    }
  }
  
  /**
   * Fügt einen Torrent zum Client hinzu
   * @param {string} torrentId - Torrent-ID (Pfad, Magnet-URI, InfoHash)
   * @returns {Promise<Object>} Torrent-Objekt
   */
  async addTorrent(torrentId) {
    return new Promise((resolve, reject) => {
      this.client.add(torrentId, { path: this.options.tempDir }, torrent => {
        this.torrents.set(torrent.infoHash, torrent);
        resolve(torrent);
      }).on('error', error => {
        reject(error);
      });
    });
  }
  
  /**
   * Wartet, bis ein Torrent vollständig heruntergeladen ist
   * @param {Object} torrent - Torrent-Objekt
   * @param {number} timeout - Timeout in Millisekunden
   * @returns {Promise<void>}
   */
  async waitForTorrent(torrent, timeout = 60000) {
    return new Promise((resolve, reject) => {
      // Setze einen Timeout
      const timeoutId = setTimeout(() => {
        reject(new Error(`Timeout beim Herunterladen des Torrents ${torrent.infoHash}`));
      }, timeout);
      
      // Warte auf das 'done'-Ereignis
      if (torrent.done) {
        clearTimeout(timeoutId);
        resolve();
      } else {
        torrent.on('done', () => {
          clearTimeout(timeoutId);
          resolve();
        });
      }
    });
  }
  
  /**
   * Ruft JSON-Daten über BitTorrent ab
   * @param {string} infoHash - InfoHash des Torrents
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Object>} Abgerufene JSON-Daten
   */
  async retrieveJson(infoHash, options = {}) {
    try {
      logger.info(`Rufe JSON-Daten mit InfoHash ${infoHash} über BitTorrent ab`);
      
      // Hole die Datei
      const data = await this.retrieveFile(infoHash, options);
      
      // Konvertiere die Daten in ein JSON-Objekt
      const jsonData = JSON.parse(data.toString());
      
      logger.debug(`JSON-Daten mit InfoHash ${infoHash} erfolgreich abgerufen`);
      
      return jsonData;
    } catch (error) {
      logger.error('Fehler beim Abrufen der JSON-Daten über BitTorrent', {
        error: error.message,
        infoHash
      });
      throw error;
    }
  }
  
  /**
   * Prüft, ob ein Torrent existiert
   * @param {string} infoHash - InfoHash des Torrents
   * @param {Object} options - Prüfoptionen
   * @returns {Promise<boolean>} True, wenn der Torrent existiert
   */
  async exists(infoHash, options = {}) {
    try {
      logger.info(`Prüfe, ob Torrent mit InfoHash ${infoHash} existiert`);
      
      // Prüfe, ob der Torrent bereits geladen ist
      if (this.torrents.has(infoHash)) {
        return true;
      }
      
      // Prüfe, ob eine Torrent-Datei existiert
      const torrentPath = path.join(this.options.tempDir, `${infoHash}.torrent`);
      
      try {
        await stat(torrentPath);
        return true;
      } catch (error) {
        // Prüfe, ob Metadaten existieren
        return this.metadataDb.has(infoHash);
      }
    } catch (error) {
      logger.error('Fehler beim Prüfen der Existenz des Torrents', {
        error: error.message,
        infoHash
      });
      return false;
    }
  }
  
  /**
   * Löscht einen Torrent
   * @param {string} infoHash - InfoHash des Torrents
   * @param {Object} options - Löschoptionen
   * @param {boolean} options.removeFiles - Ob die Dateien gelöscht werden sollen
   * @returns {Promise<boolean>} True, wenn der Torrent erfolgreich gelöscht wurde
   */
  async delete(infoHash, options = {}) {
    try {
      logger.info(`Lösche Torrent mit InfoHash ${infoHash}`);
      
      // Prüfe, ob der Torrent geladen ist
      const torrent = this.torrents.get(infoHash);
      
      if (torrent) {
        // Zerstöre den Torrent
        await new Promise(resolve => {
          torrent.destroy({ destroyStore: options.removeFiles }, () => {
            resolve();
          });
        });
        
        this.torrents.delete(infoHash);
      }
      
      // Lösche die Torrent-Datei
      const torrentPath = path.join(this.options.tempDir, `${infoHash}.torrent`);
      
      try {
        await unlink(torrentPath);
      } catch (error) {
        logger.warn(`Torrent-Datei für ${infoHash} konnte nicht gelöscht werden`);
      }
      
      // Lösche die Metadaten
      this.metadataDb.delete(infoHash);
      await this.saveMetadata();
      
      logger.debug(`Torrent mit InfoHash ${infoHash} erfolgreich gelöscht`);
      
      return true;
    } catch (error) {
      logger.error('Fehler beim Löschen des Torrents', {
        error: error.message,
        infoHash
      });
      return false;
    }
  }
  
  /**
   * Generiert eine URL für den Zugriff auf die Datei
   * @param {string} infoHash - InfoHash des Torrents
   * @param {Object} options - URL-Optionen
   * @returns {Promise<string>} Magnet-URI für den Zugriff auf die Datei
   */
  async getAccessUrl(infoHash, options = {}) {
    try {
      logger.info(`Generiere Zugriffs-URL für Torrent mit InfoHash ${infoHash}`);
      
      // Prüfe, ob der Torrent geladen ist
      const torrent = this.torrents.get(infoHash);
      
      if (torrent) {
        return torrent.magnetURI;
      }
      
      // Prüfe, ob Metadaten existieren
      const metadata = this.metadataDb.get(infoHash);
      
      if (metadata) {
        // Erstelle einen Magnet-Link
        const trackers = this.options.trackers.map(tracker => `&tr=${encodeURIComponent(tracker)}`).join('');
        const magnetUri = `magnet:?xt=urn:btih:${infoHash}&dn=${encodeURIComponent(metadata.name)}${trackers}`;
        
        return magnetUri;
      }
      
      throw new Error(`Torrent mit InfoHash ${infoHash} nicht gefunden`);
    } catch (error) {
      logger.error('Fehler beim Generieren der Zugriffs-URL', {
        error: error.message,
        infoHash
      });
      throw error;
    }
  }
}/**
 * @fileoverview BitTorrent-Adapter für dezentrale Speicherung großer Dateien
 * 
 * Dieser Adapter implementiert das StorageAdapter-Interface für BitTorrent.
 * Er wird hauptsächlich für die Speicherung und Verteilung großer Dateien wie
 * Publikationen, Patente und Forschungsdatensätze verwendet.
 */

import fs from 'fs';
import path from 'path';
import crypto from 'crypto';
import { promisify } from 'util';
import WebTorrent from 'webtorrent';
import { StorageAdapter } from './StorageAdapter.js';
import { logger } from '../../utils/logger.js';

// Promisify fs-Funktionen
const mkdir = promisify(fs.mkdir);
const writeFile = promisify(fs.writeFile);
const readFile = promisify(fs.readFile);
const stat = promisify(fs.stat);
const unlink = promisify(fs.unlink);

/**
 * BitTorrent-Adapter für dezentrale Speicherung großer Dateien
 */
export class BitTorrentAdapter extends StorageAdapter {
  /**
   * Erstellt eine neue Instanz des BitTorrentAdapter
   * @param {Object} options - Konfigurationsoptionen
   * @param {string} options.trackers - Liste der Tracker-URLs
   * @param {string} options.seedTime - Zeit in Millisekunden, für die Dateien geseedet werden sollen
   * @param {string} options.tempDir - Temporäres Verzeichnis für Dateien
   * @param {boolean} options.persistentSeeding - Ob Dateien dauerhaft geseedet werden sollen
   * @param {Array<string>} options.webSeeds - Liste der WebSeed-URLs
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      trackers: options.trackers || [
        'udp://tracker.opentrackr.org:1337/announce',
        'udp://tracker.openbittorrent.com:6969/announce',
        'udp://open.stealth.si:80/announce',
        'udp://tracker.torrent.eu.org:451/announce',
        'udp://tracker.moeking.me:6969/announce'
      ],
      seedTime: options.seedTime || 24 * 60 * 60 * 1000, // 24 Stunden
      tempDir: options.tempDir || path.join(process.cwd(), 'temp', 'torrents'),
      persistentSeeding: options.persistentSeeding !== undefined ? options.persistentSeeding : false,
      webSeeds: options.webSeeds || [],
      ...options
    };
    
    this.client = null;
    this.torrents = new Map(); // Speichert aktive Torrents
    this.metadataDb = new Map(); // Speichert Metadaten zu Torrents
    
    logger.info('BitTorrentAdapter initialisiert');
  }
  
  /**
   * Initialisiert den BitTorrent-Client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      logger.info('Initialisiere BitTorrent-Client');
      
      // Erstelle das temporäre Verzeichnis, falls es nicht existiert
      await mkdir(this.options.tempDir, { recursive: true });
      
      // Initialisiere den WebTorrent-Client
      this.client = new WebTorrent();
      
      // Lade gespeicherte Metadaten
      await this.loadMetadata();
      
      // Seed gespeicherte Torrents, falls persistentSeeding aktiviert ist
      if (this.options.persistentSeeding) {
        await this.seedStoredTorrents();
      }
      
      logger.info('BitTorrent-Client erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des BitTorrent-Clients', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Lädt gespeicherte Metadaten
   * @returns {Promise<void>}
   */
  async loadMetadata() {
    try {
      const metadataPath = path.join(this.options.tempDir, 'metadata.json');
      
      // Prüfe, ob die Metadatendatei existiert
      try {
        await stat(metadataPath);
      } catch (error) {
        // Erstelle eine leere Metadatendatei, falls sie nicht existiert
        await writeFile(metadataPath, JSON.stringify({}));
        return;
      }
      
      // Lade die Metadaten
      const metadataJson = await readFile(metadataPath, 'utf8');
      const metadata = JSON.parse(metadataJson);
      
      // Fülle die Metadaten-Map
      for (const [infoHash, torrentData] of Object.entries(metadata)) {
        this.metadataDb.set(infoHash, torrentData);
      }
      
      logger.debug(`${this.metadataDb.size} Torrent-Metadaten geladen`);
    } catch (error) {
      logger.error('Fehler beim Laden der Torrent-Metadaten', {
        error: error.message
      });
      // Erstelle eine leere Metadaten-Map im Fehlerfall
      this.metadataDb = new Map();
    }
  }
  
  /**
   * Speichert Metadaten
   * @returns {Promise<void>}
   */
  async saveMetadata() {
    try {
      const metadataPath = path.join(this.options.tempDir, 'metadata.json');
      
      // Konvertiere die Map in ein Objekt
      const metadata = {};
      for (const [infoHash, torrentData] of this.metadataDb.entries()) {
        metadata[infoHash] = torrentData;
      }
      
      // Speichere die Metadaten
      await writeFile(metadataPath, JSON.stringify(metadata, null, 2));
      
      logger.debug(`${this.metadataDb.size} Torrent-Metadaten gespeichert`);
    } catch (error) {
      logger.error('Fehler beim Speichern der Torrent-Metadaten', {
        error: error.message
      });
    }
  }
  
  /**
   * Seed gespeicherte Torrents
   * @returns {Promise<void>}
   */
  async seedStoredTorrents() {
    try {
      logger.info('Starte Seeding gespeicherter Torrents');
      
      const torrentsToSeed = [];
      
      // Sammle alle Torrent-Dateien
      for (const [infoHash, metadata] of this.metadataDb.entries()) {
        const torrentPath = path.join(this.options.tempDir, `${infoHash}.torrent`);
        
        try {
          // Prüfe, ob die Torrent-Datei existiert
          await stat(torrentPath);
          torrentsToSeed.push({ path: torrentPath, metadata });
        } catch (error) {
          logger.warn(`Torrent-Datei für ${infoHash} nicht gefunden`);
        }
      }
      
      // Seed die Torrents
      for (const { path: torrentPath, metadata } of torrentsToSeed) {
        try {
          const torrent = await this.seedTorrentFile(torrentPath);
          this.torrents.set(torrent.infoHash, torrent);
          logger.debug(`Torrent ${torrent.infoHash} wird geseedet`);
        } catch (error) {
          logger.error(`Fehler beim Seeden von Torrent ${metadata.name}`, {
            error: error.message
          });
        }
      }
      
      logger.info(`${this.torrents.size} Torrents werden geseedet`);
    } catch (error) {
      logger.error('Fehler beim Seeden gespeicherter Torrents', {
        error: error.message
      });
    }
  }
  
  /**
   * Seed eine Torrent-Datei
   * @param {string} torrentPath - Pfad zur Torrent-Datei
   * @returns {Promise<Object>} Torrent-Objekt
   */
  async seedTorrentFile(torrentPath) {
    return new Promise((resolve, reject) => {
      this.client.add(torrentPath, { path: this.options.tempDir }, torrent => {
        resolve(torrent);
      }).on('error', error => {
        reject(error);
      });
    });
  }
  
  /**
   * Speichert eine Datei über BitTorrent
   * @param {Buffer|string} data - Zu speichernde Daten
   * @param {Object} options - Speicheroptionen
   * @param {string} options.filename - Dateiname
   * @param {boolean} options.seed - Ob die Datei geseedet werden soll
   * @param {Array<string>} options.trackers - Zusätzliche Tracker
   * @param {Array<string>} options.webSeeds - Zusätzliche WebSeeds
   * @param {Object} options.metadata - Zusätzliche Metadaten
   * @returns {Promise<Object>} BitTorrent-Speicherinformationen
   */
  async storeFile(data, options = {}) {
    try {
      logger.info('Speichere Datei über BitTorrent');
      
      // Generiere einen eindeutigen Dateinamen, falls keiner angegeben ist
      const filename = options.filename || `file-${crypto.randomBytes(8).toString('hex')}`;
      const filePath = path.join(this.options.tempDir, filename);
      
      // Schreibe die Datei auf die Festplatte
      await writeFile(filePath, data);
      
      // Erstelle ein Torrent
      const torrent = await this.createTorrent(filePath, {
        name: filename,
        comment: options.metadata?.description || 'DeSci-Scholar File',
        createdBy: 'DeSci-Scholar BitTorrentAdapter',
        private: false,
        announceList: [
          ...this.options.trackers,
          ...(options.trackers || [])
        ],
        urlList: [
          ...this.options.webSeeds,
          ...(options.webSeeds || [])
        ]
      });
      
      // Speichere die Torrent-Datei
      const torrentPath = path.join(this.options.tempDir, `${torrent.infoHash}.torrent`);
      await writeFile(torrentPath, torrent.torrentFile);
      
      // Speichere Metadaten
      const metadata = {
        name: filename,
        size: torrent.length,
        files: torrent.files.map(file => ({
          name: file.name,
          path: file.path,
          length: file.length
        })),
        createdAt: new Date().toISOString(),
        originalFilename: options.filename,
        contentType: options.metadata?.contentType,
        description: options.metadata?.description,
        customMetadata: options.metadata?.custom
      };
      
      this.metadataDb.set(torrent.infoHash, metadata);
      await this.saveMetadata();
      
      // Seed die Datei für die angegebene Zeit, falls gewünscht
      if (options.seed !== false) {
        setTimeout(() => {
          if (!this.options.persistentSeeding) {
            torrent.destroy();
            this.torrents.delete(torrent.infoHash);
            logger.debug(`Seeding für Torrent ${torrent.infoHash} beendet`);
          }
        }, options.seedTime || this.options.seedTime);
      } else {
        torrent.destroy();
        this.torrents.delete(torrent.infoHash);
      }
      
      logger.debug(`Datei über BitTorrent gespeichert mit InfoHash ${torrent.infoHash}`);
      
      // Lösche die temporäre Datei, falls sie nicht geseedet wird
      if (options.seed === false) {
        await unlink(filePath);
      }
      
      return {
        infoHash: torrent.infoHash,
        magnetUri: torrent.magnetURI,
        torrentFile: torrent.torrentFile,
        size: torrent.length,
        files: torrent.files.map(file => ({
          name: file.name,
          path: file.path,
          length: file.length
        })),
        trackers: torrent.announce,
        webSeeds: torrent.urlList
      };
    } catch (error) {
      logger.error('Fehler beim Speichern der Datei über BitTorrent', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Erstellt ein Torrent für eine Datei
   * @param {string} filePath - Pfad zur Datei
   * @param {Object} options - Torrent-Optionen
   * @returns {Promise<Object>} Torrent-Objekt
   */
  async createTorrent(filePath, options = {}) {
    return new Promise((resolve, reject) => {
      this.client.seed(filePath, options, torrent => {
        this.torrents.set(torrent.infoHash, torrent);
        resolve(torrent);
      }).on('error', error => {
        reject(error);
      });
    });
  }
  
  /**
   * Speichert JSON-Daten über BitTorrent
   * @param {Object} data - Zu speichernde JSON-Daten
   * @param {Object} options - Speicheroptionen
   * @returns {Promise<Object>} BitTorrent-Speicherinformationen
   */
  async storeJson(data, options = {}) {
    try {
      logger.info('Speichere JSON-Daten über BitTorrent');
      
      // Konvertiere die JSON-Daten in einen String
      const jsonString = JSON.stringify(data);
      
      // Speichere die JSON-Daten als Datei
      return await this.storeFile(Buffer.from(jsonString), {
        filename: options.filename || 'data.json',
        seed: options.seed,
        trackers: options.trackers,
        webSeeds: options.webSeeds,
        metadata: {
          ...options.metadata,
          contentType: 'application/json'
        }
      });
    } catch (error) {
      logger.error('Fehler beim Speichern der JSON-Daten über BitTorrent', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Datei über BitTorrent ab
   * @param {string} infoHash - InfoHash des Torrents
   * @param {Object} options - Abrufoptionen
   * @param {number} options.timeout - Timeout in Millisekunden
   * @param {boolean} options.stream - Ob die Datei als Stream zurückgegeben werden soll
   * @returns {Promise<Buffer|stream.Readable>} Abgerufene Daten oder Stream
   */
  async retrieveFile(infoHash, options = {}) {
    try {
      logger.info(`Rufe Datei mit InfoHash ${infoHash} über BitTorrent ab`);
      
      // Prüfe, ob der Torrent bereits geladen ist
      let torrent = this.torrents.get(infoHash);
      
      if (!torrent) {
        // Prüfe, ob eine Torrent-Datei existiert
        const torrentPath = path.join(this.options.tempDir, `${infoHash}.torrent`);
        
        try {
          await stat(torrentPath);
          torrent = await this.addTorrent(torrentPath);
        } catch (error) {
          // Versuche, den Torrent über den Magnet-Link hinzuzufügen
          const magnetUri = `magnet:?xt=urn:btih:${infoHash}`;
          torrent = await this.addTorrent(magnetUri);
        }
      }
      
      // Warte, bis der Torrent vollständig heruntergeladen ist
      if (!torrent.done) {
        await this.waitForTorrent(torrent, options.timeout);
      }
      
      // Hole die Hauptdatei des Torrents
      const file = torrent.files[0];
      
      if (options.stream) {
        // Gib einen Stream zurück
        return file.createReadStream();
      } else {
        // Lese die Datei in einen Buffer
        return new Promise((resolve, reject) => {
          const chunks = [];
          const stream = file.createReadStream();
          
          stream.on('data', chunk => {
            chunks.push(chunk);
          });
          
          stream.on('end', () => {
            resolve(Buffer.concat(chunks));
          });
          
          stream.on('error', error => {
            reject(error);
          });
        });
      }
    } catch (error) {
      logger.error('Fehler beim Abrufen der Datei über BitTorrent', {
        error: error.message,
        infoHash
      });
      throw error;
    }
  }
  
  /**
   * Fügt einen Torrent zum Client hinzu
   * @param {string} torrentId - Torrent-ID (Pfad, Magnet-URI, InfoHash)
   * @returns {Promise<Object>} Torrent-Objekt
   */
  async addTorrent(torrentId) {
    return new Promise((resolve, reject) => {
      this.client.add(torrentId, { path: this.options.tempDir }, torrent => {
        this.torrents.set(torrent.infoHash, torrent);
        resolve(torrent);
      }).on('error', error => {
        reject(error);
      });
    });
  }
  
  /**
   * Wartet, bis ein Torrent vollständig heruntergeladen ist
   * @param {Object} torrent - Torrent-Objekt
   * @param {number} timeout - Timeout in Millisekunden
   * @returns {Promise<void>}
   */
  async waitForTorrent(torrent, timeout = 60000) {
    return new Promise((resolve, reject) => {
      // Setze einen Timeout
      const timeoutId = setTimeout(() => {
        reject(new Error(`Timeout beim Herunterladen des Torrents ${torrent.infoHash}`));
      }, timeout);
      
      // Warte auf das 'done'-Ereignis
      if (torrent.done) {
        clearTimeout(timeoutId);
        resolve();
      } else {
        torrent.on('done', () => {
          clearTimeout(timeoutId);
          resolve();
        });
      }
    });
  }
  
  /**
   * Ruft JSON-Daten über BitTorrent ab
   * @param {string} infoHash - InfoHash des Torrents
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Object>} Abgerufene JSON-Daten
   */
  async retrieveJson(infoHash, options = {}) {
    try {
      logger.info(`Rufe JSON-Daten mit InfoHash ${infoHash} über BitTorrent ab`);
      
      // Hole die Datei
      const data = await this.retrieveFile(infoHash, options);
      
      // Konvertiere die Daten in ein JSON-Objekt
      const jsonData = JSON.parse(data.toString());
      
      logger.debug(`JSON-Daten mit InfoHash ${infoHash} erfolgreich abgerufen`);
      
      return jsonData;
    } catch (error) {
      logger.error('Fehler beim Abrufen der JSON-Daten über BitTorrent', {
        error: error.message,
        infoHash
      });
      throw error;
    }
  }
  
  /**
   * Prüft, ob ein Torrent existiert
   * @param {string} infoHash - InfoHash des Torrents
   * @param {Object} options - Prüfoptionen
   * @returns {Promise<boolean>} True, wenn der Torrent existiert
   */
  async exists(infoHash, options = {}) {
    try {
      logger.info(`Prüfe, ob Torrent mit InfoHash ${infoHash} existiert`);
      
      // Prüfe, ob der Torrent bereits geladen ist
      if (this.torrents.has(infoHash)) {
        return true;
      }
      
      // Prüfe, ob eine Torrent-Datei existiert
      const torrentPath = path.join(this.options.tempDir, `${infoHash}.torrent`);
      
      try {
        await stat(torrentPath);
        return true;
      } catch (error) {
        // Prüfe, ob Metadaten existieren
        return this.metadataDb.has(infoHash);
      }
    } catch (error) {
      logger.error('Fehler beim Prüfen der Existenz des Torrents', {
        error: error.message,
        infoHash
      });
      return false;
    }
  }
  
  /**
   * Löscht einen Torrent
   * @param {string} infoHash - InfoHash des Torrents
   * @param {Object} options - Löschoptionen
   * @param {boolean} options.removeFiles - Ob die Dateien gelöscht werden sollen
   * @returns {Promise<boolean>} True, wenn der Torrent erfolgreich gelöscht wurde
   */
  async delete(infoHash, options = {}) {
    try {
      logger.info(`Lösche Torrent mit InfoHash ${infoHash}`);
      
      // Prüfe, ob der Torrent geladen ist
      const torrent = this.torrents.get(infoHash);
      
      if (torrent) {
        // Zerstöre den Torrent
        await new Promise(resolve => {
          torrent.destroy({ destroyStore: options.removeFiles }, () => {
            resolve();
          });
        });
        
        this.torrents.delete(infoHash);
      }
      
      // Lösche die Torrent-Datei
      const torrentPath = path.join(this.options.tempDir, `${infoHash}.torrent`);
      
      try {
        await unlink(torrentPath);
      } catch (error) {
        logger.warn(`Torrent-Datei für ${infoHash} konnte nicht gelöscht werden`);
      }
      
      // Lösche die Metadaten
      this.metadataDb.delete(infoHash);
      await this.saveMetadata();
      
      logger.debug(`Torrent mit InfoHash ${infoHash} erfolgreich gelöscht`);
      
      return true;
    } catch (error) {
      logger.error('Fehler beim Löschen des Torrents', {
        error: error.message,
        infoHash
      });
      return false;
    }
  }
  
  /**
   * Generiert eine URL für den Zugriff auf die Datei
   * @param {string} infoHash - InfoHash des Torrents
   * @param {Object} options - URL-Optionen
   * @returns {Promise<string>} Magnet-URI für den Zugriff auf die Datei
   */
  async getAccessUrl(infoHash, options = {}) {
    try {
      logger.info(`Generiere Zugriffs-URL für Torrent mit InfoHash ${infoHash}`);
      
      // Prüfe, ob der Torrent geladen ist
      const torrent = this.torrents.get(infoHash);
      
      if (torrent) {
        return torrent.magnetURI;
      }
      
      // Prüfe, ob Metadaten existieren
      const metadata = this.metadataDb.get(infoHash);
      
      if (metadata) {
        // Erstelle einen Magnet-Link
        const trackers = this.options.trackers.map(tracker => `&tr=${encodeURIComponent(tracker)}`).join('');
        const magnetUri = `magnet:?xt=urn:btih:${infoHash}&dn=${encodeURIComponent(metadata.name)}${trackers}`;
        
        return magnetUri;
      }
      
      throw new Error(`Torrent mit InfoHash ${infoHash} nicht gefunden`);
    } catch (error) {
      logger.error('Fehler beim Generieren der Zugriffs-URL', {
        error: error.message,
        infoHash
      });
      throw error;
    }
  }
}/**
 * @fileoverview BitTorrent-Adapter für dezentrale Speicherung großer Dateien
 * 
 * Dieser Adapter implementiert das StorageAdapter-Interface für BitTorrent.
 * Er wird hauptsächlich für die Speicherung und Verteilung großer Dateien wie
 * Publikationen, Patente und Forschungsdatensätze verwendet.
 */

import fs from 'fs';
import path from 'path';
import crypto from 'crypto';
import { promisify } from 'util';
import WebTorrent from 'webtorrent';
import { StorageAdapter } from './StorageAdapter.js';
import { logger } from '../../utils/logger.js';

// Promisify fs-Funktionen
const mkdir = promisify(fs.mkdir);
const writeFile = promisify(fs.writeFile);
const readFile = promisify(fs.readFile);
const stat = promisify(fs.stat);
const unlink = promisify(fs.unlink);

/**
 * BitTorrent-Adapter für dezentrale Speicherung großer Dateien
 */
export class BitTorrentAdapter extends StorageAdapter {
  /**
   * Erstellt eine neue Instanz des BitTorrentAdapter
   * @param {Object} options - Konfigurationsoptionen
   * @param {string} options.trackers - Liste der Tracker-URLs
   * @param {string} options.seedTime - Zeit in Millisekunden, für die Dateien geseedet werden sollen
   * @param {string} options.tempDir - Temporäres Verzeichnis für Dateien
   * @param {boolean} options.persistentSeeding - Ob Dateien dauerhaft geseedet werden sollen
   * @param {Array<string>} options.webSeeds - Liste der WebSeed-URLs
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      trackers: options.trackers || [
        'udp://tracker.opentrackr.org:1337/announce',
        'udp://tracker.openbittorrent.com:6969/announce',
        'udp://open.stealth.si:80/announce',
        'udp://tracker.torrent.eu.org:451/announce',
        'udp://tracker.moeking.me:6969/announce'
      ],
      seedTime: options.seedTime || 24 * 60 * 60 * 1000, // 24 Stunden
      tempDir: options.tempDir || path.join(process.cwd(), 'temp', 'torrents'),
      persistentSeeding: options.persistentSeeding !== undefined ? options.persistentSeeding : false,
      webSeeds: options.webSeeds || [],
      ...options
    };
    
    this.client = null;
    this.torrents = new Map(); // Speichert aktive Torrents
    this.metadataDb = new Map(); // Speichert Metadaten zu Torrents
    
    logger.info('BitTorrentAdapter initialisiert');
  }
  
  /**
   * Initialisiert den BitTorrent-Client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      logger.info('Initialisiere BitTorrent-Client');
      
      // Erstelle das temporäre Verzeichnis, falls es nicht existiert
      await mkdir(this.options.tempDir, { recursive: true });
      
      // Initialisiere den WebTorrent-Client
      this.client = new WebTorrent();
      
      // Lade gespeicherte Metadaten
      await this.loadMetadata();
      
      // Seed gespeicherte Torrents, falls persistentSeeding aktiviert ist
      if (this.options.persistentSeeding) {
        await this.seedStoredTorrents();
      }
      
      logger.info('BitTorrent-Client erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des BitTorrent-Clients', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Lädt gespeicherte Metadaten
   * @returns {Promise<void>}
   */
  async loadMetadata() {
    try {
      const metadataPath = path.join(this.options.tempDir, 'metadata.json');
      
      // Prüfe, ob die Metadatendatei existiert
      try {
        await stat(metadataPath);
      } catch (error) {
        // Erstelle eine leere Metadatendatei, falls sie nicht existiert
        await writeFile(metadataPath, JSON.stringify({}));
        return;
      }
      
      // Lade die Metadaten
      const metadataJson = await readFile(metadataPath, 'utf8');
      const metadata = JSON.parse(metadataJson);
      
      // Fülle die Metadaten-Map
      for (const [infoHash, torrentData] of Object.entries(metadata)) {
        this.metadataDb.set(infoHash, torrentData);
      }
      
      logger.debug(`${this.metadataDb.size} Torrent-Metadaten geladen`);
    } catch (error) {
      logger.error('Fehler beim Laden der Torrent-Metadaten', {
        error: error.message
      });
      // Erstelle eine leere Metadaten-Map im Fehlerfall
      this.metadataDb = new Map();
    }
  }
  
  /**
   * Speichert Metadaten
   * @returns {Promise<void>}
   */
  async saveMetadata() {
    try {
      const metadataPath = path.join(this.options.tempDir, 'metadata.json');
      
      // Konvertiere die Map in ein Objekt
      const metadata = {};
      for (const [infoHash, torrentData] of this.metadataDb.entries()) {
        metadata[infoHash] = torrentData;
      }
      
      // Speichere die Metadaten
      await writeFile(metadataPath, JSON.stringify(metadata, null, 2));
      
      logger.debug(`${this.metadataDb.size} Torrent-Metadaten gespeichert`);
    } catch (error) {
      logger.error('Fehler beim Speichern der Torrent-Metadaten', {
        error: error.message
      });
    }
  }
  
  /**
   * Seed gespeicherte Torrents
   * @returns {Promise<void>}
   */
  async seedStoredTorrents() {
    try {
      logger.info('Starte Seeding gespeicherter Torrents');
      
      const torrentsToSeed = [];
      
      // Sammle alle Torrent-Dateien
      for (const [infoHash, metadata] of this.metadataDb.entries()) {
        const torrentPath = path.join(this.options.tempDir, `${infoHash}.torrent`);
        
        try {
          // Prüfe, ob die Torrent-Datei existiert
          await stat(torrentPath);
          torrentsToSeed.push({ path: torrentPath, metadata });
        } catch (error) {
          logger.warn(`Torrent-Datei für ${infoHash} nicht gefunden`);
        }
      }
      
      // Seed die Torrents
      for (const { path: torrentPath, metadata } of torrentsToSeed) {
        try {
          const torrent = await this.seedTorrentFile(torrentPath);
          this.torrents.set(torrent.infoHash, torrent);
          logger.debug(`Torrent ${torrent.infoHash} wird geseedet`);
        } catch (error) {
          logger.error(`Fehler beim Seeden von Torrent ${metadata.name}`, {
            error: error.message
          });
        }
      }
      
      logger.info(`${this.torrents.size} Torrents werden geseedet`);
    } catch (error) {
      logger.error('Fehler beim Seeden gespeicherter Torrents', {
        error: error.message
      });
    }
  }
  
  /**
   * Seed eine Torrent-Datei
   * @param {string} torrentPath - Pfad zur Torrent-Datei
   * @returns {Promise<Object>} Torrent-Objekt
   */
  async seedTorrentFile(torrentPath) {
    return new Promise((resolve, reject) => {
      this.client.add(torrentPath, { path: this.options.tempDir }, torrent => {
        resolve(torrent);
      }).on('error', error => {
        reject(error);
      });
    });
  }
  
  /**
   * Speichert eine Datei über BitTorrent
   * @param {Buffer|string} data - Zu speichernde Daten
   * @param {Object} options - Speicheroptionen
   * @param {string} options.filename - Dateiname
   * @param {boolean} options.seed - Ob die Datei geseedet werden soll
   * @param {Array<string>} options.trackers - Zusätzliche Tracker
   * @param {Array<string>} options.webSeeds - Zusätzliche WebSeeds
   * @param {Object} options.metadata - Zusätzliche Metadaten
   * @returns {Promise<Object>} BitTorrent-Speicherinformationen
   */
  async storeFile(data, options = {}) {
    try {
      logger.info('Speichere Datei über BitTorrent');
      
      // Generiere einen eindeutigen Dateinamen, falls keiner angegeben ist
      const filename = options.filename || `file-${crypto.randomBytes(8).toString('hex')}`;
      const filePath = path.join(this.options.tempDir, filename);
      
      // Schreibe die Datei auf die Festplatte
      await writeFile(filePath, data);
      
      // Erstelle ein Torrent
      const torrent = await this.createTorrent(filePath, {
        name: filename,
        comment: options.metadata?.description || 'DeSci-Scholar File',
        createdBy: 'DeSci-Scholar BitTorrentAdapter',
        private: false,
        announceList: [
          ...this.options.trackers,
          ...(options.trackers || [])
        ],
        urlList: [
          ...this.options.webSeeds,
          ...(options.webSeeds || [])
        ]
      });
      
      // Speichere die Torrent-Datei
      const torrentPath = path.join(this.options.tempDir, `${torrent.infoHash}.torrent`);
      await writeFile(torrentPath, torrent.torrentFile);
      
      // Speichere Metadaten
      const metadata = {
        name: filename,
        size: torrent.length,
        files: torrent.files.map(file => ({
          name: file.name,
          path: file.path,
          length: file.length
        })),
        createdAt: new Date().toISOString(),
        originalFilename: options.filename,
        contentType: options.metadata?.contentType,
        description: options.metadata?.description,
        customMetadata: options.metadata?.custom
      };
      
      this.metadataDb.set(torrent.infoHash, metadata);
      await this.saveMetadata();
      
      // Seed die Datei für die angegebene Zeit, falls gewünscht
      if (options.seed !== false) {
        setTimeout(() => {
          if (!this.options.persistentSeeding) {
            torrent.destroy();
            this.torrents.delete(torrent.infoHash);
            logger.debug(`Seeding für Torrent ${torrent.infoHash} beendet`);
          }
        }, options.seedTime || this.options.seedTime);
      } else {
        torrent.destroy();
        this.torrents.delete(torrent.infoHash);
      }
      
      logger.debug(`Datei über BitTorrent gespeichert mit InfoHash ${torrent.infoHash}`);
      
      // Lösche die temporäre Datei, falls sie nicht geseedet wird
      if (options.seed === false) {
        await unlink(filePath);
      }
      
      return {
        infoHash: torrent.infoHash,
        magnetUri: torrent.magnetURI,
        torrentFile: torrent.torrentFile,
        size: torrent.length,
        files: torrent.files.map(file => ({
          name: file.name,
          path: file.path,
          length: file.length
        })),
        trackers: torrent.announce,
        webSeeds: torrent.urlList
      };
    } catch (error) {
      logger.error('Fehler beim Speichern der Datei über BitTorrent', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Erstellt ein Torrent für eine Datei
   * @param {string} filePath - Pfad zur Datei
   * @param {Object} options - Torrent-Optionen
   * @returns {Promise<Object>} Torrent-Objekt
   */
  async createTorrent(filePath, options = {}) {
    return new Promise((resolve, reject) => {
      this.client.seed(filePath, options, torrent => {
        this.torrents.set(torrent.infoHash, torrent);
        resolve(torrent);
      }).on('error', error => {
        reject(error);
      });
    });
  }
  
  /**
   * Speichert JSON-Daten über BitTorrent
   * @param {Object} data - Zu speichernde JSON-Daten
   * @param {Object} options - Speicheroptionen
   * @returns {Promise<Object>} BitTorrent-Speicherinformationen
   */
  async storeJson(data, options = {}) {
    try {
      logger.info('Speichere JSON-Daten über BitTorrent');
      
      // Konvertiere die JSON-Daten in einen String
      const jsonString = JSON.stringify(data);
      
      // Speichere die JSON-Daten als Datei
      return await this.storeFile(Buffer.from(jsonString), {
        filename: options.filename || 'data.json',
        seed: options.seed,
        trackers: options.trackers,
        webSeeds: options.webSeeds,
        metadata: {
          ...options.metadata,
          contentType: 'application/json'
        }
      });
    } catch (error) {
      logger.error('Fehler beim Speichern der JSON-Daten über BitTorrent', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Datei über BitTorrent ab
   * @param {string} infoHash - InfoHash des Torrents
   * @param {Object} options - Abrufoptionen
   * @param {number} options.timeout - Timeout in Millisekunden
   * @param {boolean} options.stream - Ob die Datei als Stream zurückgegeben werden soll
   * @returns {Promise<Buffer|stream.Readable>} Abgerufene Daten oder Stream
   */
  async retrieveFile(infoHash, options = {}) {
    try {
      logger.info(`Rufe Datei mit InfoHash ${infoHash} über BitTorrent ab`);
      
      // Prüfe, ob der Torrent bereits geladen ist
      let torrent = this.torrents.get(infoHash);
      
      if (!torrent) {
        // Prüfe, ob eine Torrent-Datei existiert
        const torrentPath = path.join(this.options.tempDir, `${infoHash}.torrent`);
        
        try {
          await stat(torrentPath);
          torrent = await this.addTorrent(torrentPath);
        } catch (error) {
          // Versuche, den Torrent über den Magnet-Link hinzuzufügen
          const magnetUri = `magnet:?xt=urn:btih:${infoHash}`;
          torrent = await this.addTorrent(magnetUri);
        }
      }
      
      // Warte, bis der Torrent vollständig heruntergeladen ist
      if (!torrent.done) {
        await this.waitForTorrent(torrent, options.timeout);
      }
      
      // Hole die Hauptdatei des Torrents
      const file = torrent.files[0];
      
      if (options.stream) {
        // Gib einen Stream zurück
        return file.createReadStream();
      } else {
        // Lese die Datei in einen Buffer
        return new Promise((resolve, reject) => {
          const chunks = [];
          const stream = file.createReadStream();
          
          stream.on('data', chunk => {
            chunks.push(chunk);
          });
          
          stream.on('end', () => {
            resolve(Buffer.concat(chunks));
          });
          
          stream.on('error', error => {
            reject(error);
          });
        });
      }
    } catch (error) {
      logger.error('Fehler beim Abrufen der Datei über BitTorrent', {
        error: error.message,
        infoHash
      });
      throw error;
    }
  }
  
  /**
   * Fügt einen Torrent zum Client hinzu
   * @param {string} torrentId - Torrent-ID (Pfad, Magnet-URI, InfoHash)
   * @returns {Promise<Object>} Torrent-Objekt
   */
  async addTorrent(torrentId) {
    return new Promise((resolve, reject) => {
      this.client.add(torrentId, { path: this.options.tempDir }, torrent => {
        this.torrents.set(torrent.infoHash, torrent);
        resolve(torrent);
      }).on('error', error => {
        reject(error);
      });
    });
  }
  
  /**
   * Wartet, bis ein Torrent vollständig heruntergeladen ist
   * @param {Object} torrent - Torrent-Objekt
   * @param {number} timeout - Timeout in Millisekunden
   * @returns {Promise<void>}
   */
  async waitForTorrent(torrent, timeout = 60000) {
    return new Promise((resolve, reject) => {
      // Setze einen Timeout
      const timeoutId = setTimeout(() => {
        reject(new Error(`Timeout beim Herunterladen des Torrents ${torrent.infoHash}`));
      }, timeout);
      
      // Warte auf das 'done'-Ereignis
      if (torrent.done) {
        clearTimeout(timeoutId);
        resolve();
      } else {
        torrent.on('done', () => {
          clearTimeout(timeoutId);
          resolve();
        });
      }
    });
  }
  
  /**
   * Ruft JSON-Daten über BitTorrent ab
   * @param {string} infoHash - InfoHash des Torrents
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Object>} Abgerufene JSON-Daten
   */
  async retrieveJson(infoHash, options = {}) {
    try {
      logger.info(`Rufe JSON-Daten mit InfoHash ${infoHash} über BitTorrent ab`);
      
      // Hole die Datei
      const data = await this.retrieveFile(infoHash, options);
      
      // Konvertiere die Daten in ein JSON-Objekt
      const jsonData = JSON.parse(data.toString());
      
      logger.debug(`JSON-Daten mit InfoHash ${infoHash} erfolgreich abgerufen`);
      
      return jsonData;
    } catch (error) {
      logger.error('Fehler beim Abrufen der JSON-Daten über BitTorrent', {
        error: error.message,
        infoHash
      });
      throw error;
    }
  }
  
  /**
   * Prüft, ob ein Torrent existiert
   * @param {string} infoHash - InfoHash des Torrents
   * @param {Object} options - Prüfoptionen
   * @returns {Promise<boolean>} True, wenn der Torrent existiert
   */
  async exists(infoHash, options = {}) {
    try {
      logger.info(`Prüfe, ob Torrent mit InfoHash ${infoHash} existiert`);
      
      // Prüfe, ob der Torrent bereits geladen ist
      if (this.torrents.has(infoHash)) {
        return true;
      }
      
      // Prüfe, ob eine Torrent-Datei existiert
      const torrentPath = path.join(this.options.tempDir, `${infoHash}.torrent`);
      
      try {
        await stat(torrentPath);
        return true;
      } catch (error) {
        // Prüfe, ob Metadaten existieren
        return this.metadataDb.has(infoHash);
      }
    } catch (error) {
      logger.error('Fehler beim Prüfen der Existenz des Torrents', {
        error: error.message,
        infoHash
      });
      return false;
    }
  }
  
  /**
   * Löscht einen Torrent
   * @param {string} infoHash - InfoHash des Torrents
   * @param {Object} options - Löschoptionen
   * @param {boolean} options.removeFiles - Ob die Dateien gelöscht werden sollen
   * @returns {Promise<boolean>} True, wenn der Torrent erfolgreich gelöscht wurde
   */
  async delete(infoHash, options = {}) {
    try {
      logger.info(`Lösche Torrent mit InfoHash ${infoHash}`);
      
      // Prüfe, ob der Torrent geladen ist
      const torrent = this.torrents.get(infoHash);
      
      if (torrent) {
        // Zerstöre den Torrent
        await new Promise(resolve => {
          torrent.destroy({ destroyStore: options.removeFiles }, () => {
            resolve();
          });
        });
        
        this.torrents.delete(infoHash);
      }
      
      // Lösche die Torrent-Datei
      const torrentPath = path.join(this.options.tempDir, `${infoHash}.torrent`);
      
      try {
        await unlink(torrentPath);
      } catch (error) {
        logger.warn(`Torrent-Datei für ${infoHash} konnte nicht gelöscht werden`);
      }
      
      // Lösche die Metadaten
      this.metadataDb.delete(infoHash);
      await this.saveMetadata();
      
      logger.debug(`Torrent mit InfoHash ${infoHash} erfolgreich gelöscht`);
      
      return true;
    } catch (error) {
      logger.error('Fehler beim Löschen des Torrents', {
        error: error.message,
        infoHash
      });
      return false;
    }
  }
  
  /**
   * Generiert eine URL für den Zugriff auf die Datei
   * @param {string} infoHash - InfoHash des Torrents
   * @param {Object} options - URL-Optionen
   * @returns {Promise<string>} Magnet-URI für den Zugriff auf die Datei
   */
  async getAccessUrl(infoHash, options = {}) {
    try {
      logger.info(`Generiere Zugriffs-URL für Torrent mit InfoHash ${infoHash}`);
      
      // Prüfe, ob der Torrent geladen ist
      const torrent = this.torrents.get(infoHash);
      
      if (torrent) {
        return torrent.magnetURI;
      }
      
      // Prüfe, ob Metadaten existieren
      const metadata = this.metadataDb.get(infoHash);
      
      if (metadata) {
        // Erstelle einen Magnet-Link
        const trackers = this.options.trackers.map(tracker => `&tr=${encodeURIComponent(tracker)}`).join('');
        const magnetUri = `magnet:?xt=urn:btih:${infoHash}&dn=${encodeURIComponent(metadata.name)}${trackers}`;
        
        return magnetUri;
      }
      
      throw new Error(`Torrent mit InfoHash ${infoHash} nicht gefunden`);
    } catch (error) {
      logger.error('Fehler beim Generieren der Zugriffs-URL', {
        error: error.message,
        infoHash
      });
      throw error;
    }
  }
}