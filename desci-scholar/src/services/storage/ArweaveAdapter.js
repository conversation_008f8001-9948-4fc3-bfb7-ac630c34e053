/**
 * @fileoverview Arweave-Adapter für permanente dezentrale Speicherung
 * 
 * Dieser Adapter implementiert das StorageAdapter-Interface für Arweave.
 * Er wird hauptsächlich für die langfristige Speicherung wichtiger Daten verwendet,
 * die dauerhaft erhalten bleiben müssen.
 */

import Arweave from 'arweave';
import { StorageAdapter } from './StorageAdapter.js';
import { logger } from '../../utils/logger.js';

/**
 * Arweave-Adapter für permanente dezentrale Speicherung
 */
export class ArweaveAdapter extends StorageAdapter {
  /**
   * Erstellt eine neue Instanz des ArweaveAdapter
   * @param {Object} options - Konfigurationsoptionen
   * @param {string} options.host - Arweave-Host (z.B. 'arweave.net')
   * @param {number} options.port - Arweave-Port
   * @param {string} options.protocol - Arweave-Protokoll (http oder https)
   * @param {Object} options.wallet - Arweave-Wallet (JWK)
   * @param {string} options.walletFile - Pfad zur Wallet-Datei
   * @param {Object} options.bundlr - Bundlr-Konfiguration für effizientere Uploads
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      host: options.host || process.env.ARWEAVE_HOST || 'arweave.net',
      port: options.port || process.env.ARWEAVE_PORT || 443,
      protocol: options.protocol || process.env.ARWEAVE_PROTOCOL || 'https',
      wallet: options.wallet || null,
      walletFile: options.walletFile || process.env.ARWEAVE_WALLET_FILE,
      bundlr: options.bundlr || {
        enabled: process.env.BUNDLR_ENABLED === 'true',
        node: process.env.BUNDLR_NODE || 'https://node1.bundlr.network',
        currency: process.env.BUNDLR_CURRENCY || 'arweave'
      },
      ...options
    };
    
    this.arweave = null;
    this.wallet = null;
    this.walletAddress = null;
    this.bundlr = null;
    
    logger.info('ArweaveAdapter initialisiert');
  }
  
  /**
   * Initialisiert den Arweave-Client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      logger.info('Initialisiere Arweave-Client');
      
      // Initialisiere den Arweave-Client
      this.arweave = Arweave.init({
        host: this.options.host,
        port: this.options.port,
        protocol: this.options.protocol
      });
      
      // Lade das Wallet
      await this.loadWallet();
      
      // Initialisiere Bundlr, falls aktiviert
      if (this.options.bundlr.enabled) {
        await this.initializeBundlr();
      }
      
      logger.info('Arweave-Client erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des Arweave-Clients', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Lädt das Arweave-Wallet
   * @returns {Promise<void>}
   */
  async loadWallet() {
    try {
      logger.info('Lade Arweave-Wallet');
      
      // Verwende das übergebene Wallet, falls vorhanden
      if (this.options.wallet) {
        this.wallet = this.options.wallet;
      } 
      // Lade das Wallet aus der Datei, falls angegeben
      else if (this.options.walletFile) {
        const fs = await import('fs');
        const walletData = await fs.promises.readFile(this.options.walletFile, 'utf8');
        this.wallet = JSON.parse(walletData);
      } 
      // Erstelle ein neues Wallet, falls kein Wallet angegeben ist
      else {
        logger.warn('Kein Wallet angegeben, erstelle ein neues Wallet (nur für Tests)');
        this.wallet = await this.arweave.wallets.generate();
      }
      
      // Hole die Wallet-Adresse
      this.walletAddress = await this.arweave.wallets.jwkToAddress(this.wallet);
      
      // Hole den Kontostand
      const balance = await this.arweave.wallets.getBalance(this.walletAddress);
      const arBalance = this.arweave.ar.winstonToAr(balance);
      
      logger.debug(`Wallet-Adresse: ${this.walletAddress}`);
      logger.debug(`Wallet-Kontostand: ${arBalance} AR`);
      
      logger.info('Arweave-Wallet erfolgreich geladen');
    } catch (error) {
      logger.error('Fehler beim Laden des Arweave-Wallets', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Initialisiert Bundlr für effizientere Uploads
   * @returns {Promise<void>}
   */
  async initializeBundlr() {
    try {
      logger.info('Initialisiere Bundlr');
      
      // Importiere Bundlr
      const { default: Bundlr } = await import('@bundlr-network/client');
      
      // Initialisiere Bundlr
      this.bundlr = new Bundlr(
        this.options.bundlr.node,
        this.options.bundlr.currency,
        this.wallet
      );
      
      // Prüfe die Verbindung
      await this.bundlr.ready();
      
      // Hole den Kontostand
      const balance = await this.bundlr.getLoadedBalance();
      
      logger.debug(`Bundlr-Kontostand: ${balance.toString()} Winston`);
      
      logger.info('Bundlr erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung von Bundlr', {
        error: error.message
      });
      // Deaktiviere Bundlr im Fehlerfall
      this.options.bundlr.enabled = false;
      logger.warn('Bundlr deaktiviert, verwende Standard-Arweave-Uploads');
    }
  }
  
  /**
   * Speichert eine Datei auf Arweave
   * @param {Buffer|string} data - Zu speichernde Daten
   * @param {Object} options - Speicheroptionen
   * @param {string} options.contentType - MIME-Typ der Datei
   * @param {Object} options.tags - Zusätzliche Tags für die Transaktion
   * @returns {Promise<Object>} Arweave-Speicherinformationen
   */
  async storeFile(data, options = {}) {
    try {
      logger.info('Speichere Datei auf Arweave');
      
      // Konvertiere String zu Buffer, falls nötig
      const buffer = Buffer.isBuffer(data) ? data : Buffer.from(data);
      
      // Erstelle die Tags
      const tags = [
        { name: 'Content-Type', value: options.contentType || 'application/octet-stream' },
        { name: 'App-Name', value: 'DeSci-Scholar' },
        { name: 'App-Version', value: '1.0.0' },
        { name: 'Unix-Time', value: Date.now().toString() }
      ];
      
      // Füge benutzerdefinierte Tags hinzu
      if (options.tags && typeof options.tags === 'object') {
        for (const [key, value] of Object.entries(options.tags)) {
          tags.push({ name: key, value: value.toString() });
        }
      }
      
      let txId;
      
      // Verwende Bundlr, falls aktiviert
      if (this.options.bundlr.enabled && this.bundlr) {
        logger.debug('Verwende Bundlr für den Upload');
        
        // Erstelle die Transaktion
        const tx = await this.bundlr.upload(buffer, {
          tags
        });
        
        txId = tx.id;
      } else {
        logger.debug('Verwende Standard-Arweave für den Upload');
        
        // Erstelle die Transaktion
        const tx = await this.arweave.createTransaction({
          data: buffer
        }, this.wallet);
        
        // Füge die Tags hinzu
        for (const tag of tags) {
          tx.addTag(tag.name, tag.value);
        }
        
        // Signiere die Transaktion
        await this.arweave.transactions.sign(tx, this.wallet);
        
        // Sende die Transaktion
        const response = await this.arweave.transactions.post(tx);
        
        if (response.status !== 200 && response.status !== 202) {
          throw new Error(`Fehler beim Senden der Transaktion: ${response.statusText}`);
        }
        
        txId = tx.id;
      }
      
      logger.debug(`Datei auf Arweave gespeichert mit Transaktions-ID ${txId}`);
      
      return {
        id: txId,
        size: buffer.length,
        uri: `ar://${txId}`,
        url: `https://${this.options.host}/${txId}`,
        gateway: `https://arweave.net/${txId}`,
        viewblock: `https://viewblock.io/arweave/tx/${txId}`
      };
    } catch (error) {
      logger.error('Fehler beim Speichern der Datei auf Arweave', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Speichert JSON-Daten auf Arweave
   * @param {Object} data - Zu speichernde JSON-Daten
   * @param {Object} options - Speicheroptionen
   * @returns {Promise<Object>} Arweave-Speicherinformationen
   */
  async storeJson(data, options = {}) {
    try {
      logger.info('Speichere JSON-Daten auf Arweave');
      
      // Konvertiere die JSON-Daten in einen String
      const jsonString = JSON.stringify(data);
      
      // Speichere die JSON-Daten als Datei
      return await this.storeFile(jsonString, {
        contentType: 'application/json',
        ...options
      });
    } catch (error) {
      logger.error('Fehler beim Speichern der JSON-Daten auf Arweave', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Datei von Arweave ab
   * @param {string} id - Transaktions-ID
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Buffer>} Abgerufene Daten
   */
  async retrieveFile(id, options = {}) {
    try {
      logger.info(`Rufe Datei mit ID ${id} von Arweave ab`);
      
      // Hole die Daten von Arweave
      const response = await this.arweave.transactions.getData(id, {
        decode: true
      });
      
      // Konvertiere die Daten in einen Buffer
      const data = Buffer.from(response);
      
      logger.debug(`Datei mit ID ${id} erfolgreich abgerufen`);
      
      return data;
    } catch (error) {
      logger.error('Fehler beim Abrufen der Datei von Arweave', {
        error: error.message,
        id
      });
      throw error;
    }
  }
  
  /**
   * Ruft JSON-Daten von Arweave ab
   * @param {string} id - Transaktions-ID
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Object>} Abgerufene JSON-Daten
   */
  async retrieveJson(id, options = {}) {
    try {
      logger.info(`Rufe JSON-Daten mit ID ${id} von Arweave ab`);
      
      // Hole die Datei von Arweave
      const data = await this.retrieveFile(id, options);
      
      // Konvertiere die Daten in ein JSON-Objekt
      const jsonData = JSON.parse(data.toString());
      
      logger.debug(`JSON-Daten mit ID ${id} erfolgreich abgerufen`);
      
      return jsonData;
    } catch (error) {
      logger.error('Fehler beim Abrufen der JSON-Daten von Arweave', {
        error: error.message,
        id
      });
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Datei auf Arweave existiert
   * @param {string} id - Transaktions-ID
   * @param {Object} options - Prüfoptionen
   * @returns {Promise<boolean>} True, wenn die Datei existiert
   */
  async exists(id, options = {}) {
    try {
      logger.info(`Prüfe, ob Datei mit ID ${id} auf Arweave existiert`);
      
      // Hole den Transaktionsstatus
      const status = await this.arweave.transactions.getStatus(id);
      
      // Prüfe, ob die Transaktion bestätigt wurde
      const exists = status.status === 200 && status.confirmed;
      
      logger.debug(`Datei mit ID ${id} existiert: ${exists}`);
      
      return exists;
    } catch (error) {
      logger.debug(`Datei mit ID ${id} existiert nicht auf Arweave`);
      return false;
    }
  }
  
  /**
   * Löscht eine Datei (nicht möglich bei Arweave)
   * @param {string} id - Transaktions-ID
   * @param {Object} options - Löschoptionen
   * @returns {Promise<boolean>} False, da Löschen nicht möglich ist
   */
  async delete(id, options = {}) {
    logger.warn(`Löschen von Dateien ist bei Arweave nicht möglich (ID: ${id})`);
    return false;
  }
  
  /**
   * Generiert eine URL für den Zugriff auf die Datei
   * @param {string} id - Transaktions-ID
   * @param {Object} options - URL-Optionen
   * @param {string} options.gateway - Zu verwendender Arweave-Gateway
   * @returns {Promise<string>} URL für den Zugriff auf die Datei
   */
  async getAccessUrl(id, options = {}) {
    const gateway = options.gateway || `https://${this.options.host}/`;
    return `${gateway}${id}`;
  }
  
  /**
   * Berechnet die Kosten für die Speicherung einer Datei
   * @param {Buffer|string} data - Zu speichernde Daten
   * @param {Object} options - Kostenberechnungsoptionen
   * @returns {Promise<Object>} Kostendetails
   */
  async calculateStorageCost(data, options = {}) {
    try {
      logger.debug('Berechne Speicherkosten für Arweave');
      
      // Konvertiere String zu Buffer, falls nötig
      const buffer = Buffer.isBuffer(data) ? data : Buffer.from(data);
      
      let winstonCost;
      
      // Verwende Bundlr, falls aktiviert
      if (this.options.bundlr.enabled && this.bundlr) {
        // Berechne die Kosten mit Bundlr
        const price = await this.bundlr.getPrice(buffer.length);
        winstonCost = price.toString();
      } else {
        // Berechne die Kosten mit Standard-Arweave
        winstonCost = await this.arweave.transactions.getPrice(buffer.length);
      }
      
      // Konvertiere Winston zu AR
      const arCost = this.arweave.ar.winstonToAr(winstonCost);
      
      return {
        bytes: buffer.length,
        winstonCost,
        arCost
      };
    } catch (error) {
      logger.error('Fehler bei der Berechnung der Speicherkosten', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Finanziert das Arweave-Wallet
   * @param {string} amount - Zu finanzierende Menge in AR
   * @returns {Promise<Object>} Finanzierungsergebnis
   */
  async fundWallet(amount) {
    try {
      logger.info(`Finanziere Arweave-Wallet mit ${amount} AR`);
      
      // Verwende Bundlr, falls aktiviert
      if (this.options.bundlr.enabled && this.bundlr) {
        // Konvertiere AR zu Winston
        const winston = this.arweave.ar.arToWinston(amount);
        
        // Finanziere das Wallet
        const response = await this.bundlr.fund(winston);
        
        logger.info(`Arweave-Wallet erfolgreich finanziert: ${response.id}`);
        
        return {
          success: true,
          id: response.id,
          amount,
          winston
        };
      } else {
        throw new Error('Wallet-Finanzierung ist nur mit Bundlr möglich');
      }
    } catch (error) {
      logger.error('Fehler bei der Finanzierung des Arweave-Wallets', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft den Kontostand des Arweave-Wallets ab
   * @returns {Promise<Object>} Kontostanddetails
   */
  async getWalletBalance() {
    try {
      logger.debug('Rufe Kontostand des Arweave-Wallets ab');
      
      // Hole den Kontostand
      const winstonBalance = await this.arweave.wallets.getBalance(this.walletAddress);
      const arBalance = this.arweave.ar.winstonToAr(winstonBalance);
      
      // Hole den Bundlr-Kontostand, falls aktiviert
      let bundlrWinstonBalance = '0';
      let bundlrArBalance = '0';
      
      if (this.options.bundlr.enabled && this.bundlr) {
        bundlrWinstonBalance = (await this.bundlr.getLoadedBalance()).toString();
        bundlrArBalance = this.arweave.ar.winstonToAr(bundlrWinstonBalance);
      }
      
      return {
        address: this.walletAddress,
        winstonBalance,
        arBalance,
        bundlr: {
          winstonBalance: bundlrWinstonBalance,
          arBalance: bundlrArBalance
        }
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen des Kontostands', {
        error: error.message
      });
      throw error;
    }
  }
}/**
 * @fileoverview Arweave-Adapter für permanente dezentrale Speicherung
 * 
 * Dieser Adapter implementiert das StorageAdapter-Interface für Arweave.
 * Er wird hauptsächlich für die langfristige Speicherung wichtiger Daten verwendet,
 * die dauerhaft erhalten bleiben müssen.
 */

import Arweave from 'arweave';
import { StorageAdapter } from './StorageAdapter.js';
import { logger } from '../../utils/logger.js';

/**
 * Arweave-Adapter für permanente dezentrale Speicherung
 */
export class ArweaveAdapter extends StorageAdapter {
  /**
   * Erstellt eine neue Instanz des ArweaveAdapter
   * @param {Object} options - Konfigurationsoptionen
   * @param {string} options.host - Arweave-Host (z.B. 'arweave.net')
   * @param {number} options.port - Arweave-Port
   * @param {string} options.protocol - Arweave-Protokoll (http oder https)
   * @param {Object} options.wallet - Arweave-Wallet (JWK)
   * @param {string} options.walletFile - Pfad zur Wallet-Datei
   * @param {Object} options.bundlr - Bundlr-Konfiguration für effizientere Uploads
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      host: options.host || process.env.ARWEAVE_HOST || 'arweave.net',
      port: options.port || process.env.ARWEAVE_PORT || 443,
      protocol: options.protocol || process.env.ARWEAVE_PROTOCOL || 'https',
      wallet: options.wallet || null,
      walletFile: options.walletFile || process.env.ARWEAVE_WALLET_FILE,
      bundlr: options.bundlr || {
        enabled: process.env.BUNDLR_ENABLED === 'true',
        node: process.env.BUNDLR_NODE || 'https://node1.bundlr.network',
        currency: process.env.BUNDLR_CURRENCY || 'arweave'
      },
      ...options
    };
    
    this.arweave = null;
    this.wallet = null;
    this.walletAddress = null;
    this.bundlr = null;
    
    logger.info('ArweaveAdapter initialisiert');
  }
  
  /**
   * Initialisiert den Arweave-Client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      logger.info('Initialisiere Arweave-Client');
      
      // Initialisiere den Arweave-Client
      this.arweave = Arweave.init({
        host: this.options.host,
        port: this.options.port,
        protocol: this.options.protocol
      });
      
      // Lade das Wallet
      await this.loadWallet();
      
      // Initialisiere Bundlr, falls aktiviert
      if (this.options.bundlr.enabled) {
        await this.initializeBundlr();
      }
      
      logger.info('Arweave-Client erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des Arweave-Clients', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Lädt das Arweave-Wallet
   * @returns {Promise<void>}
   */
  async loadWallet() {
    try {
      logger.info('Lade Arweave-Wallet');
      
      // Verwende das übergebene Wallet, falls vorhanden
      if (this.options.wallet) {
        this.wallet = this.options.wallet;
      } 
      // Lade das Wallet aus der Datei, falls angegeben
      else if (this.options.walletFile) {
        const fs = await import('fs');
        const walletData = await fs.promises.readFile(this.options.walletFile, 'utf8');
        this.wallet = JSON.parse(walletData);
      } 
      // Erstelle ein neues Wallet, falls kein Wallet angegeben ist
      else {
        logger.warn('Kein Wallet angegeben, erstelle ein neues Wallet (nur für Tests)');
        this.wallet = await this.arweave.wallets.generate();
      }
      
      // Hole die Wallet-Adresse
      this.walletAddress = await this.arweave.wallets.jwkToAddress(this.wallet);
      
      // Hole den Kontostand
      const balance = await this.arweave.wallets.getBalance(this.walletAddress);
      const arBalance = this.arweave.ar.winstonToAr(balance);
      
      logger.debug(`Wallet-Adresse: ${this.walletAddress}`);
      logger.debug(`Wallet-Kontostand: ${arBalance} AR`);
      
      logger.info('Arweave-Wallet erfolgreich geladen');
    } catch (error) {
      logger.error('Fehler beim Laden des Arweave-Wallets', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Initialisiert Bundlr für effizientere Uploads
   * @returns {Promise<void>}
   */
  async initializeBundlr() {
    try {
      logger.info('Initialisiere Bundlr');
      
      // Importiere Bundlr
      const { default: Bundlr } = await import('@bundlr-network/client');
      
      // Initialisiere Bundlr
      this.bundlr = new Bundlr(
        this.options.bundlr.node,
        this.options.bundlr.currency,
        this.wallet
      );
      
      // Prüfe die Verbindung
      await this.bundlr.ready();
      
      // Hole den Kontostand
      const balance = await this.bundlr.getLoadedBalance();
      
      logger.debug(`Bundlr-Kontostand: ${balance.toString()} Winston`);
      
      logger.info('Bundlr erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung von Bundlr', {
        error: error.message
      });
      // Deaktiviere Bundlr im Fehlerfall
      this.options.bundlr.enabled = false;
      logger.warn('Bundlr deaktiviert, verwende Standard-Arweave-Uploads');
    }
  }
  
  /**
   * Speichert eine Datei auf Arweave
   * @param {Buffer|string} data - Zu speichernde Daten
   * @param {Object} options - Speicheroptionen
   * @param {string} options.contentType - MIME-Typ der Datei
   * @param {Object} options.tags - Zusätzliche Tags für die Transaktion
   * @returns {Promise<Object>} Arweave-Speicherinformationen
   */
  async storeFile(data, options = {}) {
    try {
      logger.info('Speichere Datei auf Arweave');
      
      // Konvertiere String zu Buffer, falls nötig
      const buffer = Buffer.isBuffer(data) ? data : Buffer.from(data);
      
      // Erstelle die Tags
      const tags = [
        { name: 'Content-Type', value: options.contentType || 'application/octet-stream' },
        { name: 'App-Name', value: 'DeSci-Scholar' },
        { name: 'App-Version', value: '1.0.0' },
        { name: 'Unix-Time', value: Date.now().toString() }
      ];
      
      // Füge benutzerdefinierte Tags hinzu
      if (options.tags && typeof options.tags === 'object') {
        for (const [key, value] of Object.entries(options.tags)) {
          tags.push({ name: key, value: value.toString() });
        }
      }
      
      let txId;
      
      // Verwende Bundlr, falls aktiviert
      if (this.options.bundlr.enabled && this.bundlr) {
        logger.debug('Verwende Bundlr für den Upload');
        
        // Erstelle die Transaktion
        const tx = await this.bundlr.upload(buffer, {
          tags
        });
        
        txId = tx.id;
      } else {
        logger.debug('Verwende Standard-Arweave für den Upload');
        
        // Erstelle die Transaktion
        const tx = await this.arweave.createTransaction({
          data: buffer
        }, this.wallet);
        
        // Füge die Tags hinzu
        for (const tag of tags) {
          tx.addTag(tag.name, tag.value);
        }
        
        // Signiere die Transaktion
        await this.arweave.transactions.sign(tx, this.wallet);
        
        // Sende die Transaktion
        const response = await this.arweave.transactions.post(tx);
        
        if (response.status !== 200 && response.status !== 202) {
          throw new Error(`Fehler beim Senden der Transaktion: ${response.statusText}`);
        }
        
        txId = tx.id;
      }
      
      logger.debug(`Datei auf Arweave gespeichert mit Transaktions-ID ${txId}`);
      
      return {
        id: txId,
        size: buffer.length,
        uri: `ar://${txId}`,
        url: `https://${this.options.host}/${txId}`,
        gateway: `https://arweave.net/${txId}`,
        viewblock: `https://viewblock.io/arweave/tx/${txId}`
      };
    } catch (error) {
      logger.error('Fehler beim Speichern der Datei auf Arweave', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Speichert JSON-Daten auf Arweave
   * @param {Object} data - Zu speichernde JSON-Daten
   * @param {Object} options - Speicheroptionen
   * @returns {Promise<Object>} Arweave-Speicherinformationen
   */
  async storeJson(data, options = {}) {
    try {
      logger.info('Speichere JSON-Daten auf Arweave');
      
      // Konvertiere die JSON-Daten in einen String
      const jsonString = JSON.stringify(data);
      
      // Speichere die JSON-Daten als Datei
      return await this.storeFile(jsonString, {
        contentType: 'application/json',
        ...options
      });
    } catch (error) {
      logger.error('Fehler beim Speichern der JSON-Daten auf Arweave', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Datei von Arweave ab
   * @param {string} id - Transaktions-ID
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Buffer>} Abgerufene Daten
   */
  async retrieveFile(id, options = {}) {
    try {
      logger.info(`Rufe Datei mit ID ${id} von Arweave ab`);
      
      // Hole die Daten von Arweave
      const response = await this.arweave.transactions.getData(id, {
        decode: true
      });
      
      // Konvertiere die Daten in einen Buffer
      const data = Buffer.from(response);
      
      logger.debug(`Datei mit ID ${id} erfolgreich abgerufen`);
      
      return data;
    } catch (error) {
      logger.error('Fehler beim Abrufen der Datei von Arweave', {
        error: error.message,
        id
      });
      throw error;
    }
  }
  
  /**
   * Ruft JSON-Daten von Arweave ab
   * @param {string} id - Transaktions-ID
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Object>} Abgerufene JSON-Daten
   */
  async retrieveJson(id, options = {}) {
    try {
      logger.info(`Rufe JSON-Daten mit ID ${id} von Arweave ab`);
      
      // Hole die Datei von Arweave
      const data = await this.retrieveFile(id, options);
      
      // Konvertiere die Daten in ein JSON-Objekt
      const jsonData = JSON.parse(data.toString());
      
      logger.debug(`JSON-Daten mit ID ${id} erfolgreich abgerufen`);
      
      return jsonData;
    } catch (error) {
      logger.error('Fehler beim Abrufen der JSON-Daten von Arweave', {
        error: error.message,
        id
      });
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Datei auf Arweave existiert
   * @param {string} id - Transaktions-ID
   * @param {Object} options - Prüfoptionen
   * @returns {Promise<boolean>} True, wenn die Datei existiert
   */
  async exists(id, options = {}) {
    try {
      logger.info(`Prüfe, ob Datei mit ID ${id} auf Arweave existiert`);
      
      // Hole den Transaktionsstatus
      const status = await this.arweave.transactions.getStatus(id);
      
      // Prüfe, ob die Transaktion bestätigt wurde
      const exists = status.status === 200 && status.confirmed;
      
      logger.debug(`Datei mit ID ${id} existiert: ${exists}`);
      
      return exists;
    } catch (error) {
      logger.debug(`Datei mit ID ${id} existiert nicht auf Arweave`);
      return false;
    }
  }
  
  /**
   * Löscht eine Datei (nicht möglich bei Arweave)
   * @param {string} id - Transaktions-ID
   * @param {Object} options - Löschoptionen
   * @returns {Promise<boolean>} False, da Löschen nicht möglich ist
   */
  async delete(id, options = {}) {
    logger.warn(`Löschen von Dateien ist bei Arweave nicht möglich (ID: ${id})`);
    return false;
  }
  
  /**
   * Generiert eine URL für den Zugriff auf die Datei
   * @param {string} id - Transaktions-ID
   * @param {Object} options - URL-Optionen
   * @param {string} options.gateway - Zu verwendender Arweave-Gateway
   * @returns {Promise<string>} URL für den Zugriff auf die Datei
   */
  async getAccessUrl(id, options = {}) {
    const gateway = options.gateway || `https://${this.options.host}/`;
    return `${gateway}${id}`;
  }
  
  /**
   * Berechnet die Kosten für die Speicherung einer Datei
   * @param {Buffer|string} data - Zu speichernde Daten
   * @param {Object} options - Kostenberechnungsoptionen
   * @returns {Promise<Object>} Kostendetails
   */
  async calculateStorageCost(data, options = {}) {
    try {
      logger.debug('Berechne Speicherkosten für Arweave');
      
      // Konvertiere String zu Buffer, falls nötig
      const buffer = Buffer.isBuffer(data) ? data : Buffer.from(data);
      
      let winstonCost;
      
      // Verwende Bundlr, falls aktiviert
      if (this.options.bundlr.enabled && this.bundlr) {
        // Berechne die Kosten mit Bundlr
        const price = await this.bundlr.getPrice(buffer.length);
        winstonCost = price.toString();
      } else {
        // Berechne die Kosten mit Standard-Arweave
        winstonCost = await this.arweave.transactions.getPrice(buffer.length);
      }
      
      // Konvertiere Winston zu AR
      const arCost = this.arweave.ar.winstonToAr(winstonCost);
      
      return {
        bytes: buffer.length,
        winstonCost,
        arCost
      };
    } catch (error) {
      logger.error('Fehler bei der Berechnung der Speicherkosten', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Finanziert das Arweave-Wallet
   * @param {string} amount - Zu finanzierende Menge in AR
   * @returns {Promise<Object>} Finanzierungsergebnis
   */
  async fundWallet(amount) {
    try {
      logger.info(`Finanziere Arweave-Wallet mit ${amount} AR`);
      
      // Verwende Bundlr, falls aktiviert
      if (this.options.bundlr.enabled && this.bundlr) {
        // Konvertiere AR zu Winston
        const winston = this.arweave.ar.arToWinston(amount);
        
        // Finanziere das Wallet
        const response = await this.bundlr.fund(winston);
        
        logger.info(`Arweave-Wallet erfolgreich finanziert: ${response.id}`);
        
        return {
          success: true,
          id: response.id,
          amount,
          winston
        };
      } else {
        throw new Error('Wallet-Finanzierung ist nur mit Bundlr möglich');
      }
    } catch (error) {
      logger.error('Fehler bei der Finanzierung des Arweave-Wallets', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft den Kontostand des Arweave-Wallets ab
   * @returns {Promise<Object>} Kontostanddetails
   */
  async getWalletBalance() {
    try {
      logger.debug('Rufe Kontostand des Arweave-Wallets ab');
      
      // Hole den Kontostand
      const winstonBalance = await this.arweave.wallets.getBalance(this.walletAddress);
      const arBalance = this.arweave.ar.winstonToAr(winstonBalance);
      
      // Hole den Bundlr-Kontostand, falls aktiviert
      let bundlrWinstonBalance = '0';
      let bundlrArBalance = '0';
      
      if (this.options.bundlr.enabled && this.bundlr) {
        bundlrWinstonBalance = (await this.bundlr.getLoadedBalance()).toString();
        bundlrArBalance = this.arweave.ar.winstonToAr(bundlrWinstonBalance);
      }
      
      return {
        address: this.walletAddress,
        winstonBalance,
        arBalance,
        bundlr: {
          winstonBalance: bundlrWinstonBalance,
          arBalance: bundlrArBalance
        }
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen des Kontostands', {
        error: error.message
      });
      throw error;
    }
  }
}/**
 * @fileoverview Arweave-Adapter für permanente dezentrale Speicherung
 * 
 * Dieser Adapter implementiert das StorageAdapter-Interface für Arweave.
 * Er wird hauptsächlich für die langfristige Speicherung wichtiger Daten verwendet,
 * die dauerhaft erhalten bleiben müssen.
 */

import Arweave from 'arweave';
import { StorageAdapter } from './StorageAdapter.js';
import { logger } from '../../utils/logger.js';

/**
 * Arweave-Adapter für permanente dezentrale Speicherung
 */
export class ArweaveAdapter extends StorageAdapter {
  /**
   * Erstellt eine neue Instanz des ArweaveAdapter
   * @param {Object} options - Konfigurationsoptionen
   * @param {string} options.host - Arweave-Host (z.B. 'arweave.net')
   * @param {number} options.port - Arweave-Port
   * @param {string} options.protocol - Arweave-Protokoll (http oder https)
   * @param {Object} options.wallet - Arweave-Wallet (JWK)
   * @param {string} options.walletFile - Pfad zur Wallet-Datei
   * @param {Object} options.bundlr - Bundlr-Konfiguration für effizientere Uploads
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      host: options.host || process.env.ARWEAVE_HOST || 'arweave.net',
      port: options.port || process.env.ARWEAVE_PORT || 443,
      protocol: options.protocol || process.env.ARWEAVE_PROTOCOL || 'https',
      wallet: options.wallet || null,
      walletFile: options.walletFile || process.env.ARWEAVE_WALLET_FILE,
      bundlr: options.bundlr || {
        enabled: process.env.BUNDLR_ENABLED === 'true',
        node: process.env.BUNDLR_NODE || 'https://node1.bundlr.network',
        currency: process.env.BUNDLR_CURRENCY || 'arweave'
      },
      ...options
    };
    
    this.arweave = null;
    this.wallet = null;
    this.walletAddress = null;
    this.bundlr = null;
    
    logger.info('ArweaveAdapter initialisiert');
  }
  
  /**
   * Initialisiert den Arweave-Client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      logger.info('Initialisiere Arweave-Client');
      
      // Initialisiere den Arweave-Client
      this.arweave = Arweave.init({
        host: this.options.host,
        port: this.options.port,
        protocol: this.options.protocol
      });
      
      // Lade das Wallet
      await this.loadWallet();
      
      // Initialisiere Bundlr, falls aktiviert
      if (this.options.bundlr.enabled) {
        await this.initializeBundlr();
      }
      
      logger.info('Arweave-Client erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des Arweave-Clients', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Lädt das Arweave-Wallet
   * @returns {Promise<void>}
   */
  async loadWallet() {
    try {
      logger.info('Lade Arweave-Wallet');
      
      // Verwende das übergebene Wallet, falls vorhanden
      if (this.options.wallet) {
        this.wallet = this.options.wallet;
      } 
      // Lade das Wallet aus der Datei, falls angegeben
      else if (this.options.walletFile) {
        const fs = await import('fs');
        const walletData = await fs.promises.readFile(this.options.walletFile, 'utf8');
        this.wallet = JSON.parse(walletData);
      } 
      // Erstelle ein neues Wallet, falls kein Wallet angegeben ist
      else {
        logger.warn('Kein Wallet angegeben, erstelle ein neues Wallet (nur für Tests)');
        this.wallet = await this.arweave.wallets.generate();
      }
      
      // Hole die Wallet-Adresse
      this.walletAddress = await this.arweave.wallets.jwkToAddress(this.wallet);
      
      // Hole den Kontostand
      const balance = await this.arweave.wallets.getBalance(this.walletAddress);
      const arBalance = this.arweave.ar.winstonToAr(balance);
      
      logger.debug(`Wallet-Adresse: ${this.walletAddress}`);
      logger.debug(`Wallet-Kontostand: ${arBalance} AR`);
      
      logger.info('Arweave-Wallet erfolgreich geladen');
    } catch (error) {
      logger.error('Fehler beim Laden des Arweave-Wallets', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Initialisiert Bundlr für effizientere Uploads
   * @returns {Promise<void>}
   */
  async initializeBundlr() {
    try {
      logger.info('Initialisiere Bundlr');
      
      // Importiere Bundlr
      const { default: Bundlr } = await import('@bundlr-network/client');
      
      // Initialisiere Bundlr
      this.bundlr = new Bundlr(
        this.options.bundlr.node,
        this.options.bundlr.currency,
        this.wallet
      );
      
      // Prüfe die Verbindung
      await this.bundlr.ready();
      
      // Hole den Kontostand
      const balance = await this.bundlr.getLoadedBalance();
      
      logger.debug(`Bundlr-Kontostand: ${balance.toString()} Winston`);
      
      logger.info('Bundlr erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung von Bundlr', {
        error: error.message
      });
      // Deaktiviere Bundlr im Fehlerfall
      this.options.bundlr.enabled = false;
      logger.warn('Bundlr deaktiviert, verwende Standard-Arweave-Uploads');
    }
  }
  
  /**
   * Speichert eine Datei auf Arweave
   * @param {Buffer|string} data - Zu speichernde Daten
   * @param {Object} options - Speicheroptionen
   * @param {string} options.contentType - MIME-Typ der Datei
   * @param {Object} options.tags - Zusätzliche Tags für die Transaktion
   * @returns {Promise<Object>} Arweave-Speicherinformationen
   */
  async storeFile(data, options = {}) {
    try {
      logger.info('Speichere Datei auf Arweave');
      
      // Konvertiere String zu Buffer, falls nötig
      const buffer = Buffer.isBuffer(data) ? data : Buffer.from(data);
      
      // Erstelle die Tags
      const tags = [
        { name: 'Content-Type', value: options.contentType || 'application/octet-stream' },
        { name: 'App-Name', value: 'DeSci-Scholar' },
        { name: 'App-Version', value: '1.0.0' },
        { name: 'Unix-Time', value: Date.now().toString() }
      ];
      
      // Füge benutzerdefinierte Tags hinzu
      if (options.tags && typeof options.tags === 'object') {
        for (const [key, value] of Object.entries(options.tags)) {
          tags.push({ name: key, value: value.toString() });
        }
      }
      
      let txId;
      
      // Verwende Bundlr, falls aktiviert
      if (this.options.bundlr.enabled && this.bundlr) {
        logger.debug('Verwende Bundlr für den Upload');
        
        // Erstelle die Transaktion
        const tx = await this.bundlr.upload(buffer, {
          tags
        });
        
        txId = tx.id;
      } else {
        logger.debug('Verwende Standard-Arweave für den Upload');
        
        // Erstelle die Transaktion
        const tx = await this.arweave.createTransaction({
          data: buffer
        }, this.wallet);
        
        // Füge die Tags hinzu
        for (const tag of tags) {
          tx.addTag(tag.name, tag.value);
        }
        
        // Signiere die Transaktion
        await this.arweave.transactions.sign(tx, this.wallet);
        
        // Sende die Transaktion
        const response = await this.arweave.transactions.post(tx);
        
        if (response.status !== 200 && response.status !== 202) {
          throw new Error(`Fehler beim Senden der Transaktion: ${response.statusText}`);
        }
        
        txId = tx.id;
      }
      
      logger.debug(`Datei auf Arweave gespeichert mit Transaktions-ID ${txId}`);
      
      return {
        id: txId,
        size: buffer.length,
        uri: `ar://${txId}`,
        url: `https://${this.options.host}/${txId}`,
        gateway: `https://arweave.net/${txId}`,
        viewblock: `https://viewblock.io/arweave/tx/${txId}`
      };
    } catch (error) {
      logger.error('Fehler beim Speichern der Datei auf Arweave', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Speichert JSON-Daten auf Arweave
   * @param {Object} data - Zu speichernde JSON-Daten
   * @param {Object} options - Speicheroptionen
   * @returns {Promise<Object>} Arweave-Speicherinformationen
   */
  async storeJson(data, options = {}) {
    try {
      logger.info('Speichere JSON-Daten auf Arweave');
      
      // Konvertiere die JSON-Daten in einen String
      const jsonString = JSON.stringify(data);
      
      // Speichere die JSON-Daten als Datei
      return await this.storeFile(jsonString, {
        contentType: 'application/json',
        ...options
      });
    } catch (error) {
      logger.error('Fehler beim Speichern der JSON-Daten auf Arweave', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Datei von Arweave ab
   * @param {string} id - Transaktions-ID
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Buffer>} Abgerufene Daten
   */
  async retrieveFile(id, options = {}) {
    try {
      logger.info(`Rufe Datei mit ID ${id} von Arweave ab`);
      
      // Hole die Daten von Arweave
      const response = await this.arweave.transactions.getData(id, {
        decode: true
      });
      
      // Konvertiere die Daten in einen Buffer
      const data = Buffer.from(response);
      
      logger.debug(`Datei mit ID ${id} erfolgreich abgerufen`);
      
      return data;
    } catch (error) {
      logger.error('Fehler beim Abrufen der Datei von Arweave', {
        error: error.message,
        id
      });
      throw error;
    }
  }
  
  /**
   * Ruft JSON-Daten von Arweave ab
   * @param {string} id - Transaktions-ID
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Object>} Abgerufene JSON-Daten
   */
  async retrieveJson(id, options = {}) {
    try {
      logger.info(`Rufe JSON-Daten mit ID ${id} von Arweave ab`);
      
      // Hole die Datei von Arweave
      const data = await this.retrieveFile(id, options);
      
      // Konvertiere die Daten in ein JSON-Objekt
      const jsonData = JSON.parse(data.toString());
      
      logger.debug(`JSON-Daten mit ID ${id} erfolgreich abgerufen`);
      
      return jsonData;
    } catch (error) {
      logger.error('Fehler beim Abrufen der JSON-Daten von Arweave', {
        error: error.message,
        id
      });
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Datei auf Arweave existiert
   * @param {string} id - Transaktions-ID
   * @param {Object} options - Prüfoptionen
   * @returns {Promise<boolean>} True, wenn die Datei existiert
   */
  async exists(id, options = {}) {
    try {
      logger.info(`Prüfe, ob Datei mit ID ${id} auf Arweave existiert`);
      
      // Hole den Transaktionsstatus
      const status = await this.arweave.transactions.getStatus(id);
      
      // Prüfe, ob die Transaktion bestätigt wurde
      const exists = status.status === 200 && status.confirmed;
      
      logger.debug(`Datei mit ID ${id} existiert: ${exists}`);
      
      return exists;
    } catch (error) {
      logger.debug(`Datei mit ID ${id} existiert nicht auf Arweave`);
      return false;
    }
  }
  
  /**
   * Löscht eine Datei (nicht möglich bei Arweave)
   * @param {string} id - Transaktions-ID
   * @param {Object} options - Löschoptionen
   * @returns {Promise<boolean>} False, da Löschen nicht möglich ist
   */
  async delete(id, options = {}) {
    logger.warn(`Löschen von Dateien ist bei Arweave nicht möglich (ID: ${id})`);
    return false;
  }
  
  /**
   * Generiert eine URL für den Zugriff auf die Datei
   * @param {string} id - Transaktions-ID
   * @param {Object} options - URL-Optionen
   * @param {string} options.gateway - Zu verwendender Arweave-Gateway
   * @returns {Promise<string>} URL für den Zugriff auf die Datei
   */
  async getAccessUrl(id, options = {}) {
    const gateway = options.gateway || `https://${this.options.host}/`;
    return `${gateway}${id}`;
  }
  
  /**
   * Berechnet die Kosten für die Speicherung einer Datei
   * @param {Buffer|string} data - Zu speichernde Daten
   * @param {Object} options - Kostenberechnungsoptionen
   * @returns {Promise<Object>} Kostendetails
   */
  async calculateStorageCost(data, options = {}) {
    try {
      logger.debug('Berechne Speicherkosten für Arweave');
      
      // Konvertiere String zu Buffer, falls nötig
      const buffer = Buffer.isBuffer(data) ? data : Buffer.from(data);
      
      let winstonCost;
      
      // Verwende Bundlr, falls aktiviert
      if (this.options.bundlr.enabled && this.bundlr) {
        // Berechne die Kosten mit Bundlr
        const price = await this.bundlr.getPrice(buffer.length);
        winstonCost = price.toString();
      } else {
        // Berechne die Kosten mit Standard-Arweave
        winstonCost = await this.arweave.transactions.getPrice(buffer.length);
      }
      
      // Konvertiere Winston zu AR
      const arCost = this.arweave.ar.winstonToAr(winstonCost);
      
      return {
        bytes: buffer.length,
        winstonCost,
        arCost
      };
    } catch (error) {
      logger.error('Fehler bei der Berechnung der Speicherkosten', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Finanziert das Arweave-Wallet
   * @param {string} amount - Zu finanzierende Menge in AR
   * @returns {Promise<Object>} Finanzierungsergebnis
   */
  async fundWallet(amount) {
    try {
      logger.info(`Finanziere Arweave-Wallet mit ${amount} AR`);
      
      // Verwende Bundlr, falls aktiviert
      if (this.options.bundlr.enabled && this.bundlr) {
        // Konvertiere AR zu Winston
        const winston = this.arweave.ar.arToWinston(amount);
        
        // Finanziere das Wallet
        const response = await this.bundlr.fund(winston);
        
        logger.info(`Arweave-Wallet erfolgreich finanziert: ${response.id}`);
        
        return {
          success: true,
          id: response.id,
          amount,
          winston
        };
      } else {
        throw new Error('Wallet-Finanzierung ist nur mit Bundlr möglich');
      }
    } catch (error) {
      logger.error('Fehler bei der Finanzierung des Arweave-Wallets', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft den Kontostand des Arweave-Wallets ab
   * @returns {Promise<Object>} Kontostanddetails
   */
  async getWalletBalance() {
    try {
      logger.debug('Rufe Kontostand des Arweave-Wallets ab');
      
      // Hole den Kontostand
      const winstonBalance = await this.arweave.wallets.getBalance(this.walletAddress);
      const arBalance = this.arweave.ar.winstonToAr(winstonBalance);
      
      // Hole den Bundlr-Kontostand, falls aktiviert
      let bundlrWinstonBalance = '0';
      let bundlrArBalance = '0';
      
      if (this.options.bundlr.enabled && this.bundlr) {
        bundlrWinstonBalance = (await this.bundlr.getLoadedBalance()).toString();
        bundlrArBalance = this.arweave.ar.winstonToAr(bundlrWinstonBalance);
      }
      
      return {
        address: this.walletAddress,
        winstonBalance,
        arBalance,
        bundlr: {
          winstonBalance: bundlrWinstonBalance,
          arBalance: bundlrArBalance
        }
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen des Kontostands', {
        error: error.message
      });
      throw error;
    }
  }
}/**
 * @fileoverview Arweave-Adapter für permanente dezentrale Speicherung
 * 
 * Dieser Adapter implementiert das StorageAdapter-Interface für Arweave.
 * Er wird hauptsächlich für die langfristige Speicherung wichtiger Daten verwendet,
 * die dauerhaft erhalten bleiben müssen.
 */

import Arweave from 'arweave';
import { StorageAdapter } from './StorageAdapter.js';
import { logger } from '../../utils/logger.js';

/**
 * Arweave-Adapter für permanente dezentrale Speicherung
 */
export class ArweaveAdapter extends StorageAdapter {
  /**
   * Erstellt eine neue Instanz des ArweaveAdapter
   * @param {Object} options - Konfigurationsoptionen
   * @param {string} options.host - Arweave-Host (z.B. 'arweave.net')
   * @param {number} options.port - Arweave-Port
   * @param {string} options.protocol - Arweave-Protokoll (http oder https)
   * @param {Object} options.wallet - Arweave-Wallet (JWK)
   * @param {string} options.walletFile - Pfad zur Wallet-Datei
   * @param {Object} options.bundlr - Bundlr-Konfiguration für effizientere Uploads
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      host: options.host || process.env.ARWEAVE_HOST || 'arweave.net',
      port: options.port || process.env.ARWEAVE_PORT || 443,
      protocol: options.protocol || process.env.ARWEAVE_PROTOCOL || 'https',
      wallet: options.wallet || null,
      walletFile: options.walletFile || process.env.ARWEAVE_WALLET_FILE,
      bundlr: options.bundlr || {
        enabled: process.env.BUNDLR_ENABLED === 'true',
        node: process.env.BUNDLR_NODE || 'https://node1.bundlr.network',
        currency: process.env.BUNDLR_CURRENCY || 'arweave'
      },
      ...options
    };
    
    this.arweave = null;
    this.wallet = null;
    this.walletAddress = null;
    this.bundlr = null;
    
    logger.info('ArweaveAdapter initialisiert');
  }
  
  /**
   * Initialisiert den Arweave-Client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      logger.info('Initialisiere Arweave-Client');
      
      // Initialisiere den Arweave-Client
      this.arweave = Arweave.init({
        host: this.options.host,
        port: this.options.port,
        protocol: this.options.protocol
      });
      
      // Lade das Wallet
      await this.loadWallet();
      
      // Initialisiere Bundlr, falls aktiviert
      if (this.options.bundlr.enabled) {
        await this.initializeBundlr();
      }
      
      logger.info('Arweave-Client erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des Arweave-Clients', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Lädt das Arweave-Wallet
   * @returns {Promise<void>}
   */
  async loadWallet() {
    try {
      logger.info('Lade Arweave-Wallet');
      
      // Verwende das übergebene Wallet, falls vorhanden
      if (this.options.wallet) {
        this.wallet = this.options.wallet;
      } 
      // Lade das Wallet aus der Datei, falls angegeben
      else if (this.options.walletFile) {
        const fs = await import('fs');
        const walletData = await fs.promises.readFile(this.options.walletFile, 'utf8');
        this.wallet = JSON.parse(walletData);
      } 
      // Erstelle ein neues Wallet, falls kein Wallet angegeben ist
      else {
        logger.warn('Kein Wallet angegeben, erstelle ein neues Wallet (nur für Tests)');
        this.wallet = await this.arweave.wallets.generate();
      }
      
      // Hole die Wallet-Adresse
      this.walletAddress = await this.arweave.wallets.jwkToAddress(this.wallet);
      
      // Hole den Kontostand
      const balance = await this.arweave.wallets.getBalance(this.walletAddress);
      const arBalance = this.arweave.ar.winstonToAr(balance);
      
      logger.debug(`Wallet-Adresse: ${this.walletAddress}`);
      logger.debug(`Wallet-Kontostand: ${arBalance} AR`);
      
      logger.info('Arweave-Wallet erfolgreich geladen');
    } catch (error) {
      logger.error('Fehler beim Laden des Arweave-Wallets', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Initialisiert Bundlr für effizientere Uploads
   * @returns {Promise<void>}
   */
  async initializeBundlr() {
    try {
      logger.info('Initialisiere Bundlr');
      
      // Importiere Bundlr
      const { default: Bundlr } = await import('@bundlr-network/client');
      
      // Initialisiere Bundlr
      this.bundlr = new Bundlr(
        this.options.bundlr.node,
        this.options.bundlr.currency,
        this.wallet
      );
      
      // Prüfe die Verbindung
      await this.bundlr.ready();
      
      // Hole den Kontostand
      const balance = await this.bundlr.getLoadedBalance();
      
      logger.debug(`Bundlr-Kontostand: ${balance.toString()} Winston`);
      
      logger.info('Bundlr erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung von Bundlr', {
        error: error.message
      });
      // Deaktiviere Bundlr im Fehlerfall
      this.options.bundlr.enabled = false;
      logger.warn('Bundlr deaktiviert, verwende Standard-Arweave-Uploads');
    }
  }
  
  /**
   * Speichert eine Datei auf Arweave
   * @param {Buffer|string} data - Zu speichernde Daten
   * @param {Object} options - Speicheroptionen
   * @param {string} options.contentType - MIME-Typ der Datei
   * @param {Object} options.tags - Zusätzliche Tags für die Transaktion
   * @returns {Promise<Object>} Arweave-Speicherinformationen
   */
  async storeFile(data, options = {}) {
    try {
      logger.info('Speichere Datei auf Arweave');
      
      // Konvertiere String zu Buffer, falls nötig
      const buffer = Buffer.isBuffer(data) ? data : Buffer.from(data);
      
      // Erstelle die Tags
      const tags = [
        { name: 'Content-Type', value: options.contentType || 'application/octet-stream' },
        { name: 'App-Name', value: 'DeSci-Scholar' },
        { name: 'App-Version', value: '1.0.0' },
        { name: 'Unix-Time', value: Date.now().toString() }
      ];
      
      // Füge benutzerdefinierte Tags hinzu
      if (options.tags && typeof options.tags === 'object') {
        for (const [key, value] of Object.entries(options.tags)) {
          tags.push({ name: key, value: value.toString() });
        }
      }
      
      let txId;
      
      // Verwende Bundlr, falls aktiviert
      if (this.options.bundlr.enabled && this.bundlr) {
        logger.debug('Verwende Bundlr für den Upload');
        
        // Erstelle die Transaktion
        const tx = await this.bundlr.upload(buffer, {
          tags
        });
        
        txId = tx.id;
      } else {
        logger.debug('Verwende Standard-Arweave für den Upload');
        
        // Erstelle die Transaktion
        const tx = await this.arweave.createTransaction({
          data: buffer
        }, this.wallet);
        
        // Füge die Tags hinzu
        for (const tag of tags) {
          tx.addTag(tag.name, tag.value);
        }
        
        // Signiere die Transaktion
        await this.arweave.transactions.sign(tx, this.wallet);
        
        // Sende die Transaktion
        const response = await this.arweave.transactions.post(tx);
        
        if (response.status !== 200 && response.status !== 202) {
          throw new Error(`Fehler beim Senden der Transaktion: ${response.statusText}`);
        }
        
        txId = tx.id;
      }
      
      logger.debug(`Datei auf Arweave gespeichert mit Transaktions-ID ${txId}`);
      
      return {
        id: txId,
        size: buffer.length,
        uri: `ar://${txId}`,
        url: `https://${this.options.host}/${txId}`,
        gateway: `https://arweave.net/${txId}`,
        viewblock: `https://viewblock.io/arweave/tx/${txId}`
      };
    } catch (error) {
      logger.error('Fehler beim Speichern der Datei auf Arweave', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Speichert JSON-Daten auf Arweave
   * @param {Object} data - Zu speichernde JSON-Daten
   * @param {Object} options - Speicheroptionen
   * @returns {Promise<Object>} Arweave-Speicherinformationen
   */
  async storeJson(data, options = {}) {
    try {
      logger.info('Speichere JSON-Daten auf Arweave');
      
      // Konvertiere die JSON-Daten in einen String
      const jsonString = JSON.stringify(data);
      
      // Speichere die JSON-Daten als Datei
      return await this.storeFile(jsonString, {
        contentType: 'application/json',
        ...options
      });
    } catch (error) {
      logger.error('Fehler beim Speichern der JSON-Daten auf Arweave', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Datei von Arweave ab
   * @param {string} id - Transaktions-ID
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Buffer>} Abgerufene Daten
   */
  async retrieveFile(id, options = {}) {
    try {
      logger.info(`Rufe Datei mit ID ${id} von Arweave ab`);
      
      // Hole die Daten von Arweave
      const response = await this.arweave.transactions.getData(id, {
        decode: true
      });
      
      // Konvertiere die Daten in einen Buffer
      const data = Buffer.from(response);
      
      logger.debug(`Datei mit ID ${id} erfolgreich abgerufen`);
      
      return data;
    } catch (error) {
      logger.error('Fehler beim Abrufen der Datei von Arweave', {
        error: error.message,
        id
      });
      throw error;
    }
  }
  
  /**
   * Ruft JSON-Daten von Arweave ab
   * @param {string} id - Transaktions-ID
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Object>} Abgerufene JSON-Daten
   */
  async retrieveJson(id, options = {}) {
    try {
      logger.info(`Rufe JSON-Daten mit ID ${id} von Arweave ab`);
      
      // Hole die Datei von Arweave
      const data = await this.retrieveFile(id, options);
      
      // Konvertiere die Daten in ein JSON-Objekt
      const jsonData = JSON.parse(data.toString());
      
      logger.debug(`JSON-Daten mit ID ${id} erfolgreich abgerufen`);
      
      return jsonData;
    } catch (error) {
      logger.error('Fehler beim Abrufen der JSON-Daten von Arweave', {
        error: error.message,
        id
      });
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Datei auf Arweave existiert
   * @param {string} id - Transaktions-ID
   * @param {Object} options - Prüfoptionen
   * @returns {Promise<boolean>} True, wenn die Datei existiert
   */
  async exists(id, options = {}) {
    try {
      logger.info(`Prüfe, ob Datei mit ID ${id} auf Arweave existiert`);
      
      // Hole den Transaktionsstatus
      const status = await this.arweave.transactions.getStatus(id);
      
      // Prüfe, ob die Transaktion bestätigt wurde
      const exists = status.status === 200 && status.confirmed;
      
      logger.debug(`Datei mit ID ${id} existiert: ${exists}`);
      
      return exists;
    } catch (error) {
      logger.debug(`Datei mit ID ${id} existiert nicht auf Arweave`);
      return false;
    }
  }
  
  /**
   * Löscht eine Datei (nicht möglich bei Arweave)
   * @param {string} id - Transaktions-ID
   * @param {Object} options - Löschoptionen
   * @returns {Promise<boolean>} False, da Löschen nicht möglich ist
   */
  async delete(id, options = {}) {
    logger.warn(`Löschen von Dateien ist bei Arweave nicht möglich (ID: ${id})`);
    return false;
  }
  
  /**
   * Generiert eine URL für den Zugriff auf die Datei
   * @param {string} id - Transaktions-ID
   * @param {Object} options - URL-Optionen
   * @param {string} options.gateway - Zu verwendender Arweave-Gateway
   * @returns {Promise<string>} URL für den Zugriff auf die Datei
   */
  async getAccessUrl(id, options = {}) {
    const gateway = options.gateway || `https://${this.options.host}/`;
    return `${gateway}${id}`;
  }
  
  /**
   * Berechnet die Kosten für die Speicherung einer Datei
   * @param {Buffer|string} data - Zu speichernde Daten
   * @param {Object} options - Kostenberechnungsoptionen
   * @returns {Promise<Object>} Kostendetails
   */
  async calculateStorageCost(data, options = {}) {
    try {
      logger.debug('Berechne Speicherkosten für Arweave');
      
      // Konvertiere String zu Buffer, falls nötig
      const buffer = Buffer.isBuffer(data) ? data : Buffer.from(data);
      
      let winstonCost;
      
      // Verwende Bundlr, falls aktiviert
      if (this.options.bundlr.enabled && this.bundlr) {
        // Berechne die Kosten mit Bundlr
        const price = await this.bundlr.getPrice(buffer.length);
        winstonCost = price.toString();
      } else {
        // Berechne die Kosten mit Standard-Arweave
        winstonCost = await this.arweave.transactions.getPrice(buffer.length);
      }
      
      // Konvertiere Winston zu AR
      const arCost = this.arweave.ar.winstonToAr(winstonCost);
      
      return {
        bytes: buffer.length,
        winstonCost,
        arCost
      };
    } catch (error) {
      logger.error('Fehler bei der Berechnung der Speicherkosten', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Finanziert das Arweave-Wallet
   * @param {string} amount - Zu finanzierende Menge in AR
   * @returns {Promise<Object>} Finanzierungsergebnis
   */
  async fundWallet(amount) {
    try {
      logger.info(`Finanziere Arweave-Wallet mit ${amount} AR`);
      
      // Verwende Bundlr, falls aktiviert
      if (this.options.bundlr.enabled && this.bundlr) {
        // Konvertiere AR zu Winston
        const winston = this.arweave.ar.arToWinston(amount);
        
        // Finanziere das Wallet
        const response = await this.bundlr.fund(winston);
        
        logger.info(`Arweave-Wallet erfolgreich finanziert: ${response.id}`);
        
        return {
          success: true,
          id: response.id,
          amount,
          winston
        };
      } else {
        throw new Error('Wallet-Finanzierung ist nur mit Bundlr möglich');
      }
    } catch (error) {
      logger.error('Fehler bei der Finanzierung des Arweave-Wallets', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft den Kontostand des Arweave-Wallets ab
   * @returns {Promise<Object>} Kontostanddetails
   */
  async getWalletBalance() {
    try {
      logger.debug('Rufe Kontostand des Arweave-Wallets ab');
      
      // Hole den Kontostand
      const winstonBalance = await this.arweave.wallets.getBalance(this.walletAddress);
      const arBalance = this.arweave.ar.winstonToAr(winstonBalance);
      
      // Hole den Bundlr-Kontostand, falls aktiviert
      let bundlrWinstonBalance = '0';
      let bundlrArBalance = '0';
      
      if (this.options.bundlr.enabled && this.bundlr) {
        bundlrWinstonBalance = (await this.bundlr.getLoadedBalance()).toString();
        bundlrArBalance = this.arweave.ar.winstonToAr(bundlrWinstonBalance);
      }
      
      return {
        address: this.walletAddress,
        winstonBalance,
        arBalance,
        bundlr: {
          winstonBalance: bundlrWinstonBalance,
          arBalance: bundlrArBalance
        }
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen des Kontostands', {
        error: error.message
      });
      throw error;
    }
  }
}