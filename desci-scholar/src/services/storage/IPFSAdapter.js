/**
 * @fileoverview IPFS-Adapter für dezentrale Metadatenspeicherung
 * 
 * Dieser Adapter implementiert das StorageAdapter-Interface für IPFS.
 * Er wird hauptsächlich für die Speicherung von Metadaten und kleineren Dateien verwendet.
 */

import { create as createIPFS } from 'ipfs-http-client';
import { StorageAdapter } from './StorageAdapter.js';
import { logger } from '../../utils/logger.js';

/**
 * IPFS-Adapter für dezentrale Metadatenspeicherung
 */
export class IPFSAdapter extends StorageAdapter {
  /**
   * Erstellt eine neue Instanz des IPFSAdapter
   * @param {Object} options - Konfigurationsoptionen
   * @param {string} options.apiUrl - URL des IPFS-API (z.B. 'https://ipfs.infura.io:5001')
   * @param {string} options.apiKey - API-Schlüssel (falls erforderlich)
   * @param {string} options.apiSecret - API-Secret (falls erforderlich)
   * @param {Array<string>} options.pinningServices - Liste der Pinning-Services
   * @param {Object} options.pinningConfig - Konfiguration für Pinning-Services
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      apiUrl: options.apiUrl || process.env.IPFS_API_URL || 'https://ipfs.infura.io:5001',
      apiKey: options.apiKey || process.env.IPFS_API_KEY,
      apiSecret: options.apiSecret || process.env.IPFS_API_SECRET,
      pinningServices: options.pinningServices || ['Pinata', 'Infura', 'Web3.Storage'],
      pinningConfig: options.pinningConfig || {},
      ...options
    };
    
    this.client = null;
    this.pinningClients = {};
    
    logger.info('IPFSAdapter initialisiert');
  }
  
  /**
   * Initialisiert den IPFS-Client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      logger.info('Initialisiere IPFS-Client');
      
      // Konfiguriere den IPFS-Client
      const auth = this.options.apiKey && this.options.apiSecret
        ? 'Basic ' + Buffer.from(this.options.apiKey + ':' + this.options.apiSecret).toString('base64')
        : undefined;
      
      this.client = createIPFS({
        url: this.options.apiUrl,
        headers: {
          authorization: auth
        }
      });
      
      // Initialisiere Pinning-Services
      await this.initializePinningServices();
      
      logger.info('IPFS-Client erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des IPFS-Clients', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Initialisiert die Pinning-Services
   * @returns {Promise<void>}
   */
  async initializePinningServices() {
    try {
      for (const service of this.options.pinningServices) {
        const config = this.options.pinningConfig[service] || {};
        
        switch (service) {
          case 'Pinata':
            // Pinata-Client initialisieren
            if (config.apiKey && config.apiSecret) {
              const PinataSDK = await import('pinata-sdk');
              this.pinningClients.Pinata = PinataSDK.default(config.apiKey, config.apiSecret);
              logger.debug('Pinata-Client initialisiert');
            }
            break;
            
          case 'Web3.Storage':
            // Web3.Storage-Client initialisieren
            if (config.token) {
              const { Web3Storage } = await import('web3.storage');
              this.pinningClients.Web3Storage = new Web3Storage({ token: config.token });
              logger.debug('Web3.Storage-Client initialisiert');
            }
            break;
            
          case 'Infura':
            // Infura ist bereits durch den Haupt-IPFS-Client abgedeckt
            logger.debug('Infura-Client bereits initialisiert');
            break;
            
          default:
            logger.warn(`Unbekannter Pinning-Service: ${service}`);
        }
      }
    } catch (error) {
      logger.error('Fehler bei der Initialisierung der Pinning-Services', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Speichert eine Datei auf IPFS
   * @param {Buffer|string} data - Zu speichernde Daten
   * @param {Object} options - Speicheroptionen
   * @param {string} options.filename - Dateiname
   * @param {boolean} options.pin - Ob die Datei gepinnt werden soll
   * @param {Array<string>} options.pinningServices - Zu verwendende Pinning-Services
   * @returns {Promise<Object>} IPFS-Speicherinformationen
   */
  async storeFile(data, options = {}) {
    try {
      logger.info('Speichere Datei auf IPFS');
      
      // Erstelle ein File-Objekt
      const file = {
        path: options.filename || 'file',
        content: data
      };
      
      // Füge die Datei zu IPFS hinzu
      const result = await this.client.add(file, {
        pin: options.pin !== undefined ? options.pin : true
      });
      
      logger.debug(`Datei auf IPFS gespeichert mit CID ${result.cid.toString()}`);
      
      // Pinne die Datei auf den angegebenen Services
      if (options.pin !== false) {
        const pinningServices = options.pinningServices || this.options.pinningServices;
        await this.pinToServices(result.cid.toString(), pinningServices);
      }
      
      return {
        cid: result.cid.toString(),
        size: result.size,
        path: result.path,
        uri: `ipfs://${result.cid.toString()}`,
        url: `https://ipfs.io/ipfs/${result.cid.toString()}`
      };
    } catch (error) {
      logger.error('Fehler beim Speichern der Datei auf IPFS', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Speichert JSON-Daten auf IPFS
   * @param {Object} data - Zu speichernde JSON-Daten
   * @param {Object} options - Speicheroptionen
   * @param {boolean} options.pin - Ob die Daten gepinnt werden sollen
   * @param {Array<string>} options.pinningServices - Zu verwendende Pinning-Services
   * @returns {Promise<Object>} IPFS-Speicherinformationen
   */
  async storeJson(data, options = {}) {
    try {
      logger.info('Speichere JSON-Daten auf IPFS');
      
      // Konvertiere die JSON-Daten in einen String
      const jsonString = JSON.stringify(data);
      
      // Speichere die JSON-Daten als Datei
      return await this.storeFile(Buffer.from(jsonString), {
        filename: options.filename || 'data.json',
        pin: options.pin,
        pinningServices: options.pinningServices
      });
    } catch (error) {
      logger.error('Fehler beim Speichern der JSON-Daten auf IPFS', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Datei von IPFS ab
   * @param {string} cid - Content-Identifikator (CID)
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Buffer>} Abgerufene Daten
   */
  async retrieveFile(cid, options = {}) {
    try {
      logger.info(`Rufe Datei mit CID ${cid} von IPFS ab`);
      
      // Hole die Datei von IPFS
      const chunks = [];
      for await (const chunk of this.client.cat(cid)) {
        chunks.push(chunk);
      }
      
      // Kombiniere die Chunks zu einem Buffer
      const data = Buffer.concat(chunks);
      
      logger.debug(`Datei mit CID ${cid} erfolgreich abgerufen`);
      
      return data;
    } catch (error) {
      logger.error('Fehler beim Abrufen der Datei von IPFS', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Ruft JSON-Daten von IPFS ab
   * @param {string} cid - Content-Identifikator (CID)
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Object>} Abgerufene JSON-Daten
   */
  async retrieveJson(cid, options = {}) {
    try {
      logger.info(`Rufe JSON-Daten mit CID ${cid} von IPFS ab`);
      
      // Hole die Datei von IPFS
      const data = await this.retrieveFile(cid, options);
      
      // Konvertiere die Daten in ein JSON-Objekt
      const jsonData = JSON.parse(data.toString());
      
      logger.debug(`JSON-Daten mit CID ${cid} erfolgreich abgerufen`);
      
      return jsonData;
    } catch (error) {
      logger.error('Fehler beim Abrufen der JSON-Daten von IPFS', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Datei auf IPFS existiert
   * @param {string} cid - Content-Identifikator (CID)
   * @param {Object} options - Prüfoptionen
   * @returns {Promise<boolean>} True, wenn die Datei existiert
   */
  async exists(cid, options = {}) {
    try {
      logger.info(`Prüfe, ob Datei mit CID ${cid} auf IPFS existiert`);
      
      // Versuche, die Datei abzurufen (nur die Header)
      for await (const _ of this.client.ls(cid)) {
        return true;
      }
      
      return false;
    } catch (error) {
      logger.debug(`Datei mit CID ${cid} existiert nicht auf IPFS`);
      return false;
    }
  }
  
  /**
   * Pinnt eine Datei auf IPFS
   * @param {string} cid - Content-Identifikator (CID)
   * @param {Object} options - Pin-Optionen
   * @returns {Promise<boolean>} True, wenn die Datei erfolgreich gepinnt wurde
   */
  async pin(cid, options = {}) {
    try {
      logger.info(`Pinne Datei mit CID ${cid} auf IPFS`);
      
      // Pinne die Datei auf IPFS
      await this.client.pin.add(cid);
      
      logger.debug(`Datei mit CID ${cid} erfolgreich gepinnt`);
      
      return true;
    } catch (error) {
      logger.error('Fehler beim Pinnen der Datei auf IPFS', {
        error: error.message,
        cid
      });
      return false;
    }
  }
  
  /**
   * Pinnt eine Datei auf verschiedenen Pinning-Services
   * @param {string} cid - Content-Identifikator (CID)
   * @param {Array<string>} services - Zu verwendende Pinning-Services
   * @returns {Promise<Object>} Ergebnis des Pinning-Vorgangs
   */
  async pinToServices(cid, services = []) {
    try {
      logger.info(`Pinne Datei mit CID ${cid} auf externen Services`);
      
      const results = {};
      
      for (const service of services) {
        try {
          switch (service) {
            case 'Pinata':
              if (this.pinningClients.Pinata) {
                const result = await this.pinningClients.Pinata.pinByHash(cid);
                results.Pinata = { success: true, id: result.id };
                logger.debug(`Datei mit CID ${cid} erfolgreich auf Pinata gepinnt`);
              }
              break;
              
            case 'Web3.Storage':
              // Web3.Storage unterstützt kein direktes Pinning nach CID
              logger.debug(`Web3.Storage unterstützt kein direktes Pinning nach CID`);
              break;
              
            case 'Infura':
              // Infura ist bereits durch den Haupt-IPFS-Client abgedeckt
              results.Infura = { success: true };
              logger.debug(`Datei mit CID ${cid} bereits auf Infura gepinnt`);
              break;
              
            default:
              logger.warn(`Unbekannter Pinning-Service: ${service}`);
          }
        } catch (error) {
          logger.error(`Fehler beim Pinnen auf ${service}`, {
            error: error.message,
            cid
          });
          results[service] = { success: false, error: error.message };
        }
      }
      
      return results;
    } catch (error) {
      logger.error('Fehler beim Pinnen auf externen Services', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Generiert eine URL für den Zugriff auf die Datei
   * @param {string} cid - Content-Identifikator (CID)
   * @param {Object} options - URL-Optionen
   * @param {string} options.gateway - Zu verwendender IPFS-Gateway
   * @returns {Promise<string>} URL für den Zugriff auf die Datei
   */
  async getAccessUrl(cid, options = {}) {
    const gateway = options.gateway || 'https://ipfs.io/ipfs/';
    return `${gateway}${cid}`;
  }
}/**
 * @fileoverview IPFS-Adapter für dezentrale Metadatenspeicherung
 * 
 * Dieser Adapter implementiert das StorageAdapter-Interface für IPFS.
 * Er wird hauptsächlich für die Speicherung von Metadaten und kleineren Dateien verwendet.
 */

import { create as createIPFS } from 'ipfs-http-client';
import { StorageAdapter } from './StorageAdapter.js';
import { logger } from '../../utils/logger.js';

/**
 * IPFS-Adapter für dezentrale Metadatenspeicherung
 */
export class IPFSAdapter extends StorageAdapter {
  /**
   * Erstellt eine neue Instanz des IPFSAdapter
   * @param {Object} options - Konfigurationsoptionen
   * @param {string} options.apiUrl - URL des IPFS-API (z.B. 'https://ipfs.infura.io:5001')
   * @param {string} options.apiKey - API-Schlüssel (falls erforderlich)
   * @param {string} options.apiSecret - API-Secret (falls erforderlich)
   * @param {Array<string>} options.pinningServices - Liste der Pinning-Services
   * @param {Object} options.pinningConfig - Konfiguration für Pinning-Services
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      apiUrl: options.apiUrl || process.env.IPFS_API_URL || 'https://ipfs.infura.io:5001',
      apiKey: options.apiKey || process.env.IPFS_API_KEY,
      apiSecret: options.apiSecret || process.env.IPFS_API_SECRET,
      pinningServices: options.pinningServices || ['Pinata', 'Infura', 'Web3.Storage'],
      pinningConfig: options.pinningConfig || {},
      ...options
    };
    
    this.client = null;
    this.pinningClients = {};
    
    logger.info('IPFSAdapter initialisiert');
  }
  
  /**
   * Initialisiert den IPFS-Client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      logger.info('Initialisiere IPFS-Client');
      
      // Konfiguriere den IPFS-Client
      const auth = this.options.apiKey && this.options.apiSecret
        ? 'Basic ' + Buffer.from(this.options.apiKey + ':' + this.options.apiSecret).toString('base64')
        : undefined;
      
      this.client = createIPFS({
        url: this.options.apiUrl,
        headers: {
          authorization: auth
        }
      });
      
      // Initialisiere Pinning-Services
      await this.initializePinningServices();
      
      logger.info('IPFS-Client erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des IPFS-Clients', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Initialisiert die Pinning-Services
   * @returns {Promise<void>}
   */
  async initializePinningServices() {
    try {
      for (const service of this.options.pinningServices) {
        const config = this.options.pinningConfig[service] || {};
        
        switch (service) {
          case 'Pinata':
            // Pinata-Client initialisieren
            if (config.apiKey && config.apiSecret) {
              const PinataSDK = await import('pinata-sdk');
              this.pinningClients.Pinata = PinataSDK.default(config.apiKey, config.apiSecret);
              logger.debug('Pinata-Client initialisiert');
            }
            break;
            
          case 'Web3.Storage':
            // Web3.Storage-Client initialisieren
            if (config.token) {
              const { Web3Storage } = await import('web3.storage');
              this.pinningClients.Web3Storage = new Web3Storage({ token: config.token });
              logger.debug('Web3.Storage-Client initialisiert');
            }
            break;
            
          case 'Infura':
            // Infura ist bereits durch den Haupt-IPFS-Client abgedeckt
            logger.debug('Infura-Client bereits initialisiert');
            break;
            
          default:
            logger.warn(`Unbekannter Pinning-Service: ${service}`);
        }
      }
    } catch (error) {
      logger.error('Fehler bei der Initialisierung der Pinning-Services', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Speichert eine Datei auf IPFS
   * @param {Buffer|string} data - Zu speichernde Daten
   * @param {Object} options - Speicheroptionen
   * @param {string} options.filename - Dateiname
   * @param {boolean} options.pin - Ob die Datei gepinnt werden soll
   * @param {Array<string>} options.pinningServices - Zu verwendende Pinning-Services
   * @returns {Promise<Object>} IPFS-Speicherinformationen
   */
  async storeFile(data, options = {}) {
    try {
      logger.info('Speichere Datei auf IPFS');
      
      // Erstelle ein File-Objekt
      const file = {
        path: options.filename || 'file',
        content: data
      };
      
      // Füge die Datei zu IPFS hinzu
      const result = await this.client.add(file, {
        pin: options.pin !== undefined ? options.pin : true
      });
      
      logger.debug(`Datei auf IPFS gespeichert mit CID ${result.cid.toString()}`);
      
      // Pinne die Datei auf den angegebenen Services
      if (options.pin !== false) {
        const pinningServices = options.pinningServices || this.options.pinningServices;
        await this.pinToServices(result.cid.toString(), pinningServices);
      }
      
      return {
        cid: result.cid.toString(),
        size: result.size,
        path: result.path,
        uri: `ipfs://${result.cid.toString()}`,
        url: `https://ipfs.io/ipfs/${result.cid.toString()}`
      };
    } catch (error) {
      logger.error('Fehler beim Speichern der Datei auf IPFS', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Speichert JSON-Daten auf IPFS
   * @param {Object} data - Zu speichernde JSON-Daten
   * @param {Object} options - Speicheroptionen
   * @param {boolean} options.pin - Ob die Daten gepinnt werden sollen
   * @param {Array<string>} options.pinningServices - Zu verwendende Pinning-Services
   * @returns {Promise<Object>} IPFS-Speicherinformationen
   */
  async storeJson(data, options = {}) {
    try {
      logger.info('Speichere JSON-Daten auf IPFS');
      
      // Konvertiere die JSON-Daten in einen String
      const jsonString = JSON.stringify(data);
      
      // Speichere die JSON-Daten als Datei
      return await this.storeFile(Buffer.from(jsonString), {
        filename: options.filename || 'data.json',
        pin: options.pin,
        pinningServices: options.pinningServices
      });
    } catch (error) {
      logger.error('Fehler beim Speichern der JSON-Daten auf IPFS', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Datei von IPFS ab
   * @param {string} cid - Content-Identifikator (CID)
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Buffer>} Abgerufene Daten
   */
  async retrieveFile(cid, options = {}) {
    try {
      logger.info(`Rufe Datei mit CID ${cid} von IPFS ab`);
      
      // Hole die Datei von IPFS
      const chunks = [];
      for await (const chunk of this.client.cat(cid)) {
        chunks.push(chunk);
      }
      
      // Kombiniere die Chunks zu einem Buffer
      const data = Buffer.concat(chunks);
      
      logger.debug(`Datei mit CID ${cid} erfolgreich abgerufen`);
      
      return data;
    } catch (error) {
      logger.error('Fehler beim Abrufen der Datei von IPFS', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Ruft JSON-Daten von IPFS ab
   * @param {string} cid - Content-Identifikator (CID)
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Object>} Abgerufene JSON-Daten
   */
  async retrieveJson(cid, options = {}) {
    try {
      logger.info(`Rufe JSON-Daten mit CID ${cid} von IPFS ab`);
      
      // Hole die Datei von IPFS
      const data = await this.retrieveFile(cid, options);
      
      // Konvertiere die Daten in ein JSON-Objekt
      const jsonData = JSON.parse(data.toString());
      
      logger.debug(`JSON-Daten mit CID ${cid} erfolgreich abgerufen`);
      
      return jsonData;
    } catch (error) {
      logger.error('Fehler beim Abrufen der JSON-Daten von IPFS', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Datei auf IPFS existiert
   * @param {string} cid - Content-Identifikator (CID)
   * @param {Object} options - Prüfoptionen
   * @returns {Promise<boolean>} True, wenn die Datei existiert
   */
  async exists(cid, options = {}) {
    try {
      logger.info(`Prüfe, ob Datei mit CID ${cid} auf IPFS existiert`);
      
      // Versuche, die Datei abzurufen (nur die Header)
      for await (const _ of this.client.ls(cid)) {
        return true;
      }
      
      return false;
    } catch (error) {
      logger.debug(`Datei mit CID ${cid} existiert nicht auf IPFS`);
      return false;
    }
  }
  
  /**
   * Pinnt eine Datei auf IPFS
   * @param {string} cid - Content-Identifikator (CID)
   * @param {Object} options - Pin-Optionen
   * @returns {Promise<boolean>} True, wenn die Datei erfolgreich gepinnt wurde
   */
  async pin(cid, options = {}) {
    try {
      logger.info(`Pinne Datei mit CID ${cid} auf IPFS`);
      
      // Pinne die Datei auf IPFS
      await this.client.pin.add(cid);
      
      logger.debug(`Datei mit CID ${cid} erfolgreich gepinnt`);
      
      return true;
    } catch (error) {
      logger.error('Fehler beim Pinnen der Datei auf IPFS', {
        error: error.message,
        cid
      });
      return false;
    }
  }
  
  /**
   * Pinnt eine Datei auf verschiedenen Pinning-Services
   * @param {string} cid - Content-Identifikator (CID)
   * @param {Array<string>} services - Zu verwendende Pinning-Services
   * @returns {Promise<Object>} Ergebnis des Pinning-Vorgangs
   */
  async pinToServices(cid, services = []) {
    try {
      logger.info(`Pinne Datei mit CID ${cid} auf externen Services`);
      
      const results = {};
      
      for (const service of services) {
        try {
          switch (service) {
            case 'Pinata':
              if (this.pinningClients.Pinata) {
                const result = await this.pinningClients.Pinata.pinByHash(cid);
                results.Pinata = { success: true, id: result.id };
                logger.debug(`Datei mit CID ${cid} erfolgreich auf Pinata gepinnt`);
              }
              break;
              
            case 'Web3.Storage':
              // Web3.Storage unterstützt kein direktes Pinning nach CID
              logger.debug(`Web3.Storage unterstützt kein direktes Pinning nach CID`);
              break;
              
            case 'Infura':
              // Infura ist bereits durch den Haupt-IPFS-Client abgedeckt
              results.Infura = { success: true };
              logger.debug(`Datei mit CID ${cid} bereits auf Infura gepinnt`);
              break;
              
            default:
              logger.warn(`Unbekannter Pinning-Service: ${service}`);
          }
        } catch (error) {
          logger.error(`Fehler beim Pinnen auf ${service}`, {
            error: error.message,
            cid
          });
          results[service] = { success: false, error: error.message };
        }
      }
      
      return results;
    } catch (error) {
      logger.error('Fehler beim Pinnen auf externen Services', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Generiert eine URL für den Zugriff auf die Datei
   * @param {string} cid - Content-Identifikator (CID)
   * @param {Object} options - URL-Optionen
   * @param {string} options.gateway - Zu verwendender IPFS-Gateway
   * @returns {Promise<string>} URL für den Zugriff auf die Datei
   */
  async getAccessUrl(cid, options = {}) {
    const gateway = options.gateway || 'https://ipfs.io/ipfs/';
    return `${gateway}${cid}`;
  }
}/**
 * @fileoverview IPFS-Adapter für dezentrale Metadatenspeicherung
 * 
 * Dieser Adapter implementiert das StorageAdapter-Interface für IPFS.
 * Er wird hauptsächlich für die Speicherung von Metadaten und kleineren Dateien verwendet.
 */

import { create as createIPFS } from 'ipfs-http-client';
import { StorageAdapter } from './StorageAdapter.js';
import { logger } from '../../utils/logger.js';

/**
 * IPFS-Adapter für dezentrale Metadatenspeicherung
 */
export class IPFSAdapter extends StorageAdapter {
  /**
   * Erstellt eine neue Instanz des IPFSAdapter
   * @param {Object} options - Konfigurationsoptionen
   * @param {string} options.apiUrl - URL des IPFS-API (z.B. 'https://ipfs.infura.io:5001')
   * @param {string} options.apiKey - API-Schlüssel (falls erforderlich)
   * @param {string} options.apiSecret - API-Secret (falls erforderlich)
   * @param {Array<string>} options.pinningServices - Liste der Pinning-Services
   * @param {Object} options.pinningConfig - Konfiguration für Pinning-Services
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      apiUrl: options.apiUrl || process.env.IPFS_API_URL || 'https://ipfs.infura.io:5001',
      apiKey: options.apiKey || process.env.IPFS_API_KEY,
      apiSecret: options.apiSecret || process.env.IPFS_API_SECRET,
      pinningServices: options.pinningServices || ['Pinata', 'Infura', 'Web3.Storage'],
      pinningConfig: options.pinningConfig || {},
      ...options
    };
    
    this.client = null;
    this.pinningClients = {};
    
    logger.info('IPFSAdapter initialisiert');
  }
  
  /**
   * Initialisiert den IPFS-Client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      logger.info('Initialisiere IPFS-Client');
      
      // Konfiguriere den IPFS-Client
      const auth = this.options.apiKey && this.options.apiSecret
        ? 'Basic ' + Buffer.from(this.options.apiKey + ':' + this.options.apiSecret).toString('base64')
        : undefined;
      
      this.client = createIPFS({
        url: this.options.apiUrl,
        headers: {
          authorization: auth
        }
      });
      
      // Initialisiere Pinning-Services
      await this.initializePinningServices();
      
      logger.info('IPFS-Client erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des IPFS-Clients', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Initialisiert die Pinning-Services
   * @returns {Promise<void>}
   */
  async initializePinningServices() {
    try {
      for (const service of this.options.pinningServices) {
        const config = this.options.pinningConfig[service] || {};
        
        switch (service) {
          case 'Pinata':
            // Pinata-Client initialisieren
            if (config.apiKey && config.apiSecret) {
              const PinataSDK = await import('pinata-sdk');
              this.pinningClients.Pinata = PinataSDK.default(config.apiKey, config.apiSecret);
              logger.debug('Pinata-Client initialisiert');
            }
            break;
            
          case 'Web3.Storage':
            // Web3.Storage-Client initialisieren
            if (config.token) {
              const { Web3Storage } = await import('web3.storage');
              this.pinningClients.Web3Storage = new Web3Storage({ token: config.token });
              logger.debug('Web3.Storage-Client initialisiert');
            }
            break;
            
          case 'Infura':
            // Infura ist bereits durch den Haupt-IPFS-Client abgedeckt
            logger.debug('Infura-Client bereits initialisiert');
            break;
            
          default:
            logger.warn(`Unbekannter Pinning-Service: ${service}`);
        }
      }
    } catch (error) {
      logger.error('Fehler bei der Initialisierung der Pinning-Services', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Speichert eine Datei auf IPFS
   * @param {Buffer|string} data - Zu speichernde Daten
   * @param {Object} options - Speicheroptionen
   * @param {string} options.filename - Dateiname
   * @param {boolean} options.pin - Ob die Datei gepinnt werden soll
   * @param {Array<string>} options.pinningServices - Zu verwendende Pinning-Services
   * @returns {Promise<Object>} IPFS-Speicherinformationen
   */
  async storeFile(data, options = {}) {
    try {
      logger.info('Speichere Datei auf IPFS');
      
      // Erstelle ein File-Objekt
      const file = {
        path: options.filename || 'file',
        content: data
      };
      
      // Füge die Datei zu IPFS hinzu
      const result = await this.client.add(file, {
        pin: options.pin !== undefined ? options.pin : true
      });
      
      logger.debug(`Datei auf IPFS gespeichert mit CID ${result.cid.toString()}`);
      
      // Pinne die Datei auf den angegebenen Services
      if (options.pin !== false) {
        const pinningServices = options.pinningServices || this.options.pinningServices;
        await this.pinToServices(result.cid.toString(), pinningServices);
      }
      
      return {
        cid: result.cid.toString(),
        size: result.size,
        path: result.path,
        uri: `ipfs://${result.cid.toString()}`,
        url: `https://ipfs.io/ipfs/${result.cid.toString()}`
      };
    } catch (error) {
      logger.error('Fehler beim Speichern der Datei auf IPFS', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Speichert JSON-Daten auf IPFS
   * @param {Object} data - Zu speichernde JSON-Daten
   * @param {Object} options - Speicheroptionen
   * @param {boolean} options.pin - Ob die Daten gepinnt werden sollen
   * @param {Array<string>} options.pinningServices - Zu verwendende Pinning-Services
   * @returns {Promise<Object>} IPFS-Speicherinformationen
   */
  async storeJson(data, options = {}) {
    try {
      logger.info('Speichere JSON-Daten auf IPFS');
      
      // Konvertiere die JSON-Daten in einen String
      const jsonString = JSON.stringify(data);
      
      // Speichere die JSON-Daten als Datei
      return await this.storeFile(Buffer.from(jsonString), {
        filename: options.filename || 'data.json',
        pin: options.pin,
        pinningServices: options.pinningServices
      });
    } catch (error) {
      logger.error('Fehler beim Speichern der JSON-Daten auf IPFS', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Datei von IPFS ab
   * @param {string} cid - Content-Identifikator (CID)
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Buffer>} Abgerufene Daten
   */
  async retrieveFile(cid, options = {}) {
    try {
      logger.info(`Rufe Datei mit CID ${cid} von IPFS ab`);
      
      // Hole die Datei von IPFS
      const chunks = [];
      for await (const chunk of this.client.cat(cid)) {
        chunks.push(chunk);
      }
      
      // Kombiniere die Chunks zu einem Buffer
      const data = Buffer.concat(chunks);
      
      logger.debug(`Datei mit CID ${cid} erfolgreich abgerufen`);
      
      return data;
    } catch (error) {
      logger.error('Fehler beim Abrufen der Datei von IPFS', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Ruft JSON-Daten von IPFS ab
   * @param {string} cid - Content-Identifikator (CID)
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Object>} Abgerufene JSON-Daten
   */
  async retrieveJson(cid, options = {}) {
    try {
      logger.info(`Rufe JSON-Daten mit CID ${cid} von IPFS ab`);
      
      // Hole die Datei von IPFS
      const data = await this.retrieveFile(cid, options);
      
      // Konvertiere die Daten in ein JSON-Objekt
      const jsonData = JSON.parse(data.toString());
      
      logger.debug(`JSON-Daten mit CID ${cid} erfolgreich abgerufen`);
      
      return jsonData;
    } catch (error) {
      logger.error('Fehler beim Abrufen der JSON-Daten von IPFS', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Datei auf IPFS existiert
   * @param {string} cid - Content-Identifikator (CID)
   * @param {Object} options - Prüfoptionen
   * @returns {Promise<boolean>} True, wenn die Datei existiert
   */
  async exists(cid, options = {}) {
    try {
      logger.info(`Prüfe, ob Datei mit CID ${cid} auf IPFS existiert`);
      
      // Versuche, die Datei abzurufen (nur die Header)
      for await (const _ of this.client.ls(cid)) {
        return true;
      }
      
      return false;
    } catch (error) {
      logger.debug(`Datei mit CID ${cid} existiert nicht auf IPFS`);
      return false;
    }
  }
  
  /**
   * Pinnt eine Datei auf IPFS
   * @param {string} cid - Content-Identifikator (CID)
   * @param {Object} options - Pin-Optionen
   * @returns {Promise<boolean>} True, wenn die Datei erfolgreich gepinnt wurde
   */
  async pin(cid, options = {}) {
    try {
      logger.info(`Pinne Datei mit CID ${cid} auf IPFS`);
      
      // Pinne die Datei auf IPFS
      await this.client.pin.add(cid);
      
      logger.debug(`Datei mit CID ${cid} erfolgreich gepinnt`);
      
      return true;
    } catch (error) {
      logger.error('Fehler beim Pinnen der Datei auf IPFS', {
        error: error.message,
        cid
      });
      return false;
    }
  }
  
  /**
   * Pinnt eine Datei auf verschiedenen Pinning-Services
   * @param {string} cid - Content-Identifikator (CID)
   * @param {Array<string>} services - Zu verwendende Pinning-Services
   * @returns {Promise<Object>} Ergebnis des Pinning-Vorgangs
   */
  async pinToServices(cid, services = []) {
    try {
      logger.info(`Pinne Datei mit CID ${cid} auf externen Services`);
      
      const results = {};
      
      for (const service of services) {
        try {
          switch (service) {
            case 'Pinata':
              if (this.pinningClients.Pinata) {
                const result = await this.pinningClients.Pinata.pinByHash(cid);
                results.Pinata = { success: true, id: result.id };
                logger.debug(`Datei mit CID ${cid} erfolgreich auf Pinata gepinnt`);
              }
              break;
              
            case 'Web3.Storage':
              // Web3.Storage unterstützt kein direktes Pinning nach CID
              logger.debug(`Web3.Storage unterstützt kein direktes Pinning nach CID`);
              break;
              
            case 'Infura':
              // Infura ist bereits durch den Haupt-IPFS-Client abgedeckt
              results.Infura = { success: true };
              logger.debug(`Datei mit CID ${cid} bereits auf Infura gepinnt`);
              break;
              
            default:
              logger.warn(`Unbekannter Pinning-Service: ${service}`);
          }
        } catch (error) {
          logger.error(`Fehler beim Pinnen auf ${service}`, {
            error: error.message,
            cid
          });
          results[service] = { success: false, error: error.message };
        }
      }
      
      return results;
    } catch (error) {
      logger.error('Fehler beim Pinnen auf externen Services', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Generiert eine URL für den Zugriff auf die Datei
   * @param {string} cid - Content-Identifikator (CID)
   * @param {Object} options - URL-Optionen
   * @param {string} options.gateway - Zu verwendender IPFS-Gateway
   * @returns {Promise<string>} URL für den Zugriff auf die Datei
   */
  async getAccessUrl(cid, options = {}) {
    const gateway = options.gateway || 'https://ipfs.io/ipfs/';
    return `${gateway}${cid}`;
  }
}/**
 * @fileoverview IPFS-Adapter für dezentrale Metadatenspeicherung
 * 
 * Dieser Adapter implementiert das StorageAdapter-Interface für IPFS.
 * Er wird hauptsächlich für die Speicherung von Metadaten und kleineren Dateien verwendet.
 */

import { create as createIPFS } from 'ipfs-http-client';
import { StorageAdapter } from './StorageAdapter.js';
import { logger } from '../../utils/logger.js';

/**
 * IPFS-Adapter für dezentrale Metadatenspeicherung
 */
export class IPFSAdapter extends StorageAdapter {
  /**
   * Erstellt eine neue Instanz des IPFSAdapter
   * @param {Object} options - Konfigurationsoptionen
   * @param {string} options.apiUrl - URL des IPFS-API (z.B. 'https://ipfs.infura.io:5001')
   * @param {string} options.apiKey - API-Schlüssel (falls erforderlich)
   * @param {string} options.apiSecret - API-Secret (falls erforderlich)
   * @param {Array<string>} options.pinningServices - Liste der Pinning-Services
   * @param {Object} options.pinningConfig - Konfiguration für Pinning-Services
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      apiUrl: options.apiUrl || process.env.IPFS_API_URL || 'https://ipfs.infura.io:5001',
      apiKey: options.apiKey || process.env.IPFS_API_KEY,
      apiSecret: options.apiSecret || process.env.IPFS_API_SECRET,
      pinningServices: options.pinningServices || ['Pinata', 'Infura', 'Web3.Storage'],
      pinningConfig: options.pinningConfig || {},
      ...options
    };
    
    this.client = null;
    this.pinningClients = {};
    
    logger.info('IPFSAdapter initialisiert');
  }
  
  /**
   * Initialisiert den IPFS-Client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      logger.info('Initialisiere IPFS-Client');
      
      // Konfiguriere den IPFS-Client
      const auth = this.options.apiKey && this.options.apiSecret
        ? 'Basic ' + Buffer.from(this.options.apiKey + ':' + this.options.apiSecret).toString('base64')
        : undefined;
      
      this.client = createIPFS({
        url: this.options.apiUrl,
        headers: {
          authorization: auth
        }
      });
      
      // Initialisiere Pinning-Services
      await this.initializePinningServices();
      
      logger.info('IPFS-Client erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des IPFS-Clients', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Initialisiert die Pinning-Services
   * @returns {Promise<void>}
   */
  async initializePinningServices() {
    try {
      for (const service of this.options.pinningServices) {
        const config = this.options.pinningConfig[service] || {};
        
        switch (service) {
          case 'Pinata':
            // Pinata-Client initialisieren
            if (config.apiKey && config.apiSecret) {
              const PinataSDK = await import('pinata-sdk');
              this.pinningClients.Pinata = PinataSDK.default(config.apiKey, config.apiSecret);
              logger.debug('Pinata-Client initialisiert');
            }
            break;
            
          case 'Web3.Storage':
            // Web3.Storage-Client initialisieren
            if (config.token) {
              const { Web3Storage } = await import('web3.storage');
              this.pinningClients.Web3Storage = new Web3Storage({ token: config.token });
              logger.debug('Web3.Storage-Client initialisiert');
            }
            break;
            
          case 'Infura':
            // Infura ist bereits durch den Haupt-IPFS-Client abgedeckt
            logger.debug('Infura-Client bereits initialisiert');
            break;
            
          default:
            logger.warn(`Unbekannter Pinning-Service: ${service}`);
        }
      }
    } catch (error) {
      logger.error('Fehler bei der Initialisierung der Pinning-Services', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Speichert eine Datei auf IPFS
   * @param {Buffer|string} data - Zu speichernde Daten
   * @param {Object} options - Speicheroptionen
   * @param {string} options.filename - Dateiname
   * @param {boolean} options.pin - Ob die Datei gepinnt werden soll
   * @param {Array<string>} options.pinningServices - Zu verwendende Pinning-Services
   * @returns {Promise<Object>} IPFS-Speicherinformationen
   */
  async storeFile(data, options = {}) {
    try {
      logger.info('Speichere Datei auf IPFS');
      
      // Erstelle ein File-Objekt
      const file = {
        path: options.filename || 'file',
        content: data
      };
      
      // Füge die Datei zu IPFS hinzu
      const result = await this.client.add(file, {
        pin: options.pin !== undefined ? options.pin : true
      });
      
      logger.debug(`Datei auf IPFS gespeichert mit CID ${result.cid.toString()}`);
      
      // Pinne die Datei auf den angegebenen Services
      if (options.pin !== false) {
        const pinningServices = options.pinningServices || this.options.pinningServices;
        await this.pinToServices(result.cid.toString(), pinningServices);
      }
      
      return {
        cid: result.cid.toString(),
        size: result.size,
        path: result.path,
        uri: `ipfs://${result.cid.toString()}`,
        url: `https://ipfs.io/ipfs/${result.cid.toString()}`
      };
    } catch (error) {
      logger.error('Fehler beim Speichern der Datei auf IPFS', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Speichert JSON-Daten auf IPFS
   * @param {Object} data - Zu speichernde JSON-Daten
   * @param {Object} options - Speicheroptionen
   * @param {boolean} options.pin - Ob die Daten gepinnt werden sollen
   * @param {Array<string>} options.pinningServices - Zu verwendende Pinning-Services
   * @returns {Promise<Object>} IPFS-Speicherinformationen
   */
  async storeJson(data, options = {}) {
    try {
      logger.info('Speichere JSON-Daten auf IPFS');
      
      // Konvertiere die JSON-Daten in einen String
      const jsonString = JSON.stringify(data);
      
      // Speichere die JSON-Daten als Datei
      return await this.storeFile(Buffer.from(jsonString), {
        filename: options.filename || 'data.json',
        pin: options.pin,
        pinningServices: options.pinningServices
      });
    } catch (error) {
      logger.error('Fehler beim Speichern der JSON-Daten auf IPFS', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Datei von IPFS ab
   * @param {string} cid - Content-Identifikator (CID)
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Buffer>} Abgerufene Daten
   */
  async retrieveFile(cid, options = {}) {
    try {
      logger.info(`Rufe Datei mit CID ${cid} von IPFS ab`);
      
      // Hole die Datei von IPFS
      const chunks = [];
      for await (const chunk of this.client.cat(cid)) {
        chunks.push(chunk);
      }
      
      // Kombiniere die Chunks zu einem Buffer
      const data = Buffer.concat(chunks);
      
      logger.debug(`Datei mit CID ${cid} erfolgreich abgerufen`);
      
      return data;
    } catch (error) {
      logger.error('Fehler beim Abrufen der Datei von IPFS', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Ruft JSON-Daten von IPFS ab
   * @param {string} cid - Content-Identifikator (CID)
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Object>} Abgerufene JSON-Daten
   */
  async retrieveJson(cid, options = {}) {
    try {
      logger.info(`Rufe JSON-Daten mit CID ${cid} von IPFS ab`);
      
      // Hole die Datei von IPFS
      const data = await this.retrieveFile(cid, options);
      
      // Konvertiere die Daten in ein JSON-Objekt
      const jsonData = JSON.parse(data.toString());
      
      logger.debug(`JSON-Daten mit CID ${cid} erfolgreich abgerufen`);
      
      return jsonData;
    } catch (error) {
      logger.error('Fehler beim Abrufen der JSON-Daten von IPFS', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Datei auf IPFS existiert
   * @param {string} cid - Content-Identifikator (CID)
   * @param {Object} options - Prüfoptionen
   * @returns {Promise<boolean>} True, wenn die Datei existiert
   */
  async exists(cid, options = {}) {
    try {
      logger.info(`Prüfe, ob Datei mit CID ${cid} auf IPFS existiert`);
      
      // Versuche, die Datei abzurufen (nur die Header)
      for await (const _ of this.client.ls(cid)) {
        return true;
      }
      
      return false;
    } catch (error) {
      logger.debug(`Datei mit CID ${cid} existiert nicht auf IPFS`);
      return false;
    }
  }
  
  /**
   * Pinnt eine Datei auf IPFS
   * @param {string} cid - Content-Identifikator (CID)
   * @param {Object} options - Pin-Optionen
   * @returns {Promise<boolean>} True, wenn die Datei erfolgreich gepinnt wurde
   */
  async pin(cid, options = {}) {
    try {
      logger.info(`Pinne Datei mit CID ${cid} auf IPFS`);
      
      // Pinne die Datei auf IPFS
      await this.client.pin.add(cid);
      
      logger.debug(`Datei mit CID ${cid} erfolgreich gepinnt`);
      
      return true;
    } catch (error) {
      logger.error('Fehler beim Pinnen der Datei auf IPFS', {
        error: error.message,
        cid
      });
      return false;
    }
  }
  
  /**
   * Pinnt eine Datei auf verschiedenen Pinning-Services
   * @param {string} cid - Content-Identifikator (CID)
   * @param {Array<string>} services - Zu verwendende Pinning-Services
   * @returns {Promise<Object>} Ergebnis des Pinning-Vorgangs
   */
  async pinToServices(cid, services = []) {
    try {
      logger.info(`Pinne Datei mit CID ${cid} auf externen Services`);
      
      const results = {};
      
      for (const service of services) {
        try {
          switch (service) {
            case 'Pinata':
              if (this.pinningClients.Pinata) {
                const result = await this.pinningClients.Pinata.pinByHash(cid);
                results.Pinata = { success: true, id: result.id };
                logger.debug(`Datei mit CID ${cid} erfolgreich auf Pinata gepinnt`);
              }
              break;
              
            case 'Web3.Storage':
              // Web3.Storage unterstützt kein direktes Pinning nach CID
              logger.debug(`Web3.Storage unterstützt kein direktes Pinning nach CID`);
              break;
              
            case 'Infura':
              // Infura ist bereits durch den Haupt-IPFS-Client abgedeckt
              results.Infura = { success: true };
              logger.debug(`Datei mit CID ${cid} bereits auf Infura gepinnt`);
              break;
              
            default:
              logger.warn(`Unbekannter Pinning-Service: ${service}`);
          }
        } catch (error) {
          logger.error(`Fehler beim Pinnen auf ${service}`, {
            error: error.message,
            cid
          });
          results[service] = { success: false, error: error.message };
        }
      }
      
      return results;
    } catch (error) {
      logger.error('Fehler beim Pinnen auf externen Services', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Generiert eine URL für den Zugriff auf die Datei
   * @param {string} cid - Content-Identifikator (CID)
   * @param {Object} options - URL-Optionen
   * @param {string} options.gateway - Zu verwendender IPFS-Gateway
   * @returns {Promise<string>} URL für den Zugriff auf die Datei
   */
  async getAccessUrl(cid, options = {}) {
    const gateway = options.gateway || 'https://ipfs.io/ipfs/';
    return `${gateway}${cid}`;
  }
}/**
 * @fileoverview IPFS-Adapter für dezentrale Metadatenspeicherung
 * 
 * Dieser Adapter implementiert das StorageAdapter-Interface für IPFS.
 * Er wird hauptsächlich für die Speicherung von Metadaten und kleineren Dateien verwendet.
 */

import { create as createIPFS } from 'ipfs-http-client';
import { StorageAdapter } from './StorageAdapter.js';
import { logger } from '../../utils/logger.js';

/**
 * IPFS-Adapter für dezentrale Metadatenspeicherung
 */
export class IPFSAdapter extends StorageAdapter {
  /**
   * Erstellt eine neue Instanz des IPFSAdapter
   * @param {Object} options - Konfigurationsoptionen
   * @param {string} options.apiUrl - URL des IPFS-API (z.B. 'https://ipfs.infura.io:5001')
   * @param {string} options.apiKey - API-Schlüssel (falls erforderlich)
   * @param {string} options.apiSecret - API-Secret (falls erforderlich)
   * @param {Array<string>} options.pinningServices - Liste der Pinning-Services
   * @param {Object} options.pinningConfig - Konfiguration für Pinning-Services
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      apiUrl: options.apiUrl || process.env.IPFS_API_URL || 'https://ipfs.infura.io:5001',
      apiKey: options.apiKey || process.env.IPFS_API_KEY,
      apiSecret: options.apiSecret || process.env.IPFS_API_SECRET,
      pinningServices: options.pinningServices || ['Pinata', 'Infura', 'Web3.Storage'],
      pinningConfig: options.pinningConfig || {},
      ...options
    };
    
    this.client = null;
    this.pinningClients = {};
    
    logger.info('IPFSAdapter initialisiert');
  }
  
  /**
   * Initialisiert den IPFS-Client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      logger.info('Initialisiere IPFS-Client');
      
      // Konfiguriere den IPFS-Client
      const auth = this.options.apiKey && this.options.apiSecret
        ? 'Basic ' + Buffer.from(this.options.apiKey + ':' + this.options.apiSecret).toString('base64')
        : undefined;
      
      this.client = createIPFS({
        url: this.options.apiUrl,
        headers: {
          authorization: auth
        }
      });
      
      // Initialisiere Pinning-Services
      await this.initializePinningServices();
      
      logger.info('IPFS-Client erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des IPFS-Clients', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Initialisiert die Pinning-Services
   * @returns {Promise<void>}
   */
  async initializePinningServices() {
    try {
      for (const service of this.options.pinningServices) {
        const config = this.options.pinningConfig[service] || {};
        
        switch (service) {
          case 'Pinata':
            // Pinata-Client initialisieren
            if (config.apiKey && config.apiSecret) {
              const PinataSDK = await import('pinata-sdk');
              this.pinningClients.Pinata = PinataSDK.default(config.apiKey, config.apiSecret);
              logger.debug('Pinata-Client initialisiert');
            }
            break;
            
          case 'Web3.Storage':
            // Web3.Storage-Client initialisieren
            if (config.token) {
              const { Web3Storage } = await import('web3.storage');
              this.pinningClients.Web3Storage = new Web3Storage({ token: config.token });
              logger.debug('Web3.Storage-Client initialisiert');
            }
            break;
            
          case 'Infura':
            // Infura ist bereits durch den Haupt-IPFS-Client abgedeckt
            logger.debug('Infura-Client bereits initialisiert');
            break;
            
          default:
            logger.warn(`Unbekannter Pinning-Service: ${service}`);
        }
      }
    } catch (error) {
      logger.error('Fehler bei der Initialisierung der Pinning-Services', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Speichert eine Datei auf IPFS
   * @param {Buffer|string} data - Zu speichernde Daten
   * @param {Object} options - Speicheroptionen
   * @param {string} options.filename - Dateiname
   * @param {boolean} options.pin - Ob die Datei gepinnt werden soll
   * @param {Array<string>} options.pinningServices - Zu verwendende Pinning-Services
   * @returns {Promise<Object>} IPFS-Speicherinformationen
   */
  async storeFile(data, options = {}) {
    try {
      logger.info('Speichere Datei auf IPFS');
      
      // Erstelle ein File-Objekt
      const file = {
        path: options.filename || 'file',
        content: data
      };
      
      // Füge die Datei zu IPFS hinzu
      const result = await this.client.add(file, {
        pin: options.pin !== undefined ? options.pin : true
      });
      
      logger.debug(`Datei auf IPFS gespeichert mit CID ${result.cid.toString()}`);
      
      // Pinne die Datei auf den angegebenen Services
      if (options.pin !== false) {
        const pinningServices = options.pinningServices || this.options.pinningServices;
        await this.pinToServices(result.cid.toString(), pinningServices);
      }
      
      return {
        cid: result.cid.toString(),
        size: result.size,
        path: result.path,
        uri: `ipfs://${result.cid.toString()}`,
        url: `https://ipfs.io/ipfs/${result.cid.toString()}`
      };
    } catch (error) {
      logger.error('Fehler beim Speichern der Datei auf IPFS', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Speichert JSON-Daten auf IPFS
   * @param {Object} data - Zu speichernde JSON-Daten
   * @param {Object} options - Speicheroptionen
   * @param {boolean} options.pin - Ob die Daten gepinnt werden sollen
   * @param {Array<string>} options.pinningServices - Zu verwendende Pinning-Services
   * @returns {Promise<Object>} IPFS-Speicherinformationen
   */
  async storeJson(data, options = {}) {
    try {
      logger.info('Speichere JSON-Daten auf IPFS');
      
      // Konvertiere die JSON-Daten in einen String
      const jsonString = JSON.stringify(data);
      
      // Speichere die JSON-Daten als Datei
      return await this.storeFile(Buffer.from(jsonString), {
        filename: options.filename || 'data.json',
        pin: options.pin,
        pinningServices: options.pinningServices
      });
    } catch (error) {
      logger.error('Fehler beim Speichern der JSON-Daten auf IPFS', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Datei von IPFS ab
   * @param {string} cid - Content-Identifikator (CID)
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Buffer>} Abgerufene Daten
   */
  async retrieveFile(cid, options = {}) {
    try {
      logger.info(`Rufe Datei mit CID ${cid} von IPFS ab`);
      
      // Hole die Datei von IPFS
      const chunks = [];
      for await (const chunk of this.client.cat(cid)) {
        chunks.push(chunk);
      }
      
      // Kombiniere die Chunks zu einem Buffer
      const data = Buffer.concat(chunks);
      
      logger.debug(`Datei mit CID ${cid} erfolgreich abgerufen`);
      
      return data;
    } catch (error) {
      logger.error('Fehler beim Abrufen der Datei von IPFS', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Ruft JSON-Daten von IPFS ab
   * @param {string} cid - Content-Identifikator (CID)
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Object>} Abgerufene JSON-Daten
   */
  async retrieveJson(cid, options = {}) {
    try {
      logger.info(`Rufe JSON-Daten mit CID ${cid} von IPFS ab`);
      
      // Hole die Datei von IPFS
      const data = await this.retrieveFile(cid, options);
      
      // Konvertiere die Daten in ein JSON-Objekt
      const jsonData = JSON.parse(data.toString());
      
      logger.debug(`JSON-Daten mit CID ${cid} erfolgreich abgerufen`);
      
      return jsonData;
    } catch (error) {
      logger.error('Fehler beim Abrufen der JSON-Daten von IPFS', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Datei auf IPFS existiert
   * @param {string} cid - Content-Identifikator (CID)
   * @param {Object} options - Prüfoptionen
   * @returns {Promise<boolean>} True, wenn die Datei existiert
   */
  async exists(cid, options = {}) {
    try {
      logger.info(`Prüfe, ob Datei mit CID ${cid} auf IPFS existiert`);
      
      // Versuche, die Datei abzurufen (nur die Header)
      for await (const _ of this.client.ls(cid)) {
        return true;
      }
      
      return false;
    } catch (error) {
      logger.debug(`Datei mit CID ${cid} existiert nicht auf IPFS`);
      return false;
    }
  }
  
  /**
   * Pinnt eine Datei auf IPFS
   * @param {string} cid - Content-Identifikator (CID)
   * @param {Object} options - Pin-Optionen
   * @returns {Promise<boolean>} True, wenn die Datei erfolgreich gepinnt wurde
   */
  async pin(cid, options = {}) {
    try {
      logger.info(`Pinne Datei mit CID ${cid} auf IPFS`);
      
      // Pinne die Datei auf IPFS
      await this.client.pin.add(cid);
      
      logger.debug(`Datei mit CID ${cid} erfolgreich gepinnt`);
      
      return true;
    } catch (error) {
      logger.error('Fehler beim Pinnen der Datei auf IPFS', {
        error: error.message,
        cid
      });
      return false;
    }
  }
  
  /**
   * Pinnt eine Datei auf verschiedenen Pinning-Services
   * @param {string} cid - Content-Identifikator (CID)
   * @param {Array<string>} services - Zu verwendende Pinning-Services
   * @returns {Promise<Object>} Ergebnis des Pinning-Vorgangs
   */
  async pinToServices(cid, services = []) {
    try {
      logger.info(`Pinne Datei mit CID ${cid} auf externen Services`);
      
      const results = {};
      
      for (const service of services) {
        try {
          switch (service) {
            case 'Pinata':
              if (this.pinningClients.Pinata) {
                const result = await this.pinningClients.Pinata.pinByHash(cid);
                results.Pinata = { success: true, id: result.id };
                logger.debug(`Datei mit CID ${cid} erfolgreich auf Pinata gepinnt`);
              }
              break;
              
            case 'Web3.Storage':
              // Web3.Storage unterstützt kein direktes Pinning nach CID
              logger.debug(`Web3.Storage unterstützt kein direktes Pinning nach CID`);
              break;
              
            case 'Infura':
              // Infura ist bereits durch den Haupt-IPFS-Client abgedeckt
              results.Infura = { success: true };
              logger.debug(`Datei mit CID ${cid} bereits auf Infura gepinnt`);
              break;
              
            default:
              logger.warn(`Unbekannter Pinning-Service: ${service}`);
          }
        } catch (error) {
          logger.error(`Fehler beim Pinnen auf ${service}`, {
            error: error.message,
            cid
          });
          results[service] = { success: false, error: error.message };
        }
      }
      
      return results;
    } catch (error) {
      logger.error('Fehler beim Pinnen auf externen Services', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Generiert eine URL für den Zugriff auf die Datei
   * @param {string} cid - Content-Identifikator (CID)
   * @param {Object} options - URL-Optionen
   * @param {string} options.gateway - Zu verwendender IPFS-Gateway
   * @returns {Promise<string>} URL für den Zugriff auf die Datei
   */
  async getAccessUrl(cid, options = {}) {
    const gateway = options.gateway || 'https://ipfs.io/ipfs/';
    return `${gateway}${cid}`;
  }
}/**
 * @fileoverview IPFS-Adapter für dezentrale Metadatenspeicherung
 * 
 * Dieser Adapter implementiert das StorageAdapter-Interface für IPFS.
 * Er wird hauptsächlich für die Speicherung von Metadaten und kleineren Dateien verwendet.
 */

import { create as createIPFS } from 'ipfs-http-client';
import { StorageAdapter } from './StorageAdapter.js';
import { logger } from '../../utils/logger.js';

/**
 * IPFS-Adapter für dezentrale Metadatenspeicherung
 */
export class IPFSAdapter extends StorageAdapter {
  /**
   * Erstellt eine neue Instanz des IPFSAdapter
   * @param {Object} options - Konfigurationsoptionen
   * @param {string} options.apiUrl - URL des IPFS-API (z.B. 'https://ipfs.infura.io:5001')
   * @param {string} options.apiKey - API-Schlüssel (falls erforderlich)
   * @param {string} options.apiSecret - API-Secret (falls erforderlich)
   * @param {Array<string>} options.pinningServices - Liste der Pinning-Services
   * @param {Object} options.pinningConfig - Konfiguration für Pinning-Services
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      apiUrl: options.apiUrl || process.env.IPFS_API_URL || 'https://ipfs.infura.io:5001',
      apiKey: options.apiKey || process.env.IPFS_API_KEY,
      apiSecret: options.apiSecret || process.env.IPFS_API_SECRET,
      pinningServices: options.pinningServices || ['Pinata', 'Infura', 'Web3.Storage'],
      pinningConfig: options.pinningConfig || {},
      ...options
    };
    
    this.client = null;
    this.pinningClients = {};
    
    logger.info('IPFSAdapter initialisiert');
  }
  
  /**
   * Initialisiert den IPFS-Client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      logger.info('Initialisiere IPFS-Client');
      
      // Konfiguriere den IPFS-Client
      const auth = this.options.apiKey && this.options.apiSecret
        ? 'Basic ' + Buffer.from(this.options.apiKey + ':' + this.options.apiSecret).toString('base64')
        : undefined;
      
      this.client = createIPFS({
        url: this.options.apiUrl,
        headers: {
          authorization: auth
        }
      });
      
      // Initialisiere Pinning-Services
      await this.initializePinningServices();
      
      logger.info('IPFS-Client erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des IPFS-Clients', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Initialisiert die Pinning-Services
   * @returns {Promise<void>}
   */
  async initializePinningServices() {
    try {
      for (const service of this.options.pinningServices) {
        const config = this.options.pinningConfig[service] || {};
        
        switch (service) {
          case 'Pinata':
            // Pinata-Client initialisieren
            if (config.apiKey && config.apiSecret) {
              const PinataSDK = await import('pinata-sdk');
              this.pinningClients.Pinata = PinataSDK.default(config.apiKey, config.apiSecret);
              logger.debug('Pinata-Client initialisiert');
            }
            break;
            
          case 'Web3.Storage':
            // Web3.Storage-Client initialisieren
            if (config.token) {
              const { Web3Storage } = await import('web3.storage');
              this.pinningClients.Web3Storage = new Web3Storage({ token: config.token });
              logger.debug('Web3.Storage-Client initialisiert');
            }
            break;
            
          case 'Infura':
            // Infura ist bereits durch den Haupt-IPFS-Client abgedeckt
            logger.debug('Infura-Client bereits initialisiert');
            break;
            
          default:
            logger.warn(`Unbekannter Pinning-Service: ${service}`);
        }
      }
    } catch (error) {
      logger.error('Fehler bei der Initialisierung der Pinning-Services', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Speichert eine Datei auf IPFS
   * @param {Buffer|string} data - Zu speichernde Daten
   * @param {Object} options - Speicheroptionen
   * @param {string} options.filename - Dateiname
   * @param {boolean} options.pin - Ob die Datei gepinnt werden soll
   * @param {Array<string>} options.pinningServices - Zu verwendende Pinning-Services
   * @returns {Promise<Object>} IPFS-Speicherinformationen
   */
  async storeFile(data, options = {}) {
    try {
      logger.info('Speichere Datei auf IPFS');
      
      // Erstelle ein File-Objekt
      const file = {
        path: options.filename || 'file',
        content: data
      };
      
      // Füge die Datei zu IPFS hinzu
      const result = await this.client.add(file, {
        pin: options.pin !== undefined ? options.pin : true
      });
      
      logger.debug(`Datei auf IPFS gespeichert mit CID ${result.cid.toString()}`);
      
      // Pinne die Datei auf den angegebenen Services
      if (options.pin !== false) {
        const pinningServices = options.pinningServices || this.options.pinningServices;
        await this.pinToServices(result.cid.toString(), pinningServices);
      }
      
      return {
        cid: result.cid.toString(),
        size: result.size,
        path: result.path,
        uri: `ipfs://${result.cid.toString()}`,
        url: `https://ipfs.io/ipfs/${result.cid.toString()}`
      };
    } catch (error) {
      logger.error('Fehler beim Speichern der Datei auf IPFS', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Speichert JSON-Daten auf IPFS
   * @param {Object} data - Zu speichernde JSON-Daten
   * @param {Object} options - Speicheroptionen
   * @param {boolean} options.pin - Ob die Daten gepinnt werden sollen
   * @param {Array<string>} options.pinningServices - Zu verwendende Pinning-Services
   * @returns {Promise<Object>} IPFS-Speicherinformationen
   */
  async storeJson(data, options = {}) {
    try {
      logger.info('Speichere JSON-Daten auf IPFS');
      
      // Konvertiere die JSON-Daten in einen String
      const jsonString = JSON.stringify(data);
      
      // Speichere die JSON-Daten als Datei
      return await this.storeFile(Buffer.from(jsonString), {
        filename: options.filename || 'data.json',
        pin: options.pin,
        pinningServices: options.pinningServices
      });
    } catch (error) {
      logger.error('Fehler beim Speichern der JSON-Daten auf IPFS', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Datei von IPFS ab
   * @param {string} cid - Content-Identifikator (CID)
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Buffer>} Abgerufene Daten
   */
  async retrieveFile(cid, options = {}) {
    try {
      logger.info(`Rufe Datei mit CID ${cid} von IPFS ab`);
      
      // Hole die Datei von IPFS
      const chunks = [];
      for await (const chunk of this.client.cat(cid)) {
        chunks.push(chunk);
      }
      
      // Kombiniere die Chunks zu einem Buffer
      const data = Buffer.concat(chunks);
      
      logger.debug(`Datei mit CID ${cid} erfolgreich abgerufen`);
      
      return data;
    } catch (error) {
      logger.error('Fehler beim Abrufen der Datei von IPFS', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Ruft JSON-Daten von IPFS ab
   * @param {string} cid - Content-Identifikator (CID)
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Object>} Abgerufene JSON-Daten
   */
  async retrieveJson(cid, options = {}) {
    try {
      logger.info(`Rufe JSON-Daten mit CID ${cid} von IPFS ab`);
      
      // Hole die Datei von IPFS
      const data = await this.retrieveFile(cid, options);
      
      // Konvertiere die Daten in ein JSON-Objekt
      const jsonData = JSON.parse(data.toString());
      
      logger.debug(`JSON-Daten mit CID ${cid} erfolgreich abgerufen`);
      
      return jsonData;
    } catch (error) {
      logger.error('Fehler beim Abrufen der JSON-Daten von IPFS', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Datei auf IPFS existiert
   * @param {string} cid - Content-Identifikator (CID)
   * @param {Object} options - Prüfoptionen
   * @returns {Promise<boolean>} True, wenn die Datei existiert
   */
  async exists(cid, options = {}) {
    try {
      logger.info(`Prüfe, ob Datei mit CID ${cid} auf IPFS existiert`);
      
      // Versuche, die Datei abzurufen (nur die Header)
      for await (const _ of this.client.ls(cid)) {
        return true;
      }
      
      return false;
    } catch (error) {
      logger.debug(`Datei mit CID ${cid} existiert nicht auf IPFS`);
      return false;
    }
  }
  
  /**
   * Pinnt eine Datei auf IPFS
   * @param {string} cid - Content-Identifikator (CID)
   * @param {Object} options - Pin-Optionen
   * @returns {Promise<boolean>} True, wenn die Datei erfolgreich gepinnt wurde
   */
  async pin(cid, options = {}) {
    try {
      logger.info(`Pinne Datei mit CID ${cid} auf IPFS`);
      
      // Pinne die Datei auf IPFS
      await this.client.pin.add(cid);
      
      logger.debug(`Datei mit CID ${cid} erfolgreich gepinnt`);
      
      return true;
    } catch (error) {
      logger.error('Fehler beim Pinnen der Datei auf IPFS', {
        error: error.message,
        cid
      });
      return false;
    }
  }
  
  /**
   * Pinnt eine Datei auf verschiedenen Pinning-Services
   * @param {string} cid - Content-Identifikator (CID)
   * @param {Array<string>} services - Zu verwendende Pinning-Services
   * @returns {Promise<Object>} Ergebnis des Pinning-Vorgangs
   */
  async pinToServices(cid, services = []) {
    try {
      logger.info(`Pinne Datei mit CID ${cid} auf externen Services`);
      
      const results = {};
      
      for (const service of services) {
        try {
          switch (service) {
            case 'Pinata':
              if (this.pinningClients.Pinata) {
                const result = await this.pinningClients.Pinata.pinByHash(cid);
                results.Pinata = { success: true, id: result.id };
                logger.debug(`Datei mit CID ${cid} erfolgreich auf Pinata gepinnt`);
              }
              break;
              
            case 'Web3.Storage':
              // Web3.Storage unterstützt kein direktes Pinning nach CID
              logger.debug(`Web3.Storage unterstützt kein direktes Pinning nach CID`);
              break;
              
            case 'Infura':
              // Infura ist bereits durch den Haupt-IPFS-Client abgedeckt
              results.Infura = { success: true };
              logger.debug(`Datei mit CID ${cid} bereits auf Infura gepinnt`);
              break;
              
            default:
              logger.warn(`Unbekannter Pinning-Service: ${service}`);
          }
        } catch (error) {
          logger.error(`Fehler beim Pinnen auf ${service}`, {
            error: error.message,
            cid
          });
          results[service] = { success: false, error: error.message };
        }
      }
      
      return results;
    } catch (error) {
      logger.error('Fehler beim Pinnen auf externen Services', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Generiert eine URL für den Zugriff auf die Datei
   * @param {string} cid - Content-Identifikator (CID)
   * @param {Object} options - URL-Optionen
   * @param {string} options.gateway - Zu verwendender IPFS-Gateway
   * @returns {Promise<string>} URL für den Zugriff auf die Datei
   */
  async getAccessUrl(cid, options = {}) {
    const gateway = options.gateway || 'https://ipfs.io/ipfs/';
    return `${gateway}${cid}`;
  }
}/**
 * @fileoverview IPFS-Adapter für dezentrale Metadatenspeicherung
 * 
 * Dieser Adapter implementiert das StorageAdapter-Interface für IPFS.
 * Er wird hauptsächlich für die Speicherung von Metadaten und kleineren Dateien verwendet.
 */

import { create as createIPFS } from 'ipfs-http-client';
import { StorageAdapter } from './StorageAdapter.js';
import { logger } from '../../utils/logger.js';

/**
 * IPFS-Adapter für dezentrale Metadatenspeicherung
 */
export class IPFSAdapter extends StorageAdapter {
  /**
   * Erstellt eine neue Instanz des IPFSAdapter
   * @param {Object} options - Konfigurationsoptionen
   * @param {string} options.apiUrl - URL des IPFS-API (z.B. 'https://ipfs.infura.io:5001')
   * @param {string} options.apiKey - API-Schlüssel (falls erforderlich)
   * @param {string} options.apiSecret - API-Secret (falls erforderlich)
   * @param {Array<string>} options.pinningServices - Liste der Pinning-Services
   * @param {Object} options.pinningConfig - Konfiguration für Pinning-Services
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      apiUrl: options.apiUrl || process.env.IPFS_API_URL || 'https://ipfs.infura.io:5001',
      apiKey: options.apiKey || process.env.IPFS_API_KEY,
      apiSecret: options.apiSecret || process.env.IPFS_API_SECRET,
      pinningServices: options.pinningServices || ['Pinata', 'Infura', 'Web3.Storage'],
      pinningConfig: options.pinningConfig || {},
      ...options
    };
    
    this.client = null;
    this.pinningClients = {};
    
    logger.info('IPFSAdapter initialisiert');
  }
  
  /**
   * Initialisiert den IPFS-Client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      logger.info('Initialisiere IPFS-Client');
      
      // Konfiguriere den IPFS-Client
      const auth = this.options.apiKey && this.options.apiSecret
        ? 'Basic ' + Buffer.from(this.options.apiKey + ':' + this.options.apiSecret).toString('base64')
        : undefined;
      
      this.client = createIPFS({
        url: this.options.apiUrl,
        headers: {
          authorization: auth
        }
      });
      
      // Initialisiere Pinning-Services
      await this.initializePinningServices();
      
      logger.info('IPFS-Client erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des IPFS-Clients', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Initialisiert die Pinning-Services
   * @returns {Promise<void>}
   */
  async initializePinningServices() {
    try {
      for (const service of this.options.pinningServices) {
        const config = this.options.pinningConfig[service] || {};
        
        switch (service) {
          case 'Pinata':
            // Pinata-Client initialisieren
            if (config.apiKey && config.apiSecret) {
              const PinataSDK = await import('pinata-sdk');
              this.pinningClients.Pinata = PinataSDK.default(config.apiKey, config.apiSecret);
              logger.debug('Pinata-Client initialisiert');
            }
            break;
            
          case 'Web3.Storage':
            // Web3.Storage-Client initialisieren
            if (config.token) {
              const { Web3Storage } = await import('web3.storage');
              this.pinningClients.Web3Storage = new Web3Storage({ token: config.token });
              logger.debug('Web3.Storage-Client initialisiert');
            }
            break;
            
          case 'Infura':
            // Infura ist bereits durch den Haupt-IPFS-Client abgedeckt
            logger.debug('Infura-Client bereits initialisiert');
            break;
            
          default:
            logger.warn(`Unbekannter Pinning-Service: ${service}`);
        }
      }
    } catch (error) {
      logger.error('Fehler bei der Initialisierung der Pinning-Services', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Speichert eine Datei auf IPFS
   * @param {Buffer|string} data - Zu speichernde Daten
   * @param {Object} options - Speicheroptionen
   * @param {string} options.filename - Dateiname
   * @param {boolean} options.pin - Ob die Datei gepinnt werden soll
   * @param {Array<string>} options.pinningServices - Zu verwendende Pinning-Services
   * @returns {Promise<Object>} IPFS-Speicherinformationen
   */
  async storeFile(data, options = {}) {
    try {
      logger.info('Speichere Datei auf IPFS');
      
      // Erstelle ein File-Objekt
      const file = {
        path: options.filename || 'file',
        content: data
      };
      
      // Füge die Datei zu IPFS hinzu
      const result = await this.client.add(file, {
        pin: options.pin !== undefined ? options.pin : true
      });
      
      logger.debug(`Datei auf IPFS gespeichert mit CID ${result.cid.toString()}`);
      
      // Pinne die Datei auf den angegebenen Services
      if (options.pin !== false) {
        const pinningServices = options.pinningServices || this.options.pinningServices;
        await this.pinToServices(result.cid.toString(), pinningServices);
      }
      
      return {
        cid: result.cid.toString(),
        size: result.size,
        path: result.path,
        uri: `ipfs://${result.cid.toString()}`,
        url: `https://ipfs.io/ipfs/${result.cid.toString()}`
      };
    } catch (error) {
      logger.error('Fehler beim Speichern der Datei auf IPFS', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Speichert JSON-Daten auf IPFS
   * @param {Object} data - Zu speichernde JSON-Daten
   * @param {Object} options - Speicheroptionen
   * @param {boolean} options.pin - Ob die Daten gepinnt werden sollen
   * @param {Array<string>} options.pinningServices - Zu verwendende Pinning-Services
   * @returns {Promise<Object>} IPFS-Speicherinformationen
   */
  async storeJson(data, options = {}) {
    try {
      logger.info('Speichere JSON-Daten auf IPFS');
      
      // Konvertiere die JSON-Daten in einen String
      const jsonString = JSON.stringify(data);
      
      // Speichere die JSON-Daten als Datei
      return await this.storeFile(Buffer.from(jsonString), {
        filename: options.filename || 'data.json',
        pin: options.pin,
        pinningServices: options.pinningServices
      });
    } catch (error) {
      logger.error('Fehler beim Speichern der JSON-Daten auf IPFS', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Datei von IPFS ab
   * @param {string} cid - Content-Identifikator (CID)
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Buffer>} Abgerufene Daten
   */
  async retrieveFile(cid, options = {}) {
    try {
      logger.info(`Rufe Datei mit CID ${cid} von IPFS ab`);
      
      // Hole die Datei von IPFS
      const chunks = [];
      for await (const chunk of this.client.cat(cid)) {
        chunks.push(chunk);
      }
      
      // Kombiniere die Chunks zu einem Buffer
      const data = Buffer.concat(chunks);
      
      logger.debug(`Datei mit CID ${cid} erfolgreich abgerufen`);
      
      return data;
    } catch (error) {
      logger.error('Fehler beim Abrufen der Datei von IPFS', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Ruft JSON-Daten von IPFS ab
   * @param {string} cid - Content-Identifikator (CID)
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Object>} Abgerufene JSON-Daten
   */
  async retrieveJson(cid, options = {}) {
    try {
      logger.info(`Rufe JSON-Daten mit CID ${cid} von IPFS ab`);
      
      // Hole die Datei von IPFS
      const data = await this.retrieveFile(cid, options);
      
      // Konvertiere die Daten in ein JSON-Objekt
      const jsonData = JSON.parse(data.toString());
      
      logger.debug(`JSON-Daten mit CID ${cid} erfolgreich abgerufen`);
      
      return jsonData;
    } catch (error) {
      logger.error('Fehler beim Abrufen der JSON-Daten von IPFS', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Datei auf IPFS existiert
   * @param {string} cid - Content-Identifikator (CID)
   * @param {Object} options - Prüfoptionen
   * @returns {Promise<boolean>} True, wenn die Datei existiert
   */
  async exists(cid, options = {}) {
    try {
      logger.info(`Prüfe, ob Datei mit CID ${cid} auf IPFS existiert`);
      
      // Versuche, die Datei abzurufen (nur die Header)
      for await (const _ of this.client.ls(cid)) {
        return true;
      }
      
      return false;
    } catch (error) {
      logger.debug(`Datei mit CID ${cid} existiert nicht auf IPFS`);
      return false;
    }
  }
  
  /**
   * Pinnt eine Datei auf IPFS
   * @param {string} cid - Content-Identifikator (CID)
   * @param {Object} options - Pin-Optionen
   * @returns {Promise<boolean>} True, wenn die Datei erfolgreich gepinnt wurde
   */
  async pin(cid, options = {}) {
    try {
      logger.info(`Pinne Datei mit CID ${cid} auf IPFS`);
      
      // Pinne die Datei auf IPFS
      await this.client.pin.add(cid);
      
      logger.debug(`Datei mit CID ${cid} erfolgreich gepinnt`);
      
      return true;
    } catch (error) {
      logger.error('Fehler beim Pinnen der Datei auf IPFS', {
        error: error.message,
        cid
      });
      return false;
    }
  }
  
  /**
   * Pinnt eine Datei auf verschiedenen Pinning-Services
   * @param {string} cid - Content-Identifikator (CID)
   * @param {Array<string>} services - Zu verwendende Pinning-Services
   * @returns {Promise<Object>} Ergebnis des Pinning-Vorgangs
   */
  async pinToServices(cid, services = []) {
    try {
      logger.info(`Pinne Datei mit CID ${cid} auf externen Services`);
      
      const results = {};
      
      for (const service of services) {
        try {
          switch (service) {
            case 'Pinata':
              if (this.pinningClients.Pinata) {
                const result = await this.pinningClients.Pinata.pinByHash(cid);
                results.Pinata = { success: true, id: result.id };
                logger.debug(`Datei mit CID ${cid} erfolgreich auf Pinata gepinnt`);
              }
              break;
              
            case 'Web3.Storage':
              // Web3.Storage unterstützt kein direktes Pinning nach CID
              logger.debug(`Web3.Storage unterstützt kein direktes Pinning nach CID`);
              break;
              
            case 'Infura':
              // Infura ist bereits durch den Haupt-IPFS-Client abgedeckt
              results.Infura = { success: true };
              logger.debug(`Datei mit CID ${cid} bereits auf Infura gepinnt`);
              break;
              
            default:
              logger.warn(`Unbekannter Pinning-Service: ${service}`);
          }
        } catch (error) {
          logger.error(`Fehler beim Pinnen auf ${service}`, {
            error: error.message,
            cid
          });
          results[service] = { success: false, error: error.message };
        }
      }
      
      return results;
    } catch (error) {
      logger.error('Fehler beim Pinnen auf externen Services', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Generiert eine URL für den Zugriff auf die Datei
   * @param {string} cid - Content-Identifikator (CID)
   * @param {Object} options - URL-Optionen
   * @param {string} options.gateway - Zu verwendender IPFS-Gateway
   * @returns {Promise<string>} URL für den Zugriff auf die Datei
   */
  async getAccessUrl(cid, options = {}) {
    const gateway = options.gateway || 'https://ipfs.io/ipfs/';
    return `${gateway}${cid}`;
  }
}