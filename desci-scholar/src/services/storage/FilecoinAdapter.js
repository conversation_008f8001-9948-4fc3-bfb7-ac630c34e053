/**
 * @fileoverview Filecoin-Adapter für langfristige verifizierbare Speicherung
 * 
 * Dieser Adapter implementiert das StorageAdapter-Interface für Filecoin.
 * Er wird hauptsächlich für die langfristige Speicherung großer Forschungsdatasets verwendet
 * und bietet kryptografisch verifizierbare Speichergarantien durch wirtschaftliche Anreize.
 */

import { StorageAdapter } from './StorageAdapter.js';
import { logger } from '../../utils/logger.js';

/**
 * Filecoin-Adapter für langfristige verifizierbare Speicherung
 */
export class FilecoinAdapter extends StorageAdapter {
  /**
   * Erstellt eine neue Instanz des FilecoinAdapter
   * @param {Object} options - Konfigurationsoptionen
   * @param {string} options.apiKey - API-Schlüssel für Web3.Storage
   * @param {string} options.endpoint - API-Endpunkt
   * @param {number} options.defaultReplication - Standardanzahl der Replikationen
   * @param {number} options.defaultDealDuration - Standarddauer der Speicherdeals in Tagen
   * @param {boolean} options.useEstuary - Estuary.tech als Alternative zu Web3.Storage verwenden
   * @param {string} options.estuaryApiKey - API-Schlüssel für Estuary
   * @param {string} options.estuaryEndpoint - Estuary API-Endpunkt
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      apiKey: options.apiKey || process.env.FILECOIN_API_KEY,
      endpoint: options.endpoint || process.env.FILECOIN_ENDPOINT || 'https://api.web3.storage',
      defaultReplication: options.defaultReplication || 5,
      defaultDealDuration: options.defaultDealDuration || 525600, // 1 Jahr in Minuten
      useEstuary: options.useEstuary || process.env.USE_ESTUARY === 'true' || false,
      estuaryApiKey: options.estuaryApiKey || process.env.ESTUARY_API_KEY,
      estuaryEndpoint: options.estuaryEndpoint || process.env.ESTUARY_ENDPOINT || 'https://api.estuary.tech',
      ...options
    };
    
    this.web3StorageClient = null;
    this.estuaryClient = null;
    this.isInitialized = false;
    
    logger.info('FilecoinAdapter initialisiert');
  }
  
  /**
   * Initialisiert den Filecoin-Client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      logger.info('Initialisiere Filecoin-Client');
      
      if (this.options.useEstuary) {
        // Initialisiere Estuary-Client
        await this.initializeEstuaryClient();
      } else {
        // Initialisiere Web3.Storage-Client
        await this.initializeWeb3StorageClient();
      }
      
      this.isInitialized = true;
      
      logger.info('Filecoin-Client erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des Filecoin-Clients', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Initialisiert den Web3.Storage-Client
   * @returns {Promise<void>}
   */
  async initializeWeb3StorageClient() {
    try {
      logger.debug('Initialisiere Web3.Storage-Client');
      
      if (!this.options.apiKey) {
        throw new Error('API-Schlüssel für Web3.Storage ist erforderlich');
      }
      
      // Importiere den Web3.Storage-Client
      const { Web3Storage } = await import('web3.storage');
      
      // Erstelle den Client
      this.web3StorageClient = new Web3Storage({ 
        token: this.options.apiKey,
        endpoint: new URL(this.options.endpoint)
      });
      
      logger.debug('Web3.Storage-Client erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des Web3.Storage-Clients', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Initialisiert den Estuary-Client
   * @returns {Promise<void>}
   */
  async initializeEstuaryClient() {
    try {
      logger.debug('Initialisiere Estuary-Client');
      
      if (!this.options.estuaryApiKey) {
        throw new Error('API-Schlüssel für Estuary ist erforderlich');
      }
      
      // Erstelle einen einfachen Wrapper für die Estuary API
      this.estuaryClient = {
        apiKey: this.options.estuaryApiKey,
        endpoint: this.options.estuaryEndpoint,
        
        // Methode zum Senden von Anfragen an die Estuary API
        async request(path, options = {}) {
          const url = `${this.endpoint}${path}`;
          const headers = {
            'Authorization': `Bearer ${this.apiKey}`,
            ...options.headers
          };
          
          const response = await fetch(url, {
            ...options,
            headers
          });
          
          if (!response.ok) {
            const error = await response.text();
            throw new Error(`Estuary API-Fehler: ${error}`);
          }
          
          return response.json();
        }
      };
      
      // Teste die Verbindung
      await this.estuaryClient.request('/user/stats');
      
      logger.debug('Estuary-Client erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des Estuary-Clients', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Speichert eine Datei auf Filecoin
   * @param {Buffer|string|Blob|File} data - Zu speichernde Daten
   * @param {Object} options - Speicheroptionen
   * @param {string} options.name - Dateiname
   * @param {string} options.contentType - MIME-Typ der Datei
   * @param {number} options.replication - Anzahl der Replikationen
   * @param {number} options.dealDuration - Dauer der Speicherdeals in Tagen
   * @param {Object} options.metadata - Zusätzliche Metadaten
   * @returns {Promise<Object>} Filecoin-Speicherinformationen
   */
  async storeFile(data, options = {}) {
    try {
      logger.info('Speichere Datei auf Filecoin');
      
      // Prüfe, ob der Client initialisiert wurde
      if (!this.isInitialized) {
        await this.initialize();
      }
      
      // Setze Standardoptionen
      const name = options.name || `file-${Date.now()}`;
      const contentType = options.contentType || 'application/octet-stream';
      const replication = options.replication || this.options.defaultReplication;
      const dealDuration = options.dealDuration || this.options.defaultDealDuration;
      
      let cid;
      let size;
      
      if (this.options.useEstuary) {
        // Speichere die Datei mit Estuary
        const result = await this.storeWithEstuary(data, {
          name,
          contentType,
          replication,
          dealDuration,
          metadata: options.metadata
        });
        
        cid = result.cid;
        size = result.size;
      } else {
        // Speichere die Datei mit Web3.Storage
        const result = await this.storeWithWeb3Storage(data, {
          name,
          contentType,
          metadata: options.metadata
        });
        
        cid = result.cid;
        size = result.size;
      }
      
      logger.debug(`Datei auf Filecoin gespeichert mit CID ${cid}`);
      
      return {
        id: cid,
        cid,
        size,
        name,
        contentType,
        uri: `ipfs://${cid}`,
        url: `https://ipfs.io/ipfs/${cid}`,
        gateway: `https://dweb.link/ipfs/${cid}`,
        filecoin: true,
        replication,
        dealDuration,
        timestamp: Date.now()
      };
    } catch (error) {
      logger.error('Fehler beim Speichern der Datei auf Filecoin', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Speichert eine Datei mit Web3.Storage
   * @param {Buffer|string|Blob|File} data - Zu speichernde Daten
   * @param {Object} options - Speicheroptionen
   * @returns {Promise<Object>} Speicherinformationen
   */
  async storeWithWeb3Storage(data, options = {}) {
    try {
      logger.debug('Speichere Datei mit Web3.Storage');
      
      // Konvertiere die Daten in ein File-Objekt
      let file;
      
      if (data instanceof Blob || data instanceof File) {
        // Verwende die Datei direkt
        file = new File([data], options.name, { type: options.contentType });
      } else if (Buffer.isBuffer(data) || typeof data === 'string') {
        // Konvertiere Buffer oder String zu File
        const buffer = Buffer.isBuffer(data) ? data : Buffer.from(data);
        file = new File([buffer], options.name, { type: options.contentType });
      } else {
        throw new Error('Nicht unterstützter Datentyp');
      }
      
      // Füge Metadaten hinzu, falls vorhanden
      let files = [file];
      
      if (options.metadata) {
        const metadataFile = new File(
          [JSON.stringify(options.metadata)],
          `${options.name}.metadata.json`,
          { type: 'application/json' }
        );
        files.push(metadataFile);
      }
      
      // Speichere die Datei
      const cid = await this.web3StorageClient.put(files, {
        name: options.name,
        wrapWithDirectory: true
      });
      
      // Hole die Größe der Datei
      const size = file.size;
      
      logger.debug(`Datei mit Web3.Storage gespeichert: ${cid}`);
      
      return { cid, size };
    } catch (error) {
      logger.error('Fehler beim Speichern mit Web3.Storage', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Speichert eine Datei mit Estuary
   * @param {Buffer|string|Blob|File} data - Zu speichernde Daten
   * @param {Object} options - Speicheroptionen
   * @returns {Promise<Object>} Speicherinformationen
   */
  async storeWithEstuary(data, options = {}) {
    try {
      logger.debug('Speichere Datei mit Estuary');
      
      // Konvertiere die Daten in ein FormData-Objekt
      const formData = new FormData();
      
      if (data instanceof Blob || data instanceof File) {
        // Verwende die Datei direkt
        formData.append('data', data, options.name);
      } else if (Buffer.isBuffer(data) || typeof data === 'string') {
        // Konvertiere Buffer oder String zu Blob
        const buffer = Buffer.isBuffer(data) ? data : Buffer.from(data);
        const blob = new Blob([buffer], { type: options.contentType });
        formData.append('data', blob, options.name);
      } else {
        throw new Error('Nicht unterstützter Datentyp');
      }
      
      // Füge Metadaten hinzu
      if (options.metadata) {
        formData.append('metadata', JSON.stringify(options.metadata));
      }
      
      // Speichere die Datei
      const response = await this.estuaryClient.request('/content/add', {
        method: 'POST',
        body: formData
      });
      
      // Erstelle Speicherdeals
      await this.estuaryClient.request(`/content/deal/${response.cid}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          replication: options.replication,
          durationBlks: options.dealDuration,
          verified: true
        })
      });
      
      logger.debug(`Datei mit Estuary gespeichert: ${response.cid}`);
      
      return { 
        cid: response.cid,
        size: response.size,
        estuaryId: response.estuaryId
      };
    } catch (error) {
      logger.error('Fehler beim Speichern mit Estuary', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Speichert JSON-Daten auf Filecoin
   * @param {Object} data - Zu speichernde JSON-Daten
   * @param {Object} options - Speicheroptionen
   * @returns {Promise<Object>} Filecoin-Speicherinformationen
   */
  async storeJson(data, options = {}) {
    try {
      logger.info('Speichere JSON-Daten auf Filecoin');
      
      // Konvertiere die JSON-Daten in einen String
      const jsonString = JSON.stringify(data);
      
      // Speichere die JSON-Daten als Datei
      return await this.storeFile(jsonString, {
        name: options.name || `data-${Date.now()}.json`,
        contentType: 'application/json',
        ...options
      });
    } catch (error) {
      logger.error('Fehler beim Speichern der JSON-Daten auf Filecoin', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Datei von Filecoin/IPFS ab
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Buffer>} Abgerufene Daten
   */
  async retrieveFile(cid, options = {}) {
    try {
      logger.info(`Rufe Datei mit CID ${cid} von Filecoin/IPFS ab`);
      
      // Prüfe, ob der Client initialisiert wurde
      if (!this.isInitialized) {
        await this.initialize();
      }
      
      let data;
      
      if (this.options.useEstuary) {
        // Rufe die Datei von Estuary ab
        data = await this.retrieveFromEstuary(cid, options);
      } else {
        // Rufe die Datei von Web3.Storage ab
        data = await this.retrieveFromWeb3Storage(cid, options);
      }
      
      logger.debug(`Datei mit CID ${cid} erfolgreich abgerufen`);
      
      return data;
    } catch (error) {
      logger.error('Fehler beim Abrufen der Datei von Filecoin/IPFS', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Datei von Web3.Storage ab
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Buffer>} Abgerufene Daten
   */
  async retrieveFromWeb3Storage(cid, options = {}) {
    try {
      logger.debug(`Rufe Datei mit CID ${cid} von Web3.Storage ab`);
      
      // Hole die Datei von Web3.Storage
      const res = await this.web3StorageClient.get(cid);
      
      if (!res.ok) {
        throw new Error(`Fehler beim Abrufen der Datei: ${res.statusText}`);
      }
      
      // Extrahiere die Dateien
      const files = await res.files();
      
      if (files.length === 0) {
        throw new Error('Keine Dateien gefunden');
      }
      
      // Hole die Hauptdatei (ignoriere Metadaten-Dateien)
      let mainFile;
      
      if (options.filename) {
        // Suche nach der angegebenen Datei
        mainFile = files.find(file => file.name === options.filename);
        
        if (!mainFile) {
          throw new Error(`Datei ${options.filename} nicht gefunden`);
        }
      } else {
        // Verwende die erste Datei, die keine Metadaten-Datei ist
        mainFile = files.find(file => !file.name.endsWith('.metadata.json'));
        
        if (!mainFile) {
          // Verwende die erste Datei
          mainFile = files[0];
        }
      }
      
      // Lese die Datei
      const arrayBuffer = await mainFile.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);
      
      logger.debug(`Datei mit CID ${cid} erfolgreich von Web3.Storage abgerufen`);
      
      return buffer;
    } catch (error) {
      logger.error('Fehler beim Abrufen der Datei von Web3.Storage', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Datei von Estuary ab
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Buffer>} Abgerufene Daten
   */
  async retrieveFromEstuary(cid, options = {}) {
    try {
      logger.debug(`Rufe Datei mit CID ${cid} von Estuary ab`);
      
      // Hole die Datei von Estuary
      const response = await fetch(`${this.options.estuaryEndpoint}/gw/ipfs/${cid}`);
      
      if (!response.ok) {
        throw new Error(`Fehler beim Abrufen der Datei: ${response.statusText}`);
      }
      
      // Lese die Datei
      const arrayBuffer = await response.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);
      
      logger.debug(`Datei mit CID ${cid} erfolgreich von Estuary abgerufen`);
      
      return buffer;
    } catch (error) {
      logger.error('Fehler beim Abrufen der Datei von Estuary', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Ruft JSON-Daten von Filecoin/IPFS ab
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Object>} Abgerufene JSON-Daten
   */
  async retrieveJson(cid, options = {}) {
    try {
      logger.info(`Rufe JSON-Daten mit CID ${cid} von Filecoin/IPFS ab`);
      
      // Hole die Datei von Filecoin/IPFS
      const data = await this.retrieveFile(cid, options);
      
      // Konvertiere die Daten in ein JSON-Objekt
      const jsonData = JSON.parse(data.toString());
      
      logger.debug(`JSON-Daten mit CID ${cid} erfolgreich abgerufen`);
      
      return jsonData;
    } catch (error) {
      logger.error('Fehler beim Abrufen der JSON-Daten von Filecoin/IPFS', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Datei auf Filecoin/IPFS existiert
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Prüfoptionen
   * @returns {Promise<boolean>} True, wenn die Datei existiert
   */
  async exists(cid, options = {}) {
    try {
      logger.info(`Prüfe, ob Datei mit CID ${cid} auf Filecoin/IPFS existiert`);
      
      // Prüfe, ob der Client initialisiert wurde
      if (!this.isInitialized) {
        await this.initialize();
      }
      
      let exists;
      
      if (this.options.useEstuary) {
        // Prüfe, ob die Datei auf Estuary existiert
        exists = await this.existsOnEstuary(cid, options);
      } else {
        // Prüfe, ob die Datei auf Web3.Storage existiert
        exists = await this.existsOnWeb3Storage(cid, options);
      }
      
      logger.debug(`Datei mit CID ${cid} existiert: ${exists}`);
      
      return exists;
    } catch (error) {
      logger.debug(`Datei mit CID ${cid} existiert nicht auf Filecoin/IPFS`);
      return false;
    }
  }
  
  /**
   * Prüft, ob eine Datei auf Web3.Storage existiert
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Prüfoptionen
   * @returns {Promise<boolean>} True, wenn die Datei existiert
   */
  async existsOnWeb3Storage(cid, options = {}) {
    try {
      logger.debug(`Prüfe, ob Datei mit CID ${cid} auf Web3.Storage existiert`);
      
      // Hole den Status der Datei
      const status = await this.web3StorageClient.status(cid);
      
      // Prüfe, ob die Datei existiert
      return !!status && status.cid === cid;
    } catch (error) {
      logger.debug(`Datei mit CID ${cid} existiert nicht auf Web3.Storage`);
      return false;
    }
  }
  
  /**
   * Prüft, ob eine Datei auf Estuary existiert
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Prüfoptionen
   * @returns {Promise<boolean>} True, wenn die Datei existiert
   */
  async existsOnEstuary(cid, options = {}) {
    try {
      logger.debug(`Prüfe, ob Datei mit CID ${cid} auf Estuary existiert`);
      
      // Versuche, die Datei-Informationen abzurufen
      const response = await this.estuaryClient.request(`/content/by-cid/${cid}`);
      
      // Prüfe, ob die Datei existiert
      return !!response && response.cid === cid;
    } catch (error) {
      logger.debug(`Datei mit CID ${cid} existiert nicht auf Estuary`);
      return false;
    }
  }
  
  /**
   * Löscht eine Datei (nicht möglich bei Filecoin/IPFS)
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Löschoptionen
   * @returns {Promise<boolean>} False, da Löschen nicht möglich ist
   */
  async delete(cid, options = {}) {
    logger.warn(`Löschen von Dateien ist bei Filecoin/IPFS nicht möglich (CID: ${cid})`);
    return false;
  }
  
  /**
   * Generiert eine URL für den Zugriff auf die Datei
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - URL-Optionen
   * @param {string} options.gateway - Zu verwendender IPFS-Gateway
   * @returns {Promise<string>} URL für den Zugriff auf die Datei
   */
  async getAccessUrl(cid, options = {}) {
    const gateway = options.gateway || 'https://ipfs.io/ipfs/';
    return `${gateway}${cid}`;
  }
  
  /**
   * Berechnet die Kosten für die Speicherung einer Datei
   * @param {Buffer|string} data - Zu speichernde Daten
   * @param {Object} options - Kostenberechnungsoptionen
   * @param {number} options.replication - Anzahl der Replikationen
   * @param {number} options.dealDuration - Dauer der Speicherdeals in Tagen
   * @returns {Promise<Object>} Kostendetails
   */
  async calculateStorageCost(data, options = {}) {
    try {
      logger.debug('Berechne Speicherkosten für Filecoin');
      
      // Prüfe, ob der Client initialisiert wurde
      if (!this.isInitialized) {
        await this.initialize();
      }
      
      // Konvertiere String zu Buffer, falls nötig
      const buffer = Buffer.isBuffer(data) ? data : Buffer.from(data);
      
      // Setze Standardoptionen
      const replication = options.replication || this.options.defaultReplication;
      const dealDuration = options.dealDuration || this.options.defaultDealDuration;
      
      // Berechne die Größe in Bytes
      const sizeInBytes = buffer.length;
      
      // Berechne die Größe in GiB (1 GiB = 1024^3 Bytes)
      const sizeInGiB = sizeInBytes / Math.pow(1024, 3);
      
      // Schätze die Kosten (basierend auf aktuellen Filecoin-Preisen)
      // Dies ist eine grobe Schätzung und sollte für genaue Werte durch eine API-Abfrage ersetzt werden
      const pricePerGiBPerEpoch = 0.0000000005; // FIL pro GiB pro Epoche (30 Sekunden)
      const epochsPerDay = 2880; // 30 Sekunden pro Epoche, 86400 Sekunden pro Tag
      const totalEpochs = dealDuration * epochsPerDay;
      
      // Berechne die Gesamtkosten
      const costInFil = sizeInGiB * pricePerGiBPerEpoch * totalEpochs * replication;
      
      return {
        bytes: sizeInBytes,
        sizeInGiB,
        replication,
        dealDuration,
        costInFil,
        currency: 'FIL'
      };
    } catch (error) {
      logger.error('Fehler bei der Berechnung der Speicherkosten', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft den Status eines Speicherdeals ab
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Statusoptionen
   * @returns {Promise<Object>} Dealstatus
   */
  async getDealStatus(cid, options = {}) {
    try {
      logger.info(`Rufe Status des Speicherdeals für CID ${cid} ab`);
      
      // Prüfe, ob der Client initialisiert wurde
      if (!this.isInitialized) {
        await this.initialize();
      }
      
      let status;
      
      if (this.options.useEstuary) {
        // Hole den Status von Estuary
        status = await this.getDealStatusFromEstuary(cid, options);
      } else {
        // Hole den Status von Web3.Storage
        status = await this.getDealStatusFromWeb3Storage(cid, options);
      }
      
      logger.debug(`Status des Speicherdeals für CID ${cid} abgerufen`);
      
      return status;
    } catch (error) {
      logger.error('Fehler beim Abrufen des Dealstatus', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Ruft den Status eines Speicherdeals von Web3.Storage ab
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Statusoptionen
   * @returns {Promise<Object>} Dealstatus
   */
  async getDealStatusFromWeb3Storage(cid, options = {}) {
    try {
      logger.debug(`Rufe Status des Speicherdeals für CID ${cid} von Web3.Storage ab`);
      
      // Hole den Status der Datei
      const status = await this.web3StorageClient.status(cid);
      
      if (!status) {
        throw new Error(`Keine Statusinformationen für CID ${cid} gefunden`);
      }
      
      // Extrahiere die Dealinformationen
      const deals = status.deals || [];
      
      return {
        cid,
        deals: deals.map(deal => ({
          dealId: deal.dealId,
          storageProvider: deal.storageProvider,
          status: deal.status,
          pieceCid: deal.pieceCid,
          dataCid: deal.dataCid,
          dataModelSelector: deal.dataModelSelector,
          activation: deal.activation,
          expiration: deal.expiration,
          created: deal.created,
          updated: deal.updated
        })),
        pins: status.pins || []
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen des Dealstatus von Web3.Storage', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Ruft den Status eines Speicherdeals von Estuary ab
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Statusoptionen
   * @returns {Promise<Object>} Dealstatus
   */
  async getDealStatusFromEstuary(cid, options = {}) {
    try {
      logger.debug(`Rufe Status des Speicherdeals für CID ${cid} von Estuary ab`);
      
      // Hole die Datei-Informationen
      const content = await this.estuaryClient.request(`/content/by-cid/${cid}`);
      
      if (!content) {
        throw new Error(`Keine Informationen für CID ${cid} gefunden`);
      }
      
      // Hole die Dealinformationen
      const deals = await this.estuaryClient.request(`/content/deals/${content.id}`);
      
      return {
        cid,
        estuaryId: content.id,
        name: content.name,
        size: content.size,
        deals: deals.map(deal => ({
          dealId: deal.dealId,
          miner: deal.miner,
          status: deal.status,
          pieceCid: deal.pieceCid,
          dataCid: deal.dataCid,
          activation: deal.activation,
          expiration: deal.expiration,
          created: deal.created,
          updated: deal.updated
        }))
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen des Dealstatus von Estuary', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Erstellt einen Speicherdeal für eine bereits auf IPFS gespeicherte Datei
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Dealoptionen
   * @param {number} options.replication - Anzahl der Replikationen
   * @param {number} options.dealDuration - Dauer der Speicherdeals in Tagen
   * @returns {Promise<Object>} Dealinformationen
   */
  async createDealForCid(cid, options = {}) {
    try {
      logger.info(`Erstelle Speicherdeal für CID ${cid}`);
      
      // Prüfe, ob der Client initialisiert wurde
      if (!this.isInitialized) {
        await this.initialize();
      }
      
      // Setze Standardoptionen
      const replication = options.replication || this.options.defaultReplication;
      const dealDuration = options.dealDuration || this.options.defaultDealDuration;
      
      let result;
      
      if (this.options.useEstuary) {
        // Erstelle einen Deal mit Estuary
        result = await this.createDealWithEstuary(cid, {
          replication,
          dealDuration
        });
      } else {
        // Web3.Storage erstellt automatisch Deals, daher ist keine explizite Aktion erforderlich
        // Wir können jedoch den Status abrufen
        result = await this.getDealStatusFromWeb3Storage(cid);
      }
      
      logger.debug(`Speicherdeal für CID ${cid} erstellt`);
      
      return result;
    } catch (error) {
      logger.error('Fehler beim Erstellen des Speicherdeals', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Erstellt einen Speicherdeal mit Estuary
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Dealoptionen
   * @returns {Promise<Object>} Dealinformationen
   */
  async createDealWithEstuary(cid, options = {}) {
    try {
      logger.debug(`Erstelle Speicherdeal für CID ${cid} mit Estuary`);
      
      // Importiere die Datei in Estuary, falls sie noch nicht dort ist
      let content;
      
      try {
        // Versuche, die Datei-Informationen abzurufen
        content = await this.estuaryClient.request(`/content/by-cid/${cid}`);
      } catch (error) {
        // Datei ist noch nicht in Estuary, importiere sie
        content = await this.estuaryClient.request('/content/add-ipfs', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            cid,
            name: options.name || `ipfs-${cid}`
          })
        });
      }
      
      // Erstelle Speicherdeals
      await this.estuaryClient.request(`/content/deal/${content.id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          replication: options.replication,
          durationBlks: options.dealDuration,
          verified: true
        })
      });
      
      // Hole die Dealinformationen
      const deals = await this.estuaryClient.request(`/content/deals/${content.id}`);
      
      logger.debug(`Speicherdeal für CID ${cid} mit Estuary erstellt`);
      
      return {
        cid,
        estuaryId: content.id,
        name: content.name,
        size: content.size,
        deals
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen des Speicherdeals mit Estuary', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Ruft Statistiken über die gespeicherten Daten ab
   * @returns {Promise<Object>} Speicherstatistiken
   */
  async getStorageStats() {
    try {
      logger.info('Rufe Speicherstatistiken ab');
      
      // Prüfe, ob der Client initialisiert wurde
      if (!this.isInitialized) {
        await this.initialize();
      }
      
      let stats;
      
      if (this.options.useEstuary) {
        // Hole Statistiken von Estuary
        stats = await this.getEstuaryStats();
      } else {
        // Hole Statistiken von Web3.Storage
        stats = await this.getWeb3StorageStats();
      }
      
      logger.debug('Speicherstatistiken abgerufen');
      
      return stats;
    } catch (error) {
      logger.error('Fehler beim Abrufen der Speicherstatistiken', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft Statistiken von Web3.Storage ab
   * @returns {Promise<Object>} Speicherstatistiken
   */
  async getWeb3StorageStats() {
    try {
      logger.debug('Rufe Statistiken von Web3.Storage ab');
      
      // Hole die Statistiken
      const info = await this.web3StorageClient.user();
      
      return {
        totalStorage: info.storageUsed || 0,
        totalUploads: info.numUploads || 0,
        storageLimit: info.storageLimit || 0,
        provider: 'web3.storage'
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen der Statistiken von Web3.Storage', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft Statistiken von Estuary ab
   * @returns {Promise<Object>} Speicherstatistiken
   */
  async getEstuaryStats() {
    try {
      logger.debug('Rufe Statistiken von Estuary ab');
      
      // Hole die Statistiken
      const stats = await this.estuaryClient.request('/user/stats');
      
      return {
        totalStorage: stats.totalStorage || 0,
        totalFiles: stats.totalFiles || 0,
        totalDeals: stats.totalDeals || 0,
        provider: 'estuary'
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen der Statistiken von Estuary', {
        error: error.message
      });
      throw error;
    }
  }
}/**
 * @fileoverview Filecoin-Adapter für langfristige verifizierbare Speicherung
 * 
 * Dieser Adapter implementiert das StorageAdapter-Interface für Filecoin.
 * Er wird hauptsächlich für die langfristige Speicherung großer Forschungsdatasets verwendet
 * und bietet kryptografisch verifizierbare Speichergarantien durch wirtschaftliche Anreize.
 */

import { StorageAdapter } from './StorageAdapter.js';
import { logger } from '../../utils/logger.js';

/**
 * Filecoin-Adapter für langfristige verifizierbare Speicherung
 */
export class FilecoinAdapter extends StorageAdapter {
  /**
   * Erstellt eine neue Instanz des FilecoinAdapter
   * @param {Object} options - Konfigurationsoptionen
   * @param {string} options.apiKey - API-Schlüssel für Web3.Storage
   * @param {string} options.endpoint - API-Endpunkt
   * @param {number} options.defaultReplication - Standardanzahl der Replikationen
   * @param {number} options.defaultDealDuration - Standarddauer der Speicherdeals in Tagen
   * @param {boolean} options.useEstuary - Estuary.tech als Alternative zu Web3.Storage verwenden
   * @param {string} options.estuaryApiKey - API-Schlüssel für Estuary
   * @param {string} options.estuaryEndpoint - Estuary API-Endpunkt
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      apiKey: options.apiKey || process.env.FILECOIN_API_KEY,
      endpoint: options.endpoint || process.env.FILECOIN_ENDPOINT || 'https://api.web3.storage',
      defaultReplication: options.defaultReplication || 5,
      defaultDealDuration: options.defaultDealDuration || 525600, // 1 Jahr in Minuten
      useEstuary: options.useEstuary || process.env.USE_ESTUARY === 'true' || false,
      estuaryApiKey: options.estuaryApiKey || process.env.ESTUARY_API_KEY,
      estuaryEndpoint: options.estuaryEndpoint || process.env.ESTUARY_ENDPOINT || 'https://api.estuary.tech',
      ...options
    };
    
    this.web3StorageClient = null;
    this.estuaryClient = null;
    this.isInitialized = false;
    
    logger.info('FilecoinAdapter initialisiert');
  }
  
  /**
   * Initialisiert den Filecoin-Client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      logger.info('Initialisiere Filecoin-Client');
      
      if (this.options.useEstuary) {
        // Initialisiere Estuary-Client
        await this.initializeEstuaryClient();
      } else {
        // Initialisiere Web3.Storage-Client
        await this.initializeWeb3StorageClient();
      }
      
      this.isInitialized = true;
      
      logger.info('Filecoin-Client erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des Filecoin-Clients', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Initialisiert den Web3.Storage-Client
   * @returns {Promise<void>}
   */
  async initializeWeb3StorageClient() {
    try {
      logger.debug('Initialisiere Web3.Storage-Client');
      
      if (!this.options.apiKey) {
        throw new Error('API-Schlüssel für Web3.Storage ist erforderlich');
      }
      
      // Importiere den Web3.Storage-Client
      const { Web3Storage } = await import('web3.storage');
      
      // Erstelle den Client
      this.web3StorageClient = new Web3Storage({ 
        token: this.options.apiKey,
        endpoint: new URL(this.options.endpoint)
      });
      
      logger.debug('Web3.Storage-Client erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des Web3.Storage-Clients', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Initialisiert den Estuary-Client
   * @returns {Promise<void>}
   */
  async initializeEstuaryClient() {
    try {
      logger.debug('Initialisiere Estuary-Client');
      
      if (!this.options.estuaryApiKey) {
        throw new Error('API-Schlüssel für Estuary ist erforderlich');
      }
      
      // Erstelle einen einfachen Wrapper für die Estuary API
      this.estuaryClient = {
        apiKey: this.options.estuaryApiKey,
        endpoint: this.options.estuaryEndpoint,
        
        // Methode zum Senden von Anfragen an die Estuary API
        async request(path, options = {}) {
          const url = `${this.endpoint}${path}`;
          const headers = {
            'Authorization': `Bearer ${this.apiKey}`,
            ...options.headers
          };
          
          const response = await fetch(url, {
            ...options,
            headers
          });
          
          if (!response.ok) {
            const error = await response.text();
            throw new Error(`Estuary API-Fehler: ${error}`);
          }
          
          return response.json();
        }
      };
      
      // Teste die Verbindung
      await this.estuaryClient.request('/user/stats');
      
      logger.debug('Estuary-Client erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des Estuary-Clients', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Speichert eine Datei auf Filecoin
   * @param {Buffer|string|Blob|File} data - Zu speichernde Daten
   * @param {Object} options - Speicheroptionen
   * @param {string} options.name - Dateiname
   * @param {string} options.contentType - MIME-Typ der Datei
   * @param {number} options.replication - Anzahl der Replikationen
   * @param {number} options.dealDuration - Dauer der Speicherdeals in Tagen
   * @param {Object} options.metadata - Zusätzliche Metadaten
   * @returns {Promise<Object>} Filecoin-Speicherinformationen
   */
  async storeFile(data, options = {}) {
    try {
      logger.info('Speichere Datei auf Filecoin');
      
      // Prüfe, ob der Client initialisiert wurde
      if (!this.isInitialized) {
        await this.initialize();
      }
      
      // Setze Standardoptionen
      const name = options.name || `file-${Date.now()}`;
      const contentType = options.contentType || 'application/octet-stream';
      const replication = options.replication || this.options.defaultReplication;
      const dealDuration = options.dealDuration || this.options.defaultDealDuration;
      
      let cid;
      let size;
      
      if (this.options.useEstuary) {
        // Speichere die Datei mit Estuary
        const result = await this.storeWithEstuary(data, {
          name,
          contentType,
          replication,
          dealDuration,
          metadata: options.metadata
        });
        
        cid = result.cid;
        size = result.size;
      } else {
        // Speichere die Datei mit Web3.Storage
        const result = await this.storeWithWeb3Storage(data, {
          name,
          contentType,
          metadata: options.metadata
        });
        
        cid = result.cid;
        size = result.size;
      }
      
      logger.debug(`Datei auf Filecoin gespeichert mit CID ${cid}`);
      
      return {
        id: cid,
        cid,
        size,
        name,
        contentType,
        uri: `ipfs://${cid}`,
        url: `https://ipfs.io/ipfs/${cid}`,
        gateway: `https://dweb.link/ipfs/${cid}`,
        filecoin: true,
        replication,
        dealDuration,
        timestamp: Date.now()
      };
    } catch (error) {
      logger.error('Fehler beim Speichern der Datei auf Filecoin', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Speichert eine Datei mit Web3.Storage
   * @param {Buffer|string|Blob|File} data - Zu speichernde Daten
   * @param {Object} options - Speicheroptionen
   * @returns {Promise<Object>} Speicherinformationen
   */
  async storeWithWeb3Storage(data, options = {}) {
    try {
      logger.debug('Speichere Datei mit Web3.Storage');
      
      // Konvertiere die Daten in ein File-Objekt
      let file;
      
      if (data instanceof Blob || data instanceof File) {
        // Verwende die Datei direkt
        file = new File([data], options.name, { type: options.contentType });
      } else if (Buffer.isBuffer(data) || typeof data === 'string') {
        // Konvertiere Buffer oder String zu File
        const buffer = Buffer.isBuffer(data) ? data : Buffer.from(data);
        file = new File([buffer], options.name, { type: options.contentType });
      } else {
        throw new Error('Nicht unterstützter Datentyp');
      }
      
      // Füge Metadaten hinzu, falls vorhanden
      let files = [file];
      
      if (options.metadata) {
        const metadataFile = new File(
          [JSON.stringify(options.metadata)],
          `${options.name}.metadata.json`,
          { type: 'application/json' }
        );
        files.push(metadataFile);
      }
      
      // Speichere die Datei
      const cid = await this.web3StorageClient.put(files, {
        name: options.name,
        wrapWithDirectory: true
      });
      
      // Hole die Größe der Datei
      const size = file.size;
      
      logger.debug(`Datei mit Web3.Storage gespeichert: ${cid}`);
      
      return { cid, size };
    } catch (error) {
      logger.error('Fehler beim Speichern mit Web3.Storage', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Speichert eine Datei mit Estuary
   * @param {Buffer|string|Blob|File} data - Zu speichernde Daten
   * @param {Object} options - Speicheroptionen
   * @returns {Promise<Object>} Speicherinformationen
   */
  async storeWithEstuary(data, options = {}) {
    try {
      logger.debug('Speichere Datei mit Estuary');
      
      // Konvertiere die Daten in ein FormData-Objekt
      const formData = new FormData();
      
      if (data instanceof Blob || data instanceof File) {
        // Verwende die Datei direkt
        formData.append('data', data, options.name);
      } else if (Buffer.isBuffer(data) || typeof data === 'string') {
        // Konvertiere Buffer oder String zu Blob
        const buffer = Buffer.isBuffer(data) ? data : Buffer.from(data);
        const blob = new Blob([buffer], { type: options.contentType });
        formData.append('data', blob, options.name);
      } else {
        throw new Error('Nicht unterstützter Datentyp');
      }
      
      // Füge Metadaten hinzu
      if (options.metadata) {
        formData.append('metadata', JSON.stringify(options.metadata));
      }
      
      // Speichere die Datei
      const response = await this.estuaryClient.request('/content/add', {
        method: 'POST',
        body: formData
      });
      
      // Erstelle Speicherdeals
      await this.estuaryClient.request(`/content/deal/${response.cid}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          replication: options.replication,
          durationBlks: options.dealDuration,
          verified: true
        })
      });
      
      logger.debug(`Datei mit Estuary gespeichert: ${response.cid}`);
      
      return { 
        cid: response.cid,
        size: response.size,
        estuaryId: response.estuaryId
      };
    } catch (error) {
      logger.error('Fehler beim Speichern mit Estuary', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Speichert JSON-Daten auf Filecoin
   * @param {Object} data - Zu speichernde JSON-Daten
   * @param {Object} options - Speicheroptionen
   * @returns {Promise<Object>} Filecoin-Speicherinformationen
   */
  async storeJson(data, options = {}) {
    try {
      logger.info('Speichere JSON-Daten auf Filecoin');
      
      // Konvertiere die JSON-Daten in einen String
      const jsonString = JSON.stringify(data);
      
      // Speichere die JSON-Daten als Datei
      return await this.storeFile(jsonString, {
        name: options.name || `data-${Date.now()}.json`,
        contentType: 'application/json',
        ...options
      });
    } catch (error) {
      logger.error('Fehler beim Speichern der JSON-Daten auf Filecoin', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Datei von Filecoin/IPFS ab
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Buffer>} Abgerufene Daten
   */
  async retrieveFile(cid, options = {}) {
    try {
      logger.info(`Rufe Datei mit CID ${cid} von Filecoin/IPFS ab`);
      
      // Prüfe, ob der Client initialisiert wurde
      if (!this.isInitialized) {
        await this.initialize();
      }
      
      let data;
      
      if (this.options.useEstuary) {
        // Rufe die Datei von Estuary ab
        data = await this.retrieveFromEstuary(cid, options);
      } else {
        // Rufe die Datei von Web3.Storage ab
        data = await this.retrieveFromWeb3Storage(cid, options);
      }
      
      logger.debug(`Datei mit CID ${cid} erfolgreich abgerufen`);
      
      return data;
    } catch (error) {
      logger.error('Fehler beim Abrufen der Datei von Filecoin/IPFS', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Datei von Web3.Storage ab
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Buffer>} Abgerufene Daten
   */
  async retrieveFromWeb3Storage(cid, options = {}) {
    try {
      logger.debug(`Rufe Datei mit CID ${cid} von Web3.Storage ab`);
      
      // Hole die Datei von Web3.Storage
      const res = await this.web3StorageClient.get(cid);
      
      if (!res.ok) {
        throw new Error(`Fehler beim Abrufen der Datei: ${res.statusText}`);
      }
      
      // Extrahiere die Dateien
      const files = await res.files();
      
      if (files.length === 0) {
        throw new Error('Keine Dateien gefunden');
      }
      
      // Hole die Hauptdatei (ignoriere Metadaten-Dateien)
      let mainFile;
      
      if (options.filename) {
        // Suche nach der angegebenen Datei
        mainFile = files.find(file => file.name === options.filename);
        
        if (!mainFile) {
          throw new Error(`Datei ${options.filename} nicht gefunden`);
        }
      } else {
        // Verwende die erste Datei, die keine Metadaten-Datei ist
        mainFile = files.find(file => !file.name.endsWith('.metadata.json'));
        
        if (!mainFile) {
          // Verwende die erste Datei
          mainFile = files[0];
        }
      }
      
      // Lese die Datei
      const arrayBuffer = await mainFile.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);
      
      logger.debug(`Datei mit CID ${cid} erfolgreich von Web3.Storage abgerufen`);
      
      return buffer;
    } catch (error) {
      logger.error('Fehler beim Abrufen der Datei von Web3.Storage', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Datei von Estuary ab
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Buffer>} Abgerufene Daten
   */
  async retrieveFromEstuary(cid, options = {}) {
    try {
      logger.debug(`Rufe Datei mit CID ${cid} von Estuary ab`);
      
      // Hole die Datei von Estuary
      const response = await fetch(`${this.options.estuaryEndpoint}/gw/ipfs/${cid}`);
      
      if (!response.ok) {
        throw new Error(`Fehler beim Abrufen der Datei: ${response.statusText}`);
      }
      
      // Lese die Datei
      const arrayBuffer = await response.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);
      
      logger.debug(`Datei mit CID ${cid} erfolgreich von Estuary abgerufen`);
      
      return buffer;
    } catch (error) {
      logger.error('Fehler beim Abrufen der Datei von Estuary', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Ruft JSON-Daten von Filecoin/IPFS ab
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Object>} Abgerufene JSON-Daten
   */
  async retrieveJson(cid, options = {}) {
    try {
      logger.info(`Rufe JSON-Daten mit CID ${cid} von Filecoin/IPFS ab`);
      
      // Hole die Datei von Filecoin/IPFS
      const data = await this.retrieveFile(cid, options);
      
      // Konvertiere die Daten in ein JSON-Objekt
      const jsonData = JSON.parse(data.toString());
      
      logger.debug(`JSON-Daten mit CID ${cid} erfolgreich abgerufen`);
      
      return jsonData;
    } catch (error) {
      logger.error('Fehler beim Abrufen der JSON-Daten von Filecoin/IPFS', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Datei auf Filecoin/IPFS existiert
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Prüfoptionen
   * @returns {Promise<boolean>} True, wenn die Datei existiert
   */
  async exists(cid, options = {}) {
    try {
      logger.info(`Prüfe, ob Datei mit CID ${cid} auf Filecoin/IPFS existiert`);
      
      // Prüfe, ob der Client initialisiert wurde
      if (!this.isInitialized) {
        await this.initialize();
      }
      
      let exists;
      
      if (this.options.useEstuary) {
        // Prüfe, ob die Datei auf Estuary existiert
        exists = await this.existsOnEstuary(cid, options);
      } else {
        // Prüfe, ob die Datei auf Web3.Storage existiert
        exists = await this.existsOnWeb3Storage(cid, options);
      }
      
      logger.debug(`Datei mit CID ${cid} existiert: ${exists}`);
      
      return exists;
    } catch (error) {
      logger.debug(`Datei mit CID ${cid} existiert nicht auf Filecoin/IPFS`);
      return false;
    }
  }
  
  /**
   * Prüft, ob eine Datei auf Web3.Storage existiert
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Prüfoptionen
   * @returns {Promise<boolean>} True, wenn die Datei existiert
   */
  async existsOnWeb3Storage(cid, options = {}) {
    try {
      logger.debug(`Prüfe, ob Datei mit CID ${cid} auf Web3.Storage existiert`);
      
      // Hole den Status der Datei
      const status = await this.web3StorageClient.status(cid);
      
      // Prüfe, ob die Datei existiert
      return !!status && status.cid === cid;
    } catch (error) {
      logger.debug(`Datei mit CID ${cid} existiert nicht auf Web3.Storage`);
      return false;
    }
  }
  
  /**
   * Prüft, ob eine Datei auf Estuary existiert
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Prüfoptionen
   * @returns {Promise<boolean>} True, wenn die Datei existiert
   */
  async existsOnEstuary(cid, options = {}) {
    try {
      logger.debug(`Prüfe, ob Datei mit CID ${cid} auf Estuary existiert`);
      
      // Versuche, die Datei-Informationen abzurufen
      const response = await this.estuaryClient.request(`/content/by-cid/${cid}`);
      
      // Prüfe, ob die Datei existiert
      return !!response && response.cid === cid;
    } catch (error) {
      logger.debug(`Datei mit CID ${cid} existiert nicht auf Estuary`);
      return false;
    }
  }
  
  /**
   * Löscht eine Datei (nicht möglich bei Filecoin/IPFS)
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Löschoptionen
   * @returns {Promise<boolean>} False, da Löschen nicht möglich ist
   */
  async delete(cid, options = {}) {
    logger.warn(`Löschen von Dateien ist bei Filecoin/IPFS nicht möglich (CID: ${cid})`);
    return false;
  }
  
  /**
   * Generiert eine URL für den Zugriff auf die Datei
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - URL-Optionen
   * @param {string} options.gateway - Zu verwendender IPFS-Gateway
   * @returns {Promise<string>} URL für den Zugriff auf die Datei
   */
  async getAccessUrl(cid, options = {}) {
    const gateway = options.gateway || 'https://ipfs.io/ipfs/';
    return `${gateway}${cid}`;
  }
  
  /**
   * Berechnet die Kosten für die Speicherung einer Datei
   * @param {Buffer|string} data - Zu speichernde Daten
   * @param {Object} options - Kostenberechnungsoptionen
   * @param {number} options.replication - Anzahl der Replikationen
   * @param {number} options.dealDuration - Dauer der Speicherdeals in Tagen
   * @returns {Promise<Object>} Kostendetails
   */
  async calculateStorageCost(data, options = {}) {
    try {
      logger.debug('Berechne Speicherkosten für Filecoin');
      
      // Prüfe, ob der Client initialisiert wurde
      if (!this.isInitialized) {
        await this.initialize();
      }
      
      // Konvertiere String zu Buffer, falls nötig
      const buffer = Buffer.isBuffer(data) ? data : Buffer.from(data);
      
      // Setze Standardoptionen
      const replication = options.replication || this.options.defaultReplication;
      const dealDuration = options.dealDuration || this.options.defaultDealDuration;
      
      // Berechne die Größe in Bytes
      const sizeInBytes = buffer.length;
      
      // Berechne die Größe in GiB (1 GiB = 1024^3 Bytes)
      const sizeInGiB = sizeInBytes / Math.pow(1024, 3);
      
      // Schätze die Kosten (basierend auf aktuellen Filecoin-Preisen)
      // Dies ist eine grobe Schätzung und sollte für genaue Werte durch eine API-Abfrage ersetzt werden
      const pricePerGiBPerEpoch = 0.0000000005; // FIL pro GiB pro Epoche (30 Sekunden)
      const epochsPerDay = 2880; // 30 Sekunden pro Epoche, 86400 Sekunden pro Tag
      const totalEpochs = dealDuration * epochsPerDay;
      
      // Berechne die Gesamtkosten
      const costInFil = sizeInGiB * pricePerGiBPerEpoch * totalEpochs * replication;
      
      return {
        bytes: sizeInBytes,
        sizeInGiB,
        replication,
        dealDuration,
        costInFil,
        currency: 'FIL'
      };
    } catch (error) {
      logger.error('Fehler bei der Berechnung der Speicherkosten', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft den Status eines Speicherdeals ab
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Statusoptionen
   * @returns {Promise<Object>} Dealstatus
   */
  async getDealStatus(cid, options = {}) {
    try {
      logger.info(`Rufe Status des Speicherdeals für CID ${cid} ab`);
      
      // Prüfe, ob der Client initialisiert wurde
      if (!this.isInitialized) {
        await this.initialize();
      }
      
      let status;
      
      if (this.options.useEstuary) {
        // Hole den Status von Estuary
        status = await this.getDealStatusFromEstuary(cid, options);
      } else {
        // Hole den Status von Web3.Storage
        status = await this.getDealStatusFromWeb3Storage(cid, options);
      }
      
      logger.debug(`Status des Speicherdeals für CID ${cid} abgerufen`);
      
      return status;
    } catch (error) {
      logger.error('Fehler beim Abrufen des Dealstatus', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Ruft den Status eines Speicherdeals von Web3.Storage ab
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Statusoptionen
   * @returns {Promise<Object>} Dealstatus
   */
  async getDealStatusFromWeb3Storage(cid, options = {}) {
    try {
      logger.debug(`Rufe Status des Speicherdeals für CID ${cid} von Web3.Storage ab`);
      
      // Hole den Status der Datei
      const status = await this.web3StorageClient.status(cid);
      
      if (!status) {
        throw new Error(`Keine Statusinformationen für CID ${cid} gefunden`);
      }
      
      // Extrahiere die Dealinformationen
      const deals = status.deals || [];
      
      return {
        cid,
        deals: deals.map(deal => ({
          dealId: deal.dealId,
          storageProvider: deal.storageProvider,
          status: deal.status,
          pieceCid: deal.pieceCid,
          dataCid: deal.dataCid,
          dataModelSelector: deal.dataModelSelector,
          activation: deal.activation,
          expiration: deal.expiration,
          created: deal.created,
          updated: deal.updated
        })),
        pins: status.pins || []
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen des Dealstatus von Web3.Storage', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Ruft den Status eines Speicherdeals von Estuary ab
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Statusoptionen
   * @returns {Promise<Object>} Dealstatus
   */
  async getDealStatusFromEstuary(cid, options = {}) {
    try {
      logger.debug(`Rufe Status des Speicherdeals für CID ${cid} von Estuary ab`);
      
      // Hole die Datei-Informationen
      const content = await this.estuaryClient.request(`/content/by-cid/${cid}`);
      
      if (!content) {
        throw new Error(`Keine Informationen für CID ${cid} gefunden`);
      }
      
      // Hole die Dealinformationen
      const deals = await this.estuaryClient.request(`/content/deals/${content.id}`);
      
      return {
        cid,
        estuaryId: content.id,
        name: content.name,
        size: content.size,
        deals: deals.map(deal => ({
          dealId: deal.dealId,
          miner: deal.miner,
          status: deal.status,
          pieceCid: deal.pieceCid,
          dataCid: deal.dataCid,
          activation: deal.activation,
          expiration: deal.expiration,
          created: deal.created,
          updated: deal.updated
        }))
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen des Dealstatus von Estuary', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Erstellt einen Speicherdeal für eine bereits auf IPFS gespeicherte Datei
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Dealoptionen
   * @param {number} options.replication - Anzahl der Replikationen
   * @param {number} options.dealDuration - Dauer der Speicherdeals in Tagen
   * @returns {Promise<Object>} Dealinformationen
   */
  async createDealForCid(cid, options = {}) {
    try {
      logger.info(`Erstelle Speicherdeal für CID ${cid}`);
      
      // Prüfe, ob der Client initialisiert wurde
      if (!this.isInitialized) {
        await this.initialize();
      }
      
      // Setze Standardoptionen
      const replication = options.replication || this.options.defaultReplication;
      const dealDuration = options.dealDuration || this.options.defaultDealDuration;
      
      let result;
      
      if (this.options.useEstuary) {
        // Erstelle einen Deal mit Estuary
        result = await this.createDealWithEstuary(cid, {
          replication,
          dealDuration
        });
      } else {
        // Web3.Storage erstellt automatisch Deals, daher ist keine explizite Aktion erforderlich
        // Wir können jedoch den Status abrufen
        result = await this.getDealStatusFromWeb3Storage(cid);
      }
      
      logger.debug(`Speicherdeal für CID ${cid} erstellt`);
      
      return result;
    } catch (error) {
      logger.error('Fehler beim Erstellen des Speicherdeals', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Erstellt einen Speicherdeal mit Estuary
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Dealoptionen
   * @returns {Promise<Object>} Dealinformationen
   */
  async createDealWithEstuary(cid, options = {}) {
    try {
      logger.debug(`Erstelle Speicherdeal für CID ${cid} mit Estuary`);
      
      // Importiere die Datei in Estuary, falls sie noch nicht dort ist
      let content;
      
      try {
        // Versuche, die Datei-Informationen abzurufen
        content = await this.estuaryClient.request(`/content/by-cid/${cid}`);
      } catch (error) {
        // Datei ist noch nicht in Estuary, importiere sie
        content = await this.estuaryClient.request('/content/add-ipfs', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            cid,
            name: options.name || `ipfs-${cid}`
          })
        });
      }
      
      // Erstelle Speicherdeals
      await this.estuaryClient.request(`/content/deal/${content.id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          replication: options.replication,
          durationBlks: options.dealDuration,
          verified: true
        })
      });
      
      // Hole die Dealinformationen
      const deals = await this.estuaryClient.request(`/content/deals/${content.id}`);
      
      logger.debug(`Speicherdeal für CID ${cid} mit Estuary erstellt`);
      
      return {
        cid,
        estuaryId: content.id,
        name: content.name,
        size: content.size,
        deals
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen des Speicherdeals mit Estuary', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Ruft Statistiken über die gespeicherten Daten ab
   * @returns {Promise<Object>} Speicherstatistiken
   */
  async getStorageStats() {
    try {
      logger.info('Rufe Speicherstatistiken ab');
      
      // Prüfe, ob der Client initialisiert wurde
      if (!this.isInitialized) {
        await this.initialize();
      }
      
      let stats;
      
      if (this.options.useEstuary) {
        // Hole Statistiken von Estuary
        stats = await this.getEstuaryStats();
      } else {
        // Hole Statistiken von Web3.Storage
        stats = await this.getWeb3StorageStats();
      }
      
      logger.debug('Speicherstatistiken abgerufen');
      
      return stats;
    } catch (error) {
      logger.error('Fehler beim Abrufen der Speicherstatistiken', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft Statistiken von Web3.Storage ab
   * @returns {Promise<Object>} Speicherstatistiken
   */
  async getWeb3StorageStats() {
    try {
      logger.debug('Rufe Statistiken von Web3.Storage ab');
      
      // Hole die Statistiken
      const info = await this.web3StorageClient.user();
      
      return {
        totalStorage: info.storageUsed || 0,
        totalUploads: info.numUploads || 0,
        storageLimit: info.storageLimit || 0,
        provider: 'web3.storage'
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen der Statistiken von Web3.Storage', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft Statistiken von Estuary ab
   * @returns {Promise<Object>} Speicherstatistiken
   */
  async getEstuaryStats() {
    try {
      logger.debug('Rufe Statistiken von Estuary ab');
      
      // Hole die Statistiken
      const stats = await this.estuaryClient.request('/user/stats');
      
      return {
        totalStorage: stats.totalStorage || 0,
        totalFiles: stats.totalFiles || 0,
        totalDeals: stats.totalDeals || 0,
        provider: 'estuary'
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen der Statistiken von Estuary', {
        error: error.message
      });
      throw error;
    }
  }
}/**
 * @fileoverview Filecoin-Adapter für langfristige verifizierbare Speicherung
 * 
 * Dieser Adapter implementiert das StorageAdapter-Interface für Filecoin.
 * Er wird hauptsächlich für die langfristige Speicherung großer Forschungsdatasets verwendet
 * und bietet kryptografisch verifizierbare Speichergarantien durch wirtschaftliche Anreize.
 */

import { StorageAdapter } from './StorageAdapter.js';
import { logger } from '../../utils/logger.js';

/**
 * Filecoin-Adapter für langfristige verifizierbare Speicherung
 */
export class FilecoinAdapter extends StorageAdapter {
  /**
   * Erstellt eine neue Instanz des FilecoinAdapter
   * @param {Object} options - Konfigurationsoptionen
   * @param {string} options.apiKey - API-Schlüssel für Web3.Storage
   * @param {string} options.endpoint - API-Endpunkt
   * @param {number} options.defaultReplication - Standardanzahl der Replikationen
   * @param {number} options.defaultDealDuration - Standarddauer der Speicherdeals in Tagen
   * @param {boolean} options.useEstuary - Estuary.tech als Alternative zu Web3.Storage verwenden
   * @param {string} options.estuaryApiKey - API-Schlüssel für Estuary
   * @param {string} options.estuaryEndpoint - Estuary API-Endpunkt
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      apiKey: options.apiKey || process.env.FILECOIN_API_KEY,
      endpoint: options.endpoint || process.env.FILECOIN_ENDPOINT || 'https://api.web3.storage',
      defaultReplication: options.defaultReplication || 5,
      defaultDealDuration: options.defaultDealDuration || 525600, // 1 Jahr in Minuten
      useEstuary: options.useEstuary || process.env.USE_ESTUARY === 'true' || false,
      estuaryApiKey: options.estuaryApiKey || process.env.ESTUARY_API_KEY,
      estuaryEndpoint: options.estuaryEndpoint || process.env.ESTUARY_ENDPOINT || 'https://api.estuary.tech',
      ...options
    };
    
    this.web3StorageClient = null;
    this.estuaryClient = null;
    this.isInitialized = false;
    
    logger.info('FilecoinAdapter initialisiert');
  }
  
  /**
   * Initialisiert den Filecoin-Client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      logger.info('Initialisiere Filecoin-Client');
      
      if (this.options.useEstuary) {
        // Initialisiere Estuary-Client
        await this.initializeEstuaryClient();
      } else {
        // Initialisiere Web3.Storage-Client
        await this.initializeWeb3StorageClient();
      }
      
      this.isInitialized = true;
      
      logger.info('Filecoin-Client erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des Filecoin-Clients', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Initialisiert den Web3.Storage-Client
   * @returns {Promise<void>}
   */
  async initializeWeb3StorageClient() {
    try {
      logger.debug('Initialisiere Web3.Storage-Client');
      
      if (!this.options.apiKey) {
        throw new Error('API-Schlüssel für Web3.Storage ist erforderlich');
      }
      
      // Importiere den Web3.Storage-Client
      const { Web3Storage } = await import('web3.storage');
      
      // Erstelle den Client
      this.web3StorageClient = new Web3Storage({ 
        token: this.options.apiKey,
        endpoint: new URL(this.options.endpoint)
      });
      
      logger.debug('Web3.Storage-Client erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des Web3.Storage-Clients', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Initialisiert den Estuary-Client
   * @returns {Promise<void>}
   */
  async initializeEstuaryClient() {
    try {
      logger.debug('Initialisiere Estuary-Client');
      
      if (!this.options.estuaryApiKey) {
        throw new Error('API-Schlüssel für Estuary ist erforderlich');
      }
      
      // Erstelle einen einfachen Wrapper für die Estuary API
      this.estuaryClient = {
        apiKey: this.options.estuaryApiKey,
        endpoint: this.options.estuaryEndpoint,
        
        // Methode zum Senden von Anfragen an die Estuary API
        async request(path, options = {}) {
          const url = `${this.endpoint}${path}`;
          const headers = {
            'Authorization': `Bearer ${this.apiKey}`,
            ...options.headers
          };
          
          const response = await fetch(url, {
            ...options,
            headers
          });
          
          if (!response.ok) {
            const error = await response.text();
            throw new Error(`Estuary API-Fehler: ${error}`);
          }
          
          return response.json();
        }
      };
      
      // Teste die Verbindung
      await this.estuaryClient.request('/user/stats');
      
      logger.debug('Estuary-Client erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des Estuary-Clients', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Speichert eine Datei auf Filecoin
   * @param {Buffer|string|Blob|File} data - Zu speichernde Daten
   * @param {Object} options - Speicheroptionen
   * @param {string} options.name - Dateiname
   * @param {string} options.contentType - MIME-Typ der Datei
   * @param {number} options.replication - Anzahl der Replikationen
   * @param {number} options.dealDuration - Dauer der Speicherdeals in Tagen
   * @param {Object} options.metadata - Zusätzliche Metadaten
   * @returns {Promise<Object>} Filecoin-Speicherinformationen
   */
  async storeFile(data, options = {}) {
    try {
      logger.info('Speichere Datei auf Filecoin');
      
      // Prüfe, ob der Client initialisiert wurde
      if (!this.isInitialized) {
        await this.initialize();
      }
      
      // Setze Standardoptionen
      const name = options.name || `file-${Date.now()}`;
      const contentType = options.contentType || 'application/octet-stream';
      const replication = options.replication || this.options.defaultReplication;
      const dealDuration = options.dealDuration || this.options.defaultDealDuration;
      
      let cid;
      let size;
      
      if (this.options.useEstuary) {
        // Speichere die Datei mit Estuary
        const result = await this.storeWithEstuary(data, {
          name,
          contentType,
          replication,
          dealDuration,
          metadata: options.metadata
        });
        
        cid = result.cid;
        size = result.size;
      } else {
        // Speichere die Datei mit Web3.Storage
        const result = await this.storeWithWeb3Storage(data, {
          name,
          contentType,
          metadata: options.metadata
        });
        
        cid = result.cid;
        size = result.size;
      }
      
      logger.debug(`Datei auf Filecoin gespeichert mit CID ${cid}`);
      
      return {
        id: cid,
        cid,
        size,
        name,
        contentType,
        uri: `ipfs://${cid}`,
        url: `https://ipfs.io/ipfs/${cid}`,
        gateway: `https://dweb.link/ipfs/${cid}`,
        filecoin: true,
        replication,
        dealDuration,
        timestamp: Date.now()
      };
    } catch (error) {
      logger.error('Fehler beim Speichern der Datei auf Filecoin', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Speichert eine Datei mit Web3.Storage
   * @param {Buffer|string|Blob|File} data - Zu speichernde Daten
   * @param {Object} options - Speicheroptionen
   * @returns {Promise<Object>} Speicherinformationen
   */
  async storeWithWeb3Storage(data, options = {}) {
    try {
      logger.debug('Speichere Datei mit Web3.Storage');
      
      // Konvertiere die Daten in ein File-Objekt
      let file;
      
      if (data instanceof Blob || data instanceof File) {
        // Verwende die Datei direkt
        file = new File([data], options.name, { type: options.contentType });
      } else if (Buffer.isBuffer(data) || typeof data === 'string') {
        // Konvertiere Buffer oder String zu File
        const buffer = Buffer.isBuffer(data) ? data : Buffer.from(data);
        file = new File([buffer], options.name, { type: options.contentType });
      } else {
        throw new Error('Nicht unterstützter Datentyp');
      }
      
      // Füge Metadaten hinzu, falls vorhanden
      let files = [file];
      
      if (options.metadata) {
        const metadataFile = new File(
          [JSON.stringify(options.metadata)],
          `${options.name}.metadata.json`,
          { type: 'application/json' }
        );
        files.push(metadataFile);
      }
      
      // Speichere die Datei
      const cid = await this.web3StorageClient.put(files, {
        name: options.name,
        wrapWithDirectory: true
      });
      
      // Hole die Größe der Datei
      const size = file.size;
      
      logger.debug(`Datei mit Web3.Storage gespeichert: ${cid}`);
      
      return { cid, size };
    } catch (error) {
      logger.error('Fehler beim Speichern mit Web3.Storage', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Speichert eine Datei mit Estuary
   * @param {Buffer|string|Blob|File} data - Zu speichernde Daten
   * @param {Object} options - Speicheroptionen
   * @returns {Promise<Object>} Speicherinformationen
   */
  async storeWithEstuary(data, options = {}) {
    try {
      logger.debug('Speichere Datei mit Estuary');
      
      // Konvertiere die Daten in ein FormData-Objekt
      const formData = new FormData();
      
      if (data instanceof Blob || data instanceof File) {
        // Verwende die Datei direkt
        formData.append('data', data, options.name);
      } else if (Buffer.isBuffer(data) || typeof data === 'string') {
        // Konvertiere Buffer oder String zu Blob
        const buffer = Buffer.isBuffer(data) ? data : Buffer.from(data);
        const blob = new Blob([buffer], { type: options.contentType });
        formData.append('data', blob, options.name);
      } else {
        throw new Error('Nicht unterstützter Datentyp');
      }
      
      // Füge Metadaten hinzu
      if (options.metadata) {
        formData.append('metadata', JSON.stringify(options.metadata));
      }
      
      // Speichere die Datei
      const response = await this.estuaryClient.request('/content/add', {
        method: 'POST',
        body: formData
      });
      
      // Erstelle Speicherdeals
      await this.estuaryClient.request(`/content/deal/${response.cid}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          replication: options.replication,
          durationBlks: options.dealDuration,
          verified: true
        })
      });
      
      logger.debug(`Datei mit Estuary gespeichert: ${response.cid}`);
      
      return { 
        cid: response.cid,
        size: response.size,
        estuaryId: response.estuaryId
      };
    } catch (error) {
      logger.error('Fehler beim Speichern mit Estuary', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Speichert JSON-Daten auf Filecoin
   * @param {Object} data - Zu speichernde JSON-Daten
   * @param {Object} options - Speicheroptionen
   * @returns {Promise<Object>} Filecoin-Speicherinformationen
   */
  async storeJson(data, options = {}) {
    try {
      logger.info('Speichere JSON-Daten auf Filecoin');
      
      // Konvertiere die JSON-Daten in einen String
      const jsonString = JSON.stringify(data);
      
      // Speichere die JSON-Daten als Datei
      return await this.storeFile(jsonString, {
        name: options.name || `data-${Date.now()}.json`,
        contentType: 'application/json',
        ...options
      });
    } catch (error) {
      logger.error('Fehler beim Speichern der JSON-Daten auf Filecoin', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Datei von Filecoin/IPFS ab
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Buffer>} Abgerufene Daten
   */
  async retrieveFile(cid, options = {}) {
    try {
      logger.info(`Rufe Datei mit CID ${cid} von Filecoin/IPFS ab`);
      
      // Prüfe, ob der Client initialisiert wurde
      if (!this.isInitialized) {
        await this.initialize();
      }
      
      let data;
      
      if (this.options.useEstuary) {
        // Rufe die Datei von Estuary ab
        data = await this.retrieveFromEstuary(cid, options);
      } else {
        // Rufe die Datei von Web3.Storage ab
        data = await this.retrieveFromWeb3Storage(cid, options);
      }
      
      logger.debug(`Datei mit CID ${cid} erfolgreich abgerufen`);
      
      return data;
    } catch (error) {
      logger.error('Fehler beim Abrufen der Datei von Filecoin/IPFS', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Datei von Web3.Storage ab
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Buffer>} Abgerufene Daten
   */
  async retrieveFromWeb3Storage(cid, options = {}) {
    try {
      logger.debug(`Rufe Datei mit CID ${cid} von Web3.Storage ab`);
      
      // Hole die Datei von Web3.Storage
      const res = await this.web3StorageClient.get(cid);
      
      if (!res.ok) {
        throw new Error(`Fehler beim Abrufen der Datei: ${res.statusText}`);
      }
      
      // Extrahiere die Dateien
      const files = await res.files();
      
      if (files.length === 0) {
        throw new Error('Keine Dateien gefunden');
      }
      
      // Hole die Hauptdatei (ignoriere Metadaten-Dateien)
      let mainFile;
      
      if (options.filename) {
        // Suche nach der angegebenen Datei
        mainFile = files.find(file => file.name === options.filename);
        
        if (!mainFile) {
          throw new Error(`Datei ${options.filename} nicht gefunden`);
        }
      } else {
        // Verwende die erste Datei, die keine Metadaten-Datei ist
        mainFile = files.find(file => !file.name.endsWith('.metadata.json'));
        
        if (!mainFile) {
          // Verwende die erste Datei
          mainFile = files[0];
        }
      }
      
      // Lese die Datei
      const arrayBuffer = await mainFile.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);
      
      logger.debug(`Datei mit CID ${cid} erfolgreich von Web3.Storage abgerufen`);
      
      return buffer;
    } catch (error) {
      logger.error('Fehler beim Abrufen der Datei von Web3.Storage', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Datei von Estuary ab
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Buffer>} Abgerufene Daten
   */
  async retrieveFromEstuary(cid, options = {}) {
    try {
      logger.debug(`Rufe Datei mit CID ${cid} von Estuary ab`);
      
      // Hole die Datei von Estuary
      const response = await fetch(`${this.options.estuaryEndpoint}/gw/ipfs/${cid}`);
      
      if (!response.ok) {
        throw new Error(`Fehler beim Abrufen der Datei: ${response.statusText}`);
      }
      
      // Lese die Datei
      const arrayBuffer = await response.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);
      
      logger.debug(`Datei mit CID ${cid} erfolgreich von Estuary abgerufen`);
      
      return buffer;
    } catch (error) {
      logger.error('Fehler beim Abrufen der Datei von Estuary', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Ruft JSON-Daten von Filecoin/IPFS ab
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Object>} Abgerufene JSON-Daten
   */
  async retrieveJson(cid, options = {}) {
    try {
      logger.info(`Rufe JSON-Daten mit CID ${cid} von Filecoin/IPFS ab`);
      
      // Hole die Datei von Filecoin/IPFS
      const data = await this.retrieveFile(cid, options);
      
      // Konvertiere die Daten in ein JSON-Objekt
      const jsonData = JSON.parse(data.toString());
      
      logger.debug(`JSON-Daten mit CID ${cid} erfolgreich abgerufen`);
      
      return jsonData;
    } catch (error) {
      logger.error('Fehler beim Abrufen der JSON-Daten von Filecoin/IPFS', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Datei auf Filecoin/IPFS existiert
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Prüfoptionen
   * @returns {Promise<boolean>} True, wenn die Datei existiert
   */
  async exists(cid, options = {}) {
    try {
      logger.info(`Prüfe, ob Datei mit CID ${cid} auf Filecoin/IPFS existiert`);
      
      // Prüfe, ob der Client initialisiert wurde
      if (!this.isInitialized) {
        await this.initialize();
      }
      
      let exists;
      
      if (this.options.useEstuary) {
        // Prüfe, ob die Datei auf Estuary existiert
        exists = await this.existsOnEstuary(cid, options);
      } else {
        // Prüfe, ob die Datei auf Web3.Storage existiert
        exists = await this.existsOnWeb3Storage(cid, options);
      }
      
      logger.debug(`Datei mit CID ${cid} existiert: ${exists}`);
      
      return exists;
    } catch (error) {
      logger.debug(`Datei mit CID ${cid} existiert nicht auf Filecoin/IPFS`);
      return false;
    }
  }
  
  /**
   * Prüft, ob eine Datei auf Web3.Storage existiert
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Prüfoptionen
   * @returns {Promise<boolean>} True, wenn die Datei existiert
   */
  async existsOnWeb3Storage(cid, options = {}) {
    try {
      logger.debug(`Prüfe, ob Datei mit CID ${cid} auf Web3.Storage existiert`);
      
      // Hole den Status der Datei
      const status = await this.web3StorageClient.status(cid);
      
      // Prüfe, ob die Datei existiert
      return !!status && status.cid === cid;
    } catch (error) {
      logger.debug(`Datei mit CID ${cid} existiert nicht auf Web3.Storage`);
      return false;
    }
  }
  
  /**
   * Prüft, ob eine Datei auf Estuary existiert
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Prüfoptionen
   * @returns {Promise<boolean>} True, wenn die Datei existiert
   */
  async existsOnEstuary(cid, options = {}) {
    try {
      logger.debug(`Prüfe, ob Datei mit CID ${cid} auf Estuary existiert`);
      
      // Versuche, die Datei-Informationen abzurufen
      const response = await this.estuaryClient.request(`/content/by-cid/${cid}`);
      
      // Prüfe, ob die Datei existiert
      return !!response && response.cid === cid;
    } catch (error) {
      logger.debug(`Datei mit CID ${cid} existiert nicht auf Estuary`);
      return false;
    }
  }
  
  /**
   * Löscht eine Datei (nicht möglich bei Filecoin/IPFS)
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Löschoptionen
   * @returns {Promise<boolean>} False, da Löschen nicht möglich ist
   */
  async delete(cid, options = {}) {
    logger.warn(`Löschen von Dateien ist bei Filecoin/IPFS nicht möglich (CID: ${cid})`);
    return false;
  }
  
  /**
   * Generiert eine URL für den Zugriff auf die Datei
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - URL-Optionen
   * @param {string} options.gateway - Zu verwendender IPFS-Gateway
   * @returns {Promise<string>} URL für den Zugriff auf die Datei
   */
  async getAccessUrl(cid, options = {}) {
    const gateway = options.gateway || 'https://ipfs.io/ipfs/';
    return `${gateway}${cid}`;
  }
  
  /**
   * Berechnet die Kosten für die Speicherung einer Datei
   * @param {Buffer|string} data - Zu speichernde Daten
   * @param {Object} options - Kostenberechnungsoptionen
   * @param {number} options.replication - Anzahl der Replikationen
   * @param {number} options.dealDuration - Dauer der Speicherdeals in Tagen
   * @returns {Promise<Object>} Kostendetails
   */
  async calculateStorageCost(data, options = {}) {
    try {
      logger.debug('Berechne Speicherkosten für Filecoin');
      
      // Prüfe, ob der Client initialisiert wurde
      if (!this.isInitialized) {
        await this.initialize();
      }
      
      // Konvertiere String zu Buffer, falls nötig
      const buffer = Buffer.isBuffer(data) ? data : Buffer.from(data);
      
      // Setze Standardoptionen
      const replication = options.replication || this.options.defaultReplication;
      const dealDuration = options.dealDuration || this.options.defaultDealDuration;
      
      // Berechne die Größe in Bytes
      const sizeInBytes = buffer.length;
      
      // Berechne die Größe in GiB (1 GiB = 1024^3 Bytes)
      const sizeInGiB = sizeInBytes / Math.pow(1024, 3);
      
      // Schätze die Kosten (basierend auf aktuellen Filecoin-Preisen)
      // Dies ist eine grobe Schätzung und sollte für genaue Werte durch eine API-Abfrage ersetzt werden
      const pricePerGiBPerEpoch = 0.0000000005; // FIL pro GiB pro Epoche (30 Sekunden)
      const epochsPerDay = 2880; // 30 Sekunden pro Epoche, 86400 Sekunden pro Tag
      const totalEpochs = dealDuration * epochsPerDay;
      
      // Berechne die Gesamtkosten
      const costInFil = sizeInGiB * pricePerGiBPerEpoch * totalEpochs * replication;
      
      return {
        bytes: sizeInBytes,
        sizeInGiB,
        replication,
        dealDuration,
        costInFil,
        currency: 'FIL'
      };
    } catch (error) {
      logger.error('Fehler bei der Berechnung der Speicherkosten', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft den Status eines Speicherdeals ab
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Statusoptionen
   * @returns {Promise<Object>} Dealstatus
   */
  async getDealStatus(cid, options = {}) {
    try {
      logger.info(`Rufe Status des Speicherdeals für CID ${cid} ab`);
      
      // Prüfe, ob der Client initialisiert wurde
      if (!this.isInitialized) {
        await this.initialize();
      }
      
      let status;
      
      if (this.options.useEstuary) {
        // Hole den Status von Estuary
        status = await this.getDealStatusFromEstuary(cid, options);
      } else {
        // Hole den Status von Web3.Storage
        status = await this.getDealStatusFromWeb3Storage(cid, options);
      }
      
      logger.debug(`Status des Speicherdeals für CID ${cid} abgerufen`);
      
      return status;
    } catch (error) {
      logger.error('Fehler beim Abrufen des Dealstatus', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Ruft den Status eines Speicherdeals von Web3.Storage ab
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Statusoptionen
   * @returns {Promise<Object>} Dealstatus
   */
  async getDealStatusFromWeb3Storage(cid, options = {}) {
    try {
      logger.debug(`Rufe Status des Speicherdeals für CID ${cid} von Web3.Storage ab`);
      
      // Hole den Status der Datei
      const status = await this.web3StorageClient.status(cid);
      
      if (!status) {
        throw new Error(`Keine Statusinformationen für CID ${cid} gefunden`);
      }
      
      // Extrahiere die Dealinformationen
      const deals = status.deals || [];
      
      return {
        cid,
        deals: deals.map(deal => ({
          dealId: deal.dealId,
          storageProvider: deal.storageProvider,
          status: deal.status,
          pieceCid: deal.pieceCid,
          dataCid: deal.dataCid,
          dataModelSelector: deal.dataModelSelector,
          activation: deal.activation,
          expiration: deal.expiration,
          created: deal.created,
          updated: deal.updated
        })),
        pins: status.pins || []
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen des Dealstatus von Web3.Storage', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Ruft den Status eines Speicherdeals von Estuary ab
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Statusoptionen
   * @returns {Promise<Object>} Dealstatus
   */
  async getDealStatusFromEstuary(cid, options = {}) {
    try {
      logger.debug(`Rufe Status des Speicherdeals für CID ${cid} von Estuary ab`);
      
      // Hole die Datei-Informationen
      const content = await this.estuaryClient.request(`/content/by-cid/${cid}`);
      
      if (!content) {
        throw new Error(`Keine Informationen für CID ${cid} gefunden`);
      }
      
      // Hole die Dealinformationen
      const deals = await this.estuaryClient.request(`/content/deals/${content.id}`);
      
      return {
        cid,
        estuaryId: content.id,
        name: content.name,
        size: content.size,
        deals: deals.map(deal => ({
          dealId: deal.dealId,
          miner: deal.miner,
          status: deal.status,
          pieceCid: deal.pieceCid,
          dataCid: deal.dataCid,
          activation: deal.activation,
          expiration: deal.expiration,
          created: deal.created,
          updated: deal.updated
        }))
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen des Dealstatus von Estuary', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Erstellt einen Speicherdeal für eine bereits auf IPFS gespeicherte Datei
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Dealoptionen
   * @param {number} options.replication - Anzahl der Replikationen
   * @param {number} options.dealDuration - Dauer der Speicherdeals in Tagen
   * @returns {Promise<Object>} Dealinformationen
   */
  async createDealForCid(cid, options = {}) {
    try {
      logger.info(`Erstelle Speicherdeal für CID ${cid}`);
      
      // Prüfe, ob der Client initialisiert wurde
      if (!this.isInitialized) {
        await this.initialize();
      }
      
      // Setze Standardoptionen
      const replication = options.replication || this.options.defaultReplication;
      const dealDuration = options.dealDuration || this.options.defaultDealDuration;
      
      let result;
      
      if (this.options.useEstuary) {
        // Erstelle einen Deal mit Estuary
        result = await this.createDealWithEstuary(cid, {
          replication,
          dealDuration
        });
      } else {
        // Web3.Storage erstellt automatisch Deals, daher ist keine explizite Aktion erforderlich
        // Wir können jedoch den Status abrufen
        result = await this.getDealStatusFromWeb3Storage(cid);
      }
      
      logger.debug(`Speicherdeal für CID ${cid} erstellt`);
      
      return result;
    } catch (error) {
      logger.error('Fehler beim Erstellen des Speicherdeals', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Erstellt einen Speicherdeal mit Estuary
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Dealoptionen
   * @returns {Promise<Object>} Dealinformationen
   */
  async createDealWithEstuary(cid, options = {}) {
    try {
      logger.debug(`Erstelle Speicherdeal für CID ${cid} mit Estuary`);
      
      // Importiere die Datei in Estuary, falls sie noch nicht dort ist
      let content;
      
      try {
        // Versuche, die Datei-Informationen abzurufen
        content = await this.estuaryClient.request(`/content/by-cid/${cid}`);
      } catch (error) {
        // Datei ist noch nicht in Estuary, importiere sie
        content = await this.estuaryClient.request('/content/add-ipfs', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            cid,
            name: options.name || `ipfs-${cid}`
          })
        });
      }
      
      // Erstelle Speicherdeals
      await this.estuaryClient.request(`/content/deal/${content.id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          replication: options.replication,
          durationBlks: options.dealDuration,
          verified: true
        })
      });
      
      // Hole die Dealinformationen
      const deals = await this.estuaryClient.request(`/content/deals/${content.id}`);
      
      logger.debug(`Speicherdeal für CID ${cid} mit Estuary erstellt`);
      
      return {
        cid,
        estuaryId: content.id,
        name: content.name,
        size: content.size,
        deals
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen des Speicherdeals mit Estuary', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Ruft Statistiken über die gespeicherten Daten ab
   * @returns {Promise<Object>} Speicherstatistiken
   */
  async getStorageStats() {
    try {
      logger.info('Rufe Speicherstatistiken ab');
      
      // Prüfe, ob der Client initialisiert wurde
      if (!this.isInitialized) {
        await this.initialize();
      }
      
      let stats;
      
      if (this.options.useEstuary) {
        // Hole Statistiken von Estuary
        stats = await this.getEstuaryStats();
      } else {
        // Hole Statistiken von Web3.Storage
        stats = await this.getWeb3StorageStats();
      }
      
      logger.debug('Speicherstatistiken abgerufen');
      
      return stats;
    } catch (error) {
      logger.error('Fehler beim Abrufen der Speicherstatistiken', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft Statistiken von Web3.Storage ab
   * @returns {Promise<Object>} Speicherstatistiken
   */
  async getWeb3StorageStats() {
    try {
      logger.debug('Rufe Statistiken von Web3.Storage ab');
      
      // Hole die Statistiken
      const info = await this.web3StorageClient.user();
      
      return {
        totalStorage: info.storageUsed || 0,
        totalUploads: info.numUploads || 0,
        storageLimit: info.storageLimit || 0,
        provider: 'web3.storage'
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen der Statistiken von Web3.Storage', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft Statistiken von Estuary ab
   * @returns {Promise<Object>} Speicherstatistiken
   */
  async getEstuaryStats() {
    try {
      logger.debug('Rufe Statistiken von Estuary ab');
      
      // Hole die Statistiken
      const stats = await this.estuaryClient.request('/user/stats');
      
      return {
        totalStorage: stats.totalStorage || 0,
        totalFiles: stats.totalFiles || 0,
        totalDeals: stats.totalDeals || 0,
        provider: 'estuary'
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen der Statistiken von Estuary', {
        error: error.message
      });
      throw error;
    }
  }
}/**
 * @fileoverview Filecoin-Adapter für langfristige verifizierbare Speicherung
 * 
 * Dieser Adapter implementiert das StorageAdapter-Interface für Filecoin.
 * Er wird hauptsächlich für die langfristige Speicherung großer Forschungsdatasets verwendet
 * und bietet kryptografisch verifizierbare Speichergarantien durch wirtschaftliche Anreize.
 */

import { StorageAdapter } from './StorageAdapter.js';
import { logger } from '../../utils/logger.js';

/**
 * Filecoin-Adapter für langfristige verifizierbare Speicherung
 */
export class FilecoinAdapter extends StorageAdapter {
  /**
   * Erstellt eine neue Instanz des FilecoinAdapter
   * @param {Object} options - Konfigurationsoptionen
   * @param {string} options.apiKey - API-Schlüssel für Web3.Storage
   * @param {string} options.endpoint - API-Endpunkt
   * @param {number} options.defaultReplication - Standardanzahl der Replikationen
   * @param {number} options.defaultDealDuration - Standarddauer der Speicherdeals in Tagen
   * @param {boolean} options.useEstuary - Estuary.tech als Alternative zu Web3.Storage verwenden
   * @param {string} options.estuaryApiKey - API-Schlüssel für Estuary
   * @param {string} options.estuaryEndpoint - Estuary API-Endpunkt
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      apiKey: options.apiKey || process.env.FILECOIN_API_KEY,
      endpoint: options.endpoint || process.env.FILECOIN_ENDPOINT || 'https://api.web3.storage',
      defaultReplication: options.defaultReplication || 5,
      defaultDealDuration: options.defaultDealDuration || 525600, // 1 Jahr in Minuten
      useEstuary: options.useEstuary || process.env.USE_ESTUARY === 'true' || false,
      estuaryApiKey: options.estuaryApiKey || process.env.ESTUARY_API_KEY,
      estuaryEndpoint: options.estuaryEndpoint || process.env.ESTUARY_ENDPOINT || 'https://api.estuary.tech',
      ...options
    };
    
    this.web3StorageClient = null;
    this.estuaryClient = null;
    this.isInitialized = false;
    
    logger.info('FilecoinAdapter initialisiert');
  }
  
  /**
   * Initialisiert den Filecoin-Client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      logger.info('Initialisiere Filecoin-Client');
      
      if (this.options.useEstuary) {
        // Initialisiere Estuary-Client
        await this.initializeEstuaryClient();
      } else {
        // Initialisiere Web3.Storage-Client
        await this.initializeWeb3StorageClient();
      }
      
      this.isInitialized = true;
      
      logger.info('Filecoin-Client erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des Filecoin-Clients', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Initialisiert den Web3.Storage-Client
   * @returns {Promise<void>}
   */
  async initializeWeb3StorageClient() {
    try {
      logger.debug('Initialisiere Web3.Storage-Client');
      
      if (!this.options.apiKey) {
        throw new Error('API-Schlüssel für Web3.Storage ist erforderlich');
      }
      
      // Importiere den Web3.Storage-Client
      const { Web3Storage } = await import('web3.storage');
      
      // Erstelle den Client
      this.web3StorageClient = new Web3Storage({ 
        token: this.options.apiKey,
        endpoint: new URL(this.options.endpoint)
      });
      
      logger.debug('Web3.Storage-Client erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des Web3.Storage-Clients', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Initialisiert den Estuary-Client
   * @returns {Promise<void>}
   */
  async initializeEstuaryClient() {
    try {
      logger.debug('Initialisiere Estuary-Client');
      
      if (!this.options.estuaryApiKey) {
        throw new Error('API-Schlüssel für Estuary ist erforderlich');
      }
      
      // Erstelle einen einfachen Wrapper für die Estuary API
      this.estuaryClient = {
        apiKey: this.options.estuaryApiKey,
        endpoint: this.options.estuaryEndpoint,
        
        // Methode zum Senden von Anfragen an die Estuary API
        async request(path, options = {}) {
          const url = `${this.endpoint}${path}`;
          const headers = {
            'Authorization': `Bearer ${this.apiKey}`,
            ...options.headers
          };
          
          const response = await fetch(url, {
            ...options,
            headers
          });
          
          if (!response.ok) {
            const error = await response.text();
            throw new Error(`Estuary API-Fehler: ${error}`);
          }
          
          return response.json();
        }
      };
      
      // Teste die Verbindung
      await this.estuaryClient.request('/user/stats');
      
      logger.debug('Estuary-Client erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des Estuary-Clients', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Speichert eine Datei auf Filecoin
   * @param {Buffer|string|Blob|File} data - Zu speichernde Daten
   * @param {Object} options - Speicheroptionen
   * @param {string} options.name - Dateiname
   * @param {string} options.contentType - MIME-Typ der Datei
   * @param {number} options.replication - Anzahl der Replikationen
   * @param {number} options.dealDuration - Dauer der Speicherdeals in Tagen
   * @param {Object} options.metadata - Zusätzliche Metadaten
   * @returns {Promise<Object>} Filecoin-Speicherinformationen
   */
  async storeFile(data, options = {}) {
    try {
      logger.info('Speichere Datei auf Filecoin');
      
      // Prüfe, ob der Client initialisiert wurde
      if (!this.isInitialized) {
        await this.initialize();
      }
      
      // Setze Standardoptionen
      const name = options.name || `file-${Date.now()}`;
      const contentType = options.contentType || 'application/octet-stream';
      const replication = options.replication || this.options.defaultReplication;
      const dealDuration = options.dealDuration || this.options.defaultDealDuration;
      
      let cid;
      let size;
      
      if (this.options.useEstuary) {
        // Speichere die Datei mit Estuary
        const result = await this.storeWithEstuary(data, {
          name,
          contentType,
          replication,
          dealDuration,
          metadata: options.metadata
        });
        
        cid = result.cid;
        size = result.size;
      } else {
        // Speichere die Datei mit Web3.Storage
        const result = await this.storeWithWeb3Storage(data, {
          name,
          contentType,
          metadata: options.metadata
        });
        
        cid = result.cid;
        size = result.size;
      }
      
      logger.debug(`Datei auf Filecoin gespeichert mit CID ${cid}`);
      
      return {
        id: cid,
        cid,
        size,
        name,
        contentType,
        uri: `ipfs://${cid}`,
        url: `https://ipfs.io/ipfs/${cid}`,
        gateway: `https://dweb.link/ipfs/${cid}`,
        filecoin: true,
        replication,
        dealDuration,
        timestamp: Date.now()
      };
    } catch (error) {
      logger.error('Fehler beim Speichern der Datei auf Filecoin', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Speichert eine Datei mit Web3.Storage
   * @param {Buffer|string|Blob|File} data - Zu speichernde Daten
   * @param {Object} options - Speicheroptionen
   * @returns {Promise<Object>} Speicherinformationen
   */
  async storeWithWeb3Storage(data, options = {}) {
    try {
      logger.debug('Speichere Datei mit Web3.Storage');
      
      // Konvertiere die Daten in ein File-Objekt
      let file;
      
      if (data instanceof Blob || data instanceof File) {
        // Verwende die Datei direkt
        file = new File([data], options.name, { type: options.contentType });
      } else if (Buffer.isBuffer(data) || typeof data === 'string') {
        // Konvertiere Buffer oder String zu File
        const buffer = Buffer.isBuffer(data) ? data : Buffer.from(data);
        file = new File([buffer], options.name, { type: options.contentType });
      } else {
        throw new Error('Nicht unterstützter Datentyp');
      }
      
      // Füge Metadaten hinzu, falls vorhanden
      let files = [file];
      
      if (options.metadata) {
        const metadataFile = new File(
          [JSON.stringify(options.metadata)],
          `${options.name}.metadata.json`,
          { type: 'application/json' }
        );
        files.push(metadataFile);
      }
      
      // Speichere die Datei
      const cid = await this.web3StorageClient.put(files, {
        name: options.name,
        wrapWithDirectory: true
      });
      
      // Hole die Größe der Datei
      const size = file.size;
      
      logger.debug(`Datei mit Web3.Storage gespeichert: ${cid}`);
      
      return { cid, size };
    } catch (error) {
      logger.error('Fehler beim Speichern mit Web3.Storage', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Speichert eine Datei mit Estuary
   * @param {Buffer|string|Blob|File} data - Zu speichernde Daten
   * @param {Object} options - Speicheroptionen
   * @returns {Promise<Object>} Speicherinformationen
   */
  async storeWithEstuary(data, options = {}) {
    try {
      logger.debug('Speichere Datei mit Estuary');
      
      // Konvertiere die Daten in ein FormData-Objekt
      const formData = new FormData();
      
      if (data instanceof Blob || data instanceof File) {
        // Verwende die Datei direkt
        formData.append('data', data, options.name);
      } else if (Buffer.isBuffer(data) || typeof data === 'string') {
        // Konvertiere Buffer oder String zu Blob
        const buffer = Buffer.isBuffer(data) ? data : Buffer.from(data);
        const blob = new Blob([buffer], { type: options.contentType });
        formData.append('data', blob, options.name);
      } else {
        throw new Error('Nicht unterstützter Datentyp');
      }
      
      // Füge Metadaten hinzu
      if (options.metadata) {
        formData.append('metadata', JSON.stringify(options.metadata));
      }
      
      // Speichere die Datei
      const response = await this.estuaryClient.request('/content/add', {
        method: 'POST',
        body: formData
      });
      
      // Erstelle Speicherdeals
      await this.estuaryClient.request(`/content/deal/${response.cid}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          replication: options.replication,
          durationBlks: options.dealDuration,
          verified: true
        })
      });
      
      logger.debug(`Datei mit Estuary gespeichert: ${response.cid}`);
      
      return { 
        cid: response.cid,
        size: response.size,
        estuaryId: response.estuaryId
      };
    } catch (error) {
      logger.error('Fehler beim Speichern mit Estuary', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Speichert JSON-Daten auf Filecoin
   * @param {Object} data - Zu speichernde JSON-Daten
   * @param {Object} options - Speicheroptionen
   * @returns {Promise<Object>} Filecoin-Speicherinformationen
   */
  async storeJson(data, options = {}) {
    try {
      logger.info('Speichere JSON-Daten auf Filecoin');
      
      // Konvertiere die JSON-Daten in einen String
      const jsonString = JSON.stringify(data);
      
      // Speichere die JSON-Daten als Datei
      return await this.storeFile(jsonString, {
        name: options.name || `data-${Date.now()}.json`,
        contentType: 'application/json',
        ...options
      });
    } catch (error) {
      logger.error('Fehler beim Speichern der JSON-Daten auf Filecoin', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Datei von Filecoin/IPFS ab
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Buffer>} Abgerufene Daten
   */
  async retrieveFile(cid, options = {}) {
    try {
      logger.info(`Rufe Datei mit CID ${cid} von Filecoin/IPFS ab`);
      
      // Prüfe, ob der Client initialisiert wurde
      if (!this.isInitialized) {
        await this.initialize();
      }
      
      let data;
      
      if (this.options.useEstuary) {
        // Rufe die Datei von Estuary ab
        data = await this.retrieveFromEstuary(cid, options);
      } else {
        // Rufe die Datei von Web3.Storage ab
        data = await this.retrieveFromWeb3Storage(cid, options);
      }
      
      logger.debug(`Datei mit CID ${cid} erfolgreich abgerufen`);
      
      return data;
    } catch (error) {
      logger.error('Fehler beim Abrufen der Datei von Filecoin/IPFS', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Datei von Web3.Storage ab
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Buffer>} Abgerufene Daten
   */
  async retrieveFromWeb3Storage(cid, options = {}) {
    try {
      logger.debug(`Rufe Datei mit CID ${cid} von Web3.Storage ab`);
      
      // Hole die Datei von Web3.Storage
      const res = await this.web3StorageClient.get(cid);
      
      if (!res.ok) {
        throw new Error(`Fehler beim Abrufen der Datei: ${res.statusText}`);
      }
      
      // Extrahiere die Dateien
      const files = await res.files();
      
      if (files.length === 0) {
        throw new Error('Keine Dateien gefunden');
      }
      
      // Hole die Hauptdatei (ignoriere Metadaten-Dateien)
      let mainFile;
      
      if (options.filename) {
        // Suche nach der angegebenen Datei
        mainFile = files.find(file => file.name === options.filename);
        
        if (!mainFile) {
          throw new Error(`Datei ${options.filename} nicht gefunden`);
        }
      } else {
        // Verwende die erste Datei, die keine Metadaten-Datei ist
        mainFile = files.find(file => !file.name.endsWith('.metadata.json'));
        
        if (!mainFile) {
          // Verwende die erste Datei
          mainFile = files[0];
        }
      }
      
      // Lese die Datei
      const arrayBuffer = await mainFile.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);
      
      logger.debug(`Datei mit CID ${cid} erfolgreich von Web3.Storage abgerufen`);
      
      return buffer;
    } catch (error) {
      logger.error('Fehler beim Abrufen der Datei von Web3.Storage', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Datei von Estuary ab
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Buffer>} Abgerufene Daten
   */
  async retrieveFromEstuary(cid, options = {}) {
    try {
      logger.debug(`Rufe Datei mit CID ${cid} von Estuary ab`);
      
      // Hole die Datei von Estuary
      const response = await fetch(`${this.options.estuaryEndpoint}/gw/ipfs/${cid}`);
      
      if (!response.ok) {
        throw new Error(`Fehler beim Abrufen der Datei: ${response.statusText}`);
      }
      
      // Lese die Datei
      const arrayBuffer = await response.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);
      
      logger.debug(`Datei mit CID ${cid} erfolgreich von Estuary abgerufen`);
      
      return buffer;
    } catch (error) {
      logger.error('Fehler beim Abrufen der Datei von Estuary', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Ruft JSON-Daten von Filecoin/IPFS ab
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Object>} Abgerufene JSON-Daten
   */
  async retrieveJson(cid, options = {}) {
    try {
      logger.info(`Rufe JSON-Daten mit CID ${cid} von Filecoin/IPFS ab`);
      
      // Hole die Datei von Filecoin/IPFS
      const data = await this.retrieveFile(cid, options);
      
      // Konvertiere die Daten in ein JSON-Objekt
      const jsonData = JSON.parse(data.toString());
      
      logger.debug(`JSON-Daten mit CID ${cid} erfolgreich abgerufen`);
      
      return jsonData;
    } catch (error) {
      logger.error('Fehler beim Abrufen der JSON-Daten von Filecoin/IPFS', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Datei auf Filecoin/IPFS existiert
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Prüfoptionen
   * @returns {Promise<boolean>} True, wenn die Datei existiert
   */
  async exists(cid, options = {}) {
    try {
      logger.info(`Prüfe, ob Datei mit CID ${cid} auf Filecoin/IPFS existiert`);
      
      // Prüfe, ob der Client initialisiert wurde
      if (!this.isInitialized) {
        await this.initialize();
      }
      
      let exists;
      
      if (this.options.useEstuary) {
        // Prüfe, ob die Datei auf Estuary existiert
        exists = await this.existsOnEstuary(cid, options);
      } else {
        // Prüfe, ob die Datei auf Web3.Storage existiert
        exists = await this.existsOnWeb3Storage(cid, options);
      }
      
      logger.debug(`Datei mit CID ${cid} existiert: ${exists}`);
      
      return exists;
    } catch (error) {
      logger.debug(`Datei mit CID ${cid} existiert nicht auf Filecoin/IPFS`);
      return false;
    }
  }
  
  /**
   * Prüft, ob eine Datei auf Web3.Storage existiert
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Prüfoptionen
   * @returns {Promise<boolean>} True, wenn die Datei existiert
   */
  async existsOnWeb3Storage(cid, options = {}) {
    try {
      logger.debug(`Prüfe, ob Datei mit CID ${cid} auf Web3.Storage existiert`);
      
      // Hole den Status der Datei
      const status = await this.web3StorageClient.status(cid);
      
      // Prüfe, ob die Datei existiert
      return !!status && status.cid === cid;
    } catch (error) {
      logger.debug(`Datei mit CID ${cid} existiert nicht auf Web3.Storage`);
      return false;
    }
  }
  
  /**
   * Prüft, ob eine Datei auf Estuary existiert
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Prüfoptionen
   * @returns {Promise<boolean>} True, wenn die Datei existiert
   */
  async existsOnEstuary(cid, options = {}) {
    try {
      logger.debug(`Prüfe, ob Datei mit CID ${cid} auf Estuary existiert`);
      
      // Versuche, die Datei-Informationen abzurufen
      const response = await this.estuaryClient.request(`/content/by-cid/${cid}`);
      
      // Prüfe, ob die Datei existiert
      return !!response && response.cid === cid;
    } catch (error) {
      logger.debug(`Datei mit CID ${cid} existiert nicht auf Estuary`);
      return false;
    }
  }
  
  /**
   * Löscht eine Datei (nicht möglich bei Filecoin/IPFS)
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Löschoptionen
   * @returns {Promise<boolean>} False, da Löschen nicht möglich ist
   */
  async delete(cid, options = {}) {
    logger.warn(`Löschen von Dateien ist bei Filecoin/IPFS nicht möglich (CID: ${cid})`);
    return false;
  }
  
  /**
   * Generiert eine URL für den Zugriff auf die Datei
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - URL-Optionen
   * @param {string} options.gateway - Zu verwendender IPFS-Gateway
   * @returns {Promise<string>} URL für den Zugriff auf die Datei
   */
  async getAccessUrl(cid, options = {}) {
    const gateway = options.gateway || 'https://ipfs.io/ipfs/';
    return `${gateway}${cid}`;
  }
  
  /**
   * Berechnet die Kosten für die Speicherung einer Datei
   * @param {Buffer|string} data - Zu speichernde Daten
   * @param {Object} options - Kostenberechnungsoptionen
   * @param {number} options.replication - Anzahl der Replikationen
   * @param {number} options.dealDuration - Dauer der Speicherdeals in Tagen
   * @returns {Promise<Object>} Kostendetails
   */
  async calculateStorageCost(data, options = {}) {
    try {
      logger.debug('Berechne Speicherkosten für Filecoin');
      
      // Prüfe, ob der Client initialisiert wurde
      if (!this.isInitialized) {
        await this.initialize();
      }
      
      // Konvertiere String zu Buffer, falls nötig
      const buffer = Buffer.isBuffer(data) ? data : Buffer.from(data);
      
      // Setze Standardoptionen
      const replication = options.replication || this.options.defaultReplication;
      const dealDuration = options.dealDuration || this.options.defaultDealDuration;
      
      // Berechne die Größe in Bytes
      const sizeInBytes = buffer.length;
      
      // Berechne die Größe in GiB (1 GiB = 1024^3 Bytes)
      const sizeInGiB = sizeInBytes / Math.pow(1024, 3);
      
      // Schätze die Kosten (basierend auf aktuellen Filecoin-Preisen)
      // Dies ist eine grobe Schätzung und sollte für genaue Werte durch eine API-Abfrage ersetzt werden
      const pricePerGiBPerEpoch = 0.0000000005; // FIL pro GiB pro Epoche (30 Sekunden)
      const epochsPerDay = 2880; // 30 Sekunden pro Epoche, 86400 Sekunden pro Tag
      const totalEpochs = dealDuration * epochsPerDay;
      
      // Berechne die Gesamtkosten
      const costInFil = sizeInGiB * pricePerGiBPerEpoch * totalEpochs * replication;
      
      return {
        bytes: sizeInBytes,
        sizeInGiB,
        replication,
        dealDuration,
        costInFil,
        currency: 'FIL'
      };
    } catch (error) {
      logger.error('Fehler bei der Berechnung der Speicherkosten', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft den Status eines Speicherdeals ab
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Statusoptionen
   * @returns {Promise<Object>} Dealstatus
   */
  async getDealStatus(cid, options = {}) {
    try {
      logger.info(`Rufe Status des Speicherdeals für CID ${cid} ab`);
      
      // Prüfe, ob der Client initialisiert wurde
      if (!this.isInitialized) {
        await this.initialize();
      }
      
      let status;
      
      if (this.options.useEstuary) {
        // Hole den Status von Estuary
        status = await this.getDealStatusFromEstuary(cid, options);
      } else {
        // Hole den Status von Web3.Storage
        status = await this.getDealStatusFromWeb3Storage(cid, options);
      }
      
      logger.debug(`Status des Speicherdeals für CID ${cid} abgerufen`);
      
      return status;
    } catch (error) {
      logger.error('Fehler beim Abrufen des Dealstatus', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Ruft den Status eines Speicherdeals von Web3.Storage ab
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Statusoptionen
   * @returns {Promise<Object>} Dealstatus
   */
  async getDealStatusFromWeb3Storage(cid, options = {}) {
    try {
      logger.debug(`Rufe Status des Speicherdeals für CID ${cid} von Web3.Storage ab`);
      
      // Hole den Status der Datei
      const status = await this.web3StorageClient.status(cid);
      
      if (!status) {
        throw new Error(`Keine Statusinformationen für CID ${cid} gefunden`);
      }
      
      // Extrahiere die Dealinformationen
      const deals = status.deals || [];
      
      return {
        cid,
        deals: deals.map(deal => ({
          dealId: deal.dealId,
          storageProvider: deal.storageProvider,
          status: deal.status,
          pieceCid: deal.pieceCid,
          dataCid: deal.dataCid,
          dataModelSelector: deal.dataModelSelector,
          activation: deal.activation,
          expiration: deal.expiration,
          created: deal.created,
          updated: deal.updated
        })),
        pins: status.pins || []
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen des Dealstatus von Web3.Storage', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Ruft den Status eines Speicherdeals von Estuary ab
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Statusoptionen
   * @returns {Promise<Object>} Dealstatus
   */
  async getDealStatusFromEstuary(cid, options = {}) {
    try {
      logger.debug(`Rufe Status des Speicherdeals für CID ${cid} von Estuary ab`);
      
      // Hole die Datei-Informationen
      const content = await this.estuaryClient.request(`/content/by-cid/${cid}`);
      
      if (!content) {
        throw new Error(`Keine Informationen für CID ${cid} gefunden`);
      }
      
      // Hole die Dealinformationen
      const deals = await this.estuaryClient.request(`/content/deals/${content.id}`);
      
      return {
        cid,
        estuaryId: content.id,
        name: content.name,
        size: content.size,
        deals: deals.map(deal => ({
          dealId: deal.dealId,
          miner: deal.miner,
          status: deal.status,
          pieceCid: deal.pieceCid,
          dataCid: deal.dataCid,
          activation: deal.activation,
          expiration: deal.expiration,
          created: deal.created,
          updated: deal.updated
        }))
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen des Dealstatus von Estuary', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Erstellt einen Speicherdeal für eine bereits auf IPFS gespeicherte Datei
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Dealoptionen
   * @param {number} options.replication - Anzahl der Replikationen
   * @param {number} options.dealDuration - Dauer der Speicherdeals in Tagen
   * @returns {Promise<Object>} Dealinformationen
   */
  async createDealForCid(cid, options = {}) {
    try {
      logger.info(`Erstelle Speicherdeal für CID ${cid}`);
      
      // Prüfe, ob der Client initialisiert wurde
      if (!this.isInitialized) {
        await this.initialize();
      }
      
      // Setze Standardoptionen
      const replication = options.replication || this.options.defaultReplication;
      const dealDuration = options.dealDuration || this.options.defaultDealDuration;
      
      let result;
      
      if (this.options.useEstuary) {
        // Erstelle einen Deal mit Estuary
        result = await this.createDealWithEstuary(cid, {
          replication,
          dealDuration
        });
      } else {
        // Web3.Storage erstellt automatisch Deals, daher ist keine explizite Aktion erforderlich
        // Wir können jedoch den Status abrufen
        result = await this.getDealStatusFromWeb3Storage(cid);
      }
      
      logger.debug(`Speicherdeal für CID ${cid} erstellt`);
      
      return result;
    } catch (error) {
      logger.error('Fehler beim Erstellen des Speicherdeals', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Erstellt einen Speicherdeal mit Estuary
   * @param {string} cid - Content Identifier (CID)
   * @param {Object} options - Dealoptionen
   * @returns {Promise<Object>} Dealinformationen
   */
  async createDealWithEstuary(cid, options = {}) {
    try {
      logger.debug(`Erstelle Speicherdeal für CID ${cid} mit Estuary`);
      
      // Importiere die Datei in Estuary, falls sie noch nicht dort ist
      let content;
      
      try {
        // Versuche, die Datei-Informationen abzurufen
        content = await this.estuaryClient.request(`/content/by-cid/${cid}`);
      } catch (error) {
        // Datei ist noch nicht in Estuary, importiere sie
        content = await this.estuaryClient.request('/content/add-ipfs', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            cid,
            name: options.name || `ipfs-${cid}`
          })
        });
      }
      
      // Erstelle Speicherdeals
      await this.estuaryClient.request(`/content/deal/${content.id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          replication: options.replication,
          durationBlks: options.dealDuration,
          verified: true
        })
      });
      
      // Hole die Dealinformationen
      const deals = await this.estuaryClient.request(`/content/deals/${content.id}`);
      
      logger.debug(`Speicherdeal für CID ${cid} mit Estuary erstellt`);
      
      return {
        cid,
        estuaryId: content.id,
        name: content.name,
        size: content.size,
        deals
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen des Speicherdeals mit Estuary', {
        error: error.message,
        cid
      });
      throw error;
    }
  }
  
  /**
   * Ruft Statistiken über die gespeicherten Daten ab
   * @returns {Promise<Object>} Speicherstatistiken
   */
  async getStorageStats() {
    try {
      logger.info('Rufe Speicherstatistiken ab');
      
      // Prüfe, ob der Client initialisiert wurde
      if (!this.isInitialized) {
        await this.initialize();
      }
      
      let stats;
      
      if (this.options.useEstuary) {
        // Hole Statistiken von Estuary
        stats = await this.getEstuaryStats();
      } else {
        // Hole Statistiken von Web3.Storage
        stats = await this.getWeb3StorageStats();
      }
      
      logger.debug('Speicherstatistiken abgerufen');
      
      return stats;
    } catch (error) {
      logger.error('Fehler beim Abrufen der Speicherstatistiken', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft Statistiken von Web3.Storage ab
   * @returns {Promise<Object>} Speicherstatistiken
   */
  async getWeb3StorageStats() {
    try {
      logger.debug('Rufe Statistiken von Web3.Storage ab');
      
      // Hole die Statistiken
      const info = await this.web3StorageClient.user();
      
      return {
        totalStorage: info.storageUsed || 0,
        totalUploads: info.numUploads || 0,
        storageLimit: info.storageLimit || 0,
        provider: 'web3.storage'
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen der Statistiken von Web3.Storage', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft Statistiken von Estuary ab
   * @returns {Promise<Object>} Speicherstatistiken
   */
  async getEstuaryStats() {
    try {
      logger.debug('Rufe Statistiken von Estuary ab');
      
      // Hole die Statistiken
      const stats = await this.estuaryClient.request('/user/stats');
      
      return {
        totalStorage: stats.totalStorage || 0,
        totalFiles: stats.totalFiles || 0,
        totalDeals: stats.totalDeals || 0,
        provider: 'estuary'
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen der Statistiken von Estuary', {
        error: error.message
      });
      throw error;
    }
  }
}