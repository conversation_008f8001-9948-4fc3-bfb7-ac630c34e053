/**
 * @fileoverview Krypto-Payment-Adapter für Zahlungen mit Kryptowährungen
 * 
 * Dieser Adapter implementiert das PaymentAdapter-Interface für Zahlungen mit
 * Kryptowährungen wie DOT, ETH, BTC, etc. Er nutzt die Blockchain-Adapter für
 * die Interaktion mit den entsprechenden Blockchains.
 */

import { PaymentAdapter } from './PaymentAdapter.js';
import { logger } from '../../utils/logger.js';

/**
 * Krypto-Payment-Adapter für Zahlungen mit Kryptowährungen
 */
export class CryptoPaymentAdapter extends PaymentAdapter {
  /**
   * Erstellt eine neue Instanz des CryptoPaymentAdapter
   * @param {Object} options - Konfigurationsoptionen
   * @param {Object} options.blockchainManager - BlockchainManager-Instanz
   * @param {Object} options.walletManager - WalletManager-Instanz
   * @param {Object} options.databaseService - Datenbankdienst
   * @param {Object} options.supportedCurrencies - Unterstützte Währungen
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      blockchainManager: options.blockchainManager,
      walletManager: options.walletManager,
      databaseService: options.databaseService,
      supportedCurrencies: options.supportedCurrencies || {
        DOT: {
          name: 'Polkadot',
          symbol: 'DOT',
          decimals: 10,
          adapter: 'polkadot',
          enabled: true
        },
        ETH: {
          name: 'Ethereum',
          symbol: 'ETH',
          decimals: 18,
          adapter: 'ethereum',
          enabled: true
        },
        USDT: {
          name: 'Tether',
          symbol: 'USDT',
          decimals: 6,
          adapter: 'ethereum',
          contractAddress: '******************************************',
          enabled: true
        }
      },
      ...options
    };
    
    this.isInitialized = false;
    
    logger.info('CryptoPaymentAdapter initialisiert');
  }
  
  /**
   * Initialisiert den Payment-Adapter
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      logger.info('Initialisiere CryptoPaymentAdapter');
      
      // Prüfe, ob die erforderlichen Dienste verfügbar sind
      if (!this.options.blockchainManager) {
        throw new Error('BlockchainManager ist erforderlich');
      }
      
      if (!this.options.databaseService) {
        throw new Error('DatabaseService ist erforderlich');
      }
      
      // Initialisiere die Datenbanktabellen
      await this.initializeDatabaseTables();
      
      this.isInitialized = true;
      
      logger.info('CryptoPaymentAdapter erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des CryptoPaymentAdapter', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Initialisiert die erforderlichen Datenbanktabellen
   * @returns {Promise<void>}
   */
  async initializeDatabaseTables() {
    try {
      // Prüfe, ob die Zahlungstabelle existiert
      const paymentTableExists = await this.options.databaseService.tableExists('crypto_payments');
      
      if (!paymentTableExists) {
        // Erstelle die Zahlungstabelle
        await this.options.databaseService.query(`
          CREATE TABLE crypto_payments (
            id VARCHAR(36) PRIMARY KEY,
            customer_id VARCHAR(36),
            amount DECIMAL(24, 8) NOT NULL,
            currency VARCHAR(10) NOT NULL,
            status VARCHAR(20) NOT NULL,
            from_address VARCHAR(100),
            to_address VARCHAR(100) NOT NULL,
            transaction_hash VARCHAR(100),
            block_number BIGINT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            metadata JSON,
            INDEX (customer_id),
            INDEX (status),
            INDEX (transaction_hash)
          )
        `);
        
        logger.info('Krypto-Zahlungstabelle erstellt');
      }
      
      // Prüfe, ob die Kundentabelle existiert
      const customerTableExists = await this.options.databaseService.tableExists('crypto_customers');
      
      if (!customerTableExists) {
        // Erstelle die Kundentabelle
        await this.options.databaseService.query(`
          CREATE TABLE crypto_customers (
            id VARCHAR(36) PRIMARY KEY,
            name VARCHAR(100),
            email VARCHAR(100),
            addresses JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            metadata JSON,
            INDEX (email)
          )
        `);
        
        logger.info('Krypto-Kundentabelle erstellt');
      }
    } catch (error) {
      logger.error('Fehler bei der Initialisierung der Datenbanktabellen', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Erstellt eine Zahlung
   * @param {Object} payment - Zahlungsdaten
   * @returns {Promise<Object>} Zahlungsergebnis
   */
  async createPayment(payment) {
    try {
      logger.info('Erstelle Krypto-Zahlung');
      
      // Validiere die Zahlungsdaten
      if (!payment.amount || !payment.currency) {
        throw new Error('Betrag und Währung sind erforderlich');
      }
      
      // Prüfe, ob die Währung unterstützt wird
      if (!this.options.supportedCurrencies[payment.currency]) {
        throw new Error(`Währung ${payment.currency} wird nicht unterstützt`);
      }
      
      // Prüfe, ob die Währung aktiviert ist
      if (!this.options.supportedCurrencies[payment.currency].enabled) {
        throw new Error(`Währung ${payment.currency} ist deaktiviert`);
      }
      
      // Generiere eine eindeutige Zahlungs-ID
      const paymentId = this.generateId();
      
      // Hole die Empfängeradresse
      const toAddress = payment.toAddress || await this.getReceivingAddress(payment.currency);
      
      // Erstelle die Zahlungsdaten
      const paymentData = {
        id: paymentId,
        customer_id: payment.customerId || null,
        amount: payment.amount,
        currency: payment.currency,
        status: 'pending',
        from_address: payment.fromAddress || null,
        to_address: toAddress,
        transaction_hash: null,
        block_number: null,
        metadata: JSON.stringify(payment.metadata || {})
      };
      
      // Speichere die Zahlung in der Datenbank
      await this.options.databaseService.query(
        `INSERT INTO crypto_payments SET ?`,
        paymentData
      );
      
      logger.info(`Krypto-Zahlung erstellt: ${paymentId}`);
      
      return {
        id: paymentId,
        amount: payment.amount,
        currency: payment.currency,
        status: 'pending',
        toAddress,
        createdAt: new Date().toISOString()
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen der Krypto-Zahlung', {
        error: error.message,
        payment
      });
      throw error;
    }
  }
  
  /**
   * Verarbeitet eine Zahlung
   * @param {string} paymentId - Zahlungs-ID
   * @param {Object} options - Verarbeitungsoptionen
   * @returns {Promise<Object>} Verarbeitungsergebnis
   */
  async processPayment(paymentId, options = {}) {
    try {
      logger.info(`Verarbeite Krypto-Zahlung: ${paymentId}`);
      
      // Hole die Zahlungsdaten
      const payment = await this.getPayment(paymentId);
      
      if (!payment) {
        throw new Error(`Zahlung mit ID ${paymentId} nicht gefunden`);
      }
      
      // Prüfe, ob die Zahlung bereits verarbeitet wurde
      if (payment.status === 'completed') {
        return {
          id: paymentId,
          status: 'completed',
          message: 'Zahlung wurde bereits verarbeitet'
        };
      }
      
      // Prüfe, ob die Zahlung storniert wurde
      if (payment.status === 'cancelled') {
        throw new Error('Zahlung wurde storniert');
      }
      
      // Hole die Währungskonfiguration
      const currencyConfig = this.options.supportedCurrencies[payment.currency];
      
      // Prüfe, ob eine Transaktions-Hash angegeben wurde
      if (options.transactionHash) {
        // Aktualisiere die Zahlung mit dem Transaktions-Hash
        await this.options.databaseService.query(
          `UPDATE crypto_payments SET transaction_hash = ?, status = 'processing', from_address = ? WHERE id = ?`,
          [options.transactionHash, options.fromAddress || payment.from_address, paymentId]
        );
        
        // Hole den Blockchain-Adapter
        const adapter = this.options.blockchainManager.selectAdapter({
          adapter: currencyConfig.adapter
        });
        
        // Warte auf die Bestätigung der Transaktion
        const confirmations = options.confirmations || 1;
        const txResult = await adapter.waitForTransaction(options.transactionHash, confirmations);
        
        // Prüfe, ob die Transaktion erfolgreich war
        if (txResult.status === 'success') {
          // Aktualisiere die Zahlung
          await this.options.databaseService.query(
            `UPDATE crypto_payments SET status = 'completed', block_number = ? WHERE id = ?`,
            [txResult.blockNumber, paymentId]
          );
          
          logger.info(`Krypto-Zahlung erfolgreich verarbeitet: ${paymentId}`);
          
          return {
            id: paymentId,
            status: 'completed',
            transactionHash: options.transactionHash,
            blockNumber: txResult.blockNumber
          };
        } else {
          // Aktualisiere die Zahlung
          await this.options.databaseService.query(
            `UPDATE crypto_payments SET status = 'failed' WHERE id = ?`,
            [paymentId]
          );
          
          throw new Error('Transaktion fehlgeschlagen');
        }
      } else if (options.fromAddress && options.amount) {
        // Manuelle Verarbeitung (z.B. für Tests oder Admin-Funktionen)
        await this.options.databaseService.query(
          `UPDATE crypto_payments SET status = 'completed', from_address = ? WHERE id = ?`,
          [options.fromAddress, paymentId]
        );
        
        logger.info(`Krypto-Zahlung manuell verarbeitet: ${paymentId}`);
        
        return {
          id: paymentId,
          status: 'completed',
          message: 'Zahlung manuell verarbeitet'
        };
      } else {
        throw new Error('Transaktions-Hash oder manuelle Verarbeitungsdaten erforderlich');
      }
    } catch (error) {
      logger.error('Fehler bei der Verarbeitung der Krypto-Zahlung', {
        error: error.message,
        paymentId
      });
      throw error;
    }
  }
  
  /**
   * Storniert eine Zahlung
   * @param {string} paymentId - Zahlungs-ID
   * @param {Object} options - Stornierungsoptionen
   * @returns {Promise<Object>} Stornierungsergebnis
   */
  async cancelPayment(paymentId, options = {}) {
    try {
      logger.info(`Storniere Krypto-Zahlung: ${paymentId}`);
      
      // Hole die Zahlungsdaten
      const payment = await this.getPayment(paymentId);
      
      if (!payment) {
        throw new Error(`Zahlung mit ID ${paymentId} nicht gefunden`);
      }
      
      // Prüfe, ob die Zahlung bereits verarbeitet wurde
      if (payment.status === 'completed') {
        throw new Error('Zahlung wurde bereits verarbeitet und kann nicht storniert werden');
      }
      
      // Prüfe, ob die Zahlung bereits storniert wurde
      if (payment.status === 'cancelled') {
        return {
          id: paymentId,
          status: 'cancelled',
          message: 'Zahlung wurde bereits storniert'
        };
      }
      
      // Aktualisiere die Zahlung
      await this.options.databaseService.query(
        `UPDATE crypto_payments SET status = 'cancelled' WHERE id = ?`,
        [paymentId]
      );
      
      logger.info(`Krypto-Zahlung storniert: ${paymentId}`);
      
      return {
        id: paymentId,
        status: 'cancelled'
      };
    } catch (error) {
      logger.error('Fehler bei der Stornierung der Krypto-Zahlung', {
        error: error.message,
        paymentId
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Zahlung ab
   * @param {string} paymentId - Zahlungs-ID
   * @returns {Promise<Object>} Zahlungsdaten
   */
  async getPayment(paymentId) {
    try {
      logger.debug(`Rufe Krypto-Zahlung ab: ${paymentId}`);
      
      // Hole die Zahlungsdaten aus der Datenbank
      const [payment] = await this.options.databaseService.query(
        `SELECT * FROM crypto_payments WHERE id = ?`,
        [paymentId]
      );
      
      if (!payment) {
        return null;
      }
      
      // Konvertiere die Metadaten
      let metadata = {};
      try {
        metadata = JSON.parse(payment.metadata);
      } catch (error) {
        logger.warn(`Fehler beim Parsen der Metadaten für Zahlung ${paymentId}`);
      }
      
      return {
        id: payment.id,
        customerId: payment.customer_id,
        amount: parseFloat(payment.amount),
        currency: payment.currency,
        status: payment.status,
        fromAddress: payment.from_address,
        toAddress: payment.to_address,
        transactionHash: payment.transaction_hash,
        blockNumber: payment.block_number,
        createdAt: payment.created_at,
        updatedAt: payment.updated_at,
        metadata
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen der Krypto-Zahlung', {
        error: error.message,
        paymentId
      });
      throw error;
    }
  }
  
  /**
   * Ruft Zahlungen ab
   * @param {Object} options - Abfrageoptionen
   * @returns {Promise<Array<Object>>} Zahlungsdaten
   */
  async getPayments(options = {}) {
    try {
      logger.debug('Rufe Krypto-Zahlungen ab');
      
      // Erstelle die Abfrage
      let query = 'SELECT * FROM crypto_payments';
      const params = [];
      
      // Füge Filter hinzu
      const conditions = [];
      
      if (options.customerId) {
        conditions.push('customer_id = ?');
        params.push(options.customerId);
      }
      
      if (options.status) {
        conditions.push('status = ?');
        params.push(options.status);
      }
      
      if (options.currency) {
        conditions.push('currency = ?');
        params.push(options.currency);
      }
      
      if (options.fromAddress) {
        conditions.push('from_address = ?');
        params.push(options.fromAddress);
      }
      
      if (options.toAddress) {
        conditions.push('to_address = ?');
        params.push(options.toAddress);
      }
      
      if (options.transactionHash) {
        conditions.push('transaction_hash = ?');
        params.push(options.transactionHash);
      }
      
      if (conditions.length > 0) {
        query += ' WHERE ' + conditions.join(' AND ');
      }
      
      // Füge Sortierung hinzu
      query += ' ORDER BY created_at DESC';
      
      // Füge Limit hinzu
      if (options.limit) {
        query += ' LIMIT ?';
        params.push(options.limit);
      }
      
      // Führe die Abfrage aus
      const payments = await this.options.databaseService.query(query, params);
      
      // Konvertiere die Zahlungsdaten
      return payments.map(payment => {
        // Konvertiere die Metadaten
        let metadata = {};
        try {
          metadata = JSON.parse(payment.metadata);
        } catch (error) {
          logger.warn(`Fehler beim Parsen der Metadaten für Zahlung ${payment.id}`);
        }
        
        return {
          id: payment.id,
          customerId: payment.customer_id,
          amount: parseFloat(payment.amount),
          currency: payment.currency,
          status: payment.status,
          fromAddress: payment.from_address,
          toAddress: payment.to_address,
          transactionHash: payment.transaction_hash,
          blockNumber: payment.block_number,
          createdAt: payment.created_at,
          updatedAt: payment.updated_at,
          metadata
        };
      });
    } catch (error) {
      logger.error('Fehler beim Abrufen der Krypto-Zahlungen', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Erstellt einen Kunden
   * @param {Object} customer - Kundendaten
   * @returns {Promise<Object>} Kundenergebnis
   */
  async createCustomer(customer) {
    try {
      logger.info('Erstelle Krypto-Kunden');
      
      // Validiere die Kundendaten
      if (!customer.email) {
        throw new Error('E-Mail ist erforderlich');
      }
      
      // Prüfe, ob der Kunde bereits existiert
      const existingCustomer = await this.getCustomerByEmail(customer.email);
      
      if (existingCustomer) {
        return existingCustomer;
      }
      
      // Generiere eine eindeutige Kunden-ID
      const customerId = this.generateId();
      
      // Erstelle die Kundendaten
      const customerData = {
        id: customerId,
        name: customer.name || null,
        email: customer.email,
        addresses: JSON.stringify(customer.addresses || {}),
        metadata: JSON.stringify(customer.metadata || {})
      };
      
      // Speichere den Kunden in der Datenbank
      await this.options.databaseService.query(
        `INSERT INTO crypto_customers SET ?`,
        customerData
      );
      
      logger.info(`Krypto-Kunde erstellt: ${customerId}`);
      
      return {
        id: customerId,
        name: customer.name,
        email: customer.email,
        addresses: customer.addresses || {},
        createdAt: new Date().toISOString()
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen des Krypto-Kunden', {
        error: error.message,
        customer
      });
      throw error;
    }
  }
  
  /**
   * Aktualisiert einen Kunden
   * @param {string} customerId - Kunden-ID
   * @param {Object} customer - Kundendaten
   * @returns {Promise<Object>} Kundenergebnis
   */
  async updateCustomer(customerId, customer) {
    try {
      logger.info(`Aktualisiere Krypto-Kunden: ${customerId}`);
      
      // Hole den Kunden
      const existingCustomer = await this.getCustomer(customerId);
      
      if (!existingCustomer) {
        throw new Error(`Kunde mit ID ${customerId} nicht gefunden`);
      }
      
      // Erstelle die Kundendaten
      const customerData = {};
      
      if (customer.name !== undefined) {
        customerData.name = customer.name;
      }
      
      if (customer.email !== undefined) {
        customerData.email = customer.email;
      }
      
      if (customer.addresses !== undefined) {
        customerData.addresses = JSON.stringify(customer.addresses);
      }
      
      if (customer.metadata !== undefined) {
        customerData.metadata = JSON.stringify(customer.metadata);
      }
      
      // Aktualisiere den Kunden in der Datenbank
      await this.options.databaseService.query(
        `UPDATE crypto_customers SET ? WHERE id = ?`,
        [customerData, customerId]
      );
      
      logger.info(`Krypto-Kunde aktualisiert: ${customerId}`);
      
      // Hole den aktualisierten Kunden
      return await this.getCustomer(customerId);
    } catch (error) {
      logger.error('Fehler beim Aktualisieren des Krypto-Kunden', {
        error: error.message,
        customerId,
        customer
      });
      throw error;
    }
  }
  
  /**
   * Ruft einen Kunden ab
   * @param {string} customerId - Kunden-ID
   * @returns {Promise<Object>} Kundendaten
   */
  async getCustomer(customerId) {
    try {
      logger.debug(`Rufe Krypto-Kunden ab: ${customerId}`);
      
      // Hole den Kunden aus der Datenbank
      const [customer] = await this.options.databaseService.query(
        `SELECT * FROM crypto_customers WHERE id = ?`,
        [customerId]
      );
      
      if (!customer) {
        return null;
      }
      
      // Konvertiere die Adressen und Metadaten
      let addresses = {};
      let metadata = {};
      
      try {
        addresses = JSON.parse(customer.addresses);
      } catch (error) {
        logger.warn(`Fehler beim Parsen der Adressen für Kunde ${customerId}`);
      }
      
      try {
        metadata = JSON.parse(customer.metadata);
      } catch (error) {
        logger.warn(`Fehler beim Parsen der Metadaten für Kunde ${customerId}`);
      }
      
      return {
        id: customer.id,
        name: customer.name,
        email: customer.email,
        addresses,
        createdAt: customer.created_at,
        updatedAt: customer.updated_at,
        metadata
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen des Krypto-Kunden', {
        error: error.message,
        customerId
      });
      throw error;
    }
  }
  
  /**
   * Ruft einen Kunden anhand der E-Mail-Adresse ab
   * @param {string} email - E-Mail-Adresse
   * @returns {Promise<Object>} Kundendaten
   */
  async getCustomerByEmail(email) {
    try {
      logger.debug(`Rufe Krypto-Kunden anhand der E-Mail-Adresse ab: ${email}`);
      
      // Hole den Kunden aus der Datenbank
      const [customer] = await this.options.databaseService.query(
        `SELECT * FROM crypto_customers WHERE email = ?`,
        [email]
      );
      
      if (!customer) {
        return null;
      }
      
      // Konvertiere die Adressen und Metadaten
      let addresses = {};
      let metadata = {};
      
      try {
        addresses = JSON.parse(customer.addresses);
      } catch (error) {
        logger.warn(`Fehler beim Parsen der Adressen für Kunde ${customer.id}`);
      }
      
      try {
        metadata = JSON.parse(customer.metadata);
      } catch (error) {
        logger.warn(`Fehler beim Parsen der Metadaten für Kunde ${customer.id}`);
      }
      
      return {
        id: customer.id,
        name: customer.name,
        email: customer.email,
        addresses,
        createdAt: customer.created_at,
        updatedAt: customer.updated_at,
        metadata
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen des Krypto-Kunden anhand der E-Mail-Adresse', {
        error: error.message,
        email
      });
      throw error;
    }
  }
  
  /**
   * Ruft Kunden ab
   * @param {Object} options - Abfrageoptionen
   * @returns {Promise<Array<Object>>} Kundendaten
   */
  async getCustomers(options = {}) {
    try {
      logger.debug('Rufe Krypto-Kunden ab');
      
      // Erstelle die Abfrage
      let query = 'SELECT * FROM crypto_customers';
      const params = [];
      
      // Füge Filter hinzu
      const conditions = [];
      
      if (options.email) {
        conditions.push('email = ?');
        params.push(options.email);
      }
      
      if (conditions.length > 0) {
        query += ' WHERE ' + conditions.join(' AND ');
      }
      
      // Füge Sortierung hinzu
      query += ' ORDER BY created_at DESC';
      
      // Füge Limit hinzu
      if (options.limit) {
        query += ' LIMIT ?';
        params.push(options.limit);
      }
      
      // Führe die Abfrage aus
      const customers = await this.options.databaseService.query(query, params);
      
      // Konvertiere die Kundendaten
      return customers.map(customer => {
        // Konvertiere die Adressen und Metadaten
        let addresses = {};
        let metadata = {};
        
        try {
          addresses = JSON.parse(customer.addresses);
        } catch (error) {
          logger.warn(`Fehler beim Parsen der Adressen für Kunde ${customer.id}`);
        }
        
        try {
          metadata = JSON.parse(customer.metadata);
        } catch (error) {
          logger.warn(`Fehler beim Parsen der Metadaten für Kunde ${customer.id}`);
        }
        
        return {
          id: customer.id,
          name: customer.name,
          email: customer.email,
          addresses,
          createdAt: customer.created_at,
          updatedAt: customer.updated_at,
          metadata
        };
      });
    } catch (error) {
      logger.error('Fehler beim Abrufen der Krypto-Kunden', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Generiert eine eindeutige ID
   * @returns {string} Eindeutige ID
   */
  generateId() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }
  
  /**
   * Holt die Empfängeradresse für eine Währung
   * @param {string} currency - Währung
   * @returns {Promise<string>} Empfängeradresse
   */
  async getReceivingAddress(currency) {
    try {
      logger.debug(`Hole Empfängeradresse für Währung: ${currency}`);
      
      // Hole die Währungskonfiguration
      const currencyConfig = this.options.supportedCurrencies[currency];
      
      if (!currencyConfig) {
        throw new Error(`Währung ${currency} wird nicht unterstützt`);
      }
      
      // Hole den Blockchain-Adapter
      const adapter = this.options.blockchainManager.selectAdapter({
        adapter: currencyConfig.adapter
      });
      
      // Hole die Adresse
      const address = await adapter.getAddress();
      
      return address;
    } catch (error) {
      logger.error('Fehler beim Holen der Empfängeradresse', {
        error: error.message,
        currency
      });
      throw error;
    }
  }
}/**
 * @fileoverview Krypto-Payment-Adapter für Zahlungen mit Kryptowährungen
 * 
 * Dieser Adapter implementiert das PaymentAdapter-Interface für Zahlungen mit
 * Kryptowährungen wie DOT, ETH, BTC, etc. Er nutzt die Blockchain-Adapter für
 * die Interaktion mit den entsprechenden Blockchains.
 */

import { PaymentAdapter } from './PaymentAdapter.js';
import { logger } from '../../utils/logger.js';

/**
 * Krypto-Payment-Adapter für Zahlungen mit Kryptowährungen
 */
export class CryptoPaymentAdapter extends PaymentAdapter {
  /**
   * Erstellt eine neue Instanz des CryptoPaymentAdapter
   * @param {Object} options - Konfigurationsoptionen
   * @param {Object} options.blockchainManager - BlockchainManager-Instanz
   * @param {Object} options.walletManager - WalletManager-Instanz
   * @param {Object} options.databaseService - Datenbankdienst
   * @param {Object} options.supportedCurrencies - Unterstützte Währungen
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      blockchainManager: options.blockchainManager,
      walletManager: options.walletManager,
      databaseService: options.databaseService,
      supportedCurrencies: options.supportedCurrencies || {
        DOT: {
          name: 'Polkadot',
          symbol: 'DOT',
          decimals: 10,
          adapter: 'polkadot',
          enabled: true
        },
        ETH: {
          name: 'Ethereum',
          symbol: 'ETH',
          decimals: 18,
          adapter: 'ethereum',
          enabled: true
        },
        USDT: {
          name: 'Tether',
          symbol: 'USDT',
          decimals: 6,
          adapter: 'ethereum',
          contractAddress: '******************************************',
          enabled: true
        }
      },
      ...options
    };
    
    this.isInitialized = false;
    
    logger.info('CryptoPaymentAdapter initialisiert');
  }
  
  /**
   * Initialisiert den Payment-Adapter
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      logger.info('Initialisiere CryptoPaymentAdapter');
      
      // Prüfe, ob die erforderlichen Dienste verfügbar sind
      if (!this.options.blockchainManager) {
        throw new Error('BlockchainManager ist erforderlich');
      }
      
      if (!this.options.databaseService) {
        throw new Error('DatabaseService ist erforderlich');
      }
      
      // Initialisiere die Datenbanktabellen
      await this.initializeDatabaseTables();
      
      this.isInitialized = true;
      
      logger.info('CryptoPaymentAdapter erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des CryptoPaymentAdapter', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Initialisiert die erforderlichen Datenbanktabellen
   * @returns {Promise<void>}
   */
  async initializeDatabaseTables() {
    try {
      // Prüfe, ob die Zahlungstabelle existiert
      const paymentTableExists = await this.options.databaseService.tableExists('crypto_payments');
      
      if (!paymentTableExists) {
        // Erstelle die Zahlungstabelle
        await this.options.databaseService.query(`
          CREATE TABLE crypto_payments (
            id VARCHAR(36) PRIMARY KEY,
            customer_id VARCHAR(36),
            amount DECIMAL(24, 8) NOT NULL,
            currency VARCHAR(10) NOT NULL,
            status VARCHAR(20) NOT NULL,
            from_address VARCHAR(100),
            to_address VARCHAR(100) NOT NULL,
            transaction_hash VARCHAR(100),
            block_number BIGINT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            metadata JSON,
            INDEX (customer_id),
            INDEX (status),
            INDEX (transaction_hash)
          )
        `);
        
        logger.info('Krypto-Zahlungstabelle erstellt');
      }
      
      // Prüfe, ob die Kundentabelle existiert
      const customerTableExists = await this.options.databaseService.tableExists('crypto_customers');
      
      if (!customerTableExists) {
        // Erstelle die Kundentabelle
        await this.options.databaseService.query(`
          CREATE TABLE crypto_customers (
            id VARCHAR(36) PRIMARY KEY,
            name VARCHAR(100),
            email VARCHAR(100),
            addresses JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            metadata JSON,
            INDEX (email)
          )
        `);
        
        logger.info('Krypto-Kundentabelle erstellt');
      }
    } catch (error) {
      logger.error('Fehler bei der Initialisierung der Datenbanktabellen', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Erstellt eine Zahlung
   * @param {Object} payment - Zahlungsdaten
   * @returns {Promise<Object>} Zahlungsergebnis
   */
  async createPayment(payment) {
    try {
      logger.info('Erstelle Krypto-Zahlung');
      
      // Validiere die Zahlungsdaten
      if (!payment.amount || !payment.currency) {
        throw new Error('Betrag und Währung sind erforderlich');
      }
      
      // Prüfe, ob die Währung unterstützt wird
      if (!this.options.supportedCurrencies[payment.currency]) {
        throw new Error(`Währung ${payment.currency} wird nicht unterstützt`);
      }
      
      // Prüfe, ob die Währung aktiviert ist
      if (!this.options.supportedCurrencies[payment.currency].enabled) {
        throw new Error(`Währung ${payment.currency} ist deaktiviert`);
      }
      
      // Generiere eine eindeutige Zahlungs-ID
      const paymentId = this.generateId();
      
      // Hole die Empfängeradresse
      const toAddress = payment.toAddress || await this.getReceivingAddress(payment.currency);
      
      // Erstelle die Zahlungsdaten
      const paymentData = {
        id: paymentId,
        customer_id: payment.customerId || null,
        amount: payment.amount,
        currency: payment.currency,
        status: 'pending',
        from_address: payment.fromAddress || null,
        to_address: toAddress,
        transaction_hash: null,
        block_number: null,
        metadata: JSON.stringify(payment.metadata || {})
      };
      
      // Speichere die Zahlung in der Datenbank
      await this.options.databaseService.query(
        `INSERT INTO crypto_payments SET ?`,
        paymentData
      );
      
      logger.info(`Krypto-Zahlung erstellt: ${paymentId}`);
      
      return {
        id: paymentId,
        amount: payment.amount,
        currency: payment.currency,
        status: 'pending',
        toAddress,
        createdAt: new Date().toISOString()
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen der Krypto-Zahlung', {
        error: error.message,
        payment
      });
      throw error;
    }
  }
  
  /**
   * Verarbeitet eine Zahlung
   * @param {string} paymentId - Zahlungs-ID
   * @param {Object} options - Verarbeitungsoptionen
   * @returns {Promise<Object>} Verarbeitungsergebnis
   */
  async processPayment(paymentId, options = {}) {
    try {
      logger.info(`Verarbeite Krypto-Zahlung: ${paymentId}`);
      
      // Hole die Zahlungsdaten
      const payment = await this.getPayment(paymentId);
      
      if (!payment) {
        throw new Error(`Zahlung mit ID ${paymentId} nicht gefunden`);
      }
      
      // Prüfe, ob die Zahlung bereits verarbeitet wurde
      if (payment.status === 'completed') {
        return {
          id: paymentId,
          status: 'completed',
          message: 'Zahlung wurde bereits verarbeitet'
        };
      }
      
      // Prüfe, ob die Zahlung storniert wurde
      if (payment.status === 'cancelled') {
        throw new Error('Zahlung wurde storniert');
      }
      
      // Hole die Währungskonfiguration
      const currencyConfig = this.options.supportedCurrencies[payment.currency];
      
      // Prüfe, ob eine Transaktions-Hash angegeben wurde
      if (options.transactionHash) {
        // Aktualisiere die Zahlung mit dem Transaktions-Hash
        await this.options.databaseService.query(
          `UPDATE crypto_payments SET transaction_hash = ?, status = 'processing', from_address = ? WHERE id = ?`,
          [options.transactionHash, options.fromAddress || payment.from_address, paymentId]
        );
        
        // Hole den Blockchain-Adapter
        const adapter = this.options.blockchainManager.selectAdapter({
          adapter: currencyConfig.adapter
        });
        
        // Warte auf die Bestätigung der Transaktion
        const confirmations = options.confirmations || 1;
        const txResult = await adapter.waitForTransaction(options.transactionHash, confirmations);
        
        // Prüfe, ob die Transaktion erfolgreich war
        if (txResult.status === 'success') {
          // Aktualisiere die Zahlung
          await this.options.databaseService.query(
            `UPDATE crypto_payments SET status = 'completed', block_number = ? WHERE id = ?`,
            [txResult.blockNumber, paymentId]
          );
          
          logger.info(`Krypto-Zahlung erfolgreich verarbeitet: ${paymentId}`);
          
          return {
            id: paymentId,
            status: 'completed',
            transactionHash: options.transactionHash,
            blockNumber: txResult.blockNumber
          };
        } else {
          // Aktualisiere die Zahlung
          await this.options.databaseService.query(
            `UPDATE crypto_payments SET status = 'failed' WHERE id = ?`,
            [paymentId]
          );
          
          throw new Error('Transaktion fehlgeschlagen');
        }
      } else if (options.fromAddress && options.amount) {
        // Manuelle Verarbeitung (z.B. für Tests oder Admin-Funktionen)
        await this.options.databaseService.query(
          `UPDATE crypto_payments SET status = 'completed', from_address = ? WHERE id = ?`,
          [options.fromAddress, paymentId]
        );
        
        logger.info(`Krypto-Zahlung manuell verarbeitet: ${paymentId}`);
        
        return {
          id: paymentId,
          status: 'completed',
          message: 'Zahlung manuell verarbeitet'
        };
      } else {
        throw new Error('Transaktions-Hash oder manuelle Verarbeitungsdaten erforderlich');
      }
    } catch (error) {
      logger.error('Fehler bei der Verarbeitung der Krypto-Zahlung', {
        error: error.message,
        paymentId
      });
      throw error;
    }
  }
  
  /**
   * Storniert eine Zahlung
   * @param {string} paymentId - Zahlungs-ID
   * @param {Object} options - Stornierungsoptionen
   * @returns {Promise<Object>} Stornierungsergebnis
   */
  async cancelPayment(paymentId, options = {}) {
    try {
      logger.info(`Storniere Krypto-Zahlung: ${paymentId}`);
      
      // Hole die Zahlungsdaten
      const payment = await this.getPayment(paymentId);
      
      if (!payment) {
        throw new Error(`Zahlung mit ID ${paymentId} nicht gefunden`);
      }
      
      // Prüfe, ob die Zahlung bereits verarbeitet wurde
      if (payment.status === 'completed') {
        throw new Error('Zahlung wurde bereits verarbeitet und kann nicht storniert werden');
      }
      
      // Prüfe, ob die Zahlung bereits storniert wurde
      if (payment.status === 'cancelled') {
        return {
          id: paymentId,
          status: 'cancelled',
          message: 'Zahlung wurde bereits storniert'
        };
      }
      
      // Aktualisiere die Zahlung
      await this.options.databaseService.query(
        `UPDATE crypto_payments SET status = 'cancelled' WHERE id = ?`,
        [paymentId]
      );
      
      logger.info(`Krypto-Zahlung storniert: ${paymentId}`);
      
      return {
        id: paymentId,
        status: 'cancelled'
      };
    } catch (error) {
      logger.error('Fehler bei der Stornierung der Krypto-Zahlung', {
        error: error.message,
        paymentId
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Zahlung ab
   * @param {string} paymentId - Zahlungs-ID
   * @returns {Promise<Object>} Zahlungsdaten
   */
  async getPayment(paymentId) {
    try {
      logger.debug(`Rufe Krypto-Zahlung ab: ${paymentId}`);
      
      // Hole die Zahlungsdaten aus der Datenbank
      const [payment] = await this.options.databaseService.query(
        `SELECT * FROM crypto_payments WHERE id = ?`,
        [paymentId]
      );
      
      if (!payment) {
        return null;
      }
      
      // Konvertiere die Metadaten
      let metadata = {};
      try {
        metadata = JSON.parse(payment.metadata);
      } catch (error) {
        logger.warn(`Fehler beim Parsen der Metadaten für Zahlung ${paymentId}`);
      }
      
      return {
        id: payment.id,
        customerId: payment.customer_id,
        amount: parseFloat(payment.amount),
        currency: payment.currency,
        status: payment.status,
        fromAddress: payment.from_address,
        toAddress: payment.to_address,
        transactionHash: payment.transaction_hash,
        blockNumber: payment.block_number,
        createdAt: payment.created_at,
        updatedAt: payment.updated_at,
        metadata
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen der Krypto-Zahlung', {
        error: error.message,
        paymentId
      });
      throw error;
    }
  }
  
  /**
   * Ruft Zahlungen ab
   * @param {Object} options - Abfrageoptionen
   * @returns {Promise<Array<Object>>} Zahlungsdaten
   */
  async getPayments(options = {}) {
    try {
      logger.debug('Rufe Krypto-Zahlungen ab');
      
      // Erstelle die Abfrage
      let query = 'SELECT * FROM crypto_payments';
      const params = [];
      
      // Füge Filter hinzu
      const conditions = [];
      
      if (options.customerId) {
        conditions.push('customer_id = ?');
        params.push(options.customerId);
      }
      
      if (options.status) {
        conditions.push('status = ?');
        params.push(options.status);
      }
      
      if (options.currency) {
        conditions.push('currency = ?');
        params.push(options.currency);
      }
      
      if (options.fromAddress) {
        conditions.push('from_address = ?');
        params.push(options.fromAddress);
      }
      
      if (options.toAddress) {
        conditions.push('to_address = ?');
        params.push(options.toAddress);
      }
      
      if (options.transactionHash) {
        conditions.push('transaction_hash = ?');
        params.push(options.transactionHash);
      }
      
      if (conditions.length > 0) {
        query += ' WHERE ' + conditions.join(' AND ');
      }
      
      // Füge Sortierung hinzu
      query += ' ORDER BY created_at DESC';
      
      // Füge Limit hinzu
      if (options.limit) {
        query += ' LIMIT ?';
        params.push(options.limit);
      }
      
      // Führe die Abfrage aus
      const payments = await this.options.databaseService.query(query, params);
      
      // Konvertiere die Zahlungsdaten
      return payments.map(payment => {
        // Konvertiere die Metadaten
        let metadata = {};
        try {
          metadata = JSON.parse(payment.metadata);
        } catch (error) {
          logger.warn(`Fehler beim Parsen der Metadaten für Zahlung ${payment.id}`);
        }
        
        return {
          id: payment.id,
          customerId: payment.customer_id,
          amount: parseFloat(payment.amount),
          currency: payment.currency,
          status: payment.status,
          fromAddress: payment.from_address,
          toAddress: payment.to_address,
          transactionHash: payment.transaction_hash,
          blockNumber: payment.block_number,
          createdAt: payment.created_at,
          updatedAt: payment.updated_at,
          metadata
        };
      });
    } catch (error) {
      logger.error('Fehler beim Abrufen der Krypto-Zahlungen', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Erstellt einen Kunden
   * @param {Object} customer - Kundendaten
   * @returns {Promise<Object>} Kundenergebnis
   */
  async createCustomer(customer) {
    try {
      logger.info('Erstelle Krypto-Kunden');
      
      // Validiere die Kundendaten
      if (!customer.email) {
        throw new Error('E-Mail ist erforderlich');
      }
      
      // Prüfe, ob der Kunde bereits existiert
      const existingCustomer = await this.getCustomerByEmail(customer.email);
      
      if (existingCustomer) {
        return existingCustomer;
      }
      
      // Generiere eine eindeutige Kunden-ID
      const customerId = this.generateId();
      
      // Erstelle die Kundendaten
      const customerData = {
        id: customerId,
        name: customer.name || null,
        email: customer.email,
        addresses: JSON.stringify(customer.addresses || {}),
        metadata: JSON.stringify(customer.metadata || {})
      };
      
      // Speichere den Kunden in der Datenbank
      await this.options.databaseService.query(
        `INSERT INTO crypto_customers SET ?`,
        customerData
      );
      
      logger.info(`Krypto-Kunde erstellt: ${customerId}`);
      
      return {
        id: customerId,
        name: customer.name,
        email: customer.email,
        addresses: customer.addresses || {},
        createdAt: new Date().toISOString()
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen des Krypto-Kunden', {
        error: error.message,
        customer
      });
      throw error;
    }
  }
  
  /**
   * Aktualisiert einen Kunden
   * @param {string} customerId - Kunden-ID
   * @param {Object} customer - Kundendaten
   * @returns {Promise<Object>} Kundenergebnis
   */
  async updateCustomer(customerId, customer) {
    try {
      logger.info(`Aktualisiere Krypto-Kunden: ${customerId}`);
      
      // Hole den Kunden
      const existingCustomer = await this.getCustomer(customerId);
      
      if (!existingCustomer) {
        throw new Error(`Kunde mit ID ${customerId} nicht gefunden`);
      }
      
      // Erstelle die Kundendaten
      const customerData = {};
      
      if (customer.name !== undefined) {
        customerData.name = customer.name;
      }
      
      if (customer.email !== undefined) {
        customerData.email = customer.email;
      }
      
      if (customer.addresses !== undefined) {
        customerData.addresses = JSON.stringify(customer.addresses);
      }
      
      if (customer.metadata !== undefined) {
        customerData.metadata = JSON.stringify(customer.metadata);
      }
      
      // Aktualisiere den Kunden in der Datenbank
      await this.options.databaseService.query(
        `UPDATE crypto_customers SET ? WHERE id = ?`,
        [customerData, customerId]
      );
      
      logger.info(`Krypto-Kunde aktualisiert: ${customerId}`);
      
      // Hole den aktualisierten Kunden
      return await this.getCustomer(customerId);
    } catch (error) {
      logger.error('Fehler beim Aktualisieren des Krypto-Kunden', {
        error: error.message,
        customerId,
        customer
      });
      throw error;
    }
  }
  
  /**
   * Ruft einen Kunden ab
   * @param {string} customerId - Kunden-ID
   * @returns {Promise<Object>} Kundendaten
   */
  async getCustomer(customerId) {
    try {
      logger.debug(`Rufe Krypto-Kunden ab: ${customerId}`);
      
      // Hole den Kunden aus der Datenbank
      const [customer] = await this.options.databaseService.query(
        `SELECT * FROM crypto_customers WHERE id = ?`,
        [customerId]
      );
      
      if (!customer) {
        return null;
      }
      
      // Konvertiere die Adressen und Metadaten
      let addresses = {};
      let metadata = {};
      
      try {
        addresses = JSON.parse(customer.addresses);
      } catch (error) {
        logger.warn(`Fehler beim Parsen der Adressen für Kunde ${customerId}`);
      }
      
      try {
        metadata = JSON.parse(customer.metadata);
      } catch (error) {
        logger.warn(`Fehler beim Parsen der Metadaten für Kunde ${customerId}`);
      }
      
      return {
        id: customer.id,
        name: customer.name,
        email: customer.email,
        addresses,
        createdAt: customer.created_at,
        updatedAt: customer.updated_at,
        metadata
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen des Krypto-Kunden', {
        error: error.message,
        customerId
      });
      throw error;
    }
  }
  
  /**
   * Ruft einen Kunden anhand der E-Mail-Adresse ab
   * @param {string} email - E-Mail-Adresse
   * @returns {Promise<Object>} Kundendaten
   */
  async getCustomerByEmail(email) {
    try {
      logger.debug(`Rufe Krypto-Kunden anhand der E-Mail-Adresse ab: ${email}`);
      
      // Hole den Kunden aus der Datenbank
      const [customer] = await this.options.databaseService.query(
        `SELECT * FROM crypto_customers WHERE email = ?`,
        [email]
      );
      
      if (!customer) {
        return null;
      }
      
      // Konvertiere die Adressen und Metadaten
      let addresses = {};
      let metadata = {};
      
      try {
        addresses = JSON.parse(customer.addresses);
      } catch (error) {
        logger.warn(`Fehler beim Parsen der Adressen für Kunde ${customer.id}`);
      }
      
      try {
        metadata = JSON.parse(customer.metadata);
      } catch (error) {
        logger.warn(`Fehler beim Parsen der Metadaten für Kunde ${customer.id}`);
      }
      
      return {
        id: customer.id,
        name: customer.name,
        email: customer.email,
        addresses,
        createdAt: customer.created_at,
        updatedAt: customer.updated_at,
        metadata
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen des Krypto-Kunden anhand der E-Mail-Adresse', {
        error: error.message,
        email
      });
      throw error;
    }
  }
  
  /**
   * Ruft Kunden ab
   * @param {Object} options - Abfrageoptionen
   * @returns {Promise<Array<Object>>} Kundendaten
   */
  async getCustomers(options = {}) {
    try {
      logger.debug('Rufe Krypto-Kunden ab');
      
      // Erstelle die Abfrage
      let query = 'SELECT * FROM crypto_customers';
      const params = [];
      
      // Füge Filter hinzu
      const conditions = [];
      
      if (options.email) {
        conditions.push('email = ?');
        params.push(options.email);
      }
      
      if (conditions.length > 0) {
        query += ' WHERE ' + conditions.join(' AND ');
      }
      
      // Füge Sortierung hinzu
      query += ' ORDER BY created_at DESC';
      
      // Füge Limit hinzu
      if (options.limit) {
        query += ' LIMIT ?';
        params.push(options.limit);
      }
      
      // Führe die Abfrage aus
      const customers = await this.options.databaseService.query(query, params);
      
      // Konvertiere die Kundendaten
      return customers.map(customer => {
        // Konvertiere die Adressen und Metadaten
        let addresses = {};
        let metadata = {};
        
        try {
          addresses = JSON.parse(customer.addresses);
        } catch (error) {
          logger.warn(`Fehler beim Parsen der Adressen für Kunde ${customer.id}`);
        }
        
        try {
          metadata = JSON.parse(customer.metadata);
        } catch (error) {
          logger.warn(`Fehler beim Parsen der Metadaten für Kunde ${customer.id}`);
        }
        
        return {
          id: customer.id,
          name: customer.name,
          email: customer.email,
          addresses,
          createdAt: customer.created_at,
          updatedAt: customer.updated_at,
          metadata
        };
      });
    } catch (error) {
      logger.error('Fehler beim Abrufen der Krypto-Kunden', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Generiert eine eindeutige ID
   * @returns {string} Eindeutige ID
   */
  generateId() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }
  
  /**
   * Holt die Empfängeradresse für eine Währung
   * @param {string} currency - Währung
   * @returns {Promise<string>} Empfängeradresse
   */
  async getReceivingAddress(currency) {
    try {
      logger.debug(`Hole Empfängeradresse für Währung: ${currency}`);
      
      // Hole die Währungskonfiguration
      const currencyConfig = this.options.supportedCurrencies[currency];
      
      if (!currencyConfig) {
        throw new Error(`Währung ${currency} wird nicht unterstützt`);
      }
      
      // Hole den Blockchain-Adapter
      const adapter = this.options.blockchainManager.selectAdapter({
        adapter: currencyConfig.adapter
      });
      
      // Hole die Adresse
      const address = await adapter.getAddress();
      
      return address;
    } catch (error) {
      logger.error('Fehler beim Holen der Empfängeradresse', {
        error: error.message,
        currency
      });
      throw error;
    }
  }
}/**
 * @fileoverview Krypto-Payment-Adapter für Zahlungen mit Kryptowährungen
 * 
 * Dieser Adapter implementiert das PaymentAdapter-Interface für Zahlungen mit
 * Kryptowährungen wie DOT, ETH, BTC, etc. Er nutzt die Blockchain-Adapter für
 * die Interaktion mit den entsprechenden Blockchains.
 */

import { PaymentAdapter } from './PaymentAdapter.js';
import { logger } from '../../utils/logger.js';

/**
 * Krypto-Payment-Adapter für Zahlungen mit Kryptowährungen
 */
export class CryptoPaymentAdapter extends PaymentAdapter {
  /**
   * Erstellt eine neue Instanz des CryptoPaymentAdapter
   * @param {Object} options - Konfigurationsoptionen
   * @param {Object} options.blockchainManager - BlockchainManager-Instanz
   * @param {Object} options.walletManager - WalletManager-Instanz
   * @param {Object} options.databaseService - Datenbankdienst
   * @param {Object} options.supportedCurrencies - Unterstützte Währungen
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      blockchainManager: options.blockchainManager,
      walletManager: options.walletManager,
      databaseService: options.databaseService,
      supportedCurrencies: options.supportedCurrencies || {
        DOT: {
          name: 'Polkadot',
          symbol: 'DOT',
          decimals: 10,
          adapter: 'polkadot',
          enabled: true
        },
        ETH: {
          name: 'Ethereum',
          symbol: 'ETH',
          decimals: 18,
          adapter: 'ethereum',
          enabled: true
        },
        USDT: {
          name: 'Tether',
          symbol: 'USDT',
          decimals: 6,
          adapter: 'ethereum',
          contractAddress: '******************************************',
          enabled: true
        }
      },
      ...options
    };
    
    this.isInitialized = false;
    
    logger.info('CryptoPaymentAdapter initialisiert');
  }
  
  /**
   * Initialisiert den Payment-Adapter
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      logger.info('Initialisiere CryptoPaymentAdapter');
      
      // Prüfe, ob die erforderlichen Dienste verfügbar sind
      if (!this.options.blockchainManager) {
        throw new Error('BlockchainManager ist erforderlich');
      }
      
      if (!this.options.databaseService) {
        throw new Error('DatabaseService ist erforderlich');
      }
      
      // Initialisiere die Datenbanktabellen
      await this.initializeDatabaseTables();
      
      this.isInitialized = true;
      
      logger.info('CryptoPaymentAdapter erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des CryptoPaymentAdapter', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Initialisiert die erforderlichen Datenbanktabellen
   * @returns {Promise<void>}
   */
  async initializeDatabaseTables() {
    try {
      // Prüfe, ob die Zahlungstabelle existiert
      const paymentTableExists = await this.options.databaseService.tableExists('crypto_payments');
      
      if (!paymentTableExists) {
        // Erstelle die Zahlungstabelle
        await this.options.databaseService.query(`
          CREATE TABLE crypto_payments (
            id VARCHAR(36) PRIMARY KEY,
            customer_id VARCHAR(36),
            amount DECIMAL(24, 8) NOT NULL,
            currency VARCHAR(10) NOT NULL,
            status VARCHAR(20) NOT NULL,
            from_address VARCHAR(100),
            to_address VARCHAR(100) NOT NULL,
            transaction_hash VARCHAR(100),
            block_number BIGINT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            metadata JSON,
            INDEX (customer_id),
            INDEX (status),
            INDEX (transaction_hash)
          )
        `);
        
        logger.info('Krypto-Zahlungstabelle erstellt');
      }
      
      // Prüfe, ob die Kundentabelle existiert
      const customerTableExists = await this.options.databaseService.tableExists('crypto_customers');
      
      if (!customerTableExists) {
        // Erstelle die Kundentabelle
        await this.options.databaseService.query(`
          CREATE TABLE crypto_customers (
            id VARCHAR(36) PRIMARY KEY,
            name VARCHAR(100),
            email VARCHAR(100),
            addresses JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            metadata JSON,
            INDEX (email)
          )
        `);
        
        logger.info('Krypto-Kundentabelle erstellt');
      }
    } catch (error) {
      logger.error('Fehler bei der Initialisierung der Datenbanktabellen', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Erstellt eine Zahlung
   * @param {Object} payment - Zahlungsdaten
   * @returns {Promise<Object>} Zahlungsergebnis
   */
  async createPayment(payment) {
    try {
      logger.info('Erstelle Krypto-Zahlung');
      
      // Validiere die Zahlungsdaten
      if (!payment.amount || !payment.currency) {
        throw new Error('Betrag und Währung sind erforderlich');
      }
      
      // Prüfe, ob die Währung unterstützt wird
      if (!this.options.supportedCurrencies[payment.currency]) {
        throw new Error(`Währung ${payment.currency} wird nicht unterstützt`);
      }
      
      // Prüfe, ob die Währung aktiviert ist
      if (!this.options.supportedCurrencies[payment.currency].enabled) {
        throw new Error(`Währung ${payment.currency} ist deaktiviert`);
      }
      
      // Generiere eine eindeutige Zahlungs-ID
      const paymentId = this.generateId();
      
      // Hole die Empfängeradresse
      const toAddress = payment.toAddress || await this.getReceivingAddress(payment.currency);
      
      // Erstelle die Zahlungsdaten
      const paymentData = {
        id: paymentId,
        customer_id: payment.customerId || null,
        amount: payment.amount,
        currency: payment.currency,
        status: 'pending',
        from_address: payment.fromAddress || null,
        to_address: toAddress,
        transaction_hash: null,
        block_number: null,
        metadata: JSON.stringify(payment.metadata || {})
      };
      
      // Speichere die Zahlung in der Datenbank
      await this.options.databaseService.query(
        `INSERT INTO crypto_payments SET ?`,
        paymentData
      );
      
      logger.info(`Krypto-Zahlung erstellt: ${paymentId}`);
      
      return {
        id: paymentId,
        amount: payment.amount,
        currency: payment.currency,
        status: 'pending',
        toAddress,
        createdAt: new Date().toISOString()
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen der Krypto-Zahlung', {
        error: error.message,
        payment
      });
      throw error;
    }
  }
  
  /**
   * Verarbeitet eine Zahlung
   * @param {string} paymentId - Zahlungs-ID
   * @param {Object} options - Verarbeitungsoptionen
   * @returns {Promise<Object>} Verarbeitungsergebnis
   */
  async processPayment(paymentId, options = {}) {
    try {
      logger.info(`Verarbeite Krypto-Zahlung: ${paymentId}`);
      
      // Hole die Zahlungsdaten
      const payment = await this.getPayment(paymentId);
      
      if (!payment) {
        throw new Error(`Zahlung mit ID ${paymentId} nicht gefunden`);
      }
      
      // Prüfe, ob die Zahlung bereits verarbeitet wurde
      if (payment.status === 'completed') {
        return {
          id: paymentId,
          status: 'completed',
          message: 'Zahlung wurde bereits verarbeitet'
        };
      }
      
      // Prüfe, ob die Zahlung storniert wurde
      if (payment.status === 'cancelled') {
        throw new Error('Zahlung wurde storniert');
      }
      
      // Hole die Währungskonfiguration
      const currencyConfig = this.options.supportedCurrencies[payment.currency];
      
      // Prüfe, ob eine Transaktions-Hash angegeben wurde
      if (options.transactionHash) {
        // Aktualisiere die Zahlung mit dem Transaktions-Hash
        await this.options.databaseService.query(
          `UPDATE crypto_payments SET transaction_hash = ?, status = 'processing', from_address = ? WHERE id = ?`,
          [options.transactionHash, options.fromAddress || payment.from_address, paymentId]
        );
        
        // Hole den Blockchain-Adapter
        const adapter = this.options.blockchainManager.selectAdapter({
          adapter: currencyConfig.adapter
        });
        
        // Warte auf die Bestätigung der Transaktion
        const confirmations = options.confirmations || 1;
        const txResult = await adapter.waitForTransaction(options.transactionHash, confirmations);
        
        // Prüfe, ob die Transaktion erfolgreich war
        if (txResult.status === 'success') {
          // Aktualisiere die Zahlung
          await this.options.databaseService.query(
            `UPDATE crypto_payments SET status = 'completed', block_number = ? WHERE id = ?`,
            [txResult.blockNumber, paymentId]
          );
          
          logger.info(`Krypto-Zahlung erfolgreich verarbeitet: ${paymentId}`);
          
          return {
            id: paymentId,
            status: 'completed',
            transactionHash: options.transactionHash,
            blockNumber: txResult.blockNumber
          };
        } else {
          // Aktualisiere die Zahlung
          await this.options.databaseService.query(
            `UPDATE crypto_payments SET status = 'failed' WHERE id = ?`,
            [paymentId]
          );
          
          throw new Error('Transaktion fehlgeschlagen');
        }
      } else if (options.fromAddress && options.amount) {
        // Manuelle Verarbeitung (z.B. für Tests oder Admin-Funktionen)
        await this.options.databaseService.query(
          `UPDATE crypto_payments SET status = 'completed', from_address = ? WHERE id = ?`,
          [options.fromAddress, paymentId]
        );
        
        logger.info(`Krypto-Zahlung manuell verarbeitet: ${paymentId}`);
        
        return {
          id: paymentId,
          status: 'completed',
          message: 'Zahlung manuell verarbeitet'
        };
      } else {
        throw new Error('Transaktions-Hash oder manuelle Verarbeitungsdaten erforderlich');
      }
    } catch (error) {
      logger.error('Fehler bei der Verarbeitung der Krypto-Zahlung', {
        error: error.message,
        paymentId
      });
      throw error;
    }
  }
  
  /**
   * Storniert eine Zahlung
   * @param {string} paymentId - Zahlungs-ID
   * @param {Object} options - Stornierungsoptionen
   * @returns {Promise<Object>} Stornierungsergebnis
   */
  async cancelPayment(paymentId, options = {}) {
    try {
      logger.info(`Storniere Krypto-Zahlung: ${paymentId}`);
      
      // Hole die Zahlungsdaten
      const payment = await this.getPayment(paymentId);
      
      if (!payment) {
        throw new Error(`Zahlung mit ID ${paymentId} nicht gefunden`);
      }
      
      // Prüfe, ob die Zahlung bereits verarbeitet wurde
      if (payment.status === 'completed') {
        throw new Error('Zahlung wurde bereits verarbeitet und kann nicht storniert werden');
      }
      
      // Prüfe, ob die Zahlung bereits storniert wurde
      if (payment.status === 'cancelled') {
        return {
          id: paymentId,
          status: 'cancelled',
          message: 'Zahlung wurde bereits storniert'
        };
      }
      
      // Aktualisiere die Zahlung
      await this.options.databaseService.query(
        `UPDATE crypto_payments SET status = 'cancelled' WHERE id = ?`,
        [paymentId]
      );
      
      logger.info(`Krypto-Zahlung storniert: ${paymentId}`);
      
      return {
        id: paymentId,
        status: 'cancelled'
      };
    } catch (error) {
      logger.error('Fehler bei der Stornierung der Krypto-Zahlung', {
        error: error.message,
        paymentId
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Zahlung ab
   * @param {string} paymentId - Zahlungs-ID
   * @returns {Promise<Object>} Zahlungsdaten
   */
  async getPayment(paymentId) {
    try {
      logger.debug(`Rufe Krypto-Zahlung ab: ${paymentId}`);
      
      // Hole die Zahlungsdaten aus der Datenbank
      const [payment] = await this.options.databaseService.query(
        `SELECT * FROM crypto_payments WHERE id = ?`,
        [paymentId]
      );
      
      if (!payment) {
        return null;
      }
      
      // Konvertiere die Metadaten
      let metadata = {};
      try {
        metadata = JSON.parse(payment.metadata);
      } catch (error) {
        logger.warn(`Fehler beim Parsen der Metadaten für Zahlung ${paymentId}`);
      }
      
      return {
        id: payment.id,
        customerId: payment.customer_id,
        amount: parseFloat(payment.amount),
        currency: payment.currency,
        status: payment.status,
        fromAddress: payment.from_address,
        toAddress: payment.to_address,
        transactionHash: payment.transaction_hash,
        blockNumber: payment.block_number,
        createdAt: payment.created_at,
        updatedAt: payment.updated_at,
        metadata
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen der Krypto-Zahlung', {
        error: error.message,
        paymentId
      });
      throw error;
    }
  }
  
  /**
   * Ruft Zahlungen ab
   * @param {Object} options - Abfrageoptionen
   * @returns {Promise<Array<Object>>} Zahlungsdaten
   */
  async getPayments(options = {}) {
    try {
      logger.debug('Rufe Krypto-Zahlungen ab');
      
      // Erstelle die Abfrage
      let query = 'SELECT * FROM crypto_payments';
      const params = [];
      
      // Füge Filter hinzu
      const conditions = [];
      
      if (options.customerId) {
        conditions.push('customer_id = ?');
        params.push(options.customerId);
      }
      
      if (options.status) {
        conditions.push('status = ?');
        params.push(options.status);
      }
      
      if (options.currency) {
        conditions.push('currency = ?');
        params.push(options.currency);
      }
      
      if (options.fromAddress) {
        conditions.push('from_address = ?');
        params.push(options.fromAddress);
      }
      
      if (options.toAddress) {
        conditions.push('to_address = ?');
        params.push(options.toAddress);
      }
      
      if (options.transactionHash) {
        conditions.push('transaction_hash = ?');
        params.push(options.transactionHash);
      }
      
      if (conditions.length > 0) {
        query += ' WHERE ' + conditions.join(' AND ');
      }
      
      // Füge Sortierung hinzu
      query += ' ORDER BY created_at DESC';
      
      // Füge Limit hinzu
      if (options.limit) {
        query += ' LIMIT ?';
        params.push(options.limit);
      }
      
      // Führe die Abfrage aus
      const payments = await this.options.databaseService.query(query, params);
      
      // Konvertiere die Zahlungsdaten
      return payments.map(payment => {
        // Konvertiere die Metadaten
        let metadata = {};
        try {
          metadata = JSON.parse(payment.metadata);
        } catch (error) {
          logger.warn(`Fehler beim Parsen der Metadaten für Zahlung ${payment.id}`);
        }
        
        return {
          id: payment.id,
          customerId: payment.customer_id,
          amount: parseFloat(payment.amount),
          currency: payment.currency,
          status: payment.status,
          fromAddress: payment.from_address,
          toAddress: payment.to_address,
          transactionHash: payment.transaction_hash,
          blockNumber: payment.block_number,
          createdAt: payment.created_at,
          updatedAt: payment.updated_at,
          metadata
        };
      });
    } catch (error) {
      logger.error('Fehler beim Abrufen der Krypto-Zahlungen', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Erstellt einen Kunden
   * @param {Object} customer - Kundendaten
   * @returns {Promise<Object>} Kundenergebnis
   */
  async createCustomer(customer) {
    try {
      logger.info('Erstelle Krypto-Kunden');
      
      // Validiere die Kundendaten
      if (!customer.email) {
        throw new Error('E-Mail ist erforderlich');
      }
      
      // Prüfe, ob der Kunde bereits existiert
      const existingCustomer = await this.getCustomerByEmail(customer.email);
      
      if (existingCustomer) {
        return existingCustomer;
      }
      
      // Generiere eine eindeutige Kunden-ID
      const customerId = this.generateId();
      
      // Erstelle die Kundendaten
      const customerData = {
        id: customerId,
        name: customer.name || null,
        email: customer.email,
        addresses: JSON.stringify(customer.addresses || {}),
        metadata: JSON.stringify(customer.metadata || {})
      };
      
      // Speichere den Kunden in der Datenbank
      await this.options.databaseService.query(
        `INSERT INTO crypto_customers SET ?`,
        customerData
      );
      
      logger.info(`Krypto-Kunde erstellt: ${customerId}`);
      
      return {
        id: customerId,
        name: customer.name,
        email: customer.email,
        addresses: customer.addresses || {},
        createdAt: new Date().toISOString()
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen des Krypto-Kunden', {
        error: error.message,
        customer
      });
      throw error;
    }
  }
  
  /**
   * Aktualisiert einen Kunden
   * @param {string} customerId - Kunden-ID
   * @param {Object} customer - Kundendaten
   * @returns {Promise<Object>} Kundenergebnis
   */
  async updateCustomer(customerId, customer) {
    try {
      logger.info(`Aktualisiere Krypto-Kunden: ${customerId}`);
      
      // Hole den Kunden
      const existingCustomer = await this.getCustomer(customerId);
      
      if (!existingCustomer) {
        throw new Error(`Kunde mit ID ${customerId} nicht gefunden`);
      }
      
      // Erstelle die Kundendaten
      const customerData = {};
      
      if (customer.name !== undefined) {
        customerData.name = customer.name;
      }
      
      if (customer.email !== undefined) {
        customerData.email = customer.email;
      }
      
      if (customer.addresses !== undefined) {
        customerData.addresses = JSON.stringify(customer.addresses);
      }
      
      if (customer.metadata !== undefined) {
        customerData.metadata = JSON.stringify(customer.metadata);
      }
      
      // Aktualisiere den Kunden in der Datenbank
      await this.options.databaseService.query(
        `UPDATE crypto_customers SET ? WHERE id = ?`,
        [customerData, customerId]
      );
      
      logger.info(`Krypto-Kunde aktualisiert: ${customerId}`);
      
      // Hole den aktualisierten Kunden
      return await this.getCustomer(customerId);
    } catch (error) {
      logger.error('Fehler beim Aktualisieren des Krypto-Kunden', {
        error: error.message,
        customerId,
        customer
      });
      throw error;
    }
  }
  
  /**
   * Ruft einen Kunden ab
   * @param {string} customerId - Kunden-ID
   * @returns {Promise<Object>} Kundendaten
   */
  async getCustomer(customerId) {
    try {
      logger.debug(`Rufe Krypto-Kunden ab: ${customerId}`);
      
      // Hole den Kunden aus der Datenbank
      const [customer] = await this.options.databaseService.query(
        `SELECT * FROM crypto_customers WHERE id = ?`,
        [customerId]
      );
      
      if (!customer) {
        return null;
      }
      
      // Konvertiere die Adressen und Metadaten
      let addresses = {};
      let metadata = {};
      
      try {
        addresses = JSON.parse(customer.addresses);
      } catch (error) {
        logger.warn(`Fehler beim Parsen der Adressen für Kunde ${customerId}`);
      }
      
      try {
        metadata = JSON.parse(customer.metadata);
      } catch (error) {
        logger.warn(`Fehler beim Parsen der Metadaten für Kunde ${customerId}`);
      }
      
      return {
        id: customer.id,
        name: customer.name,
        email: customer.email,
        addresses,
        createdAt: customer.created_at,
        updatedAt: customer.updated_at,
        metadata
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen des Krypto-Kunden', {
        error: error.message,
        customerId
      });
      throw error;
    }
  }
  
  /**
   * Ruft einen Kunden anhand der E-Mail-Adresse ab
   * @param {string} email - E-Mail-Adresse
   * @returns {Promise<Object>} Kundendaten
   */
  async getCustomerByEmail(email) {
    try {
      logger.debug(`Rufe Krypto-Kunden anhand der E-Mail-Adresse ab: ${email}`);
      
      // Hole den Kunden aus der Datenbank
      const [customer] = await this.options.databaseService.query(
        `SELECT * FROM crypto_customers WHERE email = ?`,
        [email]
      );
      
      if (!customer) {
        return null;
      }
      
      // Konvertiere die Adressen und Metadaten
      let addresses = {};
      let metadata = {};
      
      try {
        addresses = JSON.parse(customer.addresses);
      } catch (error) {
        logger.warn(`Fehler beim Parsen der Adressen für Kunde ${customer.id}`);
      }
      
      try {
        metadata = JSON.parse(customer.metadata);
      } catch (error) {
        logger.warn(`Fehler beim Parsen der Metadaten für Kunde ${customer.id}`);
      }
      
      return {
        id: customer.id,
        name: customer.name,
        email: customer.email,
        addresses,
        createdAt: customer.created_at,
        updatedAt: customer.updated_at,
        metadata
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen des Krypto-Kunden anhand der E-Mail-Adresse', {
        error: error.message,
        email
      });
      throw error;
    }
  }
  
  /**
   * Ruft Kunden ab
   * @param {Object} options - Abfrageoptionen
   * @returns {Promise<Array<Object>>} Kundendaten
   */
  async getCustomers(options = {}) {
    try {
      logger.debug('Rufe Krypto-Kunden ab');
      
      // Erstelle die Abfrage
      let query = 'SELECT * FROM crypto_customers';
      const params = [];
      
      // Füge Filter hinzu
      const conditions = [];
      
      if (options.email) {
        conditions.push('email = ?');
        params.push(options.email);
      }
      
      if (conditions.length > 0) {
        query += ' WHERE ' + conditions.join(' AND ');
      }
      
      // Füge Sortierung hinzu
      query += ' ORDER BY created_at DESC';
      
      // Füge Limit hinzu
      if (options.limit) {
        query += ' LIMIT ?';
        params.push(options.limit);
      }
      
      // Führe die Abfrage aus
      const customers = await this.options.databaseService.query(query, params);
      
      // Konvertiere die Kundendaten
      return customers.map(customer => {
        // Konvertiere die Adressen und Metadaten
        let addresses = {};
        let metadata = {};
        
        try {
          addresses = JSON.parse(customer.addresses);
        } catch (error) {
          logger.warn(`Fehler beim Parsen der Adressen für Kunde ${customer.id}`);
        }
        
        try {
          metadata = JSON.parse(customer.metadata);
        } catch (error) {
          logger.warn(`Fehler beim Parsen der Metadaten für Kunde ${customer.id}`);
        }
        
        return {
          id: customer.id,
          name: customer.name,
          email: customer.email,
          addresses,
          createdAt: customer.created_at,
          updatedAt: customer.updated_at,
          metadata
        };
      });
    } catch (error) {
      logger.error('Fehler beim Abrufen der Krypto-Kunden', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Generiert eine eindeutige ID
   * @returns {string} Eindeutige ID
   */
  generateId() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }
  
  /**
   * Holt die Empfängeradresse für eine Währung
   * @param {string} currency - Währung
   * @returns {Promise<string>} Empfängeradresse
   */
  async getReceivingAddress(currency) {
    try {
      logger.debug(`Hole Empfängeradresse für Währung: ${currency}`);
      
      // Hole die Währungskonfiguration
      const currencyConfig = this.options.supportedCurrencies[currency];
      
      if (!currencyConfig) {
        throw new Error(`Währung ${currency} wird nicht unterstützt`);
      }
      
      // Hole den Blockchain-Adapter
      const adapter = this.options.blockchainManager.selectAdapter({
        adapter: currencyConfig.adapter
      });
      
      // Hole die Adresse
      const address = await adapter.getAddress();
      
      return address;
    } catch (error) {
      logger.error('Fehler beim Holen der Empfängeradresse', {
        error: error.message,
        currency
      });
      throw error;
    }
  }
}/**
 * @fileoverview Krypto-Payment-Adapter für Zahlungen mit Kryptowährungen
 * 
 * Dieser Adapter implementiert das PaymentAdapter-Interface für Zahlungen mit
 * Kryptowährungen wie DOT, ETH, BTC, etc. Er nutzt die Blockchain-Adapter für
 * die Interaktion mit den entsprechenden Blockchains.
 */

import { PaymentAdapter } from './PaymentAdapter.js';
import { logger } from '../../utils/logger.js';

/**
 * Krypto-Payment-Adapter für Zahlungen mit Kryptowährungen
 */
export class CryptoPaymentAdapter extends PaymentAdapter {
  /**
   * Erstellt eine neue Instanz des CryptoPaymentAdapter
   * @param {Object} options - Konfigurationsoptionen
   * @param {Object} options.blockchainManager - BlockchainManager-Instanz
   * @param {Object} options.walletManager - WalletManager-Instanz
   * @param {Object} options.databaseService - Datenbankdienst
   * @param {Object} options.supportedCurrencies - Unterstützte Währungen
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      blockchainManager: options.blockchainManager,
      walletManager: options.walletManager,
      databaseService: options.databaseService,
      supportedCurrencies: options.supportedCurrencies || {
        DOT: {
          name: 'Polkadot',
          symbol: 'DOT',
          decimals: 10,
          adapter: 'polkadot',
          enabled: true
        },
        ETH: {
          name: 'Ethereum',
          symbol: 'ETH',
          decimals: 18,
          adapter: 'ethereum',
          enabled: true
        },
        USDT: {
          name: 'Tether',
          symbol: 'USDT',
          decimals: 6,
          adapter: 'ethereum',
          contractAddress: '******************************************',
          enabled: true
        }
      },
      ...options
    };
    
    this.isInitialized = false;
    
    logger.info('CryptoPaymentAdapter initialisiert');
  }
  
  /**
   * Initialisiert den Payment-Adapter
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      logger.info('Initialisiere CryptoPaymentAdapter');
      
      // Prüfe, ob die erforderlichen Dienste verfügbar sind
      if (!this.options.blockchainManager) {
        throw new Error('BlockchainManager ist erforderlich');
      }
      
      if (!this.options.databaseService) {
        throw new Error('DatabaseService ist erforderlich');
      }
      
      // Initialisiere die Datenbanktabellen
      await this.initializeDatabaseTables();
      
      this.isInitialized = true;
      
      logger.info('CryptoPaymentAdapter erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des CryptoPaymentAdapter', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Initialisiert die erforderlichen Datenbanktabellen
   * @returns {Promise<void>}
   */
  async initializeDatabaseTables() {
    try {
      // Prüfe, ob die Zahlungstabelle existiert
      const paymentTableExists = await this.options.databaseService.tableExists('crypto_payments');
      
      if (!paymentTableExists) {
        // Erstelle die Zahlungstabelle
        await this.options.databaseService.query(`
          CREATE TABLE crypto_payments (
            id VARCHAR(36) PRIMARY KEY,
            customer_id VARCHAR(36),
            amount DECIMAL(24, 8) NOT NULL,
            currency VARCHAR(10) NOT NULL,
            status VARCHAR(20) NOT NULL,
            from_address VARCHAR(100),
            to_address VARCHAR(100) NOT NULL,
            transaction_hash VARCHAR(100),
            block_number BIGINT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            metadata JSON,
            INDEX (customer_id),
            INDEX (status),
            INDEX (transaction_hash)
          )
        `);
        
        logger.info('Krypto-Zahlungstabelle erstellt');
      }
      
      // Prüfe, ob die Kundentabelle existiert
      const customerTableExists = await this.options.databaseService.tableExists('crypto_customers');
      
      if (!customerTableExists) {
        // Erstelle die Kundentabelle
        await this.options.databaseService.query(`
          CREATE TABLE crypto_customers (
            id VARCHAR(36) PRIMARY KEY,
            name VARCHAR(100),
            email VARCHAR(100),
            addresses JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            metadata JSON,
            INDEX (email)
          )
        `);
        
        logger.info('Krypto-Kundentabelle erstellt');
      }
    } catch (error) {
      logger.error('Fehler bei der Initialisierung der Datenbanktabellen', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Erstellt eine Zahlung
   * @param {Object} payment - Zahlungsdaten
   * @returns {Promise<Object>} Zahlungsergebnis
   */
  async createPayment(payment) {
    try {
      logger.info('Erstelle Krypto-Zahlung');
      
      // Validiere die Zahlungsdaten
      if (!payment.amount || !payment.currency) {
        throw new Error('Betrag und Währung sind erforderlich');
      }
      
      // Prüfe, ob die Währung unterstützt wird
      if (!this.options.supportedCurrencies[payment.currency]) {
        throw new Error(`Währung ${payment.currency} wird nicht unterstützt`);
      }
      
      // Prüfe, ob die Währung aktiviert ist
      if (!this.options.supportedCurrencies[payment.currency].enabled) {
        throw new Error(`Währung ${payment.currency} ist deaktiviert`);
      }
      
      // Generiere eine eindeutige Zahlungs-ID
      const paymentId = this.generateId();
      
      // Hole die Empfängeradresse
      const toAddress = payment.toAddress || await this.getReceivingAddress(payment.currency);
      
      // Erstelle die Zahlungsdaten
      const paymentData = {
        id: paymentId,
        customer_id: payment.customerId || null,
        amount: payment.amount,
        currency: payment.currency,
        status: 'pending',
        from_address: payment.fromAddress || null,
        to_address: toAddress,
        transaction_hash: null,
        block_number: null,
        metadata: JSON.stringify(payment.metadata || {})
      };
      
      // Speichere die Zahlung in der Datenbank
      await this.options.databaseService.query(
        `INSERT INTO crypto_payments SET ?`,
        paymentData
      );
      
      logger.info(`Krypto-Zahlung erstellt: ${paymentId}`);
      
      return {
        id: paymentId,
        amount: payment.amount,
        currency: payment.currency,
        status: 'pending',
        toAddress,
        createdAt: new Date().toISOString()
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen der Krypto-Zahlung', {
        error: error.message,
        payment
      });
      throw error;
    }
  }
  
  /**
   * Verarbeitet eine Zahlung
   * @param {string} paymentId - Zahlungs-ID
   * @param {Object} options - Verarbeitungsoptionen
   * @returns {Promise<Object>} Verarbeitungsergebnis
   */
  async processPayment(paymentId, options = {}) {
    try {
      logger.info(`Verarbeite Krypto-Zahlung: ${paymentId}`);
      
      // Hole die Zahlungsdaten
      const payment = await this.getPayment(paymentId);
      
      if (!payment) {
        throw new Error(`Zahlung mit ID ${paymentId} nicht gefunden`);
      }
      
      // Prüfe, ob die Zahlung bereits verarbeitet wurde
      if (payment.status === 'completed') {
        return {
          id: paymentId,
          status: 'completed',
          message: 'Zahlung wurde bereits verarbeitet'
        };
      }
      
      // Prüfe, ob die Zahlung storniert wurde
      if (payment.status === 'cancelled') {
        throw new Error('Zahlung wurde storniert');
      }
      
      // Hole die Währungskonfiguration
      const currencyConfig = this.options.supportedCurrencies[payment.currency];
      
      // Prüfe, ob eine Transaktions-Hash angegeben wurde
      if (options.transactionHash) {
        // Aktualisiere die Zahlung mit dem Transaktions-Hash
        await this.options.databaseService.query(
          `UPDATE crypto_payments SET transaction_hash = ?, status = 'processing', from_address = ? WHERE id = ?`,
          [options.transactionHash, options.fromAddress || payment.from_address, paymentId]
        );
        
        // Hole den Blockchain-Adapter
        const adapter = this.options.blockchainManager.selectAdapter({
          adapter: currencyConfig.adapter
        });
        
        // Warte auf die Bestätigung der Transaktion
        const confirmations = options.confirmations || 1;
        const txResult = await adapter.waitForTransaction(options.transactionHash, confirmations);
        
        // Prüfe, ob die Transaktion erfolgreich war
        if (txResult.status === 'success') {
          // Aktualisiere die Zahlung
          await this.options.databaseService.query(
            `UPDATE crypto_payments SET status = 'completed', block_number = ? WHERE id = ?`,
            [txResult.blockNumber, paymentId]
          );
          
          logger.info(`Krypto-Zahlung erfolgreich verarbeitet: ${paymentId}`);
          
          return {
            id: paymentId,
            status: 'completed',
            transactionHash: options.transactionHash,
            blockNumber: txResult.blockNumber
          };
        } else {
          // Aktualisiere die Zahlung
          await this.options.databaseService.query(
            `UPDATE crypto_payments SET status = 'failed' WHERE id = ?`,
            [paymentId]
          );
          
          throw new Error('Transaktion fehlgeschlagen');
        }
      } else if (options.fromAddress && options.amount) {
        // Manuelle Verarbeitung (z.B. für Tests oder Admin-Funktionen)
        await this.options.databaseService.query(
          `UPDATE crypto_payments SET status = 'completed', from_address = ? WHERE id = ?`,
          [options.fromAddress, paymentId]
        );
        
        logger.info(`Krypto-Zahlung manuell verarbeitet: ${paymentId}`);
        
        return {
          id: paymentId,
          status: 'completed',
          message: 'Zahlung manuell verarbeitet'
        };
      } else {
        throw new Error('Transaktions-Hash oder manuelle Verarbeitungsdaten erforderlich');
      }
    } catch (error) {
      logger.error('Fehler bei der Verarbeitung der Krypto-Zahlung', {
        error: error.message,
        paymentId
      });
      throw error;
    }
  }
  
  /**
   * Storniert eine Zahlung
   * @param {string} paymentId - Zahlungs-ID
   * @param {Object} options - Stornierungsoptionen
   * @returns {Promise<Object>} Stornierungsergebnis
   */
  async cancelPayment(paymentId, options = {}) {
    try {
      logger.info(`Storniere Krypto-Zahlung: ${paymentId}`);
      
      // Hole die Zahlungsdaten
      const payment = await this.getPayment(paymentId);
      
      if (!payment) {
        throw new Error(`Zahlung mit ID ${paymentId} nicht gefunden`);
      }
      
      // Prüfe, ob die Zahlung bereits verarbeitet wurde
      if (payment.status === 'completed') {
        throw new Error('Zahlung wurde bereits verarbeitet und kann nicht storniert werden');
      }
      
      // Prüfe, ob die Zahlung bereits storniert wurde
      if (payment.status === 'cancelled') {
        return {
          id: paymentId,
          status: 'cancelled',
          message: 'Zahlung wurde bereits storniert'
        };
      }
      
      // Aktualisiere die Zahlung
      await this.options.databaseService.query(
        `UPDATE crypto_payments SET status = 'cancelled' WHERE id = ?`,
        [paymentId]
      );
      
      logger.info(`Krypto-Zahlung storniert: ${paymentId}`);
      
      return {
        id: paymentId,
        status: 'cancelled'
      };
    } catch (error) {
      logger.error('Fehler bei der Stornierung der Krypto-Zahlung', {
        error: error.message,
        paymentId
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Zahlung ab
   * @param {string} paymentId - Zahlungs-ID
   * @returns {Promise<Object>} Zahlungsdaten
   */
  async getPayment(paymentId) {
    try {
      logger.debug(`Rufe Krypto-Zahlung ab: ${paymentId}`);
      
      // Hole die Zahlungsdaten aus der Datenbank
      const [payment] = await this.options.databaseService.query(
        `SELECT * FROM crypto_payments WHERE id = ?`,
        [paymentId]
      );
      
      if (!payment) {
        return null;
      }
      
      // Konvertiere die Metadaten
      let metadata = {};
      try {
        metadata = JSON.parse(payment.metadata);
      } catch (error) {
        logger.warn(`Fehler beim Parsen der Metadaten für Zahlung ${paymentId}`);
      }
      
      return {
        id: payment.id,
        customerId: payment.customer_id,
        amount: parseFloat(payment.amount),
        currency: payment.currency,
        status: payment.status,
        fromAddress: payment.from_address,
        toAddress: payment.to_address,
        transactionHash: payment.transaction_hash,
        blockNumber: payment.block_number,
        createdAt: payment.created_at,
        updatedAt: payment.updated_at,
        metadata
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen der Krypto-Zahlung', {
        error: error.message,
        paymentId
      });
      throw error;
    }
  }
  
  /**
   * Ruft Zahlungen ab
   * @param {Object} options - Abfrageoptionen
   * @returns {Promise<Array<Object>>} Zahlungsdaten
   */
  async getPayments(options = {}) {
    try {
      logger.debug('Rufe Krypto-Zahlungen ab');
      
      // Erstelle die Abfrage
      let query = 'SELECT * FROM crypto_payments';
      const params = [];
      
      // Füge Filter hinzu
      const conditions = [];
      
      if (options.customerId) {
        conditions.push('customer_id = ?');
        params.push(options.customerId);
      }
      
      if (options.status) {
        conditions.push('status = ?');
        params.push(options.status);
      }
      
      if (options.currency) {
        conditions.push('currency = ?');
        params.push(options.currency);
      }
      
      if (options.fromAddress) {
        conditions.push('from_address = ?');
        params.push(options.fromAddress);
      }
      
      if (options.toAddress) {
        conditions.push('to_address = ?');
        params.push(options.toAddress);
      }
      
      if (options.transactionHash) {
        conditions.push('transaction_hash = ?');
        params.push(options.transactionHash);
      }
      
      if (conditions.length > 0) {
        query += ' WHERE ' + conditions.join(' AND ');
      }
      
      // Füge Sortierung hinzu
      query += ' ORDER BY created_at DESC';
      
      // Füge Limit hinzu
      if (options.limit) {
        query += ' LIMIT ?';
        params.push(options.limit);
      }
      
      // Führe die Abfrage aus
      const payments = await this.options.databaseService.query(query, params);
      
      // Konvertiere die Zahlungsdaten
      return payments.map(payment => {
        // Konvertiere die Metadaten
        let metadata = {};
        try {
          metadata = JSON.parse(payment.metadata);
        } catch (error) {
          logger.warn(`Fehler beim Parsen der Metadaten für Zahlung ${payment.id}`);
        }
        
        return {
          id: payment.id,
          customerId: payment.customer_id,
          amount: parseFloat(payment.amount),
          currency: payment.currency,
          status: payment.status,
          fromAddress: payment.from_address,
          toAddress: payment.to_address,
          transactionHash: payment.transaction_hash,
          blockNumber: payment.block_number,
          createdAt: payment.created_at,
          updatedAt: payment.updated_at,
          metadata
        };
      });
    } catch (error) {
      logger.error('Fehler beim Abrufen der Krypto-Zahlungen', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Erstellt einen Kunden
   * @param {Object} customer - Kundendaten
   * @returns {Promise<Object>} Kundenergebnis
   */
  async createCustomer(customer) {
    try {
      logger.info('Erstelle Krypto-Kunden');
      
      // Validiere die Kundendaten
      if (!customer.email) {
        throw new Error('E-Mail ist erforderlich');
      }
      
      // Prüfe, ob der Kunde bereits existiert
      const existingCustomer = await this.getCustomerByEmail(customer.email);
      
      if (existingCustomer) {
        return existingCustomer;
      }
      
      // Generiere eine eindeutige Kunden-ID
      const customerId = this.generateId();
      
      // Erstelle die Kundendaten
      const customerData = {
        id: customerId,
        name: customer.name || null,
        email: customer.email,
        addresses: JSON.stringify(customer.addresses || {}),
        metadata: JSON.stringify(customer.metadata || {})
      };
      
      // Speichere den Kunden in der Datenbank
      await this.options.databaseService.query(
        `INSERT INTO crypto_customers SET ?`,
        customerData
      );
      
      logger.info(`Krypto-Kunde erstellt: ${customerId}`);
      
      return {
        id: customerId,
        name: customer.name,
        email: customer.email,
        addresses: customer.addresses || {},
        createdAt: new Date().toISOString()
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen des Krypto-Kunden', {
        error: error.message,
        customer
      });
      throw error;
    }
  }
  
  /**
   * Aktualisiert einen Kunden
   * @param {string} customerId - Kunden-ID
   * @param {Object} customer - Kundendaten
   * @returns {Promise<Object>} Kundenergebnis
   */
  async updateCustomer(customerId, customer) {
    try {
      logger.info(`Aktualisiere Krypto-Kunden: ${customerId}`);
      
      // Hole den Kunden
      const existingCustomer = await this.getCustomer(customerId);
      
      if (!existingCustomer) {
        throw new Error(`Kunde mit ID ${customerId} nicht gefunden`);
      }
      
      // Erstelle die Kundendaten
      const customerData = {};
      
      if (customer.name !== undefined) {
        customerData.name = customer.name;
      }
      
      if (customer.email !== undefined) {
        customerData.email = customer.email;
      }
      
      if (customer.addresses !== undefined) {
        customerData.addresses = JSON.stringify(customer.addresses);
      }
      
      if (customer.metadata !== undefined) {
        customerData.metadata = JSON.stringify(customer.metadata);
      }
      
      // Aktualisiere den Kunden in der Datenbank
      await this.options.databaseService.query(
        `UPDATE crypto_customers SET ? WHERE id = ?`,
        [customerData, customerId]
      );
      
      logger.info(`Krypto-Kunde aktualisiert: ${customerId}`);
      
      // Hole den aktualisierten Kunden
      return await this.getCustomer(customerId);
    } catch (error) {
      logger.error('Fehler beim Aktualisieren des Krypto-Kunden', {
        error: error.message,
        customerId,
        customer
      });
      throw error;
    }
  }
  
  /**
   * Ruft einen Kunden ab
   * @param {string} customerId - Kunden-ID
   * @returns {Promise<Object>} Kundendaten
   */
  async getCustomer(customerId) {
    try {
      logger.debug(`Rufe Krypto-Kunden ab: ${customerId}`);
      
      // Hole den Kunden aus der Datenbank
      const [customer] = await this.options.databaseService.query(
        `SELECT * FROM crypto_customers WHERE id = ?`,
        [customerId]
      );
      
      if (!customer) {
        return null;
      }
      
      // Konvertiere die Adressen und Metadaten
      let addresses = {};
      let metadata = {};
      
      try {
        addresses = JSON.parse(customer.addresses);
      } catch (error) {
        logger.warn(`Fehler beim Parsen der Adressen für Kunde ${customerId}`);
      }
      
      try {
        metadata = JSON.parse(customer.metadata);
      } catch (error) {
        logger.warn(`Fehler beim Parsen der Metadaten für Kunde ${customerId}`);
      }
      
      return {
        id: customer.id,
        name: customer.name,
        email: customer.email,
        addresses,
        createdAt: customer.created_at,
        updatedAt: customer.updated_at,
        metadata
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen des Krypto-Kunden', {
        error: error.message,
        customerId
      });
      throw error;
    }
  }
  
  /**
   * Ruft einen Kunden anhand der E-Mail-Adresse ab
   * @param {string} email - E-Mail-Adresse
   * @returns {Promise<Object>} Kundendaten
   */
  async getCustomerByEmail(email) {
    try {
      logger.debug(`Rufe Krypto-Kunden anhand der E-Mail-Adresse ab: ${email}`);
      
      // Hole den Kunden aus der Datenbank
      const [customer] = await this.options.databaseService.query(
        `SELECT * FROM crypto_customers WHERE email = ?`,
        [email]
      );
      
      if (!customer) {
        return null;
      }
      
      // Konvertiere die Adressen und Metadaten
      let addresses = {};
      let metadata = {};
      
      try {
        addresses = JSON.parse(customer.addresses);
      } catch (error) {
        logger.warn(`Fehler beim Parsen der Adressen für Kunde ${customer.id}`);
      }
      
      try {
        metadata = JSON.parse(customer.metadata);
      } catch (error) {
        logger.warn(`Fehler beim Parsen der Metadaten für Kunde ${customer.id}`);
      }
      
      return {
        id: customer.id,
        name: customer.name,
        email: customer.email,
        addresses,
        createdAt: customer.created_at,
        updatedAt: customer.updated_at,
        metadata
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen des Krypto-Kunden anhand der E-Mail-Adresse', {
        error: error.message,
        email
      });
      throw error;
    }
  }
  
  /**
   * Ruft Kunden ab
   * @param {Object} options - Abfrageoptionen
   * @returns {Promise<Array<Object>>} Kundendaten
   */
  async getCustomers(options = {}) {
    try {
      logger.debug('Rufe Krypto-Kunden ab');
      
      // Erstelle die Abfrage
      let query = 'SELECT * FROM crypto_customers';
      const params = [];
      
      // Füge Filter hinzu
      const conditions = [];
      
      if (options.email) {
        conditions.push('email = ?');
        params.push(options.email);
      }
      
      if (conditions.length > 0) {
        query += ' WHERE ' + conditions.join(' AND ');
      }
      
      // Füge Sortierung hinzu
      query += ' ORDER BY created_at DESC';
      
      // Füge Limit hinzu
      if (options.limit) {
        query += ' LIMIT ?';
        params.push(options.limit);
      }
      
      // Führe die Abfrage aus
      const customers = await this.options.databaseService.query(query, params);
      
      // Konvertiere die Kundendaten
      return customers.map(customer => {
        // Konvertiere die Adressen und Metadaten
        let addresses = {};
        let metadata = {};
        
        try {
          addresses = JSON.parse(customer.addresses);
        } catch (error) {
          logger.warn(`Fehler beim Parsen der Adressen für Kunde ${customer.id}`);
        }
        
        try {
          metadata = JSON.parse(customer.metadata);
        } catch (error) {
          logger.warn(`Fehler beim Parsen der Metadaten für Kunde ${customer.id}`);
        }
        
        return {
          id: customer.id,
          name: customer.name,
          email: customer.email,
          addresses,
          createdAt: customer.created_at,
          updatedAt: customer.updated_at,
          metadata
        };
      });
    } catch (error) {
      logger.error('Fehler beim Abrufen der Krypto-Kunden', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Generiert eine eindeutige ID
   * @returns {string} Eindeutige ID
   */
  generateId() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }
  
  /**
   * Holt die Empfängeradresse für eine Währung
   * @param {string} currency - Währung
   * @returns {Promise<string>} Empfängeradresse
   */
  async getReceivingAddress(currency) {
    try {
      logger.debug(`Hole Empfängeradresse für Währung: ${currency}`);
      
      // Hole die Währungskonfiguration
      const currencyConfig = this.options.supportedCurrencies[currency];
      
      if (!currencyConfig) {
        throw new Error(`Währung ${currency} wird nicht unterstützt`);
      }
      
      // Hole den Blockchain-Adapter
      const adapter = this.options.blockchainManager.selectAdapter({
        adapter: currencyConfig.adapter
      });
      
      // Hole die Adresse
      const address = await adapter.getAddress();
      
      return address;
    } catch (error) {
      logger.error('Fehler beim Holen der Empfängeradresse', {
        error: error.message,
        currency
      });
      throw error;
    }
  }
}