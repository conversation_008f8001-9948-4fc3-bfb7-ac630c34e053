/**
 * @fileoverview Payment-Manager für die Verwaltung verschiedener Payment-Adapter
 * 
 * Dieser Manager verwaltet verschiedene Payment-Adapter und bietet eine einheitliche
 * Schnittstelle für die Interaktion mit verschiedenen Zahlungsmethoden. Er ermöglicht
 * die flexible Auswahl und Kombination verschiedener Zahlungsmethoden für unterschiedliche
 * Anwendungsfälle.
 */

import { logger } from '../../utils/logger.js';
import { CryptoPaymentAdapter } from './CryptoPaymentAdapter.js';

/**
 * Payment-Manager für die Verwaltung verschiedener Payment-Adapter
 */
export class PaymentManager {
  /**
   * Erstellt eine neue Instanz des PaymentManager
   * @param {Object} options - Konfigurationsoptionen
   * @param {Object} options.adapters - Konfiguration für die Adapter
   * @param {Object} options.defaultAdapter - Name des Standard-Adapters
   * @param {Object} options.supportedMethods - Unterstützte Zahlungsmethoden
   */
  constructor(options = {}) {
    this.options = {
      adapters: options.adapters || {},
      defaultAdapter: options.defaultAdapter || 'crypto',
      supportedMethods: options.supportedMethods || {
        crypto: {
          name: 'Kryptowährungen',
          adapter: 'crypto',
          enabled: true,
          currencies: ['DOT', 'ETH', 'USDT']
        }
      },
      ...options
    };
    
    this.adapters = new Map();
    
    logger.info('PaymentManager initialisiert');
  }
  
  /**
   * Initialisiert den PaymentManager und alle konfigurierten Adapter
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      logger.info('Initialisiere PaymentManager');
      
      // Initialisiere die Adapter
      await this.initializeAdapters();
      
      logger.info('PaymentManager erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des PaymentManager', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Initialisiert die konfigurierten Adapter
   * @returns {Promise<void>}
   */
  async initializeAdapters() {
    try {
      // Initialisiere den Crypto-Adapter, falls konfiguriert
      if (this.options.adapters.crypto) {
        const cryptoAdapter = new CryptoPaymentAdapter(this.options.adapters.crypto);
        await cryptoAdapter.initialize();
        this.adapters.set('crypto', cryptoAdapter);
        logger.debug('Crypto-Adapter initialisiert');
      }
      
      // Hier können weitere Adapter initialisiert werden, z.B. für Stripe, PayPal, etc.
      
      // Prüfe, ob der Standard-Adapter verfügbar ist
      if (!this.adapters.has(this.options.defaultAdapter)) {
        throw new Error(`Standard-Adapter '${this.options.defaultAdapter}' nicht verfügbar`);
      }
      
      logger.info(`${this.adapters.size} Payment-Adapter initialisiert`);
    } catch (error) {
      logger.error('Fehler bei der Initialisierung der Adapter', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Wählt den passenden Adapter basierend auf den Optionen aus
   * @param {Object} options - Optionen
   * @returns {Object} Der ausgewählte Adapter
   */
  selectAdapter(options = {}) {
    // Verwende den explizit angegebenen Adapter, falls vorhanden
    if (options.adapter && this.adapters.has(options.adapter)) {
      return this.adapters.get(options.adapter);
    }
    
    // Verwende den Adapter für die angegebene Zahlungsmethode, falls vorhanden
    if (options.method && this.options.supportedMethods[options.method]) {
      const adapterName = this.options.supportedMethods[options.method].adapter;
      if (this.adapters.has(adapterName)) {
        return this.adapters.get(adapterName);
      }
    }
    
    // Verwende den Adapter für die angegebene Währung, falls vorhanden
    if (options.currency) {
      for (const [methodName, methodConfig] of Object.entries(this.options.supportedMethods)) {
        if (methodConfig.currencies && methodConfig.currencies.includes(options.currency)) {
          const adapterName = methodConfig.adapter;
          if (this.adapters.has(adapterName)) {
            return this.adapters.get(adapterName);
          }
        }
      }
    }
    
    // Verwende den Standard-Adapter
    return this.adapters.get(this.options.defaultAdapter);
  }
  
  /**
   * Erstellt eine Zahlung
   * @param {Object} payment - Zahlungsdaten
   * @returns {Promise<Object>} Zahlungsergebnis
   */
  async createPayment(payment) {
    try {
      logger.info('Erstelle Zahlung');
      
      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter(payment);
      
      // Erstelle die Zahlung
      const result = await adapter.createPayment(payment);
      
      logger.info(`Zahlung erstellt: ${result.id}`);
      
      return {
        ...result,
        adapter: adapter.constructor.name
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen der Zahlung', {
        error: error.message,
        payment
      });
      throw error;
    }
  }
  
  /**
   * Verarbeitet eine Zahlung
   * @param {string} paymentId - Zahlungs-ID
   * @param {Object} options - Verarbeitungsoptionen
   * @returns {Promise<Object>} Verarbeitungsergebnis
   */
  async processPayment(paymentId, options = {}) {
    try {
      logger.info(`Verarbeite Zahlung: ${paymentId}`);
      
      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter(options);
      
      // Verarbeite die Zahlung
      const result = await adapter.processPayment(paymentId, options);
      
      logger.info(`Zahlung verarbeitet: ${paymentId}`);
      
      return {
        ...result,
        adapter: adapter.constructor.name
      };
    } catch (error) {
      logger.error('Fehler bei der Verarbeitung der Zahlung', {
        error: error.message,
        paymentId
      });
      throw error;
    }
  }
  
  /**
   * Storniert eine Zahlung
   * @param {string} paymentId - Zahlungs-ID
   * @param {Object} options - Stornierungsoptionen
   * @returns {Promise<Object>} Stornierungsergebnis
   */
  async cancelPayment(paymentId, options = {}) {
    try {
      logger.info(`Storniere Zahlung: ${paymentId}`);
      
      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter(options);
      
      // Storniere die Zahlung
      const result = await adapter.cancelPayment(paymentId, options);
      
      logger.info(`Zahlung storniert: ${paymentId}`);
      
      return {
        ...result,
        adapter: adapter.constructor.name
      };
    } catch (error) {
      logger.error('Fehler bei der Stornierung der Zahlung', {
        error: error.message,
        paymentId
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Zahlung ab
   * @param {string} paymentId - Zahlungs-ID
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Object>} Zahlungsdaten
   */
  async getPayment(paymentId, options = {}) {
    try {
      logger.debug(`Rufe Zahlung ab: ${paymentId}`);
      
      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter(options);
      
      // Rufe die Zahlung ab
      const payment = await adapter.getPayment(paymentId);
      
      if (!payment) {
        return null;
      }
      
      return {
        ...payment,
        adapter: adapter.constructor.name
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen der Zahlung', {
        error: error.message,
        paymentId
      });
      throw error;
    }
  }
  
  /**
   * Ruft Zahlungen ab
   * @param {Object} options - Abfrageoptionen
   * @returns {Promise<Array<Object>>} Zahlungsdaten
   */
  async getPayments(options = {}) {
    try {
      logger.debug('Rufe Zahlungen ab');
      
      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter(options);
      
      // Rufe die Zahlungen ab
      const payments = await adapter.getPayments(options);
      
      return payments.map(payment => ({
        ...payment,
        adapter: adapter.constructor.name
      }));
    } catch (error) {
      logger.error('Fehler beim Abrufen der Zahlungen', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Erstellt einen Kunden
   * @param {Object} customer - Kundendaten
   * @param {Object} options - Erstellungsoptionen
   * @returns {Promise<Object>} Kundenergebnis
   */
  async createCustomer(customer, options = {}) {
    try {
      logger.info('Erstelle Kunden');
      
      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter(options);
      
      // Erstelle den Kunden
      const result = await adapter.createCustomer(customer);
      
      logger.info(`Kunde erstellt: ${result.id}`);
      
      return {
        ...result,
        adapter: adapter.constructor.name
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen des Kunden', {
        error: error.message,
        customer
      });
      throw error;
    }
  }
  
  /**
   * Aktualisiert einen Kunden
   * @param {string} customerId - Kunden-ID
   * @param {Object} customer - Kundendaten
   * @param {Object} options - Aktualisierungsoptionen
   * @returns {Promise<Object>} Kundenergebnis
   */
  async updateCustomer(customerId, customer, options = {}) {
    try {
      logger.info(`Aktualisiere Kunden: ${customerId}`);
      
      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter(options);
      
      // Aktualisiere den Kunden
      const result = await adapter.updateCustomer(customerId, customer);
      
      logger.info(`Kunde aktualisiert: ${customerId}`);
      
      return {
        ...result,
        adapter: adapter.constructor.name
      };
    } catch (error) {
      logger.error('Fehler beim Aktualisieren des Kunden', {
        error: error.message,
        customerId,
        customer
      });
      throw error;
    }
  }
  
  /**
   * Ruft einen Kunden ab
   * @param {string} customerId - Kunden-ID
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Object>} Kundendaten
   */
  async getCustomer(customerId, options = {}) {
    try {
      logger.debug(`Rufe Kunden ab: ${customerId}`);
      
      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter(options);
      
      // Rufe den Kunden ab
      const customer = await adapter.getCustomer(customerId);
      
      if (!customer) {
        return null;
      }
      
      return {
        ...customer,
        adapter: adapter.constructor.name
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen des Kunden', {
        error: error.message,
        customerId
      });
      throw error;
    }
  }
  
  /**
   * Ruft Kunden ab
   * @param {Object} options - Abfrageoptionen
   * @returns {Promise<Array<Object>>} Kundendaten
   */
  async getCustomers(options = {}) {
    try {
      logger.debug('Rufe Kunden ab');
      
      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter(options);
      
      // Rufe die Kunden ab
      const customers = await adapter.getCustomers(options);
      
      return customers.map(customer => ({
        ...customer,
        adapter: adapter.constructor.name
      }));
    } catch (error) {
      logger.error('Fehler beim Abrufen der Kunden', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft die unterstützten Zahlungsmethoden ab
   * @returns {Array<Object>} Unterstützte Zahlungsmethoden
   */
  getSupportedMethods() {
    try {
      logger.debug('Rufe unterstützte Zahlungsmethoden ab');
      
      // Erstelle eine Liste der unterstützten Zahlungsmethoden
      const methods = [];
      
      for (const [name, config] of Object.entries(this.options.supportedMethods)) {
        if (config.enabled) {
          methods.push({
            id: name,
            name: config.name,
            currencies: config.currencies || []
          });
        }
      }
      
      return methods;
    } catch (error) {
      logger.error('Fehler beim Abrufen der unterstützten Zahlungsmethoden', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft die unterstützten Währungen ab
   * @returns {Array<Object>} Unterstützte Währungen
   */
  getSupportedCurrencies() {
    try {
      logger.debug('Rufe unterstützte Währungen ab');
      
      // Sammle alle unterstützten Währungen
      const currencies = new Set();
      
      for (const [name, config] of Object.entries(this.options.supportedMethods)) {
        if (config.enabled && config.currencies) {
          for (const currency of config.currencies) {
            currencies.add(currency);
          }
        }
      }
      
      // Hole die Währungskonfigurationen
      const currencyConfigs = [];
      
      for (const currency of currencies) {
        // Suche nach der Währungskonfiguration in den Adaptern
        for (const adapter of this.adapters.values()) {
          if (adapter.options.supportedCurrencies && adapter.options.supportedCurrencies[currency]) {
            currencyConfigs.push({
              id: currency,
              name: adapter.options.supportedCurrencies[currency].name,
              symbol: adapter.options.supportedCurrencies[currency].symbol,
              decimals: adapter.options.supportedCurrencies[currency].decimals
            });
            break;
          }
        }
      }
      
      return currencyConfigs;
    } catch (error) {
      logger.error('Fehler beim Abrufen der unterstützten Währungen', {
        error: error.message
      });
      throw error;
    }
  }
}/**
 * @fileoverview Payment-Manager für die Verwaltung verschiedener Payment-Adapter
 * 
 * Dieser Manager verwaltet verschiedene Payment-Adapter und bietet eine einheitliche
 * Schnittstelle für die Interaktion mit verschiedenen Zahlungsmethoden. Er ermöglicht
 * die flexible Auswahl und Kombination verschiedener Zahlungsmethoden für unterschiedliche
 * Anwendungsfälle.
 */

import { logger } from '../../utils/logger.js';
import { CryptoPaymentAdapter } from './CryptoPaymentAdapter.js';

/**
 * Payment-Manager für die Verwaltung verschiedener Payment-Adapter
 */
export class PaymentManager {
  /**
   * Erstellt eine neue Instanz des PaymentManager
   * @param {Object} options - Konfigurationsoptionen
   * @param {Object} options.adapters - Konfiguration für die Adapter
   * @param {Object} options.defaultAdapter - Name des Standard-Adapters
   * @param {Object} options.supportedMethods - Unterstützte Zahlungsmethoden
   */
  constructor(options = {}) {
    this.options = {
      adapters: options.adapters || {},
      defaultAdapter: options.defaultAdapter || 'crypto',
      supportedMethods: options.supportedMethods || {
        crypto: {
          name: 'Kryptowährungen',
          adapter: 'crypto',
          enabled: true,
          currencies: ['DOT', 'ETH', 'USDT']
        }
      },
      ...options
    };
    
    this.adapters = new Map();
    
    logger.info('PaymentManager initialisiert');
  }
  
  /**
   * Initialisiert den PaymentManager und alle konfigurierten Adapter
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      logger.info('Initialisiere PaymentManager');
      
      // Initialisiere die Adapter
      await this.initializeAdapters();
      
      logger.info('PaymentManager erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des PaymentManager', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Initialisiert die konfigurierten Adapter
   * @returns {Promise<void>}
   */
  async initializeAdapters() {
    try {
      // Initialisiere den Crypto-Adapter, falls konfiguriert
      if (this.options.adapters.crypto) {
        const cryptoAdapter = new CryptoPaymentAdapter(this.options.adapters.crypto);
        await cryptoAdapter.initialize();
        this.adapters.set('crypto', cryptoAdapter);
        logger.debug('Crypto-Adapter initialisiert');
      }
      
      // Hier können weitere Adapter initialisiert werden, z.B. für Stripe, PayPal, etc.
      
      // Prüfe, ob der Standard-Adapter verfügbar ist
      if (!this.adapters.has(this.options.defaultAdapter)) {
        throw new Error(`Standard-Adapter '${this.options.defaultAdapter}' nicht verfügbar`);
      }
      
      logger.info(`${this.adapters.size} Payment-Adapter initialisiert`);
    } catch (error) {
      logger.error('Fehler bei der Initialisierung der Adapter', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Wählt den passenden Adapter basierend auf den Optionen aus
   * @param {Object} options - Optionen
   * @returns {Object} Der ausgewählte Adapter
   */
  selectAdapter(options = {}) {
    // Verwende den explizit angegebenen Adapter, falls vorhanden
    if (options.adapter && this.adapters.has(options.adapter)) {
      return this.adapters.get(options.adapter);
    }
    
    // Verwende den Adapter für die angegebene Zahlungsmethode, falls vorhanden
    if (options.method && this.options.supportedMethods[options.method]) {
      const adapterName = this.options.supportedMethods[options.method].adapter;
      if (this.adapters.has(adapterName)) {
        return this.adapters.get(adapterName);
      }
    }
    
    // Verwende den Adapter für die angegebene Währung, falls vorhanden
    if (options.currency) {
      for (const [methodName, methodConfig] of Object.entries(this.options.supportedMethods)) {
        if (methodConfig.currencies && methodConfig.currencies.includes(options.currency)) {
          const adapterName = methodConfig.adapter;
          if (this.adapters.has(adapterName)) {
            return this.adapters.get(adapterName);
          }
        }
      }
    }
    
    // Verwende den Standard-Adapter
    return this.adapters.get(this.options.defaultAdapter);
  }
  
  /**
   * Erstellt eine Zahlung
   * @param {Object} payment - Zahlungsdaten
   * @returns {Promise<Object>} Zahlungsergebnis
   */
  async createPayment(payment) {
    try {
      logger.info('Erstelle Zahlung');
      
      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter(payment);
      
      // Erstelle die Zahlung
      const result = await adapter.createPayment(payment);
      
      logger.info(`Zahlung erstellt: ${result.id}`);
      
      return {
        ...result,
        adapter: adapter.constructor.name
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen der Zahlung', {
        error: error.message,
        payment
      });
      throw error;
    }
  }
  
  /**
   * Verarbeitet eine Zahlung
   * @param {string} paymentId - Zahlungs-ID
   * @param {Object} options - Verarbeitungsoptionen
   * @returns {Promise<Object>} Verarbeitungsergebnis
   */
  async processPayment(paymentId, options = {}) {
    try {
      logger.info(`Verarbeite Zahlung: ${paymentId}`);
      
      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter(options);
      
      // Verarbeite die Zahlung
      const result = await adapter.processPayment(paymentId, options);
      
      logger.info(`Zahlung verarbeitet: ${paymentId}`);
      
      return {
        ...result,
        adapter: adapter.constructor.name
      };
    } catch (error) {
      logger.error('Fehler bei der Verarbeitung der Zahlung', {
        error: error.message,
        paymentId
      });
      throw error;
    }
  }
  
  /**
   * Storniert eine Zahlung
   * @param {string} paymentId - Zahlungs-ID
   * @param {Object} options - Stornierungsoptionen
   * @returns {Promise<Object>} Stornierungsergebnis
   */
  async cancelPayment(paymentId, options = {}) {
    try {
      logger.info(`Storniere Zahlung: ${paymentId}`);
      
      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter(options);
      
      // Storniere die Zahlung
      const result = await adapter.cancelPayment(paymentId, options);
      
      logger.info(`Zahlung storniert: ${paymentId}`);
      
      return {
        ...result,
        adapter: adapter.constructor.name
      };
    } catch (error) {
      logger.error('Fehler bei der Stornierung der Zahlung', {
        error: error.message,
        paymentId
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Zahlung ab
   * @param {string} paymentId - Zahlungs-ID
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Object>} Zahlungsdaten
   */
  async getPayment(paymentId, options = {}) {
    try {
      logger.debug(`Rufe Zahlung ab: ${paymentId}`);
      
      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter(options);
      
      // Rufe die Zahlung ab
      const payment = await adapter.getPayment(paymentId);
      
      if (!payment) {
        return null;
      }
      
      return {
        ...payment,
        adapter: adapter.constructor.name
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen der Zahlung', {
        error: error.message,
        paymentId
      });
      throw error;
    }
  }
  
  /**
   * Ruft Zahlungen ab
   * @param {Object} options - Abfrageoptionen
   * @returns {Promise<Array<Object>>} Zahlungsdaten
   */
  async getPayments(options = {}) {
    try {
      logger.debug('Rufe Zahlungen ab');
      
      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter(options);
      
      // Rufe die Zahlungen ab
      const payments = await adapter.getPayments(options);
      
      return payments.map(payment => ({
        ...payment,
        adapter: adapter.constructor.name
      }));
    } catch (error) {
      logger.error('Fehler beim Abrufen der Zahlungen', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Erstellt einen Kunden
   * @param {Object} customer - Kundendaten
   * @param {Object} options - Erstellungsoptionen
   * @returns {Promise<Object>} Kundenergebnis
   */
  async createCustomer(customer, options = {}) {
    try {
      logger.info('Erstelle Kunden');
      
      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter(options);
      
      // Erstelle den Kunden
      const result = await adapter.createCustomer(customer);
      
      logger.info(`Kunde erstellt: ${result.id}`);
      
      return {
        ...result,
        adapter: adapter.constructor.name
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen des Kunden', {
        error: error.message,
        customer
      });
      throw error;
    }
  }
  
  /**
   * Aktualisiert einen Kunden
   * @param {string} customerId - Kunden-ID
   * @param {Object} customer - Kundendaten
   * @param {Object} options - Aktualisierungsoptionen
   * @returns {Promise<Object>} Kundenergebnis
   */
  async updateCustomer(customerId, customer, options = {}) {
    try {
      logger.info(`Aktualisiere Kunden: ${customerId}`);
      
      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter(options);
      
      // Aktualisiere den Kunden
      const result = await adapter.updateCustomer(customerId, customer);
      
      logger.info(`Kunde aktualisiert: ${customerId}`);
      
      return {
        ...result,
        adapter: adapter.constructor.name
      };
    } catch (error) {
      logger.error('Fehler beim Aktualisieren des Kunden', {
        error: error.message,
        customerId,
        customer
      });
      throw error;
    }
  }
  
  /**
   * Ruft einen Kunden ab
   * @param {string} customerId - Kunden-ID
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Object>} Kundendaten
   */
  async getCustomer(customerId, options = {}) {
    try {
      logger.debug(`Rufe Kunden ab: ${customerId}`);
      
      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter(options);
      
      // Rufe den Kunden ab
      const customer = await adapter.getCustomer(customerId);
      
      if (!customer) {
        return null;
      }
      
      return {
        ...customer,
        adapter: adapter.constructor.name
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen des Kunden', {
        error: error.message,
        customerId
      });
      throw error;
    }
  }
  
  /**
   * Ruft Kunden ab
   * @param {Object} options - Abfrageoptionen
   * @returns {Promise<Array<Object>>} Kundendaten
   */
  async getCustomers(options = {}) {
    try {
      logger.debug('Rufe Kunden ab');
      
      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter(options);
      
      // Rufe die Kunden ab
      const customers = await adapter.getCustomers(options);
      
      return customers.map(customer => ({
        ...customer,
        adapter: adapter.constructor.name
      }));
    } catch (error) {
      logger.error('Fehler beim Abrufen der Kunden', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft die unterstützten Zahlungsmethoden ab
   * @returns {Array<Object>} Unterstützte Zahlungsmethoden
   */
  getSupportedMethods() {
    try {
      logger.debug('Rufe unterstützte Zahlungsmethoden ab');
      
      // Erstelle eine Liste der unterstützten Zahlungsmethoden
      const methods = [];
      
      for (const [name, config] of Object.entries(this.options.supportedMethods)) {
        if (config.enabled) {
          methods.push({
            id: name,
            name: config.name,
            currencies: config.currencies || []
          });
        }
      }
      
      return methods;
    } catch (error) {
      logger.error('Fehler beim Abrufen der unterstützten Zahlungsmethoden', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft die unterstützten Währungen ab
   * @returns {Array<Object>} Unterstützte Währungen
   */
  getSupportedCurrencies() {
    try {
      logger.debug('Rufe unterstützte Währungen ab');
      
      // Sammle alle unterstützten Währungen
      const currencies = new Set();
      
      for (const [name, config] of Object.entries(this.options.supportedMethods)) {
        if (config.enabled && config.currencies) {
          for (const currency of config.currencies) {
            currencies.add(currency);
          }
        }
      }
      
      // Hole die Währungskonfigurationen
      const currencyConfigs = [];
      
      for (const currency of currencies) {
        // Suche nach der Währungskonfiguration in den Adaptern
        for (const adapter of this.adapters.values()) {
          if (adapter.options.supportedCurrencies && adapter.options.supportedCurrencies[currency]) {
            currencyConfigs.push({
              id: currency,
              name: adapter.options.supportedCurrencies[currency].name,
              symbol: adapter.options.supportedCurrencies[currency].symbol,
              decimals: adapter.options.supportedCurrencies[currency].decimals
            });
            break;
          }
        }
      }
      
      return currencyConfigs;
    } catch (error) {
      logger.error('Fehler beim Abrufen der unterstützten Währungen', {
        error: error.message
      });
      throw error;
    }
  }
}/**
 * @fileoverview Payment-Manager für die Verwaltung verschiedener Payment-Adapter
 * 
 * Dieser Manager verwaltet verschiedene Payment-Adapter und bietet eine einheitliche
 * Schnittstelle für die Interaktion mit verschiedenen Zahlungsmethoden. Er ermöglicht
 * die flexible Auswahl und Kombination verschiedener Zahlungsmethoden für unterschiedliche
 * Anwendungsfälle.
 */

import { logger } from '../../utils/logger.js';
import { CryptoPaymentAdapter } from './CryptoPaymentAdapter.js';

/**
 * Payment-Manager für die Verwaltung verschiedener Payment-Adapter
 */
export class PaymentManager {
  /**
   * Erstellt eine neue Instanz des PaymentManager
   * @param {Object} options - Konfigurationsoptionen
   * @param {Object} options.adapters - Konfiguration für die Adapter
   * @param {Object} options.defaultAdapter - Name des Standard-Adapters
   * @param {Object} options.supportedMethods - Unterstützte Zahlungsmethoden
   */
  constructor(options = {}) {
    this.options = {
      adapters: options.adapters || {},
      defaultAdapter: options.defaultAdapter || 'crypto',
      supportedMethods: options.supportedMethods || {
        crypto: {
          name: 'Kryptowährungen',
          adapter: 'crypto',
          enabled: true,
          currencies: ['DOT', 'ETH', 'USDT']
        }
      },
      ...options
    };
    
    this.adapters = new Map();
    
    logger.info('PaymentManager initialisiert');
  }
  
  /**
   * Initialisiert den PaymentManager und alle konfigurierten Adapter
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      logger.info('Initialisiere PaymentManager');
      
      // Initialisiere die Adapter
      await this.initializeAdapters();
      
      logger.info('PaymentManager erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des PaymentManager', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Initialisiert die konfigurierten Adapter
   * @returns {Promise<void>}
   */
  async initializeAdapters() {
    try {
      // Initialisiere den Crypto-Adapter, falls konfiguriert
      if (this.options.adapters.crypto) {
        const cryptoAdapter = new CryptoPaymentAdapter(this.options.adapters.crypto);
        await cryptoAdapter.initialize();
        this.adapters.set('crypto', cryptoAdapter);
        logger.debug('Crypto-Adapter initialisiert');
      }
      
      // Hier können weitere Adapter initialisiert werden, z.B. für Stripe, PayPal, etc.
      
      // Prüfe, ob der Standard-Adapter verfügbar ist
      if (!this.adapters.has(this.options.defaultAdapter)) {
        throw new Error(`Standard-Adapter '${this.options.defaultAdapter}' nicht verfügbar`);
      }
      
      logger.info(`${this.adapters.size} Payment-Adapter initialisiert`);
    } catch (error) {
      logger.error('Fehler bei der Initialisierung der Adapter', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Wählt den passenden Adapter basierend auf den Optionen aus
   * @param {Object} options - Optionen
   * @returns {Object} Der ausgewählte Adapter
   */
  selectAdapter(options = {}) {
    // Verwende den explizit angegebenen Adapter, falls vorhanden
    if (options.adapter && this.adapters.has(options.adapter)) {
      return this.adapters.get(options.adapter);
    }
    
    // Verwende den Adapter für die angegebene Zahlungsmethode, falls vorhanden
    if (options.method && this.options.supportedMethods[options.method]) {
      const adapterName = this.options.supportedMethods[options.method].adapter;
      if (this.adapters.has(adapterName)) {
        return this.adapters.get(adapterName);
      }
    }
    
    // Verwende den Adapter für die angegebene Währung, falls vorhanden
    if (options.currency) {
      for (const [methodName, methodConfig] of Object.entries(this.options.supportedMethods)) {
        if (methodConfig.currencies && methodConfig.currencies.includes(options.currency)) {
          const adapterName = methodConfig.adapter;
          if (this.adapters.has(adapterName)) {
            return this.adapters.get(adapterName);
          }
        }
      }
    }
    
    // Verwende den Standard-Adapter
    return this.adapters.get(this.options.defaultAdapter);
  }
  
  /**
   * Erstellt eine Zahlung
   * @param {Object} payment - Zahlungsdaten
   * @returns {Promise<Object>} Zahlungsergebnis
   */
  async createPayment(payment) {
    try {
      logger.info('Erstelle Zahlung');
      
      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter(payment);
      
      // Erstelle die Zahlung
      const result = await adapter.createPayment(payment);
      
      logger.info(`Zahlung erstellt: ${result.id}`);
      
      return {
        ...result,
        adapter: adapter.constructor.name
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen der Zahlung', {
        error: error.message,
        payment
      });
      throw error;
    }
  }
  
  /**
   * Verarbeitet eine Zahlung
   * @param {string} paymentId - Zahlungs-ID
   * @param {Object} options - Verarbeitungsoptionen
   * @returns {Promise<Object>} Verarbeitungsergebnis
   */
  async processPayment(paymentId, options = {}) {
    try {
      logger.info(`Verarbeite Zahlung: ${paymentId}`);
      
      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter(options);
      
      // Verarbeite die Zahlung
      const result = await adapter.processPayment(paymentId, options);
      
      logger.info(`Zahlung verarbeitet: ${paymentId}`);
      
      return {
        ...result,
        adapter: adapter.constructor.name
      };
    } catch (error) {
      logger.error('Fehler bei der Verarbeitung der Zahlung', {
        error: error.message,
        paymentId
      });
      throw error;
    }
  }
  
  /**
   * Storniert eine Zahlung
   * @param {string} paymentId - Zahlungs-ID
   * @param {Object} options - Stornierungsoptionen
   * @returns {Promise<Object>} Stornierungsergebnis
   */
  async cancelPayment(paymentId, options = {}) {
    try {
      logger.info(`Storniere Zahlung: ${paymentId}`);
      
      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter(options);
      
      // Storniere die Zahlung
      const result = await adapter.cancelPayment(paymentId, options);
      
      logger.info(`Zahlung storniert: ${paymentId}`);
      
      return {
        ...result,
        adapter: adapter.constructor.name
      };
    } catch (error) {
      logger.error('Fehler bei der Stornierung der Zahlung', {
        error: error.message,
        paymentId
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Zahlung ab
   * @param {string} paymentId - Zahlungs-ID
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Object>} Zahlungsdaten
   */
  async getPayment(paymentId, options = {}) {
    try {
      logger.debug(`Rufe Zahlung ab: ${paymentId}`);
      
      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter(options);
      
      // Rufe die Zahlung ab
      const payment = await adapter.getPayment(paymentId);
      
      if (!payment) {
        return null;
      }
      
      return {
        ...payment,
        adapter: adapter.constructor.name
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen der Zahlung', {
        error: error.message,
        paymentId
      });
      throw error;
    }
  }
  
  /**
   * Ruft Zahlungen ab
   * @param {Object} options - Abfrageoptionen
   * @returns {Promise<Array<Object>>} Zahlungsdaten
   */
  async getPayments(options = {}) {
    try {
      logger.debug('Rufe Zahlungen ab');
      
      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter(options);
      
      // Rufe die Zahlungen ab
      const payments = await adapter.getPayments(options);
      
      return payments.map(payment => ({
        ...payment,
        adapter: adapter.constructor.name
      }));
    } catch (error) {
      logger.error('Fehler beim Abrufen der Zahlungen', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Erstellt einen Kunden
   * @param {Object} customer - Kundendaten
   * @param {Object} options - Erstellungsoptionen
   * @returns {Promise<Object>} Kundenergebnis
   */
  async createCustomer(customer, options = {}) {
    try {
      logger.info('Erstelle Kunden');
      
      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter(options);
      
      // Erstelle den Kunden
      const result = await adapter.createCustomer(customer);
      
      logger.info(`Kunde erstellt: ${result.id}`);
      
      return {
        ...result,
        adapter: adapter.constructor.name
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen des Kunden', {
        error: error.message,
        customer
      });
      throw error;
    }
  }
  
  /**
   * Aktualisiert einen Kunden
   * @param {string} customerId - Kunden-ID
   * @param {Object} customer - Kundendaten
   * @param {Object} options - Aktualisierungsoptionen
   * @returns {Promise<Object>} Kundenergebnis
   */
  async updateCustomer(customerId, customer, options = {}) {
    try {
      logger.info(`Aktualisiere Kunden: ${customerId}`);
      
      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter(options);
      
      // Aktualisiere den Kunden
      const result = await adapter.updateCustomer(customerId, customer);
      
      logger.info(`Kunde aktualisiert: ${customerId}`);
      
      return {
        ...result,
        adapter: adapter.constructor.name
      };
    } catch (error) {
      logger.error('Fehler beim Aktualisieren des Kunden', {
        error: error.message,
        customerId,
        customer
      });
      throw error;
    }
  }
  
  /**
   * Ruft einen Kunden ab
   * @param {string} customerId - Kunden-ID
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Object>} Kundendaten
   */
  async getCustomer(customerId, options = {}) {
    try {
      logger.debug(`Rufe Kunden ab: ${customerId}`);
      
      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter(options);
      
      // Rufe den Kunden ab
      const customer = await adapter.getCustomer(customerId);
      
      if (!customer) {
        return null;
      }
      
      return {
        ...customer,
        adapter: adapter.constructor.name
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen des Kunden', {
        error: error.message,
        customerId
      });
      throw error;
    }
  }
  
  /**
   * Ruft Kunden ab
   * @param {Object} options - Abfrageoptionen
   * @returns {Promise<Array<Object>>} Kundendaten
   */
  async getCustomers(options = {}) {
    try {
      logger.debug('Rufe Kunden ab');
      
      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter(options);
      
      // Rufe die Kunden ab
      const customers = await adapter.getCustomers(options);
      
      return customers.map(customer => ({
        ...customer,
        adapter: adapter.constructor.name
      }));
    } catch (error) {
      logger.error('Fehler beim Abrufen der Kunden', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft die unterstützten Zahlungsmethoden ab
   * @returns {Array<Object>} Unterstützte Zahlungsmethoden
   */
  getSupportedMethods() {
    try {
      logger.debug('Rufe unterstützte Zahlungsmethoden ab');
      
      // Erstelle eine Liste der unterstützten Zahlungsmethoden
      const methods = [];
      
      for (const [name, config] of Object.entries(this.options.supportedMethods)) {
        if (config.enabled) {
          methods.push({
            id: name,
            name: config.name,
            currencies: config.currencies || []
          });
        }
      }
      
      return methods;
    } catch (error) {
      logger.error('Fehler beim Abrufen der unterstützten Zahlungsmethoden', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft die unterstützten Währungen ab
   * @returns {Array<Object>} Unterstützte Währungen
   */
  getSupportedCurrencies() {
    try {
      logger.debug('Rufe unterstützte Währungen ab');
      
      // Sammle alle unterstützten Währungen
      const currencies = new Set();
      
      for (const [name, config] of Object.entries(this.options.supportedMethods)) {
        if (config.enabled && config.currencies) {
          for (const currency of config.currencies) {
            currencies.add(currency);
          }
        }
      }
      
      // Hole die Währungskonfigurationen
      const currencyConfigs = [];
      
      for (const currency of currencies) {
        // Suche nach der Währungskonfiguration in den Adaptern
        for (const adapter of this.adapters.values()) {
          if (adapter.options.supportedCurrencies && adapter.options.supportedCurrencies[currency]) {
            currencyConfigs.push({
              id: currency,
              name: adapter.options.supportedCurrencies[currency].name,
              symbol: adapter.options.supportedCurrencies[currency].symbol,
              decimals: adapter.options.supportedCurrencies[currency].decimals
            });
            break;
          }
        }
      }
      
      return currencyConfigs;
    } catch (error) {
      logger.error('Fehler beim Abrufen der unterstützten Währungen', {
        error: error.message
      });
      throw error;
    }
  }
}/**
 * @fileoverview Payment-Manager für die Verwaltung verschiedener Payment-Adapter
 * 
 * Dieser Manager verwaltet verschiedene Payment-Adapter und bietet eine einheitliche
 * Schnittstelle für die Interaktion mit verschiedenen Zahlungsmethoden. Er ermöglicht
 * die flexible Auswahl und Kombination verschiedener Zahlungsmethoden für unterschiedliche
 * Anwendungsfälle.
 */

import { logger } from '../../utils/logger.js';
import { CryptoPaymentAdapter } from './CryptoPaymentAdapter.js';

/**
 * Payment-Manager für die Verwaltung verschiedener Payment-Adapter
 */
export class PaymentManager {
  /**
   * Erstellt eine neue Instanz des PaymentManager
   * @param {Object} options - Konfigurationsoptionen
   * @param {Object} options.adapters - Konfiguration für die Adapter
   * @param {Object} options.defaultAdapter - Name des Standard-Adapters
   * @param {Object} options.supportedMethods - Unterstützte Zahlungsmethoden
   */
  constructor(options = {}) {
    this.options = {
      adapters: options.adapters || {},
      defaultAdapter: options.defaultAdapter || 'crypto',
      supportedMethods: options.supportedMethods || {
        crypto: {
          name: 'Kryptowährungen',
          adapter: 'crypto',
          enabled: true,
          currencies: ['DOT', 'ETH', 'USDT']
        }
      },
      ...options
    };
    
    this.adapters = new Map();
    
    logger.info('PaymentManager initialisiert');
  }
  
  /**
   * Initialisiert den PaymentManager und alle konfigurierten Adapter
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      logger.info('Initialisiere PaymentManager');
      
      // Initialisiere die Adapter
      await this.initializeAdapters();
      
      logger.info('PaymentManager erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des PaymentManager', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Initialisiert die konfigurierten Adapter
   * @returns {Promise<void>}
   */
  async initializeAdapters() {
    try {
      // Initialisiere den Crypto-Adapter, falls konfiguriert
      if (this.options.adapters.crypto) {
        const cryptoAdapter = new CryptoPaymentAdapter(this.options.adapters.crypto);
        await cryptoAdapter.initialize();
        this.adapters.set('crypto', cryptoAdapter);
        logger.debug('Crypto-Adapter initialisiert');
      }
      
      // Hier können weitere Adapter initialisiert werden, z.B. für Stripe, PayPal, etc.
      
      // Prüfe, ob der Standard-Adapter verfügbar ist
      if (!this.adapters.has(this.options.defaultAdapter)) {
        throw new Error(`Standard-Adapter '${this.options.defaultAdapter}' nicht verfügbar`);
      }
      
      logger.info(`${this.adapters.size} Payment-Adapter initialisiert`);
    } catch (error) {
      logger.error('Fehler bei der Initialisierung der Adapter', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Wählt den passenden Adapter basierend auf den Optionen aus
   * @param {Object} options - Optionen
   * @returns {Object} Der ausgewählte Adapter
   */
  selectAdapter(options = {}) {
    // Verwende den explizit angegebenen Adapter, falls vorhanden
    if (options.adapter && this.adapters.has(options.adapter)) {
      return this.adapters.get(options.adapter);
    }
    
    // Verwende den Adapter für die angegebene Zahlungsmethode, falls vorhanden
    if (options.method && this.options.supportedMethods[options.method]) {
      const adapterName = this.options.supportedMethods[options.method].adapter;
      if (this.adapters.has(adapterName)) {
        return this.adapters.get(adapterName);
      }
    }
    
    // Verwende den Adapter für die angegebene Währung, falls vorhanden
    if (options.currency) {
      for (const [methodName, methodConfig] of Object.entries(this.options.supportedMethods)) {
        if (methodConfig.currencies && methodConfig.currencies.includes(options.currency)) {
          const adapterName = methodConfig.adapter;
          if (this.adapters.has(adapterName)) {
            return this.adapters.get(adapterName);
          }
        }
      }
    }
    
    // Verwende den Standard-Adapter
    return this.adapters.get(this.options.defaultAdapter);
  }
  
  /**
   * Erstellt eine Zahlung
   * @param {Object} payment - Zahlungsdaten
   * @returns {Promise<Object>} Zahlungsergebnis
   */
  async createPayment(payment) {
    try {
      logger.info('Erstelle Zahlung');
      
      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter(payment);
      
      // Erstelle die Zahlung
      const result = await adapter.createPayment(payment);
      
      logger.info(`Zahlung erstellt: ${result.id}`);
      
      return {
        ...result,
        adapter: adapter.constructor.name
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen der Zahlung', {
        error: error.message,
        payment
      });
      throw error;
    }
  }
  
  /**
   * Verarbeitet eine Zahlung
   * @param {string} paymentId - Zahlungs-ID
   * @param {Object} options - Verarbeitungsoptionen
   * @returns {Promise<Object>} Verarbeitungsergebnis
   */
  async processPayment(paymentId, options = {}) {
    try {
      logger.info(`Verarbeite Zahlung: ${paymentId}`);
      
      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter(options);
      
      // Verarbeite die Zahlung
      const result = await adapter.processPayment(paymentId, options);
      
      logger.info(`Zahlung verarbeitet: ${paymentId}`);
      
      return {
        ...result,
        adapter: adapter.constructor.name
      };
    } catch (error) {
      logger.error('Fehler bei der Verarbeitung der Zahlung', {
        error: error.message,
        paymentId
      });
      throw error;
    }
  }
  
  /**
   * Storniert eine Zahlung
   * @param {string} paymentId - Zahlungs-ID
   * @param {Object} options - Stornierungsoptionen
   * @returns {Promise<Object>} Stornierungsergebnis
   */
  async cancelPayment(paymentId, options = {}) {
    try {
      logger.info(`Storniere Zahlung: ${paymentId}`);
      
      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter(options);
      
      // Storniere die Zahlung
      const result = await adapter.cancelPayment(paymentId, options);
      
      logger.info(`Zahlung storniert: ${paymentId}`);
      
      return {
        ...result,
        adapter: adapter.constructor.name
      };
    } catch (error) {
      logger.error('Fehler bei der Stornierung der Zahlung', {
        error: error.message,
        paymentId
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Zahlung ab
   * @param {string} paymentId - Zahlungs-ID
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Object>} Zahlungsdaten
   */
  async getPayment(paymentId, options = {}) {
    try {
      logger.debug(`Rufe Zahlung ab: ${paymentId}`);
      
      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter(options);
      
      // Rufe die Zahlung ab
      const payment = await adapter.getPayment(paymentId);
      
      if (!payment) {
        return null;
      }
      
      return {
        ...payment,
        adapter: adapter.constructor.name
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen der Zahlung', {
        error: error.message,
        paymentId
      });
      throw error;
    }
  }
  
  /**
   * Ruft Zahlungen ab
   * @param {Object} options - Abfrageoptionen
   * @returns {Promise<Array<Object>>} Zahlungsdaten
   */
  async getPayments(options = {}) {
    try {
      logger.debug('Rufe Zahlungen ab');
      
      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter(options);
      
      // Rufe die Zahlungen ab
      const payments = await adapter.getPayments(options);
      
      return payments.map(payment => ({
        ...payment,
        adapter: adapter.constructor.name
      }));
    } catch (error) {
      logger.error('Fehler beim Abrufen der Zahlungen', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Erstellt einen Kunden
   * @param {Object} customer - Kundendaten
   * @param {Object} options - Erstellungsoptionen
   * @returns {Promise<Object>} Kundenergebnis
   */
  async createCustomer(customer, options = {}) {
    try {
      logger.info('Erstelle Kunden');
      
      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter(options);
      
      // Erstelle den Kunden
      const result = await adapter.createCustomer(customer);
      
      logger.info(`Kunde erstellt: ${result.id}`);
      
      return {
        ...result,
        adapter: adapter.constructor.name
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen des Kunden', {
        error: error.message,
        customer
      });
      throw error;
    }
  }
  
  /**
   * Aktualisiert einen Kunden
   * @param {string} customerId - Kunden-ID
   * @param {Object} customer - Kundendaten
   * @param {Object} options - Aktualisierungsoptionen
   * @returns {Promise<Object>} Kundenergebnis
   */
  async updateCustomer(customerId, customer, options = {}) {
    try {
      logger.info(`Aktualisiere Kunden: ${customerId}`);
      
      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter(options);
      
      // Aktualisiere den Kunden
      const result = await adapter.updateCustomer(customerId, customer);
      
      logger.info(`Kunde aktualisiert: ${customerId}`);
      
      return {
        ...result,
        adapter: adapter.constructor.name
      };
    } catch (error) {
      logger.error('Fehler beim Aktualisieren des Kunden', {
        error: error.message,
        customerId,
        customer
      });
      throw error;
    }
  }
  
  /**
   * Ruft einen Kunden ab
   * @param {string} customerId - Kunden-ID
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Object>} Kundendaten
   */
  async getCustomer(customerId, options = {}) {
    try {
      logger.debug(`Rufe Kunden ab: ${customerId}`);
      
      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter(options);
      
      // Rufe den Kunden ab
      const customer = await adapter.getCustomer(customerId);
      
      if (!customer) {
        return null;
      }
      
      return {
        ...customer,
        adapter: adapter.constructor.name
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen des Kunden', {
        error: error.message,
        customerId
      });
      throw error;
    }
  }
  
  /**
   * Ruft Kunden ab
   * @param {Object} options - Abfrageoptionen
   * @returns {Promise<Array<Object>>} Kundendaten
   */
  async getCustomers(options = {}) {
    try {
      logger.debug('Rufe Kunden ab');
      
      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter(options);
      
      // Rufe die Kunden ab
      const customers = await adapter.getCustomers(options);
      
      return customers.map(customer => ({
        ...customer,
        adapter: adapter.constructor.name
      }));
    } catch (error) {
      logger.error('Fehler beim Abrufen der Kunden', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft die unterstützten Zahlungsmethoden ab
   * @returns {Array<Object>} Unterstützte Zahlungsmethoden
   */
  getSupportedMethods() {
    try {
      logger.debug('Rufe unterstützte Zahlungsmethoden ab');
      
      // Erstelle eine Liste der unterstützten Zahlungsmethoden
      const methods = [];
      
      for (const [name, config] of Object.entries(this.options.supportedMethods)) {
        if (config.enabled) {
          methods.push({
            id: name,
            name: config.name,
            currencies: config.currencies || []
          });
        }
      }
      
      return methods;
    } catch (error) {
      logger.error('Fehler beim Abrufen der unterstützten Zahlungsmethoden', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft die unterstützten Währungen ab
   * @returns {Array<Object>} Unterstützte Währungen
   */
  getSupportedCurrencies() {
    try {
      logger.debug('Rufe unterstützte Währungen ab');
      
      // Sammle alle unterstützten Währungen
      const currencies = new Set();
      
      for (const [name, config] of Object.entries(this.options.supportedMethods)) {
        if (config.enabled && config.currencies) {
          for (const currency of config.currencies) {
            currencies.add(currency);
          }
        }
      }
      
      // Hole die Währungskonfigurationen
      const currencyConfigs = [];
      
      for (const currency of currencies) {
        // Suche nach der Währungskonfiguration in den Adaptern
        for (const adapter of this.adapters.values()) {
          if (adapter.options.supportedCurrencies && adapter.options.supportedCurrencies[currency]) {
            currencyConfigs.push({
              id: currency,
              name: adapter.options.supportedCurrencies[currency].name,
              symbol: adapter.options.supportedCurrencies[currency].symbol,
              decimals: adapter.options.supportedCurrencies[currency].decimals
            });
            break;
          }
        }
      }
      
      return currencyConfigs;
    } catch (error) {
      logger.error('Fehler beim Abrufen der unterstützten Währungen', {
        error: error.message
      });
      throw error;
    }
  }
}