/**
 * @fileoverview ExternalCitationService für die Integration mit externen Zitationsdatenbanken
 * 
 * Dieser Service integriert die DeSci-Scholar-Plattform mit externen Zitationsdatenbanken
 * wie Semantic Scholar, Scinapse, arXiv und WorldCat, um umfassendere Zitationsdaten zu erhalten.
 */

import axios from 'axios';
import { logger } from '../utils/logger.js';

/**
 * Konfiguration für externe Zitationsdienste
 */
const EXTERNAL_SERVICES_CONFIG = {
  semanticScholar: {
    baseUrl: 'https://api.semanticscholar.org/v1',
    apiKey: process.env.SEMANTIC_SCHOLAR_API_KEY || '',
    enabled: true,
    name: 'Semantic Scholar'
  },
  scinapse: {
    baseUrl: 'https://api.scinapse.io/v1',
    apiKey: process.env.SCINAPSE_API_KEY || '',
    enabled: true,
    name: '<PERSON>ina<PERSON>'
  },
  arxiv: {
    baseUrl: 'http://export.arxiv.org/api/query',
    enabled: true,
    name: 'arXiv'
  },
  arxiv: {
    baseUrl: 'http://export.arxiv.org/api/query',
    enabled: true,
    name: 'PubMed'
  },
  arxiv: {
    baseUrl: 'http://export.arxiv.org/api/query',
    enabled: false, // Standardmäßig deaktiviert für Phase 1
    name: 'arXiv'
  // Platzhalter für zukünftige PubMed-Integration (Phase 2)
  pubmed: {
    baseUrl: 'https://eutils.ncbi.nlm.nih.gov/entrez/eutils},
  arxiv: {
    baseUrl: 'http://export.arxiv.org/api/query',
    enabled: false, // Standardmäßig deaktiviert für Phase 1
    name: 'arXiv'
  // Platzhalter für zukünftige PubMed-Integration (Phase 2)
  pubmed: {
    baseUrl: 'https://eutils.ncbi.nlm.nih.gov/entrez/eutils},
  // Platzhalter für zukünftige PubMed-Integration (Phase 2)
  pubmed: {
    baseUrl: 'https://eutils.ncbi.nlm.nih.gov/entrez/eutils',
    apiKey: process.env.PUBMED_API_KEY || '',
    enabled: false, // Standardmäßig deaktiviert für Phase 1
    name: 'PubMed'
  },
  // Platzhalter für zukünftige PubMed-Integration (Phase 2)
  pubmed: {
    baseUrl: 'https://eutils.ncbi.nlm.nih.gov/entrez/eutils',
    apiKey: process.env.PUBMED_API_KEY || '',
    enabled: false, // Standardmäßig deaktiviert für Phase 1
    name: 'PubMed'
  }
};

/**
 * Service für die Integration mit externen Zitationsdatenbanken
 */
export class ExternalCitationService {
  /**
   * Erstellt eine neue Instanz des ExternalCitationService
   * @param {Object} customConfig - Optionale benutzerdefinierte Konfiguration
   * @param {Object} customConfig - Optionale benutzerdefinierte Konfiguration
   * @param {Object} customConfig - Optionale benutzerdefinierte Konfiguration
   * @param {Object} customConfig - Optionale benutzerdefinierte Konfiguration
   */
  constructor(customConfig = {}
    // Initialisiere die Standard-Service-Adapter
    this._initializeServiceAdapters();
  }

  /**
   * Initialisiert die Standard-Service-Adapter
   * @private
   */
  _initializeServiceAdapters() {
    // Registriere die Standard-Adapter für die konfigurierten Dienste
    if (this.config.semanticScholar) {
      this.registerServiceAdapter('semanticScholar', {
        testConnection: this.testSemanticScholarConnection.bind(this),
        getCitations: this.getSemanticScholarCitations.bind(this)
      });
    }

    if (this.config.scinapse) {
      this.registerServiceAdapter('scinapse', {
        testConnection: this.testScinapseConnection.bind(this),
        getCitations: this.getScinapseCitations.bind(this)
      });
    }

    if (this.config.arxiv) {
      this.registerServiceAdapter('arxiv', {
        testConnection: this.testArxivConnection.bind(this),
        getCitations: this.getArxivCitations.bind(this)
      });
    }

    if (this.config.worldcat) {
      this.registerServiceAdapter('worldcat', {
        testConnection: this.testWorldCatConnection.bind(this),
        getCitations: this.getWorldCatCitations.bind(this)
      });
    }

    if (this.config.pubmed) {
      // Platzhalter für zukünftige PubMed-Integration
      this.registerServiceAdapter('pubmed', {
        testConnection: async () => {
          throw new Error('PubMed-Integration ist noch nicht implementiert');
        },
        getCitations: async () => {
          throw new Error('PubMed-Integration ist noch nicht implementiert');
        }
      });
    }
  }

  /**
   * Registriert einen neuen Service-Adapter
   * @param {string} serviceName - Name des Dienstes
   * @param {Object} adapter - Adapter-Objekt mit Methoden
   * @returns {boolean} - Erfolg der Registrierung
   */
  registerServiceAdapter(serviceName, adapter) {
    if (!serviceName || typeof serviceName !== 'string') {
      logger.error('Ungültiger Service-Name bei der Adapter-Registrierung');
      return false;
    }

    if (!adapter || typeof adapter !== 'object') {
      logger.error(`Ungültiger Adapter für Service ${serviceName}`);
      return false;
    }

    // Prüfe, ob die erforderlichen Methoden vorhanden sind
    const requiredMethods = ['testConnection', 'getCitations'];
    for (const method of requiredMethods) {
      if (typeof adapter[method] !== 'function') {
        logger.error(`Adapter für ${serviceName} fehlt die erforderliche Methode: ${method}`);
        return false;
      }
    }

    // Registriere den Adapter
    this.serviceAdapters[serviceName] = adapter;
    logger.info(`Service-Adapter für ${serviceName} erfolgreich registriert`);
    return true;
  }

  /**
   * Entfernt einen Service-Adapter
   * @param {string} serviceName - Name des Dienstes
   * @returns {boolean} - Erfolg der Entfernung
   */
  unregisterServiceAdapter(serviceName) {
    if (this.serviceAdapters[serviceName]) {
      delete this.serviceAdapters[serviceName];
      logger.info(`Service-Adapter für ${serviceName} entfernt`);
      return true;
    }
    return false;
  }
  
  /**
   * Initialisiert den Service
   * @param {Array<string>} specificServices - Optional: Liste bestimmter Dienste, die initialisiert werden sollen
   * @returns {Promise<boolean>} - Erfolg der Initialisierung
   */
  async initialize(specificServices = null) {
    // Kombiniere Standard-Konfiguration mit benutzerdefinierter KonfigurationcustomConfig = {}) {
    // Kombiniere Standard-Konfiguration mit benutzerdefinierter KonfigurationcustomConfig = {}) {
    // Kombiniere Standard-Konfiguration mit benutzerdefinierter KonfigurationcustomConfig = {}) {
    // Kombiniere Standard-Konfiguration mit benutzerdefinierter Konfiguration
    this.config = { ...{ ...EXTERNAL_SERVICES_CONFIG, ...customConfig };
    this.initialized = false;
    this.serviceAdapters = {}; // Speichert die Service-Adapter für jeden Dienst

    // HTTP-Client für API-Anfragen
    this.httpClient = axios.create({
      timeout: 10000, // 10 Sekunden Timeout
      if (!adapter || !serviceConfig) {
            logger.warn(`Service ${serviceName} ist nicht konfiguriert oder hat keinen Adapter`);
          continue;
        }

        if (serviceConfigheaders: const servicesToInitialize = specificServices || Object.keys(this.serviceAdapters);

      for (const serviceName of servicesToInitialize)ent-Type': 'application/json',
        'Accept': 'application/json'
    const servicesToInitialize = specificServices || Object.keys(this.serviceAdapters);

      for (const serviceName of servicesToInitialize)  // Initialisiere die Standard-Service-AdapterviceConfig.name} erfolgreich initialisiert`
      this._initializeServiceAdapters();
  }

  /**
   * Initialisiert die Standard-Service-Adapter
   * @private
   */
  _initializeServiceAdapters() {
    // Registriere die Standard-Adapter für die konfigurierten Dienste
    if (this.config.semanticScholar) {
    } else {
          logger.info(`Service ${serviceConfig.name} ist deaktiviert und wird übersprungen`)  this.registerServiceAdapter('semanticScholar', {
        testConnection: this.testSemanticScholarConnection.bind(this),
        getCitations: this.getSemanticScholarCitations.bind(this)
      });
    }

    if (this.config.scinapse) {
      this.registerServiceAdapter('scinapse', {
        testConnection: this.testScinapseConnection.bind(this),
        getCitations: this.getScinapseCitations.bind(this)
      });
    }

    if (this.config.arxiv) {
      this.registerServiceAdapter('arxiv', {
        testConnection: this.testArxivConnection.bind(this),
        getCitations: this.getArxivCitations.bind(this)
      });
    }

    if (this.config.worldcat) {
      this.registerServiceAdapter('worldcat', {
        testConnection: this.testWorldCatConnection.bind(this),
        getCitations: this.getWorldCatCitations.bind(this)
      });
    }

    if (this.config.pubmed) {
      // Platzhalter für zukünftige PubMed-Integration
      this.registerServiceAdapter('pubmed', {
        testConnection: async () => {
          throw new Error('PubMed-Integration ist noch nicht implementiert');
        },
        getCitations: async () => {
          throw new Error('PubMed-Integration ist noch nicht implementiert');
        }
      });
    }
  }

  /**
   * Registriert einen neuen Service-Adapter
   * @param {string} serviceName - Name des Dienstes
   * @param {Object} adapter - Adapter-Objekt mit Methoden
   * @returns {boolean} - Erfolg der Registrierung
   */
  registerServiceAdapter(serviceName, adapter) {
    if (!serviceName || typeof serviceName !== 'string') {
      logger.error('Ungültiger Service-Name bei der Adapter-Registrierung');
      return false;
    }

    if (!adapter || typeof adapter !== 'object') {
      logger.error(`Ungültiger Adapter für Service ${serviceName}`);
      return false;
    }

    // Prüfe, ob die erforderlichen Methoden vorhanden sind
    const requiredMethods = ['testConnection', 'getCitations'];
    for (const method of requiredMethods) {
      if (typeof adapter[method] !== 'function') {
        logger.error(`Adapter für ${serviceName} fehlt die erforderliche Methode: ${method}`);
        return false;
      }
    }

    // Registriere den Adapter
    this.serviceAdapters[serviceName] = adapter;
    logger.info(`Service-Adapter für ${serviceName} erfolgreich registriert`);
    return true;
  }

  /**
   * Entfernt einen Service-Adapter
   * @param {string} serviceName - Name des Dienstes
   * @returns {boolean} - Erfolg der Entfernung
   */
  unregisterServiceAdapter(serviceName) {
    if (this.serviceAdapters[serviceName]) {
      delete this.serviceAdapters[serviceName];
      logger.info(`Service-Adapter für ${serviceName} entfernt`);
      return true;
    const adapter = this.serviceAdapters[serviceName];
        const serviceConfig = me]}

    if (!adapter || !serviceConfig) {
            logger.warn(`Service ${serviceName} ist nicht konfiguriert oder hat keinen Adapter`);
          continue;
        }

        if (serviceConfigreturn false;
  }
  
  /**
   * Initialisiert den Service
   * @param {Array<string>} specificServices - Optional: Liste bestimmter Dienste, die initialisiert werden sollen
   * @returns {Promise<boolean>} - Erfolg der Initialisierung
   */
  async initialize(specificServices = null
    // Initialisiere die Standard-Service-AdapterviceConfig.name} erfolgreich initialisiert`
      this._initializeServiceAdapters();
  }

  /**
   * Initialisiert die Standard-Service-Adapter
   * @private
   } else {
          logger.info(`Service ${serviceConfig.name} ist deaktiviert und wird übersprungen`)*/
  _initializeServiceAdapters() {
    // Registriere die Standard-Adapter für die konfigurierten Dienste{
      this.registerServiceAdapter('semanticScholar', {
        testConnection: this.testSemanticScholarConnection.bind(this),
        if (!adapter || !serviceConfig) {
                logger.warn(`Service ${serviceName} ist nicht konfiguriert oder hat keinen Adapter`viceConfig.name} erfolgreich initialisiert`);
            continueviceConfig.name} erfolgreich initialisiert`;
          if (serviceConfiggetCitations: this.getSemanticScholarCitations.bind(this)
      });
    }
cinapse) {
      this.registerServiceAdapter('scinapse', {
        testConnection: this.testScinapseConnection.bind(this),
        getCitations: this.getScinapseCitations.bind(this)
      });
    }

      this.registerServiceAdapter('arxiv', {
    } else {
          logger.info(`Service ${serviceConfig.name} ist deaktiviert und wird übersprungen`)    testConnection: this.testArxivConnection.bind(this),
        getCitations: this.getArxivCitations.bind(this)
      });

    if (this.config.worldcat) {
      this.registerServiceAdapter('worldcat', {
      } else {
          logger.info(`Service ${serviceConfig.name} ist deaktiviert und wird übersprungen`)  testConnection: this.testWorldCatConnection.bind(this),
        getCitations: this.getWorldCatCitations.bind(this)
      });
    }

    if (this.config.pubmed) {
      // Platzhalter für zukünftige PubMed-Integration
      this.registerServiceAdapter('pubmed', {
        testConnection: async () => {
          throw new Error('PubMed-Integration ist noch nicht implementiert');
        },
        getCitations: async () => {
          throw new Error('PubMed-Integration ist noch nicht implementiert');
      logger.info(`Rufe Zitationsdaten von ${serviceConfig.name} für DOI ${doi} ab`);
        }
      }
      });
    }
  }

  /**
   * Registriert einen neuen Service-Adapter
   * @param {string} serviceName - Name des Dienstes
   * @param {Object} adapter - Adapter-Objekt mit Methoden
   * @returns {boolean} - Erfolg der Registrierung
   */
  registerServiceAdapter(serviceName, adapter) {
    if (!serviceName || typeof serviceName !== 'string') {
      logger.error('Ungültiger Service-Name bei der Adapter-Registrierung');
      return false;
    }

    if (!adapter || typeof adapter !== 'object') {
      logger.error(`Ungültiger Adapter für Service ${serviceName}`);
      return false;
    }

    // Prüfe, ob die erforderlichen Methoden vorhanden sind
    const requiredMethods = ['testConnection', 'getCitations'];
    for (const method of requiredMethods) {
      if (typeof adapter[method] !== 'function') {
        logger.error(`Adapter für ${serviceName} fehlt die erforderliche Methode: ${method}`);
        return false;
      }
    }

    // Registriere den Adapter
    this.serviceAdapters[serviceName] = adapter;
    logger.info(`Service-Adapter für ${serviceName} erfolgreich registriert`);
    return true;
  }

  /**
   * Entfernt einen Service-Adapter
   * @param {string} serviceName - Name des Dienstes
   * @returns {boolean} - Erfolg der Entfernung
   */
  unregisterServiceAdapter(serviceName) {
    if (this.serviceAdapters[serviceName]) {
      delete this.serviceAdapters[serviceName];
      logger.info(`Service-Adapter für ${serviceName} entfernt`);
      return true;
    }
    return false;
  }
  
  /**
   * Initialisiert den Service
   * @param {Array<string>} specificServices - Optional: Liste bestimmter Dienste, die initialisiert werden sollen
   * @returns {Promise<boolean>} - Erfolg der Initialisierung
   */
  async initialize(specificServices = null) {
    try {
      logger.info('ExternalCitationService wird initialisiert');

      // Prüfe die Verfügbarkeit der externen Dienste
      const availableServices = [];
      const servicesToInitialize = specificServices || Object.keys(this.serviceAdapters);

      for (const serviceName of servicesToInitialize) {
        const adapter = this.serviceAdapters[serviceName];
        const serviceConfig = this.config[serviceName];

        if (!adapter || !serviceConfig) {
          logger.warn(`Service ${serviceName} ist nicht konfiguriert oder hat keinen Adapter`);
          continue;
        }

        if (serviceConfig.enabled) {
          try {
            await adapter.testConnection();
            availableServices.push(serviceConfig.name);
            logger.info(`Service ${serviceConfig.name} erfolgreich initialisiert`);
          } catch (error) {
            logger.warn(`${serviceConfig.name} API nicht verfügbar: ${error.message}`);
            serviceConfig.enabled = false;
            } else {
          logger.info(`Service ${serviceConfig.name} ist deaktiviert und wird übersprungen`);
        }
      }

      if (availableServices.length > 0) {
        logger.info(`ExternalCitationService erfolgreich initialisiert. Verfügbare Dienste: ${availableServices.join(', ')}`);
        this.initialized = true;
      } else {
        logger.warn('ExternalCitationService initialisiert, aber keine externen Dienste verfügbar');
        this.initialized = true; // Trotzdem als initialisiert markieren, um Fehler zu vermeiden
      }

      return true;
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des ExternalCitationService', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Testet die Verbindung zu Semantic Scholar
   */
  async testSemanticScholarConnection() {
    try {
      // Einfache Testanfrage an Semantic Scholar
      const response = await this.httpClient.get(`${this.config.semanticScholar.baseUrl}/paper/10.1145/3025453.3025773`, {
      logger.info(`Rufe Zitationsdaten von ${serviceConfig.name} für DOI ${doi} ab`);
      headers: this.config.semanticScholar.apiKey ? {
          'x-api-key': this.config.semanticScholar.apiKey
        } : {}
      });
        }
      
      if (response.status !== 200) {
        throw new Error(`Unerwarteter Statuscode: ${response.status}`);
      }

      return true;
    } catch (error) {
      logger.error('Fehler beim Testen der Semantic Scholar Verbindung', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Testet die Verbindung zu Scinapse
   */
  async testScinapseConnection() {
    try {
      // Einfache Testanfrage an Scinapse
      // Hinweis: Die genaue API-Struktur von Scinapse muss angepasst werden
      const response = await this.httpClient.get(`${this.config.scinapse.baseUrl}/papers/search?query=artificial+intelligence`, {
      logger.info(`Rufe Zitationsdaten von ${serviceConfig.name} für DOI ${doi} ab`);
      headers: this.config.scinapse.apiKey ? {
          'x-api-key': this.config.scinapse.apiKey
        } : {}
      });

      // Sammle Daten von allen verfügbaren Diensten
      const promises = [];
      const servicesToQuery = specificServices || Object.keys(this.serviceAdapters);

      for (const serviceName of servicesToQuery) {
      logger.info(`Rufe Zitationsdaten von ${serviceConfig.name} für DOI ${doi} ab`);
      const adapter = this.serviceAdapters[serviceName];
        const serviceConfig = this.config[serviceName];

        if (!adapter || !serviceConfig) {
          logger.warn(`Service ${serviceName} ist nicht konfiguriert oder hat keinen Adapter`);
          continue;        }

        if (se.status !== 200) {
        throw new Error(`Unerwarteter Statuscode: ${response.status}`);
      }
      
      return true;
    } catch (error) {
      logger.error('Fehler beim Testen der Scinapse Verbindung', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Testet die Verbindung zu arXiv
   */
  async testArxivConnection() {
    try {
      // Einfache Testanfrage an arXiv
      const response = await axios.get(`${this.config.arxiv.baseUrl}?search_query=all:electron&start=0&max_results=1`);
      
      if (response.status !== 200) {
        throw new Error(`Unerwarteter Statuscode: ${response.status}`);
      }
      
      return true;
    } catch (error) {
      logger.error('Fehler beim Testen der arXiv Verbindung', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Testet die Verbindung zu WorldCat
   */
  async testWorldCatConnection() {
    try {
      // Einfache Testanfrage an WorldCat
      const url = `${this.config.worldcat.baseUrl}?q=science&wskey=${this.config.worldcat.apiKey}&format=json`;
      const response = await axios.get(url);
      
      if (response.status !== 200) {
        throw new Error(`Unerwarteter Statuscode: ${response.status}`);
      }
      
      return true;
    } catch (error) {
      logger.error('Fehler beim Testen der WorldCat Verbindung', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Testet die Verbindung zu WorldCat
   */
  async testWorldCatConnection() {
    try {
      // Einfache Testanfrage an WorldCat
      const url = `${this.config.worldcat.baseUrl}?q=science&wskey=${this.config.worldcat.apiKey}&format=json`;
      const response = await axios.get(url);
      
      if (response.status !== 200) {
        throw new Error(`Unerwarteter Statuscode: ${response.status}`);
      }
      
      return true;
    } catch (error) {
      logger.error('Fehler beim Testen der WorldCat Verbindung', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Testet die Verbindung zu WorldCat
   */
  async testWorldCatConnection() {
    try {
      // Einfache Testanfrage an WorldCat
      const url = `${this.config.worldcat.baseUrl}?q=science&wskey=${this.config.worldcat.apiKey}&format=json`;
      const response = await axios.get(url);

      if (response.status !== 200) {
        throw new Error(`Unerwarteter Statuscode: ${response.status}`);
      }

      return true;
    } catch (error) {
      logger.error('Fehler beim Testen der WorldCat Verbindung', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Holt Zitationsdaten für eine DOI von externen Diensten
   *
   * @param {string} doi - DOI der Publikation
   * @param {Array<string>} specificServices - Optional: Liste bestimmter Dienste, die abgefragt werden sollen
   * @returns {Promise<Object>} Zitationsdaten
   */
  async getCitationsForDOI(doi, specificServices = null) {
    try {
      if (!this.initialized) {
        await this.initialize();
      }

      logger.info(`Hole externe Zitationsdaten für DOI ${doi}`);

      const results = {
        success: false,
        doi,
        totalCitationCount: 0,
        sources: [],
        citations: []
      };

      // Sammle Daten von allen verfügbaren Diensten
      const promises = [];
      const servicesToQuery = specificServices || Object.keys(this.serviceAdapters);

      for (const serviceName of servicesToQuery) {
        const adapter = this.serviceAdapters[serviceName];
        const serviceConfig = this.config[serviceName];

        if (!adapter || !serviceConfig) {
          logger.warn(`Service ${serviceName} ist nicht konfiguriert oder hat keinen Adapter`);
          continue;
        }

        if (serviceConfig.enabled) {
          logger.info(`Rufe Zitationsdaten von ${serviceConfig.name} für DOI ${doi} ab`);
          promises.push(adapter.getCitations(doi));
        }
      }
      
      const serviceResults = await Promise.allSettled(promises);
      
      // Verarbeite die Ergebnisse
      for (const result of serviceResults) {
        if (result.status === 'fulfilled' && result.value.success) {
          results.success = true;
          results.totalCitationCount = Math.max(results.totalCitationCount, result.value.citationCount);
          results.sources.push({
            name: result.value.source,
            citationCount: result.value.citationCount
          });
          
          // Füge die Zitationen hinzu, wenn vorhanden
          if (result.value.citations && result.value.citations.length > 0) {
            results.citations = [...results.citations, ...result.value.citations];
          }
        }
      }
      
      // Entferne Duplikate aus den Zitationen
      if (results.citations.length > 0) {
        results.citations = this.removeDuplicateCitations(results.citations);
      }
      
      if (results.success) {
        logger.info(`Externe Zitationsdaten für DOI ${doi} erfolgreich abgerufen. Quellen: ${results.sources.map(s => s.name).join(', ')}`);
      } else {
        logger.warn(`Keine externen Zitationsdaten für DOI ${doi} gefunden`);
      }
      
      return results;
    } catch (error) {
      logger.error('Fehler beim Abrufen externer Zitationsdaten', {
        doi,
        error: error.message
      });
      
      return {
        success: false,
        doi,
        error: error.message
      };
    }
  }
  
  /**
   * Holt Zitationsdaten von Semantic Scholar
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Zitationsdaten
   */
  async getSemanticScholarCitations(doi) {
    try {
      const encodedDoi = encodeURIComponent(doi);
      const url = `${this.config.semanticScholar.baseUrl}/paper/${encodedDoi}`;
      
      const response = await this.httpClient.get(url, {
        headers: this.config.semanticScholar.apiKey ? {
          'x-api-key': this.config.semanticScholar.apiKey
        } : {}
      });
      
      if (response.status !== 200 || !response.data) {
        return {
          success: false,
          source: this.config.semanticScholar.name,
          message: `Unerwarteter Statuscode: ${response.status}`
        };
      }
      
      const data = response.data;
      
      // Extrahiere die relevanten Daten
      const result = {
        success: true,
        source: this.config.semanticScholar.name,
        citationCount: data.citationCount || 0,
        citations: []
      };
      
      // Wenn detaillierte Zitationsdaten verfügbar sind, extrahiere sie
      if (data.citations && Array.isArray(data.citations)) {
        result.citations = data.citations.map(citation => ({
          doi: citation.doi,
          title: citation.title,
          authors: citation.authors ? citation.authors.map(author => author.name).join(', ') : '',
          year: citation.year,
          venue: citation.venue,
          url: citation.url,
          source: this.config.semanticScholar.name
        }));
      }
      
      return result;
    } catch (error) {
      logger.error('Fehler beim Abrufen von Zitationsdaten von Semantic Scholar', {
        doi,
        error: error.message
      });
      
      return {
        success: false,
        source: this.config.semanticScholar.name,
        message: error.message
      };
    }
  }
  
  /**
   * Holt Zitationsdaten von Scinapse
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Zitationsdaten
   */
  async getScinapseCitations(doi) {
    try {
      const encodedDoi = encodeURIComponent(doi);
      // Hinweis: Die genaue API-Struktur von Scinapse muss angepasst werden
      const url = `${this.config.scinapse.baseUrl}/papers/doi/${encodedDoi}`;
      
      const response = await this.httpClient.get(url, {
        headers: this.config.scinapse.apiKey ? {
          'x-api-key': this.config.scinapse.apiKey
        } : {}
      });
      
      if (response.status !== 200 || !response.data) {
        return {
          success: false,
          source: this.config.scinapse.name,
          message: `Unerwarteter Statuscode: ${response.status}`
        };
      }
      
      const data = response.data;
      
      // Extrahiere die relevanten Daten
      const result = {
        success: true,
        source: this.config.scinapse.name,
        citationCount: data.citationCount || 0,
        citations: []
      };
      
      // Wenn detaillierte Zitationsdaten verfügbar sind, extrahiere sie
      if (data.citations && Array.isArray(data.citations)) {
        result.citations = data.citations.map(citation => ({
          doi: citation.doi,
          title: citation.title,
          authors: citation.authors || '',
          year: citation.year,
          venue: citation.venue,
          url: citation.url,
          source: this.config.scinapse.name
        }));
      }
      
      return result;
    } catch (error) {
      logger.error('Fehler beim Abrufen von Zitationsdaten von Scinapse', {
        doi,
        error: error.message
      });
      
      return {
        success: false,
        source: this.config.scinapse.name,
        message: error.message
      };
    }
  }
  
  /**
   * Holt Zitationsdaten von arXiv
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Zitationsdaten
   */
  async getArxivCitations(doi) {
    try {
      // arXiv hat keine direkte API für Zitationen, aber wir können versuchen, 
      // den arXiv-Eintrag für die DOI zu finden und dann verwandte Artikel zu holen
      
      // Suche nach der DOI in arXiv
      const encodedDoi = encodeURIComponent(`doi:${doi}`);
      const url = `${this.config.arxiv.baseUrl}?search_query=${encodedDoi}&start=0&max_results=1`;
      
      const response = await axios.get(url);
      
      if (response.status !== 200 || !response.data) {
        return {
          success: false,
          source: this.config.arxiv.name,
          message: `Unerwarteter Statuscode: ${response.status}`
        };
      }
      
      // Parse die XML-Antwort
      // Hinweis: In einer realen Implementierung würde hier ein XML-Parser verwendet werden
      const xmlData = response.data;
      
      // Extrahiere die arXiv-ID aus der Antwort
      // Dies ist eine vereinfachte Implementierung
      const arxivIdMatch = xmlData.match(/<id>http:\/\/arxiv\.org\/abs\/(.*?)<\/id>/);
      
      if (!arxivIdMatch) {
        return {
          success: false,
          source: this.config.arxiv.name,
          message: 'Keine arXiv-ID gefunden'
        };
      }
      
      const arxivId = arxivIdMatch[1];
      
      // Hole verwandte Artikel
      const relatedUrl = `${this.config.arxiv.baseUrl}?id_list=${arxivId}&max_results=10`;
      const relatedResponse = await axios.get(relatedUrl);
      
      if (relatedResponse.status !== 200 || !relatedResponse.data) {
        return {
          success: false,
          source: this.config.arxiv.name,
          message: `Unerwarteter Statuscode bei verwandten Artikeln: ${relatedResponse.status}`
        };
      }
      
      // Parse die XML-Antwort für verwandte Artikel
      const relatedXmlData = relatedResponse.data;
      
      // Extrahiere die Anzahl der Einträge
      const totalResultsMatch = relatedXmlData.match(/<opensearch:totalResults>(.*?)<\/opensearch:totalResults>/);
      const totalResults = totalResultsMatch ? parseInt(totalResultsMatch[1], 10) : 0;
      
      // Extrahiere die Einträge
      const entries = [];
      const entryMatches = relatedXmlData.matchAll(/<entry>([\s\S]*?)<\/entry>/g);
      
      for (const match of entryMatches) {
        const entryXml = match[1];
        
        // Extrahiere Titel
        const titleMatch = entryXml.match(/<title>([\s\S]*?)<\/title>/);
        const title = titleMatch ? titleMatch[1].trim() : '';
        
        // Extrahiere Autoren
        const authorMatches = entryXml.matchAll(/<author>([\s\S]*?)<\/author>/g);
        const authors = [];
        
        for (const authorMatch of authorMatches) {
          const authorXml = authorMatch[1];
          const nameMatch = authorXml.match(/<name>([\s\S]*?)<\/name>/);
          if (nameMatch) {
            authors.push(nameMatch[1].trim());
          }
        }
        
        // Extrahiere DOI, wenn vorhanden
        const doiMatch = entryXml.match(/<arxiv:doi>([\s\S]*?)<\/arxiv:doi>/);
        const entryDoi = doiMatch ? doiMatch[1].trim() : null;
        
        // Extrahiere URL
        const urlMatch = entryXml.match(/<id>([\s\S]*?)<\/id>/);
        const url = urlMatch ? urlMatch[1].trim() : '';
        
        // Extrahiere Datum
        const dateMatch = entryXml.match(/<published>([\s\S]*?)<\/published>/);
        const date = dateMatch ? new Date(dateMatch[1].trim()) : null;
        const year = date ? date.getFullYear() : null;
        
        entries.push({
          doi: entryDoi,
          title,
          authors: authors.join(', '),
          year,
          venue: 'arXiv',
          url,
          source: this.config.arxiv.name
        });
      }
      
      return {
        success: true,
        source: this.config.arxiv.name,
        citationCount: totalResults,
        citations: entries
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen von Daten von arXiv', {
        doi,
        error: error.message
      });
      
      return {
        success: false,
        source: this.config.arxiv.name,
        message: error.message
      };
    }
  }
  
  /**
   * Holt Zitationsdaten von WorldCat
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Zitationsdaten
   */
  async getWorldCatCitations(doi) {
    try {
      // WorldCat hat keine direkte API für Zitationen, aber wir können nach der DOI suchen
      // und dann verwandte Werke oder Referenzen finden
      
      // Suche nach der DOI in WorldCat
      const encodedDoi = encodeURIComponent(doi);
      const url = `${this.config.worldcat.baseUrl}?q=doi:${encodedDoi}&wskey=${this.config.worldcat.apiKey}&format=json`;
      
      const response = await axios.get(url);
      
      if (response.status !== 200 || !response.data) {
        return {
          success: false,
          source: this.config.worldcat.name,
          message: `Unerwarteter Statuscode: ${response.status}`
        };
      }
      
      // Parse die JSON-Antwort
      const data = response.data;
      
      // Prüfe, ob Ergebnisse vorhanden sind
      if (!data.entry || data.entry.length === 0) {
        return {
          success: false,
          source: this.config.worldcat.name,
          message: 'Keine Einträge gefunden'
        };
      }
      
      // Extrahiere die OCLC-Nummer des ersten Eintrags
      const oclcNumber = data.entry[0].oclcnum;
      
      if (!oclcNumber) {
        return {
          success: false,
          source: this.config.worldcat.name,
          message: 'Keine OCLC-Nummer gefunden'
        };
      }
      
      // Hole verwandte Werke
      const relatedUrl = `${this.config.worldcat.baseUrl}/related?oclcnum=${oclcNumber}&wskey=${this.config.worldcat.apiKey}&format=json`;
      const relatedResponse = await axios.get(relatedUrl);
      
      if (relatedResponse.status !== 200 || !relatedResponse.data) {
        return {
          success: false,
          source: this.config.worldcat.name,
          message: `Unerwarteter Statuscode bei verwandten Werken: ${relatedResponse.status}`
        };
      }
      
      // Parse die JSON-Antwort für verwandte Werke
      const relatedData = relatedResponse.data;
      
      // Extrahiere die Anzahl der Einträge
      const totalResults = relatedData.numberOfRecords || 0;
      
      // Extrahiere die Einträge
      const entries = [];
      
      if (relatedData.entry && Array.isArray(relatedData.entry)) {
        for (const entry of relatedData.entry) {
          // Extrahiere Titel
          const title = entry.title || '';
          
          // Extrahiere Autoren
          const authors = entry.author ? 
            (Array.isArray(entry.author) ? entry.author.map(a => a.name).join(', ') : entry.author.name) : '';
          
          // Extrahiere DOI, wenn vorhanden
          const entryDoi = entry.doi || null;
          
          // Extrahiere URL
          const url = entry.link ? entry.link.href : '';
          
          // Extrahiere Jahr
          const year = entry.date ? parseInt(entry.date.substring(0, 4), 10) : null;
          
          entries.push({
            doi: entryDoi,
            title,
            authors,
            year,
            venue: entry.publisher || 'WorldCat',
            url,
            source: this.config.worldcat.name
          });
        }
      }
      
      return {
        success: true,
        source: this.config.worldcat.name,
        citationCount: totalResults,
        citations: entries
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen von Daten von WorldCat', {
        doi,
        error: error.message
      });
      
      return {
        success: false,
        source: this.config.worldcat.name,
        message: error.message
      };
    }
  }
  
  /**
   * Entfernt Duplikate aus einer Liste von Zitationen
   * 
   * @param {Array<Object>} citations - Liste von Zitationen
   * @returns {Array<Object>} Liste ohne Duplikate
   */
  removeDuplicateCitations(citations) {
    const uniqueCitations = [];
    const seenDois = new Set();
    
    for (const citation of citations) {
      if (citation.doi && !seenDois.has(citation.doi)) {
        seenDois.add(citation.doi);
        uniqueCitations.push(citation);
      } else if (!citation.doi && citation.title) {
        // Wenn keine DOI vorhanden ist, verwende den Titel als Identifikator
        const normalizedTitle = citation.title.toLowerCase().trim();
        if (!uniqueCitations.some(c => c.title && c.title.toLowerCase().trim() === normalizedTitle)) {
          uniqueCitations.push(citation);
        }
      }
    }
    
    return uniqueCitations;
  }
  
  /**
   * Registriert einen neuen externen Dienst
   *
   * @param {string} serviceName - Name des Dienstes
   * @param {Object} serviceConfig - Konfiguration des Dienstes
   * @param {Object} serviceAdapter - Adapter für den Dienst
   * @returns {boolean} - Erfolg der Registrierung
   */
  registerExternalService(serviceName, serviceConfig, serviceAdapter) {
    try {
      if (!serviceName || !serviceConfig || !serviceAdapter) {
        logger.error('Ungültige Parameter bei der Registrierung eines externen Dienstes');
        return false;
      }

      // Prüfe, ob die Konfiguration die erforderlichen Felder enthält
      const requiredConfigFields = ['baseUrl', 'name', 'enabled'];
      for (const field of requiredConfigFields) {
        if (serviceConfig[field] === undefined) {
          logger.error(`Konfiguration für ${serviceName} fehlt das erforderliche Feld: ${field}`);
          return false;
        }
      }

      // Füge den Dienst zur Konfiguration hinzu
      this.config[serviceName] = serviceConfig;

      // Registriere den Adapter
      const success = this.registerServiceAdapter(serviceName, serviceAdapter);

      if (success) {
        logger.info(`Externer Dienst ${serviceConfig.name} (${serviceName}) erfolgreich registriert`);
      }

      return success;
    } catch (error) {
      logger.error('Fehler bei der Registrierung eines externen Dienstes', {
        serviceName,
        error: error.message
      });
      return false;
    }
  }

  /**
   * Aktualisiert Zitationsdaten für eine DOI in der Datenbank
   *
   * @param {string} doi - DOI der Publikation
   * @param {Object} databaseService - Datenbankdienst
   * @returns {Promise<Object>} Ergebnis der Aktualisierung
   */
  async updateCitationDataForDOI(doi, databaseService) {
    try {
      if (!databaseService) {
        throw new Error('DatabaseService ist erforderlich');
      }
      
      logger.info(`Aktualisiere Zitationsdaten für DOI ${doi} in der Datenbank`);
      
      // Hole die Zitationsdaten von externen Diensten
      const citationData = await this.getCitationsForDOI(doi);
      
      if (!citationData.success) {
        return {
          success: false,
          doi,
          message: 'Keine externen Zitationsdaten gefunden'
        };
      }
      
      // Speichere die Zitationsdaten in der Datenbank
      const updatedCount = await this.storeCitationsInDatabase(doi, citationData, databaseService);
      
      return {
        success: true,
        doi,
        updatedCount,
        sources: citationData.sources.map(s => s.name)
      };
    } catch (error) {
      logger.error('Fehler bei der Aktualisierung von Zitationsdaten in der Datenbank', {
        doi,
        error: error.message
      });
      
      return {
        success: false,
        doi,
        error: error.message
      };
    }
  }
  
  /**
   * Registriert einen neuen externen Dienst
   *
   * @param {string} serviceName - Name des Dienstes
   * @param {Object} serviceConfig - Konfiguration des Dienstes
   * @param {Object} serviceAdapter - Adapter für den Dienst
   * @returns {boolean} - Erfolg der Registrierung
   */
  registerExternalService(serviceName, serviceConfig, serviceAdapter) {
    try {
      if (!serviceName || !serviceConfig || !serviceAdapter) {
        logger.error('Ungültige Parameter bei der Registrierung eines externen Dienstes');
        return false;
      }

      // Prüfe, ob die Konfiguration die erforderlichen Felder enthält
      const requiredConfigFields = ['baseUrl', 'name', 'enabled'];
      for (const field of requiredConfigFields) {
        if (serviceConfig[field] === undefined) {
          logger.error(`Konfiguration für ${serviceName} fehlt das erforderliche Feld: ${field}`);
          return false;
        }
      }

      // Füge den Dienst zur Konfiguration hinzu
      this.config[serviceName] = serviceConfig;

      // Registriere den Adapter
      const success = this.registerServiceAdapter(serviceName, serviceAdapter);

      if (success) {
        logger.info(`Externer Dienst ${serviceConfig.name} (${serviceName}) erfolgreich registriert`);
      }

      return success;
    } catch (error) {
      logger.error('Fehler bei der Registrierung eines externen Dienstes', {
        serviceName,
        error: error.message
      });
      return false;
    }
  }

  /**
   * Registriert einen neuen externen Dienst
   *
   * @param {string} serviceName - Name des Dienstes
   * @param {Object} serviceConfig - Konfiguration des Dienstes
   * @param {Object} serviceAdapter - Adapter für den Dienst
   * @returns {boolean} - Erfolg der Registrierung
   */
  registerExternalService(serviceName, serviceConfig, serviceAdapter) {
    try {
      if (!serviceName || !serviceConfig || !serviceAdapter) {
        logger.error('Ungültige Parameter bei der Registrierung eines externen Dienstes');
        return false;
      }

      // Prüfe, ob die Konfiguration die erforderlichen Felder enthält
      const requiredConfigFields = ['baseUrl', 'name', 'enabled'];
      for (const field of requiredConfigFields) {
        if (serviceConfig[field] === undefined) {
          logger.error(`Konfiguration für ${serviceName} fehlt das erforderliche Feld: ${field}`);
          return false;
        }
      }

      // Füge den Dienst zur Konfiguration hinzu
      this.config[serviceName] = serviceConfig;

      // Registriere den Adapter
      const success = this.registerServiceAdapter(serviceName, serviceAdapter);

      if (success) {
        logger.info(`Externer Dienst ${serviceConfig.name} (${serviceName}) erfolgreich registriert`);
      }

      return success;
    } catch (error) {
      logger.error('Fehler bei der Registrierung eines externen Dienstes', {
        serviceName,
        error: error.message
      });
      return false;
    }
  }

  /**
   * Speichert Zitationsdaten in der Datenbank
   *
   * @param {string} doi - DOI der Publikation
   * @param {Object} citationData - Zitationsdaten
   * @param {Object} databaseService - Datenbankdienst
   * @returns {Promise<number>} Anzahl der aktualisierten Zitationen
   */
  async storeCitationsInDatabase(doi, citationData, databaseService) {
    try {
      let updatedCount = 0;
      
      // Aktualisiere die Zitationszahl in der Publikationstabelle
      await databaseService.query(
        'UPDATE publications SET citation_count = ?, external_citation_sources = ?, updated_at = NOW() WHERE doi = ?',
        [
          citationData.totalCitationCount,
          JSON.stringify(citationData.sources.map(s => s.name)),
          doi
        ]
      );
      
      // Speichere die einzelnen Zitationen, wenn vorhanden
      if (citationData.citations && citationData.citations.length > 0) {
        for (const citation of citationData.citations) {
          // Prüfe, ob die zitierte Publikation bereits in der Datenbank existiert
          const existingPublication = await databaseService.query(
            'SELECT id FROM publications WHERE doi = ?',
            [citation.doi]
          );
          
          if (!existingPublication || existingPublication.length === 0) {
            // Füge die zitierte Publikation zur Datenbank hinzu
            await databaseService.query(
              'INSERT INTO publications (doi, title, authors, publication_date, journal, abstract, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())',
              [
                citation.doi,
                citation.title,
                citation.authors,
                citation.year ? `${citation.year}-01-01` : null, // Verwende das Jahr als Datum
                citation.venue,
                citation.abstract || ''
              ]
            );
          }
          
          // Speichere die Zitationsbeziehung
          const existingRelationship = await databaseService.query(
            'SELECT id FROM citation_relationships WHERE citing_doi = ? AND cited_doi = ?',
            [doi, citation.doi]
          );
          
          if (!existingRelationship || existingRelationship.length === 0) {
            await databaseService.query(
              'INSERT INTO citation_relationships (citing_doi, cited_doi, source, created_at) VALUES (?, ?, ?, NOW())',
              [doi, citation.doi, citation.source]
            );
            updatedCount++;
          }
        }
      }
      
      logger.info(`${updatedCount} Zitationsbeziehungen für DOI ${doi} in der Datenbank aktualisiert`);
      
      return updatedCount;
    } catch (error) {
      logger.error('Fehler beim Speichern von Zitationsdaten in der Datenbank', {
        doi,
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Holt Patentdaten von externen Diensten
   * 
   * @param {string} patentId - Patent-ID
   * @param {string} patentOffice - Patentamt
   * @returns {Promise<Object>} Patentdaten
   */
  async getPatentData(patentId, patentOffice) {
    try {
      if (!this.initialized) {
        await this.initialize();
      }
      
      logger.info(`Hole externe Patentdaten für Patent ${patentId} (${patentOffice})`);
      
      // Implementierung der Patentdatenabruf-Logik
      // Dies würde normalerweise eine API-Anfrage an einen Patentdatendienst beinhalten
      
      // Beispielimplementierung
      // In einer realen Implementierung würden hier Anfragen an Patentdatenbanken wie
      // Google Patents, USPTO, EPO, etc. gestellt werden
      
      // Simuliere eine erfolgreiche Antwort
      return {
        success: true,
        patentId,
        patentOffice,
        patent: {
          title: `Patent ${patentId}`,
          inventors: 'Erfinder nicht verfügbar',
          assignee: 'Inhaber nicht verfügbar',
          filingDate: new Date().toISOString().split('T')[0],
          issueDate: new Date().toISOString().split('T')[0],
          abstract: 'Zusammenfassung nicht verfügbar',
          claims: [],
          citations: []
        }
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen externer Patentdaten', {
        patentId,
        patentOffice,
        error: error.message
      });
      
      return {
        success: false,
        patentId,
        patentOffice,
        error: error.message
      };
    }
  }
  
  /**
   * Registriert einen neuen externen Dienst
   *
   * @param {string} serviceName - Name des Dienstes
   * @param {Object} serviceConfig - Konfiguration des Dienstes
   * @param {Object} serviceAdapter - Adapter für den Dienst
   * @returns {boolean} - Erfolg der Registrierung
   */
  registerExternalService(serviceName, serviceConfig, serviceAdapter) {
    try {
      if (!serviceName || !serviceConfig || !serviceAdapter) {
        logger.error('Ungültige Parameter bei der Registrierung eines externen Dienstes');
        return false;
      }

      // Prüfe, ob die Konfiguration die erforderlichen Felder enthält
      const requiredConfigFields = ['baseUrl', 'name', 'enabled'];
      for (const field of requiredConfigFields) {
        if (serviceConfig[field] === undefined) {
          logger.error(`Konfiguration für ${serviceName} fehlt das erforderliche Feld: ${field}`);
          return false;
        }
      }

      // Füge den Dienst zur Konfiguration hinzu
      this.config[serviceName] = serviceConfig;

      // Registriere den Adapter
      const success = this.registerServiceAdapter(serviceName, serviceAdapter);

      if (success) {
        logger.info(`Externer Dienst ${serviceConfig.name} (${serviceName}) erfolgreich registriert`);
      }

      return success;
    } catch (error) {
      logger.error('Fehler bei der Registrierung eines externen Dienstes', {
        serviceName,
        error: error.message
      });
      return false;
    }
  }

  /**
   * Aktualisiert Patentdaten in der Datenbank
   *
   * @param {string} patentId - Patent-ID
   * @param {string} patentOffice - Patentamt
   * @param {Object} databaseService - Datenbankdienst
   * @returns {Promise<Object>} Ergebnis der Aktualisierung
   */
  async updatePatentData(patentId, patentOffice, databaseService) {
    try {
      if (!databaseService) {
        throw new Error('DatabaseService ist erforderlich');
      }
      
      logger.info(`Aktualisiere Patentdaten für Patent ${patentId} (${patentOffice}) in der Datenbank`);
      
      // Hole die Patentdaten von externen Diensten
      const patentData = await this.getPatentData(patentId, patentOffice);
      
      if (!patentData.success) {
        return {
          success: false,
          patentId,
          patentOffice,
          message: 'Keine externen Patentdaten gefunden'
        };
      }
      
      // Prüfe, ob das Patent bereits in der Datenbank existiert
      const existingPatent = await databaseService.query(
        'SELECT id FROM patents WHERE patent_id = ?',
        [patentId]
      );
      
      if (existingPatent && existingPatent.length > 0) {
        // Aktualisiere das bestehende Patent
        await databaseService.query(
          'UPDATE patents SET title = ?, inventors = ?, assignee = ?, filing_date = ?, issue_date = ?, abstract = ?, updated_at = NOW() WHERE patent_id = ?',
          [
            patentData.patent.title,
            patentData.patent.inventors,
            patentData.patent.assignee,
            patentData.patent.filingDate,
            patentData.patent.issueDate,
            patentData.patent.abstract,
            patentId
          ]
        );
      } else {
        // Füge ein neues Patent hinzu
        await databaseService.query(
          'INSERT INTO patents (patent_id, patent_office, title, inventors, assignee, filing_date, issue_date, abstract, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())',
          [
            patentId,
            patentOffice,
            patentData.patent.title,
            patentData.patent.inventors,
            patentData.patent.assignee,
            patentData.patent.filingDate,
            patentData.patent.issueDate,
            patentData.patent.abstract
          ]
        );
      }
      
      // Speichere die Patentzitationen, wenn vorhanden
      let updatedCitationsCount = 0;
      
      if (patentData.patent.citations && patentData.patent.citations.length > 0) {
        for (const citation of patentData.patent.citations) {
          // Speichere die Patentzitation
          const existingCitation = await databaseService.query(
            'SELECT id FROM patent_citations WHERE citing_patent_id = ? AND cited_patent_id = ?',
            [patentId, citation.patentId]
          );
          
          if (!existingCitation || existingCitation.length === 0) {
            await databaseService.query(
              'INSERT INTO patent_citations (citing_patent_id, cited_patent_id, cited_patent_office, context, created_at) VALUES (?, ?, ?, ?, NOW())',
              [patentId, citation.patentId, citation.patentOffice, citation.context || '']
            );
            updatedCitationsCount++;
          }
        }
      }
      
      return {
        success: true,
        patentId,
        patentOffice,
        updatedCitationsCount
      };
    } catch (error) {
      logger.error('Fehler bei der Aktualisierung von Patentdaten in der Datenbank', {
        patentId,
        patentOffice,
        error: error.message
      });
      
      return {
        success: false,
        patentId,
        patentOffice,
        error: error.message
      };
    }
  }
}

export default ExternalCitationService;