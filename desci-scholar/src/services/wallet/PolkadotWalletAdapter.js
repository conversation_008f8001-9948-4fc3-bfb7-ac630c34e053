/**
 * @fileoverview Polkadot-Wallet-Adapter für die Interaktion mit Polkadot-Wallets
 * 
 * Dieser Adapter implementiert das WalletAdapter-Interface für Polkadot-Wallets
 * wie Polkadot.js Extension, Talisman, SubWallet, etc.
 */

import { WalletAdapter } from './WalletAdapter.js';
import { logger } from '../../utils/logger.js';

/**
 * Polkadot-Wallet-Adapter für die Interaktion mit Polkadot-Wallets
 */
export class PolkadotWalletAdapter extends WalletAdapter {
  /**
   * Erstellt eine neue Instanz des PolkadotWalletAdapter
   * @param {Object} options - Konfigurationsoptionen
   * @param {string} options.appName - Name der Anwendung
   * @param {string} options.appVersion - Version der Anwendung
   * @param {Array<string>} options.supportedWallets - Unterstützte Wallet-Typen
   * @param {Object} options.rpc - RPC-Konfiguration
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      appName: options.appName || 'DeSci-Scholar',
      appVersion: options.appVersion || '1.0.0',
      supportedWallets: options.supportedWallets || ['polkadot-js', 'talisman', 'subwallet'],
      rpc: options.rpc || {
        polkadot: process.env.POLKADOT_PROVIDER_URL || 'wss://rpc.polkadot.io',
        kusama: process.env.KUSAMA_PROVIDER_URL || 'wss://kusama-rpc.polkadot.io',
        westend: process.env.WESTEND_PROVIDER_URL || 'wss://westend-rpc.polkadot.io'
      },
      ...options
    };
    
    this.extension = null;
    this.api = null;
    this.accounts = [];
    this.selectedAccount = null;
    this.selectedNetwork = null;
    this.isInitialized = false;
    
    logger.info('PolkadotWalletAdapter initialisiert');
  }
  
  /**
   * Initialisiert den Wallet-Adapter
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      logger.info('Initialisiere PolkadotWalletAdapter');
      
      // Importiere die benötigten Module
      const { web3Enable, web3Accounts } = await import('@polkadot/extension-dapp');
      const { ApiPromise, WsProvider } = await import('@polkadot/api');
      
      // Aktiviere die Web3-Erweiterungen
      const extensions = await web3Enable(this.options.appName);
      
      if (extensions.length === 0) {
        logger.warn('Keine Polkadot-Erweiterung gefunden oder Zugriff nicht erlaubt');
      } else {
        this.extension = extensions[0];
        logger.debug(`Polkadot-Erweiterung gefunden: ${this.extension.name}`);
        
        // Hole die verfügbaren Konten
        this.accounts = await web3Accounts();
        logger.debug(`${this.accounts.length} Konten gefunden`);
        
        // Wähle das erste Konto aus, falls verfügbar
        if (this.accounts.length > 0) {
          this.selectedAccount = this.accounts[0];
          logger.debug(`Konto ausgewählt: ${this.selectedAccount.address}`);
        }
      }
      
      // Verbinde mit dem Standard-Netzwerk
      await this.connectToNetwork('polkadot');
      
      this.isInitialized = true;
      
      logger.info('PolkadotWalletAdapter erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des PolkadotWalletAdapter', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Verbindet mit einem Polkadot-Netzwerk
   * @param {string} network - Netzwerkname
   * @returns {Promise<void>}
   */
  async connectToNetwork(network) {
    try {
      logger.info(`Verbinde mit Polkadot-Netzwerk: ${network}`);
      
      // Prüfe, ob das Netzwerk unterstützt wird
      if (!this.options.rpc[network]) {
        throw new Error(`Netzwerk ${network} wird nicht unterstützt`);
      }
      
      // Importiere die benötigten Module
      const { ApiPromise, WsProvider } = await import('@polkadot/api');
      
      // Trenne die Verbindung zum aktuellen Netzwerk, falls vorhanden
      if (this.api) {
        await this.api.disconnect();
      }
      
      // Erstelle den Provider
      const provider = new WsProvider(this.options.rpc[network]);
      
      // Erstelle die API
      this.api = await ApiPromise.create({ provider });
      
      // Prüfe die Verbindung
      const chain = await this.api.rpc.system.chain();
      
      logger.debug(`Verbunden mit ${chain}`);
      
      // Setze das ausgewählte Netzwerk
      this.selectedNetwork = network;
      
      logger.info(`Erfolgreich mit Polkadot-Netzwerk ${network} verbunden`);
    } catch (error) {
      logger.error('Fehler beim Verbinden mit dem Polkadot-Netzwerk', {
        error: error.message,
        network
      });
      throw error;
    }
  }
  
  /**
   * Verbindet mit dem Wallet
   * @param {Object} options - Verbindungsoptionen
   * @returns {Promise<Object>} Verbindungsergebnis
   */
  async connect(options = {}) {
    try {
      logger.info('Verbinde mit Polkadot-Wallet');
      
      // Initialisiere den Adapter, falls noch nicht geschehen
      if (!this.isInitialized) {
        await this.initialize();
      }
      
      // Prüfe, ob eine Erweiterung gefunden wurde
      if (!this.extension) {
        throw new Error('Keine Polkadot-Erweiterung gefunden oder Zugriff nicht erlaubt');
      }
      
      // Prüfe, ob Konten verfügbar sind
      if (this.accounts.length === 0) {
        throw new Error('Keine Konten in der Polkadot-Erweiterung gefunden');
      }
      
      // Wähle ein Konto aus, falls angegeben
      if (options.accountIndex !== undefined) {
        if (options.accountIndex >= 0 && options.accountIndex < this.accounts.length) {
          this.selectedAccount = this.accounts[options.accountIndex];
          logger.debug(`Konto ausgewählt: ${this.selectedAccount.address}`);
        } else {
          throw new Error(`Ungültiger Kontoindex: ${options.accountIndex}`);
        }
      } else if (options.address) {
        const account = this.accounts.find(acc => acc.address === options.address);
        if (account) {
          this.selectedAccount = account;
          logger.debug(`Konto ausgewählt: ${this.selectedAccount.address}`);
        } else {
          throw new Error(`Konto mit Adresse ${options.address} nicht gefunden`);
        }
      }
      
      // Verbinde mit dem angegebenen Netzwerk, falls angegeben
      if (options.network && options.network !== this.selectedNetwork) {
        await this.connectToNetwork(options.network);
      }
      
      logger.info('Erfolgreich mit Polkadot-Wallet verbunden');
      
      return {
        success: true,
        address: this.selectedAccount.address,
        network: this.selectedNetwork,
        accounts: this.accounts.map(acc => ({
          address: acc.address,
          name: acc.meta.name,
          source: acc.meta.source
        }))
      };
    } catch (error) {
      logger.error('Fehler beim Verbinden mit dem Polkadot-Wallet', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Trennt die Verbindung zum Wallet
   * @returns {Promise<void>}
   */
  async disconnect() {
    try {
      logger.info('Trenne Verbindung zum Polkadot-Wallet');
      
      // Trenne die Verbindung zur API, falls vorhanden
      if (this.api) {
        await this.api.disconnect();
        this.api = null;
      }
      
      // Setze die Variablen zurück
      this.selectedAccount = null;
      this.selectedNetwork = null;
      
      logger.info('Verbindung zum Polkadot-Wallet getrennt');
    } catch (error) {
      logger.error('Fehler beim Trennen der Verbindung zum Polkadot-Wallet', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Verbindung zum Wallet besteht
   * @returns {Promise<boolean>} True, wenn eine Verbindung besteht
   */
  async isConnected() {
    try {
      // Prüfe, ob ein Konto ausgewählt ist
      if (!this.selectedAccount) {
        return false;
      }
      
      // Prüfe, ob die API verbunden ist
      if (!this.api || !this.api.isConnected) {
        return false;
      }
      
      return true;
    } catch (error) {
      logger.debug('Keine Verbindung zum Polkadot-Wallet');
      return false;
    }
  }
  
  /**
   * Ruft die Adresse des Wallets ab
   * @returns {Promise<string>} Wallet-Adresse
   */
  async getAddress() {
    try {
      logger.debug('Rufe Wallet-Adresse ab');
      
      // Prüfe, ob ein Konto ausgewählt ist
      if (!this.selectedAccount) {
        throw new Error('Kein Konto ausgewählt');
      }
      
      return this.selectedAccount.address;
    } catch (error) {
      logger.error('Fehler beim Abrufen der Wallet-Adresse', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft den Kontostand des Wallets ab
   * @param {Object} options - Abfrageoptionen
   * @returns {Promise<Object>} Kontostanddetails
   */
  async getBalance(options = {}) {
    try {
      logger.debug('Rufe Kontostand des Wallets ab');
      
      // Prüfe, ob ein Konto ausgewählt ist
      if (!this.selectedAccount) {
        throw new Error('Kein Konto ausgewählt');
      }
      
      // Prüfe, ob die API verbunden ist
      if (!this.api || !this.api.isConnected) {
        throw new Error('Keine Verbindung zur API');
      }
      
      // Hole den Kontostand
      const { data: balance } = await this.api.query.system.account(this.selectedAccount.address);
      
      // Konvertiere den Kontostand in eine lesbare Form
      const free = this.api.createType('Balance', balance.free).toHuman();
      const reserved = this.api.createType('Balance', balance.reserved).toHuman();
      const miscFrozen = this.api.createType('Balance', balance.miscFrozen).toHuman();
      const feeFrozen = this.api.createType('Balance', balance.feeFrozen).toHuman();
      
      return {
        address: this.selectedAccount.address,
        network: this.selectedNetwork,
        free,
        reserved,
        miscFrozen,
        feeFrozen,
        total: this.api.createType('Balance', balance.free.add(balance.reserved)).toHuman()
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen des Kontostands', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Signiert eine Nachricht
   * @param {string} message - Zu signierende Nachricht
   * @param {Object} options - Signierungsoptionen
   * @returns {Promise<string>} Signatur
   */
  async signMessage(message, options = {}) {
    try {
      logger.info('Signiere Nachricht');
      
      // Prüfe, ob ein Konto ausgewählt ist
      if (!this.selectedAccount) {
        throw new Error('Kein Konto ausgewählt');
      }
      
      // Importiere die benötigten Module
      const { web3FromSource } = await import('@polkadot/extension-dapp');
      const { stringToU8a, u8aToHex } = await import('@polkadot/util');
      
      // Hole den Signer
      const injector = await web3FromSource(this.selectedAccount.meta.source);
      
      // Signiere die Nachricht
      const signRaw = injector?.signer?.signRaw;
      
      if (!signRaw) {
        throw new Error('Signieren wird von dieser Erweiterung nicht unterstützt');
      }
      
      // Konvertiere die Nachricht in ein Uint8Array
      const messageU8a = options.isHex
        ? message
        : u8aToHex(stringToU8a(message));
      
      // Signiere die Nachricht
      const { signature } = await signRaw({
        address: this.selectedAccount.address,
        data: messageU8a,
        type: options.type || 'bytes'
      });
      
      logger.info('Nachricht erfolgreich signiert');
      
      return signature;
    } catch (error) {
      logger.error('Fehler beim Signieren der Nachricht', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Signiert eine Transaktion
   * @param {Object} transaction - Zu signierende Transaktion
   * @param {Object} options - Signierungsoptionen
   * @returns {Promise<Object>} Signierte Transaktion
   */
  async signTransaction(transaction, options = {}) {
    try {
      logger.info('Signiere Transaktion');
      
      // Prüfe, ob ein Konto ausgewählt ist
      if (!this.selectedAccount) {
        throw new Error('Kein Konto ausgewählt');
      }
      
      // Prüfe, ob die API verbunden ist
      if (!this.api || !this.api.isConnected) {
        throw new Error('Keine Verbindung zur API');
      }
      
      // Importiere die benötigten Module
      const { web3FromSource } = await import('@polkadot/extension-dapp');
      
      // Hole den Signer
      const injector = await web3FromSource(this.selectedAccount.meta.source);
      
      // Erstelle die Transaktion
      const { section, method, args = [] } = transaction;
      
      // Prüfe, ob die Methode existiert
      if (!this.api.tx[section] || !this.api.tx[section][method]) {
        throw new Error(`Methode ${section}.${method} nicht gefunden`);
      }
      
      // Erstelle die Transaktion
      const tx = this.api.tx[section][method](...args);
      
      // Signiere die Transaktion
      const signedTx = await tx.signAsync(this.selectedAccount.address, {
        signer: injector.signer,
        nonce: options.nonce
      });
      
      logger.info('Transaktion erfolgreich signiert');
      
      return {
        transaction: tx,
        signedTransaction: signedTx,
        hash: signedTx.hash.toHex()
      };
    } catch (error) {
      logger.error('Fehler beim Signieren der Transaktion', {
        error: error.message,
        transaction
      });
      throw error;
    }
  }
  
  /**
   * Sendet eine Transaktion
   * @param {Object} transaction - Zu sendende Transaktion
   * @param {Object} options - Sendeoptionen
   * @returns {Promise<Object>} Transaktionsergebnis
   */
  async sendTransaction(transaction, options = {}) {
    try {
      logger.info('Sende Transaktion');
      
      // Signiere die Transaktion, falls noch nicht geschehen
      let signedTx;
      
      if (transaction.signedTransaction) {
        signedTx = transaction.signedTransaction;
      } else {
        const result = await this.signTransaction(transaction, options);
        signedTx = result.signedTransaction;
      }
      
      // Sende die Transaktion
      const hash = await new Promise((resolve, reject) => {
        signedTx.send(({ status, dispatchError, events = [] }) => {
          if (status.isInBlock || status.isFinalized) {
            // Prüfe auf Fehler
            if (dispatchError) {
              if (dispatchError.isModule) {
                const decoded = this.api.registry.findMetaError(dispatchError.asModule);
                const { section, method, docs } = decoded;
                
                reject(new Error(`${section}.${method}: ${docs.join(' ')}`));
              } else {
                reject(new Error(dispatchError.toString()));
              }
            } else {
              // Suche nach relevanten Events
              events.forEach(({ event: { data, method, section } }) => {
                if (section === 'system' && method === 'ExtrinsicSuccess') {
                  resolve(signedTx.hash.toHex());
                }
              });
            }
          }
        });
      });
      
      logger.info(`Transaktion erfolgreich gesendet: ${hash}`);
      
      return {
        success: true,
        hash,
        blockHash: null, // Wird erst bekannt, wenn die Transaktion in einem Block ist
        blockNumber: null
      };
    } catch (error) {
      logger.error('Fehler beim Senden der Transaktion', {
        error: error.message,
        transaction
      });
      throw error;
    }
  }
  
  /**
   * Ruft die unterstützten Netzwerke ab
   * @returns {Promise<Array<Object>>} Unterstützte Netzwerke
   */
  async getSupportedNetworks() {
    try {
      logger.debug('Rufe unterstützte Netzwerke ab');
      
      // Erstelle eine Liste der unterstützten Netzwerke
      const networks = [];
      
      for (const [name, url] of Object.entries(this.options.rpc)) {
        networks.push({
          id: name,
          name: name.charAt(0).toUpperCase() + name.slice(1),
          url
        });
      }
      
      return networks;
    } catch (error) {
      logger.error('Fehler beim Abrufen der unterstützten Netzwerke', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Wechselt das Netzwerk
   * @param {string} networkId - Netzwerk-ID
   * @returns {Promise<Object>} Netzwerkwechselergebnis
   */
  async switchNetwork(networkId) {
    try {
      logger.info(`Wechsle zu Netzwerk: ${networkId}`);
      
      // Prüfe, ob das Netzwerk unterstützt wird
      if (!this.options.rpc[networkId]) {
        throw new Error(`Netzwerk ${networkId} wird nicht unterstützt`);
      }
      
      // Verbinde mit dem Netzwerk
      await this.connectToNetwork(networkId);
      
      logger.info(`Erfolgreich zu Netzwerk ${networkId} gewechselt`);
      
      return {
        success: true,
        network: networkId
      };
    } catch (error) {
      logger.error('Fehler beim Wechseln des Netzwerks', {
        error: error.message,
        networkId
      });
      throw error;
    }
  }
  
  /**
   * Ruft das aktuelle Netzwerk ab
   * @returns {Promise<Object>} Aktuelles Netzwerk
   */
  async getNetwork() {
    try {
      logger.debug('Rufe aktuelles Netzwerk ab');
      
      // Prüfe, ob die API verbunden ist
      if (!this.api || !this.api.isConnected) {
        throw new Error('Keine Verbindung zur API');
      }
      
      // Hole den Chain-Namen
      const chain = await this.api.rpc.system.chain();
      
      return {
        id: this.selectedNetwork,
        name: chain.toString(),
        url: this.options.rpc[this.selectedNetwork]
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen des aktuellen Netzwerks', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft die unterstützten Funktionen des Wallets ab
   * @returns {Promise<Object>} Unterstützte Funktionen
   */
  async getCapabilities() {
    try {
      logger.debug('Rufe unterstützte Funktionen des Wallets ab');
      
      // Prüfe, ob eine Erweiterung gefunden wurde
      if (!this.extension) {
        throw new Error('Keine Polkadot-Erweiterung gefunden oder Zugriff nicht erlaubt');
      }
      
      // Importiere die benötigten Module
      const { web3FromSource } = await import('@polkadot/extension-dapp');
      
      // Hole den Injector
      const injector = await web3FromSource(this.selectedAccount.meta.source);
      
      return {
        signMessage: !!injector?.signer?.signRaw,
        signTransaction: !!injector?.signer?.signPayload,
        accounts: true,
        switchNetwork: true,
        name: this.extension.name,
        version: this.extension.version
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen der unterstützten Funktionen', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Exportiert das Wallet (nicht unterstützt)
   * @param {Object} options - Exportoptionen
   * @returns {Promise<Object>} Exportergebnis
   */
  async exportWallet(options = {}) {
    logger.warn('Exportieren des Wallets wird nicht unterstützt');
    throw new Error('Exportieren des Wallets wird nicht unterstützt');
  }
  
  /**
   * Importiert ein Wallet (nicht unterstützt)
   * @param {Object} data - Zu importierende Daten
   * @param {Object} options - Importoptionen
   * @returns {Promise<Object>} Importergebnis
   */
  async importWallet(data, options = {}) {
    logger.warn('Importieren des Wallets wird nicht unterstützt');
    throw new Error('Importieren des Wallets wird nicht unterstützt');
  }
}/**
 * @fileoverview Polkadot-Wallet-Adapter für die Interaktion mit Polkadot-Wallets
 * 
 * Dieser Adapter implementiert das WalletAdapter-Interface für Polkadot-Wallets
 * wie Polkadot.js Extension, Talisman, SubWallet, etc.
 */

import { WalletAdapter } from './WalletAdapter.js';
import { logger } from '../../utils/logger.js';

/**
 * Polkadot-Wallet-Adapter für die Interaktion mit Polkadot-Wallets
 */
export class PolkadotWalletAdapter extends WalletAdapter {
  /**
   * Erstellt eine neue Instanz des PolkadotWalletAdapter
   * @param {Object} options - Konfigurationsoptionen
   * @param {string} options.appName - Name der Anwendung
   * @param {string} options.appVersion - Version der Anwendung
   * @param {Array<string>} options.supportedWallets - Unterstützte Wallet-Typen
   * @param {Object} options.rpc - RPC-Konfiguration
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      appName: options.appName || 'DeSci-Scholar',
      appVersion: options.appVersion || '1.0.0',
      supportedWallets: options.supportedWallets || ['polkadot-js', 'talisman', 'subwallet'],
      rpc: options.rpc || {
        polkadot: process.env.POLKADOT_PROVIDER_URL || 'wss://rpc.polkadot.io',
        kusama: process.env.KUSAMA_PROVIDER_URL || 'wss://kusama-rpc.polkadot.io',
        westend: process.env.WESTEND_PROVIDER_URL || 'wss://westend-rpc.polkadot.io'
      },
      ...options
    };
    
    this.extension = null;
    this.api = null;
    this.accounts = [];
    this.selectedAccount = null;
    this.selectedNetwork = null;
    this.isInitialized = false;
    
    logger.info('PolkadotWalletAdapter initialisiert');
  }
  
  /**
   * Initialisiert den Wallet-Adapter
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      logger.info('Initialisiere PolkadotWalletAdapter');
      
      // Importiere die benötigten Module
      const { web3Enable, web3Accounts } = await import('@polkadot/extension-dapp');
      const { ApiPromise, WsProvider } = await import('@polkadot/api');
      
      // Aktiviere die Web3-Erweiterungen
      const extensions = await web3Enable(this.options.appName);
      
      if (extensions.length === 0) {
        logger.warn('Keine Polkadot-Erweiterung gefunden oder Zugriff nicht erlaubt');
      } else {
        this.extension = extensions[0];
        logger.debug(`Polkadot-Erweiterung gefunden: ${this.extension.name}`);
        
        // Hole die verfügbaren Konten
        this.accounts = await web3Accounts();
        logger.debug(`${this.accounts.length} Konten gefunden`);
        
        // Wähle das erste Konto aus, falls verfügbar
        if (this.accounts.length > 0) {
          this.selectedAccount = this.accounts[0];
          logger.debug(`Konto ausgewählt: ${this.selectedAccount.address}`);
        }
      }
      
      // Verbinde mit dem Standard-Netzwerk
      await this.connectToNetwork('polkadot');
      
      this.isInitialized = true;
      
      logger.info('PolkadotWalletAdapter erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des PolkadotWalletAdapter', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Verbindet mit einem Polkadot-Netzwerk
   * @param {string} network - Netzwerkname
   * @returns {Promise<void>}
   */
  async connectToNetwork(network) {
    try {
      logger.info(`Verbinde mit Polkadot-Netzwerk: ${network}`);
      
      // Prüfe, ob das Netzwerk unterstützt wird
      if (!this.options.rpc[network]) {
        throw new Error(`Netzwerk ${network} wird nicht unterstützt`);
      }
      
      // Importiere die benötigten Module
      const { ApiPromise, WsProvider } = await import('@polkadot/api');
      
      // Trenne die Verbindung zum aktuellen Netzwerk, falls vorhanden
      if (this.api) {
        await this.api.disconnect();
      }
      
      // Erstelle den Provider
      const provider = new WsProvider(this.options.rpc[network]);
      
      // Erstelle die API
      this.api = await ApiPromise.create({ provider });
      
      // Prüfe die Verbindung
      const chain = await this.api.rpc.system.chain();
      
      logger.debug(`Verbunden mit ${chain}`);
      
      // Setze das ausgewählte Netzwerk
      this.selectedNetwork = network;
      
      logger.info(`Erfolgreich mit Polkadot-Netzwerk ${network} verbunden`);
    } catch (error) {
      logger.error('Fehler beim Verbinden mit dem Polkadot-Netzwerk', {
        error: error.message,
        network
      });
      throw error;
    }
  }
  
  /**
   * Verbindet mit dem Wallet
   * @param {Object} options - Verbindungsoptionen
   * @returns {Promise<Object>} Verbindungsergebnis
   */
  async connect(options = {}) {
    try {
      logger.info('Verbinde mit Polkadot-Wallet');
      
      // Initialisiere den Adapter, falls noch nicht geschehen
      if (!this.isInitialized) {
        await this.initialize();
      }
      
      // Prüfe, ob eine Erweiterung gefunden wurde
      if (!this.extension) {
        throw new Error('Keine Polkadot-Erweiterung gefunden oder Zugriff nicht erlaubt');
      }
      
      // Prüfe, ob Konten verfügbar sind
      if (this.accounts.length === 0) {
        throw new Error('Keine Konten in der Polkadot-Erweiterung gefunden');
      }
      
      // Wähle ein Konto aus, falls angegeben
      if (options.accountIndex !== undefined) {
        if (options.accountIndex >= 0 && options.accountIndex < this.accounts.length) {
          this.selectedAccount = this.accounts[options.accountIndex];
          logger.debug(`Konto ausgewählt: ${this.selectedAccount.address}`);
        } else {
          throw new Error(`Ungültiger Kontoindex: ${options.accountIndex}`);
        }
      } else if (options.address) {
        const account = this.accounts.find(acc => acc.address === options.address);
        if (account) {
          this.selectedAccount = account;
          logger.debug(`Konto ausgewählt: ${this.selectedAccount.address}`);
        } else {
          throw new Error(`Konto mit Adresse ${options.address} nicht gefunden`);
        }
      }
      
      // Verbinde mit dem angegebenen Netzwerk, falls angegeben
      if (options.network && options.network !== this.selectedNetwork) {
        await this.connectToNetwork(options.network);
      }
      
      logger.info('Erfolgreich mit Polkadot-Wallet verbunden');
      
      return {
        success: true,
        address: this.selectedAccount.address,
        network: this.selectedNetwork,
        accounts: this.accounts.map(acc => ({
          address: acc.address,
          name: acc.meta.name,
          source: acc.meta.source
        }))
      };
    } catch (error) {
      logger.error('Fehler beim Verbinden mit dem Polkadot-Wallet', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Trennt die Verbindung zum Wallet
   * @returns {Promise<void>}
   */
  async disconnect() {
    try {
      logger.info('Trenne Verbindung zum Polkadot-Wallet');
      
      // Trenne die Verbindung zur API, falls vorhanden
      if (this.api) {
        await this.api.disconnect();
        this.api = null;
      }
      
      // Setze die Variablen zurück
      this.selectedAccount = null;
      this.selectedNetwork = null;
      
      logger.info('Verbindung zum Polkadot-Wallet getrennt');
    } catch (error) {
      logger.error('Fehler beim Trennen der Verbindung zum Polkadot-Wallet', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Verbindung zum Wallet besteht
   * @returns {Promise<boolean>} True, wenn eine Verbindung besteht
   */
  async isConnected() {
    try {
      // Prüfe, ob ein Konto ausgewählt ist
      if (!this.selectedAccount) {
        return false;
      }
      
      // Prüfe, ob die API verbunden ist
      if (!this.api || !this.api.isConnected) {
        return false;
      }
      
      return true;
    } catch (error) {
      logger.debug('Keine Verbindung zum Polkadot-Wallet');
      return false;
    }
  }
  
  /**
   * Ruft die Adresse des Wallets ab
   * @returns {Promise<string>} Wallet-Adresse
   */
  async getAddress() {
    try {
      logger.debug('Rufe Wallet-Adresse ab');
      
      // Prüfe, ob ein Konto ausgewählt ist
      if (!this.selectedAccount) {
        throw new Error('Kein Konto ausgewählt');
      }
      
      return this.selectedAccount.address;
    } catch (error) {
      logger.error('Fehler beim Abrufen der Wallet-Adresse', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft den Kontostand des Wallets ab
   * @param {Object} options - Abfrageoptionen
   * @returns {Promise<Object>} Kontostanddetails
   */
  async getBalance(options = {}) {
    try {
      logger.debug('Rufe Kontostand des Wallets ab');
      
      // Prüfe, ob ein Konto ausgewählt ist
      if (!this.selectedAccount) {
        throw new Error('Kein Konto ausgewählt');
      }
      
      // Prüfe, ob die API verbunden ist
      if (!this.api || !this.api.isConnected) {
        throw new Error('Keine Verbindung zur API');
      }
      
      // Hole den Kontostand
      const { data: balance } = await this.api.query.system.account(this.selectedAccount.address);
      
      // Konvertiere den Kontostand in eine lesbare Form
      const free = this.api.createType('Balance', balance.free).toHuman();
      const reserved = this.api.createType('Balance', balance.reserved).toHuman();
      const miscFrozen = this.api.createType('Balance', balance.miscFrozen).toHuman();
      const feeFrozen = this.api.createType('Balance', balance.feeFrozen).toHuman();
      
      return {
        address: this.selectedAccount.address,
        network: this.selectedNetwork,
        free,
        reserved,
        miscFrozen,
        feeFrozen,
        total: this.api.createType('Balance', balance.free.add(balance.reserved)).toHuman()
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen des Kontostands', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Signiert eine Nachricht
   * @param {string} message - Zu signierende Nachricht
   * @param {Object} options - Signierungsoptionen
   * @returns {Promise<string>} Signatur
   */
  async signMessage(message, options = {}) {
    try {
      logger.info('Signiere Nachricht');
      
      // Prüfe, ob ein Konto ausgewählt ist
      if (!this.selectedAccount) {
        throw new Error('Kein Konto ausgewählt');
      }
      
      // Importiere die benötigten Module
      const { web3FromSource } = await import('@polkadot/extension-dapp');
      const { stringToU8a, u8aToHex } = await import('@polkadot/util');
      
      // Hole den Signer
      const injector = await web3FromSource(this.selectedAccount.meta.source);
      
      // Signiere die Nachricht
      const signRaw = injector?.signer?.signRaw;
      
      if (!signRaw) {
        throw new Error('Signieren wird von dieser Erweiterung nicht unterstützt');
      }
      
      // Konvertiere die Nachricht in ein Uint8Array
      const messageU8a = options.isHex
        ? message
        : u8aToHex(stringToU8a(message));
      
      // Signiere die Nachricht
      const { signature } = await signRaw({
        address: this.selectedAccount.address,
        data: messageU8a,
        type: options.type || 'bytes'
      });
      
      logger.info('Nachricht erfolgreich signiert');
      
      return signature;
    } catch (error) {
      logger.error('Fehler beim Signieren der Nachricht', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Signiert eine Transaktion
   * @param {Object} transaction - Zu signierende Transaktion
   * @param {Object} options - Signierungsoptionen
   * @returns {Promise<Object>} Signierte Transaktion
   */
  async signTransaction(transaction, options = {}) {
    try {
      logger.info('Signiere Transaktion');
      
      // Prüfe, ob ein Konto ausgewählt ist
      if (!this.selectedAccount) {
        throw new Error('Kein Konto ausgewählt');
      }
      
      // Prüfe, ob die API verbunden ist
      if (!this.api || !this.api.isConnected) {
        throw new Error('Keine Verbindung zur API');
      }
      
      // Importiere die benötigten Module
      const { web3FromSource } = await import('@polkadot/extension-dapp');
      
      // Hole den Signer
      const injector = await web3FromSource(this.selectedAccount.meta.source);
      
      // Erstelle die Transaktion
      const { section, method, args = [] } = transaction;
      
      // Prüfe, ob die Methode existiert
      if (!this.api.tx[section] || !this.api.tx[section][method]) {
        throw new Error(`Methode ${section}.${method} nicht gefunden`);
      }
      
      // Erstelle die Transaktion
      const tx = this.api.tx[section][method](...args);
      
      // Signiere die Transaktion
      const signedTx = await tx.signAsync(this.selectedAccount.address, {
        signer: injector.signer,
        nonce: options.nonce
      });
      
      logger.info('Transaktion erfolgreich signiert');
      
      return {
        transaction: tx,
        signedTransaction: signedTx,
        hash: signedTx.hash.toHex()
      };
    } catch (error) {
      logger.error('Fehler beim Signieren der Transaktion', {
        error: error.message,
        transaction
      });
      throw error;
    }
  }
  
  /**
   * Sendet eine Transaktion
   * @param {Object} transaction - Zu sendende Transaktion
   * @param {Object} options - Sendeoptionen
   * @returns {Promise<Object>} Transaktionsergebnis
   */
  async sendTransaction(transaction, options = {}) {
    try {
      logger.info('Sende Transaktion');
      
      // Signiere die Transaktion, falls noch nicht geschehen
      let signedTx;
      
      if (transaction.signedTransaction) {
        signedTx = transaction.signedTransaction;
      } else {
        const result = await this.signTransaction(transaction, options);
        signedTx = result.signedTransaction;
      }
      
      // Sende die Transaktion
      const hash = await new Promise((resolve, reject) => {
        signedTx.send(({ status, dispatchError, events = [] }) => {
          if (status.isInBlock || status.isFinalized) {
            // Prüfe auf Fehler
            if (dispatchError) {
              if (dispatchError.isModule) {
                const decoded = this.api.registry.findMetaError(dispatchError.asModule);
                const { section, method, docs } = decoded;
                
                reject(new Error(`${section}.${method}: ${docs.join(' ')}`));
              } else {
                reject(new Error(dispatchError.toString()));
              }
            } else {
              // Suche nach relevanten Events
              events.forEach(({ event: { data, method, section } }) => {
                if (section === 'system' && method === 'ExtrinsicSuccess') {
                  resolve(signedTx.hash.toHex());
                }
              });
            }
          }
        });
      });
      
      logger.info(`Transaktion erfolgreich gesendet: ${hash}`);
      
      return {
        success: true,
        hash,
        blockHash: null, // Wird erst bekannt, wenn die Transaktion in einem Block ist
        blockNumber: null
      };
    } catch (error) {
      logger.error('Fehler beim Senden der Transaktion', {
        error: error.message,
        transaction
      });
      throw error;
    }
  }
  
  /**
   * Ruft die unterstützten Netzwerke ab
   * @returns {Promise<Array<Object>>} Unterstützte Netzwerke
   */
  async getSupportedNetworks() {
    try {
      logger.debug('Rufe unterstützte Netzwerke ab');
      
      // Erstelle eine Liste der unterstützten Netzwerke
      const networks = [];
      
      for (const [name, url] of Object.entries(this.options.rpc)) {
        networks.push({
          id: name,
          name: name.charAt(0).toUpperCase() + name.slice(1),
          url
        });
      }
      
      return networks;
    } catch (error) {
      logger.error('Fehler beim Abrufen der unterstützten Netzwerke', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Wechselt das Netzwerk
   * @param {string} networkId - Netzwerk-ID
   * @returns {Promise<Object>} Netzwerkwechselergebnis
   */
  async switchNetwork(networkId) {
    try {
      logger.info(`Wechsle zu Netzwerk: ${networkId}`);
      
      // Prüfe, ob das Netzwerk unterstützt wird
      if (!this.options.rpc[networkId]) {
        throw new Error(`Netzwerk ${networkId} wird nicht unterstützt`);
      }
      
      // Verbinde mit dem Netzwerk
      await this.connectToNetwork(networkId);
      
      logger.info(`Erfolgreich zu Netzwerk ${networkId} gewechselt`);
      
      return {
        success: true,
        network: networkId
      };
    } catch (error) {
      logger.error('Fehler beim Wechseln des Netzwerks', {
        error: error.message,
        networkId
      });
      throw error;
    }
  }
  
  /**
   * Ruft das aktuelle Netzwerk ab
   * @returns {Promise<Object>} Aktuelles Netzwerk
   */
  async getNetwork() {
    try {
      logger.debug('Rufe aktuelles Netzwerk ab');
      
      // Prüfe, ob die API verbunden ist
      if (!this.api || !this.api.isConnected) {
        throw new Error('Keine Verbindung zur API');
      }
      
      // Hole den Chain-Namen
      const chain = await this.api.rpc.system.chain();
      
      return {
        id: this.selectedNetwork,
        name: chain.toString(),
        url: this.options.rpc[this.selectedNetwork]
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen des aktuellen Netzwerks', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft die unterstützten Funktionen des Wallets ab
   * @returns {Promise<Object>} Unterstützte Funktionen
   */
  async getCapabilities() {
    try {
      logger.debug('Rufe unterstützte Funktionen des Wallets ab');
      
      // Prüfe, ob eine Erweiterung gefunden wurde
      if (!this.extension) {
        throw new Error('Keine Polkadot-Erweiterung gefunden oder Zugriff nicht erlaubt');
      }
      
      // Importiere die benötigten Module
      const { web3FromSource } = await import('@polkadot/extension-dapp');
      
      // Hole den Injector
      const injector = await web3FromSource(this.selectedAccount.meta.source);
      
      return {
        signMessage: !!injector?.signer?.signRaw,
        signTransaction: !!injector?.signer?.signPayload,
        accounts: true,
        switchNetwork: true,
        name: this.extension.name,
        version: this.extension.version
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen der unterstützten Funktionen', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Exportiert das Wallet (nicht unterstützt)
   * @param {Object} options - Exportoptionen
   * @returns {Promise<Object>} Exportergebnis
   */
  async exportWallet(options = {}) {
    logger.warn('Exportieren des Wallets wird nicht unterstützt');
    throw new Error('Exportieren des Wallets wird nicht unterstützt');
  }
  
  /**
   * Importiert ein Wallet (nicht unterstützt)
   * @param {Object} data - Zu importierende Daten
   * @param {Object} options - Importoptionen
   * @returns {Promise<Object>} Importergebnis
   */
  async importWallet(data, options = {}) {
    logger.warn('Importieren des Wallets wird nicht unterstützt');
    throw new Error('Importieren des Wallets wird nicht unterstützt');
  }
}/**
 * @fileoverview Polkadot-Wallet-Adapter für die Interaktion mit Polkadot-Wallets
 * 
 * Dieser Adapter implementiert das WalletAdapter-Interface für Polkadot-Wallets
 * wie Polkadot.js Extension, Talisman, SubWallet, etc.
 */

import { WalletAdapter } from './WalletAdapter.js';
import { logger } from '../../utils/logger.js';

/**
 * Polkadot-Wallet-Adapter für die Interaktion mit Polkadot-Wallets
 */
export class PolkadotWalletAdapter extends WalletAdapter {
  /**
   * Erstellt eine neue Instanz des PolkadotWalletAdapter
   * @param {Object} options - Konfigurationsoptionen
   * @param {string} options.appName - Name der Anwendung
   * @param {string} options.appVersion - Version der Anwendung
   * @param {Array<string>} options.supportedWallets - Unterstützte Wallet-Typen
   * @param {Object} options.rpc - RPC-Konfiguration
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      appName: options.appName || 'DeSci-Scholar',
      appVersion: options.appVersion || '1.0.0',
      supportedWallets: options.supportedWallets || ['polkadot-js', 'talisman', 'subwallet'],
      rpc: options.rpc || {
        polkadot: process.env.POLKADOT_PROVIDER_URL || 'wss://rpc.polkadot.io',
        kusama: process.env.KUSAMA_PROVIDER_URL || 'wss://kusama-rpc.polkadot.io',
        westend: process.env.WESTEND_PROVIDER_URL || 'wss://westend-rpc.polkadot.io'
      },
      ...options
    };
    
    this.extension = null;
    this.api = null;
    this.accounts = [];
    this.selectedAccount = null;
    this.selectedNetwork = null;
    this.isInitialized = false;
    
    logger.info('PolkadotWalletAdapter initialisiert');
  }
  
  /**
   * Initialisiert den Wallet-Adapter
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      logger.info('Initialisiere PolkadotWalletAdapter');
      
      // Importiere die benötigten Module
      const { web3Enable, web3Accounts } = await import('@polkadot/extension-dapp');
      const { ApiPromise, WsProvider } = await import('@polkadot/api');
      
      // Aktiviere die Web3-Erweiterungen
      const extensions = await web3Enable(this.options.appName);
      
      if (extensions.length === 0) {
        logger.warn('Keine Polkadot-Erweiterung gefunden oder Zugriff nicht erlaubt');
      } else {
        this.extension = extensions[0];
        logger.debug(`Polkadot-Erweiterung gefunden: ${this.extension.name}`);
        
        // Hole die verfügbaren Konten
        this.accounts = await web3Accounts();
        logger.debug(`${this.accounts.length} Konten gefunden`);
        
        // Wähle das erste Konto aus, falls verfügbar
        if (this.accounts.length > 0) {
          this.selectedAccount = this.accounts[0];
          logger.debug(`Konto ausgewählt: ${this.selectedAccount.address}`);
        }
      }
      
      // Verbinde mit dem Standard-Netzwerk
      await this.connectToNetwork('polkadot');
      
      this.isInitialized = true;
      
      logger.info('PolkadotWalletAdapter erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des PolkadotWalletAdapter', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Verbindet mit einem Polkadot-Netzwerk
   * @param {string} network - Netzwerkname
   * @returns {Promise<void>}
   */
  async connectToNetwork(network) {
    try {
      logger.info(`Verbinde mit Polkadot-Netzwerk: ${network}`);
      
      // Prüfe, ob das Netzwerk unterstützt wird
      if (!this.options.rpc[network]) {
        throw new Error(`Netzwerk ${network} wird nicht unterstützt`);
      }
      
      // Importiere die benötigten Module
      const { ApiPromise, WsProvider } = await import('@polkadot/api');
      
      // Trenne die Verbindung zum aktuellen Netzwerk, falls vorhanden
      if (this.api) {
        await this.api.disconnect();
      }
      
      // Erstelle den Provider
      const provider = new WsProvider(this.options.rpc[network]);
      
      // Erstelle die API
      this.api = await ApiPromise.create({ provider });
      
      // Prüfe die Verbindung
      const chain = await this.api.rpc.system.chain();
      
      logger.debug(`Verbunden mit ${chain}`);
      
      // Setze das ausgewählte Netzwerk
      this.selectedNetwork = network;
      
      logger.info(`Erfolgreich mit Polkadot-Netzwerk ${network} verbunden`);
    } catch (error) {
      logger.error('Fehler beim Verbinden mit dem Polkadot-Netzwerk', {
        error: error.message,
        network
      });
      throw error;
    }
  }
  
  /**
   * Verbindet mit dem Wallet
   * @param {Object} options - Verbindungsoptionen
   * @returns {Promise<Object>} Verbindungsergebnis
   */
  async connect(options = {}) {
    try {
      logger.info('Verbinde mit Polkadot-Wallet');
      
      // Initialisiere den Adapter, falls noch nicht geschehen
      if (!this.isInitialized) {
        await this.initialize();
      }
      
      // Prüfe, ob eine Erweiterung gefunden wurde
      if (!this.extension) {
        throw new Error('Keine Polkadot-Erweiterung gefunden oder Zugriff nicht erlaubt');
      }
      
      // Prüfe, ob Konten verfügbar sind
      if (this.accounts.length === 0) {
        throw new Error('Keine Konten in der Polkadot-Erweiterung gefunden');
      }
      
      // Wähle ein Konto aus, falls angegeben
      if (options.accountIndex !== undefined) {
        if (options.accountIndex >= 0 && options.accountIndex < this.accounts.length) {
          this.selectedAccount = this.accounts[options.accountIndex];
          logger.debug(`Konto ausgewählt: ${this.selectedAccount.address}`);
        } else {
          throw new Error(`Ungültiger Kontoindex: ${options.accountIndex}`);
        }
      } else if (options.address) {
        const account = this.accounts.find(acc => acc.address === options.address);
        if (account) {
          this.selectedAccount = account;
          logger.debug(`Konto ausgewählt: ${this.selectedAccount.address}`);
        } else {
          throw new Error(`Konto mit Adresse ${options.address} nicht gefunden`);
        }
      }
      
      // Verbinde mit dem angegebenen Netzwerk, falls angegeben
      if (options.network && options.network !== this.selectedNetwork) {
        await this.connectToNetwork(options.network);
      }
      
      logger.info('Erfolgreich mit Polkadot-Wallet verbunden');
      
      return {
        success: true,
        address: this.selectedAccount.address,
        network: this.selectedNetwork,
        accounts: this.accounts.map(acc => ({
          address: acc.address,
          name: acc.meta.name,
          source: acc.meta.source
        }))
      };
    } catch (error) {
      logger.error('Fehler beim Verbinden mit dem Polkadot-Wallet', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Trennt die Verbindung zum Wallet
   * @returns {Promise<void>}
   */
  async disconnect() {
    try {
      logger.info('Trenne Verbindung zum Polkadot-Wallet');
      
      // Trenne die Verbindung zur API, falls vorhanden
      if (this.api) {
        await this.api.disconnect();
        this.api = null;
      }
      
      // Setze die Variablen zurück
      this.selectedAccount = null;
      this.selectedNetwork = null;
      
      logger.info('Verbindung zum Polkadot-Wallet getrennt');
    } catch (error) {
      logger.error('Fehler beim Trennen der Verbindung zum Polkadot-Wallet', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Verbindung zum Wallet besteht
   * @returns {Promise<boolean>} True, wenn eine Verbindung besteht
   */
  async isConnected() {
    try {
      // Prüfe, ob ein Konto ausgewählt ist
      if (!this.selectedAccount) {
        return false;
      }
      
      // Prüfe, ob die API verbunden ist
      if (!this.api || !this.api.isConnected) {
        return false;
      }
      
      return true;
    } catch (error) {
      logger.debug('Keine Verbindung zum Polkadot-Wallet');
      return false;
    }
  }
  
  /**
   * Ruft die Adresse des Wallets ab
   * @returns {Promise<string>} Wallet-Adresse
   */
  async getAddress() {
    try {
      logger.debug('Rufe Wallet-Adresse ab');
      
      // Prüfe, ob ein Konto ausgewählt ist
      if (!this.selectedAccount) {
        throw new Error('Kein Konto ausgewählt');
      }
      
      return this.selectedAccount.address;
    } catch (error) {
      logger.error('Fehler beim Abrufen der Wallet-Adresse', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft den Kontostand des Wallets ab
   * @param {Object} options - Abfrageoptionen
   * @returns {Promise<Object>} Kontostanddetails
   */
  async getBalance(options = {}) {
    try {
      logger.debug('Rufe Kontostand des Wallets ab');
      
      // Prüfe, ob ein Konto ausgewählt ist
      if (!this.selectedAccount) {
        throw new Error('Kein Konto ausgewählt');
      }
      
      // Prüfe, ob die API verbunden ist
      if (!this.api || !this.api.isConnected) {
        throw new Error('Keine Verbindung zur API');
      }
      
      // Hole den Kontostand
      const { data: balance } = await this.api.query.system.account(this.selectedAccount.address);
      
      // Konvertiere den Kontostand in eine lesbare Form
      const free = this.api.createType('Balance', balance.free).toHuman();
      const reserved = this.api.createType('Balance', balance.reserved).toHuman();
      const miscFrozen = this.api.createType('Balance', balance.miscFrozen).toHuman();
      const feeFrozen = this.api.createType('Balance', balance.feeFrozen).toHuman();
      
      return {
        address: this.selectedAccount.address,
        network: this.selectedNetwork,
        free,
        reserved,
        miscFrozen,
        feeFrozen,
        total: this.api.createType('Balance', balance.free.add(balance.reserved)).toHuman()
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen des Kontostands', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Signiert eine Nachricht
   * @param {string} message - Zu signierende Nachricht
   * @param {Object} options - Signierungsoptionen
   * @returns {Promise<string>} Signatur
   */
  async signMessage(message, options = {}) {
    try {
      logger.info('Signiere Nachricht');
      
      // Prüfe, ob ein Konto ausgewählt ist
      if (!this.selectedAccount) {
        throw new Error('Kein Konto ausgewählt');
      }
      
      // Importiere die benötigten Module
      const { web3FromSource } = await import('@polkadot/extension-dapp');
      const { stringToU8a, u8aToHex } = await import('@polkadot/util');
      
      // Hole den Signer
      const injector = await web3FromSource(this.selectedAccount.meta.source);
      
      // Signiere die Nachricht
      const signRaw = injector?.signer?.signRaw;
      
      if (!signRaw) {
        throw new Error('Signieren wird von dieser Erweiterung nicht unterstützt');
      }
      
      // Konvertiere die Nachricht in ein Uint8Array
      const messageU8a = options.isHex
        ? message
        : u8aToHex(stringToU8a(message));
      
      // Signiere die Nachricht
      const { signature } = await signRaw({
        address: this.selectedAccount.address,
        data: messageU8a,
        type: options.type || 'bytes'
      });
      
      logger.info('Nachricht erfolgreich signiert');
      
      return signature;
    } catch (error) {
      logger.error('Fehler beim Signieren der Nachricht', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Signiert eine Transaktion
   * @param {Object} transaction - Zu signierende Transaktion
   * @param {Object} options - Signierungsoptionen
   * @returns {Promise<Object>} Signierte Transaktion
   */
  async signTransaction(transaction, options = {}) {
    try {
      logger.info('Signiere Transaktion');
      
      // Prüfe, ob ein Konto ausgewählt ist
      if (!this.selectedAccount) {
        throw new Error('Kein Konto ausgewählt');
      }
      
      // Prüfe, ob die API verbunden ist
      if (!this.api || !this.api.isConnected) {
        throw new Error('Keine Verbindung zur API');
      }
      
      // Importiere die benötigten Module
      const { web3FromSource } = await import('@polkadot/extension-dapp');
      
      // Hole den Signer
      const injector = await web3FromSource(this.selectedAccount.meta.source);
      
      // Erstelle die Transaktion
      const { section, method, args = [] } = transaction;
      
      // Prüfe, ob die Methode existiert
      if (!this.api.tx[section] || !this.api.tx[section][method]) {
        throw new Error(`Methode ${section}.${method} nicht gefunden`);
      }
      
      // Erstelle die Transaktion
      const tx = this.api.tx[section][method](...args);
      
      // Signiere die Transaktion
      const signedTx = await tx.signAsync(this.selectedAccount.address, {
        signer: injector.signer,
        nonce: options.nonce
      });
      
      logger.info('Transaktion erfolgreich signiert');
      
      return {
        transaction: tx,
        signedTransaction: signedTx,
        hash: signedTx.hash.toHex()
      };
    } catch (error) {
      logger.error('Fehler beim Signieren der Transaktion', {
        error: error.message,
        transaction
      });
      throw error;
    }
  }
  
  /**
   * Sendet eine Transaktion
   * @param {Object} transaction - Zu sendende Transaktion
   * @param {Object} options - Sendeoptionen
   * @returns {Promise<Object>} Transaktionsergebnis
   */
  async sendTransaction(transaction, options = {}) {
    try {
      logger.info('Sende Transaktion');
      
      // Signiere die Transaktion, falls noch nicht geschehen
      let signedTx;
      
      if (transaction.signedTransaction) {
        signedTx = transaction.signedTransaction;
      } else {
        const result = await this.signTransaction(transaction, options);
        signedTx = result.signedTransaction;
      }
      
      // Sende die Transaktion
      const hash = await new Promise((resolve, reject) => {
        signedTx.send(({ status, dispatchError, events = [] }) => {
          if (status.isInBlock || status.isFinalized) {
            // Prüfe auf Fehler
            if (dispatchError) {
              if (dispatchError.isModule) {
                const decoded = this.api.registry.findMetaError(dispatchError.asModule);
                const { section, method, docs } = decoded;
                
                reject(new Error(`${section}.${method}: ${docs.join(' ')}`));
              } else {
                reject(new Error(dispatchError.toString()));
              }
            } else {
              // Suche nach relevanten Events
              events.forEach(({ event: { data, method, section } }) => {
                if (section === 'system' && method === 'ExtrinsicSuccess') {
                  resolve(signedTx.hash.toHex());
                }
              });
            }
          }
        });
      });
      
      logger.info(`Transaktion erfolgreich gesendet: ${hash}`);
      
      return {
        success: true,
        hash,
        blockHash: null, // Wird erst bekannt, wenn die Transaktion in einem Block ist
        blockNumber: null
      };
    } catch (error) {
      logger.error('Fehler beim Senden der Transaktion', {
        error: error.message,
        transaction
      });
      throw error;
    }
  }
  
  /**
   * Ruft die unterstützten Netzwerke ab
   * @returns {Promise<Array<Object>>} Unterstützte Netzwerke
   */
  async getSupportedNetworks() {
    try {
      logger.debug('Rufe unterstützte Netzwerke ab');
      
      // Erstelle eine Liste der unterstützten Netzwerke
      const networks = [];
      
      for (const [name, url] of Object.entries(this.options.rpc)) {
        networks.push({
          id: name,
          name: name.charAt(0).toUpperCase() + name.slice(1),
          url
        });
      }
      
      return networks;
    } catch (error) {
      logger.error('Fehler beim Abrufen der unterstützten Netzwerke', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Wechselt das Netzwerk
   * @param {string} networkId - Netzwerk-ID
   * @returns {Promise<Object>} Netzwerkwechselergebnis
   */
  async switchNetwork(networkId) {
    try {
      logger.info(`Wechsle zu Netzwerk: ${networkId}`);
      
      // Prüfe, ob das Netzwerk unterstützt wird
      if (!this.options.rpc[networkId]) {
        throw new Error(`Netzwerk ${networkId} wird nicht unterstützt`);
      }
      
      // Verbinde mit dem Netzwerk
      await this.connectToNetwork(networkId);
      
      logger.info(`Erfolgreich zu Netzwerk ${networkId} gewechselt`);
      
      return {
        success: true,
        network: networkId
      };
    } catch (error) {
      logger.error('Fehler beim Wechseln des Netzwerks', {
        error: error.message,
        networkId
      });
      throw error;
    }
  }
  
  /**
   * Ruft das aktuelle Netzwerk ab
   * @returns {Promise<Object>} Aktuelles Netzwerk
   */
  async getNetwork() {
    try {
      logger.debug('Rufe aktuelles Netzwerk ab');
      
      // Prüfe, ob die API verbunden ist
      if (!this.api || !this.api.isConnected) {
        throw new Error('Keine Verbindung zur API');
      }
      
      // Hole den Chain-Namen
      const chain = await this.api.rpc.system.chain();
      
      return {
        id: this.selectedNetwork,
        name: chain.toString(),
        url: this.options.rpc[this.selectedNetwork]
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen des aktuellen Netzwerks', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft die unterstützten Funktionen des Wallets ab
   * @returns {Promise<Object>} Unterstützte Funktionen
   */
  async getCapabilities() {
    try {
      logger.debug('Rufe unterstützte Funktionen des Wallets ab');
      
      // Prüfe, ob eine Erweiterung gefunden wurde
      if (!this.extension) {
        throw new Error('Keine Polkadot-Erweiterung gefunden oder Zugriff nicht erlaubt');
      }
      
      // Importiere die benötigten Module
      const { web3FromSource } = await import('@polkadot/extension-dapp');
      
      // Hole den Injector
      const injector = await web3FromSource(this.selectedAccount.meta.source);
      
      return {
        signMessage: !!injector?.signer?.signRaw,
        signTransaction: !!injector?.signer?.signPayload,
        accounts: true,
        switchNetwork: true,
        name: this.extension.name,
        version: this.extension.version
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen der unterstützten Funktionen', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Exportiert das Wallet (nicht unterstützt)
   * @param {Object} options - Exportoptionen
   * @returns {Promise<Object>} Exportergebnis
   */
  async exportWallet(options = {}) {
    logger.warn('Exportieren des Wallets wird nicht unterstützt');
    throw new Error('Exportieren des Wallets wird nicht unterstützt');
  }
  
  /**
   * Importiert ein Wallet (nicht unterstützt)
   * @param {Object} data - Zu importierende Daten
   * @param {Object} options - Importoptionen
   * @returns {Promise<Object>} Importergebnis
   */
  async importWallet(data, options = {}) {
    logger.warn('Importieren des Wallets wird nicht unterstützt');
    throw new Error('Importieren des Wallets wird nicht unterstützt');
  }
}/**
 * @fileoverview Polkadot-Wallet-Adapter für die Interaktion mit Polkadot-Wallets
 * 
 * Dieser Adapter implementiert das WalletAdapter-Interface für Polkadot-Wallets
 * wie Polkadot.js Extension, Talisman, SubWallet, etc.
 */

import { WalletAdapter } from './WalletAdapter.js';
import { logger } from '../../utils/logger.js';

/**
 * Polkadot-Wallet-Adapter für die Interaktion mit Polkadot-Wallets
 */
export class PolkadotWalletAdapter extends WalletAdapter {
  /**
   * Erstellt eine neue Instanz des PolkadotWalletAdapter
   * @param {Object} options - Konfigurationsoptionen
   * @param {string} options.appName - Name der Anwendung
   * @param {string} options.appVersion - Version der Anwendung
   * @param {Array<string>} options.supportedWallets - Unterstützte Wallet-Typen
   * @param {Object} options.rpc - RPC-Konfiguration
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      appName: options.appName || 'DeSci-Scholar',
      appVersion: options.appVersion || '1.0.0',
      supportedWallets: options.supportedWallets || ['polkadot-js', 'talisman', 'subwallet'],
      rpc: options.rpc || {
        polkadot: process.env.POLKADOT_PROVIDER_URL || 'wss://rpc.polkadot.io',
        kusama: process.env.KUSAMA_PROVIDER_URL || 'wss://kusama-rpc.polkadot.io',
        westend: process.env.WESTEND_PROVIDER_URL || 'wss://westend-rpc.polkadot.io'
      },
      ...options
    };
    
    this.extension = null;
    this.api = null;
    this.accounts = [];
    this.selectedAccount = null;
    this.selectedNetwork = null;
    this.isInitialized = false;
    
    logger.info('PolkadotWalletAdapter initialisiert');
  }
  
  /**
   * Initialisiert den Wallet-Adapter
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      logger.info('Initialisiere PolkadotWalletAdapter');
      
      // Importiere die benötigten Module
      const { web3Enable, web3Accounts } = await import('@polkadot/extension-dapp');
      const { ApiPromise, WsProvider } = await import('@polkadot/api');
      
      // Aktiviere die Web3-Erweiterungen
      const extensions = await web3Enable(this.options.appName);
      
      if (extensions.length === 0) {
        logger.warn('Keine Polkadot-Erweiterung gefunden oder Zugriff nicht erlaubt');
      } else {
        this.extension = extensions[0];
        logger.debug(`Polkadot-Erweiterung gefunden: ${this.extension.name}`);
        
        // Hole die verfügbaren Konten
        this.accounts = await web3Accounts();
        logger.debug(`${this.accounts.length} Konten gefunden`);
        
        // Wähle das erste Konto aus, falls verfügbar
        if (this.accounts.length > 0) {
          this.selectedAccount = this.accounts[0];
          logger.debug(`Konto ausgewählt: ${this.selectedAccount.address}`);
        }
      }
      
      // Verbinde mit dem Standard-Netzwerk
      await this.connectToNetwork('polkadot');
      
      this.isInitialized = true;
      
      logger.info('PolkadotWalletAdapter erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des PolkadotWalletAdapter', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Verbindet mit einem Polkadot-Netzwerk
   * @param {string} network - Netzwerkname
   * @returns {Promise<void>}
   */
  async connectToNetwork(network) {
    try {
      logger.info(`Verbinde mit Polkadot-Netzwerk: ${network}`);
      
      // Prüfe, ob das Netzwerk unterstützt wird
      if (!this.options.rpc[network]) {
        throw new Error(`Netzwerk ${network} wird nicht unterstützt`);
      }
      
      // Importiere die benötigten Module
      const { ApiPromise, WsProvider } = await import('@polkadot/api');
      
      // Trenne die Verbindung zum aktuellen Netzwerk, falls vorhanden
      if (this.api) {
        await this.api.disconnect();
      }
      
      // Erstelle den Provider
      const provider = new WsProvider(this.options.rpc[network]);
      
      // Erstelle die API
      this.api = await ApiPromise.create({ provider });
      
      // Prüfe die Verbindung
      const chain = await this.api.rpc.system.chain();
      
      logger.debug(`Verbunden mit ${chain}`);
      
      // Setze das ausgewählte Netzwerk
      this.selectedNetwork = network;
      
      logger.info(`Erfolgreich mit Polkadot-Netzwerk ${network} verbunden`);
    } catch (error) {
      logger.error('Fehler beim Verbinden mit dem Polkadot-Netzwerk', {
        error: error.message,
        network
      });
      throw error;
    }
  }
  
  /**
   * Verbindet mit dem Wallet
   * @param {Object} options - Verbindungsoptionen
   * @returns {Promise<Object>} Verbindungsergebnis
   */
  async connect(options = {}) {
    try {
      logger.info('Verbinde mit Polkadot-Wallet');
      
      // Initialisiere den Adapter, falls noch nicht geschehen
      if (!this.isInitialized) {
        await this.initialize();
      }
      
      // Prüfe, ob eine Erweiterung gefunden wurde
      if (!this.extension) {
        throw new Error('Keine Polkadot-Erweiterung gefunden oder Zugriff nicht erlaubt');
      }
      
      // Prüfe, ob Konten verfügbar sind
      if (this.accounts.length === 0) {
        throw new Error('Keine Konten in der Polkadot-Erweiterung gefunden');
      }
      
      // Wähle ein Konto aus, falls angegeben
      if (options.accountIndex !== undefined) {
        if (options.accountIndex >= 0 && options.accountIndex < this.accounts.length) {
          this.selectedAccount = this.accounts[options.accountIndex];
          logger.debug(`Konto ausgewählt: ${this.selectedAccount.address}`);
        } else {
          throw new Error(`Ungültiger Kontoindex: ${options.accountIndex}`);
        }
      } else if (options.address) {
        const account = this.accounts.find(acc => acc.address === options.address);
        if (account) {
          this.selectedAccount = account;
          logger.debug(`Konto ausgewählt: ${this.selectedAccount.address}`);
        } else {
          throw new Error(`Konto mit Adresse ${options.address} nicht gefunden`);
        }
      }
      
      // Verbinde mit dem angegebenen Netzwerk, falls angegeben
      if (options.network && options.network !== this.selectedNetwork) {
        await this.connectToNetwork(options.network);
      }
      
      logger.info('Erfolgreich mit Polkadot-Wallet verbunden');
      
      return {
        success: true,
        address: this.selectedAccount.address,
        network: this.selectedNetwork,
        accounts: this.accounts.map(acc => ({
          address: acc.address,
          name: acc.meta.name,
          source: acc.meta.source
        }))
      };
    } catch (error) {
      logger.error('Fehler beim Verbinden mit dem Polkadot-Wallet', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Trennt die Verbindung zum Wallet
   * @returns {Promise<void>}
   */
  async disconnect() {
    try {
      logger.info('Trenne Verbindung zum Polkadot-Wallet');
      
      // Trenne die Verbindung zur API, falls vorhanden
      if (this.api) {
        await this.api.disconnect();
        this.api = null;
      }
      
      // Setze die Variablen zurück
      this.selectedAccount = null;
      this.selectedNetwork = null;
      
      logger.info('Verbindung zum Polkadot-Wallet getrennt');
    } catch (error) {
      logger.error('Fehler beim Trennen der Verbindung zum Polkadot-Wallet', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Verbindung zum Wallet besteht
   * @returns {Promise<boolean>} True, wenn eine Verbindung besteht
   */
  async isConnected() {
    try {
      // Prüfe, ob ein Konto ausgewählt ist
      if (!this.selectedAccount) {
        return false;
      }
      
      // Prüfe, ob die API verbunden ist
      if (!this.api || !this.api.isConnected) {
        return false;
      }
      
      return true;
    } catch (error) {
      logger.debug('Keine Verbindung zum Polkadot-Wallet');
      return false;
    }
  }
  
  /**
   * Ruft die Adresse des Wallets ab
   * @returns {Promise<string>} Wallet-Adresse
   */
  async getAddress() {
    try {
      logger.debug('Rufe Wallet-Adresse ab');
      
      // Prüfe, ob ein Konto ausgewählt ist
      if (!this.selectedAccount) {
        throw new Error('Kein Konto ausgewählt');
      }
      
      return this.selectedAccount.address;
    } catch (error) {
      logger.error('Fehler beim Abrufen der Wallet-Adresse', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft den Kontostand des Wallets ab
   * @param {Object} options - Abfrageoptionen
   * @returns {Promise<Object>} Kontostanddetails
   */
  async getBalance(options = {}) {
    try {
      logger.debug('Rufe Kontostand des Wallets ab');
      
      // Prüfe, ob ein Konto ausgewählt ist
      if (!this.selectedAccount) {
        throw new Error('Kein Konto ausgewählt');
      }
      
      // Prüfe, ob die API verbunden ist
      if (!this.api || !this.api.isConnected) {
        throw new Error('Keine Verbindung zur API');
      }
      
      // Hole den Kontostand
      const { data: balance } = await this.api.query.system.account(this.selectedAccount.address);
      
      // Konvertiere den Kontostand in eine lesbare Form
      const free = this.api.createType('Balance', balance.free).toHuman();
      const reserved = this.api.createType('Balance', balance.reserved).toHuman();
      const miscFrozen = this.api.createType('Balance', balance.miscFrozen).toHuman();
      const feeFrozen = this.api.createType('Balance', balance.feeFrozen).toHuman();
      
      return {
        address: this.selectedAccount.address,
        network: this.selectedNetwork,
        free,
        reserved,
        miscFrozen,
        feeFrozen,
        total: this.api.createType('Balance', balance.free.add(balance.reserved)).toHuman()
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen des Kontostands', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Signiert eine Nachricht
   * @param {string} message - Zu signierende Nachricht
   * @param {Object} options - Signierungsoptionen
   * @returns {Promise<string>} Signatur
   */
  async signMessage(message, options = {}) {
    try {
      logger.info('Signiere Nachricht');
      
      // Prüfe, ob ein Konto ausgewählt ist
      if (!this.selectedAccount) {
        throw new Error('Kein Konto ausgewählt');
      }
      
      // Importiere die benötigten Module
      const { web3FromSource } = await import('@polkadot/extension-dapp');
      const { stringToU8a, u8aToHex } = await import('@polkadot/util');
      
      // Hole den Signer
      const injector = await web3FromSource(this.selectedAccount.meta.source);
      
      // Signiere die Nachricht
      const signRaw = injector?.signer?.signRaw;
      
      if (!signRaw) {
        throw new Error('Signieren wird von dieser Erweiterung nicht unterstützt');
      }
      
      // Konvertiere die Nachricht in ein Uint8Array
      const messageU8a = options.isHex
        ? message
        : u8aToHex(stringToU8a(message));
      
      // Signiere die Nachricht
      const { signature } = await signRaw({
        address: this.selectedAccount.address,
        data: messageU8a,
        type: options.type || 'bytes'
      });
      
      logger.info('Nachricht erfolgreich signiert');
      
      return signature;
    } catch (error) {
      logger.error('Fehler beim Signieren der Nachricht', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Signiert eine Transaktion
   * @param {Object} transaction - Zu signierende Transaktion
   * @param {Object} options - Signierungsoptionen
   * @returns {Promise<Object>} Signierte Transaktion
   */
  async signTransaction(transaction, options = {}) {
    try {
      logger.info('Signiere Transaktion');
      
      // Prüfe, ob ein Konto ausgewählt ist
      if (!this.selectedAccount) {
        throw new Error('Kein Konto ausgewählt');
      }
      
      // Prüfe, ob die API verbunden ist
      if (!this.api || !this.api.isConnected) {
        throw new Error('Keine Verbindung zur API');
      }
      
      // Importiere die benötigten Module
      const { web3FromSource } = await import('@polkadot/extension-dapp');
      
      // Hole den Signer
      const injector = await web3FromSource(this.selectedAccount.meta.source);
      
      // Erstelle die Transaktion
      const { section, method, args = [] } = transaction;
      
      // Prüfe, ob die Methode existiert
      if (!this.api.tx[section] || !this.api.tx[section][method]) {
        throw new Error(`Methode ${section}.${method} nicht gefunden`);
      }
      
      // Erstelle die Transaktion
      const tx = this.api.tx[section][method](...args);
      
      // Signiere die Transaktion
      const signedTx = await tx.signAsync(this.selectedAccount.address, {
        signer: injector.signer,
        nonce: options.nonce
      });
      
      logger.info('Transaktion erfolgreich signiert');
      
      return {
        transaction: tx,
        signedTransaction: signedTx,
        hash: signedTx.hash.toHex()
      };
    } catch (error) {
      logger.error('Fehler beim Signieren der Transaktion', {
        error: error.message,
        transaction
      });
      throw error;
    }
  }
  
  /**
   * Sendet eine Transaktion
   * @param {Object} transaction - Zu sendende Transaktion
   * @param {Object} options - Sendeoptionen
   * @returns {Promise<Object>} Transaktionsergebnis
   */
  async sendTransaction(transaction, options = {}) {
    try {
      logger.info('Sende Transaktion');
      
      // Signiere die Transaktion, falls noch nicht geschehen
      let signedTx;
      
      if (transaction.signedTransaction) {
        signedTx = transaction.signedTransaction;
      } else {
        const result = await this.signTransaction(transaction, options);
        signedTx = result.signedTransaction;
      }
      
      // Sende die Transaktion
      const hash = await new Promise((resolve, reject) => {
        signedTx.send(({ status, dispatchError, events = [] }) => {
          if (status.isInBlock || status.isFinalized) {
            // Prüfe auf Fehler
            if (dispatchError) {
              if (dispatchError.isModule) {
                const decoded = this.api.registry.findMetaError(dispatchError.asModule);
                const { section, method, docs } = decoded;
                
                reject(new Error(`${section}.${method}: ${docs.join(' ')}`));
              } else {
                reject(new Error(dispatchError.toString()));
              }
            } else {
              // Suche nach relevanten Events
              events.forEach(({ event: { data, method, section } }) => {
                if (section === 'system' && method === 'ExtrinsicSuccess') {
                  resolve(signedTx.hash.toHex());
                }
              });
            }
          }
        });
      });
      
      logger.info(`Transaktion erfolgreich gesendet: ${hash}`);
      
      return {
        success: true,
        hash,
        blockHash: null, // Wird erst bekannt, wenn die Transaktion in einem Block ist
        blockNumber: null
      };
    } catch (error) {
      logger.error('Fehler beim Senden der Transaktion', {
        error: error.message,
        transaction
      });
      throw error;
    }
  }
  
  /**
   * Ruft die unterstützten Netzwerke ab
   * @returns {Promise<Array<Object>>} Unterstützte Netzwerke
   */
  async getSupportedNetworks() {
    try {
      logger.debug('Rufe unterstützte Netzwerke ab');
      
      // Erstelle eine Liste der unterstützten Netzwerke
      const networks = [];
      
      for (const [name, url] of Object.entries(this.options.rpc)) {
        networks.push({
          id: name,
          name: name.charAt(0).toUpperCase() + name.slice(1),
          url
        });
      }
      
      return networks;
    } catch (error) {
      logger.error('Fehler beim Abrufen der unterstützten Netzwerke', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Wechselt das Netzwerk
   * @param {string} networkId - Netzwerk-ID
   * @returns {Promise<Object>} Netzwerkwechselergebnis
   */
  async switchNetwork(networkId) {
    try {
      logger.info(`Wechsle zu Netzwerk: ${networkId}`);
      
      // Prüfe, ob das Netzwerk unterstützt wird
      if (!this.options.rpc[networkId]) {
        throw new Error(`Netzwerk ${networkId} wird nicht unterstützt`);
      }
      
      // Verbinde mit dem Netzwerk
      await this.connectToNetwork(networkId);
      
      logger.info(`Erfolgreich zu Netzwerk ${networkId} gewechselt`);
      
      return {
        success: true,
        network: networkId
      };
    } catch (error) {
      logger.error('Fehler beim Wechseln des Netzwerks', {
        error: error.message,
        networkId
      });
      throw error;
    }
  }
  
  /**
   * Ruft das aktuelle Netzwerk ab
   * @returns {Promise<Object>} Aktuelles Netzwerk
   */
  async getNetwork() {
    try {
      logger.debug('Rufe aktuelles Netzwerk ab');
      
      // Prüfe, ob die API verbunden ist
      if (!this.api || !this.api.isConnected) {
        throw new Error('Keine Verbindung zur API');
      }
      
      // Hole den Chain-Namen
      const chain = await this.api.rpc.system.chain();
      
      return {
        id: this.selectedNetwork,
        name: chain.toString(),
        url: this.options.rpc[this.selectedNetwork]
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen des aktuellen Netzwerks', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft die unterstützten Funktionen des Wallets ab
   * @returns {Promise<Object>} Unterstützte Funktionen
   */
  async getCapabilities() {
    try {
      logger.debug('Rufe unterstützte Funktionen des Wallets ab');
      
      // Prüfe, ob eine Erweiterung gefunden wurde
      if (!this.extension) {
        throw new Error('Keine Polkadot-Erweiterung gefunden oder Zugriff nicht erlaubt');
      }
      
      // Importiere die benötigten Module
      const { web3FromSource } = await import('@polkadot/extension-dapp');
      
      // Hole den Injector
      const injector = await web3FromSource(this.selectedAccount.meta.source);
      
      return {
        signMessage: !!injector?.signer?.signRaw,
        signTransaction: !!injector?.signer?.signPayload,
        accounts: true,
        switchNetwork: true,
        name: this.extension.name,
        version: this.extension.version
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen der unterstützten Funktionen', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Exportiert das Wallet (nicht unterstützt)
   * @param {Object} options - Exportoptionen
   * @returns {Promise<Object>} Exportergebnis
   */
  async exportWallet(options = {}) {
    logger.warn('Exportieren des Wallets wird nicht unterstützt');
    throw new Error('Exportieren des Wallets wird nicht unterstützt');
  }
  
  /**
   * Importiert ein Wallet (nicht unterstützt)
   * @param {Object} data - Zu importierende Daten
   * @param {Object} options - Importoptionen
   * @returns {Promise<Object>} Importergebnis
   */
  async importWallet(data, options = {}) {
    logger.warn('Importieren des Wallets wird nicht unterstützt');
    throw new Error('Importieren des Wallets wird nicht unterstützt');
  }
}