/**
 * HandleSystemBridge - Integration mit dem Handle System (hdl.handle.net)
 * 
 * Dieser Service integriert das bestehende Handle System mit Blockchain-basierten
 * NFT-URLs für eine nahtlose Migration von zentralisierten zu dezentralisierten
 * wissenschaftlichen Identifikatoren.
 */

import axios from 'axios';
import { LoggerFactory } from '../../utils/logger.js';

const logger = LoggerFactory.createLogger('HandleSystemBridge');

class HandleSystemBridge {
  constructor(options = {}) {
    this.options = {
      handleProxyUrl: 'http://hdl.handle.net',
      timeout: 10000,
      enableCaching: true,
      cacheTtl: 3600, // 1 Stunde
      ...options
    };
    
    this.cache = new Map();
    this.initialized = false;
  }

  /**
   * Initialisiert den Handle System Bridge
   */
  async initialize() {
    try {
      logger.info('Initialisiere HandleSystemBridge...');
      
      // Test-Verbindung zum Handle System
      await this._testConnection();
      
      this.initialized = true;
      logger.info('HandleSystemBridge erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des HandleSystemBridge:', error);
      throw error;
    }
  }

  /**
   * Löst einen Handle über das Handle System auf
   *
   * @param {string} handle - Handle-Identifier (z.B. "10.1000/paper123")
   * @param {Object} options - Auflösungsoptionen
   * @param {boolean} options.noredirect - Don't Redirect to URLs (wie hdl.handle.net)
   * @param {boolean} options.auth - Authoritative Query (bypass cache)
   * @param {boolean} options.noalias - Don't Follow Aliases
   * @param {string} options.type - Response type ('JSON', 'XML', 'HTML')
   * @returns {Promise<Object>} Handle-Auflösungsergebnis
   */
  async resolveHandle(handle, options = {}) {
    if (!this.initialized) {
      await this.initialize();
    }

    // Cache-Check (nur wenn nicht authoritative)
    const cacheKey = `handle:${handle}:${JSON.stringify(options)}`;
    if (this.options.enableCaching && !options.auth && this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < this.options.cacheTtl * 1000) {
        logger.debug(`Handle aus Cache abgerufen: ${handle}`);
        return cached.data;
      }
    }

    try {
      logger.info(`Löse Handle über Handle System auf: ${handle}`, { options });

      // Baue Query-Parameter wie hdl.handle.net
      const params = {};
      if (options.noredirect) params.noredirect = 'true';
      if (options.auth) params.auth = 'true';
      if (options.noalias) params.noalias = 'true';
      if (options.type) params.type = options.type;

      // Handle System API-Aufruf mit vollständigen hdl.handle.net Features
      const response = await axios.get(`${this.options.handleProxyUrl}/${handle}`, {
        timeout: this.options.timeout,
        headers: {
          'Accept': options.type === 'JSON' ? 'application/json' : 'text/html',
          'User-Agent': 'DeSci-Scholar/2.0 HandleSystemBridge'
        },
        params
      });

      const handleData = await this._parseHandleResponse(response.data, handle, options);

      // Cache speichern (nur wenn nicht authoritative)
      if (this.options.enableCaching && !options.auth) {
        this.cache.set(cacheKey, {
          data: handleData,
          timestamp: Date.now()
        });
      }

      logger.info(`Handle erfolgreich aufgelöst: ${handle}`);
      return handleData;

    } catch (error) {
      logger.error(`Fehler bei Handle-Auflösung für ${handle}:`, error);
      
      // Fallback: Simulierte Handle-Daten für Demo
      return this._generateFallbackHandleData(handle);
    }
  }

  /**
   * Generiert NFT-URL basierend auf Handle-Daten
   * 
   * @param {string} handle - Original Handle
   * @param {Object} metadata - Metadaten aus DataCite/Crossref
   * @returns {Promise<Object>} NFT-URL-Vorschlag
   */
  async generateNFTURL(handle, metadata = {}) {
    try {
      logger.info(`Generiere NFT-URL für Handle: ${handle}`);

      // Handle-Daten abrufen
      const handleData = await this.resolveHandle(handle);

      // NFT-URL-Struktur generieren
      const nftURL = this._createNFTURLStructure(handle, handleData, metadata);

      logger.info(`NFT-URL generiert: ${nftURL.url}`);
      return nftURL;

    } catch (error) {
      logger.error(`Fehler bei NFT-URL-Generierung für ${handle}:`, error);
      throw error;
    }
  }

  /**
   * Erstellt bidirektionale Verknüpfung zwischen Handle und NFT-URL
   * 
   * @param {string} handle - Original Handle
   * @param {string} nftURL - Blockchain-basierte NFT-URL
   * @returns {Promise<Object>} Bridge-Verknüpfung
   */
  async createBidirectionalLink(handle, nftURL) {
    try {
      logger.info(`Erstelle bidirektionale Verknüpfung: ${handle} ↔ ${nftURL}`);

      const bridgeLink = {
        handle,
        nftURL,
        createdAt: new Date().toISOString(),
        status: 'active',
        bridgeType: 'handle-nft-url',
        
        // Auflösungsrichtungen
        resolutions: {
          handleToNFT: `${handle} → ${nftURL}`,
          nftToHandle: `${nftURL} → ${handle}`
        },

        // Metadaten-Synchronisation
        sync: {
          lastSync: new Date().toISOString(),
          syncEnabled: true,
          syncDirection: 'bidirectional'
        }
      };

      // Bridge-Verknüpfung registrieren (würde in echter Implementation in DB gespeichert)
      await this._registerBridgeLink(bridgeLink);

      logger.info(`Bidirektionale Verknüpfung erstellt: ${handle} ↔ ${nftURL}`);
      return bridgeLink;

    } catch (error) {
      logger.error(`Fehler bei bidirektionaler Verknüpfung:`, error);
      throw error;
    }
  }

  /**
   * Migriert bestehende DOI/Handle zu NFT-URL
   * 
   * @param {string} doi - DOI-Identifier
   * @param {Object} options - Migrations-Optionen
   * @returns {Promise<Object>} Migrations-Ergebnis
   */
  async migrateDOIToNFTURL(doi, options = {}) {
    try {
      logger.info(`Starte DOI-zu-NFT-URL Migration: ${doi}`);

      const migrationOptions = {
        preserveHandle: true,
        enableBidirectional: true,
        includeMetadata: true,
        blockchain: 'ethereum',
        domain: '.desci',
        ...options
      };

      // Phase 1: Handle-Auflösung
      const handleData = await this.resolveHandle(doi);

      // Phase 2: Metadaten-Sammlung
      const enrichedMetadata = await this._gatherMigrationMetadata(doi, handleData);

      // Phase 3: NFT-URL-Generierung
      const nftURL = await this.generateNFTURL(doi, enrichedMetadata);

      // Phase 4: Blockchain-Registrierung (simuliert)
      const blockchainResult = await this._registerOnBlockchain(nftURL, migrationOptions);

      // Phase 5: Bidirektionale Verknüpfung
      const bridgeLink = await this.createBidirectionalLink(doi, nftURL.url);

      const migrationResult = {
        success: true,
        originalDOI: doi,
        nftURL: nftURL.url,
        handleData,
        blockchainResult,
        bridgeLink,
        migration: {
          startedAt: new Date().toISOString(),
          completedAt: new Date().toISOString(),
          status: 'completed',
          preservedFeatures: [
            'persistent_identifier',
            'metadata_integrity',
            'citation_tracking',
            'bidirectional_resolution'
          ]
        }
      };

      logger.info(`DOI-zu-NFT-URL Migration erfolgreich: ${doi} → ${nftURL.url}`);
      return migrationResult;

    } catch (error) {
      logger.error(`Fehler bei DOI-Migration für ${doi}:`, error);
      throw error;
    }
  }

  /**
   * Hilfsmethoden
   */

  async _testConnection() {
    try {
      // Test mit bekanntem Handle
      const testHandle = '10.1000/test';
      await axios.get(`${this.options.handleProxyUrl}/${testHandle}`, {
        timeout: 5000,
        validateStatus: () => true // Akzeptiere alle Status-Codes für Test
      });
      logger.debug('Handle System Verbindung erfolgreich getestet');
    } catch (error) {
      logger.warn('Handle System Verbindungstest fehlgeschlagen, verwende Fallback-Modus');
    }
  }

  async _parseHandleResponse(responseData, handle, options = {}) {
    // Parse Handle System Response (HTML oder JSON)
    if (typeof responseData === 'string' && responseData.includes('<html>')) {
      // HTML Response parsen (wie von hdl.handle.net)
      return this._parseHTMLHandleResponse(responseData, handle, options);
    } else {
      // JSON Response parsen
      return {
        handle,
        resolved: true,
        timestamp: new Date().toISOString(),
        values: responseData.values || [],
        metadata: responseData.metadata || {},
        source: 'handle_system_json',
        proxyUrl: `${this.options.handleProxyUrl}/${handle}`,
        options
      };
    }
  }

  _parseHTMLHandleResponse(htmlData, handle, options) {
    try {
      // Extrahiere Handle Values aus HTML-Tabelle
      const values = [];

      // Regex für Tabellenzeilen mit Handle-Werten
      const rowRegex = /<tr><td><b>(\d+)<\/b><\/td><td><b>(?:<a[^>]*>)?([^<]+)(?:<\/a>)?<\/b><\/td><td[^>]*>([^<]+)<\/td>\s*<td[^>]*>([^<]+)<\/td><\/tr>/g;

      let match;
      while ((match = rowRegex.exec(htmlData)) !== null) {
        const [, index, type, timestamp, data] = match;

        values.push({
          index: parseInt(index),
          type: type.trim(),
          timestamp: timestamp.trim(),
          data: data.trim().replace(/<[^>]*>/g, ''), // HTML-Tags entfernen
          raw: data.trim()
        });
      }

      // Extrahiere URL-Werte speziell
      const urlValues = values.filter(v => v.type === 'URL');
      const primaryUrl = urlValues.length > 0 ? urlValues[0].data : null;

      // Extrahiere Admin-Informationen
      const adminValues = values.filter(v => v.type === 'HS_ADMIN');

      return {
        handle,
        resolved: values.length > 0,
        timestamp: new Date().toISOString(),
        values,
        metadata: {
          primaryUrl,
          totalValues: values.length,
          urlCount: urlValues.length,
          adminInfo: adminValues.length > 0 ? adminValues[0].data : null,
          lastModified: values.length > 0 ? values[0].timestamp : null
        },
        source: 'handle_system_html',
        proxyUrl: `${this.options.handleProxyUrl}/${handle}`,
        options,

        // NFT-URL-Vorschlag basierend auf echten Handle-Daten
        nftUrlSuggestion: this._generateNFTURLFromHandle(handle, values)
      };
    } catch (error) {
      logger.warn(`Fehler beim Parsen der HTML Handle Response: ${error.message}`);
      return {
        handle,
        resolved: false,
        error: error.message,
        timestamp: new Date().toISOString(),
        source: 'handle_system_html_error',
        proxyUrl: `${this.options.handleProxyUrl}/${handle}`,
        options
      };
    }
  }

  _generateNFTURLFromHandle(handle, values) {
    // Generiere NFT-URL-Vorschlag basierend auf Handle-Struktur
    const urlSlug = handle.replace(/[\/\.]/g, '-').toLowerCase();
    const primaryUrl = values.find(v => v.type === 'URL')?.data;

    return {
      suggestedURL: `${urlSlug}.desci`,
      domain: '.desci',
      originalHandle: handle,
      originalUrl: primaryUrl,
      benefits: [
        'blockchain_ownership',
        'no_annual_fees',
        'automatic_royalties',
        'decentralized_resolution',
        'immutable_metadata'
      ],
      migration: {
        preserveOriginal: true,
        bidirectionalSync: true,
        metadataEmbedding: true
      }
    };
  }

  _generateFallbackHandleData(handle) {
    logger.info(`Generiere Fallback-Handle-Daten für: ${handle}`);
    
    return {
      handle,
      resolved: false,
      fallback: true,
      timestamp: new Date().toISOString(),
      values: [
        {
          type: 'URL',
          value: `https://doi.org/${handle}`
        }
      ],
      metadata: {
        title: `Academic Paper ${handle}`,
        type: 'academic_publication'
      },
      source: 'fallback_generator'
    };
  }

  _createNFTURLStructure(handle, handleData, metadata) {
    // Generiere benutzerfreundliche NFT-URL
    const urlSlug = this._generateURLSlug(handle, metadata);
    
    return {
      url: `${urlSlug}.desci`,
      originalHandle: handle,
      structure: {
        slug: urlSlug,
        domain: '.desci',
        fullURL: `${urlSlug}.desci`
      },
      metadata: {
        title: metadata.title || handleData.metadata?.title || `Paper ${handle}`,
        authors: metadata.authors || [],
        year: metadata.year || new Date().getFullYear(),
        type: 'academic_nft_url'
      },
      blockchain: {
        network: 'ethereum',
        standard: 'ENS',
        resolver: 'ens_resolver'
      },
      handleSystem: {
        originalHandle: handle,
        proxyURL: handleData.proxyUrl,
        bidirectional: true
      }
    };
  }

  _generateURLSlug(handle, metadata) {
    // Erstelle benutzerfreundlichen URL-Slug
    const title = metadata.title || '';
    const year = metadata.year || new Date().getFullYear();
    
    // Bereinige Titel für URL
    const cleanTitle = title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .substring(0, 30);

    if (cleanTitle) {
      return `${cleanTitle}-${year}`;
    }

    // Fallback: Handle-basierter Slug
    return handle.replace(/[^a-z0-9]/gi, '-').toLowerCase();
  }

  async _gatherMigrationMetadata(doi, handleData) {
    // Sammle Metadaten für Migration
    return {
      doi,
      handleData,
      timestamp: new Date().toISOString(),
      migrationVersion: '1.0'
    };
  }

  async _registerOnBlockchain(nftURL, options) {
    // Simuliere Blockchain-Registrierung
    return {
      transactionHash: `0x${Math.random().toString(16).substring(2, 66)}`,
      contractAddress: `0x${Math.random().toString(16).substring(2, 42)}`,
      tokenId: Math.floor(Math.random() * 1000000),
      blockchain: options.blockchain,
      status: 'confirmed',
      gasUsed: 150000,
      timestamp: new Date().toISOString()
    };
  }

  async _registerBridgeLink(bridgeLink) {
    // Registriere Bridge-Verknüpfung (würde in DB gespeichert)
    logger.debug('Bridge-Verknüpfung registriert:', bridgeLink);
    return bridgeLink;
  }
}

export default HandleSystemBridge;
