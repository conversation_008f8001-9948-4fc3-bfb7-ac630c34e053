/**
 * DoiNftBridgeService - Zentraler Service für die DOI-zu-NFT-Konvertierung
 * 
 * Dieser Service orchestriert die gesamte Pipeline von DOI-Auflösung über
 * DataCite/Crossref-Integration bis hin zur NFT-Prägung mit eingebetteten Metadaten.
 */

import { LoggerFactory } from '../../utils/logger.js';
import DataCiteService from '../integrations/DataCiteService.js';
import CrossrefService from '../integrations/CrossrefService.js';

const logger = LoggerFactory.createLogger('DoiNftBridgeService');

class DoiNftBridgeService {
  constructor(options = {}) {
    this.options = {
      defaultBlockchain: 'polkadot',
      includeDataCite: true,
      includeCrossref: true,
      includeOrcid: true,
      enableZeroKnowledgeProofs: false,
      ...options
    };
    
    this.dataCiteService = new DataCiteService(options.dataCite);
    this.crossrefService = new CrossrefService(options.crossref);
    this.blockchainAdapter = options.blockchainAdapter;
    this.storageService = options.storageService;
    
    this.initialized = false;
  }

  /**
   * Initialisiert den Bridge Service
   */
  async initialize() {
    try {
      logger.info('Initialisiere DoiNftBridgeService...');
      
      if (this.dataCiteService) {
        await this.dataCiteService.initialize();
      }
      
      if (this.crossrefService) {
        await this.crossrefService.initialize();
      }
      
      this.initialized = true;
      logger.info('DoiNftBridgeService erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des DoiNftBridgeService:', error);
      throw error;
    }
  }

  /**
   * Hauptfunktion: Konvertiert einen DOI in einen NFT
   * 
   * @param {string} doi - Digital Object Identifier
   * @param {Object} options - Konvertierungsoptionen
   * @returns {Promise<Object>} Bridge-Ergebnis mit NFT-Details
   */
  async convertDoiToNft(doi, options = {}) {
    if (!this.initialized) {
      await this.initialize();
    }

    const bridgeOptions = {
      ...this.options,
      ...options
    };

    logger.info(`Starte DOI-zu-NFT Bridge für: ${doi}`, { options: bridgeOptions });

    try {
      // Phase 1: DOI-Auflösung und Metadaten-Sammlung
      const resolvedMetadata = await this._resolveDoiMetadata(doi, bridgeOptions);
      
      // Phase 2: Metadaten-Anreicherung und -Validierung
      const enrichedMetadata = await this._enrichMetadata(resolvedMetadata, bridgeOptions);
      
      // Phase 3: NFT-Metadaten vorbereiten
      const nftMetadata = await this._prepareNftMetadata(enrichedMetadata, bridgeOptions);
      
      // Phase 4: NFT prägen
      const nftResult = await this._mintBridgeNft(nftMetadata, bridgeOptions);
      
      // Phase 5: Bridge-Registrierung und Tracking
      const bridgeRecord = await this._registerBridge(doi, nftResult, enrichedMetadata);

      logger.info(`DOI-zu-NFT Bridge erfolgreich abgeschlossen für: ${doi}`, {
        tokenId: nftResult.tokenId,
        blockchain: bridgeOptions.blockchain
      });

      return {
        success: true,
        bridgeType: 'NFT-DOI Bridge',
        doi,
        nft: nftResult,
        metadata: enrichedMetadata,
        bridge: bridgeRecord,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      logger.error(`Fehler bei DOI-zu-NFT Bridge für ${doi}:`, error);
      throw new Error(`Bridge-Konvertierung fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Löst DOI-Metadaten über DataCite und Crossref auf
   */
  async _resolveDoiMetadata(doi, options) {
    const metadata = {
      doi,
      dataCite: null,
      crossref: null,
      resolved: false
    };

    try {
      // DataCite-Auflösung
      if (options.includeDataCite && this.dataCiteService) {
        logger.info(`Löse DOI über DataCite auf: ${doi}`);
        const dataCiteResult = await this.dataCiteService.getDOI(doi);
        
        if (dataCiteResult.success) {
          metadata.dataCite = dataCiteResult.metadata;
          metadata.resolved = true;
          logger.info(`DataCite-Metadaten erfolgreich abgerufen für: ${doi}`);
        }
      }

      // Crossref-Auflösung
      if (options.includeCrossref && this.crossrefService) {
        logger.info(`Löse DOI über Crossref auf: ${doi}`);
        const crossrefResult = await this.crossrefService.getWorkByDOI(doi);
        
        if (crossrefResult.success) {
          metadata.crossref = crossrefResult.metadata;
          metadata.resolved = true;
          logger.info(`Crossref-Metadaten erfolgreich abgerufen für: ${doi}`);
        }
      }

      if (!metadata.resolved) {
        throw new Error(`DOI konnte weder über DataCite noch Crossref aufgelöst werden: ${doi}`);
      }

      return metadata;

    } catch (error) {
      logger.error(`Fehler bei DOI-Auflösung für ${doi}:`, error);
      throw error;
    }
  }

  /**
   * Reichert Metadaten mit zusätzlichen Informationen an
   */
  async _enrichMetadata(resolvedMetadata, options) {
    const enriched = {
      ...resolvedMetadata,
      enrichments: {
        orcidData: null,
        citationMetrics: null,
        impactData: null,
        timestamp: new Date().toISOString()
      }
    };

    try {
      // ORCID-Anreicherung für Autoren
      if (options.includeOrcid) {
        enriched.enrichments.orcidData = await this._enrichWithOrcidData(resolvedMetadata);
      }

      // Zitations-Metriken von Crossref
      if (resolvedMetadata.crossref) {
        enriched.enrichments.citationMetrics = await this._extractCitationMetrics(resolvedMetadata.crossref);
      }

      // Impact-Daten sammeln
      enriched.enrichments.impactData = await this._gatherImpactData(resolvedMetadata.doi);

      return enriched;

    } catch (error) {
      logger.warn(`Warnung bei Metadaten-Anreicherung für ${resolvedMetadata.doi}:`, error);
      return enriched; // Fahre fort, auch wenn Anreicherung teilweise fehlschlägt
    }
  }

  /**
   * Bereitet NFT-Metadaten im Standard-Format vor
   */
  async _prepareNftMetadata(enrichedMetadata, options) {
    const { doi, dataCite, crossref, enrichments } = enrichedMetadata;
    
    // Basis-Metadaten aus DataCite oder Crossref extrahieren
    const baseTitle = dataCite?.titles?.[0]?.title || crossref?.title?.[0] || `Academic Paper ${doi}`;
    const baseAuthors = this._extractAuthors(dataCite, crossref);
    const baseDescription = this._extractDescription(dataCite, crossref);

    const nftMetadata = {
      name: `DOI-NFT: ${baseTitle}`,
      description: `NFT-DOI Bridge representation of academic paper ${doi} with integrated DataCite and Crossref metadata. ${baseDescription}`,
      image: `https://api.desci-scholar.org/bridge/nft/${encodeURIComponent(doi)}/image`,
      external_url: `https://doi.org/${doi}`,
      
      // Standard NFT-Attribute
      attributes: [
        { trait_type: 'Bridge_Type', value: 'NFT-DOI' },
        { trait_type: 'DOI', value: doi },
        { trait_type: 'Blockchain', value: options.blockchain || 'polkadot' },
        { trait_type: 'DataCite_Integration', value: options.includeDataCite ? 'Yes' : 'No' },
        { trait_type: 'Crossref_Integration', value: options.includeCrossref ? 'Yes' : 'No' },
        { trait_type: 'ORCID_Integration', value: options.includeOrcid ? 'Yes' : 'No' },
        { trait_type: 'Citation_Tracking', value: 'Enabled' },
        { trait_type: 'Metadata_Embedding', value: 'Full' },
        { trait_type: 'Minted', value: new Date().toISOString().split('T')[0] }
      ],

      // Eingebettete wissenschaftliche Metadaten
      scientific_metadata: {
        doi,
        title: baseTitle,
        authors: baseAuthors,
        dataCite: options.includeDataCite ? dataCite : null,
        crossref: options.includeCrossref ? crossref : null,
        enrichments: enrichments
      }
    };

    // Zusätzliche Attribute basierend auf verfügbaren Daten
    if (dataCite?.publicationYear) {
      nftMetadata.attributes.push({ trait_type: 'Publication_Year', value: dataCite.publicationYear.toString() });
    }

    if (crossref?.type) {
      nftMetadata.attributes.push({ trait_type: 'Publication_Type', value: crossref.type });
    }

    if (enrichments.citationMetrics?.citedByCount) {
      nftMetadata.attributes.push({ trait_type: 'Citation_Count', value: enrichments.citationMetrics.citedByCount.toString() });
    }

    return nftMetadata;
  }

  /**
   * Prägt den NFT mit den vorbereiteten Metadaten
   */
  async _mintBridgeNft(nftMetadata, options) {
    if (!this.blockchainAdapter) {
      // Fallback: Simuliere NFT-Prägung für Demo-Zwecke
      return {
        tokenId: Math.floor(Math.random() * 1000000),
        contractAddress: `0x${Math.random().toString(16).substring(2, 42)}`,
        transactionHash: `0x${Math.random().toString(16).substring(2, 66)}`,
        blockchain: options.blockchain || 'polkadot',
        status: 'minted',
        metadataUri: await this._storeMetadata(nftMetadata)
      };
    }

    // Echte NFT-Prägung über Blockchain-Adapter
    return await this.blockchainAdapter.mintNft(nftMetadata, options);
  }

  /**
   * Registriert die Bridge-Verbindung für Tracking
   */
  async _registerBridge(doi, nftResult, metadata) {
    const bridgeRecord = {
      doi,
      nftTokenId: nftResult.tokenId,
      nftContract: nftResult.contractAddress,
      blockchain: nftResult.blockchain,
      createdAt: new Date().toISOString(),
      status: 'active',
      lastSync: new Date().toISOString()
    };

    // Hier würde die Bridge-Registrierung in einer Datenbank erfolgen
    logger.info(`Bridge registriert: DOI ${doi} → NFT ${nftResult.tokenId}`);
    
    return bridgeRecord;
  }

  /**
   * Hilfsmethoden für Metadaten-Extraktion
   */
  _extractAuthors(dataCite, crossref) {
    const authors = [];
    
    if (dataCite?.creators) {
      authors.push(...dataCite.creators.map(creator => ({
        name: creator.name,
        orcid: creator.nameIdentifiers?.find(id => id.nameIdentifierScheme === 'ORCID')?.nameIdentifier
      })));
    }
    
    if (crossref?.author && authors.length === 0) {
      authors.push(...crossref.author.map(author => ({
        name: `${author.given || ''} ${author.family || ''}`.trim(),
        orcid: author.ORCID
      })));
    }
    
    return authors;
  }

  _extractDescription(dataCite, crossref) {
    return dataCite?.descriptions?.[0]?.description || 
           crossref?.abstract || 
           'Academic publication with embedded metadata and citation tracking.';
  }

  async _enrichWithOrcidData(metadata) {
    // ORCID-Anreicherung würde hier implementiert
    return null;
  }

  async _extractCitationMetrics(crossrefData) {
    return {
      citedByCount: crossrefData['is-referenced-by-count'] || 0,
      referencesCount: crossrefData['references-count'] || 0
    };
  }

  async _gatherImpactData(doi) {
    // Impact-Daten-Sammlung würde hier implementiert
    return null;
  }

  async _storeMetadata(metadata) {
    if (this.storageService) {
      return await this.storageService.storeMetadata(metadata);
    }
    return `ipfs://mock-${Date.now()}`;
  }
}

export default DoiNftBridgeService;
