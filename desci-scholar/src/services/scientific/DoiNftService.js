const { EventEmitter } = require('events');
const axios = require('axios');

/**
 * DoiNftService - Konvertiert DOIs in NFTs
 * Kernfunktionalität von DeSci-Scholar
 */
class DoiNftService extends EventEmitter {
  constructor(options = {}) {
    super();
    this.options = {
      blockchainManager: null,
      storageManager: null,
      ...options
    };
    
    if (!this.options.blockchainManager) {
      throw new Error('blockchainManager ist erforderlich');
    }
    
    if (!this.options.storageManager) {
      throw new Error('storageManager ist erforderlich');
    }
  }
  
  /**
   * Validiert einen DOI und ruft Metadaten ab
   * @param {string} doi - Digital Object Identifier
   * @returns {Promise<object>} DOI-Metadaten
   */
  async validateAndFetchDoiMetadata(doi) {
    try {
      // DOI-API-Aufruf (z.B. CrossRef oder DataCite)
      const response = await axios.get(`https://api.crossref.org/works/${doi}`);
      return response.data.message;
    } catch (error) {
      throw new Error(`DOI konnte nicht validiert werden: ${error.message}`);
    }
  }
  
  /**
   * Bereitet Metadaten für die NFT-Prägung vor
   * @param {object} doiMetadata - DOI-Metadaten
   * @returns {object} NFT-Metadaten
   */
  prepareNftMetadata(doiMetadata) {
    return {
      name: doiMetadata.title[0] || 'Unbenannte Publikation',
      description: doiMetadata.abstract || '',
      external_url: `https://doi.org/${doiMetadata.DOI}`,
      attributes: [
        {
          trait_type: 'DOI',
          value: doiMetadata.DOI
        },
        {
          trait_type: 'Publication Date',
          value: doiMetadata.created?.['date-parts']?.[0]?.join('-') || ''
        },
        {
          trait_type: 'Journal',
          value: doiMetadata['container-title']?.[0] || ''
        },
        {
          trait_type: 'Authors',
          value: doiMetadata.author?.map(a => `${a.given} ${a.family}`).join(', ') || ''
        }
      ]
    };
  }
  
  /**
   * Konvertiert einen DOI in einen NFT
   * @param {string} doi - Digital Object Identifier
   * @param {object} options - Optionen für die Konvertierung
   * @returns {Promise<object>} NFT-Details
   */
  async convertDoiToNft(doi, options = {}) {
    try {
      // 1. DOI validieren und Metadaten abrufen
      const doiMetadata = await this.validateAndFetchDoiMetadata(doi);
      this.emit('doi:validated', { doi, metadata: doiMetadata });
      
      // 2. NFT-Metadaten vorbereiten
      const nftMetadata = this.prepareNftMetadata(doiMetadata);
      
      // 3. Metadaten auf IPFS speichern
      const storageAdapter = this.options.storageManager.selectAdapter({ type: 'metadata' });
      const metadataUri = await storageAdapter.store(JSON.stringify(nftMetadata));
      this.emit('metadata:stored', { doi, metadataUri });
      
      // 4. NFT prägen
      const blockchainAdapter = this.options.blockchainManager.selectAdapter(options);
      const nftResult = await blockchainAdapter.mintNft(metadataUri, {
        ...options,
        doiReference: doi
      });
      
      this.emit('nft:minted', {
        doi,
        tokenId: nftResult.tokenId,
        contractAddress: nftResult.contractAddress,
        transactionHash: nftResult.transactionHash
      });
      
      return {
        doi,
        tokenId: nftResult.tokenId,
        contractAddress: nftResult.contractAddress,
        blockchain: blockchainAdapter.getNetworkName(),
        transactionHash: nftResult.transactionHash,
        metadataUri
      };
    } catch (error) {
      this.emit('error', { doi, error: error.message });
      throw error;
    }
  }
  
  /**
   * Ruft NFT-Details für einen DOI ab
   * @param {string} doi - Digital Object Identifier
   * @param {object} options - Optionen für die Abfrage
   * @returns {Promise<object>} NFT-Details
   */
  async getNftByDoi(doi, options = {}) {
    // Diese Methode würde in einer vollständigen Implementierung
    // die NFT-Details aus einer Datenbank oder direkt von der Blockchain abrufen
    throw new Error('Noch nicht implementiert');
  }
}

module.exports = DoiNftService;