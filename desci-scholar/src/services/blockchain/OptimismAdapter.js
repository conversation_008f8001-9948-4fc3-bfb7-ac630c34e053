/**
 * @fileoverview Optimism-Adapter für die Interaktion mit dem Optimism-Netzwerk
 * 
 * Dieser Adapter implementiert die BaseAdapter-Klasse für Optimism und
 * ermöglicht die Interaktion mit dem Optimism-Netzwerk und Smart Contracts.
 * Optimism ist ein EVM-kompatibler Layer-2 für Ethereum mit schnellen Transaktionen und niedrigen Gebühren.
 */

import { ethers } from 'ethers';
import { BaseAdapter } from '../../core/adapter/BaseAdapter.js';
import { LoggerFactory } from '../../utils/logger.js';

// ABIs für verschiedene Vertragstypen
import ERC721ABI from '../../contracts/abis/ERC721.json';
import IPNFTContractABI from '../../contracts/abis/IPNFTContract.json';

const logger = LoggerFactory.createLogger('OptimismAdapter');

/**
 * Optimism-Adapter für die Interaktion mit dem Optimism-Netzwerk
 */
export class OptimismAdapter extends BaseAdapter {
  /**
   * Erstellt eine neue Instanz des OptimismAdapter
   * @param {Object} options - Konfigurationsoptionen
   * @param {string} options.providerUrl - URL des Optimism-Providers
   * @param {string} options.privateKey - Private Key für Transaktionen
   * @param {string} options.network - Netzwerk (mainnet, goerli)
   * @param {number} options.gasLimit - Gas-Limit für Transaktionen
   * @param {string} options.gasPrice - Gas-Preis für Transaktionen
   */
  constructor(options = {}) {
    super(options);
    
    this.options = {
      providerUrl: options.providerUrl || process.env.OPTIMISM_PROVIDER_URL || 'https://mainnet.optimism.io',
      privateKey: options.privateKey || process.env.OPTIMISM_PRIVATE_KEY,
      network: options.network || process.env.OPTIMISM_NETWORK || 'mainnet',
      gasLimit: options.gasLimit || 3000000,
      gasPrice: options.gasPrice || 'auto',
      ...options
    };
    
    this.provider = null;
    this.wallet = null;
    this.signer = null;
    this.contracts = new Map();
    
    this.logger.info('OptimismAdapter initialisiert');
  }
  
  /**
   * Gibt die Funktionen zurück, die dieser Adapter unterstützt
   * @returns {Array<string>} Unterstützte Funktionen
   */
  getCapabilities() {
    return [
      'sendTransaction',
      'getTransaction',
      'getTransactionStatus',
      'waitForTransaction',
      'deployContract',
      'getContract',
      'callContractMethod',
      'executeContractMethod',
      'mintNFT',
      'mintIPNFT'
    ];
  }
  
  /**
   * Initialisiert den Adapter
   * @returns {Promise<boolean>} Initialisierungsstatus
   */
  async initialize() {
    if (this.initialized) {
      return true;
    }
    
    try {
      this.logger.info('Initialisiere OptimismAdapter');
      
      // Verbinde mit dem Optimism-Netzwerk
      await this.connect();
      
      this.initialized = true;
      this.logger.info('OptimismAdapter erfolgreich initialisiert');
      return true;
    } catch (error) {
      this.logger.error('Fehler bei der Initialisierung des OptimismAdapter', {
        error: error.message,
        stack: error.stack
      });
      return false;
    }
  }
  
  /**
   * Fährt den Adapter herunter
   * @returns {Promise<boolean>} Herunterfahrstatus
   */
  async shutdown() {
    if (!this.initialized) {
      return true;
    }
    
    try {
      this.logger.info('Fahre OptimismAdapter herunter');
      
      // Trenne die Verbindung zum Optimism-Netzwerk
      await this.disconnect();
      
      this.initialized = false;
      this.logger.info('OptimismAdapter erfolgreich heruntergefahren');
      return true;
    } catch (error) {
      this.logger.error('Fehler beim Herunterfahren des OptimismAdapter', {
        error: error.message,
        stack: error.stack
      });
      return false;
    }
  }
  
  /**
   * Verbindet mit dem Optimism-Netzwerk
   * @returns {Promise<void>}
   */
  async connect() {
    try {
      this.logger.info(`Verbinde mit Optimism-Netzwerk: ${this.options.network}`);
      
      // Erstelle den Provider
      if (this.options.providerUrl) {
        this.provider = new ethers.providers.JsonRpcProvider(this.options.providerUrl);
      } else {
        // Fallback zu öffentlichen Providern
        const networkUrls = {
          mainnet: 'https://mainnet.optimism.io',
          goerli: 'https://goerli.optimism.io'
        };
        
        const url = networkUrls[this.options.network] || networkUrls.mainnet;
        this.provider = new ethers.providers.JsonRpcProvider(url);
      }
      
      // Erstelle den Wallet und Signer
      if (this.options.privateKey) {
        this.wallet = new ethers.Wallet(this.options.privateKey, this.provider);
        this.signer = this.wallet;
        
        // Hole die Adresse des Wallets
        const address = await this.wallet.getAddress();
        this.logger.debug(`Wallet-Adresse: ${address}`);
        
        // Hole den Kontostand
        const balance = await this.provider.getBalance(address);
        this.logger.debug(`Kontostand: ${ethers.utils.formatEther(balance)} ETH`);
      } else {
        this.logger.warn('Kein Private Key angegeben, nur Lesezugriff möglich');
        this.signer = this.provider;
      }
      
      // Prüfe die Verbindung
      const network = await this.provider.getNetwork();
      this.logger.debug(`Verbunden mit Netzwerk: ${network.name} (Chain-ID: ${network.chainId})`);
      
      // Optimism Mainnet hat Chain-ID 10, Goerli Testnet hat Chain-ID 420
      const expectedChainId = this.options.network === 'mainnet' ? 10 : 420;
      
      if (network.chainId !== expectedChainId) {
        this.logger.warn(`Chain-ID stimmt nicht überein: Erwartet ${expectedChainId}, erhalten ${network.chainId}`);
      }
      
      this.logger.info('Erfolgreich mit Optimism-Netzwerk verbunden');
    } catch (error) {
      this.logger.error('Fehler beim Verbinden mit dem Optimism-Netzwerk', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Trennt die Verbindung zum Optimism-Netzwerk
   * @returns {Promise<void>}
   */
  async disconnect() {
    try {
      this.logger.info('Trenne Verbindung zum Optimism-Netzwerk');
      
      // Lösche alle Referenzen
      this.provider = null;
      this.wallet = null;
      this.signer = null;
      this.contracts.clear();
      
      this.logger.info('Verbindung zum Optimism-Netzwerk getrennt');
    } catch (error) {
      this.logger.error('Fehler beim Trennen der Verbindung zum Optimism-Netzwerk', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Instanz eines Smart Contracts ab
   * @param {string} address - Adresse des Smart Contracts
   * @param {string} abi - ABI des Smart Contracts
   * @returns {ethers.Contract} Smart Contract-Instanz
   */
  getContract(address, abi) {
    if (!this.initialized) {
      throw new Error('OptimismAdapter nicht initialisiert');
    }
    
    // Prüfe, ob der Contract bereits in der Map ist
    if (this.contracts.has(address)) {
      return this.contracts.get(address);
    }
    
    // Erstelle eine neue Contract-Instanz
    const contract = new ethers.Contract(address, abi, this.signer || this.provider);
    
    // Speichere den Contract in der Map
    this.contracts.set(address, contract);
    
    return contract;
  }
  
  /**
   * Prägt einen NFT
   * @param {string} contractAddress - Contract-Adresse
   * @param {string} recipient - Empfängeradresse
   * @param {string} tokenURI - URI der Token-Metadaten
   * @param {Object} options - Prägungsoptionen
   * @returns {Promise<Object>} Prägungsergebnis
   */
  async mintNFT(contractAddress, recipient, tokenURI, options = {}) {
    if (!this.initialized) {
      throw new Error('OptimismAdapter nicht initialisiert');
    }
    
    if (!this.signer) {
      throw new Error('Kein Signer für Transaktion verfügbar');
    }
    
    try {
      this.logger.info(`Präge NFT auf Optimism für ${recipient} mit URI ${tokenURI}`);
      
      // Lade Contract
      const abi = options.abi || ERC721ABI;
      const contract = this.getContract(contractAddress, abi);
      
      // Bereite Transaktion vor
      const gasLimit = options.gasLimit || this.options.gasLimit;
      const gasPrice = options.gasPrice || this.options.gasPrice;
      
      const txOptions = {
        gasLimit
      };
      
      if (gasPrice !== 'auto') {
        txOptions.gasPrice = ethers.utils.parseUnits(
          typeof gasPrice === 'string' ? gasPrice : gasPrice.toString(),
          'gwei'
        );
      }
      
      // Sende Transaktion
      const tx = await contract.mint(recipient, tokenURI, txOptions);
      this.logger.debug('Transaktion gesendet', { txHash: tx.hash });
      
      // Warte auf Bestätigung
      const receipt = await tx.wait();
      this.logger.info('Transaktion bestätigt', { txHash: tx.hash, blockNumber: receipt.blockNumber });
      
      // Extrahiere Token-ID aus Event
      const event = receipt.events.find(e => e.event === 'Transfer');
      const tokenId = event ? event.args.tokenId.toString() : '0';
      
      return {
        success: true,
        tokenId,
        transactionHash: tx.hash,
        contractAddress,
        network: 'optimism'
      };
    } catch (error) {
      this.logger.error('Fehler beim Prägen des NFT auf Optimism', {
        error: error.message,
        contractAddress,
        recipient,
        tokenURI
      });
      throw error;
    }
  }
}

export default OptimismAdapter;
