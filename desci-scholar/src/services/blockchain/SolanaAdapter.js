/**
 * @fileoverview Solana-Adapter für die Interaktion mit dem Solana-Netzwerk
 * 
 * Dieser Adapter implementiert die BaseAdapter-Klasse für Solana und
 * ermöglicht die Interaktion mit dem Solana-Netzwerk und Smart Contracts.
 * Solana bietet hohen Durchsatz und niedrige Transaktionskosten.
 */

import { 
  Connection, 
  PublicKey, 
  Keypair, 
  Transaction, 
  SystemProgram, 
  LAMPORTS_PER_SOL,
  clusterApiUrl
} from '@solana/web3.js';
import { 
  Metaplex, 
  keypairIdentity, 
  bundlrStorage 
} from '@metaplex-foundation/js';
import { BaseAdapter } from '../../core/adapter/BaseAdapter.js';
import { LoggerFactory } from '../../utils/logger.js';

const logger = LoggerFactory.createLogger('SolanaAdapter');

/**
 * Solana-Adapter für die Interaktion mit dem Solana-Netzwerk
 */
export class SolanaAdapter extends BaseAdapter {
  /**
   * Erstellt eine neue Instanz des SolanaAdapter
   * @param {Object} options - Konfigurationsoptionen
   * @param {string} options.endpoint - URL des Solana-Endpoints
   * @param {string} options.privateKey - Private Key für Transaktionen (Base58 oder Uint8Array)
   * @param {string} options.network - Netzwerk (mainnet-beta, devnet, testnet)
   * @param {Object} options.metaplex - Metaplex-Konfiguration
   */
  constructor(options = {}) {
    super(options);
    
    this.options = {
      endpoint: options.endpoint || process.env.SOLANA_ENDPOINT,
      privateKey: options.privateKey || process.env.SOLANA_PRIVATE_KEY,
      network: options.network || process.env.SOLANA_NETWORK || 'devnet',
      metaplex: options.metaplex || {
        useStorageAdapter: 'bundlr'
      },
      ...options
    };
    
    this.connection = null;
    this.keypair = null;
    this.metaplex = null;
    this.programs = new Map();
    
    this.logger.info('SolanaAdapter initialisiert');
  }
  
  /**
   * Gibt die Funktionen zurück, die dieser Adapter unterstützt
   * @returns {Array<string>} Unterstützte Funktionen
   */
  getCapabilities() {
    return [
      'sendTransaction',
      'getTransaction',
      'getTransactionStatus',
      'waitForTransaction',
      'mintNFT',
      'getBalance',
      'getBlockNumber'
    ];
  }
  
  /**
   * Initialisiert den Adapter
   * @returns {Promise<boolean>} Initialisierungsstatus
   */
  async initialize() {
    if (this.initialized) {
      return true;
    }
    
    try {
      this.logger.info('Initialisiere SolanaAdapter');
      
      // Verbinde mit dem Solana-Netzwerk
      await this.connect();
      
      this.initialized = true;
      this.logger.info('SolanaAdapter erfolgreich initialisiert');
      return true;
    } catch (error) {
      this.logger.error('Fehler bei der Initialisierung des SolanaAdapter', {
        error: error.message,
        stack: error.stack
      });
      return false;
    }
  }
  
  /**
   * Fährt den Adapter herunter
   * @returns {Promise<boolean>} Herunterfahrstatus
   */
  async shutdown() {
    if (!this.initialized) {
      return true;
    }
    
    try {
      this.logger.info('Fahre SolanaAdapter herunter');
      
      // Trenne die Verbindung zum Solana-Netzwerk
      await this.disconnect();
      
      this.initialized = false;
      this.logger.info('SolanaAdapter erfolgreich heruntergefahren');
      return true;
    } catch (error) {
      this.logger.error('Fehler beim Herunterfahren des SolanaAdapter', {
        error: error.message,
        stack: error.stack
      });
      return false;
    }
  }
  
  /**
   * Verbindet mit dem Solana-Netzwerk
   * @returns {Promise<void>}
   */
  async connect() {
    try {
      this.logger.info(`Verbinde mit Solana-Netzwerk: ${this.options.network}`);
      
      // Erstelle die Verbindung
      let endpoint;
      if (this.options.endpoint) {
        endpoint = this.options.endpoint;
      } else {
        endpoint = clusterApiUrl(this.options.network);
      }
      
      this.connection = new Connection(endpoint, 'confirmed');
      
      // Initialisiere den Keypair, falls ein Private Key angegeben wurde
      if (this.options.privateKey) {
        try {
          // Versuche, den Private Key als Base58-String zu interpretieren
          const privateKeyBytes = Buffer.from(this.options.privateKey, 'base58');
          this.keypair = Keypair.fromSecretKey(privateKeyBytes);
        } catch (error) {
          // Versuche, den Private Key als JSON-Array zu interpretieren
          try {
            const privateKeyArray = JSON.parse(this.options.privateKey);
            this.keypair = Keypair.fromSecretKey(new Uint8Array(privateKeyArray));
          } catch (jsonError) {
            throw new Error('Ungültiges Format des Private Key');
          }
        }
        
        this.logger.debug(`Wallet-Adresse: ${this.keypair.publicKey.toString()}`);
        
        // Hole den Kontostand
        const balance = await this.connection.getBalance(this.keypair.publicKey);
        this.logger.debug(`Kontostand: ${balance / LAMPORTS_PER_SOL} SOL`);
      } else {
        this.logger.warn('Kein Private Key angegeben, nur Lesezugriff möglich');
      }
      
      // Initialisiere Metaplex
      if (this.keypair) {
        this.metaplex = new Metaplex(this.connection);
        this.metaplex.use(keypairIdentity(this.keypair));
        
        // Konfiguriere Storage-Adapter
        if (this.options.metaplex.useStorageAdapter === 'bundlr') {
          this.metaplex.use(bundlrStorage({
            address: this.options.network === 'mainnet-beta' 
              ? 'https://node1.bundlr.network' 
              : 'https://devnet.bundlr.network',
            providerUrl: endpoint,
            timeout: 60000
          }));
        }
      }
      
      // Prüfe die Verbindung
      const version = await this.connection.getVersion();
      this.logger.debug(`Verbunden mit Solana-Version: ${version['solana-core']}`);
      
      this.logger.info('Erfolgreich mit Solana-Netzwerk verbunden');
    } catch (error) {
      this.logger.error('Fehler beim Verbinden mit dem Solana-Netzwerk', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Trennt die Verbindung zum Solana-Netzwerk
   * @returns {Promise<void>}
   */
  async disconnect() {
    try {
      this.logger.info('Trenne Verbindung zum Solana-Netzwerk');
      
      // Lösche alle Referenzen
      this.connection = null;
      this.keypair = null;
      this.metaplex = null;
      this.programs.clear();
      
      this.logger.info('Verbindung zum Solana-Netzwerk getrennt');
    } catch (error) {
      this.logger.error('Fehler beim Trennen der Verbindung zum Solana-Netzwerk', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft den Kontostand einer Adresse ab
   * @param {string} address - Adresse
   * @returns {Promise<string>} Kontostand in SOL
   */
  async getBalance(address) {
    if (!this.initialized) {
      throw new Error('SolanaAdapter nicht initialisiert');
    }
    
    try {
      const publicKey = new PublicKey(address);
      const balance = await this.connection.getBalance(publicKey);
      return (balance / LAMPORTS_PER_SOL).toString();
    } catch (error) {
      this.logger.error('Fehler beim Abrufen des Kontostands', {
        error: error.message,
        address
      });
      throw error;
    }
  }
  
  /**
   * Ruft die aktuelle Blocknummer ab
   * @returns {Promise<number>} Aktuelle Blocknummer
   */
  async getBlockNumber() {
    if (!this.initialized) {
      throw new Error('SolanaAdapter nicht initialisiert');
    }
    
    try {
      const slot = await this.connection.getSlot();
      return slot;
    } catch (error) {
      this.logger.error('Fehler beim Abrufen der aktuellen Blocknummer', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Prägt einen NFT
   * @param {string} name - Name des NFT
   * @param {string} description - Beschreibung des NFT
   * @param {string} imageUrl - URL des Bildes
   * @param {Object} options - Prägungsoptionen
   * @returns {Promise<Object>} Prägungsergebnis
   */
  async mintNFT(name, description, imageUrl, options = {}) {
    if (!this.initialized) {
      throw new Error('SolanaAdapter nicht initialisiert');
    }
    
    if (!this.keypair) {
      throw new Error('Kein Keypair für Transaktion verfügbar');
    }
    
    if (!this.metaplex) {
      throw new Error('Metaplex nicht initialisiert');
    }
    
    try {
      this.logger.info(`Präge NFT auf Solana: ${name}`);
      
      // Erstelle die Metadaten
      const { uri } = await this.metaplex.nfts().uploadMetadata({
        name,
        description,
        image: imageUrl,
        ...options.metadata
      });
      
      // Präge den NFT
      const { nft } = await this.metaplex.nfts().create({
        uri,
        name,
        sellerFeeBasisPoints: options.sellerFeeBasisPoints || 500, // 5%
        ...options.nftOptions
      });
      
      this.logger.info('NFT erfolgreich geprägt', { 
        mint: nft.address.toString(),
        name: nft.name,
        uri: nft.uri
      });
      
      return {
        success: true,
        tokenId: nft.address.toString(),
        name: nft.name,
        uri: nft.uri,
        network: 'solana'
      };
    } catch (error) {
      this.logger.error('Fehler beim Prägen des NFT auf Solana', {
        error: error.message,
        name,
        imageUrl
      });
      throw error;
    }
  }
}

export default SolanaAdapter;
