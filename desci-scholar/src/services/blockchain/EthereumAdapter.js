/**
 * @fileoverview Ethereum-Adapter für die Interaktion mit dem Ethereum-Netzwerk
 * 
 * Dieser Adapter implementiert die BaseAdapter-Klasse für Ethereum und
 * ermöglicht die Interaktion mit dem Ethereum-Netzwerk und Smart Contracts.
 */

import { ethers } from 'ethers';
import { BaseAdapter } from '../../core/adapter/BaseAdapter.js';
import { LoggerFactory } from '../../utils/logger.js';

// ABIs für verschiedene Vertragstypen
import ERC721ABI from '../../contracts/abis/ERC721.json';
import IPNFTContractABI from '../../contracts/abis/IPNFTContract.json';
import ERC20ABI from '../../contracts/abis/ERC20.json';

/**
 * Ethereum-Adapter für die Interaktion mit dem Ethereum-Netzwerk
 */
export class EthereumAdapter extends BaseAdapter {
  /**
   * Erstellt eine neue Instanz des EthereumAdapter
   * @param {Object} options - Konfigurationsoptionen
   * @param {string} options.providerUrl - URL des Ethereum-Providers
   * @param {string} options.privateKey - Private Key für Transaktionen
   * @param {string} options.network - Netzwerk (mainnet, ropsten, rinkeby, etc.)
   * @param {number} options.chainId - Chain-ID des Netzwerks
   * @param {number} options.gasLimit - Gas-Limit für Transaktionen
   * @param {string} options.gasPrice - Gas-Preis für Transaktionen
   */
  constructor(options = {}) {
    super(options);
    
    this.options = {
      providerUrl: options.providerUrl || process.env.ETHEREUM_PROVIDER_URL,
      privateKey: options.privateKey || process.env.ETHEREUM_PRIVATE_KEY,
      network: options.network || process.env.ETHEREUM_NETWORK || 'mainnet',
      chainId: options.chainId || (options.network === 'mainnet' ? 1 : 5), // 5 für Goerli
      gasLimit: options.gasLimit || 3000000,
      gasPrice: options.gasPrice || 'auto',
      ...options
    };
    
    this.provider = null;
    this.wallet = null;
    this.signer = null;
    this.contracts = new Map();
  }
  
  /**
   * Gibt die Funktionen zurück, die dieser Adapter unterstützt
   * @returns {Array<string>} Unterstützte Funktionen
   */
  getCapabilities() {
    return [
      'sendTransaction',
      'getTransaction',
      'getTransactionStatus',
      'waitForTransaction',
      'deployContract',
      'getContract',
      'callContractMethod',
      'executeContractMethod',
      'mintNFT',
      'mintIPNFT'
    ];
  }
  
  /**
   * Initialisiert den Adapter
   * @returns {Promise<boolean>} Initialisierungsstatus
   */
  async initialize() {
    if (this.initialized) {
      return true;
    }
    
    try {
      this.logger.info('Initialisiere EthereumAdapter');
      
      // Verbinde mit dem Ethereum-Netzwerk
      await this.connect();
      
      this.initialized = true;
      this.logger.info('EthereumAdapter erfolgreich initialisiert');
      return true;
    } catch (error) {
      this.logger.error('Fehler bei der Initialisierung des EthereumAdapter', {
        error: error.message,
        stack: error.stack
      });
      return false;
    }
  }
  
  /**
   * Fährt den Adapter herunter
   * @returns {Promise<boolean>} Herunterfahrstatus
   */
  async shutdown() {
    if (!this.initialized) {
      return true;
    }
    
    try {
      this.logger.info('Fahre EthereumAdapter herunter');
      
      // Trenne die Verbindung zum Ethereum-Netzwerk
      await this.disconnect();
      
      this.initialized = false;
      this.logger.info('EthereumAdapter erfolgreich heruntergefahren');
      return true;
    } catch (error) {
      this.logger.error('Fehler beim Herunterfahren des EthereumAdapter', {
        error: error.message,
        stack: error.stack
      });
      return false;
    }
  }
  
  /**
   * Verbindet mit dem Ethereum-Netzwerk
   * @returns {Promise<void>}
   */
  async connect() {
    try {
      this.logger.info(`Verbinde mit Ethereum-Netzwerk: ${this.options.network}`);
      
      // Erstelle den Provider
      if (this.options.providerUrl) {
        this.provider = new ethers.providers.JsonRpcProvider(this.options.providerUrl);
      } else {
        this.provider = ethers.getDefaultProvider(this.options.network);
      }
      
      // Erstelle den Wallet und Signer
      if (this.options.privateKey) {
        this.wallet = new ethers.Wallet(this.options.privateKey, this.provider);
        this.signer = this.wallet;
        
        // Hole die Adresse des Wallets
        const address = await this.wallet.getAddress();
        this.logger.debug(`Wallet-Adresse: ${address}`);
        
        // Hole den Kontostand
        const balance = await this.provider.getBalance(address);
        this.logger.debug(`Kontostand: ${ethers.utils.formatEther(balance)} ETH`);
      } else {
        this.logger.warn('Kein Private Key angegeben, nur Lesezugriff möglich');
        this.signer = this.provider;
      }
      
      // Prüfe die Verbindung
      const network = await this.provider.getNetwork();
      this.logger.debug(`Verbunden mit Netzwerk: ${network.name} (Chain-ID: ${network.chainId})`);
      
      // Prüfe, ob die Chain-ID übereinstimmt
      if (this.options.chainId && network.chainId !== this.options.chainId) {
        this.logger.warn(`Chain-ID stimmt nicht überein: Erwartet ${this.options.chainId}, erhalten ${network.chainId}`);
      }
      
      this.logger.info('Erfolgreich mit Ethereum-Netzwerk verbunden');
    } catch (error) {
      this.logger.error('Fehler beim Verbinden mit dem Ethereum-Netzwerk', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Trennt die Verbindung zum Ethereum-Netzwerk
   * @returns {Promise<void>}
   */
  async disconnect() {
    try {
      this.logger.info('Trenne Verbindung zum Ethereum-Netzwerk');
      
      // Lösche alle Referenzen
      this.provider = null;
      this.wallet = null;
      this.signer = null;
      this.contracts.clear();
      
      this.logger.info('Verbindung zum Ethereum-Netzwerk getrennt');
    } catch (error) {
      this.logger.error('Fehler beim Trennen der Verbindung zum Ethereum-Netzwerk', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Instanz eines Smart Contracts ab
   * @param {string} address - Adresse des Smart Contracts
   * @param {string} abi - ABI des Smart Contracts
   * @returns {ethers.Contract} Smart Contract-Instanz
   */
  getContract(address, abi) {
    if (!this.initialized) {
      throw new Error('EthereumAdapter nicht initialisiert');
    }
    
    // Prüfe, ob der Contract bereits in der Map ist
    if (this.contracts.has(address)) {
      return this.contracts.get(address);
    }
    
    // Erstelle eine neue Contract-Instanz
    const contract = new ethers.Contract(address, abi, this.signer || this.provider);
    
    // Speichere den Contract in der Map
    this.contracts.set(address, contract);
    
    return contract;
  }
  
  /**
   * Ruft eine Methode eines Smart Contracts auf (read-only)
   * @param {string} address - Adresse des Smart Contracts
   * @param {string} abi - ABI des Smart Contracts
   * @param {string} method - Name der Methode
   * @param {Array} args - Methodenargumente
   * @returns {Promise<any>} Ergebnis des Methodenaufrufs
   */
  async callContractMethod(address, abi, method, args = []) {
    if (!this.initialized) {
      throw new Error('EthereumAdapter nicht initialisiert');
    }
    
    try {
      this.logger.debug(`Rufe Methode ${method} des Smart Contracts ${address} auf`);
      
      // Hole den Contract
      const contract = this.getContract(address, abi);
      
      // Rufe die Methode auf
      const result = await contract[method](...args);
      
      return result;
    } catch (error) {
      this.logger.error('Fehler beim Aufrufen der Contract-Methode', {
        error: error.message,
        address,
        method,
        args
      });
      throw error;
    }
  }
  
  /**
   * Führt eine Methode eines Smart Contracts aus (Transaktion)
   * @param {string} address - Adresse des Smart Contracts
   * @param {string} abi - ABI des Smart Contracts
   * @param {string} method - Name der Methode
   * @param {Array} args - Methodenargumente
   * @param {Object} options - Transaktionsoptionen
   * @returns {Promise<Object>} Transaktionsergebnis
   */
  async executeContractMethod(address, abi, method, args = [], options = {}) {
    if (!this.initialized) {
      throw new Error('EthereumAdapter nicht initialisiert');
    }
    
    if (!this.signer) {
      throw new Error('Kein Signer für Transaktion verfügbar');
    }
    
    try {
      this.logger.info(`Führe Methode ${method} des Smart Contracts ${address} aus`);
      
      // Hole den Contract
      const contract = this.getContract(address, abi);
      
      // Bereite Transaktionsoptionen vor
      const txOptions = {
        gasLimit: options.gasLimit || this.options.gasLimit,
        gasPrice: options.gasPrice || (this.options.gasPrice === 'auto' ? 
          await this.provider.getGasPrice() : 
          ethers.utils.parseUnits(this.options.gasPrice, 'gwei'))
      };
      
      // Führe die Methode aus
      const tx = await contract[method](...args, txOptions);
      this.logger.debug('Transaktion gesendet', { txHash: tx.hash });
      
      // Warte auf Bestätigung
      const receipt = await tx.wait();
      this.logger.info('Transaktion bestätigt', { txHash: tx.hash, blockNumber: receipt.blockNumber });
      
      return {
        success: true,
        transactionHash: tx.hash,
        blockNumber: receipt.blockNumber,
        gasUsed: receipt.gasUsed.toString()
      };
    } catch (error) {
      this.logger.error('Fehler beim Ausführen der Contract-Methode', {
        error: error.message,
        address,
        method,
        args
      });
      throw error;
    }
  }
  
  /**
   * Prägt einen NFT
   * @param {string} contractAddress - Contract-Adresse
   * @param {string} recipient - Empfängeradresse
   * @param {string} tokenURI - URI der Token-Metadaten
   * @param {Object} options - Prägungsoptionen
   * @returns {Promise<Object>} Prägungsergebnis
   */
  async mintNFT(contractAddress, recipient, tokenURI, options = {}) {
    return this.executeContractMethod(
      contractAddress,
      options.abi || ERC721ABI,
      'mint',
      [recipient, tokenURI],
      options
    );
  }
  
  /**
   * Prägt einen IP-NFT
   * @param {string} contractAddress - Contract-Adresse
   * @param {string} recipient - Empfängeradresse
   * @param {string} tokenURI - URI der Token-Metadaten
   * @param {number} royaltyPercentage - Lizenzgebühr in Prozent
   * @param {Object} options - Prägungsoptionen
   * @returns {Promise<Object>} Prägungsergebnis
   */
  async mintIPNFT(contractAddress, recipient, tokenURI, royaltyPercentage = 0, options = {}) {
    return this.executeContractMethod(
      contractAddress,
      options.abi || IPNFTContractABI,
      'mintIPNFT',
      [recipient, tokenURI, royaltyPercentage],
      options
    );
  }
}

export default EthereumAdapter;
