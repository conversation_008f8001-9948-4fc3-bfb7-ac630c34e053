/**
 * @fileoverview Polkadot-Adapter für die Interaktion mit dem Polkadot-Netzwerk
 * 
 * Dieser Adapter implementiert das BlockchainAdapter-Interface für Polkadot und
 * ermöglicht die Interaktion mit dem Polkadot-Netzwerk und seinen Parachains.
 * Er unterstützt Cross-Chain-Funktionalität und ist optimiert für die Interoperabilität.
 */

import { ApiPromise, WsProvider, Keyring } from '@polkadot/api';
import { cryptoWaitReady } from '@polkadot/util-crypto';
import { BlockchainAdapter } from './BlockchainAdapter.js';
import { logger } from '../../utils/logger.js';

/**
 * Polkadot-Adapter für die Interaktion mit dem Polkadot-Netzwerk
 */
export class PolkadotAdapter extends BlockchainAdapter {
  /**
   * Erstellt eine neue Instanz des PolkadotAdapter
   * @param {Object} options - Konfigurationsoptionen
   * @param {string} options.providerUrl - URL des Polkadot-Providers
   * @param {string} options.mnemonic - Mnemonic für die Schlüsselerstellung
   * @param {string} options.seed - Seed für die Schlüsselerstellung
   * @param {string} options.privateKey - Private Key für Transaktionen
   * @param {string} options.network - Netzwerk (polkadot, kusama, westend, etc.)
   * @param {Object} options.parachains - Konfiguration für Parachains
   * @param {Object} options.contractOptions - Optionen für Smart Contracts
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      providerUrl: options.providerUrl || process.env.POLKADOT_PROVIDER_URL || 'wss://rpc.polkadot.io',
      mnemonic: options.mnemonic || process.env.POLKADOT_MNEMONIC,
      seed: options.seed || process.env.POLKADOT_SEED,
      privateKey: options.privateKey || process.env.POLKADOT_PRIVATE_KEY,
      network: options.network || process.env.POLKADOT_NETWORK || 'polkadot',
      parachains: options.parachains || {
        moonbeam: {
          url: process.env.MOONBEAM_PROVIDER_URL || 'wss://moonbeam.api.onfinality.io/public-ws',
          enabled: true
        },
        acala: {
          url: process.env.ACALA_PROVIDER_URL || 'wss://acala-rpc.dwellir.com',
          enabled: true
        }
      },
      contractOptions: options.contractOptions || {
        gasLimit: 3000000,
        storageDepositLimit: null
      },
      ...options
    };
    
    this.api = null;
    this.parachainApis = {};
    this.keyring = null;
    this.account = null;
    this.contracts = new Map();
    
    logger.info('PolkadotAdapter initialisiert');
  }
  
  /**
   * Initialisiert den Polkadot-Adapter
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      logger.info('Initialisiere PolkadotAdapter');
      
      // Warte auf die Initialisierung der Kryptofunktionen
      await cryptoWaitReady();
      
      // Verbinde mit dem Polkadot-Netzwerk
      await this.connect();
      
      // Initialisiere den Keyring
      await this.initializeKeyring();
      
      // Verbinde mit den konfigurierten Parachains
      await this.connectToParachains();
      
      logger.info('PolkadotAdapter erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des PolkadotAdapter', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Verbindet mit dem Polkadot-Netzwerk
   * @param {Object} options - Verbindungsoptionen
   * @returns {Promise<void>}
   */
  async connect(options = {}) {
    try {
      logger.info(`Verbinde mit Polkadot-Netzwerk: ${this.options.network}`);
      
      // Erstelle den Provider
      const provider = new WsProvider(this.options.providerUrl);
      
      // Erstelle die API
      this.api = await ApiPromise.create({ provider });
      
      // Prüfe die Verbindung
      const chain = await this.api.rpc.system.chain();
      const version = await this.api.rpc.system.version();
      
      logger.debug(`Verbunden mit ${chain} (${version})`);
      
      logger.info('Erfolgreich mit Polkadot-Netzwerk verbunden');
    } catch (error) {
      logger.error('Fehler beim Verbinden mit dem Polkadot-Netzwerk', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Initialisiert den Keyring und das Konto
   * @returns {Promise<void>}
   */
  async initializeKeyring() {
    try {
      logger.info('Initialisiere Keyring');
      
      // Erstelle den Keyring
      this.keyring = new Keyring({ type: 'sr25519' });
      
      // Erstelle das Konto basierend auf den verfügbaren Optionen
      if (this.options.mnemonic) {
        this.account = this.keyring.addFromMnemonic(this.options.mnemonic);
        logger.debug(`Konto aus Mnemonic erstellt: ${this.account.address}`);
      } else if (this.options.seed) {
        this.account = this.keyring.addFromSeed(this.options.seed);
        logger.debug(`Konto aus Seed erstellt: ${this.account.address}`);
      } else if (this.options.privateKey) {
        this.account = this.keyring.addFromUri(this.options.privateKey);
        logger.debug(`Konto aus Private Key erstellt: ${this.account.address}`);
      } else {
        logger.warn('Kein Mnemonic, Seed oder Private Key angegeben, nur Lesezugriff möglich');
      }
      
      logger.info('Keyring erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des Keyring', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Verbindet mit den konfigurierten Parachains
   * @returns {Promise<void>}
   */
  async connectToParachains() {
    try {
      logger.info('Verbinde mit Parachains');
      
      // Verbinde mit jeder konfigurierten Parachain
      for (const [name, config] of Object.entries(this.options.parachains)) {
        if (config.enabled) {
          try {
            logger.debug(`Verbinde mit Parachain: ${name}`);
            
            // Erstelle den Provider
            const provider = new WsProvider(config.url);
            
            // Erstelle die API
            const api = await ApiPromise.create({ provider });
            
            // Speichere die API
            this.parachainApis[name] = api;
            
            // Prüfe die Verbindung
            const chain = await api.rpc.system.chain();
            
            logger.debug(`Verbunden mit Parachain ${name} (${chain})`);
          } catch (error) {
            logger.error(`Fehler beim Verbinden mit Parachain ${name}`, {
              error: error.message
            });
          }
        }
      }
      
      logger.info(`Erfolgreich mit ${Object.keys(this.parachainApis).length} Parachains verbunden`);
    } catch (error) {
      logger.error('Fehler beim Verbinden mit Parachains', {
        error: error.message
      });
      // Wir werfen hier keinen Fehler, da die Hauptfunktionalität auch ohne Parachains funktionieren sollte
    }
  }
  
  /**
   * Trennt die Verbindung zum Polkadot-Netzwerk
   * @returns {Promise<void>}
   */
  async disconnect() {
    try {
      logger.info('Trenne Verbindung zum Polkadot-Netzwerk');
      
      // Trenne die Verbindung zu allen Parachains
      for (const [name, api] of Object.entries(this.parachainApis)) {
        try {
          await api.disconnect();
          logger.debug(`Verbindung zu Parachain ${name} getrennt`);
        } catch (error) {
          logger.warn(`Fehler beim Trennen der Verbindung zu Parachain ${name}`, {
            error: error.message
          });
        }
      }
      
      // Trenne die Verbindung zum Hauptnetzwerk
      if (this.api) {
        await this.api.disconnect();
      }
      
      // Lösche alle Referenzen
      this.api = null;
      this.parachainApis = {};
      this.account = null;
      this.contracts.clear();
      
      logger.info('Verbindung zum Polkadot-Netzwerk getrennt');
    } catch (error) {
      logger.error('Fehler beim Trennen der Verbindung zum Polkadot-Netzwerk', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Verbindung zum Polkadot-Netzwerk besteht
   * @returns {Promise<boolean>} True, wenn eine Verbindung besteht
   */
  async isConnected() {
    try {
      if (!this.api) {
        return false;
      }
      
      // Prüfe, ob die API verbunden ist
      return this.api.isConnected;
    } catch (error) {
      logger.debug('Keine Verbindung zum Polkadot-Netzwerk');
      return false;
    }
  }
  
  /**
   * Ruft die aktuelle Blocknummer ab
   * @returns {Promise<number>} Aktuelle Blocknummer
   */
  async getBlockNumber() {
    try {
      logger.debug('Rufe aktuelle Blocknummer ab');
      
      if (!this.api) {
        throw new Error('Keine Verbindung zum Polkadot-Netzwerk');
      }
      
      // Hole den aktuellen Header
      const header = await this.api.rpc.chain.getHeader();
      
      // Extrahiere die Blocknummer
      const blockNumber = header.number.toNumber();
      
      logger.debug(`Aktuelle Blocknummer: ${blockNumber}`);
      
      return blockNumber;
    } catch (error) {
      logger.error('Fehler beim Abrufen der aktuellen Blocknummer', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft den Kontostand einer Adresse ab
   * @param {string} address - Adresse
   * @param {Object} options - Abfrageoptionen
   * @returns {Promise<string>} Kontostand
   */
  async getBalance(address, options = {}) {
    try {
      logger.debug(`Rufe Kontostand für Adresse ${address} ab`);
      
      if (!this.api) {
        throw new Error('Keine Verbindung zum Polkadot-Netzwerk');
      }
      
      // Hole den Kontostand
      const { data: balance } = await this.api.query.system.account(address);
      
      // Konvertiere den Kontostand in eine lesbare Form
      const formattedBalance = this.api.createType('Balance', balance.free).toHuman();
      
      logger.debug(`Kontostand für Adresse ${address}: ${formattedBalance}`);
      
      return formattedBalance;
    } catch (error) {
      logger.error('Fehler beim Abrufen des Kontostands', {
        error: error.message,
        address
      });
      throw error;
    }
  }
  
  /**
   * Sendet eine Transaktion
   * @param {Object} transaction - Transaktionsdaten
   * @returns {Promise<Object>} Transaktionsergebnis
   */
  async sendTransaction(transaction) {
    try {
      logger.info('Sende Transaktion');
      
      if (!this.api) {
        throw new Error('Keine Verbindung zum Polkadot-Netzwerk');
      }
      
      if (!this.account) {
        throw new Error('Kein Konto verfügbar');
      }
      
      // Extrahiere die Transaktionsdaten
      const { to, value, method, section, args = [] } = transaction;
      
      // Erstelle die Transaktion
      let tx;
      
      if (to && value) {
        // Einfache Überweisung
        tx = this.api.tx.balances.transfer(to, value);
      } else if (method && section) {
        // Spezifische Methode
        tx = this.api.tx[section][method](...args);
      } else {
        throw new Error('Ungültige Transaktionsdaten');
      }
      
      // Sende die Transaktion
      const unsub = await tx.signAndSend(this.account, { nonce: -1 }, (result) => {
        const { status, events = [] } = result;
        
        if (status.isInBlock) {
          logger.debug(`Transaktion in Block ${status.asInBlock.toHex()}`);
          
          // Verarbeite die Events
          events.forEach(({ event: { data, method, section } }) => {
            if (section === 'system' && method === 'ExtrinsicSuccess') {
              logger.info('Transaktion erfolgreich');
              unsub();
            } else if (section === 'system' && method === 'ExtrinsicFailed') {
              const [dispatchError] = data;
              let errorInfo;
              
              if (dispatchError.isModule) {
                const decoded = this.api.registry.findMetaError(dispatchError.asModule);
                errorInfo = `${decoded.section}.${decoded.name}`;
              } else {
                errorInfo = dispatchError.toString();
              }
              
              logger.error(`Transaktion fehlgeschlagen: ${errorInfo}`);
              unsub();
            }
          });
        } else if (status.isFinalized) {
          logger.info(`Transaktion finalisiert in Block ${status.asFinalized.toHex()}`);
          unsub();
        }
      });
      
      // Warte auf die Bestätigung
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          reject(new Error('Timeout bei der Transaktionsbestätigung'));
        }, 60000); // 60 Sekunden Timeout
      });
    } catch (error) {
      logger.error('Fehler beim Senden der Transaktion', {
        error: error.message,
        transaction
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Transaktion ab
   * @param {string} txHash - Transaktions-Hash
   * @returns {Promise<Object>} Transaktionsdaten
   */
  async getTransaction(txHash) {
    try {
      logger.debug(`Rufe Transaktion mit Hash ${txHash} ab`);
      
      if (!this.api) {
        throw new Error('Keine Verbindung zum Polkadot-Netzwerk');
      }
      
      // Hole die Transaktion
      const hash = this.api.createType('Hash', txHash);
      const signedBlock = await this.api.rpc.chain.getBlock(hash);
      
      // Suche die Transaktion im Block
      const transactions = signedBlock.block.extrinsics;
      const transaction = transactions.find(tx => tx.hash.toHex() === txHash);
      
      if (!transaction) {
        throw new Error(`Transaktion mit Hash ${txHash} nicht gefunden`);
      }
      
      logger.debug(`Transaktion mit Hash ${txHash} abgerufen`);
      
      return {
        hash: transaction.hash.toHex(),
        blockHash: hash.toHex(),
        blockNumber: signedBlock.block.header.number.toNumber(),
        from: transaction.signer.toString(),
        method: transaction.method.method,
        section: transaction.method.section,
        args: transaction.method.args.map(arg => arg.toString()),
        nonce: transaction.nonce.toNumber(),
        tip: transaction.tip.toNumber(),
        isSigned: transaction.isSigned
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen der Transaktion', {
        error: error.message,
        txHash
      });
      throw error;
    }
  }
  
  /**
   * Ruft den Status einer Transaktion ab
   * @param {string} txHash - Transaktions-Hash
   * @returns {Promise<string>} Transaktionsstatus
   */
  async getTransactionStatus(txHash) {
    try {
      logger.debug(`Rufe Status der Transaktion mit Hash ${txHash} ab`);
      
      if (!this.api) {
        throw new Error('Keine Verbindung zum Polkadot-Netzwerk');
      }
      
      // Prüfe, ob die Transaktion in einem Block ist
      try {
        const hash = this.api.createType('Hash', txHash);
        await this.api.rpc.chain.getBlock(hash);
        return 'finalized';
      } catch (error) {
        // Transaktion nicht gefunden
        return 'pending';
      }
    } catch (error) {
      logger.error('Fehler beim Abrufen des Transaktionsstatus', {
        error: error.message,
        txHash
      });
      throw error;
    }
  }
  
  /**
   * Wartet auf die Bestätigung einer Transaktion
   * @param {string} txHash - Transaktions-Hash
   * @param {number} confirmations - Anzahl der erforderlichen Bestätigungen
   * @returns {Promise<Object>} Bestätigte Transaktionsdaten
   */
  async waitForTransaction(txHash, confirmations = 1) {
    try {
      logger.info(`Warte auf Bestätigung der Transaktion ${txHash} (${confirmations} Bestätigungen)`);
      
      if (!this.api) {
        throw new Error('Keine Verbindung zum Polkadot-Netzwerk');
      }
      
      // Warte auf die Bestätigung
      return new Promise((resolve, reject) => {
        let blockCount = 0;
        
        const unsub = this.api.rpc.chain.subscribeNewHeads(async (header) => {
          try {
            // Prüfe, ob die Transaktion in einem Block ist
            const status = await this.getTransactionStatus(txHash);
            
            if (status === 'finalized') {
              blockCount++;
              
              if (blockCount >= confirmations) {
                unsub();
                
                // Hole die Transaktionsdaten
                const transaction = await this.getTransaction(txHash);
                
                logger.info(`Transaktion ${txHash} bestätigt`);
                
                resolve(transaction);
              }
            }
          } catch (error) {
            unsub();
            reject(error);
          }
        });
        
        // Timeout
        setTimeout(() => {
          unsub();
          reject(new Error('Timeout bei der Transaktionsbestätigung'));
        }, 60000 * confirmations); // 60 Sekunden pro Bestätigung
      });
    } catch (error) {
      logger.error('Fehler beim Warten auf die Transaktionsbestätigung', {
        error: error.message,
        txHash
      });
      throw error;
    }
  }
  
  /**
   * Erstellt einen Smart Contract
   * @param {string} abi - ABI des Smart Contracts
   * @param {string} bytecode - Bytecode des Smart Contracts
   * @param {Array} args - Konstruktorargumente
   * @returns {Promise<Object>} Ergebnis der Vertragserstellung
   */
  async deployContract(abi, bytecode, args = []) {
    try {
      logger.info('Erstelle Smart Contract');
      
      if (!this.api) {
        throw new Error('Keine Verbindung zum Polkadot-Netzwerk');
      }
      
      if (!this.account) {
        throw new Error('Kein Konto verfügbar');
      }
      
      // Prüfe, ob wir mit Moonbeam verbunden sind
      if (!this.parachainApis.moonbeam) {
        throw new Error('Keine Verbindung zu Moonbeam (EVM-kompatible Parachain)');
      }
      
      // Verwende die Moonbeam-API für EVM-Kompatibilität
      const api = this.parachainApis.moonbeam;
      
      // Erstelle den Contract
      const { Contract } = await import('@polkadot/api-contract');
      const contract = new Contract(api, abi, bytecode);
      
      // Deploye den Contract
      const gasLimit = this.options.contractOptions.gasLimit;
      const storageDepositLimit = this.options.contractOptions.storageDepositLimit;
      
      const tx = contract.tx.new({ gasLimit, storageDepositLimit }, ...args);
      
      // Sende die Transaktion
      const result = await tx.signAndSend(this.account);
      
      // Warte auf die Bestätigung
      const address = await new Promise((resolve, reject) => {
        const unsub = result.subscribe(async (result) => {
          const { status, events = [] } = result;
          
          if (status.isInBlock || status.isFinalized) {
            // Suche nach dem Instantiated-Event
            for (const { event } of events) {
              if (api.events.contracts && event.section === 'contracts' && event.method === 'Instantiated') {
                const [deployer, contractAddress] = event.data;
                
                unsub();
                resolve(contractAddress.toString());
                break;
              }
            }
          }
        });
        
        // Timeout
        setTimeout(() => {
          unsub();
          reject(new Error('Timeout bei der Vertragsinstanziierung'));
        }, 60000); // 60 Sekunden Timeout
      });
      
      logger.info(`Smart Contract erstellt: ${address}`);
      
      // Speichere den Contract in der Map
      this.contracts.set(address, contract);
      
      return {
        address,
        transactionHash: result.hash.toHex(),
        abi
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen des Smart Contracts', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Instanz eines Smart Contracts ab
   * @param {string} address - Adresse des Smart Contracts
   * @param {string} abi - ABI des Smart Contracts
   * @returns {Promise<Object>} Smart Contract-Instanz
   */
  async getContract(address, abi) {
    try {
      logger.debug(`Rufe Smart Contract mit Adresse ${address} ab`);
      
      // Prüfe, ob der Contract bereits in der Map ist
      if (this.contracts.has(address)) {
        return this.contracts.get(address);
      }
      
      // Prüfe, ob wir mit Moonbeam verbunden sind
      if (!this.parachainApis.moonbeam) {
        throw new Error('Keine Verbindung zu Moonbeam (EVM-kompatible Parachain)');
      }
      
      // Verwende die Moonbeam-API für EVM-Kompatibilität
      const api = this.parachainApis.moonbeam;
      
      // Erstelle eine neue Contract-Instanz
      const { Contract } = await import('@polkadot/api-contract');
      const contract = new Contract(api, abi, address);
      
      // Speichere den Contract in der Map
      this.contracts.set(address, contract);
      
      return contract;
    } catch (error) {
      logger.error('Fehler beim Abrufen des Smart Contracts', {
        error: error.message,
        address
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Methode eines Smart Contracts auf (read-only)
   * @param {string} address - Adresse des Smart Contracts
   * @param {string} abi - ABI des Smart Contracts
   * @param {string} method - Name der Methode
   * @param {Array} args - Methodenargumente
   * @returns {Promise<any>} Ergebnis des Methodenaufrufs
   */
  async callContractMethod(address, abi, method, args = []) {
    try {
      logger.debug(`Rufe Methode ${method} des Smart Contracts ${address} auf`);
      
      // Hole den Contract
      const contract = await this.getContract(address, abi);
      
      // Rufe die Methode auf
      const result = await contract.query[method](this.account.address, {}, ...args);
      
      // Prüfe auf Fehler
      if (result.result.isErr) {
        throw new Error(`Fehler beim Aufruf der Methode ${method}: ${result.result.asErr.toString()}`);
      }
      
      // Extrahiere das Ergebnis
      const value = result.output.toHuman();
      
      return value;
    } catch (error) {
      logger.error('Fehler beim Aufrufen der Contract-Methode', {
        error: error.message,
        address,
        method,
        args
      });
      throw error;
    }
  }
  
  /**
   * Führt eine Methode eines Smart Contracts aus (Transaktion)
   * @param {string} address - Adresse des Smart Contracts
   * @param {string} abi - ABI des Smart Contracts
   * @param {string} method - Name der Methode
   * @param {Array} args - Methodenargumente
   * @param {Object} options - Transaktionsoptionen
   * @returns {Promise<Object>} Transaktionsergebnis
   */
  async executeContractMethod(address, abi, method, args = [], options = {}) {
    try {
      logger.info(`Führe Methode ${method} des Smart Contracts ${address} aus`);
      
      if (!this.account) {
        throw new Error('Kein Konto verfügbar');
      }
      
      // Hole den Contract
      const contract = await this.getContract(address, abi);
      
      // Bereite die Transaktionsoptionen vor
      const gasLimit = options.gasLimit || this.options.contractOptions.gasLimit;
      const storageDepositLimit = options.storageDepositLimit || this.options.contractOptions.storageDepositLimit;
      const value = options.value || 0;
      
      // Führe die Methode aus
      const tx = contract.tx[method]({ gasLimit, storageDepositLimit, value }, ...args);
      
      // Sende die Transaktion
      const result = await tx.signAndSend(this.account);
      
      // Warte auf die Bestätigung
      const events = await new Promise((resolve, reject) => {
        const unsub = result.subscribe((result) => {
          const { status, events = [] } = result;
          
          if (status.isInBlock || status.isFinalized) {
            unsub();
            resolve(events);
          }
        });
        
        // Timeout
        setTimeout(() => {
          unsub();
          reject(new Error('Timeout bei der Transaktionsbestätigung'));
        }, 60000); // 60 Sekunden Timeout
      });
      
      logger.info(`Methode ${method} des Smart Contracts ${address} erfolgreich ausgeführt`);
      
      // Extrahiere die Events
      const formattedEvents = events.map(({ event }) => ({
        name: `${event.section}.${event.method}`,
        data: event.data.toHuman()
      }));
      
      return {
        hash: result.hash.toHex(),
        events: formattedEvents,
        status: 'success'
      };
    } catch (error) {
      logger.error('Fehler beim Ausführen der Contract-Methode', {
        error: error.message,
        address,
        method,
        args
      });
      throw error;
    }
  }
  
  /**
   * Prägt einen NFT
   * @param {string} contractAddress - Adresse des NFT-Vertrags
   * @param {string} to - Empfängeradresse
   * @param {string} tokenURI - URI der Token-Metadaten
   * @param {Object} options - Prägungsoptionen
   * @returns {Promise<Object>} Prägungsergebnis
   */
  async mintNFT(contractAddress, to, tokenURI, options = {}) {
    try {
      logger.info(`Präge NFT für ${to} mit URI ${tokenURI}`);
      
      // Führe die mint-Methode des NFT-Vertrags aus
      const result = await this.executeContractMethod(
        contractAddress,
        options.abi || require('../../contracts/abis/ERC721.json'),
        'mint',
        [to, tokenURI],
        options
      );
      
      // Extrahiere die Token-ID aus dem Event
      const transferEvent = result.events.find(event => event.name === 'contracts.Transfer');
      const tokenId = transferEvent ? transferEvent.data[2] : null;
      
      logger.info(`NFT erfolgreich geprägt mit Token-ID ${tokenId}`);
      
      return {
        tokenId,
        contractAddress,
        owner: to,
        tokenURI,
        transactionHash: result.hash
      };
    } catch (error) {
      logger.error('Fehler beim Prägen des NFT', {
        error: error.message,
        contractAddress,
        to,
        tokenURI
      });
      throw error;
    }
  }
  
  /**
   * Prägt einen IP-NFT
   * @param {string} contractAddress - Adresse des IP-NFT-Vertrags
   * @param {string} to - Empfängeradresse
   * @param {string} tokenURI - URI der Token-Metadaten
   * @param {Object} options - Prägungsoptionen
   * @returns {Promise<Object>} Prägungsergebnis
   */
  async mintIPNFT(contractAddress, to, tokenURI, options = {}) {
    try {
      logger.info(`Präge IP-NFT für ${to} mit URI ${tokenURI}`);
      
      // Extrahiere die Optionen
      const {
        royaltyPercentage = 0,
        contentCid,
        legalAgreementCid,
        contentType,
        contentId
      } = options;
      
      // Führe die mintIPNFT-Methode des IP-NFT-Vertrags aus
      const result = await this.executeContractMethod(
        contractAddress,
        options.abi || require('../../contracts/abis/IPNFTContract.json'),
        'mintIPNFT',
        [
          to,
          tokenURI,
          royaltyPercentage * 100, // Konvertiere Prozent in Basispunkte (1% = 100 Basispunkte)
          contentCid || '',
          legalAgreementCid || '',
          {
            contentType: contentType || '',
            contentId: contentId || '',
            timestamp: Math.floor(Date.now() / 1000)
          }
        ],
        options
      );
      
      // Extrahiere die Token-ID aus dem Event
      const transferEvent = result.events.find(event => event.name === 'contracts.Transfer');
      const tokenId = transferEvent ? transferEvent.data[2] : null;
      
      logger.info(`IP-NFT erfolgreich geprägt mit Token-ID ${tokenId}`);
      
      return {
        tokenId,
        contractAddress,
        owner: to,
        tokenURI,
        royaltyPercentage,
        contentCid,
        legalAgreementCid,
        transactionHash: result.hash
      };
    } catch (error) {
      logger.error('Fehler beim Prägen des IP-NFT', {
        error: error.message,
        contractAddress,
        to,
        tokenURI
      });
      throw error;
    }
  }
  
  /**
   * Fractionalisiert einen IP-NFT
   * @param {string} contractAddress - Adresse des IP-NFT-Vertrags
   * @param {string} tokenId - Token-ID des IP-NFT
   * @param {Object} options - Fractionalisierungsoptionen
   * @returns {Promise<Object>} Fractionalisierungsergebnis
   */
  async fractionalizeIPNFT(contractAddress, tokenId, options = {}) {
    try {
      logger.info(`Fractionalisiere IP-NFT mit Token-ID ${tokenId}`);
      
      // Extrahiere die Optionen
      const {
        name = `IP-Shares-${tokenId}`,
        symbol = `IPS-${tokenId}`,
        initialSupply = 1000000,
        ownerAddress
      } = options;
      
      // Führe die fractionalizeIPNFT-Methode des IP-NFT-Vertrags aus
      const result = await this.executeContractMethod(
        contractAddress,
        options.abi || require('../../contracts/abis/IPNFTContract.json'),
        'fractionalizeIPNFT',
        [
          tokenId,
          name,
          symbol,
          initialSupply,
          ownerAddress || this.account.address
        ],
        options
      );
      
      // Extrahiere die Token-Adresse aus dem Event
      const fractionalizationEvent = result.events.find(event => event.name === 'contracts.IPNFTFractionalized');
      const tokenAddress = fractionalizationEvent ? fractionalizationEvent.data[1] : null;
      
      logger.info(`IP-NFT erfolgreich fractionalisiert mit Token-Adresse ${tokenAddress}`);
      
      return {
        tokenId,
        contractAddress,
        tokenAddress,
        name,
        symbol,
        initialSupply,
        transactionHash: result.hash
      };
    } catch (error) {
      logger.error('Fehler beim Fractionalisieren des IP-NFT', {
        error: error.message,
        contractAddress,
        tokenId
      });
      throw error;
    }
  }
  
  /**
   * Erstellt einen Governance-Token für einen IP-NFT
   * @param {string} contractAddress - Adresse des IP-NFT-Vertrags
   * @param {string} tokenId - Token-ID des IP-NFT
   * @param {Object} options - Token-Erstellungsoptionen
   * @returns {Promise<Object>} Token-Erstellungsergebnis
   */
  async createIPGovernanceToken(contractAddress, tokenId, options = {}) {
    try {
      logger.info(`Erstelle Governance-Token für IP-NFT mit Token-ID ${tokenId}`);
      
      // Extrahiere die Optionen
      const {
        name = `IP-Gov-${tokenId}`,
        symbol = `IPG-${tokenId}`,
        initialSupply = 100000,
        ownerAddress
      } = options;
      
      // Führe die createIPGovernanceToken-Methode des IP-NFT-Vertrags aus
      const result = await this.executeContractMethod(
        contractAddress,
        options.abi || require('../../contracts/abis/IPNFTContract.json'),
        'createIPGovernanceToken',
        [
          tokenId,
          name,
          symbol,
          initialSupply,
          ownerAddress || this.account.address
        ],
        options
      );
      
      // Extrahiere die Token-Adresse aus dem Event
      const governanceEvent = result.events.find(event => event.name === 'contracts.IPGovernanceTokenCreated');
      const tokenAddress = governanceEvent ? governanceEvent.data[1] : null;
      
      logger.info(`Governance-Token erfolgreich erstellt mit Token-Adresse ${tokenAddress}`);
      
      return {
        tokenId,
        contractAddress,
        tokenAddress,
        name,
        symbol,
        initialSupply,
        transactionHash: result.hash
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen des Governance-Tokens', {
        error: error.message,
        contractAddress,
        tokenId
      });
      throw error;
    }
  }
  
  /**
   * Registriert eine Berechtigung für einen IP-NFT
   * @param {string} contractAddress - Adresse des IP-NFT-Vertrags
   * @param {string} tokenId - Token-ID des IP-NFT
   * @param {string} grantee - Adresse des Berechtigungsempfängers
   * @param {string} permissionType - Art der Berechtigung
   * @param {Object} options - Berechtigungsoptionen
   * @returns {Promise<Object>} Registrierungsergebnis
   */
  async registerIPNFTPermission(contractAddress, tokenId, grantee, permissionType, options = {}) {
    try {
      logger.info(`Registriere Berechtigung ${permissionType} für IP-NFT ${tokenId} an ${grantee}`);
      
      // Extrahiere die Optionen
      const {
        expirationDate,
        metadata
      } = options;
      
      // Berechne das Ablaufdatum in Sekunden seit der Epoche
      const expirationTimestamp = expirationDate ? Math.floor(new Date(expirationDate).getTime() / 1000) : 0;
      
      // Führe die registerPermission-Methode des IP-NFT-Vertrags aus
      const result = await this.executeContractMethod(
        contractAddress,
        options.abi || require('../../contracts/abis/IPNFTContract.json'),
        'registerPermission',
        [
          tokenId,
          grantee,
          permissionType,
          expirationTimestamp,
          JSON.stringify(metadata || {})
        ],
        options
      );
      
      logger.info(`Berechtigung erfolgreich registriert`);
      
      return {
        tokenId,
        contractAddress,
        grantee,
        permissionType,
        expirationDate: expirationDate || null,
        transactionHash: result.hash
      };
    } catch (error) {
      logger.error('Fehler beim Registrieren der Berechtigung', {
        error: error.message,
        contractAddress,
        tokenId,
        grantee,
        permissionType
      });
      throw error;
    }
  }
  
  /**
   * Widerruft eine Berechtigung für einen IP-NFT
   * @param {string} contractAddress - Adresse des IP-NFT-Vertrags
   * @param {string} tokenId - Token-ID des IP-NFT
   * @param {string} grantee - Adresse des Berechtigungsempfängers
   * @param {string} permissionType - Art der Berechtigung
   * @returns {Promise<Object>} Widerrufsergebnis
   */
  async revokeIPNFTPermission(contractAddress, tokenId, grantee, permissionType) {
    try {
      logger.info(`Widerrufe Berechtigung ${permissionType} für IP-NFT ${tokenId} von ${grantee}`);
      
      // Führe die revokePermission-Methode des IP-NFT-Vertrags aus
      const result = await this.executeContractMethod(
        contractAddress,
        require('../../contracts/abis/IPNFTContract.json'),
        'revokePermission',
        [
          tokenId,
          grantee,
          permissionType
        ]
      );
      
      logger.info(`Berechtigung erfolgreich widerrufen`);
      
      return {
        tokenId,
        contractAddress,
        grantee,
        permissionType,
        transactionHash: result.hash
      };
    } catch (error) {
      logger.error('Fehler beim Widerrufen der Berechtigung', {
        error: error.message,
        contractAddress,
        tokenId,
        grantee,
        permissionType
      });
      throw error;
    }
  }
}/**
 * @fileoverview Polkadot-Adapter für die Interaktion mit dem Polkadot-Netzwerk
 * 
 * Dieser Adapter implementiert das BlockchainAdapter-Interface für Polkadot und
 * ermöglicht die Interaktion mit dem Polkadot-Netzwerk und seinen Parachains.
 * Er unterstützt Cross-Chain-Funktionalität und ist optimiert für die Interoperabilität.
 */

import { ApiPromise, WsProvider, Keyring } from '@polkadot/api';
import { cryptoWaitReady } from '@polkadot/util-crypto';
import { BlockchainAdapter } from './BlockchainAdapter.js';
import { logger } from '../../utils/logger.js';

/**
 * Polkadot-Adapter für die Interaktion mit dem Polkadot-Netzwerk
 */
export class PolkadotAdapter extends BlockchainAdapter {
  /**
   * Erstellt eine neue Instanz des PolkadotAdapter
   * @param {Object} options - Konfigurationsoptionen
   * @param {string} options.providerUrl - URL des Polkadot-Providers
   * @param {string} options.mnemonic - Mnemonic für die Schlüsselerstellung
   * @param {string} options.seed - Seed für die Schlüsselerstellung
   * @param {string} options.privateKey - Private Key für Transaktionen
   * @param {string} options.network - Netzwerk (polkadot, kusama, westend, etc.)
   * @param {Object} options.parachains - Konfiguration für Parachains
   * @param {Object} options.contractOptions - Optionen für Smart Contracts
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      providerUrl: options.providerUrl || process.env.POLKADOT_PROVIDER_URL || 'wss://rpc.polkadot.io',
      mnemonic: options.mnemonic || process.env.POLKADOT_MNEMONIC,
      seed: options.seed || process.env.POLKADOT_SEED,
      privateKey: options.privateKey || process.env.POLKADOT_PRIVATE_KEY,
      network: options.network || process.env.POLKADOT_NETWORK || 'polkadot',
      parachains: options.parachains || {
        moonbeam: {
          url: process.env.MOONBEAM_PROVIDER_URL || 'wss://moonbeam.api.onfinality.io/public-ws',
          enabled: true
        },
        acala: {
          url: process.env.ACALA_PROVIDER_URL || 'wss://acala-rpc.dwellir.com',
          enabled: true
        }
      },
      contractOptions: options.contractOptions || {
        gasLimit: 3000000,
        storageDepositLimit: null
      },
      ...options
    };
    
    this.api = null;
    this.parachainApis = {};
    this.keyring = null;
    this.account = null;
    this.contracts = new Map();
    
    logger.info('PolkadotAdapter initialisiert');
  }
  
  /**
   * Initialisiert den Polkadot-Adapter
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      logger.info('Initialisiere PolkadotAdapter');
      
      // Warte auf die Initialisierung der Kryptofunktionen
      await cryptoWaitReady();
      
      // Verbinde mit dem Polkadot-Netzwerk
      await this.connect();
      
      // Initialisiere den Keyring
      await this.initializeKeyring();
      
      // Verbinde mit den konfigurierten Parachains
      await this.connectToParachains();
      
      logger.info('PolkadotAdapter erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des PolkadotAdapter', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Verbindet mit dem Polkadot-Netzwerk
   * @param {Object} options - Verbindungsoptionen
   * @returns {Promise<void>}
   */
  async connect(options = {}) {
    try {
      logger.info(`Verbinde mit Polkadot-Netzwerk: ${this.options.network}`);
      
      // Erstelle den Provider
      const provider = new WsProvider(this.options.providerUrl);
      
      // Erstelle die API
      this.api = await ApiPromise.create({ provider });
      
      // Prüfe die Verbindung
      const chain = await this.api.rpc.system.chain();
      const version = await this.api.rpc.system.version();
      
      logger.debug(`Verbunden mit ${chain} (${version})`);
      
      logger.info('Erfolgreich mit Polkadot-Netzwerk verbunden');
    } catch (error) {
      logger.error('Fehler beim Verbinden mit dem Polkadot-Netzwerk', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Initialisiert den Keyring und das Konto
   * @returns {Promise<void>}
   */
  async initializeKeyring() {
    try {
      logger.info('Initialisiere Keyring');
      
      // Erstelle den Keyring
      this.keyring = new Keyring({ type: 'sr25519' });
      
      // Erstelle das Konto basierend auf den verfügbaren Optionen
      if (this.options.mnemonic) {
        this.account = this.keyring.addFromMnemonic(this.options.mnemonic);
        logger.debug(`Konto aus Mnemonic erstellt: ${this.account.address}`);
      } else if (this.options.seed) {
        this.account = this.keyring.addFromSeed(this.options.seed);
        logger.debug(`Konto aus Seed erstellt: ${this.account.address}`);
      } else if (this.options.privateKey) {
        this.account = this.keyring.addFromUri(this.options.privateKey);
        logger.debug(`Konto aus Private Key erstellt: ${this.account.address}`);
      } else {
        logger.warn('Kein Mnemonic, Seed oder Private Key angegeben, nur Lesezugriff möglich');
      }
      
      logger.info('Keyring erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des Keyring', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Verbindet mit den konfigurierten Parachains
   * @returns {Promise<void>}
   */
  async connectToParachains() {
    try {
      logger.info('Verbinde mit Parachains');
      
      // Verbinde mit jeder konfigurierten Parachain
      for (const [name, config] of Object.entries(this.options.parachains)) {
        if (config.enabled) {
          try {
            logger.debug(`Verbinde mit Parachain: ${name}`);
            
            // Erstelle den Provider
            const provider = new WsProvider(config.url);
            
            // Erstelle die API
            const api = await ApiPromise.create({ provider });
            
            // Speichere die API
            this.parachainApis[name] = api;
            
            // Prüfe die Verbindung
            const chain = await api.rpc.system.chain();
            
            logger.debug(`Verbunden mit Parachain ${name} (${chain})`);
          } catch (error) {
            logger.error(`Fehler beim Verbinden mit Parachain ${name}`, {
              error: error.message
            });
          }
        }
      }
      
      logger.info(`Erfolgreich mit ${Object.keys(this.parachainApis).length} Parachains verbunden`);
    } catch (error) {
      logger.error('Fehler beim Verbinden mit Parachains', {
        error: error.message
      });
      // Wir werfen hier keinen Fehler, da die Hauptfunktionalität auch ohne Parachains funktionieren sollte
    }
  }
  
  /**
   * Trennt die Verbindung zum Polkadot-Netzwerk
   * @returns {Promise<void>}
   */
  async disconnect() {
    try {
      logger.info('Trenne Verbindung zum Polkadot-Netzwerk');
      
      // Trenne die Verbindung zu allen Parachains
      for (const [name, api] of Object.entries(this.parachainApis)) {
        try {
          await api.disconnect();
          logger.debug(`Verbindung zu Parachain ${name} getrennt`);
        } catch (error) {
          logger.warn(`Fehler beim Trennen der Verbindung zu Parachain ${name}`, {
            error: error.message
          });
        }
      }
      
      // Trenne die Verbindung zum Hauptnetzwerk
      if (this.api) {
        await this.api.disconnect();
      }
      
      // Lösche alle Referenzen
      this.api = null;
      this.parachainApis = {};
      this.account = null;
      this.contracts.clear();
      
      logger.info('Verbindung zum Polkadot-Netzwerk getrennt');
    } catch (error) {
      logger.error('Fehler beim Trennen der Verbindung zum Polkadot-Netzwerk', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Verbindung zum Polkadot-Netzwerk besteht
   * @returns {Promise<boolean>} True, wenn eine Verbindung besteht
   */
  async isConnected() {
    try {
      if (!this.api) {
        return false;
      }
      
      // Prüfe, ob die API verbunden ist
      return this.api.isConnected;
    } catch (error) {
      logger.debug('Keine Verbindung zum Polkadot-Netzwerk');
      return false;
    }
  }
  
  /**
   * Ruft die aktuelle Blocknummer ab
   * @returns {Promise<number>} Aktuelle Blocknummer
   */
  async getBlockNumber() {
    try {
      logger.debug('Rufe aktuelle Blocknummer ab');
      
      if (!this.api) {
        throw new Error('Keine Verbindung zum Polkadot-Netzwerk');
      }
      
      // Hole den aktuellen Header
      const header = await this.api.rpc.chain.getHeader();
      
      // Extrahiere die Blocknummer
      const blockNumber = header.number.toNumber();
      
      logger.debug(`Aktuelle Blocknummer: ${blockNumber}`);
      
      return blockNumber;
    } catch (error) {
      logger.error('Fehler beim Abrufen der aktuellen Blocknummer', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft den Kontostand einer Adresse ab
   * @param {string} address - Adresse
   * @param {Object} options - Abfrageoptionen
   * @returns {Promise<string>} Kontostand
   */
  async getBalance(address, options = {}) {
    try {
      logger.debug(`Rufe Kontostand für Adresse ${address} ab`);
      
      if (!this.api) {
        throw new Error('Keine Verbindung zum Polkadot-Netzwerk');
      }
      
      // Hole den Kontostand
      const { data: balance } = await this.api.query.system.account(address);
      
      // Konvertiere den Kontostand in eine lesbare Form
      const formattedBalance = this.api.createType('Balance', balance.free).toHuman();
      
      logger.debug(`Kontostand für Adresse ${address}: ${formattedBalance}`);
      
      return formattedBalance;
    } catch (error) {
      logger.error('Fehler beim Abrufen des Kontostands', {
        error: error.message,
        address
      });
      throw error;
    }
  }
  
  /**
   * Sendet eine Transaktion
   * @param {Object} transaction - Transaktionsdaten
   * @returns {Promise<Object>} Transaktionsergebnis
   */
  async sendTransaction(transaction) {
    try {
      logger.info('Sende Transaktion');
      
      if (!this.api) {
        throw new Error('Keine Verbindung zum Polkadot-Netzwerk');
      }
      
      if (!this.account) {
        throw new Error('Kein Konto verfügbar');
      }
      
      // Extrahiere die Transaktionsdaten
      const { to, value, method, section, args = [] } = transaction;
      
      // Erstelle die Transaktion
      let tx;
      
      if (to && value) {
        // Einfache Überweisung
        tx = this.api.tx.balances.transfer(to, value);
      } else if (method && section) {
        // Spezifische Methode
        tx = this.api.tx[section][method](...args);
      } else {
        throw new Error('Ungültige Transaktionsdaten');
      }
      
      // Sende die Transaktion
      const unsub = await tx.signAndSend(this.account, { nonce: -1 }, (result) => {
        const { status, events = [] } = result;
        
        if (status.isInBlock) {
          logger.debug(`Transaktion in Block ${status.asInBlock.toHex()}`);
          
          // Verarbeite die Events
          events.forEach(({ event: { data, method, section } }) => {
            if (section === 'system' && method === 'ExtrinsicSuccess') {
              logger.info('Transaktion erfolgreich');
              unsub();
            } else if (section === 'system' && method === 'ExtrinsicFailed') {
              const [dispatchError] = data;
              let errorInfo;
              
              if (dispatchError.isModule) {
                const decoded = this.api.registry.findMetaError(dispatchError.asModule);
                errorInfo = `${decoded.section}.${decoded.name}`;
              } else {
                errorInfo = dispatchError.toString();
              }
              
              logger.error(`Transaktion fehlgeschlagen: ${errorInfo}`);
              unsub();
            }
          });
        } else if (status.isFinalized) {
          logger.info(`Transaktion finalisiert in Block ${status.asFinalized.toHex()}`);
          unsub();
        }
      });
      
      // Warte auf die Bestätigung
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          reject(new Error('Timeout bei der Transaktionsbestätigung'));
        }, 60000); // 60 Sekunden Timeout
      });
    } catch (error) {
      logger.error('Fehler beim Senden der Transaktion', {
        error: error.message,
        transaction
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Transaktion ab
   * @param {string} txHash - Transaktions-Hash
   * @returns {Promise<Object>} Transaktionsdaten
   */
  async getTransaction(txHash) {
    try {
      logger.debug(`Rufe Transaktion mit Hash ${txHash} ab`);
      
      if (!this.api) {
        throw new Error('Keine Verbindung zum Polkadot-Netzwerk');
      }
      
      // Hole die Transaktion
      const hash = this.api.createType('Hash', txHash);
      const signedBlock = await this.api.rpc.chain.getBlock(hash);
      
      // Suche die Transaktion im Block
      const transactions = signedBlock.block.extrinsics;
      const transaction = transactions.find(tx => tx.hash.toHex() === txHash);
      
      if (!transaction) {
        throw new Error(`Transaktion mit Hash ${txHash} nicht gefunden`);
      }
      
      logger.debug(`Transaktion mit Hash ${txHash} abgerufen`);
      
      return {
        hash: transaction.hash.toHex(),
        blockHash: hash.toHex(),
        blockNumber: signedBlock.block.header.number.toNumber(),
        from: transaction.signer.toString(),
        method: transaction.method.method,
        section: transaction.method.section,
        args: transaction.method.args.map(arg => arg.toString()),
        nonce: transaction.nonce.toNumber(),
        tip: transaction.tip.toNumber(),
        isSigned: transaction.isSigned
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen der Transaktion', {
        error: error.message,
        txHash
      });
      throw error;
    }
  }
  
  /**
   * Ruft den Status einer Transaktion ab
   * @param {string} txHash - Transaktions-Hash
   * @returns {Promise<string>} Transaktionsstatus
   */
  async getTransactionStatus(txHash) {
    try {
      logger.debug(`Rufe Status der Transaktion mit Hash ${txHash} ab`);
      
      if (!this.api) {
        throw new Error('Keine Verbindung zum Polkadot-Netzwerk');
      }
      
      // Prüfe, ob die Transaktion in einem Block ist
      try {
        const hash = this.api.createType('Hash', txHash);
        await this.api.rpc.chain.getBlock(hash);
        return 'finalized';
      } catch (error) {
        // Transaktion nicht gefunden
        return 'pending';
      }
    } catch (error) {
      logger.error('Fehler beim Abrufen des Transaktionsstatus', {
        error: error.message,
        txHash
      });
      throw error;
    }
  }
  
  /**
   * Wartet auf die Bestätigung einer Transaktion
   * @param {string} txHash - Transaktions-Hash
   * @param {number} confirmations - Anzahl der erforderlichen Bestätigungen
   * @returns {Promise<Object>} Bestätigte Transaktionsdaten
   */
  async waitForTransaction(txHash, confirmations = 1) {
    try {
      logger.info(`Warte auf Bestätigung der Transaktion ${txHash} (${confirmations} Bestätigungen)`);
      
      if (!this.api) {
        throw new Error('Keine Verbindung zum Polkadot-Netzwerk');
      }
      
      // Warte auf die Bestätigung
      return new Promise((resolve, reject) => {
        let blockCount = 0;
        
        const unsub = this.api.rpc.chain.subscribeNewHeads(async (header) => {
          try {
            // Prüfe, ob die Transaktion in einem Block ist
            const status = await this.getTransactionStatus(txHash);
            
            if (status === 'finalized') {
              blockCount++;
              
              if (blockCount >= confirmations) {
                unsub();
                
                // Hole die Transaktionsdaten
                const transaction = await this.getTransaction(txHash);
                
                logger.info(`Transaktion ${txHash} bestätigt`);
                
                resolve(transaction);
              }
            }
          } catch (error) {
            unsub();
            reject(error);
          }
        });
        
        // Timeout
        setTimeout(() => {
          unsub();
          reject(new Error('Timeout bei der Transaktionsbestätigung'));
        }, 60000 * confirmations); // 60 Sekunden pro Bestätigung
      });
    } catch (error) {
      logger.error('Fehler beim Warten auf die Transaktionsbestätigung', {
        error: error.message,
        txHash
      });
      throw error;
    }
  }
  
  /**
   * Erstellt einen Smart Contract
   * @param {string} abi - ABI des Smart Contracts
   * @param {string} bytecode - Bytecode des Smart Contracts
   * @param {Array} args - Konstruktorargumente
   * @returns {Promise<Object>} Ergebnis der Vertragserstellung
   */
  async deployContract(abi, bytecode, args = []) {
    try {
      logger.info('Erstelle Smart Contract');
      
      if (!this.api) {
        throw new Error('Keine Verbindung zum Polkadot-Netzwerk');
      }
      
      if (!this.account) {
        throw new Error('Kein Konto verfügbar');
      }
      
      // Prüfe, ob wir mit Moonbeam verbunden sind
      if (!this.parachainApis.moonbeam) {
        throw new Error('Keine Verbindung zu Moonbeam (EVM-kompatible Parachain)');
      }
      
      // Verwende die Moonbeam-API für EVM-Kompatibilität
      const api = this.parachainApis.moonbeam;
      
      // Erstelle den Contract
      const { Contract } = await import('@polkadot/api-contract');
      const contract = new Contract(api, abi, bytecode);
      
      // Deploye den Contract
      const gasLimit = this.options.contractOptions.gasLimit;
      const storageDepositLimit = this.options.contractOptions.storageDepositLimit;
      
      const tx = contract.tx.new({ gasLimit, storageDepositLimit }, ...args);
      
      // Sende die Transaktion
      const result = await tx.signAndSend(this.account);
      
      // Warte auf die Bestätigung
      const address = await new Promise((resolve, reject) => {
        const unsub = result.subscribe(async (result) => {
          const { status, events = [] } = result;
          
          if (status.isInBlock || status.isFinalized) {
            // Suche nach dem Instantiated-Event
            for (const { event } of events) {
              if (api.events.contracts && event.section === 'contracts' && event.method === 'Instantiated') {
                const [deployer, contractAddress] = event.data;
                
                unsub();
                resolve(contractAddress.toString());
                break;
              }
            }
          }
        });
        
        // Timeout
        setTimeout(() => {
          unsub();
          reject(new Error('Timeout bei der Vertragsinstanziierung'));
        }, 60000); // 60 Sekunden Timeout
      });
      
      logger.info(`Smart Contract erstellt: ${address}`);
      
      // Speichere den Contract in der Map
      this.contracts.set(address, contract);
      
      return {
        address,
        transactionHash: result.hash.toHex(),
        abi
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen des Smart Contracts', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Instanz eines Smart Contracts ab
   * @param {string} address - Adresse des Smart Contracts
   * @param {string} abi - ABI des Smart Contracts
   * @returns {Promise<Object>} Smart Contract-Instanz
   */
  async getContract(address, abi) {
    try {
      logger.debug(`Rufe Smart Contract mit Adresse ${address} ab`);
      
      // Prüfe, ob der Contract bereits in der Map ist
      if (this.contracts.has(address)) {
        return this.contracts.get(address);
      }
      
      // Prüfe, ob wir mit Moonbeam verbunden sind
      if (!this.parachainApis.moonbeam) {
        throw new Error('Keine Verbindung zu Moonbeam (EVM-kompatible Parachain)');
      }
      
      // Verwende die Moonbeam-API für EVM-Kompatibilität
      const api = this.parachainApis.moonbeam;
      
      // Erstelle eine neue Contract-Instanz
      const { Contract } = await import('@polkadot/api-contract');
      const contract = new Contract(api, abi, address);
      
      // Speichere den Contract in der Map
      this.contracts.set(address, contract);
      
      return contract;
    } catch (error) {
      logger.error('Fehler beim Abrufen des Smart Contracts', {
        error: error.message,
        address
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Methode eines Smart Contracts auf (read-only)
   * @param {string} address - Adresse des Smart Contracts
   * @param {string} abi - ABI des Smart Contracts
   * @param {string} method - Name der Methode
   * @param {Array} args - Methodenargumente
   * @returns {Promise<any>} Ergebnis des Methodenaufrufs
   */
  async callContractMethod(address, abi, method, args = []) {
    try {
      logger.debug(`Rufe Methode ${method} des Smart Contracts ${address} auf`);
      
      // Hole den Contract
      const contract = await this.getContract(address, abi);
      
      // Rufe die Methode auf
      const result = await contract.query[method](this.account.address, {}, ...args);
      
      // Prüfe auf Fehler
      if (result.result.isErr) {
        throw new Error(`Fehler beim Aufruf der Methode ${method}: ${result.result.asErr.toString()}`);
      }
      
      // Extrahiere das Ergebnis
      const value = result.output.toHuman();
      
      return value;
    } catch (error) {
      logger.error('Fehler beim Aufrufen der Contract-Methode', {
        error: error.message,
        address,
        method,
        args
      });
      throw error;
    }
  }
  
  /**
   * Führt eine Methode eines Smart Contracts aus (Transaktion)
   * @param {string} address - Adresse des Smart Contracts
   * @param {string} abi - ABI des Smart Contracts
   * @param {string} method - Name der Methode
   * @param {Array} args - Methodenargumente
   * @param {Object} options - Transaktionsoptionen
   * @returns {Promise<Object>} Transaktionsergebnis
   */
  async executeContractMethod(address, abi, method, args = [], options = {}) {
    try {
      logger.info(`Führe Methode ${method} des Smart Contracts ${address} aus`);
      
      if (!this.account) {
        throw new Error('Kein Konto verfügbar');
      }
      
      // Hole den Contract
      const contract = await this.getContract(address, abi);
      
      // Bereite die Transaktionsoptionen vor
      const gasLimit = options.gasLimit || this.options.contractOptions.gasLimit;
      const storageDepositLimit = options.storageDepositLimit || this.options.contractOptions.storageDepositLimit;
      const value = options.value || 0;
      
      // Führe die Methode aus
      const tx = contract.tx[method]({ gasLimit, storageDepositLimit, value }, ...args);
      
      // Sende die Transaktion
      const result = await tx.signAndSend(this.account);
      
      // Warte auf die Bestätigung
      const events = await new Promise((resolve, reject) => {
        const unsub = result.subscribe((result) => {
          const { status, events = [] } = result;
          
          if (status.isInBlock || status.isFinalized) {
            unsub();
            resolve(events);
          }
        });
        
        // Timeout
        setTimeout(() => {
          unsub();
          reject(new Error('Timeout bei der Transaktionsbestätigung'));
        }, 60000); // 60 Sekunden Timeout
      });
      
      logger.info(`Methode ${method} des Smart Contracts ${address} erfolgreich ausgeführt`);
      
      // Extrahiere die Events
      const formattedEvents = events.map(({ event }) => ({
        name: `${event.section}.${event.method}`,
        data: event.data.toHuman()
      }));
      
      return {
        hash: result.hash.toHex(),
        events: formattedEvents,
        status: 'success'
      };
    } catch (error) {
      logger.error('Fehler beim Ausführen der Contract-Methode', {
        error: error.message,
        address,
        method,
        args
      });
      throw error;
    }
  }
  
  /**
   * Prägt einen NFT
   * @param {string} contractAddress - Adresse des NFT-Vertrags
   * @param {string} to - Empfängeradresse
   * @param {string} tokenURI - URI der Token-Metadaten
   * @param {Object} options - Prägungsoptionen
   * @returns {Promise<Object>} Prägungsergebnis
   */
  async mintNFT(contractAddress, to, tokenURI, options = {}) {
    try {
      logger.info(`Präge NFT für ${to} mit URI ${tokenURI}`);
      
      // Führe die mint-Methode des NFT-Vertrags aus
      const result = await this.executeContractMethod(
        contractAddress,
        options.abi || require('../../contracts/abis/ERC721.json'),
        'mint',
        [to, tokenURI],
        options
      );
      
      // Extrahiere die Token-ID aus dem Event
      const transferEvent = result.events.find(event => event.name === 'contracts.Transfer');
      const tokenId = transferEvent ? transferEvent.data[2] : null;
      
      logger.info(`NFT erfolgreich geprägt mit Token-ID ${tokenId}`);
      
      return {
        tokenId,
        contractAddress,
        owner: to,
        tokenURI,
        transactionHash: result.hash
      };
    } catch (error) {
      logger.error('Fehler beim Prägen des NFT', {
        error: error.message,
        contractAddress,
        to,
        tokenURI
      });
      throw error;
    }
  }
  
  /**
   * Prägt einen IP-NFT
   * @param {string} contractAddress - Adresse des IP-NFT-Vertrags
   * @param {string} to - Empfängeradresse
   * @param {string} tokenURI - URI der Token-Metadaten
   * @param {Object} options - Prägungsoptionen
   * @returns {Promise<Object>} Prägungsergebnis
   */
  async mintIPNFT(contractAddress, to, tokenURI, options = {}) {
    try {
      logger.info(`Präge IP-NFT für ${to} mit URI ${tokenURI}`);
      
      // Extrahiere die Optionen
      const {
        royaltyPercentage = 0,
        contentCid,
        legalAgreementCid,
        contentType,
        contentId
      } = options;
      
      // Führe die mintIPNFT-Methode des IP-NFT-Vertrags aus
      const result = await this.executeContractMethod(
        contractAddress,
        options.abi || require('../../contracts/abis/IPNFTContract.json'),
        'mintIPNFT',
        [
          to,
          tokenURI,
          royaltyPercentage * 100, // Konvertiere Prozent in Basispunkte (1% = 100 Basispunkte)
          contentCid || '',
          legalAgreementCid || '',
          {
            contentType: contentType || '',
            contentId: contentId || '',
            timestamp: Math.floor(Date.now() / 1000)
          }
        ],
        options
      );
      
      // Extrahiere die Token-ID aus dem Event
      const transferEvent = result.events.find(event => event.name === 'contracts.Transfer');
      const tokenId = transferEvent ? transferEvent.data[2] : null;
      
      logger.info(`IP-NFT erfolgreich geprägt mit Token-ID ${tokenId}`);
      
      return {
        tokenId,
        contractAddress,
        owner: to,
        tokenURI,
        royaltyPercentage,
        contentCid,
        legalAgreementCid,
        transactionHash: result.hash
      };
    } catch (error) {
      logger.error('Fehler beim Prägen des IP-NFT', {
        error: error.message,
        contractAddress,
        to,
        tokenURI
      });
      throw error;
    }
  }
  
  /**
   * Fractionalisiert einen IP-NFT
   * @param {string} contractAddress - Adresse des IP-NFT-Vertrags
   * @param {string} tokenId - Token-ID des IP-NFT
   * @param {Object} options - Fractionalisierungsoptionen
   * @returns {Promise<Object>} Fractionalisierungsergebnis
   */
  async fractionalizeIPNFT(contractAddress, tokenId, options = {}) {
    try {
      logger.info(`Fractionalisiere IP-NFT mit Token-ID ${tokenId}`);
      
      // Extrahiere die Optionen
      const {
        name = `IP-Shares-${tokenId}`,
        symbol = `IPS-${tokenId}`,
        initialSupply = 1000000,
        ownerAddress
      } = options;
      
      // Führe die fractionalizeIPNFT-Methode des IP-NFT-Vertrags aus
      const result = await this.executeContractMethod(
        contractAddress,
        options.abi || require('../../contracts/abis/IPNFTContract.json'),
        'fractionalizeIPNFT',
        [
          tokenId,
          name,
          symbol,
          initialSupply,
          ownerAddress || this.account.address
        ],
        options
      );
      
      // Extrahiere die Token-Adresse aus dem Event
      const fractionalizationEvent = result.events.find(event => event.name === 'contracts.IPNFTFractionalized');
      const tokenAddress = fractionalizationEvent ? fractionalizationEvent.data[1] : null;
      
      logger.info(`IP-NFT erfolgreich fractionalisiert mit Token-Adresse ${tokenAddress}`);
      
      return {
        tokenId,
        contractAddress,
        tokenAddress,
        name,
        symbol,
        initialSupply,
        transactionHash: result.hash
      };
    } catch (error) {
      logger.error('Fehler beim Fractionalisieren des IP-NFT', {
        error: error.message,
        contractAddress,
        tokenId
      });
      throw error;
    }
  }
  
  /**
   * Erstellt einen Governance-Token für einen IP-NFT
   * @param {string} contractAddress - Adresse des IP-NFT-Vertrags
   * @param {string} tokenId - Token-ID des IP-NFT
   * @param {Object} options - Token-Erstellungsoptionen
   * @returns {Promise<Object>} Token-Erstellungsergebnis
   */
  async createIPGovernanceToken(contractAddress, tokenId, options = {}) {
    try {
      logger.info(`Erstelle Governance-Token für IP-NFT mit Token-ID ${tokenId}`);
      
      // Extrahiere die Optionen
      const {
        name = `IP-Gov-${tokenId}`,
        symbol = `IPG-${tokenId}`,
        initialSupply = 100000,
        ownerAddress
      } = options;
      
      // Führe die createIPGovernanceToken-Methode des IP-NFT-Vertrags aus
      const result = await this.executeContractMethod(
        contractAddress,
        options.abi || require('../../contracts/abis/IPNFTContract.json'),
        'createIPGovernanceToken',
        [
          tokenId,
          name,
          symbol,
          initialSupply,
          ownerAddress || this.account.address
        ],
        options
      );
      
      // Extrahiere die Token-Adresse aus dem Event
      const governanceEvent = result.events.find(event => event.name === 'contracts.IPGovernanceTokenCreated');
      const tokenAddress = governanceEvent ? governanceEvent.data[1] : null;
      
      logger.info(`Governance-Token erfolgreich erstellt mit Token-Adresse ${tokenAddress}`);
      
      return {
        tokenId,
        contractAddress,
        tokenAddress,
        name,
        symbol,
        initialSupply,
        transactionHash: result.hash
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen des Governance-Tokens', {
        error: error.message,
        contractAddress,
        tokenId
      });
      throw error;
    }
  }
  
  /**
   * Registriert eine Berechtigung für einen IP-NFT
   * @param {string} contractAddress - Adresse des IP-NFT-Vertrags
   * @param {string} tokenId - Token-ID des IP-NFT
   * @param {string} grantee - Adresse des Berechtigungsempfängers
   * @param {string} permissionType - Art der Berechtigung
   * @param {Object} options - Berechtigungsoptionen
   * @returns {Promise<Object>} Registrierungsergebnis
   */
  async registerIPNFTPermission(contractAddress, tokenId, grantee, permissionType, options = {}) {
    try {
      logger.info(`Registriere Berechtigung ${permissionType} für IP-NFT ${tokenId} an ${grantee}`);
      
      // Extrahiere die Optionen
      const {
        expirationDate,
        metadata
      } = options;
      
      // Berechne das Ablaufdatum in Sekunden seit der Epoche
      const expirationTimestamp = expirationDate ? Math.floor(new Date(expirationDate).getTime() / 1000) : 0;
      
      // Führe die registerPermission-Methode des IP-NFT-Vertrags aus
      const result = await this.executeContractMethod(
        contractAddress,
        options.abi || require('../../contracts/abis/IPNFTContract.json'),
        'registerPermission',
        [
          tokenId,
          grantee,
          permissionType,
          expirationTimestamp,
          JSON.stringify(metadata || {})
        ],
        options
      );
      
      logger.info(`Berechtigung erfolgreich registriert`);
      
      return {
        tokenId,
        contractAddress,
        grantee,
        permissionType,
        expirationDate: expirationDate || null,
        transactionHash: result.hash
      };
    } catch (error) {
      logger.error('Fehler beim Registrieren der Berechtigung', {
        error: error.message,
        contractAddress,
        tokenId,
        grantee,
        permissionType
      });
      throw error;
    }
  }
  
  /**
   * Widerruft eine Berechtigung für einen IP-NFT
   * @param {string} contractAddress - Adresse des IP-NFT-Vertrags
   * @param {string} tokenId - Token-ID des IP-NFT
   * @param {string} grantee - Adresse des Berechtigungsempfängers
   * @param {string} permissionType - Art der Berechtigung
   * @returns {Promise<Object>} Widerrufsergebnis
   */
  async revokeIPNFTPermission(contractAddress, tokenId, grantee, permissionType) {
    try {
      logger.info(`Widerrufe Berechtigung ${permissionType} für IP-NFT ${tokenId} von ${grantee}`);
      
      // Führe die revokePermission-Methode des IP-NFT-Vertrags aus
      const result = await this.executeContractMethod(
        contractAddress,
        require('../../contracts/abis/IPNFTContract.json'),
        'revokePermission',
        [
          tokenId,
          grantee,
          permissionType
        ]
      );
      
      logger.info(`Berechtigung erfolgreich widerrufen`);
      
      return {
        tokenId,
        contractAddress,
        grantee,
        permissionType,
        transactionHash: result.hash
      };
    } catch (error) {
      logger.error('Fehler beim Widerrufen der Berechtigung', {
        error: error.message,
        contractAddress,
        tokenId,
        grantee,
        permissionType
      });
      throw error;
    }
  }
}/**
 * @fileoverview Polkadot-Adapter für die Interaktion mit dem Polkadot-Netzwerk
 * 
 * Dieser Adapter implementiert das BlockchainAdapter-Interface für Polkadot und
 * ermöglicht die Interaktion mit dem Polkadot-Netzwerk und seinen Parachains.
 * Er unterstützt Cross-Chain-Funktionalität und ist optimiert für die Interoperabilität.
 */

import { ApiPromise, WsProvider, Keyring } from '@polkadot/api';
import { cryptoWaitReady } from '@polkadot/util-crypto';
import { BlockchainAdapter } from './BlockchainAdapter.js';
import { logger } from '../../utils/logger.js';

/**
 * Polkadot-Adapter für die Interaktion mit dem Polkadot-Netzwerk
 */
export class PolkadotAdapter extends BlockchainAdapter {
  /**
   * Erstellt eine neue Instanz des PolkadotAdapter
   * @param {Object} options - Konfigurationsoptionen
   * @param {string} options.providerUrl - URL des Polkadot-Providers
   * @param {string} options.mnemonic - Mnemonic für die Schlüsselerstellung
   * @param {string} options.seed - Seed für die Schlüsselerstellung
   * @param {string} options.privateKey - Private Key für Transaktionen
   * @param {string} options.network - Netzwerk (polkadot, kusama, westend, etc.)
   * @param {Object} options.parachains - Konfiguration für Parachains
   * @param {Object} options.contractOptions - Optionen für Smart Contracts
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      providerUrl: options.providerUrl || process.env.POLKADOT_PROVIDER_URL || 'wss://rpc.polkadot.io',
      mnemonic: options.mnemonic || process.env.POLKADOT_MNEMONIC,
      seed: options.seed || process.env.POLKADOT_SEED,
      privateKey: options.privateKey || process.env.POLKADOT_PRIVATE_KEY,
      network: options.network || process.env.POLKADOT_NETWORK || 'polkadot',
      parachains: options.parachains || {
        moonbeam: {
          url: process.env.MOONBEAM_PROVIDER_URL || 'wss://moonbeam.api.onfinality.io/public-ws',
          enabled: true
        },
        acala: {
          url: process.env.ACALA_PROVIDER_URL || 'wss://acala-rpc.dwellir.com',
          enabled: true
        }
      },
      contractOptions: options.contractOptions || {
        gasLimit: 3000000,
        storageDepositLimit: null
      },
      ...options
    };
    
    this.api = null;
    this.parachainApis = {};
    this.keyring = null;
    this.account = null;
    this.contracts = new Map();
    
    logger.info('PolkadotAdapter initialisiert');
  }
  
  /**
   * Initialisiert den Polkadot-Adapter
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      logger.info('Initialisiere PolkadotAdapter');
      
      // Warte auf die Initialisierung der Kryptofunktionen
      await cryptoWaitReady();
      
      // Verbinde mit dem Polkadot-Netzwerk
      await this.connect();
      
      // Initialisiere den Keyring
      await this.initializeKeyring();
      
      // Verbinde mit den konfigurierten Parachains
      await this.connectToParachains();
      
      logger.info('PolkadotAdapter erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des PolkadotAdapter', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Verbindet mit dem Polkadot-Netzwerk
   * @param {Object} options - Verbindungsoptionen
   * @returns {Promise<void>}
   */
  async connect(options = {}) {
    try {
      logger.info(`Verbinde mit Polkadot-Netzwerk: ${this.options.network}`);
      
      // Erstelle den Provider
      const provider = new WsProvider(this.options.providerUrl);
      
      // Erstelle die API
      this.api = await ApiPromise.create({ provider });
      
      // Prüfe die Verbindung
      const chain = await this.api.rpc.system.chain();
      const version = await this.api.rpc.system.version();
      
      logger.debug(`Verbunden mit ${chain} (${version})`);
      
      logger.info('Erfolgreich mit Polkadot-Netzwerk verbunden');
    } catch (error) {
      logger.error('Fehler beim Verbinden mit dem Polkadot-Netzwerk', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Initialisiert den Keyring und das Konto
   * @returns {Promise<void>}
   */
  async initializeKeyring() {
    try {
      logger.info('Initialisiere Keyring');
      
      // Erstelle den Keyring
      this.keyring = new Keyring({ type: 'sr25519' });
      
      // Erstelle das Konto basierend auf den verfügbaren Optionen
      if (this.options.mnemonic) {
        this.account = this.keyring.addFromMnemonic(this.options.mnemonic);
        logger.debug(`Konto aus Mnemonic erstellt: ${this.account.address}`);
      } else if (this.options.seed) {
        this.account = this.keyring.addFromSeed(this.options.seed);
        logger.debug(`Konto aus Seed erstellt: ${this.account.address}`);
      } else if (this.options.privateKey) {
        this.account = this.keyring.addFromUri(this.options.privateKey);
        logger.debug(`Konto aus Private Key erstellt: ${this.account.address}`);
      } else {
        logger.warn('Kein Mnemonic, Seed oder Private Key angegeben, nur Lesezugriff möglich');
      }
      
      logger.info('Keyring erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des Keyring', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Verbindet mit den konfigurierten Parachains
   * @returns {Promise<void>}
   */
  async connectToParachains() {
    try {
      logger.info('Verbinde mit Parachains');
      
      // Verbinde mit jeder konfigurierten Parachain
      for (const [name, config] of Object.entries(this.options.parachains)) {
        if (config.enabled) {
          try {
            logger.debug(`Verbinde mit Parachain: ${name}`);
            
            // Erstelle den Provider
            const provider = new WsProvider(config.url);
            
            // Erstelle die API
            const api = await ApiPromise.create({ provider });
            
            // Speichere die API
            this.parachainApis[name] = api;
            
            // Prüfe die Verbindung
            const chain = await api.rpc.system.chain();
            
            logger.debug(`Verbunden mit Parachain ${name} (${chain})`);
          } catch (error) {
            logger.error(`Fehler beim Verbinden mit Parachain ${name}`, {
              error: error.message
            });
          }
        }
      }
      
      logger.info(`Erfolgreich mit ${Object.keys(this.parachainApis).length} Parachains verbunden`);
    } catch (error) {
      logger.error('Fehler beim Verbinden mit Parachains', {
        error: error.message
      });
      // Wir werfen hier keinen Fehler, da die Hauptfunktionalität auch ohne Parachains funktionieren sollte
    }
  }
  
  /**
   * Trennt die Verbindung zum Polkadot-Netzwerk
   * @returns {Promise<void>}
   */
  async disconnect() {
    try {
      logger.info('Trenne Verbindung zum Polkadot-Netzwerk');
      
      // Trenne die Verbindung zu allen Parachains
      for (const [name, api] of Object.entries(this.parachainApis)) {
        try {
          await api.disconnect();
          logger.debug(`Verbindung zu Parachain ${name} getrennt`);
        } catch (error) {
          logger.warn(`Fehler beim Trennen der Verbindung zu Parachain ${name}`, {
            error: error.message
          });
        }
      }
      
      // Trenne die Verbindung zum Hauptnetzwerk
      if (this.api) {
        await this.api.disconnect();
      }
      
      // Lösche alle Referenzen
      this.api = null;
      this.parachainApis = {};
      this.account = null;
      this.contracts.clear();
      
      logger.info('Verbindung zum Polkadot-Netzwerk getrennt');
    } catch (error) {
      logger.error('Fehler beim Trennen der Verbindung zum Polkadot-Netzwerk', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Verbindung zum Polkadot-Netzwerk besteht
   * @returns {Promise<boolean>} True, wenn eine Verbindung besteht
   */
  async isConnected() {
    try {
      if (!this.api) {
        return false;
      }
      
      // Prüfe, ob die API verbunden ist
      return this.api.isConnected;
    } catch (error) {
      logger.debug('Keine Verbindung zum Polkadot-Netzwerk');
      return false;
    }
  }
  
  /**
   * Ruft die aktuelle Blocknummer ab
   * @returns {Promise<number>} Aktuelle Blocknummer
   */
  async getBlockNumber() {
    try {
      logger.debug('Rufe aktuelle Blocknummer ab');
      
      if (!this.api) {
        throw new Error('Keine Verbindung zum Polkadot-Netzwerk');
      }
      
      // Hole den aktuellen Header
      const header = await this.api.rpc.chain.getHeader();
      
      // Extrahiere die Blocknummer
      const blockNumber = header.number.toNumber();
      
      logger.debug(`Aktuelle Blocknummer: ${blockNumber}`);
      
      return blockNumber;
    } catch (error) {
      logger.error('Fehler beim Abrufen der aktuellen Blocknummer', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft den Kontostand einer Adresse ab
   * @param {string} address - Adresse
   * @param {Object} options - Abfrageoptionen
   * @returns {Promise<string>} Kontostand
   */
  async getBalance(address, options = {}) {
    try {
      logger.debug(`Rufe Kontostand für Adresse ${address} ab`);
      
      if (!this.api) {
        throw new Error('Keine Verbindung zum Polkadot-Netzwerk');
      }
      
      // Hole den Kontostand
      const { data: balance } = await this.api.query.system.account(address);
      
      // Konvertiere den Kontostand in eine lesbare Form
      const formattedBalance = this.api.createType('Balance', balance.free).toHuman();
      
      logger.debug(`Kontostand für Adresse ${address}: ${formattedBalance}`);
      
      return formattedBalance;
    } catch (error) {
      logger.error('Fehler beim Abrufen des Kontostands', {
        error: error.message,
        address
      });
      throw error;
    }
  }
  
  /**
   * Sendet eine Transaktion
   * @param {Object} transaction - Transaktionsdaten
   * @returns {Promise<Object>} Transaktionsergebnis
   */
  async sendTransaction(transaction) {
    try {
      logger.info('Sende Transaktion');
      
      if (!this.api) {
        throw new Error('Keine Verbindung zum Polkadot-Netzwerk');
      }
      
      if (!this.account) {
        throw new Error('Kein Konto verfügbar');
      }
      
      // Extrahiere die Transaktionsdaten
      const { to, value, method, section, args = [] } = transaction;
      
      // Erstelle die Transaktion
      let tx;
      
      if (to && value) {
        // Einfache Überweisung
        tx = this.api.tx.balances.transfer(to, value);
      } else if (method && section) {
        // Spezifische Methode
        tx = this.api.tx[section][method](...args);
      } else {
        throw new Error('Ungültige Transaktionsdaten');
      }
      
      // Sende die Transaktion
      const unsub = await tx.signAndSend(this.account, { nonce: -1 }, (result) => {
        const { status, events = [] } = result;
        
        if (status.isInBlock) {
          logger.debug(`Transaktion in Block ${status.asInBlock.toHex()}`);
          
          // Verarbeite die Events
          events.forEach(({ event: { data, method, section } }) => {
            if (section === 'system' && method === 'ExtrinsicSuccess') {
              logger.info('Transaktion erfolgreich');
              unsub();
            } else if (section === 'system' && method === 'ExtrinsicFailed') {
              const [dispatchError] = data;
              let errorInfo;
              
              if (dispatchError.isModule) {
                const decoded = this.api.registry.findMetaError(dispatchError.asModule);
                errorInfo = `${decoded.section}.${decoded.name}`;
              } else {
                errorInfo = dispatchError.toString();
              }
              
              logger.error(`Transaktion fehlgeschlagen: ${errorInfo}`);
              unsub();
            }
          });
        } else if (status.isFinalized) {
          logger.info(`Transaktion finalisiert in Block ${status.asFinalized.toHex()}`);
          unsub();
        }
      });
      
      // Warte auf die Bestätigung
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          reject(new Error('Timeout bei der Transaktionsbestätigung'));
        }, 60000); // 60 Sekunden Timeout
      });
    } catch (error) {
      logger.error('Fehler beim Senden der Transaktion', {
        error: error.message,
        transaction
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Transaktion ab
   * @param {string} txHash - Transaktions-Hash
   * @returns {Promise<Object>} Transaktionsdaten
   */
  async getTransaction(txHash) {
    try {
      logger.debug(`Rufe Transaktion mit Hash ${txHash} ab`);
      
      if (!this.api) {
        throw new Error('Keine Verbindung zum Polkadot-Netzwerk');
      }
      
      // Hole die Transaktion
      const hash = this.api.createType('Hash', txHash);
      const signedBlock = await this.api.rpc.chain.getBlock(hash);
      
      // Suche die Transaktion im Block
      const transactions = signedBlock.block.extrinsics;
      const transaction = transactions.find(tx => tx.hash.toHex() === txHash);
      
      if (!transaction) {
        throw new Error(`Transaktion mit Hash ${txHash} nicht gefunden`);
      }
      
      logger.debug(`Transaktion mit Hash ${txHash} abgerufen`);
      
      return {
        hash: transaction.hash.toHex(),
        blockHash: hash.toHex(),
        blockNumber: signedBlock.block.header.number.toNumber(),
        from: transaction.signer.toString(),
        method: transaction.method.method,
        section: transaction.method.section,
        args: transaction.method.args.map(arg => arg.toString()),
        nonce: transaction.nonce.toNumber(),
        tip: transaction.tip.toNumber(),
        isSigned: transaction.isSigned
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen der Transaktion', {
        error: error.message,
        txHash
      });
      throw error;
    }
  }
  
  /**
   * Ruft den Status einer Transaktion ab
   * @param {string} txHash - Transaktions-Hash
   * @returns {Promise<string>} Transaktionsstatus
   */
  async getTransactionStatus(txHash) {
    try {
      logger.debug(`Rufe Status der Transaktion mit Hash ${txHash} ab`);
      
      if (!this.api) {
        throw new Error('Keine Verbindung zum Polkadot-Netzwerk');
      }
      
      // Prüfe, ob die Transaktion in einem Block ist
      try {
        const hash = this.api.createType('Hash', txHash);
        await this.api.rpc.chain.getBlock(hash);
        return 'finalized';
      } catch (error) {
        // Transaktion nicht gefunden
        return 'pending';
      }
    } catch (error) {
      logger.error('Fehler beim Abrufen des Transaktionsstatus', {
        error: error.message,
        txHash
      });
      throw error;
    }
  }
  
  /**
   * Wartet auf die Bestätigung einer Transaktion
   * @param {string} txHash - Transaktions-Hash
   * @param {number} confirmations - Anzahl der erforderlichen Bestätigungen
   * @returns {Promise<Object>} Bestätigte Transaktionsdaten
   */
  async waitForTransaction(txHash, confirmations = 1) {
    try {
      logger.info(`Warte auf Bestätigung der Transaktion ${txHash} (${confirmations} Bestätigungen)`);
      
      if (!this.api) {
        throw new Error('Keine Verbindung zum Polkadot-Netzwerk');
      }
      
      // Warte auf die Bestätigung
      return new Promise((resolve, reject) => {
        let blockCount = 0;
        
        const unsub = this.api.rpc.chain.subscribeNewHeads(async (header) => {
          try {
            // Prüfe, ob die Transaktion in einem Block ist
            const status = await this.getTransactionStatus(txHash);
            
            if (status === 'finalized') {
              blockCount++;
              
              if (blockCount >= confirmations) {
                unsub();
                
                // Hole die Transaktionsdaten
                const transaction = await this.getTransaction(txHash);
                
                logger.info(`Transaktion ${txHash} bestätigt`);
                
                resolve(transaction);
              }
            }
          } catch (error) {
            unsub();
            reject(error);
          }
        });
        
        // Timeout
        setTimeout(() => {
          unsub();
          reject(new Error('Timeout bei der Transaktionsbestätigung'));
        }, 60000 * confirmations); // 60 Sekunden pro Bestätigung
      });
    } catch (error) {
      logger.error('Fehler beim Warten auf die Transaktionsbestätigung', {
        error: error.message,
        txHash
      });
      throw error;
    }
  }
  
  /**
   * Erstellt einen Smart Contract
   * @param {string} abi - ABI des Smart Contracts
   * @param {string} bytecode - Bytecode des Smart Contracts
   * @param {Array} args - Konstruktorargumente
   * @returns {Promise<Object>} Ergebnis der Vertragserstellung
   */
  async deployContract(abi, bytecode, args = []) {
    try {
      logger.info('Erstelle Smart Contract');
      
      if (!this.api) {
        throw new Error('Keine Verbindung zum Polkadot-Netzwerk');
      }
      
      if (!this.account) {
        throw new Error('Kein Konto verfügbar');
      }
      
      // Prüfe, ob wir mit Moonbeam verbunden sind
      if (!this.parachainApis.moonbeam) {
        throw new Error('Keine Verbindung zu Moonbeam (EVM-kompatible Parachain)');
      }
      
      // Verwende die Moonbeam-API für EVM-Kompatibilität
      const api = this.parachainApis.moonbeam;
      
      // Erstelle den Contract
      const { Contract } = await import('@polkadot/api-contract');
      const contract = new Contract(api, abi, bytecode);
      
      // Deploye den Contract
      const gasLimit = this.options.contractOptions.gasLimit;
      const storageDepositLimit = this.options.contractOptions.storageDepositLimit;
      
      const tx = contract.tx.new({ gasLimit, storageDepositLimit }, ...args);
      
      // Sende die Transaktion
      const result = await tx.signAndSend(this.account);
      
      // Warte auf die Bestätigung
      const address = await new Promise((resolve, reject) => {
        const unsub = result.subscribe(async (result) => {
          const { status, events = [] } = result;
          
          if (status.isInBlock || status.isFinalized) {
            // Suche nach dem Instantiated-Event
            for (const { event } of events) {
              if (api.events.contracts && event.section === 'contracts' && event.method === 'Instantiated') {
                const [deployer, contractAddress] = event.data;
                
                unsub();
                resolve(contractAddress.toString());
                break;
              }
            }
          }
        });
        
        // Timeout
        setTimeout(() => {
          unsub();
          reject(new Error('Timeout bei der Vertragsinstanziierung'));
        }, 60000); // 60 Sekunden Timeout
      });
      
      logger.info(`Smart Contract erstellt: ${address}`);
      
      // Speichere den Contract in der Map
      this.contracts.set(address, contract);
      
      return {
        address,
        transactionHash: result.hash.toHex(),
        abi
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen des Smart Contracts', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Instanz eines Smart Contracts ab
   * @param {string} address - Adresse des Smart Contracts
   * @param {string} abi - ABI des Smart Contracts
   * @returns {Promise<Object>} Smart Contract-Instanz
   */
  async getContract(address, abi) {
    try {
      logger.debug(`Rufe Smart Contract mit Adresse ${address} ab`);
      
      // Prüfe, ob der Contract bereits in der Map ist
      if (this.contracts.has(address)) {
        return this.contracts.get(address);
      }
      
      // Prüfe, ob wir mit Moonbeam verbunden sind
      if (!this.parachainApis.moonbeam) {
        throw new Error('Keine Verbindung zu Moonbeam (EVM-kompatible Parachain)');
      }
      
      // Verwende die Moonbeam-API für EVM-Kompatibilität
      const api = this.parachainApis.moonbeam;
      
      // Erstelle eine neue Contract-Instanz
      const { Contract } = await import('@polkadot/api-contract');
      const contract = new Contract(api, abi, address);
      
      // Speichere den Contract in der Map
      this.contracts.set(address, contract);
      
      return contract;
    } catch (error) {
      logger.error('Fehler beim Abrufen des Smart Contracts', {
        error: error.message,
        address
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Methode eines Smart Contracts auf (read-only)
   * @param {string} address - Adresse des Smart Contracts
   * @param {string} abi - ABI des Smart Contracts
   * @param {string} method - Name der Methode
   * @param {Array} args - Methodenargumente
   * @returns {Promise<any>} Ergebnis des Methodenaufrufs
   */
  async callContractMethod(address, abi, method, args = []) {
    try {
      logger.debug(`Rufe Methode ${method} des Smart Contracts ${address} auf`);
      
      // Hole den Contract
      const contract = await this.getContract(address, abi);
      
      // Rufe die Methode auf
      const result = await contract.query[method](this.account.address, {}, ...args);
      
      // Prüfe auf Fehler
      if (result.result.isErr) {
        throw new Error(`Fehler beim Aufruf der Methode ${method}: ${result.result.asErr.toString()}`);
      }
      
      // Extrahiere das Ergebnis
      const value = result.output.toHuman();
      
      return value;
    } catch (error) {
      logger.error('Fehler beim Aufrufen der Contract-Methode', {
        error: error.message,
        address,
        method,
        args
      });
      throw error;
    }
  }
  
  /**
   * Führt eine Methode eines Smart Contracts aus (Transaktion)
   * @param {string} address - Adresse des Smart Contracts
   * @param {string} abi - ABI des Smart Contracts
   * @param {string} method - Name der Methode
   * @param {Array} args - Methodenargumente
   * @param {Object} options - Transaktionsoptionen
   * @returns {Promise<Object>} Transaktionsergebnis
   */
  async executeContractMethod(address, abi, method, args = [], options = {}) {
    try {
      logger.info(`Führe Methode ${method} des Smart Contracts ${address} aus`);
      
      if (!this.account) {
        throw new Error('Kein Konto verfügbar');
      }
      
      // Hole den Contract
      const contract = await this.getContract(address, abi);
      
      // Bereite die Transaktionsoptionen vor
      const gasLimit = options.gasLimit || this.options.contractOptions.gasLimit;
      const storageDepositLimit = options.storageDepositLimit || this.options.contractOptions.storageDepositLimit;
      const value = options.value || 0;
      
      // Führe die Methode aus
      const tx = contract.tx[method]({ gasLimit, storageDepositLimit, value }, ...args);
      
      // Sende die Transaktion
      const result = await tx.signAndSend(this.account);
      
      // Warte auf die Bestätigung
      const events = await new Promise((resolve, reject) => {
        const unsub = result.subscribe((result) => {
          const { status, events = [] } = result;
          
          if (status.isInBlock || status.isFinalized) {
            unsub();
            resolve(events);
          }
        });
        
        // Timeout
        setTimeout(() => {
          unsub();
          reject(new Error('Timeout bei der Transaktionsbestätigung'));
        }, 60000); // 60 Sekunden Timeout
      });
      
      logger.info(`Methode ${method} des Smart Contracts ${address} erfolgreich ausgeführt`);
      
      // Extrahiere die Events
      const formattedEvents = events.map(({ event }) => ({
        name: `${event.section}.${event.method}`,
        data: event.data.toHuman()
      }));
      
      return {
        hash: result.hash.toHex(),
        events: formattedEvents,
        status: 'success'
      };
    } catch (error) {
      logger.error('Fehler beim Ausführen der Contract-Methode', {
        error: error.message,
        address,
        method,
        args
      });
      throw error;
    }
  }
  
  /**
   * Prägt einen NFT
   * @param {string} contractAddress - Adresse des NFT-Vertrags
   * @param {string} to - Empfängeradresse
   * @param {string} tokenURI - URI der Token-Metadaten
   * @param {Object} options - Prägungsoptionen
   * @returns {Promise<Object>} Prägungsergebnis
   */
  async mintNFT(contractAddress, to, tokenURI, options = {}) {
    try {
      logger.info(`Präge NFT für ${to} mit URI ${tokenURI}`);
      
      // Führe die mint-Methode des NFT-Vertrags aus
      const result = await this.executeContractMethod(
        contractAddress,
        options.abi || require('../../contracts/abis/ERC721.json'),
        'mint',
        [to, tokenURI],
        options
      );
      
      // Extrahiere die Token-ID aus dem Event
      const transferEvent = result.events.find(event => event.name === 'contracts.Transfer');
      const tokenId = transferEvent ? transferEvent.data[2] : null;
      
      logger.info(`NFT erfolgreich geprägt mit Token-ID ${tokenId}`);
      
      return {
        tokenId,
        contractAddress,
        owner: to,
        tokenURI,
        transactionHash: result.hash
      };
    } catch (error) {
      logger.error('Fehler beim Prägen des NFT', {
        error: error.message,
        contractAddress,
        to,
        tokenURI
      });
      throw error;
    }
  }
  
  /**
   * Prägt einen IP-NFT
   * @param {string} contractAddress - Adresse des IP-NFT-Vertrags
   * @param {string} to - Empfängeradresse
   * @param {string} tokenURI - URI der Token-Metadaten
   * @param {Object} options - Prägungsoptionen
   * @returns {Promise<Object>} Prägungsergebnis
   */
  async mintIPNFT(contractAddress, to, tokenURI, options = {}) {
    try {
      logger.info(`Präge IP-NFT für ${to} mit URI ${tokenURI}`);
      
      // Extrahiere die Optionen
      const {
        royaltyPercentage = 0,
        contentCid,
        legalAgreementCid,
        contentType,
        contentId
      } = options;
      
      // Führe die mintIPNFT-Methode des IP-NFT-Vertrags aus
      const result = await this.executeContractMethod(
        contractAddress,
        options.abi || require('../../contracts/abis/IPNFTContract.json'),
        'mintIPNFT',
        [
          to,
          tokenURI,
          royaltyPercentage * 100, // Konvertiere Prozent in Basispunkte (1% = 100 Basispunkte)
          contentCid || '',
          legalAgreementCid || '',
          {
            contentType: contentType || '',
            contentId: contentId || '',
            timestamp: Math.floor(Date.now() / 1000)
          }
        ],
        options
      );
      
      // Extrahiere die Token-ID aus dem Event
      const transferEvent = result.events.find(event => event.name === 'contracts.Transfer');
      const tokenId = transferEvent ? transferEvent.data[2] : null;
      
      logger.info(`IP-NFT erfolgreich geprägt mit Token-ID ${tokenId}`);
      
      return {
        tokenId,
        contractAddress,
        owner: to,
        tokenURI,
        royaltyPercentage,
        contentCid,
        legalAgreementCid,
        transactionHash: result.hash
      };
    } catch (error) {
      logger.error('Fehler beim Prägen des IP-NFT', {
        error: error.message,
        contractAddress,
        to,
        tokenURI
      });
      throw error;
    }
  }
  
  /**
   * Fractionalisiert einen IP-NFT
   * @param {string} contractAddress - Adresse des IP-NFT-Vertrags
   * @param {string} tokenId - Token-ID des IP-NFT
   * @param {Object} options - Fractionalisierungsoptionen
   * @returns {Promise<Object>} Fractionalisierungsergebnis
   */
  async fractionalizeIPNFT(contractAddress, tokenId, options = {}) {
    try {
      logger.info(`Fractionalisiere IP-NFT mit Token-ID ${tokenId}`);
      
      // Extrahiere die Optionen
      const {
        name = `IP-Shares-${tokenId}`,
        symbol = `IPS-${tokenId}`,
        initialSupply = 1000000,
        ownerAddress
      } = options;
      
      // Führe die fractionalizeIPNFT-Methode des IP-NFT-Vertrags aus
      const result = await this.executeContractMethod(
        contractAddress,
        options.abi || require('../../contracts/abis/IPNFTContract.json'),
        'fractionalizeIPNFT',
        [
          tokenId,
          name,
          symbol,
          initialSupply,
          ownerAddress || this.account.address
        ],
        options
      );
      
      // Extrahiere die Token-Adresse aus dem Event
      const fractionalizationEvent = result.events.find(event => event.name === 'contracts.IPNFTFractionalized');
      const tokenAddress = fractionalizationEvent ? fractionalizationEvent.data[1] : null;
      
      logger.info(`IP-NFT erfolgreich fractionalisiert mit Token-Adresse ${tokenAddress}`);
      
      return {
        tokenId,
        contractAddress,
        tokenAddress,
        name,
        symbol,
        initialSupply,
        transactionHash: result.hash
      };
    } catch (error) {
      logger.error('Fehler beim Fractionalisieren des IP-NFT', {
        error: error.message,
        contractAddress,
        tokenId
      });
      throw error;
    }
  }
  
  /**
   * Erstellt einen Governance-Token für einen IP-NFT
   * @param {string} contractAddress - Adresse des IP-NFT-Vertrags
   * @param {string} tokenId - Token-ID des IP-NFT
   * @param {Object} options - Token-Erstellungsoptionen
   * @returns {Promise<Object>} Token-Erstellungsergebnis
   */
  async createIPGovernanceToken(contractAddress, tokenId, options = {}) {
    try {
      logger.info(`Erstelle Governance-Token für IP-NFT mit Token-ID ${tokenId}`);
      
      // Extrahiere die Optionen
      const {
        name = `IP-Gov-${tokenId}`,
        symbol = `IPG-${tokenId}`,
        initialSupply = 100000,
        ownerAddress
      } = options;
      
      // Führe die createIPGovernanceToken-Methode des IP-NFT-Vertrags aus
      const result = await this.executeContractMethod(
        contractAddress,
        options.abi || require('../../contracts/abis/IPNFTContract.json'),
        'createIPGovernanceToken',
        [
          tokenId,
          name,
          symbol,
          initialSupply,
          ownerAddress || this.account.address
        ],
        options
      );
      
      // Extrahiere die Token-Adresse aus dem Event
      const governanceEvent = result.events.find(event => event.name === 'contracts.IPGovernanceTokenCreated');
      const tokenAddress = governanceEvent ? governanceEvent.data[1] : null;
      
      logger.info(`Governance-Token erfolgreich erstellt mit Token-Adresse ${tokenAddress}`);
      
      return {
        tokenId,
        contractAddress,
        tokenAddress,
        name,
        symbol,
        initialSupply,
        transactionHash: result.hash
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen des Governance-Tokens', {
        error: error.message,
        contractAddress,
        tokenId
      });
      throw error;
    }
  }
  
  /**
   * Registriert eine Berechtigung für einen IP-NFT
   * @param {string} contractAddress - Adresse des IP-NFT-Vertrags
   * @param {string} tokenId - Token-ID des IP-NFT
   * @param {string} grantee - Adresse des Berechtigungsempfängers
   * @param {string} permissionType - Art der Berechtigung
   * @param {Object} options - Berechtigungsoptionen
   * @returns {Promise<Object>} Registrierungsergebnis
   */
  async registerIPNFTPermission(contractAddress, tokenId, grantee, permissionType, options = {}) {
    try {
      logger.info(`Registriere Berechtigung ${permissionType} für IP-NFT ${tokenId} an ${grantee}`);
      
      // Extrahiere die Optionen
      const {
        expirationDate,
        metadata
      } = options;
      
      // Berechne das Ablaufdatum in Sekunden seit der Epoche
      const expirationTimestamp = expirationDate ? Math.floor(new Date(expirationDate).getTime() / 1000) : 0;
      
      // Führe die registerPermission-Methode des IP-NFT-Vertrags aus
      const result = await this.executeContractMethod(
        contractAddress,
        options.abi || require('../../contracts/abis/IPNFTContract.json'),
        'registerPermission',
        [
          tokenId,
          grantee,
          permissionType,
          expirationTimestamp,
          JSON.stringify(metadata || {})
        ],
        options
      );
      
      logger.info(`Berechtigung erfolgreich registriert`);
      
      return {
        tokenId,
        contractAddress,
        grantee,
        permissionType,
        expirationDate: expirationDate || null,
        transactionHash: result.hash
      };
    } catch (error) {
      logger.error('Fehler beim Registrieren der Berechtigung', {
        error: error.message,
        contractAddress,
        tokenId,
        grantee,
        permissionType
      });
      throw error;
    }
  }
  
  /**
   * Widerruft eine Berechtigung für einen IP-NFT
   * @param {string} contractAddress - Adresse des IP-NFT-Vertrags
   * @param {string} tokenId - Token-ID des IP-NFT
   * @param {string} grantee - Adresse des Berechtigungsempfängers
   * @param {string} permissionType - Art der Berechtigung
   * @returns {Promise<Object>} Widerrufsergebnis
   */
  async revokeIPNFTPermission(contractAddress, tokenId, grantee, permissionType) {
    try {
      logger.info(`Widerrufe Berechtigung ${permissionType} für IP-NFT ${tokenId} von ${grantee}`);
      
      // Führe die revokePermission-Methode des IP-NFT-Vertrags aus
      const result = await this.executeContractMethod(
        contractAddress,
        require('../../contracts/abis/IPNFTContract.json'),
        'revokePermission',
        [
          tokenId,
          grantee,
          permissionType
        ]
      );
      
      logger.info(`Berechtigung erfolgreich widerrufen`);
      
      return {
        tokenId,
        contractAddress,
        grantee,
        permissionType,
        transactionHash: result.hash
      };
    } catch (error) {
      logger.error('Fehler beim Widerrufen der Berechtigung', {
        error: error.message,
        contractAddress,
        tokenId,
        grantee,
        permissionType
      });
      throw error;
    }
  }
}/**
 * @fileoverview Polkadot-Adapter für die Interaktion mit dem Polkadot-Netzwerk
 * 
 * Dieser Adapter implementiert das BlockchainAdapter-Interface für Polkadot und
 * ermöglicht die Interaktion mit dem Polkadot-Netzwerk und seinen Parachains.
 * Er unterstützt Cross-Chain-Funktionalität und ist optimiert für die Interoperabilität.
 */

import { ApiPromise, WsProvider, Keyring } from '@polkadot/api';
import { cryptoWaitReady } from '@polkadot/util-crypto';
import { BlockchainAdapter } from './BlockchainAdapter.js';
import { logger } from '../../utils/logger.js';

/**
 * Polkadot-Adapter für die Interaktion mit dem Polkadot-Netzwerk
 */
export class PolkadotAdapter extends BlockchainAdapter {
  /**
   * Erstellt eine neue Instanz des PolkadotAdapter
   * @param {Object} options - Konfigurationsoptionen
   * @param {string} options.providerUrl - URL des Polkadot-Providers
   * @param {string} options.mnemonic - Mnemonic für die Schlüsselerstellung
   * @param {string} options.seed - Seed für die Schlüsselerstellung
   * @param {string} options.privateKey - Private Key für Transaktionen
   * @param {string} options.network - Netzwerk (polkadot, kusama, westend, etc.)
   * @param {Object} options.parachains - Konfiguration für Parachains
   * @param {Object} options.contractOptions - Optionen für Smart Contracts
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      providerUrl: options.providerUrl || process.env.POLKADOT_PROVIDER_URL || 'wss://rpc.polkadot.io',
      mnemonic: options.mnemonic || process.env.POLKADOT_MNEMONIC,
      seed: options.seed || process.env.POLKADOT_SEED,
      privateKey: options.privateKey || process.env.POLKADOT_PRIVATE_KEY,
      network: options.network || process.env.POLKADOT_NETWORK || 'polkadot',
      parachains: options.parachains || {
        moonbeam: {
          url: process.env.MOONBEAM_PROVIDER_URL || 'wss://moonbeam.api.onfinality.io/public-ws',
          enabled: true
        },
        acala: {
          url: process.env.ACALA_PROVIDER_URL || 'wss://acala-rpc.dwellir.com',
          enabled: true
        }
      },
      contractOptions: options.contractOptions || {
        gasLimit: 3000000,
        storageDepositLimit: null
      },
      ...options
    };
    
    this.api = null;
    this.parachainApis = {};
    this.keyring = null;
    this.account = null;
    this.contracts = new Map();
    
    logger.info('PolkadotAdapter initialisiert');
  }
  
  /**
   * Initialisiert den Polkadot-Adapter
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      logger.info('Initialisiere PolkadotAdapter');
      
      // Warte auf die Initialisierung der Kryptofunktionen
      await cryptoWaitReady();
      
      // Verbinde mit dem Polkadot-Netzwerk
      await this.connect();
      
      // Initialisiere den Keyring
      await this.initializeKeyring();
      
      // Verbinde mit den konfigurierten Parachains
      await this.connectToParachains();
      
      logger.info('PolkadotAdapter erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des PolkadotAdapter', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Verbindet mit dem Polkadot-Netzwerk
   * @param {Object} options - Verbindungsoptionen
   * @returns {Promise<void>}
   */
  async connect(options = {}) {
    try {
      logger.info(`Verbinde mit Polkadot-Netzwerk: ${this.options.network}`);
      
      // Erstelle den Provider
      const provider = new WsProvider(this.options.providerUrl);
      
      // Erstelle die API
      this.api = await ApiPromise.create({ provider });
      
      // Prüfe die Verbindung
      const chain = await this.api.rpc.system.chain();
      const version = await this.api.rpc.system.version();
      
      logger.debug(`Verbunden mit ${chain} (${version})`);
      
      logger.info('Erfolgreich mit Polkadot-Netzwerk verbunden');
    } catch (error) {
      logger.error('Fehler beim Verbinden mit dem Polkadot-Netzwerk', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Initialisiert den Keyring und das Konto
   * @returns {Promise<void>}
   */
  async initializeKeyring() {
    try {
      logger.info('Initialisiere Keyring');
      
      // Erstelle den Keyring
      this.keyring = new Keyring({ type: 'sr25519' });
      
      // Erstelle das Konto basierend auf den verfügbaren Optionen
      if (this.options.mnemonic) {
        this.account = this.keyring.addFromMnemonic(this.options.mnemonic);
        logger.debug(`Konto aus Mnemonic erstellt: ${this.account.address}`);
      } else if (this.options.seed) {
        this.account = this.keyring.addFromSeed(this.options.seed);
        logger.debug(`Konto aus Seed erstellt: ${this.account.address}`);
      } else if (this.options.privateKey) {
        this.account = this.keyring.addFromUri(this.options.privateKey);
        logger.debug(`Konto aus Private Key erstellt: ${this.account.address}`);
      } else {
        logger.warn('Kein Mnemonic, Seed oder Private Key angegeben, nur Lesezugriff möglich');
      }
      
      logger.info('Keyring erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des Keyring', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Verbindet mit den konfigurierten Parachains
   * @returns {Promise<void>}
   */
  async connectToParachains() {
    try {
      logger.info('Verbinde mit Parachains');
      
      // Verbinde mit jeder konfigurierten Parachain
      for (const [name, config] of Object.entries(this.options.parachains)) {
        if (config.enabled) {
          try {
            logger.debug(`Verbinde mit Parachain: ${name}`);
            
            // Erstelle den Provider
            const provider = new WsProvider(config.url);
            
            // Erstelle die API
            const api = await ApiPromise.create({ provider });
            
            // Speichere die API
            this.parachainApis[name] = api;
            
            // Prüfe die Verbindung
            const chain = await api.rpc.system.chain();
            
            logger.debug(`Verbunden mit Parachain ${name} (${chain})`);
          } catch (error) {
            logger.error(`Fehler beim Verbinden mit Parachain ${name}`, {
              error: error.message
            });
          }
        }
      }
      
      logger.info(`Erfolgreich mit ${Object.keys(this.parachainApis).length} Parachains verbunden`);
    } catch (error) {
      logger.error('Fehler beim Verbinden mit Parachains', {
        error: error.message
      });
      // Wir werfen hier keinen Fehler, da die Hauptfunktionalität auch ohne Parachains funktionieren sollte
    }
  }
  
  /**
   * Trennt die Verbindung zum Polkadot-Netzwerk
   * @returns {Promise<void>}
   */
  async disconnect() {
    try {
      logger.info('Trenne Verbindung zum Polkadot-Netzwerk');
      
      // Trenne die Verbindung zu allen Parachains
      for (const [name, api] of Object.entries(this.parachainApis)) {
        try {
          await api.disconnect();
          logger.debug(`Verbindung zu Parachain ${name} getrennt`);
        } catch (error) {
          logger.warn(`Fehler beim Trennen der Verbindung zu Parachain ${name}`, {
            error: error.message
          });
        }
      }
      
      // Trenne die Verbindung zum Hauptnetzwerk
      if (this.api) {
        await this.api.disconnect();
      }
      
      // Lösche alle Referenzen
      this.api = null;
      this.parachainApis = {};
      this.account = null;
      this.contracts.clear();
      
      logger.info('Verbindung zum Polkadot-Netzwerk getrennt');
    } catch (error) {
      logger.error('Fehler beim Trennen der Verbindung zum Polkadot-Netzwerk', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Verbindung zum Polkadot-Netzwerk besteht
   * @returns {Promise<boolean>} True, wenn eine Verbindung besteht
   */
  async isConnected() {
    try {
      if (!this.api) {
        return false;
      }
      
      // Prüfe, ob die API verbunden ist
      return this.api.isConnected;
    } catch (error) {
      logger.debug('Keine Verbindung zum Polkadot-Netzwerk');
      return false;
    }
  }
  
  /**
   * Ruft die aktuelle Blocknummer ab
   * @returns {Promise<number>} Aktuelle Blocknummer
   */
  async getBlockNumber() {
    try {
      logger.debug('Rufe aktuelle Blocknummer ab');
      
      if (!this.api) {
        throw new Error('Keine Verbindung zum Polkadot-Netzwerk');
      }
      
      // Hole den aktuellen Header
      const header = await this.api.rpc.chain.getHeader();
      
      // Extrahiere die Blocknummer
      const blockNumber = header.number.toNumber();
      
      logger.debug(`Aktuelle Blocknummer: ${blockNumber}`);
      
      return blockNumber;
    } catch (error) {
      logger.error('Fehler beim Abrufen der aktuellen Blocknummer', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft den Kontostand einer Adresse ab
   * @param {string} address - Adresse
   * @param {Object} options - Abfrageoptionen
   * @returns {Promise<string>} Kontostand
   */
  async getBalance(address, options = {}) {
    try {
      logger.debug(`Rufe Kontostand für Adresse ${address} ab`);
      
      if (!this.api) {
        throw new Error('Keine Verbindung zum Polkadot-Netzwerk');
      }
      
      // Hole den Kontostand
      const { data: balance } = await this.api.query.system.account(address);
      
      // Konvertiere den Kontostand in eine lesbare Form
      const formattedBalance = this.api.createType('Balance', balance.free).toHuman();
      
      logger.debug(`Kontostand für Adresse ${address}: ${formattedBalance}`);
      
      return formattedBalance;
    } catch (error) {
      logger.error('Fehler beim Abrufen des Kontostands', {
        error: error.message,
        address
      });
      throw error;
    }
  }
  
  /**
   * Sendet eine Transaktion
   * @param {Object} transaction - Transaktionsdaten
   * @returns {Promise<Object>} Transaktionsergebnis
   */
  async sendTransaction(transaction) {
    try {
      logger.info('Sende Transaktion');
      
      if (!this.api) {
        throw new Error('Keine Verbindung zum Polkadot-Netzwerk');
      }
      
      if (!this.account) {
        throw new Error('Kein Konto verfügbar');
      }
      
      // Extrahiere die Transaktionsdaten
      const { to, value, method, section, args = [] } = transaction;
      
      // Erstelle die Transaktion
      let tx;
      
      if (to && value) {
        // Einfache Überweisung
        tx = this.api.tx.balances.transfer(to, value);
      } else if (method && section) {
        // Spezifische Methode
        tx = this.api.tx[section][method](...args);
      } else {
        throw new Error('Ungültige Transaktionsdaten');
      }
      
      // Sende die Transaktion
      const unsub = await tx.signAndSend(this.account, { nonce: -1 }, (result) => {
        const { status, events = [] } = result;
        
        if (status.isInBlock) {
          logger.debug(`Transaktion in Block ${status.asInBlock.toHex()}`);
          
          // Verarbeite die Events
          events.forEach(({ event: { data, method, section } }) => {
            if (section === 'system' && method === 'ExtrinsicSuccess') {
              logger.info('Transaktion erfolgreich');
              unsub();
            } else if (section === 'system' && method === 'ExtrinsicFailed') {
              const [dispatchError] = data;
              let errorInfo;
              
              if (dispatchError.isModule) {
                const decoded = this.api.registry.findMetaError(dispatchError.asModule);
                errorInfo = `${decoded.section}.${decoded.name}`;
              } else {
                errorInfo = dispatchError.toString();
              }
              
              logger.error(`Transaktion fehlgeschlagen: ${errorInfo}`);
              unsub();
            }
          });
        } else if (status.isFinalized) {
          logger.info(`Transaktion finalisiert in Block ${status.asFinalized.toHex()}`);
          unsub();
        }
      });
      
      // Warte auf die Bestätigung
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          reject(new Error('Timeout bei der Transaktionsbestätigung'));
        }, 60000); // 60 Sekunden Timeout
      });
    } catch (error) {
      logger.error('Fehler beim Senden der Transaktion', {
        error: error.message,
        transaction
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Transaktion ab
   * @param {string} txHash - Transaktions-Hash
   * @returns {Promise<Object>} Transaktionsdaten
   */
  async getTransaction(txHash) {
    try {
      logger.debug(`Rufe Transaktion mit Hash ${txHash} ab`);
      
      if (!this.api) {
        throw new Error('Keine Verbindung zum Polkadot-Netzwerk');
      }
      
      // Hole die Transaktion
      const hash = this.api.createType('Hash', txHash);
      const signedBlock = await this.api.rpc.chain.getBlock(hash);
      
      // Suche die Transaktion im Block
      const transactions = signedBlock.block.extrinsics;
      const transaction = transactions.find(tx => tx.hash.toHex() === txHash);
      
      if (!transaction) {
        throw new Error(`Transaktion mit Hash ${txHash} nicht gefunden`);
      }
      
      logger.debug(`Transaktion mit Hash ${txHash} abgerufen`);
      
      return {
        hash: transaction.hash.toHex(),
        blockHash: hash.toHex(),
        blockNumber: signedBlock.block.header.number.toNumber(),
        from: transaction.signer.toString(),
        method: transaction.method.method,
        section: transaction.method.section,
        args: transaction.method.args.map(arg => arg.toString()),
        nonce: transaction.nonce.toNumber(),
        tip: transaction.tip.toNumber(),
        isSigned: transaction.isSigned
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen der Transaktion', {
        error: error.message,
        txHash
      });
      throw error;
    }
  }
  
  /**
   * Ruft den Status einer Transaktion ab
   * @param {string} txHash - Transaktions-Hash
   * @returns {Promise<string>} Transaktionsstatus
   */
  async getTransactionStatus(txHash) {
    try {
      logger.debug(`Rufe Status der Transaktion mit Hash ${txHash} ab`);
      
      if (!this.api) {
        throw new Error('Keine Verbindung zum Polkadot-Netzwerk');
      }
      
      // Prüfe, ob die Transaktion in einem Block ist
      try {
        const hash = this.api.createType('Hash', txHash);
        await this.api.rpc.chain.getBlock(hash);
        return 'finalized';
      } catch (error) {
        // Transaktion nicht gefunden
        return 'pending';
      }
    } catch (error) {
      logger.error('Fehler beim Abrufen des Transaktionsstatus', {
        error: error.message,
        txHash
      });
      throw error;
    }
  }
  
  /**
   * Wartet auf die Bestätigung einer Transaktion
   * @param {string} txHash - Transaktions-Hash
   * @param {number} confirmations - Anzahl der erforderlichen Bestätigungen
   * @returns {Promise<Object>} Bestätigte Transaktionsdaten
   */
  async waitForTransaction(txHash, confirmations = 1) {
    try {
      logger.info(`Warte auf Bestätigung der Transaktion ${txHash} (${confirmations} Bestätigungen)`);
      
      if (!this.api) {
        throw new Error('Keine Verbindung zum Polkadot-Netzwerk');
      }
      
      // Warte auf die Bestätigung
      return new Promise((resolve, reject) => {
        let blockCount = 0;
        
        const unsub = this.api.rpc.chain.subscribeNewHeads(async (header) => {
          try {
            // Prüfe, ob die Transaktion in einem Block ist
            const status = await this.getTransactionStatus(txHash);
            
            if (status === 'finalized') {
              blockCount++;
              
              if (blockCount >= confirmations) {
                unsub();
                
                // Hole die Transaktionsdaten
                const transaction = await this.getTransaction(txHash);
                
                logger.info(`Transaktion ${txHash} bestätigt`);
                
                resolve(transaction);
              }
            }
          } catch (error) {
            unsub();
            reject(error);
          }
        });
        
        // Timeout
        setTimeout(() => {
          unsub();
          reject(new Error('Timeout bei der Transaktionsbestätigung'));
        }, 60000 * confirmations); // 60 Sekunden pro Bestätigung
      });
    } catch (error) {
      logger.error('Fehler beim Warten auf die Transaktionsbestätigung', {
        error: error.message,
        txHash
      });
      throw error;
    }
  }
  
  /**
   * Erstellt einen Smart Contract
   * @param {string} abi - ABI des Smart Contracts
   * @param {string} bytecode - Bytecode des Smart Contracts
   * @param {Array} args - Konstruktorargumente
   * @returns {Promise<Object>} Ergebnis der Vertragserstellung
   */
  async deployContract(abi, bytecode, args = []) {
    try {
      logger.info('Erstelle Smart Contract');
      
      if (!this.api) {
        throw new Error('Keine Verbindung zum Polkadot-Netzwerk');
      }
      
      if (!this.account) {
        throw new Error('Kein Konto verfügbar');
      }
      
      // Prüfe, ob wir mit Moonbeam verbunden sind
      if (!this.parachainApis.moonbeam) {
        throw new Error('Keine Verbindung zu Moonbeam (EVM-kompatible Parachain)');
      }
      
      // Verwende die Moonbeam-API für EVM-Kompatibilität
      const api = this.parachainApis.moonbeam;
      
      // Erstelle den Contract
      const { Contract } = await import('@polkadot/api-contract');
      const contract = new Contract(api, abi, bytecode);
      
      // Deploye den Contract
      const gasLimit = this.options.contractOptions.gasLimit;
      const storageDepositLimit = this.options.contractOptions.storageDepositLimit;
      
      const tx = contract.tx.new({ gasLimit, storageDepositLimit }, ...args);
      
      // Sende die Transaktion
      const result = await tx.signAndSend(this.account);
      
      // Warte auf die Bestätigung
      const address = await new Promise((resolve, reject) => {
        const unsub = result.subscribe(async (result) => {
          const { status, events = [] } = result;
          
          if (status.isInBlock || status.isFinalized) {
            // Suche nach dem Instantiated-Event
            for (const { event } of events) {
              if (api.events.contracts && event.section === 'contracts' && event.method === 'Instantiated') {
                const [deployer, contractAddress] = event.data;
                
                unsub();
                resolve(contractAddress.toString());
                break;
              }
            }
          }
        });
        
        // Timeout
        setTimeout(() => {
          unsub();
          reject(new Error('Timeout bei der Vertragsinstanziierung'));
        }, 60000); // 60 Sekunden Timeout
      });
      
      logger.info(`Smart Contract erstellt: ${address}`);
      
      // Speichere den Contract in der Map
      this.contracts.set(address, contract);
      
      return {
        address,
        transactionHash: result.hash.toHex(),
        abi
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen des Smart Contracts', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Instanz eines Smart Contracts ab
   * @param {string} address - Adresse des Smart Contracts
   * @param {string} abi - ABI des Smart Contracts
   * @returns {Promise<Object>} Smart Contract-Instanz
   */
  async getContract(address, abi) {
    try {
      logger.debug(`Rufe Smart Contract mit Adresse ${address} ab`);
      
      // Prüfe, ob der Contract bereits in der Map ist
      if (this.contracts.has(address)) {
        return this.contracts.get(address);
      }
      
      // Prüfe, ob wir mit Moonbeam verbunden sind
      if (!this.parachainApis.moonbeam) {
        throw new Error('Keine Verbindung zu Moonbeam (EVM-kompatible Parachain)');
      }
      
      // Verwende die Moonbeam-API für EVM-Kompatibilität
      const api = this.parachainApis.moonbeam;
      
      // Erstelle eine neue Contract-Instanz
      const { Contract } = await import('@polkadot/api-contract');
      const contract = new Contract(api, abi, address);
      
      // Speichere den Contract in der Map
      this.contracts.set(address, contract);
      
      return contract;
    } catch (error) {
      logger.error('Fehler beim Abrufen des Smart Contracts', {
        error: error.message,
        address
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Methode eines Smart Contracts auf (read-only)
   * @param {string} address - Adresse des Smart Contracts
   * @param {string} abi - ABI des Smart Contracts
   * @param {string} method - Name der Methode
   * @param {Array} args - Methodenargumente
   * @returns {Promise<any>} Ergebnis des Methodenaufrufs
   */
  async callContractMethod(address, abi, method, args = []) {
    try {
      logger.debug(`Rufe Methode ${method} des Smart Contracts ${address} auf`);
      
      // Hole den Contract
      const contract = await this.getContract(address, abi);
      
      // Rufe die Methode auf
      const result = await contract.query[method](this.account.address, {}, ...args);
      
      // Prüfe auf Fehler
      if (result.result.isErr) {
        throw new Error(`Fehler beim Aufruf der Methode ${method}: ${result.result.asErr.toString()}`);
      }
      
      // Extrahiere das Ergebnis
      const value = result.output.toHuman();
      
      return value;
    } catch (error) {
      logger.error('Fehler beim Aufrufen der Contract-Methode', {
        error: error.message,
        address,
        method,
        args
      });
      throw error;
    }
  }
  
  /**
   * Führt eine Methode eines Smart Contracts aus (Transaktion)
   * @param {string} address - Adresse des Smart Contracts
   * @param {string} abi - ABI des Smart Contracts
   * @param {string} method - Name der Methode
   * @param {Array} args - Methodenargumente
   * @param {Object} options - Transaktionsoptionen
   * @returns {Promise<Object>} Transaktionsergebnis
   */
  async executeContractMethod(address, abi, method, args = [], options = {}) {
    try {
      logger.info(`Führe Methode ${method} des Smart Contracts ${address} aus`);
      
      if (!this.account) {
        throw new Error('Kein Konto verfügbar');
      }
      
      // Hole den Contract
      const contract = await this.getContract(address, abi);
      
      // Bereite die Transaktionsoptionen vor
      const gasLimit = options.gasLimit || this.options.contractOptions.gasLimit;
      const storageDepositLimit = options.storageDepositLimit || this.options.contractOptions.storageDepositLimit;
      const value = options.value || 0;
      
      // Führe die Methode aus
      const tx = contract.tx[method]({ gasLimit, storageDepositLimit, value }, ...args);
      
      // Sende die Transaktion
      const result = await tx.signAndSend(this.account);
      
      // Warte auf die Bestätigung
      const events = await new Promise((resolve, reject) => {
        const unsub = result.subscribe((result) => {
          const { status, events = [] } = result;
          
          if (status.isInBlock || status.isFinalized) {
            unsub();
            resolve(events);
          }
        });
        
        // Timeout
        setTimeout(() => {
          unsub();
          reject(new Error('Timeout bei der Transaktionsbestätigung'));
        }, 60000); // 60 Sekunden Timeout
      });
      
      logger.info(`Methode ${method} des Smart Contracts ${address} erfolgreich ausgeführt`);
      
      // Extrahiere die Events
      const formattedEvents = events.map(({ event }) => ({
        name: `${event.section}.${event.method}`,
        data: event.data.toHuman()
      }));
      
      return {
        hash: result.hash.toHex(),
        events: formattedEvents,
        status: 'success'
      };
    } catch (error) {
      logger.error('Fehler beim Ausführen der Contract-Methode', {
        error: error.message,
        address,
        method,
        args
      });
      throw error;
    }
  }
  
  /**
   * Prägt einen NFT
   * @param {string} contractAddress - Adresse des NFT-Vertrags
   * @param {string} to - Empfängeradresse
   * @param {string} tokenURI - URI der Token-Metadaten
   * @param {Object} options - Prägungsoptionen
   * @returns {Promise<Object>} Prägungsergebnis
   */
  async mintNFT(contractAddress, to, tokenURI, options = {}) {
    try {
      logger.info(`Präge NFT für ${to} mit URI ${tokenURI}`);
      
      // Führe die mint-Methode des NFT-Vertrags aus
      const result = await this.executeContractMethod(
        contractAddress,
        options.abi || require('../../contracts/abis/ERC721.json'),
        'mint',
        [to, tokenURI],
        options
      );
      
      // Extrahiere die Token-ID aus dem Event
      const transferEvent = result.events.find(event => event.name === 'contracts.Transfer');
      const tokenId = transferEvent ? transferEvent.data[2] : null;
      
      logger.info(`NFT erfolgreich geprägt mit Token-ID ${tokenId}`);
      
      return {
        tokenId,
        contractAddress,
        owner: to,
        tokenURI,
        transactionHash: result.hash
      };
    } catch (error) {
      logger.error('Fehler beim Prägen des NFT', {
        error: error.message,
        contractAddress,
        to,
        tokenURI
      });
      throw error;
    }
  }
  
  /**
   * Prägt einen IP-NFT
   * @param {string} contractAddress - Adresse des IP-NFT-Vertrags
   * @param {string} to - Empfängeradresse
   * @param {string} tokenURI - URI der Token-Metadaten
   * @param {Object} options - Prägungsoptionen
   * @returns {Promise<Object>} Prägungsergebnis
   */
  async mintIPNFT(contractAddress, to, tokenURI, options = {}) {
    try {
      logger.info(`Präge IP-NFT für ${to} mit URI ${tokenURI}`);
      
      // Extrahiere die Optionen
      const {
        royaltyPercentage = 0,
        contentCid,
        legalAgreementCid,
        contentType,
        contentId
      } = options;
      
      // Führe die mintIPNFT-Methode des IP-NFT-Vertrags aus
      const result = await this.executeContractMethod(
        contractAddress,
        options.abi || require('../../contracts/abis/IPNFTContract.json'),
        'mintIPNFT',
        [
          to,
          tokenURI,
          royaltyPercentage * 100, // Konvertiere Prozent in Basispunkte (1% = 100 Basispunkte)
          contentCid || '',
          legalAgreementCid || '',
          {
            contentType: contentType || '',
            contentId: contentId || '',
            timestamp: Math.floor(Date.now() / 1000)
          }
        ],
        options
      );
      
      // Extrahiere die Token-ID aus dem Event
      const transferEvent = result.events.find(event => event.name === 'contracts.Transfer');
      const tokenId = transferEvent ? transferEvent.data[2] : null;
      
      logger.info(`IP-NFT erfolgreich geprägt mit Token-ID ${tokenId}`);
      
      return {
        tokenId,
        contractAddress,
        owner: to,
        tokenURI,
        royaltyPercentage,
        contentCid,
        legalAgreementCid,
        transactionHash: result.hash
      };
    } catch (error) {
      logger.error('Fehler beim Prägen des IP-NFT', {
        error: error.message,
        contractAddress,
        to,
        tokenURI
      });
      throw error;
    }
  }
  
  /**
   * Fractionalisiert einen IP-NFT
   * @param {string} contractAddress - Adresse des IP-NFT-Vertrags
   * @param {string} tokenId - Token-ID des IP-NFT
   * @param {Object} options - Fractionalisierungsoptionen
   * @returns {Promise<Object>} Fractionalisierungsergebnis
   */
  async fractionalizeIPNFT(contractAddress, tokenId, options = {}) {
    try {
      logger.info(`Fractionalisiere IP-NFT mit Token-ID ${tokenId}`);
      
      // Extrahiere die Optionen
      const {
        name = `IP-Shares-${tokenId}`,
        symbol = `IPS-${tokenId}`,
        initialSupply = 1000000,
        ownerAddress
      } = options;
      
      // Führe die fractionalizeIPNFT-Methode des IP-NFT-Vertrags aus
      const result = await this.executeContractMethod(
        contractAddress,
        options.abi || require('../../contracts/abis/IPNFTContract.json'),
        'fractionalizeIPNFT',
        [
          tokenId,
          name,
          symbol,
          initialSupply,
          ownerAddress || this.account.address
        ],
        options
      );
      
      // Extrahiere die Token-Adresse aus dem Event
      const fractionalizationEvent = result.events.find(event => event.name === 'contracts.IPNFTFractionalized');
      const tokenAddress = fractionalizationEvent ? fractionalizationEvent.data[1] : null;
      
      logger.info(`IP-NFT erfolgreich fractionalisiert mit Token-Adresse ${tokenAddress}`);
      
      return {
        tokenId,
        contractAddress,
        tokenAddress,
        name,
        symbol,
        initialSupply,
        transactionHash: result.hash
      };
    } catch (error) {
      logger.error('Fehler beim Fractionalisieren des IP-NFT', {
        error: error.message,
        contractAddress,
        tokenId
      });
      throw error;
    }
  }
  
  /**
   * Erstellt einen Governance-Token für einen IP-NFT
   * @param {string} contractAddress - Adresse des IP-NFT-Vertrags
   * @param {string} tokenId - Token-ID des IP-NFT
   * @param {Object} options - Token-Erstellungsoptionen
   * @returns {Promise<Object>} Token-Erstellungsergebnis
   */
  async createIPGovernanceToken(contractAddress, tokenId, options = {}) {
    try {
      logger.info(`Erstelle Governance-Token für IP-NFT mit Token-ID ${tokenId}`);
      
      // Extrahiere die Optionen
      const {
        name = `IP-Gov-${tokenId}`,
        symbol = `IPG-${tokenId}`,
        initialSupply = 100000,
        ownerAddress
      } = options;
      
      // Führe die createIPGovernanceToken-Methode des IP-NFT-Vertrags aus
      const result = await this.executeContractMethod(
        contractAddress,
        options.abi || require('../../contracts/abis/IPNFTContract.json'),
        'createIPGovernanceToken',
        [
          tokenId,
          name,
          symbol,
          initialSupply,
          ownerAddress || this.account.address
        ],
        options
      );
      
      // Extrahiere die Token-Adresse aus dem Event
      const governanceEvent = result.events.find(event => event.name === 'contracts.IPGovernanceTokenCreated');
      const tokenAddress = governanceEvent ? governanceEvent.data[1] : null;
      
      logger.info(`Governance-Token erfolgreich erstellt mit Token-Adresse ${tokenAddress}`);
      
      return {
        tokenId,
        contractAddress,
        tokenAddress,
        name,
        symbol,
        initialSupply,
        transactionHash: result.hash
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen des Governance-Tokens', {
        error: error.message,
        contractAddress,
        tokenId
      });
      throw error;
    }
  }
  
  /**
   * Registriert eine Berechtigung für einen IP-NFT
   * @param {string} contractAddress - Adresse des IP-NFT-Vertrags
   * @param {string} tokenId - Token-ID des IP-NFT
   * @param {string} grantee - Adresse des Berechtigungsempfängers
   * @param {string} permissionType - Art der Berechtigung
   * @param {Object} options - Berechtigungsoptionen
   * @returns {Promise<Object>} Registrierungsergebnis
   */
  async registerIPNFTPermission(contractAddress, tokenId, grantee, permissionType, options = {}) {
    try {
      logger.info(`Registriere Berechtigung ${permissionType} für IP-NFT ${tokenId} an ${grantee}`);
      
      // Extrahiere die Optionen
      const {
        expirationDate,
        metadata
      } = options;
      
      // Berechne das Ablaufdatum in Sekunden seit der Epoche
      const expirationTimestamp = expirationDate ? Math.floor(new Date(expirationDate).getTime() / 1000) : 0;
      
      // Führe die registerPermission-Methode des IP-NFT-Vertrags aus
      const result = await this.executeContractMethod(
        contractAddress,
        options.abi || require('../../contracts/abis/IPNFTContract.json'),
        'registerPermission',
        [
          tokenId,
          grantee,
          permissionType,
          expirationTimestamp,
          JSON.stringify(metadata || {})
        ],
        options
      );
      
      logger.info(`Berechtigung erfolgreich registriert`);
      
      return {
        tokenId,
        contractAddress,
        grantee,
        permissionType,
        expirationDate: expirationDate || null,
        transactionHash: result.hash
      };
    } catch (error) {
      logger.error('Fehler beim Registrieren der Berechtigung', {
        error: error.message,
        contractAddress,
        tokenId,
        grantee,
        permissionType
      });
      throw error;
    }
  }
  
  /**
   * Widerruft eine Berechtigung für einen IP-NFT
   * @param {string} contractAddress - Adresse des IP-NFT-Vertrags
   * @param {string} tokenId - Token-ID des IP-NFT
   * @param {string} grantee - Adresse des Berechtigungsempfängers
   * @param {string} permissionType - Art der Berechtigung
   * @returns {Promise<Object>} Widerrufsergebnis
   */
  async revokeIPNFTPermission(contractAddress, tokenId, grantee, permissionType) {
    try {
      logger.info(`Widerrufe Berechtigung ${permissionType} für IP-NFT ${tokenId} von ${grantee}`);
      
      // Führe die revokePermission-Methode des IP-NFT-Vertrags aus
      const result = await this.executeContractMethod(
        contractAddress,
        require('../../contracts/abis/IPNFTContract.json'),
        'revokePermission',
        [
          tokenId,
          grantee,
          permissionType
        ]
      );
      
      logger.info(`Berechtigung erfolgreich widerrufen`);
      
      return {
        tokenId,
        contractAddress,
        grantee,
        permissionType,
        transactionHash: result.hash
      };
    } catch (error) {
      logger.error('Fehler beim Widerrufen der Berechtigung', {
        error: error.message,
        contractAddress,
        tokenId,
        grantee,
        permissionType
      });
      throw error;
    }
  }
}