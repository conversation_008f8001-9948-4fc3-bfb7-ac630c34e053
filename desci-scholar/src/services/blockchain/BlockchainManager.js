/**
 * @fileoverview Blockchain-Manager für die Verwaltung verschiedener Blockchain-Adapter
 *
 * Dieser Manager verwaltet verschiedene Blockchain-Adapter und bietet eine einheitliche
 * Schnittstelle für die Interaktion mit verschiedenen Blockchain-Netzwerken. Er ermöglicht
 * die flexible Auswahl und Kombination verschiedener Blockchains für unterschiedliche
 * Anwendungsfälle.
 *
 * Diese Version verwendet das zentrale Adapter-Registry-System für verbesserte Modularität.
 */

import { LoggerFactory } from '../../utils/logger.js';
import { getAdapterRegistry } from '../../core/adapter/AdapterRegistry.js';
import { blockchainAdapterConfig } from '../../config/adapters.js';

const logger = LoggerFactory.createLogger('BlockchainManager');

/**
 * Blockchain-Manager für die Verwaltung verschiedener Blockchain-Adapter
 */
export class BlockchainManager {
  /**
   * Erstellt eine neue Instanz des BlockchainManager
   * @param {Object} options - Konfigurationsoptionen
   * @param {Object} options.adapters - Konfiguration für die Adapter
   * @param {Object} options.defaultAdapter - Name des Standard-Adapters
   * @param {Object} options.contracts - Konfiguration für Smart Contracts
   * @param {AdapterRegistry} options.registry - Adapter-Registry (optional)
   */
  constructor(options = {}) {
    this.options = {
      adapters: options.adapters || blockchainAdapterConfig,
      defaultAdapter: options.defaultAdapter || 'ethereum',
      contracts: options.contracts || {
        ipNft: {
          address: process.env.IP_NFT_CONTRACT_ADDRESS,
          network: 'ethereum'
        },
        nft: {
          address: process.env.NFT_CONTRACT_ADDRESS,
          network: 'ethereum'
        }
      },
      ...options
    };

    // Verwende die übergebene Registry oder hole die Singleton-Instanz
    this.registry = options.registry || getAdapterRegistry();

    // Lokaler Cache für initialisierte Adapter
    this.adapters = new Map();

    logger.info('BlockchainManager initialisiert');
  }

  /**
   * Initialisiert den BlockchainManager und alle konfigurierten Adapter
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      logger.info('Initialisiere BlockchainManager');

      // Stelle sicher, dass die Registry initialisiert ist
      if (!this.registry.isInitialized) {
        await this.registry.initialize();
      }

      // Initialisiere die Adapter
      await this.initializeAdapters();

      logger.info('BlockchainManager erfolgreich initialisiert');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des BlockchainManager', {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Initialisiert die konfigurierten Adapter
   * @returns {Promise<void>}
   */
  async initializeAdapters() {
    try {
      // Hole alle verfügbaren Blockchain-Adapter aus der Registry
      const availableAdapters = this.registry.getAllAdapters().blockchain || [];

      // Initialisiere jeden konfigurierten Adapter
      for (const adapterInfo of availableAdapters) {
        const adapterName = adapterInfo.name;

        // Prüfe, ob eine Konfiguration für diesen Adapter vorhanden ist
        if (this.options.adapters[adapterName]) {
          try {
            // Hole den Adapter aus der Registry
            const adapter = this.registry.getAdapter('blockchain', adapterName, this.options.adapters[adapterName]);

            // Initialisiere den Adapter, falls noch nicht geschehen
            if (!adapter.isInitialized()) {
              await adapter.initialize();
            }

            // Füge den Adapter zum lokalen Cache hinzu
            this.adapters.set(adapterName, adapter);
            logger.debug(`${adapterName}-Adapter initialisiert`);
          } catch (adapterError) {
            logger.warn(`Fehler bei der Initialisierung des ${adapterName}-Adapters`, {
              error: adapterError.message
            });
          }
        }
      }

      // Wenn keine Adapter initialisiert wurden, versuche den Standard-Adapter zu initialisieren
      if (this.adapters.size === 0) {
        const defaultAdapter = this.registry.getAdapter('blockchain', this.options.defaultAdapter,
          this.options.adapters[this.options.defaultAdapter] || {});

        await defaultAdapter.initialize();
        this.adapters.set(this.options.defaultAdapter, defaultAdapter);
        logger.debug(`Standard-Adapter ${this.options.defaultAdapter} initialisiert`);
      }

      // Prüfe, ob der Standard-Adapter verfügbar ist
      if (!this.adapters.has(this.options.defaultAdapter)) {
        // Setze den ersten verfügbaren Adapter als Standard
        if (this.adapters.size > 0) {
          const firstAdapter = Array.from(this.adapters.keys())[0];
          this.options.defaultAdapter = firstAdapter;
          logger.warn(`Standard-Adapter nicht verfügbar, verwende ${firstAdapter} als Standard`);
        } else {
          throw new Error('Keine Blockchain-Adapter verfügbar');
        }
      }

      logger.info(`${this.adapters.size} Blockchain-Adapter initialisiert`);
    } catch (error) {
      logger.error('Fehler bei der Initialisierung der Adapter', {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Wählt den passenden Adapter basierend auf den Optionen aus
   * @param {Object} options - Optionen
   * @returns {Object} Der ausgewählte Adapter
   */
  selectAdapter(options = {}) {
    // Verwende den explizit angegebenen Adapter, falls vorhanden
    if (options.adapter && this.adapters.has(options.adapter)) {
      return this.adapters.get(options.adapter);
    }

    // Verwende den Adapter für das angegebene Netzwerk, falls vorhanden
    if (options.network && this.adapters.has(options.network)) {
      return this.adapters.get(options.network);
    }

    // Verwende den Adapter für den angegebenen Vertragstyp, falls vorhanden
    if (options.contractType && this.options.contracts[options.contractType]) {
      const network = this.options.contracts[options.contractType].network;
      if (network && this.adapters.has(network)) {
        return this.adapters.get(network);
      }
    }

    // Verwende den Standard-Adapter
    return this.adapters.get(this.options.defaultAdapter);
  }

  /**
   * Ruft die Adresse eines Vertrags ab
   * @param {string} contractType - Vertragstyp
   * @returns {string} Vertragsadresse
   */
  getContractAddress(contractType) {
    if (!this.options.contracts[contractType]) {
      throw new Error(`Vertragstyp '${contractType}' nicht konfiguriert`);
    }

    return this.options.contracts[contractType].address;
  }

  /**
   * Prägt einen NFT
   * @param {string} tokenURI - URI der Token-Metadaten
   * @param {string} recipient - Empfängeradresse
   * @param {Object} options - Prägungsoptionen
   * @returns {Promise<Object>} Prägungsergebnis
   */
  async mintNFT(tokenURI, recipient, options = {}) {
    try {
      logger.info(`Präge NFT für ${recipient} mit URI ${tokenURI}`);

      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter({ contractType: 'nft', ...options });

      // Hole die Vertragsadresse
      const contractAddress = options.contractAddress || this.getContractAddress('nft');

      // Präge den NFT
      const result = await adapter.mintNFT(contractAddress, recipient, tokenURI, options);

      logger.info(`NFT erfolgreich geprägt mit Token-ID ${result.tokenId}`);

      return result;
    } catch (error) {
      logger.error('Fehler beim Prägen des NFT', {
        error: error.message,
        tokenURI,
        recipient
      });
      throw error;
    }
  }

  /**
   * Prägt einen IP-NFT
   * @param {string} tokenURI - URI der Token-Metadaten
   * @param {string} recipient - Empfängeradresse
   * @param {Object} options - Prägungsoptionen
   * @returns {Promise<Object>} Prägungsergebnis
   */
  async mintIPNFT(tokenURI, recipient, options = {}) {
    try {
      logger.info(`Präge IP-NFT für ${recipient} mit URI ${tokenURI}`);

      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter({ contractType: 'ipNft', ...options });

      // Hole die Vertragsadresse
      const contractAddress = options.contractAddress || this.getContractAddress('ipNft');

      // Präge den IP-NFT
      const result = await adapter.mintIPNFT(contractAddress, recipient, tokenURI, options);

      logger.info(`IP-NFT erfolgreich geprägt mit Token-ID ${result.tokenId}`);

      return result;
    } catch (error) {
      logger.error('Fehler beim Prägen des IP-NFT', {
        error: error.message,
        tokenURI,
        recipient
      });
      throw error;
    }
  }

  /**
   * Fractionalisiert einen IP-NFT
   * @param {string} tokenId - Token-ID des IP-NFT
   * @param {Object} options - Fractionalisierungsoptionen
   * @returns {Promise<Object>} Fractionalisierungsergebnis
   */
  async fractionalizeIPNFT(tokenId, options = {}) {
    try {
      logger.info(`Fractionalisiere IP-NFT mit Token-ID ${tokenId}`);

      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter({ contractType: 'ipNft', ...options });

      // Hole die Vertragsadresse
      const contractAddress = options.contractAddress || this.getContractAddress('ipNft');

      // Fractionalisiere den IP-NFT
      const result = await adapter.fractionalizeIPNFT(contractAddress, tokenId, options);

      logger.info(`IP-NFT erfolgreich fractionalisiert mit Token-Adresse ${result.tokenAddress}`);

      return result;
    } catch (error) {
      logger.error('Fehler beim Fractionalisieren des IP-NFT', {
        error: error.message,
        tokenId
      });
      throw error;
    }
  }

  /**
   * Erstellt einen Governance-Token für einen IP-NFT
   * @param {string} tokenId - Token-ID des IP-NFT
   * @param {Object} options - Token-Erstellungsoptionen
   * @returns {Promise<Object>} Token-Erstellungsergebnis
   */
  async createIPGovernanceToken(tokenId, options = {}) {
    try {
      logger.info(`Erstelle Governance-Token für IP-NFT mit Token-ID ${tokenId}`);

      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter({ contractType: 'ipNft', ...options });

      // Hole die Vertragsadresse
      const contractAddress = options.contractAddress || this.getContractAddress('ipNft');

      // Erstelle den Governance-Token
      const result = await adapter.createIPGovernanceToken(contractAddress, tokenId, options);

      logger.info(`Governance-Token erfolgreich erstellt mit Token-Adresse ${result.tokenAddress}`);

      return result;
    } catch (error) {
      logger.error('Fehler beim Erstellen des Governance-Tokens', {
        error: error.message,
        tokenId
      });
      throw error;
    }
  }

  /**
   * Registriert eine Berechtigung für einen IP-NFT
   * @param {string} tokenId - Token-ID des IP-NFT
   * @param {string} grantee - Adresse des Berechtigungsempfängers
   * @param {string} permissionType - Art der Berechtigung
   * @param {Object} options - Berechtigungsoptionen
   * @returns {Promise<Object>} Registrierungsergebnis
   */
  async registerIPNFTPermission(tokenId, grantee, permissionType, options = {}) {
    try {
      logger.info(`Registriere Berechtigung ${permissionType} für IP-NFT ${tokenId} an ${grantee}`);

      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter({ contractType: 'ipNft', ...options });

      // Hole die Vertragsadresse
      const contractAddress = options.contractAddress || this.getContractAddress('ipNft');

      // Registriere die Berechtigung
      const result = await adapter.registerIPNFTPermission(contractAddress, tokenId, grantee, permissionType, options);

      logger.info(`Berechtigung erfolgreich registriert`);

      return result;
    } catch (error) {
      logger.error('Fehler beim Registrieren der Berechtigung', {
        error: error.message,
        tokenId,
        grantee,
        permissionType
      });
      throw error;
    }
  }

  /**
   * Widerruft eine Berechtigung für einen IP-NFT
   * @param {string} tokenId - Token-ID des IP-NFT
   * @param {string} grantee - Adresse des Berechtigungsempfängers
   * @param {string} permissionType - Art der Berechtigung
   * @param {Object} options - Optionen
   * @returns {Promise<Object>} Widerrufsergebnis
   */
  async revokeIPNFTPermission(tokenId, grantee, permissionType, options = {}) {
    try {
      logger.info(`Widerrufe Berechtigung ${permissionType} für IP-NFT ${tokenId} von ${grantee}`);

      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter({ contractType: 'ipNft', ...options });

      // Hole die Vertragsadresse
      const contractAddress = options.contractAddress || this.getContractAddress('ipNft');

      // Widerrufe die Berechtigung
      const result = await adapter.revokeIPNFTPermission(contractAddress, tokenId, grantee, permissionType);

      logger.info(`Berechtigung erfolgreich widerrufen`);

      return result;
    } catch (error) {
      logger.error('Fehler beim Widerrufen der Berechtigung', {
        error: error.message,
        tokenId,
        grantee,
        permissionType
      });
      throw error;
    }
  }

  /**
   * Ruft Informationen zu einem NFT ab
   * @param {string} tokenId - Token-ID des NFT
   * @param {Object} options - Abfrageoptionen
   * @returns {Promise<Object>} NFT-Informationen
   */
  async getNFTInfo(tokenId, options = {}) {
    try {
      logger.info(`Rufe Informationen für NFT mit Token-ID ${tokenId} ab`);

      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter({ contractType: 'nft', ...options });

      // Hole die Vertragsadresse
      const contractAddress = options.contractAddress || this.getContractAddress('nft');

      // Rufe die Informationen ab
      const owner = await adapter.callContractMethod(contractAddress, 'ownerOf', [tokenId]);
      const tokenURI = await adapter.callContractMethod(contractAddress, 'tokenURI', [tokenId]);

      logger.info(`Informationen für NFT mit Token-ID ${tokenId} abgerufen`);

      return {
        tokenId,
        contractAddress,
        owner,
        tokenURI
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen der NFT-Informationen', {
        error: error.message,
        tokenId
      });
      throw error;
    }
  }

  /**
   * Ruft Informationen zu einem IP-NFT ab
   * @param {string} tokenId - Token-ID des IP-NFT
   * @param {Object} options - Abfrageoptionen
   * @returns {Promise<Object>} IP-NFT-Informationen
   */
  async getIPNFTInfo(tokenId, options = {}) {
    try {
      logger.info(`Rufe Informationen für IP-NFT mit Token-ID ${tokenId} ab`);

      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter({ contractType: 'ipNft', ...options });

      // Hole die Vertragsadresse
      const contractAddress = options.contractAddress || this.getContractAddress('ipNft');

      // Rufe die Informationen ab
      const owner = await adapter.callContractMethod(contractAddress, 'ownerOf', [tokenId]);
      const tokenURI = await adapter.callContractMethod(contractAddress, 'tokenURI', [tokenId]);
      const royaltyInfo = await adapter.callContractMethod(contractAddress, 'royaltyInfo', [tokenId, 10000]);
      const contentCid = await adapter.callContractMethod(contractAddress, 'getContentCid', [tokenId]);
      const legalAgreementCid = await adapter.callContractMethod(contractAddress, 'getLegalAgreementCid', [tokenId]);
      const fractionToken = await adapter.callContractMethod(contractAddress, 'getFractionToken', [tokenId]);
      const governanceToken = await adapter.callContractMethod(contractAddress, 'getGovernanceToken', [tokenId]);

      logger.info(`Informationen für IP-NFT mit Token-ID ${tokenId} abgerufen`);

      return {
        tokenId,
        contractAddress,
        owner,
        tokenURI,
        royaltyPercentage: royaltyInfo.royaltyAmount / 100, // Konvertiere Basispunkte in Prozent
        contentCid,
        legalAgreementCid,
        fractionToken,
        governanceToken
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen der IP-NFT-Informationen', {
        error: error.message,
        tokenId
      });
      throw error;
    }
  }
}


  /**
   * Fractionalisiert einen IP-NFT
   * @param {string} tokenId - Token-ID des IP-NFT
   * @param {Object} options - Fractionalisierungsoptionen
   * @returns {Promise<Object>} Fractionalisierungsergebnis
   */
  async fractionalizeIPNFT(tokenId, options = {}) {
    try {
      logger.info(`Fractionalisiere IP-NFT mit Token-ID ${tokenId}`);

      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter({ contractType: 'ipNft', ...options });

      // Hole die Vertragsadresse
      const contractAddress = options.contractAddress || this.getContractAddress('ipNft');

      // Fractionalisiere den IP-NFT
      const result = await adapter.fractionalizeIPNFT(contractAddress, tokenId, options);

      logger.info(`IP-NFT erfolgreich fractionalisiert mit Token-Adresse ${result.tokenAddress}`);

      return result;
    } catch (error) {
      logger.error('Fehler beim Fractionalisieren des IP-NFT', {
        error: error.message,
        tokenId
      });
      throw error;
    }
  }

  /**
   * Erstellt einen Governance-Token für einen IP-NFT
   * @param {string} tokenId - Token-ID des IP-NFT
   * @param {Object} options - Token-Erstellungsoptionen
   * @returns {Promise<Object>} Token-Erstellungsergebnis
   */
  async createIPGovernanceToken(tokenId, options = {}) {
    try {
      logger.info(`Erstelle Governance-Token für IP-NFT mit Token-ID ${tokenId}`);

      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter({ contractType: 'ipNft', ...options });

      // Hole die Vertragsadresse
      const contractAddress = options.contractAddress || this.getContractAddress('ipNft');

      // Erstelle den Governance-Token
      const result = await adapter.createIPGovernanceToken(contractAddress, tokenId, options);

      logger.info(`Governance-Token erfolgreich erstellt mit Token-Adresse ${result.tokenAddress}`);

      return result;
    } catch (error) {
      logger.error('Fehler beim Erstellen des Governance-Tokens', {
        error: error.message,
        tokenId
      });
      throw error;
    }
  }

  /**
   * Registriert eine Berechtigung für einen IP-NFT
   * @param {string} tokenId - Token-ID des IP-NFT
   * @param {string} grantee - Adresse des Berechtigungsempfängers
   * @param {string} permissionType - Art der Berechtigung
   * @param {Object} options - Berechtigungsoptionen
   * @returns {Promise<Object>} Registrierungsergebnis
   */
  async registerIPNFTPermission(tokenId, grantee, permissionType, options = {}) {
    try {
      logger.info(`Registriere Berechtigung ${permissionType} für IP-NFT ${tokenId} an ${grantee}`);

      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter({ contractType: 'ipNft', ...options });

      // Hole die Vertragsadresse
      const contractAddress = options.contractAddress || this.getContractAddress('ipNft');

      // Registriere die Berechtigung
      const result = await adapter.registerIPNFTPermission(contractAddress, tokenId, grantee, permissionType, options);

      logger.info(`Berechtigung erfolgreich registriert`);

      return result;
    } catch (error) {
      logger.error('Fehler beim Registrieren der Berechtigung', {
        error: error.message,
        tokenId,
        grantee,
        permissionType
      });
      throw error;
    }
  }

  /**
   * Widerruft eine Berechtigung für einen IP-NFT
   * @param {string} tokenId - Token-ID des IP-NFT
   * @param {string} grantee - Adresse des Berechtigungsempfängers
   * @param {string} permissionType - Art der Berechtigung
   * @param {Object} options - Optionen
   * @returns {Promise<Object>} Widerrufsergebnis
   */
  async revokeIPNFTPermission(tokenId, grantee, permissionType, options = {}) {
    try {
      logger.info(`Widerrufe Berechtigung ${permissionType} für IP-NFT ${tokenId} von ${grantee}`);

      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter({ contractType: 'ipNft', ...options });

      // Hole die Vertragsadresse
      const contractAddress = options.contractAddress || this.getContractAddress('ipNft');

      // Widerrufe die Berechtigung
      const result = await adapter.revokeIPNFTPermission(contractAddress, tokenId, grantee, permissionType);

      logger.info(`Berechtigung erfolgreich widerrufen`);

      return result;
    } catch (error) {
      logger.error('Fehler beim Widerrufen der Berechtigung', {
        error: error.message,
        tokenId,
        grantee,
        permissionType
      });
      throw error;
    }
  }

  /**
   * Ruft Informationen zu einem NFT ab
   * @param {string} tokenId - Token-ID des NFT
   * @param {Object} options - Abfrageoptionen
   * @returns {Promise<Object>} NFT-Informationen
   */
  async getNFTInfo(tokenId, options = {}) {
    try {
      logger.info(`Rufe Informationen für NFT mit Token-ID ${tokenId} ab`);

      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter({ contractType: 'nft', ...options });

      // Hole die Vertragsadresse
      const contractAddress = options.contractAddress || this.getContractAddress('nft');

      // Rufe die Informationen ab
      const owner = await adapter.callContractMethod(contractAddress, 'ownerOf', [tokenId]);
      const tokenURI = await adapter.callContractMethod(contractAddress, 'tokenURI', [tokenId]);

      logger.info(`Informationen für NFT mit Token-ID ${tokenId} abgerufen`);

      return {
        tokenId,
        contractAddress,
        owner,
        tokenURI
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen der NFT-Informationen', {
        error: error.message,
        tokenId
      });
      throw error;
    }
  }

  /**
   * Ruft Informationen zu einem IP-NFT ab
   * @param {string} tokenId - Token-ID des IP-NFT
   * @param {Object} options - Abfrageoptionen
   * @returns {Promise<Object>} IP-NFT-Informationen
   */
  async getIPNFTInfo(tokenId, options = {}) {
    try {
      logger.info(`Rufe Informationen für IP-NFT mit Token-ID ${tokenId} ab`);

      // Wähle den passenden Adapter aus
      const adapter = this.selectAdapter({ contractType: 'ipNft', ...options });

      // Hole die Vertragsadresse
      const contractAddress = options.contractAddress || this.getContractAddress('ipNft');

      // Rufe die Informationen ab
      const owner = await adapter.callContractMethod(contractAddress, 'ownerOf', [tokenId]);
      const tokenURI = await adapter.callContractMethod(contractAddress, 'tokenURI', [tokenId]);
      const royaltyInfo = await adapter.callContractMethod(contractAddress, 'royaltyInfo', [tokenId, 10000]);
      const contentCid = await adapter.callContractMethod(contractAddress, 'getContentCid', [tokenId]);
      const legalAgreementCid = await adapter.callContractMethod(contractAddress, 'getLegalAgreementCid', [tokenId]);
      const fractionToken = await adapter.callContractMethod(contractAddress, 'getFractionToken', [tokenId]);
      const governanceToken = await adapter.callContractMethod(contractAddress, 'getGovernanceToken', [tokenId]);

      logger.info(`Informationen für IP-NFT mit Token-ID ${tokenId} abgerufen`);

      return {
        tokenId,
        contractAddress,
        owner,
        tokenURI,
        royaltyPercentage: royaltyInfo.royaltyAmount / 100, // Konvertiere Basispunkte in Prozent
        contentCid,
        legalAgreementCid,
        fractionToken,
        governanceToken
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen der IP-NFT-Informationen', {
        error: error.message,
        tokenId
      });
      throw error;
    }
  }
}



