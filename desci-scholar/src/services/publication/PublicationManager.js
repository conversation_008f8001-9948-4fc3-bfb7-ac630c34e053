/**
 * PublicationManager - Verwaltet wissenschaftliche Publikationen
 * Unterstützt Hochladen, Versionierung und Metadaten-Management
 */
class PublicationManager {
  constructor(options = {}) {
    this.storageManager = options.storageManager;
    this.blockchainManager = options.blockchainManager;
    this.database = options.database;
    this.events = new EventEmitter();
    
    this.options = {
      defaultStorage: 'ipfs',
      metadataSchema: 'schema/publication.json',
      ...options
    };
  }
  
  /**
   * Erstellt eine neue Publikation
   * @param {object} publicationData - Publikationsdaten
   * @param {object} options - Optionen für die Erstellung
   * @returns {Promise<object>} Erstellungsergebnis
   */
  async createPublication(publicationData, options = {}) {
    // Validiere Publikationsdaten gegen Schema
    this._validatePublicationData(publicationData);
    
    // Speichere Datei im ausgewählten Speicher
    const storageAdapter = this.storageManager.selectAdapter({
      adapter: options.storage || this.options.defaultStorage
    });
    
    const fileResult = await storageAdapter.storeFile(
      publicationData.file,
      { contentType: publicationData.contentType }
    );
    
    // Erstelle Metadaten-Objekt
    const metadata = {
      title: publicationData.title,
      abstract: publicationData.abstract,
      authors: publicationData.authors,
      keywords: publicationData.keywords,
      doi: publicationData.doi,
      fileHash: fileResult.hash,
      fileUrl: fileResult.url,
      contentType: publicationData.contentType,
      createdAt: new Date().toISOString(),
      version: '1.0.0'
    };
    
    // Speichere Metadaten im Speicher
    const metadataResult = await storageAdapter.storeJSON(
      metadata,
      { filename: `${fileResult.hash}.json` }
    );
    
    // Speichere Referenz in der Datenbank
    const dbResult = await this.database.publications.create({
      title: metadata.title,
      abstract: metadata.abstract,
      authors: metadata.authors,
      doi: metadata.doi,
      fileHash: fileResult.hash,
      metadataHash: metadataResult.hash,
      metadataUrl: metadataResult.url,
      status: 'published',
      createdAt: new Date()
    });
    
    this.events.emit('publication:created', {
      publicationId: dbResult.id,
      doi: metadata.doi
    });
    
    return {
      publicationId: dbResult.id,
      metadata,
      storage: {
        fileHash: fileResult.hash,
        fileUrl: fileResult.url,
        metadataHash: metadataResult.hash,
        metadataUrl: metadataResult.url
      }
    };
  }
  
  /**
   * Ruft eine Publikation anhand ihrer ID ab
   * @param {string} publicationId - Publikations-ID
   * @param {object} options - Optionen für den Abruf
   * @returns {Promise<object>} Publikationsdaten
   */
  async getPublication(publicationId, options = {}) {
    // Rufe Publikationsreferenz aus der Datenbank ab
    const publication = await this.database.publications.findById(publicationId);
    
    if (!publication) {
      throw new Error(`Publikation mit ID ${publicationId} nicht gefunden`);
    }
    
    // Rufe Metadaten aus dem Speicher ab
    const storageAdapter = this.storageManager.selectAdapter({
      adapter: options.storage || this.options.defaultStorage
    });
    
    const metadata = await storageAdapter.getJSON(publication.metadataUrl);
    
    return {
      ...publication,
      metadata
    };
  }
  
  /**
   * Aktualisiert eine Publikation
   * @param {string} publicationId - Publikations-ID
   * @param {object} updates - Zu aktualisierende Daten
   * @param {object} options - Optionen für die Aktualisierung
   * @returns {Promise<object>} Aktualisierungsergebnis
   */
  async updatePublication(publicationId, updates, options = {}) {
    // Rufe aktuelle Publikation ab
    const publication = await this.getPublication(publicationId);
    
    // Aktualisiere Metadaten
    const updatedMetadata = {
      ...publication.metadata,
      ...updates,
      updatedAt: new Date().toISOString(),
      version: this._incrementVersion(publication.metadata.version)
    };
    
    // Speichere aktualisierte Metadaten
    const storageAdapter = this.storageManager.selectAdapter({
      adapter: options.storage || this.options.defaultStorage
    });
    
    const metadataResult = await storageAdapter.storeJSON(
      updatedMetadata,
      { filename: `${publication.fileHash}_v${updatedMetadata.version}.json` }
    );
    
    // Aktualisiere Datenbank
    await this.database.publications.update(publicationId, {
      metadataHash: metadataResult.hash,
      metadataUrl: metadataResult.url,
      updatedAt: new Date()
    });
    
    this.events.emit('publication:updated', {
      publicationId,
      version: updatedMetadata.version
    });
    
    return {
      publicationId,
      metadata: updatedMetadata,
      storage: {
        metadataHash: metadataResult.hash,
        metadataUrl: metadataResult.url
      }
    };
  }
  
  /**
   * Konvertiert eine Publikation mit DOI zu einem NFT
   * @param {string} publicationId - Publikations-ID
   * @param {object} options - Optionen für die Konvertierung
   * @returns {Promise<object>} Konvertierungsergebnis
   */
  async convertToNft(publicationId, options = {}) {
    // Diese Methode wird im DOI-zu-NFT-Modul implementiert
    // Hier nur ein Platzhalter für die Integration
    const doiNftService = options.doiNftService || this.doiNftService;
    
    if (!doiNftService) {
      throw new Error('DOI-NFT-Service ist erforderlich für die Konvertierung');
    }
    
    return doiNftService.convertPublicationToNft(publicationId, options);
  }
  
  /**
   * Validiert Publikationsdaten gegen Schema
   * @private
   * @param {object} data - Zu validierende Daten
   */
  _validatePublicationData(data) {
    // Implementierung der Schema-Validierung
    const requiredFields = ['title', 'abstract', 'authors', 'file'];
    
    for (const field of requiredFields) {
      if (!data[field]) {
        throw new Error(`Feld '${field}' ist erforderlich`);
      }
    }
    
    // Weitere Validierungslogik hier...
  }
  
  /**
   * Inkrementiert die Versionsnummer
   * @private
   * @param {string} version - Aktuelle Version
   * @returns {string} Inkrementierte Version
   */
  _incrementVersion(version) {
    const parts = version.split('.');
    parts[2] = (parseInt(parts[2], 10) + 1).toString();
    return parts.join('.');
  }
}

module.exports = PublicationManager;