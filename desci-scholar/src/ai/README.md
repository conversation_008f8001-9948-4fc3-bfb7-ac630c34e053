# KI-Agenten für DeSci-Scholar

Dieses Verzeichnis enthält KI-Agenten, die verschiedene Aspekte der DeSci-Scholar-Plattform unterstützen.

## Verfügbare Agenten

### ResearchLearningAgent

Der `ResearchLearningAgent` kann aus wissenschaftlichen Publikationen auf der Plattform lernen, Zusammenhänge zwischen Forschungsgebieten erkennen, Trends identifizieren und Forschern bei ihrer Arbeit helfen.

**Hauptfunktionen:**

- **Wissensbasis aufbauen**: Lernt aus wissenschaftlichen Publikationen und extrahiert Konzepte, Domänen und Trends
- **Forschungsfragen beantworten**: Beantwortet Fragen basierend auf dem gelernten Wissen und relevanten Publikationen
- **Forschungstrends identifizieren**: Analysiert aktuelle Publikationen, um aufkommende Trends zu erkennen
- **Forschungsempfehlungen generieren**: Gibt personalisierte Empfehlungen für Forscher basierend auf ihren bisherigen Arbeiten

**Verwendung:**

```javascript
import ResearchLearningAgent from '../ai/agents/ResearchLearningAgent.js';
import { DatabaseService } from '../database/DatabaseService.js';
import { VectorDatabase } from '../database/VectorDatabase.js';
import { TextEmbeddingService } from '../ai/TextEmbeddingService.js';

// Initialisiere Dienste
const databaseService = new DatabaseService();
const vectorDatabase = new VectorDatabase();
const textEmbeddingService = new TextEmbeddingService();

// Erstelle und initialisiere den ResearchLearningAgent
const researchLearningAgent = new ResearchLearningAgent({
  databaseService,
  vectorDatabase,
  textEmbeddingService
});

await researchLearningAgent.initialize();

// Beantworte eine Forschungsfrage
const answer = await researchLearningAgent.answerResearchQuestion(
  'Welche Fortschritte gibt es bei der Anwendung von Quantencomputing in der Materialwissenschaft?'
);

// Identifiziere Forschungstrends
const trends = await researchLearningAgent.identifyResearchTrends();

// Generiere Forschungsempfehlungen für einen Forscher
const recommendations = await researchLearningAgent.generateResearchRecommendations(researcher);
```

### ProposalVettingAgent

Der `ProposalVettingAgent` bewertet Forschungsvorschläge nach verschiedenen Kriterien wie Neuartigkeit, Methodik, Durchführbarkeit und Auswirkung.

**Hauptfunktionen:**

- **Vorschläge bewerten**: Bewertet Forschungsvorschläge nach definierten Kriterien
- **Zusammenfassungen erstellen**: Fasst Peer-Reviews zusammen und identifiziert Konsens und Widersprüche

**Verwendung:**

```javascript
import { ProposalVettingAgent } from '../ai/agents/SpecializedAgents.js';

// Erstelle und initialisiere den ProposalVettingAgent
const proposalVettingAgent = new ProposalVettingAgent({
  criteria: {
    novelty: 0.3,
    methodology: 0.3,
    feasibility: 0.2,
    impact: 0.2
  }
});

// Bewerte einen Forschungsvorschlag
const evaluation = await proposalVettingAgent.evaluateProposal({
  title: 'Quantencomputing für nachhaltige Energielösungen',
  abstract: 'Dieser Vorschlag untersucht die Anwendung von Quantenalgorithmen...',
  methodology: 'Wir werden Quantensimulationen verwenden, um...',
  resources: 'Benötigte Ressourcen umfassen Zugang zu Quantencomputern...',
  timeline: 'Das Projekt wird in drei Phasen über 18 Monate durchgeführt...'
});
```

### ResearchMatchingAgent

Der `ResearchMatchingAgent` findet passende Kollaborateure und Projekte für Forscher basierend auf ihren Interessen und Expertise.

**Hauptfunktionen:**

- **Kollaborateure finden**: Identifiziert geeignete Forscher für Zusammenarbeit
- **Projekte empfehlen**: Empfiehlt passende Forschungsprojekte
- **Trends analysieren**: Analysiert Forschungstrends basierend auf Publikationen

**Verwendung:**

```javascript
import ResearchMatchingAgent from '../ai/ResearchMatchingAgent.js';

// Erstelle und initialisiere den ResearchMatchingAgent
const matchingAgent = new ResearchMatchingAgent({
  similarityThreshold: 0.65,
  maxRecommendations: 10,
  considerCitations: true,
  considerKeywords: true
});

await matchingAgent.initialize();

// Finde passende Kollaborateure
const collaborators = await matchingAgent.findCollaborators(researcher, researcherPool);

// Empfehle Projekte
const projects = await matchingAgent.recommendProjects(researcher, availableProjects);

// Analysiere Trends
const trends = await matchingAgent.analyzeTrends(publications);
```

## Integration in die Plattform

Die KI-Agenten können in verschiedene Teile der DeSci-Scholar-Plattform integriert werden:

1. **Forschungsassistent**: Integriere den `ResearchLearningAgent` in die Benutzeroberfläche, um Forschern bei der Beantwortung von Fragen zu helfen und Empfehlungen zu geben.

2. **Peer-Review-System**: Verwende den `ProposalVettingAgent`, um Peer-Reviews zu unterstützen und zusammenzufassen.

3. **Kollaborationsplattform**: Integriere den `ResearchMatchingAgent`, um Forscher mit ähnlichen Interessen zusammenzubringen.

4. **Trendanalyse**: Verwende den `ResearchLearningAgent`, um regelmäßige Berichte über Forschungstrends zu erstellen.

## Technische Anforderungen

Die KI-Agenten benötigen folgende Dienste:

- **OpenAI API-Schlüssel**: Für die Generierung von Text und die Analyse von Inhalten
- **Vektordatenbank**: Für semantische Suche und Ähnlichkeitsvergleiche
- **Textembedding-Dienst**: Für die Umwandlung von Text in Vektoren
- **Datenbank**: Für die Speicherung von Publikationen, Forschern und anderen Daten

## Datenschutz und Ethik

Bei der Verwendung der KI-Agenten sollten folgende Aspekte beachtet werden:

- **Datenschutz**: Stellen Sie sicher, dass personenbezogene Daten geschützt sind und die Einwilligung der Forscher eingeholt wird.
- **Transparenz**: Informieren Sie die Nutzer darüber, wann KI-Agenten verwendet werden und wie sie funktionieren.
- **Fairness**: Überwachen Sie die Empfehlungen der Agenten, um sicherzustellen, dass sie fair und unvoreingenommen sind.
- **Menschliche Aufsicht**: Wichtige Entscheidungen sollten immer von Menschen überprüft werden.

## Weiterentwicklung

Die KI-Agenten können in folgenden Bereichen weiterentwickelt werden:

- **Domänenspezifische Modelle**: Trainieren Sie spezialisierte Modelle für bestimmte Forschungsgebiete.
- **Mehrsprachige Unterstützung**: Erweitern Sie die Agenten, um mehrere Sprachen zu unterstützen.
- **Interaktive Dialoge**: Implementieren Sie dialogbasierte Interaktionen für tiefergehende Forschungsunterstützung.
- **Visuelle Analyse**: Integrieren Sie die Analyse von Diagrammen, Grafiken und anderen visuellen Elementen in wissenschaftlichen Publikationen.

## Beispiele

Beispiele für die Verwendung der KI-Agenten finden Sie im Verzeichnis `src/examples/`.