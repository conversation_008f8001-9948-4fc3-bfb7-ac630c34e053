import { logger } from '../utils/logger.js';

/**
 * Prozessor für die Verarbeitung und Aufbereitung von Dokumenten
 */
export class DocumentProcessor {
  /**
   * Erstellt eine neue DocumentProcessor-Instanz
   * @param {Object} options - Konfigurationsoptionen
   * @param {number} options.maxChunkSize - Maximale Größe eines Chunks in Zeichen
   * @param {number} options.chunkOverlap - Überlappung zwischen Chunks in Zeichen
   * @param {boolean} options.removeStopwords - Ob Stoppwörter entfernt werden sollen
   * @param {boolean} options.stemming - Ob Stemming angewendet werden soll
   * @param {string[]} options.languages - Zu unterstützende Sprachen
   */
  constructor(options = {}) {
    const {
      maxChunkSize = 1000,
      chunkOverlap = 200,
      removeStopwords = true,
      stemming = false,
      languages = ['en', 'de']
    } = options;
    
    this.maxChunkSize = maxChunkSize;
    this.chunkOverlap = chunkOverlap;
    this.removeStopwords = removeStopwords;
    this.stemming = stemming;
    this.languages = languages;
    
    // Stoppwörter für unterstützte Sprachen
    this.stopwords = {
      en: new Set(['a', 'an', 'the', 'and', 'or', 'but', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'in', 'on', 'at', 'to', 'for', 'with', 'by', 'about', 'against', 'between', 'into', 'through', 'during', 'before', 'after', 'above', 'below', 'from', 'up', 'down', 'of', 'off', 'over', 'under', 'again', 'further', 'then', 'once', 'here', 'there', 'when', 'where', 'why', 'how', 'all', 'any', 'both', 'each', 'few', 'more', 'most', 'other', 'some', 'such', 'no', 'nor', 'not', 'only', 'own', 'same', 'so', 'than', 'too', 'very', 's', 't', 'can', 'will', 'just', 'don', 'should', 'now']),
      de: new Set(['der', 'die', 'das', 'den', 'dem', 'des', 'ein', 'eine', 'eines', 'einem', 'einen', 'einer', 'und', 'oder', 'aber', 'ist', 'sind', 'war', 'waren', 'sein', 'gewesen', 'in', 'an', 'auf', 'zu', 'für', 'mit', 'durch', 'über', 'unter', 'von', 'bei', 'nach', 'aus', 'vor', 'hinter', 'neben', 'zwischen', 'wegen', 'während', 'ohne', 'um', 'am', 'im', 'zum', 'zur', 'beim', 'vom', 'zum', 'zur', 'als', 'wie', 'wenn', 'weil', 'dass', 'ob', 'obwohl', 'damit', 'sodass', 'indem', 'denn', 'da', 'wo', 'wann', 'warum', 'wie', 'alle', 'jede', 'jeder', 'jedes', 'manche', 'einige', 'viele', 'wenige', 'andere', 'solche', 'keine', 'nicht', 'nur', 'sehr', 'auch', 'noch', 'schon', 'wieder', 'immer', 'nie', 'so', 'also'])
    };
    
    this.stats = {
      documentsProcessed: 0,
      chunksCreated: 0,
      tokensProcessed: 0,
      errors: 0
    };
  }
  
  /**
   * Initialisiert den DocumentProcessor
   * @returns {Promise<boolean>} Erfolgsstatus
   */
  async initialize() {
    try {
      logger.info('DocumentProcessor: Initialisiere', {
        maxChunkSize: this.maxChunkSize,
        chunkOverlap: this.chunkOverlap,
        languages: this.languages
      });
      
      // Hier könnten zusätzliche Ressourcen geladen werden, z.B.
