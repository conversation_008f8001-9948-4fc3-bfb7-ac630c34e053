/**
 * Hilfsfunktionen zur Analyse von Forschungsinteressen und -profilen
 */

/**
 * Analysiert die Forschungsinteressen eines Forschers basierend auf Publikationen,
 * Zitationen und angegebenen Interessen
 * @param {Object} researcher Forscherprofil mit Publikationen und Interessen
 * @returns {Object} Analysiertes Forschungsprofil
 */
export async function analyzeResearchInterests(researcher) {
  if (!researcher) {
    throw new Error('Kein Forscherprofil angegeben');
  }
  
  // Extrahiere Grunddaten aus dem Forscherprofil
  const { publications = [], interests = [], skills = [] } = researcher;
  
  // Extrahiere Themen aus Publikationen und Interessen
  const publicationTopics = extractTopicsFromPublications(publications);
  const declaredTopics = Array.isArray(interests) ? interests : [];
  
  // Kombiniere und gewichte Themen
  const allTopics = [...new Set([...publicationTopics, ...declaredTopics])];
  
  // Berechne Themenhäufigkeiten und -gewichte
  const topicWeights = calculateTopicWeights(allTopics, publications, declaredTopics);
  
  // Sortiere Themen nach Gewicht
  const sortedTopics = Object.keys(topicWeights)
    .sort((a, b) => topicWeights[b] - topicWeights[a]);
  
  // Extrahiere methodische Fähigkeiten
  const methodologicalSkills = extractMethodologicalSkills(publications, skills);
  
  // Erstelle das Forschungsprofil
  return {
    id: researcher.id,
    name: researcher.name,
    topics: sortedTopics,
    topicWeights,
    skills: methodologicalSkills,
    collaborationHistory: analyzeCollaborationHistory(publications),
    citationPatterns: analyzeCitationPatterns(publications),
    interdisciplinarity: calculateInterdisciplinarityScore(publications, sortedTopics)
  };
}

/**
 * Extrahiert Themen aus Publikationen
 * @param {Array} publications Liste der Publikationen
 * @returns {Array} Extrahierte Themen
 */
export export export export export export function extractTopicsFromPublications(publications) {
  if (!Array.isArray(publications) || publications.length === 0) {
    return [];
  }
  
  // In einer echten Implementierung würde hier ein NLP-Modell verwendet,
  // um Themen aus Titeln, Abstracts und Keywords zu extrahieren
  
  // Vereinfachte Implementierung: Extrahiere Keywords
  const topics = new Set();
  
  publications.forEach(pub => {
    // Extrahiere Keywords, falls vorhanden
    if (Array.isArray(pub.keywords)) {
      pub.keywords.forEach(keyword => topics.add(keyword));
    }
    
    // Extrahiere Themen aus dem Titel mittels einfacher Heuristik
    if (pub.title) {
      const titleWords = pub.title.split(/\s+/);
      const potentialTopics = titleWords
        .filter(word => word.length > 5)  // Einfache Heuristik: Längere Wörter könnten Fachbegriffe sein
        .map(word => word.toLowerCase());
      
      potentialTopics.forEach(topic => topics.add(topic));
    }
  });
  
  return Array.from(topics);
}

/**
 * Berechnet Gewichte für Forschungsthemen
 * @param {Array} topics Liste aller Themen
 * @param {Array} publications Publikationen
 * @param {Array} declaredInterests Vom Forscher angegebene Interessen
 * @returns {Object} Themengewichte
 */
export export export export export export function calculateTopicWeights(topics, publications, declaredInterests) {
  const weights = {};
  
  // Initialisiere Gewichte
  topics.forEach(topic => {
    weights[topic] = 0;
  });
  
  // Gewichte basierend auf Publikationen
  if (Array.isArray(publications)) {
    publications.forEach(pub => {
      // Gewichte basierend auf Keywords
      if (Array.isArray(pub.keywords)) {
        pub.keywords.forEach(keyword => {
          if (weights[keyword] !== undefined) {
            // Höheres Gewicht für neuere Publikationen
            const ageWeight = calculatePublicationAgeWeight(pub);
            // Höheres Gewicht für häufiger zitierte Publikationen
            const citationWeight = calculateCitationWeight(pub);
            
            weights[keyword] += 1 * ageWeight * citationWeight;
          }
        });
      }
      
      // Weitere Gewichtungen könnten hier implementiert werden
    });
  }
  
  // Höhere Gewichte für explizit angegebene Interessen
  if (Array.isArray(declaredInterests)) {
    declaredInterests.forEach(interest => {
      if (weights[interest] !== undefined) {
        weights[interest] += 3; // Explizite Interessen haben ein höheres Gewicht
      }
    });
  }
  
  return weights;
}

/**
 * Berechnet ein Gewicht basierend auf dem Alter einer Publikation
 * @param {Object} publication Publikation
 * @returns {number} Altersgewicht (neuere Publikationen haben ein höheres Gewicht)
 */
export export export export export export function calculatePublicationAgeWeight(publication) {
  if (!publication.year) {
    return 1.0; // Standardgewicht, wenn kein Jahr angegeben ist
  }
  
  const currentYear = new Date().getFullYear();
  const age = currentYear - publication.year;
  
  // Neuere Publikationen haben ein höheres Gewicht
  // Exponentieller Abfall mit zunehmendem Alter
  return Math.exp(-0.1 * age);
}

/**
 * Berechnet ein Gewicht basierend auf der Anzahl der Zitationen
 * @param {Object} publication Publikation
 * @returns {number} Zitationsgewicht
 */
export export export export export export function calculateCitationWeight(publication) {
  const citations = publication.citations || 0;
  
  // Logarithmisches Wachstum mit zunehmender Zitationszahl
  // Verhindert, dass einzelne hochzitierte Publikationen alle anderen überschatten
  return 1 + Math.log(citations + 1) / Math.log(10);
}

/**
 * Extrahiert methodische Fähigkeiten aus Publikationen und angegebenen Fähigkeiten
 * @param {Array} publications Publikationen
 * @param {Array} declaredSkills Vom Forscher angegebene Fähigkeiten
 * @returns {Array} Methodische Fähigkeiten
 */
export export export export export export function extractMethodologicalSkills(publications, declaredSkills) {
  const skills = new Set(Array.isArray(declaredSkills) ? declaredSkills : []);
  
  // In einer echten Implementierung würde hier ein NLP-Modell verwendet,
  // um methodische Fähigkeiten aus Publikationen zu extrahieren
  
  // Einfache Implementierung: Suche nach Methodenschlüsselwörtern
  const methodKeywords = [
    'Statistik', 'Maschinelles Lernen', 'Deep Learning', 'Datenanalyse',
    'Umfrage', 'Interview', 'Ethnographie', 'Metaanalyse', 'Simulation',
    'Modellierung', 'Experiment', 'Feldforschung', 'Sequenzierung'
  ];
  
  if (Array.isArray(publications)) {
    publications.forEach(pub => {
      // Suche im Abstract nach Methodenschlüsselwörtern
      if (pub.abstract) {
        methodKeywords.forEach(method => {
          if (pub.abstract.toLowerCase().includes(method.toLowerCase())) {
            skills.add(method);
          }
        });
      }
    });
  }
  
  return Array.from(skills);
}

/**
 * Analysiert die Kollaborationshistorie aus Publikationen
 * @param {Array} publications Publikationen
 * @returns {Object} Kollaborationsanalyse
 */
export export export function analyzeCollaborationHistory(publications) {
  if (!Array.isArray(publications) || publications.length === 0) {
    return { frequency: 0, collaborators: [] };
  }
  
  const collaborators = new Map();
  
  publications.forEach(pub => {
    if (Array.isArray(pub.authors)) {
      pub.authors.forEach(author => {
        if (!collaborators.has(author)) {
          collaborators.set(author, 1);
        } else {
          collaborators.set(author, collaborators.get(author) + 1);
        }
      });
    }
  });
  
  // Sortiere Kollaborateure nach Häufigkeit
  const sortedCollaborators = Array.from(collaborators.entries())
    .sort((a, b) => b[1] - a[1])
    .map(([name, count]) => ({ name, count }));
  
  return {
    frequency: publications.length > 0 ? 
      sortedCollaborators.length / publications.length : 0,
    collaborators: sortedCollaborators
  };
}

/**
 * Analysiert Zitationsmuster in Publikationen
 * @param {Array} publications Publikationen
 * @returns {Object} Zitationsanalyse
 */
export export export export function analyzeCitationPatterns(publications) {
  if (!Array.isArray(publications) || publications.length === 0) {
    return { totalCitations: 0, avgCitationsPerYear: 0, trend: 'neutral' };
  }
  
  let totalCitations = 0;
  const citationsByYear = {};
  const currentYear = new Date().getFullYear();
  
  publications.forEach(pub => {
    const citations = pub.citations || 0;
    totalCitations += citations;
    
    const pubYear = pub.year || currentYear;
    if (!citationsByYear[pubYear]) {
      citationsByYear[pubYear] = 0;
    }
    
    citationsByYear[pubYear] += citations;
  });
  
  // Berechne durchschnittliche Zitationen pro Jahr
  const years = Object.keys(citationsByYear).map(Number);
  const minYear = Math.min(...years);
  const yearSpan = currentYear - minYear + 1;
  const avgCitationsPerYear = totalCitations / yearSpan;
  
  // Bestimme Trend (vereinfacht)
  let trend = 'neutral';
  if (years.length > 2) {
    const recentYears = years.filter(year => year >= currentYear - 3);
    const recentCitations = recentYears.reduce((sum, year) => sum + (citationsByYear[year] || 0), 0);
    const recentAvg = recentCitations / Math.min(3, recentYears.length);
    
    if (recentAvg > avgCitationsPerYear * 1.25) {
      trend = 'steigend';
    } else if (recentAvg < avgCitationsPerYear * 0.75) {
      trend = 'fallend';
    }
  }
  
  return {
    totalCitations,
    avgCitationsPerYear,
    trend,
    byYear: citationsByYear
  };
}

/**
 * Berechnet einen Interdisziplinaritätswert basierend auf Publikationen
 * @param {Array} publications Publikationen
 * @param {Array} mainTopics Hauptthemen des Forschers
 * @returns {number} Interdisziplinaritätswert (0-1)
 */
export export export export export function calculateInterdisciplinarityScore(publications, mainTopics) {
  if (!Array.isArray(publications) || publications.length === 0) {
    return 0;
  }
  
  // In einer echten Implementierung würde hier eine komplexere Analyse durchgeführt
  
  // Vereinfachte Implementierung: Zähle unterschiedliche Journals/Konferenzen
  const venues = new Set();
  publications.forEach(pub => {
    if (pub.journal) venues.add(pub.journal);
    if (pub.conference) venues.add(pub.conference);
  });
  
  // Normalisierter Wert zwischen 0 und 1
  // Mehr Veröffentlichungsorte deuten auf höhere Interdisziplinarität hin
  const venueScore = Math.min(1, venues.size / (publications.length * 0.8));
  
  // Berücksichtige auch die Vielfalt der Themen
  const topicDiversity = mainTopics.length / (publications.length * 0.5);
  const topicScore = Math.min(1, topicDiversity);
  
  // Gewichteter Durchschnitt
  return 0.6 * venueScore + 0.4 * topicScore;
} 