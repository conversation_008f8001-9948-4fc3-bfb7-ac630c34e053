/**
 * Hilfsfunktionen zur automatischen Extraktion von Zitationen und Referenzen
 * aus wissenschaftlichen Publikationen
 */

import fetch from 'node-fetch';

/**
 * Extrahiert Zitationen und Referenzen aus einer Publikation mit DOI
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Extrahierte Zitationen und Referenzen
 */
export async function extractCitationsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche zuerst Crossref
    const crossrefData = await fetchCrossrefCitations(doi);
    if (crossrefData && crossrefData.citations && crossrefData.citations.length > 0) {
      return crossrefData;
    }
    
    // Wenn Crossref keine Ergebnis<PERSON> liefert, versuche Semantic Scholar
    const semanticScholarData = await fetchSemanticScholarCitations(doi);
    if (semanticScholarData && semanticScholarData.citations && semanticScholarData.citations.length > 0) {
      return semanticScholarData;
    }
    
    // Wenn beide keine Ergebnisse liefern, versuche OpenCitations
    const openCitationsData = await fetchOpenCitationsCitations(doi);
    
    return openCitationsData;
  } catch (error) {
    console.error('Fehler bei der Zitationsextraktion:', error);
    return {
      doi,
      citations: [],
      references: [],
      error: error.message
    };
  }
}

/**
 * Ruft Zitationen von Crossref ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchCrossrefCitations(doi) {
  try {
    // Crossref API für Referenzen (was die Publikation zitiert)
    const referencesUrl = `https://api.crossref.org/works/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
      }
    });
    
    if (!referencesResponse.ok) {
      throw new Error(`Crossref API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Referenzen
    const references = [];
    if (referencesData.message && referencesData.message.reference) {
      referencesData.message.reference.forEach(ref => {
        references.push({
          doi: ref.DOI,
          title: ref['article-title'] || ref.title,
          authors: ref.author ? [ref.author] : [],
          year: ref.year,
          journal: ref['journal-title']
        });
      });
    }
    
    // Für Zitationen (was die Publikation zitiert) gibt es keine direkte Crossref-API
    // Wir könnten OpenCitations oder Semantic Scholar verwenden
    
    return {
      doi,
      citations: [], // Crossref bietet keine direkte API für eingehende Zitationen
      references,
      source: 'crossref'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Crossref-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von Semantic Scholar ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchSemanticScholarCitations(doi) {
  try {
    // Semantic Scholar API
    const apiUrl = `https://api.semanticscholar.org/v1/paper/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      throw new Error(`Semantic Scholar API-Fehler: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Extrahiere Zitationen (eingehende Zitationen)
    const citations = data.citations ? data.citations.map(citation => ({
      doi: citation.doi,
      title: citation.title,
      authors: citation.authors ? citation.authors.map(author => author.name) : [],
      year: citation.year,
      journal: citation.venue
    })) : [];
    
    // Extrahiere Referenzen (ausgehende Zitationen)
    const references = data.references ? data.references.map(reference => ({
      doi: reference.doi,
      title: reference.title,
      authors: reference.authors ? reference.authors.map(author => author.name) : [],
      year: reference.year,
      journal: reference.venue
    })) : [];
    
    return {
      doi,
      citations,
      references,
      source: 'semantic_scholar'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Semantic Scholar-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von OpenCitations ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchOpenCitationsCitations(doi) {
  try {
    // OpenCitations API für eingehende Zitationen
    const citationsUrl = `https://opencitations.net/index/api/v1/citations/${encodeURIComponent(doi)}`;
    const citationsResponse = await fetch(citationsUrl);
    
    if (!citationsResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${citationsResponse.status}`);
    }
    
    const citationsData = await citationsResponse.json();
    
    // OpenCitations API für ausgehende Referenzen
    const referencesUrl = `https://opencitations.net/index/api/v1/references/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl);
    
    if (!referencesResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Zitationen
    const citations = citationsData.map(citation => ({
      doi: citation.citing,
      year: citation.creation ? citation.creation.substring(0, 4) : null
    }));
    
    // Extrahiere Referenzen
    const references = referencesData.map(reference => ({
      doi: reference.cited,
      year: reference.creation ? reference.creation.substring(0, 4) : null
    }));
    
    // Anreicherung der Metadaten für Zitationen und Referenzen
    const enrichedCitations = await enrichCitationsMetadata(citations);
    const enrichedReferences = await enrichCitationsMetadata(references);
    
    return {
      doi,
      citations: enrichedCitations,
      references: enrichedReferences,
      source: 'opencitations'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von OpenCitations-Zitationen:', error);
    return {
      doi,
      citations: [],
      references: [],
      source: 'opencitations',
      error: error.message
    };
  }
}

/**
 * Reichert Zitationsmetadaten mit Titel und Autoren an
 * @param {Array} citations - Liste von Zitationen mit DOIs
 * @returns {Promise<Array>} - Angereicherte Zitationen
 */
async function enrichCitationsMetadata(citations) {
  const enrichedCitations = [];
  
  for (const citation of citations) {
    if (!citation.doi) {
      enrichedCitations.push(citation);
      continue;
    }
    
    try {
      // Crossref API für Metadaten
      const url = `https://api.crossref.org/works/${encodeURIComponent(citation.doi)}`;
      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
        }
      });
      
      if (!response.ok) {
        enrichedCitations.push(citation);
        continue;
      }
      
      const data = await response.json();
      const message = data.message;
      
      // Extrahiere Titel
      const title = message.title ? message.title[0] : null;
      
      // Extrahiere Autoren
      const authors = message.author ? message.author.map(author => 
        `${author.given || ''} ${author.family || ''}`.trim()
      ) : [];
      
      // Extrahiere Journal
      const journal = message['container-title'] ? message['container-title'][0] : null;
      
      // Extrahiere Jahr
      const year = message.published ? 
        (message.published['date-parts'] ? message.published['date-parts'][0][0] : null) : 
        citation.year;
      
      enrichedCitations.push({
        ...citation,
        title,
        authors,
        journal,
        year
      });
    } catch (error) {
      console.error(`Fehler beim Anreichern der Metadaten für DOI ${citation.doi}:`, error);
      enrichedCitations.push(citation);
    }
  }
  
  return enrichedCitations;
}

/**
 * Extrahiert Impact-Faktoren und andere Metriken für eine Publikation
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Metriken
 */
export async function extractMetricsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche Altmetric
    const altmetricData = await fetchAltmetricData(doi);
    
    // Versuche Dimensions
    const dimensionsData = await fetchDimensionsData(doi);
    
    // Kombiniere die Daten
    return {
      doi,
      altmetric: altmetricData,
      dimensions: dimensionsData,
      timestamp: Date.now()
    };
  } catch (error) {
    console.error('Fehler bei der Metrikextraktion:', error);
    return {
      doi,
      error: error.message
    };
  }
}

/**
 * Ruft Altmetric-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Altmetric-Daten
 */
async function fetchAltmetricData(doi) {
  try {
    // Altmetric API
    const apiUrl = `https://api.altmetric.com/v1/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      score: data.score,
      readers: {
        mendeley: data.readers ? data.readers.mendeley : 0,
        citeulike: data.readers ? data.readers.citeulike : 0
      },
      mentions: {
        twitter: data.cited_by_tweeters_count || 0,
        facebook: data.cited_by_fbwalls_count || 0,
        blogs: data.cited_by_feeds_count || 0,
        news: data.cited_by_msm_count || 0,
        reddit: data.cited_by_rdts_count || 0,
        wikipedia: data.cited_by_wikipedia_count || 0
      },
      url: data.details_url
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Altmetric-Daten:', error);
    return null;
  }
}

/**
 * Ruft Dimensions-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Dimensions-Daten
 */
async function fetchDimensionsData(doi) {
  try {
    // Dimensions API (öffentliche Endpunkte sind begrenzt)
    // Dies ist ein Beispiel und müsste mit einem tatsächlichen API-Schlüssel implementiert werden
    const apiUrl = `https://metrics-api.dimensions.ai/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      times_cited: data.times_cited || 0,
      recent_citations: data.recent_citations || 0,
      relative_citation_ratio: data.relative_citation_ratio || 0,
      field_citation_ratio: data.field_citation_ratio || 0
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Dimensions-Daten:', error);
    return null;
  }
}/**
 * Hilfsfunktionen zur automatischen Extraktion von Zitationen und Referenzen
 * aus wissenschaftlichen Publikationen
 */

import fetch from 'node-fetch';

/**
 * Extrahiert Zitationen und Referenzen aus einer Publikation mit DOI
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Extrahierte Zitationen und Referenzen
 */
export async function extractCitationsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche zuerst Crossref
    const crossrefData = await fetchCrossrefCitations(doi);
    if (crossrefData && crossrefData.citations && crossrefData.citations.length > 0) {
      return crossrefData;
    }
    
    // Wenn Crossref keine Ergebnisse liefert, versuche Semantic Scholar
    const semanticScholarData = await fetchSemanticScholarCitations(doi);
    if (semanticScholarData && semanticScholarData.citations && semanticScholarData.citations.length > 0) {
      return semanticScholarData;
    }
    
    // Wenn beide keine Ergebnisse liefern, versuche OpenCitations
    const openCitationsData = await fetchOpenCitationsCitations(doi);
    
    return openCitationsData;
  } catch (error) {
    console.error('Fehler bei der Zitationsextraktion:', error);
    return {
      doi,
      citations: [],
      references: [],
      error: error.message
    };
  }
}

/**
 * Ruft Zitationen von Crossref ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchCrossrefCitations(doi) {
  try {
    // Crossref API für Referenzen (was die Publikation zitiert)
    const referencesUrl = `https://api.crossref.org/works/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
      }
    });
    
    if (!referencesResponse.ok) {
      throw new Error(`Crossref API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Referenzen
    const references = [];
    if (referencesData.message && referencesData.message.reference) {
      referencesData.message.reference.forEach(ref => {
        references.push({
          doi: ref.DOI,
          title: ref['article-title'] || ref.title,
          authors: ref.author ? [ref.author] : [],
          year: ref.year,
          journal: ref['journal-title']
        });
      });
    }
    
    // Für Zitationen (was die Publikation zitiert) gibt es keine direkte Crossref-API
    // Wir könnten OpenCitations oder Semantic Scholar verwenden
    
    return {
      doi,
      citations: [], // Crossref bietet keine direkte API für eingehende Zitationen
      references,
      source: 'crossref'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Crossref-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von Semantic Scholar ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchSemanticScholarCitations(doi) {
  try {
    // Semantic Scholar API
    const apiUrl = `https://api.semanticscholar.org/v1/paper/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      throw new Error(`Semantic Scholar API-Fehler: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Extrahiere Zitationen (eingehende Zitationen)
    const citations = data.citations ? data.citations.map(citation => ({
      doi: citation.doi,
      title: citation.title,
      authors: citation.authors ? citation.authors.map(author => author.name) : [],
      year: citation.year,
      journal: citation.venue
    })) : [];
    
    // Extrahiere Referenzen (ausgehende Zitationen)
    const references = data.references ? data.references.map(reference => ({
      doi: reference.doi,
      title: reference.title,
      authors: reference.authors ? reference.authors.map(author => author.name) : [],
      year: reference.year,
      journal: reference.venue
    })) : [];
    
    return {
      doi,
      citations,
      references,
      source: 'semantic_scholar'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Semantic Scholar-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von OpenCitations ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchOpenCitationsCitations(doi) {
  try {
    // OpenCitations API für eingehende Zitationen
    const citationsUrl = `https://opencitations.net/index/api/v1/citations/${encodeURIComponent(doi)}`;
    const citationsResponse = await fetch(citationsUrl);
    
    if (!citationsResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${citationsResponse.status}`);
    }
    
    const citationsData = await citationsResponse.json();
    
    // OpenCitations API für ausgehende Referenzen
    const referencesUrl = `https://opencitations.net/index/api/v1/references/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl);
    
    if (!referencesResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Zitationen
    const citations = citationsData.map(citation => ({
      doi: citation.citing,
      year: citation.creation ? citation.creation.substring(0, 4) : null
    }));
    
    // Extrahiere Referenzen
    const references = referencesData.map(reference => ({
      doi: reference.cited,
      year: reference.creation ? reference.creation.substring(0, 4) : null
    }));
    
    // Anreicherung der Metadaten für Zitationen und Referenzen
    const enrichedCitations = await enrichCitationsMetadata(citations);
    const enrichedReferences = await enrichCitationsMetadata(references);
    
    return {
      doi,
      citations: enrichedCitations,
      references: enrichedReferences,
      source: 'opencitations'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von OpenCitations-Zitationen:', error);
    return {
      doi,
      citations: [],
      references: [],
      source: 'opencitations',
      error: error.message
    };
  }
}

/**
 * Reichert Zitationsmetadaten mit Titel und Autoren an
 * @param {Array} citations - Liste von Zitationen mit DOIs
 * @returns {Promise<Array>} - Angereicherte Zitationen
 */
async function enrichCitationsMetadata(citations) {
  const enrichedCitations = [];
  
  for (const citation of citations) {
    if (!citation.doi) {
      enrichedCitations.push(citation);
      continue;
    }
    
    try {
      // Crossref API für Metadaten
      const url = `https://api.crossref.org/works/${encodeURIComponent(citation.doi)}`;
      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
        }
      });
      
      if (!response.ok) {
        enrichedCitations.push(citation);
        continue;
      }
      
      const data = await response.json();
      const message = data.message;
      
      // Extrahiere Titel
      const title = message.title ? message.title[0] : null;
      
      // Extrahiere Autoren
      const authors = message.author ? message.author.map(author => 
        `${author.given || ''} ${author.family || ''}`.trim()
      ) : [];
      
      // Extrahiere Journal
      const journal = message['container-title'] ? message['container-title'][0] : null;
      
      // Extrahiere Jahr
      const year = message.published ? 
        (message.published['date-parts'] ? message.published['date-parts'][0][0] : null) : 
        citation.year;
      
      enrichedCitations.push({
        ...citation,
        title,
        authors,
        journal,
        year
      });
    } catch (error) {
      console.error(`Fehler beim Anreichern der Metadaten für DOI ${citation.doi}:`, error);
      enrichedCitations.push(citation);
    }
  }
  
  return enrichedCitations;
}

/**
 * Extrahiert Impact-Faktoren und andere Metriken für eine Publikation
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Metriken
 */
export async function extractMetricsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche Altmetric
    const altmetricData = await fetchAltmetricData(doi);
    
    // Versuche Dimensions
    const dimensionsData = await fetchDimensionsData(doi);
    
    // Kombiniere die Daten
    return {
      doi,
      altmetric: altmetricData,
      dimensions: dimensionsData,
      timestamp: Date.now()
    };
  } catch (error) {
    console.error('Fehler bei der Metrikextraktion:', error);
    return {
      doi,
      error: error.message
    };
  }
}

/**
 * Ruft Altmetric-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Altmetric-Daten
 */
async function fetchAltmetricData(doi) {
  try {
    // Altmetric API
    const apiUrl = `https://api.altmetric.com/v1/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      score: data.score,
      readers: {
        mendeley: data.readers ? data.readers.mendeley : 0,
        citeulike: data.readers ? data.readers.citeulike : 0
      },
      mentions: {
        twitter: data.cited_by_tweeters_count || 0,
        facebook: data.cited_by_fbwalls_count || 0,
        blogs: data.cited_by_feeds_count || 0,
        news: data.cited_by_msm_count || 0,
        reddit: data.cited_by_rdts_count || 0,
        wikipedia: data.cited_by_wikipedia_count || 0
      },
      url: data.details_url
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Altmetric-Daten:', error);
    return null;
  }
}

/**
 * Ruft Dimensions-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Dimensions-Daten
 */
async function fetchDimensionsData(doi) {
  try {
    // Dimensions API (öffentliche Endpunkte sind begrenzt)
    // Dies ist ein Beispiel und müsste mit einem tatsächlichen API-Schlüssel implementiert werden
    const apiUrl = `https://metrics-api.dimensions.ai/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      times_cited: data.times_cited || 0,
      recent_citations: data.recent_citations || 0,
      relative_citation_ratio: data.relative_citation_ratio || 0,
      field_citation_ratio: data.field_citation_ratio || 0
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Dimensions-Daten:', error);
    return null;
  }
}/**
 * Hilfsfunktionen zur automatischen Extraktion von Zitationen und Referenzen
 * aus wissenschaftlichen Publikationen
 */

import fetch from 'node-fetch';

/**
 * Extrahiert Zitationen und Referenzen aus einer Publikation mit DOI
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Extrahierte Zitationen und Referenzen
 */
export async function extractCitationsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche zuerst Crossref
    const crossrefData = await fetchCrossrefCitations(doi);
    if (crossrefData && crossrefData.citations && crossrefData.citations.length > 0) {
      return crossrefData;
    }
    
    // Wenn Crossref keine Ergebnisse liefert, versuche Semantic Scholar
    const semanticScholarData = await fetchSemanticScholarCitations(doi);
    if (semanticScholarData && semanticScholarData.citations && semanticScholarData.citations.length > 0) {
      return semanticScholarData;
    }
    
    // Wenn beide keine Ergebnisse liefern, versuche OpenCitations
    const openCitationsData = await fetchOpenCitationsCitations(doi);
    
    return openCitationsData;
  } catch (error) {
    console.error('Fehler bei der Zitationsextraktion:', error);
    return {
      doi,
      citations: [],
      references: [],
      error: error.message
    };
  }
}

/**
 * Ruft Zitationen von Crossref ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchCrossrefCitations(doi) {
  try {
    // Crossref API für Referenzen (was die Publikation zitiert)
    const referencesUrl = `https://api.crossref.org/works/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
      }
    });
    
    if (!referencesResponse.ok) {
      throw new Error(`Crossref API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Referenzen
    const references = [];
    if (referencesData.message && referencesData.message.reference) {
      referencesData.message.reference.forEach(ref => {
        references.push({
          doi: ref.DOI,
          title: ref['article-title'] || ref.title,
          authors: ref.author ? [ref.author] : [],
          year: ref.year,
          journal: ref['journal-title']
        });
      });
    }
    
    // Für Zitationen (was die Publikation zitiert) gibt es keine direkte Crossref-API
    // Wir könnten OpenCitations oder Semantic Scholar verwenden
    
    return {
      doi,
      citations: [], // Crossref bietet keine direkte API für eingehende Zitationen
      references,
      source: 'crossref'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Crossref-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von Semantic Scholar ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchSemanticScholarCitations(doi) {
  try {
    // Semantic Scholar API
    const apiUrl = `https://api.semanticscholar.org/v1/paper/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      throw new Error(`Semantic Scholar API-Fehler: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Extrahiere Zitationen (eingehende Zitationen)
    const citations = data.citations ? data.citations.map(citation => ({
      doi: citation.doi,
      title: citation.title,
      authors: citation.authors ? citation.authors.map(author => author.name) : [],
      year: citation.year,
      journal: citation.venue
    })) : [];
    
    // Extrahiere Referenzen (ausgehende Zitationen)
    const references = data.references ? data.references.map(reference => ({
      doi: reference.doi,
      title: reference.title,
      authors: reference.authors ? reference.authors.map(author => author.name) : [],
      year: reference.year,
      journal: reference.venue
    })) : [];
    
    return {
      doi,
      citations,
      references,
      source: 'semantic_scholar'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Semantic Scholar-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von OpenCitations ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchOpenCitationsCitations(doi) {
  try {
    // OpenCitations API für eingehende Zitationen
    const citationsUrl = `https://opencitations.net/index/api/v1/citations/${encodeURIComponent(doi)}`;
    const citationsResponse = await fetch(citationsUrl);
    
    if (!citationsResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${citationsResponse.status}`);
    }
    
    const citationsData = await citationsResponse.json();
    
    // OpenCitations API für ausgehende Referenzen
    const referencesUrl = `https://opencitations.net/index/api/v1/references/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl);
    
    if (!referencesResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Zitationen
    const citations = citationsData.map(citation => ({
      doi: citation.citing,
      year: citation.creation ? citation.creation.substring(0, 4) : null
    }));
    
    // Extrahiere Referenzen
    const references = referencesData.map(reference => ({
      doi: reference.cited,
      year: reference.creation ? reference.creation.substring(0, 4) : null
    }));
    
    // Anreicherung der Metadaten für Zitationen und Referenzen
    const enrichedCitations = await enrichCitationsMetadata(citations);
    const enrichedReferences = await enrichCitationsMetadata(references);
    
    return {
      doi,
      citations: enrichedCitations,
      references: enrichedReferences,
      source: 'opencitations'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von OpenCitations-Zitationen:', error);
    return {
      doi,
      citations: [],
      references: [],
      source: 'opencitations',
      error: error.message
    };
  }
}

/**
 * Reichert Zitationsmetadaten mit Titel und Autoren an
 * @param {Array} citations - Liste von Zitationen mit DOIs
 * @returns {Promise<Array>} - Angereicherte Zitationen
 */
async function enrichCitationsMetadata(citations) {
  const enrichedCitations = [];
  
  for (const citation of citations) {
    if (!citation.doi) {
      enrichedCitations.push(citation);
      continue;
    }
    
    try {
      // Crossref API für Metadaten
      const url = `https://api.crossref.org/works/${encodeURIComponent(citation.doi)}`;
      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
        }
      });
      
      if (!response.ok) {
        enrichedCitations.push(citation);
        continue;
      }
      
      const data = await response.json();
      const message = data.message;
      
      // Extrahiere Titel
      const title = message.title ? message.title[0] : null;
      
      // Extrahiere Autoren
      const authors = message.author ? message.author.map(author => 
        `${author.given || ''} ${author.family || ''}`.trim()
      ) : [];
      
      // Extrahiere Journal
      const journal = message['container-title'] ? message['container-title'][0] : null;
      
      // Extrahiere Jahr
      const year = message.published ? 
        (message.published['date-parts'] ? message.published['date-parts'][0][0] : null) : 
        citation.year;
      
      enrichedCitations.push({
        ...citation,
        title,
        authors,
        journal,
        year
      });
    } catch (error) {
      console.error(`Fehler beim Anreichern der Metadaten für DOI ${citation.doi}:`, error);
      enrichedCitations.push(citation);
    }
  }
  
  return enrichedCitations;
}

/**
 * Extrahiert Impact-Faktoren und andere Metriken für eine Publikation
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Metriken
 */
export async function extractMetricsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche Altmetric
    const altmetricData = await fetchAltmetricData(doi);
    
    // Versuche Dimensions
    const dimensionsData = await fetchDimensionsData(doi);
    
    // Kombiniere die Daten
    return {
      doi,
      altmetric: altmetricData,
      dimensions: dimensionsData,
      timestamp: Date.now()
    };
  } catch (error) {
    console.error('Fehler bei der Metrikextraktion:', error);
    return {
      doi,
      error: error.message
    };
  }
}

/**
 * Ruft Altmetric-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Altmetric-Daten
 */
async function fetchAltmetricData(doi) {
  try {
    // Altmetric API
    const apiUrl = `https://api.altmetric.com/v1/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      score: data.score,
      readers: {
        mendeley: data.readers ? data.readers.mendeley : 0,
        citeulike: data.readers ? data.readers.citeulike : 0
      },
      mentions: {
        twitter: data.cited_by_tweeters_count || 0,
        facebook: data.cited_by_fbwalls_count || 0,
        blogs: data.cited_by_feeds_count || 0,
        news: data.cited_by_msm_count || 0,
        reddit: data.cited_by_rdts_count || 0,
        wikipedia: data.cited_by_wikipedia_count || 0
      },
      url: data.details_url
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Altmetric-Daten:', error);
    return null;
  }
}

/**
 * Ruft Dimensions-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Dimensions-Daten
 */
async function fetchDimensionsData(doi) {
  try {
    // Dimensions API (öffentliche Endpunkte sind begrenzt)
    // Dies ist ein Beispiel und müsste mit einem tatsächlichen API-Schlüssel implementiert werden
    const apiUrl = `https://metrics-api.dimensions.ai/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      times_cited: data.times_cited || 0,
      recent_citations: data.recent_citations || 0,
      relative_citation_ratio: data.relative_citation_ratio || 0,
      field_citation_ratio: data.field_citation_ratio || 0
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Dimensions-Daten:', error);
    return null;
  }
}/**
 * Hilfsfunktionen zur automatischen Extraktion von Zitationen und Referenzen
 * aus wissenschaftlichen Publikationen
 */

import fetch from 'node-fetch';

/**
 * Extrahiert Zitationen und Referenzen aus einer Publikation mit DOI
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Extrahierte Zitationen und Referenzen
 */
export async function extractCitationsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche zuerst Crossref
    const crossrefData = await fetchCrossrefCitations(doi);
    if (crossrefData && crossrefData.citations && crossrefData.citations.length > 0) {
      return crossrefData;
    }
    
    // Wenn Crossref keine Ergebnisse liefert, versuche Semantic Scholar
    const semanticScholarData = await fetchSemanticScholarCitations(doi);
    if (semanticScholarData && semanticScholarData.citations && semanticScholarData.citations.length > 0) {
      return semanticScholarData;
    }
    
    // Wenn beide keine Ergebnisse liefern, versuche OpenCitations
    const openCitationsData = await fetchOpenCitationsCitations(doi);
    
    return openCitationsData;
  } catch (error) {
    console.error('Fehler bei der Zitationsextraktion:', error);
    return {
      doi,
      citations: [],
      references: [],
      error: error.message
    };
  }
}

/**
 * Ruft Zitationen von Crossref ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchCrossrefCitations(doi) {
  try {
    // Crossref API für Referenzen (was die Publikation zitiert)
    const referencesUrl = `https://api.crossref.org/works/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
      }
    });
    
    if (!referencesResponse.ok) {
      throw new Error(`Crossref API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Referenzen
    const references = [];
    if (referencesData.message && referencesData.message.reference) {
      referencesData.message.reference.forEach(ref => {
        references.push({
          doi: ref.DOI,
          title: ref['article-title'] || ref.title,
          authors: ref.author ? [ref.author] : [],
          year: ref.year,
          journal: ref['journal-title']
        });
      });
    }
    
    // Für Zitationen (was die Publikation zitiert) gibt es keine direkte Crossref-API
    // Wir könnten OpenCitations oder Semantic Scholar verwenden
    
    return {
      doi,
      citations: [], // Crossref bietet keine direkte API für eingehende Zitationen
      references,
      source: 'crossref'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Crossref-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von Semantic Scholar ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchSemanticScholarCitations(doi) {
  try {
    // Semantic Scholar API
    const apiUrl = `https://api.semanticscholar.org/v1/paper/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      throw new Error(`Semantic Scholar API-Fehler: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Extrahiere Zitationen (eingehende Zitationen)
    const citations = data.citations ? data.citations.map(citation => ({
      doi: citation.doi,
      title: citation.title,
      authors: citation.authors ? citation.authors.map(author => author.name) : [],
      year: citation.year,
      journal: citation.venue
    })) : [];
    
    // Extrahiere Referenzen (ausgehende Zitationen)
    const references = data.references ? data.references.map(reference => ({
      doi: reference.doi,
      title: reference.title,
      authors: reference.authors ? reference.authors.map(author => author.name) : [],
      year: reference.year,
      journal: reference.venue
    })) : [];
    
    return {
      doi,
      citations,
      references,
      source: 'semantic_scholar'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Semantic Scholar-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von OpenCitations ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchOpenCitationsCitations(doi) {
  try {
    // OpenCitations API für eingehende Zitationen
    const citationsUrl = `https://opencitations.net/index/api/v1/citations/${encodeURIComponent(doi)}`;
    const citationsResponse = await fetch(citationsUrl);
    
    if (!citationsResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${citationsResponse.status}`);
    }
    
    const citationsData = await citationsResponse.json();
    
    // OpenCitations API für ausgehende Referenzen
    const referencesUrl = `https://opencitations.net/index/api/v1/references/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl);
    
    if (!referencesResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Zitationen
    const citations = citationsData.map(citation => ({
      doi: citation.citing,
      year: citation.creation ? citation.creation.substring(0, 4) : null
    }));
    
    // Extrahiere Referenzen
    const references = referencesData.map(reference => ({
      doi: reference.cited,
      year: reference.creation ? reference.creation.substring(0, 4) : null
    }));
    
    // Anreicherung der Metadaten für Zitationen und Referenzen
    const enrichedCitations = await enrichCitationsMetadata(citations);
    const enrichedReferences = await enrichCitationsMetadata(references);
    
    return {
      doi,
      citations: enrichedCitations,
      references: enrichedReferences,
      source: 'opencitations'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von OpenCitations-Zitationen:', error);
    return {
      doi,
      citations: [],
      references: [],
      source: 'opencitations',
      error: error.message
    };
  }
}

/**
 * Reichert Zitationsmetadaten mit Titel und Autoren an
 * @param {Array} citations - Liste von Zitationen mit DOIs
 * @returns {Promise<Array>} - Angereicherte Zitationen
 */
async function enrichCitationsMetadata(citations) {
  const enrichedCitations = [];
  
  for (const citation of citations) {
    if (!citation.doi) {
      enrichedCitations.push(citation);
      continue;
    }
    
    try {
      // Crossref API für Metadaten
      const url = `https://api.crossref.org/works/${encodeURIComponent(citation.doi)}`;
      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
        }
      });
      
      if (!response.ok) {
        enrichedCitations.push(citation);
        continue;
      }
      
      const data = await response.json();
      const message = data.message;
      
      // Extrahiere Titel
      const title = message.title ? message.title[0] : null;
      
      // Extrahiere Autoren
      const authors = message.author ? message.author.map(author => 
        `${author.given || ''} ${author.family || ''}`.trim()
      ) : [];
      
      // Extrahiere Journal
      const journal = message['container-title'] ? message['container-title'][0] : null;
      
      // Extrahiere Jahr
      const year = message.published ? 
        (message.published['date-parts'] ? message.published['date-parts'][0][0] : null) : 
        citation.year;
      
      enrichedCitations.push({
        ...citation,
        title,
        authors,
        journal,
        year
      });
    } catch (error) {
      console.error(`Fehler beim Anreichern der Metadaten für DOI ${citation.doi}:`, error);
      enrichedCitations.push(citation);
    }
  }
  
  return enrichedCitations;
}

/**
 * Extrahiert Impact-Faktoren und andere Metriken für eine Publikation
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Metriken
 */
export async function extractMetricsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche Altmetric
    const altmetricData = await fetchAltmetricData(doi);
    
    // Versuche Dimensions
    const dimensionsData = await fetchDimensionsData(doi);
    
    // Kombiniere die Daten
    return {
      doi,
      altmetric: altmetricData,
      dimensions: dimensionsData,
      timestamp: Date.now()
    };
  } catch (error) {
    console.error('Fehler bei der Metrikextraktion:', error);
    return {
      doi,
      error: error.message
    };
  }
}

/**
 * Ruft Altmetric-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Altmetric-Daten
 */
async function fetchAltmetricData(doi) {
  try {
    // Altmetric API
    const apiUrl = `https://api.altmetric.com/v1/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      score: data.score,
      readers: {
        mendeley: data.readers ? data.readers.mendeley : 0,
        citeulike: data.readers ? data.readers.citeulike : 0
      },
      mentions: {
        twitter: data.cited_by_tweeters_count || 0,
        facebook: data.cited_by_fbwalls_count || 0,
        blogs: data.cited_by_feeds_count || 0,
        news: data.cited_by_msm_count || 0,
        reddit: data.cited_by_rdts_count || 0,
        wikipedia: data.cited_by_wikipedia_count || 0
      },
      url: data.details_url
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Altmetric-Daten:', error);
    return null;
  }
}

/**
 * Ruft Dimensions-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Dimensions-Daten
 */
async function fetchDimensionsData(doi) {
  try {
    // Dimensions API (öffentliche Endpunkte sind begrenzt)
    // Dies ist ein Beispiel und müsste mit einem tatsächlichen API-Schlüssel implementiert werden
    const apiUrl = `https://metrics-api.dimensions.ai/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      times_cited: data.times_cited || 0,
      recent_citations: data.recent_citations || 0,
      relative_citation_ratio: data.relative_citation_ratio || 0,
      field_citation_ratio: data.field_citation_ratio || 0
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Dimensions-Daten:', error);
    return null;
  }
}/**
 * Hilfsfunktionen zur automatischen Extraktion von Zitationen und Referenzen
 * aus wissenschaftlichen Publikationen
 */

import fetch from 'node-fetch';

/**
 * Extrahiert Zitationen und Referenzen aus einer Publikation mit DOI
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Extrahierte Zitationen und Referenzen
 */
export async function extractCitationsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche zuerst Crossref
    const crossrefData = await fetchCrossrefCitations(doi);
    if (crossrefData && crossrefData.citations && crossrefData.citations.length > 0) {
      return crossrefData;
    }
    
    // Wenn Crossref keine Ergebnisse liefert, versuche Semantic Scholar
    const semanticScholarData = await fetchSemanticScholarCitations(doi);
    if (semanticScholarData && semanticScholarData.citations && semanticScholarData.citations.length > 0) {
      return semanticScholarData;
    }
    
    // Wenn beide keine Ergebnisse liefern, versuche OpenCitations
    const openCitationsData = await fetchOpenCitationsCitations(doi);
    
    return openCitationsData;
  } catch (error) {
    console.error('Fehler bei der Zitationsextraktion:', error);
    return {
      doi,
      citations: [],
      references: [],
      error: error.message
    };
  }
}

/**
 * Ruft Zitationen von Crossref ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchCrossrefCitations(doi) {
  try {
    // Crossref API für Referenzen (was die Publikation zitiert)
    const referencesUrl = `https://api.crossref.org/works/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
      }
    });
    
    if (!referencesResponse.ok) {
      throw new Error(`Crossref API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Referenzen
    const references = [];
    if (referencesData.message && referencesData.message.reference) {
      referencesData.message.reference.forEach(ref => {
        references.push({
          doi: ref.DOI,
          title: ref['article-title'] || ref.title,
          authors: ref.author ? [ref.author] : [],
          year: ref.year,
          journal: ref['journal-title']
        });
      });
    }
    
    // Für Zitationen (was die Publikation zitiert) gibt es keine direkte Crossref-API
    // Wir könnten OpenCitations oder Semantic Scholar verwenden
    
    return {
      doi,
      citations: [], // Crossref bietet keine direkte API für eingehende Zitationen
      references,
      source: 'crossref'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Crossref-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von Semantic Scholar ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchSemanticScholarCitations(doi) {
  try {
    // Semantic Scholar API
    const apiUrl = `https://api.semanticscholar.org/v1/paper/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      throw new Error(`Semantic Scholar API-Fehler: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Extrahiere Zitationen (eingehende Zitationen)
    const citations = data.citations ? data.citations.map(citation => ({
      doi: citation.doi,
      title: citation.title,
      authors: citation.authors ? citation.authors.map(author => author.name) : [],
      year: citation.year,
      journal: citation.venue
    })) : [];
    
    // Extrahiere Referenzen (ausgehende Zitationen)
    const references = data.references ? data.references.map(reference => ({
      doi: reference.doi,
      title: reference.title,
      authors: reference.authors ? reference.authors.map(author => author.name) : [],
      year: reference.year,
      journal: reference.venue
    })) : [];
    
    return {
      doi,
      citations,
      references,
      source: 'semantic_scholar'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Semantic Scholar-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von OpenCitations ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchOpenCitationsCitations(doi) {
  try {
    // OpenCitations API für eingehende Zitationen
    const citationsUrl = `https://opencitations.net/index/api/v1/citations/${encodeURIComponent(doi)}`;
    const citationsResponse = await fetch(citationsUrl);
    
    if (!citationsResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${citationsResponse.status}`);
    }
    
    const citationsData = await citationsResponse.json();
    
    // OpenCitations API für ausgehende Referenzen
    const referencesUrl = `https://opencitations.net/index/api/v1/references/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl);
    
    if (!referencesResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Zitationen
    const citations = citationsData.map(citation => ({
      doi: citation.citing,
      year: citation.creation ? citation.creation.substring(0, 4) : null
    }));
    
    // Extrahiere Referenzen
    const references = referencesData.map(reference => ({
      doi: reference.cited,
      year: reference.creation ? reference.creation.substring(0, 4) : null
    }));
    
    // Anreicherung der Metadaten für Zitationen und Referenzen
    const enrichedCitations = await enrichCitationsMetadata(citations);
    const enrichedReferences = await enrichCitationsMetadata(references);
    
    return {
      doi,
      citations: enrichedCitations,
      references: enrichedReferences,
      source: 'opencitations'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von OpenCitations-Zitationen:', error);
    return {
      doi,
      citations: [],
      references: [],
      source: 'opencitations',
      error: error.message
    };
  }
}

/**
 * Reichert Zitationsmetadaten mit Titel und Autoren an
 * @param {Array} citations - Liste von Zitationen mit DOIs
 * @returns {Promise<Array>} - Angereicherte Zitationen
 */
async function enrichCitationsMetadata(citations) {
  const enrichedCitations = [];
  
  for (const citation of citations) {
    if (!citation.doi) {
      enrichedCitations.push(citation);
      continue;
    }
    
    try {
      // Crossref API für Metadaten
      const url = `https://api.crossref.org/works/${encodeURIComponent(citation.doi)}`;
      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
        }
      });
      
      if (!response.ok) {
        enrichedCitations.push(citation);
        continue;
      }
      
      const data = await response.json();
      const message = data.message;
      
      // Extrahiere Titel
      const title = message.title ? message.title[0] : null;
      
      // Extrahiere Autoren
      const authors = message.author ? message.author.map(author => 
        `${author.given || ''} ${author.family || ''}`.trim()
      ) : [];
      
      // Extrahiere Journal
      const journal = message['container-title'] ? message['container-title'][0] : null;
      
      // Extrahiere Jahr
      const year = message.published ? 
        (message.published['date-parts'] ? message.published['date-parts'][0][0] : null) : 
        citation.year;
      
      enrichedCitations.push({
        ...citation,
        title,
        authors,
        journal,
        year
      });
    } catch (error) {
      console.error(`Fehler beim Anreichern der Metadaten für DOI ${citation.doi}:`, error);
      enrichedCitations.push(citation);
    }
  }
  
  return enrichedCitations;
}

/**
 * Extrahiert Impact-Faktoren und andere Metriken für eine Publikation
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Metriken
 */
export async function extractMetricsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche Altmetric
    const altmetricData = await fetchAltmetricData(doi);
    
    // Versuche Dimensions
    const dimensionsData = await fetchDimensionsData(doi);
    
    // Kombiniere die Daten
    return {
      doi,
      altmetric: altmetricData,
      dimensions: dimensionsData,
      timestamp: Date.now()
    };
  } catch (error) {
    console.error('Fehler bei der Metrikextraktion:', error);
    return {
      doi,
      error: error.message
    };
  }
}

/**
 * Ruft Altmetric-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Altmetric-Daten
 */
async function fetchAltmetricData(doi) {
  try {
    // Altmetric API
    const apiUrl = `https://api.altmetric.com/v1/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      score: data.score,
      readers: {
        mendeley: data.readers ? data.readers.mendeley : 0,
        citeulike: data.readers ? data.readers.citeulike : 0
      },
      mentions: {
        twitter: data.cited_by_tweeters_count || 0,
        facebook: data.cited_by_fbwalls_count || 0,
        blogs: data.cited_by_feeds_count || 0,
        news: data.cited_by_msm_count || 0,
        reddit: data.cited_by_rdts_count || 0,
        wikipedia: data.cited_by_wikipedia_count || 0
      },
      url: data.details_url
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Altmetric-Daten:', error);
    return null;
  }
}

/**
 * Ruft Dimensions-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Dimensions-Daten
 */
async function fetchDimensionsData(doi) {
  try {
    // Dimensions API (öffentliche Endpunkte sind begrenzt)
    // Dies ist ein Beispiel und müsste mit einem tatsächlichen API-Schlüssel implementiert werden
    const apiUrl = `https://metrics-api.dimensions.ai/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      times_cited: data.times_cited || 0,
      recent_citations: data.recent_citations || 0,
      relative_citation_ratio: data.relative_citation_ratio || 0,
      field_citation_ratio: data.field_citation_ratio || 0
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Dimensions-Daten:', error);
    return null;
  }
}/**
 * Hilfsfunktionen zur automatischen Extraktion von Zitationen und Referenzen
 * aus wissenschaftlichen Publikationen
 */

import fetch from 'node-fetch';

/**
 * Extrahiert Zitationen und Referenzen aus einer Publikation mit DOI
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Extrahierte Zitationen und Referenzen
 */
export async function extractCitationsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche zuerst Crossref
    const crossrefData = await fetchCrossrefCitations(doi);
    if (crossrefData && crossrefData.citations && crossrefData.citations.length > 0) {
      return crossrefData;
    }
    
    // Wenn Crossref keine Ergebnisse liefert, versuche Semantic Scholar
    const semanticScholarData = await fetchSemanticScholarCitations(doi);
    if (semanticScholarData && semanticScholarData.citations && semanticScholarData.citations.length > 0) {
      return semanticScholarData;
    }
    
    // Wenn beide keine Ergebnisse liefern, versuche OpenCitations
    const openCitationsData = await fetchOpenCitationsCitations(doi);
    
    return openCitationsData;
  } catch (error) {
    console.error('Fehler bei der Zitationsextraktion:', error);
    return {
      doi,
      citations: [],
      references: [],
      error: error.message
    };
  }
}

/**
 * Ruft Zitationen von Crossref ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchCrossrefCitations(doi) {
  try {
    // Crossref API für Referenzen (was die Publikation zitiert)
    const referencesUrl = `https://api.crossref.org/works/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
      }
    });
    
    if (!referencesResponse.ok) {
      throw new Error(`Crossref API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Referenzen
    const references = [];
    if (referencesData.message && referencesData.message.reference) {
      referencesData.message.reference.forEach(ref => {
        references.push({
          doi: ref.DOI,
          title: ref['article-title'] || ref.title,
          authors: ref.author ? [ref.author] : [],
          year: ref.year,
          journal: ref['journal-title']
        });
      });
    }
    
    // Für Zitationen (was die Publikation zitiert) gibt es keine direkte Crossref-API
    // Wir könnten OpenCitations oder Semantic Scholar verwenden
    
    return {
      doi,
      citations: [], // Crossref bietet keine direkte API für eingehende Zitationen
      references,
      source: 'crossref'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Crossref-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von Semantic Scholar ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchSemanticScholarCitations(doi) {
  try {
    // Semantic Scholar API
    const apiUrl = `https://api.semanticscholar.org/v1/paper/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      throw new Error(`Semantic Scholar API-Fehler: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Extrahiere Zitationen (eingehende Zitationen)
    const citations = data.citations ? data.citations.map(citation => ({
      doi: citation.doi,
      title: citation.title,
      authors: citation.authors ? citation.authors.map(author => author.name) : [],
      year: citation.year,
      journal: citation.venue
    })) : [];
    
    // Extrahiere Referenzen (ausgehende Zitationen)
    const references = data.references ? data.references.map(reference => ({
      doi: reference.doi,
      title: reference.title,
      authors: reference.authors ? reference.authors.map(author => author.name) : [],
      year: reference.year,
      journal: reference.venue
    })) : [];
    
    return {
      doi,
      citations,
      references,
      source: 'semantic_scholar'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Semantic Scholar-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von OpenCitations ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchOpenCitationsCitations(doi) {
  try {
    // OpenCitations API für eingehende Zitationen
    const citationsUrl = `https://opencitations.net/index/api/v1/citations/${encodeURIComponent(doi)}`;
    const citationsResponse = await fetch(citationsUrl);
    
    if (!citationsResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${citationsResponse.status}`);
    }
    
    const citationsData = await citationsResponse.json();
    
    // OpenCitations API für ausgehende Referenzen
    const referencesUrl = `https://opencitations.net/index/api/v1/references/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl);
    
    if (!referencesResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Zitationen
    const citations = citationsData.map(citation => ({
      doi: citation.citing,
      year: citation.creation ? citation.creation.substring(0, 4) : null
    }));
    
    // Extrahiere Referenzen
    const references = referencesData.map(reference => ({
      doi: reference.cited,
      year: reference.creation ? reference.creation.substring(0, 4) : null
    }));
    
    // Anreicherung der Metadaten für Zitationen und Referenzen
    const enrichedCitations = await enrichCitationsMetadata(citations);
    const enrichedReferences = await enrichCitationsMetadata(references);
    
    return {
      doi,
      citations: enrichedCitations,
      references: enrichedReferences,
      source: 'opencitations'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von OpenCitations-Zitationen:', error);
    return {
      doi,
      citations: [],
      references: [],
      source: 'opencitations',
      error: error.message
    };
  }
}

/**
 * Reichert Zitationsmetadaten mit Titel und Autoren an
 * @param {Array} citations - Liste von Zitationen mit DOIs
 * @returns {Promise<Array>} - Angereicherte Zitationen
 */
async function enrichCitationsMetadata(citations) {
  const enrichedCitations = [];
  
  for (const citation of citations) {
    if (!citation.doi) {
      enrichedCitations.push(citation);
      continue;
    }
    
    try {
      // Crossref API für Metadaten
      const url = `https://api.crossref.org/works/${encodeURIComponent(citation.doi)}`;
      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
        }
      });
      
      if (!response.ok) {
        enrichedCitations.push(citation);
        continue;
      }
      
      const data = await response.json();
      const message = data.message;
      
      // Extrahiere Titel
      const title = message.title ? message.title[0] : null;
      
      // Extrahiere Autoren
      const authors = message.author ? message.author.map(author => 
        `${author.given || ''} ${author.family || ''}`.trim()
      ) : [];
      
      // Extrahiere Journal
      const journal = message['container-title'] ? message['container-title'][0] : null;
      
      // Extrahiere Jahr
      const year = message.published ? 
        (message.published['date-parts'] ? message.published['date-parts'][0][0] : null) : 
        citation.year;
      
      enrichedCitations.push({
        ...citation,
        title,
        authors,
        journal,
        year
      });
    } catch (error) {
      console.error(`Fehler beim Anreichern der Metadaten für DOI ${citation.doi}:`, error);
      enrichedCitations.push(citation);
    }
  }
  
  return enrichedCitations;
}

/**
 * Extrahiert Impact-Faktoren und andere Metriken für eine Publikation
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Metriken
 */
export async function extractMetricsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche Altmetric
    const altmetricData = await fetchAltmetricData(doi);
    
    // Versuche Dimensions
    const dimensionsData = await fetchDimensionsData(doi);
    
    // Kombiniere die Daten
    return {
      doi,
      altmetric: altmetricData,
      dimensions: dimensionsData,
      timestamp: Date.now()
    };
  } catch (error) {
    console.error('Fehler bei der Metrikextraktion:', error);
    return {
      doi,
      error: error.message
    };
  }
}

/**
 * Ruft Altmetric-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Altmetric-Daten
 */
async function fetchAltmetricData(doi) {
  try {
    // Altmetric API
    const apiUrl = `https://api.altmetric.com/v1/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      score: data.score,
      readers: {
        mendeley: data.readers ? data.readers.mendeley : 0,
        citeulike: data.readers ? data.readers.citeulike : 0
      },
      mentions: {
        twitter: data.cited_by_tweeters_count || 0,
        facebook: data.cited_by_fbwalls_count || 0,
        blogs: data.cited_by_feeds_count || 0,
        news: data.cited_by_msm_count || 0,
        reddit: data.cited_by_rdts_count || 0,
        wikipedia: data.cited_by_wikipedia_count || 0
      },
      url: data.details_url
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Altmetric-Daten:', error);
    return null;
  }
}

/**
 * Ruft Dimensions-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Dimensions-Daten
 */
async function fetchDimensionsData(doi) {
  try {
    // Dimensions API (öffentliche Endpunkte sind begrenzt)
    // Dies ist ein Beispiel und müsste mit einem tatsächlichen API-Schlüssel implementiert werden
    const apiUrl = `https://metrics-api.dimensions.ai/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      times_cited: data.times_cited || 0,
      recent_citations: data.recent_citations || 0,
      relative_citation_ratio: data.relative_citation_ratio || 0,
      field_citation_ratio: data.field_citation_ratio || 0
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Dimensions-Daten:', error);
    return null;
  }
}/**
 * Hilfsfunktionen zur automatischen Extraktion von Zitationen und Referenzen
 * aus wissenschaftlichen Publikationen
 */

import fetch from 'node-fetch';

/**
 * Extrahiert Zitationen und Referenzen aus einer Publikation mit DOI
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Extrahierte Zitationen und Referenzen
 */
export async function extractCitationsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche zuerst Crossref
    const crossrefData = await fetchCrossrefCitations(doi);
    if (crossrefData && crossrefData.citations && crossrefData.citations.length > 0) {
      return crossrefData;
    }
    
    // Wenn Crossref keine Ergebnisse liefert, versuche Semantic Scholar
    const semanticScholarData = await fetchSemanticScholarCitations(doi);
    if (semanticScholarData && semanticScholarData.citations && semanticScholarData.citations.length > 0) {
      return semanticScholarData;
    }
    
    // Wenn beide keine Ergebnisse liefern, versuche OpenCitations
    const openCitationsData = await fetchOpenCitationsCitations(doi);
    
    return openCitationsData;
  } catch (error) {
    console.error('Fehler bei der Zitationsextraktion:', error);
    return {
      doi,
      citations: [],
      references: [],
      error: error.message
    };
  }
}

/**
 * Ruft Zitationen von Crossref ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchCrossrefCitations(doi) {
  try {
    // Crossref API für Referenzen (was die Publikation zitiert)
    const referencesUrl = `https://api.crossref.org/works/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
      }
    });
    
    if (!referencesResponse.ok) {
      throw new Error(`Crossref API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Referenzen
    const references = [];
    if (referencesData.message && referencesData.message.reference) {
      referencesData.message.reference.forEach(ref => {
        references.push({
          doi: ref.DOI,
          title: ref['article-title'] || ref.title,
          authors: ref.author ? [ref.author] : [],
          year: ref.year,
          journal: ref['journal-title']
        });
      });
    }
    
    // Für Zitationen (was die Publikation zitiert) gibt es keine direkte Crossref-API
    // Wir könnten OpenCitations oder Semantic Scholar verwenden
    
    return {
      doi,
      citations: [], // Crossref bietet keine direkte API für eingehende Zitationen
      references,
      source: 'crossref'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Crossref-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von Semantic Scholar ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchSemanticScholarCitations(doi) {
  try {
    // Semantic Scholar API
    const apiUrl = `https://api.semanticscholar.org/v1/paper/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      throw new Error(`Semantic Scholar API-Fehler: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Extrahiere Zitationen (eingehende Zitationen)
    const citations = data.citations ? data.citations.map(citation => ({
      doi: citation.doi,
      title: citation.title,
      authors: citation.authors ? citation.authors.map(author => author.name) : [],
      year: citation.year,
      journal: citation.venue
    })) : [];
    
    // Extrahiere Referenzen (ausgehende Zitationen)
    const references = data.references ? data.references.map(reference => ({
      doi: reference.doi,
      title: reference.title,
      authors: reference.authors ? reference.authors.map(author => author.name) : [],
      year: reference.year,
      journal: reference.venue
    })) : [];
    
    return {
      doi,
      citations,
      references,
      source: 'semantic_scholar'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Semantic Scholar-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von OpenCitations ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchOpenCitationsCitations(doi) {
  try {
    // OpenCitations API für eingehende Zitationen
    const citationsUrl = `https://opencitations.net/index/api/v1/citations/${encodeURIComponent(doi)}`;
    const citationsResponse = await fetch(citationsUrl);
    
    if (!citationsResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${citationsResponse.status}`);
    }
    
    const citationsData = await citationsResponse.json();
    
    // OpenCitations API für ausgehende Referenzen
    const referencesUrl = `https://opencitations.net/index/api/v1/references/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl);
    
    if (!referencesResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Zitationen
    const citations = citationsData.map(citation => ({
      doi: citation.citing,
      year: citation.creation ? citation.creation.substring(0, 4) : null
    }));
    
    // Extrahiere Referenzen
    const references = referencesData.map(reference => ({
      doi: reference.cited,
      year: reference.creation ? reference.creation.substring(0, 4) : null
    }));
    
    // Anreicherung der Metadaten für Zitationen und Referenzen
    const enrichedCitations = await enrichCitationsMetadata(citations);
    const enrichedReferences = await enrichCitationsMetadata(references);
    
    return {
      doi,
      citations: enrichedCitations,
      references: enrichedReferences,
      source: 'opencitations'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von OpenCitations-Zitationen:', error);
    return {
      doi,
      citations: [],
      references: [],
      source: 'opencitations',
      error: error.message
    };
  }
}

/**
 * Reichert Zitationsmetadaten mit Titel und Autoren an
 * @param {Array} citations - Liste von Zitationen mit DOIs
 * @returns {Promise<Array>} - Angereicherte Zitationen
 */
async function enrichCitationsMetadata(citations) {
  const enrichedCitations = [];
  
  for (const citation of citations) {
    if (!citation.doi) {
      enrichedCitations.push(citation);
      continue;
    }
    
    try {
      // Crossref API für Metadaten
      const url = `https://api.crossref.org/works/${encodeURIComponent(citation.doi)}`;
      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
        }
      });
      
      if (!response.ok) {
        enrichedCitations.push(citation);
        continue;
      }
      
      const data = await response.json();
      const message = data.message;
      
      // Extrahiere Titel
      const title = message.title ? message.title[0] : null;
      
      // Extrahiere Autoren
      const authors = message.author ? message.author.map(author => 
        `${author.given || ''} ${author.family || ''}`.trim()
      ) : [];
      
      // Extrahiere Journal
      const journal = message['container-title'] ? message['container-title'][0] : null;
      
      // Extrahiere Jahr
      const year = message.published ? 
        (message.published['date-parts'] ? message.published['date-parts'][0][0] : null) : 
        citation.year;
      
      enrichedCitations.push({
        ...citation,
        title,
        authors,
        journal,
        year
      });
    } catch (error) {
      console.error(`Fehler beim Anreichern der Metadaten für DOI ${citation.doi}:`, error);
      enrichedCitations.push(citation);
    }
  }
  
  return enrichedCitations;
}

/**
 * Extrahiert Impact-Faktoren und andere Metriken für eine Publikation
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Metriken
 */
export async function extractMetricsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche Altmetric
    const altmetricData = await fetchAltmetricData(doi);
    
    // Versuche Dimensions
    const dimensionsData = await fetchDimensionsData(doi);
    
    // Kombiniere die Daten
    return {
      doi,
      altmetric: altmetricData,
      dimensions: dimensionsData,
      timestamp: Date.now()
    };
  } catch (error) {
    console.error('Fehler bei der Metrikextraktion:', error);
    return {
      doi,
      error: error.message
    };
  }
}

/**
 * Ruft Altmetric-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Altmetric-Daten
 */
async function fetchAltmetricData(doi) {
  try {
    // Altmetric API
    const apiUrl = `https://api.altmetric.com/v1/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      score: data.score,
      readers: {
        mendeley: data.readers ? data.readers.mendeley : 0,
        citeulike: data.readers ? data.readers.citeulike : 0
      },
      mentions: {
        twitter: data.cited_by_tweeters_count || 0,
        facebook: data.cited_by_fbwalls_count || 0,
        blogs: data.cited_by_feeds_count || 0,
        news: data.cited_by_msm_count || 0,
        reddit: data.cited_by_rdts_count || 0,
        wikipedia: data.cited_by_wikipedia_count || 0
      },
      url: data.details_url
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Altmetric-Daten:', error);
    return null;
  }
}

/**
 * Ruft Dimensions-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Dimensions-Daten
 */
async function fetchDimensionsData(doi) {
  try {
    // Dimensions API (öffentliche Endpunkte sind begrenzt)
    // Dies ist ein Beispiel und müsste mit einem tatsächlichen API-Schlüssel implementiert werden
    const apiUrl = `https://metrics-api.dimensions.ai/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      times_cited: data.times_cited || 0,
      recent_citations: data.recent_citations || 0,
      relative_citation_ratio: data.relative_citation_ratio || 0,
      field_citation_ratio: data.field_citation_ratio || 0
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Dimensions-Daten:', error);
    return null;
  }
}/**
 * Hilfsfunktionen zur automatischen Extraktion von Zitationen und Referenzen
 * aus wissenschaftlichen Publikationen
 */

import fetch from 'node-fetch';

/**
 * Extrahiert Zitationen und Referenzen aus einer Publikation mit DOI
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Extrahierte Zitationen und Referenzen
 */
export async function extractCitationsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche zuerst Crossref
    const crossrefData = await fetchCrossrefCitations(doi);
    if (crossrefData && crossrefData.citations && crossrefData.citations.length > 0) {
      return crossrefData;
    }
    
    // Wenn Crossref keine Ergebnisse liefert, versuche Semantic Scholar
    const semanticScholarData = await fetchSemanticScholarCitations(doi);
    if (semanticScholarData && semanticScholarData.citations && semanticScholarData.citations.length > 0) {
      return semanticScholarData;
    }
    
    // Wenn beide keine Ergebnisse liefern, versuche OpenCitations
    const openCitationsData = await fetchOpenCitationsCitations(doi);
    
    return openCitationsData;
  } catch (error) {
    console.error('Fehler bei der Zitationsextraktion:', error);
    return {
      doi,
      citations: [],
      references: [],
      error: error.message
    };
  }
}

/**
 * Ruft Zitationen von Crossref ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchCrossrefCitations(doi) {
  try {
    // Crossref API für Referenzen (was die Publikation zitiert)
    const referencesUrl = `https://api.crossref.org/works/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
      }
    });
    
    if (!referencesResponse.ok) {
      throw new Error(`Crossref API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Referenzen
    const references = [];
    if (referencesData.message && referencesData.message.reference) {
      referencesData.message.reference.forEach(ref => {
        references.push({
          doi: ref.DOI,
          title: ref['article-title'] || ref.title,
          authors: ref.author ? [ref.author] : [],
          year: ref.year,
          journal: ref['journal-title']
        });
      });
    }
    
    // Für Zitationen (was die Publikation zitiert) gibt es keine direkte Crossref-API
    // Wir könnten OpenCitations oder Semantic Scholar verwenden
    
    return {
      doi,
      citations: [], // Crossref bietet keine direkte API für eingehende Zitationen
      references,
      source: 'crossref'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Crossref-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von Semantic Scholar ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchSemanticScholarCitations(doi) {
  try {
    // Semantic Scholar API
    const apiUrl = `https://api.semanticscholar.org/v1/paper/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      throw new Error(`Semantic Scholar API-Fehler: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Extrahiere Zitationen (eingehende Zitationen)
    const citations = data.citations ? data.citations.map(citation => ({
      doi: citation.doi,
      title: citation.title,
      authors: citation.authors ? citation.authors.map(author => author.name) : [],
      year: citation.year,
      journal: citation.venue
    })) : [];
    
    // Extrahiere Referenzen (ausgehende Zitationen)
    const references = data.references ? data.references.map(reference => ({
      doi: reference.doi,
      title: reference.title,
      authors: reference.authors ? reference.authors.map(author => author.name) : [],
      year: reference.year,
      journal: reference.venue
    })) : [];
    
    return {
      doi,
      citations,
      references,
      source: 'semantic_scholar'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Semantic Scholar-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von OpenCitations ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchOpenCitationsCitations(doi) {
  try {
    // OpenCitations API für eingehende Zitationen
    const citationsUrl = `https://opencitations.net/index/api/v1/citations/${encodeURIComponent(doi)}`;
    const citationsResponse = await fetch(citationsUrl);
    
    if (!citationsResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${citationsResponse.status}`);
    }
    
    const citationsData = await citationsResponse.json();
    
    // OpenCitations API für ausgehende Referenzen
    const referencesUrl = `https://opencitations.net/index/api/v1/references/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl);
    
    if (!referencesResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Zitationen
    const citations = citationsData.map(citation => ({
      doi: citation.citing,
      year: citation.creation ? citation.creation.substring(0, 4) : null
    }));
    
    // Extrahiere Referenzen
    const references = referencesData.map(reference => ({
      doi: reference.cited,
      year: reference.creation ? reference.creation.substring(0, 4) : null
    }));
    
    // Anreicherung der Metadaten für Zitationen und Referenzen
    const enrichedCitations = await enrichCitationsMetadata(citations);
    const enrichedReferences = await enrichCitationsMetadata(references);
    
    return {
      doi,
      citations: enrichedCitations,
      references: enrichedReferences,
      source: 'opencitations'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von OpenCitations-Zitationen:', error);
    return {
      doi,
      citations: [],
      references: [],
      source: 'opencitations',
      error: error.message
    };
  }
}

/**
 * Reichert Zitationsmetadaten mit Titel und Autoren an
 * @param {Array} citations - Liste von Zitationen mit DOIs
 * @returns {Promise<Array>} - Angereicherte Zitationen
 */
async function enrichCitationsMetadata(citations) {
  const enrichedCitations = [];
  
  for (const citation of citations) {
    if (!citation.doi) {
      enrichedCitations.push(citation);
      continue;
    }
    
    try {
      // Crossref API für Metadaten
      const url = `https://api.crossref.org/works/${encodeURIComponent(citation.doi)}`;
      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
        }
      });
      
      if (!response.ok) {
        enrichedCitations.push(citation);
        continue;
      }
      
      const data = await response.json();
      const message = data.message;
      
      // Extrahiere Titel
      const title = message.title ? message.title[0] : null;
      
      // Extrahiere Autoren
      const authors = message.author ? message.author.map(author => 
        `${author.given || ''} ${author.family || ''}`.trim()
      ) : [];
      
      // Extrahiere Journal
      const journal = message['container-title'] ? message['container-title'][0] : null;
      
      // Extrahiere Jahr
      const year = message.published ? 
        (message.published['date-parts'] ? message.published['date-parts'][0][0] : null) : 
        citation.year;
      
      enrichedCitations.push({
        ...citation,
        title,
        authors,
        journal,
        year
      });
    } catch (error) {
      console.error(`Fehler beim Anreichern der Metadaten für DOI ${citation.doi}:`, error);
      enrichedCitations.push(citation);
    }
  }
  
  return enrichedCitations;
}

/**
 * Extrahiert Impact-Faktoren und andere Metriken für eine Publikation
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Metriken
 */
export async function extractMetricsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche Altmetric
    const altmetricData = await fetchAltmetricData(doi);
    
    // Versuche Dimensions
    const dimensionsData = await fetchDimensionsData(doi);
    
    // Kombiniere die Daten
    return {
      doi,
      altmetric: altmetricData,
      dimensions: dimensionsData,
      timestamp: Date.now()
    };
  } catch (error) {
    console.error('Fehler bei der Metrikextraktion:', error);
    return {
      doi,
      error: error.message
    };
  }
}

/**
 * Ruft Altmetric-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Altmetric-Daten
 */
async function fetchAltmetricData(doi) {
  try {
    // Altmetric API
    const apiUrl = `https://api.altmetric.com/v1/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      score: data.score,
      readers: {
        mendeley: data.readers ? data.readers.mendeley : 0,
        citeulike: data.readers ? data.readers.citeulike : 0
      },
      mentions: {
        twitter: data.cited_by_tweeters_count || 0,
        facebook: data.cited_by_fbwalls_count || 0,
        blogs: data.cited_by_feeds_count || 0,
        news: data.cited_by_msm_count || 0,
        reddit: data.cited_by_rdts_count || 0,
        wikipedia: data.cited_by_wikipedia_count || 0
      },
      url: data.details_url
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Altmetric-Daten:', error);
    return null;
  }
}

/**
 * Ruft Dimensions-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Dimensions-Daten
 */
async function fetchDimensionsData(doi) {
  try {
    // Dimensions API (öffentliche Endpunkte sind begrenzt)
    // Dies ist ein Beispiel und müsste mit einem tatsächlichen API-Schlüssel implementiert werden
    const apiUrl = `https://metrics-api.dimensions.ai/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      times_cited: data.times_cited || 0,
      recent_citations: data.recent_citations || 0,
      relative_citation_ratio: data.relative_citation_ratio || 0,
      field_citation_ratio: data.field_citation_ratio || 0
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Dimensions-Daten:', error);
    return null;
  }
}/**
 * Hilfsfunktionen zur automatischen Extraktion von Zitationen und Referenzen
 * aus wissenschaftlichen Publikationen
 */

import fetch from 'node-fetch';

/**
 * Extrahiert Zitationen und Referenzen aus einer Publikation mit DOI
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Extrahierte Zitationen und Referenzen
 */
export async function extractCitationsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche zuerst Crossref
    const crossrefData = await fetchCrossrefCitations(doi);
    if (crossrefData && crossrefData.citations && crossrefData.citations.length > 0) {
      return crossrefData;
    }
    
    // Wenn Crossref keine Ergebnisse liefert, versuche Semantic Scholar
    const semanticScholarData = await fetchSemanticScholarCitations(doi);
    if (semanticScholarData && semanticScholarData.citations && semanticScholarData.citations.length > 0) {
      return semanticScholarData;
    }
    
    // Wenn beide keine Ergebnisse liefern, versuche OpenCitations
    const openCitationsData = await fetchOpenCitationsCitations(doi);
    
    return openCitationsData;
  } catch (error) {
    console.error('Fehler bei der Zitationsextraktion:', error);
    return {
      doi,
      citations: [],
      references: [],
      error: error.message
    };
  }
}

/**
 * Ruft Zitationen von Crossref ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchCrossrefCitations(doi) {
  try {
    // Crossref API für Referenzen (was die Publikation zitiert)
    const referencesUrl = `https://api.crossref.org/works/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
      }
    });
    
    if (!referencesResponse.ok) {
      throw new Error(`Crossref API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Referenzen
    const references = [];
    if (referencesData.message && referencesData.message.reference) {
      referencesData.message.reference.forEach(ref => {
        references.push({
          doi: ref.DOI,
          title: ref['article-title'] || ref.title,
          authors: ref.author ? [ref.author] : [],
          year: ref.year,
          journal: ref['journal-title']
        });
      });
    }
    
    // Für Zitationen (was die Publikation zitiert) gibt es keine direkte Crossref-API
    // Wir könnten OpenCitations oder Semantic Scholar verwenden
    
    return {
      doi,
      citations: [], // Crossref bietet keine direkte API für eingehende Zitationen
      references,
      source: 'crossref'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Crossref-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von Semantic Scholar ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchSemanticScholarCitations(doi) {
  try {
    // Semantic Scholar API
    const apiUrl = `https://api.semanticscholar.org/v1/paper/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      throw new Error(`Semantic Scholar API-Fehler: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Extrahiere Zitationen (eingehende Zitationen)
    const citations = data.citations ? data.citations.map(citation => ({
      doi: citation.doi,
      title: citation.title,
      authors: citation.authors ? citation.authors.map(author => author.name) : [],
      year: citation.year,
      journal: citation.venue
    })) : [];
    
    // Extrahiere Referenzen (ausgehende Zitationen)
    const references = data.references ? data.references.map(reference => ({
      doi: reference.doi,
      title: reference.title,
      authors: reference.authors ? reference.authors.map(author => author.name) : [],
      year: reference.year,
      journal: reference.venue
    })) : [];
    
    return {
      doi,
      citations,
      references,
      source: 'semantic_scholar'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Semantic Scholar-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von OpenCitations ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchOpenCitationsCitations(doi) {
  try {
    // OpenCitations API für eingehende Zitationen
    const citationsUrl = `https://opencitations.net/index/api/v1/citations/${encodeURIComponent(doi)}`;
    const citationsResponse = await fetch(citationsUrl);
    
    if (!citationsResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${citationsResponse.status}`);
    }
    
    const citationsData = await citationsResponse.json();
    
    // OpenCitations API für ausgehende Referenzen
    const referencesUrl = `https://opencitations.net/index/api/v1/references/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl);
    
    if (!referencesResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Zitationen
    const citations = citationsData.map(citation => ({
      doi: citation.citing,
      year: citation.creation ? citation.creation.substring(0, 4) : null
    }));
    
    // Extrahiere Referenzen
    const references = referencesData.map(reference => ({
      doi: reference.cited,
      year: reference.creation ? reference.creation.substring(0, 4) : null
    }));
    
    // Anreicherung der Metadaten für Zitationen und Referenzen
    const enrichedCitations = await enrichCitationsMetadata(citations);
    const enrichedReferences = await enrichCitationsMetadata(references);
    
    return {
      doi,
      citations: enrichedCitations,
      references: enrichedReferences,
      source: 'opencitations'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von OpenCitations-Zitationen:', error);
    return {
      doi,
      citations: [],
      references: [],
      source: 'opencitations',
      error: error.message
    };
  }
}

/**
 * Reichert Zitationsmetadaten mit Titel und Autoren an
 * @param {Array} citations - Liste von Zitationen mit DOIs
 * @returns {Promise<Array>} - Angereicherte Zitationen
 */
async function enrichCitationsMetadata(citations) {
  const enrichedCitations = [];
  
  for (const citation of citations) {
    if (!citation.doi) {
      enrichedCitations.push(citation);
      continue;
    }
    
    try {
      // Crossref API für Metadaten
      const url = `https://api.crossref.org/works/${encodeURIComponent(citation.doi)}`;
      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
        }
      });
      
      if (!response.ok) {
        enrichedCitations.push(citation);
        continue;
      }
      
      const data = await response.json();
      const message = data.message;
      
      // Extrahiere Titel
      const title = message.title ? message.title[0] : null;
      
      // Extrahiere Autoren
      const authors = message.author ? message.author.map(author => 
        `${author.given || ''} ${author.family || ''}`.trim()
      ) : [];
      
      // Extrahiere Journal
      const journal = message['container-title'] ? message['container-title'][0] : null;
      
      // Extrahiere Jahr
      const year = message.published ? 
        (message.published['date-parts'] ? message.published['date-parts'][0][0] : null) : 
        citation.year;
      
      enrichedCitations.push({
        ...citation,
        title,
        authors,
        journal,
        year
      });
    } catch (error) {
      console.error(`Fehler beim Anreichern der Metadaten für DOI ${citation.doi}:`, error);
      enrichedCitations.push(citation);
    }
  }
  
  return enrichedCitations;
}

/**
 * Extrahiert Impact-Faktoren und andere Metriken für eine Publikation
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Metriken
 */
export async function extractMetricsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche Altmetric
    const altmetricData = await fetchAltmetricData(doi);
    
    // Versuche Dimensions
    const dimensionsData = await fetchDimensionsData(doi);
    
    // Kombiniere die Daten
    return {
      doi,
      altmetric: altmetricData,
      dimensions: dimensionsData,
      timestamp: Date.now()
    };
  } catch (error) {
    console.error('Fehler bei der Metrikextraktion:', error);
    return {
      doi,
      error: error.message
    };
  }
}

/**
 * Ruft Altmetric-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Altmetric-Daten
 */
async function fetchAltmetricData(doi) {
  try {
    // Altmetric API
    const apiUrl = `https://api.altmetric.com/v1/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      score: data.score,
      readers: {
        mendeley: data.readers ? data.readers.mendeley : 0,
        citeulike: data.readers ? data.readers.citeulike : 0
      },
      mentions: {
        twitter: data.cited_by_tweeters_count || 0,
        facebook: data.cited_by_fbwalls_count || 0,
        blogs: data.cited_by_feeds_count || 0,
        news: data.cited_by_msm_count || 0,
        reddit: data.cited_by_rdts_count || 0,
        wikipedia: data.cited_by_wikipedia_count || 0
      },
      url: data.details_url
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Altmetric-Daten:', error);
    return null;
  }
}

/**
 * Ruft Dimensions-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Dimensions-Daten
 */
async function fetchDimensionsData(doi) {
  try {
    // Dimensions API (öffentliche Endpunkte sind begrenzt)
    // Dies ist ein Beispiel und müsste mit einem tatsächlichen API-Schlüssel implementiert werden
    const apiUrl = `https://metrics-api.dimensions.ai/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      times_cited: data.times_cited || 0,
      recent_citations: data.recent_citations || 0,
      relative_citation_ratio: data.relative_citation_ratio || 0,
      field_citation_ratio: data.field_citation_ratio || 0
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Dimensions-Daten:', error);
    return null;
  }
}/**
 * Hilfsfunktionen zur automatischen Extraktion von Zitationen und Referenzen
 * aus wissenschaftlichen Publikationen
 */

import fetch from 'node-fetch';

/**
 * Extrahiert Zitationen und Referenzen aus einer Publikation mit DOI
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Extrahierte Zitationen und Referenzen
 */
export async function extractCitationsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche zuerst Crossref
    const crossrefData = await fetchCrossrefCitations(doi);
    if (crossrefData && crossrefData.citations && crossrefData.citations.length > 0) {
      return crossrefData;
    }
    
    // Wenn Crossref keine Ergebnisse liefert, versuche Semantic Scholar
    const semanticScholarData = await fetchSemanticScholarCitations(doi);
    if (semanticScholarData && semanticScholarData.citations && semanticScholarData.citations.length > 0) {
      return semanticScholarData;
    }
    
    // Wenn beide keine Ergebnisse liefern, versuche OpenCitations
    const openCitationsData = await fetchOpenCitationsCitations(doi);
    
    return openCitationsData;
  } catch (error) {
    console.error('Fehler bei der Zitationsextraktion:', error);
    return {
      doi,
      citations: [],
      references: [],
      error: error.message
    };
  }
}

/**
 * Ruft Zitationen von Crossref ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchCrossrefCitations(doi) {
  try {
    // Crossref API für Referenzen (was die Publikation zitiert)
    const referencesUrl = `https://api.crossref.org/works/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
      }
    });
    
    if (!referencesResponse.ok) {
      throw new Error(`Crossref API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Referenzen
    const references = [];
    if (referencesData.message && referencesData.message.reference) {
      referencesData.message.reference.forEach(ref => {
        references.push({
          doi: ref.DOI,
          title: ref['article-title'] || ref.title,
          authors: ref.author ? [ref.author] : [],
          year: ref.year,
          journal: ref['journal-title']
        });
      });
    }
    
    // Für Zitationen (was die Publikation zitiert) gibt es keine direkte Crossref-API
    // Wir könnten OpenCitations oder Semantic Scholar verwenden
    
    return {
      doi,
      citations: [], // Crossref bietet keine direkte API für eingehende Zitationen
      references,
      source: 'crossref'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Crossref-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von Semantic Scholar ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchSemanticScholarCitations(doi) {
  try {
    // Semantic Scholar API
    const apiUrl = `https://api.semanticscholar.org/v1/paper/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      throw new Error(`Semantic Scholar API-Fehler: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Extrahiere Zitationen (eingehende Zitationen)
    const citations = data.citations ? data.citations.map(citation => ({
      doi: citation.doi,
      title: citation.title,
      authors: citation.authors ? citation.authors.map(author => author.name) : [],
      year: citation.year,
      journal: citation.venue
    })) : [];
    
    // Extrahiere Referenzen (ausgehende Zitationen)
    const references = data.references ? data.references.map(reference => ({
      doi: reference.doi,
      title: reference.title,
      authors: reference.authors ? reference.authors.map(author => author.name) : [],
      year: reference.year,
      journal: reference.venue
    })) : [];
    
    return {
      doi,
      citations,
      references,
      source: 'semantic_scholar'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Semantic Scholar-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von OpenCitations ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchOpenCitationsCitations(doi) {
  try {
    // OpenCitations API für eingehende Zitationen
    const citationsUrl = `https://opencitations.net/index/api/v1/citations/${encodeURIComponent(doi)}`;
    const citationsResponse = await fetch(citationsUrl);
    
    if (!citationsResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${citationsResponse.status}`);
    }
    
    const citationsData = await citationsResponse.json();
    
    // OpenCitations API für ausgehende Referenzen
    const referencesUrl = `https://opencitations.net/index/api/v1/references/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl);
    
    if (!referencesResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Zitationen
    const citations = citationsData.map(citation => ({
      doi: citation.citing,
      year: citation.creation ? citation.creation.substring(0, 4) : null
    }));
    
    // Extrahiere Referenzen
    const references = referencesData.map(reference => ({
      doi: reference.cited,
      year: reference.creation ? reference.creation.substring(0, 4) : null
    }));
    
    // Anreicherung der Metadaten für Zitationen und Referenzen
    const enrichedCitations = await enrichCitationsMetadata(citations);
    const enrichedReferences = await enrichCitationsMetadata(references);
    
    return {
      doi,
      citations: enrichedCitations,
      references: enrichedReferences,
      source: 'opencitations'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von OpenCitations-Zitationen:', error);
    return {
      doi,
      citations: [],
      references: [],
      source: 'opencitations',
      error: error.message
    };
  }
}

/**
 * Reichert Zitationsmetadaten mit Titel und Autoren an
 * @param {Array} citations - Liste von Zitationen mit DOIs
 * @returns {Promise<Array>} - Angereicherte Zitationen
 */
async function enrichCitationsMetadata(citations) {
  const enrichedCitations = [];
  
  for (const citation of citations) {
    if (!citation.doi) {
      enrichedCitations.push(citation);
      continue;
    }
    
    try {
      // Crossref API für Metadaten
      const url = `https://api.crossref.org/works/${encodeURIComponent(citation.doi)}`;
      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
        }
      });
      
      if (!response.ok) {
        enrichedCitations.push(citation);
        continue;
      }
      
      const data = await response.json();
      const message = data.message;
      
      // Extrahiere Titel
      const title = message.title ? message.title[0] : null;
      
      // Extrahiere Autoren
      const authors = message.author ? message.author.map(author => 
        `${author.given || ''} ${author.family || ''}`.trim()
      ) : [];
      
      // Extrahiere Journal
      const journal = message['container-title'] ? message['container-title'][0] : null;
      
      // Extrahiere Jahr
      const year = message.published ? 
        (message.published['date-parts'] ? message.published['date-parts'][0][0] : null) : 
        citation.year;
      
      enrichedCitations.push({
        ...citation,
        title,
        authors,
        journal,
        year
      });
    } catch (error) {
      console.error(`Fehler beim Anreichern der Metadaten für DOI ${citation.doi}:`, error);
      enrichedCitations.push(citation);
    }
  }
  
  return enrichedCitations;
}

/**
 * Extrahiert Impact-Faktoren und andere Metriken für eine Publikation
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Metriken
 */
export async function extractMetricsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche Altmetric
    const altmetricData = await fetchAltmetricData(doi);
    
    // Versuche Dimensions
    const dimensionsData = await fetchDimensionsData(doi);
    
    // Kombiniere die Daten
    return {
      doi,
      altmetric: altmetricData,
      dimensions: dimensionsData,
      timestamp: Date.now()
    };
  } catch (error) {
    console.error('Fehler bei der Metrikextraktion:', error);
    return {
      doi,
      error: error.message
    };
  }
}

/**
 * Ruft Altmetric-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Altmetric-Daten
 */
async function fetchAltmetricData(doi) {
  try {
    // Altmetric API
    const apiUrl = `https://api.altmetric.com/v1/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      score: data.score,
      readers: {
        mendeley: data.readers ? data.readers.mendeley : 0,
        citeulike: data.readers ? data.readers.citeulike : 0
      },
      mentions: {
        twitter: data.cited_by_tweeters_count || 0,
        facebook: data.cited_by_fbwalls_count || 0,
        blogs: data.cited_by_feeds_count || 0,
        news: data.cited_by_msm_count || 0,
        reddit: data.cited_by_rdts_count || 0,
        wikipedia: data.cited_by_wikipedia_count || 0
      },
      url: data.details_url
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Altmetric-Daten:', error);
    return null;
  }
}

/**
 * Ruft Dimensions-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Dimensions-Daten
 */
async function fetchDimensionsData(doi) {
  try {
    // Dimensions API (öffentliche Endpunkte sind begrenzt)
    // Dies ist ein Beispiel und müsste mit einem tatsächlichen API-Schlüssel implementiert werden
    const apiUrl = `https://metrics-api.dimensions.ai/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      times_cited: data.times_cited || 0,
      recent_citations: data.recent_citations || 0,
      relative_citation_ratio: data.relative_citation_ratio || 0,
      field_citation_ratio: data.field_citation_ratio || 0
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Dimensions-Daten:', error);
    return null;
  }
}/**
 * Hilfsfunktionen zur automatischen Extraktion von Zitationen und Referenzen
 * aus wissenschaftlichen Publikationen
 */

import fetch from 'node-fetch';

/**
 * Extrahiert Zitationen und Referenzen aus einer Publikation mit DOI
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Extrahierte Zitationen und Referenzen
 */
export async function extractCitationsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche zuerst Crossref
    const crossrefData = await fetchCrossrefCitations(doi);
    if (crossrefData && crossrefData.citations && crossrefData.citations.length > 0) {
      return crossrefData;
    }
    
    // Wenn Crossref keine Ergebnisse liefert, versuche Semantic Scholar
    const semanticScholarData = await fetchSemanticScholarCitations(doi);
    if (semanticScholarData && semanticScholarData.citations && semanticScholarData.citations.length > 0) {
      return semanticScholarData;
    }
    
    // Wenn beide keine Ergebnisse liefern, versuche OpenCitations
    const openCitationsData = await fetchOpenCitationsCitations(doi);
    
    return openCitationsData;
  } catch (error) {
    console.error('Fehler bei der Zitationsextraktion:', error);
    return {
      doi,
      citations: [],
      references: [],
      error: error.message
    };
  }
}

/**
 * Ruft Zitationen von Crossref ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchCrossrefCitations(doi) {
  try {
    // Crossref API für Referenzen (was die Publikation zitiert)
    const referencesUrl = `https://api.crossref.org/works/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
      }
    });
    
    if (!referencesResponse.ok) {
      throw new Error(`Crossref API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Referenzen
    const references = [];
    if (referencesData.message && referencesData.message.reference) {
      referencesData.message.reference.forEach(ref => {
        references.push({
          doi: ref.DOI,
          title: ref['article-title'] || ref.title,
          authors: ref.author ? [ref.author] : [],
          year: ref.year,
          journal: ref['journal-title']
        });
      });
    }
    
    // Für Zitationen (was die Publikation zitiert) gibt es keine direkte Crossref-API
    // Wir könnten OpenCitations oder Semantic Scholar verwenden
    
    return {
      doi,
      citations: [], // Crossref bietet keine direkte API für eingehende Zitationen
      references,
      source: 'crossref'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Crossref-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von Semantic Scholar ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchSemanticScholarCitations(doi) {
  try {
    // Semantic Scholar API
    const apiUrl = `https://api.semanticscholar.org/v1/paper/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      throw new Error(`Semantic Scholar API-Fehler: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Extrahiere Zitationen (eingehende Zitationen)
    const citations = data.citations ? data.citations.map(citation => ({
      doi: citation.doi,
      title: citation.title,
      authors: citation.authors ? citation.authors.map(author => author.name) : [],
      year: citation.year,
      journal: citation.venue
    })) : [];
    
    // Extrahiere Referenzen (ausgehende Zitationen)
    const references = data.references ? data.references.map(reference => ({
      doi: reference.doi,
      title: reference.title,
      authors: reference.authors ? reference.authors.map(author => author.name) : [],
      year: reference.year,
      journal: reference.venue
    })) : [];
    
    return {
      doi,
      citations,
      references,
      source: 'semantic_scholar'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Semantic Scholar-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von OpenCitations ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchOpenCitationsCitations(doi) {
  try {
    // OpenCitations API für eingehende Zitationen
    const citationsUrl = `https://opencitations.net/index/api/v1/citations/${encodeURIComponent(doi)}`;
    const citationsResponse = await fetch(citationsUrl);
    
    if (!citationsResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${citationsResponse.status}`);
    }
    
    const citationsData = await citationsResponse.json();
    
    // OpenCitations API für ausgehende Referenzen
    const referencesUrl = `https://opencitations.net/index/api/v1/references/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl);
    
    if (!referencesResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Zitationen
    const citations = citationsData.map(citation => ({
      doi: citation.citing,
      year: citation.creation ? citation.creation.substring(0, 4) : null
    }));
    
    // Extrahiere Referenzen
    const references = referencesData.map(reference => ({
      doi: reference.cited,
      year: reference.creation ? reference.creation.substring(0, 4) : null
    }));
    
    // Anreicherung der Metadaten für Zitationen und Referenzen
    const enrichedCitations = await enrichCitationsMetadata(citations);
    const enrichedReferences = await enrichCitationsMetadata(references);
    
    return {
      doi,
      citations: enrichedCitations,
      references: enrichedReferences,
      source: 'opencitations'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von OpenCitations-Zitationen:', error);
    return {
      doi,
      citations: [],
      references: [],
      source: 'opencitations',
      error: error.message
    };
  }
}

/**
 * Reichert Zitationsmetadaten mit Titel und Autoren an
 * @param {Array} citations - Liste von Zitationen mit DOIs
 * @returns {Promise<Array>} - Angereicherte Zitationen
 */
async function enrichCitationsMetadata(citations) {
  const enrichedCitations = [];
  
  for (const citation of citations) {
    if (!citation.doi) {
      enrichedCitations.push(citation);
      continue;
    }
    
    try {
      // Crossref API für Metadaten
      const url = `https://api.crossref.org/works/${encodeURIComponent(citation.doi)}`;
      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
        }
      });
      
      if (!response.ok) {
        enrichedCitations.push(citation);
        continue;
      }
      
      const data = await response.json();
      const message = data.message;
      
      // Extrahiere Titel
      const title = message.title ? message.title[0] : null;
      
      // Extrahiere Autoren
      const authors = message.author ? message.author.map(author => 
        `${author.given || ''} ${author.family || ''}`.trim()
      ) : [];
      
      // Extrahiere Journal
      const journal = message['container-title'] ? message['container-title'][0] : null;
      
      // Extrahiere Jahr
      const year = message.published ? 
        (message.published['date-parts'] ? message.published['date-parts'][0][0] : null) : 
        citation.year;
      
      enrichedCitations.push({
        ...citation,
        title,
        authors,
        journal,
        year
      });
    } catch (error) {
      console.error(`Fehler beim Anreichern der Metadaten für DOI ${citation.doi}:`, error);
      enrichedCitations.push(citation);
    }
  }
  
  return enrichedCitations;
}

/**
 * Extrahiert Impact-Faktoren und andere Metriken für eine Publikation
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Metriken
 */
export async function extractMetricsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche Altmetric
    const altmetricData = await fetchAltmetricData(doi);
    
    // Versuche Dimensions
    const dimensionsData = await fetchDimensionsData(doi);
    
    // Kombiniere die Daten
    return {
      doi,
      altmetric: altmetricData,
      dimensions: dimensionsData,
      timestamp: Date.now()
    };
  } catch (error) {
    console.error('Fehler bei der Metrikextraktion:', error);
    return {
      doi,
      error: error.message
    };
  }
}

/**
 * Ruft Altmetric-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Altmetric-Daten
 */
async function fetchAltmetricData(doi) {
  try {
    // Altmetric API
    const apiUrl = `https://api.altmetric.com/v1/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      score: data.score,
      readers: {
        mendeley: data.readers ? data.readers.mendeley : 0,
        citeulike: data.readers ? data.readers.citeulike : 0
      },
      mentions: {
        twitter: data.cited_by_tweeters_count || 0,
        facebook: data.cited_by_fbwalls_count || 0,
        blogs: data.cited_by_feeds_count || 0,
        news: data.cited_by_msm_count || 0,
        reddit: data.cited_by_rdts_count || 0,
        wikipedia: data.cited_by_wikipedia_count || 0
      },
      url: data.details_url
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Altmetric-Daten:', error);
    return null;
  }
}

/**
 * Ruft Dimensions-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Dimensions-Daten
 */
async function fetchDimensionsData(doi) {
  try {
    // Dimensions API (öffentliche Endpunkte sind begrenzt)
    // Dies ist ein Beispiel und müsste mit einem tatsächlichen API-Schlüssel implementiert werden
    const apiUrl = `https://metrics-api.dimensions.ai/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      times_cited: data.times_cited || 0,
      recent_citations: data.recent_citations || 0,
      relative_citation_ratio: data.relative_citation_ratio || 0,
      field_citation_ratio: data.field_citation_ratio || 0
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Dimensions-Daten:', error);
    return null;
  }
}/**
 * Hilfsfunktionen zur automatischen Extraktion von Zitationen und Referenzen
 * aus wissenschaftlichen Publikationen
 */

import fetch from 'node-fetch';

/**
 * Extrahiert Zitationen und Referenzen aus einer Publikation mit DOI
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Extrahierte Zitationen und Referenzen
 */
export async function extractCitationsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche zuerst Crossref
    const crossrefData = await fetchCrossrefCitations(doi);
    if (crossrefData && crossrefData.citations && crossrefData.citations.length > 0) {
      return crossrefData;
    }
    
    // Wenn Crossref keine Ergebnisse liefert, versuche Semantic Scholar
    const semanticScholarData = await fetchSemanticScholarCitations(doi);
    if (semanticScholarData && semanticScholarData.citations && semanticScholarData.citations.length > 0) {
      return semanticScholarData;
    }
    
    // Wenn beide keine Ergebnisse liefern, versuche OpenCitations
    const openCitationsData = await fetchOpenCitationsCitations(doi);
    
    return openCitationsData;
  } catch (error) {
    console.error('Fehler bei der Zitationsextraktion:', error);
    return {
      doi,
      citations: [],
      references: [],
      error: error.message
    };
  }
}

/**
 * Ruft Zitationen von Crossref ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchCrossrefCitations(doi) {
  try {
    // Crossref API für Referenzen (was die Publikation zitiert)
    const referencesUrl = `https://api.crossref.org/works/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
      }
    });
    
    if (!referencesResponse.ok) {
      throw new Error(`Crossref API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Referenzen
    const references = [];
    if (referencesData.message && referencesData.message.reference) {
      referencesData.message.reference.forEach(ref => {
        references.push({
          doi: ref.DOI,
          title: ref['article-title'] || ref.title,
          authors: ref.author ? [ref.author] : [],
          year: ref.year,
          journal: ref['journal-title']
        });
      });
    }
    
    // Für Zitationen (was die Publikation zitiert) gibt es keine direkte Crossref-API
    // Wir könnten OpenCitations oder Semantic Scholar verwenden
    
    return {
      doi,
      citations: [], // Crossref bietet keine direkte API für eingehende Zitationen
      references,
      source: 'crossref'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Crossref-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von Semantic Scholar ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchSemanticScholarCitations(doi) {
  try {
    // Semantic Scholar API
    const apiUrl = `https://api.semanticscholar.org/v1/paper/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      throw new Error(`Semantic Scholar API-Fehler: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Extrahiere Zitationen (eingehende Zitationen)
    const citations = data.citations ? data.citations.map(citation => ({
      doi: citation.doi,
      title: citation.title,
      authors: citation.authors ? citation.authors.map(author => author.name) : [],
      year: citation.year,
      journal: citation.venue
    })) : [];
    
    // Extrahiere Referenzen (ausgehende Zitationen)
    const references = data.references ? data.references.map(reference => ({
      doi: reference.doi,
      title: reference.title,
      authors: reference.authors ? reference.authors.map(author => author.name) : [],
      year: reference.year,
      journal: reference.venue
    })) : [];
    
    return {
      doi,
      citations,
      references,
      source: 'semantic_scholar'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Semantic Scholar-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von OpenCitations ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchOpenCitationsCitations(doi) {
  try {
    // OpenCitations API für eingehende Zitationen
    const citationsUrl = `https://opencitations.net/index/api/v1/citations/${encodeURIComponent(doi)}`;
    const citationsResponse = await fetch(citationsUrl);
    
    if (!citationsResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${citationsResponse.status}`);
    }
    
    const citationsData = await citationsResponse.json();
    
    // OpenCitations API für ausgehende Referenzen
    const referencesUrl = `https://opencitations.net/index/api/v1/references/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl);
    
    if (!referencesResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Zitationen
    const citations = citationsData.map(citation => ({
      doi: citation.citing,
      year: citation.creation ? citation.creation.substring(0, 4) : null
    }));
    
    // Extrahiere Referenzen
    const references = referencesData.map(reference => ({
      doi: reference.cited,
      year: reference.creation ? reference.creation.substring(0, 4) : null
    }));
    
    // Anreicherung der Metadaten für Zitationen und Referenzen
    const enrichedCitations = await enrichCitationsMetadata(citations);
    const enrichedReferences = await enrichCitationsMetadata(references);
    
    return {
      doi,
      citations: enrichedCitations,
      references: enrichedReferences,
      source: 'opencitations'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von OpenCitations-Zitationen:', error);
    return {
      doi,
      citations: [],
      references: [],
      source: 'opencitations',
      error: error.message
    };
  }
}

/**
 * Reichert Zitationsmetadaten mit Titel und Autoren an
 * @param {Array} citations - Liste von Zitationen mit DOIs
 * @returns {Promise<Array>} - Angereicherte Zitationen
 */
async function enrichCitationsMetadata(citations) {
  const enrichedCitations = [];
  
  for (const citation of citations) {
    if (!citation.doi) {
      enrichedCitations.push(citation);
      continue;
    }
    
    try {
      // Crossref API für Metadaten
      const url = `https://api.crossref.org/works/${encodeURIComponent(citation.doi)}`;
      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
        }
      });
      
      if (!response.ok) {
        enrichedCitations.push(citation);
        continue;
      }
      
      const data = await response.json();
      const message = data.message;
      
      // Extrahiere Titel
      const title = message.title ? message.title[0] : null;
      
      // Extrahiere Autoren
      const authors = message.author ? message.author.map(author => 
        `${author.given || ''} ${author.family || ''}`.trim()
      ) : [];
      
      // Extrahiere Journal
      const journal = message['container-title'] ? message['container-title'][0] : null;
      
      // Extrahiere Jahr
      const year = message.published ? 
        (message.published['date-parts'] ? message.published['date-parts'][0][0] : null) : 
        citation.year;
      
      enrichedCitations.push({
        ...citation,
        title,
        authors,
        journal,
        year
      });
    } catch (error) {
      console.error(`Fehler beim Anreichern der Metadaten für DOI ${citation.doi}:`, error);
      enrichedCitations.push(citation);
    }
  }
  
  return enrichedCitations;
}

/**
 * Extrahiert Impact-Faktoren und andere Metriken für eine Publikation
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Metriken
 */
export async function extractMetricsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche Altmetric
    const altmetricData = await fetchAltmetricData(doi);
    
    // Versuche Dimensions
    const dimensionsData = await fetchDimensionsData(doi);
    
    // Kombiniere die Daten
    return {
      doi,
      altmetric: altmetricData,
      dimensions: dimensionsData,
      timestamp: Date.now()
    };
  } catch (error) {
    console.error('Fehler bei der Metrikextraktion:', error);
    return {
      doi,
      error: error.message
    };
  }
}

/**
 * Ruft Altmetric-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Altmetric-Daten
 */
async function fetchAltmetricData(doi) {
  try {
    // Altmetric API
    const apiUrl = `https://api.altmetric.com/v1/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      score: data.score,
      readers: {
        mendeley: data.readers ? data.readers.mendeley : 0,
        citeulike: data.readers ? data.readers.citeulike : 0
      },
      mentions: {
        twitter: data.cited_by_tweeters_count || 0,
        facebook: data.cited_by_fbwalls_count || 0,
        blogs: data.cited_by_feeds_count || 0,
        news: data.cited_by_msm_count || 0,
        reddit: data.cited_by_rdts_count || 0,
        wikipedia: data.cited_by_wikipedia_count || 0
      },
      url: data.details_url
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Altmetric-Daten:', error);
    return null;
  }
}

/**
 * Ruft Dimensions-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Dimensions-Daten
 */
async function fetchDimensionsData(doi) {
  try {
    // Dimensions API (öffentliche Endpunkte sind begrenzt)
    // Dies ist ein Beispiel und müsste mit einem tatsächlichen API-Schlüssel implementiert werden
    const apiUrl = `https://metrics-api.dimensions.ai/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      times_cited: data.times_cited || 0,
      recent_citations: data.recent_citations || 0,
      relative_citation_ratio: data.relative_citation_ratio || 0,
      field_citation_ratio: data.field_citation_ratio || 0
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Dimensions-Daten:', error);
    return null;
  }
}/**
 * Hilfsfunktionen zur automatischen Extraktion von Zitationen und Referenzen
 * aus wissenschaftlichen Publikationen
 */

import fetch from 'node-fetch';

/**
 * Extrahiert Zitationen und Referenzen aus einer Publikation mit DOI
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Extrahierte Zitationen und Referenzen
 */
export async function extractCitationsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche zuerst Crossref
    const crossrefData = await fetchCrossrefCitations(doi);
    if (crossrefData && crossrefData.citations && crossrefData.citations.length > 0) {
      return crossrefData;
    }
    
    // Wenn Crossref keine Ergebnisse liefert, versuche Semantic Scholar
    const semanticScholarData = await fetchSemanticScholarCitations(doi);
    if (semanticScholarData && semanticScholarData.citations && semanticScholarData.citations.length > 0) {
      return semanticScholarData;
    }
    
    // Wenn beide keine Ergebnisse liefern, versuche OpenCitations
    const openCitationsData = await fetchOpenCitationsCitations(doi);
    
    return openCitationsData;
  } catch (error) {
    console.error('Fehler bei der Zitationsextraktion:', error);
    return {
      doi,
      citations: [],
      references: [],
      error: error.message
    };
  }
}

/**
 * Ruft Zitationen von Crossref ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchCrossrefCitations(doi) {
  try {
    // Crossref API für Referenzen (was die Publikation zitiert)
    const referencesUrl = `https://api.crossref.org/works/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
      }
    });
    
    if (!referencesResponse.ok) {
      throw new Error(`Crossref API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Referenzen
    const references = [];
    if (referencesData.message && referencesData.message.reference) {
      referencesData.message.reference.forEach(ref => {
        references.push({
          doi: ref.DOI,
          title: ref['article-title'] || ref.title,
          authors: ref.author ? [ref.author] : [],
          year: ref.year,
          journal: ref['journal-title']
        });
      });
    }
    
    // Für Zitationen (was die Publikation zitiert) gibt es keine direkte Crossref-API
    // Wir könnten OpenCitations oder Semantic Scholar verwenden
    
    return {
      doi,
      citations: [], // Crossref bietet keine direkte API für eingehende Zitationen
      references,
      source: 'crossref'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Crossref-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von Semantic Scholar ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchSemanticScholarCitations(doi) {
  try {
    // Semantic Scholar API
    const apiUrl = `https://api.semanticscholar.org/v1/paper/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      throw new Error(`Semantic Scholar API-Fehler: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Extrahiere Zitationen (eingehende Zitationen)
    const citations = data.citations ? data.citations.map(citation => ({
      doi: citation.doi,
      title: citation.title,
      authors: citation.authors ? citation.authors.map(author => author.name) : [],
      year: citation.year,
      journal: citation.venue
    })) : [];
    
    // Extrahiere Referenzen (ausgehende Zitationen)
    const references = data.references ? data.references.map(reference => ({
      doi: reference.doi,
      title: reference.title,
      authors: reference.authors ? reference.authors.map(author => author.name) : [],
      year: reference.year,
      journal: reference.venue
    })) : [];
    
    return {
      doi,
      citations,
      references,
      source: 'semantic_scholar'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Semantic Scholar-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von OpenCitations ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchOpenCitationsCitations(doi) {
  try {
    // OpenCitations API für eingehende Zitationen
    const citationsUrl = `https://opencitations.net/index/api/v1/citations/${encodeURIComponent(doi)}`;
    const citationsResponse = await fetch(citationsUrl);
    
    if (!citationsResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${citationsResponse.status}`);
    }
    
    const citationsData = await citationsResponse.json();
    
    // OpenCitations API für ausgehende Referenzen
    const referencesUrl = `https://opencitations.net/index/api/v1/references/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl);
    
    if (!referencesResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Zitationen
    const citations = citationsData.map(citation => ({
      doi: citation.citing,
      year: citation.creation ? citation.creation.substring(0, 4) : null
    }));
    
    // Extrahiere Referenzen
    const references = referencesData.map(reference => ({
      doi: reference.cited,
      year: reference.creation ? reference.creation.substring(0, 4) : null
    }));
    
    // Anreicherung der Metadaten für Zitationen und Referenzen
    const enrichedCitations = await enrichCitationsMetadata(citations);
    const enrichedReferences = await enrichCitationsMetadata(references);
    
    return {
      doi,
      citations: enrichedCitations,
      references: enrichedReferences,
      source: 'opencitations'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von OpenCitations-Zitationen:', error);
    return {
      doi,
      citations: [],
      references: [],
      source: 'opencitations',
      error: error.message
    };
  }
}

/**
 * Reichert Zitationsmetadaten mit Titel und Autoren an
 * @param {Array} citations - Liste von Zitationen mit DOIs
 * @returns {Promise<Array>} - Angereicherte Zitationen
 */
async function enrichCitationsMetadata(citations) {
  const enrichedCitations = [];
  
  for (const citation of citations) {
    if (!citation.doi) {
      enrichedCitations.push(citation);
      continue;
    }
    
    try {
      // Crossref API für Metadaten
      const url = `https://api.crossref.org/works/${encodeURIComponent(citation.doi)}`;
      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
        }
      });
      
      if (!response.ok) {
        enrichedCitations.push(citation);
        continue;
      }
      
      const data = await response.json();
      const message = data.message;
      
      // Extrahiere Titel
      const title = message.title ? message.title[0] : null;
      
      // Extrahiere Autoren
      const authors = message.author ? message.author.map(author => 
        `${author.given || ''} ${author.family || ''}`.trim()
      ) : [];
      
      // Extrahiere Journal
      const journal = message['container-title'] ? message['container-title'][0] : null;
      
      // Extrahiere Jahr
      const year = message.published ? 
        (message.published['date-parts'] ? message.published['date-parts'][0][0] : null) : 
        citation.year;
      
      enrichedCitations.push({
        ...citation,
        title,
        authors,
        journal,
        year
      });
    } catch (error) {
      console.error(`Fehler beim Anreichern der Metadaten für DOI ${citation.doi}:`, error);
      enrichedCitations.push(citation);
    }
  }
  
  return enrichedCitations;
}

/**
 * Extrahiert Impact-Faktoren und andere Metriken für eine Publikation
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Metriken
 */
export async function extractMetricsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche Altmetric
    const altmetricData = await fetchAltmetricData(doi);
    
    // Versuche Dimensions
    const dimensionsData = await fetchDimensionsData(doi);
    
    // Kombiniere die Daten
    return {
      doi,
      altmetric: altmetricData,
      dimensions: dimensionsData,
      timestamp: Date.now()
    };
  } catch (error) {
    console.error('Fehler bei der Metrikextraktion:', error);
    return {
      doi,
      error: error.message
    };
  }
}

/**
 * Ruft Altmetric-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Altmetric-Daten
 */
async function fetchAltmetricData(doi) {
  try {
    // Altmetric API
    const apiUrl = `https://api.altmetric.com/v1/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      score: data.score,
      readers: {
        mendeley: data.readers ? data.readers.mendeley : 0,
        citeulike: data.readers ? data.readers.citeulike : 0
      },
      mentions: {
        twitter: data.cited_by_tweeters_count || 0,
        facebook: data.cited_by_fbwalls_count || 0,
        blogs: data.cited_by_feeds_count || 0,
        news: data.cited_by_msm_count || 0,
        reddit: data.cited_by_rdts_count || 0,
        wikipedia: data.cited_by_wikipedia_count || 0
      },
      url: data.details_url
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Altmetric-Daten:', error);
    return null;
  }
}

/**
 * Ruft Dimensions-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Dimensions-Daten
 */
async function fetchDimensionsData(doi) {
  try {
    // Dimensions API (öffentliche Endpunkte sind begrenzt)
    // Dies ist ein Beispiel und müsste mit einem tatsächlichen API-Schlüssel implementiert werden
    const apiUrl = `https://metrics-api.dimensions.ai/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      times_cited: data.times_cited || 0,
      recent_citations: data.recent_citations || 0,
      relative_citation_ratio: data.relative_citation_ratio || 0,
      field_citation_ratio: data.field_citation_ratio || 0
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Dimensions-Daten:', error);
    return null;
  }
}/**
 * Hilfsfunktionen zur automatischen Extraktion von Zitationen und Referenzen
 * aus wissenschaftlichen Publikationen
 */

import fetch from 'node-fetch';

/**
 * Extrahiert Zitationen und Referenzen aus einer Publikation mit DOI
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Extrahierte Zitationen und Referenzen
 */
export async function extractCitationsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche zuerst Crossref
    const crossrefData = await fetchCrossrefCitations(doi);
    if (crossrefData && crossrefData.citations && crossrefData.citations.length > 0) {
      return crossrefData;
    }
    
    // Wenn Crossref keine Ergebnisse liefert, versuche Semantic Scholar
    const semanticScholarData = await fetchSemanticScholarCitations(doi);
    if (semanticScholarData && semanticScholarData.citations && semanticScholarData.citations.length > 0) {
      return semanticScholarData;
    }
    
    // Wenn beide keine Ergebnisse liefern, versuche OpenCitations
    const openCitationsData = await fetchOpenCitationsCitations(doi);
    
    return openCitationsData;
  } catch (error) {
    console.error('Fehler bei der Zitationsextraktion:', error);
    return {
      doi,
      citations: [],
      references: [],
      error: error.message
    };
  }
}

/**
 * Ruft Zitationen von Crossref ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchCrossrefCitations(doi) {
  try {
    // Crossref API für Referenzen (was die Publikation zitiert)
    const referencesUrl = `https://api.crossref.org/works/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
      }
    });
    
    if (!referencesResponse.ok) {
      throw new Error(`Crossref API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Referenzen
    const references = [];
    if (referencesData.message && referencesData.message.reference) {
      referencesData.message.reference.forEach(ref => {
        references.push({
          doi: ref.DOI,
          title: ref['article-title'] || ref.title,
          authors: ref.author ? [ref.author] : [],
          year: ref.year,
          journal: ref['journal-title']
        });
      });
    }
    
    // Für Zitationen (was die Publikation zitiert) gibt es keine direkte Crossref-API
    // Wir könnten OpenCitations oder Semantic Scholar verwenden
    
    return {
      doi,
      citations: [], // Crossref bietet keine direkte API für eingehende Zitationen
      references,
      source: 'crossref'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Crossref-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von Semantic Scholar ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchSemanticScholarCitations(doi) {
  try {
    // Semantic Scholar API
    const apiUrl = `https://api.semanticscholar.org/v1/paper/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      throw new Error(`Semantic Scholar API-Fehler: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Extrahiere Zitationen (eingehende Zitationen)
    const citations = data.citations ? data.citations.map(citation => ({
      doi: citation.doi,
      title: citation.title,
      authors: citation.authors ? citation.authors.map(author => author.name) : [],
      year: citation.year,
      journal: citation.venue
    })) : [];
    
    // Extrahiere Referenzen (ausgehende Zitationen)
    const references = data.references ? data.references.map(reference => ({
      doi: reference.doi,
      title: reference.title,
      authors: reference.authors ? reference.authors.map(author => author.name) : [],
      year: reference.year,
      journal: reference.venue
    })) : [];
    
    return {
      doi,
      citations,
      references,
      source: 'semantic_scholar'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Semantic Scholar-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von OpenCitations ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchOpenCitationsCitations(doi) {
  try {
    // OpenCitations API für eingehende Zitationen
    const citationsUrl = `https://opencitations.net/index/api/v1/citations/${encodeURIComponent(doi)}`;
    const citationsResponse = await fetch(citationsUrl);
    
    if (!citationsResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${citationsResponse.status}`);
    }
    
    const citationsData = await citationsResponse.json();
    
    // OpenCitations API für ausgehende Referenzen
    const referencesUrl = `https://opencitations.net/index/api/v1/references/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl);
    
    if (!referencesResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Zitationen
    const citations = citationsData.map(citation => ({
      doi: citation.citing,
      year: citation.creation ? citation.creation.substring(0, 4) : null
    }));
    
    // Extrahiere Referenzen
    const references = referencesData.map(reference => ({
      doi: reference.cited,
      year: reference.creation ? reference.creation.substring(0, 4) : null
    }));
    
    // Anreicherung der Metadaten für Zitationen und Referenzen
    const enrichedCitations = await enrichCitationsMetadata(citations);
    const enrichedReferences = await enrichCitationsMetadata(references);
    
    return {
      doi,
      citations: enrichedCitations,
      references: enrichedReferences,
      source: 'opencitations'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von OpenCitations-Zitationen:', error);
    return {
      doi,
      citations: [],
      references: [],
      source: 'opencitations',
      error: error.message
    };
  }
}

/**
 * Reichert Zitationsmetadaten mit Titel und Autoren an
 * @param {Array} citations - Liste von Zitationen mit DOIs
 * @returns {Promise<Array>} - Angereicherte Zitationen
 */
async function enrichCitationsMetadata(citations) {
  const enrichedCitations = [];
  
  for (const citation of citations) {
    if (!citation.doi) {
      enrichedCitations.push(citation);
      continue;
    }
    
    try {
      // Crossref API für Metadaten
      const url = `https://api.crossref.org/works/${encodeURIComponent(citation.doi)}`;
      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
        }
      });
      
      if (!response.ok) {
        enrichedCitations.push(citation);
        continue;
      }
      
      const data = await response.json();
      const message = data.message;
      
      // Extrahiere Titel
      const title = message.title ? message.title[0] : null;
      
      // Extrahiere Autoren
      const authors = message.author ? message.author.map(author => 
        `${author.given || ''} ${author.family || ''}`.trim()
      ) : [];
      
      // Extrahiere Journal
      const journal = message['container-title'] ? message['container-title'][0] : null;
      
      // Extrahiere Jahr
      const year = message.published ? 
        (message.published['date-parts'] ? message.published['date-parts'][0][0] : null) : 
        citation.year;
      
      enrichedCitations.push({
        ...citation,
        title,
        authors,
        journal,
        year
      });
    } catch (error) {
      console.error(`Fehler beim Anreichern der Metadaten für DOI ${citation.doi}:`, error);
      enrichedCitations.push(citation);
    }
  }
  
  return enrichedCitations;
}

/**
 * Extrahiert Impact-Faktoren und andere Metriken für eine Publikation
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Metriken
 */
export async function extractMetricsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche Altmetric
    const altmetricData = await fetchAltmetricData(doi);
    
    // Versuche Dimensions
    const dimensionsData = await fetchDimensionsData(doi);
    
    // Kombiniere die Daten
    return {
      doi,
      altmetric: altmetricData,
      dimensions: dimensionsData,
      timestamp: Date.now()
    };
  } catch (error) {
    console.error('Fehler bei der Metrikextraktion:', error);
    return {
      doi,
      error: error.message
    };
  }
}

/**
 * Ruft Altmetric-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Altmetric-Daten
 */
async function fetchAltmetricData(doi) {
  try {
    // Altmetric API
    const apiUrl = `https://api.altmetric.com/v1/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      score: data.score,
      readers: {
        mendeley: data.readers ? data.readers.mendeley : 0,
        citeulike: data.readers ? data.readers.citeulike : 0
      },
      mentions: {
        twitter: data.cited_by_tweeters_count || 0,
        facebook: data.cited_by_fbwalls_count || 0,
        blogs: data.cited_by_feeds_count || 0,
        news: data.cited_by_msm_count || 0,
        reddit: data.cited_by_rdts_count || 0,
        wikipedia: data.cited_by_wikipedia_count || 0
      },
      url: data.details_url
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Altmetric-Daten:', error);
    return null;
  }
}

/**
 * Ruft Dimensions-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Dimensions-Daten
 */
async function fetchDimensionsData(doi) {
  try {
    // Dimensions API (öffentliche Endpunkte sind begrenzt)
    // Dies ist ein Beispiel und müsste mit einem tatsächlichen API-Schlüssel implementiert werden
    const apiUrl = `https://metrics-api.dimensions.ai/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      times_cited: data.times_cited || 0,
      recent_citations: data.recent_citations || 0,
      relative_citation_ratio: data.relative_citation_ratio || 0,
      field_citation_ratio: data.field_citation_ratio || 0
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Dimensions-Daten:', error);
    return null;
  }
}/**
 * Hilfsfunktionen zur automatischen Extraktion von Zitationen und Referenzen
 * aus wissenschaftlichen Publikationen
 */

import fetch from 'node-fetch';

/**
 * Extrahiert Zitationen und Referenzen aus einer Publikation mit DOI
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Extrahierte Zitationen und Referenzen
 */
export async function extractCitationsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche zuerst Crossref
    const crossrefData = await fetchCrossrefCitations(doi);
    if (crossrefData && crossrefData.citations && crossrefData.citations.length > 0) {
      return crossrefData;
    }
    
    // Wenn Crossref keine Ergebnisse liefert, versuche Semantic Scholar
    const semanticScholarData = await fetchSemanticScholarCitations(doi);
    if (semanticScholarData && semanticScholarData.citations && semanticScholarData.citations.length > 0) {
      return semanticScholarData;
    }
    
    // Wenn beide keine Ergebnisse liefern, versuche OpenCitations
    const openCitationsData = await fetchOpenCitationsCitations(doi);
    
    return openCitationsData;
  } catch (error) {
    console.error('Fehler bei der Zitationsextraktion:', error);
    return {
      doi,
      citations: [],
      references: [],
      error: error.message
    };
  }
}

/**
 * Ruft Zitationen von Crossref ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchCrossrefCitations(doi) {
  try {
    // Crossref API für Referenzen (was die Publikation zitiert)
    const referencesUrl = `https://api.crossref.org/works/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
      }
    });
    
    if (!referencesResponse.ok) {
      throw new Error(`Crossref API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Referenzen
    const references = [];
    if (referencesData.message && referencesData.message.reference) {
      referencesData.message.reference.forEach(ref => {
        references.push({
          doi: ref.DOI,
          title: ref['article-title'] || ref.title,
          authors: ref.author ? [ref.author] : [],
          year: ref.year,
          journal: ref['journal-title']
        });
      });
    }
    
    // Für Zitationen (was die Publikation zitiert) gibt es keine direkte Crossref-API
    // Wir könnten OpenCitations oder Semantic Scholar verwenden
    
    return {
      doi,
      citations: [], // Crossref bietet keine direkte API für eingehende Zitationen
      references,
      source: 'crossref'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Crossref-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von Semantic Scholar ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchSemanticScholarCitations(doi) {
  try {
    // Semantic Scholar API
    const apiUrl = `https://api.semanticscholar.org/v1/paper/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      throw new Error(`Semantic Scholar API-Fehler: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Extrahiere Zitationen (eingehende Zitationen)
    const citations = data.citations ? data.citations.map(citation => ({
      doi: citation.doi,
      title: citation.title,
      authors: citation.authors ? citation.authors.map(author => author.name) : [],
      year: citation.year,
      journal: citation.venue
    })) : [];
    
    // Extrahiere Referenzen (ausgehende Zitationen)
    const references = data.references ? data.references.map(reference => ({
      doi: reference.doi,
      title: reference.title,
      authors: reference.authors ? reference.authors.map(author => author.name) : [],
      year: reference.year,
      journal: reference.venue
    })) : [];
    
    return {
      doi,
      citations,
      references,
      source: 'semantic_scholar'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Semantic Scholar-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von OpenCitations ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchOpenCitationsCitations(doi) {
  try {
    // OpenCitations API für eingehende Zitationen
    const citationsUrl = `https://opencitations.net/index/api/v1/citations/${encodeURIComponent(doi)}`;
    const citationsResponse = await fetch(citationsUrl);
    
    if (!citationsResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${citationsResponse.status}`);
    }
    
    const citationsData = await citationsResponse.json();
    
    // OpenCitations API für ausgehende Referenzen
    const referencesUrl = `https://opencitations.net/index/api/v1/references/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl);
    
    if (!referencesResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Zitationen
    const citations = citationsData.map(citation => ({
      doi: citation.citing,
      year: citation.creation ? citation.creation.substring(0, 4) : null
    }));
    
    // Extrahiere Referenzen
    const references = referencesData.map(reference => ({
      doi: reference.cited,
      year: reference.creation ? reference.creation.substring(0, 4) : null
    }));
    
    // Anreicherung der Metadaten für Zitationen und Referenzen
    const enrichedCitations = await enrichCitationsMetadata(citations);
    const enrichedReferences = await enrichCitationsMetadata(references);
    
    return {
      doi,
      citations: enrichedCitations,
      references: enrichedReferences,
      source: 'opencitations'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von OpenCitations-Zitationen:', error);
    return {
      doi,
      citations: [],
      references: [],
      source: 'opencitations',
      error: error.message
    };
  }
}

/**
 * Reichert Zitationsmetadaten mit Titel und Autoren an
 * @param {Array} citations - Liste von Zitationen mit DOIs
 * @returns {Promise<Array>} - Angereicherte Zitationen
 */
async function enrichCitationsMetadata(citations) {
  const enrichedCitations = [];
  
  for (const citation of citations) {
    if (!citation.doi) {
      enrichedCitations.push(citation);
      continue;
    }
    
    try {
      // Crossref API für Metadaten
      const url = `https://api.crossref.org/works/${encodeURIComponent(citation.doi)}`;
      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
        }
      });
      
      if (!response.ok) {
        enrichedCitations.push(citation);
        continue;
      }
      
      const data = await response.json();
      const message = data.message;
      
      // Extrahiere Titel
      const title = message.title ? message.title[0] : null;
      
      // Extrahiere Autoren
      const authors = message.author ? message.author.map(author => 
        `${author.given || ''} ${author.family || ''}`.trim()
      ) : [];
      
      // Extrahiere Journal
      const journal = message['container-title'] ? message['container-title'][0] : null;
      
      // Extrahiere Jahr
      const year = message.published ? 
        (message.published['date-parts'] ? message.published['date-parts'][0][0] : null) : 
        citation.year;
      
      enrichedCitations.push({
        ...citation,
        title,
        authors,
        journal,
        year
      });
    } catch (error) {
      console.error(`Fehler beim Anreichern der Metadaten für DOI ${citation.doi}:`, error);
      enrichedCitations.push(citation);
    }
  }
  
  return enrichedCitations;
}

/**
 * Extrahiert Impact-Faktoren und andere Metriken für eine Publikation
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Metriken
 */
export async function extractMetricsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche Altmetric
    const altmetricData = await fetchAltmetricData(doi);
    
    // Versuche Dimensions
    const dimensionsData = await fetchDimensionsData(doi);
    
    // Kombiniere die Daten
    return {
      doi,
      altmetric: altmetricData,
      dimensions: dimensionsData,
      timestamp: Date.now()
    };
  } catch (error) {
    console.error('Fehler bei der Metrikextraktion:', error);
    return {
      doi,
      error: error.message
    };
  }
}

/**
 * Ruft Altmetric-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Altmetric-Daten
 */
async function fetchAltmetricData(doi) {
  try {
    // Altmetric API
    const apiUrl = `https://api.altmetric.com/v1/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      score: data.score,
      readers: {
        mendeley: data.readers ? data.readers.mendeley : 0,
        citeulike: data.readers ? data.readers.citeulike : 0
      },
      mentions: {
        twitter: data.cited_by_tweeters_count || 0,
        facebook: data.cited_by_fbwalls_count || 0,
        blogs: data.cited_by_feeds_count || 0,
        news: data.cited_by_msm_count || 0,
        reddit: data.cited_by_rdts_count || 0,
        wikipedia: data.cited_by_wikipedia_count || 0
      },
      url: data.details_url
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Altmetric-Daten:', error);
    return null;
  }
}

/**
 * Ruft Dimensions-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Dimensions-Daten
 */
async function fetchDimensionsData(doi) {
  try {
    // Dimensions API (öffentliche Endpunkte sind begrenzt)
    // Dies ist ein Beispiel und müsste mit einem tatsächlichen API-Schlüssel implementiert werden
    const apiUrl = `https://metrics-api.dimensions.ai/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      times_cited: data.times_cited || 0,
      recent_citations: data.recent_citations || 0,
      relative_citation_ratio: data.relative_citation_ratio || 0,
      field_citation_ratio: data.field_citation_ratio || 0
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Dimensions-Daten:', error);
    return null;
  }
}/**
 * Hilfsfunktionen zur automatischen Extraktion von Zitationen und Referenzen
 * aus wissenschaftlichen Publikationen
 */

import fetch from 'node-fetch';

/**
 * Extrahiert Zitationen und Referenzen aus einer Publikation mit DOI
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Extrahierte Zitationen und Referenzen
 */
export async function extractCitationsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche zuerst Crossref
    const crossrefData = await fetchCrossrefCitations(doi);
    if (crossrefData && crossrefData.citations && crossrefData.citations.length > 0) {
      return crossrefData;
    }
    
    // Wenn Crossref keine Ergebnisse liefert, versuche Semantic Scholar
    const semanticScholarData = await fetchSemanticScholarCitations(doi);
    if (semanticScholarData && semanticScholarData.citations && semanticScholarData.citations.length > 0) {
      return semanticScholarData;
    }
    
    // Wenn beide keine Ergebnisse liefern, versuche OpenCitations
    const openCitationsData = await fetchOpenCitationsCitations(doi);
    
    return openCitationsData;
  } catch (error) {
    console.error('Fehler bei der Zitationsextraktion:', error);
    return {
      doi,
      citations: [],
      references: [],
      error: error.message
    };
  }
}

/**
 * Ruft Zitationen von Crossref ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchCrossrefCitations(doi) {
  try {
    // Crossref API für Referenzen (was die Publikation zitiert)
    const referencesUrl = `https://api.crossref.org/works/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
      }
    });
    
    if (!referencesResponse.ok) {
      throw new Error(`Crossref API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Referenzen
    const references = [];
    if (referencesData.message && referencesData.message.reference) {
      referencesData.message.reference.forEach(ref => {
        references.push({
          doi: ref.DOI,
          title: ref['article-title'] || ref.title,
          authors: ref.author ? [ref.author] : [],
          year: ref.year,
          journal: ref['journal-title']
        });
      });
    }
    
    // Für Zitationen (was die Publikation zitiert) gibt es keine direkte Crossref-API
    // Wir könnten OpenCitations oder Semantic Scholar verwenden
    
    return {
      doi,
      citations: [], // Crossref bietet keine direkte API für eingehende Zitationen
      references,
      source: 'crossref'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Crossref-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von Semantic Scholar ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchSemanticScholarCitations(doi) {
  try {
    // Semantic Scholar API
    const apiUrl = `https://api.semanticscholar.org/v1/paper/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      throw new Error(`Semantic Scholar API-Fehler: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Extrahiere Zitationen (eingehende Zitationen)
    const citations = data.citations ? data.citations.map(citation => ({
      doi: citation.doi,
      title: citation.title,
      authors: citation.authors ? citation.authors.map(author => author.name) : [],
      year: citation.year,
      journal: citation.venue
    })) : [];
    
    // Extrahiere Referenzen (ausgehende Zitationen)
    const references = data.references ? data.references.map(reference => ({
      doi: reference.doi,
      title: reference.title,
      authors: reference.authors ? reference.authors.map(author => author.name) : [],
      year: reference.year,
      journal: reference.venue
    })) : [];
    
    return {
      doi,
      citations,
      references,
      source: 'semantic_scholar'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Semantic Scholar-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von OpenCitations ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchOpenCitationsCitations(doi) {
  try {
    // OpenCitations API für eingehende Zitationen
    const citationsUrl = `https://opencitations.net/index/api/v1/citations/${encodeURIComponent(doi)}`;
    const citationsResponse = await fetch(citationsUrl);
    
    if (!citationsResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${citationsResponse.status}`);
    }
    
    const citationsData = await citationsResponse.json();
    
    // OpenCitations API für ausgehende Referenzen
    const referencesUrl = `https://opencitations.net/index/api/v1/references/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl);
    
    if (!referencesResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Zitationen
    const citations = citationsData.map(citation => ({
      doi: citation.citing,
      year: citation.creation ? citation.creation.substring(0, 4) : null
    }));
    
    // Extrahiere Referenzen
    const references = referencesData.map(reference => ({
      doi: reference.cited,
      year: reference.creation ? reference.creation.substring(0, 4) : null
    }));
    
    // Anreicherung der Metadaten für Zitationen und Referenzen
    const enrichedCitations = await enrichCitationsMetadata(citations);
    const enrichedReferences = await enrichCitationsMetadata(references);
    
    return {
      doi,
      citations: enrichedCitations,
      references: enrichedReferences,
      source: 'opencitations'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von OpenCitations-Zitationen:', error);
    return {
      doi,
      citations: [],
      references: [],
      source: 'opencitations',
      error: error.message
    };
  }
}

/**
 * Reichert Zitationsmetadaten mit Titel und Autoren an
 * @param {Array} citations - Liste von Zitationen mit DOIs
 * @returns {Promise<Array>} - Angereicherte Zitationen
 */
async function enrichCitationsMetadata(citations) {
  const enrichedCitations = [];
  
  for (const citation of citations) {
    if (!citation.doi) {
      enrichedCitations.push(citation);
      continue;
    }
    
    try {
      // Crossref API für Metadaten
      const url = `https://api.crossref.org/works/${encodeURIComponent(citation.doi)}`;
      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
        }
      });
      
      if (!response.ok) {
        enrichedCitations.push(citation);
        continue;
      }
      
      const data = await response.json();
      const message = data.message;
      
      // Extrahiere Titel
      const title = message.title ? message.title[0] : null;
      
      // Extrahiere Autoren
      const authors = message.author ? message.author.map(author => 
        `${author.given || ''} ${author.family || ''}`.trim()
      ) : [];
      
      // Extrahiere Journal
      const journal = message['container-title'] ? message['container-title'][0] : null;
      
      // Extrahiere Jahr
      const year = message.published ? 
        (message.published['date-parts'] ? message.published['date-parts'][0][0] : null) : 
        citation.year;
      
      enrichedCitations.push({
        ...citation,
        title,
        authors,
        journal,
        year
      });
    } catch (error) {
      console.error(`Fehler beim Anreichern der Metadaten für DOI ${citation.doi}:`, error);
      enrichedCitations.push(citation);
    }
  }
  
  return enrichedCitations;
}

/**
 * Extrahiert Impact-Faktoren und andere Metriken für eine Publikation
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Metriken
 */
export async function extractMetricsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche Altmetric
    const altmetricData = await fetchAltmetricData(doi);
    
    // Versuche Dimensions
    const dimensionsData = await fetchDimensionsData(doi);
    
    // Kombiniere die Daten
    return {
      doi,
      altmetric: altmetricData,
      dimensions: dimensionsData,
      timestamp: Date.now()
    };
  } catch (error) {
    console.error('Fehler bei der Metrikextraktion:', error);
    return {
      doi,
      error: error.message
    };
  }
}

/**
 * Ruft Altmetric-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Altmetric-Daten
 */
async function fetchAltmetricData(doi) {
  try {
    // Altmetric API
    const apiUrl = `https://api.altmetric.com/v1/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      score: data.score,
      readers: {
        mendeley: data.readers ? data.readers.mendeley : 0,
        citeulike: data.readers ? data.readers.citeulike : 0
      },
      mentions: {
        twitter: data.cited_by_tweeters_count || 0,
        facebook: data.cited_by_fbwalls_count || 0,
        blogs: data.cited_by_feeds_count || 0,
        news: data.cited_by_msm_count || 0,
        reddit: data.cited_by_rdts_count || 0,
        wikipedia: data.cited_by_wikipedia_count || 0
      },
      url: data.details_url
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Altmetric-Daten:', error);
    return null;
  }
}

/**
 * Ruft Dimensions-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Dimensions-Daten
 */
async function fetchDimensionsData(doi) {
  try {
    // Dimensions API (öffentliche Endpunkte sind begrenzt)
    // Dies ist ein Beispiel und müsste mit einem tatsächlichen API-Schlüssel implementiert werden
    const apiUrl = `https://metrics-api.dimensions.ai/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      times_cited: data.times_cited || 0,
      recent_citations: data.recent_citations || 0,
      relative_citation_ratio: data.relative_citation_ratio || 0,
      field_citation_ratio: data.field_citation_ratio || 0
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Dimensions-Daten:', error);
    return null;
  }
}/**
 * Hilfsfunktionen zur automatischen Extraktion von Zitationen und Referenzen
 * aus wissenschaftlichen Publikationen
 */

import fetch from 'node-fetch';

/**
 * Extrahiert Zitationen und Referenzen aus einer Publikation mit DOI
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Extrahierte Zitationen und Referenzen
 */
export async function extractCitationsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche zuerst Crossref
    const crossrefData = await fetchCrossrefCitations(doi);
    if (crossrefData && crossrefData.citations && crossrefData.citations.length > 0) {
      return crossrefData;
    }
    
    // Wenn Crossref keine Ergebnisse liefert, versuche Semantic Scholar
    const semanticScholarData = await fetchSemanticScholarCitations(doi);
    if (semanticScholarData && semanticScholarData.citations && semanticScholarData.citations.length > 0) {
      return semanticScholarData;
    }
    
    // Wenn beide keine Ergebnisse liefern, versuche OpenCitations
    const openCitationsData = await fetchOpenCitationsCitations(doi);
    
    return openCitationsData;
  } catch (error) {
    console.error('Fehler bei der Zitationsextraktion:', error);
    return {
      doi,
      citations: [],
      references: [],
      error: error.message
    };
  }
}

/**
 * Ruft Zitationen von Crossref ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchCrossrefCitations(doi) {
  try {
    // Crossref API für Referenzen (was die Publikation zitiert)
    const referencesUrl = `https://api.crossref.org/works/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
      }
    });
    
    if (!referencesResponse.ok) {
      throw new Error(`Crossref API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Referenzen
    const references = [];
    if (referencesData.message && referencesData.message.reference) {
      referencesData.message.reference.forEach(ref => {
        references.push({
          doi: ref.DOI,
          title: ref['article-title'] || ref.title,
          authors: ref.author ? [ref.author] : [],
          year: ref.year,
          journal: ref['journal-title']
        });
      });
    }
    
    // Für Zitationen (was die Publikation zitiert) gibt es keine direkte Crossref-API
    // Wir könnten OpenCitations oder Semantic Scholar verwenden
    
    return {
      doi,
      citations: [], // Crossref bietet keine direkte API für eingehende Zitationen
      references,
      source: 'crossref'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Crossref-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von Semantic Scholar ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchSemanticScholarCitations(doi) {
  try {
    // Semantic Scholar API
    const apiUrl = `https://api.semanticscholar.org/v1/paper/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      throw new Error(`Semantic Scholar API-Fehler: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Extrahiere Zitationen (eingehende Zitationen)
    const citations = data.citations ? data.citations.map(citation => ({
      doi: citation.doi,
      title: citation.title,
      authors: citation.authors ? citation.authors.map(author => author.name) : [],
      year: citation.year,
      journal: citation.venue
    })) : [];
    
    // Extrahiere Referenzen (ausgehende Zitationen)
    const references = data.references ? data.references.map(reference => ({
      doi: reference.doi,
      title: reference.title,
      authors: reference.authors ? reference.authors.map(author => author.name) : [],
      year: reference.year,
      journal: reference.venue
    })) : [];
    
    return {
      doi,
      citations,
      references,
      source: 'semantic_scholar'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Semantic Scholar-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von OpenCitations ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchOpenCitationsCitations(doi) {
  try {
    // OpenCitations API für eingehende Zitationen
    const citationsUrl = `https://opencitations.net/index/api/v1/citations/${encodeURIComponent(doi)}`;
    const citationsResponse = await fetch(citationsUrl);
    
    if (!citationsResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${citationsResponse.status}`);
    }
    
    const citationsData = await citationsResponse.json();
    
    // OpenCitations API für ausgehende Referenzen
    const referencesUrl = `https://opencitations.net/index/api/v1/references/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl);
    
    if (!referencesResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Zitationen
    const citations = citationsData.map(citation => ({
      doi: citation.citing,
      year: citation.creation ? citation.creation.substring(0, 4) : null
    }));
    
    // Extrahiere Referenzen
    const references = referencesData.map(reference => ({
      doi: reference.cited,
      year: reference.creation ? reference.creation.substring(0, 4) : null
    }));
    
    // Anreicherung der Metadaten für Zitationen und Referenzen
    const enrichedCitations = await enrichCitationsMetadata(citations);
    const enrichedReferences = await enrichCitationsMetadata(references);
    
    return {
      doi,
      citations: enrichedCitations,
      references: enrichedReferences,
      source: 'opencitations'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von OpenCitations-Zitationen:', error);
    return {
      doi,
      citations: [],
      references: [],
      source: 'opencitations',
      error: error.message
    };
  }
}

/**
 * Reichert Zitationsmetadaten mit Titel und Autoren an
 * @param {Array} citations - Liste von Zitationen mit DOIs
 * @returns {Promise<Array>} - Angereicherte Zitationen
 */
async function enrichCitationsMetadata(citations) {
  const enrichedCitations = [];
  
  for (const citation of citations) {
    if (!citation.doi) {
      enrichedCitations.push(citation);
      continue;
    }
    
    try {
      // Crossref API für Metadaten
      const url = `https://api.crossref.org/works/${encodeURIComponent(citation.doi)}`;
      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
        }
      });
      
      if (!response.ok) {
        enrichedCitations.push(citation);
        continue;
      }
      
      const data = await response.json();
      const message = data.message;
      
      // Extrahiere Titel
      const title = message.title ? message.title[0] : null;
      
      // Extrahiere Autoren
      const authors = message.author ? message.author.map(author => 
        `${author.given || ''} ${author.family || ''}`.trim()
      ) : [];
      
      // Extrahiere Journal
      const journal = message['container-title'] ? message['container-title'][0] : null;
      
      // Extrahiere Jahr
      const year = message.published ? 
        (message.published['date-parts'] ? message.published['date-parts'][0][0] : null) : 
        citation.year;
      
      enrichedCitations.push({
        ...citation,
        title,
        authors,
        journal,
        year
      });
    } catch (error) {
      console.error(`Fehler beim Anreichern der Metadaten für DOI ${citation.doi}:`, error);
      enrichedCitations.push(citation);
    }
  }
  
  return enrichedCitations;
}

/**
 * Extrahiert Impact-Faktoren und andere Metriken für eine Publikation
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Metriken
 */
export async function extractMetricsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche Altmetric
    const altmetricData = await fetchAltmetricData(doi);
    
    // Versuche Dimensions
    const dimensionsData = await fetchDimensionsData(doi);
    
    // Kombiniere die Daten
    return {
      doi,
      altmetric: altmetricData,
      dimensions: dimensionsData,
      timestamp: Date.now()
    };
  } catch (error) {
    console.error('Fehler bei der Metrikextraktion:', error);
    return {
      doi,
      error: error.message
    };
  }
}

/**
 * Ruft Altmetric-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Altmetric-Daten
 */
async function fetchAltmetricData(doi) {
  try {
    // Altmetric API
    const apiUrl = `https://api.altmetric.com/v1/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      score: data.score,
      readers: {
        mendeley: data.readers ? data.readers.mendeley : 0,
        citeulike: data.readers ? data.readers.citeulike : 0
      },
      mentions: {
        twitter: data.cited_by_tweeters_count || 0,
        facebook: data.cited_by_fbwalls_count || 0,
        blogs: data.cited_by_feeds_count || 0,
        news: data.cited_by_msm_count || 0,
        reddit: data.cited_by_rdts_count || 0,
        wikipedia: data.cited_by_wikipedia_count || 0
      },
      url: data.details_url
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Altmetric-Daten:', error);
    return null;
  }
}

/**
 * Ruft Dimensions-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Dimensions-Daten
 */
async function fetchDimensionsData(doi) {
  try {
    // Dimensions API (öffentliche Endpunkte sind begrenzt)
    // Dies ist ein Beispiel und müsste mit einem tatsächlichen API-Schlüssel implementiert werden
    const apiUrl = `https://metrics-api.dimensions.ai/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      times_cited: data.times_cited || 0,
      recent_citations: data.recent_citations || 0,
      relative_citation_ratio: data.relative_citation_ratio || 0,
      field_citation_ratio: data.field_citation_ratio || 0
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Dimensions-Daten:', error);
    return null;
  }
}/**
 * Hilfsfunktionen zur automatischen Extraktion von Zitationen und Referenzen
 * aus wissenschaftlichen Publikationen
 */

import fetch from 'node-fetch';

/**
 * Extrahiert Zitationen und Referenzen aus einer Publikation mit DOI
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Extrahierte Zitationen und Referenzen
 */
export async function extractCitationsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche zuerst Crossref
    const crossrefData = await fetchCrossrefCitations(doi);
    if (crossrefData && crossrefData.citations && crossrefData.citations.length > 0) {
      return crossrefData;
    }
    
    // Wenn Crossref keine Ergebnisse liefert, versuche Semantic Scholar
    const semanticScholarData = await fetchSemanticScholarCitations(doi);
    if (semanticScholarData && semanticScholarData.citations && semanticScholarData.citations.length > 0) {
      return semanticScholarData;
    }
    
    // Wenn beide keine Ergebnisse liefern, versuche OpenCitations
    const openCitationsData = await fetchOpenCitationsCitations(doi);
    
    return openCitationsData;
  } catch (error) {
    console.error('Fehler bei der Zitationsextraktion:', error);
    return {
      doi,
      citations: [],
      references: [],
      error: error.message
    };
  }
}

/**
 * Ruft Zitationen von Crossref ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchCrossrefCitations(doi) {
  try {
    // Crossref API für Referenzen (was die Publikation zitiert)
    const referencesUrl = `https://api.crossref.org/works/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
      }
    });
    
    if (!referencesResponse.ok) {
      throw new Error(`Crossref API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Referenzen
    const references = [];
    if (referencesData.message && referencesData.message.reference) {
      referencesData.message.reference.forEach(ref => {
        references.push({
          doi: ref.DOI,
          title: ref['article-title'] || ref.title,
          authors: ref.author ? [ref.author] : [],
          year: ref.year,
          journal: ref['journal-title']
        });
      });
    }
    
    // Für Zitationen (was die Publikation zitiert) gibt es keine direkte Crossref-API
    // Wir könnten OpenCitations oder Semantic Scholar verwenden
    
    return {
      doi,
      citations: [], // Crossref bietet keine direkte API für eingehende Zitationen
      references,
      source: 'crossref'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Crossref-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von Semantic Scholar ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchSemanticScholarCitations(doi) {
  try {
    // Semantic Scholar API
    const apiUrl = `https://api.semanticscholar.org/v1/paper/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      throw new Error(`Semantic Scholar API-Fehler: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Extrahiere Zitationen (eingehende Zitationen)
    const citations = data.citations ? data.citations.map(citation => ({
      doi: citation.doi,
      title: citation.title,
      authors: citation.authors ? citation.authors.map(author => author.name) : [],
      year: citation.year,
      journal: citation.venue
    })) : [];
    
    // Extrahiere Referenzen (ausgehende Zitationen)
    const references = data.references ? data.references.map(reference => ({
      doi: reference.doi,
      title: reference.title,
      authors: reference.authors ? reference.authors.map(author => author.name) : [],
      year: reference.year,
      journal: reference.venue
    })) : [];
    
    return {
      doi,
      citations,
      references,
      source: 'semantic_scholar'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Semantic Scholar-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von OpenCitations ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchOpenCitationsCitations(doi) {
  try {
    // OpenCitations API für eingehende Zitationen
    const citationsUrl = `https://opencitations.net/index/api/v1/citations/${encodeURIComponent(doi)}`;
    const citationsResponse = await fetch(citationsUrl);
    
    if (!citationsResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${citationsResponse.status}`);
    }
    
    const citationsData = await citationsResponse.json();
    
    // OpenCitations API für ausgehende Referenzen
    const referencesUrl = `https://opencitations.net/index/api/v1/references/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl);
    
    if (!referencesResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Zitationen
    const citations = citationsData.map(citation => ({
      doi: citation.citing,
      year: citation.creation ? citation.creation.substring(0, 4) : null
    }));
    
    // Extrahiere Referenzen
    const references = referencesData.map(reference => ({
      doi: reference.cited,
      year: reference.creation ? reference.creation.substring(0, 4) : null
    }));
    
    // Anreicherung der Metadaten für Zitationen und Referenzen
    const enrichedCitations = await enrichCitationsMetadata(citations);
    const enrichedReferences = await enrichCitationsMetadata(references);
    
    return {
      doi,
      citations: enrichedCitations,
      references: enrichedReferences,
      source: 'opencitations'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von OpenCitations-Zitationen:', error);
    return {
      doi,
      citations: [],
      references: [],
      source: 'opencitations',
      error: error.message
    };
  }
}

/**
 * Reichert Zitationsmetadaten mit Titel und Autoren an
 * @param {Array} citations - Liste von Zitationen mit DOIs
 * @returns {Promise<Array>} - Angereicherte Zitationen
 */
async function enrichCitationsMetadata(citations) {
  const enrichedCitations = [];
  
  for (const citation of citations) {
    if (!citation.doi) {
      enrichedCitations.push(citation);
      continue;
    }
    
    try {
      // Crossref API für Metadaten
      const url = `https://api.crossref.org/works/${encodeURIComponent(citation.doi)}`;
      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
        }
      });
      
      if (!response.ok) {
        enrichedCitations.push(citation);
        continue;
      }
      
      const data = await response.json();
      const message = data.message;
      
      // Extrahiere Titel
      const title = message.title ? message.title[0] : null;
      
      // Extrahiere Autoren
      const authors = message.author ? message.author.map(author => 
        `${author.given || ''} ${author.family || ''}`.trim()
      ) : [];
      
      // Extrahiere Journal
      const journal = message['container-title'] ? message['container-title'][0] : null;
      
      // Extrahiere Jahr
      const year = message.published ? 
        (message.published['date-parts'] ? message.published['date-parts'][0][0] : null) : 
        citation.year;
      
      enrichedCitations.push({
        ...citation,
        title,
        authors,
        journal,
        year
      });
    } catch (error) {
      console.error(`Fehler beim Anreichern der Metadaten für DOI ${citation.doi}:`, error);
      enrichedCitations.push(citation);
    }
  }
  
  return enrichedCitations;
}

/**
 * Extrahiert Impact-Faktoren und andere Metriken für eine Publikation
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Metriken
 */
export async function extractMetricsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche Altmetric
    const altmetricData = await fetchAltmetricData(doi);
    
    // Versuche Dimensions
    const dimensionsData = await fetchDimensionsData(doi);
    
    // Kombiniere die Daten
    return {
      doi,
      altmetric: altmetricData,
      dimensions: dimensionsData,
      timestamp: Date.now()
    };
  } catch (error) {
    console.error('Fehler bei der Metrikextraktion:', error);
    return {
      doi,
      error: error.message
    };
  }
}

/**
 * Ruft Altmetric-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Altmetric-Daten
 */
async function fetchAltmetricData(doi) {
  try {
    // Altmetric API
    const apiUrl = `https://api.altmetric.com/v1/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      score: data.score,
      readers: {
        mendeley: data.readers ? data.readers.mendeley : 0,
        citeulike: data.readers ? data.readers.citeulike : 0
      },
      mentions: {
        twitter: data.cited_by_tweeters_count || 0,
        facebook: data.cited_by_fbwalls_count || 0,
        blogs: data.cited_by_feeds_count || 0,
        news: data.cited_by_msm_count || 0,
        reddit: data.cited_by_rdts_count || 0,
        wikipedia: data.cited_by_wikipedia_count || 0
      },
      url: data.details_url
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Altmetric-Daten:', error);
    return null;
  }
}

/**
 * Ruft Dimensions-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Dimensions-Daten
 */
async function fetchDimensionsData(doi) {
  try {
    // Dimensions API (öffentliche Endpunkte sind begrenzt)
    // Dies ist ein Beispiel und müsste mit einem tatsächlichen API-Schlüssel implementiert werden
    const apiUrl = `https://metrics-api.dimensions.ai/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      times_cited: data.times_cited || 0,
      recent_citations: data.recent_citations || 0,
      relative_citation_ratio: data.relative_citation_ratio || 0,
      field_citation_ratio: data.field_citation_ratio || 0
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Dimensions-Daten:', error);
    return null;
  }
}/**
 * Hilfsfunktionen zur automatischen Extraktion von Zitationen und Referenzen
 * aus wissenschaftlichen Publikationen
 */

import fetch from 'node-fetch';

/**
 * Extrahiert Zitationen und Referenzen aus einer Publikation mit DOI
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Extrahierte Zitationen und Referenzen
 */
export async function extractCitationsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche zuerst Crossref
    const crossrefData = await fetchCrossrefCitations(doi);
    if (crossrefData && crossrefData.citations && crossrefData.citations.length > 0) {
      return crossrefData;
    }
    
    // Wenn Crossref keine Ergebnisse liefert, versuche Semantic Scholar
    const semanticScholarData = await fetchSemanticScholarCitations(doi);
    if (semanticScholarData && semanticScholarData.citations && semanticScholarData.citations.length > 0) {
      return semanticScholarData;
    }
    
    // Wenn beide keine Ergebnisse liefern, versuche OpenCitations
    const openCitationsData = await fetchOpenCitationsCitations(doi);
    
    return openCitationsData;
  } catch (error) {
    console.error('Fehler bei der Zitationsextraktion:', error);
    return {
      doi,
      citations: [],
      references: [],
      error: error.message
    };
  }
}

/**
 * Ruft Zitationen von Crossref ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchCrossrefCitations(doi) {
  try {
    // Crossref API für Referenzen (was die Publikation zitiert)
    const referencesUrl = `https://api.crossref.org/works/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
      }
    });
    
    if (!referencesResponse.ok) {
      throw new Error(`Crossref API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Referenzen
    const references = [];
    if (referencesData.message && referencesData.message.reference) {
      referencesData.message.reference.forEach(ref => {
        references.push({
          doi: ref.DOI,
          title: ref['article-title'] || ref.title,
          authors: ref.author ? [ref.author] : [],
          year: ref.year,
          journal: ref['journal-title']
        });
      });
    }
    
    // Für Zitationen (was die Publikation zitiert) gibt es keine direkte Crossref-API
    // Wir könnten OpenCitations oder Semantic Scholar verwenden
    
    return {
      doi,
      citations: [], // Crossref bietet keine direkte API für eingehende Zitationen
      references,
      source: 'crossref'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Crossref-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von Semantic Scholar ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchSemanticScholarCitations(doi) {
  try {
    // Semantic Scholar API
    const apiUrl = `https://api.semanticscholar.org/v1/paper/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      throw new Error(`Semantic Scholar API-Fehler: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Extrahiere Zitationen (eingehende Zitationen)
    const citations = data.citations ? data.citations.map(citation => ({
      doi: citation.doi,
      title: citation.title,
      authors: citation.authors ? citation.authors.map(author => author.name) : [],
      year: citation.year,
      journal: citation.venue
    })) : [];
    
    // Extrahiere Referenzen (ausgehende Zitationen)
    const references = data.references ? data.references.map(reference => ({
      doi: reference.doi,
      title: reference.title,
      authors: reference.authors ? reference.authors.map(author => author.name) : [],
      year: reference.year,
      journal: reference.venue
    })) : [];
    
    return {
      doi,
      citations,
      references,
      source: 'semantic_scholar'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Semantic Scholar-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von OpenCitations ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchOpenCitationsCitations(doi) {
  try {
    // OpenCitations API für eingehende Zitationen
    const citationsUrl = `https://opencitations.net/index/api/v1/citations/${encodeURIComponent(doi)}`;
    const citationsResponse = await fetch(citationsUrl);
    
    if (!citationsResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${citationsResponse.status}`);
    }
    
    const citationsData = await citationsResponse.json();
    
    // OpenCitations API für ausgehende Referenzen
    const referencesUrl = `https://opencitations.net/index/api/v1/references/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl);
    
    if (!referencesResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Zitationen
    const citations = citationsData.map(citation => ({
      doi: citation.citing,
      year: citation.creation ? citation.creation.substring(0, 4) : null
    }));
    
    // Extrahiere Referenzen
    const references = referencesData.map(reference => ({
      doi: reference.cited,
      year: reference.creation ? reference.creation.substring(0, 4) : null
    }));
    
    // Anreicherung der Metadaten für Zitationen und Referenzen
    const enrichedCitations = await enrichCitationsMetadata(citations);
    const enrichedReferences = await enrichCitationsMetadata(references);
    
    return {
      doi,
      citations: enrichedCitations,
      references: enrichedReferences,
      source: 'opencitations'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von OpenCitations-Zitationen:', error);
    return {
      doi,
      citations: [],
      references: [],
      source: 'opencitations',
      error: error.message
    };
  }
}

/**
 * Reichert Zitationsmetadaten mit Titel und Autoren an
 * @param {Array} citations - Liste von Zitationen mit DOIs
 * @returns {Promise<Array>} - Angereicherte Zitationen
 */
async function enrichCitationsMetadata(citations) {
  const enrichedCitations = [];
  
  for (const citation of citations) {
    if (!citation.doi) {
      enrichedCitations.push(citation);
      continue;
    }
    
    try {
      // Crossref API für Metadaten
      const url = `https://api.crossref.org/works/${encodeURIComponent(citation.doi)}`;
      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
        }
      });
      
      if (!response.ok) {
        enrichedCitations.push(citation);
        continue;
      }
      
      const data = await response.json();
      const message = data.message;
      
      // Extrahiere Titel
      const title = message.title ? message.title[0] : null;
      
      // Extrahiere Autoren
      const authors = message.author ? message.author.map(author => 
        `${author.given || ''} ${author.family || ''}`.trim()
      ) : [];
      
      // Extrahiere Journal
      const journal = message['container-title'] ? message['container-title'][0] : null;
      
      // Extrahiere Jahr
      const year = message.published ? 
        (message.published['date-parts'] ? message.published['date-parts'][0][0] : null) : 
        citation.year;
      
      enrichedCitations.push({
        ...citation,
        title,
        authors,
        journal,
        year
      });
    } catch (error) {
      console.error(`Fehler beim Anreichern der Metadaten für DOI ${citation.doi}:`, error);
      enrichedCitations.push(citation);
    }
  }
  
  return enrichedCitations;
}

/**
 * Extrahiert Impact-Faktoren und andere Metriken für eine Publikation
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Metriken
 */
export async function extractMetricsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche Altmetric
    const altmetricData = await fetchAltmetricData(doi);
    
    // Versuche Dimensions
    const dimensionsData = await fetchDimensionsData(doi);
    
    // Kombiniere die Daten
    return {
      doi,
      altmetric: altmetricData,
      dimensions: dimensionsData,
      timestamp: Date.now()
    };
  } catch (error) {
    console.error('Fehler bei der Metrikextraktion:', error);
    return {
      doi,
      error: error.message
    };
  }
}

/**
 * Ruft Altmetric-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Altmetric-Daten
 */
async function fetchAltmetricData(doi) {
  try {
    // Altmetric API
    const apiUrl = `https://api.altmetric.com/v1/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      score: data.score,
      readers: {
        mendeley: data.readers ? data.readers.mendeley : 0,
        citeulike: data.readers ? data.readers.citeulike : 0
      },
      mentions: {
        twitter: data.cited_by_tweeters_count || 0,
        facebook: data.cited_by_fbwalls_count || 0,
        blogs: data.cited_by_feeds_count || 0,
        news: data.cited_by_msm_count || 0,
        reddit: data.cited_by_rdts_count || 0,
        wikipedia: data.cited_by_wikipedia_count || 0
      },
      url: data.details_url
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Altmetric-Daten:', error);
    return null;
  }
}

/**
 * Ruft Dimensions-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Dimensions-Daten
 */
async function fetchDimensionsData(doi) {
  try {
    // Dimensions API (öffentliche Endpunkte sind begrenzt)
    // Dies ist ein Beispiel und müsste mit einem tatsächlichen API-Schlüssel implementiert werden
    const apiUrl = `https://metrics-api.dimensions.ai/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      times_cited: data.times_cited || 0,
      recent_citations: data.recent_citations || 0,
      relative_citation_ratio: data.relative_citation_ratio || 0,
      field_citation_ratio: data.field_citation_ratio || 0
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Dimensions-Daten:', error);
    return null;
  }
}/**
 * Hilfsfunktionen zur automatischen Extraktion von Zitationen und Referenzen
 * aus wissenschaftlichen Publikationen
 */

import fetch from 'node-fetch';

/**
 * Extrahiert Zitationen und Referenzen aus einer Publikation mit DOI
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Extrahierte Zitationen und Referenzen
 */
export async function extractCitationsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche zuerst Crossref
    const crossrefData = await fetchCrossrefCitations(doi);
    if (crossrefData && crossrefData.citations && crossrefData.citations.length > 0) {
      return crossrefData;
    }
    
    // Wenn Crossref keine Ergebnisse liefert, versuche Semantic Scholar
    const semanticScholarData = await fetchSemanticScholarCitations(doi);
    if (semanticScholarData && semanticScholarData.citations && semanticScholarData.citations.length > 0) {
      return semanticScholarData;
    }
    
    // Wenn beide keine Ergebnisse liefern, versuche OpenCitations
    const openCitationsData = await fetchOpenCitationsCitations(doi);
    
    return openCitationsData;
  } catch (error) {
    console.error('Fehler bei der Zitationsextraktion:', error);
    return {
      doi,
      citations: [],
      references: [],
      error: error.message
    };
  }
}

/**
 * Ruft Zitationen von Crossref ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchCrossrefCitations(doi) {
  try {
    // Crossref API für Referenzen (was die Publikation zitiert)
    const referencesUrl = `https://api.crossref.org/works/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
      }
    });
    
    if (!referencesResponse.ok) {
      throw new Error(`Crossref API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Referenzen
    const references = [];
    if (referencesData.message && referencesData.message.reference) {
      referencesData.message.reference.forEach(ref => {
        references.push({
          doi: ref.DOI,
          title: ref['article-title'] || ref.title,
          authors: ref.author ? [ref.author] : [],
          year: ref.year,
          journal: ref['journal-title']
        });
      });
    }
    
    // Für Zitationen (was die Publikation zitiert) gibt es keine direkte Crossref-API
    // Wir könnten OpenCitations oder Semantic Scholar verwenden
    
    return {
      doi,
      citations: [], // Crossref bietet keine direkte API für eingehende Zitationen
      references,
      source: 'crossref'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Crossref-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von Semantic Scholar ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchSemanticScholarCitations(doi) {
  try {
    // Semantic Scholar API
    const apiUrl = `https://api.semanticscholar.org/v1/paper/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      throw new Error(`Semantic Scholar API-Fehler: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Extrahiere Zitationen (eingehende Zitationen)
    const citations = data.citations ? data.citations.map(citation => ({
      doi: citation.doi,
      title: citation.title,
      authors: citation.authors ? citation.authors.map(author => author.name) : [],
      year: citation.year,
      journal: citation.venue
    })) : [];
    
    // Extrahiere Referenzen (ausgehende Zitationen)
    const references = data.references ? data.references.map(reference => ({
      doi: reference.doi,
      title: reference.title,
      authors: reference.authors ? reference.authors.map(author => author.name) : [],
      year: reference.year,
      journal: reference.venue
    })) : [];
    
    return {
      doi,
      citations,
      references,
      source: 'semantic_scholar'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Semantic Scholar-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von OpenCitations ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchOpenCitationsCitations(doi) {
  try {
    // OpenCitations API für eingehende Zitationen
    const citationsUrl = `https://opencitations.net/index/api/v1/citations/${encodeURIComponent(doi)}`;
    const citationsResponse = await fetch(citationsUrl);
    
    if (!citationsResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${citationsResponse.status}`);
    }
    
    const citationsData = await citationsResponse.json();
    
    // OpenCitations API für ausgehende Referenzen
    const referencesUrl = `https://opencitations.net/index/api/v1/references/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl);
    
    if (!referencesResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Zitationen
    const citations = citationsData.map(citation => ({
      doi: citation.citing,
      year: citation.creation ? citation.creation.substring(0, 4) : null
    }));
    
    // Extrahiere Referenzen
    const references = referencesData.map(reference => ({
      doi: reference.cited,
      year: reference.creation ? reference.creation.substring(0, 4) : null
    }));
    
    // Anreicherung der Metadaten für Zitationen und Referenzen
    const enrichedCitations = await enrichCitationsMetadata(citations);
    const enrichedReferences = await enrichCitationsMetadata(references);
    
    return {
      doi,
      citations: enrichedCitations,
      references: enrichedReferences,
      source: 'opencitations'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von OpenCitations-Zitationen:', error);
    return {
      doi,
      citations: [],
      references: [],
      source: 'opencitations',
      error: error.message
    };
  }
}

/**
 * Reichert Zitationsmetadaten mit Titel und Autoren an
 * @param {Array} citations - Liste von Zitationen mit DOIs
 * @returns {Promise<Array>} - Angereicherte Zitationen
 */
async function enrichCitationsMetadata(citations) {
  const enrichedCitations = [];
  
  for (const citation of citations) {
    if (!citation.doi) {
      enrichedCitations.push(citation);
      continue;
    }
    
    try {
      // Crossref API für Metadaten
      const url = `https://api.crossref.org/works/${encodeURIComponent(citation.doi)}`;
      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
        }
      });
      
      if (!response.ok) {
        enrichedCitations.push(citation);
        continue;
      }
      
      const data = await response.json();
      const message = data.message;
      
      // Extrahiere Titel
      const title = message.title ? message.title[0] : null;
      
      // Extrahiere Autoren
      const authors = message.author ? message.author.map(author => 
        `${author.given || ''} ${author.family || ''}`.trim()
      ) : [];
      
      // Extrahiere Journal
      const journal = message['container-title'] ? message['container-title'][0] : null;
      
      // Extrahiere Jahr
      const year = message.published ? 
        (message.published['date-parts'] ? message.published['date-parts'][0][0] : null) : 
        citation.year;
      
      enrichedCitations.push({
        ...citation,
        title,
        authors,
        journal,
        year
      });
    } catch (error) {
      console.error(`Fehler beim Anreichern der Metadaten für DOI ${citation.doi}:`, error);
      enrichedCitations.push(citation);
    }
  }
  
  return enrichedCitations;
}

/**
 * Extrahiert Impact-Faktoren und andere Metriken für eine Publikation
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Metriken
 */
export async function extractMetricsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche Altmetric
    const altmetricData = await fetchAltmetricData(doi);
    
    // Versuche Dimensions
    const dimensionsData = await fetchDimensionsData(doi);
    
    // Kombiniere die Daten
    return {
      doi,
      altmetric: altmetricData,
      dimensions: dimensionsData,
      timestamp: Date.now()
    };
  } catch (error) {
    console.error('Fehler bei der Metrikextraktion:', error);
    return {
      doi,
      error: error.message
    };
  }
}

/**
 * Ruft Altmetric-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Altmetric-Daten
 */
async function fetchAltmetricData(doi) {
  try {
    // Altmetric API
    const apiUrl = `https://api.altmetric.com/v1/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      score: data.score,
      readers: {
        mendeley: data.readers ? data.readers.mendeley : 0,
        citeulike: data.readers ? data.readers.citeulike : 0
      },
      mentions: {
        twitter: data.cited_by_tweeters_count || 0,
        facebook: data.cited_by_fbwalls_count || 0,
        blogs: data.cited_by_feeds_count || 0,
        news: data.cited_by_msm_count || 0,
        reddit: data.cited_by_rdts_count || 0,
        wikipedia: data.cited_by_wikipedia_count || 0
      },
      url: data.details_url
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Altmetric-Daten:', error);
    return null;
  }
}

/**
 * Ruft Dimensions-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Dimensions-Daten
 */
async function fetchDimensionsData(doi) {
  try {
    // Dimensions API (öffentliche Endpunkte sind begrenzt)
    // Dies ist ein Beispiel und müsste mit einem tatsächlichen API-Schlüssel implementiert werden
    const apiUrl = `https://metrics-api.dimensions.ai/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      times_cited: data.times_cited || 0,
      recent_citations: data.recent_citations || 0,
      relative_citation_ratio: data.relative_citation_ratio || 0,
      field_citation_ratio: data.field_citation_ratio || 0
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Dimensions-Daten:', error);
    return null;
  }
}/**
 * Hilfsfunktionen zur automatischen Extraktion von Zitationen und Referenzen
 * aus wissenschaftlichen Publikationen
 */

import fetch from 'node-fetch';

/**
 * Extrahiert Zitationen und Referenzen aus einer Publikation mit DOI
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Extrahierte Zitationen und Referenzen
 */
export async function extractCitationsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche zuerst Crossref
    const crossrefData = await fetchCrossrefCitations(doi);
    if (crossrefData && crossrefData.citations && crossrefData.citations.length > 0) {
      return crossrefData;
    }
    
    // Wenn Crossref keine Ergebnisse liefert, versuche Semantic Scholar
    const semanticScholarData = await fetchSemanticScholarCitations(doi);
    if (semanticScholarData && semanticScholarData.citations && semanticScholarData.citations.length > 0) {
      return semanticScholarData;
    }
    
    // Wenn beide keine Ergebnisse liefern, versuche OpenCitations
    const openCitationsData = await fetchOpenCitationsCitations(doi);
    
    return openCitationsData;
  } catch (error) {
    console.error('Fehler bei der Zitationsextraktion:', error);
    return {
      doi,
      citations: [],
      references: [],
      error: error.message
    };
  }
}

/**
 * Ruft Zitationen von Crossref ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchCrossrefCitations(doi) {
  try {
    // Crossref API für Referenzen (was die Publikation zitiert)
    const referencesUrl = `https://api.crossref.org/works/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
      }
    });
    
    if (!referencesResponse.ok) {
      throw new Error(`Crossref API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Referenzen
    const references = [];
    if (referencesData.message && referencesData.message.reference) {
      referencesData.message.reference.forEach(ref => {
        references.push({
          doi: ref.DOI,
          title: ref['article-title'] || ref.title,
          authors: ref.author ? [ref.author] : [],
          year: ref.year,
          journal: ref['journal-title']
        });
      });
    }
    
    // Für Zitationen (was die Publikation zitiert) gibt es keine direkte Crossref-API
    // Wir könnten OpenCitations oder Semantic Scholar verwenden
    
    return {
      doi,
      citations: [], // Crossref bietet keine direkte API für eingehende Zitationen
      references,
      source: 'crossref'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Crossref-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von Semantic Scholar ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchSemanticScholarCitations(doi) {
  try {
    // Semantic Scholar API
    const apiUrl = `https://api.semanticscholar.org/v1/paper/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      throw new Error(`Semantic Scholar API-Fehler: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Extrahiere Zitationen (eingehende Zitationen)
    const citations = data.citations ? data.citations.map(citation => ({
      doi: citation.doi,
      title: citation.title,
      authors: citation.authors ? citation.authors.map(author => author.name) : [],
      year: citation.year,
      journal: citation.venue
    })) : [];
    
    // Extrahiere Referenzen (ausgehende Zitationen)
    const references = data.references ? data.references.map(reference => ({
      doi: reference.doi,
      title: reference.title,
      authors: reference.authors ? reference.authors.map(author => author.name) : [],
      year: reference.year,
      journal: reference.venue
    })) : [];
    
    return {
      doi,
      citations,
      references,
      source: 'semantic_scholar'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Semantic Scholar-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von OpenCitations ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchOpenCitationsCitations(doi) {
  try {
    // OpenCitations API für eingehende Zitationen
    const citationsUrl = `https://opencitations.net/index/api/v1/citations/${encodeURIComponent(doi)}`;
    const citationsResponse = await fetch(citationsUrl);
    
    if (!citationsResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${citationsResponse.status}`);
    }
    
    const citationsData = await citationsResponse.json();
    
    // OpenCitations API für ausgehende Referenzen
    const referencesUrl = `https://opencitations.net/index/api/v1/references/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl);
    
    if (!referencesResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Zitationen
    const citations = citationsData.map(citation => ({
      doi: citation.citing,
      year: citation.creation ? citation.creation.substring(0, 4) : null
    }));
    
    // Extrahiere Referenzen
    const references = referencesData.map(reference => ({
      doi: reference.cited,
      year: reference.creation ? reference.creation.substring(0, 4) : null
    }));
    
    // Anreicherung der Metadaten für Zitationen und Referenzen
    const enrichedCitations = await enrichCitationsMetadata(citations);
    const enrichedReferences = await enrichCitationsMetadata(references);
    
    return {
      doi,
      citations: enrichedCitations,
      references: enrichedReferences,
      source: 'opencitations'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von OpenCitations-Zitationen:', error);
    return {
      doi,
      citations: [],
      references: [],
      source: 'opencitations',
      error: error.message
    };
  }
}

/**
 * Reichert Zitationsmetadaten mit Titel und Autoren an
 * @param {Array} citations - Liste von Zitationen mit DOIs
 * @returns {Promise<Array>} - Angereicherte Zitationen
 */
async function enrichCitationsMetadata(citations) {
  const enrichedCitations = [];
  
  for (const citation of citations) {
    if (!citation.doi) {
      enrichedCitations.push(citation);
      continue;
    }
    
    try {
      // Crossref API für Metadaten
      const url = `https://api.crossref.org/works/${encodeURIComponent(citation.doi)}`;
      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
        }
      });
      
      if (!response.ok) {
        enrichedCitations.push(citation);
        continue;
      }
      
      const data = await response.json();
      const message = data.message;
      
      // Extrahiere Titel
      const title = message.title ? message.title[0] : null;
      
      // Extrahiere Autoren
      const authors = message.author ? message.author.map(author => 
        `${author.given || ''} ${author.family || ''}`.trim()
      ) : [];
      
      // Extrahiere Journal
      const journal = message['container-title'] ? message['container-title'][0] : null;
      
      // Extrahiere Jahr
      const year = message.published ? 
        (message.published['date-parts'] ? message.published['date-parts'][0][0] : null) : 
        citation.year;
      
      enrichedCitations.push({
        ...citation,
        title,
        authors,
        journal,
        year
      });
    } catch (error) {
      console.error(`Fehler beim Anreichern der Metadaten für DOI ${citation.doi}:`, error);
      enrichedCitations.push(citation);
    }
  }
  
  return enrichedCitations;
}

/**
 * Extrahiert Impact-Faktoren und andere Metriken für eine Publikation
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Metriken
 */
export async function extractMetricsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche Altmetric
    const altmetricData = await fetchAltmetricData(doi);
    
    // Versuche Dimensions
    const dimensionsData = await fetchDimensionsData(doi);
    
    // Kombiniere die Daten
    return {
      doi,
      altmetric: altmetricData,
      dimensions: dimensionsData,
      timestamp: Date.now()
    };
  } catch (error) {
    console.error('Fehler bei der Metrikextraktion:', error);
    return {
      doi,
      error: error.message
    };
  }
}

/**
 * Ruft Altmetric-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Altmetric-Daten
 */
async function fetchAltmetricData(doi) {
  try {
    // Altmetric API
    const apiUrl = `https://api.altmetric.com/v1/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      score: data.score,
      readers: {
        mendeley: data.readers ? data.readers.mendeley : 0,
        citeulike: data.readers ? data.readers.citeulike : 0
      },
      mentions: {
        twitter: data.cited_by_tweeters_count || 0,
        facebook: data.cited_by_fbwalls_count || 0,
        blogs: data.cited_by_feeds_count || 0,
        news: data.cited_by_msm_count || 0,
        reddit: data.cited_by_rdts_count || 0,
        wikipedia: data.cited_by_wikipedia_count || 0
      },
      url: data.details_url
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Altmetric-Daten:', error);
    return null;
  }
}

/**
 * Ruft Dimensions-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Dimensions-Daten
 */
async function fetchDimensionsData(doi) {
  try {
    // Dimensions API (öffentliche Endpunkte sind begrenzt)
    // Dies ist ein Beispiel und müsste mit einem tatsächlichen API-Schlüssel implementiert werden
    const apiUrl = `https://metrics-api.dimensions.ai/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      times_cited: data.times_cited || 0,
      recent_citations: data.recent_citations || 0,
      relative_citation_ratio: data.relative_citation_ratio || 0,
      field_citation_ratio: data.field_citation_ratio || 0
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Dimensions-Daten:', error);
    return null;
  }
}/**
 * Hilfsfunktionen zur automatischen Extraktion von Zitationen und Referenzen
 * aus wissenschaftlichen Publikationen
 */

import fetch from 'node-fetch';

/**
 * Extrahiert Zitationen und Referenzen aus einer Publikation mit DOI
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Extrahierte Zitationen und Referenzen
 */
export async function extractCitationsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche zuerst Crossref
    const crossrefData = await fetchCrossrefCitations(doi);
    if (crossrefData && crossrefData.citations && crossrefData.citations.length > 0) {
      return crossrefData;
    }
    
    // Wenn Crossref keine Ergebnisse liefert, versuche Semantic Scholar
    const semanticScholarData = await fetchSemanticScholarCitations(doi);
    if (semanticScholarData && semanticScholarData.citations && semanticScholarData.citations.length > 0) {
      return semanticScholarData;
    }
    
    // Wenn beide keine Ergebnisse liefern, versuche OpenCitations
    const openCitationsData = await fetchOpenCitationsCitations(doi);
    
    return openCitationsData;
  } catch (error) {
    console.error('Fehler bei der Zitationsextraktion:', error);
    return {
      doi,
      citations: [],
      references: [],
      error: error.message
    };
  }
}

/**
 * Ruft Zitationen von Crossref ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchCrossrefCitations(doi) {
  try {
    // Crossref API für Referenzen (was die Publikation zitiert)
    const referencesUrl = `https://api.crossref.org/works/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
      }
    });
    
    if (!referencesResponse.ok) {
      throw new Error(`Crossref API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Referenzen
    const references = [];
    if (referencesData.message && referencesData.message.reference) {
      referencesData.message.reference.forEach(ref => {
        references.push({
          doi: ref.DOI,
          title: ref['article-title'] || ref.title,
          authors: ref.author ? [ref.author] : [],
          year: ref.year,
          journal: ref['journal-title']
        });
      });
    }
    
    // Für Zitationen (was die Publikation zitiert) gibt es keine direkte Crossref-API
    // Wir könnten OpenCitations oder Semantic Scholar verwenden
    
    return {
      doi,
      citations: [], // Crossref bietet keine direkte API für eingehende Zitationen
      references,
      source: 'crossref'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Crossref-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von Semantic Scholar ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchSemanticScholarCitations(doi) {
  try {
    // Semantic Scholar API
    const apiUrl = `https://api.semanticscholar.org/v1/paper/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      throw new Error(`Semantic Scholar API-Fehler: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Extrahiere Zitationen (eingehende Zitationen)
    const citations = data.citations ? data.citations.map(citation => ({
      doi: citation.doi,
      title: citation.title,
      authors: citation.authors ? citation.authors.map(author => author.name) : [],
      year: citation.year,
      journal: citation.venue
    })) : [];
    
    // Extrahiere Referenzen (ausgehende Zitationen)
    const references = data.references ? data.references.map(reference => ({
      doi: reference.doi,
      title: reference.title,
      authors: reference.authors ? reference.authors.map(author => author.name) : [],
      year: reference.year,
      journal: reference.venue
    })) : [];
    
    return {
      doi,
      citations,
      references,
      source: 'semantic_scholar'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Semantic Scholar-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von OpenCitations ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchOpenCitationsCitations(doi) {
  try {
    // OpenCitations API für eingehende Zitationen
    const citationsUrl = `https://opencitations.net/index/api/v1/citations/${encodeURIComponent(doi)}`;
    const citationsResponse = await fetch(citationsUrl);
    
    if (!citationsResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${citationsResponse.status}`);
    }
    
    const citationsData = await citationsResponse.json();
    
    // OpenCitations API für ausgehende Referenzen
    const referencesUrl = `https://opencitations.net/index/api/v1/references/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl);
    
    if (!referencesResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Zitationen
    const citations = citationsData.map(citation => ({
      doi: citation.citing,
      year: citation.creation ? citation.creation.substring(0, 4) : null
    }));
    
    // Extrahiere Referenzen
    const references = referencesData.map(reference => ({
      doi: reference.cited,
      year: reference.creation ? reference.creation.substring(0, 4) : null
    }));
    
    // Anreicherung der Metadaten für Zitationen und Referenzen
    const enrichedCitations = await enrichCitationsMetadata(citations);
    const enrichedReferences = await enrichCitationsMetadata(references);
    
    return {
      doi,
      citations: enrichedCitations,
      references: enrichedReferences,
      source: 'opencitations'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von OpenCitations-Zitationen:', error);
    return {
      doi,
      citations: [],
      references: [],
      source: 'opencitations',
      error: error.message
    };
  }
}

/**
 * Reichert Zitationsmetadaten mit Titel und Autoren an
 * @param {Array} citations - Liste von Zitationen mit DOIs
 * @returns {Promise<Array>} - Angereicherte Zitationen
 */
async function enrichCitationsMetadata(citations) {
  const enrichedCitations = [];
  
  for (const citation of citations) {
    if (!citation.doi) {
      enrichedCitations.push(citation);
      continue;
    }
    
    try {
      // Crossref API für Metadaten
      const url = `https://api.crossref.org/works/${encodeURIComponent(citation.doi)}`;
      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
        }
      });
      
      if (!response.ok) {
        enrichedCitations.push(citation);
        continue;
      }
      
      const data = await response.json();
      const message = data.message;
      
      // Extrahiere Titel
      const title = message.title ? message.title[0] : null;
      
      // Extrahiere Autoren
      const authors = message.author ? message.author.map(author => 
        `${author.given || ''} ${author.family || ''}`.trim()
      ) : [];
      
      // Extrahiere Journal
      const journal = message['container-title'] ? message['container-title'][0] : null;
      
      // Extrahiere Jahr
      const year = message.published ? 
        (message.published['date-parts'] ? message.published['date-parts'][0][0] : null) : 
        citation.year;
      
      enrichedCitations.push({
        ...citation,
        title,
        authors,
        journal,
        year
      });
    } catch (error) {
      console.error(`Fehler beim Anreichern der Metadaten für DOI ${citation.doi}:`, error);
      enrichedCitations.push(citation);
    }
  }
  
  return enrichedCitations;
}

/**
 * Extrahiert Impact-Faktoren und andere Metriken für eine Publikation
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Metriken
 */
export async function extractMetricsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche Altmetric
    const altmetricData = await fetchAltmetricData(doi);
    
    // Versuche Dimensions
    const dimensionsData = await fetchDimensionsData(doi);
    
    // Kombiniere die Daten
    return {
      doi,
      altmetric: altmetricData,
      dimensions: dimensionsData,
      timestamp: Date.now()
    };
  } catch (error) {
    console.error('Fehler bei der Metrikextraktion:', error);
    return {
      doi,
      error: error.message
    };
  }
}

/**
 * Ruft Altmetric-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Altmetric-Daten
 */
async function fetchAltmetricData(doi) {
  try {
    // Altmetric API
    const apiUrl = `https://api.altmetric.com/v1/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      score: data.score,
      readers: {
        mendeley: data.readers ? data.readers.mendeley : 0,
        citeulike: data.readers ? data.readers.citeulike : 0
      },
      mentions: {
        twitter: data.cited_by_tweeters_count || 0,
        facebook: data.cited_by_fbwalls_count || 0,
        blogs: data.cited_by_feeds_count || 0,
        news: data.cited_by_msm_count || 0,
        reddit: data.cited_by_rdts_count || 0,
        wikipedia: data.cited_by_wikipedia_count || 0
      },
      url: data.details_url
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Altmetric-Daten:', error);
    return null;
  }
}

/**
 * Ruft Dimensions-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Dimensions-Daten
 */
async function fetchDimensionsData(doi) {
  try {
    // Dimensions API (öffentliche Endpunkte sind begrenzt)
    // Dies ist ein Beispiel und müsste mit einem tatsächlichen API-Schlüssel implementiert werden
    const apiUrl = `https://metrics-api.dimensions.ai/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      times_cited: data.times_cited || 0,
      recent_citations: data.recent_citations || 0,
      relative_citation_ratio: data.relative_citation_ratio || 0,
      field_citation_ratio: data.field_citation_ratio || 0
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Dimensions-Daten:', error);
    return null;
  }
} * aus wissenschaftlichen Publikationen
 */

import fetch from 'node-fetch';

/**
 * Extrahiert Zitationen und Referenzen aus einer Publikation mit DOI
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Extrahierte Zitationen und Referenzen
 */
export async function extractCitationsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche zuerst Crossref
    const crossrefData = await fetchCrossrefCitations(doi);
    if (crossrefData && crossrefData.citations && crossrefData.citations.length > 0) {
      return crossrefData;
    }
    
    // Wenn Crossref keine Ergebnisse liefert, versuche Semantic Scholar
    const semanticScholarData = await fetchSemanticScholarCitations(doi);
    if (semanticScholarData && semanticScholarData.citations && semanticScholarData.citations.length > 0) {
      return semanticScholarData;
    }
    
    // Wenn beide keine Ergebnisse liefern, versuche OpenCitations
    const openCitationsData = await fetchOpenCitationsCitations(doi);
    
    return openCitationsData;
  } catch (error) {
    console.error('Fehler bei der Zitationsextraktion:', error);
    return {
      doi,
      citations: [],
      references: [],
      error: error.message
    };
  }
}

/**
 * Ruft Zitationen von Crossref ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchCrossrefCitations(doi) {
  try {
    // Crossref API für Referenzen (was die Publikation zitiert)
    const referencesUrl = `https://api.crossref.org/works/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
      }
    });
    
    if (!referencesResponse.ok) {
      throw new Error(`Crossref API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Referenzen
    const references = [];
    if (referencesData.message && referencesData.message.reference) {
      referencesData.message.reference.forEach(ref => {
        references.push({
          doi: ref.DOI,
          title: ref['article-title'] || ref.title,
          authors: ref.author ? [ref.author] : [],
          year: ref.year,
          journal: ref['journal-title']
        });
      });
    }
    
    // Für Zitationen (was die Publikation zitiert) gibt es keine direkte Crossref-API
    // Wir könnten OpenCitations oder Semantic Scholar verwenden
    
    return {
      doi,
      citations: [], // Crossref bietet keine direkte API für eingehende Zitationen
      references,
      source: 'crossref'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Crossref-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von Semantic Scholar ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchSemanticScholarCitations(doi) {
  try {
    // Semantic Scholar API
    const apiUrl = `https://api.semanticscholar.org/v1/paper/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      throw new Error(`Semantic Scholar API-Fehler: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Extrahiere Zitationen (eingehende Zitationen)
    const citations = data.citations ? data.citations.map(citation => ({
      doi: citation.doi,
      title: citation.title,
      authors: citation.authors ? citation.authors.map(author => author.name) : [],
      year: citation.year,
      journal: citation.venue
    })) : [];
    
    // Extrahiere Referenzen (ausgehende Zitationen)
    const references = data.references ? data.references.map(reference => ({
      doi: reference.doi,
      title: reference.title,
      authors: reference.authors ? reference.authors.map(author => author.name) : [],
      year: reference.year,
      journal: reference.venue
    })) : [];
    
    return {
      doi,
      citations,
      references,
      source: 'semantic_scholar'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Semantic Scholar-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von OpenCitations ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchOpenCitationsCitations(doi) {
  try {
    // OpenCitations API für eingehende Zitationen
    const citationsUrl = `https://opencitations.net/index/api/v1/citations/${encodeURIComponent(doi)}`;
    const citationsResponse = await fetch(citationsUrl);
    
    if (!citationsResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${citationsResponse.status}`);
    }
    
    const citationsData = await citationsResponse.json();
    
    // OpenCitations API für ausgehende Referenzen
    const referencesUrl = `https://opencitations.net/index/api/v1/references/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl);
    
    if (!referencesResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Zitationen
    const citations = citationsData.map(citation => ({
      doi: citation.citing,
      year: citation.creation ? citation.creation.substring(0, 4) : null
    }));
    
    // Extrahiere Referenzen
    const references = referencesData.map(reference => ({
      doi: reference.cited,
      year: reference.creation ? reference.creation.substring(0, 4) : null
    }));
    
    // Anreicherung der Metadaten für Zitationen und Referenzen
    const enrichedCitations = await enrichCitationsMetadata(citations);
    const enrichedReferences = await enrichCitationsMetadata(references);
    
    return {
      doi,
      citations: enrichedCitations,
      references: enrichedReferences,
      source: 'opencitations'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von OpenCitations-Zitationen:', error);
    return {
      doi,
      citations: [],
      references: [],
      source: 'opencitations',
      error: error.message
    };
  }
}

/**
 * Reichert Zitationsmetadaten mit Titel und Autoren an
 * @param {Array} citations - Liste von Zitationen mit DOIs
 * @returns {Promise<Array>} - Angereicherte Zitationen
 */
async function enrichCitationsMetadata(citations) {
  const enrichedCitations = [];
  
  for (const citation of citations) {
    if (!citation.doi) {
      enrichedCitations.push(citation);
      continue;
    }
    
    try {
      // Crossref API für Metadaten
      const url = `https://api.crossref.org/works/${encodeURIComponent(citation.doi)}`;
      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
        }
      });
      
      if (!response.ok) {
        enrichedCitations.push(citation);
        continue;
      }
      
      const data = await response.json();
      const message = data.message;
      
      // Extrahiere Titel
      const title = message.title ? message.title[0] : null;
      
      // Extrahiere Autoren
      const authors = message.author ? message.author.map(author => 
        `${author.given || ''} ${author.family || ''}`.trim()
      ) : [];
      
      // Extrahiere Journal
      const journal = message['container-title'] ? message['container-title'][0] : null;
      
      // Extrahiere Jahr
      const year = message.published ? 
        (message.published['date-parts'] ? message.published['date-parts'][0][0] : null) : 
        citation.year;
      
      enrichedCitations.push({
        ...citation,
        title,
        authors,
        journal,
        year
      });
    } catch (error) {
      console.error(`Fehler beim Anreichern der Metadaten für DOI ${citation.doi}:`, error);
      enrichedCitations.push(citation);
    }
  }
  
  return enrichedCitations;
}

/**
 * Extrahiert Impact-Faktoren und andere Metriken für eine Publikation
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Metriken
 */
export async function extractMetricsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche Altmetric
    const altmetricData = await fetchAltmetricData(doi);
    
    // Versuche Dimensions
    const dimensionsData = await fetchDimensionsData(doi);
    
    // Kombiniere die Daten
    return {
      doi,
      altmetric: altmetricData,
      dimensions: dimensionsData,
      timestamp: Date.now()
    };
  } catch (error) {
    console.error('Fehler bei der Metrikextraktion:', error);
    return {
      doi,
      error: error.message
    };
  }
}

/**
 * Ruft Altmetric-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Altmetric-Daten
 */
async function fetchAltmetricData(doi) {
  try {
    // Altmetric API
    const apiUrl = `https://api.altmetric.com/v1/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      score: data.score,
      readers: {
        mendeley: data.readers ? data.readers.mendeley : 0,
        citeulike: data.readers ? data.readers.citeulike : 0
      },
      mentions: {
        twitter: data.cited_by_tweeters_count || 0,
        facebook: data.cited_by_fbwalls_count || 0,
        blogs: data.cited_by_feeds_count || 0,
        news: data.cited_by_msm_count || 0,
        reddit: data.cited_by_rdts_count || 0,
        wikipedia: data.cited_by_wikipedia_count || 0
      },
      url: data.details_url
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Altmetric-Daten:', error);
    return null;
  }
}

/**
 * Ruft Dimensions-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Dimensions-Daten
 */
async function fetchDimensionsData(doi) {
  try {
    // Dimensions API (öffentliche Endpunkte sind begrenzt)
    // Dies ist ein Beispiel und müsste mit einem tatsächlichen API-Schlüssel implementiert werden
    const apiUrl = `https://metrics-api.dimensions.ai/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      times_cited: data.times_cited || 0,
      recent_citations: data.recent_citations || 0,
      relative_citation_ratio: data.relative_citation_ratio || 0,
      field_citation_ratio: data.field_citation_ratio || 0
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Dimensions-Daten:', error);
    return null;
  }
} * Hilfsfunktionen zur automatischen Extraktion von Zitationen und Referenzen
 * aus wissenschaftlichen Publikationen
 */

import fetch from 'node-fetch';

/**
 * Extrahiert Zitationen und Referenzen aus einer Publikation mit DOI
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Extrahierte Zitationen und Referenzen
 */
export async function extractCitationsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche zuerst Crossref
    const crossrefData = await fetchCrossrefCitations(doi);
    if (crossrefData && crossrefData.citations && crossrefData.citations.length > 0) {
      return crossrefData;
    }
    
    // Wenn Crossref keine Ergebnisse liefert, versuche Semantic Scholar
    const semanticScholarData = await fetchSemanticScholarCitations(doi);
    if (semanticScholarData && semanticScholarData.citations && semanticScholarData.citations.length > 0) {
      return semanticScholarData;
    }
    
    // Wenn beide keine Ergebnisse liefern, versuche OpenCitations
    const openCitationsData = await fetchOpenCitationsCitations(doi);
    
    return openCitationsData;
  } catch (error) {
    console.error('Fehler bei der Zitationsextraktion:', error);
    return {
      doi,
      citations: [],
      references: [],
      error: error.message
    };
  }
}

/**
 * Ruft Zitationen von Crossref ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchCrossrefCitations(doi) {
  try {
    // Crossref API für Referenzen (was die Publikation zitiert)
    const referencesUrl = `https://api.crossref.org/works/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
      }
    });
    
    if (!referencesResponse.ok) {
      throw new Error(`Crossref API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Referenzen
    const references = [];
    if (referencesData.message && referencesData.message.reference) {
      referencesData.message.reference.forEach(ref => {
        references.push({
          doi: ref.DOI,
          title: ref['article-title'] || ref.title,
          authors: ref.author ? [ref.author] : [],
          year: ref.year,
          journal: ref['journal-title']
        });
      });
    }
    
    // Für Zitationen (was die Publikation zitiert) gibt es keine direkte Crossref-API
    // Wir könnten OpenCitations oder Semantic Scholar verwenden
    
    return {
      doi,
      citations: [], // Crossref bietet keine direkte API für eingehende Zitationen
      references,
      source: 'crossref'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Crossref-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von Semantic Scholar ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchSemanticScholarCitations(doi) {
  try {
    // Semantic Scholar API
    const apiUrl = `https://api.semanticscholar.org/v1/paper/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      throw new Error(`Semantic Scholar API-Fehler: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Extrahiere Zitationen (eingehende Zitationen)
    const citations = data.citations ? data.citations.map(citation => ({
      doi: citation.doi,
      title: citation.title,
      authors: citation.authors ? citation.authors.map(author => author.name) : [],
      year: citation.year,
      journal: citation.venue
    })) : [];
    
    // Extrahiere Referenzen (ausgehende Zitationen)
    const references = data.references ? data.references.map(reference => ({
      doi: reference.doi,
      title: reference.title,
      authors: reference.authors ? reference.authors.map(author => author.name) : [],
      year: reference.year,
      journal: reference.venue
    })) : [];
    
    return {
      doi,
      citations,
      references,
      source: 'semantic_scholar'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Semantic Scholar-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von OpenCitations ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchOpenCitationsCitations(doi) {
  try {
    // OpenCitations API für eingehende Zitationen
    const citationsUrl = `https://opencitations.net/index/api/v1/citations/${encodeURIComponent(doi)}`;
    const citationsResponse = await fetch(citationsUrl);
    
    if (!citationsResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${citationsResponse.status}`);
    }
    
    const citationsData = await citationsResponse.json();
    
    // OpenCitations API für ausgehende Referenzen
    const referencesUrl = `https://opencitations.net/index/api/v1/references/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl);
    
    if (!referencesResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Zitationen
    const citations = citationsData.map(citation => ({
      doi: citation.citing,
      year: citation.creation ? citation.creation.substring(0, 4) : null
    }));
    
    // Extrahiere Referenzen
    const references = referencesData.map(reference => ({
      doi: reference.cited,
      year: reference.creation ? reference.creation.substring(0, 4) : null
    }));
    
    // Anreicherung der Metadaten für Zitationen und Referenzen
    const enrichedCitations = await enrichCitationsMetadata(citations);
    const enrichedReferences = await enrichCitationsMetadata(references);
    
    return {
      doi,
      citations: enrichedCitations,
      references: enrichedReferences,
      source: 'opencitations'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von OpenCitations-Zitationen:', error);
    return {
      doi,
      citations: [],
      references: [],
      source: 'opencitations',
      error: error.message
    };
  }
}

/**
 * Reichert Zitationsmetadaten mit Titel und Autoren an
 * @param {Array} citations - Liste von Zitationen mit DOIs
 * @returns {Promise<Array>} - Angereicherte Zitationen
 */
async function enrichCitationsMetadata(citations) {
  const enrichedCitations = [];
  
  for (const citation of citations) {
    if (!citation.doi) {
      enrichedCitations.push(citation);
      continue;
    }
    
    try {
      // Crossref API für Metadaten
      const url = `https://api.crossref.org/works/${encodeURIComponent(citation.doi)}`;
      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
        }
      });
      
      if (!response.ok) {
        enrichedCitations.push(citation);
        continue;
      }
      
      const data = await response.json();
      const message = data.message;
      
      // Extrahiere Titel
      const title = message.title ? message.title[0] : null;
      
      // Extrahiere Autoren
      const authors = message.author ? message.author.map(author => 
        `${author.given || ''} ${author.family || ''}`.trim()
      ) : [];
      
      // Extrahiere Journal
      const journal = message['container-title'] ? message['container-title'][0] : null;
      
      // Extrahiere Jahr
      const year = message.published ? 
        (message.published['date-parts'] ? message.published['date-parts'][0][0] : null) : 
        citation.year;
      
      enrichedCitations.push({
        ...citation,
        title,
        authors,
        journal,
        year
      });
    } catch (error) {
      console.error(`Fehler beim Anreichern der Metadaten für DOI ${citation.doi}:`, error);
      enrichedCitations.push(citation);
    }
  }
  
  return enrichedCitations;
}

/**
 * Extrahiert Impact-Faktoren und andere Metriken für eine Publikation
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Metriken
 */
export async function extractMetricsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche Altmetric
    const altmetricData = await fetchAltmetricData(doi);
    
    // Versuche Dimensions
    const dimensionsData = await fetchDimensionsData(doi);
    
    // Kombiniere die Daten
    return {
      doi,
      altmetric: altmetricData,
      dimensions: dimensionsData,
      timestamp: Date.now()
    };
  } catch (error) {
    console.error('Fehler bei der Metrikextraktion:', error);
    return {
      doi,
      error: error.message
    };
  }
}

/**
 * Ruft Altmetric-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Altmetric-Daten
 */
async function fetchAltmetricData(doi) {
  try {
    // Altmetric API
    const apiUrl = `https://api.altmetric.com/v1/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      score: data.score,
      readers: {
        mendeley: data.readers ? data.readers.mendeley : 0,
        citeulike: data.readers ? data.readers.citeulike : 0
      },
      mentions: {
        twitter: data.cited_by_tweeters_count || 0,
        facebook: data.cited_by_fbwalls_count || 0,
        blogs: data.cited_by_feeds_count || 0,
        news: data.cited_by_msm_count || 0,
        reddit: data.cited_by_rdts_count || 0,
        wikipedia: data.cited_by_wikipedia_count || 0
      },
      url: data.details_url
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Altmetric-Daten:', error);
    return null;
  }
}

/**
 * Ruft Dimensions-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Dimensions-Daten
 */
async function fetchDimensionsData(doi) {
  try {
    // Dimensions API (öffentliche Endpunkte sind begrenzt)
    // Dies ist ein Beispiel und müsste mit einem tatsächlichen API-Schlüssel implementiert werden
    const apiUrl = `https://metrics-api.dimensions.ai/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      times_cited: data.times_cited || 0,
      recent_citations: data.recent_citations || 0,
      relative_citation_ratio: data.relative_citation_ratio || 0,
      field_citation_ratio: data.field_citation_ratio || 0
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Dimensions-Daten:', error);
    return null;
  }
}  }
} */

import fetch from 'node-fetch';

/**
 * Extrahiert Zitationen und Referenzen aus einer Publikation mit DOI
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Extrahierte Zitationen und Referenzen
 */
export async function extractCitationsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche zuerst Crossref
    const crossrefData = await fetchCrossrefCitations(doi);
    if (crossrefData && crossrefData.citations && crossrefData.citations.length > 0) {
      return crossrefData;
    }
    
    // Wenn Crossref keine Ergebnisse liefert, versuche Semantic Scholar
    const semanticScholarData = await fetchSemanticScholarCitations(doi);
    if (semanticScholarData && semanticScholarData.citations && semanticScholarData.citations.length > 0) {
      return semanticScholarData;
    }
    
    // Wenn beide keine Ergebnisse liefern, versuche OpenCitations
    const openCitationsData = await fetchOpenCitationsCitations(doi);
    
    return openCitationsData;
  } catch (error) {
    console.error('Fehler bei der Zitationsextraktion:', error);
    return {
      doi,
      citations: [],
      references: [],
      error: error.message
    };
  }
}

/**
 * Ruft Zitationen von Crossref ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchCrossrefCitations(doi) {
  try {
    // Crossref API für Referenzen (was die Publikation zitiert)
    const referencesUrl = `https://api.crossref.org/works/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
      }
    });
    
    if (!referencesResponse.ok) {
      throw new Error(`Crossref API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Referenzen
    const references = [];
    if (referencesData.message && referencesData.message.reference) {
      referencesData.message.reference.forEach(ref => {
        references.push({
          doi: ref.DOI,
          title: ref['article-title'] || ref.title,
          authors: ref.author ? [ref.author] : [],
          year: ref.year,
          journal: ref['journal-title']
        });
      });
    }
    
    // Für Zitationen (was die Publikation zitiert) gibt es keine direkte Crossref-API
    // Wir könnten OpenCitations oder Semantic Scholar verwenden
    
    return {
      doi,
      citations: [], // Crossref bietet keine direkte API für eingehende Zitationen
      references,
      source: 'crossref'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Crossref-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von Semantic Scholar ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchSemanticScholarCitations(doi) {
  try {
    // Semantic Scholar API
    const apiUrl = `https://api.semanticscholar.org/v1/paper/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      throw new Error(`Semantic Scholar API-Fehler: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Extrahiere Zitationen (eingehende Zitationen)
    const citations = data.citations ? data.citations.map(citation => ({
      doi: citation.doi,
      title: citation.title,
      authors: citation.authors ? citation.authors.map(author => author.name) : [],
      year: citation.year,
      journal: citation.venue
    })) : [];
    
    // Extrahiere Referenzen (ausgehende Zitationen)
    const references = data.references ? data.references.map(reference => ({
      doi: reference.doi,
      title: reference.title,
      authors: reference.authors ? reference.authors.map(author => author.name) : [],
      year: reference.year,
      journal: reference.venue
    })) : [];
    
    return {
      doi,
      citations,
      references,
      source: 'semantic_scholar'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Semantic Scholar-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von OpenCitations ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchOpenCitationsCitations(doi) {
  try {
    // OpenCitations API für eingehende Zitationen
    const citationsUrl = `https://opencitations.net/index/api/v1/citations/${encodeURIComponent(doi)}`;
    const citationsResponse = await fetch(citationsUrl);
    
    if (!citationsResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${citationsResponse.status}`);
    }
    
    const citationsData = await citationsResponse.json();
    
    // OpenCitations API für ausgehende Referenzen
    const referencesUrl = `https://opencitations.net/index/api/v1/references/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl);
    
    if (!referencesResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Zitationen
    const citations = citationsData.map(citation => ({
      doi: citation.citing,
      year: citation.creation ? citation.creation.substring(0, 4) : null
    }));
    
    // Extrahiere Referenzen
    const references = referencesData.map(reference => ({
      doi: reference.cited,
      year: reference.creation ? reference.creation.substring(0, 4) : null
    }));
    
    // Anreicherung der Metadaten für Zitationen und Referenzen
    const enrichedCitations = await enrichCitationsMetadata(citations);
    const enrichedReferences = await enrichCitationsMetadata(references);
    
    return {
      doi,
      citations: enrichedCitations,
      references: enrichedReferences,
      source: 'opencitations'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von OpenCitations-Zitationen:', error);
    return {
      doi,
      citations: [],
      references: [],
      source: 'opencitations',
      error: error.message
    };
  }
}

/**
 * Reichert Zitationsmetadaten mit Titel und Autoren an
 * @param {Array} citations - Liste von Zitationen mit DOIs
 * @returns {Promise<Array>} - Angereicherte Zitationen
 */
async function enrichCitationsMetadata(citations) {
  const enrichedCitations = [];
  
  for (const citation of citations) {
    if (!citation.doi) {
      enrichedCitations.push(citation);
      continue;
    }
    
    try {
      // Crossref API für Metadaten
      const url = `https://api.crossref.org/works/${encodeURIComponent(citation.doi)}`;
      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
        }
      });
      
      if (!response.ok) {
        enrichedCitations.push(citation);
        continue;
      }
      
      const data = await response.json();
      const message = data.message;
      
      // Extrahiere Titel
      const title = message.title ? message.title[0] : null;
      
      // Extrahiere Autoren
      const authors = message.author ? message.author.map(author => 
        `${author.given || ''} ${author.family || ''}`.trim()
      ) : [];
      
      // Extrahiere Journal
      const journal = message['container-title'] ? message['container-title'][0] : null;
      
      // Extrahiere Jahr
      const year = message.published ? 
        (message.published['date-parts'] ? message.published['date-parts'][0][0] : null) : 
        citation.year;
      
      enrichedCitations.push({
        ...citation,
        title,
        authors,
        journal,
        year
      });
    } catch (error) {
      console.error(`Fehler beim Anreichern der Metadaten für DOI ${citation.doi}:`, error);
      enrichedCitations.push(citation);
    }
  }
  
  return enrichedCitations;
}

/**
 * Extrahiert Impact-Faktoren und andere Metriken für eine Publikation
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Metriken
 */
export async function extractMetricsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche Altmetric
    const altmetricData = await fetchAltmetricData(doi);
    
    // Versuche Dimensions
    const dimensionsData = await fetchDimensionsData(doi);
    
    // Kombiniere die Daten
    return {
      doi,
      altmetric: altmetricData,
      dimensions: dimensionsData,
      timestamp: Date.now()
    };
  } catch (error) {
    console.error('Fehler bei der Metrikextraktion:', error);
    return {
      doi,
      error: error.message
    };
  }
}

/**
 * Ruft Altmetric-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Altmetric-Daten
 */
async function fetchAltmetricData(doi) {
  try {
    // Altmetric API
    const apiUrl = `https://api.altmetric.com/v1/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      score: data.score,
      readers: {
        mendeley: data.readers ? data.readers.mendeley : 0,
        citeulike: data.readers ? data.readers.citeulike : 0
      },
      mentions: {
        twitter: data.cited_by_tweeters_count || 0,
        facebook: data.cited_by_fbwalls_count || 0,
        blogs: data.cited_by_feeds_count || 0,
        news: data.cited_by_msm_count || 0,
        reddit: data.cited_by_rdts_count || 0,
        wikipedia: data.cited_by_wikipedia_count || 0
      },
      url: data.details_url
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Altmetric-Daten:', error);
    return null;
  }
}

/**
 * Ruft Dimensions-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Dimensions-Daten
 */
async function fetchDimensionsData(doi) {
  try {
    // Dimensions API (öffentliche Endpunkte sind begrenzt)
    // Dies ist ein Beispiel und müsste mit einem tatsächlichen API-Schlüssel implementiert werden
    const apiUrl = `https://metrics-api.dimensions.ai/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      times_cited: data.times_cited || 0,
      recent_citations: data.recent_citations || 0,
      relative_citation_ratio: data.relative_citation_ratio || 0,
      field_citation_ratio: data.field_citation_ratio || 0
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Dimensions-Daten:', error);
    return null;
  }
}  }
}}  }
} */

import fetch from 'node-fetch';

/**
 * Extrahiert Zitationen und Referenzen aus einer Publikation mit DOI
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Extrahierte Zitationen und Referenzen
 */
export async function extractCitationsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche zuerst Crossref
    const crossrefData = await fetchCrossrefCitations(doi);
    if (crossrefData && crossrefData.citations && crossrefData.citations.length > 0) {
      return crossrefData;
    }
    
    // Wenn Crossref keine Ergebnisse liefert, versuche Semantic Scholar
    const semanticScholarData = await fetchSemanticScholarCitations(doi);
    if (semanticScholarData && semanticScholarData.citations && semanticScholarData.citations.length > 0) {
      return semanticScholarData;
    }
    
    // Wenn beide keine Ergebnisse liefern, versuche OpenCitations
    const openCitationsData = await fetchOpenCitationsCitations(doi);
    
    return openCitationsData;
  } catch (error) {
    console.error('Fehler bei der Zitationsextraktion:', error);
    return {
      doi,
      citations: [],
      references: [],
      error: error.message
    };
  }
}

/**
 * Ruft Zitationen von Crossref ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchCrossrefCitations(doi) {
  try {
    // Crossref API für Referenzen (was die Publikation zitiert)
    const referencesUrl = `https://api.crossref.org/works/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
      }
    });
    
    if (!referencesResponse.ok) {
      throw new Error(`Crossref API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Referenzen
    const references = [];
    if (referencesData.message && referencesData.message.reference) {
      referencesData.message.reference.forEach(ref => {
        references.push({
          doi: ref.DOI,
          title: ref['article-title'] || ref.title,
          authors: ref.author ? [ref.author] : [],
          year: ref.year,
          journal: ref['journal-title']
        });
      });
    }
    
    // Für Zitationen (was die Publikation zitiert) gibt es keine direkte Crossref-API
    // Wir könnten OpenCitations oder Semantic Scholar verwenden
    
    return {
      doi,
      citations: [], // Crossref bietet keine direkte API für eingehende Zitationen
      references,
      source: 'crossref'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Crossref-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von Semantic Scholar ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchSemanticScholarCitations(doi) {
  try {
    // Semantic Scholar API
    const apiUrl = `https://api.semanticscholar.org/v1/paper/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      throw new Error(`Semantic Scholar API-Fehler: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Extrahiere Zitationen (eingehende Zitationen)
    const citations = data.citations ? data.citations.map(citation => ({
      doi: citation.doi,
      title: citation.title,
      authors: citation.authors ? citation.authors.map(author => author.name) : [],
      year: citation.year,
      journal: citation.venue
    })) : [];
    
    // Extrahiere Referenzen (ausgehende Zitationen)
    const references = data.references ? data.references.map(reference => ({
      doi: reference.doi,
      title: reference.title,
      authors: reference.authors ? reference.authors.map(author => author.name) : [],
      year: reference.year,
      journal: reference.venue
    })) : [];
    
    return {
      doi,
      citations,
      references,
      source: 'semantic_scholar'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Semantic Scholar-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von OpenCitations ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchOpenCitationsCitations(doi) {
  try {
    // OpenCitations API für eingehende Zitationen
    const citationsUrl = `https://opencitations.net/index/api/v1/citations/${encodeURIComponent(doi)}`;
    const citationsResponse = await fetch(citationsUrl);
    
    if (!citationsResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${citationsResponse.status}`);
    }
    
    const citationsData = await citationsResponse.json();
    
    // OpenCitations API für ausgehende Referenzen
    const referencesUrl = `https://opencitations.net/index/api/v1/references/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl);
    
    if (!referencesResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Zitationen
    const citations = citationsData.map(citation => ({
      doi: citation.citing,
      year: citation.creation ? citation.creation.substring(0, 4) : null
    }));
    
    // Extrahiere Referenzen
    const references = referencesData.map(reference => ({
      doi: reference.cited,
      year: reference.creation ? reference.creation.substring(0, 4) : null
    }));
    
    // Anreicherung der Metadaten für Zitationen und Referenzen
    const enrichedCitations = await enrichCitationsMetadata(citations);
    const enrichedReferences = await enrichCitationsMetadata(references);
    
    return {
      doi,
      citations: enrichedCitations,
      references: enrichedReferences,
      source: 'opencitations'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von OpenCitations-Zitationen:', error);
    return {
      doi,
      citations: [],
      references: [],
      source: 'opencitations',
      error: error.message
    };
  }
}

/**
 * Reichert Zitationsmetadaten mit Titel und Autoren an
 * @param {Array} citations - Liste von Zitationen mit DOIs
 * @returns {Promise<Array>} - Angereicherte Zitationen
 */
async function enrichCitationsMetadata(citations) {
  const enrichedCitations = [];
  
  for (const citation of citations) {
    if (!citation.doi) {
      enrichedCitations.push(citation);
      continue;
    }
    
    try {
      // Crossref API für Metadaten
      const url = `https://api.crossref.org/works/${encodeURIComponent(citation.doi)}`;
      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
        }
      });
      
      if (!response.ok) {
        enrichedCitations.push(citation);
        continue;
      }
      
      const data = await response.json();
      const message = data.message;
      
      // Extrahiere Titel
      const title = message.title ? message.title[0] : null;
      
      // Extrahiere Autoren
      const authors = message.author ? message.author.map(author => 
        `${author.given || ''} ${author.family || ''}`.trim()
      ) : [];
      
      // Extrahiere Journal
      const journal = message['container-title'] ? message['container-title'][0] : null;
      
      // Extrahiere Jahr
      const year = message.published ? 
        (message.published['date-parts'] ? message.published['date-parts'][0][0] : null) : 
        citation.year;
      
      enrichedCitations.push({
        ...citation,
        title,
        authors,
        journal,
        year
      });
    } catch (error) {
      console.error(`Fehler beim Anreichern der Metadaten für DOI ${citation.doi}:`, error);
      enrichedCitations.push(citation);
    }
  }
  
  return enrichedCitations;
}

/**
 * Extrahiert Impact-Faktoren und andere Metriken für eine Publikation
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Metriken
 */
export async function extractMetricsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche Altmetric
    const altmetricData = await fetchAltmetricData(doi);
    
    // Versuche Dimensions
    const dimensionsData = await fetchDimensionsData(doi);
    
    // Kombiniere die Daten
    return {
      doi,
      altmetric: altmetricData,
      dimensions: dimensionsData,
      timestamp: Date.now()
    };
  } catch (error) {
    console.error('Fehler bei der Metrikextraktion:', error);
    return {
      doi,
      error: error.message
    };
  }
}

/**
 * Ruft Altmetric-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Altmetric-Daten
 */
async function fetchAltmetricData(doi) {
  try {
    // Altmetric API
    const apiUrl = `https://api.altmetric.com/v1/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      score: data.score,
      readers: {
        mendeley: data.readers ? data.readers.mendeley : 0,
        citeulike: data.readers ? data.readers.citeulike : 0
      },
      mentions: {
        twitter: data.cited_by_tweeters_count || 0,
        facebook: data.cited_by_fbwalls_count || 0,
        blogs: data.cited_by_feeds_count || 0,
        news: data.cited_by_msm_count || 0,
        reddit: data.cited_by_rdts_count || 0,
        wikipedia: data.cited_by_wikipedia_count || 0
      },
      url: data.details_url
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Altmetric-Daten:', error);
    return null;
  }
}

/**
 * Ruft Dimensions-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Dimensions-Daten
 */
async function fetchDimensionsData(doi) {
  try {
    // Dimensions API (öffentliche Endpunkte sind begrenzt)
    // Dies ist ein Beispiel und müsste mit einem tatsächlichen API-Schlüssel implementiert werden
    const apiUrl = `https://metrics-api.dimensions.ai/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      times_cited: data.times_cited || 0,
      recent_citations: data.recent_citations || 0,
      relative_citation_ratio: data.relative_citation_ratio || 0,
      field_citation_ratio: data.field_citation_ratio || 0
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Dimensions-Daten:', error);
    return null;
  }
}  }
}}}} */

import fetch from 'node-fetch';

/**
 * Extrahiert Zitationen und Referenzen aus einer Publikation mit DOI
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Extrahierte Zitationen und Referenzen
 */
export async function extractCitationsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche zuerst Crossref
    const crossrefData = await fetchCrossrefCitations(doi);
    if (crossrefData && crossrefData.citations && crossrefData.citations.length > 0) {
      return crossrefData;
    }
    
    // Wenn Crossref keine Ergebnisse liefert, versuche Semantic Scholar
    const semanticScholarData = await fetchSemanticScholarCitations(doi);
    if (semanticScholarData && semanticScholarData.citations && semanticScholarData.citations.length > 0) {
      return semanticScholarData;
    }
    
    // Wenn beide keine Ergebnisse liefern, versuche OpenCitations
    const openCitationsData = await fetchOpenCitationsCitations(doi);
    
    return openCitationsData;
  } catch (error) {
    console.error('Fehler bei der Zitationsextraktion:', error);
    return {
      doi,
      citations: [],
      references: [],
      error: error.message
    };
  }
}

/**
 * Ruft Zitationen von Crossref ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchCrossrefCitations(doi) {
  try {
    // Crossref API für Referenzen (was die Publikation zitiert)
    const referencesUrl = `https://api.crossref.org/works/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
      }
    });
    
    if (!referencesResponse.ok) {
      throw new Error(`Crossref API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Referenzen
    const references = [];
    if (referencesData.message && referencesData.message.reference) {
      referencesData.message.reference.forEach(ref => {
        references.push({
          doi: ref.DOI,
          title: ref['article-title'] || ref.title,
          authors: ref.author ? [ref.author] : [],
          year: ref.year,
          journal: ref['journal-title']
        });
      });
    }
    
    // Für Zitationen (was die Publikation zitiert) gibt es keine direkte Crossref-API
    // Wir könnten OpenCitations oder Semantic Scholar verwenden
    
    return {
      doi,
      citations: [], // Crossref bietet keine direkte API für eingehende Zitationen
      references,
      source: 'crossref'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Crossref-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von Semantic Scholar ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchSemanticScholarCitations(doi) {
  try {
    // Semantic Scholar API
    const apiUrl = `https://api.semanticscholar.org/v1/paper/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      throw new Error(`Semantic Scholar API-Fehler: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Extrahiere Zitationen (eingehende Zitationen)
    const citations = data.citations ? data.citations.map(citation => ({
      doi: citation.doi,
      title: citation.title,
      authors: citation.authors ? citation.authors.map(author => author.name) : [],
      year: citation.year,
      journal: citation.venue
    })) : [];
    
    // Extrahiere Referenzen (ausgehende Zitationen)
    const references = data.references ? data.references.map(reference => ({
      doi: reference.doi,
      title: reference.title,
      authors: reference.authors ? reference.authors.map(author => author.name) : [],
      year: reference.year,
      journal: reference.venue
    })) : [];
    
    return {
      doi,
      citations,
      references,
      source: 'semantic_scholar'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Semantic Scholar-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von OpenCitations ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchOpenCitationsCitations(doi) {
  try {
    // OpenCitations API für eingehende Zitationen
    const citationsUrl = `https://opencitations.net/index/api/v1/citations/${encodeURIComponent(doi)}`;
    const citationsResponse = await fetch(citationsUrl);
    
    if (!citationsResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${citationsResponse.status}`);
    }
    
    const citationsData = await citationsResponse.json();
    
    // OpenCitations API für ausgehende Referenzen
    const referencesUrl = `https://opencitations.net/index/api/v1/references/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl);
    
    if (!referencesResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Zitationen
    const citations = citationsData.map(citation => ({
      doi: citation.citing,
      year: citation.creation ? citation.creation.substring(0, 4) : null
    }));
    
    // Extrahiere Referenzen
    const references = referencesData.map(reference => ({
      doi: reference.cited,
      year: reference.creation ? reference.creation.substring(0, 4) : null
    }));
    
    // Anreicherung der Metadaten für Zitationen und Referenzen
    const enrichedCitations = await enrichCitationsMetadata(citations);
    const enrichedReferences = await enrichCitationsMetadata(references);
    
    return {
      doi,
      citations: enrichedCitations,
      references: enrichedReferences,
      source: 'opencitations'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von OpenCitations-Zitationen:', error);
    return {
      doi,
      citations: [],
      references: [],
      source: 'opencitations',
      error: error.message
    };
  }
}

/**
 * Reichert Zitationsmetadaten mit Titel und Autoren an
 * @param {Array} citations - Liste von Zitationen mit DOIs
 * @returns {Promise<Array>} - Angereicherte Zitationen
 */
async function enrichCitationsMetadata(citations) {
  const enrichedCitations = [];
  
  for (const citation of citations) {
    if (!citation.doi) {
      enrichedCitations.push(citation);
      continue;
    }
    
    try {
      // Crossref API für Metadaten
      const url = `https://api.crossref.org/works/${encodeURIComponent(citation.doi)}`;
      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
        }
      });
      
      if (!response.ok) {
        enrichedCitations.push(citation);
        continue;
      }
      
      const data = await response.json();
      const message = data.message;
      
      // Extrahiere Titel
      const title = message.title ? message.title[0] : null;
      
      // Extrahiere Autoren
      const authors = message.author ? message.author.map(author => 
        `${author.given || ''} ${author.family || ''}`.trim()
      ) : [];
      
      // Extrahiere Journal
      const journal = message['container-title'] ? message['container-title'][0] : null;
      
      // Extrahiere Jahr
      const year = message.published ? 
        (message.published['date-parts'] ? message.published['date-parts'][0][0] : null) : 
        citation.year;
      
      enrichedCitations.push({
        ...citation,
        title,
        authors,
        journal,
        year
      });
    } catch (error) {
      console.error(`Fehler beim Anreichern der Metadaten für DOI ${citation.doi}:`, error);
      enrichedCitations.push(citation);
    }
  }
  
  return enrichedCitations;
}

/**
 * Extrahiert Impact-Faktoren und andere Metriken für eine Publikation
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Metriken
 */
export async function extractMetricsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche Altmetric
    const altmetricData = await fetchAltmetricData(doi);
    
    // Versuche Dimensions
    const dimensionsData = await fetchDimensionsData(doi);
    
    // Kombiniere die Daten
    return {
      doi,
      altmetric: altmetricData,
      dimensions: dimensionsData,
      timestamp: Date.now()
    };
  } catch (error) {
    console.error('Fehler bei der Metrikextraktion:', error);
    return {
      doi,
      error: error.message
    };
  }
}

/**
 * Ruft Altmetric-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Altmetric-Daten
 */
async function fetchAltmetricData(doi) {
  try {
    // Altmetric API
    const apiUrl = `https://api.altmetric.com/v1/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      score: data.score,
      readers: {
        mendeley: data.readers ? data.readers.mendeley : 0,
        citeulike: data.readers ? data.readers.citeulike : 0
      },
      mentions: {
        twitter: data.cited_by_tweeters_count || 0,
        facebook: data.cited_by_fbwalls_count || 0,
        blogs: data.cited_by_feeds_count || 0,
        news: data.cited_by_msm_count || 0,
        reddit: data.cited_by_rdts_count || 0,
        wikipedia: data.cited_by_wikipedia_count || 0
      },
      url: data.details_url
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Altmetric-Daten:', error);
    return null;
  }
}

/**
 * Ruft Dimensions-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Dimensions-Daten
 */
async function fetchDimensionsData(doi) {
  try {
    // Dimensions API (öffentliche Endpunkte sind begrenzt)
    // Dies ist ein Beispiel und müsste mit einem tatsächlichen API-Schlüssel implementiert werden
    const apiUrl = `https://metrics-api.dimensions.ai/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      times_cited: data.times_cited || 0,
      recent_citations: data.recent_citations || 0,
      relative_citation_ratio: data.relative_citation_ratio || 0,
      field_citation_ratio: data.field_citation_ratio || 0
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Dimensions-Daten:', error);
    return null;
  }
}  }
}}  }
}}} */

import fetch from 'node-fetch';

/**
 * Extrahiert Zitationen und Referenzen aus einer Publikation mit DOI
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Extrahierte Zitationen und Referenzen
 */
export async function extractCitationsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche zuerst Crossref
    const crossrefData = await fetchCrossrefCitations(doi);
    if (crossrefData && crossrefData.citations && crossrefData.citations.length > 0) {
      return crossrefData;
    }
    
    // Wenn Crossref keine Ergebnisse liefert, versuche Semantic Scholar
    const semanticScholarData = await fetchSemanticScholarCitations(doi);
    if (semanticScholarData && semanticScholarData.citations && semanticScholarData.citations.length > 0) {
      return semanticScholarData;
    }
    
    // Wenn beide keine Ergebnisse liefern, versuche OpenCitations
    const openCitationsData = await fetchOpenCitationsCitations(doi);
    
    return openCitationsData;
  } catch (error) {
    console.error('Fehler bei der Zitationsextraktion:', error);
    return {
      doi,
      citations: [],
      references: [],
      error: error.message
    };
  }
}

/**
 * Ruft Zitationen von Crossref ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchCrossrefCitations(doi) {
  try {
    // Crossref API für Referenzen (was die Publikation zitiert)
    const referencesUrl = `https://api.crossref.org/works/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
      }
    });
    
    if (!referencesResponse.ok) {
      throw new Error(`Crossref API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Referenzen
    const references = [];
    if (referencesData.message && referencesData.message.reference) {
      referencesData.message.reference.forEach(ref => {
        references.push({
          doi: ref.DOI,
          title: ref['article-title'] || ref.title,
          authors: ref.author ? [ref.author] : [],
          year: ref.year,
          journal: ref['journal-title']
        });
      });
    }
    
    // Für Zitationen (was die Publikation zitiert) gibt es keine direkte Crossref-API
    // Wir könnten OpenCitations oder Semantic Scholar verwenden
    
    return {
      doi,
      citations: [], // Crossref bietet keine direkte API für eingehende Zitationen
      references,
      source: 'crossref'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Crossref-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von Semantic Scholar ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchSemanticScholarCitations(doi) {
  try {
    // Semantic Scholar API
    const apiUrl = `https://api.semanticscholar.org/v1/paper/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      throw new Error(`Semantic Scholar API-Fehler: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Extrahiere Zitationen (eingehende Zitationen)
    const citations = data.citations ? data.citations.map(citation => ({
      doi: citation.doi,
      title: citation.title,
      authors: citation.authors ? citation.authors.map(author => author.name) : [],
      year: citation.year,
      journal: citation.venue
    })) : [];
    
    // Extrahiere Referenzen (ausgehende Zitationen)
    const references = data.references ? data.references.map(reference => ({
      doi: reference.doi,
      title: reference.title,
      authors: reference.authors ? reference.authors.map(author => author.name) : [],
      year: reference.year,
      journal: reference.venue
    })) : [];
    
    return {
      doi,
      citations,
      references,
      source: 'semantic_scholar'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Semantic Scholar-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von OpenCitations ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchOpenCitationsCitations(doi) {
  try {
    // OpenCitations API für eingehende Zitationen
    const citationsUrl = `https://opencitations.net/index/api/v1/citations/${encodeURIComponent(doi)}`;
    const citationsResponse = await fetch(citationsUrl);
    
    if (!citationsResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${citationsResponse.status}`);
    }
    
    const citationsData = await citationsResponse.json();
    
    // OpenCitations API für ausgehende Referenzen
    const referencesUrl = `https://opencitations.net/index/api/v1/references/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl);
    
    if (!referencesResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Zitationen
    const citations = citationsData.map(citation => ({
      doi: citation.citing,
      year: citation.creation ? citation.creation.substring(0, 4) : null
    }));
    
    // Extrahiere Referenzen
    const references = referencesData.map(reference => ({
      doi: reference.cited,
      year: reference.creation ? reference.creation.substring(0, 4) : null
    }));
    
    // Anreicherung der Metadaten für Zitationen und Referenzen
    const enrichedCitations = await enrichCitationsMetadata(citations);
    const enrichedReferences = await enrichCitationsMetadata(references);
    
    return {
      doi,
      citations: enrichedCitations,
      references: enrichedReferences,
      source: 'opencitations'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von OpenCitations-Zitationen:', error);
    return {
      doi,
      citations: [],
      references: [],
      source: 'opencitations',
      error: error.message
    };
  }
}

/**
 * Reichert Zitationsmetadaten mit Titel und Autoren an
 * @param {Array} citations - Liste von Zitationen mit DOIs
 * @returns {Promise<Array>} - Angereicherte Zitationen
 */
async function enrichCitationsMetadata(citations) {
  const enrichedCitations = [];
  
  for (const citation of citations) {
    if (!citation.doi) {
      enrichedCitations.push(citation);
      continue;
    }
    
    try {
      // Crossref API für Metadaten
      const url = `https://api.crossref.org/works/${encodeURIComponent(citation.doi)}`;
      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
        }
      });
      
      if (!response.ok) {
        enrichedCitations.push(citation);
        continue;
      }
      
      const data = await response.json();
      const message = data.message;
      
      // Extrahiere Titel
      const title = message.title ? message.title[0] : null;
      
      // Extrahiere Autoren
      const authors = message.author ? message.author.map(author => 
        `${author.given || ''} ${author.family || ''}`.trim()
      ) : [];
      
      // Extrahiere Journal
      const journal = message['container-title'] ? message['container-title'][0] : null;
      
      // Extrahiere Jahr
      const year = message.published ? 
        (message.published['date-parts'] ? message.published['date-parts'][0][0] : null) : 
        citation.year;
      
      enrichedCitations.push({
        ...citation,
        title,
        authors,
        journal,
        year
      });
    } catch (error) {
      console.error(`Fehler beim Anreichern der Metadaten für DOI ${citation.doi}:`, error);
      enrichedCitations.push(citation);
    }
  }
  
  return enrichedCitations;
}

/**
 * Extrahiert Impact-Faktoren und andere Metriken für eine Publikation
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Metriken
 */
export async function extractMetricsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche Altmetric
    const altmetricData = await fetchAltmetricData(doi);
    
    // Versuche Dimensions
    const dimensionsData = await fetchDimensionsData(doi);
    
    // Kombiniere die Daten
    return {
      doi,
      altmetric: altmetricData,
      dimensions: dimensionsData,
      timestamp: Date.now()
    };
  } catch (error) {
    console.error('Fehler bei der Metrikextraktion:', error);
    return {
      doi,
      error: error.message
    };
  }
}

/**
 * Ruft Altmetric-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Altmetric-Daten
 */
async function fetchAltmetricData(doi) {
  try {
    // Altmetric API
    const apiUrl = `https://api.altmetric.com/v1/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      score: data.score,
      readers: {
        mendeley: data.readers ? data.readers.mendeley : 0,
        citeulike: data.readers ? data.readers.citeulike : 0
      },
      mentions: {
        twitter: data.cited_by_tweeters_count || 0,
        facebook: data.cited_by_fbwalls_count || 0,
        blogs: data.cited_by_feeds_count || 0,
        news: data.cited_by_msm_count || 0,
        reddit: data.cited_by_rdts_count || 0,
        wikipedia: data.cited_by_wikipedia_count || 0
      },
      url: data.details_url
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Altmetric-Daten:', error);
    return null;
  }
}

/**
 * Ruft Dimensions-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Dimensions-Daten
 */
async function fetchDimensionsData(doi) {
  try {
    // Dimensions API (öffentliche Endpunkte sind begrenzt)
    // Dies ist ein Beispiel und müsste mit einem tatsächlichen API-Schlüssel implementiert werden
    const apiUrl = `https://metrics-api.dimensions.ai/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      times_cited: data.times_cited || 0,
      recent_citations: data.recent_citations || 0,
      relative_citation_ratio: data.relative_citation_ratio || 0,
      field_citation_ratio: data.field_citation_ratio || 0
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Dimensions-Daten:', error);
    return null;
  }
}  }
}}}} */

import fetch from 'node-fetch';

/**
 * Extrahiert Zitationen und Referenzen aus einer Publikation mit DOI
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Extrahierte Zitationen und Referenzen
 */
export async function extractCitationsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche zuerst Crossref
    const crossrefData = await fetchCrossrefCitations(doi);
    if (crossrefData && crossrefData.citations && crossrefData.citations.length > 0) {
      return crossrefData;
    }
    
    // Wenn Crossref keine Ergebnisse liefert, versuche Semantic Scholar
    const semanticScholarData = await fetchSemanticScholarCitations(doi);
    if (semanticScholarData && semanticScholarData.citations && semanticScholarData.citations.length > 0) {
      return semanticScholarData;
    }
    
    // Wenn beide keine Ergebnisse liefern, versuche OpenCitations
    const openCitationsData = await fetchOpenCitationsCitations(doi);
    
    return openCitationsData;
  } catch (error) {
    console.error('Fehler bei der Zitationsextraktion:', error);
    return {
      doi,
      citations: [],
      references: [],
      error: error.message
    };
  }
}

/**
 * Ruft Zitationen von Crossref ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchCrossrefCitations(doi) {
  try {
    // Crossref API für Referenzen (was die Publikation zitiert)
    const referencesUrl = `https://api.crossref.org/works/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
      }
    });
    
    if (!referencesResponse.ok) {
      throw new Error(`Crossref API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Referenzen
    const references = [];
    if (referencesData.message && referencesData.message.reference) {
      referencesData.message.reference.forEach(ref => {
        references.push({
          doi: ref.DOI,
          title: ref['article-title'] || ref.title,
          authors: ref.author ? [ref.author] : [],
          year: ref.year,
          journal: ref['journal-title']
        });
      });
    }
    
    // Für Zitationen (was die Publikation zitiert) gibt es keine direkte Crossref-API
    // Wir könnten OpenCitations oder Semantic Scholar verwenden
    
    return {
      doi,
      citations: [], // Crossref bietet keine direkte API für eingehende Zitationen
      references,
      source: 'crossref'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Crossref-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von Semantic Scholar ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchSemanticScholarCitations(doi) {
  try {
    // Semantic Scholar API
    const apiUrl = `https://api.semanticscholar.org/v1/paper/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      throw new Error(`Semantic Scholar API-Fehler: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Extrahiere Zitationen (eingehende Zitationen)
    const citations = data.citations ? data.citations.map(citation => ({
      doi: citation.doi,
      title: citation.title,
      authors: citation.authors ? citation.authors.map(author => author.name) : [],
      year: citation.year,
      journal: citation.venue
    })) : [];
    
    // Extrahiere Referenzen (ausgehende Zitationen)
    const references = data.references ? data.references.map(reference => ({
      doi: reference.doi,
      title: reference.title,
      authors: reference.authors ? reference.authors.map(author => author.name) : [],
      year: reference.year,
      journal: reference.venue
    })) : [];
    
    return {
      doi,
      citations,
      references,
      source: 'semantic_scholar'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Semantic Scholar-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von OpenCitations ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchOpenCitationsCitations(doi) {
  try {
    // OpenCitations API für eingehende Zitationen
    const citationsUrl = `https://opencitations.net/index/api/v1/citations/${encodeURIComponent(doi)}`;
    const citationsResponse = await fetch(citationsUrl);
    
    if (!citationsResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${citationsResponse.status}`);
    }
    
    const citationsData = await citationsResponse.json();
    
    // OpenCitations API für ausgehende Referenzen
    const referencesUrl = `https://opencitations.net/index/api/v1/references/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl);
    
    if (!referencesResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Zitationen
    const citations = citationsData.map(citation => ({
      doi: citation.citing,
      year: citation.creation ? citation.creation.substring(0, 4) : null
    }));
    
    // Extrahiere Referenzen
    const references = referencesData.map(reference => ({
      doi: reference.cited,
      year: reference.creation ? reference.creation.substring(0, 4) : null
    }));
    
    // Anreicherung der Metadaten für Zitationen und Referenzen
    const enrichedCitations = await enrichCitationsMetadata(citations);
    const enrichedReferences = await enrichCitationsMetadata(references);
    
    return {
      doi,
      citations: enrichedCitations,
      references: enrichedReferences,
      source: 'opencitations'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von OpenCitations-Zitationen:', error);
    return {
      doi,
      citations: [],
      references: [],
      source: 'opencitations',
      error: error.message
    };
  }
}

/**
 * Reichert Zitationsmetadaten mit Titel und Autoren an
 * @param {Array} citations - Liste von Zitationen mit DOIs
 * @returns {Promise<Array>} - Angereicherte Zitationen
 */
async function enrichCitationsMetadata(citations) {
  const enrichedCitations = [];
  
  for (const citation of citations) {
    if (!citation.doi) {
      enrichedCitations.push(citation);
      continue;
    }
    
    try {
      // Crossref API für Metadaten
      const url = `https://api.crossref.org/works/${encodeURIComponent(citation.doi)}`;
      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
        }
      });
      
      if (!response.ok) {
        enrichedCitations.push(citation);
        continue;
      }
      
      const data = await response.json();
      const message = data.message;
      
      // Extrahiere Titel
      const title = message.title ? message.title[0] : null;
      
      // Extrahiere Autoren
      const authors = message.author ? message.author.map(author => 
        `${author.given || ''} ${author.family || ''}`.trim()
      ) : [];
      
      // Extrahiere Journal
      const journal = message['container-title'] ? message['container-title'][0] : null;
      
      // Extrahiere Jahr
      const year = message.published ? 
        (message.published['date-parts'] ? message.published['date-parts'][0][0] : null) : 
        citation.year;
      
      enrichedCitations.push({
        ...citation,
        title,
        authors,
        journal,
        year
      });
    } catch (error) {
      console.error(`Fehler beim Anreichern der Metadaten für DOI ${citation.doi}:`, error);
      enrichedCitations.push(citation);
    }
  }
  
  return enrichedCitations;
}

/**
 * Extrahiert Impact-Faktoren und andere Metriken für eine Publikation
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Metriken
 */
export async function extractMetricsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche Altmetric
    const altmetricData = await fetchAltmetricData(doi);
    
    // Versuche Dimensions
    const dimensionsData = await fetchDimensionsData(doi);
    
    // Kombiniere die Daten
    return {
      doi,
      altmetric: altmetricData,
      dimensions: dimensionsData,
      timestamp: Date.now()
    };
  } catch (error) {
    console.error('Fehler bei der Metrikextraktion:', error);
    return {
      doi,
      error: error.message
    };
  }
}

/**
 * Ruft Altmetric-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Altmetric-Daten
 */
async function fetchAltmetricData(doi) {
  try {
    // Altmetric API
    const apiUrl = `https://api.altmetric.com/v1/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      score: data.score,
      readers: {
        mendeley: data.readers ? data.readers.mendeley : 0,
        citeulike: data.readers ? data.readers.citeulike : 0
      },
      mentions: {
        twitter: data.cited_by_tweeters_count || 0,
        facebook: data.cited_by_fbwalls_count || 0,
        blogs: data.cited_by_feeds_count || 0,
        news: data.cited_by_msm_count || 0,
        reddit: data.cited_by_rdts_count || 0,
        wikipedia: data.cited_by_wikipedia_count || 0
      },
      url: data.details_url
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Altmetric-Daten:', error);
    return null;
  }
}

/**
 * Ruft Dimensions-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Dimensions-Daten
 */
async function fetchDimensionsData(doi) {
  try {
    // Dimensions API (öffentliche Endpunkte sind begrenzt)
    // Dies ist ein Beispiel und müsste mit einem tatsächlichen API-Schlüssel implementiert werden
    const apiUrl = `https://metrics-api.dimensions.ai/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      times_cited: data.times_cited || 0,
      recent_citations: data.recent_citations || 0,
      relative_citation_ratio: data.relative_citation_ratio || 0,
      field_citation_ratio: data.field_citation_ratio || 0
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Dimensions-Daten:', error);
    return null;
  }
}  }
}}}}}} */

import fetch from 'node-fetch';

/**
 * Extrahiert Zitationen und Referenzen aus einer Publikation mit DOI
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Extrahierte Zitationen und Referenzen
 */
export async function extractCitationsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche zuerst Crossref
    const crossrefData = await fetchCrossrefCitations(doi);
    if (crossrefData && crossrefData.citations && crossrefData.citations.length > 0) {
      return crossrefData;
    }
    
    // Wenn Crossref keine Ergebnisse liefert, versuche Semantic Scholar
    const semanticScholarData = await fetchSemanticScholarCitations(doi);
    if (semanticScholarData && semanticScholarData.citations && semanticScholarData.citations.length > 0) {
      return semanticScholarData;
    }
    
    // Wenn beide keine Ergebnisse liefern, versuche OpenCitations
    const openCitationsData = await fetchOpenCitationsCitations(doi);
    
    return openCitationsData;
  } catch (error) {
    console.error('Fehler bei der Zitationsextraktion:', error);
    return {
      doi,
      citations: [],
      references: [],
      error: error.message
    };
  }
}

/**
 * Ruft Zitationen von Crossref ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchCrossrefCitations(doi) {
  try {
    // Crossref API für Referenzen (was die Publikation zitiert)
    const referencesUrl = `https://api.crossref.org/works/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
      }
    });
    
    if (!referencesResponse.ok) {
      throw new Error(`Crossref API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Referenzen
    const references = [];
    if (referencesData.message && referencesData.message.reference) {
      referencesData.message.reference.forEach(ref => {
        references.push({
          doi: ref.DOI,
          title: ref['article-title'] || ref.title,
          authors: ref.author ? [ref.author] : [],
          year: ref.year,
          journal: ref['journal-title']
        });
      });
    }
    
    // Für Zitationen (was die Publikation zitiert) gibt es keine direkte Crossref-API
    // Wir könnten OpenCitations oder Semantic Scholar verwenden
    
    return {
      doi,
      citations: [], // Crossref bietet keine direkte API für eingehende Zitationen
      references,
      source: 'crossref'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Crossref-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von Semantic Scholar ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchSemanticScholarCitations(doi) {
  try {
    // Semantic Scholar API
    const apiUrl = `https://api.semanticscholar.org/v1/paper/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      throw new Error(`Semantic Scholar API-Fehler: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Extrahiere Zitationen (eingehende Zitationen)
    const citations = data.citations ? data.citations.map(citation => ({
      doi: citation.doi,
      title: citation.title,
      authors: citation.authors ? citation.authors.map(author => author.name) : [],
      year: citation.year,
      journal: citation.venue
    })) : [];
    
    // Extrahiere Referenzen (ausgehende Zitationen)
    const references = data.references ? data.references.map(reference => ({
      doi: reference.doi,
      title: reference.title,
      authors: reference.authors ? reference.authors.map(author => author.name) : [],
      year: reference.year,
      journal: reference.venue
    })) : [];
    
    return {
      doi,
      citations,
      references,
      source: 'semantic_scholar'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Semantic Scholar-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von OpenCitations ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchOpenCitationsCitations(doi) {
  try {
    // OpenCitations API für eingehende Zitationen
    const citationsUrl = `https://opencitations.net/index/api/v1/citations/${encodeURIComponent(doi)}`;
    const citationsResponse = await fetch(citationsUrl);
    
    if (!citationsResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${citationsResponse.status}`);
    }
    
    const citationsData = await citationsResponse.json();
    
    // OpenCitations API für ausgehende Referenzen
    const referencesUrl = `https://opencitations.net/index/api/v1/references/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl);
    
    if (!referencesResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Zitationen
    const citations = citationsData.map(citation => ({
      doi: citation.citing,
      year: citation.creation ? citation.creation.substring(0, 4) : null
    }));
    
    // Extrahiere Referenzen
    const references = referencesData.map(reference => ({
      doi: reference.cited,
      year: reference.creation ? reference.creation.substring(0, 4) : null
    }));
    
    // Anreicherung der Metadaten für Zitationen und Referenzen
    const enrichedCitations = await enrichCitationsMetadata(citations);
    const enrichedReferences = await enrichCitationsMetadata(references);
    
    return {
      doi,
      citations: enrichedCitations,
      references: enrichedReferences,
      source: 'opencitations'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von OpenCitations-Zitationen:', error);
    return {
      doi,
      citations: [],
      references: [],
      source: 'opencitations',
      error: error.message
    };
  }
}

/**
 * Reichert Zitationsmetadaten mit Titel und Autoren an
 * @param {Array} citations - Liste von Zitationen mit DOIs
 * @returns {Promise<Array>} - Angereicherte Zitationen
 */
async function enrichCitationsMetadata(citations) {
  const enrichedCitations = [];
  
  for (const citation of citations) {
    if (!citation.doi) {
      enrichedCitations.push(citation);
      continue;
    }
    
    try {
      // Crossref API für Metadaten
      const url = `https://api.crossref.org/works/${encodeURIComponent(citation.doi)}`;
      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
        }
      });
      
      if (!response.ok) {
        enrichedCitations.push(citation);
        continue;
      }
      
      const data = await response.json();
      const message = data.message;
      
      // Extrahiere Titel
      const title = message.title ? message.title[0] : null;
      
      // Extrahiere Autoren
      const authors = message.author ? message.author.map(author => 
        `${author.given || ''} ${author.family || ''}`.trim()
      ) : [];
      
      // Extrahiere Journal
      const journal = message['container-title'] ? message['container-title'][0] : null;
      
      // Extrahiere Jahr
      const year = message.published ? 
        (message.published['date-parts'] ? message.published['date-parts'][0][0] : null) : 
        citation.year;
      
      enrichedCitations.push({
        ...citation,
        title,
        authors,
        journal,
        year
      });
    } catch (error) {
      console.error(`Fehler beim Anreichern der Metadaten für DOI ${citation.doi}:`, error);
      enrichedCitations.push(citation);
    }
  }
  
  return enrichedCitations;
}

/**
 * Extrahiert Impact-Faktoren und andere Metriken für eine Publikation
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Metriken
 */
export async function extractMetricsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche Altmetric
    const altmetricData = await fetchAltmetricData(doi);
    
    // Versuche Dimensions
    const dimensionsData = await fetchDimensionsData(doi);
    
    // Kombiniere die Daten
    return {
      doi,
      altmetric: altmetricData,
      dimensions: dimensionsData,
      timestamp: Date.now()
    };
  } catch (error) {
    console.error('Fehler bei der Metrikextraktion:', error);
    return {
      doi,
      error: error.message
    };
  }
}

/**
 * Ruft Altmetric-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Altmetric-Daten
 */
async function fetchAltmetricData(doi) {
  try {
    // Altmetric API
    const apiUrl = `https://api.altmetric.com/v1/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      score: data.score,
      readers: {
        mendeley: data.readers ? data.readers.mendeley : 0,
        citeulike: data.readers ? data.readers.citeulike : 0
      },
      mentions: {
        twitter: data.cited_by_tweeters_count || 0,
        facebook: data.cited_by_fbwalls_count || 0,
        blogs: data.cited_by_feeds_count || 0,
        news: data.cited_by_msm_count || 0,
        reddit: data.cited_by_rdts_count || 0,
        wikipedia: data.cited_by_wikipedia_count || 0
      },
      url: data.details_url
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Altmetric-Daten:', error);
    return null;
  }
}

/**
 * Ruft Dimensions-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Dimensions-Daten
 */
async function fetchDimensionsData(doi) {
  try {
    // Dimensions API (öffentliche Endpunkte sind begrenzt)
    // Dies ist ein Beispiel und müsste mit einem tatsächlichen API-Schlüssel implementiert werden
    const apiUrl = `https://metrics-api.dimensions.ai/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      times_cited: data.times_cited || 0,
      recent_citations: data.recent_citations || 0,
      relative_citation_ratio: data.relative_citation_ratio || 0,
      field_citation_ratio: data.field_citation_ratio || 0
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Dimensions-Daten:', error);
    return null;
  }
}  }
}}}}}}} */

import fetch from 'node-fetch';

/**
 * Extrahiert Zitationen und Referenzen aus einer Publikation mit DOI
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Extrahierte Zitationen und Referenzen
 */
export async function extractCitationsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche zuerst Crossref
    const crossrefData = await fetchCrossrefCitations(doi);
    if (crossrefData && crossrefData.citations && crossrefData.citations.length > 0) {
      return crossrefData;
    }
    
    // Wenn Crossref keine Ergebnisse liefert, versuche Semantic Scholar
    const semanticScholarData = await fetchSemanticScholarCitations(doi);
    if (semanticScholarData && semanticScholarData.citations && semanticScholarData.citations.length > 0) {
      return semanticScholarData;
    }
    
    // Wenn beide keine Ergebnisse liefern, versuche OpenCitations
    const openCitationsData = await fetchOpenCitationsCitations(doi);
    
    return openCitationsData;
  } catch (error) {
    console.error('Fehler bei der Zitationsextraktion:', error);
    return {
      doi,
      citations: [],
      references: [],
      error: error.message
    };
  }
}

/**
 * Ruft Zitationen von Crossref ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchCrossrefCitations(doi) {
  try {
    // Crossref API für Referenzen (was die Publikation zitiert)
    const referencesUrl = `https://api.crossref.org/works/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
      }
    });
    
    if (!referencesResponse.ok) {
      throw new Error(`Crossref API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Referenzen
    const references = [];
    if (referencesData.message && referencesData.message.reference) {
      referencesData.message.reference.forEach(ref => {
        references.push({
          doi: ref.DOI,
          title: ref['article-title'] || ref.title,
          authors: ref.author ? [ref.author] : [],
          year: ref.year,
          journal: ref['journal-title']
        });
      });
    }
    
    // Für Zitationen (was die Publikation zitiert) gibt es keine direkte Crossref-API
    // Wir könnten OpenCitations oder Semantic Scholar verwenden
    
    return {
      doi,
      citations: [], // Crossref bietet keine direkte API für eingehende Zitationen
      references,
      source: 'crossref'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Crossref-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von Semantic Scholar ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchSemanticScholarCitations(doi) {
  try {
    // Semantic Scholar API
    const apiUrl = `https://api.semanticscholar.org/v1/paper/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      throw new Error(`Semantic Scholar API-Fehler: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Extrahiere Zitationen (eingehende Zitationen)
    const citations = data.citations ? data.citations.map(citation => ({
      doi: citation.doi,
      title: citation.title,
      authors: citation.authors ? citation.authors.map(author => author.name) : [],
      year: citation.year,
      journal: citation.venue
    })) : [];
    
    // Extrahiere Referenzen (ausgehende Zitationen)
    const references = data.references ? data.references.map(reference => ({
      doi: reference.doi,
      title: reference.title,
      authors: reference.authors ? reference.authors.map(author => author.name) : [],
      year: reference.year,
      journal: reference.venue
    })) : [];
    
    return {
      doi,
      citations,
      references,
      source: 'semantic_scholar'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Semantic Scholar-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von OpenCitations ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchOpenCitationsCitations(doi) {
  try {
    // OpenCitations API für eingehende Zitationen
    const citationsUrl = `https://opencitations.net/index/api/v1/citations/${encodeURIComponent(doi)}`;
    const citationsResponse = await fetch(citationsUrl);
    
    if (!citationsResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${citationsResponse.status}`);
    }
    
    const citationsData = await citationsResponse.json();
    
    // OpenCitations API für ausgehende Referenzen
    const referencesUrl = `https://opencitations.net/index/api/v1/references/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl);
    
    if (!referencesResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Zitationen
    const citations = citationsData.map(citation => ({
      doi: citation.citing,
      year: citation.creation ? citation.creation.substring(0, 4) : null
    }));
    
    // Extrahiere Referenzen
    const references = referencesData.map(reference => ({
      doi: reference.cited,
      year: reference.creation ? reference.creation.substring(0, 4) : null
    }));
    
    // Anreicherung der Metadaten für Zitationen und Referenzen
    const enrichedCitations = await enrichCitationsMetadata(citations);
    const enrichedReferences = await enrichCitationsMetadata(references);
    
    return {
      doi,
      citations: enrichedCitations,
      references: enrichedReferences,
      source: 'opencitations'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von OpenCitations-Zitationen:', error);
    return {
      doi,
      citations: [],
      references: [],
      source: 'opencitations',
      error: error.message
    };
  }
}

/**
 * Reichert Zitationsmetadaten mit Titel und Autoren an
 * @param {Array} citations - Liste von Zitationen mit DOIs
 * @returns {Promise<Array>} - Angereicherte Zitationen
 */
async function enrichCitationsMetadata(citations) {
  const enrichedCitations = [];
  
  for (const citation of citations) {
    if (!citation.doi) {
      enrichedCitations.push(citation);
      continue;
    }
    
    try {
      // Crossref API für Metadaten
      const url = `https://api.crossref.org/works/${encodeURIComponent(citation.doi)}`;
      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
        }
      });
      
      if (!response.ok) {
        enrichedCitations.push(citation);
        continue;
      }
      
      const data = await response.json();
      const message = data.message;
      
      // Extrahiere Titel
      const title = message.title ? message.title[0] : null;
      
      // Extrahiere Autoren
      const authors = message.author ? message.author.map(author => 
        `${author.given || ''} ${author.family || ''}`.trim()
      ) : [];
      
      // Extrahiere Journal
      const journal = message['container-title'] ? message['container-title'][0] : null;
      
      // Extrahiere Jahr
      const year = message.published ? 
        (message.published['date-parts'] ? message.published['date-parts'][0][0] : null) : 
        citation.year;
      
      enrichedCitations.push({
        ...citation,
        title,
        authors,
        journal,
        year
      });
    } catch (error) {
      console.error(`Fehler beim Anreichern der Metadaten für DOI ${citation.doi}:`, error);
      enrichedCitations.push(citation);
    }
  }
  
  return enrichedCitations;
}

/**
 * Extrahiert Impact-Faktoren und andere Metriken für eine Publikation
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Metriken
 */
export async function extractMetricsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche Altmetric
    const altmetricData = await fetchAltmetricData(doi);
    
    // Versuche Dimensions
    const dimensionsData = await fetchDimensionsData(doi);
    
    // Kombiniere die Daten
    return {
      doi,
      altmetric: altmetricData,
      dimensions: dimensionsData,
      timestamp: Date.now()
    };
  } catch (error) {
    console.error('Fehler bei der Metrikextraktion:', error);
    return {
      doi,
      error: error.message
    };
  }
}

/**
 * Ruft Altmetric-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Altmetric-Daten
 */
async function fetchAltmetricData(doi) {
  try {
    // Altmetric API
    const apiUrl = `https://api.altmetric.com/v1/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      score: data.score,
      readers: {
        mendeley: data.readers ? data.readers.mendeley : 0,
        citeulike: data.readers ? data.readers.citeulike : 0
      },
      mentions: {
        twitter: data.cited_by_tweeters_count || 0,
        facebook: data.cited_by_fbwalls_count || 0,
        blogs: data.cited_by_feeds_count || 0,
        news: data.cited_by_msm_count || 0,
        reddit: data.cited_by_rdts_count || 0,
        wikipedia: data.cited_by_wikipedia_count || 0
      },
      url: data.details_url
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Altmetric-Daten:', error);
    return null;
  }
}

/**
 * Ruft Dimensions-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Dimensions-Daten
 */
async function fetchDimensionsData(doi) {
  try {
    // Dimensions API (öffentliche Endpunkte sind begrenzt)
    // Dies ist ein Beispiel und müsste mit einem tatsächlichen API-Schlüssel implementiert werden
    const apiUrl = `https://metrics-api.dimensions.ai/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      times_cited: data.times_cited || 0,
      recent_citations: data.recent_citations || 0,
      relative_citation_ratio: data.relative_citation_ratio || 0,
      field_citation_ratio: data.field_citation_ratio || 0
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Dimensions-Daten:', error);
    return null;
  }
}  }
}}}}}}}} */

import fetch from 'node-fetch';

/**
 * Extrahiert Zitationen und Referenzen aus einer Publikation mit DOI
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Extrahierte Zitationen und Referenzen
 */
export async function extractCitationsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche zuerst Crossref
    const crossrefData = await fetchCrossrefCitations(doi);
    if (crossrefData && crossrefData.citations && crossrefData.citations.length > 0) {
      return crossrefData;
    }
    
    // Wenn Crossref keine Ergebnisse liefert, versuche Semantic Scholar
    const semanticScholarData = await fetchSemanticScholarCitations(doi);
    if (semanticScholarData && semanticScholarData.citations && semanticScholarData.citations.length > 0) {
      return semanticScholarData;
    }
    
    // Wenn beide keine Ergebnisse liefern, versuche OpenCitations
    const openCitationsData = await fetchOpenCitationsCitations(doi);
    
    return openCitationsData;
  } catch (error) {
    console.error('Fehler bei der Zitationsextraktion:', error);
    return {
      doi,
      citations: [],
      references: [],
      error: error.message
    };
  }
}

/**
 * Ruft Zitationen von Crossref ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchCrossrefCitations(doi) {
  try {
    // Crossref API für Referenzen (was die Publikation zitiert)
    const referencesUrl = `https://api.crossref.org/works/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
      }
    });
    
    if (!referencesResponse.ok) {
      throw new Error(`Crossref API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Referenzen
    const references = [];
    if (referencesData.message && referencesData.message.reference) {
      referencesData.message.reference.forEach(ref => {
        references.push({
          doi: ref.DOI,
          title: ref['article-title'] || ref.title,
          authors: ref.author ? [ref.author] : [],
          year: ref.year,
          journal: ref['journal-title']
        });
      });
    }
    
    // Für Zitationen (was die Publikation zitiert) gibt es keine direkte Crossref-API
    // Wir könnten OpenCitations oder Semantic Scholar verwenden
    
    return {
      doi,
      citations: [], // Crossref bietet keine direkte API für eingehende Zitationen
      references,
      source: 'crossref'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Crossref-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von Semantic Scholar ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchSemanticScholarCitations(doi) {
  try {
    // Semantic Scholar API
    const apiUrl = `https://api.semanticscholar.org/v1/paper/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      throw new Error(`Semantic Scholar API-Fehler: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Extrahiere Zitationen (eingehende Zitationen)
    const citations = data.citations ? data.citations.map(citation => ({
      doi: citation.doi,
      title: citation.title,
      authors: citation.authors ? citation.authors.map(author => author.name) : [],
      year: citation.year,
      journal: citation.venue
    })) : [];
    
    // Extrahiere Referenzen (ausgehende Zitationen)
    const references = data.references ? data.references.map(reference => ({
      doi: reference.doi,
      title: reference.title,
      authors: reference.authors ? reference.authors.map(author => author.name) : [],
      year: reference.year,
      journal: reference.venue
    })) : [];
    
    return {
      doi,
      citations,
      references,
      source: 'semantic_scholar'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Semantic Scholar-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von OpenCitations ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchOpenCitationsCitations(doi) {
  try {
    // OpenCitations API für eingehende Zitationen
    const citationsUrl = `https://opencitations.net/index/api/v1/citations/${encodeURIComponent(doi)}`;
    const citationsResponse = await fetch(citationsUrl);
    
    if (!citationsResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${citationsResponse.status}`);
    }
    
    const citationsData = await citationsResponse.json();
    
    // OpenCitations API für ausgehende Referenzen
    const referencesUrl = `https://opencitations.net/index/api/v1/references/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl);
    
    if (!referencesResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Zitationen
    const citations = citationsData.map(citation => ({
      doi: citation.citing,
      year: citation.creation ? citation.creation.substring(0, 4) : null
    }));
    
    // Extrahiere Referenzen
    const references = referencesData.map(reference => ({
      doi: reference.cited,
      year: reference.creation ? reference.creation.substring(0, 4) : null
    }));
    
    // Anreicherung der Metadaten für Zitationen und Referenzen
    const enrichedCitations = await enrichCitationsMetadata(citations);
    const enrichedReferences = await enrichCitationsMetadata(references);
    
    return {
      doi,
      citations: enrichedCitations,
      references: enrichedReferences,
      source: 'opencitations'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von OpenCitations-Zitationen:', error);
    return {
      doi,
      citations: [],
      references: [],
      source: 'opencitations',
      error: error.message
    };
  }
}

/**
 * Reichert Zitationsmetadaten mit Titel und Autoren an
 * @param {Array} citations - Liste von Zitationen mit DOIs
 * @returns {Promise<Array>} - Angereicherte Zitationen
 */
async function enrichCitationsMetadata(citations) {
  const enrichedCitations = [];
  
  for (const citation of citations) {
    if (!citation.doi) {
      enrichedCitations.push(citation);
      continue;
    }
    
    try {
      // Crossref API für Metadaten
      const url = `https://api.crossref.org/works/${encodeURIComponent(citation.doi)}`;
      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
        }
      });
      
      if (!response.ok) {
        enrichedCitations.push(citation);
        continue;
      }
      
      const data = await response.json();
      const message = data.message;
      
      // Extrahiere Titel
      const title = message.title ? message.title[0] : null;
      
      // Extrahiere Autoren
      const authors = message.author ? message.author.map(author => 
        `${author.given || ''} ${author.family || ''}`.trim()
      ) : [];
      
      // Extrahiere Journal
      const journal = message['container-title'] ? message['container-title'][0] : null;
      
      // Extrahiere Jahr
      const year = message.published ? 
        (message.published['date-parts'] ? message.published['date-parts'][0][0] : null) : 
        citation.year;
      
      enrichedCitations.push({
        ...citation,
        title,
        authors,
        journal,
        year
      });
    } catch (error) {
      console.error(`Fehler beim Anreichern der Metadaten für DOI ${citation.doi}:`, error);
      enrichedCitations.push(citation);
    }
  }
  
  return enrichedCitations;
}

/**
 * Extrahiert Impact-Faktoren und andere Metriken für eine Publikation
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Metriken
 */
export async function extractMetricsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche Altmetric
    const altmetricData = await fetchAltmetricData(doi);
    
    // Versuche Dimensions
    const dimensionsData = await fetchDimensionsData(doi);
    
    // Kombiniere die Daten
    return {
      doi,
      altmetric: altmetricData,
      dimensions: dimensionsData,
      timestamp: Date.now()
    };
  } catch (error) {
    console.error('Fehler bei der Metrikextraktion:', error);
    return {
      doi,
      error: error.message
    };
  }
}

/**
 * Ruft Altmetric-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Altmetric-Daten
 */
async function fetchAltmetricData(doi) {
  try {
    // Altmetric API
    const apiUrl = `https://api.altmetric.com/v1/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      score: data.score,
      readers: {
        mendeley: data.readers ? data.readers.mendeley : 0,
        citeulike: data.readers ? data.readers.citeulike : 0
      },
      mentions: {
        twitter: data.cited_by_tweeters_count || 0,
        facebook: data.cited_by_fbwalls_count || 0,
        blogs: data.cited_by_feeds_count || 0,
        news: data.cited_by_msm_count || 0,
        reddit: data.cited_by_rdts_count || 0,
        wikipedia: data.cited_by_wikipedia_count || 0
      },
      url: data.details_url
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Altmetric-Daten:', error);
    return null;
  }
}

/**
 * Ruft Dimensions-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Dimensions-Daten
 */
async function fetchDimensionsData(doi) {
  try {
    // Dimensions API (öffentliche Endpunkte sind begrenzt)
    // Dies ist ein Beispiel und müsste mit einem tatsächlichen API-Schlüssel implementiert werden
    const apiUrl = `https://metrics-api.dimensions.ai/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      times_cited: data.times_cited || 0,
      recent_citations: data.recent_citations || 0,
      relative_citation_ratio: data.relative_citation_ratio || 0,
      field_citation_ratio: data.field_citation_ratio || 0
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Dimensions-Daten:', error);
    return null;
  }
}  }
}}  }
}}}}}}}} */

import fetch from 'node-fetch';

/**
 * Extrahiert Zitationen und Referenzen aus einer Publikation mit DOI
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Extrahierte Zitationen und Referenzen
 */
export async function extractCitationsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche zuerst Crossref
    const crossrefData = await fetchCrossrefCitations(doi);
    if (crossrefData && crossrefData.citations && crossrefData.citations.length > 0) {
      return crossrefData;
    }
    
    // Wenn Crossref keine Ergebnisse liefert, versuche Semantic Scholar
    const semanticScholarData = await fetchSemanticScholarCitations(doi);
    if (semanticScholarData && semanticScholarData.citations && semanticScholarData.citations.length > 0) {
      return semanticScholarData;
    }
    
    // Wenn beide keine Ergebnisse liefern, versuche OpenCitations
    const openCitationsData = await fetchOpenCitationsCitations(doi);
    
    return openCitationsData;
  } catch (error) {
    console.error('Fehler bei der Zitationsextraktion:', error);
    return {
      doi,
      citations: [],
      references: [],
      error: error.message
    };
  }
}

/**
 * Ruft Zitationen von Crossref ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchCrossrefCitations(doi) {
  try {
    // Crossref API für Referenzen (was die Publikation zitiert)
    const referencesUrl = `https://api.crossref.org/works/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
      }
    });
    
    if (!referencesResponse.ok) {
      throw new Error(`Crossref API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Referenzen
    const references = [];
    if (referencesData.message && referencesData.message.reference) {
      referencesData.message.reference.forEach(ref => {
        references.push({
          doi: ref.DOI,
          title: ref['article-title'] || ref.title,
          authors: ref.author ? [ref.author] : [],
          year: ref.year,
          journal: ref['journal-title']
        });
      });
    }
    
    // Für Zitationen (was die Publikation zitiert) gibt es keine direkte Crossref-API
    // Wir könnten OpenCitations oder Semantic Scholar verwenden
    
    return {
      doi,
      citations: [], // Crossref bietet keine direkte API für eingehende Zitationen
      references,
      source: 'crossref'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Crossref-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von Semantic Scholar ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchSemanticScholarCitations(doi) {
  try {
    // Semantic Scholar API
    const apiUrl = `https://api.semanticscholar.org/v1/paper/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      throw new Error(`Semantic Scholar API-Fehler: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Extrahiere Zitationen (eingehende Zitationen)
    const citations = data.citations ? data.citations.map(citation => ({
      doi: citation.doi,
      title: citation.title,
      authors: citation.authors ? citation.authors.map(author => author.name) : [],
      year: citation.year,
      journal: citation.venue
    })) : [];
    
    // Extrahiere Referenzen (ausgehende Zitationen)
    const references = data.references ? data.references.map(reference => ({
      doi: reference.doi,
      title: reference.title,
      authors: reference.authors ? reference.authors.map(author => author.name) : [],
      year: reference.year,
      journal: reference.venue
    })) : [];
    
    return {
      doi,
      citations,
      references,
      source: 'semantic_scholar'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Semantic Scholar-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von OpenCitations ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchOpenCitationsCitations(doi) {
  try {
    // OpenCitations API für eingehende Zitationen
    const citationsUrl = `https://opencitations.net/index/api/v1/citations/${encodeURIComponent(doi)}`;
    const citationsResponse = await fetch(citationsUrl);
    
    if (!citationsResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${citationsResponse.status}`);
    }
    
    const citationsData = await citationsResponse.json();
    
    // OpenCitations API für ausgehende Referenzen
    const referencesUrl = `https://opencitations.net/index/api/v1/references/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl);
    
    if (!referencesResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Zitationen
    const citations = citationsData.map(citation => ({
      doi: citation.citing,
      year: citation.creation ? citation.creation.substring(0, 4) : null
    }));
    
    // Extrahiere Referenzen
    const references = referencesData.map(reference => ({
      doi: reference.cited,
      year: reference.creation ? reference.creation.substring(0, 4) : null
    }));
    
    // Anreicherung der Metadaten für Zitationen und Referenzen
    const enrichedCitations = await enrichCitationsMetadata(citations);
    const enrichedReferences = await enrichCitationsMetadata(references);
    
    return {
      doi,
      citations: enrichedCitations,
      references: enrichedReferences,
      source: 'opencitations'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von OpenCitations-Zitationen:', error);
    return {
      doi,
      citations: [],
      references: [],
      source: 'opencitations',
      error: error.message
    };
  }
}

/**
 * Reichert Zitationsmetadaten mit Titel und Autoren an
 * @param {Array} citations - Liste von Zitationen mit DOIs
 * @returns {Promise<Array>} - Angereicherte Zitationen
 */
async function enrichCitationsMetadata(citations) {
  const enrichedCitations = [];
  
  for (const citation of citations) {
    if (!citation.doi) {
      enrichedCitations.push(citation);
      continue;
    }
    
    try {
      // Crossref API für Metadaten
      const url = `https://api.crossref.org/works/${encodeURIComponent(citation.doi)}`;
      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
        }
      });
      
      if (!response.ok) {
        enrichedCitations.push(citation);
        continue;
      }
      
      const data = await response.json();
      const message = data.message;
      
      // Extrahiere Titel
      const title = message.title ? message.title[0] : null;
      
      // Extrahiere Autoren
      const authors = message.author ? message.author.map(author => 
        `${author.given || ''} ${author.family || ''}`.trim()
      ) : [];
      
      // Extrahiere Journal
      const journal = message['container-title'] ? message['container-title'][0] : null;
      
      // Extrahiere Jahr
      const year = message.published ? 
        (message.published['date-parts'] ? message.published['date-parts'][0][0] : null) : 
        citation.year;
      
      enrichedCitations.push({
        ...citation,
        title,
        authors,
        journal,
        year
      });
    } catch (error) {
      console.error(`Fehler beim Anreichern der Metadaten für DOI ${citation.doi}:`, error);
      enrichedCitations.push(citation);
    }
  }
  
  return enrichedCitations;
}

/**
 * Extrahiert Impact-Faktoren und andere Metriken für eine Publikation
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Metriken
 */
export async function extractMetricsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche Altmetric
    const altmetricData = await fetchAltmetricData(doi);
    
    // Versuche Dimensions
    const dimensionsData = await fetchDimensionsData(doi);
    
    // Kombiniere die Daten
    return {
      doi,
      altmetric: altmetricData,
      dimensions: dimensionsData,
      timestamp: Date.now()
    };
  } catch (error) {
    console.error('Fehler bei der Metrikextraktion:', error);
    return {
      doi,
      error: error.message
    };
  }
}

/**
 * Ruft Altmetric-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Altmetric-Daten
 */
async function fetchAltmetricData(doi) {
  try {
    // Altmetric API
    const apiUrl = `https://api.altmetric.com/v1/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      score: data.score,
      readers: {
        mendeley: data.readers ? data.readers.mendeley : 0,
        citeulike: data.readers ? data.readers.citeulike : 0
      },
      mentions: {
        twitter: data.cited_by_tweeters_count || 0,
        facebook: data.cited_by_fbwalls_count || 0,
        blogs: data.cited_by_feeds_count || 0,
        news: data.cited_by_msm_count || 0,
        reddit: data.cited_by_rdts_count || 0,
        wikipedia: data.cited_by_wikipedia_count || 0
      },
      url: data.details_url
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Altmetric-Daten:', error);
    return null;
  }
}

/**
 * Ruft Dimensions-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Dimensions-Daten
 */
async function fetchDimensionsData(doi) {
  try {
    // Dimensions API (öffentliche Endpunkte sind begrenzt)
    // Dies ist ein Beispiel und müsste mit einem tatsächlichen API-Schlüssel implementiert werden
    const apiUrl = `https://metrics-api.dimensions.ai/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      times_cited: data.times_cited || 0,
      recent_citations: data.recent_citations || 0,
      relative_citation_ratio: data.relative_citation_ratio || 0,
      field_citation_ratio: data.field_citation_ratio || 0
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Dimensions-Daten:', error);
    return null;
  }
}  }
}}}}}}}}}} */

import fetch from 'node-fetch';

/**
 * Extrahiert Zitationen und Referenzen aus einer Publikation mit DOI
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Extrahierte Zitationen und Referenzen
 */
export async function extractCitationsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche zuerst Crossref
    const crossrefData = await fetchCrossrefCitations(doi);
    if (crossrefData && crossrefData.citations && crossrefData.citations.length > 0) {
      return crossrefData;
    }
    
    // Wenn Crossref keine Ergebnisse liefert, versuche Semantic Scholar
    const semanticScholarData = await fetchSemanticScholarCitations(doi);
    if (semanticScholarData && semanticScholarData.citations && semanticScholarData.citations.length > 0) {
      return semanticScholarData;
    }
    
    // Wenn beide keine Ergebnisse liefern, versuche OpenCitations
    const openCitationsData = await fetchOpenCitationsCitations(doi);
    
    return openCitationsData;
  } catch (error) {
    console.error('Fehler bei der Zitationsextraktion:', error);
    return {
      doi,
      citations: [],
      references: [],
      error: error.message
    };
  }
}

/**
 * Ruft Zitationen von Crossref ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchCrossrefCitations(doi) {
  try {
    // Crossref API für Referenzen (was die Publikation zitiert)
    const referencesUrl = `https://api.crossref.org/works/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
      }
    });
    
    if (!referencesResponse.ok) {
      throw new Error(`Crossref API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Referenzen
    const references = [];
    if (referencesData.message && referencesData.message.reference) {
      referencesData.message.reference.forEach(ref => {
        references.push({
          doi: ref.DOI,
          title: ref['article-title'] || ref.title,
          authors: ref.author ? [ref.author] : [],
          year: ref.year,
          journal: ref['journal-title']
        });
      });
    }
    
    // Für Zitationen (was die Publikation zitiert) gibt es keine direkte Crossref-API
    // Wir könnten OpenCitations oder Semantic Scholar verwenden
    
    return {
      doi,
      citations: [], // Crossref bietet keine direkte API für eingehende Zitationen
      references,
      source: 'crossref'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Crossref-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von Semantic Scholar ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchSemanticScholarCitations(doi) {
  try {
    // Semantic Scholar API
    const apiUrl = `https://api.semanticscholar.org/v1/paper/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      throw new Error(`Semantic Scholar API-Fehler: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Extrahiere Zitationen (eingehende Zitationen)
    const citations = data.citations ? data.citations.map(citation => ({
      doi: citation.doi,
      title: citation.title,
      authors: citation.authors ? citation.authors.map(author => author.name) : [],
      year: citation.year,
      journal: citation.venue
    })) : [];
    
    // Extrahiere Referenzen (ausgehende Zitationen)
    const references = data.references ? data.references.map(reference => ({
      doi: reference.doi,
      title: reference.title,
      authors: reference.authors ? reference.authors.map(author => author.name) : [],
      year: reference.year,
      journal: reference.venue
    })) : [];
    
    return {
      doi,
      citations,
      references,
      source: 'semantic_scholar'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Semantic Scholar-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von OpenCitations ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchOpenCitationsCitations(doi) {
  try {
    // OpenCitations API für eingehende Zitationen
    const citationsUrl = `https://opencitations.net/index/api/v1/citations/${encodeURIComponent(doi)}`;
    const citationsResponse = await fetch(citationsUrl);
    
    if (!citationsResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${citationsResponse.status}`);
    }
    
    const citationsData = await citationsResponse.json();
    
    // OpenCitations API für ausgehende Referenzen
    const referencesUrl = `https://opencitations.net/index/api/v1/references/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl);
    
    if (!referencesResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Zitationen
    const citations = citationsData.map(citation => ({
      doi: citation.citing,
      year: citation.creation ? citation.creation.substring(0, 4) : null
    }));
    
    // Extrahiere Referenzen
    const references = referencesData.map(reference => ({
      doi: reference.cited,
      year: reference.creation ? reference.creation.substring(0, 4) : null
    }));
    
    // Anreicherung der Metadaten für Zitationen und Referenzen
    const enrichedCitations = await enrichCitationsMetadata(citations);
    const enrichedReferences = await enrichCitationsMetadata(references);
    
    return {
      doi,
      citations: enrichedCitations,
      references: enrichedReferences,
      source: 'opencitations'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von OpenCitations-Zitationen:', error);
    return {
      doi,
      citations: [],
      references: [],
      source: 'opencitations',
      error: error.message
    };
  }
}

/**
 * Reichert Zitationsmetadaten mit Titel und Autoren an
 * @param {Array} citations - Liste von Zitationen mit DOIs
 * @returns {Promise<Array>} - Angereicherte Zitationen
 */
async function enrichCitationsMetadata(citations) {
  const enrichedCitations = [];
  
  for (const citation of citations) {
    if (!citation.doi) {
      enrichedCitations.push(citation);
      continue;
    }
    
    try {
      // Crossref API für Metadaten
      const url = `https://api.crossref.org/works/${encodeURIComponent(citation.doi)}`;
      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
        }
      });
      
      if (!response.ok) {
        enrichedCitations.push(citation);
        continue;
      }
      
      const data = await response.json();
      const message = data.message;
      
      // Extrahiere Titel
      const title = message.title ? message.title[0] : null;
      
      // Extrahiere Autoren
      const authors = message.author ? message.author.map(author => 
        `${author.given || ''} ${author.family || ''}`.trim()
      ) : [];
      
      // Extrahiere Journal
      const journal = message['container-title'] ? message['container-title'][0] : null;
      
      // Extrahiere Jahr
      const year = message.published ? 
        (message.published['date-parts'] ? message.published['date-parts'][0][0] : null) : 
        citation.year;
      
      enrichedCitations.push({
        ...citation,
        title,
        authors,
        journal,
        year
      });
    } catch (error) {
      console.error(`Fehler beim Anreichern der Metadaten für DOI ${citation.doi}:`, error);
      enrichedCitations.push(citation);
    }
  }
  
  return enrichedCitations;
}

/**
 * Extrahiert Impact-Faktoren und andere Metriken für eine Publikation
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Metriken
 */
export async function extractMetricsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche Altmetric
    const altmetricData = await fetchAltmetricData(doi);
    
    // Versuche Dimensions
    const dimensionsData = await fetchDimensionsData(doi);
    
    // Kombiniere die Daten
    return {
      doi,
      altmetric: altmetricData,
      dimensions: dimensionsData,
      timestamp: Date.now()
    };
  } catch (error) {
    console.error('Fehler bei der Metrikextraktion:', error);
    return {
      doi,
      error: error.message
    };
  }
}

/**
 * Ruft Altmetric-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Altmetric-Daten
 */
async function fetchAltmetricData(doi) {
  try {
    // Altmetric API
    const apiUrl = `https://api.altmetric.com/v1/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      score: data.score,
      readers: {
        mendeley: data.readers ? data.readers.mendeley : 0,
        citeulike: data.readers ? data.readers.citeulike : 0
      },
      mentions: {
        twitter: data.cited_by_tweeters_count || 0,
        facebook: data.cited_by_fbwalls_count || 0,
        blogs: data.cited_by_feeds_count || 0,
        news: data.cited_by_msm_count || 0,
        reddit: data.cited_by_rdts_count || 0,
        wikipedia: data.cited_by_wikipedia_count || 0
      },
      url: data.details_url
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Altmetric-Daten:', error);
    return null;
  }
}

/**
 * Ruft Dimensions-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Dimensions-Daten
 */
async function fetchDimensionsData(doi) {
  try {
    // Dimensions API (öffentliche Endpunkte sind begrenzt)
    // Dies ist ein Beispiel und müsste mit einem tatsächlichen API-Schlüssel implementiert werden
    const apiUrl = `https://metrics-api.dimensions.ai/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      times_cited: data.times_cited || 0,
      recent_citations: data.recent_citations || 0,
      relative_citation_ratio: data.relative_citation_ratio || 0,
      field_citation_ratio: data.field_citation_ratio || 0
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Dimensions-Daten:', error);
    return null;
  }
}  }
}}}}}}}}}}} */

import fetch from 'node-fetch';

/**
 * Extrahiert Zitationen und Referenzen aus einer Publikation mit DOI
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Extrahierte Zitationen und Referenzen
 */
export async function extractCitationsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche zuerst Crossref
    const crossrefData = await fetchCrossrefCitations(doi);
    if (crossrefData && crossrefData.citations && crossrefData.citations.length > 0) {
      return crossrefData;
    }
    
    // Wenn Crossref keine Ergebnisse liefert, versuche Semantic Scholar
    const semanticScholarData = await fetchSemanticScholarCitations(doi);
    if (semanticScholarData && semanticScholarData.citations && semanticScholarData.citations.length > 0) {
      return semanticScholarData;
    }
    
    // Wenn beide keine Ergebnisse liefern, versuche OpenCitations
    const openCitationsData = await fetchOpenCitationsCitations(doi);
    
    return openCitationsData;
  } catch (error) {
    console.error('Fehler bei der Zitationsextraktion:', error);
    return {
      doi,
      citations: [],
      references: [],
      error: error.message
    };
  }
}

/**
 * Ruft Zitationen von Crossref ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchCrossrefCitations(doi) {
  try {
    // Crossref API für Referenzen (was die Publikation zitiert)
    const referencesUrl = `https://api.crossref.org/works/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
      }
    });
    
    if (!referencesResponse.ok) {
      throw new Error(`Crossref API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Referenzen
    const references = [];
    if (referencesData.message && referencesData.message.reference) {
      referencesData.message.reference.forEach(ref => {
        references.push({
          doi: ref.DOI,
          title: ref['article-title'] || ref.title,
          authors: ref.author ? [ref.author] : [],
          year: ref.year,
          journal: ref['journal-title']
        });
      });
    }
    
    // Für Zitationen (was die Publikation zitiert) gibt es keine direkte Crossref-API
    // Wir könnten OpenCitations oder Semantic Scholar verwenden
    
    return {
      doi,
      citations: [], // Crossref bietet keine direkte API für eingehende Zitationen
      references,
      source: 'crossref'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Crossref-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von Semantic Scholar ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchSemanticScholarCitations(doi) {
  try {
    // Semantic Scholar API
    const apiUrl = `https://api.semanticscholar.org/v1/paper/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      throw new Error(`Semantic Scholar API-Fehler: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Extrahiere Zitationen (eingehende Zitationen)
    const citations = data.citations ? data.citations.map(citation => ({
      doi: citation.doi,
      title: citation.title,
      authors: citation.authors ? citation.authors.map(author => author.name) : [],
      year: citation.year,
      journal: citation.venue
    })) : [];
    
    // Extrahiere Referenzen (ausgehende Zitationen)
    const references = data.references ? data.references.map(reference => ({
      doi: reference.doi,
      title: reference.title,
      authors: reference.authors ? reference.authors.map(author => author.name) : [],
      year: reference.year,
      journal: reference.venue
    })) : [];
    
    return {
      doi,
      citations,
      references,
      source: 'semantic_scholar'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Semantic Scholar-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von OpenCitations ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchOpenCitationsCitations(doi) {
  try {
    // OpenCitations API für eingehende Zitationen
    const citationsUrl = `https://opencitations.net/index/api/v1/citations/${encodeURIComponent(doi)}`;
    const citationsResponse = await fetch(citationsUrl);
    
    if (!citationsResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${citationsResponse.status}`);
    }
    
    const citationsData = await citationsResponse.json();
    
    // OpenCitations API für ausgehende Referenzen
    const referencesUrl = `https://opencitations.net/index/api/v1/references/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl);
    
    if (!referencesResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Zitationen
    const citations = citationsData.map(citation => ({
      doi: citation.citing,
      year: citation.creation ? citation.creation.substring(0, 4) : null
    }));
    
    // Extrahiere Referenzen
    const references = referencesData.map(reference => ({
      doi: reference.cited,
      year: reference.creation ? reference.creation.substring(0, 4) : null
    }));
    
    // Anreicherung der Metadaten für Zitationen und Referenzen
    const enrichedCitations = await enrichCitationsMetadata(citations);
    const enrichedReferences = await enrichCitationsMetadata(references);
    
    return {
      doi,
      citations: enrichedCitations,
      references: enrichedReferences,
      source: 'opencitations'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von OpenCitations-Zitationen:', error);
    return {
      doi,
      citations: [],
      references: [],
      source: 'opencitations',
      error: error.message
    };
  }
}

/**
 * Reichert Zitationsmetadaten mit Titel und Autoren an
 * @param {Array} citations - Liste von Zitationen mit DOIs
 * @returns {Promise<Array>} - Angereicherte Zitationen
 */
async function enrichCitationsMetadata(citations) {
  const enrichedCitations = [];
  
  for (const citation of citations) {
    if (!citation.doi) {
      enrichedCitations.push(citation);
      continue;
    }
    
    try {
      // Crossref API für Metadaten
      const url = `https://api.crossref.org/works/${encodeURIComponent(citation.doi)}`;
      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
        }
      });
      
      if (!response.ok) {
        enrichedCitations.push(citation);
        continue;
      }
      
      const data = await response.json();
      const message = data.message;
      
      // Extrahiere Titel
      const title = message.title ? message.title[0] : null;
      
      // Extrahiere Autoren
      const authors = message.author ? message.author.map(author => 
        `${author.given || ''} ${author.family || ''}`.trim()
      ) : [];
      
      // Extrahiere Journal
      const journal = message['container-title'] ? message['container-title'][0] : null;
      
      // Extrahiere Jahr
      const year = message.published ? 
        (message.published['date-parts'] ? message.published['date-parts'][0][0] : null) : 
        citation.year;
      
      enrichedCitations.push({
        ...citation,
        title,
        authors,
        journal,
        year
      });
    } catch (error) {
      console.error(`Fehler beim Anreichern der Metadaten für DOI ${citation.doi}:`, error);
      enrichedCitations.push(citation);
    }
  }
  
  return enrichedCitations;
}

/**
 * Extrahiert Impact-Faktoren und andere Metriken für eine Publikation
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Metriken
 */
export async function extractMetricsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche Altmetric
    const altmetricData = await fetchAltmetricData(doi);
    
    // Versuche Dimensions
    const dimensionsData = await fetchDimensionsData(doi);
    
    // Kombiniere die Daten
    return {
      doi,
      altmetric: altmetricData,
      dimensions: dimensionsData,
      timestamp: Date.now()
    };
  } catch (error) {
    console.error('Fehler bei der Metrikextraktion:', error);
    return {
      doi,
      error: error.message
    };
  }
}

/**
 * Ruft Altmetric-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Altmetric-Daten
 */
async function fetchAltmetricData(doi) {
  try {
    // Altmetric API
    const apiUrl = `https://api.altmetric.com/v1/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      score: data.score,
      readers: {
        mendeley: data.readers ? data.readers.mendeley : 0,
        citeulike: data.readers ? data.readers.citeulike : 0
      },
      mentions: {
        twitter: data.cited_by_tweeters_count || 0,
        facebook: data.cited_by_fbwalls_count || 0,
        blogs: data.cited_by_feeds_count || 0,
        news: data.cited_by_msm_count || 0,
        reddit: data.cited_by_rdts_count || 0,
        wikipedia: data.cited_by_wikipedia_count || 0
      },
      url: data.details_url
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Altmetric-Daten:', error);
    return null;
  }
}

/**
 * Ruft Dimensions-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Dimensions-Daten
 */
async function fetchDimensionsData(doi) {
  try {
    // Dimensions API (öffentliche Endpunkte sind begrenzt)
    // Dies ist ein Beispiel und müsste mit einem tatsächlichen API-Schlüssel implementiert werden
    const apiUrl = `https://metrics-api.dimensions.ai/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      times_cited: data.times_cited || 0,
      recent_citations: data.recent_citations || 0,
      relative_citation_ratio: data.relative_citation_ratio || 0,
      field_citation_ratio: data.field_citation_ratio || 0
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Dimensions-Daten:', error);
    return null;
  }
}  }
}}}}}}}}}}}}} */

import fetch from 'node-fetch';

/**
 * Extrahiert Zitationen und Referenzen aus einer Publikation mit DOI
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Extrahierte Zitationen und Referenzen
 */
export async function extractCitationsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche zuerst Crossref
    const crossrefData = await fetchCrossrefCitations(doi);
    if (crossrefData && crossrefData.citations && crossrefData.citations.length > 0) {
      return crossrefData;
    }
    
    // Wenn Crossref keine Ergebnisse liefert, versuche Semantic Scholar
    const semanticScholarData = await fetchSemanticScholarCitations(doi);
    if (semanticScholarData && semanticScholarData.citations && semanticScholarData.citations.length > 0) {
      return semanticScholarData;
    }
    
    // Wenn beide keine Ergebnisse liefern, versuche OpenCitations
    const openCitationsData = await fetchOpenCitationsCitations(doi);
    
    return openCitationsData;
  } catch (error) {
    console.error('Fehler bei der Zitationsextraktion:', error);
    return {
      doi,
      citations: [],
      references: [],
      error: error.message
    };
  }
}

/**
 * Ruft Zitationen von Crossref ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchCrossrefCitations(doi) {
  try {
    // Crossref API für Referenzen (was die Publikation zitiert)
    const referencesUrl = `https://api.crossref.org/works/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
      }
    });
    
    if (!referencesResponse.ok) {
      throw new Error(`Crossref API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Referenzen
    const references = [];
    if (referencesData.message && referencesData.message.reference) {
      referencesData.message.reference.forEach(ref => {
        references.push({
          doi: ref.DOI,
          title: ref['article-title'] || ref.title,
          authors: ref.author ? [ref.author] : [],
          year: ref.year,
          journal: ref['journal-title']
        });
      });
    }
    
    // Für Zitationen (was die Publikation zitiert) gibt es keine direkte Crossref-API
    // Wir könnten OpenCitations oder Semantic Scholar verwenden
    
    return {
      doi,
      citations: [], // Crossref bietet keine direkte API für eingehende Zitationen
      references,
      source: 'crossref'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Crossref-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von Semantic Scholar ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchSemanticScholarCitations(doi) {
  try {
    // Semantic Scholar API
    const apiUrl = `https://api.semanticscholar.org/v1/paper/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      throw new Error(`Semantic Scholar API-Fehler: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Extrahiere Zitationen (eingehende Zitationen)
    const citations = data.citations ? data.citations.map(citation => ({
      doi: citation.doi,
      title: citation.title,
      authors: citation.authors ? citation.authors.map(author => author.name) : [],
      year: citation.year,
      journal: citation.venue
    })) : [];
    
    // Extrahiere Referenzen (ausgehende Zitationen)
    const references = data.references ? data.references.map(reference => ({
      doi: reference.doi,
      title: reference.title,
      authors: reference.authors ? reference.authors.map(author => author.name) : [],
      year: reference.year,
      journal: reference.venue
    })) : [];
    
    return {
      doi,
      citations,
      references,
      source: 'semantic_scholar'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Semantic Scholar-Zitationen:', error);
    return null;
  }
}

/**
 * Ruft Zitationen von OpenCitations ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Zitationen und Referenzen
 */
async function fetchOpenCitationsCitations(doi) {
  try {
    // OpenCitations API für eingehende Zitationen
    const citationsUrl = `https://opencitations.net/index/api/v1/citations/${encodeURIComponent(doi)}`;
    const citationsResponse = await fetch(citationsUrl);
    
    if (!citationsResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${citationsResponse.status}`);
    }
    
    const citationsData = await citationsResponse.json();
    
    // OpenCitations API für ausgehende Referenzen
    const referencesUrl = `https://opencitations.net/index/api/v1/references/${encodeURIComponent(doi)}`;
    const referencesResponse = await fetch(referencesUrl);
    
    if (!referencesResponse.ok) {
      throw new Error(`OpenCitations API-Fehler: ${referencesResponse.status}`);
    }
    
    const referencesData = await referencesResponse.json();
    
    // Extrahiere Zitationen
    const citations = citationsData.map(citation => ({
      doi: citation.citing,
      year: citation.creation ? citation.creation.substring(0, 4) : null
    }));
    
    // Extrahiere Referenzen
    const references = referencesData.map(reference => ({
      doi: reference.cited,
      year: reference.creation ? reference.creation.substring(0, 4) : null
    }));
    
    // Anreicherung der Metadaten für Zitationen und Referenzen
    const enrichedCitations = await enrichCitationsMetadata(citations);
    const enrichedReferences = await enrichCitationsMetadata(references);
    
    return {
      doi,
      citations: enrichedCitations,
      references: enrichedReferences,
      source: 'opencitations'
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von OpenCitations-Zitationen:', error);
    return {
      doi,
      citations: [],
      references: [],
      source: 'opencitations',
      error: error.message
    };
  }
}

/**
 * Reichert Zitationsmetadaten mit Titel und Autoren an
 * @param {Array} citations - Liste von Zitationen mit DOIs
 * @returns {Promise<Array>} - Angereicherte Zitationen
 */
async function enrichCitationsMetadata(citations) {
  const enrichedCitations = [];
  
  for (const citation of citations) {
    if (!citation.doi) {
      enrichedCitations.push(citation);
      continue;
    }
    
    try {
      // Crossref API für Metadaten
      const url = `https://api.crossref.org/works/${encodeURIComponent(citation.doi)}`;
      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'DeSci-Scholar/1.0 (mailto:<EMAIL>)'
        }
      });
      
      if (!response.ok) {
        enrichedCitations.push(citation);
        continue;
      }
      
      const data = await response.json();
      const message = data.message;
      
      // Extrahiere Titel
      const title = message.title ? message.title[0] : null;
      
      // Extrahiere Autoren
      const authors = message.author ? message.author.map(author => 
        `${author.given || ''} ${author.family || ''}`.trim()
      ) : [];
      
      // Extrahiere Journal
      const journal = message['container-title'] ? message['container-title'][0] : null;
      
      // Extrahiere Jahr
      const year = message.published ? 
        (message.published['date-parts'] ? message.published['date-parts'][0][0] : null) : 
        citation.year;
      
      enrichedCitations.push({
        ...citation,
        title,
        authors,
        journal,
        year
      });
    } catch (error) {
      console.error(`Fehler beim Anreichern der Metadaten für DOI ${citation.doi}:`, error);
      enrichedCitations.push(citation);
    }
  }
  
  return enrichedCitations;
}

/**
 * Extrahiert Impact-Faktoren und andere Metriken für eine Publikation
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Metriken
 */
export async function extractMetricsFromDoi(doi) {
  try {
    // Validiere DOI-Format
    if (!doi || !doi.match(/^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/)) {
      throw new Error('Ungültiges DOI-Format');
    }
    
    // Versuche Altmetric
    const altmetricData = await fetchAltmetricData(doi);
    
    // Versuche Dimensions
    const dimensionsData = await fetchDimensionsData(doi);
    
    // Kombiniere die Daten
    return {
      doi,
      altmetric: altmetricData,
      dimensions: dimensionsData,
      timestamp: Date.now()
    };
  } catch (error) {
    console.error('Fehler bei der Metrikextraktion:', error);
    return {
      doi,
      error: error.message
    };
  }
}

/**
 * Ruft Altmetric-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Altmetric-Daten
 */
async function fetchAltmetricData(doi) {
  try {
    // Altmetric API
    const apiUrl = `https://api.altmetric.com/v1/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      score: data.score,
      readers: {
        mendeley: data.readers ? data.readers.mendeley : 0,
        citeulike: data.readers ? data.readers.citeulike : 0
      },
      mentions: {
        twitter: data.cited_by_tweeters_count || 0,
        facebook: data.cited_by_fbwalls_count || 0,
        blogs: data.cited_by_feeds_count || 0,
        news: data.cited_by_msm_count || 0,
        reddit: data.cited_by_rdts_count || 0,
        wikipedia: data.cited_by_wikipedia_count || 0
      },
      url: data.details_url
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Altmetric-Daten:', error);
    return null;
  }
}

/**
 * Ruft Dimensions-Daten für eine Publikation ab
 * @param {string} doi - DOI der Publikation
 * @returns {Promise<Object>} - Dimensions-Daten
 */
async function fetchDimensionsData(doi) {
  try {
    // Dimensions API (öffentliche Endpunkte sind begrenzt)
    // Dies ist ein Beispiel und müsste mit einem tatsächlichen API-Schlüssel implementiert werden
    const apiUrl = `https://metrics-api.dimensions.ai/doi/${encodeURIComponent(doi)}`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    
    return {
      times_cited: data.times_cited || 0,
      recent_citations: data.recent_citations || 0,
      relative_citation_ratio: data.relative_citation_ratio || 0,
      field_citation_ratio: data.field_citation_ratio || 0
    };
  } catch (error) {
    console.error('Fehler beim Abrufen von Dimensions-Daten:', error);
    return null;
  }
}  }
}