/**
 * Hilfsfunktionen zur Berechnung von Ähnlichkeitswerten zwischen Forschungsprofilen
 */

/**
 * Berechnet einen Ähnlichkeitswert zwischen zwei Forschungsprofilen
 * @param {Object} profile1 Erstes Forschungsprofil
 * @param {Object} profile2 Zweites Forschungsprofil
 * @param {Object} config Konfiguration für die Ähnlichkeitsberechnung
 * @returns {number} Ähnlichkeitswert zwischen 0 und 1
 */
export async function getSimilarityScore(profile1, profile2, config = {}) {
  if (!profile1 || !profile2) {
    return 0;
  }
  
  // Standardkonfiguration mit benutzerdefinierten Einstellungen überschreiben
  const settings = {
    topicWeight: 0.5,
    skillWeight: 0.3,
    collaborationWeight: 0.1,
    interdisciplinarityWeight: 0.1,
    considerCitations: true,
    ...config
  };
  
  // Berechne Ähnlichkeit basierend auf Forschungsthemen
  const topicSimilarity = calculateTopicSimilarity(profile1, profile2);
  
  // Berechne Ähnlichkeit basierend auf methodischen Fähigkeiten
  const skillSimilarity = calculateSkillSimilarity(profile1, profile2);
  
  // Berechne Ähnlichkeit basierend auf Kollaborationsmustern
  const collaborationSimilarity = calculateCollaborationSimilarity(profile1, profile2);
  
  // Berechne Kompatibilität basierend auf Interdisziplinarität
  const interdisciplinaritySimilarity = calculateInterdisciplinaritySimilarity(profile1, profile2);
  
  // Berechne Zitationsähnlichkeit, falls gewünscht
  let citationSimilarity = 0;
  if (settings.considerCitations) {
    citationSimilarity = calculateCitationSimilarity(profile1, profile2);
    
    // Gewichte anpassen, um Zitationen zu berücksichtigen
    const totalWeight = settings.topicWeight + settings.skillWeight + 
                       settings.collaborationWeight + settings.interdisciplinarityWeight;
    
    settings.topicWeight = settings.topicWeight / totalWeight * 0.8;
    settings.skillWeight = settings.skillWeight / totalWeight * 0.8;
    settings.collaborationWeight = settings.collaborationWeight / totalWeight * 0.8;
    settings.interdisciplinarityWeight = settings.interdisciplinarityWeight / totalWeight * 0.8;
  }
  
  // Gewichteter Gesamtähnlichkeitswert
  const totalSimilarity = 
    settings.topicWeight * topicSimilarity +
    settings.skillWeight * skillSimilarity +
    settings.collaborationWeight * collaborationSimilarity +
    settings.interdisciplinarityWeight * interdisciplinaritySimilarity +
    (settings.considerCitations ? 0.2 * citationSimilarity : 0);
  
  return totalSimilarity;
}

/**
 * Berechnet die Ähnlichkeit zwischen den Forschungsthemen zweier Profile
 * @param {Object} profile1 Erstes Forschungsprofil
 * @param {Object} profile2 Zweites Forschungsprofil
 * @returns {number} Themenähnlichkeit zwischen 0 und 1
 */
ion calculateTopicSimilarity(profile1, profile2) {
  const topics1 = profile1.topics || [];
  const topics2 = profile2.topics || [];
  
  if (topics1.length === 0 || topics2.length === 0) {
    return 0;
  }
  
  // Jaccard-Ähnlichkeit für die Themen
  const intersection = topics1.filter(topic => 
    topics2.some(t => t.toLowerCase() === topic.toLowerCase())
  ).length;
  
  const union = new Set([
    ...topics1.map(t => t.toLowerCase()),
    ...topics2.map(t => t.toLowerCase())
  ]).size;
  
  // Basisähnlichkeit auf Grundlage gemeinsamer Themen
  const jaccardSimilarity = intersection / union;
  
  // Berücksichtige Themengewichte, falls verfügbar
  let weightedSimilarity = jaccardSimilarity;
  
  if (profile1.topicWeights && profile2.topicWeights) {
    // Berechne Ähnlichkeit basierend auf den Gewichten der gemeinsamen Themen
    let weightSum = 0;
    let commonWeightSum = 0;
    
    // Finde gemeinsame Themen und summiere ihre Gewichte
    topics1.forEach(topic => {
      const lowerTopic = topic.toLowerCase();
      const matchingTopic = topics2.find(t => t.toLowerCase() === lowerTopic);
      
      if (matchingTopic) {
        const weight1 = profile1.topicWeights[topic] || 0;
        const weight2 = profile2.topicWeights[matchingTopic] || 0;
        
        // Verwende den Durchschnitt der beiden Gewichte
        commonWeightSum += (weight1 + weight2) / 2;
      }
      
      weightSum += profile1.topicWeights[topic] || 0;
    });
    
    // Normalisiere die gewichtete Ähnlichkeit
    if (weightSum > 0) {
      weightedSimilarity = commonWeightSum / weightSum;
    }
  }
  
  // Kombiniere Jaccard-Ähnlichkeit mit gewichteter Ähnlichkeit
  return 0.4 * jaccardSimilarity + 0.6 * weightedSimilarity;
}

/**
 * Berechnet die Ähnlichkeit zwischen den methodischen Fähigkeiten zweier Profile
 * @param {Object} profile1 Erstes Forschungsprofil
 * @param {Object} profile2 Zweites Forschungsprofil
 * @returns {number} Fähigkeitsähnlichkeit zwischen 0 und 1
 */
ion calculateSkillSimilarity(profile1, profile2) {
  const skills1 = profile1.skills || [];
  const skills2 = profile2.skills || [];
  
  if (skills1.length === 0 || skills2.length === 0) {
    return 0;
  }
  
  // Jaccard-Ähnlichkeit für die Fähigkeiten
  const intersection = skills1.filter(skill => 
    skills2.some(s => s.toLowerCase() === skill.toLowerCase())
  ).length;
  
  const union = new Set([
    ...skills1.map(s => s.toLowerCase()),
    ...skills2.map(s => s.toLowerCase())
  ]).size;
  
  return intersection / union;
}

/**
 * Berechnet die Ähnlichkeit zwischen den Kollaborationsmustern zweier Profile
 * @param {Object} profile1 Erstes Forschungsprofil
 * @param {Object} profile2 Zweites Forschungsprofil
 * @returns {number} Kollaborationsähnlichkeit zwischen 0 und 1
 */
export function calculateCollaborationSimilarity(profile1, profile2) {
  const collab1 = profile1.collaborationHistory || { collaborators: [] };
  const collab2 = profile2.collaborationHistory || { collaborators: [] };
  
  // Wenn keine Kollaborationshistorie vorhanden ist, Standardwert zurückgeben
  if (!collab1.collaborators || !collab2.collaborators ||
      collab1.collaborators.length === 0 || collab2.collaborators.length === 0) {
    return 0.5; // Neutraler Wert
  }
  
  // Finde gemeinsame Kollaborateure
  const collaborators1 = collab1.collaborators.map(c => c.name.toLowerCase());
  const collaborators2 = collab2.collaborators.map(c => c.name.toLowerCase());
  
  const commonCollaborators = collaborators1.filter(name => 
    collaborators2.includes(name)
  ).length;
  
  // Berechne Jaccard-Ähnlichkeit
  const union = new Set([...collaborators1, ...collaborators2]).size;
  const jaccardSimilarity = commonCollaborators / union;
  
  // Vergleiche auch die Kollaborationsfrequenz
  const freqSimilarity = 1 - Math.abs(collab1.frequency - collab2.frequency);
  
  // Kombiniere die beiden Werte
  return 0.7 * jaccardSimilarity + 0.3 * freqSimilarity;
}

/**
 * Berechnet die Ähnlichkeit basierend auf der Interdisziplinarität der Profile
 * @param {Object} profile1 Erstes Forschungsprofil
 * @param {Object} profile2 Zweites Forschungsprofil
 * @returns {number} Interdisziplinaritätsähnlichkeit zwischen 0 und 1
 */
ion calculateInterdisciplinaritySimilarity(profile1, profile2) {
  const interdisciplinarity1 = profile1.interdisciplinarity || 0;
  const interdisciplinarity2 = profile2.interdisciplinarity || 0;
  
  // Berechne Ähnlichkeit basierend auf dem Unterschied der Interdisziplinaritätswerte
  const diff = Math.abs(interdisciplinarity1 - interdisciplinarity2);
  
  // Umkehrung: Je geringer der Unterschied, desto höher die Ähnlichkeit
  return 1 - diff;
}

/**
 * Berechnet die Ähnlichkeit basierend auf Zitationsmustern
 * @param {Object} profile1 Erstes Forschungsprofil
 * @param {Object} profile2 Zweites Forschungsprofil
 * @returns {number} Zitationsähnlichkeit zwischen 0 und 1
 */
function calculateCitationSimilarity(profile1, profile2) {
  const citations1 = profile1.citationPatterns || { totalCitations: 0, trend: 'neutral' };
  const citations2 = profile2.citationPatterns || { totalCitations: 0, trend: 'neutral' };
  
  // Ähnlichkeit basierend auf Zitationstrends
  let trendSimilarity = 0.5; // Neutraler Wert
  
  if (citations1.trend === citations2.trend) {
    trendSimilarity = 1.0;
  } else if (
    (citations1.trend === 'steigend' && citations2.trend === 'neutral') ||
    (citations1.trend === 'neutral' && citations2.trend === 'steigend') ||
    (citations1.trend === 'neutral' && citations2.trend === 'fallend') ||
    (citations1.trend === 'fallend' && citations2.trend === 'neutral')
  ) {
    trendSimilarity = 0.75;
  } else {
    // Entgegengesetzte Trends (steigend vs. fallend)
    trendSimilarity = 0.25;
  }
  
  // Vergleiche auch die Gesamtzahl der Zitationen
  // Normalisiere mit logarithmischer Skalierung, um große Unterschiede abzuschwächen
  const maxCitations = Math.max(citations1.totalCitations, citations2.totalCitations);
  const minCitations = Math.min(citations1.totalCitations, citations2.totalCitations);
  
  let citationCountSimilarity = 0;
  if (maxCitations > 0) {
    // Logarithmisches Verhältnis zur Bewertung der Größenordnung, nicht der absoluten Zahlen
    citationCountSimilarity = Math.log(minCitations + 1) / Math.log(maxCitations + 1);
  }
  
  // Kombiniere Trend- und Zitationszahlähnlichkeit
  return 0.6 * trendSimilarity + 0.4 * citationCountSimilarity;
} 