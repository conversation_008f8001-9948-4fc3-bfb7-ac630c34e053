import { logger } from '../utils/logger.js';

/**
 * Service zur Erstellung von Text-Embeddings
 */
export class TextEmbeddingService {
  /**
   * Erstellt eine neue TextEmbeddingService-Instanz
   * @param {Object} options - Konfigurationsoptionen
   * @param {string} options.provider - Embedding-Provider ('openai', 'huggingface', 'cohere', 'local')
   * @param {string} options.model - Zu verwendendes Embedding-Modell
   * @param {Object} options.config - Spezifische Konfiguration für den gewählten Provider
   */
  constructor(options = {}) {
    const {
      provider = 'local',
      model = 'mini',
      config = {}
    } = options;
    
    this.provider = provider;
    this.model = model;
    this.config = config;
    this.client = null;
    this.isInitialized = false;
    
    // Lokales Modell für einfache Implementierung
    this.localModel = null;
    
    this.stats = {
      embeddingsCreated: 0,
      tokensProcessed: 0,
      errors: 0
