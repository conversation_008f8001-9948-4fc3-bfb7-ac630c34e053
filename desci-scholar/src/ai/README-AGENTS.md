# DeSci Scholar - KI-Agenten

Dieses Verzeichnis enthält die KI-Agenten, die in der DeSci-Scholar-Plattform verwendet werden.

## Übersicht

Die KI-Agenten in DeSci Scholar sind spezialisierte Module, die verschiedene Aufgaben im Bereich der dezentralen Wissenschaft automatisieren. Sie verbinden traditionelle wissenschaftliche Prozesse mit Web3-Technologien wie Blockchain und IPFS.

## Verfügbare Agenten

### BaseAgent

Der `BaseAgent` dient als Basisklasse für alle anderen Agenten und stellt grundlegende Funktionalitäten bereit.

**Hauptfunktionen:**
- Logging und Fehlerbehandlung
- Konfigurationsmanagement
- Lebenszyklusverwaltung

### PublicationAgent

Der `PublicationAgent` verwaltet wissenschaftliche Publikationen und ihre Integration in die dezentrale Infrastruktur.

**Hauptfunktionen:**
- Extraktion von Metadaten aus PDFs
- Verknüpfung von Publikationen mit DOIs
- Erstellung von NFTs für Publikationen
- Speicherung von Metadaten in IPFS
- Ana<PERSON><PERSON> von Zitationen und Referenzen

### PatentIntegrationAgent

Der `PatentIntegrationAgent` integriert Patentdaten aus verschiedenen Patentämtern in die DeSci-Scholar-Plattform.

**Hauptfunktionen:**
- Automatisierte Integration von Patentdaten aus USPTO, EPO und WIPO
- Verknüpfung von Patenten mit wissenschaftlichen Publikationen (DOIs)
- Erstellung von Smart Contracts für Patentlizenzierung
- KI-gestützte Analyse von Patentzitationen
- Direkte Verknüpfung von Patent-IDs mit NFTs

**Dezentrale Integration:**
- Speichert Patentmetadaten in IPFS
- Prägt NFTs für Patente auf der Blockchain

### EnhancedPatentIntegrationAgent

Der `EnhancedPatentIntegrationAgent` erweitert den `PatentIntegrationAgent` um fortgeschrittene Analysefunktionen.

**Hauptfunktionen:**
- Alle Funktionen des PatentIntegrationAgent
- Semantische Suche nach ähnlichen Patenten
- Analyse von Patent-Publikations-Beziehungen
- Erstellung von Lizenzverträgen als Smart Contracts
- Berechnung von Einflussmetriken für Patente

### ResearchGraphAgent

Der `ResearchGraphAgent` erstellt und analysiert Graphen wissenschaftlicher Zusammenhänge.

**Hauptfunktionen:**
- Erstellung von Wissensgraphen aus Publikationen und Patenten
- Identifikation von Forschungstrends und -lücken
- Berechnung von Einflussmetriken für Forscher und Institutionen
- Visualisierung von Forschungsnetzwerken

### PeerReviewAgent

Der `PeerReviewAgent` unterstützt den dezentralen Peer-Review-Prozess.

**Hauptfunktionen:**
- Automatische Zuweisung von Gutachtern basierend auf Expertise
- Anonymisierung von Einreichungen
- Verifikation von Gutachten mittels Blockchain
- Tokenisierte Anreize für Gutachter

### FundingAgent

Der `FundingAgent` verwaltet dezentrale Forschungsfinanzierung.

**Hauptfunktionen:**
- Erstellung und Verwaltung von Forschungs-DAOs
- Implementierung von Quadratic Funding für Forschungsprojekte
- Automatisierte Meilenstein-basierte Auszahlungen
- Transparente Nachverfolgung von Forschungsgeldern

## Verwendung

Jeder Agent kann unabhängig instanziiert und verwendet werden. Für die meisten Agenten werden jedoch Dienste wie `DatabaseService`, `BlockchainService` und `IPFSService` benötigt.

Beispiel:

```javascript
import { PatentIntegrationAgent } from './agents/PatentIntegrationAgent.js';
import { DatabaseService } from '../services/DatabaseService.js';
import { BlockchainService } from '../services/BlockchainService.js';
import { IPFSService } from '../services/IPFSService.js';

// Dienste initialisieren
const databaseService = new DatabaseService();
const blockchainService = new BlockchainService();
const ipfsService = new IPFSService();

// Agent erstellen
const patentAgent = new PatentIntegrationAgent({
  databaseService,
  blockchainService,
  ipfsService,
  patentApiConfig: {
    usptoApiKey: process.env.USPTO_API_KEY,
    epoApiKey: process.env.EPO_API_KEY,
    wipoApiKey: process.env.WIPO_API_KEY
  },
  config: {
    energyEfficient: true,
    batchSize: 50
  }
});

// Agent initialisieren
await patentAgent.initialize();

// Agent verwenden
const updateResult = await patentAgent.updatePatentData();
console.log(`Patentdaten aktualisiert: ${updateResult.results.total} neue Patente`);

// NFT für ein Patent erstellen
const nftResult = await patentAgent.createPatentNFT('US12345678');
console.log(`NFT erstellt: ${nftResult.nftId}`);
```

## Erweiterung

Um einen neuen Agenten zu erstellen, sollte die `BaseAgent`-Klasse erweitert werden:

```javascript
import { BaseAgent } from './BaseAgent.js';

export class MyCustomAgent extends BaseAgent {
  constructor(options = {}) {
    super('MyCustomAgent');
    // Initialisierung
  }
  
  async initialize() {
    // Initialisierungslogik
    return true;
  }
  
  // Benutzerdefinierte Methoden
}
```

## Hinweise zur Implementierung

- Alle Agenten sollten asynchrone Methoden verwenden, um die Leistung zu optimieren.
- Fehlerbehandlung sollte über das Logger-System erfolgen.
- Energieeffizienz sollte berücksichtigt werden, insbesondere bei ressourcenintensiven Operationen.
- Blockchain-Interaktionen sollten minimiert werden, um Gas-Kosten zu reduzieren.