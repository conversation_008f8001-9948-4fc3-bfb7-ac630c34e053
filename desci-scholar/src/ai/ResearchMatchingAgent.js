/**
 * ResearchMatchingAgent.js
 * KI-Agent zur Identifizierung potenzieller Forschungskollaborationen und zum Matching von Forschern
 */

import { analyzeResearchInterests } from './utils/researchAnalysis';
import { getSimilarityScore } from './utils/similarityMetrics';

/**
 * Klasse für den KI-Agenten zum Matching von Forschern und Projekten
 */
class ResearchMatchingAgent {
  constructor(config = {}) {
    this.config = {
      similarityThreshold: 0.65,
      maxRecommendations: 10,
      considerCitations: true,
      considerKeywords: true,
      ...config,
    };
    
    this.modelLoaded = false;
    this.model = null;
  }
  
  /**
   * Initialisiert das KI-Modell
   * @returns {Promise<boolean>} Erfolg der Initialisierung
   */
  async initialize() {
    try {
      // In einer realen Implementierung würde hier ein KI-Modell geladen
      // z.B. über TensorFlow.js oder einen API-Aufruf an einen KI-Dienst
      console.log('Initialisiere KI-Matching-Modell...');
      
      // Simuliere das Laden eines Modells
      this.model = {
        name: 'research-collaboration-matcher-v1',
        type: 'transformer',
        ready: true,
      };
      
      this.modelLoaded = true;
      return true;
    } catch (error) {
      console.error('Fehler bei der Initialisierung des KI-Modells:', error);
      return false;
    }
  }
  
  /**
   * Findet geeignete Forscher für eine Zusammenarbeit basierend auf Forschungsinteressen
   * @param {Object} researcher Forscher, für den Kollaborationen gefunden werden sollen
   * @param {Array} researcherPool Liste potenzieller Kollaborateure
   * @returns {Array} Sortierte Liste empfohlener Kollaborateure
   */
  async findCollaborators(researcher, researcherPool) {
    if (!this.modelLoaded) {
      await this.initialize();
    }
    
    if (!researcher || !researcherPool || !Array.isArray(researcherPool)) {
      throw new Error('Ungültige Eingabeparameter für die Kollaborationssuche');
    }
    
    try {
      // Analysiere die Forschungsinteressen des anfragenden Forschers
      const researchProfile = await analyzeResearchInterests(researcher);
      
      // Berechne Ähnlichkeitswerte mit jedem potenziellen Kollaborateur
      const matchResults = await Promise.all(researcherPool.map(async (candidate) => {
        // Überspringe den eigenen Forscher
        if (candidate.id === researcher.id) {
          return null;
        }
        
        const candidateProfile = await analyzeResearchInterests(candidate);
        const similarityScore = await getSimilarityScore(researchProfile, candidateProfile, this.config);
        
        return {
          researcher: candidate,
          score: similarityScore,
          matchReason: this.generateMatchReason(researchProfile, candidateProfile),
        };
      }));
      
      // Filtere null-Werte und sortiere nach Ähnlichkeitswert
      const validMatches = matchResults
        .filter(match => match !== null && match.score >= this.config.similarityThreshold)
        .sort((a, b) => b.score - a.score)
        .slice(0, this.config.maxRecommendations);
      
      return validMatches;
    } catch (error) {
      console.error('Fehler bei der Suche nach Kollaborateuren:', error);
      throw error;
    }
  }
  
  /**
   * Generiert KI-basierte Begründung für ein Match
   * @param {Object} profile1 Forschungsprofil 1
   * @param {Object} profile2 Forschungsprofil 2
   * @returns {string} Begründung für das Match
   */
  generateMatchReason(profile1, profile2) {
    // In einer echten Implementierung würde hier ein KI-Modell verwendet
    // um eine fundierte Begründung zu generieren
    
    // Finde gemeinsame Forschungsthemen
    const commonTopics = profile1.topics.filter(topic => 
      profile2.topics.some(t => t.toLowerCase() === topic.toLowerCase())
    );
    
    // Finde komplementäre Fähigkeiten
    const complementarySkills = profile2.skills.filter(skill => 
      !profile1.skills.some(s => s.toLowerCase() === skill.toLowerCase())
    );
    
    let reason = 'Potenzielle Zusammenarbeit basierend auf ';
    
    if (commonTopics.length > 0) {
      reason += `gemeinsamen Forschungsinteressen (${commonTopics.join(', ')})`;
    }
    
    if (complementarySkills.length > 0) {
      if (commonTopics.length > 0) reason += ' und ';
      reason += `komplementären Fähigkeiten (${complementarySkills.slice(0, 3).join(', ')})`;
    }
    
    return reason;
  }
  
  /**
   * Empfiehlt Projekte für einen Forscher
   * @param {Object} researcher Forscher
   * @param {Array} availableProjects Liste verfügbarer Projekte
   * @returns {Array} Empfohlene Projekte
   */
  async recommendProjects(researcher, availableProjects) {
    if (!this.modelLoaded) {
      await this.initialize();
    }
    
    // Ähnliche Implementierung wie bei findCollaborators, aber für Projekte
    // Dies würde in einer vollständigen Implementierung ergänzt
    
    return availableProjects
      .slice(0, 5)
      .map(project => ({
        project,
        score: Math.random() * 0.5 + 0.5, // Platzhalter
        reason: 'Basierend auf Ihren Forschungsinteressen'
      }));
  }
  
  /**
   * Analysiert Forschungstrends basierend auf Publikationen und Zitaten
   * @param {Array} publications Liste von Publikationen
   * @returns {Object} Trendanalyse
   */
  async analyzeTrends(_publications) {
    if (!this.modelLoaded) {
      await this.initialize();
    }
    
    // Implementierung für Trendanalyse
    // Dies würde in einer vollständigen Implementierung ergänzt
    
    return {
      emergingTopics: ['Quantum Machine Learning', 'Blockchain in Science'],
      decliningTopics: ['Legacy Database Systems'],
      potentialOpportunities: ['Interdisciplinary Research', 'Open Science Collaboration']
    };
  }
}

export default ResearchMatchingAgent; 