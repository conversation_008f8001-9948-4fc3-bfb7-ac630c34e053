/**
 * @fileoverview IP-NFT-Agent für DeSci-Scholar
 * 
 * Dieser Agent implementiert die Funktionalität für IP-NFTs (Intellectual Property NFTs),
 * die es ermöglichen, geistiges Eigentum wie wissenschaftliche Publikationen und Patente
 * als NFTs zu tokenisieren und zu verwalten. Basierend auf dem Molecule-Protokoll für IP-NFTs.
 */

import { BaseAgent } from './index.js';
import { logger } from '../../utils/logger.js';

/**
 * Agent für die Verwaltung von IP-NFTs
 */
export class IPNFTAgent extends BaseAgent {
  /**
   * Erstellt eine neue Instanz des IPNFTAgent
   * @param {Object} options - Konfigurationsoptionen
   * @param {Object} options.blockchainService - Blockchain-Dienst für NFT-Operationen
   * @param {Object} options.ipfsService - IPFS-Dienst für Metadatenspeicherung
   * @param {Object} options.databaseService - Datenbankdienst
   * @param {Object} options.legalService - Rechtsdienst für IP-Vereinbarungen (optional)
   * @param {Object} options.config - Weitere Konfigurationsoptionen
   */
  constructor(options = {}) {
    super('IPNFTAgent');
    
    const {
      blockchainService,
      ipfsService,
      databaseService,
      legalService,
      config = {}
    } = options;
    
    this.blockchainService = blockchainService;
    this.ipfsService = ipfsService;
    this.databaseService = databaseService;
    this.legalService = legalService;
    
    // Konfiguration
    this.config = {
      ipNftContractAddress: config.ipNftContractAddress || process.env.IP_NFT_CONTRACT_ADDRESS,
      ipNftVersion: config.ipNftVersion || 'v2',
      defaultRoyaltyPercentage: config.defaultRoyaltyPercentage || 2.5, // 2.5%
      defaultLicenseType: config.defaultLicenseType || 'CC-BY',
      fractionalizationEnabled: config.fractionalizationEnabled !== undefined ? config.fractionalizationEnabled : true,
      governanceEnabled: config.governanceEnabled !== undefined ? config.governanceEnabled : true,
      ...config
    };
    
    logger.info('IPNFTAgent initialisiert');
  }
  
  /**
   * Initialisiert den Agent
   */
  async initialize() {
    logger.info('Initialisiere IPNFTAgent');
    
    // Prüfe, ob die erforderlichen Dienste verfügbar sind
    if (!this.blockchainService) {
      throw new Error('BlockchainService ist erforderlich');
    }
    
    if (!this.ipfsService) {
      throw new Error('IPFSService ist erforderlich');
    }
    
    if (!this.databaseService) {
      throw new Error('DatabaseService ist erforderlich');
    }
    
    // Prüfe, ob die IP-NFT-Vertragskonfiguration vorhanden ist
    if (!this.config.ipNftContractAddress) {
      throw new Error('IP-NFT-Vertragsadresse ist erforderlich');
    }
    
    // Initialisiere die Datenbanktabellen
    await this.initializeDatabaseTables();
    
    logger.info('IPNFTAgent erfolgreich initialisiert');
  }
  
  /**
   * Initialisiert die erforderlichen Datenbanktabellen
   */
  async initializeDatabaseTables() {
    try {
      // Prüfe, ob die IP-NFT-Tabelle existiert
      const ipNftTableExists = await this.databaseService.tableExists('ip_nfts');
      
      if (!ipNftTableExists) {
        // Erstelle die IP-NFT-Tabelle
        await this.databaseService.query(`
          CREATE TABLE ip_nfts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            token_id VARCHAR(100) NOT NULL,
            contract_address VARCHAR(100) NOT NULL,
            content_type VARCHAR(50) NOT NULL,
            content_id VARCHAR(100) NOT NULL,
            owner_address VARCHAR(100) NOT NULL,
            metadata_uri VARCHAR(255) NOT NULL,
            ipfs_cid VARCHAR(100) NOT NULL,
            license_type VARCHAR(50),
            royalty_percentage FLOAT,
            is_fractionalized BOOLEAN DEFAULT FALSE,
            fraction_token_address VARCHAR(100),
            governance_token_address VARCHAR(100),
            legal_agreement_cid VARCHAR(100),
            transaction_hash VARCHAR(100),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY token_unique (contract_address, token_id),
            UNIQUE KEY content_unique (content_type, content_id)
          )
        `);
        
        logger.info('IP-NFT-Tabelle erstellt');
      }
      
      // Prüfe, ob die IP-NFT-Berechtigungstabelle existiert
      const ipNftPermissionsTableExists = await this.databaseService.tableExists('ip_nft_permissions');
      
      if (!ipNftPermissionsTableExists) {
        // Erstelle die IP-NFT-Berechtigungstabelle
        await this.databaseService.query(`
          CREATE TABLE ip_nft_permissions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            ip_nft_id INT NOT NULL,
            permission_type VARCHAR(50) NOT NULL,
            grantee_address VARCHAR(100) NOT NULL,
            granted_by VARCHAR(100) NOT NULL,
            is_active BOOLEAN DEFAULT TRUE,
            expiration_date TIMESTAMP NULL,
            metadata JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (ip_nft_id) REFERENCES ip_nfts(id) ON DELETE CASCADE,
            UNIQUE KEY permission_unique (ip_nft_id, permission_type, grantee_address)
          )
        `);
        
        logger.info('IP-NFT-Berechtigungstabelle erstellt');
      }
      
      // Prüfe, ob die IP-NFT-Transaktionsverlaufstabelle existiert
      const ipNftHistoryTableExists = await this.databaseService.tableExists('ip_nft_history');
      
      if (!ipNftHistoryTableExists) {
        // Erstelle die IP-NFT-Transaktionsverlaufstabelle
        await this.databaseService.query(`
          CREATE TABLE ip_nft_history (
            id INT AUTO_INCREMENT PRIMARY KEY,
            ip_nft_id INT NOT NULL,
            action_type VARCHAR(50) NOT NULL,
            from_address VARCHAR(100),
            to_address VARCHAR(100),
            transaction_hash VARCHAR(100),
            block_number BIGINT,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            metadata JSON,
            FOREIGN KEY (ip_nft_id) REFERENCES ip_nfts(id) ON DELETE CASCADE
          )
        `);
        
        logger.info('IP-NFT-Transaktionsverlaufstabelle erstellt');
      }
    } catch (error) {
      logger.error('Fehler bei der Initialisierung der Datenbanktabellen', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Erstellt einen IP-NFT für eine wissenschaftliche Publikation
   * 
   * @param {Object} publication - Publikationsdaten
   * @param {Object} options - Optionen für den IP-NFT
   * @returns {Promise<Object>} Ergebnis der IP-NFT-Erstellung
   */
  async createPublicationIPNFT(publication, options = {}) {
    try {
      logger.info(`Erstelle IP-NFT für Publikation mit DOI ${publication.doi}`);
      
      // Prüfe, ob bereits ein IP-NFT für diese Publikation existiert
      const existingIpNft = await this.databaseService.query(
        'SELECT * FROM ip_nfts WHERE content_type = ? AND content_id = ?',
        ['publication', publication.doi]
      );
      
      if (existingIpNft && existingIpNft.length > 0) {
        logger.info(`Publikation mit DOI ${publication.doi} hat bereits einen IP-NFT`);
        
        return {
          success: true,
          doi: publication.doi,
          tokenId: existingIpNft[0].token_id,
          contractAddress: existingIpNft[0].contract_address,
          ownerAddress: existingIpNft[0].owner_address,
          metadataUri: existingIpNft[0].metadata_uri,
          alreadyExists: true
        };
      }
      
      // Extrahiere Optionen
      const {
        ownerAddress = options.creatorAddress || process.env.DEFAULT_NFT_RECIPIENT,
        licenseType = this.config.defaultLicenseType,
        royaltyPercentage = this.config.defaultRoyaltyPercentage,
        legalAgreement = null,
        fractionalize = this.config.fractionalizationEnabled,
        governanceEnabled = this.config.governanceEnabled,
        ipfsCid = null
      } = options;
      
      // Hole oder erstelle die IPFS-CID für die Publikation
      let contentCid = ipfsCid;
      if (!contentCid) {
        // Prüfe, ob die Publikation bereits auf IPFS gespeichert ist
        const ipfsStorage = await this.databaseService.query(
          'SELECT ipfs_cid FROM ipfs_storage WHERE content_type = ? AND content_id = ?',
          ['publication', publication.doi]
        );
        
        if (ipfsStorage && ipfsStorage.length > 0) {
          contentCid = ipfsStorage[0].ipfs_cid;
        } else {
          throw new Error(`Publikation mit DOI ${publication.doi} muss zuerst auf IPFS gespeichert werden`);
        }
      }
      
      // Erstelle die Metadaten für den IP-NFT
      const ipNftMetadata = {
        name: `IP-NFT: ${publication.title}`,
        description: `Intellectual Property NFT for scientific publication: ${publication.title}`,
        image: publication.coverImage || 'https://desci-scholar.org/assets/ip-nft-placeholder.png',
        external_url: `https://desci-scholar.org/publications/${encodeURIComponent(publication.doi)}`,
        animation_url: `https://ipfs.io/ipfs/${contentCid}`,
        attributes: [
          {
            trait_type: 'Content Type',
            value: 'Scientific Publication'
          },
          {
            trait_type: 'DOI',
            value: publication.doi
          },
          {
            trait_type: 'License Type',
            value: licenseType
          },
          {
            trait_type: 'Royalty Percentage',
            value: `${royaltyPercentage}%`
          },
          {
            trait_type: 'Authors',
            value: publication.authors
          },
          {
            trait_type: 'Journal',
            value: publication.journal
          },
          {
            trait_type: 'Publication Date',
            value: publication.publicationDate
          }
        ],
        properties: {
          contentType: 'publication',
          doi: publication.doi,
          ipfs_cid: contentCid,
          licenseType,
          royaltyPercentage,
          authors: publication.authors.split(',').map(author => author.trim()),
          journal: publication.journal,
          publicationDate: publication.publicationDate,
          abstract: publication.abstract,
          keywords: publication.keywords ? publication.keywords.split(',').map(keyword => keyword.trim()) : [],
          citations: publication.citationCount || 0,
          schema: 'desci-scholar-ip-nft-v1'
        }
      };
      
      // Speichere die IP-NFT-Metadaten auf IPFS
      const metadataResult = await this.ipfsService.addJson(ipNftMetadata);
      const metadataUri = `ipfs://${metadataResult.cid}`;
      logger.debug(`IP-NFT-Metadaten auf IPFS gespeichert mit CID ${metadataResult.cid}`);
      
      // Speichere die rechtliche Vereinbarung auf IPFS, falls vorhanden
      let legalAgreementCid = null;
      if (legalAgreement) {
        if (this.legalService) {
          // Verwende den Rechtsdienst, um eine rechtliche Vereinbarung zu erstellen
          const legalResult = await this.legalService.createIPAgreement({
            contentType: 'publication',
            contentId: publication.doi,
            ownerAddress,
            licenseType,
            royaltyPercentage,
            customTerms: legalAgreement.customTerms
          });
          legalAgreementCid = legalResult.cid;
        } else {
          // Speichere die bereitgestellte rechtliche Vereinbarung direkt
          const legalResult = await this.ipfsService.addJson(legalAgreement);
          legalAgreementCid = legalResult.cid;
        }
        logger.debug(`Rechtliche Vereinbarung auf IPFS gespeichert mit CID ${legalAgreementCid}`);
      }
      
      // Präge den IP-NFT
      const mintResult = await this.blockchainService.mintIPNFT(
        this.config.ipNftContractAddress,
        ownerAddress,
        metadataUri,
        {
          royaltyPercentage,
          contentCid,
          legalAgreementCid,
          contentType: 'publication',
          contentId: publication.doi
        }
      );
      
      logger.debug(`IP-NFT geprägt mit Token-ID ${mintResult.tokenId}`);
      
      // Fractionalisiere den IP-NFT, falls gewünscht
      let fractionTokenAddress = null;
      if (fractionalize) {
        const fractionResult = await this.blockchainService.fractionalizeIPNFT(
          this.config.ipNftContractAddress,
          mintResult.tokenId,
          {
            name: `IP-Shares: ${publication.title}`,
            symbol: `IP-${mintResult.tokenId.substring(0, 4)}`,
            initialSupply: options.initialSupply || 1000000,
            ownerAddress
          }
        );
        fractionTokenAddress = fractionResult.tokenAddress;
        logger.debug(`IP-NFT fractionalisiert mit Token-Adresse ${fractionTokenAddress}`);
      }
      
      // Erstelle einen Governance-Token, falls gewünscht
      let governanceTokenAddress = null;
      if (governanceEnabled) {
        const governanceResult = await this.blockchainService.createIPGovernanceToken(
          this.config.ipNftContractAddress,
          mintResult.tokenId,
          {
            name: `IP-Gov: ${publication.title}`,
            symbol: `IPG-${mintResult.tokenId.substring(0, 4)}`,
            initialSupply: options.governanceSupply || 100000,
            ownerAddress
          }
        );
        governanceTokenAddress = governanceResult.tokenAddress;
        logger.debug(`IP-Governance-Token erstellt mit Token-Adresse ${governanceTokenAddress}`);
      }
      
      // Speichere den IP-NFT in der Datenbank
      await this.databaseService.query(
        `INSERT INTO ip_nfts (
          token_id, contract_address, content_type, content_id, owner_address,
          metadata_uri, ipfs_cid, license_type, royalty_percentage,
          is_fractionalized, fraction_token_address, governance_token_address,
          legal_agreement_cid, transaction_hash
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          mintResult.tokenId,
          this.config.ipNftContractAddress,
          'publication',
          publication.doi,
          ownerAddress,
          metadataUri,
          contentCid,
          licenseType,
          royaltyPercentage,
          fractionalize,
          fractionTokenAddress,
          governanceTokenAddress,
          legalAgreementCid,
          mintResult.transactionHash
        ]
      );
      
      // Hole die ID des eingefügten IP-NFT
      const ipNftResult = await this.databaseService.query(
        'SELECT id FROM ip_nfts WHERE token_id = ? AND contract_address = ?',
        [mintResult.tokenId, this.config.ipNftContractAddress]
      );
      
      const ipNftId = ipNftResult[0].id;
      
      // Speichere den Transaktionsverlauf
      await this.databaseService.query(
        `INSERT INTO ip_nft_history (
          ip_nft_id, action_type, from_address, to_address,
          transaction_hash, block_number
        ) VALUES (?, ?, ?, ?, ?, ?)`,
        [
          ipNftId,
          'mint',
          '0x0000000000000000000000000000000000000000',
          ownerAddress,
          mintResult.transactionHash,
          mintResult.blockNumber
        ]
      );
      
      // Aktualisiere die Publikationstabelle mit der NFT-Referenz
      await this.databaseService.query(
        'UPDATE publications SET nft_token_id = ?, nft_contract_address = ? WHERE doi = ?',
        [mintResult.tokenId, this.config.ipNftContractAddress, publication.doi]
      );
      
      logger.info(`IP-NFT für Publikation mit DOI ${publication.doi} erfolgreich erstellt`);
      
      return {
        success: true,
        doi: publication.doi,
        tokenId: mintResult.tokenId,
        contractAddress: this.config.ipNftContractAddress,
        ownerAddress,
        metadataUri,
        ipfsCid: contentCid,
        legalAgreementCid,
        transactionHash: mintResult.transactionHash,
        blockNumber: mintResult.blockNumber,
        fractionTokenAddress,
        governanceTokenAddress
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen des IP-NFT für die Publikation', {
        error: error.message,
        doi: publication.doi
      });
      throw error;
    }
  }
  
  /**
   * Erstellt einen IP-NFT für ein Patent
   * 
   * @param {Object} patent - Patentdaten
   * @param {Object} options - Optionen für den IP-NFT
   * @returns {Promise<Object>} Ergebnis der IP-NFT-Erstellung
   */
  async createPatentIPNFT(patent, options = {}) {
    try {
      logger.info(`Erstelle IP-NFT für Patent ${patent.id}`);
      
      // Prüfe, ob bereits ein IP-NFT für dieses Patent existiert
      const existingIpNft = await this.databaseService.query(
        'SELECT * FROM ip_nfts WHERE content_type = ? AND content_id = ?',
        ['patent', patent.id]
      );
      
      if (existingIpNft && existingIpNft.length > 0) {
        logger.info(`Patent ${patent.id} hat bereits einen IP-NFT`);
        
        return {
          success: true,
          patentId: patent.id,
          tokenId: existingIpNft[0].token_id,
          contractAddress: existingIpNft[0].contract_address,
          ownerAddress: existingIpNft[0].owner_address,
          metadataUri: existingIpNft[0].metadata_uri,
          alreadyExists: true
        };
      }
      
      // Extrahiere Optionen
      const {
        ownerAddress = options.creatorAddress || process.env.DEFAULT_NFT_RECIPIENT,
        licenseType = this.config.defaultLicenseType,
        royaltyPercentage = this.config.defaultRoyaltyPercentage,
        legalAgreement = null,
        fractionalize = this.config.fractionalizationEnabled,
        governanceEnabled = this.config.governanceEnabled,
        ipfsCid = null
      } = options;
      
      // Hole oder erstelle die IPFS-CID für das Patent
      let contentCid = ipfsCid;
      if (!contentCid) {
        // Prüfe, ob das Patent bereits auf IPFS gespeichert ist
        const ipfsStorage = await this.databaseService.query(
          'SELECT ipfs_cid FROM ipfs_storage WHERE content_type = ? AND content_id = ?',
          ['patent', patent.id]
        );
        
        if (ipfsStorage && ipfsStorage.length > 0) {
          contentCid = ipfsStorage[0].ipfs_cid;
        } else {
          throw new Error(`Patent ${patent.id} muss zuerst auf IPFS gespeichert werden`);
        }
      }
      
      // Erstelle die Metadaten für den IP-NFT
      const ipNftMetadata = {
        name: `IP-NFT: ${patent.title}`,
        description: `Intellectual Property NFT for patent: ${patent.title}`,
        image: patent.image || 'https://desci-scholar.org/assets/ip-nft-patent-placeholder.png',
        external_url: `https://desci-scholar.org/patents/${encodeURIComponent(patent.id)}`,
        animation_url: `https://ipfs.io/ipfs/${contentCid}`,
        attributes: [
          {
            trait_type: 'Content Type',
            value: 'Patent'
          },
          {
            trait_type: 'Patent ID',
            value: patent.id
          },
          {
            trait_type: 'Patent Office',
            value: patent.patentOffice
          },
          {
            trait_type: 'License Type',
            value: licenseType
          },
          {
            trait_type: 'Royalty Percentage',
            value: `${royaltyPercentage}%`
          },
          {
            trait_type: 'Inventors',
            value: patent.inventors
          },
          {
            trait_type: 'Assignees',
            value: patent.assignees
          },
          {
            trait_type: 'Filing Date',
            value: patent.filingDate
          },
          {
            trait_type: 'Grant Date',
            value: patent.grantDate
          }
        ],
        properties: {
          contentType: 'patent',
          patentId: patent.id,
          ipfs_cid: contentCid,
          licenseType,
          royaltyPercentage,
          inventors: patent.inventors.split(',').map(inventor => inventor.trim()),
          assignees: patent.assignees.split(',').map(assignee => assignee.trim()),
          patentOffice: patent.patentOffice,
          patentType: patent.patentType,
          filingDate: patent.filingDate,
          grantDate: patent.grantDate,
          abstract: patent.abstract,
          classificationCodes: patent.classificationCodes ? patent.classificationCodes.split(',').map(code => code.trim()) : [],
          schema: 'desci-scholar-ip-nft-v1'
        }
      };
      
      // Speichere die IP-NFT-Metadaten auf IPFS
      const metadataResult = await this.ipfsService.addJson(ipNftMetadata);
      const metadataUri = `ipfs://${metadataResult.cid}`;
      logger.debug(`IP-NFT-Metadaten auf IPFS gespeichert mit CID ${metadataResult.cid}`);
      
      // Speichere die rechtliche Vereinbarung auf IPFS, falls vorhanden
      let legalAgreementCid = null;
      if (legalAgreement) {
        if (this.legalService) {
          // Verwende den Rechtsdienst, um eine rechtliche Vereinbarung zu erstellen
          const legalResult = await this.legalService.createIPAgreement({
            contentType: 'patent',
            contentId: patent.id,
            ownerAddress,
            licenseType,
            royaltyPercentage,
            customTerms: legalAgreement.customTerms
          });
          legalAgreementCid = legalResult.cid;
        } else {
          // Speichere die bereitgestellte rechtliche Vereinbarung direkt
          const legalResult = await this.ipfsService.addJson(legalAgreement);
          legalAgreementCid = legalResult.cid;
        }
        logger.debug(`Rechtliche Vereinbarung auf IPFS gespeichert mit CID ${legalAgreementCid}`);
      }
      
      // Präge den IP-NFT
      const mintResult = await this.blockchainService.mintIPNFT(
        this.config.ipNftContractAddress,
        ownerAddress,
        metadataUri,
        {
          royaltyPercentage,
          contentCid,
          legalAgreementCid,
          contentType: 'patent',
          contentId: patent.id
        }
      );
      
      logger.debug(`IP-NFT geprägt mit Token-ID ${mintResult.tokenId}`);
      
      // Fractionalisiere den IP-NFT, falls gewünscht
      let fractionTokenAddress = null;
      if (fractionalize) {
        const fractionResult = await this.blockchainService.fractionalizeIPNFT(
          this.config.ipNftContractAddress,
          mintResult.tokenId,
          {
            name: `IP-Shares: ${patent.title}`,
            symbol: `IP-${mintResult.tokenId.substring(0, 4)}`,
            initialSupply: options.initialSupply || 1000000,
            ownerAddress
          }
        );
        fractionTokenAddress = fractionResult.tokenAddress;
        logger.debug(`IP-NFT fractionalisiert mit Token-Adresse ${fractionTokenAddress}`);
      }
      
      // Erstelle einen Governance-Token, falls gewünscht
      let governanceTokenAddress = null;
      if (governanceEnabled) {
        const governanceResult = await this.blockchainService.createIPGovernanceToken(
          this.config.ipNftContractAddress,
          mintResult.tokenId,
          {
            name: `IP-Gov: ${patent.title}`,
            symbol: `IPG-${mintResult.tokenId.substring(0, 4)}`,
            initialSupply: options.governanceSupply || 100000,
            ownerAddress
          }
        );
        governanceTokenAddress = governanceResult.tokenAddress;
        logger.debug(`IP-Governance-Token erstellt mit Token-Adresse ${governanceTokenAddress}`);
      }
      
      // Speichere den IP-NFT in der Datenbank
      await this.databaseService.query(
        `INSERT INTO ip_nfts (
          token_id, contract_address, content_type, content_id, owner_address,
          metadata_uri, ipfs_cid, license_type, royalty_percentage,
          is_fractionalized, fraction_token_address, governance_token_address,
          legal_agreement_cid, transaction_hash
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          mintResult.tokenId,
          this.config.ipNftContractAddress,
          'patent',
          patent.id,
          ownerAddress,
          metadataUri,
          contentCid,
          licenseType,
          royaltyPercentage,
          fractionalize,
          fractionTokenAddress,
          governanceTokenAddress,
          legalAgreementCid,
          mintResult.transactionHash
        ]
      );
      
      // Hole die ID des eingefügten IP-NFT
      const ipNftResult = await this.databaseService.query(
        'SELECT id FROM ip_nfts WHERE token_id = ? AND contract_address = ?',
        [mintResult.tokenId, this.config.ipNftContractAddress]
      );
      
      const ipNftId = ipNftResult[0].id;
      
      // Speichere den Transaktionsverlauf
      await this.databaseService.query(
        `INSERT INTO ip_nft_history (
          ip_nft_id, action_type, from_address, to_address,
          transaction_hash, block_number
        ) VALUES (?, ?, ?, ?, ?, ?)`,
        [
          ipNftId,
          'mint',
          '0x0000000000000000000000000000000000000000',
          ownerAddress,
          mintResult.transactionHash,
          mintResult.blockNumber
        ]
      );
      
      // Aktualisiere die Patenttabelle mit der NFT-Referenz
      await this.databaseService.query(
        'UPDATE patents SET nft_token_id = ?, nft_contract_address = ? WHERE id = ?',
        [mintResult.tokenId, this.config.ipNftContractAddress, patent.id]
      );
      
      logger.info(`IP-NFT für Patent ${patent.id} erfolgreich erstellt`);
      
      return {
        success: true,
        patentId: patent.id,
        tokenId: mintResult.tokenId,
        contractAddress: this.config.ipNftContractAddress,
        ownerAddress,
        metadataUri,
        ipfsCid: contentCid,
        legalAgreementCid,
        transactionHash: mintResult.transactionHash,
        blockNumber: mintResult.blockNumber,
        fractionTokenAddress,
        governanceTokenAddress
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen des IP-NFT für das Patent', {
        error: error.message,
        patentId: patent.id
      });
      throw error;
    }
  }
  
  /**
   * Gewährt Berechtigungen für einen IP-NFT
   * 
   * @param {string} tokenId - Token-ID des IP-NFT
   * @param {string} permissionType - Art der Berechtigung (z.B. 'read', 'use', 'modify', 'commercialize')
   * @param {string} granteeAddress - Adresse des Berechtigungsempfängers
   * @param {Object} options - Weitere Optionen
   * @returns {Promise<Object>} Ergebnis der Berechtigungserteilung
   */
  async grantPermission(tokenId, permissionType, granteeAddress, options = {}) {
    try {
      logger.info(`Gewähre Berechtigung ${permissionType} für IP-NFT ${tokenId} an ${granteeAddress}`);
      
      // Prüfe, ob der IP-NFT existiert
      const ipNft = await this.databaseService.query(
        'SELECT * FROM ip_nfts WHERE token_id = ? AND contract_address = ?',
        [tokenId, this.config.ipNftContractAddress]
      );
      
      if (!ipNft || ipNft.length === 0) {
        throw new Error(`IP-NFT mit Token-ID ${tokenId} nicht gefunden`);
      }
      
      const ipNftId = ipNft[0].id;
      const ownerAddress = ipNft[0].owner_address;
      
      // Prüfe, ob der Aufrufer der Eigentümer des IP-NFT ist
      if (options.callerAddress && options.callerAddress !== ownerAddress) {
        throw new Error('Nur der Eigentümer kann Berechtigungen gewähren');
      }
      
      // Prüfe, ob die Berechtigung bereits existiert
      const existingPermission = await this.databaseService.query(
        'SELECT * FROM ip_nft_permissions WHERE ip_nft_id = ? AND permission_type = ? AND grantee_address = ?',
        [ipNftId, permissionType, granteeAddress]
      );
      
      if (existingPermission && existingPermission.length > 0) {
        // Aktualisiere die bestehende Berechtigung
        await this.databaseService.query(
          'UPDATE ip_nft_permissions SET is_active = TRUE, expiration_date = ?, metadata = ? WHERE id = ?',
          [
            options.expirationDate || null,
            JSON.stringify(options.metadata || {}),
            existingPermission[0].id
          ]
        );
        
        logger.info(`Berechtigung ${permissionType} für IP-NFT ${tokenId} an ${granteeAddress} aktualisiert`);
      } else {
        // Füge eine neue Berechtigung hinzu
        await this.databaseService.query(
          `INSERT INTO ip_nft_permissions (
            ip_nft_id, permission_type, grantee_address, granted_by,
            is_active, expiration_date, metadata
          ) VALUES (?, ?, ?, ?, TRUE, ?, ?)`,
          [
            ipNftId,
            permissionType,
            granteeAddress,
            options.callerAddress || ownerAddress,
            options.expirationDate || null,
            JSON.stringify(options.metadata || {})
          ]
        );
        
        logger.info(`Berechtigung ${permissionType} für IP-NFT ${tokenId} an ${granteeAddress} hinzugefügt`);
      }
      
      // Registriere die Berechtigung auf der Blockchain, falls gewünscht
      if (options.registerOnChain) {
        const onChainResult = await this.blockchainService.registerIPNFTPermission(
          this.config.ipNftContractAddress,
          tokenId,
          granteeAddress,
          permissionType,
          {
            expirationDate: options.expirationDate,
            metadata: options.metadata
          }
        );
        
        logger.debug(`Berechtigung auf der Blockchain registriert mit Transaktion ${onChainResult.transactionHash}`);
        
        return {
          success: true,
          tokenId,
          permissionType,
          granteeAddress,
          transactionHash: onChainResult.transactionHash
        };
      }
      
      return {
        success: true,
        tokenId,
        permissionType,
        granteeAddress
      };
    } catch (error) {
      logger.error('Fehler beim Gewähren der Berechtigung', {
        error: error.message,
        tokenId,
        permissionType,
        granteeAddress
      });
      throw error;
    }
  }
  
  /**
   * Widerruft Berechtigungen für einen IP-NFT
   * 
   * @param {string} tokenId - Token-ID des IP-NFT
   * @param {string} permissionType - Art der Berechtigung
   * @param {string} granteeAddress - Adresse des Berechtigungsempfängers
   * @param {Object} options - Weitere Optionen
   * @returns {Promise<Object>} Ergebnis des Berechtigungswiderrufs
   */
  async revokePermission(tokenId, permissionType, granteeAddress, options = {}) {
    try {
      logger.info(`Widerrufe Berechtigung ${permissionType} für IP-NFT ${tokenId} von ${granteeAddress}`);
      
      // Prüfe, ob der IP-NFT existiert
      const ipNft = await this.databaseService.query(
        'SELECT * FROM ip_nfts WHERE token_id = ? AND contract_address = ?',
        [tokenId, this.config.ipNftContractAddress]
      );
      
      if (!ipNft || ipNft.length === 0) {
        throw new Error(`IP-NFT mit Token-ID ${tokenId} nicht gefunden`);
      }
      
      const ipNftId = ipNft[0].id;
      const ownerAddress = ipNft[0].owner_address;
      
      // Prüfe, ob der Aufrufer der Eigentümer des IP-NFT ist
      if (options.callerAddress && options.callerAddress !== ownerAddress) {
        throw new Error('Nur der Eigentümer kann Berechtigungen widerrufen');
      }
      
      // Prüfe, ob die Berechtigung existiert
      const existingPermission = await this.databaseService.query(
        'SELECT * FROM ip_nft_permissions WHERE ip_nft_id = ? AND permission_type = ? AND grantee_address = ?',
        [ipNftId, permissionType, granteeAddress]
      );
      
      if (!existingPermission || existingPermission.length === 0) {
        throw new Error(`Berechtigung ${permissionType} für IP-NFT ${tokenId} an ${granteeAddress} nicht gefunden`);
      }
      
      // Deaktiviere die Berechtigung
      await this.databaseService.query(
        'UPDATE ip_nft_permissions SET is_active = FALSE, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [existingPermission[0].id]
      );
      
      logger.info(`Berechtigung ${permissionType} für IP-NFT ${tokenId} von ${granteeAddress} widerrufen`);
      
      // Widerrufe die Berechtigung auf der Blockchain, falls gewünscht
      if (options.registerOnChain) {
        const onChainResult = await this.blockchainService.revokeIPNFTPermission(
          this.config.ipNftContractAddress,
          tokenId,
          granteeAddress,
          permissionType
        );
        
        logger.debug(`Berechtigung auf der Blockchain widerrufen mit Transaktion ${onChainResult.transactionHash}`);
        
        return {
          success: true,
          tokenId,
          permissionType,
          granteeAddress,
          transactionHash: onChainResult.transactionHash
        };
      }
      
      return {
        success: true,
        tokenId,
        permissionType,
        granteeAddress
      };
    } catch (error) {
      logger.error('Fehler beim Widerrufen der Berechtigung', {
        error: error.message,
        tokenId,
        permissionType,
        granteeAddress
      });
      throw error;
    }
  }
  
  /**
   * Holt Informationen zu einem IP-NFT
   * 
   * @param {string} tokenId - Token-ID des IP-NFT
   * @returns {Promise<Object>} IP-NFT-Informationen
   */
  async getIPNFTInfo(tokenId) {
    try {
      logger.info(`Hole Informationen für IP-NFT ${tokenId}`);
      
      // Hole den IP-NFT aus der Datenbank
      const ipNft = await this.databaseService.query(
        'SELECT * FROM ip_nfts WHERE token_id = ? AND contract_address = ?',
        [tokenId, this.config.ipNftContractAddress]
      );
      
      if (!ipNft || ipNft.length === 0) {
        throw new Error(`IP-NFT mit Token-ID ${tokenId} nicht gefunden`);
      }
      
      const ipNftData = ipNft[0];
      
      // Hole die Berechtigungen für den IP-NFT
      const permissions = await this.databaseService.query(
        'SELECT * FROM ip_nft_permissions WHERE ip_nft_id = ? AND is_active = TRUE',
        [ipNftData.id]
      );
      
      // Hole den Transaktionsverlauf für den IP-NFT
      const history = await this.databaseService.query(
        'SELECT * FROM ip_nft_history WHERE ip_nft_id = ? ORDER BY timestamp DESC',
        [ipNftData.id]
      );
      
      // Hole die Metadaten von IPFS
      const metadataUri = ipNftData.metadata_uri;
      let metadata = null;
      
      if (metadataUri && metadataUri.startsWith('ipfs://')) {
        const cid = metadataUri.replace('ipfs://', '');
        try {
          metadata = await this.ipfsService.getJson(cid);
        } catch (error) {
          logger.warn(`Fehler beim Abrufen der Metadaten von IPFS: ${error.message}`);
        }
      }
      
      // Hole Informationen zum Inhalt
      let contentInfo = null;
      
      if (ipNftData.content_type === 'publication') {
        const publications = await this.databaseService.query(
          'SELECT * FROM publications WHERE doi = ?',
          [ipNftData.content_id]
        );
        
        if (publications && publications.length > 0) {
          contentInfo = publications[0];
        }
      } else if (ipNftData.content_type === 'patent') {
        const patents = await this.databaseService.query(
          'SELECT * FROM patents WHERE id = ?',
          [ipNftData.content_id]
        );
        
        if (patents && patents.length > 0) {
          contentInfo = patents[0];
        }
      }
      
      // Erstelle die Antwort
      const result = {
        tokenId: ipNftData.token_id,
        contractAddress: ipNftData.contract_address,
        contentType: ipNftData.content_type,
        contentId: ipNftData.content_id,
        ownerAddress: ipNftData.owner_address,
        metadataUri: ipNftData.metadata_uri,
        ipfsCid: ipNftData.ipfs_cid,
        licenseType: ipNftData.license_type,
        royaltyPercentage: ipNftData.royalty_percentage,
        isFragmentalized: ipNftData.is_fractionalized,
        fractionTokenAddress: ipNftData.fraction_token_address,
        governanceTokenAddress: ipNftData.governance_token_address,
        legalAgreementCid: ipNftData.legal_agreement_cid,
        createdAt: ipNftData.created_at,
        updatedAt: ipNftData.updated_at,
        permissions: permissions.map(p => ({
          permissionType: p.permission_type,
          granteeAddress: p.grantee_address,
          grantedBy: p.granted_by,
          isActive: p.is_active,
          expirationDate: p.expiration_date,
          metadata: p.metadata ? JSON.parse(p.metadata) : null,
          createdAt: p.created_at,
          updatedAt: p.updated_at
        })),
        history: history.map(h => ({
          actionType: h.action_type,
          fromAddress: h.from_address,
          toAddress: h.to_address,
          transactionHash: h.transaction_hash,
          blockNumber: h.block_number,
          timestamp: h.timestamp,
          metadata: h.metadata ? JSON.parse(h.metadata) : null
        })),
        metadata,
        contentInfo
      };
      
      logger.info(`Informationen für IP-NFT ${tokenId} erfolgreich abgerufen`);
      
      return {
        success: true,
        ipNft: result
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen der IP-NFT-Informationen', {
        error: error.message,
        tokenId
      });
      throw error;
    }
  }
}/**
 * @fileoverview IP-NFT-Agent für DeSci-Scholar
 * 
 * Dieser Agent implementiert die Funktionalität für IP-NFTs (Intellectual Property NFTs),
 * die es ermöglichen, geistiges Eigentum wie wissenschaftliche Publikationen und Patente
 * als NFTs zu tokenisieren und zu verwalten. Basierend auf dem Molecule-Protokoll für IP-NFTs.
 */

import { BaseAgent } from './index.js';
import { logger } from '../../utils/logger.js';

/**
 * Agent für die Verwaltung von IP-NFTs
 */
export class IPNFTAgent extends BaseAgent {
  /**
   * Erstellt eine neue Instanz des IPNFTAgent
   * @param {Object} options - Konfigurationsoptionen
   * @param {Object} options.blockchainService - Blockchain-Dienst für NFT-Operationen
   * @param {Object} options.ipfsService - IPFS-Dienst für Metadatenspeicherung
   * @param {Object} options.databaseService - Datenbankdienst
   * @param {Object} options.legalService - Rechtsdienst für IP-Vereinbarungen (optional)
   * @param {Object} options.config - Weitere Konfigurationsoptionen
   */
  constructor(options = {}) {
    super('IPNFTAgent');
    
    const {
      blockchainService,
      ipfsService,
      databaseService,
      legalService,
      config = {}
    } = options;
    
    this.blockchainService = blockchainService;
    this.ipfsService = ipfsService;
    this.databaseService = databaseService;
    this.legalService = legalService;
    
    // Konfiguration
    this.config = {
      ipNftContractAddress: config.ipNftContractAddress || process.env.IP_NFT_CONTRACT_ADDRESS,
      ipNftVersion: config.ipNftVersion || 'v2',
      defaultRoyaltyPercentage: config.defaultRoyaltyPercentage || 2.5, // 2.5%
      defaultLicenseType: config.defaultLicenseType || 'CC-BY',
      fractionalizationEnabled: config.fractionalizationEnabled !== undefined ? config.fractionalizationEnabled : true,
      governanceEnabled: config.governanceEnabled !== undefined ? config.governanceEnabled : true,
      ...config
    };
    
    logger.info('IPNFTAgent initialisiert');
  }
  
  /**
   * Initialisiert den Agent
   */
  async initialize() {
    logger.info('Initialisiere IPNFTAgent');
    
    // Prüfe, ob die erforderlichen Dienste verfügbar sind
    if (!this.blockchainService) {
      throw new Error('BlockchainService ist erforderlich');
    }
    
    if (!this.ipfsService) {
      throw new Error('IPFSService ist erforderlich');
    }
    
    if (!this.databaseService) {
      throw new Error('DatabaseService ist erforderlich');
    }
    
    // Prüfe, ob die IP-NFT-Vertragskonfiguration vorhanden ist
    if (!this.config.ipNftContractAddress) {
      throw new Error('IP-NFT-Vertragsadresse ist erforderlich');
    }
    
    // Initialisiere die Datenbanktabellen
    await this.initializeDatabaseTables();
    
    logger.info('IPNFTAgent erfolgreich initialisiert');
  }
  
  /**
   * Initialisiert die erforderlichen Datenbanktabellen
   */
  async initializeDatabaseTables() {
    try {
      // Prüfe, ob die IP-NFT-Tabelle existiert
      const ipNftTableExists = await this.databaseService.tableExists('ip_nfts');
      
      if (!ipNftTableExists) {
        // Erstelle die IP-NFT-Tabelle
        await this.databaseService.query(`
          CREATE TABLE ip_nfts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            token_id VARCHAR(100) NOT NULL,
            contract_address VARCHAR(100) NOT NULL,
            content_type VARCHAR(50) NOT NULL,
            content_id VARCHAR(100) NOT NULL,
            owner_address VARCHAR(100) NOT NULL,
            metadata_uri VARCHAR(255) NOT NULL,
            ipfs_cid VARCHAR(100) NOT NULL,
            license_type VARCHAR(50),
            royalty_percentage FLOAT,
            is_fractionalized BOOLEAN DEFAULT FALSE,
            fraction_token_address VARCHAR(100),
            governance_token_address VARCHAR(100),
            legal_agreement_cid VARCHAR(100),
            transaction_hash VARCHAR(100),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY token_unique (contract_address, token_id),
            UNIQUE KEY content_unique (content_type, content_id)
          )
        `);
        
        logger.info('IP-NFT-Tabelle erstellt');
      }
      
      // Prüfe, ob die IP-NFT-Berechtigungstabelle existiert
      const ipNftPermissionsTableExists = await this.databaseService.tableExists('ip_nft_permissions');
      
      if (!ipNftPermissionsTableExists) {
        // Erstelle die IP-NFT-Berechtigungstabelle
        await this.databaseService.query(`
          CREATE TABLE ip_nft_permissions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            ip_nft_id INT NOT NULL,
            permission_type VARCHAR(50) NOT NULL,
            grantee_address VARCHAR(100) NOT NULL,
            granted_by VARCHAR(100) NOT NULL,
            is_active BOOLEAN DEFAULT TRUE,
            expiration_date TIMESTAMP NULL,
            metadata JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (ip_nft_id) REFERENCES ip_nfts(id) ON DELETE CASCADE,
            UNIQUE KEY permission_unique (ip_nft_id, permission_type, grantee_address)
          )
        `);
        
        logger.info('IP-NFT-Berechtigungstabelle erstellt');
      }
      
      // Prüfe, ob die IP-NFT-Transaktionsverlaufstabelle existiert
      const ipNftHistoryTableExists = await this.databaseService.tableExists('ip_nft_history');
      
      if (!ipNftHistoryTableExists) {
        // Erstelle die IP-NFT-Transaktionsverlaufstabelle
        await this.databaseService.query(`
          CREATE TABLE ip_nft_history (
            id INT AUTO_INCREMENT PRIMARY KEY,
            ip_nft_id INT NOT NULL,
            action_type VARCHAR(50) NOT NULL,
            from_address VARCHAR(100),
            to_address VARCHAR(100),
            transaction_hash VARCHAR(100),
            block_number BIGINT,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            metadata JSON,
            FOREIGN KEY (ip_nft_id) REFERENCES ip_nfts(id) ON DELETE CASCADE
          )
        `);
        
        logger.info('IP-NFT-Transaktionsverlaufstabelle erstellt');
      }
    } catch (error) {
      logger.error('Fehler bei der Initialisierung der Datenbanktabellen', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Erstellt einen IP-NFT für eine wissenschaftliche Publikation
   * 
   * @param {Object} publication - Publikationsdaten
   * @param {Object} options - Optionen für den IP-NFT
   * @returns {Promise<Object>} Ergebnis der IP-NFT-Erstellung
   */
  async createPublicationIPNFT(publication, options = {}) {
    try {
      logger.info(`Erstelle IP-NFT für Publikation mit DOI ${publication.doi}`);
      
      // Prüfe, ob bereits ein IP-NFT für diese Publikation existiert
      const existingIpNft = await this.databaseService.query(
        'SELECT * FROM ip_nfts WHERE content_type = ? AND content_id = ?',
        ['publication', publication.doi]
      );
      
      if (existingIpNft && existingIpNft.length > 0) {
        logger.info(`Publikation mit DOI ${publication.doi} hat bereits einen IP-NFT`);
        
        return {
          success: true,
          doi: publication.doi,
          tokenId: existingIpNft[0].token_id,
          contractAddress: existingIpNft[0].contract_address,
          ownerAddress: existingIpNft[0].owner_address,
          metadataUri: existingIpNft[0].metadata_uri,
          alreadyExists: true
        };
      }
      
      // Extrahiere Optionen
      const {
        ownerAddress = options.creatorAddress || process.env.DEFAULT_NFT_RECIPIENT,
        licenseType = this.config.defaultLicenseType,
        royaltyPercentage = this.config.defaultRoyaltyPercentage,
        legalAgreement = null,
        fractionalize = this.config.fractionalizationEnabled,
        governanceEnabled = this.config.governanceEnabled,
        ipfsCid = null
      } = options;
      
      // Hole oder erstelle die IPFS-CID für die Publikation
      let contentCid = ipfsCid;
      if (!contentCid) {
        // Prüfe, ob die Publikation bereits auf IPFS gespeichert ist
        const ipfsStorage = await this.databaseService.query(
          'SELECT ipfs_cid FROM ipfs_storage WHERE content_type = ? AND content_id = ?',
          ['publication', publication.doi]
        );
        
        if (ipfsStorage && ipfsStorage.length > 0) {
          contentCid = ipfsStorage[0].ipfs_cid;
        } else {
          throw new Error(`Publikation mit DOI ${publication.doi} muss zuerst auf IPFS gespeichert werden`);
        }
      }
      
      // Erstelle die Metadaten für den IP-NFT
      const ipNftMetadata = {
        name: `IP-NFT: ${publication.title}`,
        description: `Intellectual Property NFT for scientific publication: ${publication.title}`,
        image: publication.coverImage || 'https://desci-scholar.org/assets/ip-nft-placeholder.png',
        external_url: `https://desci-scholar.org/publications/${encodeURIComponent(publication.doi)}`,
        animation_url: `https://ipfs.io/ipfs/${contentCid}`,
        attributes: [
          {
            trait_type: 'Content Type',
            value: 'Scientific Publication'
          },
          {
            trait_type: 'DOI',
            value: publication.doi
          },
          {
            trait_type: 'License Type',
            value: licenseType
          },
          {
            trait_type: 'Royalty Percentage',
            value: `${royaltyPercentage}%`
          },
          {
            trait_type: 'Authors',
            value: publication.authors
          },
          {
            trait_type: 'Journal',
            value: publication.journal
          },
          {
            trait_type: 'Publication Date',
            value: publication.publicationDate
          }
        ],
        properties: {
          contentType: 'publication',
          doi: publication.doi,
          ipfs_cid: contentCid,
          licenseType,
          royaltyPercentage,
          authors: publication.authors.split(',').map(author => author.trim()),
          journal: publication.journal,
          publicationDate: publication.publicationDate,
          abstract: publication.abstract,
          keywords: publication.keywords ? publication.keywords.split(',').map(keyword => keyword.trim()) : [],
          citations: publication.citationCount || 0,
          schema: 'desci-scholar-ip-nft-v1'
        }
      };
      
      // Speichere die IP-NFT-Metadaten auf IPFS
      const metadataResult = await this.ipfsService.addJson(ipNftMetadata);
      const metadataUri = `ipfs://${metadataResult.cid}`;
      logger.debug(`IP-NFT-Metadaten auf IPFS gespeichert mit CID ${metadataResult.cid}`);
      
      // Speichere die rechtliche Vereinbarung auf IPFS, falls vorhanden
      let legalAgreementCid = null;
      if (legalAgreement) {
        if (this.legalService) {
          // Verwende den Rechtsdienst, um eine rechtliche Vereinbarung zu erstellen
          const legalResult = await this.legalService.createIPAgreement({
            contentType: 'publication',
            contentId: publication.doi,
            ownerAddress,
            licenseType,
            royaltyPercentage,
            customTerms: legalAgreement.customTerms
          });
          legalAgreementCid = legalResult.cid;
        } else {
          // Speichere die bereitgestellte rechtliche Vereinbarung direkt
          const legalResult = await this.ipfsService.addJson(legalAgreement);
          legalAgreementCid = legalResult.cid;
        }
        logger.debug(`Rechtliche Vereinbarung auf IPFS gespeichert mit CID ${legalAgreementCid}`);
      }
      
      // Präge den IP-NFT
      const mintResult = await this.blockchainService.mintIPNFT(
        this.config.ipNftContractAddress,
        ownerAddress,
        metadataUri,
        {
          royaltyPercentage,
          contentCid,
          legalAgreementCid,
          contentType: 'publication',
          contentId: publication.doi
        }
      );
      
      logger.debug(`IP-NFT geprägt mit Token-ID ${mintResult.tokenId}`);
      
      // Fractionalisiere den IP-NFT, falls gewünscht
      let fractionTokenAddress = null;
      if (fractionalize) {
        const fractionResult = await this.blockchainService.fractionalizeIPNFT(
          this.config.ipNftContractAddress,
          mintResult.tokenId,
          {
            name: `IP-Shares: ${publication.title}`,
            symbol: `IP-${mintResult.tokenId.substring(0, 4)}`,
            initialSupply: options.initialSupply || 1000000,
            ownerAddress
          }
        );
        fractionTokenAddress = fractionResult.tokenAddress;
        logger.debug(`IP-NFT fractionalisiert mit Token-Adresse ${fractionTokenAddress}`);
      }
      
      // Erstelle einen Governance-Token, falls gewünscht
      let governanceTokenAddress = null;
      if (governanceEnabled) {
        const governanceResult = await this.blockchainService.createIPGovernanceToken(
          this.config.ipNftContractAddress,
          mintResult.tokenId,
          {
            name: `IP-Gov: ${publication.title}`,
            symbol: `IPG-${mintResult.tokenId.substring(0, 4)}`,
            initialSupply: options.governanceSupply || 100000,
            ownerAddress
          }
        );
        governanceTokenAddress = governanceResult.tokenAddress;
        logger.debug(`IP-Governance-Token erstellt mit Token-Adresse ${governanceTokenAddress}`);
      }
      
      // Speichere den IP-NFT in der Datenbank
      await this.databaseService.query(
        `INSERT INTO ip_nfts (
          token_id, contract_address, content_type, content_id, owner_address,
          metadata_uri, ipfs_cid, license_type, royalty_percentage,
          is_fractionalized, fraction_token_address, governance_token_address,
          legal_agreement_cid, transaction_hash
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          mintResult.tokenId,
          this.config.ipNftContractAddress,
          'publication',
          publication.doi,
          ownerAddress,
          metadataUri,
          contentCid,
          licenseType,
          royaltyPercentage,
          fractionalize,
          fractionTokenAddress,
          governanceTokenAddress,
          legalAgreementCid,
          mintResult.transactionHash
        ]
      );
      
      // Hole die ID des eingefügten IP-NFT
      const ipNftResult = await this.databaseService.query(
        'SELECT id FROM ip_nfts WHERE token_id = ? AND contract_address = ?',
        [mintResult.tokenId, this.config.ipNftContractAddress]
      );
      
      const ipNftId = ipNftResult[0].id;
      
      // Speichere den Transaktionsverlauf
      await this.databaseService.query(
        `INSERT INTO ip_nft_history (
          ip_nft_id, action_type, from_address, to_address,
          transaction_hash, block_number
        ) VALUES (?, ?, ?, ?, ?, ?)`,
        [
          ipNftId,
          'mint',
          '0x0000000000000000000000000000000000000000',
          ownerAddress,
          mintResult.transactionHash,
          mintResult.blockNumber
        ]
      );
      
      // Aktualisiere die Publikationstabelle mit der NFT-Referenz
      await this.databaseService.query(
        'UPDATE publications SET nft_token_id = ?, nft_contract_address = ? WHERE doi = ?',
        [mintResult.tokenId, this.config.ipNftContractAddress, publication.doi]
      );
      
      logger.info(`IP-NFT für Publikation mit DOI ${publication.doi} erfolgreich erstellt`);
      
      return {
        success: true,
        doi: publication.doi,
        tokenId: mintResult.tokenId,
        contractAddress: this.config.ipNftContractAddress,
        ownerAddress,
        metadataUri,
        ipfsCid: contentCid,
        legalAgreementCid,
        transactionHash: mintResult.transactionHash,
        blockNumber: mintResult.blockNumber,
        fractionTokenAddress,
        governanceTokenAddress
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen des IP-NFT für die Publikation', {
        error: error.message,
        doi: publication.doi
      });
      throw error;
    }
  }
  
  /**
   * Erstellt einen IP-NFT für ein Patent
   * 
   * @param {Object} patent - Patentdaten
   * @param {Object} options - Optionen für den IP-NFT
   * @returns {Promise<Object>} Ergebnis der IP-NFT-Erstellung
   */
  async createPatentIPNFT(patent, options = {}) {
    try {
      logger.info(`Erstelle IP-NFT für Patent ${patent.id}`);
      
      // Prüfe, ob bereits ein IP-NFT für dieses Patent existiert
      const existingIpNft = await this.databaseService.query(
        'SELECT * FROM ip_nfts WHERE content_type = ? AND content_id = ?',
        ['patent', patent.id]
      );
      
      if (existingIpNft && existingIpNft.length > 0) {
        logger.info(`Patent ${patent.id} hat bereits einen IP-NFT`);
        
        return {
          success: true,
          patentId: patent.id,
          tokenId: existingIpNft[0].token_id,
          contractAddress: existingIpNft[0].contract_address,
          ownerAddress: existingIpNft[0].owner_address,
          metadataUri: existingIpNft[0].metadata_uri,
          alreadyExists: true
        };
      }
      
      // Extrahiere Optionen
      const {
        ownerAddress = options.creatorAddress || process.env.DEFAULT_NFT_RECIPIENT,
        licenseType = this.config.defaultLicenseType,
        royaltyPercentage = this.config.defaultRoyaltyPercentage,
        legalAgreement = null,
        fractionalize = this.config.fractionalizationEnabled,
        governanceEnabled = this.config.governanceEnabled,
        ipfsCid = null
      } = options;
      
      // Hole oder erstelle die IPFS-CID für das Patent
      let contentCid = ipfsCid;
      if (!contentCid) {
        // Prüfe, ob das Patent bereits auf IPFS gespeichert ist
        const ipfsStorage = await this.databaseService.query(
          'SELECT ipfs_cid FROM ipfs_storage WHERE content_type = ? AND content_id = ?',
          ['patent', patent.id]
        );
        
        if (ipfsStorage && ipfsStorage.length > 0) {
          contentCid = ipfsStorage[0].ipfs_cid;
        } else {
          throw new Error(`Patent ${patent.id} muss zuerst auf IPFS gespeichert werden`);
        }
      }
      
      // Erstelle die Metadaten für den IP-NFT
      const ipNftMetadata = {
        name: `IP-NFT: ${patent.title}`,
        description: `Intellectual Property NFT for patent: ${patent.title}`,
        image: patent.image || 'https://desci-scholar.org/assets/ip-nft-patent-placeholder.png',
        external_url: `https://desci-scholar.org/patents/${encodeURIComponent(patent.id)}`,
        animation_url: `https://ipfs.io/ipfs/${contentCid}`,
        attributes: [
          {
            trait_type: 'Content Type',
            value: 'Patent'
          },
          {
            trait_type: 'Patent ID',
            value: patent.id
          },
          {
            trait_type: 'Patent Office',
            value: patent.patentOffice
          },
          {
            trait_type: 'License Type',
            value: licenseType
          },
          {
            trait_type: 'Royalty Percentage',
            value: `${royaltyPercentage}%`
          },
          {
            trait_type: 'Inventors',
            value: patent.inventors
          },
          {
            trait_type: 'Assignees',
            value: patent.assignees
          },
          {
            trait_type: 'Filing Date',
            value: patent.filingDate
          },
          {
            trait_type: 'Grant Date',
            value: patent.grantDate
          }
        ],
        properties: {
          contentType: 'patent',
          patentId: patent.id,
          ipfs_cid: contentCid,
          licenseType,
          royaltyPercentage,
          inventors: patent.inventors.split(',').map(inventor => inventor.trim()),
          assignees: patent.assignees.split(',').map(assignee => assignee.trim()),
          patentOffice: patent.patentOffice,
          patentType: patent.patentType,
          filingDate: patent.filingDate,
          grantDate: patent.grantDate,
          abstract: patent.abstract,
          classificationCodes: patent.classificationCodes ? patent.classificationCodes.split(',').map(code => code.trim()) : [],
          schema: 'desci-scholar-ip-nft-v1'
        }
      };
      
      // Speichere die IP-NFT-Metadaten auf IPFS
      const metadataResult = await this.ipfsService.addJson(ipNftMetadata);
      const metadataUri = `ipfs://${metadataResult.cid}`;
      logger.debug(`IP-NFT-Metadaten auf IPFS gespeichert mit CID ${metadataResult.cid}`);
      
      // Speichere die rechtliche Vereinbarung auf IPFS, falls vorhanden
      let legalAgreementCid = null;
      if (legalAgreement) {
        if (this.legalService) {
          // Verwende den Rechtsdienst, um eine rechtliche Vereinbarung zu erstellen
          const legalResult = await this.legalService.createIPAgreement({
            contentType: 'patent',
            contentId: patent.id,
            ownerAddress,
            licenseType,
            royaltyPercentage,
            customTerms: legalAgreement.customTerms
          });
          legalAgreementCid = legalResult.cid;
        } else {
          // Speichere die bereitgestellte rechtliche Vereinbarung direkt
          const legalResult = await this.ipfsService.addJson(legalAgreement);
          legalAgreementCid = legalResult.cid;
        }
        logger.debug(`Rechtliche Vereinbarung auf IPFS gespeichert mit CID ${legalAgreementCid}`);
      }
      
      // Präge den IP-NFT
      const mintResult = await this.blockchainService.mintIPNFT(
        this.config.ipNftContractAddress,
        ownerAddress,
        metadataUri,
        {
          royaltyPercentage,
          contentCid,
          legalAgreementCid,
          contentType: 'patent',
          contentId: patent.id
        }
      );
      
      logger.debug(`IP-NFT geprägt mit Token-ID ${mintResult.tokenId}`);
      
      // Fractionalisiere den IP-NFT, falls gewünscht
      let fractionTokenAddress = null;
      if (fractionalize) {
        const fractionResult = await this.blockchainService.fractionalizeIPNFT(
          this.config.ipNftContractAddress,
          mintResult.tokenId,
          {
            name: `IP-Shares: ${patent.title}`,
            symbol: `IP-${mintResult.tokenId.substring(0, 4)}`,
            initialSupply: options.initialSupply || 1000000,
            ownerAddress
          }
        );
        fractionTokenAddress = fractionResult.tokenAddress;
        logger.debug(`IP-NFT fractionalisiert mit Token-Adresse ${fractionTokenAddress}`);
      }
      
      // Erstelle einen Governance-Token, falls gewünscht
      let governanceTokenAddress = null;
      if (governanceEnabled) {
        const governanceResult = await this.blockchainService.createIPGovernanceToken(
          this.config.ipNftContractAddress,
          mintResult.tokenId,
          {
            name: `IP-Gov: ${patent.title}`,
            symbol: `IPG-${mintResult.tokenId.substring(0, 4)}`,
            initialSupply: options.governanceSupply || 100000,
            ownerAddress
          }
        );
        governanceTokenAddress = governanceResult.tokenAddress;
        logger.debug(`IP-Governance-Token erstellt mit Token-Adresse ${governanceTokenAddress}`);
      }
      
      // Speichere den IP-NFT in der Datenbank
      await this.databaseService.query(
        `INSERT INTO ip_nfts (
          token_id, contract_address, content_type, content_id, owner_address,
          metadata_uri, ipfs_cid, license_type, royalty_percentage,
          is_fractionalized, fraction_token_address, governance_token_address,
          legal_agreement_cid, transaction_hash
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          mintResult.tokenId,
          this.config.ipNftContractAddress,
          'patent',
          patent.id,
          ownerAddress,
          metadataUri,
          contentCid,
          licenseType,
          royaltyPercentage,
          fractionalize,
          fractionTokenAddress,
          governanceTokenAddress,
          legalAgreementCid,
          mintResult.transactionHash
        ]
      );
      
      // Hole die ID des eingefügten IP-NFT
      const ipNftResult = await this.databaseService.query(
        'SELECT id FROM ip_nfts WHERE token_id = ? AND contract_address = ?',
        [mintResult.tokenId, this.config.ipNftContractAddress]
      );
      
      const ipNftId = ipNftResult[0].id;
      
      // Speichere den Transaktionsverlauf
      await this.databaseService.query(
        `INSERT INTO ip_nft_history (
          ip_nft_id, action_type, from_address, to_address,
          transaction_hash, block_number
        ) VALUES (?, ?, ?, ?, ?, ?)`,
        [
          ipNftId,
          'mint',
          '0x0000000000000000000000000000000000000000',
          ownerAddress,
          mintResult.transactionHash,
          mintResult.blockNumber
        ]
      );
      
      // Aktualisiere die Patenttabelle mit der NFT-Referenz
      await this.databaseService.query(
        'UPDATE patents SET nft_token_id = ?, nft_contract_address = ? WHERE id = ?',
        [mintResult.tokenId, this.config.ipNftContractAddress, patent.id]
      );
      
      logger.info(`IP-NFT für Patent ${patent.id} erfolgreich erstellt`);
      
      return {
        success: true,
        patentId: patent.id,
        tokenId: mintResult.tokenId,
        contractAddress: this.config.ipNftContractAddress,
        ownerAddress,
        metadataUri,
        ipfsCid: contentCid,
        legalAgreementCid,
        transactionHash: mintResult.transactionHash,
        blockNumber: mintResult.blockNumber,
        fractionTokenAddress,
        governanceTokenAddress
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen des IP-NFT für das Patent', {
        error: error.message,
        patentId: patent.id
      });
      throw error;
    }
  }
  
  /**
   * Gewährt Berechtigungen für einen IP-NFT
   * 
   * @param {string} tokenId - Token-ID des IP-NFT
   * @param {string} permissionType - Art der Berechtigung (z.B. 'read', 'use', 'modify', 'commercialize')
   * @param {string} granteeAddress - Adresse des Berechtigungsempfängers
   * @param {Object} options - Weitere Optionen
   * @returns {Promise<Object>} Ergebnis der Berechtigungserteilung
   */
  async grantPermission(tokenId, permissionType, granteeAddress, options = {}) {
    try {
      logger.info(`Gewähre Berechtigung ${permissionType} für IP-NFT ${tokenId} an ${granteeAddress}`);
      
      // Prüfe, ob der IP-NFT existiert
      const ipNft = await this.databaseService.query(
        'SELECT * FROM ip_nfts WHERE token_id = ? AND contract_address = ?',
        [tokenId, this.config.ipNftContractAddress]
      );
      
      if (!ipNft || ipNft.length === 0) {
        throw new Error(`IP-NFT mit Token-ID ${tokenId} nicht gefunden`);
      }
      
      const ipNftId = ipNft[0].id;
      const ownerAddress = ipNft[0].owner_address;
      
      // Prüfe, ob der Aufrufer der Eigentümer des IP-NFT ist
      if (options.callerAddress && options.callerAddress !== ownerAddress) {
        throw new Error('Nur der Eigentümer kann Berechtigungen gewähren');
      }
      
      // Prüfe, ob die Berechtigung bereits existiert
      const existingPermission = await this.databaseService.query(
        'SELECT * FROM ip_nft_permissions WHERE ip_nft_id = ? AND permission_type = ? AND grantee_address = ?',
        [ipNftId, permissionType, granteeAddress]
      );
      
      if (existingPermission && existingPermission.length > 0) {
        // Aktualisiere die bestehende Berechtigung
        await this.databaseService.query(
          'UPDATE ip_nft_permissions SET is_active = TRUE, expiration_date = ?, metadata = ? WHERE id = ?',
          [
            options.expirationDate || null,
            JSON.stringify(options.metadata || {}),
            existingPermission[0].id
          ]
        );
        
        logger.info(`Berechtigung ${permissionType} für IP-NFT ${tokenId} an ${granteeAddress} aktualisiert`);
      } else {
        // Füge eine neue Berechtigung hinzu
        await this.databaseService.query(
          `INSERT INTO ip_nft_permissions (
            ip_nft_id, permission_type, grantee_address, granted_by,
            is_active, expiration_date, metadata
          ) VALUES (?, ?, ?, ?, TRUE, ?, ?)`,
          [
            ipNftId,
            permissionType,
            granteeAddress,
            options.callerAddress || ownerAddress,
            options.expirationDate || null,
            JSON.stringify(options.metadata || {})
          ]
        );
        
        logger.info(`Berechtigung ${permissionType} für IP-NFT ${tokenId} an ${granteeAddress} hinzugefügt`);
      }
      
      // Registriere die Berechtigung auf der Blockchain, falls gewünscht
      if (options.registerOnChain) {
        const onChainResult = await this.blockchainService.registerIPNFTPermission(
          this.config.ipNftContractAddress,
          tokenId,
          granteeAddress,
          permissionType,
          {
            expirationDate: options.expirationDate,
            metadata: options.metadata
          }
        );
        
        logger.debug(`Berechtigung auf der Blockchain registriert mit Transaktion ${onChainResult.transactionHash}`);
        
        return {
          success: true,
          tokenId,
          permissionType,
          granteeAddress,
          transactionHash: onChainResult.transactionHash
        };
      }
      
      return {
        success: true,
        tokenId,
        permissionType,
        granteeAddress
      };
    } catch (error) {
      logger.error('Fehler beim Gewähren der Berechtigung', {
        error: error.message,
        tokenId,
        permissionType,
        granteeAddress
      });
      throw error;
    }
  }
  
  /**
   * Widerruft Berechtigungen für einen IP-NFT
   * 
   * @param {string} tokenId - Token-ID des IP-NFT
   * @param {string} permissionType - Art der Berechtigung
   * @param {string} granteeAddress - Adresse des Berechtigungsempfängers
   * @param {Object} options - Weitere Optionen
   * @returns {Promise<Object>} Ergebnis des Berechtigungswiderrufs
   */
  async revokePermission(tokenId, permissionType, granteeAddress, options = {}) {
    try {
      logger.info(`Widerrufe Berechtigung ${permissionType} für IP-NFT ${tokenId} von ${granteeAddress}`);
      
      // Prüfe, ob der IP-NFT existiert
      const ipNft = await this.databaseService.query(
        'SELECT * FROM ip_nfts WHERE token_id = ? AND contract_address = ?',
        [tokenId, this.config.ipNftContractAddress]
      );
      
      if (!ipNft || ipNft.length === 0) {
        throw new Error(`IP-NFT mit Token-ID ${tokenId} nicht gefunden`);
      }
      
      const ipNftId = ipNft[0].id;
      const ownerAddress = ipNft[0].owner_address;
      
      // Prüfe, ob der Aufrufer der Eigentümer des IP-NFT ist
      if (options.callerAddress && options.callerAddress !== ownerAddress) {
        throw new Error('Nur der Eigentümer kann Berechtigungen widerrufen');
      }
      
      // Prüfe, ob die Berechtigung existiert
      const existingPermission = await this.databaseService.query(
        'SELECT * FROM ip_nft_permissions WHERE ip_nft_id = ? AND permission_type = ? AND grantee_address = ?',
        [ipNftId, permissionType, granteeAddress]
      );
      
      if (!existingPermission || existingPermission.length === 0) {
        throw new Error(`Berechtigung ${permissionType} für IP-NFT ${tokenId} an ${granteeAddress} nicht gefunden`);
      }
      
      // Deaktiviere die Berechtigung
      await this.databaseService.query(
        'UPDATE ip_nft_permissions SET is_active = FALSE, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [existingPermission[0].id]
      );
      
      logger.info(`Berechtigung ${permissionType} für IP-NFT ${tokenId} von ${granteeAddress} widerrufen`);
      
      // Widerrufe die Berechtigung auf der Blockchain, falls gewünscht
      if (options.registerOnChain) {
        const onChainResult = await this.blockchainService.revokeIPNFTPermission(
          this.config.ipNftContractAddress,
          tokenId,
          granteeAddress,
          permissionType
        );
        
        logger.debug(`Berechtigung auf der Blockchain widerrufen mit Transaktion ${onChainResult.transactionHash}`);
        
        return {
          success: true,
          tokenId,
          permissionType,
          granteeAddress,
          transactionHash: onChainResult.transactionHash
        };
      }
      
      return {
        success: true,
        tokenId,
        permissionType,
        granteeAddress
      };
    } catch (error) {
      logger.error('Fehler beim Widerrufen der Berechtigung', {
        error: error.message,
        tokenId,
        permissionType,
        granteeAddress
      });
      throw error;
    }
  }
  
  /**
   * Holt Informationen zu einem IP-NFT
   * 
   * @param {string} tokenId - Token-ID des IP-NFT
   * @returns {Promise<Object>} IP-NFT-Informationen
   */
  async getIPNFTInfo(tokenId) {
    try {
      logger.info(`Hole Informationen für IP-NFT ${tokenId}`);
      
      // Hole den IP-NFT aus der Datenbank
      const ipNft = await this.databaseService.query(
        'SELECT * FROM ip_nfts WHERE token_id = ? AND contract_address = ?',
        [tokenId, this.config.ipNftContractAddress]
      );
      
      if (!ipNft || ipNft.length === 0) {
        throw new Error(`IP-NFT mit Token-ID ${tokenId} nicht gefunden`);
      }
      
      const ipNftData = ipNft[0];
      
      // Hole die Berechtigungen für den IP-NFT
      const permissions = await this.databaseService.query(
        'SELECT * FROM ip_nft_permissions WHERE ip_nft_id = ? AND is_active = TRUE',
        [ipNftData.id]
      );
      
      // Hole den Transaktionsverlauf für den IP-NFT
      const history = await this.databaseService.query(
        'SELECT * FROM ip_nft_history WHERE ip_nft_id = ? ORDER BY timestamp DESC',
        [ipNftData.id]
      );
      
      // Hole die Metadaten von IPFS
      const metadataUri = ipNftData.metadata_uri;
      let metadata = null;
      
      if (metadataUri && metadataUri.startsWith('ipfs://')) {
        const cid = metadataUri.replace('ipfs://', '');
        try {
          metadata = await this.ipfsService.getJson(cid);
        } catch (error) {
          logger.warn(`Fehler beim Abrufen der Metadaten von IPFS: ${error.message}`);
        }
      }
      
      // Hole Informationen zum Inhalt
      let contentInfo = null;
      
      if (ipNftData.content_type === 'publication') {
        const publications = await this.databaseService.query(
          'SELECT * FROM publications WHERE doi = ?',
          [ipNftData.content_id]
        );
        
        if (publications && publications.length > 0) {
          contentInfo = publications[0];
        }
      } else if (ipNftData.content_type === 'patent') {
        const patents = await this.databaseService.query(
          'SELECT * FROM patents WHERE id = ?',
          [ipNftData.content_id]
        );
        
        if (patents && patents.length > 0) {
          contentInfo = patents[0];
        }
      }
      
      // Erstelle die Antwort
      const result = {
        tokenId: ipNftData.token_id,
        contractAddress: ipNftData.contract_address,
        contentType: ipNftData.content_type,
        contentId: ipNftData.content_id,
        ownerAddress: ipNftData.owner_address,
        metadataUri: ipNftData.metadata_uri,
        ipfsCid: ipNftData.ipfs_cid,
        licenseType: ipNftData.license_type,
        royaltyPercentage: ipNftData.royalty_percentage,
        isFragmentalized: ipNftData.is_fractionalized,
        fractionTokenAddress: ipNftData.fraction_token_address,
        governanceTokenAddress: ipNftData.governance_token_address,
        legalAgreementCid: ipNftData.legal_agreement_cid,
        createdAt: ipNftData.created_at,
        updatedAt: ipNftData.updated_at,
        permissions: permissions.map(p => ({
          permissionType: p.permission_type,
          granteeAddress: p.grantee_address,
          grantedBy: p.granted_by,
          isActive: p.is_active,
          expirationDate: p.expiration_date,
          metadata: p.metadata ? JSON.parse(p.metadata) : null,
          createdAt: p.created_at,
          updatedAt: p.updated_at
        })),
        history: history.map(h => ({
          actionType: h.action_type,
          fromAddress: h.from_address,
          toAddress: h.to_address,
          transactionHash: h.transaction_hash,
          blockNumber: h.block_number,
          timestamp: h.timestamp,
          metadata: h.metadata ? JSON.parse(h.metadata) : null
        })),
        metadata,
        contentInfo
      };
      
      logger.info(`Informationen für IP-NFT ${tokenId} erfolgreich abgerufen`);
      
      return {
        success: true,
        ipNft: result
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen der IP-NFT-Informationen', {
        error: error.message,
        tokenId
      });
      throw error;
    }
  }
}/**
 * @fileoverview IP-NFT-Agent für DeSci-Scholar
 * 
 * Dieser Agent implementiert die Funktionalität für IP-NFTs (Intellectual Property NFTs),
 * die es ermöglichen, geistiges Eigentum wie wissenschaftliche Publikationen und Patente
 * als NFTs zu tokenisieren und zu verwalten. Basierend auf dem Molecule-Protokoll für IP-NFTs.
 */

import { BaseAgent } from './index.js';
import { logger } from '../../utils/logger.js';

/**
 * Agent für die Verwaltung von IP-NFTs
 */
export class IPNFTAgent extends BaseAgent {
  /**
   * Erstellt eine neue Instanz des IPNFTAgent
   * @param {Object} options - Konfigurationsoptionen
   * @param {Object} options.blockchainService - Blockchain-Dienst für NFT-Operationen
   * @param {Object} options.ipfsService - IPFS-Dienst für Metadatenspeicherung
   * @param {Object} options.databaseService - Datenbankdienst
   * @param {Object} options.legalService - Rechtsdienst für IP-Vereinbarungen (optional)
   * @param {Object} options.config - Weitere Konfigurationsoptionen
   */
  constructor(options = {}) {
    super('IPNFTAgent');
    
    const {
      blockchainService,
      ipfsService,
      databaseService,
      legalService,
      config = {}
    } = options;
    
    this.blockchainService = blockchainService;
    this.ipfsService = ipfsService;
    this.databaseService = databaseService;
    this.legalService = legalService;
    
    // Konfiguration
    this.config = {
      ipNftContractAddress: config.ipNftContractAddress || process.env.IP_NFT_CONTRACT_ADDRESS,
      ipNftVersion: config.ipNftVersion || 'v2',
      defaultRoyaltyPercentage: config.defaultRoyaltyPercentage || 2.5, // 2.5%
      defaultLicenseType: config.defaultLicenseType || 'CC-BY',
      fractionalizationEnabled: config.fractionalizationEnabled !== undefined ? config.fractionalizationEnabled : true,
      governanceEnabled: config.governanceEnabled !== undefined ? config.governanceEnabled : true,
      ...config
    };
    
    logger.info('IPNFTAgent initialisiert');
  }
  
  /**
   * Initialisiert den Agent
   */
  async initialize() {
    logger.info('Initialisiere IPNFTAgent');
    
    // Prüfe, ob die erforderlichen Dienste verfügbar sind
    if (!this.blockchainService) {
      throw new Error('BlockchainService ist erforderlich');
    }
    
    if (!this.ipfsService) {
      throw new Error('IPFSService ist erforderlich');
    }
    
    if (!this.databaseService) {
      throw new Error('DatabaseService ist erforderlich');
    }
    
    // Prüfe, ob die IP-NFT-Vertragskonfiguration vorhanden ist
    if (!this.config.ipNftContractAddress) {
      throw new Error('IP-NFT-Vertragsadresse ist erforderlich');
    }
    
    // Initialisiere die Datenbanktabellen
    await this.initializeDatabaseTables();
    
    logger.info('IPNFTAgent erfolgreich initialisiert');
  }
  
  /**
   * Initialisiert die erforderlichen Datenbanktabellen
   */
  async initializeDatabaseTables() {
    try {
      // Prüfe, ob die IP-NFT-Tabelle existiert
      const ipNftTableExists = await this.databaseService.tableExists('ip_nfts');
      
      if (!ipNftTableExists) {
        // Erstelle die IP-NFT-Tabelle
        await this.databaseService.query(`
          CREATE TABLE ip_nfts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            token_id VARCHAR(100) NOT NULL,
            contract_address VARCHAR(100) NOT NULL,
            content_type VARCHAR(50) NOT NULL,
            content_id VARCHAR(100) NOT NULL,
            owner_address VARCHAR(100) NOT NULL,
            metadata_uri VARCHAR(255) NOT NULL,
            ipfs_cid VARCHAR(100) NOT NULL,
            license_type VARCHAR(50),
            royalty_percentage FLOAT,
            is_fractionalized BOOLEAN DEFAULT FALSE,
            fraction_token_address VARCHAR(100),
            governance_token_address VARCHAR(100),
            legal_agreement_cid VARCHAR(100),
            transaction_hash VARCHAR(100),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY token_unique (contract_address, token_id),
            UNIQUE KEY content_unique (content_type, content_id)
          )
        `);
        
        logger.info('IP-NFT-Tabelle erstellt');
      }
      
      // Prüfe, ob die IP-NFT-Berechtigungstabelle existiert
      const ipNftPermissionsTableExists = await this.databaseService.tableExists('ip_nft_permissions');
      
      if (!ipNftPermissionsTableExists) {
        // Erstelle die IP-NFT-Berechtigungstabelle
        await this.databaseService.query(`
          CREATE TABLE ip_nft_permissions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            ip_nft_id INT NOT NULL,
            permission_type VARCHAR(50) NOT NULL,
            grantee_address VARCHAR(100) NOT NULL,
            granted_by VARCHAR(100) NOT NULL,
            is_active BOOLEAN DEFAULT TRUE,
            expiration_date TIMESTAMP NULL,
            metadata JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (ip_nft_id) REFERENCES ip_nfts(id) ON DELETE CASCADE,
            UNIQUE KEY permission_unique (ip_nft_id, permission_type, grantee_address)
          )
        `);
        
        logger.info('IP-NFT-Berechtigungstabelle erstellt');
      }
      
      // Prüfe, ob die IP-NFT-Transaktionsverlaufstabelle existiert
      const ipNftHistoryTableExists = await this.databaseService.tableExists('ip_nft_history');
      
      if (!ipNftHistoryTableExists) {
        // Erstelle die IP-NFT-Transaktionsverlaufstabelle
        await this.databaseService.query(`
          CREATE TABLE ip_nft_history (
            id INT AUTO_INCREMENT PRIMARY KEY,
            ip_nft_id INT NOT NULL,
            action_type VARCHAR(50) NOT NULL,
            from_address VARCHAR(100),
            to_address VARCHAR(100),
            transaction_hash VARCHAR(100),
            block_number BIGINT,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            metadata JSON,
            FOREIGN KEY (ip_nft_id) REFERENCES ip_nfts(id) ON DELETE CASCADE
          )
        `);
        
        logger.info('IP-NFT-Transaktionsverlaufstabelle erstellt');
      }
    } catch (error) {
      logger.error('Fehler bei der Initialisierung der Datenbanktabellen', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Erstellt einen IP-NFT für eine wissenschaftliche Publikation
   * 
   * @param {Object} publication - Publikationsdaten
   * @param {Object} options - Optionen für den IP-NFT
   * @returns {Promise<Object>} Ergebnis der IP-NFT-Erstellung
   */
  async createPublicationIPNFT(publication, options = {}) {
    try {
      logger.info(`Erstelle IP-NFT für Publikation mit DOI ${publication.doi}`);
      
      // Prüfe, ob bereits ein IP-NFT für diese Publikation existiert
      const existingIpNft = await this.databaseService.query(
        'SELECT * FROM ip_nfts WHERE content_type = ? AND content_id = ?',
        ['publication', publication.doi]
      );
      
      if (existingIpNft && existingIpNft.length > 0) {
        logger.info(`Publikation mit DOI ${publication.doi} hat bereits einen IP-NFT`);
        
        return {
          success: true,
          doi: publication.doi,
          tokenId: existingIpNft[0].token_id,
          contractAddress: existingIpNft[0].contract_address,
          ownerAddress: existingIpNft[0].owner_address,
          metadataUri: existingIpNft[0].metadata_uri,
          alreadyExists: true
        };
      }
      
      // Extrahiere Optionen
      const {
        ownerAddress = options.creatorAddress || process.env.DEFAULT_NFT_RECIPIENT,
        licenseType = this.config.defaultLicenseType,
        royaltyPercentage = this.config.defaultRoyaltyPercentage,
        legalAgreement = null,
        fractionalize = this.config.fractionalizationEnabled,
        governanceEnabled = this.config.governanceEnabled,
        ipfsCid = null
      } = options;
      
      // Hole oder erstelle die IPFS-CID für die Publikation
      let contentCid = ipfsCid;
      if (!contentCid) {
        // Prüfe, ob die Publikation bereits auf IPFS gespeichert ist
        const ipfsStorage = await this.databaseService.query(
          'SELECT ipfs_cid FROM ipfs_storage WHERE content_type = ? AND content_id = ?',
          ['publication', publication.doi]
        );
        
        if (ipfsStorage && ipfsStorage.length > 0) {
          contentCid = ipfsStorage[0].ipfs_cid;
        } else {
          throw new Error(`Publikation mit DOI ${publication.doi} muss zuerst auf IPFS gespeichert werden`);
        }
      }
      
      // Erstelle die Metadaten für den IP-NFT
      const ipNftMetadata = {
        name: `IP-NFT: ${publication.title}`,
        description: `Intellectual Property NFT for scientific publication: ${publication.title}`,
        image: publication.coverImage || 'https://desci-scholar.org/assets/ip-nft-placeholder.png',
        external_url: `https://desci-scholar.org/publications/${encodeURIComponent(publication.doi)}`,
        animation_url: `https://ipfs.io/ipfs/${contentCid}`,
        attributes: [
          {
            trait_type: 'Content Type',
            value: 'Scientific Publication'
          },
          {
            trait_type: 'DOI',
            value: publication.doi
          },
          {
            trait_type: 'License Type',
            value: licenseType
          },
          {
            trait_type: 'Royalty Percentage',
            value: `${royaltyPercentage}%`
          },
          {
            trait_type: 'Authors',
            value: publication.authors
          },
          {
            trait_type: 'Journal',
            value: publication.journal
          },
          {
            trait_type: 'Publication Date',
            value: publication.publicationDate
          }
        ],
        properties: {
          contentType: 'publication',
          doi: publication.doi,
          ipfs_cid: contentCid,
          licenseType,
          royaltyPercentage,
          authors: publication.authors.split(',').map(author => author.trim()),
          journal: publication.journal,
          publicationDate: publication.publicationDate,
          abstract: publication.abstract,
          keywords: publication.keywords ? publication.keywords.split(',').map(keyword => keyword.trim()) : [],
          citations: publication.citationCount || 0,
          schema: 'desci-scholar-ip-nft-v1'
        }
      };
      
      // Speichere die IP-NFT-Metadaten auf IPFS
      const metadataResult = await this.ipfsService.addJson(ipNftMetadata);
      const metadataUri = `ipfs://${metadataResult.cid}`;
      logger.debug(`IP-NFT-Metadaten auf IPFS gespeichert mit CID ${metadataResult.cid}`);
      
      // Speichere die rechtliche Vereinbarung auf IPFS, falls vorhanden
      let legalAgreementCid = null;
      if (legalAgreement) {
        if (this.legalService) {
          // Verwende den Rechtsdienst, um eine rechtliche Vereinbarung zu erstellen
          const legalResult = await this.legalService.createIPAgreement({
            contentType: 'publication',
            contentId: publication.doi,
            ownerAddress,
            licenseType,
            royaltyPercentage,
            customTerms: legalAgreement.customTerms
          });
          legalAgreementCid = legalResult.cid;
        } else {
          // Speichere die bereitgestellte rechtliche Vereinbarung direkt
          const legalResult = await this.ipfsService.addJson(legalAgreement);
          legalAgreementCid = legalResult.cid;
        }
        logger.debug(`Rechtliche Vereinbarung auf IPFS gespeichert mit CID ${legalAgreementCid}`);
      }
      
      // Präge den IP-NFT
      const mintResult = await this.blockchainService.mintIPNFT(
        this.config.ipNftContractAddress,
        ownerAddress,
        metadataUri,
        {
          royaltyPercentage,
          contentCid,
          legalAgreementCid,
          contentType: 'publication',
          contentId: publication.doi
        }
      );
      
      logger.debug(`IP-NFT geprägt mit Token-ID ${mintResult.tokenId}`);
      
      // Fractionalisiere den IP-NFT, falls gewünscht
      let fractionTokenAddress = null;
      if (fractionalize) {
        const fractionResult = await this.blockchainService.fractionalizeIPNFT(
          this.config.ipNftContractAddress,
          mintResult.tokenId,
          {
            name: `IP-Shares: ${publication.title}`,
            symbol: `IP-${mintResult.tokenId.substring(0, 4)}`,
            initialSupply: options.initialSupply || 1000000,
            ownerAddress
          }
        );
        fractionTokenAddress = fractionResult.tokenAddress;
        logger.debug(`IP-NFT fractionalisiert mit Token-Adresse ${fractionTokenAddress}`);
      }
      
      // Erstelle einen Governance-Token, falls gewünscht
      let governanceTokenAddress = null;
      if (governanceEnabled) {
        const governanceResult = await this.blockchainService.createIPGovernanceToken(
          this.config.ipNftContractAddress,
          mintResult.tokenId,
          {
            name: `IP-Gov: ${publication.title}`,
            symbol: `IPG-${mintResult.tokenId.substring(0, 4)}`,
            initialSupply: options.governanceSupply || 100000,
            ownerAddress
          }
        );
        governanceTokenAddress = governanceResult.tokenAddress;
        logger.debug(`IP-Governance-Token erstellt mit Token-Adresse ${governanceTokenAddress}`);
      }
      
      // Speichere den IP-NFT in der Datenbank
      await this.databaseService.query(
        `INSERT INTO ip_nfts (
          token_id, contract_address, content_type, content_id, owner_address,
          metadata_uri, ipfs_cid, license_type, royalty_percentage,
          is_fractionalized, fraction_token_address, governance_token_address,
          legal_agreement_cid, transaction_hash
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          mintResult.tokenId,
          this.config.ipNftContractAddress,
          'publication',
          publication.doi,
          ownerAddress,
          metadataUri,
          contentCid,
          licenseType,
          royaltyPercentage,
          fractionalize,
          fractionTokenAddress,
          governanceTokenAddress,
          legalAgreementCid,
          mintResult.transactionHash
        ]
      );
      
      // Hole die ID des eingefügten IP-NFT
      const ipNftResult = await this.databaseService.query(
        'SELECT id FROM ip_nfts WHERE token_id = ? AND contract_address = ?',
        [mintResult.tokenId, this.config.ipNftContractAddress]
      );
      
      const ipNftId = ipNftResult[0].id;
      
      // Speichere den Transaktionsverlauf
      await this.databaseService.query(
        `INSERT INTO ip_nft_history (
          ip_nft_id, action_type, from_address, to_address,
          transaction_hash, block_number
        ) VALUES (?, ?, ?, ?, ?, ?)`,
        [
          ipNftId,
          'mint',
          '0x0000000000000000000000000000000000000000',
          ownerAddress,
          mintResult.transactionHash,
          mintResult.blockNumber
        ]
      );
      
      // Aktualisiere die Publikationstabelle mit der NFT-Referenz
      await this.databaseService.query(
        'UPDATE publications SET nft_token_id = ?, nft_contract_address = ? WHERE doi = ?',
        [mintResult.tokenId, this.config.ipNftContractAddress, publication.doi]
      );
      
      logger.info(`IP-NFT für Publikation mit DOI ${publication.doi} erfolgreich erstellt`);
      
      return {
        success: true,
        doi: publication.doi,
        tokenId: mintResult.tokenId,
        contractAddress: this.config.ipNftContractAddress,
        ownerAddress,
        metadataUri,
        ipfsCid: contentCid,
        legalAgreementCid,
        transactionHash: mintResult.transactionHash,
        blockNumber: mintResult.blockNumber,
        fractionTokenAddress,
        governanceTokenAddress
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen des IP-NFT für die Publikation', {
        error: error.message,
        doi: publication.doi
      });
      throw error;
    }
  }
  
  /**
   * Erstellt einen IP-NFT für ein Patent
   * 
   * @param {Object} patent - Patentdaten
   * @param {Object} options - Optionen für den IP-NFT
   * @returns {Promise<Object>} Ergebnis der IP-NFT-Erstellung
   */
  async createPatentIPNFT(patent, options = {}) {
    try {
      logger.info(`Erstelle IP-NFT für Patent ${patent.id}`);
      
      // Prüfe, ob bereits ein IP-NFT für dieses Patent existiert
      const existingIpNft = await this.databaseService.query(
        'SELECT * FROM ip_nfts WHERE content_type = ? AND content_id = ?',
        ['patent', patent.id]
      );
      
      if (existingIpNft && existingIpNft.length > 0) {
        logger.info(`Patent ${patent.id} hat bereits einen IP-NFT`);
        
        return {
          success: true,
          patentId: patent.id,
          tokenId: existingIpNft[0].token_id,
          contractAddress: existingIpNft[0].contract_address,
          ownerAddress: existingIpNft[0].owner_address,
          metadataUri: existingIpNft[0].metadata_uri,
          alreadyExists: true
        };
      }
      
      // Extrahiere Optionen
      const {
        ownerAddress = options.creatorAddress || process.env.DEFAULT_NFT_RECIPIENT,
        licenseType = this.config.defaultLicenseType,
        royaltyPercentage = this.config.defaultRoyaltyPercentage,
        legalAgreement = null,
        fractionalize = this.config.fractionalizationEnabled,
        governanceEnabled = this.config.governanceEnabled,
        ipfsCid = null
      } = options;
      
      // Hole oder erstelle die IPFS-CID für das Patent
      let contentCid = ipfsCid;
      if (!contentCid) {
        // Prüfe, ob das Patent bereits auf IPFS gespeichert ist
        const ipfsStorage = await this.databaseService.query(
          'SELECT ipfs_cid FROM ipfs_storage WHERE content_type = ? AND content_id = ?',
          ['patent', patent.id]
        );
        
        if (ipfsStorage && ipfsStorage.length > 0) {
          contentCid = ipfsStorage[0].ipfs_cid;
        } else {
          throw new Error(`Patent ${patent.id} muss zuerst auf IPFS gespeichert werden`);
        }
      }
      
      // Erstelle die Metadaten für den IP-NFT
      const ipNftMetadata = {
        name: `IP-NFT: ${patent.title}`,
        description: `Intellectual Property NFT for patent: ${patent.title}`,
        image: patent.image || 'https://desci-scholar.org/assets/ip-nft-patent-placeholder.png',
        external_url: `https://desci-scholar.org/patents/${encodeURIComponent(patent.id)}`,
        animation_url: `https://ipfs.io/ipfs/${contentCid}`,
        attributes: [
          {
            trait_type: 'Content Type',
            value: 'Patent'
          },
          {
            trait_type: 'Patent ID',
            value: patent.id
          },
          {
            trait_type: 'Patent Office',
            value: patent.patentOffice
          },
          {
            trait_type: 'License Type',
            value: licenseType
          },
          {
            trait_type: 'Royalty Percentage',
            value: `${royaltyPercentage}%`
          },
          {
            trait_type: 'Inventors',
            value: patent.inventors
          },
          {
            trait_type: 'Assignees',
            value: patent.assignees
          },
          {
            trait_type: 'Filing Date',
            value: patent.filingDate
          },
          {
            trait_type: 'Grant Date',
            value: patent.grantDate
          }
        ],
        properties: {
          contentType: 'patent',
          patentId: patent.id,
          ipfs_cid: contentCid,
          licenseType,
          royaltyPercentage,
          inventors: patent.inventors.split(',').map(inventor => inventor.trim()),
          assignees: patent.assignees.split(',').map(assignee => assignee.trim()),
          patentOffice: patent.patentOffice,
          patentType: patent.patentType,
          filingDate: patent.filingDate,
          grantDate: patent.grantDate,
          abstract: patent.abstract,
          classificationCodes: patent.classificationCodes ? patent.classificationCodes.split(',').map(code => code.trim()) : [],
          schema: 'desci-scholar-ip-nft-v1'
        }
      };
      
      // Speichere die IP-NFT-Metadaten auf IPFS
      const metadataResult = await this.ipfsService.addJson(ipNftMetadata);
      const metadataUri = `ipfs://${metadataResult.cid}`;
      logger.debug(`IP-NFT-Metadaten auf IPFS gespeichert mit CID ${metadataResult.cid}`);
      
      // Speichere die rechtliche Vereinbarung auf IPFS, falls vorhanden
      let legalAgreementCid = null;
      if (legalAgreement) {
        if (this.legalService) {
          // Verwende den Rechtsdienst, um eine rechtliche Vereinbarung zu erstellen
          const legalResult = await this.legalService.createIPAgreement({
            contentType: 'patent',
            contentId: patent.id,
            ownerAddress,
            licenseType,
            royaltyPercentage,
            customTerms: legalAgreement.customTerms
          });
          legalAgreementCid = legalResult.cid;
        } else {
          // Speichere die bereitgestellte rechtliche Vereinbarung direkt
          const legalResult = await this.ipfsService.addJson(legalAgreement);
          legalAgreementCid = legalResult.cid;
        }
        logger.debug(`Rechtliche Vereinbarung auf IPFS gespeichert mit CID ${legalAgreementCid}`);
      }
      
      // Präge den IP-NFT
      const mintResult = await this.blockchainService.mintIPNFT(
        this.config.ipNftContractAddress,
        ownerAddress,
        metadataUri,
        {
          royaltyPercentage,
          contentCid,
          legalAgreementCid,
          contentType: 'patent',
          contentId: patent.id
        }
      );
      
      logger.debug(`IP-NFT geprägt mit Token-ID ${mintResult.tokenId}`);
      
      // Fractionalisiere den IP-NFT, falls gewünscht
      let fractionTokenAddress = null;
      if (fractionalize) {
        const fractionResult = await this.blockchainService.fractionalizeIPNFT(
          this.config.ipNftContractAddress,
          mintResult.tokenId,
          {
            name: `IP-Shares: ${patent.title}`,
            symbol: `IP-${mintResult.tokenId.substring(0, 4)}`,
            initialSupply: options.initialSupply || 1000000,
            ownerAddress
          }
        );
        fractionTokenAddress = fractionResult.tokenAddress;
        logger.debug(`IP-NFT fractionalisiert mit Token-Adresse ${fractionTokenAddress}`);
      }
      
      // Erstelle einen Governance-Token, falls gewünscht
      let governanceTokenAddress = null;
      if (governanceEnabled) {
        const governanceResult = await this.blockchainService.createIPGovernanceToken(
          this.config.ipNftContractAddress,
          mintResult.tokenId,
          {
            name: `IP-Gov: ${patent.title}`,
            symbol: `IPG-${mintResult.tokenId.substring(0, 4)}`,
            initialSupply: options.governanceSupply || 100000,
            ownerAddress
          }
        );
        governanceTokenAddress = governanceResult.tokenAddress;
        logger.debug(`IP-Governance-Token erstellt mit Token-Adresse ${governanceTokenAddress}`);
      }
      
      // Speichere den IP-NFT in der Datenbank
      await this.databaseService.query(
        `INSERT INTO ip_nfts (
          token_id, contract_address, content_type, content_id, owner_address,
          metadata_uri, ipfs_cid, license_type, royalty_percentage,
          is_fractionalized, fraction_token_address, governance_token_address,
          legal_agreement_cid, transaction_hash
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          mintResult.tokenId,
          this.config.ipNftContractAddress,
          'patent',
          patent.id,
          ownerAddress,
          metadataUri,
          contentCid,
          licenseType,
          royaltyPercentage,
          fractionalize,
          fractionTokenAddress,
          governanceTokenAddress,
          legalAgreementCid,
          mintResult.transactionHash
        ]
      );
      
      // Hole die ID des eingefügten IP-NFT
      const ipNftResult = await this.databaseService.query(
        'SELECT id FROM ip_nfts WHERE token_id = ? AND contract_address = ?',
        [mintResult.tokenId, this.config.ipNftContractAddress]
      );
      
      const ipNftId = ipNftResult[0].id;
      
      // Speichere den Transaktionsverlauf
      await this.databaseService.query(
        `INSERT INTO ip_nft_history (
          ip_nft_id, action_type, from_address, to_address,
          transaction_hash, block_number
        ) VALUES (?, ?, ?, ?, ?, ?)`,
        [
          ipNftId,
          'mint',
          '0x0000000000000000000000000000000000000000',
          ownerAddress,
          mintResult.transactionHash,
          mintResult.blockNumber
        ]
      );
      
      // Aktualisiere die Patenttabelle mit der NFT-Referenz
      await this.databaseService.query(
        'UPDATE patents SET nft_token_id = ?, nft_contract_address = ? WHERE id = ?',
        [mintResult.tokenId, this.config.ipNftContractAddress, patent.id]
      );
      
      logger.info(`IP-NFT für Patent ${patent.id} erfolgreich erstellt`);
      
      return {
        success: true,
        patentId: patent.id,
        tokenId: mintResult.tokenId,
        contractAddress: this.config.ipNftContractAddress,
        ownerAddress,
        metadataUri,
        ipfsCid: contentCid,
        legalAgreementCid,
        transactionHash: mintResult.transactionHash,
        blockNumber: mintResult.blockNumber,
        fractionTokenAddress,
        governanceTokenAddress
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen des IP-NFT für das Patent', {
        error: error.message,
        patentId: patent.id
      });
      throw error;
    }
  }
  
  /**
   * Gewährt Berechtigungen für einen IP-NFT
   * 
   * @param {string} tokenId - Token-ID des IP-NFT
   * @param {string} permissionType - Art der Berechtigung (z.B. 'read', 'use', 'modify', 'commercialize')
   * @param {string} granteeAddress - Adresse des Berechtigungsempfängers
   * @param {Object} options - Weitere Optionen
   * @returns {Promise<Object>} Ergebnis der Berechtigungserteilung
   */
  async grantPermission(tokenId, permissionType, granteeAddress, options = {}) {
    try {
      logger.info(`Gewähre Berechtigung ${permissionType} für IP-NFT ${tokenId} an ${granteeAddress}`);
      
      // Prüfe, ob der IP-NFT existiert
      const ipNft = await this.databaseService.query(
        'SELECT * FROM ip_nfts WHERE token_id = ? AND contract_address = ?',
        [tokenId, this.config.ipNftContractAddress]
      );
      
      if (!ipNft || ipNft.length === 0) {
        throw new Error(`IP-NFT mit Token-ID ${tokenId} nicht gefunden`);
      }
      
      const ipNftId = ipNft[0].id;
      const ownerAddress = ipNft[0].owner_address;
      
      // Prüfe, ob der Aufrufer der Eigentümer des IP-NFT ist
      if (options.callerAddress && options.callerAddress !== ownerAddress) {
        throw new Error('Nur der Eigentümer kann Berechtigungen gewähren');
      }
      
      // Prüfe, ob die Berechtigung bereits existiert
      const existingPermission = await this.databaseService.query(
        'SELECT * FROM ip_nft_permissions WHERE ip_nft_id = ? AND permission_type = ? AND grantee_address = ?',
        [ipNftId, permissionType, granteeAddress]
      );
      
      if (existingPermission && existingPermission.length > 0) {
        // Aktualisiere die bestehende Berechtigung
        await this.databaseService.query(
          'UPDATE ip_nft_permissions SET is_active = TRUE, expiration_date = ?, metadata = ? WHERE id = ?',
          [
            options.expirationDate || null,
            JSON.stringify(options.metadata || {}),
            existingPermission[0].id
          ]
        );
        
        logger.info(`Berechtigung ${permissionType} für IP-NFT ${tokenId} an ${granteeAddress} aktualisiert`);
      } else {
        // Füge eine neue Berechtigung hinzu
        await this.databaseService.query(
          `INSERT INTO ip_nft_permissions (
            ip_nft_id, permission_type, grantee_address, granted_by,
            is_active, expiration_date, metadata
          ) VALUES (?, ?, ?, ?, TRUE, ?, ?)`,
          [
            ipNftId,
            permissionType,
            granteeAddress,
            options.callerAddress || ownerAddress,
            options.expirationDate || null,
            JSON.stringify(options.metadata || {})
          ]
        );
        
        logger.info(`Berechtigung ${permissionType} für IP-NFT ${tokenId} an ${granteeAddress} hinzugefügt`);
      }
      
      // Registriere die Berechtigung auf der Blockchain, falls gewünscht
      if (options.registerOnChain) {
        const onChainResult = await this.blockchainService.registerIPNFTPermission(
          this.config.ipNftContractAddress,
          tokenId,
          granteeAddress,
          permissionType,
          {
            expirationDate: options.expirationDate,
            metadata: options.metadata
          }
        );
        
        logger.debug(`Berechtigung auf der Blockchain registriert mit Transaktion ${onChainResult.transactionHash}`);
        
        return {
          success: true,
          tokenId,
          permissionType,
          granteeAddress,
          transactionHash: onChainResult.transactionHash
        };
      }
      
      return {
        success: true,
        tokenId,
        permissionType,
        granteeAddress
      };
    } catch (error) {
      logger.error('Fehler beim Gewähren der Berechtigung', {
        error: error.message,
        tokenId,
        permissionType,
        granteeAddress
      });
      throw error;
    }
  }
  
  /**
   * Widerruft Berechtigungen für einen IP-NFT
   * 
   * @param {string} tokenId - Token-ID des IP-NFT
   * @param {string} permissionType - Art der Berechtigung
   * @param {string} granteeAddress - Adresse des Berechtigungsempfängers
   * @param {Object} options - Weitere Optionen
   * @returns {Promise<Object>} Ergebnis des Berechtigungswiderrufs
   */
  async revokePermission(tokenId, permissionType, granteeAddress, options = {}) {
    try {
      logger.info(`Widerrufe Berechtigung ${permissionType} für IP-NFT ${tokenId} von ${granteeAddress}`);
      
      // Prüfe, ob der IP-NFT existiert
      const ipNft = await this.databaseService.query(
        'SELECT * FROM ip_nfts WHERE token_id = ? AND contract_address = ?',
        [tokenId, this.config.ipNftContractAddress]
      );
      
      if (!ipNft || ipNft.length === 0) {
        throw new Error(`IP-NFT mit Token-ID ${tokenId} nicht gefunden`);
      }
      
      const ipNftId = ipNft[0].id;
      const ownerAddress = ipNft[0].owner_address;
      
      // Prüfe, ob der Aufrufer der Eigentümer des IP-NFT ist
      if (options.callerAddress && options.callerAddress !== ownerAddress) {
        throw new Error('Nur der Eigentümer kann Berechtigungen widerrufen');
      }
      
      // Prüfe, ob die Berechtigung existiert
      const existingPermission = await this.databaseService.query(
        'SELECT * FROM ip_nft_permissions WHERE ip_nft_id = ? AND permission_type = ? AND grantee_address = ?',
        [ipNftId, permissionType, granteeAddress]
      );
      
      if (!existingPermission || existingPermission.length === 0) {
        throw new Error(`Berechtigung ${permissionType} für IP-NFT ${tokenId} an ${granteeAddress} nicht gefunden`);
      }
      
      // Deaktiviere die Berechtigung
      await this.databaseService.query(
        'UPDATE ip_nft_permissions SET is_active = FALSE, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [existingPermission[0].id]
      );
      
      logger.info(`Berechtigung ${permissionType} für IP-NFT ${tokenId} von ${granteeAddress} widerrufen`);
      
      // Widerrufe die Berechtigung auf der Blockchain, falls gewünscht
      if (options.registerOnChain) {
        const onChainResult = await this.blockchainService.revokeIPNFTPermission(
          this.config.ipNftContractAddress,
          tokenId,
          granteeAddress,
          permissionType
        );
        
        logger.debug(`Berechtigung auf der Blockchain widerrufen mit Transaktion ${onChainResult.transactionHash}`);
        
        return {
          success: true,
          tokenId,
          permissionType,
          granteeAddress,
          transactionHash: onChainResult.transactionHash
        };
      }
      
      return {
        success: true,
        tokenId,
        permissionType,
        granteeAddress
      };
    } catch (error) {
      logger.error('Fehler beim Widerrufen der Berechtigung', {
        error: error.message,
        tokenId,
        permissionType,
        granteeAddress
      });
      throw error;
    }
  }
  
  /**
   * Holt Informationen zu einem IP-NFT
   * 
   * @param {string} tokenId - Token-ID des IP-NFT
   * @returns {Promise<Object>} IP-NFT-Informationen
   */
  async getIPNFTInfo(tokenId) {
    try {
      logger.info(`Hole Informationen für IP-NFT ${tokenId}`);
      
      // Hole den IP-NFT aus der Datenbank
      const ipNft = await this.databaseService.query(
        'SELECT * FROM ip_nfts WHERE token_id = ? AND contract_address = ?',
        [tokenId, this.config.ipNftContractAddress]
      );
      
      if (!ipNft || ipNft.length === 0) {
        throw new Error(`IP-NFT mit Token-ID ${tokenId} nicht gefunden`);
      }
      
      const ipNftData = ipNft[0];
      
      // Hole die Berechtigungen für den IP-NFT
      const permissions = await this.databaseService.query(
        'SELECT * FROM ip_nft_permissions WHERE ip_nft_id = ? AND is_active = TRUE',
        [ipNftData.id]
      );
      
      // Hole den Transaktionsverlauf für den IP-NFT
      const history = await this.databaseService.query(
        'SELECT * FROM ip_nft_history WHERE ip_nft_id = ? ORDER BY timestamp DESC',
        [ipNftData.id]
      );
      
      // Hole die Metadaten von IPFS
      const metadataUri = ipNftData.metadata_uri;
      let metadata = null;
      
      if (metadataUri && metadataUri.startsWith('ipfs://')) {
        const cid = metadataUri.replace('ipfs://', '');
        try {
          metadata = await this.ipfsService.getJson(cid);
        } catch (error) {
          logger.warn(`Fehler beim Abrufen der Metadaten von IPFS: ${error.message}`);
        }
      }
      
      // Hole Informationen zum Inhalt
      let contentInfo = null;
      
      if (ipNftData.content_type === 'publication') {
        const publications = await this.databaseService.query(
          'SELECT * FROM publications WHERE doi = ?',
          [ipNftData.content_id]
        );
        
        if (publications && publications.length > 0) {
          contentInfo = publications[0];
        }
      } else if (ipNftData.content_type === 'patent') {
        const patents = await this.databaseService.query(
          'SELECT * FROM patents WHERE id = ?',
          [ipNftData.content_id]
        );
        
        if (patents && patents.length > 0) {
          contentInfo = patents[0];
        }
      }
      
      // Erstelle die Antwort
      const result = {
        tokenId: ipNftData.token_id,
        contractAddress: ipNftData.contract_address,
        contentType: ipNftData.content_type,
        contentId: ipNftData.content_id,
        ownerAddress: ipNftData.owner_address,
        metadataUri: ipNftData.metadata_uri,
        ipfsCid: ipNftData.ipfs_cid,
        licenseType: ipNftData.license_type,
        royaltyPercentage: ipNftData.royalty_percentage,
        isFragmentalized: ipNftData.is_fractionalized,
        fractionTokenAddress: ipNftData.fraction_token_address,
        governanceTokenAddress: ipNftData.governance_token_address,
        legalAgreementCid: ipNftData.legal_agreement_cid,
        createdAt: ipNftData.created_at,
        updatedAt: ipNftData.updated_at,
        permissions: permissions.map(p => ({
          permissionType: p.permission_type,
          granteeAddress: p.grantee_address,
          grantedBy: p.granted_by,
          isActive: p.is_active,
          expirationDate: p.expiration_date,
          metadata: p.metadata ? JSON.parse(p.metadata) : null,
          createdAt: p.created_at,
          updatedAt: p.updated_at
        })),
        history: history.map(h => ({
          actionType: h.action_type,
          fromAddress: h.from_address,
          toAddress: h.to_address,
          transactionHash: h.transaction_hash,
          blockNumber: h.block_number,
          timestamp: h.timestamp,
          metadata: h.metadata ? JSON.parse(h.metadata) : null
        })),
        metadata,
        contentInfo
      };
      
      logger.info(`Informationen für IP-NFT ${tokenId} erfolgreich abgerufen`);
      
      return {
        success: true,
        ipNft: result
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen der IP-NFT-Informationen', {
        error: error.message,
        tokenId
      });
      throw error;
    }
  }
}