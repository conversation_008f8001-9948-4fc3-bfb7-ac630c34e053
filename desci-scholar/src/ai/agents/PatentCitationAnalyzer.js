/**
 * @fileoverview KI-Agent für die Analyse von Patent- und Publikationszitationen
 * 
 * Dieser Agent analysiert Zitationsbeziehungen zwischen Patenten und wissenschaftlichen
 * Publikationen, um Einblicke in Forschungstrends und Technologietransfer zu gewinnen.
 */

import { logger } from '../../utils/logger.js';
import { BaseAgent } from './index.js';

/**
 * Agent für die Analyse von Patent- und Publikationszitationen
 */
export class PatentCitationAnalyzer extends BaseAgent {
  /**
   * Erstellt eine neue Instanz des PatentCitationAnalyzer
   * @param {Object} options - Konfigurationsoptionen
   * @param {Object} options.databaseService - Datenbankdienst
   * @param {Object} options.textAnalysisService - Textanalyse-Dienst (optional)
   * @param {Object} options.config - Weitere Konfigurationsoptionen
   */
  constructor(options = {}) {
    super('PatentCitationAnalyzer');
    
    const {
      databaseService,
      textAnalysisService,
      config = {}
    } = options;
    
    this.databaseService = databaseService;
    this.textAnalysisService = textAnalysisService;
    
    // Konfiguration
    this.config = {
      similarityThreshold: 0.7,
      maxRelatedPublications: 50,
      maxCitationsToAnalyze: 100,
      ...config
    };
    
    logger.info('PatentCitationAnalyzer initialisiert');
  }
  
  /**
   * Initialisiert den Agent
   */
  async initialize() {
    logger.info('Initialisiere PatentCitationAnalyzer');
    
    // Prüfe, ob die erforderlichen Dienste verfügbar sind
    if (!this.databaseService) {
      throw new Error('DatabaseService ist erforderlich');
    }
    
    // Initialisiere die Datenbanktabellen
    await this.initializeDatabaseTables();
    
    logger.info('PatentCitationAnalyzer erfolgreich initialisiert');
  }
  
  /**
   * Initialisiert die erforderlichen Datenbanktabellen
   */
  async initializeDatabaseTables() {
    try {
      // Prüfe, ob die Patent-Zitationstabelle existiert
      const patentCitationTableExists = await this.databaseService.tableExists('patent_citations');
      
      if (!patentCitationTableExists) {
        // Erstelle die Patent-Zitationstabelle
        await this.databaseService.query(`
          CREATE TABLE patent_citations (
            id INT AUTO_INCREMENT PRIMARY KEY,
            citing_patent_id VARCHAR(50) NOT NULL,
            cited_patent_id VARCHAR(50) NOT NULL,
            citation_type VARCHAR(50),
            citation_category VARCHAR(50),
            citation_context TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY patent_citation_unique (citing_patent_id, cited_patent_id)
          )
        `);
        
        logger.info('Patent-Zitationstabelle erstellt');
      }
      
      // Prüfe, ob die Patent-Publikations-Zitationstabelle existiert
      const patentPublicationCitationTableExists = await this.databaseService.tableExists('patent_publication_citations');
      
      if (!patentPublicationCitationTableExists) {
        // Erstelle die Patent-Publikations-Zitationstabelle
        await this.databaseService.query(`
          CREATE TABLE patent_publication_citations (
            id INT AUTO_INCREMENT PRIMARY KEY,
            patent_id VARCHAR(50) NOT NULL,
            doi VARCHAR(255) NOT NULL,
            citation_direction ENUM('patent_cites_publication', 'publication_cites_patent'),
            citation_context TEXT,
            confidence FLOAT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY patent_publication_citation_unique (patent_id, doi, citation_direction)
          )
        `);
        
        logger.info('Patent-Publikations-Zitationstabelle erstellt');
      }
    } catch (error) {
      logger.error('Fehler bei der Initialisierung der Datenbanktabellen', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Analysiert die Zitationsbeziehungen zwischen einem Patent und einer Publikation
   * 
   * @param {string} patentId - Patent-ID
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Ergebnis der Analyse
   */
  async analyzeCitationRelationship(patentId, doi) {
    try {
      logger.info(`Analysiere Zitationsbeziehung zwischen Patent ${patentId} und DOI ${doi}`);
      
      // Hole die Patentdaten
      const patents = await this.databaseService.query(
        'SELECT * FROM patents WHERE id = ?',
        [patentId]
      );
      
      if (!patents || patents.length === 0) {
        throw new Error(`Patent ${patentId} nicht gefunden`);
      }
      
      const patent = patents[0];
      
      // Hole die Publikationsdaten
      const publications = await this.databaseService.query(
        'SELECT * FROM publications WHERE doi = ?',
        [doi]
      );
      
      if (!publications || publications.length === 0) {
        throw new Error(`Publikation mit DOI ${doi} nicht gefunden`);
      }
      
      const publication = publications[0];
      
      // Prüfe, ob das Patent die Publikation zitiert
      const patentCitesPublication = await this.checkIfPatentCitesPublication(patent, publication);
      
      // Prüfe, ob die Publikation das Patent zitiert
      const publicationCitesPatent = await this.checkIfPublicationCitesPatent(publication, patent);
      
      // Speichere die Zitationsbeziehungen
      if (patentCitesPublication.cites) {
        await this.savePatentPublicationCitation(
          patentId,
          doi,
          'patent_cites_publication',
          patentCitesPublication.context,
          patentCitesPublication.confidence
        );
      }
      
      if (publicationCitesPatent.cites) {
        await this.savePatentPublicationCitation(
          patentId,
          doi,
          'publication_cites_patent',
          publicationCitesPatent.context,
          publicationCitesPatent.confidence
        );
      }
      
      return {
        success: true,
        patentId,
        doi,
        patentCitesPublication: patentCitesPublication.cites,
        publicationCitesPatent: publicationCitesPatent.cites,
        bidirectionalCitation: patentCitesPublication.cites && publicationCitesPatent.cites
      };
    } catch (error) {
      logger.error('Fehler bei der Analyse der Zitationsbeziehung', {
        error: error.message,
        patentId,
        doi
      });
      
      return {
        success: false,
        patentId,
        doi,
        error: error.message
      };
    }
  }
  
  /**
   * Prüft, ob ein Patent eine Publikation zitiert
   * 
   * @param {Object} patent - Patentdaten
   * @param {Object} publication - Publikationsdaten
   * @returns {Promise<Object>} Ergebnis der Prüfung
   */
  async checkIfPatentCitesPublication(patent, publication) {
    try {
      // Wenn kein Volltext vorhanden ist, können wir nicht prüfen
      if (!patent.full_text) {
        return { cites: false, confidence: 0, context: null };
      }
      
      const fullText = patent.full_text;
      const publicationTitle = publication.title;
      const publicationAuthors = publication.authors ? publication.authors.split(',').map(a => a.trim()) : [];
      const doi = publication.doi;
      
      // Suche nach dem DOI im Volltext
      if (fullText.includes(doi)) {
        const doiIndex = fullText.indexOf(doi);
        const startIndex = Math.max(0, doiIndex - 200);
        const endIndex = Math.min(fullText.length, doiIndex + 200);
        const context = fullText.substring(startIndex, endIndex);
        
        return { cites: true, confidence: 1.0, context };
      }
      
      // Suche nach dem Titel im Volltext
      if (publicationTitle && fullText.includes(publicationTitle)) {
        const titleIndex = fullText.indexOf(publicationTitle);
        const startIndex = Math.max(0, titleIndex - 200);
        const endIndex = Math.min(fullText.length, titleIndex + 200);
        const context = fullText.substring(startIndex, endIndex);
        
        return { cites: true, confidence: 0.9, context };
      }
      
      // Suche nach Autoren im Volltext
      for (const author of publicationAuthors) {
        if (author.length > 3 && fullText.includes(author)) {
          const authorIndex = fullText.indexOf(author);
          const startIndex = Math.max(0, authorIndex - 200);
          const endIndex = Math.min(fullText.length, authorIndex + 200);
          const context = fullText.substring(startIndex, endIndex);
          
          // Prüfe, ob der Kontext auf eine Zitation hindeutet
          if (context.match(/reference|cited|citation|bibliography/i)) {
            return { cites: true, confidence: 0.8, context };
          }
        }
      }
      
      // Verwende den Textanalysedienst für eine tiefere Analyse, falls verfügbar
      if (this.textAnalysisService) {
        const analysisResult = await this.textAnalysisService.findCitations(fullText, {
          title: publicationTitle,
          authors: publicationAuthors,
          doi
        });
        
        if (analysisResult.found) {
          return {
            cites: true,
            confidence: analysisResult.confidence,
            context: analysisResult.context
          };
        }
      }
      
      return { cites: false, confidence: 0, context: null };
    } catch (error) {
      logger.error('Fehler bei der Prüfung, ob Patent Publikation zitiert', {
        error: error.message,
        patentId: patent.id,
        doi: publication.doi
      });
      return { cites: false, confidence: 0, context: null };
    }
  }
  
  /**
   * Prüft, ob eine Publikation ein Patent zitiert
   * 
   * @param {Object} publication - Publikationsdaten
   * @param {Object} patent - Patentdaten
   * @returns {Promise<Object>} Ergebnis der Prüfung
   */
  async checkIfPublicationCitesPatent(publication, patent) {
    try {
      // Wenn kein Volltext vorhanden ist, können wir nicht prüfen
      if (!publication.full_text) {
        return { cites: false, confidence: 0, context: null };
      }
      
      const fullText = publication.full_text;
      const patentId = patent.id;
      const patentTitle = patent.title;
      const inventors = patent.inventors ? patent.inventors.split(',').map(i => i.trim()) : [];
      
      // Suche nach der Patent-ID im Volltext
      if (fullText.includes(patentId)) {
        const patentIdIndex = fullText.indexOf(patentId);
        const startIndex = Math.max(0, patentIdIndex - 200);
        const endIndex = Math.min(fullText.length, patentIdIndex + 200);
        const context = fullText.substring(startIndex, endIndex);
        
        return { cites: true, confidence: 1.0, context };
      }
      
      // Suche nach dem Patenttitel im Volltext
      if (patentTitle && fullText.includes(patentTitle)) {
        const titleIndex = fullText.indexOf(patentTitle);
        const startIndex = Math.max(0, titleIndex - 200);
        const endIndex = Math.min(fullText.length, titleIndex + 200);
        const context = fullText.substring(startIndex, endIndex);
        
        return { cites: true, confidence: 0.9, context };
      }
      
      // Suche nach Erfindern im Volltext
      for (const inventor of inventors) {
        if (inventor.length > 3 && fullText.includes(inventor)) {
          const inventorIndex = fullText.indexOf(inventor);
          const startIndex = Math.max(0, inventorIndex - 200);
          const endIndex = Math.min(fullText.length, inventorIndex + 200);
          const context = fullText.substring(startIndex, endIndex);
          
          // Prüfe, ob der Kontext auf eine Patentzitation hindeutet
          if (context.match(/patent|invention|inventor|US\d+|EP\d+|WO\d+/i)) {
            return { cites: true, confidence: 0.8, context };
          }
        }
      }
      
      // Verwende den Textanalysedienst für eine tiefere Analyse, falls verfügbar
      if (this.textAnalysisService) {
        const analysisResult = await this.textAnalysisService.findPatentCitations(fullText, {
          patentId,
          title: patentTitle,
          inventors
        });
        
        if (analysisResult.found) {
          return {
            cites: true,
            confidence: analysisResult.confidence,
            context: analysisResult.context
          };
        }
      }
      
      return { cites: false, confidence: 0, context: null };
    } catch (error) {
      logger.error('Fehler bei der Prüfung, ob Publikation Patent zitiert', {
        error: error.message,
        doi: publication.doi,
        patentId: patent.id
      });
      return { cites: false, confidence: 0, context: null };
    }
  }
  
  /**
   * Speichert eine Zitationsbeziehung zwischen einem Patent und einer Publikation
   * 
   * @param {string} patentId - Patent-ID
   * @param {string} doi - DOI der Publikation
   * @param {string} direction - Richtung der Zitation ('patent_cites_publication' oder 'publication_cites_patent')
   * @param {string} context - Kontext der Zitation
   * @param {number} confidence - Konfidenzwert der Zitation (0-1)
   * @returns {Promise<void>}
   */
  async savePatentPublicationCitation(patentId, doi, direction, context, confidence) {
    try {
      // Prüfe, ob die Zitation bereits existiert
      const existingCitations = await this.databaseService.query(
        'SELECT id FROM patent_publication_citations WHERE patent_id = ? AND doi = ? AND citation_direction = ?',
        [patentId, doi, direction]
      );
      
      if (existingCitations && existingCitations.length > 0) {
        // Aktualisiere die bestehende Zitation
        await this.databaseService.query(
          'UPDATE patent_publication_citations SET citation_context = ?, confidence = ? WHERE id = ?',
          [context, confidence, existingCitations[0].id]
        );
        
        logger.debug(`Zitationsbeziehung zwischen Patent ${patentId} und DOI ${doi} (${direction}) aktualisiert`);
      } else {
        // Füge eine neue Zitation hinzu
        await this.databaseService.query(
          'INSERT INTO patent_publication_citations (patent_id, doi, citation_direction, citation_context, confidence) VALUES (?, ?, ?, ?, ?)',
          [patentId, doi, direction, context, confidence]
        );
        
        logger.debug(`Zitationsbeziehung zwischen Patent ${patentId} und DOI ${doi} (${direction}) gespeichert`);
      }
    } catch (error) {
      logger.error('Fehler beim Speichern der Patent-Publikations-Zitation', {
        error: error.message,
        patentId,
        doi,
        direction
      });
      throw error;
    }
  }
  
  /**
   * Findet Publikationen, die mit einem Patent in Beziehung stehen
   * 
   * @param {string} patentId - Patent-ID
   * @returns {Promise<Object>} Gefundene Beziehungen
   */
  async findRelatedPublications(patentId) {
    try {
      logger.info(`Suche nach Publikationen, die mit Patent ${patentId} in Beziehung stehen`);
      
      // Hole die Patentdaten
      const patents = await this.databaseService.query(
        'SELECT * FROM patents WHERE id = ?',
        [patentId]
      );
      
      if (!patents || patents.length === 0) {
        throw new Error(`Patent ${patentId} nicht gefunden`);
      }
      
      const patent = patents[0];
      
      // Strategien zur Identifizierung von verwandten Publikationen
      const strategies = [
        this.findPublicationsByTitle.bind(this),
        this.findPublicationsByInventors.bind(this),
        this.findPublicationsByKeywords.bind(this),
        this.findPublicationsByCitations.bind(this)
      ];
      
      // Führe alle Strategien aus und sammle die Ergebnisse
      const allRelationships = [];
      
      for (const strategy of strategies) {
        const relationships = await strategy(patent);
        allRelationships.push(...relationships);
      }
      
      // Entferne Duplikate und sortiere nach Konfidenz
      const uniqueRelationships = this.deduplicateRelationships(allRelationships);
      uniqueRelationships.sort((a, b) => b.confidence - a.confidence);
      
      // Begrenze die Anzahl der Beziehungen
      const limitedRelationships = uniqueRelationships.slice(0, this.config.maxRelatedPublications);
      
      logger.info(`${limitedRelationships.length} verwandte Publikationen für Patent ${patentId} gefunden`);
      
      return {
        success: true,
        patentId,
        relationships: limitedRelationships
      };
    } catch (error) {
      logger.error('Fehler beim Suchen nach verwandten Publikationen', {
        error: error.message,
        patentId
      });
      
      return {
        success: false,
        patentId,
        error: error.message,
        relationships: []
      };
    }
  }
  
  /**
   * Findet Publikationen basierend auf Ähnlichkeiten im Titel
   * 
   * @param {Object} patent - Patentdaten
   * @returns {Promise<Array>} Gefundene Beziehungen
   */
  async findPublicationsByTitle(patent) {
    try {
      const title = patent.title;
      
      if (!title) {
        return [];
      }
      
      // Suche nach Publikationen mit ähnlichem Titel
      let publications;
      
      if (this.textAnalysisService) {
        // Verwende den Textanalysedienst für semantische Suche
        publications = await this.textAnalysisService.findSimilarTitles(title, {
          limit: 20,
          threshold: this.config.similarityThreshold
        });
      } else {
        // Einfache Schlüsselwortsuche
        const keywords = title
          .toLowerCase()
          .replace(/[^\w\s]/g, '')
          .split(/\s+/)
          .filter(word => word.length > 3);
        
        if (keywords.length === 0) {
          return [];
        }
        
        // Erstelle eine SQL-Abfrage mit LIKE-Operatoren für jeden Schlüsselbegriff
        const likeConditions = keywords.map(() => 'title LIKE ?').join(' OR ');
        const likeParams = keywords.map(keyword => `%${keyword}%`);
        
        publications = await this.databaseService.query(
          `SELECT * FROM publications WHERE ${likeConditions} LIMIT 20`,
          likeParams
        );
      }
      
      if (!publications || publications.length === 0) {
        return [];
      }
      
      // Erstelle Beziehungen für die gefundenen Publikationen
      return publications.map(pub => ({
        doi: pub.doi,
        title: pub.title,
        type: 'related_by_title',
        confidence: pub.similarity || 0.7,
        patent: patent.id
      }));
    } catch (error) {
      logger.error('Fehler beim Suchen nach Publikationen basierend auf dem Titel', {
        error: error.message,
        patentId: patent.id
      });
      return [];
    }
  }
  
  /**
   * Findet Publikationen basierend auf den Erfindern des Patents
   * 
   * @param {Object} patent - Patentdaten
   * @returns {Promise<Array>} Gefundene Beziehungen
   */
  async findPublicationsByInventors(patent) {
    try {
      const inventors = patent.inventors ? patent.inventors.split(',').map(i => i.trim()) : [];
      
      if (inventors.length === 0) {
        return [];
      }
      
      // Suche nach Publikationen der Erfinder
      const relationships = [];
      
      for (const inventor of inventors) {
        if (inventor.length < 4) continue;
        
        const publications = await this.databaseService.query(
          'SELECT * FROM publications WHERE authors LIKE ? LIMIT 10',
          [`%${inventor}%`]
        );
        
        if (publications && publications.length > 0) {
          for (const pub of publications) {
            relationships.push({
              doi: pub.doi,
              title: pub.title,
              type: 'related_by_author',
              confidence: 0.8,
              patent: patent.id
            });
          }
        }
      }
      
      return relationships;
    } catch (error) {
      logger.error('Fehler beim Suchen nach Publikationen basierend auf Erfindern', {
        error: error.message,
        patentId: patent.id
      });
      return [];
    }
  }
  
  /**
   * Findet Publikationen basierend auf Schlüsselwörtern aus dem Patent
   * 
   * @param {Object} patent - Patentdaten
   * @returns {Promise<Array>} Gefundene Beziehungen
   */
  async findPublicationsByKeywords(patent) {
    try {
      // Extrahiere Schlüsselwörter aus dem Patenttitel und Abstract
      let keywords = [];
      
      if (patent.title) {
        keywords = keywords.concat(
          patent.title
            .toLowerCase()
            .replace(/[^\w\s]/g, '')
            .split(/\s+/)
            .filter(word => word.length > 4)
        );
      }
      
      if (patent.abstract) {
        keywords = keywords.concat(
          patent.abstract
            .toLowerCase()
            .replace(/[^\w\s]/g, '')
            .split(/\s+/)
            .filter(word => word.length > 4)
        );
      }
      
      // Entferne Duplikate und häufige Wörter
      const commonWords = ['about', 'above', 'after', 'again', 'against', 'method', 'system', 'device', 'apparatus'];
      keywords = [...new Set(keywords)].filter(word => !commonWords.includes(word));
      
      if (keywords.length === 0) {
        return [];
      }
      
      // Begrenze die Anzahl der Schlüsselwörter
      keywords = keywords.slice(0, 5);
      
      // Suche nach Publikationen mit diesen Schlüsselwörtern
      const likeConditions = keywords.map(() => '(title LIKE ? OR abstract LIKE ?)').join(' OR ');
      const likeParams = [];
      
      for (const keyword of keywords) {
        likeParams.push(`%${keyword}%`, `%${keyword}%`);
      }
      
      const publications = await this.databaseService.query(
        `SELECT * FROM publications WHERE ${likeConditions} LIMIT 15`,
        likeParams
      );
      
      if (!publications || publications.length === 0) {
        return [];
      }
      
      // Erstelle Beziehungen für die gefundenen Publikationen
      return publications.map(pub => ({
        doi: pub.doi,
        title: pub.title,
        type: 'related_by_keywords',
        confidence: 0.6,
        patent: patent.id
      }));
    } catch (error) {
      logger.error('Fehler beim Suchen nach Publikationen basierend auf Schlüsselwörtern', {
        error: error.message,
        patentId: patent.id
      });
      return [];
    }
  }
  
  /**
   * Findet Publikationen basierend auf Zitationen
   * 
   * @param {Object} patent - Patentdaten
   * @returns {Promise<Array>} Gefundene Beziehungen
   */
  async findPublicationsByCitations(patent) {
    try {
      // Wenn kein Volltext vorhanden ist, können wir nicht nach Zitationen suchen
      if (!patent.full_text) {
        return [];
      }
      
      const fullText = patent.full_text;
      
      // Suche nach DOIs im Volltext
      const doiRegex = /10\.\d{4,9}\/[-._;()/:A-Z0-9]+/gi;
      const doiMatches = fullText.match(doiRegex) || [];
      
      const relationships = [];
      
      // Prüfe jede gefundene DOI
      for (const doi of doiMatches) {
        // Prüfe, ob die DOI in der Datenbank existiert
        const publications = await this.databaseService.query(
          'SELECT * FROM publications WHERE doi = ?',
          [doi]
        );
        
        if (publications && publications.length > 0) {
          const pub = publications[0];
          
          // Finde den Kontext der Zitation
          const doiIndex = fullText.indexOf(doi);
          const startIndex = Math.max(0, doiIndex - 200);
          const endIndex = Math.min(fullText.length, doiIndex + 200);
          const context = fullText.substring(startIndex, endIndex);
          
          relationships.push({
            doi: pub.doi,
            title: pub.title,
            type: 'cited_by_patent',
            confidence: 1.0,
            context,
            patent: patent.id
          });
          
          // Speichere die Zitation in der Datenbank
          await this.savePatentPublicationCitation(
            patent.id,
            pub.doi,
            'patent_cites_publication',
            context,
            1.0
          );
        }
      }
      
      return relationships;
    } catch (error) {
      logger.error('Fehler beim Suchen nach Publikationen basierend auf Zitationen', {
        error: error.message,
        patentId: patent.id
      });
      return [];
    }
  }
  
  /**
   * Entfernt Duplikate aus einer Liste von Beziehungen
   * 
   * @param {Array} relationships - Liste von Beziehungen
   * @returns {Array} Liste ohne Duplikate
   */
  deduplicateRelationships(relationships) {
    const uniqueRelationships = [];
    const seenDois = new Set();
    
    for (const relationship of relationships) {
      if (!seenDois.has(relationship.doi)) {
        uniqueRelationships.push(relationship);
        seenDois.add(relationship.doi);
      }
    }
    
    return uniqueRelationships;
  }
  
  /**
   * Analysiert die Zitationsnetzwerke zwischen Patenten und Publikationen
   * 
   * @param {Object} options - Optionen für die Analyse
   * @param {string} options.patentId - Patent-ID (optional)
   * @param {string} options.doi - DOI der Publikation (optional)
   * @param {number} options.depth - Tiefe der Netzwerkanalyse (Standard: 2)
   * @returns {Promise<Object>} Ergebnis der Netzwerkanalyse
   */
  async analyzeCitationNetwork(options = {}) {
    try {
      const { patentId, doi, depth = 2 } = options;
      
      logger.info('Analysiere Zitationsnetzwerk', { patentId, doi, depth });
      
      if (!patentId && !doi) {
        throw new Error('Entweder patentId oder doi muss angegeben werden');
      }
      
      // Initialisiere das Netzwerk
      const network = {
        nodes: [],
        edges: [],
        metrics: {}
      };
      
      // Startknoten hinzufügen
      if (patentId) {
        const patents = await this.databaseService.query(
          'SELECT * FROM patents WHERE id = ?',
          [patentId]
        );
        
        if (!patents || patents.length === 0) {
          throw new Error(`Patent ${patentId} nicht gefunden`);
        }
        
        const patent = patents[0];
        
        network.nodes.push({
          id: `patent-${patent.id}`,
          type: 'patent',
          label: patent.title,
          patentId: patent.id,
          patentOffice: patent.patent_office
        });
        
        // Baue das Netzwerk ausgehend vom Patent auf
        await this.buildNetworkFromPatent(patent.id, network, depth);
      }
      
      if (doi) {
        const publications = await this.databaseService.query(
          'SELECT * FROM publications WHERE doi = ?',
          [doi]
        );
        
        if (!publications || publications.length === 0) {
          throw new Error(`Publikation mit DOI ${doi} nicht gefunden`);
        }
        
        const publication = publications[0];
        
        network.nodes.push({
          id: `publication-${publication.doi}`,
          type: 'publication',
          label: publication.title,
          doi: publication.doi,
          authors: publication.authors
        });
        
        // Baue das Netzwerk ausgehend von der Publikation auf
        await this.buildNetworkFromPublication(publication.doi, network, depth);
      }
      
      // Berechne Netzwerkmetriken
      network.metrics = this.calculateNetworkMetrics(network);
      
      logger.info('Zitationsnetzwerkanalyse abgeschlossen', {
        nodes: network.nodes.length,
        edges: network.edges.length
      });
      
      return {
        success: true,
        network
      };
    } catch (error) {
      logger.error('Fehler bei der Analyse des Zitationsnetzwerks', {
        error: error.message,
        options
      });
      
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * Baut das Netzwerk ausgehend von einem Patent auf
   * 
   * @param {string} patentId - Patent-ID
   * @param {Object} network - Netzwerkobjekt
   * @param {number} depth - Verbleibende Tiefe
   * @param {Set} visited - Bereits besuchte Knoten
   * @returns {Promise<void>}
   */
  async buildNetworkFromPatent(patentId, network, depth, visited = new Set()) {
    if (depth <= 0 || visited.has(`patent-${patentId}`)) {
      return;
    }
    
    visited.add(`patent-${patentId}`);
    
    try {
      // Finde Publikationen, die vom Patent zitiert werden
      const citedPublications = await this.databaseService.query(
        'SELECT p.*, ppc.citation_context, ppc.confidence FROM publications p JOIN patent_publication_citations ppc ON p.doi = ppc.doi WHERE ppc.patent_id = ? AND ppc.citation_direction = ?',
        [patentId, 'patent_cites_publication']
      );
      
      if (citedPublications && citedPublications.length > 0) {
        for (const pub of citedPublications) {
          const pubNodeId = `publication-${pub.doi}`;
          
          // Füge den Publikationsknoten hinzu, falls noch nicht vorhanden
          if (!network.nodes.some(node => node.id === pubNodeId)) {
            network.nodes.push({
              id: pubNodeId,
              type: 'publication',
              label: pub.title,
              doi: pub.doi,
              authors: pub.authors
            });
          }
          
          // Füge die Kante hinzu
          network.edges.push({
            source: `patent-${patentId}`,
            target: pubNodeId,
            type: 'patent_cites_publication',
            confidence: pub.confidence
          });
          
          // Rekursiver Aufruf für die Publikation
          await this.buildNetworkFromPublication(pub.doi, network, depth - 1, visited);
        }
      }
      
      // Finde Publikationen, die das Patent zitieren
      const citingPublications = await this.databaseService.query(
        'SELECT p.*, ppc.citation_context, ppc.confidence FROM publications p JOIN patent_publication_citations ppc ON p.doi = ppc.doi WHERE ppc.patent_id = ? AND ppc.citation_direction = ?',
        [patentId, 'publication_cites_patent']
      );
      
      if (citingPublications && citingPublications.length > 0) {
        for (const pub of citingPublications) {
          const pubNodeId = `publication-${pub.doi}`;
          
          // Füge den Publikationsknoten hinzu, falls noch nicht vorhanden
          if (!network.nodes.some(node => node.id === pubNodeId)) {
            network.nodes.push({
              id: pubNodeId,
              type: 'publication',
              label: pub.title,
              doi: pub.doi,
              authors: pub.authors
            });
          }
          
          // Füge die Kante hinzu
          network.edges.push({
            source: pubNodeId,
            target: `patent-${patentId}`,
            type: 'publication_cites_patent',
            confidence: pub.confidence
          });
          
          // Rekursiver Aufruf für die Publikation
          await this.buildNetworkFromPublication(pub.doi, network, depth - 1, visited);
        }
      }
      
      // Finde Patente, die vom aktuellen Patent zitiert werden
      const citedPatents = await this.databaseService.query(
        'SELECT p.*, pc.citation_context, pc.citation_type FROM patents p JOIN patent_citations pc ON p.id = pc.cited_patent_id WHERE pc.citing_patent_id = ?',
        [patentId]
      );
      
      if (citedPatents && citedPatents.length > 0) {
        for (const pat of citedPatents) {
          const patNodeId = `patent-${pat.id}`;
          
          // Füge den Patentknoten hinzu, falls noch nicht vorhanden
          if (!network.nodes.some(node => node.id === patNodeId)) {
            network.nodes.push({
              id: patNodeId,
              type: 'patent',
              label: pat.title,
              patentId: pat.id,
              patentOffice: pat.patent_office
            });
          }
          
          // Füge die Kante hinzu
          network.edges.push({
            source: `patent-${patentId}`,
            target: patNodeId,
            type: 'patent_cites_patent',
            citationType: pat.citation_type
          });
          
          // Rekursiver Aufruf für das Patent
          await this.buildNetworkFromPatent(pat.id, network, depth - 1, visited);
        }
      }
      
      // Finde Patente, die das aktuelle Patent zitieren
      const citingPatents = await this.databaseService.query(
        'SELECT p.*, pc.citation_context, pc.citation_type FROM patents p JOIN patent_citations pc ON p.id = pc.citing_patent_id WHERE pc.cited_patent_id = ?',
        [patentId]
      );
      
      if (citingPatents && citingPatents.length > 0) {
        for (const pat of citingPatents) {
          const patNodeId = `patent-${pat.id}`;
          
          // Füge den Patentknoten hinzu, falls noch nicht vorhanden
          if (!network.nodes.some(node => node.id === patNodeId)) {
            network.nodes.push({
              id: patNodeId,
              type: 'patent',
              label: pat.title,
              patentId: pat.id,
              patentOffice: pat.patent_office
            });
          }
          
          // Füge die Kante hinzu
          network.edges.push({
            source: patNodeId,
            target: `patent-${patentId}`,
            type: 'patent_cites_patent',
            citationType: pat.citation_type
          });
          
          // Rekursiver Aufruf für das Patent
          await this.buildNetworkFromPatent(pat.id, network, depth - 1, visited);
        }
      }
    } catch (error) {
      logger.error('Fehler beim Aufbau des Netzwerks ausgehend vom Patent', {
        error: error.message,
        patentId
      });
    }
  }
  
  /**
   * Baut das Netzwerk ausgehend von einer Publikation auf
   * 
   * @param {string} doi - DOI der Publikation
   * @param {Object} network - Netzwerkobjekt
   * @param {number} depth - Verbleibende Tiefe
   * @param {Set} visited - Bereits besuchte Knoten
   * @returns {Promise<void>}
   */
  async buildNetworkFromPublication(doi, network, depth, visited = new Set()) {
    if (depth <= 0 || visited.has(`publication-${doi}`)) {
      return;
    }
    
    visited.add(`publication-${doi}`);
    
    try {
      // Finde Patente, die von der Publikation zitiert werden
      const citedPatents = await this.databaseService.query(
        'SELECT p.*, ppc.citation_context, ppc.confidence FROM patents p JOIN patent_publication_citations ppc ON p.id = ppc.patent_id WHERE ppc.doi = ? AND ppc.citation_direction = ?',
        [doi, 'publication_cites_patent']
      );
      
      if (citedPatents && citedPatents.length > 0) {
        for (const pat of citedPatents) {
          const patNodeId = `patent-${pat.id}`;
          
          // Füge den Patentknoten hinzu, falls noch nicht vorhanden
          if (!network.nodes.some(node => node.id === patNodeId)) {
            network.nodes.push({
              id: patNodeId,
              type: 'patent',
              label: pat.title,
              patentId: pat.id,
              patentOffice: pat.patent_office
            });
          }
          
          // Füge die Kante hinzu
          network.edges.push({
            source: `publication-${doi}`,
            target: patNodeId,
            type: 'publication_cites_patent',
            confidence: pat.confidence
          });
          
          // Rekursiver Aufruf für das Patent
          await this.buildNetworkFromPatent(pat.id, network, depth - 1, visited);
        }
      }
      
      // Finde Patente, die die Publikation zitieren
      const citingPatents = await this.databaseService.query(
        'SELECT p.*, ppc.citation_context, ppc.confidence FROM patents p JOIN patent_publication_citations ppc ON p.id = ppc.patent_id WHERE ppc.doi = ? AND ppc.citation_direction = ?',
        [doi, 'patent_cites_publication']
      );
      
      if (citingPatents && citingPatents.length > 0) {
        for (const pat of citingPatents) {
          const patNodeId = `patent-${pat.id}`;
          
          // Füge den Patentknoten hinzu, falls noch nicht vorhanden
          if (!network.nodes.some(node => node.id === patNodeId)) {
            network.nodes.push({
              id: patNodeId,
              type: 'patent',
              label: pat.title,
              patentId: pat.id,
              patentOffice: pat.patent_office
            });
          }
          
          // Füge die Kante hinzu
          network.edges.push({
            source: patNodeId,
            target: `publication-${doi}`,
            type: 'patent_cites_publication',
            confidence: pat.confidence
          });
          
          // Rekursiver Aufruf für das Patent
          await this.buildNetworkFromPatent(pat.id, network, depth - 1, visited);
        }
      }
      
      // Finde Publikationen, die von der aktuellen Publikation zitiert werden
      const citedPublications = await this.databaseService.query(
        'SELECT p.*, cr.context FROM publications p JOIN citation_relationships cr ON p.doi = cr.cited_doi WHERE cr.citing_doi = ?',
        [doi]
      );
      
      if (citedPublications && citedPublications.length > 0) {
        for (const pub of citedPublications) {
          const pubNodeId = `publication-${pub.doi}`;
          
          // Füge den Publikationsknoten hinzu, falls noch nicht vorhanden
          if (!network.nodes.some(node => node.id === pubNodeId)) {
            network.nodes.push({
              id: pubNodeId,
              type: 'publication',
              label: pub.title,
              doi: pub.doi,
              authors: pub.authors
            });
          }
          
          // Füge die Kante hinzu
          network.edges.push({
            source: `publication-${doi}`,
            target: pubNodeId,
            type: 'publication_cites_publication'
          });
          
          // Rekursiver Aufruf für die Publikation
          await this.buildNetworkFromPublication(pub.doi, network, depth - 1, visited);
        }
      }
      
      // Finde Publikationen, die die aktuelle Publikation zitieren
      const citingPublications = await this.databaseService.query(
        'SELECT p.*, cr.context FROM publications p JOIN citation_relationships cr ON p.doi = cr.citing_doi WHERE cr.cited_doi = ?',
        [doi]
      );
      
      if (citingPublications && citingPublications.length > 0) {
        for (const pub of citingPublications) {
          const pubNodeId = `publication-${pub.doi}`;
          
          // Füge den Publikationsknoten hinzu, falls noch nicht vorhanden
          if (!network.nodes.some(node => node.id === pubNodeId)) {
            network.nodes.push({
              id: pubNodeId,
              type: 'publication',
              label: pub.title,
              doi: pub.doi,
              authors: pub.authors
            });
          }
          
          // Füge die Kante hinzu
          network.edges.push({
            source: pubNodeId,
            target: `publication-${doi}`,
            type: 'publication_cites_publication'
          });
          
          // Rekursiver Aufruf für die Publikation
          await this.buildNetworkFromPublication(pub.doi, network, depth - 1, visited);
        }
      }
    } catch (error) {
      logger.error('Fehler beim Aufbau des Netzwerks ausgehend von der Publikation', {
        error: error.message,
        doi
      });
    }
  }
  
  /**
   * Berechnet Metriken für das Zitationsnetzwerk
   * 
   * @param {Object} network - Netzwerkobjekt
   * @returns {Object} Berechnete Metriken
   */
  calculateNetworkMetrics(network) {
    const metrics = {
      nodeCount: network.nodes.length,
      edgeCount: network.edges.length,
      patentCount: network.nodes.filter(node => node.type === 'patent').length,
      publicationCount: network.nodes.filter(node => node.type === 'publication').length,
      density: 0,
      centralNodes: []
    };
    
    // Berechne die Netzwerkdichte
    if (metrics.nodeCount > 1) {
      metrics.density = (2 * metrics.edgeCount) / (metrics.nodeCount * (metrics.nodeCount - 1));
    }
    
    // Berechne den Eingangs- und Ausgangsgrad für jeden Knoten
    const nodeDegrees = {};
    
    for (const node of network.nodes) {
      nodeDegrees[node.id] = {
        id: node.id,
        type: node.type,
        label: node.label,
        inDegree: 0,
        outDegree: 0
      };
    }
    
    for (const edge of network.edges) {
      if (nodeDegrees[edge.source]) {
        nodeDegrees[edge.source].outDegree++;
      }
      
      if (nodeDegrees[edge.target]) {
        nodeDegrees[edge.target].inDegree++;
      }
    }
    
    // Berechne den Gesamtgrad und sortiere die Knoten
    const nodeDegreesArray = Object.values(nodeDegrees).map(node => ({
      ...node,
      totalDegree: node.inDegree + node.outDegree
    }));
    
    nodeDegreesArray.sort((a, b) => b.totalDegree - a.totalDegree);
    
    // Identifiziere die zentralen Knoten (Top 5)
    metrics.centralNodes = nodeDegreesArray.slice(0, 5);
    
    // Berechne die durchschnittlichen Grade
    metrics.avgInDegree = nodeDegreesArray.reduce((sum, node) => sum + node.inDegree, 0) / metrics.nodeCount;
    metrics.avgOutDegree = nodeDegreesArray.reduce((sum, node) => sum + node.outDegree, 0) / metrics.nodeCount;
    metrics.avgTotalDegree = nodeDegreesArray.reduce((sum, node) => sum + node.totalDegree, 0) / metrics.nodeCount;
    
    return metrics;
  }
}