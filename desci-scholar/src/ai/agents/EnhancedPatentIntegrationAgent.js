/**
 * @fileoverview Erweiterter KI-Agent für die automatisierte Patentintegration und -analyse
 * 
 * Dieser Agent verbindet sich mit den APIs großer Patentämter, integriert Patentdaten
 * in die DeSci-Scholar-Plattform und verknüpft sie mit DOIs und NFTs. Er nutzt den
 * PatentCitationAnalyzer für erweiterte Zitationsanalysen.
 */

import axios from 'axios';
import { logger } from '../../utils/logger.js';
import { BaseAgent } from './index.js';
import { PatentCitationAnalyzer } from './PatentCitationAnalyzer.js';

/**
 * Erweiterter Agent für die Patentintegration und -analyse
 */
export class EnhancedPatentIntegrationAgent extends BaseAgent {
  /**
   * Erstellt eine neue Instanz des EnhancedPatentIntegrationAgent
   * @param {Object} options - Konfigurationsoptionen
   * @param {Object} options.databaseService - Datenbankdienst
   * @param {Object} options.blockchainService - Blockchain-Dienst für NFT-Operationen
   * @param {Object} options.ipfsService - IPFS-Dienst für Metadatenspeicherung
   * @param {Object} options.textAnalysisService - Textanalyse-Dienst (optional)
   * @param {Object} options.patentApiConfig - Konfiguration für Patent-APIs
   * @param {Object} options.config - Weitere Konfigurationsoptionen
   */
  constructor(options = {}) {
    super('EnhancedPatentIntegrationAgent');
    
    const {
      databaseService,
      blockchainService,
      ipfsService,
      textAnalysisService,
      patentApiConfig = {},
      config = {}
    } = options;
    
    this.databaseService = databaseService;
    this.blockchainService = blockchainService;
    this.ipfsService = ipfsService;
    this.textAnalysisService = textAnalysisService;
    
    // Konfiguration für Patent-APIs
    this.patentApiConfig = {
      uspto: {
        baseUrl: 'https://developer.uspto.gov/ibd-api/v1',
        apiKey: patentApiConfig.usptoApiKey || process.env.USPTO_API_KEY,
        rateLimit: 5 // Anfragen pro Sekunde
      },
      epo: {
        baseUrl: 'https://data.epo.org/linked-data/api/v1',
        apiKey: patentApiConfig.epoApiKey || process.env.EPO_API_KEY,
        rateLimit: 3 // Anfragen pro Sekunde
      },
      wipo: {
        baseUrl: 'https://patentscope.wipo.int/api',
        apiKey: patentApiConfig.wipoApiKey || process.env.WIPO_API_KEY,
        rateLimit: 2 // Anfragen pro Sekunde
      }
    };
    
    // Allgemeine Konfiguration
    this.config = {
      batchSize: 10,
      maxRetries: 3,
      retryDelay: 1000,
      cacheExpiration: 86400000, // 24 Stunden in Millisekunden
      ...config
    };
    
    // Initialisiere den PatentCitationAnalyzer
    this.patentCitationAnalyzer = new PatentCitationAnalyzer({
      databaseService,
      textAnalysisService
    });
    
    logger.info('EnhancedPatentIntegrationAgent initialisiert');
  }
  
  /**
   * Initialisiert den Agent
   */
  async initialize() {
    logger.info('Initialisiere EnhancedPatentIntegrationAgent');
    
    // Prüfe, ob die erforderlichen Dienste verfügbar sind
    if (!this.databaseService) {
      throw new Error('DatabaseService ist erforderlich');
    }
    
    if (!this.blockchainService) {
      throw new Error('BlockchainService ist erforderlich');
    }
    
    if (!this.ipfsService) {
      throw new Error('IPFSService ist erforderlich');
    }
    
    // Initialisiere den PatentCitationAnalyzer
    await this.patentCitationAnalyzer.initialize();
    
    // Initialisiere die Datenbanktabellen
    await this.initializeDatabaseTables();
    
    logger.info('EnhancedPatentIntegrationAgent erfolgreich initialisiert');
  }
  
  /**
   * Initialisiert die erforderlichen Datenbanktabellen
   */
  async initializeDatabaseTables() {
    try {
      // Prüfe, ob die Patenttabelle existiert
      const patentTableExists = await this.databaseService.tableExists('patents');
      
      if (!patentTableExists) {
        // Erstelle die Patenttabelle
        await this.databaseService.query(`
          CREATE TABLE patents (
            id VARCHAR(50) PRIMARY KEY,
            title TEXT NOT NULL,
            abstract TEXT,
            inventors TEXT,
            assignees TEXT,
            filing_date DATE,
            grant_date DATE,
            patent_office VARCHAR(10),
            patent_type VARCHAR(20),
            classification_codes TEXT,
            full_text LONGTEXT,
            metadata JSON,
            nft_token_id VARCHAR(100),
            nft_contract_address VARCHAR(100),
            nft_metadata_uri VARCHAR(255),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
          )
        `);
        
        logger.info('Patenttabelle erstellt');
      }
      
      // Prüfe, ob die Patent-DOI-Beziehungstabelle existiert
      const patentDoiTableExists = await this.databaseService.tableExists('patent_doi_relationships');
      
      if (!patentDoiTableExists) {
        // Erstelle die Patent-DOI-Beziehungstabelle
        await this.databaseService.query(`
          CREATE TABLE patent_doi_relationships (
            id INT AUTO_INCREMENT PRIMARY KEY,
            patent_id VARCHAR(50) NOT NULL,
            doi VARCHAR(255) NOT NULL,
            relationship_type VARCHAR(50),
            confidence FLOAT,
            metadata JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY patent_doi_unique (patent_id, doi),
            FOREIGN KEY (patent_id) REFERENCES patents(id) ON DELETE CASCADE
          )
        `);
        
        logger.info('Patent-DOI-Beziehungstabelle erstellt');
      }
      
      // Prüfe, ob die Patent-Zitationstabelle existiert
      const patentCitationTableExists = await this.databaseService.tableExists('patent_citations');
      
      if (!patentCitationTableExists) {
        // Erstelle die Patent-Zitationstabelle
        await this.databaseService.query(`
          CREATE TABLE patent_citations (
            id INT AUTO_INCREMENT PRIMARY KEY,
            citing_patent_id VARCHAR(50) NOT NULL,
            cited_patent_id VARCHAR(50) NOT NULL,
            citation_type VARCHAR(50),
            citation_category VARCHAR(50),
            citation_context TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY patent_citation_unique (citing_patent_id, cited_patent_id),
            FOREIGN KEY (citing_patent_id) REFERENCES patents(id) ON DELETE CASCADE,
            FOREIGN KEY (cited_patent_id) REFERENCES patents(id) ON DELETE CASCADE
          )
        `);
        
        logger.info('Patent-Zitationstabelle erstellt');
      }
    } catch (error) {
      logger.error('Fehler bei der Initialisierung der Datenbanktabellen', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Sucht nach Patenten basierend auf verschiedenen Kriterien
   * 
   * @param {Object} searchParams - Suchparameter
   * @param {string} searchParams.query - Suchbegriff
   * @param {string} searchParams.inventor - Name des Erfinders
   * @param {string} searchParams.assignee - Name des Patentinhabers
   * @param {string} searchParams.patentOffice - Patentamt (USPTO, EPO, WIPO)
   * @param {string} searchParams.dateRange - Datumsbereich (z.B. "2010-01-01:2020-12-31")
   * @param {string} searchParams.classification - Klassifikationscode
   * @param {number} searchParams.limit - Maximale Anzahl von Ergebnissen
   * @returns {Promise<Array>} Gefundene Patente
   */
  async searchPatents(searchParams) {
    try {
      logger.info('Suche nach Patenten', { searchParams });
      
      const { 
        query, 
        inventor, 
        assignee, 
        patentOffice = 'all', 
        dateRange, 
        classification,
        limit = 100
      } = searchParams;
      
      // Bestimme, welche Patentämter durchsucht werden sollen
      const patentOffices = patentOffice === 'all' 
        ? ['uspto', 'epo', 'wipo'] 
        : [patentOffice.toLowerCase()];
      
      // Sammle Ergebnisse von allen angegebenen Patentämtern
      const allResults = [];
      
      for (const office of patentOffices) {
        if (!this.patentApiConfig[office]) {
          logger.warn(`Konfiguration für Patentamt ${office} nicht gefunden`);
          continue;
        }
        
        const apiConfig = this.patentApiConfig[office];
        
        // Baue die Suchanfrage basierend auf dem Patentamt
        let searchResults;
        
        switch (office) {
          case 'uspto':
            searchResults = await this.searchUsptoPatents(apiConfig, searchParams);
            break;
          case 'epo':
            searchResults = await this.searchEpoPatents(apiConfig, searchParams);
            break;
          case 'wipo':
            searchResults = await this.searchWipoPatents(apiConfig, searchParams);
            break;
          default:
            logger.warn(`Nicht unterstütztes Patentamt: ${office}`);
            continue;
        }
        
        // Füge die Ergebnisse zur Gesamtliste hinzu
        allResults.push(...searchResults);
        
        // Begrenze die Gesamtanzahl der Ergebnisse
        if (allResults.length >= limit) {
          allResults.length = limit;
          break;
        }
      }
      
      logger.info(`${allResults.length} Patente gefunden`);
      
      return allResults;
    } catch (error) {
      logger.error('Fehler bei der Patentsuche', {
        error: error.message,
        searchParams
      });
      throw error;
    }
  }
  
  /**
   * Sucht nach Patenten im USPTO (US Patent and Trademark Office)
   * 
   * @param {Object} apiConfig - API-Konfiguration
   * @param {Object} searchParams - Suchparameter
   * @returns {Promise<Array>} Gefundene Patente
   */
  async searchUsptoPatents(apiConfig, searchParams) {
    try {
      const { 
        query, 
        inventor, 
        assignee, 
        dateRange, 
        classification,
        limit = 100
      } = searchParams;
      
      // Baue die Suchanfrage
      let queryString = '';
      
      if (query) {
        queryString += `${query}`;
      }
      
      if (inventor) {
        queryString += queryString ? ` AND inventor:(${inventor})` : `inventor:(${inventor})`;
      }
      
      if (assignee) {
        queryString += queryString ? ` AND assignee:(${assignee})` : `assignee:(${assignee})`;
      }
      
      if (dateRange) {
        const [startDate, endDate] = dateRange.split(':');
        queryString += queryString ? ` AND grantDate:[${startDate} TO ${endDate}]` : `grantDate:[${startDate} TO ${endDate}]`;
      }
      
      if (classification) {
        queryString += queryString ? ` AND classification:(${classification})` : `classification:(${classification})`;
      }
      
      // Führe die Suchanfrage aus
      const response = await axios.get(`${apiConfig.baseUrl}/patent/search`, {
        params: {
          q: queryString,
          rows: limit
        },
        headers: {
          'X-API-Key': apiConfig.apiKey,
          'Accept': 'application/json'
        }
      });
      
      // Verarbeite die Ergebnisse
      const patents = response.data.results.map(patent => ({
        id: patent.patentNumber,
        title: patent.title,
        abstract: patent.abstract,
        inventors: patent.inventors.join(', '),
        assignees: patent.assignees.join(', '),
        filingDate: patent.filingDate,
        grantDate: patent.grantDate,
        patentOffice: 'USPTO',
        patentType: patent.patentType,
        classificationCodes: patent.classificationCodes.join(', '),
        source: 'uspto'
      }));
      
      return patents;
    } catch (error) {
      logger.error('Fehler bei der USPTO-Patentsuche', {
        error: error.message,
        searchParams
      });
      return [];
    }
  }
  
  /**
   * Sucht nach Patenten im EPO (European Patent Office)
   * 
   * @param {Object} apiConfig - API-Konfiguration
   * @param {Object} searchParams - Suchparameter
   * @returns {Promise<Array>} Gefundene Patente
   */
  async searchEpoPatents(apiConfig, searchParams) {
    try {
      const { 
        query, 
        inventor, 
        assignee, 
        dateRange, 
        classification,
        limit = 100
      } = searchParams;
      
      // Baue die Suchanfrage
      let queryString = '';
      
      if (query) {
        queryString += `${query}`;
      }
      
      if (inventor) {
        queryString += queryString ? ` AND inventor=${inventor}` : `inventor=${inventor}`;
      }
      
      if (assignee) {
        queryString += queryString ? ` AND applicant=${assignee}` : `applicant=${assignee}`;
      }
      
      if (dateRange) {
        const [startDate, endDate] = dateRange.split(':');
        queryString += queryString ? ` AND pd within "${startDate} ${endDate}"` : `pd within "${startDate} ${endDate}"`;
      }
      
      if (classification) {
        queryString += queryString ? ` AND cl=${classification}` : `cl=${classification}`;
      }
      
      // Führe die Suchanfrage aus
      const response = await axios.get(`${apiConfig.baseUrl}/search`, {
        params: {
          q: queryString,
          limit
        },
        headers: {
          'Authorization': `Bearer ${apiConfig.apiKey}`,
          'Accept': 'application/json'
        }
      });
      
      // Verarbeite die Ergebnisse
      const patents = response.data.results.map(patent => ({
        id: patent.publicationNumber,
        title: patent.title,
        abstract: patent.abstract,
        inventors: patent.inventors.join(', '),
        assignees: patent.applicants.join(', '),
        filingDate: patent.filingDate,
        grantDate: patent.publicationDate,
        patentOffice: 'EPO',
        patentType: patent.publicationKind,
        classificationCodes: patent.classifications.join(', '),
        source: 'epo'
      }));
      
      return patents;
    } catch (error) {
      logger.error('Fehler bei der EPO-Patentsuche', {
        error: error.message,
        searchParams
      });
      return [];
    }
  }
  
  /**
   * Sucht nach Patenten im WIPO (World Intellectual Property Organization)
   * 
   * @param {Object} apiConfig - API-Konfiguration
   * @param {Object} searchParams - Suchparameter
   * @returns {Promise<Array>} Gefundene Patente
   */
  async searchWipoPatents(apiConfig, searchParams) {
    try {
      const { 
        query, 
        inventor, 
        assignee, 
        dateRange, 
        classification,
        limit = 100
      } = searchParams;
      
      // Baue die Suchanfrage
      let queryString = '';
      
      if (query) {
        queryString += `${query}`;
      }
      
      if (inventor) {
        queryString += queryString ? ` AND IN:(${inventor})` : `IN:(${inventor})`;
      }
      
      if (assignee) {
        queryString += queryString ? ` AND PA:(${assignee})` : `PA:(${assignee})`;
      }
      
      if (dateRange) {
        const [startDate, endDate] = dateRange.split(':');
        queryString += queryString ? ` AND PD:[${startDate} TO ${endDate}]` : `PD:[${startDate} TO ${endDate}]`;
      }
      
      if (classification) {
        queryString += queryString ? ` AND IC:(${classification})` : `IC:(${classification})`;
      }
      
      // Führe die Suchanfrage aus
      const response = await axios.get(`${apiConfig.baseUrl}/search`, {
        params: {
          q: queryString,
          limit
        },
        headers: {
          'Authorization': `Bearer ${apiConfig.apiKey}`,
          'Accept': 'application/json'
        }
      });
      
      // Verarbeite die Ergebnisse
      const patents = response.data.results.map(patent => ({
        id: patent.wo,
        title: patent.title,
        abstract: patent.abstract,
        inventors: patent.inventors.join(', '),
        assignees: patent.applicants.join(', '),
        filingDate: patent.filingDate,
        grantDate: patent.publicationDate,
        patentOffice: 'WIPO',
        patentType: 'PCT',
        classificationCodes: patent.ipcClassifications.join(', '),
        source: 'wipo'
      }));
      
      return patents;
    } catch (error) {
      logger.error('Fehler bei der WIPO-Patentsuche', {
        error: error.message,
        searchParams
      });
      return [];
    }
  }
  
  /**
   * Holt detaillierte Informationen zu einem Patent
   * 
   * @param {string} patentId - Patent-ID
   * @param {string} patentOffice - Patentamt (USPTO, EPO, WIPO)
   * @returns {Promise<Object>} Detaillierte Patentinformationen
   */
  async getPatentDetails(patentId, patentOffice) {
    try {
      logger.info(`Hole Patentdetails für ${patentId} vom ${patentOffice}`);
      
      // Prüfe, ob das Patent bereits in der Datenbank existiert
      const existingPatent = await this.databaseService.query(
        'SELECT * FROM patents WHERE id = ?',
        [patentId]
      );
      
      if (existingPatent && existingPatent.length > 0) {
        logger.info(`Patent ${patentId} in der Datenbank gefunden`);
        return existingPatent[0];
      }
      
      // Bestimme das Patentamt und hole die Details
      const office = patentOffice.toLowerCase();
      
      if (!this.patentApiConfig[office]) {
        throw new Error(`Konfiguration für Patentamt ${patentOffice} nicht gefunden`);
      }
      
      const apiConfig = this.patentApiConfig[office];
      
      let patentDetails;
      
      switch (office) {
        case 'uspto':
          patentDetails = await this.getUsptoPatentDetails(apiConfig, patentId);
          break;
        case 'epo':
          patentDetails = await this.getEpoPatentDetails(apiConfig, patentId);
          break;
        case 'wipo':
          patentDetails = await this.getWipoPatentDetails(apiConfig, patentId);
          break;
        default:
          throw new Error(`Nicht unterstütztes Patentamt: ${patentOffice}`);
      }
      
      // Speichere die Patentdetails in der Datenbank
      await this.savePatentToDatabase(patentDetails);
      
      return patentDetails;
    } catch (error) {
      logger.error('Fehler beim Abrufen der Patentdetails', {
        error: error.message,
        patentId,
        patentOffice
      });
      throw error;
    }
  }
  
  /**
   * Speichert ein Patent in der Datenbank
   * 
   * @param {Object} patent - Patentdaten
   * @returns {Promise<void>}
   */
  async savePatentToDatabase(patent) {
    try {
      logger.info(`Speichere Patent ${patent.id} in der Datenbank`);
      
      // Prüfe, ob das Patent bereits existiert
      const existingPatent = await this.databaseService.query(
        'SELECT id FROM patents WHERE id = ?',
        [patent.id]
      );
      
      if (existingPatent && existingPatent.length > 0) {
        // Aktualisiere das bestehende Patent
        await this.databaseService.query(
          `UPDATE patents SET 
            title = ?, 
            abstract = ?, 
            inventors = ?, 
            assignees = ?, 
            filing_date = ?, 
            grant_date = ?, 
            patent_office = ?, 
            patent_type = ?, 
            classification_codes = ?, 
            full_text = ?, 
            metadata = ?
          WHERE id = ?`,
          [
            patent.title,
            patent.abstract,
            patent.inventors,
            patent.assignees,
            patent.filingDate,
            patent.grantDate,
            patent.patentOffice,
            patent.patentType,
            patent.classificationCodes,
            patent.fullText || null,
            JSON.stringify(patent.metadata || {}),
            patent.id
          ]
        );
        
        logger.info(`Patent ${patent.id} aktualisiert`);
      } else {
        // Füge ein neues Patent hinzu
        await this.databaseService.query(
          `INSERT INTO patents (
            id, title, abstract, inventors, assignees, filing_date, grant_date, 
            patent_office, patent_type, classification_codes, full_text, metadata
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            patent.id,
            patent.title,
            patent.abstract,
            patent.inventors,
            patent.assignees,
            patent.filingDate,
            patent.grantDate,
            patent.patentOffice,
            patent.patentType,
            patent.classificationCodes,
            patent.fullText || null,
            JSON.stringify(patent.metadata || {})
          ]
        );
        
        logger.info(`Patent ${patent.id} zur Datenbank hinzugefügt`);
      }
    } catch (error) {
      logger.error('Fehler beim Speichern des Patents in der Datenbank', {
        error: error.message,
        patentId: patent.id
      });
      throw error;
    }
  }
  
  /**
   * Verknüpft ein Patent mit einer DOI
   * 
   * @param {string} patentId - Patent-ID
   * @param {string} doi - DOI der Publikation
   * @param {string} relationshipType - Art der Beziehung (z.B. 'cites', 'citedBy', 'related')
   * @param {number} confidence - Konfidenzwert der Beziehung (0-1)
   * @returns {Promise<Object>} Ergebnis der Verknüpfung
   */
  async linkPatentToDoi(patentId, doi, relationshipType = 'related', confidence = 1.0) {
    try {
      logger.info(`Verknüpfe Patent ${patentId} mit DOI ${doi}`);
      
      // Prüfe, ob das Patent existiert
      const patent = await this.databaseService.query(
        'SELECT id FROM patents WHERE id = ?',
        [patentId]
      );
      
      if (!patent || patent.length === 0) {
        throw new Error(`Patent ${patentId} nicht gefunden`);
      }
      
      // Prüfe, ob die DOI existiert
      const publication = await this.databaseService.query(
        'SELECT doi FROM publications WHERE doi = ?',
        [doi]
      );
      
      if (!publication || publication.length === 0) {
        throw new Error(`Publikation mit DOI ${doi} nicht gefunden`);
      }
      
      // Prüfe, ob die Beziehung bereits existiert
      const existingRelationship = await this.databaseService.query(
        'SELECT id FROM patent_doi_relationships WHERE patent_id = ? AND doi = ?',
        [patentId, doi]
      );
      
      if (existingRelationship && existingRelationship.length > 0) {
        // Aktualisiere die bestehende Beziehung
        await this.databaseService.query(
          'UPDATE patent_doi_relationships SET relationship_type = ?, confidence = ? WHERE id = ?',
          [relationshipType, confidence, existingRelationship[0].id]
        );
        
        logger.info(`Beziehung zwischen Patent ${patentId} und DOI ${doi} aktualisiert`);
      } else {
        // Füge eine neue Beziehung hinzu
        await this.databaseService.query(
          'INSERT INTO patent_doi_relationships (patent_id, doi, relationship_type, confidence) VALUES (?, ?, ?, ?)',
          [patentId, doi, relationshipType, confidence]
        );
        
        logger.info(`Beziehung zwischen Patent ${patentId} und DOI ${doi} erstellt`);
      }
      
      // Analysiere die Zitationsbeziehungen
      await this.patentCitationAnalyzer.analyzeCitationRelationship(patentId, doi);
      
      return {
        success: true,
        patentId,
        doi,
        relationshipType,
        confidence
      };
    } catch (error) {
      logger.error('Fehler beim Verknüpfen von Patent und DOI', {
        error: error.message,
        patentId,
        doi
      });
      
      return {
        success: false,
        patentId,
        doi,
        error: error.message
      };
    }
  }
  
  /**
   * Erstellt einen NFT für ein Patent
   * 
   * @param {string} patentId - Patent-ID
   * @param {Object} nftMetadata - Zusätzliche Metadaten für den NFT
   * @returns {Promise<Object>} Ergebnis der NFT-Erstellung
   */
  async createPatentNft(patentId, nftMetadata = {}) {
    try {
      logger.info(`Erstelle NFT für Patent ${patentId}`);
      
      // Prüfe, ob das Patent existiert
      const patents = await this.databaseService.query(
        'SELECT * FROM patents WHERE id = ?',
        [patentId]
      );
      
      if (!patents || patents.length === 0) {
        throw new Error(`Patent ${patentId} nicht gefunden`);
      }
      
      const patent = patents[0];
      
      // Prüfe, ob bereits ein NFT für dieses Patent existiert
      if (patent.nft_token_id) {
        logger.info(`Patent ${patentId} hat bereits einen NFT mit Token-ID ${patent.nft_token_id}`);
        
        return {
          success: true,
          patentId,
          tokenId: patent.nft_token_id,
          contractAddress: patent.nft_contract_address,
          metadataUri: patent.nft_metadata_uri,
          alreadyExists: true
        };
      }
      
      // Erstelle die Metadaten für den NFT
      const metadata = {
        name: `Patent: ${patent.title}`,
        description: patent.abstract,
        external_url: `https://desci-scholar.org/patents/${patentId}`,
        image: nftMetadata.image || 'https://desci-scholar.org/assets/patent-nft-placeholder.png',
        attributes: [
          {
            trait_type: 'Patent ID',
            value: patentId
          },
          {
            trait_type: 'Patent Office',
            value: patent.patent_office
          },
          {
            trait_type: 'Filing Date',
            value: patent.filing_date
          },
          {
            trait_type: 'Grant Date',
            value: patent.grant_date
          },
          {
            trait_type: 'Inventors',
            value: patent.inventors
          }
        ],
        ...nftMetadata
      };
      
      // Speichere die Metadaten auf IPFS
      const ipfsResult = await this.ipfsService.addJson(metadata);
      const metadataUri = `ipfs://${ipfsResult.cid}`;
      
      // Präge den NFT
      const mintResult = await this.blockchainService.mintNft(
        metadataUri,
        nftMetadata.recipient || process.env.DEFAULT_NFT_RECIPIENT
      );
      
      // Aktualisiere die Patentdaten in der Datenbank
      await this.databaseService.query(
        'UPDATE patents SET nft_token_id = ?, nft_contract_address = ?, nft_metadata_uri = ? WHERE id = ?',
        [mintResult.tokenId, mintResult.contractAddress, metadataUri, patentId]
      );
      
      logger.info(`NFT für Patent ${patentId} erstellt mit Token-ID ${mintResult.tokenId}`);
      
      return {
        success: true,
        patentId,
        tokenId: mintResult.tokenId,
        contractAddress: mintResult.contractAddress,
        metadataUri,
        transactionHash: mintResult.transactionHash
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen des Patent-NFT', {
        error: error.message,
        patentId
      });
      
      return {
        success: false,
        patentId,
        error: error.message
      };
    }
  }
  
  /**
   * Analysiert die Beziehungen zwischen Patenten und wissenschaftlichen Publikationen
   * 
   * @param {string} patentId - Patent-ID (optional, wenn alle Patente analysiert werden sollen)
   * @returns {Promise<Object>} Ergebnis der Analyse
   */
  async analyzePatentPublicationRelationships(patentId = null) {
    try {
      logger.info('Analysiere Beziehungen zwischen Patenten und Publikationen', {
        patentId: patentId || 'all'
      });
      
      // Bestimme die zu analysierenden Patente
      let patents;
      
      if (patentId) {
        patents = await this.databaseService.query(
          'SELECT * FROM patents WHERE id = ?',
          [patentId]
        );
        
        if (!patents || patents.length === 0) {
          throw new Error(`Patent ${patentId} nicht gefunden`);
        }
      } else {
        // Hole alle Patente, die noch nicht analysiert wurden oder aktualisiert werden müssen
        patents = await this.databaseService.query(
          'SELECT * FROM patents WHERE updated_at > (SELECT MAX(created_at) FROM patent_doi_relationships WHERE patent_id = patents.id) OR NOT EXISTS (SELECT 1 FROM patent_doi_relationships WHERE patent_id = patents.id) LIMIT ?',
          [this.config.batchSize]
        );
      }
      
      if (!patents || patents.length === 0) {
        logger.info('Keine Patente zur Analyse gefunden');
        return { success: true, patentsAnalyzed: 0 };
      }
      
      // Analysiere jedes Patent
      const results = [];
      
      for (const patent of patents) {
        // Verwende den PatentCitationAnalyzer, um Beziehungen zu finden
        const relationshipResults = await this.patentCitationAnalyzer.findRelatedPublications(patent.id);
        
        // Speichere die gefundenen Beziehungen
        for (const relationship of relationshipResults.relationships) {
          await this.linkPatentToDoi(
            patent.id,
            relationship.doi,
            relationship.type,
            relationship.confidence
          );
        }
        
        results.push({
          patentId: patent.id,
          relationshipsFound: relationshipResults.relationships.length
        });
      }
      
      logger.info(`Analyse von ${patents.length} Patenten abgeschlossen`);
      
      return {
        success: true,
        patentsAnalyzed: patents.length,
        results
      };
    } catch (error) {
      logger.error('Fehler bei der Analyse von Patent-Publikations-Beziehungen', {
        error: error.message,
        patentId
      });
      
      return {
        success: false,
        error: error.message
      };
    }
  }
}