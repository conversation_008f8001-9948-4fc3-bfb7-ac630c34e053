/**
 * @fileoverview AI Agents for DeSci-Scholar
 * This module implements smart agents for automated tasks in the platform
 */

import axios from 'axios';
import dotenv from 'dotenv';

dotenv.config();

/**
 * Base Agent class with common functionality
 */
export class BaseAgent {
  /**
   * Creates an instance of BaseAgent
   * @param {string} name - Name of the agent
   */
  constructor(name) {
    this.name = name;
    this.apiKey = process.env.OPENAI_API_KEY;
    this.model = process.env.AI_AGENT_MODEL || 'gpt-4';
    
    if (!this.apiKey) {
      console.warn('OpenAI API key not found. AI functionality will be limited.');
    }
  }

  /**
   * Generate text using a language model
   * @param {string} prompt - Input prompt for the model
   * @param {Object} options - Generation options
   * @returns {Promise<string>} Generated text
   */
  async generateText(prompt, options = {}) {
    if (!this.apiKey) {
      throw new Error('OpenAI API key not configured');
    }
    
    try {
      const response = await axios.post(
        'https://api.openai.com/v1/chat/completions',
        {
          model: options.model || this.model,
          messages: [{ role: 'user', content: prompt }],
          temperature: options.temperature || 0.7,
          max_tokens: options.maxTokens || 500
        },
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json'
          }
        }
      );
      
      return response.data.choices[0].message.content;
    } catch (error) {
      console.error('Error generating text:', error);
      throw error;
    }
  }

  /**
   * Log agent activity
   * @param {string} action - Action performed
   * @param {Object} details - Details of the action
   */
  log(action, details) {
    console.log(`[${this.name}] ${action}:`, details);
    // In a production environment, this would log to a database or monitoring system
  }
}

/**
 * Peer Review Agent for automating the peer review process
 */
export class PeerReviewAgent extends BaseAgent {
  constructor() {
    super('PeerReviewAgent');
  }

  /**
   * Analyze a paper and generate a review
   * @param {Object} paper - Paper metadata and content
   * @returns {Promise<Object>} Review results
   */
  async reviewPaper(paper) {
    this.log('Reviewing paper', { title: paper.title, doi: paper.doi });
    
    const { title, abstract, content, authors } = paper;
    
    // Generate prompt for the review
    const prompt = `
    You are an expert scientific peer reviewer. Please review the following research paper:
    
    Title: ${title}
    Authors: <AUTHORS>
    Abstract: ${abstract}
    
    ${content ? `Content: ${content.substring(0, 1000)}...` : ''}
    
    Please provide a comprehensive review addressing the following aspects:
    1. Originality and significance of the research
    2. Methodology and technical soundness
    3. Quality of writing and presentation
    4. Strengths and weaknesses
    5. Recommendations for improvement
    
    Format your review in a structured manner with clear sections. Be constructive and specific in your feedback.
    `;
    
    // Generate the review
    const reviewText = await this.generateText(prompt, { 
      temperature: 0.3,
      maxTokens: 1000
    });
    
    // Generate a summary
    const summaryPrompt = `
    Summarize the key points of this peer review in 3-5 bullet points:
    
    ${reviewText}
    `;
    
    const summary = await this.generateText(summaryPrompt, {
      temperature: 0.3,
      maxTokens: 200
    });
    
    // Return structured review
    return {
      timestamp: new Date().toISOString(),
      paper: {
        title,
        doi: paper.doi
      },
      fullReview: reviewText,
      summary,
      agent: this.name,
      model: this.model
    };
  }

  /**
   * Check a paper for potential issues
   * @param {Object} paper - Paper metadata and content
   * @returns {Promise<Object>} Issues found
   */
  async checkPaperIssues(paper) {
    this.log('Checking paper for issues', { title: paper.title });
    
    const { title, abstract, content } = paper;
    
    // Generate prompt for issue detection
    const prompt = `
    You are an expert in scientific research integrity. Please analyze the following research paper for potential issues:
    
    Title: ${title}
    Abstract: ${abstract}
    
    ${content ? `Content: ${content.substring(0, 2000)}...` : ''}
    
    Please identify any potential issues in the following categories:
    1. Methodological flaws or inconsistencies
    2. Statistical errors or misinterpretations
    3. Incomplete or missing information
    4. Ethical concerns
    5. Citation or reference problems
    6. Potential plagiarism indicators
    
    For each identified issue, provide a brief explanation and suggestions for resolution.
    If no issues are found in a category, state "No issues detected" for that category.
    `;
    
    // Generate the analysis
    const analysisText = await this.generateText(prompt, { 
      temperature: 0.2,
      maxTokens: 800
    });
    
    // Parse the analysis to categorize issues
    const categories = [
      'Methodological flaws',
      'Statistical errors',
      'Incomplete information',
      'Ethical concerns',
      'Citation problems',
      'Plagiarism indicators'
    ];
    
    const issues = {};
    
    for (const category of categories) {
      const regex = new RegExp(`${category}[:\\s]+(.*?)(?=\\n\\d\\.|$)`, 's');
      const match = analysisText.match(regex);
      issues[category] = match ? match[1].trim() : 'Not analyzed';
    }
    
    return {
      timestamp: new Date().toISOString(),
      paper: {
        title,
        doi: paper.doi
      },
      issues,
      fullAnalysis: analysisText,
      hasSignificantIssues: !analysisText.includes('No issues detected'),
      agent: this.name,
      model: this.model
    };
  }
}

/**
 * Research Proposal Agent for vetting research proposals
 */
export class ProposalAgent extends BaseAgent {
  constructor() {
    super('ProposalAgent');
  }

  /**
   * Evaluate a research proposal
   * @param {Object} proposal - Research proposal data
   * @returns {Promise<Object>} Evaluation results
   */
  async evaluateProposal(proposal) {
    this.log('Evaluating proposal', { title: proposal.title });
    
    const { title, abstract, methodology, budget, timeline, team } = proposal;
    
    // Generate prompt for evaluation
    const prompt = `
    You are an expert in research funding and evaluation. Please evaluate the following research proposal:
    
    Title: ${title}
    Abstract: ${abstract}
    Methodology: ${methodology}
    Budget: ${budget}
    Timeline: ${timeline}
    Team: ${team}
    
    Please provide a comprehensive evaluation addressing the following aspects:
    1. Innovation and scientific merit
    2. Feasibility of the methodology and timeline
    3. Appropriateness of the budget
    4. Qualification of the research team
    5. Potential impact and significance
    6. Overall recommendation (Fund/Revise and Resubmit/Reject)
    
    For each aspect, provide a score from 1-10 and a brief justification.
    `;
    
    // Generate the evaluation
    const evaluationText = await this.generateText(prompt, { 
      temperature: 0.4,
      maxTokens: 1000
    });
    
    // Extract scores and recommendation
    const scoreRegex = /(\d+)\/10/g;
    const scores = [...evaluationText.matchAll(scoreRegex)].map(match => parseInt(match[1]));
    
    const recommendationRegex = /recommendation:?\s*(Fund|Revise and Resubmit|Reject)/i;
    const recommendationMatch = evaluationText.match(recommendationRegex);
    const recommendation = recommendationMatch ? recommendationMatch[1] : 'Not specified';
    
    const averageScore = scores.length > 0 
      ? scores.reduce((a, b) => a + b, 0) / scores.length 
      : null;
    
    return {
      timestamp: new Date().toISOString(),
      proposal: {
        title,
        id: proposal.id
      },
      evaluation: {
        fullText: evaluationText,
        averageScore,
        recommendation,
        scores
      },
      agent: this.name,
      model: this.model
    };
  }

  /**
   * Generate improvement suggestions for a proposal
   * @param {Object} proposal - Research proposal data
   * @returns {Promise<Object>} Improvement suggestions
   */
  async suggestImprovements(proposal) {
    this.log('Suggesting improvements', { title: proposal.title });
    
    const { title, abstract, methodology, budget, timeline, team } = proposal;
    
    // Generate prompt for suggestions
    const prompt = `
    You are an expert in research funding and proposal writing. Please provide constructive suggestions to improve the following research proposal:
    
    Title: ${title}
    Abstract: ${abstract}
    Methodology: ${methodology}
    Budget: ${budget}
    Timeline: ${timeline}
    Team: ${team}
    
    Please provide specific, actionable suggestions to improve the proposal in the following areas:
    1. Clarity and focus of the research question
    2. Methodology and technical approach
    3. Budget justification and allocation
    4. Timeline feasibility and milestones
    5. Team composition and expertise
    6. Potential impact and significance framing
    
    For each area, provide concrete examples and recommendations.
    `;
    
    // Generate the suggestions
    const suggestionsText = await this.generateText(prompt, { 
      temperature: 0.4,
      maxTokens: 1000
    });
    
    return {
      timestamp: new Date().toISOString(),
      proposal: {
        title,
        id: proposal.id
      },
      suggestions: suggestionsText,
      agent: this.name,
      model: this.model
    };
  }
}

/**
 * Collaboration Agent for matching researchers and projects
 */
export class CollaborationAgent extends BaseAgent {
  constructor() {
    super('CollaborationAgent');
  }

  /**
   * Find potential collaborators for a researcher or project
   * @param {Object} profile - Researcher or project profile
   * @param {Array} candidates - Pool of potential collaborators
   * @returns {Promise<Array>} Ranked list of potential collaborators
   */
  async findCollaborators(profile, candidates) {
    this.log('Finding collaborators', { 
      name: profile.name || profile.title,
      candidatesCount: candidates.length
    });
    
    // For each candidate, calculate a match score
    const matches = [];
    
    for (const candidate of candidates) {
      // Generate a prompt to evaluate the match
      const prompt = `
      You are an expert in scientific collaboration and team formation. Please evaluate the potential research collaboration match between the following:
      
      Profile:
      Name: ${profile.name || 'N/A'}
      Title: ${profile.title || 'N/A'}
      Expertise: ${profile.expertise ? profile.expertise.join(', ') : 'N/A'}
      Research Interests: ${profile.interests ? profile.interests.join(', ') : 'N/A'}
      Publications: ${profile.publications ? profile.publications.slice(0, 3).join(', ') : 'N/A'}
      
      Potential Collaborator:
      Name: ${candidate.name || 'N/A'}
      Title: ${candidate.title || 'N/A'}
      Expertise: ${candidate.expertise ? candidate.expertise.join(', ') : 'N/A'}
      Research Interests: ${candidate.interests ? candidate.interests.join(', ') : 'N/A'}
      Publications: ${candidate.publications ? candidate.publications.slice(0, 3).join(', ') : 'N/A'}
      
      Please evaluate this potential collaboration on the following aspects:
      1. Complementary expertise (score 1-10)
      2. Alignment of research interests (score 1-10)
      3. Potential for innovative outcomes (score 1-10)
      4. Overall collaboration fit (score 1-10)
      
      For each aspect, provide a score and a brief justification.
      Conclude with an overall recommendation on whether this collaboration should be pursued (Highly Recommended, Recommended, Neutral, Not Recommended).
      `;
      
      // Generate the evaluation
      const evaluationText = await this.generateText(prompt, { 
        temperature: 0.3,
        maxTokens: 600
      });
      
      // Extract scores and recommendation
      const scoreRegex = /(\d+)\/10/g;
      const scores = [...evaluationText.matchAll(scoreRegex)].map(match => parseInt(match[1]));
      
      const recommendationRegex = /(Highly Recommended|Recommended|Neutral|Not Recommended)/i;
      const recommendationMatch = evaluationText.match(recommendationRegex);
      const recommendation = recommendationMatch ? recommendationMatch[1] : 'Not specified';
      
      const averageScore = scores.length > 0 
        ? scores.reduce((a, b) => a + b, 0) / scores.length 
        : 0;
      
      matches.push({
        candidate: {
          name: candidate.name,
          id: candidate.id
        },
        evaluation: {
          text: evaluationText,
          scores,
          averageScore,
          recommendation
        }
      });
    }
    
    // Sort matches by average score in descending order
    matches.sort((a, b) => b.evaluation.averageScore - a.evaluation.averageScore);
    
    return {
      timestamp: new Date().toISOString(),
      profile: {
        name: profile.name || profile.title,
        id: profile.id
      },
      matches,
      topMatches: matches.slice(0, 5),
      agent: this.name,
      model: this.model
    };
  }
}

export {
  BaseAgent,
  BaseAgent,
  PeerReviewAgent,
  ProposalAgent,
  CollaborationAgent
};
