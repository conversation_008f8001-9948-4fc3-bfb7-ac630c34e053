/**
 * @fileoverview Speicher-Agent für DeSci-Scholar
 * 
 * Dieser Agent verwaltet die dezentrale Speicherung von wissenschaftlichen Publikationen,
 * Patenten und zugehörigen Metadaten. Er nutzt den StorageManager, um Daten flexibel
 * auf verschiedenen dezentralen Speicherlösungen wie IPFS und BitTorrent zu speichern.
 * Metadaten werden typischerweise auf IPFS gespeichert, während große Dateien über
 * BitTorrent verteilt werden.
 */

import { BaseAgent } from './index.js';
import { logger } from '../../utils/logger.js';
import { StorageManager } from '../../services/storage/StorageManager.js';

/**
 * Agent für die dezentrale Speicherung
 */
export class StorageAgent extends BaseAgent {
  /**
   * Erstellt eine neue Instanz des StorageAgent
   * @param {Object} options - Konfigurationsoptionen
   * @param {Object} options.storageManager - StorageManager-Instanz oder Konfiguration
   * @param {Object} options.databaseService - Datenbankdienst für Metadaten
   * @param {Object} options.config - Weitere Konfigurationsoptionen
   */
  constructor(options = {}) {
    super('StorageAgent');
    
    const {
      storageManager,
      databaseService,
      config = {}
    } = options;
    
    // Verwende die übergebene StorageManager-Instanz oder erstelle eine neue
    this.storageManager = storageManager instanceof StorageManager
      ? storageManager
      : new StorageManager(storageManager || {});
    
    this.databaseService = databaseService;
    
    // Konfiguration
    this.config = {
      metadataSchema: 'desci-scholar-v1',
      sizeThreshold: 10 * 1024 * 1024, // 10 MB
      preferredAdapters: {
        metadata: 'ipfs',
        smallFiles: 'ipfs',
        largeFiles: 'bittorrent'
      },
      ...config
    };
    
    logger.info('StorageAgent initialisiert');
  }
  
  /**
   * Initialisiert den Agent
   */
  async initialize() {
    logger.info('Initialisiere StorageAgent');
    
    // Prüfe, ob die erforderlichen Dienste verfügbar sind
    if (!this.storageManager) {
      throw new Error('StorageManager ist erforderlich');
    }
    
    if (!this.databaseService) {
      throw new Error('DatabaseService ist erforderlich');
    }
    
    // Initialisiere den StorageManager
    await this.storageManager.initialize();
    
    // Initialisiere die Datenbanktabellen
    await this.initializeDatabaseTables();
    
    logger.info('StorageAgent erfolgreich initialisiert');
  }
  
  /**
   * Initialisiert die erforderlichen Datenbanktabellen
   */
  async initializeDatabaseTables() {
    try {
      // Prüfe, ob die Speichertabelle existiert
      const storageTableExists = await this.databaseService.tableExists('decentralized_storage');
      
      if (!storageTableExists) {
        // Erstelle die Speichertabelle
        await this.databaseService.query(`
          CREATE TABLE decentralized_storage (
            id INT AUTO_INCREMENT PRIMARY KEY,
            content_type VARCHAR(50) NOT NULL,
            content_id VARCHAR(100) NOT NULL,
            metadata_id VARCHAR(100) NOT NULL,
            metadata_adapter VARCHAR(50) NOT NULL,
            content_id_full VARCHAR(100) NOT NULL,
            content_adapter VARCHAR(50) NOT NULL,
            size_bytes BIGINT,
            metadata_url VARCHAR(255),
            content_url VARCHAR(255),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY content_unique (content_type, content_id)
          )
        `);
        
        logger.info('Dezentrale Speichertabelle erstellt');
      }
    } catch (error) {
      logger.error('Fehler bei der Initialisierung der Datenbanktabellen', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Speichert eine wissenschaftliche Publikation
   * 
   * @param {Object} publication - Publikationsdaten
   * @param {Buffer|string} fullText - Volltext der Publikation
   * @param {Object} options - Speicheroptionen
   * @returns {Promise<Object>} Speicherinformationen
   */
  async storePublication(publication, fullText, options = {}) {
    try {
      logger.info(`Speichere Publikation mit DOI ${publication.doi}`);
      
      // Speichere die Publikation mit dem StorageManager
      const result = await this.storageManager.storePublication(publication, fullText, {
        ...options,
        type: options.type || (Buffer.isBuffer(fullText) ? (fullText.length >= this.config.sizeThreshold ? 'largeFile' : 'smallFile') : 'smallFile'),
        adapter: options.adapter || (options.type === 'metadata' ? this.config.preferredAdapters.metadata : (Buffer.isBuffer(fullText) && fullText.length >= this.config.sizeThreshold ? this.config.preferredAdapters.largeFiles : this.config.preferredAdapters.smallFiles))
      });
      
      // Speichere die Informationen in der Datenbank
      await this.databaseService.query(
        `INSERT INTO decentralized_storage 
          (content_type, content_id, metadata_id, metadata_adapter, content_id_full, content_adapter, size_bytes, metadata_url, content_url) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          'publication',
          publication.doi,
          result.metadataId,
          result.metadataAdapter,
          result.fullTextId,
          result.fullTextAdapter,
          result.fullTextSize,
          result.metadataUrl,
          result.fullTextUrl
        ]
      );
      
      logger.info(`Publikation mit DOI ${publication.doi} erfolgreich gespeichert`);
      
      return {
        success: true,
        doi: publication.doi,
        metadataId: result.metadataId,
        metadataAdapter: result.metadataAdapter,
        fullTextId: result.fullTextId,
        fullTextAdapter: result.fullTextAdapter,
        fullTextSize: result.fullTextSize,
        metadataUrl: result.metadataUrl,
        fullTextUrl: result.fullTextUrl
      };
    } catch (error) {
      logger.error('Fehler beim Speichern der Publikation', {
        error: error.message,
        doi: publication.doi
      });
      throw error;
    }
  }
  
  /**
   * Speichert ein Patent
   * 
   * @param {Object} patent - Patentdaten
   * @param {Buffer|string} fullText - Volltext des Patents
   * @param {Object} options - Speicheroptionen
   * @returns {Promise<Object>} Speicherinformationen
   */
  async storePatent(patent, fullText, options = {}) {
    try {
      logger.info(`Speichere Patent ${patent.id}`);
      
      // Speichere das Patent mit dem StorageManager
      const result = await this.storageManager.storePatent(patent, fullText, {
        ...options,
        type: options.type || (Buffer.isBuffer(fullText) ? (fullText.length >= this.config.sizeThreshold ? 'largeFile' : 'smallFile') : 'smallFile'),
        adapter: options.adapter || (options.type === 'metadata' ? this.config.preferredAdapters.metadata : (Buffer.isBuffer(fullText) && fullText.length >= this.config.sizeThreshold ? this.config.preferredAdapters.largeFiles : this.config.preferredAdapters.smallFiles))
      });
      
      // Speichere die Informationen in der Datenbank
      await this.databaseService.query(
        `INSERT INTO decentralized_storage 
          (content_type, content_id, metadata_id, metadata_adapter, content_id_full, content_adapter, size_bytes, metadata_url, content_url) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          'patent',
          patent.id,
          result.metadataId,
          result.metadataAdapter,
          result.fullTextId,
          result.fullTextAdapter,
          result.fullTextSize,
          result.metadataUrl,
          result.fullTextUrl
        ]
      );
      
      logger.info(`Patent ${patent.id} erfolgreich gespeichert`);
      
      return {
        success: true,
        patentId: patent.id,
        metadataId: result.metadataId,
        metadataAdapter: result.metadataAdapter,
        fullTextId: result.fullTextId,
        fullTextAdapter: result.fullTextAdapter,
        fullTextSize: result.fullTextSize,
        metadataUrl: result.metadataUrl,
        fullTextUrl: result.fullTextUrl
      };
    } catch (error) {
      logger.error('Fehler beim Speichern des Patents', {
        error: error.message,
        patentId: patent.id
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Publikation ab
   * 
   * @param {string} doi - DOI der Publikation
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Object>} Abgerufene Publikation
   */
  async retrievePublication(doi, options = {}) {
    try {
      logger.info(`Rufe Publikation mit DOI ${doi} ab`);
      
      // Hole die Speicherinformationen aus der Datenbank
      const storageInfo = await this.databaseService.query(
        'SELECT * FROM decentralized_storage WHERE content_type = ? AND content_id = ?',
        ['publication', doi]
      );
      
      if (!storageInfo || storageInfo.length === 0) {
        throw new Error(`Publikation mit DOI ${doi} nicht gefunden`);
      }
      
      const info = storageInfo[0];
      
      // Rufe die Metadaten ab
      const metadata = await this.storageManager.retrieveJson(info.metadata_id, {
        adapter: info.metadata_adapter
      });
      
      // Rufe den Volltext ab, falls gewünscht
      let fullText = null;
      if (options.includeFullText) {
        fullText = await this.storageManager.retrieveFile(info.content_id_full, {
          adapter: info.content_adapter
        });
      }
      
      logger.info(`Publikation mit DOI ${doi} erfolgreich abgerufen`);
      
      return {
        success: true,
        doi,
        metadata,
        fullText,
        metadataUrl: info.metadata_url,
        fullTextUrl: info.content_url,
        fullTextSize: info.size_bytes
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen der Publikation', {
        error: error.message,
        doi
      });
      throw error;
    }
  }
  
  /**
   * Ruft ein Patent ab
   * 
   * @param {string} patentId - Patent-ID
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Object>} Abgerufenes Patent
   */
  async retrievePatent(patentId, options = {}) {
    try {
      logger.info(`Rufe Patent ${patentId} ab`);
      
      // Hole die Speicherinformationen aus der Datenbank
      const storageInfo = await this.databaseService.query(
        'SELECT * FROM decentralized_storage WHERE content_type = ? AND content_id = ?',
        ['patent', patentId]
      );
      
      if (!storageInfo || storageInfo.length === 0) {
        throw new Error(`Patent ${patentId} nicht gefunden`);
      }
      
      const info = storageInfo[0];
      
      // Rufe die Metadaten ab
      const metadata = await this.storageManager.retrieveJson(info.metadata_id, {
        adapter: info.metadata_adapter
      });
      
      // Rufe den Volltext ab, falls gewünscht
      let fullText = null;
      if (options.includeFullText) {
        fullText = await this.storageManager.retrieveFile(info.content_id_full, {
          adapter: info.content_adapter
        });
      }
      
      logger.info(`Patent ${patentId} erfolgreich abgerufen`);
      
      return {
        success: true,
        patentId,
        metadata,
        fullText,
        metadataUrl: info.metadata_url,
        fullTextUrl: info.content_url,
        fullTextSize: info.size_bytes
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen des Patents', {
        error: error.message,
        patentId
      });
      throw error;
    }
  }
  
  /**
   * Erstellt einen NFT-Metadaten-Standard für wissenschaftliche Publikationen
   * 
   * @param {Object} publication - Publikationsdaten
   * @param {Object} storageInfo - Speicherinformationen
   * @returns {Promise<Object>} NFT-Metadaten
   */
  async createPublicationNftMetadata(publication, storageInfo) {
    try {
      logger.info(`Erstelle NFT-Metadaten für Publikation mit DOI ${publication.doi}`);
      
      // Erstelle die NFT-Metadaten nach dem ERC-721-Metadatenstandard
      const nftMetadata = {
        name: publication.title,
        description: publication.abstract,
        image: publication.coverImage || 'https://desci-scholar.org/assets/publication-nft-placeholder.png',
        external_url: `https://desci-scholar.org/publications/${encodeURIComponent(publication.doi)}`,
        animation_url: storageInfo.fullTextUrl,
        attributes: [
          {
            trait_type: 'DOI',
            value: publication.doi
          },
          {
            trait_type: 'Journal',
            value: publication.journal
          },
          {
            trait_type: 'Publication Date',
            value: publication.publicationDate
          },
          {
            trait_type: 'Authors',
            value: publication.authors
          },
          {
            trait_type: 'License',
            value: publication.license || 'CC-BY'
          }
        ],
        properties: {
          doi: publication.doi,
          metadata_id: storageInfo.metadataId,
          metadata_adapter: storageInfo.metadataAdapter,
          content_id: storageInfo.fullTextId,
          content_adapter: storageInfo.fullTextAdapter,
          schema: 'desci-scholar-publication-v1',
          license: publication.license || 'CC-BY',
          authors: publication.authors.split(',').map(author => author.trim()),
          keywords: publication.keywords ? publication.keywords.split(',').map(keyword => keyword.trim()) : [],
          citations: publication.citationCount || 0
        }
      };
      
      // Speichere die NFT-Metadaten
      const metadataResult = await this.storageManager.storeJson(nftMetadata, {
        type: 'metadata',
        adapter: this.config.preferredAdapters.metadata,
        filename: `${publication.doi.replace(/\//g, '_')}_nft_metadata.json`
      });
      
      logger.info(`NFT-Metadaten für Publikation mit DOI ${publication.doi} erfolgreich erstellt`);
      
      return {
        success: true,
        doi: publication.doi,
        metadataId: metadataResult.adapter === 'IPFSAdapter' ? metadataResult.cid : metadataResult.infoHash,
        metadataAdapter: metadataResult.adapter,
        metadata: nftMetadata,
        metadataUrl: metadataResult.url
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen der NFT-Metadaten', {
        error: error.message,
        doi: publication.doi
      });
      throw error;
    }
  }
  
  /**
   * Erstellt einen NFT-Metadaten-Standard für Patente
   * 
   * @param {Object} patent - Patentdaten
   * @param {Object} storageInfo - Speicherinformationen
   * @returns {Promise<Object>} NFT-Metadaten
   */
  async createPatentNftMetadata(patent, storageInfo) {
    try {
      logger.info(`Erstelle NFT-Metadaten für Patent ${patent.id}`);
      
      // Erstelle die NFT-Metadaten nach dem ERC-721-Metadatenstandard
      const nftMetadata = {
        name: `Patent: ${patent.title}`,
        description: patent.abstract,
        image: patent.image || 'https://desci-scholar.org/assets/patent-nft-placeholder.png',
        external_url: `https://desci-scholar.org/patents/${encodeURIComponent(patent.id)}`,
        animation_url: storageInfo.fullTextUrl,
        attributes: [
          {
            trait_type: 'Patent ID',
            value: patent.id
          },
          {
            trait_type: 'Patent Office',
            value: patent.patentOffice
          },
          {
            trait_type: 'Filing Date',
            value: patent.filingDate
          },
          {
            trait_type: 'Grant Date',
            value: patent.grantDate
          },
          {
            trait_type: 'Inventors',
            value: patent.inventors
          },
          {
            trait_type: 'Assignees',
            value: patent.assignees
          }
        ],
        properties: {
          patentId: patent.id,
          metadata_id: storageInfo.metadataId,
          metadata_adapter: storageInfo.metadataAdapter,
          content_id: storageInfo.fullTextId,
          content_adapter: storageInfo.fullTextAdapter,
          schema: 'desci-scholar-patent-v1',
          inventors: patent.inventors.split(',').map(inventor => inventor.trim()),
          assignees: patent.assignees.split(',').map(assignee => assignee.trim()),
          classificationCodes: patent.classificationCodes ? patent.classificationCodes.split(',').map(code => code.trim()) : []
        }
      };
      
      // Speichere die NFT-Metadaten
      const metadataResult = await this.storageManager.storeJson(nftMetadata, {
        type: 'metadata',
        adapter: this.config.preferredAdapters.metadata,
        filename: `${patent.id.replace(/\//g, '_')}_nft_metadata.json`
      });
      
      logger.info(`NFT-Metadaten für Patent ${patent.id} erfolgreich erstellt`);
      
      return {
        success: true,
        patentId: patent.id,
        metadataId: metadataResult.adapter === 'IPFSAdapter' ? metadataResult.cid : metadataResult.infoHash,
        metadataAdapter: metadataResult.adapter,
        metadata: nftMetadata,
        metadataUrl: metadataResult.url
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen der NFT-Metadaten', {
        error: error.message,
        patentId: patent.id
      });
      throw error;
    }
  }
}/**
 * @fileoverview Speicher-Agent für DeSci-Scholar
 * 
 * Dieser Agent verwaltet die dezentrale Speicherung von wissenschaftlichen Publikationen,
 * Patenten und zugehörigen Metadaten. Er nutzt den StorageManager, um Daten flexibel
 * auf verschiedenen dezentralen Speicherlösungen wie IPFS und BitTorrent zu speichern.
 * Metadaten werden typischerweise auf IPFS gespeichert, während große Dateien über
 * BitTorrent verteilt werden.
 */

import { BaseAgent } from './index.js';
import { logger } from '../../utils/logger.js';
import { StorageManager } from '../../services/storage/StorageManager.js';

/**
 * Agent für die dezentrale Speicherung
 */
export class StorageAgent extends BaseAgent {
  /**
   * Erstellt eine neue Instanz des StorageAgent
   * @param {Object} options - Konfigurationsoptionen
   * @param {Object} options.storageManager - StorageManager-Instanz oder Konfiguration
   * @param {Object} options.databaseService - Datenbankdienst für Metadaten
   * @param {Object} options.config - Weitere Konfigurationsoptionen
   */
  constructor(options = {}) {
    super('StorageAgent');
    
    const {
      storageManager,
      databaseService,
      config = {}
    } = options;
    
    // Verwende die übergebene StorageManager-Instanz oder erstelle eine neue
    this.storageManager = storageManager instanceof StorageManager
      ? storageManager
      : new StorageManager(storageManager || {});
    
    this.databaseService = databaseService;
    
    // Konfiguration
    this.config = {
      metadataSchema: 'desci-scholar-v1',
      sizeThreshold: 10 * 1024 * 1024, // 10 MB
      preferredAdapters: {
        metadata: 'ipfs',
        smallFiles: 'ipfs',
        largeFiles: 'bittorrent'
      },
      ...config
    };
    
    logger.info('StorageAgent initialisiert');
  }
  
  /**
   * Initialisiert den Agent
   */
  async initialize() {
    logger.info('Initialisiere StorageAgent');
    
    // Prüfe, ob die erforderlichen Dienste verfügbar sind
    if (!this.storageManager) {
      throw new Error('StorageManager ist erforderlich');
    }
    
    if (!this.databaseService) {
      throw new Error('DatabaseService ist erforderlich');
    }
    
    // Initialisiere den StorageManager
    await this.storageManager.initialize();
    
    // Initialisiere die Datenbanktabellen
    await this.initializeDatabaseTables();
    
    logger.info('StorageAgent erfolgreich initialisiert');
  }
  
  /**
   * Initialisiert die erforderlichen Datenbanktabellen
   */
  async initializeDatabaseTables() {
    try {
      // Prüfe, ob die Speichertabelle existiert
      const storageTableExists = await this.databaseService.tableExists('decentralized_storage');
      
      if (!storageTableExists) {
        // Erstelle die Speichertabelle
        await this.databaseService.query(`
          CREATE TABLE decentralized_storage (
            id INT AUTO_INCREMENT PRIMARY KEY,
            content_type VARCHAR(50) NOT NULL,
            content_id VARCHAR(100) NOT NULL,
            metadata_id VARCHAR(100) NOT NULL,
            metadata_adapter VARCHAR(50) NOT NULL,
            content_id_full VARCHAR(100) NOT NULL,
            content_adapter VARCHAR(50) NOT NULL,
            size_bytes BIGINT,
            metadata_url VARCHAR(255),
            content_url VARCHAR(255),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY content_unique (content_type, content_id)
          )
        `);
        
        logger.info('Dezentrale Speichertabelle erstellt');
      }
    } catch (error) {
      logger.error('Fehler bei der Initialisierung der Datenbanktabellen', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Speichert eine wissenschaftliche Publikation
   * 
   * @param {Object} publication - Publikationsdaten
   * @param {Buffer|string} fullText - Volltext der Publikation
   * @param {Object} options - Speicheroptionen
   * @returns {Promise<Object>} Speicherinformationen
   */
  async storePublication(publication, fullText, options = {}) {
    try {
      logger.info(`Speichere Publikation mit DOI ${publication.doi}`);
      
      // Speichere die Publikation mit dem StorageManager
      const result = await this.storageManager.storePublication(publication, fullText, {
        ...options,
        type: options.type || (Buffer.isBuffer(fullText) ? (fullText.length >= this.config.sizeThreshold ? 'largeFile' : 'smallFile') : 'smallFile'),
        adapter: options.adapter || (options.type === 'metadata' ? this.config.preferredAdapters.metadata : (Buffer.isBuffer(fullText) && fullText.length >= this.config.sizeThreshold ? this.config.preferredAdapters.largeFiles : this.config.preferredAdapters.smallFiles))
      });
      
      // Speichere die Informationen in der Datenbank
      await this.databaseService.query(
        `INSERT INTO decentralized_storage 
          (content_type, content_id, metadata_id, metadata_adapter, content_id_full, content_adapter, size_bytes, metadata_url, content_url) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          'publication',
          publication.doi,
          result.metadataId,
          result.metadataAdapter,
          result.fullTextId,
          result.fullTextAdapter,
          result.fullTextSize,
          result.metadataUrl,
          result.fullTextUrl
        ]
      );
      
      logger.info(`Publikation mit DOI ${publication.doi} erfolgreich gespeichert`);
      
      return {
        success: true,
        doi: publication.doi,
        metadataId: result.metadataId,
        metadataAdapter: result.metadataAdapter,
        fullTextId: result.fullTextId,
        fullTextAdapter: result.fullTextAdapter,
        fullTextSize: result.fullTextSize,
        metadataUrl: result.metadataUrl,
        fullTextUrl: result.fullTextUrl
      };
    } catch (error) {
      logger.error('Fehler beim Speichern der Publikation', {
        error: error.message,
        doi: publication.doi
      });
      throw error;
    }
  }
  
  /**
   * Speichert ein Patent
   * 
   * @param {Object} patent - Patentdaten
   * @param {Buffer|string} fullText - Volltext des Patents
   * @param {Object} options - Speicheroptionen
   * @returns {Promise<Object>} Speicherinformationen
   */
  async storePatent(patent, fullText, options = {}) {
    try {
      logger.info(`Speichere Patent ${patent.id}`);
      
      // Speichere das Patent mit dem StorageManager
      const result = await this.storageManager.storePatent(patent, fullText, {
        ...options,
        type: options.type || (Buffer.isBuffer(fullText) ? (fullText.length >= this.config.sizeThreshold ? 'largeFile' : 'smallFile') : 'smallFile'),
        adapter: options.adapter || (options.type === 'metadata' ? this.config.preferredAdapters.metadata : (Buffer.isBuffer(fullText) && fullText.length >= this.config.sizeThreshold ? this.config.preferredAdapters.largeFiles : this.config.preferredAdapters.smallFiles))
      });
      
      // Speichere die Informationen in der Datenbank
      await this.databaseService.query(
        `INSERT INTO decentralized_storage 
          (content_type, content_id, metadata_id, metadata_adapter, content_id_full, content_adapter, size_bytes, metadata_url, content_url) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          'patent',
          patent.id,
          result.metadataId,
          result.metadataAdapter,
          result.fullTextId,
          result.fullTextAdapter,
          result.fullTextSize,
          result.metadataUrl,
          result.fullTextUrl
        ]
      );
      
      logger.info(`Patent ${patent.id} erfolgreich gespeichert`);
      
      return {
        success: true,
        patentId: patent.id,
        metadataId: result.metadataId,
        metadataAdapter: result.metadataAdapter,
        fullTextId: result.fullTextId,
        fullTextAdapter: result.fullTextAdapter,
        fullTextSize: result.fullTextSize,
        metadataUrl: result.metadataUrl,
        fullTextUrl: result.fullTextUrl
      };
    } catch (error) {
      logger.error('Fehler beim Speichern des Patents', {
        error: error.message,
        patentId: patent.id
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Publikation ab
   * 
   * @param {string} doi - DOI der Publikation
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Object>} Abgerufene Publikation
   */
  async retrievePublication(doi, options = {}) {
    try {
      logger.info(`Rufe Publikation mit DOI ${doi} ab`);
      
      // Hole die Speicherinformationen aus der Datenbank
      const storageInfo = await this.databaseService.query(
        'SELECT * FROM decentralized_storage WHERE content_type = ? AND content_id = ?',
        ['publication', doi]
      );
      
      if (!storageInfo || storageInfo.length === 0) {
        throw new Error(`Publikation mit DOI ${doi} nicht gefunden`);
      }
      
      const info = storageInfo[0];
      
      // Rufe die Metadaten ab
      const metadata = await this.storageManager.retrieveJson(info.metadata_id, {
        adapter: info.metadata_adapter
      });
      
      // Rufe den Volltext ab, falls gewünscht
      let fullText = null;
      if (options.includeFullText) {
        fullText = await this.storageManager.retrieveFile(info.content_id_full, {
          adapter: info.content_adapter
        });
      }
      
      logger.info(`Publikation mit DOI ${doi} erfolgreich abgerufen`);
      
      return {
        success: true,
        doi,
        metadata,
        fullText,
        metadataUrl: info.metadata_url,
        fullTextUrl: info.content_url,
        fullTextSize: info.size_bytes
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen der Publikation', {
        error: error.message,
        doi
      });
      throw error;
    }
  }
  
  /**
   * Ruft ein Patent ab
   * 
   * @param {string} patentId - Patent-ID
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Object>} Abgerufenes Patent
   */
  async retrievePatent(patentId, options = {}) {
    try {
      logger.info(`Rufe Patent ${patentId} ab`);
      
      // Hole die Speicherinformationen aus der Datenbank
      const storageInfo = await this.databaseService.query(
        'SELECT * FROM decentralized_storage WHERE content_type = ? AND content_id = ?',
        ['patent', patentId]
      );
      
      if (!storageInfo || storageInfo.length === 0) {
        throw new Error(`Patent ${patentId} nicht gefunden`);
      }
      
      const info = storageInfo[0];
      
      // Rufe die Metadaten ab
      const metadata = await this.storageManager.retrieveJson(info.metadata_id, {
        adapter: info.metadata_adapter
      });
      
      // Rufe den Volltext ab, falls gewünscht
      let fullText = null;
      if (options.includeFullText) {
        fullText = await this.storageManager.retrieveFile(info.content_id_full, {
          adapter: info.content_adapter
        });
      }
      
      logger.info(`Patent ${patentId} erfolgreich abgerufen`);
      
      return {
        success: true,
        patentId,
        metadata,
        fullText,
        metadataUrl: info.metadata_url,
        fullTextUrl: info.content_url,
        fullTextSize: info.size_bytes
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen des Patents', {
        error: error.message,
        patentId
      });
      throw error;
    }
  }
  
  /**
   * Erstellt einen NFT-Metadaten-Standard für wissenschaftliche Publikationen
   * 
   * @param {Object} publication - Publikationsdaten
   * @param {Object} storageInfo - Speicherinformationen
   * @returns {Promise<Object>} NFT-Metadaten
   */
  async createPublicationNftMetadata(publication, storageInfo) {
    try {
      logger.info(`Erstelle NFT-Metadaten für Publikation mit DOI ${publication.doi}`);
      
      // Erstelle die NFT-Metadaten nach dem ERC-721-Metadatenstandard
      const nftMetadata = {
        name: publication.title,
        description: publication.abstract,
        image: publication.coverImage || 'https://desci-scholar.org/assets/publication-nft-placeholder.png',
        external_url: `https://desci-scholar.org/publications/${encodeURIComponent(publication.doi)}`,
        animation_url: storageInfo.fullTextUrl,
        attributes: [
          {
            trait_type: 'DOI',
            value: publication.doi
          },
          {
            trait_type: 'Journal',
            value: publication.journal
          },
          {
            trait_type: 'Publication Date',
            value: publication.publicationDate
          },
          {
            trait_type: 'Authors',
            value: publication.authors
          },
          {
            trait_type: 'License',
            value: publication.license || 'CC-BY'
          }
        ],
        properties: {
          doi: publication.doi,
          metadata_id: storageInfo.metadataId,
          metadata_adapter: storageInfo.metadataAdapter,
          content_id: storageInfo.fullTextId,
          content_adapter: storageInfo.fullTextAdapter,
          schema: 'desci-scholar-publication-v1',
          license: publication.license || 'CC-BY',
          authors: publication.authors.split(',').map(author => author.trim()),
          keywords: publication.keywords ? publication.keywords.split(',').map(keyword => keyword.trim()) : [],
          citations: publication.citationCount || 0
        }
      };
      
      // Speichere die NFT-Metadaten
      const metadataResult = await this.storageManager.storeJson(nftMetadata, {
        type: 'metadata',
        adapter: this.config.preferredAdapters.metadata,
        filename: `${publication.doi.replace(/\//g, '_')}_nft_metadata.json`
      });
      
      logger.info(`NFT-Metadaten für Publikation mit DOI ${publication.doi} erfolgreich erstellt`);
      
      return {
        success: true,
        doi: publication.doi,
        metadataId: metadataResult.adapter === 'IPFSAdapter' ? metadataResult.cid : metadataResult.infoHash,
        metadataAdapter: metadataResult.adapter,
        metadata: nftMetadata,
        metadataUrl: metadataResult.url
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen der NFT-Metadaten', {
        error: error.message,
        doi: publication.doi
      });
      throw error;
    }
  }
  
  /**
   * Erstellt einen NFT-Metadaten-Standard für Patente
   * 
   * @param {Object} patent - Patentdaten
   * @param {Object} storageInfo - Speicherinformationen
   * @returns {Promise<Object>} NFT-Metadaten
   */
  async createPatentNftMetadata(patent, storageInfo) {
    try {
      logger.info(`Erstelle NFT-Metadaten für Patent ${patent.id}`);
      
      // Erstelle die NFT-Metadaten nach dem ERC-721-Metadatenstandard
      const nftMetadata = {
        name: `Patent: ${patent.title}`,
        description: patent.abstract,
        image: patent.image || 'https://desci-scholar.org/assets/patent-nft-placeholder.png',
        external_url: `https://desci-scholar.org/patents/${encodeURIComponent(patent.id)}`,
        animation_url: storageInfo.fullTextUrl,
        attributes: [
          {
            trait_type: 'Patent ID',
            value: patent.id
          },
          {
            trait_type: 'Patent Office',
            value: patent.patentOffice
          },
          {
            trait_type: 'Filing Date',
            value: patent.filingDate
          },
          {
            trait_type: 'Grant Date',
            value: patent.grantDate
          },
          {
            trait_type: 'Inventors',
            value: patent.inventors
          },
          {
            trait_type: 'Assignees',
            value: patent.assignees
          }
        ],
        properties: {
          patentId: patent.id,
          metadata_id: storageInfo.metadataId,
          metadata_adapter: storageInfo.metadataAdapter,
          content_id: storageInfo.fullTextId,
          content_adapter: storageInfo.fullTextAdapter,
          schema: 'desci-scholar-patent-v1',
          inventors: patent.inventors.split(',').map(inventor => inventor.trim()),
          assignees: patent.assignees.split(',').map(assignee => assignee.trim()),
          classificationCodes: patent.classificationCodes ? patent.classificationCodes.split(',').map(code => code.trim()) : []
        }
      };
      
      // Speichere die NFT-Metadaten
      const metadataResult = await this.storageManager.storeJson(nftMetadata, {
        type: 'metadata',
        adapter: this.config.preferredAdapters.metadata,
        filename: `${patent.id.replace(/\//g, '_')}_nft_metadata.json`
      });
      
      logger.info(`NFT-Metadaten für Patent ${patent.id} erfolgreich erstellt`);
      
      return {
        success: true,
        patentId: patent.id,
        metadataId: metadataResult.adapter === 'IPFSAdapter' ? metadataResult.cid : metadataResult.infoHash,
        metadataAdapter: metadataResult.adapter,
        metadata: nftMetadata,
        metadataUrl: metadataResult.url
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen der NFT-Metadaten', {
        error: error.message,
        patentId: patent.id
      });
      throw error;
    }
  }
}/**
 * @fileoverview Speicher-Agent für DeSci-Scholar
 * 
 * Dieser Agent verwaltet die dezentrale Speicherung von wissenschaftlichen Publikationen,
 * Patenten und zugehörigen Metadaten. Er nutzt den StorageManager, um Daten flexibel
 * auf verschiedenen dezentralen Speicherlösungen wie IPFS und BitTorrent zu speichern.
 * Metadaten werden typischerweise auf IPFS gespeichert, während große Dateien über
 * BitTorrent verteilt werden.
 */

import { BaseAgent } from './index.js';
import { logger } from '../../utils/logger.js';
import { StorageManager } from '../../services/storage/StorageManager.js';

/**
 * Agent für die dezentrale Speicherung
 */
export class StorageAgent extends BaseAgent {
  /**
   * Erstellt eine neue Instanz des StorageAgent
   * @param {Object} options - Konfigurationsoptionen
   * @param {Object} options.storageManager - StorageManager-Instanz oder Konfiguration
   * @param {Object} options.databaseService - Datenbankdienst für Metadaten
   * @param {Object} options.config - Weitere Konfigurationsoptionen
   */
  constructor(options = {}) {
    super('StorageAgent');
    
    const {
      storageManager,
      databaseService,
      config = {}
    } = options;
    
    // Verwende die übergebene StorageManager-Instanz oder erstelle eine neue
    this.storageManager = storageManager instanceof StorageManager
      ? storageManager
      : new StorageManager(storageManager || {});
    
    this.databaseService = databaseService;
    
    // Konfiguration
    this.config = {
      metadataSchema: 'desci-scholar-v1',
      sizeThreshold: 10 * 1024 * 1024, // 10 MB
      preferredAdapters: {
        metadata: 'ipfs',
        smallFiles: 'ipfs',
        largeFiles: 'bittorrent'
      },
      ...config
    };
    
    logger.info('StorageAgent initialisiert');
  }
  
  /**
   * Initialisiert den Agent
   */
  async initialize() {
    logger.info('Initialisiere StorageAgent');
    
    // Prüfe, ob die erforderlichen Dienste verfügbar sind
    if (!this.storageManager) {
      throw new Error('StorageManager ist erforderlich');
    }
    
    if (!this.databaseService) {
      throw new Error('DatabaseService ist erforderlich');
    }
    
    // Initialisiere den StorageManager
    await this.storageManager.initialize();
    
    // Initialisiere die Datenbanktabellen
    await this.initializeDatabaseTables();
    
    logger.info('StorageAgent erfolgreich initialisiert');
  }
  
  /**
   * Initialisiert die erforderlichen Datenbanktabellen
   */
  async initializeDatabaseTables() {
    try {
      // Prüfe, ob die Speichertabelle existiert
      const storageTableExists = await this.databaseService.tableExists('decentralized_storage');
      
      if (!storageTableExists) {
        // Erstelle die Speichertabelle
        await this.databaseService.query(`
          CREATE TABLE decentralized_storage (
            id INT AUTO_INCREMENT PRIMARY KEY,
            content_type VARCHAR(50) NOT NULL,
            content_id VARCHAR(100) NOT NULL,
            metadata_id VARCHAR(100) NOT NULL,
            metadata_adapter VARCHAR(50) NOT NULL,
            content_id_full VARCHAR(100) NOT NULL,
            content_adapter VARCHAR(50) NOT NULL,
            size_bytes BIGINT,
            metadata_url VARCHAR(255),
            content_url VARCHAR(255),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY content_unique (content_type, content_id)
          )
        `);
        
        logger.info('Dezentrale Speichertabelle erstellt');
      }
    } catch (error) {
      logger.error('Fehler bei der Initialisierung der Datenbanktabellen', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Speichert eine wissenschaftliche Publikation
   * 
   * @param {Object} publication - Publikationsdaten
   * @param {Buffer|string} fullText - Volltext der Publikation
   * @param {Object} options - Speicheroptionen
   * @returns {Promise<Object>} Speicherinformationen
   */
  async storePublication(publication, fullText, options = {}) {
    try {
      logger.info(`Speichere Publikation mit DOI ${publication.doi}`);
      
      // Speichere die Publikation mit dem StorageManager
      const result = await this.storageManager.storePublication(publication, fullText, {
        ...options,
        type: options.type || (Buffer.isBuffer(fullText) ? (fullText.length >= this.config.sizeThreshold ? 'largeFile' : 'smallFile') : 'smallFile'),
        adapter: options.adapter || (options.type === 'metadata' ? this.config.preferredAdapters.metadata : (Buffer.isBuffer(fullText) && fullText.length >= this.config.sizeThreshold ? this.config.preferredAdapters.largeFiles : this.config.preferredAdapters.smallFiles))
      });
      
      // Speichere die Informationen in der Datenbank
      await this.databaseService.query(
        `INSERT INTO decentralized_storage 
          (content_type, content_id, metadata_id, metadata_adapter, content_id_full, content_adapter, size_bytes, metadata_url, content_url) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          'publication',
          publication.doi,
          result.metadataId,
          result.metadataAdapter,
          result.fullTextId,
          result.fullTextAdapter,
          result.fullTextSize,
          result.metadataUrl,
          result.fullTextUrl
        ]
      );
      
      logger.info(`Publikation mit DOI ${publication.doi} erfolgreich gespeichert`);
      
      return {
        success: true,
        doi: publication.doi,
        metadataId: result.metadataId,
        metadataAdapter: result.metadataAdapter,
        fullTextId: result.fullTextId,
        fullTextAdapter: result.fullTextAdapter,
        fullTextSize: result.fullTextSize,
        metadataUrl: result.metadataUrl,
        fullTextUrl: result.fullTextUrl
      };
    } catch (error) {
      logger.error('Fehler beim Speichern der Publikation', {
        error: error.message,
        doi: publication.doi
      });
      throw error;
    }
  }
  
  /**
   * Speichert ein Patent
   * 
   * @param {Object} patent - Patentdaten
   * @param {Buffer|string} fullText - Volltext des Patents
   * @param {Object} options - Speicheroptionen
   * @returns {Promise<Object>} Speicherinformationen
   */
  async storePatent(patent, fullText, options = {}) {
    try {
      logger.info(`Speichere Patent ${patent.id}`);
      
      // Speichere das Patent mit dem StorageManager
      const result = await this.storageManager.storePatent(patent, fullText, {
        ...options,
        type: options.type || (Buffer.isBuffer(fullText) ? (fullText.length >= this.config.sizeThreshold ? 'largeFile' : 'smallFile') : 'smallFile'),
        adapter: options.adapter || (options.type === 'metadata' ? this.config.preferredAdapters.metadata : (Buffer.isBuffer(fullText) && fullText.length >= this.config.sizeThreshold ? this.config.preferredAdapters.largeFiles : this.config.preferredAdapters.smallFiles))
      });
      
      // Speichere die Informationen in der Datenbank
      await this.databaseService.query(
        `INSERT INTO decentralized_storage 
          (content_type, content_id, metadata_id, metadata_adapter, content_id_full, content_adapter, size_bytes, metadata_url, content_url) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          'patent',
          patent.id,
          result.metadataId,
          result.metadataAdapter,
          result.fullTextId,
          result.fullTextAdapter,
          result.fullTextSize,
          result.metadataUrl,
          result.fullTextUrl
        ]
      );
      
      logger.info(`Patent ${patent.id} erfolgreich gespeichert`);
      
      return {
        success: true,
        patentId: patent.id,
        metadataId: result.metadataId,
        metadataAdapter: result.metadataAdapter,
        fullTextId: result.fullTextId,
        fullTextAdapter: result.fullTextAdapter,
        fullTextSize: result.fullTextSize,
        metadataUrl: result.metadataUrl,
        fullTextUrl: result.fullTextUrl
      };
    } catch (error) {
      logger.error('Fehler beim Speichern des Patents', {
        error: error.message,
        patentId: patent.id
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Publikation ab
   * 
   * @param {string} doi - DOI der Publikation
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Object>} Abgerufene Publikation
   */
  async retrievePublication(doi, options = {}) {
    try {
      logger.info(`Rufe Publikation mit DOI ${doi} ab`);
      
      // Hole die Speicherinformationen aus der Datenbank
      const storageInfo = await this.databaseService.query(
        'SELECT * FROM decentralized_storage WHERE content_type = ? AND content_id = ?',
        ['publication', doi]
      );
      
      if (!storageInfo || storageInfo.length === 0) {
        throw new Error(`Publikation mit DOI ${doi} nicht gefunden`);
      }
      
      const info = storageInfo[0];
      
      // Rufe die Metadaten ab
      const metadata = await this.storageManager.retrieveJson(info.metadata_id, {
        adapter: info.metadata_adapter
      });
      
      // Rufe den Volltext ab, falls gewünscht
      let fullText = null;
      if (options.includeFullText) {
        fullText = await this.storageManager.retrieveFile(info.content_id_full, {
          adapter: info.content_adapter
        });
      }
      
      logger.info(`Publikation mit DOI ${doi} erfolgreich abgerufen`);
      
      return {
        success: true,
        doi,
        metadata,
        fullText,
        metadataUrl: info.metadata_url,
        fullTextUrl: info.content_url,
        fullTextSize: info.size_bytes
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen der Publikation', {
        error: error.message,
        doi
      });
      throw error;
    }
  }
  
  /**
   * Ruft ein Patent ab
   * 
   * @param {string} patentId - Patent-ID
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Object>} Abgerufenes Patent
   */
  async retrievePatent(patentId, options = {}) {
    try {
      logger.info(`Rufe Patent ${patentId} ab`);
      
      // Hole die Speicherinformationen aus der Datenbank
      const storageInfo = await this.databaseService.query(
        'SELECT * FROM decentralized_storage WHERE content_type = ? AND content_id = ?',
        ['patent', patentId]
      );
      
      if (!storageInfo || storageInfo.length === 0) {
        throw new Error(`Patent ${patentId} nicht gefunden`);
      }
      
      const info = storageInfo[0];
      
      // Rufe die Metadaten ab
      const metadata = await this.storageManager.retrieveJson(info.metadata_id, {
        adapter: info.metadata_adapter
      });
      
      // Rufe den Volltext ab, falls gewünscht
      let fullText = null;
      if (options.includeFullText) {
        fullText = await this.storageManager.retrieveFile(info.content_id_full, {
          adapter: info.content_adapter
        });
      }
      
      logger.info(`Patent ${patentId} erfolgreich abgerufen`);
      
      return {
        success: true,
        patentId,
        metadata,
        fullText,
        metadataUrl: info.metadata_url,
        fullTextUrl: info.content_url,
        fullTextSize: info.size_bytes
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen des Patents', {
        error: error.message,
        patentId
      });
      throw error;
    }
  }
  
  /**
   * Erstellt einen NFT-Metadaten-Standard für wissenschaftliche Publikationen
   * 
   * @param {Object} publication - Publikationsdaten
   * @param {Object} storageInfo - Speicherinformationen
   * @returns {Promise<Object>} NFT-Metadaten
   */
  async createPublicationNftMetadata(publication, storageInfo) {
    try {
      logger.info(`Erstelle NFT-Metadaten für Publikation mit DOI ${publication.doi}`);
      
      // Erstelle die NFT-Metadaten nach dem ERC-721-Metadatenstandard
      const nftMetadata = {
        name: publication.title,
        description: publication.abstract,
        image: publication.coverImage || 'https://desci-scholar.org/assets/publication-nft-placeholder.png',
        external_url: `https://desci-scholar.org/publications/${encodeURIComponent(publication.doi)}`,
        animation_url: storageInfo.fullTextUrl,
        attributes: [
          {
            trait_type: 'DOI',
            value: publication.doi
          },
          {
            trait_type: 'Journal',
            value: publication.journal
          },
          {
            trait_type: 'Publication Date',
            value: publication.publicationDate
          },
          {
            trait_type: 'Authors',
            value: publication.authors
          },
          {
            trait_type: 'License',
            value: publication.license || 'CC-BY'
          }
        ],
        properties: {
          doi: publication.doi,
          metadata_id: storageInfo.metadataId,
          metadata_adapter: storageInfo.metadataAdapter,
          content_id: storageInfo.fullTextId,
          content_adapter: storageInfo.fullTextAdapter,
          schema: 'desci-scholar-publication-v1',
          license: publication.license || 'CC-BY',
          authors: publication.authors.split(',').map(author => author.trim()),
          keywords: publication.keywords ? publication.keywords.split(',').map(keyword => keyword.trim()) : [],
          citations: publication.citationCount || 0
        }
      };
      
      // Speichere die NFT-Metadaten
      const metadataResult = await this.storageManager.storeJson(nftMetadata, {
        type: 'metadata',
        adapter: this.config.preferredAdapters.metadata,
        filename: `${publication.doi.replace(/\//g, '_')}_nft_metadata.json`
      });
      
      logger.info(`NFT-Metadaten für Publikation mit DOI ${publication.doi} erfolgreich erstellt`);
      
      return {
        success: true,
        doi: publication.doi,
        metadataId: metadataResult.adapter === 'IPFSAdapter' ? metadataResult.cid : metadataResult.infoHash,
        metadataAdapter: metadataResult.adapter,
        metadata: nftMetadata,
        metadataUrl: metadataResult.url
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen der NFT-Metadaten', {
        error: error.message,
        doi: publication.doi
      });
      throw error;
    }
  }
  
  /**
   * Erstellt einen NFT-Metadaten-Standard für Patente
   * 
   * @param {Object} patent - Patentdaten
   * @param {Object} storageInfo - Speicherinformationen
   * @returns {Promise<Object>} NFT-Metadaten
   */
  async createPatentNftMetadata(patent, storageInfo) {
    try {
      logger.info(`Erstelle NFT-Metadaten für Patent ${patent.id}`);
      
      // Erstelle die NFT-Metadaten nach dem ERC-721-Metadatenstandard
      const nftMetadata = {
        name: `Patent: ${patent.title}`,
        description: patent.abstract,
        image: patent.image || 'https://desci-scholar.org/assets/patent-nft-placeholder.png',
        external_url: `https://desci-scholar.org/patents/${encodeURIComponent(patent.id)}`,
        animation_url: storageInfo.fullTextUrl,
        attributes: [
          {
            trait_type: 'Patent ID',
            value: patent.id
          },
          {
            trait_type: 'Patent Office',
            value: patent.patentOffice
          },
          {
            trait_type: 'Filing Date',
            value: patent.filingDate
          },
          {
            trait_type: 'Grant Date',
            value: patent.grantDate
          },
          {
            trait_type: 'Inventors',
            value: patent.inventors
          },
          {
            trait_type: 'Assignees',
            value: patent.assignees
          }
        ],
        properties: {
          patentId: patent.id,
          metadata_id: storageInfo.metadataId,
          metadata_adapter: storageInfo.metadataAdapter,
          content_id: storageInfo.fullTextId,
          content_adapter: storageInfo.fullTextAdapter,
          schema: 'desci-scholar-patent-v1',
          inventors: patent.inventors.split(',').map(inventor => inventor.trim()),
          assignees: patent.assignees.split(',').map(assignee => assignee.trim()),
          classificationCodes: patent.classificationCodes ? patent.classificationCodes.split(',').map(code => code.trim()) : []
        }
      };
      
      // Speichere die NFT-Metadaten
      const metadataResult = await this.storageManager.storeJson(nftMetadata, {
        type: 'metadata',
        adapter: this.config.preferredAdapters.metadata,
        filename: `${patent.id.replace(/\//g, '_')}_nft_metadata.json`
      });
      
      logger.info(`NFT-Metadaten für Patent ${patent.id} erfolgreich erstellt`);
      
      return {
        success: true,
        patentId: patent.id,
        metadataId: metadataResult.adapter === 'IPFSAdapter' ? metadataResult.cid : metadataResult.infoHash,
        metadataAdapter: metadataResult.adapter,
        metadata: nftMetadata,
        metadataUrl: metadataResult.url
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen der NFT-Metadaten', {
        error: error.message,
        patentId: patent.id
      });
      throw error;
    }
  }
}/**
 * @fileoverview Speicher-Agent für DeSci-Scholar
 * 
 * Dieser Agent verwaltet die dezentrale Speicherung von wissenschaftlichen Publikationen,
 * Patenten und zugehörigen Metadaten. Er nutzt den StorageManager, um Daten flexibel
 * auf verschiedenen dezentralen Speicherlösungen wie IPFS und BitTorrent zu speichern.
 * Metadaten werden typischerweise auf IPFS gespeichert, während große Dateien über
 * BitTorrent verteilt werden.
 */

import { BaseAgent } from './index.js';
import { logger } from '../../utils/logger.js';
import { StorageManager } from '../../services/storage/StorageManager.js';

/**
 * Agent für die dezentrale Speicherung
 */
export class StorageAgent extends BaseAgent {
  /**
   * Erstellt eine neue Instanz des StorageAgent
   * @param {Object} options - Konfigurationsoptionen
   * @param {Object} options.storageManager - StorageManager-Instanz oder Konfiguration
   * @param {Object} options.databaseService - Datenbankdienst für Metadaten
   * @param {Object} options.config - Weitere Konfigurationsoptionen
   */
  constructor(options = {}) {
    super('StorageAgent');
    
    const {
      storageManager,
      databaseService,
      config = {}
    } = options;
    
    // Verwende die übergebene StorageManager-Instanz oder erstelle eine neue
    this.storageManager = storageManager instanceof StorageManager
      ? storageManager
      : new StorageManager(storageManager || {});
    
    this.databaseService = databaseService;
    
    // Konfiguration
    this.config = {
      metadataSchema: 'desci-scholar-v1',
      sizeThreshold: 10 * 1024 * 1024, // 10 MB
      preferredAdapters: {
        metadata: 'ipfs',
        smallFiles: 'ipfs',
        largeFiles: 'bittorrent'
      },
      ...config
    };
    
    logger.info('StorageAgent initialisiert');
  }
  
  /**
   * Initialisiert den Agent
   */
  async initialize() {
    logger.info('Initialisiere StorageAgent');
    
    // Prüfe, ob die erforderlichen Dienste verfügbar sind
    if (!this.storageManager) {
      throw new Error('StorageManager ist erforderlich');
    }
    
    if (!this.databaseService) {
      throw new Error('DatabaseService ist erforderlich');
    }
    
    // Initialisiere den StorageManager
    await this.storageManager.initialize();
    
    // Initialisiere die Datenbanktabellen
    await this.initializeDatabaseTables();
    
    logger.info('StorageAgent erfolgreich initialisiert');
  }
  
  /**
   * Initialisiert die erforderlichen Datenbanktabellen
   */
  async initializeDatabaseTables() {
    try {
      // Prüfe, ob die Speichertabelle existiert
      const storageTableExists = await this.databaseService.tableExists('decentralized_storage');
      
      if (!storageTableExists) {
        // Erstelle die Speichertabelle
        await this.databaseService.query(`
          CREATE TABLE decentralized_storage (
            id INT AUTO_INCREMENT PRIMARY KEY,
            content_type VARCHAR(50) NOT NULL,
            content_id VARCHAR(100) NOT NULL,
            metadata_id VARCHAR(100) NOT NULL,
            metadata_adapter VARCHAR(50) NOT NULL,
            content_id_full VARCHAR(100) NOT NULL,
            content_adapter VARCHAR(50) NOT NULL,
            size_bytes BIGINT,
            metadata_url VARCHAR(255),
            content_url VARCHAR(255),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY content_unique (content_type, content_id)
          )
        `);
        
        logger.info('Dezentrale Speichertabelle erstellt');
      }
    } catch (error) {
      logger.error('Fehler bei der Initialisierung der Datenbanktabellen', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Speichert eine wissenschaftliche Publikation
   * 
   * @param {Object} publication - Publikationsdaten
   * @param {Buffer|string} fullText - Volltext der Publikation
   * @param {Object} options - Speicheroptionen
   * @returns {Promise<Object>} Speicherinformationen
   */
  async storePublication(publication, fullText, options = {}) {
    try {
      logger.info(`Speichere Publikation mit DOI ${publication.doi}`);
      
      // Speichere die Publikation mit dem StorageManager
      const result = await this.storageManager.storePublication(publication, fullText, {
        ...options,
        type: options.type || (Buffer.isBuffer(fullText) ? (fullText.length >= this.config.sizeThreshold ? 'largeFile' : 'smallFile') : 'smallFile'),
        adapter: options.adapter || (options.type === 'metadata' ? this.config.preferredAdapters.metadata : (Buffer.isBuffer(fullText) && fullText.length >= this.config.sizeThreshold ? this.config.preferredAdapters.largeFiles : this.config.preferredAdapters.smallFiles))
      });
      
      // Speichere die Informationen in der Datenbank
      await this.databaseService.query(
        `INSERT INTO decentralized_storage 
          (content_type, content_id, metadata_id, metadata_adapter, content_id_full, content_adapter, size_bytes, metadata_url, content_url) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          'publication',
          publication.doi,
          result.metadataId,
          result.metadataAdapter,
          result.fullTextId,
          result.fullTextAdapter,
          result.fullTextSize,
          result.metadataUrl,
          result.fullTextUrl
        ]
      );
      
      logger.info(`Publikation mit DOI ${publication.doi} erfolgreich gespeichert`);
      
      return {
        success: true,
        doi: publication.doi,
        metadataId: result.metadataId,
        metadataAdapter: result.metadataAdapter,
        fullTextId: result.fullTextId,
        fullTextAdapter: result.fullTextAdapter,
        fullTextSize: result.fullTextSize,
        metadataUrl: result.metadataUrl,
        fullTextUrl: result.fullTextUrl
      };
    } catch (error) {
      logger.error('Fehler beim Speichern der Publikation', {
        error: error.message,
        doi: publication.doi
      });
      throw error;
    }
  }
  
  /**
   * Speichert ein Patent
   * 
   * @param {Object} patent - Patentdaten
   * @param {Buffer|string} fullText - Volltext des Patents
   * @param {Object} options - Speicheroptionen
   * @returns {Promise<Object>} Speicherinformationen
   */
  async storePatent(patent, fullText, options = {}) {
    try {
      logger.info(`Speichere Patent ${patent.id}`);
      
      // Speichere das Patent mit dem StorageManager
      const result = await this.storageManager.storePatent(patent, fullText, {
        ...options,
        type: options.type || (Buffer.isBuffer(fullText) ? (fullText.length >= this.config.sizeThreshold ? 'largeFile' : 'smallFile') : 'smallFile'),
        adapter: options.adapter || (options.type === 'metadata' ? this.config.preferredAdapters.metadata : (Buffer.isBuffer(fullText) && fullText.length >= this.config.sizeThreshold ? this.config.preferredAdapters.largeFiles : this.config.preferredAdapters.smallFiles))
      });
      
      // Speichere die Informationen in der Datenbank
      await this.databaseService.query(
        `INSERT INTO decentralized_storage 
          (content_type, content_id, metadata_id, metadata_adapter, content_id_full, content_adapter, size_bytes, metadata_url, content_url) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          'patent',
          patent.id,
          result.metadataId,
          result.metadataAdapter,
          result.fullTextId,
          result.fullTextAdapter,
          result.fullTextSize,
          result.metadataUrl,
          result.fullTextUrl
        ]
      );
      
      logger.info(`Patent ${patent.id} erfolgreich gespeichert`);
      
      return {
        success: true,
        patentId: patent.id,
        metadataId: result.metadataId,
        metadataAdapter: result.metadataAdapter,
        fullTextId: result.fullTextId,
        fullTextAdapter: result.fullTextAdapter,
        fullTextSize: result.fullTextSize,
        metadataUrl: result.metadataUrl,
        fullTextUrl: result.fullTextUrl
      };
    } catch (error) {
      logger.error('Fehler beim Speichern des Patents', {
        error: error.message,
        patentId: patent.id
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Publikation ab
   * 
   * @param {string} doi - DOI der Publikation
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Object>} Abgerufene Publikation
   */
  async retrievePublication(doi, options = {}) {
    try {
      logger.info(`Rufe Publikation mit DOI ${doi} ab`);
      
      // Hole die Speicherinformationen aus der Datenbank
      const storageInfo = await this.databaseService.query(
        'SELECT * FROM decentralized_storage WHERE content_type = ? AND content_id = ?',
        ['publication', doi]
      );
      
      if (!storageInfo || storageInfo.length === 0) {
        throw new Error(`Publikation mit DOI ${doi} nicht gefunden`);
      }
      
      const info = storageInfo[0];
      
      // Rufe die Metadaten ab
      const metadata = await this.storageManager.retrieveJson(info.metadata_id, {
        adapter: info.metadata_adapter
      });
      
      // Rufe den Volltext ab, falls gewünscht
      let fullText = null;
      if (options.includeFullText) {
        fullText = await this.storageManager.retrieveFile(info.content_id_full, {
          adapter: info.content_adapter
        });
      }
      
      logger.info(`Publikation mit DOI ${doi} erfolgreich abgerufen`);
      
      return {
        success: true,
        doi,
        metadata,
        fullText,
        metadataUrl: info.metadata_url,
        fullTextUrl: info.content_url,
        fullTextSize: info.size_bytes
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen der Publikation', {
        error: error.message,
        doi
      });
      throw error;
    }
  }
  
  /**
   * Ruft ein Patent ab
   * 
   * @param {string} patentId - Patent-ID
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Object>} Abgerufenes Patent
   */
  async retrievePatent(patentId, options = {}) {
    try {
      logger.info(`Rufe Patent ${patentId} ab`);
      
      // Hole die Speicherinformationen aus der Datenbank
      const storageInfo = await this.databaseService.query(
        'SELECT * FROM decentralized_storage WHERE content_type = ? AND content_id = ?',
        ['patent', patentId]
      );
      
      if (!storageInfo || storageInfo.length === 0) {
        throw new Error(`Patent ${patentId} nicht gefunden`);
      }
      
      const info = storageInfo[0];
      
      // Rufe die Metadaten ab
      const metadata = await this.storageManager.retrieveJson(info.metadata_id, {
        adapter: info.metadata_adapter
      });
      
      // Rufe den Volltext ab, falls gewünscht
      let fullText = null;
      if (options.includeFullText) {
        fullText = await this.storageManager.retrieveFile(info.content_id_full, {
          adapter: info.content_adapter
        });
      }
      
      logger.info(`Patent ${patentId} erfolgreich abgerufen`);
      
      return {
        success: true,
        patentId,
        metadata,
        fullText,
        metadataUrl: info.metadata_url,
        fullTextUrl: info.content_url,
        fullTextSize: info.size_bytes
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen des Patents', {
        error: error.message,
        patentId
      });
      throw error;
    }
  }
  
  /**
   * Erstellt einen NFT-Metadaten-Standard für wissenschaftliche Publikationen
   * 
   * @param {Object} publication - Publikationsdaten
   * @param {Object} storageInfo - Speicherinformationen
   * @returns {Promise<Object>} NFT-Metadaten
   */
  async createPublicationNftMetadata(publication, storageInfo) {
    try {
      logger.info(`Erstelle NFT-Metadaten für Publikation mit DOI ${publication.doi}`);
      
      // Erstelle die NFT-Metadaten nach dem ERC-721-Metadatenstandard
      const nftMetadata = {
        name: publication.title,
        description: publication.abstract,
        image: publication.coverImage || 'https://desci-scholar.org/assets/publication-nft-placeholder.png',
        external_url: `https://desci-scholar.org/publications/${encodeURIComponent(publication.doi)}`,
        animation_url: storageInfo.fullTextUrl,
        attributes: [
          {
            trait_type: 'DOI',
            value: publication.doi
          },
          {
            trait_type: 'Journal',
            value: publication.journal
          },
          {
            trait_type: 'Publication Date',
            value: publication.publicationDate
          },
          {
            trait_type: 'Authors',
            value: publication.authors
          },
          {
            trait_type: 'License',
            value: publication.license || 'CC-BY'
          }
        ],
        properties: {
          doi: publication.doi,
          metadata_id: storageInfo.metadataId,
          metadata_adapter: storageInfo.metadataAdapter,
          content_id: storageInfo.fullTextId,
          content_adapter: storageInfo.fullTextAdapter,
          schema: 'desci-scholar-publication-v1',
          license: publication.license || 'CC-BY',
          authors: publication.authors.split(',').map(author => author.trim()),
          keywords: publication.keywords ? publication.keywords.split(',').map(keyword => keyword.trim()) : [],
          citations: publication.citationCount || 0
        }
      };
      
      // Speichere die NFT-Metadaten
      const metadataResult = await this.storageManager.storeJson(nftMetadata, {
        type: 'metadata',
        adapter: this.config.preferredAdapters.metadata,
        filename: `${publication.doi.replace(/\//g, '_')}_nft_metadata.json`
      });
      
      logger.info(`NFT-Metadaten für Publikation mit DOI ${publication.doi} erfolgreich erstellt`);
      
      return {
        success: true,
        doi: publication.doi,
        metadataId: metadataResult.adapter === 'IPFSAdapter' ? metadataResult.cid : metadataResult.infoHash,
        metadataAdapter: metadataResult.adapter,
        metadata: nftMetadata,
        metadataUrl: metadataResult.url
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen der NFT-Metadaten', {
        error: error.message,
        doi: publication.doi
      });
      throw error;
    }
  }
  
  /**
   * Erstellt einen NFT-Metadaten-Standard für Patente
   * 
   * @param {Object} patent - Patentdaten
   * @param {Object} storageInfo - Speicherinformationen
   * @returns {Promise<Object>} NFT-Metadaten
   */
  async createPatentNftMetadata(patent, storageInfo) {
    try {
      logger.info(`Erstelle NFT-Metadaten für Patent ${patent.id}`);
      
      // Erstelle die NFT-Metadaten nach dem ERC-721-Metadatenstandard
      const nftMetadata = {
        name: `Patent: ${patent.title}`,
        description: patent.abstract,
        image: patent.image || 'https://desci-scholar.org/assets/patent-nft-placeholder.png',
        external_url: `https://desci-scholar.org/patents/${encodeURIComponent(patent.id)}`,
        animation_url: storageInfo.fullTextUrl,
        attributes: [
          {
            trait_type: 'Patent ID',
            value: patent.id
          },
          {
            trait_type: 'Patent Office',
            value: patent.patentOffice
          },
          {
            trait_type: 'Filing Date',
            value: patent.filingDate
          },
          {
            trait_type: 'Grant Date',
            value: patent.grantDate
          },
          {
            trait_type: 'Inventors',
            value: patent.inventors
          },
          {
            trait_type: 'Assignees',
            value: patent.assignees
          }
        ],
        properties: {
          patentId: patent.id,
          metadata_id: storageInfo.metadataId,
          metadata_adapter: storageInfo.metadataAdapter,
          content_id: storageInfo.fullTextId,
          content_adapter: storageInfo.fullTextAdapter,
          schema: 'desci-scholar-patent-v1',
          inventors: patent.inventors.split(',').map(inventor => inventor.trim()),
          assignees: patent.assignees.split(',').map(assignee => assignee.trim()),
          classificationCodes: patent.classificationCodes ? patent.classificationCodes.split(',').map(code => code.trim()) : []
        }
      };
      
      // Speichere die NFT-Metadaten
      const metadataResult = await this.storageManager.storeJson(nftMetadata, {
        type: 'metadata',
        adapter: this.config.preferredAdapters.metadata,
        filename: `${patent.id.replace(/\//g, '_')}_nft_metadata.json`
      });
      
      logger.info(`NFT-Metadaten für Patent ${patent.id} erfolgreich erstellt`);
      
      return {
        success: true,
        patentId: patent.id,
        metadataId: metadataResult.adapter === 'IPFSAdapter' ? metadataResult.cid : metadataResult.infoHash,
        metadataAdapter: metadataResult.adapter,
        metadata: nftMetadata,
        metadataUrl: metadataResult.url
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen der NFT-Metadaten', {
        error: error.message,
        patentId: patent.id
      });
      throw error;
    }
  }
}/**
 * @fileoverview Speicher-Agent für DeSci-Scholar
 * 
 * Dieser Agent verwaltet die dezentrale Speicherung von wissenschaftlichen Publikationen,
 * Patenten und zugehörigen Metadaten. Er nutzt den StorageManager, um Daten flexibel
 * auf verschiedenen dezentralen Speicherlösungen wie IPFS und BitTorrent zu speichern.
 * Metadaten werden typischerweise auf IPFS gespeichert, während große Dateien über
 * BitTorrent verteilt werden.
 */

import { BaseAgent } from './index.js';
import { logger } from '../../utils/logger.js';
import { StorageManager } from '../../services/storage/StorageManager.js';

/**
 * Agent für die dezentrale Speicherung
 */
export class StorageAgent extends BaseAgent {
  /**
   * Erstellt eine neue Instanz des StorageAgent
   * @param {Object} options - Konfigurationsoptionen
   * @param {Object} options.storageManager - StorageManager-Instanz oder Konfiguration
   * @param {Object} options.databaseService - Datenbankdienst für Metadaten
   * @param {Object} options.config - Weitere Konfigurationsoptionen
   */
  constructor(options = {}) {
    super('StorageAgent');
    
    const {
      storageManager,
      databaseService,
      config = {}
    } = options;
    
    // Verwende die übergebene StorageManager-Instanz oder erstelle eine neue
    this.storageManager = storageManager instanceof StorageManager
      ? storageManager
      : new StorageManager(storageManager || {});
    
    this.databaseService = databaseService;
    
    // Konfiguration
    this.config = {
      metadataSchema: 'desci-scholar-v1',
      sizeThreshold: 10 * 1024 * 1024, // 10 MB
      preferredAdapters: {
        metadata: 'ipfs',
        smallFiles: 'ipfs',
        largeFiles: 'bittorrent'
      },
      ...config
    };
    
    logger.info('StorageAgent initialisiert');
  }
  
  /**
   * Initialisiert den Agent
   */
  async initialize() {
    logger.info('Initialisiere StorageAgent');
    
    // Prüfe, ob die erforderlichen Dienste verfügbar sind
    if (!this.storageManager) {
      throw new Error('StorageManager ist erforderlich');
    }
    
    if (!this.databaseService) {
      throw new Error('DatabaseService ist erforderlich');
    }
    
    // Initialisiere den StorageManager
    await this.storageManager.initialize();
    
    // Initialisiere die Datenbanktabellen
    await this.initializeDatabaseTables();
    
    logger.info('StorageAgent erfolgreich initialisiert');
  }
  
  /**
   * Initialisiert die erforderlichen Datenbanktabellen
   */
  async initializeDatabaseTables() {
    try {
      // Prüfe, ob die Speichertabelle existiert
      const storageTableExists = await this.databaseService.tableExists('decentralized_storage');
      
      if (!storageTableExists) {
        // Erstelle die Speichertabelle
        await this.databaseService.query(`
          CREATE TABLE decentralized_storage (
            id INT AUTO_INCREMENT PRIMARY KEY,
            content_type VARCHAR(50) NOT NULL,
            content_id VARCHAR(100) NOT NULL,
            metadata_id VARCHAR(100) NOT NULL,
            metadata_adapter VARCHAR(50) NOT NULL,
            content_id_full VARCHAR(100) NOT NULL,
            content_adapter VARCHAR(50) NOT NULL,
            size_bytes BIGINT,
            metadata_url VARCHAR(255),
            content_url VARCHAR(255),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY content_unique (content_type, content_id)
          )
        `);
        
        logger.info('Dezentrale Speichertabelle erstellt');
      }
    } catch (error) {
      logger.error('Fehler bei der Initialisierung der Datenbanktabellen', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Speichert eine wissenschaftliche Publikation
   * 
   * @param {Object} publication - Publikationsdaten
   * @param {Buffer|string} fullText - Volltext der Publikation
   * @param {Object} options - Speicheroptionen
   * @returns {Promise<Object>} Speicherinformationen
   */
  async storePublication(publication, fullText, options = {}) {
    try {
      logger.info(`Speichere Publikation mit DOI ${publication.doi}`);
      
      // Speichere die Publikation mit dem StorageManager
      const result = await this.storageManager.storePublication(publication, fullText, {
        ...options,
        type: options.type || (Buffer.isBuffer(fullText) ? (fullText.length >= this.config.sizeThreshold ? 'largeFile' : 'smallFile') : 'smallFile'),
        adapter: options.adapter || (options.type === 'metadata' ? this.config.preferredAdapters.metadata : (Buffer.isBuffer(fullText) && fullText.length >= this.config.sizeThreshold ? this.config.preferredAdapters.largeFiles : this.config.preferredAdapters.smallFiles))
      });
      
      // Speichere die Informationen in der Datenbank
      await this.databaseService.query(
        `INSERT INTO decentralized_storage 
          (content_type, content_id, metadata_id, metadata_adapter, content_id_full, content_adapter, size_bytes, metadata_url, content_url) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          'publication',
          publication.doi,
          result.metadataId,
          result.metadataAdapter,
          result.fullTextId,
          result.fullTextAdapter,
          result.fullTextSize,
          result.metadataUrl,
          result.fullTextUrl
        ]
      );
      
      logger.info(`Publikation mit DOI ${publication.doi} erfolgreich gespeichert`);
      
      return {
        success: true,
        doi: publication.doi,
        metadataId: result.metadataId,
        metadataAdapter: result.metadataAdapter,
        fullTextId: result.fullTextId,
        fullTextAdapter: result.fullTextAdapter,
        fullTextSize: result.fullTextSize,
        metadataUrl: result.metadataUrl,
        fullTextUrl: result.fullTextUrl
      };
    } catch (error) {
      logger.error('Fehler beim Speichern der Publikation', {
        error: error.message,
        doi: publication.doi
      });
      throw error;
    }
  }
  
  /**
   * Speichert ein Patent
   * 
   * @param {Object} patent - Patentdaten
   * @param {Buffer|string} fullText - Volltext des Patents
   * @param {Object} options - Speicheroptionen
   * @returns {Promise<Object>} Speicherinformationen
   */
  async storePatent(patent, fullText, options = {}) {
    try {
      logger.info(`Speichere Patent ${patent.id}`);
      
      // Speichere das Patent mit dem StorageManager
      const result = await this.storageManager.storePatent(patent, fullText, {
        ...options,
        type: options.type || (Buffer.isBuffer(fullText) ? (fullText.length >= this.config.sizeThreshold ? 'largeFile' : 'smallFile') : 'smallFile'),
        adapter: options.adapter || (options.type === 'metadata' ? this.config.preferredAdapters.metadata : (Buffer.isBuffer(fullText) && fullText.length >= this.config.sizeThreshold ? this.config.preferredAdapters.largeFiles : this.config.preferredAdapters.smallFiles))
      });
      
      // Speichere die Informationen in der Datenbank
      await this.databaseService.query(
        `INSERT INTO decentralized_storage 
          (content_type, content_id, metadata_id, metadata_adapter, content_id_full, content_adapter, size_bytes, metadata_url, content_url) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          'patent',
          patent.id,
          result.metadataId,
          result.metadataAdapter,
          result.fullTextId,
          result.fullTextAdapter,
          result.fullTextSize,
          result.metadataUrl,
          result.fullTextUrl
        ]
      );
      
      logger.info(`Patent ${patent.id} erfolgreich gespeichert`);
      
      return {
        success: true,
        patentId: patent.id,
        metadataId: result.metadataId,
        metadataAdapter: result.metadataAdapter,
        fullTextId: result.fullTextId,
        fullTextAdapter: result.fullTextAdapter,
        fullTextSize: result.fullTextSize,
        metadataUrl: result.metadataUrl,
        fullTextUrl: result.fullTextUrl
      };
    } catch (error) {
      logger.error('Fehler beim Speichern des Patents', {
        error: error.message,
        patentId: patent.id
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Publikation ab
   * 
   * @param {string} doi - DOI der Publikation
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Object>} Abgerufene Publikation
   */
  async retrievePublication(doi, options = {}) {
    try {
      logger.info(`Rufe Publikation mit DOI ${doi} ab`);
      
      // Hole die Speicherinformationen aus der Datenbank
      const storageInfo = await this.databaseService.query(
        'SELECT * FROM decentralized_storage WHERE content_type = ? AND content_id = ?',
        ['publication', doi]
      );
      
      if (!storageInfo || storageInfo.length === 0) {
        throw new Error(`Publikation mit DOI ${doi} nicht gefunden`);
      }
      
      const info = storageInfo[0];
      
      // Rufe die Metadaten ab
      const metadata = await this.storageManager.retrieveJson(info.metadata_id, {
        adapter: info.metadata_adapter
      });
      
      // Rufe den Volltext ab, falls gewünscht
      let fullText = null;
      if (options.includeFullText) {
        fullText = await this.storageManager.retrieveFile(info.content_id_full, {
          adapter: info.content_adapter
        });
      }
      
      logger.info(`Publikation mit DOI ${doi} erfolgreich abgerufen`);
      
      return {
        success: true,
        doi,
        metadata,
        fullText,
        metadataUrl: info.metadata_url,
        fullTextUrl: info.content_url,
        fullTextSize: info.size_bytes
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen der Publikation', {
        error: error.message,
        doi
      });
      throw error;
    }
  }
  
  /**
   * Ruft ein Patent ab
   * 
   * @param {string} patentId - Patent-ID
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Object>} Abgerufenes Patent
   */
  async retrievePatent(patentId, options = {}) {
    try {
      logger.info(`Rufe Patent ${patentId} ab`);
      
      // Hole die Speicherinformationen aus der Datenbank
      const storageInfo = await this.databaseService.query(
        'SELECT * FROM decentralized_storage WHERE content_type = ? AND content_id = ?',
        ['patent', patentId]
      );
      
      if (!storageInfo || storageInfo.length === 0) {
        throw new Error(`Patent ${patentId} nicht gefunden`);
      }
      
      const info = storageInfo[0];
      
      // Rufe die Metadaten ab
      const metadata = await this.storageManager.retrieveJson(info.metadata_id, {
        adapter: info.metadata_adapter
      });
      
      // Rufe den Volltext ab, falls gewünscht
      let fullText = null;
      if (options.includeFullText) {
        fullText = await this.storageManager.retrieveFile(info.content_id_full, {
          adapter: info.content_adapter
        });
      }
      
      logger.info(`Patent ${patentId} erfolgreich abgerufen`);
      
      return {
        success: true,
        patentId,
        metadata,
        fullText,
        metadataUrl: info.metadata_url,
        fullTextUrl: info.content_url,
        fullTextSize: info.size_bytes
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen des Patents', {
        error: error.message,
        patentId
      });
      throw error;
    }
  }
  
  /**
   * Erstellt einen NFT-Metadaten-Standard für wissenschaftliche Publikationen
   * 
   * @param {Object} publication - Publikationsdaten
   * @param {Object} storageInfo - Speicherinformationen
   * @returns {Promise<Object>} NFT-Metadaten
   */
  async createPublicationNftMetadata(publication, storageInfo) {
    try {
      logger.info(`Erstelle NFT-Metadaten für Publikation mit DOI ${publication.doi}`);
      
      // Erstelle die NFT-Metadaten nach dem ERC-721-Metadatenstandard
      const nftMetadata = {
        name: publication.title,
        description: publication.abstract,
        image: publication.coverImage || 'https://desci-scholar.org/assets/publication-nft-placeholder.png',
        external_url: `https://desci-scholar.org/publications/${encodeURIComponent(publication.doi)}`,
        animation_url: storageInfo.fullTextUrl,
        attributes: [
          {
            trait_type: 'DOI',
            value: publication.doi
          },
          {
            trait_type: 'Journal',
            value: publication.journal
          },
          {
            trait_type: 'Publication Date',
            value: publication.publicationDate
          },
          {
            trait_type: 'Authors',
            value: publication.authors
          },
          {
            trait_type: 'License',
            value: publication.license || 'CC-BY'
          }
        ],
        properties: {
          doi: publication.doi,
          metadata_id: storageInfo.metadataId,
          metadata_adapter: storageInfo.metadataAdapter,
          content_id: storageInfo.fullTextId,
          content_adapter: storageInfo.fullTextAdapter,
          schema: 'desci-scholar-publication-v1',
          license: publication.license || 'CC-BY',
          authors: publication.authors.split(',').map(author => author.trim()),
          keywords: publication.keywords ? publication.keywords.split(',').map(keyword => keyword.trim()) : [],
          citations: publication.citationCount || 0
        }
      };
      
      // Speichere die NFT-Metadaten
      const metadataResult = await this.storageManager.storeJson(nftMetadata, {
        type: 'metadata',
        adapter: this.config.preferredAdapters.metadata,
        filename: `${publication.doi.replace(/\//g, '_')}_nft_metadata.json`
      });
      
      logger.info(`NFT-Metadaten für Publikation mit DOI ${publication.doi} erfolgreich erstellt`);
      
      return {
        success: true,
        doi: publication.doi,
        metadataId: metadataResult.adapter === 'IPFSAdapter' ? metadataResult.cid : metadataResult.infoHash,
        metadataAdapter: metadataResult.adapter,
        metadata: nftMetadata,
        metadataUrl: metadataResult.url
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen der NFT-Metadaten', {
        error: error.message,
        doi: publication.doi
      });
      throw error;
    }
  }
  
  /**
   * Erstellt einen NFT-Metadaten-Standard für Patente
   * 
   * @param {Object} patent - Patentdaten
   * @param {Object} storageInfo - Speicherinformationen
   * @returns {Promise<Object>} NFT-Metadaten
   */
  async createPatentNftMetadata(patent, storageInfo) {
    try {
      logger.info(`Erstelle NFT-Metadaten für Patent ${patent.id}`);
      
      // Erstelle die NFT-Metadaten nach dem ERC-721-Metadatenstandard
      const nftMetadata = {
        name: `Patent: ${patent.title}`,
        description: patent.abstract,
        image: patent.image || 'https://desci-scholar.org/assets/patent-nft-placeholder.png',
        external_url: `https://desci-scholar.org/patents/${encodeURIComponent(patent.id)}`,
        animation_url: storageInfo.fullTextUrl,
        attributes: [
          {
            trait_type: 'Patent ID',
            value: patent.id
          },
          {
            trait_type: 'Patent Office',
            value: patent.patentOffice
          },
          {
            trait_type: 'Filing Date',
            value: patent.filingDate
          },
          {
            trait_type: 'Grant Date',
            value: patent.grantDate
          },
          {
            trait_type: 'Inventors',
            value: patent.inventors
          },
          {
            trait_type: 'Assignees',
            value: patent.assignees
          }
        ],
        properties: {
          patentId: patent.id,
          metadata_id: storageInfo.metadataId,
          metadata_adapter: storageInfo.metadataAdapter,
          content_id: storageInfo.fullTextId,
          content_adapter: storageInfo.fullTextAdapter,
          schema: 'desci-scholar-patent-v1',
          inventors: patent.inventors.split(',').map(inventor => inventor.trim()),
          assignees: patent.assignees.split(',').map(assignee => assignee.trim()),
          classificationCodes: patent.classificationCodes ? patent.classificationCodes.split(',').map(code => code.trim()) : []
        }
      };
      
      // Speichere die NFT-Metadaten
      const metadataResult = await this.storageManager.storeJson(nftMetadata, {
        type: 'metadata',
        adapter: this.config.preferredAdapters.metadata,
        filename: `${patent.id.replace(/\//g, '_')}_nft_metadata.json`
      });
      
      logger.info(`NFT-Metadaten für Patent ${patent.id} erfolgreich erstellt`);
      
      return {
        success: true,
        patentId: patent.id,
        metadataId: metadataResult.adapter === 'IPFSAdapter' ? metadataResult.cid : metadataResult.infoHash,
        metadataAdapter: metadataResult.adapter,
        metadata: nftMetadata,
        metadataUrl: metadataResult.url
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen der NFT-Metadaten', {
        error: error.message,
        patentId: patent.id
      });
      throw error;
    }
  }
}/**
 * @fileoverview Speicher-Agent für DeSci-Scholar
 * 
 * Dieser Agent verwaltet die dezentrale Speicherung von wissenschaftlichen Publikationen,
 * Patenten und zugehörigen Metadaten. Er nutzt den StorageManager, um Daten flexibel
 * auf verschiedenen dezentralen Speicherlösungen wie IPFS und BitTorrent zu speichern.
 * Metadaten werden typischerweise auf IPFS gespeichert, während große Dateien über
 * BitTorrent verteilt werden.
 */

import { BaseAgent } from './index.js';
import { logger } from '../../utils/logger.js';
import { StorageManager } from '../../services/storage/StorageManager.js';

/**
 * Agent für die dezentrale Speicherung
 */
export class StorageAgent extends BaseAgent {
  /**
   * Erstellt eine neue Instanz des StorageAgent
   * @param {Object} options - Konfigurationsoptionen
   * @param {Object} options.storageManager - StorageManager-Instanz oder Konfiguration
   * @param {Object} options.databaseService - Datenbankdienst für Metadaten
   * @param {Object} options.config - Weitere Konfigurationsoptionen
   */
  constructor(options = {}) {
    super('StorageAgent');
    
    const {
      storageManager,
      databaseService,
      config = {}
    } = options;
    
    // Verwende die übergebene StorageManager-Instanz oder erstelle eine neue
    this.storageManager = storageManager instanceof StorageManager
      ? storageManager
      : new StorageManager(storageManager || {});
    
    this.databaseService = databaseService;
    
    // Konfiguration
    this.config = {
      metadataSchema: 'desci-scholar-v1',
      sizeThreshold: 10 * 1024 * 1024, // 10 MB
      preferredAdapters: {
        metadata: 'ipfs',
        smallFiles: 'ipfs',
        largeFiles: 'bittorrent'
      },
      ...config
    };
    
    logger.info('StorageAgent initialisiert');
  }
  
  /**
   * Initialisiert den Agent
   */
  async initialize() {
    logger.info('Initialisiere StorageAgent');
    
    // Prüfe, ob die erforderlichen Dienste verfügbar sind
    if (!this.storageManager) {
      throw new Error('StorageManager ist erforderlich');
    }
    
    if (!this.databaseService) {
      throw new Error('DatabaseService ist erforderlich');
    }
    
    // Initialisiere den StorageManager
    await this.storageManager.initialize();
    
    // Initialisiere die Datenbanktabellen
    await this.initializeDatabaseTables();
    
    logger.info('StorageAgent erfolgreich initialisiert');
  }
  
  /**
   * Initialisiert die erforderlichen Datenbanktabellen
   */
  async initializeDatabaseTables() {
    try {
      // Prüfe, ob die Speichertabelle existiert
      const storageTableExists = await this.databaseService.tableExists('decentralized_storage');
      
      if (!storageTableExists) {
        // Erstelle die Speichertabelle
        await this.databaseService.query(`
          CREATE TABLE decentralized_storage (
            id INT AUTO_INCREMENT PRIMARY KEY,
            content_type VARCHAR(50) NOT NULL,
            content_id VARCHAR(100) NOT NULL,
            metadata_id VARCHAR(100) NOT NULL,
            metadata_adapter VARCHAR(50) NOT NULL,
            content_id_full VARCHAR(100) NOT NULL,
            content_adapter VARCHAR(50) NOT NULL,
            size_bytes BIGINT,
            metadata_url VARCHAR(255),
            content_url VARCHAR(255),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY content_unique (content_type, content_id)
          )
        `);
        
        logger.info('Dezentrale Speichertabelle erstellt');
      }
    } catch (error) {
      logger.error('Fehler bei der Initialisierung der Datenbanktabellen', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Speichert eine wissenschaftliche Publikation
   * 
   * @param {Object} publication - Publikationsdaten
   * @param {Buffer|string} fullText - Volltext der Publikation
   * @param {Object} options - Speicheroptionen
   * @returns {Promise<Object>} Speicherinformationen
   */
  async storePublication(publication, fullText, options = {}) {
    try {
      logger.info(`Speichere Publikation mit DOI ${publication.doi}`);
      
      // Speichere die Publikation mit dem StorageManager
      const result = await this.storageManager.storePublication(publication, fullText, {
        ...options,
        type: options.type || (Buffer.isBuffer(fullText) ? (fullText.length >= this.config.sizeThreshold ? 'largeFile' : 'smallFile') : 'smallFile'),
        adapter: options.adapter || (options.type === 'metadata' ? this.config.preferredAdapters.metadata : (Buffer.isBuffer(fullText) && fullText.length >= this.config.sizeThreshold ? this.config.preferredAdapters.largeFiles : this.config.preferredAdapters.smallFiles))
      });
      
      // Speichere die Informationen in der Datenbank
      await this.databaseService.query(
        `INSERT INTO decentralized_storage 
          (content_type, content_id, metadata_id, metadata_adapter, content_id_full, content_adapter, size_bytes, metadata_url, content_url) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          'publication',
          publication.doi,
          result.metadataId,
          result.metadataAdapter,
          result.fullTextId,
          result.fullTextAdapter,
          result.fullTextSize,
          result.metadataUrl,
          result.fullTextUrl
        ]
      );
      
      logger.info(`Publikation mit DOI ${publication.doi} erfolgreich gespeichert`);
      
      return {
        success: true,
        doi: publication.doi,
        metadataId: result.metadataId,
        metadataAdapter: result.metadataAdapter,
        fullTextId: result.fullTextId,
        fullTextAdapter: result.fullTextAdapter,
        fullTextSize: result.fullTextSize,
        metadataUrl: result.metadataUrl,
        fullTextUrl: result.fullTextUrl
      };
    } catch (error) {
      logger.error('Fehler beim Speichern der Publikation', {
        error: error.message,
        doi: publication.doi
      });
      throw error;
    }
  }
  
  /**
   * Speichert ein Patent
   * 
   * @param {Object} patent - Patentdaten
   * @param {Buffer|string} fullText - Volltext des Patents
   * @param {Object} options - Speicheroptionen
   * @returns {Promise<Object>} Speicherinformationen
   */
  async storePatent(patent, fullText, options = {}) {
    try {
      logger.info(`Speichere Patent ${patent.id}`);
      
      // Speichere das Patent mit dem StorageManager
      const result = await this.storageManager.storePatent(patent, fullText, {
        ...options,
        type: options.type || (Buffer.isBuffer(fullText) ? (fullText.length >= this.config.sizeThreshold ? 'largeFile' : 'smallFile') : 'smallFile'),
        adapter: options.adapter || (options.type === 'metadata' ? this.config.preferredAdapters.metadata : (Buffer.isBuffer(fullText) && fullText.length >= this.config.sizeThreshold ? this.config.preferredAdapters.largeFiles : this.config.preferredAdapters.smallFiles))
      });
      
      // Speichere die Informationen in der Datenbank
      await this.databaseService.query(
        `INSERT INTO decentralized_storage 
          (content_type, content_id, metadata_id, metadata_adapter, content_id_full, content_adapter, size_bytes, metadata_url, content_url) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          'patent',
          patent.id,
          result.metadataId,
          result.metadataAdapter,
          result.fullTextId,
          result.fullTextAdapter,
          result.fullTextSize,
          result.metadataUrl,
          result.fullTextUrl
        ]
      );
      
      logger.info(`Patent ${patent.id} erfolgreich gespeichert`);
      
      return {
        success: true,
        patentId: patent.id,
        metadataId: result.metadataId,
        metadataAdapter: result.metadataAdapter,
        fullTextId: result.fullTextId,
        fullTextAdapter: result.fullTextAdapter,
        fullTextSize: result.fullTextSize,
        metadataUrl: result.metadataUrl,
        fullTextUrl: result.fullTextUrl
      };
    } catch (error) {
      logger.error('Fehler beim Speichern des Patents', {
        error: error.message,
        patentId: patent.id
      });
      throw error;
    }
  }
  
  /**
   * Ruft eine Publikation ab
   * 
   * @param {string} doi - DOI der Publikation
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Object>} Abgerufene Publikation
   */
  async retrievePublication(doi, options = {}) {
    try {
      logger.info(`Rufe Publikation mit DOI ${doi} ab`);
      
      // Hole die Speicherinformationen aus der Datenbank
      const storageInfo = await this.databaseService.query(
        'SELECT * FROM decentralized_storage WHERE content_type = ? AND content_id = ?',
        ['publication', doi]
      );
      
      if (!storageInfo || storageInfo.length === 0) {
        throw new Error(`Publikation mit DOI ${doi} nicht gefunden`);
      }
      
      const info = storageInfo[0];
      
      // Rufe die Metadaten ab
      const metadata = await this.storageManager.retrieveJson(info.metadata_id, {
        adapter: info.metadata_adapter
      });
      
      // Rufe den Volltext ab, falls gewünscht
      let fullText = null;
      if (options.includeFullText) {
        fullText = await this.storageManager.retrieveFile(info.content_id_full, {
          adapter: info.content_adapter
        });
      }
      
      logger.info(`Publikation mit DOI ${doi} erfolgreich abgerufen`);
      
      return {
        success: true,
        doi,
        metadata,
        fullText,
        metadataUrl: info.metadata_url,
        fullTextUrl: info.content_url,
        fullTextSize: info.size_bytes
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen der Publikation', {
        error: error.message,
        doi
      });
      throw error;
    }
  }
  
  /**
   * Ruft ein Patent ab
   * 
   * @param {string} patentId - Patent-ID
   * @param {Object} options - Abrufoptionen
   * @returns {Promise<Object>} Abgerufenes Patent
   */
  async retrievePatent(patentId, options = {}) {
    try {
      logger.info(`Rufe Patent ${patentId} ab`);
      
      // Hole die Speicherinformationen aus der Datenbank
      const storageInfo = await this.databaseService.query(
        'SELECT * FROM decentralized_storage WHERE content_type = ? AND content_id = ?',
        ['patent', patentId]
      );
      
      if (!storageInfo || storageInfo.length === 0) {
        throw new Error(`Patent ${patentId} nicht gefunden`);
      }
      
      const info = storageInfo[0];
      
      // Rufe die Metadaten ab
      const metadata = await this.storageManager.retrieveJson(info.metadata_id, {
        adapter: info.metadata_adapter
      });
      
      // Rufe den Volltext ab, falls gewünscht
      let fullText = null;
      if (options.includeFullText) {
        fullText = await this.storageManager.retrieveFile(info.content_id_full, {
          adapter: info.content_adapter
        });
      }
      
      logger.info(`Patent ${patentId} erfolgreich abgerufen`);
      
      return {
        success: true,
        patentId,
        metadata,
        fullText,
        metadataUrl: info.metadata_url,
        fullTextUrl: info.content_url,
        fullTextSize: info.size_bytes
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen des Patents', {
        error: error.message,
        patentId
      });
      throw error;
    }
  }
  
  /**
   * Erstellt einen NFT-Metadaten-Standard für wissenschaftliche Publikationen
   * 
   * @param {Object} publication - Publikationsdaten
   * @param {Object} storageInfo - Speicherinformationen
   * @returns {Promise<Object>} NFT-Metadaten
   */
  async createPublicationNftMetadata(publication, storageInfo) {
    try {
      logger.info(`Erstelle NFT-Metadaten für Publikation mit DOI ${publication.doi}`);
      
      // Erstelle die NFT-Metadaten nach dem ERC-721-Metadatenstandard
      const nftMetadata = {
        name: publication.title,
        description: publication.abstract,
        image: publication.coverImage || 'https://desci-scholar.org/assets/publication-nft-placeholder.png',
        external_url: `https://desci-scholar.org/publications/${encodeURIComponent(publication.doi)}`,
        animation_url: storageInfo.fullTextUrl,
        attributes: [
          {
            trait_type: 'DOI',
            value: publication.doi
          },
          {
            trait_type: 'Journal',
            value: publication.journal
          },
          {
            trait_type: 'Publication Date',
            value: publication.publicationDate
          },
          {
            trait_type: 'Authors',
            value: publication.authors
          },
          {
            trait_type: 'License',
            value: publication.license || 'CC-BY'
          }
        ],
        properties: {
          doi: publication.doi,
          metadata_id: storageInfo.metadataId,
          metadata_adapter: storageInfo.metadataAdapter,
          content_id: storageInfo.fullTextId,
          content_adapter: storageInfo.fullTextAdapter,
          schema: 'desci-scholar-publication-v1',
          license: publication.license || 'CC-BY',
          authors: publication.authors.split(',').map(author => author.trim()),
          keywords: publication.keywords ? publication.keywords.split(',').map(keyword => keyword.trim()) : [],
          citations: publication.citationCount || 0
        }
      };
      
      // Speichere die NFT-Metadaten
      const metadataResult = await this.storageManager.storeJson(nftMetadata, {
        type: 'metadata',
        adapter: this.config.preferredAdapters.metadata,
        filename: `${publication.doi.replace(/\//g, '_')}_nft_metadata.json`
      });
      
      logger.info(`NFT-Metadaten für Publikation mit DOI ${publication.doi} erfolgreich erstellt`);
      
      return {
        success: true,
        doi: publication.doi,
        metadataId: metadataResult.adapter === 'IPFSAdapter' ? metadataResult.cid : metadataResult.infoHash,
        metadataAdapter: metadataResult.adapter,
        metadata: nftMetadata,
        metadataUrl: metadataResult.url
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen der NFT-Metadaten', {
        error: error.message,
        doi: publication.doi
      });
      throw error;
    }
  }
  
  /**
   * Erstellt einen NFT-Metadaten-Standard für Patente
   * 
   * @param {Object} patent - Patentdaten
   * @param {Object} storageInfo - Speicherinformationen
   * @returns {Promise<Object>} NFT-Metadaten
   */
  async createPatentNftMetadata(patent, storageInfo) {
    try {
      logger.info(`Erstelle NFT-Metadaten für Patent ${patent.id}`);
      
      // Erstelle die NFT-Metadaten nach dem ERC-721-Metadatenstandard
      const nftMetadata = {
        name: `Patent: ${patent.title}`,
        description: patent.abstract,
        image: patent.image || 'https://desci-scholar.org/assets/patent-nft-placeholder.png',
        external_url: `https://desci-scholar.org/patents/${encodeURIComponent(patent.id)}`,
        animation_url: storageInfo.fullTextUrl,
        attributes: [
          {
            trait_type: 'Patent ID',
            value: patent.id
          },
          {
            trait_type: 'Patent Office',
            value: patent.patentOffice
          },
          {
            trait_type: 'Filing Date',
            value: patent.filingDate
          },
          {
            trait_type: 'Grant Date',
            value: patent.grantDate
          },
          {
            trait_type: 'Inventors',
            value: patent.inventors
          },
          {
            trait_type: 'Assignees',
            value: patent.assignees
          }
        ],
        properties: {
          patentId: patent.id,
          metadata_id: storageInfo.metadataId,
          metadata_adapter: storageInfo.metadataAdapter,
          content_id: storageInfo.fullTextId,
          content_adapter: storageInfo.fullTextAdapter,
          schema: 'desci-scholar-patent-v1',
          inventors: patent.inventors.split(',').map(inventor => inventor.trim()),
          assignees: patent.assignees.split(',').map(assignee => assignee.trim()),
          classificationCodes: patent.classificationCodes ? patent.classificationCodes.split(',').map(code => code.trim()) : []
        }
      };
      
      // Speichere die NFT-Metadaten
      const metadataResult = await this.storageManager.storeJson(nftMetadata, {
        type: 'metadata',
        adapter: this.config.preferredAdapters.metadata,
        filename: `${patent.id.replace(/\//g, '_')}_nft_metadata.json`
      });
      
      logger.info(`NFT-Metadaten für Patent ${patent.id} erfolgreich erstellt`);
      
      return {
        success: true,
        patentId: patent.id,
        metadataId: metadataResult.adapter === 'IPFSAdapter' ? metadataResult.cid : metadataResult.infoHash,
        metadataAdapter: metadataResult.adapter,
        metadata: nftMetadata,
        metadataUrl: metadataResult.url
      };
    } catch (error) {
      logger.error('Fehler beim Erstellen der NFT-Metadaten', {
        error: error.message,
        patentId: patent.id
      });
      throw error;
    }
  }
}