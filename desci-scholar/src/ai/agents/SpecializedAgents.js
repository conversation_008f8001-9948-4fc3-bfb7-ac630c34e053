import { BaseAgent } from './index.js';
import { logger } from '../../utils/logger.js';

/**
 * Agent für die Bewertung von Forschungsvorschlägen
 */
export class ProposalVettingAgent extends BaseAgent {
  constructor(options = {}) {
    super('ProposalVettingAgent');
    this.criteria = options.criteria || {
      novelty: 0.3,
      methodology: 0.3,
      feasibility: 0.2,
      impact: 0.2
    };
  }
  
  /**
   * Bewertet einen Forschungsvorschlag
   * @param {Object} proposal - Forschungsvorschlag
   * @returns {Promise<Object>} Bewertungsergebnis
   */
  async evaluateProposal(proposal) {
    logger.info('Bewerte Forschungsvorschlag', { 
      title: proposal.title,
      id: proposal.id
    });
    
    const prompt = this._buildEvaluationPrompt(proposal);
    
    try {
      const response = await this.generateText(prompt, {
        temperature: 0.2,
        maxTokens: 1000
      });
      
      // <PERSON><PERSON> der strukturierten Antwort
      const result = this._parseEvaluationResponse(response);
      
      // Berechnung des Gesamtscores
      const overallScore = Object.entries(this.criteria).reduce((sum, [key, weight]) => {
        return sum + (result.scores[key] || 0) * weight;
      }, 0);
      
      return {
        ...result,
        overallScore: parseFloat(overallScore.toFixed(2))
      };
    } catch (error) {
      logger.error('Fehler bei der Bewertung des Vorschlags', {
        error: error.message,
        proposalId: proposal.id
      });
      throw error;
    }
  }
  
  /**
   * Erstellt den Prompt für die Bewertung
   * @private
   * @param {Object} proposal - Forschungsvorschlag
   * @returns {string} Bewertungsprompt
   */
  _buildEvaluationPrompt(proposal) {
    return `
Als wissenschaftlicher Gutachter bewerten Sie bitte den folgenden Forschungsvorschlag.
Berücksichtigen Sie dabei die Kriterien Neuartigkeit, Methodik, Durchführbarkeit und Auswirkung.

TITEL: ${proposal.title}

ZUSAMMENFASSUNG:
${proposal.abstract}

METHODIK:
${proposal.methodology || 'Keine detaillierte Methodik angegeben.'}

RESSOURCEN UND ZEITPLAN:
${proposal.resources || 'Keine Ressourcen angegeben.'} 
${proposal.timeline || 'Kein Zeitplan angegeben.'}

Bitte bewerten Sie den Vorschlag nach folgenden Kriterien auf einer Skala von 1-10:
1. Neuartigkeit: Ist der Vorschlag innovativ und trägt er zum Fortschritt des Feldes bei?
2. Methodik: Ist die vorgeschlagene Methodik angemessen und gut durchdacht?
3. Durchführbarkeit: Ist das Projekt mit den angegebenen Ressourcen und im angegebenen Zeitrahmen durchführbar?
4. Auswirkung: Welche potenziellen Auswirkungen könnte das Projekt auf das Forschungsfeld und darüber hinaus haben?

Geben Sie für jedes Kriterium eine Bewertung und eine kurze Begründung an.
Schließen Sie mit einer Gesamtbewertung und Empfehlung ab.

Formatieren Sie Ihre Antwort wie folgt:
NEUARTIGKEIT: [Bewertung] - [Begründung]
METHODIK: [Bewertung] - [Begründung]
DURCHFÜHRBARKEIT: [Bewertung] - [Begründung]
AUSWIRKUNG: [Bewertung] - [Begründung]
GESAMTBEWERTUNG: [Empfehlung: Annehmen/Mit Änderungen annehmen/Ablehnen]
BEGRÜNDUNG: [Zusammenfassende Begründung]
`
  }

  _buildSummaryPrompt(reviews, publicationContext) {
    // Reviews formatieren
    const formattedReviews = reviews.map((review, index) => {
      return `
REVIEW ${index + 1}:
BEWERTUNG: ${review.rating}/10
STÄRKEN: ${review.strengths}
SCHWÄCHEN: ${review.weaknesses}
KOMMENTARE: ${review.comments}
`;
    }).join('\n');
    
    return `
Als wissenschaftlicher Editor fassen Sie bitte die folgenden Peer-Reviews für eine Publikation zusammen.
Identifizieren Sie gemeinsame Themen, Stärken, Schwächen und Empfehlungen.

PUBLIKATION: ${publicationContext.title}
AUTOREN: ${publicationContext.authors.join(', ')}
ABSTRACT: ${publicationContext.abstract}

PEER-REVIEWS:
${formattedReviews}

Bitte erstellen Sie eine strukturierte Zusammenfassung der Reviews mit folgenden Abschnitten:
1. Allgemeiner Konsens: Gibt es eine allgemeine Übereinstimmung unter den Reviewern?
2. Hauptstärken: Was sind die von den Reviewern identifizierten Hauptstärken?
3. Hauptschwächen: Was sind die von den Reviewern identifizierten Hauptschwächen?
4. Widersprüche: Gibt es Punkte, bei denen sich die Reviewer widersprechen?
5. Empfehlungen: Welche Empfehlungen für Verbesserungen wurden gegeben?
6. Redaktionelle Empfehlung: Basierend auf den Reviews, welche Entscheidung wäre angemessen?

Formatieren Sie Ihre Antwort wie folgt:
KONSENS: [Zusammenfassung des allgemeinen Konsens]
STÄRKEN: [Liste der Hauptstärken]
SCHWÄCHEN: [Liste der Hauptschwächen]
WIDERSPRÜCHE: [Identifizierte Widersprüche zwischen den Reviews]
EMPFEHLUNGEN: [Zusammengefasste Empfehlungen für Verbesserungen]
REDAKTIONELLE EMPFEHLUNG: [Annehmen/Mit größeren Überarbeitungen annehmen/Mit kleineren Überarbeitungen annehmen/Ablehnen]
BEGRÜNDUNG: [Begründung für die redaktionelle Empfehlung]
`;
  }
  
  /**
   * Parst die Antwort des KI-Modells
   * @private
   * @param {string} response - Antwort des KI-Modells
   * @returns {Object} Strukturierte Zusammenfassung
   */
  _parseSummaryResponse(response) {
    let consensus = '';
    let strengths = [];
    let weaknesses = [];
    let contradictions = '';
    let recommendations = '';
    let editorialRecommendation = '';
    let justification = '';
    
    // Extrahiere Konsens
    const consensusMatch = response.match(/KONSENS:\s*([\s\S]*?)(?=STÄRKEN:|$)/);
    if (consensusMatch) consensus = consensusMatch[1].trim();
    
    // Extrahiere Stärken
    const strengthsMatch = response.match(/STÄRKEN:\s*([\s\S]*?)(?=SCHWÄCHEN:|$)/);
    if (strengthsMatch) {
      const strengthsText = strengthsMatch[1].trim();
      strengths = strengthsText.split(/\n-\s*/).filter(s => s.trim()).map(s => s.trim());
      if (strengths.length === 1 && !strengthsText.includes('-')) {
        // Falls keine Aufzählungszeichen verwendet wurden
        strengths = strengthsText.split(/\.\s*/).filter(s => s.trim()).map(s => s.trim() + '.');
      }
    }
    
    // Extrahiere Schwächen
    const weaknessesMatch = response.match(/SCHWÄCHEN:\s*([\s\S]*?)(?=WIDERSPRÜCHE:|$)/);
    if (weaknessesMatch) {
      const weaknessesText = weaknessesMatch[1].trim();
      weaknesses = weaknessesText.split(/\n-\s*/).filter(s => s.trim()).map(s => s.trim());
      if (weaknesses.length === 1 && !weaknessesText.includes('-')) {
        // Falls keine Aufzählungszeichen verwendet wurden
        weaknesses = weaknessesText.split(/\.\s*/).filter(s => s.trim()).map(s => s.trim() + '.');
      }
    }
    
    // Extrahiere Widersprüche
    const contradictionsMatch = response.match(/WIDERSPRÜCHE:\s*([\s\S]*?)(?=EMPFEHLUNGEN:|$)/);
    if (contradictionsMatch) contradictions = contradictionsMatch[1].trim();
    
    // Extrahiere Empfehlungen
    const recommendationsMatch = response.match(/EMPFEHLUNGEN:\s*([\s\S]*?)(?=REDAKTIONELLE EMPFEHLUNG:|$)/);
    if (recommendationsMatch) recommendations = recommendationsMatch[1].trim();
    
    // Extrahiere redaktionelle Empfehlung
    const editorialMatch = response.match(/REDAKTIONELLE EMPFEHLUNG:\s*(Annehmen|Mit größeren Überarbeitungen annehmen|Mit kleineren Überarbeitungen annehmen|Ablehnen)/);
    if (editorialMatch) editorialRecommendation = editorialMatch[1];
    
    // Extrahiere Begründung
    const justificationMatch = response.match(/BEGRÜNDUNG:\s*([\s\S]+)$/);
    if (justificationMatch) justification = justificationMatch[1].trim();
    
    return {
      consensus,
      strengths,
      weaknesses,
      contradictions,
      recommendations,
      editorialRecommendation,
      justification,
      rawResponse: response
    };
  }
}

/**
 * Agent für die Analyse von Forschungsdaten
 */
export class ResearchDataAnalysisAgent extends BaseAgent {
  constructor() {
    super('ResearchDataAnalysisAgent');
  }
  
  /**
   * Analysiert Forschungsdaten und identifiziert Trends
   * @param {Object} dataset - Forschungsdatensatz
   * @param {Object} analysisParams - Parameter für die Analyse
   * @returns {Promise<Object>} Analyseergebnis
   */
  async analyzeData(dataset, analysisParams = {}) {
    logger.info('Analysiere Forschungsdaten', { 
      datasetId: dataset.id,
      datasetName: dataset.name,
      analysisType: analysisParams.type || 'general'
    });
    
    const prompt = this._buildAnalysisPrompt(dataset, analysisParams);
    
    try {
      const response = await this.generateText(prompt, {
        temperature: 0.2,
        maxTokens: 1500
      });
      
      // Parsen der strukturierten Antwort
      const result = this._parseAnalysisResponse(response);
      
      return {
        ...result,
        datasetId: dataset.id,
        analysisType: analysisParams.type || 'general',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      logger.error('Fehler bei der Analyse der Forschungsdaten', {
        error: error.message,
        datasetId: dataset.id
      });
      throw error;
    }
  }
  
  /**
   * Erstellt den Prompt für die Datenanalyse
   * @private
   * @param {Object} dataset - Forschungsdatensatz
   * @param {Object} analysisParams - Parameter für die Analyse
   * @returns {string} Analyseprompt
   */
  _buildAnalysisPrompt(dataset, analysisParams) {
    // Daten formatieren (vereinfachte Version, in der Realität würden wir
    // die Daten je nach Typ anders aufbereiten)
    let dataDescription = '';
    
    if (dataset.description) {
      dataDescription = `BESCHREIBUNG: ${dataset.description}\n\n`;
    }
    
    if (dataset.variables && dataset.variables.length > 0) {
      dataDescription += 'VARIABLEN:\n';
      dataset.variables.forEach(variable => {
        dataDescription += `- ${variable.name}: ${variable.type} - ${variable.description || 'Keine Beschreibung'}\n`;
      });
      dataDescription += '\n';
    }
    
    if (dataset.summary) {
      dataDescription += `ZUSAMMENFASSUNG DER DATEN:\n${dataset.summary}\n\n`;
    }
    
    // Analyseparameter formatieren
    let analysisInstructions = '';
    if (analysisParams.type === 'trend') {
      analysisInstructions = 'Konzentrieren Sie sich besonders auf Trends und Muster in den Daten über Zeit.';
    } else if (analysisParams.type === 'correlation') {
      analysisInstructions = 'Identifizieren Sie Korrelationen zwischen verschiedenen Variablen in den Daten.';
    } else if (analysisParams.type === 'outlier') {
      analysisInstructions = 'Identifizieren Sie Ausreißer und ungewöhnliche Datenpunkte.';
    } else {
      analysisInstructions = 'Führen Sie eine allgemeine Analyse der Daten durch, einschließlich Trends, Korrelationen und Ausreißer.';
    }
    
    return `
Als Datenanalyst analysieren Sie bitte den folgenden Forschungsdatensatz.
${analysisInstructions}

DATENSATZ: ${dataset.name}
${dataDescription}
DATENQUELLE: ${dataset.source || 'Nicht angegeben'}
ZEITRAUM: ${dataset.timeframe || 'Nicht angegeben'}

Bitte erstellen Sie eine strukturierte Analyse mit folgenden Abschnitten:
1. Haupterkenntnisse: Was sind die wichtigsten Erkenntnisse aus den Daten?
2. Identifizierte Muster: Welche Muster oder Trends sind in den Daten erkennbar?
3. Statistische Auffälligkeiten: Welche statistischen Auffälligkeiten gibt es in den Daten?
4. Forschungslücken: Welche Forschungslücken oder offenen Fragen werden durch die Daten aufgezeigt?
5. Empfehlungen: Welche Empfehlungen für weitere Forschung oder Datenerhebung können gegeben werden?

Formatieren Sie Ihre Antwort wie folgt:
HAUPTERKENNTNISSE: [Liste der wichtigsten Erkenntnisse]
MUSTER: [Identifizierte Muster und Trends]
STATISTISCHE AUFFÄLLIGKEITEN: [Statistische Auffälligkeiten]
FORSCHUNGSLÜCKEN: [Identifizierte Forschungslücken]