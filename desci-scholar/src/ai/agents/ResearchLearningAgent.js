/**
 * @fileoverview KI-Agent, der aus wissenschaftlichen Publikationen lernt
 * 
 * Dieser Agent kann aus den wissenschaftlichen Publikationen auf der Plattform lernen,
 * Zusammenhänge zwischen Forschungsgebieten erkennen, Trends identifizieren und
 * Forschern bei ihrer Arbeit unterstützen.
 */

import axios from 'axios';
import { logger } from '../../utils/logger.js';
import { BaseAgent } from './index.js';

/**
 * Agent für das Lernen aus wissenschaftlichen Publikationen
 */
export class ResearchLearningAgent extends BaseAgent {
  /**
   * Erstellt eine neue Instanz des ResearchLearningAgent
   * @param {Object} options - Konfigurationsoptionen
   * @param {Object} options.databaseService - Datenbankdienst für den Zugriff auf Publikationen
   * @param {Object} options.vectorDatabase - Vektordatenbank für semantische Suche
   * @param {Object} options.textEmbeddingService - Dienst für Text-Embeddings
   * @param {Object} options.config - Weitere Konfigurationsoptionen
   */
  constructor(options = {}) {
    super('ResearchLearningAgent');
    
    const {
      databaseService,
      vectorDatabase,
      textEmbeddingService,
      config = {}
    } = options;
    
    this.databaseService = databaseService;
    this.vectorDatabase = vectorDatabase;
    this.textEmbeddingService = textEmbeddingService;
    
    this.config = {
      maxContextSize: 8000,
      minRelevanceScore: 0.7,
      maxPublicationsPerQuery: 10,
      ...config
    };
    
    this.knowledgeBase = {
      domains: new Map(),
      concepts: new Map(),
      trends: new Map(),
      lastUpdated: null
    };
  }
  
  /**
   * Initialisiert den Agenten und lädt die Wissensbasis
   * @returns {Promise<boolean>} Erfolgsstatus
   */
  async initialize() {
    try {
      logger.info('ResearchLearningAgent: Initialisiere');
      
      // Prüfen, ob die erforderlichen Dienste verfügbar sind
      if (!this.databaseService) {
        logger.warn('ResearchLearningAgent: DatabaseService nicht verfügbar, Funktionalität wird eingeschränkt sein');
      }
      
      if (!this.vectorDatabase) {
        logger.warn('ResearchLearningAgent: VectorDatabase nicht verfügbar, semantische Suche wird deaktiviert');
      }
      
      if (!this.textEmbeddingService) {
        logger.warn('ResearchLearningAgent: TextEmbeddingService nicht verfügbar, Embedding-Funktionalität wird deaktiviert');
      }
      
      // Lade initiale Wissensbasis
      await this._loadKnowledgeBase();
      
      logger.info('ResearchLearningAgent: Erfolgreich initialisiert');
      
      return true;
    } catch (error) {
      logger.error('ResearchLearningAgent: Fehler bei der Initialisierung', {
        error: error.message,
        stack: error.stack
      });
      
      return false;
    }
  }
  
  /**
   * Lädt die Wissensbasis aus der Datenbank
   * @private
   * @returns {Promise<void>}
   */
  async _loadKnowledgeBase() {
    if (!this.databaseService) {
      return;
    }
    
    try {
      logger.info('ResearchLearningAgent: Lade Wissensbasis');
      
      // Lade Domänen
      const domains = await this.databaseService.query(
        'SELECT * FROM research_domains ORDER BY publication_count DESC LIMIT 100'
      );
      
      if (domains && domains.length > 0) {
        domains.forEach(domain => {
          this.knowledgeBase.domains.set(domain.id, {
            name: domain.name,
            description: domain.description,
            publicationCount: domain.publication_count,
            relatedDomains: new Set(domain.related_domains || [])
          });
        });
      }
      
      // Lade Konzepte
      const concepts = await this.databaseService.query(
        'SELECT * FROM research_concepts ORDER BY importance DESC LIMIT 500'
      );
      
      if (concepts && concepts.length > 0) {
        concepts.forEach(concept => {
          this.knowledgeBase.concepts.set(concept.id, {
            name: concept.name,
            description: concept.description,
            domains: new Set(concept.domains || []),
            relatedConcepts: new Set(concept.related_concepts || [])
          });
        });
      }
      
      // Lade Trends
      const trends = await this.databaseService.query(
        'SELECT * FROM research_trends WHERE active = 1 ORDER BY momentum DESC LIMIT 50'
      );
      
      if (trends && trends.length > 0) {
        trends.forEach(trend => {
          this.knowledgeBase.trends.set(trend.id, {
            name: trend.name,
            description: trend.description,
            momentum: trend.momentum,
            startDate: trend.start_date,
            domains: new Set(trend.domains || [])
          });
        });
      }
      
      this.knowledgeBase.lastUpdated = new Date();
      
      logger.info('ResearchLearningAgent: Wissensbasis erfolgreich geladen', {
        domains: this.knowledgeBase.domains.size,
        concepts: this.knowledgeBase.concepts.size,
        trends: this.knowledgeBase.trends.size
      });
    } catch (error) {
      logger.error('ResearchLearningAgent: Fehler beim Laden der Wissensbasis', {
        error: error.message,
        stack: error.stack
      });
    }
  }
  
  /**
   * Aktualisiert die Wissensbasis mit neuen Publikationen
   * @param {Array<Object>} publications - Liste von Publikationen
   * @returns {Promise<Object>} Ergebnis der Aktualisierung
   */
  async updateKnowledgeBase(publications) {
    try {
      logger.info('ResearchLearningAgent: Aktualisiere Wissensbasis', {
        publicationCount: publications.length
      });
      
      // Extrahiere Konzepte und Domänen aus den Publikationen
      const extractionPrompt = this._buildConceptExtractionPrompt(publications);
      
      const extractionResponse = await this.generateText(extractionPrompt, {
        temperature: 0.2,
        maxTokens: 2000
      });
      
      // Parsen der strukturierten Antwort
      const extractedData = this._parseExtractionResponse(extractionResponse);
      
      // Aktualisiere die Wissensbasis
      await this._updateKnowledgeBaseWithExtractedData(extractedData);
      
      this.knowledgeBase.lastUpdated = new Date();
      
      return {
        success: true,
        extractedDomains: extractedData.domains.length,
        extractedConcepts: extractedData.concepts.length,
        extractedTrends: extractedData.trends.length
      };
    } catch (error) {
      logger.error('ResearchLearningAgent: Fehler bei der Aktualisierung der Wissensbasis', {
        error: error.message,
        stack: error.stack
      });
      
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * Erstellt einen Prompt für die Extraktion von Konzepten und Domänen
   * @private
   * @param {Array<Object>} publications - Liste von Publikationen
   * @returns {string} Extraktionsprompt
   */
  _buildConceptExtractionPrompt(publications) {
    // Formatiere Publikationen
    const formattedPublications = publications.map((pub, index) => {
      return `
PUBLIKATION ${index + 1}:
TITEL: ${pub.title}
AUTOREN: ${Array.isArray(pub.authors) ? pub.authors.join(', ') : pub.authors}
ABSTRACT: ${pub.abstract || 'Kein Abstract verfügbar'}
KEYWORDS: ${Array.isArray(pub.keywords) ? pub.keywords.join(', ') : (pub.keywords || 'Keine Keywords verfügbar')}
`;
    }).join('\n');
    
    return `
Als wissenschaftlicher Experte analysieren Sie bitte die folgenden Publikationen und extrahieren Sie wichtige Forschungsdomänen, Konzepte und potenzielle Trends.

${formattedPublications}

Bitte extrahieren Sie folgende Informationen:
1. Forschungsdomänen: Identifizieren Sie die Hauptforschungsgebiete, denen diese Publikationen zugeordnet werden können.
2. Konzepte: Extrahieren Sie wichtige wissenschaftliche Konzepte, Methoden oder Theorien, die in diesen Publikationen diskutiert werden.
3. Potenzielle Trends: Identifizieren Sie aufkommende Forschungstrends oder -richtungen basierend auf diesen Publikationen.

Formatieren Sie Ihre Antwort wie folgt:
DOMÄNEN:
- [Name der Domäne]: [Kurze Beschreibung]

KONZEPTE:
- [Name des Konzepts]: [Kurze Beschreibung] (Zugehörige Domänen: [Liste der zugehörigen Domänen])

TRENDS:
- [Name des Trends]: [Kurze Beschreibung] (Zugehörige Domänen: [Liste der zugehörigen Domänen])
`;
  }
  
  /**
   * Parst die Antwort der Konzeptextraktion
   * @private
   * @param {string} response - Antwort des KI-Modells
   * @returns {Object} Extrahierte Daten
   */
  _parseExtractionResponse(response) {
    const result = {
      domains: [],
      concepts: [],
      trends: []
    };
    
    try {
      // Extrahiere Domänen
      const domainsMatch = response.match(/DOMÄNEN:\s*([\s\S]*?)(?=KONZEPTE:|$)/);
      if (domainsMatch && domainsMatch[1]) {
        const domainsText = domainsMatch[1].trim();
        const domainEntries = domainsText.split(/\n-\s*/).filter(entry => entry.trim());
        
        domainEntries.forEach(entry => {
          const match = entry.match(/([^:]+):\s*(.*)/);
          if (match) {
            result.domains.push({
              name: match[1].trim(),
              description: match[2].trim()
            });
          }
        });
      }
      
      // Extrahiere Konzepte
      const conceptsMatch = response.match(/KONZEPTE:\s*([\s\S]*?)(?=TRENDS:|$)/);
      if (conceptsMatch && conceptsMatch[1]) {
        const conceptsText = conceptsMatch[1].trim();
        const conceptEntries = conceptsText.split(/\n-\s*/).filter(entry => entry.trim());
        
        conceptEntries.forEach(entry => {
          const match = entry.match(/([^:]+):\s*(.*?)(?:\(Zugehörige Domänen:\s*(.*?)\))?$/);
          if (match) {
            const domains = match[3] ? match[3].split(',').map(d => d.trim()) : [];
            
            result.concepts.push({
              name: match[1].trim(),
              description: match[2].trim(),
              domains
            });
          }
        });
      }
      
      // Extrahiere Trends
      const trendsMatch = response.match(/TRENDS:\s*([\s\S]*?)(?=$)/);
      if (trendsMatch && trendsMatch[1]) {
        const trendsText = trendsMatch[1].trim();
        const trendEntries = trendsText.split(/\n-\s*/).filter(entry => entry.trim());
        
        trendEntries.forEach(entry => {
          const match = entry.match(/([^:]+):\s*(.*?)(?:\(Zugehörige Domänen:\s*(.*?)\))?$/);
          if (match) {
            const domains = match[3] ? match[3].split(',').map(d => d.trim()) : [];
            
            result.trends.push({
              name: match[1].trim(),
              description: match[2].trim(),
              domains
            });
          }
        });
      }
    } catch (error) {
      logger.error('ResearchLearningAgent: Fehler beim Parsen der Extraktionsantwort', {
        error: error.message,
        stack: error.stack
      });
    }
    
    return result;
  }
  
  /**
   * Aktualisiert die Wissensbasis mit extrahierten Daten
   * @private
   * @param {Object} extractedData - Extrahierte Daten
   * @returns {Promise<void>}
   */
  async _updateKnowledgeBaseWithExtractedData(extractedData) {
    if (!this.databaseService) {
      return;
    }
    
    try {
      // Aktualisiere Domänen
      for (const domain of extractedData.domains) {
        // Prüfe, ob die Domäne bereits existiert
        const existingDomain = await this.databaseService.query(
          'SELECT * FROM research_domains WHERE name = ?',
          [domain.name]
        );
        
        if (existingDomain && existingDomain.length > 0) {
          // Aktualisiere bestehende Domäne
          await this.databaseService.execute(
            'UPDATE research_domains SET description = ?, updated_at = NOW() WHERE id = ?',
            [domain.description, existingDomain[0].id]
          );
          
          // Aktualisiere Wissensbasis
          this.knowledgeBase.domains.set(existingDomain[0].id, {
            name: domain.name,
            description: domain.description,
            publicationCount: existingDomain[0].publication_count,
            relatedDomains: new Set(existingDomain[0].related_domains || [])
          });
        } else {
          // Erstelle neue Domäne
          const result = await this.databaseService.execute(
            'INSERT INTO research_domains (name, description, publication_count, created_at, updated_at) VALUES (?, ?, 1, NOW(), NOW())',
            [domain.name, domain.description]
          );
          
          const domainId = result.insertId;
          
          // Aktualisiere Wissensbasis
          this.knowledgeBase.domains.set(domainId, {
            name: domain.name,
            description: domain.description,
            publicationCount: 1,
            relatedDomains: new Set()
          });
        }
      }
      
      // Aktualisiere Konzepte (ähnliche Implementierung wie für Domänen)
      // ...
      
      // Aktualisiere Trends (ähnliche Implementierung wie für Domänen)
      // ...
      
      logger.info('ResearchLearningAgent: Wissensbasis erfolgreich aktualisiert');
    } catch (error) {
      logger.error('ResearchLearningAgent: Fehler bei der Aktualisierung der Wissensbasis mit extrahierten Daten', {
        error: error.message,
        stack: error.stack
      });
    }
  }
  
  /**
   * Beantwortet eine Forschungsfrage basierend auf der Wissensbasis
   * @param {string} question - Forschungsfrage
   * @param {Object} options - Optionen für die Beantwortung
   * @returns {Promise<Object>} Antwort auf die Frage
   */
  async answerResearchQuestion(question, options = {}) {
    try {
      logger.info('ResearchLearningAgent: Beantworte Forschungsfrage', { question });
      
      // Finde relevante Publikationen
      const relevantPublications = await this._findRelevantPublications(question);
      
      // Erstelle Kontext für die Beantwortung
      const context = this._buildQuestionContext(question, relevantPublications);
      
      // Erstelle Prompt für die Beantwortung
      const prompt = this._buildAnswerPrompt(question, context);
      
      // Generiere Antwort
      const answer = await this.generateText(prompt, {
        temperature: options.temperature || 0.3,
        maxTokens: options.maxTokens || 1500
      });
      
      // Extrahiere Quellen
      const sources = this._extractSourcesFromAnswer(answer, relevantPublications);
      
      return {
        success: true,
        question,
        answer: this._formatAnswer(answer),
        sources,
        relevantPublicationsCount: relevantPublications.length
      };
    } catch (error) {
      logger.error('ResearchLearningAgent: Fehler bei der Beantwortung der Forschungsfrage', {
        error: error.message,
        stack: error.stack,
        question
      });
      
      return {
        success: false,
        question,
        error: error.message
      };
    }
  }
  
  /**
   * Findet relevante Publikationen für eine Frage
   * @private
   * @param {string} question - Forschungsfrage
   * @returns {Promise<Array<Object>>} Relevante Publikationen
   */
  async _findRelevantPublications(question) {
    if (!this.vectorDatabase || !this.textEmbeddingService) {
      // Fallback: Suche in der Datenbank nach Schlüsselwörtern
      return this._findPublicationsByKeywords(question);
    }
    
    try {
      // Erstelle Embedding für die Frage
      const questionEmbedding = await this.textEmbeddingService.createEmbedding(question);
      
      // Suche nach ähnlichen Publikationen in der Vektordatenbank
      const searchResults = await this.vectorDatabase.search({
        embedding: questionEmbedding,
        collection: 'publications',
        limit: this.config.maxPublicationsPerQuery,
        minScore: this.config.minRelevanceScore
      });
      
      // Lade vollständige Publikationsdaten
      const publications = await Promise.all(searchResults.map(async (result) => {
        const publication = await this.databaseService.query(
          'SELECT * FROM publications WHERE id = ?',
          [result.id]
        );
        
        return publication && publication.length > 0 ? {
          ...publication[0],
          relevanceScore: result.score
        } : null;
      }));
      
      return publications.filter(Boolean);
    } catch (error) {
      logger.error('ResearchLearningAgent: Fehler bei der Suche nach relevanten Publikationen', {
        error: error.message,
        stack: error.stack,
        question
      });
      
      // Fallback: Suche in der Datenbank nach Schlüsselwörtern
      return this._findPublicationsByKeywords(question);
    }
  }
  
  /**
   * Findet Publikationen anhand von Schlüsselwörtern
   * @private
   * @param {string} query - Suchanfrage
   * @returns {Promise<Array<Object>>} Gefundene Publikationen
   */
  async _findPublicationsByKeywords(query) {
    if (!this.databaseService) {
      return [];
    }
    
    try {
      // Extrahiere Schlüsselwörter aus der Anfrage
      const keywords = query.toLowerCase()
        .replace(/[^\w\s]/g, '')
        .split(/\s+/)
        .filter(word => word.length > 3);
      
      if (keywords.length === 0) {
        return [];
      }
      
      // Erstelle SQL-Abfrage mit LIKE-Operatoren
      const whereClauses = keywords.map(() => '(title LIKE ? OR abstract LIKE ?)');
      const whereClause = whereClauses.join(' OR ');
      
      const params = [];
      keywords.forEach(keyword => {
        params.push(`%${keyword}%`);
        params.push(`%${keyword}%`);
      });
      
      // Führe Abfrage aus
      const publications = await this.databaseService.query(
        `SELECT * FROM publications WHERE ${whereClause} ORDER BY published_at DESC LIMIT ?`,
        [...params, this.config.maxPublicationsPerQuery]
      );
      
      return publications || [];
    } catch (error) {
      logger.error('ResearchLearningAgent: Fehler bei der Suche nach Publikationen anhand von Schlüsselwörtern', {
        error: error.message,
        stack: error.stack,
        query
      });
      
      return [];
    }
  }
  
  /**
   * Erstellt den Kontext für die Beantwortung einer Frage
   * @private
   * @param {string} question - Forschungsfrage
   * @param {Array<Object>} publications - Relevante Publikationen
   * @returns {string} Kontext für die Beantwortung
   */
  _buildQuestionContext(question, publications) {
    // Formatiere Publikationen
    const formattedPublications = publications.map((pub, index) => {
      return `
QUELLE [${index + 1}]:
TITEL: ${pub.title}
AUTOREN: ${Array.isArray(pub.authors) ? pub.authors.join(', ') : pub.authors}
JAHR: ${pub.year || 'Unbekannt'}
DOI: ${pub.doi || 'Nicht verfügbar'}
ABSTRACT: ${pub.abstract || 'Kein Abstract verfügbar'}
`;
    }).join('\n');
    
    return formattedPublications;
  }
  
  /**
   * Erstellt einen Prompt für die Beantwortung einer Frage
   * @private
   * @param {string} question - Forschungsfrage
   * @param {string} context - Kontext für die Beantwortung
   * @returns {string} Prompt für die Beantwortung
   */
  _buildAnswerPrompt(question, context) {
    return `
Als wissenschaftlicher Experte beantworten Sie bitte die folgende Forschungsfrage basierend auf den bereitgestellten Quellen.
Beziehen Sie sich in Ihrer Antwort auf die Quellen und geben Sie an, welche Informationen aus welcher Quelle stammen.

FRAGE: ${question}

QUELLEN:
${context}

Bitte geben Sie eine fundierte, wissenschaftlich korrekte Antwort auf die Frage. Berücksichtigen Sie dabei:
1. Verschiedene Perspektiven und Ansätze aus den Quellen
2. Übereinstimmungen und Widersprüche zwischen den Quellen
3. Aktuelle Forschungsergebnisse und -trends
4. Einschränkungen und offene Fragen

Formatieren Sie Ihre Antwort wie folgt:
ANTWORT: [Ihre ausführliche Antwort]

QUELLEN: [Liste der verwendeten Quellen im Format [1], [2], etc.]
`;
  }
  
  /**
   * Formatiert die Antwort
   * @private
   * @param {string} answer - Antwort des KI-Modells
   * @returns {string} Formatierte Antwort
   */
  _formatAnswer(answer) {
    // Extrahiere den Antwortteil
    const answerMatch = answer.match(/ANTWORT:\s*([\s\S]*?)(?=QUELLEN:|$)/);
    if (answerMatch && answerMatch[1]) {
      return answerMatch[1].trim();
    }
    
    return answer;
  }
  
  /**
   * Extrahiert Quellen aus der Antwort
   * @private
   * @param {string} answer - Antwort des KI-Modells
   * @param {Array<Object>} publications - Relevante Publikationen
   * @returns {Array<Object>} Verwendete Quellen
   */
  _extractSourcesFromAnswer(answer, publications) {
    const sources = [];
    const sourceRegex = /\[(\d+)\]/g;
    let match;
    
    // Finde alle Quellenverweise in der Antwort
    const sourceReferences = new Set();
    while ((match = sourceRegex.exec(answer)) !== null) {
      const sourceIndex = parseInt(match[1]) - 1;
      if (sourceIndex >= 0 && sourceIndex < publications.length) {
        sourceReferences.add(sourceIndex);
      }
    }
    
    // Erstelle Quellenliste
    sourceReferences.forEach(index => {
      const publication = publications[index];
      sources.push({
        title: publication.title,
        authors: publication.authors,
        year: publication.year,
        doi: publication.doi,
        url: publication.url
      });
    });
    
    return sources;
  }
  
  /**
   * Identifiziert Forschungstrends basierend auf der Wissensbasis
   * @param {Object} options - Optionen für die Trendanalyse
   * @returns {Promise<Object>} Identifizierte Trends
   */
  async identifyResearchTrends(options = {}) {
    try {
      logger.info('ResearchLearningAgent: Identifiziere Forschungstrends');
      
      // Lade aktuelle Publikationen
      const recentPublications = await this._getRecentPublications(options.limit || 100);
      
      if (recentPublications.length === 0) {
        return {
          success: false,
          error: 'Keine aktuellen Publikationen gefunden'
        };
      }
      
      // Erstelle Prompt für die Trendanalyse
      const prompt = this._buildTrendAnalysisPrompt(recentPublications);
      
      // Generiere Trendanalyse
      const analysisResponse = await this.generateText(prompt, {
        temperature: 0.3,
        maxTokens: 2000
      });
      
      // Parsen der strukturierten Antwort
      const trends = this._parseTrendAnalysisResponse(analysisResponse);
      
      return {
        success: true,
        trends,
        publicationsAnalyzed: recentPublications.length,
        rawAnalysis: analysisResponse
      };
    } catch (error) {
      logger.error('ResearchLearningAgent: Fehler bei der Identifizierung von Forschungstrends', {
        error: error.message,
        stack: error.stack
      });
      
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * Ruft aktuelle Publikationen ab
   * @private
   * @param {number} limit - Maximale Anzahl von Publikationen
   * @returns {Promise<Array<Object>>} Aktuelle Publikationen
   */
  async _getRecentPublications(limit) {
    if (!this.databaseService) {
      return [];
    }
    
    try {
      const publications = await this.databaseService.query(
        'SELECT * FROM publications ORDER BY published_at DESC LIMIT ?',
        [limit]
      );
      
      return publications || [];
    } catch (error) {
      logger.error('ResearchLearningAgent: Fehler beim Abrufen aktueller Publikationen', {
        error: error.message,
        stack: error.stack
      });
      
      return [];
    }
  }
  
  /**
   * Erstellt einen Prompt für die Trendanalyse
   * @private
   * @param {Array<Object>} publications - Zu analysierende Publikationen
   * @returns {string} Prompt für die Trendanalyse
   */
  _buildTrendAnalysisPrompt(publications) {
    // Formatiere Publikationen
    const formattedPublications = publications.slice(0, 20).map((pub, index) => {
      return `
PUBLIKATION ${index + 1}:
TITEL: ${pub.title}
AUTOREN: ${Array.isArray(pub.authors) ? pub.authors.join(', ') : pub.authors}
JAHR: ${pub.year || 'Unbekannt'}
ABSTRACT: ${pub.abstract || 'Kein Abstract verfügbar'}
KEYWORDS: ${Array.isArray(pub.keywords) ? pub.keywords.join(', ') : (pub.keywords || 'Keine Keywords verfügbar')}
`;
    }).join('\n');
    
    return `
Als wissenschaftlicher Trendanalyst identifizieren Sie bitte aufkommende Forschungstrends basierend auf den folgenden aktuellen Publikationen.

${formattedPublications}

Bitte analysieren Sie diese Publikationen und identifizieren Sie:
1. Aufkommende Forschungstrends: Welche neuen Forschungsrichtungen oder -themen zeichnen sich ab?
2. Methodische Trends: Welche neuen Methoden oder Techniken werden zunehmend eingesetzt?
3. Interdisziplinäre Verbindungen: Welche Verbindungen zwischen verschiedenen Forschungsgebieten werden sichtbar?
4. Potenzielle Zukunftstrends: Welche Forschungsrichtungen könnten in naher Zukunft an Bedeutung gewinnen?

Formatieren Sie Ihre Antwort wie folgt:
AUFKOMMENDE TRENDS:
- [Name des Trends]: [Beschreibung] (Relevanz: Hoch/Mittel/Niedrig)

METHODISCHE TRENDS:
- [Name des Trends]: [Beschreibung] (Relevanz: Hoch/Mittel/Niedrig)

INTERDISZIPLINÄRE VERBINDUNGEN:
- [Verbindung]: [Beschreibung] (Relevanz: Hoch/Mittel/Niedrig)

ZUKUNFTSTRENDS:
- [Name des Trends]: [Beschreibung] (Relevanz: Hoch/Mittel/Niedrig)

ZUSAMMENFASSUNG:
[Kurze Zusammenfassung der wichtigsten Erkenntnisse]
`;
  }
  
  /**
   * Parst die Antwort der Trendanalyse
   * @private
   * @param {string} response - Antwort des KI-Modells
   * @returns {Object} Analysierte Trends
   */
  _parseTrendAnalysisResponse(response) {
    const result = {
      emergingTrends: [],
      methodologicalTrends: [],
      interdisciplinaryConnections: [],
      futureTrends: [],
      summary: ''
    };
    
    try {
      // Extrahiere aufkommende Trends
      const emergingMatch = response.match(/AUFKOMMENDE TRENDS:\s*([\s\S]*?)(?=METHODISCHE TRENDS:|$)/);
      if (emergingMatch && emergingMatch[1]) {
        const trendsText = emergingMatch[1].trim();
        const trendEntries = trendsText.split(/\n-\s*/).filter(entry => entry.trim());
        
        trendEntries.forEach(entry => {
          const match = entry.match(/([^:]+):\s*(.*?)(?:\(Relevanz:\s*(Hoch|Mittel|Niedrig)\))?$/);
          if (match) {
            result.emergingTrends.push({
              name: match[1].trim(),
              description: match[2].trim(),
              relevance: match[3] ? match[3].toLowerCase() : 'mittel'
            });
          }
        });
      }
      
      // Extrahiere methodische Trends
      const methodologicalMatch = response.match(/METHODISCHE TRENDS:\s*([\s\S]*?)(?=INTERDISZIPLINÄRE VERBINDUNGEN:|$)/);
      if (methodologicalMatch && methodologicalMatch[1]) {
        const trendsText = methodologicalMatch[1].trim();
        const trendEntries = trendsText.split(/\n-\s*/).filter(entry => entry.trim());
        
        trendEntries.forEach(entry => {
          const match = entry.match(/([^:]+):\s*(.*?)(?:\(Relevanz:\s*(Hoch|Mittel|Niedrig)\))?$/);
          if (match) {
            result.methodologicalTrends.push({
              name: match[1].trim(),
              description: match[2].trim(),
              relevance: match[3] ? match[3].toLowerCase() : 'mittel'
            });
          }
        });
      }
      
      // Extrahiere interdisziplinäre Verbindungen
      const interdisciplinaryMatch = response.match(/INTERDISZIPLINÄRE VERBINDUNGEN:\s*([\s\S]*?)(?=ZUKUNFTSTRENDS:|$)/);
      if (interdisciplinaryMatch && interdisciplinaryMatch[1]) {
        const connectionsText = interdisciplinaryMatch[1].trim();
        const connectionEntries = connectionsText.split(/\n-\s*/).filter(entry => entry.trim());
        
        connectionEntries.forEach(entry => {
          const match = entry.match(/([^:]+):\s*(.*?)(?:\(Relevanz:\s*(Hoch|Mittel|Niedrig)\))?$/);
          if (match) {
            result.interdisciplinaryConnections.push({
              name: match[1].trim(),
              description: match[2].trim(),
              relevance: match[3] ? match[3].toLowerCase() : 'mittel'
            });
          }
        });
      }
      
      // Extrahiere Zukunftstrends
      const futureMatch = response.match(/ZUKUNFTSTRENDS:\s*([\s\S]*?)(?=ZUSAMMENFASSUNG:|$)/);
      if (futureMatch && futureMatch[1]) {
        const trendsText = futureMatch[1].trim();
        const trendEntries = trendsText.split(/\n-\s*/).filter(entry => entry.trim());
        
        trendEntries.forEach(entry => {
          const match = entry.match(/([^:]+):\s*(.*?)(?:\(Relevanz:\s*(Hoch|Mittel|Niedrig)\))?$/);
          if (match) {
            result.futureTrends.push({
              name: match[1].trim(),
              description: match[2].trim(),
              relevance: match[3] ? match[3].toLowerCase() : 'mittel'
            });
          }
        });
      }
      
      // Extrahiere Zusammenfassung
      const summaryMatch = response.match(/ZUSAMMENFASSUNG:\s*([\s\S]+)$/);
      if (summaryMatch && summaryMatch[1]) {
        result.summary = summaryMatch[1].trim();
      }
    } catch (error) {
      logger.error('ResearchLearningAgent: Fehler beim Parsen der Trendanalyse', {
        error: error.message,
        stack: error.stack
      });
    }
    
    return result;
  }
  
  /**
   * Generiert Forschungsempfehlungen für einen Forscher
   * @param {Object} researcher - Forscher
   * @param {Object} options - Optionen für die Empfehlungen
   * @returns {Promise<Object>} Forschungsempfehlungen
   */
  async generateResearchRecommendations(researcher, options = {}) {
    try {
      logger.info('ResearchLearningAgent: Generiere Forschungsempfehlungen', {
        researcherId: researcher.id
      });
      
      // Lade Publikationen des Forschers
      const researcherPublications = await this._getResearcherPublications(researcher.id);
      
      // Lade aktuelle Trends
      const trendsAnalysis = await this.identifyResearchTrends({ limit: 50 });
      
      if (!trendsAnalysis.success) {
        throw new Error('Fehler bei der Trendanalyse: ' + trendsAnalysis.error);
      }
      
      // Erstelle Prompt für Empfehlungen
      const prompt = this._buildRecommendationsPrompt(researcher, researcherPublications, trendsAnalysis.trends);
      
      // Generiere Empfehlungen
      const recommendationsResponse = await this.generateText(prompt, {
        temperature: 0.4,
        maxTokens: 2000
      });
      
      // Parsen der strukturierten Antwort
      const recommendations = this._parseRecommendationsResponse(recommendationsResponse);
      
      return {
        success: true,
        researcher: {
          id: researcher.id,
          name: researcher.name
        },
        recommendations,
        publicationsAnalyzed: researcherPublications.length,
        rawRecommendations: recommendationsResponse
      };
    } catch (error) {
      logger.error('ResearchLearningAgent: Fehler bei der Generierung von Forschungsempfehlungen', {
        error: error.message,
        stack: error.stack,
        researcherId: researcher.id
      });
      
      return {
        success: false,
        error: error.message,
        researcher: {
          id: researcher.id,
          name: researcher.name
        }
      };
    }
  }
  
  /**
   * Ruft Publikationen eines Forschers ab
   * @private
   * @param {string} researcherId - ID des Forschers
   * @returns {Promise<Array<Object>>} Publikationen des Forschers
   */
  async _getResearcherPublications(researcherId) {
    if (!this.databaseService) {
      return [];
    }
    
    try {
      const publications = await this.databaseService.query(
        'SELECT p.* FROM publications p JOIN publication_authors pa ON p.id = pa.publication_id WHERE pa.researcher_id = ? ORDER BY p.published_at DESC',
        [researcherId]
      );
      
      return publications || [];
    } catch (error) {
      logger.error('ResearchLearningAgent: Fehler beim Abrufen der Publikationen eines Forschers', {
        error: error.message,
        stack: error.stack,
        researcherId
      });
      
      return [];
    }
  }
  
  /**
   * Erstellt einen Prompt für Forschungsempfehlungen
   * @private
   * @param {Object} researcher - Forscher
   * @param {Array<Object>} publications - Publikationen des Forschers
   * @param {Object} trends - Aktuelle Forschungstrends
   * @returns {string} Prompt für Forschungsempfehlungen
   */
  _buildRecommendationsPrompt(researcher, publications, trends) {
    // Formatiere Publikationen
    const formattedPublications = publications.slice(0, 10).map((pub, index) => {
      return `
PUBLIKATION ${index + 1}:
TITEL: ${pub.title}
JAHR: ${pub.year || 'Unbekannt'}
ABSTRACT: ${pub.abstract || 'Kein Abstract verfügbar'}
KEYWORDS: ${Array.isArray(pub.keywords) ? pub.keywords.join(', ') : (pub.keywords || 'Keine Keywords verfügbar')}
`;
    }).join('\n');
    
    // Formatiere Trends
    const formattedTrends = `
AUFKOMMENDE TRENDS:
${trends.emergingTrends.map(trend => `- ${trend.name}: ${trend.description}`).join('\n')}

METHODISCHE TRENDS:
${trends.methodologicalTrends.map(trend => `- ${trend.name}: ${trend.description}`).join('\n')}

INTERDISZIPLINÄRE VERBINDUNGEN:
${trends.interdisciplinaryConnections.map(connection => `- ${connection.name}: ${connection.description}`).join('\n')}

ZUKUNFTSTRENDS:
${trends.futureTrends.map(trend => `- ${trend.name}: ${trend.description}`).join('\n')}
`;
    
    return `
Als wissenschaftlicher Berater generieren Sie bitte Forschungsempfehlungen für einen Forscher basierend auf dessen bisherigen Publikationen und aktuellen Forschungstrends.

FORSCHER:
Name: ${researcher.name}
Fachgebiet: ${researcher.field || 'Nicht angegeben'}
Interessen: ${researcher.interests || 'Nicht angegeben'}

BISHERIGE PUBLIKATIONEN:
${formattedPublications}

AKTUELLE FORSCHUNGSTRENDS:
${formattedTrends}

Bitte generieren Sie fundierte Forschungsempfehlungen für diesen Forscher. Berücksichtigen Sie dabei:
1. Die bisherigen Forschungsinteressen und Expertise des Forschers
2. Aktuelle und aufkommende Trends im Forschungsfeld
3. Potenzielle interdisziplinäre Verbindungen
4. Methodische Innovationen, die der Forscher anwenden könnte
5. Konkrete Forschungsfragen oder -projekte, die der Forscher verfolgen könnte

Formatieren Sie Ihre Antwort wie folgt:
FORSCHUNGSRICHTUNGEN:
- [Forschungsrichtung 1]: [Beschreibung und Begründung]
- [Forschungsrichtung 2]: [Beschreibung und Begründung]
...

METHODISCHE EMPFEHLUNGEN:
- [Methode 1]: [Beschreibung und Begründung]
- [Methode 2]: [Beschreibung und Begründung]
...

KONKRETE FORSCHUNGSFRAGEN:
- [Forschungsfrage 1]: [Beschreibung und Begründung]
- [Forschungsfrage 2]: [Beschreibung und Begründung]
...

KOLLABORATIONSMÖGLICHKEITEN:
- [Kollaborationsmöglichkeit 1]: [Beschreibung und Begründung]
- [Kollaborationsmöglichkeit 2]: [Beschreibung und Begründung]
...

ZUSAMMENFASSUNG:
[Kurze Zusammenfassung der wichtigsten Empfehlungen]
`;
  }
  
  /**
   * Parst die Antwort der Forschungsempfehlungen
   * @private
   * @param {string} response - Antwort des KI-Modells
   * @returns {Object} Forschungsempfehlungen
   */
  _parseRecommendationsResponse(response) {
    const result = {
      researchDirections: [],
      methodologicalRecommendations: [],
      researchQuestions: [],
      collaborationOpportunities: [],
      summary: ''
    };
    
    try {
      // Extrahiere Forschungsrichtungen
      const directionsMatch = response.match(/FORSCHUNGSRICHTUNGEN:\s*([\s\S]*?)(?=METHODISCHE EMPFEHLUNGEN:|$)/);
      if (directionsMatch && directionsMatch[1]) {
        const directionsText = directionsMatch[1].trim();
        const directionEntries = directionsText.split(/\n-\s*/).filter(entry => entry.trim());
        
        directionEntries.forEach(entry => {
          const match = entry.match(/([^:]+):\s*(.*)/);
          if (match) {
            result.researchDirections.push({
              name: match[1].trim(),
              description: match[2].trim()
            });
          }
        });
      }
      
      // Extrahiere methodische Empfehlungen
      const methodsMatch = response.match(/METHODISCHE EMPFEHLUNGEN:\s*([\s\S]*?)(?=KONKRETE FORSCHUNGSFRAGEN:|$)/);
      if (methodsMatch && methodsMatch[1]) {
        const methodsText = methodsMatch[1].trim();
        const methodEntries = methodsText.split(/\n-\s*/).filter(entry => entry.trim());
        
        methodEntries.forEach(entry => {
          const match = entry.match(/([^:]+):\s*(.*)/);
          if (match) {
            result.methodologicalRecommendations.push({
              name: match[1].trim(),
              description: match[2].trim()
            });
          }
        });
      }
      
      // Extrahiere Forschungsfragen
      const questionsMatch = response.match(/KONKRETE FORSCHUNGSFRAGEN:\s*([\s\S]*?)(?=KOLLABORATIONSMÖGLICHKEITEN:|$)/);
      if (questionsMatch && questionsMatch[1]) {
        const questionsText = questionsMatch[1].trim();
        const questionEntries = questionsText.split(/\n-\s*/).filter(entry => entry.trim());
        
        questionEntries.forEach(entry => {
          const match = entry.match(/([^:]+):\s*(.*)/);
          if (match) {
            result.researchQuestions.push({
              question: match[1].trim(),
              description: match[2].trim()
            });
          }
        });
      }
      
      // Extrahiere Kollaborationsmöglichkeiten
      const collaborationsMatch = response.match(/KOLLABORATIONSMÖGLICHKEITEN:\s*([\s\S]*?)(?=ZUSAMMENFASSUNG:|$)/);
      if (collaborationsMatch && collaborationsMatch[1]) {
        const collaborationsText = collaborationsMatch[1].trim();
        const collaborationEntries = collaborationsText.split(/\n-\s*/).filter(entry => entry.trim());
        
        collaborationEntries.forEach(entry => {
          const match = entry.match(/([^:]+):\s*(.*)/);
          if (match) {
            result.collaborationOpportunities.push({
              name: match[1].trim(),
              description: match[2].trim()
            });
          }
        });
      }
      
      // Extrahiere Zusammenfassung
      const summaryMatch = response.match(/ZUSAMMENFASSUNG:\s*([\s\S]+)$/);
      if (summaryMatch && summaryMatch[1]) {
        result.summary = summaryMatch[1].trim();
      }
    } catch (error) {
      logger.error('ResearchLearningAgent: Fehler beim Parsen der Forschungsempfehlungen', {
        error: error.message,
        stack: error.stack
      });
    }
    
    return result;
  }
}

export default ResearchLearningAgent;/**
 * @fileoverview KI-Agent, der aus wissenschaftlichen Publikationen lernt
 * 
 * Dieser Agent kann aus den wissenschaftlichen Publikationen auf der Plattform lernen,
 * Zusammenhänge zwischen Forschungsgebieten erkennen, Trends identifizieren und
 * Forschern bei ihrer Arbeit unterstützen.
 */

import axios from 'axios';
import { logger } from '../../utils/logger.js';
import { BaseAgent } from './index.js';

/**
 * Agent für das Lernen aus wissenschaftlichen Publikationen
 */
export class ResearchLearningAgent extends BaseAgent {
  /**
   * Erstellt eine neue Instanz des ResearchLearningAgent
   * @param {Object} options - Konfigurationsoptionen
   * @param {Object} options.databaseService - Datenbankdienst für den Zugriff auf Publikationen
   * @param {Object} options.vectorDatabase - Vektordatenbank für semantische Suche
   * @param {Object} options.textEmbeddingService - Dienst für Text-Embeddings
   * @param {Object} options.config - Weitere Konfigurationsoptionen
   */
  constructor(options = {}) {
    super('ResearchLearningAgent');
    
    const {
      databaseService,
      vectorDatabase,
      textEmbeddingService,
      config = {}
    } = options;
    
    this.databaseService = databaseService;
    this.vectorDatabase = vectorDatabase;
    this.textEmbeddingService = textEmbeddingService;
    
    this.config = {
      maxContextSize: 8000,
      minRelevanceScore: 0.7,
      maxPublicationsPerQuery: 10,
      ...config
    };
    
    this.knowledgeBase = {
      domains: new Map(),
      concepts: new Map(),
      trends: new Map(),
      lastUpdated: null
    };
  }
  
  /**
   * Initialisiert den Agenten und lädt die Wissensbasis
   * @returns {Promise<boolean>} Erfolgsstatus
   */
  async initialize() {
    try {
      logger.info('ResearchLearningAgent: Initialisiere');
      
      // Prüfen, ob die erforderlichen Dienste verfügbar sind
      if (!this.databaseService) {
        logger.warn('ResearchLearningAgent: DatabaseService nicht verfügbar, Funktionalität wird eingeschränkt sein');
      }
      
      if (!this.vectorDatabase) {
        logger.warn('ResearchLearningAgent: VectorDatabase nicht verfügbar, semantische Suche wird deaktiviert');
      }
      
      if (!this.textEmbeddingService) {
        logger.warn('ResearchLearningAgent: TextEmbeddingService nicht verfügbar, Embedding-Funktionalität wird deaktiviert');
      }
      
      // Lade initiale Wissensbasis
      await this._loadKnowledgeBase();
      
      logger.info('ResearchLearningAgent: Erfolgreich initialisiert');
      
      return true;
    } catch (error) {
      logger.error('ResearchLearningAgent: Fehler bei der Initialisierung', {
        error: error.message,
        stack: error.stack
      });
      
      return false;
    }
  }
  
  /**
   * Lädt die Wissensbasis aus der Datenbank
   * @private
   * @returns {Promise<void>}
   */
  async _loadKnowledgeBase() {
    if (!this.databaseService) {
      return;
    }
    
    try {
      logger.info('ResearchLearningAgent: Lade Wissensbasis');
      
      // Lade Domänen
      const domains = await this.databaseService.query(
        'SELECT * FROM research_domains ORDER BY publication_count DESC LIMIT 100'
      );
      
      if (domains && domains.length > 0) {
        domains.forEach(domain => {
          this.knowledgeBase.domains.set(domain.id, {
            name: domain.name,
            description: domain.description,
            publicationCount: domain.publication_count,
            relatedDomains: new Set(domain.related_domains || [])
          });
        });
      }
      
      // Lade Konzepte
      const concepts = await this.databaseService.query(
        'SELECT * FROM research_concepts ORDER BY importance DESC LIMIT 500'
      );
      
      if (concepts && concepts.length > 0) {
        concepts.forEach(concept => {
          this.knowledgeBase.concepts.set(concept.id, {
            name: concept.name,
            description: concept.description,
            domains: new Set(concept.domains || []),
            relatedConcepts: new Set(concept.related_concepts || [])
          });
        });
      }
      
      // Lade Trends
      const trends = await this.databaseService.query(
        'SELECT * FROM research_trends WHERE active = 1 ORDER BY momentum DESC LIMIT 50'
      );
      
      if (trends && trends.length > 0) {
        trends.forEach(trend => {
          this.knowledgeBase.trends.set(trend.id, {
            name: trend.name,
            description: trend.description,
            momentum: trend.momentum,
            startDate: trend.start_date,
            domains: new Set(trend.domains || [])
          });
        });
      }
      
      this.knowledgeBase.lastUpdated = new Date();
      
      logger.info('ResearchLearningAgent: Wissensbasis erfolgreich geladen', {
        domains: this.knowledgeBase.domains.size,
        concepts: this.knowledgeBase.concepts.size,
        trends: this.knowledgeBase.trends.size
      });
    } catch (error) {
      logger.error('ResearchLearningAgent: Fehler beim Laden der Wissensbasis', {
        error: error.message,
        stack: error.stack
      });
    }
  }
  
  /**
   * Aktualisiert die Wissensbasis mit neuen Publikationen
   * @param {Array<Object>} publications - Liste von Publikationen
   * @returns {Promise<Object>} Ergebnis der Aktualisierung
   */
  async updateKnowledgeBase(publications) {
    try {
      logger.info('ResearchLearningAgent: Aktualisiere Wissensbasis', {
        publicationCount: publications.length
      });
      
      // Extrahiere Konzepte und Domänen aus den Publikationen
      const extractionPrompt = this._buildConceptExtractionPrompt(publications);
      
      const extractionResponse = await this.generateText(extractionPrompt, {
        temperature: 0.2,
        maxTokens: 2000
      });
      
      // Parsen der strukturierten Antwort
      const extractedData = this._parseExtractionResponse(extractionResponse);
      
      // Aktualisiere die Wissensbasis
      await this._updateKnowledgeBaseWithExtractedData(extractedData);
      
      this.knowledgeBase.lastUpdated = new Date();
      
      return {
        success: true,
        extractedDomains: extractedData.domains.length,
        extractedConcepts: extractedData.concepts.length,
        extractedTrends: extractedData.trends.length
      };
    } catch (error) {
      logger.error('ResearchLearningAgent: Fehler bei der Aktualisierung der Wissensbasis', {
        error: error.message,
        stack: error.stack
      });
      
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * Erstellt einen Prompt für die Extraktion von Konzepten und Domänen
   * @private
   * @param {Array<Object>} publications - Liste von Publikationen
   * @returns {string} Extraktionsprompt
   */
  _buildConceptExtractionPrompt(publications) {
    // Formatiere Publikationen
    const formattedPublications = publications.map((pub, index) => {
      return `
PUBLIKATION ${index + 1}:
TITEL: ${pub.title}
AUTOREN: ${Array.isArray(pub.authors) ? pub.authors.join(', ') : pub.authors}
ABSTRACT: ${pub.abstract || 'Kein Abstract verfügbar'}
KEYWORDS: ${Array.isArray(pub.keywords) ? pub.keywords.join(', ') : (pub.keywords || 'Keine Keywords verfügbar')}
`;
    }).join('\n');
    
    return `
Als wissenschaftlicher Experte analysieren Sie bitte die folgenden Publikationen und extrahieren Sie wichtige Forschungsdomänen, Konzepte und potenzielle Trends.

${formattedPublications}

Bitte extrahieren Sie folgende Informationen:
1. Forschungsdomänen: Identifizieren Sie die Hauptforschungsgebiete, denen diese Publikationen zugeordnet werden können.
2. Konzepte: Extrahieren Sie wichtige wissenschaftliche Konzepte, Methoden oder Theorien, die in diesen Publikationen diskutiert werden.
3. Potenzielle Trends: Identifizieren Sie aufkommende Forschungstrends oder -richtungen basierend auf diesen Publikationen.

Formatieren Sie Ihre Antwort wie folgt:
DOMÄNEN:
- [Name der Domäne]: [Kurze Beschreibung]

KONZEPTE:
- [Name des Konzepts]: [Kurze Beschreibung] (Zugehörige Domänen: [Liste der zugehörigen Domänen])

TRENDS:
- [Name des Trends]: [Kurze Beschreibung] (Zugehörige Domänen: [Liste der zugehörigen Domänen])
`;
  }
  
  /**
   * Parst die Antwort der Konzeptextraktion
   * @private
   * @param {string} response - Antwort des KI-Modells
   * @returns {Object} Extrahierte Daten
   */
  _parseExtractionResponse(response) {
    const result = {
      domains: [],
      concepts: [],
      trends: []
    };
    
    try {
      // Extrahiere Domänen
      const domainsMatch = response.match(/DOMÄNEN:\s*([\s\S]*?)(?=KONZEPTE:|$)/);
      if (domainsMatch && domainsMatch[1]) {
        const domainsText = domainsMatch[1].trim();
        const domainEntries = domainsText.split(/\n-\s*/).filter(entry => entry.trim());
        
        domainEntries.forEach(entry => {
          const match = entry.match(/([^:]+):\s*(.*)/);
          if (match) {
            result.domains.push({
              name: match[1].trim(),
              description: match[2].trim()
            });
          }
        });
      }
      
      // Extrahiere Konzepte
      const conceptsMatch = response.match(/KONZEPTE:\s*([\s\S]*?)(?=TRENDS:|$)/);
      if (conceptsMatch && conceptsMatch[1]) {
        const conceptsText = conceptsMatch[1].trim();
        const conceptEntries = conceptsText.split(/\n-\s*/).filter(entry => entry.trim());
        
        conceptEntries.forEach(entry => {
          const match = entry.match(/([^:]+):\s*(.*?)(?:\(Zugehörige Domänen:\s*(.*?)\))?$/);
          if (match) {
            const domains = match[3] ? match[3].split(',').map(d => d.trim()) : [];
            
            result.concepts.push({
              name: match[1].trim(),
              description: match[2].trim(),
              domains
            });
          }
        });
      }
      
      // Extrahiere Trends
      const trendsMatch = response.match(/TRENDS:\s*([\s\S]*?)(?=$)/);
      if (trendsMatch && trendsMatch[1]) {
        const trendsText = trendsMatch[1].trim();
        const trendEntries = trendsText.split(/\n-\s*/).filter(entry => entry.trim());
        
        trendEntries.forEach(entry => {
          const match = entry.match(/([^:]+):\s*(.*?)(?:\(Zugehörige Domänen:\s*(.*?)\))?$/);
          if (match) {
            const domains = match[3] ? match[3].split(',').map(d => d.trim()) : [];
            
            result.trends.push({
              name: match[1].trim(),
              description: match[2].trim(),
              domains
            });
          }
        });
      }
    } catch (error) {
      logger.error('ResearchLearningAgent: Fehler beim Parsen der Extraktionsantwort', {
        error: error.message,
        stack: error.stack
      });
    }
    
    return result;
  }
  
  /**
   * Aktualisiert die Wissensbasis mit extrahierten Daten
   * @private
   * @param {Object} extractedData - Extrahierte Daten
   * @returns {Promise<void>}
   */
  async _updateKnowledgeBaseWithExtractedData(extractedData) {
    if (!this.databaseService) {
      return;
    }
    
    try {
      // Aktualisiere Domänen
      for (const domain of extractedData.domains) {
        // Prüfe, ob die Domäne bereits existiert
        const existingDomain = await this.databaseService.query(
          'SELECT * FROM research_domains WHERE name = ?',
          [domain.name]
        );
        
        if (existingDomain && existingDomain.length > 0) {
          // Aktualisiere bestehende Domäne
          await this.databaseService.execute(
            'UPDATE research_domains SET description = ?, updated_at = NOW() WHERE id = ?',
            [domain.description, existingDomain[0].id]
          );
          
          // Aktualisiere Wissensbasis
          this.knowledgeBase.domains.set(existingDomain[0].id, {
            name: domain.name,
            description: domain.description,
            publicationCount: existingDomain[0].publication_count,
            relatedDomains: new Set(existingDomain[0].related_domains || [])
          });
        } else {
          // Erstelle neue Domäne
          const result = await this.databaseService.execute(
            'INSERT INTO research_domains (name, description, publication_count, created_at, updated_at) VALUES (?, ?, 1, NOW(), NOW())',
            [domain.name, domain.description]
          );
          
          const domainId = result.insertId;
          
          // Aktualisiere Wissensbasis
          this.knowledgeBase.domains.set(domainId, {
            name: domain.name,
            description: domain.description,
            publicationCount: 1,
            relatedDomains: new Set()
          });
        }
      }
      
      // Aktualisiere Konzepte (ähnliche Implementierung wie für Domänen)
      // ...
      
      // Aktualisiere Trends (ähnliche Implementierung wie für Domänen)
      // ...
      
      logger.info('ResearchLearningAgent: Wissensbasis erfolgreich aktualisiert');
    } catch (error) {
      logger.error('ResearchLearningAgent: Fehler bei der Aktualisierung der Wissensbasis mit extrahierten Daten', {
        error: error.message,
        stack: error.stack
      });
    }
  }
  
  /**
   * Beantwortet eine Forschungsfrage basierend auf der Wissensbasis
   * @param {string} question - Forschungsfrage
   * @param {Object} options - Optionen für die Beantwortung
   * @returns {Promise<Object>} Antwort auf die Frage
   */
  async answerResearchQuestion(question, options = {}) {
    try {
      logger.info('ResearchLearningAgent: Beantworte Forschungsfrage', { question });
      
      // Finde relevante Publikationen
      const relevantPublications = await this._findRelevantPublications(question);
      
      // Erstelle Kontext für die Beantwortung
      const context = this._buildQuestionContext(question, relevantPublications);
      
      // Erstelle Prompt für die Beantwortung
      const prompt = this._buildAnswerPrompt(question, context);
      
      // Generiere Antwort
      const answer = await this.generateText(prompt, {
        temperature: options.temperature || 0.3,
        maxTokens: options.maxTokens || 1500
      });
      
      // Extrahiere Quellen
      const sources = this._extractSourcesFromAnswer(answer, relevantPublications);
      
      return {
        success: true,
        question,
        answer: this._formatAnswer(answer),
        sources,
        relevantPublicationsCount: relevantPublications.length
      };
    } catch (error) {
      logger.error('ResearchLearningAgent: Fehler bei der Beantwortung der Forschungsfrage', {
        error: error.message,
        stack: error.stack,
        question
      });
      
      return {
        success: false,
        question,
        error: error.message
      };
    }
  }
  
  /**
   * Findet relevante Publikationen für eine Frage
   * @private
   * @param {string} question - Forschungsfrage
   * @returns {Promise<Array<Object>>} Relevante Publikationen
   */
  async _findRelevantPublications(question) {
    if (!this.vectorDatabase || !this.textEmbeddingService) {
      // Fallback: Suche in der Datenbank nach Schlüsselwörtern
      return this._findPublicationsByKeywords(question);
    }
    
    try {
      // Erstelle Embedding für die Frage
      const questionEmbedding = await this.textEmbeddingService.createEmbedding(question);
      
      // Suche nach ähnlichen Publikationen in der Vektordatenbank
      const searchResults = await this.vectorDatabase.search({
        embedding: questionEmbedding,
        collection: 'publications',
        limit: this.config.maxPublicationsPerQuery,
        minScore: this.config.minRelevanceScore
      });
      
      // Lade vollständige Publikationsdaten
      const publications = await Promise.all(searchResults.map(async (result) => {
        const publication = await this.databaseService.query(
          'SELECT * FROM publications WHERE id = ?',
          [result.id]
        );
        
        return publication && publication.length > 0 ? {
          ...publication[0],
          relevanceScore: result.score
        } : null;
      }));
      
      return publications.filter(Boolean);
    } catch (error) {
      logger.error('ResearchLearningAgent: Fehler bei der Suche nach relevanten Publikationen', {
        error: error.message,
        stack: error.stack,
        question
      });
      
      // Fallback: Suche in der Datenbank nach Schlüsselwörtern
      return this._findPublicationsByKeywords(question);
    }
  }
  
  /**
   * Findet Publikationen anhand von Schlüsselwörtern
   * @private
   * @param {string} query - Suchanfrage
   * @returns {Promise<Array<Object>>} Gefundene Publikationen
   */
  async _findPublicationsByKeywords(query) {
    if (!this.databaseService) {
      return [];
    }
    
    try {
      // Extrahiere Schlüsselwörter aus der Anfrage
      const keywords = query.toLowerCase()
        .replace(/[^\w\s]/g, '')
        .split(/\s+/)
        .filter(word => word.length > 3);
      
      if (keywords.length === 0) {
        return [];
      }
      
      // Erstelle SQL-Abfrage mit LIKE-Operatoren
      const whereClauses = keywords.map(() => '(title LIKE ? OR abstract LIKE ?)');
      const whereClause = whereClauses.join(' OR ');
      
      const params = [];
      keywords.forEach(keyword => {
        params.push(`%${keyword}%`);
        params.push(`%${keyword}%`);
      });
      
      // Führe Abfrage aus
      const publications = await this.databaseService.query(
        `SELECT * FROM publications WHERE ${whereClause} ORDER BY published_at DESC LIMIT ?`,
        [...params, this.config.maxPublicationsPerQuery]
      );
      
      return publications || [];
    } catch (error) {
      logger.error('ResearchLearningAgent: Fehler bei der Suche nach Publikationen anhand von Schlüsselwörtern', {
        error: error.message,
        stack: error.stack,
        query
      });
      
      return [];
    }
  }
  
  /**
   * Erstellt den Kontext für die Beantwortung einer Frage
   * @private
   * @param {string} question - Forschungsfrage
   * @param {Array<Object>} publications - Relevante Publikationen
   * @returns {string} Kontext für die Beantwortung
   */
  _buildQuestionContext(question, publications) {
    // Formatiere Publikationen
    const formattedPublications = publications.map((pub, index) => {
      return `
QUELLE [${index + 1}]:
TITEL: ${pub.title}
AUTOREN: ${Array.isArray(pub.authors) ? pub.authors.join(', ') : pub.authors}
JAHR: ${pub.year || 'Unbekannt'}
DOI: ${pub.doi || 'Nicht verfügbar'}
ABSTRACT: ${pub.abstract || 'Kein Abstract verfügbar'}
`;
    }).join('\n');
    
    return formattedPublications;
  }
  
  /**
   * Erstellt einen Prompt für die Beantwortung einer Frage
   * @private
   * @param {string} question - Forschungsfrage
   * @param {string} context - Kontext für die Beantwortung
   * @returns {string} Prompt für die Beantwortung
   */
  _buildAnswerPrompt(question, context) {
    return `
Als wissenschaftlicher Experte beantworten Sie bitte die folgende Forschungsfrage basierend auf den bereitgestellten Quellen.
Beziehen Sie sich in Ihrer Antwort auf die Quellen und geben Sie an, welche Informationen aus welcher Quelle stammen.

FRAGE: ${question}

QUELLEN:
${context}

Bitte geben Sie eine fundierte, wissenschaftlich korrekte Antwort auf die Frage. Berücksichtigen Sie dabei:
1. Verschiedene Perspektiven und Ansätze aus den Quellen
2. Übereinstimmungen und Widersprüche zwischen den Quellen
3. Aktuelle Forschungsergebnisse und -trends
4. Einschränkungen und offene Fragen

Formatieren Sie Ihre Antwort wie folgt:
ANTWORT: [Ihre ausführliche Antwort]

QUELLEN: [Liste der verwendeten Quellen im Format [1], [2], etc.]
`;
  }
  
  /**
   * Formatiert die Antwort
   * @private
   * @param {string} answer - Antwort des KI-Modells
   * @returns {string} Formatierte Antwort
   */
  _formatAnswer(answer) {
    // Extrahiere den Antwortteil
    const answerMatch = answer.match(/ANTWORT:\s*([\s\S]*?)(?=QUELLEN:|$)/);
    if (answerMatch && answerMatch[1]) {
      return answerMatch[1].trim();
    }
    
    return answer;
  }
  
  /**
   * Extrahiert Quellen aus der Antwort
   * @private
   * @param {string} answer - Antwort des KI-Modells
   * @param {Array<Object>} publications - Relevante Publikationen
   * @returns {Array<Object>} Verwendete Quellen
   */
  _extractSourcesFromAnswer(answer, publications) {
    const sources = [];
    const sourceRegex = /\[(\d+)\]/g;
    let match;
    
    // Finde alle Quellenverweise in der Antwort
    const sourceReferences = new Set();
    while ((match = sourceRegex.exec(answer)) !== null) {
      const sourceIndex = parseInt(match[1]) - 1;
      if (sourceIndex >= 0 && sourceIndex < publications.length) {
        sourceReferences.add(sourceIndex);
      }
    }
    
    // Erstelle Quellenliste
    sourceReferences.forEach(index => {
      const publication = publications[index];
      sources.push({
        title: publication.title,
        authors: publication.authors,
        year: publication.year,
        doi: publication.doi,
        url: publication.url
      });
    });
    
    return sources;
  }
  
  /**
   * Identifiziert Forschungstrends basierend auf der Wissensbasis
   * @param {Object} options - Optionen für die Trendanalyse
   * @returns {Promise<Object>} Identifizierte Trends
   */
  async identifyResearchTrends(options = {}) {
    try {
      logger.info('ResearchLearningAgent: Identifiziere Forschungstrends');
      
      // Lade aktuelle Publikationen
      const recentPublications = await this._getRecentPublications(options.limit || 100);
      
      if (recentPublications.length === 0) {
        return {
          success: false,
          error: 'Keine aktuellen Publikationen gefunden'
        };
      }
      
      // Erstelle Prompt für die Trendanalyse
      const prompt = this._buildTrendAnalysisPrompt(recentPublications);
      
      // Generiere Trendanalyse
      const analysisResponse = await this.generateText(prompt, {
        temperature: 0.3,
        maxTokens: 2000
      });
      
      // Parsen der strukturierten Antwort
      const trends = this._parseTrendAnalysisResponse(analysisResponse);
      
      return {
        success: true,
        trends,
        publicationsAnalyzed: recentPublications.length,
        rawAnalysis: analysisResponse
      };
    } catch (error) {
      logger.error('ResearchLearningAgent: Fehler bei der Identifizierung von Forschungstrends', {
        error: error.message,
        stack: error.stack
      });
      
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * Ruft aktuelle Publikationen ab
   * @private
   * @param {number} limit - Maximale Anzahl von Publikationen
   * @returns {Promise<Array<Object>>} Aktuelle Publikationen
   */
  async _getRecentPublications(limit) {
    if (!this.databaseService) {
      return [];
    }
    
    try {
      const publications = await this.databaseService.query(
        'SELECT * FROM publications ORDER BY published_at DESC LIMIT ?',
        [limit]
      );
      
      return publications || [];
    } catch (error) {
      logger.error('ResearchLearningAgent: Fehler beim Abrufen aktueller Publikationen', {
        error: error.message,
        stack: error.stack
      });
      
      return [];
    }
  }
  
  /**
   * Erstellt einen Prompt für die Trendanalyse
   * @private
   * @param {Array<Object>} publications - Zu analysierende Publikationen
   * @returns {string} Prompt für die Trendanalyse
   */
  _buildTrendAnalysisPrompt(publications) {
    // Formatiere Publikationen
    const formattedPublications = publications.slice(0, 20).map((pub, index) => {
      return `
PUBLIKATION ${index + 1}:
TITEL: ${pub.title}
AUTOREN: ${Array.isArray(pub.authors) ? pub.authors.join(', ') : pub.authors}
JAHR: ${pub.year || 'Unbekannt'}
ABSTRACT: ${pub.abstract || 'Kein Abstract verfügbar'}
KEYWORDS: ${Array.isArray(pub.keywords) ? pub.keywords.join(', ') : (pub.keywords || 'Keine Keywords verfügbar')}
`;
    }).join('\n');
    
    return `
Als wissenschaftlicher Trendanalyst identifizieren Sie bitte aufkommende Forschungstrends basierend auf den folgenden aktuellen Publikationen.

${formattedPublications}

Bitte analysieren Sie diese Publikationen und identifizieren Sie:
1. Aufkommende Forschungstrends: Welche neuen Forschungsrichtungen oder -themen zeichnen sich ab?
2. Methodische Trends: Welche neuen Methoden oder Techniken werden zunehmend eingesetzt?
3. Interdisziplinäre Verbindungen: Welche Verbindungen zwischen verschiedenen Forschungsgebieten werden sichtbar?
4. Potenzielle Zukunftstrends: Welche Forschungsrichtungen könnten in naher Zukunft an Bedeutung gewinnen?

Formatieren Sie Ihre Antwort wie folgt:
AUFKOMMENDE TRENDS:
- [Name des Trends]: [Beschreibung] (Relevanz: Hoch/Mittel/Niedrig)

METHODISCHE TRENDS:
- [Name des Trends]: [Beschreibung] (Relevanz: Hoch/Mittel/Niedrig)

INTERDISZIPLINÄRE VERBINDUNGEN:
- [Verbindung]: [Beschreibung] (Relevanz: Hoch/Mittel/Niedrig)

ZUKUNFTSTRENDS:
- [Name des Trends]: [Beschreibung] (Relevanz: Hoch/Mittel/Niedrig)

ZUSAMMENFASSUNG:
[Kurze Zusammenfassung der wichtigsten Erkenntnisse]
`;
  }
  
  /**
   * Parst die Antwort der Trendanalyse
   * @private
   * @param {string} response - Antwort des KI-Modells
   * @returns {Object} Analysierte Trends
   */
  _parseTrendAnalysisResponse(response) {
    const result = {
      emergingTrends: [],
      methodologicalTrends: [],
      interdisciplinaryConnections: [],
      futureTrends: [],
      summary: ''
    };
    
    try {
      // Extrahiere aufkommende Trends
      const emergingMatch = response.match(/AUFKOMMENDE TRENDS:\s*([\s\S]*?)(?=METHODISCHE TRENDS:|$)/);
      if (emergingMatch && emergingMatch[1]) {
        const trendsText = emergingMatch[1].trim();
        const trendEntries = trendsText.split(/\n-\s*/).filter(entry => entry.trim());
        
        trendEntries.forEach(entry => {
          const match = entry.match(/([^:]+):\s*(.*?)(?:\(Relevanz:\s*(Hoch|Mittel|Niedrig)\))?$/);
          if (match) {
            result.emergingTrends.push({
              name: match[1].trim(),
              description: match[2].trim(),
              relevance: match[3] ? match[3].toLowerCase() : 'mittel'
            });
          }
        });
      }
      
      // Extrahiere methodische Trends
      const methodologicalMatch = response.match(/METHODISCHE TRENDS:\s*([\s\S]*?)(?=INTERDISZIPLINÄRE VERBINDUNGEN:|$)/);
      if (methodologicalMatch && methodologicalMatch[1]) {
        const trendsText = methodologicalMatch[1].trim();
        const trendEntries = trendsText.split(/\n-\s*/).filter(entry => entry.trim());
        
        trendEntries.forEach(entry => {
          const match = entry.match(/([^:]+):\s*(.*?)(?:\(Relevanz:\s*(Hoch|Mittel|Niedrig)\))?$/);
          if (match) {
            result.methodologicalTrends.push({
              name: match[1].trim(),
              description: match[2].trim(),
              relevance: match[3] ? match[3].toLowerCase() : 'mittel'
            });
          }
        });
      }
      
      // Extrahiere interdisziplinäre Verbindungen
      const interdisciplinaryMatch = response.match(/INTERDISZIPLINÄRE VERBINDUNGEN:\s*([\s\S]*?)(?=ZUKUNFTSTRENDS:|$)/);
      if (interdisciplinaryMatch && interdisciplinaryMatch[1]) {
        const connectionsText = interdisciplinaryMatch[1].trim();
        const connectionEntries = connectionsText.split(/\n-\s*/).filter(entry => entry.trim());
        
        connectionEntries.forEach(entry => {
          const match = entry.match(/([^:]+):\s*(.*?)(?:\(Relevanz:\s*(Hoch|Mittel|Niedrig)\))?$/);
          if (match) {
            result.interdisciplinaryConnections.push({
              name: match[1].trim(),
              description: match[2].trim(),
              relevance: match[3] ? match[3].toLowerCase() : 'mittel'
            });
          }
        });
      }
      
      // Extrahiere Zukunftstrends
      const futureMatch = response.match(/ZUKUNFTSTRENDS:\s*([\s\S]*?)(?=ZUSAMMENFASSUNG:|$)/);
      if (futureMatch && futureMatch[1]) {
        const trendsText = futureMatch[1].trim();
        const trendEntries = trendsText.split(/\n-\s*/).filter(entry => entry.trim());
        
        trendEntries.forEach(entry => {
          const match = entry.match(/([^:]+):\s*(.*?)(?:\(Relevanz:\s*(Hoch|Mittel|Niedrig)\))?$/);
          if (match) {
            result.futureTrends.push({
              name: match[1].trim(),
              description: match[2].trim(),
              relevance: match[3] ? match[3].toLowerCase() : 'mittel'
            });
          }
        });
      }
      
      // Extrahiere Zusammenfassung
      const summaryMatch = response.match(/ZUSAMMENFASSUNG:\s*([\s\S]+)$/);
      if (summaryMatch && summaryMatch[1]) {
        result.summary = summaryMatch[1].trim();
      }
    } catch (error) {
      logger.error('ResearchLearningAgent: Fehler beim Parsen der Trendanalyse', {
        error: error.message,
        stack: error.stack
      });
    }
    
    return result;
  }
  
  /**
   * Generiert Forschungsempfehlungen für einen Forscher
   * @param {Object} researcher - Forscher
   * @param {Object} options - Optionen für die Empfehlungen
   * @returns {Promise<Object>} Forschungsempfehlungen
   */
  async generateResearchRecommendations(researcher, options = {}) {
    try {
      logger.info('ResearchLearningAgent: Generiere Forschungsempfehlungen', {
        researcherId: researcher.id
      });
      
      // Lade Publikationen des Forschers
      const researcherPublications = await this._getResearcherPublications(researcher.id);
      
      // Lade aktuelle Trends
      const trendsAnalysis = await this.identifyResearchTrends({ limit: 50 });
      
      if (!trendsAnalysis.success) {
        throw new Error('Fehler bei der Trendanalyse: ' + trendsAnalysis.error);
      }
      
      // Erstelle Prompt für Empfehlungen
      const prompt = this._buildRecommendationsPrompt(researcher, researcherPublications, trendsAnalysis.trends);
      
      // Generiere Empfehlungen
      const recommendationsResponse = await this.generateText(prompt, {
        temperature: 0.4,
        maxTokens: 2000
      });
      
      // Parsen der strukturierten Antwort
      const recommendations = this._parseRecommendationsResponse(recommendationsResponse);
      
      return {
        success: true,
        researcher: {
          id: researcher.id,
          name: researcher.name
        },
        recommendations,
        publicationsAnalyzed: researcherPublications.length,
        rawRecommendations: recommendationsResponse
      };
    } catch (error) {
      logger.error('ResearchLearningAgent: Fehler bei der Generierung von Forschungsempfehlungen', {
        error: error.message,
        stack: error.stack,
        researcherId: researcher.id
      });
      
      return {
        success: false,
        error: error.message,
        researcher: {
          id: researcher.id,
          name: researcher.name
        }
      };
    }
  }
  
  /**
   * Ruft Publikationen eines Forschers ab
   * @private
   * @param {string} researcherId - ID des Forschers
   * @returns {Promise<Array<Object>>} Publikationen des Forschers
   */
  async _getResearcherPublications(researcherId) {
    if (!this.databaseService) {
      return [];
    }
    
    try {
      const publications = await this.databaseService.query(
        'SELECT p.* FROM publications p JOIN publication_authors pa ON p.id = pa.publication_id WHERE pa.researcher_id = ? ORDER BY p.published_at DESC',
        [researcherId]
      );
      
      return publications || [];
    } catch (error) {
      logger.error('ResearchLearningAgent: Fehler beim Abrufen der Publikationen eines Forschers', {
        error: error.message,
        stack: error.stack,
        researcherId
      });
      
      return [];
    }
  }
  
  /**
   * Erstellt einen Prompt für Forschungsempfehlungen
   * @private
   * @param {Object} researcher - Forscher
   * @param {Array<Object>} publications - Publikationen des Forschers
   * @param {Object} trends - Aktuelle Forschungstrends
   * @returns {string} Prompt für Forschungsempfehlungen
   */
  _buildRecommendationsPrompt(researcher, publications, trends) {
    // Formatiere Publikationen
    const formattedPublications = publications.slice(0, 10).map((pub, index) => {
      return `
PUBLIKATION ${index + 1}:
TITEL: ${pub.title}
JAHR: ${pub.year || 'Unbekannt'}
ABSTRACT: ${pub.abstract || 'Kein Abstract verfügbar'}
KEYWORDS: ${Array.isArray(pub.keywords) ? pub.keywords.join(', ') : (pub.keywords || 'Keine Keywords verfügbar')}
`;
    }).join('\n');
    
    // Formatiere Trends
    const formattedTrends = `
AUFKOMMENDE TRENDS:
${trends.emergingTrends.map(trend => `- ${trend.name}: ${trend.description}`).join('\n')}

METHODISCHE TRENDS:
${trends.methodologicalTrends.map(trend => `- ${trend.name}: ${trend.description}`).join('\n')}

INTERDISZIPLINÄRE VERBINDUNGEN:
${trends.interdisciplinaryConnections.map(connection => `- ${connection.name}: ${connection.description}`).join('\n')}

ZUKUNFTSTRENDS:
${trends.futureTrends.map(trend => `- ${trend.name}: ${trend.description}`).join('\n')}
`;
    
    return `
Als wissenschaftlicher Berater generieren Sie bitte Forschungsempfehlungen für einen Forscher basierend auf dessen bisherigen Publikationen und aktuellen Forschungstrends.

FORSCHER:
Name: ${researcher.name}
Fachgebiet: ${researcher.field || 'Nicht angegeben'}
Interessen: ${researcher.interests || 'Nicht angegeben'}

BISHERIGE PUBLIKATIONEN:
${formattedPublications}

AKTUELLE FORSCHUNGSTRENDS:
${formattedTrends}

Bitte generieren Sie fundierte Forschungsempfehlungen für diesen Forscher. Berücksichtigen Sie dabei:
1. Die bisherigen Forschungsinteressen und Expertise des Forschers
2. Aktuelle und aufkommende Trends im Forschungsfeld
3. Potenzielle interdisziplinäre Verbindungen
4. Methodische Innovationen, die der Forscher anwenden könnte
5. Konkrete Forschungsfragen oder -projekte, die der Forscher verfolgen könnte

Formatieren Sie Ihre Antwort wie folgt:
FORSCHUNGSRICHTUNGEN:
- [Forschungsrichtung 1]: [Beschreibung und Begründung]
- [Forschungsrichtung 2]: [Beschreibung und Begründung]
...

METHODISCHE EMPFEHLUNGEN:
- [Methode 1]: [Beschreibung und Begründung]
- [Methode 2]: [Beschreibung und Begründung]
...

KONKRETE FORSCHUNGSFRAGEN:
- [Forschungsfrage 1]: [Beschreibung und Begründung]
- [Forschungsfrage 2]: [Beschreibung und Begründung]
...

KOLLABORATIONSMÖGLICHKEITEN:
- [Kollaborationsmöglichkeit 1]: [Beschreibung und Begründung]
- [Kollaborationsmöglichkeit 2]: [Beschreibung und Begründung]
...

ZUSAMMENFASSUNG:
[Kurze Zusammenfassung der wichtigsten Empfehlungen]
`;
  }
  
  /**
   * Parst die Antwort der Forschungsempfehlungen
   * @private
   * @param {string} response - Antwort des KI-Modells
   * @returns {Object} Forschungsempfehlungen
   */
  _parseRecommendationsResponse(response) {
    const result = {
      researchDirections: [],
      methodologicalRecommendations: [],
      researchQuestions: [],
      collaborationOpportunities: [],
      summary: ''
    };
    
    try {
      // Extrahiere Forschungsrichtungen
      const directionsMatch = response.match(/FORSCHUNGSRICHTUNGEN:\s*([\s\S]*?)(?=METHODISCHE EMPFEHLUNGEN:|$)/);
      if (directionsMatch && directionsMatch[1]) {
        const directionsText = directionsMatch[1].trim();
        const directionEntries = directionsText.split(/\n-\s*/).filter(entry => entry.trim());
        
        directionEntries.forEach(entry => {
          const match = entry.match(/([^:]+):\s*(.*)/);
          if (match) {
            result.researchDirections.push({
              name: match[1].trim(),
              description: match[2].trim()
            });
          }
        });
      }
      
      // Extrahiere methodische Empfehlungen
      const methodsMatch = response.match(/METHODISCHE EMPFEHLUNGEN:\s*([\s\S]*?)(?=KONKRETE FORSCHUNGSFRAGEN:|$)/);
      if (methodsMatch && methodsMatch[1]) {
        const methodsText = methodsMatch[1].trim();
        const methodEntries = methodsText.split(/\n-\s*/).filter(entry => entry.trim());
        
        methodEntries.forEach(entry => {
          const match = entry.match(/([^:]+):\s*(.*)/);
          if (match) {
            result.methodologicalRecommendations.push({
              name: match[1].trim(),
              description: match[2].trim()
            });
          }
        });
      }
      
      // Extrahiere Forschungsfragen
      const questionsMatch = response.match(/KONKRETE FORSCHUNGSFRAGEN:\s*([\s\S]*?)(?=KOLLABORATIONSMÖGLICHKEITEN:|$)/);
      if (questionsMatch && questionsMatch[1]) {
        const questionsText = questionsMatch[1].trim();
        const questionEntries = questionsText.split(/\n-\s*/).filter(entry => entry.trim());
        
        questionEntries.forEach(entry => {
          const match = entry.match(/([^:]+):\s*(.*)/);
          if (match) {
            result.researchQuestions.push({
              question: match[1].trim(),
              description: match[2].trim()
            });
          }
        });
      }
      
      // Extrahiere Kollaborationsmöglichkeiten
      const collaborationsMatch = response.match(/KOLLABORATIONSMÖGLICHKEITEN:\s*([\s\S]*?)(?=ZUSAMMENFASSUNG:|$)/);
      if (collaborationsMatch && collaborationsMatch[1]) {
        const collaborationsText = collaborationsMatch[1].trim();
        const collaborationEntries = collaborationsText.split(/\n-\s*/).filter(entry => entry.trim());
        
        collaborationEntries.forEach(entry => {
          const match = entry.match(/([^:]+):\s*(.*)/);
          if (match) {
            result.collaborationOpportunities.push({
              name: match[1].trim(),
              description: match[2].trim()
            });
          }
        });
      }
      
      // Extrahiere Zusammenfassung
      const summaryMatch = response.match(/ZUSAMMENFASSUNG:\s*([\s\S]+)$/);
      if (summaryMatch && summaryMatch[1]) {
        result.summary = summaryMatch[1].trim();
      }
    } catch (error) {
      logger.error('ResearchLearningAgent: Fehler beim Parsen der Forschungsempfehlungen', {
        error: error.message,
        stack: error.stack
      });
    }
    
    return result;
  }
}

export default ResearchLearningAgent;/**
 * @fileoverview KI-Agent, der aus wissenschaftlichen Publikationen lernt
 * 
 * Dieser Agent kann aus den wissenschaftlichen Publikationen auf der Plattform lernen,
 * Zusammenhänge zwischen Forschungsgebieten erkennen, Trends identifizieren und
 * Forschern bei ihrer Arbeit unterstützen.
 */

import axios from 'axios';
import { logger } from '../../utils/logger.js';
import { BaseAgent } from './index.js';

/**
 * Agent für das Lernen aus wissenschaftlichen Publikationen
 */
export class ResearchLearningAgent extends BaseAgent {
  /**
   * Erstellt eine neue Instanz des ResearchLearningAgent
   * @param {Object} options - Konfigurationsoptionen
   * @param {Object} options.databaseService - Datenbankdienst für den Zugriff auf Publikationen
   * @param {Object} options.vectorDatabase - Vektordatenbank für semantische Suche
   * @param {Object} options.textEmbeddingService - Dienst für Text-Embeddings
   * @param {Object} options.config - Weitere Konfigurationsoptionen
   */
  constructor(options = {}) {
    super('ResearchLearningAgent');
    
    const {
      databaseService,
      vectorDatabase,
      textEmbeddingService,
      config = {}
    } = options;
    
    this.databaseService = databaseService;
    this.vectorDatabase = vectorDatabase;
    this.textEmbeddingService = textEmbeddingService;
    
    this.config = {
      maxContextSize: 8000,
      minRelevanceScore: 0.7,
      maxPublicationsPerQuery: 10,
      ...config
    };
    
    this.knowledgeBase = {
      domains: new Map(),
      concepts: new Map(),
      trends: new Map(),
      lastUpdated: null
    };
  }
  
  /**
   * Initialisiert den Agenten und lädt die Wissensbasis
   * @returns {Promise<boolean>} Erfolgsstatus
   */
  async initialize() {
    try {
      logger.info('ResearchLearningAgent: Initialisiere');
      
      // Prüfen, ob die erforderlichen Dienste verfügbar sind
      if (!this.databaseService) {
        logger.warn('ResearchLearningAgent: DatabaseService nicht verfügbar, Funktionalität wird eingeschränkt sein');
      }
      
      if (!this.vectorDatabase) {
        logger.warn('ResearchLearningAgent: VectorDatabase nicht verfügbar, semantische Suche wird deaktiviert');
      }
      
      if (!this.textEmbeddingService) {
        logger.warn('ResearchLearningAgent: TextEmbeddingService nicht verfügbar, Embedding-Funktionalität wird deaktiviert');
      }
      
      // Lade initiale Wissensbasis
      await this._loadKnowledgeBase();
      
      logger.info('ResearchLearningAgent: Erfolgreich initialisiert');
      
      return true;
    } catch (error) {
      logger.error('ResearchLearningAgent: Fehler bei der Initialisierung', {
        error: error.message,
        stack: error.stack
      });
      
      return false;
    }
  }
  
  /**
   * Lädt die Wissensbasis aus der Datenbank
   * @private
   * @returns {Promise<void>}
   */
  async _loadKnowledgeBase() {
    if (!this.databaseService) {
      return;
    }
    
    try {
      logger.info('ResearchLearningAgent: Lade Wissensbasis');
      
      // Lade Domänen
      const domains = await this.databaseService.query(
        'SELECT * FROM research_domains ORDER BY publication_count DESC LIMIT 100'
      );
      
      if (domains && domains.length > 0) {
        domains.forEach(domain => {
          this.knowledgeBase.domains.set(domain.id, {
            name: domain.name,
            description: domain.description,
            publicationCount: domain.publication_count,
            relatedDomains: new Set(domain.related_domains || [])
          });
        });
      }
      
      // Lade Konzepte
      const concepts = await this.databaseService.query(
        'SELECT * FROM research_concepts ORDER BY importance DESC LIMIT 500'
      );
      
      if (concepts && concepts.length > 0) {
        concepts.forEach(concept => {
          this.knowledgeBase.concepts.set(concept.id, {
            name: concept.name,
            description: concept.description,
            domains: new Set(concept.domains || []),
            relatedConcepts: new Set(concept.related_concepts || [])
          });
        });
      }
      
      // Lade Trends
      const trends = await this.databaseService.query(
        'SELECT * FROM research_trends WHERE active = 1 ORDER BY momentum DESC LIMIT 50'
      );
      
      if (trends && trends.length > 0) {
        trends.forEach(trend => {
          this.knowledgeBase.trends.set(trend.id, {
            name: trend.name,
            description: trend.description,
            momentum: trend.momentum,
            startDate: trend.start_date,
            domains: new Set(trend.domains || [])
          });
        });
      }
      
      this.knowledgeBase.lastUpdated = new Date();
      
      logger.info('ResearchLearningAgent: Wissensbasis erfolgreich geladen', {
        domains: this.knowledgeBase.domains.size,
        concepts: this.knowledgeBase.concepts.size,
        trends: this.knowledgeBase.trends.size
      });
    } catch (error) {
      logger.error('ResearchLearningAgent: Fehler beim Laden der Wissensbasis', {
        error: error.message,
        stack: error.stack
      });
    }
  }
  
  /**
   * Aktualisiert die Wissensbasis mit neuen Publikationen
   * @param {Array<Object>} publications - Liste von Publikationen
   * @returns {Promise<Object>} Ergebnis der Aktualisierung
   */
  async updateKnowledgeBase(publications) {
    try {
      logger.info('ResearchLearningAgent: Aktualisiere Wissensbasis', {
        publicationCount: publications.length
      });
      
      // Extrahiere Konzepte und Domänen aus den Publikationen
      const extractionPrompt = this._buildConceptExtractionPrompt(publications);
      
      const extractionResponse = await this.generateText(extractionPrompt, {
        temperature: 0.2,
        maxTokens: 2000
      });
      
      // Parsen der strukturierten Antwort
      const extractedData = this._parseExtractionResponse(extractionResponse);
      
      // Aktualisiere die Wissensbasis
      await this._updateKnowledgeBaseWithExtractedData(extractedData);
      
      this.knowledgeBase.lastUpdated = new Date();
      
      return {
        success: true,
        extractedDomains: extractedData.domains.length,
        extractedConcepts: extractedData.concepts.length,
        extractedTrends: extractedData.trends.length
      };
    } catch (error) {
      logger.error('ResearchLearningAgent: Fehler bei der Aktualisierung der Wissensbasis', {
        error: error.message,
        stack: error.stack
      });
      
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * Erstellt einen Prompt für die Extraktion von Konzepten und Domänen
   * @private
   * @param {Array<Object>} publications - Liste von Publikationen
   * @returns {string} Extraktionsprompt
   */
  _buildConceptExtractionPrompt(publications) {
    // Formatiere Publikationen
    const formattedPublications = publications.map((pub, index) => {
      return `
PUBLIKATION ${index + 1}:
TITEL: ${pub.title}
AUTOREN: ${Array.isArray(pub.authors) ? pub.authors.join(', ') : pub.authors}
ABSTRACT: ${pub.abstract || 'Kein Abstract verfügbar'}
KEYWORDS: ${Array.isArray(pub.keywords) ? pub.keywords.join(', ') : (pub.keywords || 'Keine Keywords verfügbar')}
`;
    }).join('\n');
    
    return `
Als wissenschaftlicher Experte analysieren Sie bitte die folgenden Publikationen und extrahieren Sie wichtige Forschungsdomänen, Konzepte und potenzielle Trends.

${formattedPublications}

Bitte extrahieren Sie folgende Informationen:
1. Forschungsdomänen: Identifizieren Sie die Hauptforschungsgebiete, denen diese Publikationen zugeordnet werden können.
2. Konzepte: Extrahieren Sie wichtige wissenschaftliche Konzepte, Methoden oder Theorien, die in diesen Publikationen diskutiert werden.
3. Potenzielle Trends: Identifizieren Sie aufkommende Forschungstrends oder -richtungen basierend auf diesen Publikationen.

Formatieren Sie Ihre Antwort wie folgt:
DOMÄNEN:
- [Name der Domäne]: [Kurze Beschreibung]

KONZEPTE:
- [Name des Konzepts]: [Kurze Beschreibung] (Zugehörige Domänen: [Liste der zugehörigen Domänen])

TRENDS:
- [Name des Trends]: [Kurze Beschreibung] (Zugehörige Domänen: [Liste der zugehörigen Domänen])
`;
  }
  
  /**
   * Parst die Antwort der Konzeptextraktion
   * @private
   * @param {string} response - Antwort des KI-Modells
   * @returns {Object} Extrahierte Daten
   */
  _parseExtractionResponse(response) {
    const result = {
      domains: [],
      concepts: [],
      trends: []
    };
    
    try {
      // Extrahiere Domänen
      const domainsMatch = response.match(/DOMÄNEN:\s*([\s\S]*?)(?=KONZEPTE:|$)/);
      if (domainsMatch && domainsMatch[1]) {
        const domainsText = domainsMatch[1].trim();
        const domainEntries = domainsText.split(/\n-\s*/).filter(entry => entry.trim());
        
        domainEntries.forEach(entry => {
          const match = entry.match(/([^:]+):\s*(.*)/);
          if (match) {
            result.domains.push({
              name: match[1].trim(),
              description: match[2].trim()
            });
          }
        });
      }
      
      // Extrahiere Konzepte
      const conceptsMatch = response.match(/KONZEPTE:\s*([\s\S]*?)(?=TRENDS:|$)/);
      if (conceptsMatch && conceptsMatch[1]) {
        const conceptsText = conceptsMatch[1].trim();
        const conceptEntries = conceptsText.split(/\n-\s*/).filter(entry => entry.trim());
        
        conceptEntries.forEach(entry => {
          const match = entry.match(/([^:]+):\s*(.*?)(?:\(Zugehörige Domänen:\s*(.*?)\))?$/);
          if (match) {
            const domains = match[3] ? match[3].split(',').map(d => d.trim()) : [];
            
            result.concepts.push({
              name: match[1].trim(),
              description: match[2].trim(),
              domains
            });
          }
        });
      }
      
      // Extrahiere Trends
      const trendsMatch = response.match(/TRENDS:\s*([\s\S]*?)(?=$)/);
      if (trendsMatch && trendsMatch[1]) {
        const trendsText = trendsMatch[1].trim();
        const trendEntries = trendsText.split(/\n-\s*/).filter(entry => entry.trim());
        
        trendEntries.forEach(entry => {
          const match = entry.match(/([^:]+):\s*(.*?)(?:\(Zugehörige Domänen:\s*(.*?)\))?$/);
          if (match) {
            const domains = match[3] ? match[3].split(',').map(d => d.trim()) : [];
            
            result.trends.push({
              name: match[1].trim(),
              description: match[2].trim(),
              domains
            });
          }
        });
      }
    } catch (error) {
      logger.error('ResearchLearningAgent: Fehler beim Parsen der Extraktionsantwort', {
        error: error.message,
        stack: error.stack
      });
    }
    
    return result;
  }
  
  /**
   * Aktualisiert die Wissensbasis mit extrahierten Daten
   * @private
   * @param {Object} extractedData - Extrahierte Daten
   * @returns {Promise<void>}
   */
  async _updateKnowledgeBaseWithExtractedData(extractedData) {
    if (!this.databaseService) {
      return;
    }
    
    try {
      // Aktualisiere Domänen
      for (const domain of extractedData.domains) {
        // Prüfe, ob die Domäne bereits existiert
        const existingDomain = await this.databaseService.query(
          'SELECT * FROM research_domains WHERE name = ?',
          [domain.name]
        );
        
        if (existingDomain && existingDomain.length > 0) {
          // Aktualisiere bestehende Domäne
          await this.databaseService.execute(
            'UPDATE research_domains SET description = ?, updated_at = NOW() WHERE id = ?',
            [domain.description, existingDomain[0].id]
          );
          
          // Aktualisiere Wissensbasis
          this.knowledgeBase.domains.set(existingDomain[0].id, {
            name: domain.name,
            description: domain.description,
            publicationCount: existingDomain[0].publication_count,
            relatedDomains: new Set(existingDomain[0].related_domains || [])
          });
        } else {
          // Erstelle neue Domäne
          const result = await this.databaseService.execute(
            'INSERT INTO research_domains (name, description, publication_count, created_at, updated_at) VALUES (?, ?, 1, NOW(), NOW())',
            [domain.name, domain.description]
          );
          
          const domainId = result.insertId;
          
          // Aktualisiere Wissensbasis
          this.knowledgeBase.domains.set(domainId, {
            name: domain.name,
            description: domain.description,
            publicationCount: 1,
            relatedDomains: new Set()
          });
        }
      }
      
      // Aktualisiere Konzepte (ähnliche Implementierung wie für Domänen)
      // ...
      
      // Aktualisiere Trends (ähnliche Implementierung wie für Domänen)
      // ...
      
      logger.info('ResearchLearningAgent: Wissensbasis erfolgreich aktualisiert');
    } catch (error) {
      logger.error('ResearchLearningAgent: Fehler bei der Aktualisierung der Wissensbasis mit extrahierten Daten', {
        error: error.message,
        stack: error.stack
      });
    }
  }
  
  /**
   * Beantwortet eine Forschungsfrage basierend auf der Wissensbasis
   * @param {string} question - Forschungsfrage
   * @param {Object} options - Optionen für die Beantwortung
   * @returns {Promise<Object>} Antwort auf die Frage
   */
  async answerResearchQuestion(question, options = {}) {
    try {
      logger.info('ResearchLearningAgent: Beantworte Forschungsfrage', { question });
      
      // Finde relevante Publikationen
      const relevantPublications = await this._findRelevantPublications(question);
      
      // Erstelle Kontext für die Beantwortung
      const context = this._buildQuestionContext(question, relevantPublications);
      
      // Erstelle Prompt für die Beantwortung
      const prompt = this._buildAnswerPrompt(question, context);
      
      // Generiere Antwort
      const answer = await this.generateText(prompt, {
        temperature: options.temperature || 0.3,
        maxTokens: options.maxTokens || 1500
      });
      
      // Extrahiere Quellen
      const sources = this._extractSourcesFromAnswer(answer, relevantPublications);
      
      return {
        success: true,
        question,
        answer: this._formatAnswer(answer),
        sources,
        relevantPublicationsCount: relevantPublications.length
      };
    } catch (error) {
      logger.error('ResearchLearningAgent: Fehler bei der Beantwortung der Forschungsfrage', {
        error: error.message,
        stack: error.stack,
        question
      });
      
      return {
        success: false,
        question,
        error: error.message
      };
    }
  }
  
  /**
   * Findet relevante Publikationen für eine Frage
   * @private
   * @param {string} question - Forschungsfrage
   * @returns {Promise<Array<Object>>} Relevante Publikationen
   */
  async _findRelevantPublications(question) {
    if (!this.vectorDatabase || !this.textEmbeddingService) {
      // Fallback: Suche in der Datenbank nach Schlüsselwörtern
      return this._findPublicationsByKeywords(question);
    }
    
    try {
      // Erstelle Embedding für die Frage
      const questionEmbedding = await this.textEmbeddingService.createEmbedding(question);
      
      // Suche nach ähnlichen Publikationen in der Vektordatenbank
      const searchResults = await this.vectorDatabase.search({
        embedding: questionEmbedding,
        collection: 'publications',
        limit: this.config.maxPublicationsPerQuery,
        minScore: this.config.minRelevanceScore
      });
      
      // Lade vollständige Publikationsdaten
      const publications = await Promise.all(searchResults.map(async (result) => {
        const publication = await this.databaseService.query(
          'SELECT * FROM publications WHERE id = ?',
          [result.id]
        );
        
        return publication && publication.length > 0 ? {
          ...publication[0],
          relevanceScore: result.score
        } : null;
      }));
      
      return publications.filter(Boolean);
    } catch (error) {
      logger.error('ResearchLearningAgent: Fehler bei der Suche nach relevanten Publikationen', {
        error: error.message,
        stack: error.stack,
        question
      });
      
      // Fallback: Suche in der Datenbank nach Schlüsselwörtern
      return this._findPublicationsByKeywords(question);
    }
  }
  
  /**
   * Findet Publikationen anhand von Schlüsselwörtern
   * @private
   * @param {string} query - Suchanfrage
   * @returns {Promise<Array<Object>>} Gefundene Publikationen
   */
  async _findPublicationsByKeywords(query) {
    if (!this.databaseService) {
      return [];
    }
    
    try {
      // Extrahiere Schlüsselwörter aus der Anfrage
      const keywords = query.toLowerCase()
        .replace(/[^\w\s]/g, '')
        .split(/\s+/)
        .filter(word => word.length > 3);
      
      if (keywords.length === 0) {
        return [];
      }
      
      // Erstelle SQL-Abfrage mit LIKE-Operatoren
      const whereClauses = keywords.map(() => '(title LIKE ? OR abstract LIKE ?)');
      const whereClause = whereClauses.join(' OR ');
      
      const params = [];
      keywords.forEach(keyword => {
        params.push(`%${keyword}%`);
        params.push(`%${keyword}%`);
      });
      
      // Führe Abfrage aus
      const publications = await this.databaseService.query(
        `SELECT * FROM publications WHERE ${whereClause} ORDER BY published_at DESC LIMIT ?`,
        [...params, this.config.maxPublicationsPerQuery]
      );
      
      return publications || [];
    } catch (error) {
      logger.error('ResearchLearningAgent: Fehler bei der Suche nach Publikationen anhand von Schlüsselwörtern', {
        error: error.message,
        stack: error.stack,
        query
      });
      
      return [];
    }
  }
  
  /**
   * Erstellt den Kontext für die Beantwortung einer Frage
   * @private
   * @param {string} question - Forschungsfrage
   * @param {Array<Object>} publications - Relevante Publikationen
   * @returns {string} Kontext für die Beantwortung
   */
  _buildQuestionContext(question, publications) {
    // Formatiere Publikationen
    const formattedPublications = publications.map((pub, index) => {
      return `
QUELLE [${index + 1}]:
TITEL: ${pub.title}
AUTOREN: ${Array.isArray(pub.authors) ? pub.authors.join(', ') : pub.authors}
JAHR: ${pub.year || 'Unbekannt'}
DOI: ${pub.doi || 'Nicht verfügbar'}
ABSTRACT: ${pub.abstract || 'Kein Abstract verfügbar'}
`;
    }).join('\n');
    
    return formattedPublications;
  }
  
  /**
   * Erstellt einen Prompt für die Beantwortung einer Frage
   * @private
   * @param {string} question - Forschungsfrage
   * @param {string} context - Kontext für die Beantwortung
   * @returns {string} Prompt für die Beantwortung
   */
  _buildAnswerPrompt(question, context) {
    return `
Als wissenschaftlicher Experte beantworten Sie bitte die folgende Forschungsfrage basierend auf den bereitgestellten Quellen.
Beziehen Sie sich in Ihrer Antwort auf die Quellen und geben Sie an, welche Informationen aus welcher Quelle stammen.

FRAGE: ${question}

QUELLEN:
${context}

Bitte geben Sie eine fundierte, wissenschaftlich korrekte Antwort auf die Frage. Berücksichtigen Sie dabei:
1. Verschiedene Perspektiven und Ansätze aus den Quellen
2. Übereinstimmungen und Widersprüche zwischen den Quellen
3. Aktuelle Forschungsergebnisse und -trends
4. Einschränkungen und offene Fragen

Formatieren Sie Ihre Antwort wie folgt:
ANTWORT: [Ihre ausführliche Antwort]

QUELLEN: [Liste der verwendeten Quellen im Format [1], [2], etc.]
`;
  }
  
  /**
   * Formatiert die Antwort
   * @private
   * @param {string} answer - Antwort des KI-Modells
   * @returns {string} Formatierte Antwort
   */
  _formatAnswer(answer) {
    // Extrahiere den Antwortteil
    const answerMatch = answer.match(/ANTWORT:\s*([\s\S]*?)(?=QUELLEN:|$)/);
    if (answerMatch && answerMatch[1]) {
      return answerMatch[1].trim();
    }
    
    return answer;
  }
  
  /**
   * Extrahiert Quellen aus der Antwort
   * @private
   * @param {string} answer - Antwort des KI-Modells
   * @param {Array<Object>} publications - Relevante Publikationen
   * @returns {Array<Object>} Verwendete Quellen
   */
  _extractSourcesFromAnswer(answer, publications) {
    const sources = [];
    const sourceRegex = /\[(\d+)\]/g;
    let match;
    
    // Finde alle Quellenverweise in der Antwort
    const sourceReferences = new Set();
    while ((match = sourceRegex.exec(answer)) !== null) {
      const sourceIndex = parseInt(match[1]) - 1;
      if (sourceIndex >= 0 && sourceIndex < publications.length) {
        sourceReferences.add(sourceIndex);
      }
    }
    
    // Erstelle Quellenliste
    sourceReferences.forEach(index => {
      const publication = publications[index];
      sources.push({
        title: publication.title,
        authors: publication.authors,
        year: publication.year,
        doi: publication.doi,
        url: publication.url
      });
    });
    
    return sources;
  }
  
  /**
   * Identifiziert Forschungstrends basierend auf der Wissensbasis
   * @param {Object} options - Optionen für die Trendanalyse
   * @returns {Promise<Object>} Identifizierte Trends
   */
  async identifyResearchTrends(options = {}) {
    try {
      logger.info('ResearchLearningAgent: Identifiziere Forschungstrends');
      
      // Lade aktuelle Publikationen
      const recentPublications = await this._getRecentPublications(options.limit || 100);
      
      if (recentPublications.length === 0) {
        return {
          success: false,
          error: 'Keine aktuellen Publikationen gefunden'
        };
      }
      
      // Erstelle Prompt für die Trendanalyse
      const prompt = this._buildTrendAnalysisPrompt(recentPublications);
      
      // Generiere Trendanalyse
      const analysisResponse = await this.generateText(prompt, {
        temperature: 0.3,
        maxTokens: 2000
      });
      
      // Parsen der strukturierten Antwort
      const trends = this._parseTrendAnalysisResponse(analysisResponse);
      
      return {
        success: true,
        trends,
        publicationsAnalyzed: recentPublications.length,
        rawAnalysis: analysisResponse
      };
    } catch (error) {
      logger.error('ResearchLearningAgent: Fehler bei der Identifizierung von Forschungstrends', {
        error: error.message,
        stack: error.stack
      });
      
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * Ruft aktuelle Publikationen ab
   * @private
   * @param {number} limit - Maximale Anzahl von Publikationen
   * @returns {Promise<Array<Object>>} Aktuelle Publikationen
   */
  async _getRecentPublications(limit) {
    if (!this.databaseService) {
      return [];
    }
    
    try {
      const publications = await this.databaseService.query(
        'SELECT * FROM publications ORDER BY published_at DESC LIMIT ?',
        [limit]
      );
      
      return publications || [];
    } catch (error) {
      logger.error('ResearchLearningAgent: Fehler beim Abrufen aktueller Publikationen', {
        error: error.message,
        stack: error.stack
      });
      
      return [];
    }
  }
  
  /**
   * Erstellt einen Prompt für die Trendanalyse
   * @private
   * @param {Array<Object>} publications - Zu analysierende Publikationen
   * @returns {string} Prompt für die Trendanalyse
   */
  _buildTrendAnalysisPrompt(publications) {
    // Formatiere Publikationen
    const formattedPublications = publications.slice(0, 20).map((pub, index) => {
      return `
PUBLIKATION ${index + 1}:
TITEL: ${pub.title}
AUTOREN: ${Array.isArray(pub.authors) ? pub.authors.join(', ') : pub.authors}
JAHR: ${pub.year || 'Unbekannt'}
ABSTRACT: ${pub.abstract || 'Kein Abstract verfügbar'}
KEYWORDS: ${Array.isArray(pub.keywords) ? pub.keywords.join(', ') : (pub.keywords || 'Keine Keywords verfügbar')}
`;
    }).join('\n');
    
    return `
Als wissenschaftlicher Trendanalyst identifizieren Sie bitte aufkommende Forschungstrends basierend auf den folgenden aktuellen Publikationen.

${formattedPublications}

Bitte analysieren Sie diese Publikationen und identifizieren Sie:
1. Aufkommende Forschungstrends: Welche neuen Forschungsrichtungen oder -themen zeichnen sich ab?
2. Methodische Trends: Welche neuen Methoden oder Techniken werden zunehmend eingesetzt?
3. Interdisziplinäre Verbindungen: Welche Verbindungen zwischen verschiedenen Forschungsgebieten werden sichtbar?
4. Potenzielle Zukunftstrends: Welche Forschungsrichtungen könnten in naher Zukunft an Bedeutung gewinnen?

Formatieren Sie Ihre Antwort wie folgt:
AUFKOMMENDE TRENDS:
- [Name des Trends]: [Beschreibung] (Relevanz: Hoch/Mittel/Niedrig)

METHODISCHE TRENDS:
- [Name des Trends]: [Beschreibung] (Relevanz: Hoch/Mittel/Niedrig)

INTERDISZIPLINÄRE VERBINDUNGEN:
- [Verbindung]: [Beschreibung] (Relevanz: Hoch/Mittel/Niedrig)

ZUKUNFTSTRENDS:
- [Name des Trends]: [Beschreibung] (Relevanz: Hoch/Mittel/Niedrig)

ZUSAMMENFASSUNG:
[Kurze Zusammenfassung der wichtigsten Erkenntnisse]
`;
  }
  
  /**
   * Parst die Antwort der Trendanalyse
   * @private
   * @param {string} response - Antwort des KI-Modells
   * @returns {Object} Analysierte Trends
   */
  _parseTrendAnalysisResponse(response) {
    const result = {
      emergingTrends: [],
      methodologicalTrends: [],
      interdisciplinaryConnections: [],
      futureTrends: [],
      summary: ''
    };
    
    try {
      // Extrahiere aufkommende Trends
      const emergingMatch = response.match(/AUFKOMMENDE TRENDS:\s*([\s\S]*?)(?=METHODISCHE TRENDS:|$)/);
      if (emergingMatch && emergingMatch[1]) {
        const trendsText = emergingMatch[1].trim();
        const trendEntries = trendsText.split(/\n-\s*/).filter(entry => entry.trim());
        
        trendEntries.forEach(entry => {
          const match = entry.match(/([^:]+):\s*(.*?)(?:\(Relevanz:\s*(Hoch|Mittel|Niedrig)\))?$/);
          if (match) {
            result.emergingTrends.push({
              name: match[1].trim(),
              description: match[2].trim(),
              relevance: match[3] ? match[3].toLowerCase() : 'mittel'
            });
          }
        });
      }
      
      // Extrahiere methodische Trends
      const methodologicalMatch = response.match(/METHODISCHE TRENDS:\s*([\s\S]*?)(?=INTERDISZIPLINÄRE VERBINDUNGEN:|$)/);
      if (methodologicalMatch && methodologicalMatch[1]) {
        const trendsText = methodologicalMatch[1].trim();
        const trendEntries = trendsText.split(/\n-\s*/).filter(entry => entry.trim());
        
        trendEntries.forEach(entry => {
          const match = entry.match(/([^:]+):\s*(.*?)(?:\(Relevanz:\s*(Hoch|Mittel|Niedrig)\))?$/);
          if (match) {
            result.methodologicalTrends.push({
              name: match[1].trim(),
              description: match[2].trim(),
              relevance: match[3] ? match[3].toLowerCase() : 'mittel'
            });
          }
        });
      }
      
      // Extrahiere interdisziplinäre Verbindungen
      const interdisciplinaryMatch = response.match(/INTERDISZIPLINÄRE VERBINDUNGEN:\s*([\s\S]*?)(?=ZUKUNFTSTRENDS:|$)/);
      if (interdisciplinaryMatch && interdisciplinaryMatch[1]) {
        const connectionsText = interdisciplinaryMatch[1].trim();
        const connectionEntries = connectionsText.split(/\n-\s*/).filter(entry => entry.trim());
        
        connectionEntries.forEach(entry => {
          const match = entry.match(/([^:]+):\s*(.*?)(?:\(Relevanz:\s*(Hoch|Mittel|Niedrig)\))?$/);
          if (match) {
            result.interdisciplinaryConnections.push({
              name: match[1].trim(),
              description: match[2].trim(),
              relevance: match[3] ? match[3].toLowerCase() : 'mittel'
            });
          }
        });
      }
      
      // Extrahiere Zukunftstrends
      const futureMatch = response.match(/ZUKUNFTSTRENDS:\s*([\s\S]*?)(?=ZUSAMMENFASSUNG:|$)/);
      if (futureMatch && futureMatch[1]) {
        const trendsText = futureMatch[1].trim();
        const trendEntries = trendsText.split(/\n-\s*/).filter(entry => entry.trim());
        
        trendEntries.forEach(entry => {
          const match = entry.match(/([^:]+):\s*(.*?)(?:\(Relevanz:\s*(Hoch|Mittel|Niedrig)\))?$/);
          if (match) {
            result.futureTrends.push({
              name: match[1].trim(),
              description: match[2].trim(),
              relevance: match[3] ? match[3].toLowerCase() : 'mittel'
            });
          }
        });
      }
      
      // Extrahiere Zusammenfassung
      const summaryMatch = response.match(/ZUSAMMENFASSUNG:\s*([\s\S]+)$/);
      if (summaryMatch && summaryMatch[1]) {
        result.summary = summaryMatch[1].trim();
      }
    } catch (error) {
      logger.error('ResearchLearningAgent: Fehler beim Parsen der Trendanalyse', {
        error: error.message,
        stack: error.stack
      });
    }
    
    return result;
  }
  
  /**
   * Generiert Forschungsempfehlungen für einen Forscher
   * @param {Object} researcher - Forscher
   * @param {Object} options - Optionen für die Empfehlungen
   * @returns {Promise<Object>} Forschungsempfehlungen
   */
  async generateResearchRecommendations(researcher, options = {}) {
    try {
      logger.info('ResearchLearningAgent: Generiere Forschungsempfehlungen', {
        researcherId: researcher.id
      });
      
      // Lade Publikationen des Forschers
      const researcherPublications = await this._getResearcherPublications(researcher.id);
      
      // Lade aktuelle Trends
      const trendsAnalysis = await this.identifyResearchTrends({ limit: 50 });
      
      if (!trendsAnalysis.success) {
        throw new Error('Fehler bei der Trendanalyse: ' + trendsAnalysis.error);
      }
      
      // Erstelle Prompt für Empfehlungen
      const prompt = this._buildRecommendationsPrompt(researcher, researcherPublications, trendsAnalysis.trends);
      
      // Generiere Empfehlungen
      const recommendationsResponse = await this.generateText(prompt, {
        temperature: 0.4,
        maxTokens: 2000
      });
      
      // Parsen der strukturierten Antwort
      const recommendations = this._parseRecommendationsResponse(recommendationsResponse);
      
      return {
        success: true,
        researcher: {
          id: researcher.id,
          name: researcher.name
        },
        recommendations,
        publicationsAnalyzed: researcherPublications.length,
        rawRecommendations: recommendationsResponse
      };
    } catch (error) {
      logger.error('ResearchLearningAgent: Fehler bei der Generierung von Forschungsempfehlungen', {
        error: error.message,
        stack: error.stack,
        researcherId: researcher.id
      });
      
      return {
        success: false,
        error: error.message,
        researcher: {
          id: researcher.id,
          name: researcher.name
        }
      };
    }
  }
  
  /**
   * Ruft Publikationen eines Forschers ab
   * @private
   * @param {string} researcherId - ID des Forschers
   * @returns {Promise<Array<Object>>} Publikationen des Forschers
   */
  async _getResearcherPublications(researcherId) {
    if (!this.databaseService) {
      return [];
    }
    
    try {
      const publications = await this.databaseService.query(
        'SELECT p.* FROM publications p JOIN publication_authors pa ON p.id = pa.publication_id WHERE pa.researcher_id = ? ORDER BY p.published_at DESC',
        [researcherId]
      );
      
      return publications || [];
    } catch (error) {
      logger.error('ResearchLearningAgent: Fehler beim Abrufen der Publikationen eines Forschers', {
        error: error.message,
        stack: error.stack,
        researcherId
      });
      
      return [];
    }
  }
  
  /**
   * Erstellt einen Prompt für Forschungsempfehlungen
   * @private
   * @param {Object} researcher - Forscher
   * @param {Array<Object>} publications - Publikationen des Forschers
   * @param {Object} trends - Aktuelle Forschungstrends
   * @returns {string} Prompt für Forschungsempfehlungen
   */
  _buildRecommendationsPrompt(researcher, publications, trends) {
    // Formatiere Publikationen
    const formattedPublications = publications.slice(0, 10).map((pub, index) => {
      return `
PUBLIKATION ${index + 1}:
TITEL: ${pub.title}
JAHR: ${pub.year || 'Unbekannt'}
ABSTRACT: ${pub.abstract || 'Kein Abstract verfügbar'}
KEYWORDS: ${Array.isArray(pub.keywords) ? pub.keywords.join(', ') : (pub.keywords || 'Keine Keywords verfügbar')}
`;
    }).join('\n');
    
    // Formatiere Trends
    const formattedTrends = `
AUFKOMMENDE TRENDS:
${trends.emergingTrends.map(trend => `- ${trend.name}: ${trend.description}`).join('\n')}

METHODISCHE TRENDS:
${trends.methodologicalTrends.map(trend => `- ${trend.name}: ${trend.description}`).join('\n')}

INTERDISZIPLINÄRE VERBINDUNGEN:
${trends.interdisciplinaryConnections.map(connection => `- ${connection.name}: ${connection.description}`).join('\n')}

ZUKUNFTSTRENDS:
${trends.futureTrends.map(trend => `- ${trend.name}: ${trend.description}`).join('\n')}
`;
    
    return `
Als wissenschaftlicher Berater generieren Sie bitte Forschungsempfehlungen für einen Forscher basierend auf dessen bisherigen Publikationen und aktuellen Forschungstrends.

FORSCHER:
Name: ${researcher.name}
Fachgebiet: ${researcher.field || 'Nicht angegeben'}
Interessen: ${researcher.interests || 'Nicht angegeben'}

BISHERIGE PUBLIKATIONEN:
${formattedPublications}

AKTUELLE FORSCHUNGSTRENDS:
${formattedTrends}

Bitte generieren Sie fundierte Forschungsempfehlungen für diesen Forscher. Berücksichtigen Sie dabei:
1. Die bisherigen Forschungsinteressen und Expertise des Forschers
2. Aktuelle und aufkommende Trends im Forschungsfeld
3. Potenzielle interdisziplinäre Verbindungen
4. Methodische Innovationen, die der Forscher anwenden könnte
5. Konkrete Forschungsfragen oder -projekte, die der Forscher verfolgen könnte

Formatieren Sie Ihre Antwort wie folgt:
FORSCHUNGSRICHTUNGEN:
- [Forschungsrichtung 1]: [Beschreibung und Begründung]
- [Forschungsrichtung 2]: [Beschreibung und Begründung]
...

METHODISCHE EMPFEHLUNGEN:
- [Methode 1]: [Beschreibung und Begründung]
- [Methode 2]: [Beschreibung und Begründung]
...

KONKRETE FORSCHUNGSFRAGEN:
- [Forschungsfrage 1]: [Beschreibung und Begründung]
- [Forschungsfrage 2]: [Beschreibung und Begründung]
...

KOLLABORATIONSMÖGLICHKEITEN:
- [Kollaborationsmöglichkeit 1]: [Beschreibung und Begründung]
- [Kollaborationsmöglichkeit 2]: [Beschreibung und Begründung]
...

ZUSAMMENFASSUNG:
[Kurze Zusammenfassung der wichtigsten Empfehlungen]
`;
  }
  
  /**
   * Parst die Antwort der Forschungsempfehlungen
   * @private
   * @param {string} response - Antwort des KI-Modells
   * @returns {Object} Forschungsempfehlungen
   */
  _parseRecommendationsResponse(response) {
    const result = {
      researchDirections: [],
      methodologicalRecommendations: [],
      researchQuestions: [],
      collaborationOpportunities: [],
      summary: ''
    };
    
    try {
      // Extrahiere Forschungsrichtungen
      const directionsMatch = response.match(/FORSCHUNGSRICHTUNGEN:\s*([\s\S]*?)(?=METHODISCHE EMPFEHLUNGEN:|$)/);
      if (directionsMatch && directionsMatch[1]) {
        const directionsText = directionsMatch[1].trim();
        const directionEntries = directionsText.split(/\n-\s*/).filter(entry => entry.trim());
        
        directionEntries.forEach(entry => {
          const match = entry.match(/([^:]+):\s*(.*)/);
          if (match) {
            result.researchDirections.push({
              name: match[1].trim(),
              description: match[2].trim()
            });
          }
        });
      }
      
      // Extrahiere methodische Empfehlungen
      const methodsMatch = response.match(/METHODISCHE EMPFEHLUNGEN:\s*([\s\S]*?)(?=KONKRETE FORSCHUNGSFRAGEN:|$)/);
      if (methodsMatch && methodsMatch[1]) {
        const methodsText = methodsMatch[1].trim();
        const methodEntries = methodsText.split(/\n-\s*/).filter(entry => entry.trim());
        
        methodEntries.forEach(entry => {
          const match = entry.match(/([^:]+):\s*(.*)/);
          if (match) {
            result.methodologicalRecommendations.push({
              name: match[1].trim(),
              description: match[2].trim()
            });
          }
        });
      }
      
      // Extrahiere Forschungsfragen
      const questionsMatch = response.match(/KONKRETE FORSCHUNGSFRAGEN:\s*([\s\S]*?)(?=KOLLABORATIONSMÖGLICHKEITEN:|$)/);
      if (questionsMatch && questionsMatch[1]) {
        const questionsText = questionsMatch[1].trim();
        const questionEntries = questionsText.split(/\n-\s*/).filter(entry => entry.trim());
        
        questionEntries.forEach(entry => {
          const match = entry.match(/([^:]+):\s*(.*)/);
          if (match) {
            result.researchQuestions.push({
              question: match[1].trim(),
              description: match[2].trim()
            });
          }
        });
      }
      
      // Extrahiere Kollaborationsmöglichkeiten
      const collaborationsMatch = response.match(/KOLLABORATIONSMÖGLICHKEITEN:\s*([\s\S]*?)(?=ZUSAMMENFASSUNG:|$)/);
      if (collaborationsMatch && collaborationsMatch[1]) {
        const collaborationsText = collaborationsMatch[1].trim();
        const collaborationEntries = collaborationsText.split(/\n-\s*/).filter(entry => entry.trim());
        
        collaborationEntries.forEach(entry => {
          const match = entry.match(/([^:]+):\s*(.*)/);
          if (match) {
            result.collaborationOpportunities.push({
              name: match[1].trim(),
              description: match[2].trim()
            });
          }
        });
      }
      
      // Extrahiere Zusammenfassung
      const summaryMatch = response.match(/ZUSAMMENFASSUNG:\s*([\s\S]+)$/);
      if (summaryMatch && summaryMatch[1]) {
        result.summary = summaryMatch[1].trim();
      }
    } catch (error) {
      logger.error('ResearchLearningAgent: Fehler beim Parsen der Forschungsempfehlungen', {
        error: error.message,
        stack: error.stack
      });
    }
    
    return result;
  }
}

export default ResearchLearningAgent;