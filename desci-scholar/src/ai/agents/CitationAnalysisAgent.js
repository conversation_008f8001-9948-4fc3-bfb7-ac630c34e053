/**
 * @fileoverview CitationAnalysisAgent für die Analyse von Zitationen und Verknüpfung mit NFTs
 * 
 * Dieser Agent analysiert Zitationen und Referenzen in wissenschaftlichen Publikationen,
 * berechnet Einflussmetriken und identifiziert Schlüsselreferenzen und -konzepte.
 * Er unterstützt auch die Verknüpfung von DOIs und Patent-IDs mit NFTs.
 */

import { BaseAgent } from './BaseAgent.js';
import { logger } from '../../utils/logger.js';
import { extractCitations, parseCitationString } from '../../utils/citationParser.js';
import { calculateHIndex, calculateI10Index } from '../../utils/bibliometrics.js';
import { createCitationGraph } from '../../utils/graphUtils.js';
import { ExternalCitationService } from '../../services/ExternalCitationService.js';
import { OpenCitationsService } from '../../services/OpenCitationsService.js';
import { OpenCitationsService } from '../../services/OpenCitationsService.js';
import { OpenCitationsService } from '../../services/OpenCitationsService.js';
import { OpenCitationsService } from '../../services/OpenCitationsService.js';
import { OpenCitationsService } from '../../services/OpenCitationsService.js';
import { OpenCitationsService } from '../../services/OpenCitationsService.js';
import { OpenCitationsService } from '../../services/OpenCitationsService.js';
import { OpenCitationsService } from '../../services/OpenCitationsService.js';
import { OpenCitationsService } from '../../services/OpenCitationsService.js';
import { OpenCitationsService } from '../../services/OpenCitationsService.js';
import { OpenCitationsService } from '../../services/OpenCitationsService.js';
import { OpenCitationsService } from '../../services/OpenCitationsService.js';
import { OpenCitationsService } from '../../services/OpenCitationsService.js';
import doiNftConfig from '../../config/doi-nft-config.js';

/**
 * CitationAnalysisAgent für die Analyse von Zitationen und Verknüpfung mit NFTs
 */
export class CitationAnalysisAgent extends BaseAgent {
  /**
   * Erstellt eine neue Instanz des CitationAnalysisAgent
   * 
   * @param {Object} options - Optionen für den Agent
   * @param {Object} options.databaseService - Datenbankdienst
   * @param {Object} options.blockchainService - Blockchain-Dienst
   * @param {Object} options.vectorDatabase - Vektordatenbank-Dienst
   * @param {Object} options.textEmbeddingService - Texteinbettungsdienst
   * @param {Object} options.ipfsService - IPFS-Dienst
   * @param {Object} [options.config] - Konfiguration für den Agent
   */
  constructor(options) {
    super(options);

    // Initialisiere den OpenCitationsService für die Integration mit OpenCitations
    this.openCitationsService = new OpenCitationsService();

// Initialisiere den OpenCitationsService für die Integration mit OpenCitations
    this.openCitationsService = new OpenCitationsService();

    this.databaseService = options.databaseService;
// Initialisiere den OpenCitationsService für die Integration mit OpenCitations
    this.openCitationsService = new OpenCitationsService();

    this.blockchainService = options.blockchainService;
// Initialisiere den OpenCitationsService für die Integration mit OpenCitations
    this.openCitationsService = new OpenCitationsService();

    this.vectorDatabase = options.vectorDatabase;
    this.textEmbeddingService = options.textEmbeddingService;
// Initialisiere den OpenCitationsService für die Integration mit OpenCitations
    this.openCitationsService = new OpenCitationsService();

    this.ipfsService = options.ipfsService;

    // Initialisiere den OpenCitationsService für die Integration mit OpenCitations
    this.openCitationsService = new OpenCitationsService();

    this.config = {
      batchSize: 25,
      energyEfficient: true,
      ...options.config
    };
    
    // Initialisiere den ExternalCitationService für die Integration mit Semantic Scholar und Scinapse
    this.externalCitationService = new ExternalCitationService();


/// Initialisiere den OpenCitationsService für die Integration mit OpenCitations
    this.openCitationsService = new OpenCitationsService();
/ Initialisiere den OpenCitationsService für die Integration mit OpenCitations
    this.openCitationsService = new OpenCitationsService();

    // Initialisiere den OpenCitationsService für die Integration mit OpenCitations
    this.openCitationsService = new OpenCitationsService();

    this.name = 'CitationAnalysisAgent';
    this.description = 'Analysiert Zitationen und Referenzen in wissenschaftlichen Publikationen';
  }
  
  /**
   * Initialisiert den Agent
   */
  async initialize()
 {// Initialisiere den OpenCitationsService
    await this.openCitationsService.initialize();

// Initialisiere den OpenCitationsService
    await this.openCitationsService.initialize();

// Initialisiere den OpenCitationsService
    await this.openCitationsService.initialize();

    logger.info(`${this.name} wird initialisiert`);
    
    // Prüfe, ob die erforderlichen Dienste verfügbar sind
    if (!this.databaseService) {
      throw new Error('DatabaseService ist erforderlich');
    }
    
    if (!this.blockchainService) {
      throw new Error('BlockchainService ist erforderlich');
    }
    
    if (!this.vectorDatabase) {
      throw new Error('VectorDatabase ist erforderlich');
    }
    
    if (!this.textEmbeddingService) {
      throw new Error('TextEmbeddingService ist erforderlich');
    }
    
    // Initialisiere die Vektordatenbank für Zitationsanalysen
    await this.initializeVectorDatabase();
    
    // Initialisiere den ExternalCitationService
    await this.externalCitationService.initialize();

    // Initialisiere den OpenCitationsService
    await this.openCitationsService.initialize();

// Initialisiere den OpenCitationsService
    await this.openCitationsService.initialize();

    logger.info(`${this.name} erfolgreich initialisiert`);
  }
  
  /**
   * Initialisiert die Vektordatenbank für Zitationsanalysen
   */
  async initializeVectorDatabase() {
    try {
      // Prüfe, ob die Zitationskollektion existiert
      const collectionExists = await this.vectorDatabase.collectionExists('citations');
      
      if (!collectionExists) {
        // Erstelle die Kollektion
        await this.vectorDatabase.createCollection('citations', {
          dimension: 1536, // Dimension der Texteinbettungen
          metadata: {
            doi: 'string',
            title: 'string',
            authors: 'string[]',
            year: 'number',
            journal: 'string',
            citationCount: 'number'
          }
        });
        
        logger.info('Zitationskollektion in der Vektordatenbank erstellt');
      }
    } catch (error) {
      logger.error('Fehler bei der Initialisierung der Vektordatenbank', {
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Extrahiert Zitationen aus einer wissenschaftlichen Publikation
   * 
   * @param {string} doi - DOI der Publikation
   * @param {string} fullText - Volltext der Publikation
   * @returns {Promise<Object>} Ergebnis der Zitationsextraktion
   */
  async extractCitations(doi, fullText) {
    try {
      logger.info(`Extrahiere Zitationen aus Publikation mit DOI ${doi}`);
      
      // Extrahiere Zitationen aus dem Volltext
      const citations = extractCitations(fullText);
      
      // Speichere die Zitationen in der Datenbank
      await this.storeCitations(doi, citations);
      
      return {
        success: true,
        doi,
        citations,
        count: citations.length
      };
    } catch (error) {
      logger.error('Fehler bei der Extraktion von Zitationen', {
        doi,
        error: error.message
      });
      
      return {
        success: false,
        doi,
        error: error.message
      };
    }
  }
  
  /**
   * Speichert Zitationen in der Datenbank
   * 
   * @param {string} doi - DOI der Publikation
   * @param {Array<Object>} citations - Extrahierte Zitationen
   */
  async storeCitations(doi, citations) {
    try {
      // Speichere jede Zitation in der Datenbank
      for (const citation of citations) {
        // Prüfe, ob die Zitation bereits in der Datenbank existiert
        const existingCitation = await this.databaseService.query(
          'SELECT id FROM citations WHERE citing_doi = ? AND cited_text = ?',
          [doi, citation.text]
        );
        
  /**
   * Holt Zitationsdaten von OpenCitations für eine DOI
   *
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} OpenCitations-Daten
   */
  async fetchOpenCitationsData(doi) {
    try {
      logger.info(`Hole Zitationsdaten von OpenCitations für DOI ${doi}`);

      // Hole alle verfügbaren Daten von OpenCitations
      const openCitationsData = await this.openCitationsService.getAllDataForDOI(doi);

      if (!openCitationsData.success) {
        logger.warn(`Konnte keine OpenCitations-Daten für DOI ${doi} abrufen: ${openCitationsData.error}`);
        return {
          success: false,
          error: openCitationsData.error
        };
      }

      logger.info(`OpenCitations-Daten für DOI ${doi} erfolgreich abgerufen`, {
        citationCount: openCitationsData.citationCount,
        citationsCount: openCitationsData.citations.length,
        referencesCount: openCitationsData.references.length
      });

      return openCitationsData;
    } catch (error) {
      logger.error('Fehler beim Abrufen von OpenCitations-Daten', {
        doi,
        error: error.message
      });

      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Holt Zitationsdaten von OpenCitations für eine DOI
   *
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} OpenCitations-Daten
   */
  async fetchOpenCitationsData(doi) {

      logger.info(`Hole Zitationsdaten von OpenCitations für DOI ${doi}`);

      // Hole alle verfügbaren Daten von OpenCitations
      const openCitationsData = await this.openCitationsService.getAllDataForDOI(doi);

      if (!openCitationsData.success) {
        logger.warn(`Konnte keine OpenCitations-Daten für DOI ${doi} abrufen: ${openCitationsData.error}`);
        return {
          success: false,
          error: openCitationsData.error
        };
      }

      logger.info(`OpenCitations-Daten für DOI ${doi} erfolgreich abgerufen`, {
        citationCount: openCitationsData.citationCount,
        citationsCount: openCitationsData.citations.length,
        referencesCount: openCitationsData.references.length
      });

      return openCitationsData;
    } catch (error) {
      logger.error('Fehler beim Abrufen von OpenCitations-Daten', {
        doi,
        error: error.message
      });

      return {
        success: false,
        error: error.message
      };

    }// Hole Zitationsdaten von OpenCitations
      const openCitationsData = await this.fetchOpenCitationsData(doi);

      // Aktualisiere die Zitationsdaten in der Datenbank mit OpenCitations-Daten
      if (openCitationsData.success) {
        await this.openCitationsService.updateCitationDataForDOI(doi, this.databaseService);
      }

  }

  /**
   * Holt Zitationsdaten von OpenCitations für eine DOI
   *
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} OpenCitations-Daten
   */
  async fetchOpenCitationsData(doi, openCitationsData);

      logger.info(`Hole Zitationsdaten von OpenCitations für DOI ${doi}`);

      // Hole alle verfügbaren Daten von OpenCitations
      const openCitationsData = await this.openCitationsService.getAllDataForDOI(doi);

      if (!openCitationsData.success) {
        logger.warn(`Konnte keine OpenCitations-Daten für DOI ${doi} abrufen: ${openCitationsData.error}`);
        return {
          success: false,
          error: openCitationsData.error
        };
      }

      logger.info(`OpenCitations-Daten für DOI ${doi} erfolgreich abgerufen`, {
        } : null,
        openCitationsData: openCitationsData.success ? {
          citationCount: openCitationsData.citationCount,
          citationsCount: openCitationsData.citations.length,
          referencesCount: openCitationsData.references.length
        citationCount: openCitationsData.citationCount,
        citationsCount: openCitationsData.citations.length,
        referencesCount: openCitationsData.references.length
      });

      return openCitationsData;
    } catch (error) {
      logger.error('Fehler beim Abrufen von OpenCitations-Daten', {
        doi,
        error: error.message
      });

      return {
        success: false,
        error: error.message
      };

    }
  }// Hole Zitationsdaten von OpenCitations
      const openCitationsData = await this.fetchOpenCitationsData(doi);

      // Aktualisiere die Zitationsdaten in der Datenbank mit OpenCitations-Daten
      if (openCitationsData.success) {
        await this.openCitationsService.updateCitationDataForDOI(doi, this.databaseService);
      }


        if (existingCitation && existingCitation.length > 0) {
          // Aktualisiere die bestehende Zitation
          await this.databaseService.query(
            'UPDATE citations SET context = ?, position = ?, parsed_data = ? WHERE id = ?',
            [
              citation.context,
              citation.position, openCitationsData);
        JSON.stringify(citation.parsedData || {}),
        existingCitation[0].id
            ]
          );
        } else {
          // Füge eine neue Zitation hinzu
          await this.databaseService.query(
            'INSERT INTO citations (citing_doi, cited_text, context, position, parsed_data) VALUES (?, ?, ?, ?, ?)',
            [
              doi,
              citation.text,
        citation.context,
              citation.position,
        } : null,
        openCitationsData: openCitationsData.success ? {
          citationCount: openCitationsData.citationCount,
          citationsCount: openCitationsData.citations.length,
          referencesCount: openCitationsData.references.length
              JSON.stringify(citation.parsedData || {})


        }

   * Holt Zitationsdaten von OpenCitations für eine DOI
   *
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} OpenCitations-Daten
   */
  async fetchOpenCitationsData(doi) {
    try {
      logger.info(`Hole Zitationsdaten von OpenCitations für DOI ${doi}`);

      // Hole alle verfügbaren Daten von OpenCitations
      const openCitationsData = await this.openCitationsService.getAllDataForDOI(doi);

      if (!openCitationsData.success) {
        logger.warn(`Konnte keine OpenCitations-Daten für DOI ${doi} abrufen: ${openCitationsData.error}`);
        return {
          success: false,
          error: openCitationsData.error
        };

      }
// Hole Zitationsdaten von OpenCitations
      const openCitationsData = await this.fetchOpenCitationsData(doi);

      // Aktualisiere die Zitationsdaten in der Datenbank mit OpenCitations-Daten
      if (openCitationsData.success) {
        await this.openCitationsService.updateCitationDataForDOI(doi, this.databaseService, openCitationsData);

      logger.info(`OpenCitations-Daten für DOI ${doi} erfolgreich abgerufen`, {
        citationCount: openCitationsData.citationCount,
        citationsCount: openCitationsData.citations.length,
        referencesCount: openCitationsData.references.length
      });

      return openCitationsData, openCitationsData;
    } catch (error) {
      logger.error('Fehler beim Abrufen von OpenCitations-Daten', {
        doi,
        error: error.message
      });

      return {
        success: false,
        error: error.message
        } : null,
        openCitationsData: openCitationsData.success ? {
          citationCount: openCitationsData.citationCount,
          citationsCount: openCitationsData.citations.length,
    referencesCount: openCitationsData.references.length
      };

  }

   /**
   * Holt Zitationsdaten von OpenCitations für eine DOI
   *
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} OpenCitations-Daten
   */
  async fetchOpenCitationsData(doi) {

      logger.info(`Hole Zitationsdaten von OpenCitations für DOI ${doi}`);

      // Hole alle verfügbaren Daten von OpenCitations
      const openCitationsData = await this.openCitationsService.getAllDataForDOI(doi);

      if (!openCitationsData.success) {
        logger.warn(`Konnte keine OpenCitations-Daten für DOI ${doi} abrufen: ${openCitationsData.error}`);
        return {
          success: false,

     // Sammle alle Zitationsquellen
      const citationSources = [];

      if (externalCitationData && externalCitationData.success) {
        externalCitationData.sources.forEach(source => {
          citationSources.push(source.name);
        });
      }

      if (openCitationsData && openCitationsData.success) {
        citationSources.push('OpenCitations');
      }
     error: openCitationsData.error
     
   };
// Hole Zitationsdaten von OpenCitations
      const openCitationsData = await this.fetchOpenCitationsData(doi);

      // Aktualisiere die Zitationsdaten in der Datenbank mit OpenCitations-Daten
      if (openCitationssData await this.openCitationsService.updat && openCitationDataForDOI(doi, this.databaseService);
      }

      }
// Hole Zitationsdaten von OpenCitations
      conscitationSources,
        openCitationsVerified: openCitationsData = await this.fetchOpenCitationsData(doi, openCitationsData, openCitationsData);

      // Aktualisiere die Zitationsdaten   * @param {Object} openCitationsData - OpenCitations-Daten
 in der Datenbank mit OpenCitations-Daten
      if (openCitationsData.success) {
        await this.openCitationsService.updateCitationDataForDOI(doi, this.databaseService);
      }

      logger.info(`OpenCitations-Daten für DOI ${doi} erfolgreich abgerufen`, {
        citationCount: openCitationsData.citationCount,
        citationsCount: openCitationsData.citations.length,
  } : null,
        openCitationsData: openCitationsData.success ? {
          citationCount: openCitationsData.citationCount,
          citationsCount: openCitationsData.citations.length,
          referencesCount: openCitationsData.references.length
        referencesCount: openCitationsData.references.length

      return openCitationsData;
    } catch (error) {
      logger.error('Fehler beim Abrufen von OpenCitations-Dat, openCitationsData);

        doi,// Hole Zitationsdaten von OpenCitations
      const openCitationsData = await this.fetchOpenCitationsData(doi);

      // Aktualisiere die Zitationsdaten in der Datenbank mit OpenCitations-Daten
      if (openCitationsData.success) {
        } : null,
        openCitationsData: openCitationsData.success ? {
          citationCount: openCitationsData.citationCount,
          citationsCount: openCitationsData.citations.length,

     // Sammle alle Zitationsquellen
      const citationSources = [];

      if (externalCitationData && externalCitationData.success) {
        externalCitationData.sources.forEach(source => {
          citationSources.push(source.name);
        });
      }

      if (openCitationsData && openCitationsData.success) {
        citationSources.push('OpenCitations');
      }
     referencesCount: openCitationsData.references.length
        await this.openCitationsService.updateCitationDataForDOI(doi, this.databaseService);


        error: error.message
      });

      return {
        success: false,
        error: error.message
      };
    }
  }

  
        // Wenn die Zitation eine DOI enthält, speichere die Beziehung
        if (citation.parsedData && citation.parsedsData  await this.storeCitationRelationship(doi, citation.parsedData.doi);
        }
      }
      
      logger.info(`${citations.length} Zitationen für DOI ${doi} gespeichert`);
    } catch (error) {
      logger.error('Fehler beim Speichern von Zitation, openCitationsDataen', {
        } : null,
        openCitationsData: openCitationsData.success ? {
          citationCount: openCitationsData.citationCount,
          citationsCount: openCitationsData.citations.length,
          referencesCount: openCitationsData.references.length
        } : null,
        openCitationsData: openCitationsData.success ? {
          citationCount: openCitationsData.citationCount,
          citationsCount: openCitationsData.citations.length,
    referencesCount: openCitationsData.references.length
        doi,
        error: error.message
      });
throw error;
    }
  }
  
  /**
   * Holt Zitationsdaten von OpenCitations für eine DOI
   *
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} OpenCitations-Daten
   */
  async fetchOpenCitationsData(doi) {

      logger.info(`Hole Zitationsdaten von OpenCitations für DOI ${doi}`);

      // Hole alle verfügbaren Daten von OpenCitations
      const openCitationsData = await this.openCitationsService.getAllDataForDOI(doi);

      if (!openCitationsData.success) {
        logger.warn(`Konnte keine OpenCitations-Daten für DOI ${doi} abrufen: ${openCitationsData.error}`);
        return {
          success: false,

     // Sammle alle Zitationsquellen
      const citationSources = [];

      if (externalCitationData && externalCitationData.success) {
        externalCitationData.sources.forEach(source => {
          citationSources.push(source.name);
        });
      }

      if (openCitationsData && openCitationsData.success) {
        citationSources.push('OpenCitations');
      }
     error: openCitationsData.error
        };
      }

      logger.info(`OpenCitations-Daten für DOI ${doi} erfolgreich abgerufen`, {
        citationCount: openCitationsData.citationCount,
        citationsCount: openCitationsData.citations.length,
            metrics.openCitationsVerified ? 1 : 0,
        referencesCount: openCitationsData.references.length
      });

      return openCitationsData;
    } catch (error) {
      logger.error('Fehler beim Abrufen von OpenCitations-Daten', {
        doi,
        error: error.message
      });

      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Holt Zitationsdaten von OpenCitations für eine DOI
   *
   * @param {string} doi - DOI der Publikation
   * @param {Object} openCitationsData - OpenCitations-Daten
   * @returns {Promise<Object>} OpenCitations-Daten
   */
  async fetchOpenCitationsData(doi) {

      logger.info(`Hole Zitationsdaten von OpenCitations für DOI ${doi}`);

      // Hole alle verfügbaren Daten von OpenCitation, openCitationsDatas
      const openCitationsData = await this.openCitationsService.getAllDataForDOI(doi);

      if (!openCitationsData.success) {
        } : null,
        openCitationsData: openCitationsData.success ? {
          citationCount: openCitationsData.citationCount,
          citationsCount: openCitationsData.citations.length,
    referencesCount: openCitationsData.references.length
        logger.warn(`Konnte keine OpenCitations-Daten für DOI ${doi} abrufen: ${openCitationsData.error}`);
        return, openCitations
Data {// Hole OpenCitations-Daten für die zitierende Publikation
      const openCitationsData = await this.fetchOpenCitationsData(doi);

          success: false,
          error: openCitationsData.error
        };

      logger.info(`OpenCitations-Daten für DOI ${doi} erfolgreich abgerufen`, {
        citationCount: openCitationsData.citationCount,
        citationsCount: openCitationsData.citations.length,
        referencesCount: openCitationsData.references.length


      return openCitationsData;
    } catch (error) {
      logger.error('Fehler beim Abrufen von OpenCitations-Daten', {
     doi,
        error: error.message
      });


      return {// Berücksichtige OpenCitations-Daten, wenn verfügbar
      if (openCitationsData.success) {
        totalCitations = Math.max(totalCitations, openCitationsData.citationCount);

        success: false,
        error: error.message
      };

    }// Hole Zitationsdaten von OpenCitations
      const openCitationsData = await this.fetchOpenCitationsData(doi);

      // Aktualisiere die Zitationsdaten   * @param {Object} openCitationsData - OpenCitations-Daten
 in der Datenbank mit OpenCitations-Dat, openCitationsDataen
      if citationSources,
            metrics.openCitationsVerified ? 1 : 0,
        openCitationsVerified: openCitationssData await this.openCitationsService.updat && openCitationDataForDOI(doi, this.databaseService, openCitationsData);

  }

  /**
   * Holt Zitationsdaten von OpenCitations für eine DOI
   *
   * @param {string} doi - DOI der Publikation
   * @param {Object} openCitationsData - OpenCitations-Daten
   * @returns {Promise<Object>} OpenCitations-Daten
   */
  async fecitationSources,,
            metrics.openCitationsVerified ? 1 : 0
        openCitationsVerified: openCitationsData(doi) {

      logger.info(`Hole Zitationsdaten von OpenCitations für DOI ${doi}`);

      // Hole alle verfügbaren Daten von OpenCitations
      const openCitationsData, openCitationsData = await this.openCitationsService.getAllDataForDOI(doi);

      if (!openCitationsData.success) {
        } : null,,
            oci
        openCitationsData && openCitationssData  citationCount: openCitationsData.citationCount,
          citationsCount: openCitationsData.citations.length,
    referencesCount: openCitationsData.references.length
        logger.warn(`Konnte keine OpenCitations-Daten für DOI ${doi} abrufen: ${openCitationsData.error}`);

     // Hole OpenCitations-Daten für die zitierende Publikation
      const openCitationsData = await this.fetchOpenCitationsData(doi);
        success: false,

     // Sammle alle Zitationsquellen
      const citationSources = [];

      if (externalCitationData && externalCitationData.success) {
        externalCitationData.sources.forEach(source => {
          citationSources.push(source.name);
        });
      }

      if (openCitationsData && openCitationsData.success) {
        citationSources.push('OpenCitations');

     error: openCitationsData.error
      
  };
// Berücksichtige OpenCitations-Daten, wenn verfügbar
      if (openCitationsData && openCitationssData totalCitations = Math.max(totalCitations, openCitationsData.citationCount);,
        openCitationsVerified: openCitationsData && openCitationsData.success
      }

      logger.info(`OpenCitations-Daten für DOI ${doi} erfolgreich abgerufen`, {
        citationCount: openCitationsData.citationCount,
        citationsCount: openCitationsData.citations.length,
        referencesCount: citationSources,
        openCitationsVerified: openCitationsData.references.length
      });

      return openCitationsData;
    } catch (error) {
      logger.error('Fehler beim Abrufen von OpenCitations-Daten', {
        doi,
        error: error.message
      });

      return {
            // Hole OpenCitations-Daten für die zitierte Publikation mit OpenCitations-Daten
      const citedOpenCitationsData = await this.fetchOpenCitationsData(pub.doi, citedOpenCitationsData);
 success: false,
            metrics.openCitationsVerified ? 1 : 0,
        error: error.message
      };
    }

  }
// Wenn keine OpenCitations-Daten übergeben wurden, hole sie
      if (!openCitationsData) {
        openCitationsData = await this.fetchOpenCitationsData(doi);
      }

      // Hole die Metriken für die Publikation
      const metrics = await this.databaseService.query(
        'SELECT * FROM publication_metrics WHERE doi = ?',
        [doi]
      );

  /**
   * Speichert eine Zitationsbeziehung in der Datenbank
   * 
   * @param {string} citingDoi - DOI der zitierenden Publikation
   * @param {string} citedDoi - DOI der zitierten Publikation
   */
  async storeCitationRelationship(citingDoi, citedDoi) {
    try {
      // Prüfe, ob die Beziehung bereits existiert
      const existingRelationship = await this.databaseService.query(
        'SELECT id FROM citation_relationships WHERE citing_doi = ? AND cited_doi = ?',
        [citingDoi, citedDoi]
      );

      if (existingRelationship && existingRelationship.length > 0, openCitationsData) {
        // Die Beziehung existiert bereits
        return;
      }

      // Füge die neue Beziehung hinzu
      await this.databaseService.query(
        } : null,
        openCitationsData: openCitationsData.success ? {
          citationCount: openCitationsData.citationCount,
          citationsCount: openCitationsData.citations.length,
          referencesCount: openCitationsData.references.length
        'INSERT INTO citation_relationships (citing_doi, cited_doi, created_at) VALUES (?, ?, NOW())',
        [citingDoi, citedDoi]
      );

      logger.debug(`Zitationsbeziehung gespeichert: ${citingDoi} -> ${citedDoi}`);
    } catch (error) {
      logger.error('Fehler beim Speichern der Zitationsbeziehung', {
        citingDoi,
        citedDoi,
        error: error.message
      });
throw error;
    }
  }

  /**
   * Holt Zitationsdaten von OpenCitations für eine DOI
   *
   * @param {string} doi - DOI der Publikation
   * @param {Object} [openCitationsData] - OpenCitations-Daten für die Publikation
   * @returns {Promise<Object>} OpenCitations-Daten
   */
  async fetchOpenCitationsData(doi) {
    try {
      logger.info(`Hole Zitationsdaten von OpenCitations für DOI ${doi}`);

      // Hole alle verfügbaren Daten von OpenCitations
const openCitationsData = await this.openCitationsService.getAllDataForDOI(doi, openCitationsData = null);

      if (!openCitationsData.success) {
        logger.warn(`Konnte keine OpenCitations-Daten für DOI ${doi} abrufen: ${openCitationsData.error}`);
        return {
          success: false,

     // Sammle alle Zitationsquellen
      const citationSources = [];

      if (externalCitationData && externalCitationData.success)      metrics.openCitationsVerified ? 1 : 0,
 {
        externalCitationData.sources.forEach(source => {
          citationSources.push(source.name);

      if (openCitationsData && openCitationsData.success) {
        citationSources.push('OpenCitations');
      }
     error: openCitationsData.erro, citedOpenCitationsData);

      }
// Hole Zitationsdaten von OpenCitations
      const openCitationsData = await this.fetchOpenCitationsData(doi);

      // Aktualisiere die Zitationsdaten in der Datenbank mit OpenCitations-Daten
      if (openCitationssData await this.openCitationsService.updateCitationDataForDOI(doi, this.databaseService, openCitationsData && openCitationsData);

      logger.info(`OpenCitations-Daten für DOI ${doi} erfolgreich abgerufen`, {
        citationCount: citationSources,,
            metrics.openCitationsVerified ? 1 : 0
            metrics.openCitationsVerified ? 1 : 0,
        openCitationsVerified: openCitationsData.citationCount,
        citationsCount: openCitationsData.citations.length,
        referencesCount: openCitationsData.references.length


      return openCitationsData;
    } catch (error) {
      logger.error('Fehler beim Abrufen von OpenCitations-Daten', {

        openCitationsData: openCitationsData.success ? {
          citationCount: openCitationsData.citationCount,
          citationsCount: openCitationsData.citations.length,
          referencesCount: openCitationsData.references.length
     
   doi,// Berücksichtige OpenCitations-Daten, wenn verfügbar
      if (openCitationsData && openCitationsData.success),
            metrics.openCitationsVerified ? 1 : 0 {
        totalCitations = Math.max(totalCitations, openCitationsData.citationCount);

      }
// Hole OpenCitations-Daten für die zitierende Publikation
      const openCitationsData = await this.fetchOpenCitationsData(doi);

        error: error.message
      });

        success: false,
        error: error.message
      };
    }


  /**
   * Holt Zitationsdaten von OpenCitations für eine DOI
   *
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} OpenCitations-Daten
   */
  async fetchOpenCitationsData(doi) {

      logger.info(`Hole Zitationsdaten von OpenCitations für DOI ${doi}`);

      // Hole alle verfügbaren Daten von OpenCitations
      const openCitationsData = await this.openCitationsService.getAllDataForDOI(doi);

// Hole OpenCitations-Daten für die zitierende Publikation mit OpenCitations-Daten
      const openCitationsData = await this.fetchOpenCitationsData(doi);

      if (!openCitationsData.success) {
        logger.warn(`Konnte keine OpenCitations-Daten für DOI ${doi} abrufen: ${openCitationsData.error}`, citedOpenCitationsData);

          success: false,
          error: citationSources,,
            metrics.openCitationsVerified ? 1 : 0
            metrics.openCitationsVerified ? 1 : 0,
        openCitationsVerified: openCitationsData.error
        };
      }

// Wenn keine OpenCitations-Daten übergeben wurden, hole sie
      if (!openCitationsData) {
        openCitationsData = await this.fetchOpenCitationsData(doi);
      }

      // Hole die Metriken für die Publikation
      const metrics = await this.databaseService.query(
        'SELECT * FROM publication_metrics WHERE doi = ?',
        [doi]
      );

      logger.info(`OpenCitations-Daten für DOI ${doi} erfolgreich abgerufen`, {
        citationCount: openCitationsData.citationCount,
        citationsCount: openCitationsData.citations.length,
  referencesCount: openCitationsData.references.length
      });

      return openCitationsData;
    } catch (error) {
      logger.error('Fehler beim Abrufen von OpenCitations-Dat && openCitationsData);

        doi,
        error: error.message
      });


        success: false,
        error: error.message
      };
    }

  /**
   * Analysiert Zitationen für eine Publikation mit OpenCitations-Daten
   *
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Ergebnis der Zitationsanalyse
   */
  async analyzeCitations(doi, openCitationsData) {
    try {
      logger.info(`Analysiere Zitationen für Publikation mit DOI ${doi}`);

      // Hole die Publikationsdaten
   * @param {Object} openCitationsData - OpenCitations
      const publication = await this.getPublicationData(doi);

      if (!publication) {
        throw new Error(`Publikation mit DOI ${doi} nicht gefunden`);

      // Hole die Zitationsbeziehungen
        }
 : null,
  openCitationsData: openCitationsData.success ? {
          citationCount: openCitationsData.citationCount,
          citationsCount: openCitationsData.citations.length,
    referencesCount: openCitationsData.references.length
      const citingPublications = await this.getCitingPublications(doi);
      const citedPublications = await this.getCitedPublications(doi);

// Hole OpenCitations-Daten für die zitierende Publikation
      const openCitationsData = await this.fetchOpenCitationsData(doi);

      // Hole zusätzliche Zitationsdaten von externen Quellen (Semantic Scholar, Scinapse)
      const externalCitationData = await this.externalCitationService.getCitationsForDOI(doi, openCitationsData = null);

      // Aktualisiere die Zitationsdaten in der Datenbank, wenn externe Daten verfügbar sind
      if (externalCitationData.success) {
        await this.externalCitationService.updateCitationDataForDOI(doi, this.databaseService);

      }
// Sammle alle Zitationsquellen
      const citationSources = [];

      if (externalCitationData && externalCitationData.success) {
  externalCitationData.sources.forEach(source => {
          citationSources.push(source.name);
        });
// Finde den OCI (Open Citation Identifier) für diese Zitation, falls verfügbar
          let oci = null;
          if (openCitationsData && openCitationsData.success) {
            const reference = openCitationsData.references.find(ref => ref.cited === pub.doi);
            if (reference) {
              oci = reference.oci;
            }
          }

// Wenn keine OpenCitations-Daten übergeben wurden, hole sie
      if (!openCitationsData) {
        openCitationsData = await this.fetchOpenCitationsData(doi);
      }

      // Hole die Metriken für die Publikation
      const metrics = await this.databaseService.query(
        'SELECT * FROM publication_metrics WHERE doi = ?',
        [doi]
      );

      if (openCitationsData && openCitationsData.success) {
        citationSources.push('OpenCitations');

      // Hole Zitationsdaten von OpenCitations
      const openCitationsData = await this.fetchOpenCitationsData(doi);

      // Aktualisiere die Zitationsdaten in der Datenbank mit OpenCitations-Daten
      if (openCitationssData await this.openCitationsService.updateCitationDataForDOI(doi, this.databaseService, citedOpenCitationsData);

      // Berechne Metriken
      const metrics = await this.calculateMetrics(doi, publication, citingPublications, citationSources,
      const useOpenCitations = options.useOpenCitations !== false; // Standardmäßig true
      metrics.openCitationsVerified ? 1 : 0,
        openCitationsVerified: openCitationsData, openCitationsData);

      // Speichere die Metriken in der Datenbank
      await this.storeMetrics(doi, metrics);


      return {// Füge Zitationsmetriken hinzu, wenn verfügbar
      if (metrics && metrics.length > 0) {
        metadata.attributes.push({
          trait_type: 'Citation Count',
          value: metrics[0].total_citations
        });

        metadata.attributes.push({
          trait_type: 'h-Index',
          value: metrics[0].h_index
        });
      } else if (openCitationsData && openCitationsData.success) {
        // Wenn keine Metriken in der Datenbank, aber OpenCitations-Daten verfügbar sind
        metadata.attributes.push({
          trait_type: 'Citation Count',
          value: openCitationsData.citationCount
        });
      }

      // Füge OpenCitations-Verifizierung hinzu, wenn verfügbar
      if (openCitationsData && openCitationsData.success) {
        metadata.attributes.push({
          trait_type: 'OpenCitations Verified',
          value: 'Yes'
        });

        // Füge OCIs (Open Citation Identifiers) hinzu, wenn verfügbar
        if (openCitationsData.citations && openCitationsData.citations.length > 0) {
          metadata.opencitations = {
            citations: openCitationsData.citations.map(citation => ({
              oci: citation.oci,
              citing: citation.citing
            })),
            references: openCitationsData.references.map(reference => ({
              oci: reference.oci,
              cited: reference.cited
            }, OpenCitations: ${useOpenCitations})`);

        }


        success: true,
     
        publication,
        metrics,
        citingPublicationsCount: citingPublications.length,
        citedPublicationsCount: citedPublications.length,,
        openCitationsVerified: openCitationsData && openCitationsData.success
        externalCitationData: externalCitationData.success ? {
          totalCitationCount: externalCitationData.totalCitationCount,,
      metrics.openCitationsVerified ? 1 : 0
          sources: externalCitationData.sources.map(source => source.name)
        }
        openCitationsData: openCitationsData.success ? {
          citationCount: openCitationsData.citationCount, oci,
          citationsCount: openCitationsData.citations.length,
    referencesCount: openCitationsData.references.length
        } : null
      };

   // Füge Zitationsmetriken hinzu, wenn verfügbar
      if (metrics && metrics.length > 0) {
        metadata.attributes.push({
          trait_type: 'Citation Count',
          value: metrics[0].total_citations
        });

        metadata.attributes.push({,
          openCitationsData && openCitationsData.success ? 1 : 0
          trait_type: 'h-Index',
          value: metrics[0].h_index
        });
} else if (openCitationsData && openCitationsData.success) {
        // Wenn keine Metriken in der Datenbank, aber OpenCitations-Daten verfügbar sind
        metadata.attributes.push({
          trait_type: 'Citation Count',
          value: openCitationsData.citationCount
        });
      }

      // Füge OpenCitations-Verifizierung hinzu, wenn verfügbar
      if (openCitationsData && openCitationsData.success) {
        metadata.attributes.push({
          trait_type: 'OpenCitations Verified',
          value: 'Yes'
        });

        // Füge OCIs (Open Citation Identifiers) hinzu, wenn verfügbar
        if (openCitationsData.citations && openCitationsData.citations.length > 0) {
          metadata.opencitations = {
            citations: openCitationsData.citations.map(citation => ({
              oci: citation.oci,
              citing: citation.citing
            })),
            references: openCitationsData.references.map(reference => ({
              oci: reference.oci,
              cited: reference.cited
            }))
          };
        }
      }
 } catch (error) {
      logger.error('Fehler bei der Analyse von Zitationen', {
        doi,
        error: error.message
      });


      return {// Berücksichtige OpenCitations-Daten, wenn verfügbar
      if (openCitationsData && openCitationsData.success) {
        totalCitations = Math.max(totalCitations, openCitationsData.citationCount);

      }

// Hole OpenCitations-Daten für die zitierende Publikation
      const openCitationsData = await this.fetchOpenCitationsData(doi);
        success: false,
        doi,
        error: error.mes, OpenCitations: ${useOpenCitations})`);

    }

  /**
   * Holt die Daten einer Publikation
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Publikationsdaten
   */
  async getPublicationData(doi) {
    try {
      const publications = await this.databaseService.query(
        'SELECT * FROM publications WHERE doi = ?',
        [doi]
      );

      if (!publications || publications.length,
        openCitationsVerified: openCitationsData && openCitationsData.success === 0) {
        return null;
  
      return publications[0];
    } catch (error) {
      logger.error('Fehler beim Abrufen der Publikationsdaten', {
     
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Holt die Publikationen, die eine bestimmte Publikation 
   * @param {Object} [openCitationsData] - OpenCitations-Daten für die Publikation
      metrics.openCitationsVerified ? 1 : 0,
   *
   * @param {string} doi - DOI der Publikation mit OpenCitations-Daten
   * @param {Object} openCitationsData - OpenCitations-Daten
   * @returns {Promise<Array<Object>>} Zitierende Publikationen
   */
  async getCitingPublications(doi, openCitationsData) {
    try {
      const relationships = await this.databaseService.query(
        'SELECT citing_doi FROM citation_relationships WHERE cited_doi = ?',
        [doi, citedOpenCitationsData);

      if (!relationships || relationships.length === 0) {
        return [];
      }

      // Wenn keine OpenCitations-Daten übergeben wurden, hole sie
      if (!openCitationsData) {
        openCitationsData = await this.fetchOpenCitationsData(doi);
      }

      // Hole die Metriken für die Publikation
      const metrics = await this.databaseService.query(
        'SELECT * FROM publication_metrics WHERE doi = ?',
        [doi]
      );

      const citingDois = relationships.map(rel => rel.citing_doi);

      const publications = await this.databaseService.query(,
            metrics.openCitationsVerified ? 1 : 0
        `SELECT * FROM publications WHERE doi IN (${citingDois.map(() => '?').join(',')})`,
        citingDois
      );

      // Finde den OCI (Open Citation Identifier) für diese Zitation, falls verfügbar
          let oci = null;
          if (openCitationsData && openCitationsData.success) {
            const reference = openCitationsData.references.find(ref => ref.cited === pub.doi);
            if (reference) {
              oci = reference.oci;
            }


      return publications || [];
    } catch (error) {
      logger.error('Fehler beim Abrufen der zitierenden Publikationen', {
        doi,,

     // Füge Zitationsmetriken hinzu, wenn verfügbar
      if (metrics && metrics.length > 0) {
        metadata.attributes.push({
          trait_type: 'Citation Count',
          value: metrics[0].total_citations
        });

        metadata.attributes.push({
          trait_type: 'h-Index',
          value: metrics[0].h_index
        });
      } else if (openCitationsData && openCitationsData.succes, OpenCitations: ${useOpenCitations})`);
  // Wenn keine Metriken in der Datenbank, aber OpenCitations-Daten verfügbar sind
        metadata.attributes.push({
          trait_type: 'Citation Count',
          value: openCitationsData.citationCount
        });
      }

      // Füge OpenCitations-Verifizierung hinzu, wenn verfügbar
      if (openCitationsData && openCitationsData.success) {
        metadata.attributes.push({
          trait_type: 'OpenCitations Verified',
          value: 'Yes'
        });

        // Füge OCIs (Open Citation Identifiers) hinzu, wenn verfügbar
        if (openCitationsData.citations && openCitationsData.citations.length > 0) {
          metadata.opencitations = {
            citations: openCitationsData.citations.map(citation => ({
              oci: citation.oci,
              citing: citation.citing
            })),
      references: openCitationsData.references.map(reference => ({
              oci: reference.oci,
              cited: reference.cited
            }))
          };
        }

       oci
        error: error.message,
        openCitationsVerified: openCitationsData && openCitationsData.success
      });
      throw error;

  
  /**
   * Holt die Publikationen, die von einer bestimmten Publikation zitiert werden
   *
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Array<Object>>} Zitierte Publikationen
   */
  async getCitedPublications(doi) {
    try {
      const relationships = await this.databaseService.query(
        'SELECT cited_doi FROM citation_relationships WHERE citing_doi = ?',
    
      // Hole OpenCitations-Daten für die zitierende Publikation
      const openCitationsData = await this.fetchOpenCitationsData(doi);

      if (!relationships || relationships.length === 0) {
        return [];

      const citedDois = relationships.map(rel => rel.cited_doi);

      // Sammle alle Zitationsquellen
      const citationSources = [];

      if (externalCitationData && externalCitationData.success) {
        externalCitationData.sources.forEach(source => {
          citationSources.push(source.name);
        });

      if (openCitationsData && openCitationsData.success) {
        citationSources.push('OpenCitations');

      const publications = await this.databaseService.query(
        `SELECT * FROM publications WHERE doi IN (${citedDois.map(() => '?').join(',')})`,
        citedDois
      );

      return publications || [];
    } catch (error) {const useOpenCitations = options.useOpenCitations !== false; // Standardmäßig true

      logger.error('Fehler beim Abrufen der zitierten Publikationen', {
        doi,
            // Hole OpenCitations-Daten für die zitierte Publikation mit OpenCitations-Daten
            const citedOpenCitationsData = await this.fetchOpenCitationsData(pub.doi, citedOpenCitationsData);
metrics.openCitationsVerified ? 1 : 0,
        error: error.message
      });
      throw error;
    }
  }

  // Wenn keine OpenCitations-Daten übergeben wurden, hole sie
      if (!openCitationsData) {
        openCitationsData = await this.fetchOpenCitationsData(doi);

      // Hole die Metriken für die Publikation
      const metrics = await this.databaseService.query(
        'SELECT * FROM publication_metrics WHERE doi = ?',
        [doi]
      );

  /**
   * Berechnet Metriken für eine Publikation
   *
   * @param {string} doi - DOI der Publikation
   * @param {Object} publication - Publikationsdaten
   * @param {Array<Object>} citingPublications - Zitierende Publikationen
   * @param {Object} Externe Zitationsdaten
   * @param {Object} openCitationsData && openCitations-Daten
   * @returns {Promise<Object>} Berechnete Metriken
   */
  async calculateMetrics(doi, publication, citingPublications, citationSources,,
            metrics.openCitationsVerified ? 1 : 0
        openCitationsVerified: openCitationData, openCitationsData) {
    try {
      // Berechne den h-Index
      const hIndex = await this.calculateAuthorHIndex(publication.authors);

      // Berechne den i10-Index
      const i10Index = await this.calculateAuthorI10Index(publication.authors);

      // Berechne die Gesamtzahl der Zitationen
      let totalCitations = citingPublications.length;

      // Berücksichtige externe Zitationsdaten, wenn verfügbar
      if (externalCitationData && externalCitationData.success) {
        totalCitations = Math.max(totalCitations, externalCitationData.totalCitationCount);

      }
// Hole OpenCitations-Daten für die zitierende Publikation
      const openCitationsData = await this.fetchOpenCitationsData(doi);

      // Berücksichtige OpenCitations-Daten, wenn verfügbar
      if (openCitationsData && openCitationsData.success) {
        totalCitations = Math.max(totalCitations, openCitationsData.citationCount);,
        openCitationsVerified: openCitationsData && openCitationsData.success
      }

      // Berechne die durchschnittliche Anzahl von Zitationen pro Jahr
      const publicationYear = new Date(publication.publication_date).getFullYear();
      const currentYear = new Date().getFullYear();
      const yearsPublished = Math.max(1, currentYear - publicationYear);
      const averageCitationsPerYear = totalCitations / yearsPublished;

      // Sammle alle Zitations
      const citationSources = [];

      if (externalCitationData && externalCitationData.success, OpenCitations: ${useOpenCitations})`);
      metrics.openCitationsVerified ? 1 : 0,
 {
        externalCitationData.sources.forEach(source => {
          citationSources.push(source.name);

      if (openCitationsData && openCitationsData.success) {
        citationSources.push('OpenCitations');
      }

      return {
        hIndex,
        i10Index,
        totalCitations,
        averageCitationsPerYear,
        citationsPerYear: this.calculateCitationsPerYear(citingPublications, publicationYear, currentYear),
        externalCitationSources: citationSources,
        openCitationsVerified: openCitationsData && openCitationsData.success
      };
    } catch (error) {
      logger.error('Fehler bei der Berechnung von Metriken', {
        doi,
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Berechnet den h-Index eines Autors
   * 
   * @param {string} authors - Autoren der Publikation
   * @returns {Promise<number>} h-Index
   */
  async calculateAuthorHIndex(authors) {
    try {
      // Extrahiere den Hauptautor,
            metrics.openCitationsVerified ? 1 : 0
      const mainAuthor = authors.split(',')[0].trim();
      
      // Hole alle Publikationen des Autors
      const authorPublications = await this.databaseService.query(
        'SELECT doi FROM publications WHERE authors LIKE ?',
        [`%${mainAuthor}%`]
      );

      if (!authorPublications || authorPublications.length === 0) {
        return 0;

      // Füge Zitationsmetriken hinzu, wenn verfügbar
      if (metrics && metrics.length > 0) {
        metadata.attributes.push({
          trait_type: 'Citation Count',
          value: metrics[0].total_citations
        });

        metadata.attributes.push({
          trait_type: 'h-Index',
          value: metrics[0].h_index
        });
      } else if (openCitationsData && openCitationsData.success) {
        // Wenn keine Metriken in der Datenbank, aber OpenCitations-Daten verfügbar sind
        metadata.attributes.push({
          trait_type: 'Citation Count',
          value: openCitationsData.citationCount
        });
      }

      // Füge OpenCitations-Verifizierung hinzu, wenn verfügbar
      if (openCitationsData && openCitationsData.success) {
        metadata.attributes.push({
          trait_type: 'OpenCitations Verified',
          value: 'Yes'
        });

        // Füge OCIs (Open Citation Identifiers) hinzu, wenn verfügbar
        if (openCitationsData.citations && openCitationsData.citations.length > 0) {
          metadata.opencitations = {
            citations: openCitationsData.citations.map(citation => ({
              oci: citation.oci,
              citing: citation.citing
            })),
            references: openCitationsData.references.map(reference => ({
      const useOpenCitations = options.useOpenCitations !== false; // Standardmäßig true
        oci: reference.oci,
              cited: reference.cited
            }))
          };
        }
      }

      // Hole die Zitationszahlen für jede Publikation
      const citationCounts = [], OpenCitations: ${useOpenCitations})`);

      for (const pub of authorPublications) {
        const citingPublications = await this.getCitingPublications(pub.doi);
  citationCounts.push(citingPublications.length);

      }// Hole OpenCitations-Daten für die zitierende Publikation mit den lokalen Daten
      const openCitationsData = await this.fetchOpenCitationsData(doi);


      // Berechne den h-Index
      return calculateHIndex(citationCounts);
    } catch (error) {
      logger.error('Fehler bei der Berechnung des h-Index', {
        authors,
        error: error.message,
        openCitationsEnhanced: useOpenCitations
      };
    } catch (error) {
      logger.error('Fehler bei der Erstellung eines Zitationsnetzwerks', {
        doi,
        error: error.message
      });

      return {
        success: false,
        doi,
        error: error.message
      };
    }

  /**
   * Erweitert ein Zitationsnetzwerk mit Daten von OpenCitations
   *
   * @private
   * @param {Object} network - Das zu erweiternde Netzwerk
   * @param {string} doi - DOI der Publikation
   * @param {number} depth - Tiefe des Netzwerks
   * @param {number} maxNodes - Maximale Anzahl von Knoten
   */
  async enhanceNetworkWithOpenCitations(network, doi, depth, maxNodes) {
    try {
      logger.info(`Erweitere Zitationsnetzwerk für DOI ${doi} mit OpenCitations-Daten`);

      // Hole OpenCitations-Daten für die Publikation
      const openCitationsData = await this.fetchOpenCitationsData(doi);

      if (!openCitationsData.success) {
        logger.warn(`Keine OpenCitations-Daten für DOI ${doi} verfügbar`);
        return;
      }

      // Füge eingehende Zitationen zum Netzwerk hinzu
      for (const citation of openCitationsData.citations) {
        // Prüfe, ob der Knoten bereits existiert
        const existingNode = network.nodes.find(node => node.id === citation.citing);

        if (!existingNode && network.nodes.length < maxNodes) {
          // Füge den neuen Knoten hinzu
          network.nodes.push({
            id: citation.citing,
            label: citation.citing,
            type: 'citing',
            oci: citation.oci,
            source: 'opencitations'
          });

          // Füge die Kante hinzu
          network.edges.push({
            source: citation.citing,
            target: doi,
            type: 'cites',
            oci: citation.oci,
            source: 'opencitations'
          });
        } else if (existingNode) {
          // Füge die OCI-Information zum bestehenden Knoten hinzu
          existingNode.oci = citation.oci;
          existingNode.source = existingNode.source ? `${existingNode.source},opencitations` : 'opencitations';

          // Prüfe, ob die Kante bereits existiert
          const existingEdge = network.edges.find(edge =>
            edge.source === citation.citing && edge.target === doi
          );

          if (!existingEdge) {
            // Füge die Kante hinzu
            network.edges.push({
              source: citation.citing,
              target: doi,
              type: 'cites',
              oci: citation.oci,
              source: 'opencitations'
            });
          } else {
            // Füge die OCI-Information zur bestehenden Kante hinzu
            existingEdge.oci = citation.oci;
            existingEdge.source = existingEdge.source ? `${existingEdge.source},opencitations` : 'opencitations';
          }
        }
      }

      // Füge ausgehende Zitationen zum Netzwerk hinzu
      for (const reference of openCitationsData.references) {
        // Prüfe, ob der Knoten bereits existiert
        const existingNode = network.nodes.find(node => node.id === reference.cited);

        if (!existingNode && network.nodes.length < maxNodes) {
          // Füge den neuen Knoten hinzu
          network.nodes.push({
            id: reference.cited,
            label: reference.cited,
            type: 'cited',
            oci: reference.oci,
            source: 'opencitations'
          });

          // Füge die Kante hinzu
          network.edges.push({
            source: doi,
            target: reference.cited,
            type: 'cites',
            oci: reference.oci,
            source: 'opencitations'
          });
        } else if (existingNode) {
          // Füge die OCI-Information zum bestehenden Knoten hinzu
          existingNode.oci = reference.oci;
          existingNode.source = existingNode.source ? `${existingNode.source},opencitations` : 'opencitations';

          // Prüfe, ob die Kante bereits existiert
          const existingEdge = network.edges.find(edge =>
            edge.source === doi && edge.target === reference.cited
          );

          if (!existingEdge) {
            // Füge die Kante hinzu
            network.edges.push({
              source: doi,
              target: reference.cited,
              type: 'cites',
              oci: reference.oci,
              source: 'opencitations'
            });
          } else {
            // Füge die OCI-Information zur bestehenden Kante hinzu
            existingEdge.oci = reference.oci;
            existingEdge.source = existingEdge.source ? `${existingEdge.source},opencitations` : 'opencitations';
          }
        }
      }

      // Wenn die Tiefe größer als 1 ist und noch Platz für Knoten ist, erweitere rekursiv
      if (depth > 1 && network.nodes.length < maxNodes) {
        // Beschränke die Anzahl der zu erweiternden Knoten, um Überlastung zu vermeiden
        const nodesToExpand = network.nodes
          .filter(node => node.id !== doi && !node.expanded)
          .slice(0, Math.min(5, maxNodes - network.nodes.length));

        for (const node of nodesToExpand) {
          // Markiere den Knoten als erweitert
          node.expanded = true;

          // Rekursiv erweitern mit reduzierter Tiefe
          await this.enhanceNetworkWithOpenCitations(
            network,
            node.id,
            depth - 1,
            maxNodes
          );

          // Wenn die maximale Knotenzahl erreicht ist, breche ab
          if (network.nodes.length >= maxNodes) {
            break;
          }
        }
      }

      logger.info(`Zitationsnetzwerk für DOI ${doi} mit OpenCitations-Daten erweitert`, {
        nodesCount: network.nodes.length,
        edgesCount: network.edges.length
      });
    } catch (error) {
      logger.error('Fehler bei der Erweiterung des Netzwerks mit OpenCitations-Daten', {
        doi,
        error: error.message
      });
    }
  }
      
    }
  }
  
  /**
   * Berechnet den i10-Index eines Autors
   * 
   * @param {string} authors - Autoren der Publikation
   * @returns {Promise<number>} i10-Index
   */
  async calculateAuthorI10Index(authors) {
    try {
      // Extrahiere den Hauptautor
      const mainAuthor = authors.split(',')[0].trim();

      // Hole alle Publikationen des Autors
      const authorPublications = await this.databaseService.query(
        'SELECT doi FROM publications WHERE authors LIKE ?',
        [`%${mainAuthor}%`]
      );

      if (!authorPublications || authorPublications.length === 0) {
        return 0;

      // Hole die Zitationszahlen für jede Publikation
   * @param {Object} [openCitationsData] - OpenCitations-Daten für die Publikation
      const citationCounts = [];
      const useOpenCitations = options.useOpenCitations !== false; // Standardmäßig true

      for (const pub of authorPublications) {
        const citingPublications = await this.getCitingPublications(pub.doi);
            metrics.openCitationsVerified ? 1 : 0,
        citationCounts.push(citingPublications.le, OpenCitations: ${useOpenCitations})`);

      // Hole OpenCitations-Daten für die zitierte Publikation mit OpenCitations-Daten mit den lokalen Daten
      const citedOpenCitationsData = await this.fetchOpenCitationsData(pub.doi, citedOpenCitationsData);

      // Berechne den i10-Index
      return calculateI10Index(citationCounts);
    } catch (error) {
      logger.error('Fehler bei der Berechnung des i10-Index', {
        authors,
        error: error.message,
        openCitationsEnhanced: useOpenCitations
      };
    } catch (error) {
      logger.error('Fehler bei der Erstellung eines Zitationsnetzwerks', {
        doi,
        error: error.message
      });

      return {
        success: false,
        doi,
        error: error.message
      };
    }

  /**
   * Erweitert ein Zitationsnetzwerk mit Daten von OpenCitations
   *
   * @private
   * @param {Object} network - Das zu erweiternde Netzwerk
   * @param {string} doi - DOI der Publikation
   * @param {number} depth - Tiefe des Netzwerks
   * @param {number} maxNodes - Maximale Anzahl von Knoten
   */
  async enhanceNetworkWithOpenCitations(network, doi, depth, maxNodes) {
    try {
      logger.info(`Erweitere Zitationsnetzwerk für DOI ${doi} mit OpenCitations-Daten`);

      // Hole OpenCitations-Daten für die Publikation
      const openCitationsData = await this.fetchOpenCitationsData(doi);

      if (!openCitationsData.success) {
        logger.warn(`Keine OpenCitations-Daten für DOI ${doi} verfügbar`);
        return;
      }

      // Füge eingehende Zitationen zum Netzwerk hinzu
      for (const citation of openCitationsData.citations) {
        // Prüfe, ob der Knoten bereits existiert
        const existingNode = network.nodes.find(node => node.id === citation.citing);

        if (!existingNode && network.nodes.length < maxNodes) {
          // Füge den neuen Knoten hinzu
          network.nodes.push({
            id: citation.citing,
            label: citation.citing,
            type: 'citing',
            oci: citation.oci,
            source: 'opencitations'
          });

          // Füge die Kante hinzu
          network.edges.push({
            source: citation.citing,
            target: doi,
            type: 'cites',
            oci: citation.oci,
            source: 'opencitations'
          });
        } else if (existingNode) {
          // Füge die OCI-Information zum bestehenden Knoten hinzu
          existingNode.oci = citation.oci;
          existingNode.source = existingNode.source ? `${existingNode.source},opencitations` : 'opencitations';

          // Prüfe, ob die Kante bereits existiert
          const existingEdge = network.edges.find(edge =>
            edge.source === citation.citing && edge.target === doi
          );

          if (!existingEdge) {
            // Füge die Kante hinzu
            network.edges.push({
              source: citation.citing,
              target: doi,
              type: 'cites',
              oci: citation.oci,
              source: 'opencitations'
            });
          } else {
            // Füge die OCI-Information zur bestehenden Kante hinzu
            existingEdge.oci = citation.oci;
            existingEdge.source = existingEdge.source ? `${existingEdge.source},opencitations` : 'opencitations';
          }
        }
      }

      // Füge ausgehende Zitationen zum Netzwerk hinzu
      for (const reference of openCitationsData.references) {
        // Prüfe, ob der Knoten bereits existiert
        const existingNode = network.nodes.find(node => node.id === reference.cited);

        if (!existingNode && network.nodes.length < maxNodes) {
          // Füge den neuen Knoten hinzu
          network.nodes.push({
            id: reference.cited,
            label: reference.cited,
            type: 'cited',
            oci: reference.oci,
            source: 'opencitations'
          });

          // Füge die Kante hinzu
          network.edges.push({
            source: doi,
            target: reference.cited,
            type: 'cites',
            oci: reference.oci,
            source: 'opencitations'
          });
        } else if (existingNode) {
          // Füge die OCI-Information zum bestehenden Knoten hinzu
          existingNode.oci = reference.oci;
          existingNode.source = existingNode.source ? `${existingNode.source},opencitations` : 'opencitations';

          // Prüfe, ob die Kante bereits existiert
          const existingEdge = network.edges.find(edge =>
            edge.source === doi && edge.target === reference.cited
          );

          if (!existingEdge) {
            // Füge die Kante hinzu
            network.edges.push({
              source: doi,
              target: reference.cited,
              type: 'cites',
              oci: reference.oci,
              source: 'opencitations'
            });
          } else {
            // Füge die OCI-Information zur bestehenden Kante hinzu
            existingEdge.oci = reference.oci;
            existingEdge.source = existingEdge.source ? `${existingEdge.source},opencitations` : 'opencitations';
          }
        }
      }

      // Wenn die Tiefe größer als 1 ist und noch Platz für Knoten ist, erweitere rekursiv
      if (depth > 1 && network.nodes.length < maxNodes) {
        // Beschränke die Anzahl der zu erweiternden Knoten, um Überlastung zu vermeiden
        const nodesToExpand = network.nodes
          .filter(node => node.id !== doi && !node.expanded)
          .slice(0, Math.min(5, maxNodes - network.nodes.length));

        for (const node of nodesToExpand) {
          // Markiere den Knoten als erweitert
          node.expanded = true;

          // Rekursiv erweitern mit reduzierter Tiefe
          await this.enhanceNetworkWithOpenCitations(
            network,
            node.id,
            depth - 1,
            maxNodes
          );

          // Wenn die maximale Knotenzahl erreicht ist, breche ab
          if (network.nodes.length >= maxNodes) {
            break;
          }
        }
      }

      logger.info(`Zitationsnetzwerk für DOI ${doi} mit OpenCitations-Daten erweitert`, {
        nodesCount: network.nodes.length,
        edgesCount: network.edges.length
      });
    } catch (error) {
      logger.error('Fehler bei der Erweiterung des Netzwerks mit OpenCitations-Daten', {
        doi,
        error: error.message
      });
    }
  }
      
return 0;// Wenn keine OpenCitations-Daten übergeben wurden, hole sie
      if (!openCitationsData) {
        openCitationsData = await this.fetchOpenCitationsData(doi);
      }

      // Hole die Metriken für die Publikation
      const metrics = await this.databaseService.query(
        'SELECT * FROM publication_metrics WHERE doi = ?',
        [doi]
      );

    }
  }
  
  /**
   * Berechnet die Anzahl der Zitationen pro Jahr
   * 
   * @param {Array<Object>} citingPublications - Zitierende Publikationen
   * @param {number} startYear - Startjahr
   * @param {number} endYear - Endjahr
   * @returns {Object} Zitationen pro Jahr
   */
  calculateCitationsPerYear(citingPublications, startYear, endYear) {
    const citationsPerYear = {};
    
    // Initialisiere das Objekt mit allen Jahren
    for (let year = startYear; year <= endYear; year++) {
      citationsPerYear[year] = 0;
    }
    
    // Zähle die Zitationen pro Jahr
    for (const pub of citingPublications) {
      const year = new Date(pub.publication_date).getFullYear();
      
      if (year >= startYear && year <= endYear) {
        citationsPerYear[year]++;
      }

    }// Füge Zitationsmetriken hinzu, wenn verfügbar
      if (metrics && metrics.length > 0) {
        metadata.attributes.push({
          trait_type: 'Citation Count',
          value: metrics[0].total_citations
        });

        metadata.attributes.push({
          trait_type: 'h-Index',
          value: metrics[0].h_index
        });
      } else if (openCitationsData && openCitationsData.success) {
        // Wenn keine Metriken in der Datenbank, aber OpenCitations-Daten verfügbar sind
        metadata.attributes.push({
          trait_type: 'Citation Count',
          value: openCitationsData.citationCount
        });
      }

      // Füge OpenCitations-Verifizierung hinzu, wenn verfügbar
      if (openCitationsData && openCitationsData.success) {
        metadata.attributes.push({
          trait_type: 'OpenCitations Verified',
          value: 'Yes'
        });

        // Füge OCIs (Open Citation Identifiers) hinzu, wenn verfügbar
        if (openCitationsData.citations && openCitationsData.citations.length > 0) {
          metadata.opencitations = {
            citations: openCitationsData.citations.map(citation => ({
              oci: citation.oci,
              citing: citation.citing
            })),
            references: openCitationsData.references.map(reference => ({
              oci: reference.oci,
              cited: reference.cited
            }))
          };
        }
      }


    return citationsPerYear;
  }
  
  /**
   * Speichert Metriken in der Datenbank
   * 
   * @param {string} doi - DOI der Publikation
   * @param {Object} metrics - Berechnete Metriken
   */
  async storeMetrics(doi, metrics) {

      // Prüfe, ob bereits Metriken für diese Publikation existieren
      const existingMetrics = await this.databaseService.query(
        'SELECT id FROM publication_metrics WHERE doi = ?',
        [doi]
      );

      const metricsJson = JSON.stringify(metrics);
      
if (existingMetrics && existingMetrics.length > 0) {
        // Aktualisiere die bestehenden Metriken
        await this.databaseService.query(
          'UPDATE publication_metrics SET h_index = ?, i10_index = ?, total_citations = ?, average_citations_per_year = ?, citations_per_year = ?, external_citation_sources = ?, opencitations_verified = ?, updated_at = NOW() WHERE doi = ?',
          [
      metrics.hIndex,
            metrics.i10Index,
            metrics.totalCitations,
            metrics.averageCitationsPerYear,
            JSON.stringify(metrics.citationsPerYear),
            JSON.stringify(metrics.externalCitationSources, OpenCitations: ${useOpenCitations})`);
      metrics.openCitationsVerified ? 1 : 0,
            doi

      } else {
      // Hole OpenCitations-Daten für die zitierte Publikation mit OpenCitations-Daten
      const citedOpenCitationsData = await this.fetchOpenCitationsData(pub.doi, citedOpenCitationsData);
    // Füge neue Metriken hinzu
        await this.databaseService.query(
          'INSERT INTO publication_metrics (doi, h_index, i10_index, total_citations, average_citations_per_year, citations_per_year, external_citation_sources, opencitations_verified, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())',
      
     // Wenn keine OpenCitations-Daten übergeben wurden, hole sie
      if (!openCitationsData) {
        openCitationsData = await this.fetchOpenCitationsData(doi);

      }
// Erweitere das Netzwerk mit OpenCitations-Daten, wenn gewünscht
      if (useOpenCitations) {
        await this.enhanceNetworkWithOpenCitations(network, doi, depth, maxNodes);,
        openCitationsEnhanced: useOpenCitations
      };
    } catch (error) {
      logger.error('Fehler bei der Erstellung eines Zitationsnetzwerks', {
        doi,
        error: error.message
      });

      return {
        success: false,
        doi,
        error: error.message
      };
    }

  /**
   * Erweitert ein Zitationsnetzwerk mit Daten von OpenCitations
   *
   * @private
   * @param {Object} network - Das zu erweiternde Netzwerk
   * @param {string} doi - DOI der Publikation
   * @param {number} depth - Tiefe des Netzwerks
   * @param {number} maxNodes - Maximale Anzahl von Knoten
   */
  async enhanceNetworkWithOpenCitations(network, doi, depth, maxNodes) {
    try {
      logger.info(`Erweitere Zitationsnetzwerk für DOI ${doi} mit OpenCitations-Daten`);

      // Hole OpenCitations-Daten für die Publikation
      const openCitationsData = await this.fetchOpenCitationsData(doi);

      if (!openCitationsData.success) {
        logger.warn(`Keine OpenCitations-Daten für DOI ${doi} verfügbar`);
        return;
      }

      // Füge eingehende Zitationen zum Netzwerk hinzu
      for (const citation of openCitationsData.citations) {
        // Prüfe, ob der Knoten bereits existiert
        const existingNode = network.nodes.find(node => node.id === citation.citing);

        if (!existingNode && network.nodes.length < maxNodes) {
          // Füge den neuen Knoten hinzu
          network.nodes.push({
            id: citation.citing,
            label: citation.citing,
            type: 'citing',
            oci: citation.oci,
            source: 'opencitations'
          });

          // Füge die Kante hinzu
          network.edges.push({
            source: citation.citing,
            target: doi,
            type: 'cites',
            oci: citation.oci,
            source: 'opencitations'
          });
        } else if (existingNode) {
          // Füge die OCI-Information zum bestehenden Knoten hinzu
          existingNode.oci = citation.oci;
          existingNode.source = existingNode.source ? `${existingNode.source},opencitations` : 'opencitations';

          // Prüfe, ob die Kante bereits existiert
          const existingEdge = network.edges.find(edge =>
            edge.source === citation.citing && edge.target === doi
          );

          if (!existingEdge) {
            // Füge die Kante hinzu
            network.edges.push({
              source: citation.citing,
              target: doi,
              type: 'cites',
              oci: citation.oci,
              source: 'opencitations'
            });
          } else {
            // Füge die OCI-Information zur bestehenden Kante hinzu
            existingEdge.oci = citation.oci;
            existingEdge.source = existingEdge.source ? `${existingEdge.source},opencitations` : 'opencitations';
          }
        }
      }

      // Füge ausgehende Zitationen zum Netzwerk hinzu
      for (const reference of openCitationsData.references) {
        // Prüfe, ob der Knoten bereits existiert
        const existingNode = network.nodes.find(node => node.id === reference.cited);

        if (!existingNode && network.nodes.length < maxNodes) {
          // Füge den neuen Knoten hinzu
          network.nodes.push({
            id: reference.cited,
            label: reference.cited,
            type: 'cited',
            oci: reference.oci,
            source: 'opencitations'
          });

          // Füge die Kante hinzu
          network.edges.push({
            source: doi,
            target: reference.cited,
            type: 'cites',
            oci: reference.oci,
            source: 'opencitations'
          });
        } else if (existingNode) {
          // Füge die OCI-Information zum bestehenden Knoten hinzu
          existingNode.oci = reference.oci;
          existingNode.source = existingNode.source ? `${existingNode.source},opencitations` : 'opencitations';

          // Prüfe, ob die Kante bereits existiert
          const existingEdge = network.edges.find(edge =>
            edge.source === doi && edge.target === reference.cited
          );

          if (!existingEdge) {
            // Füge die Kante hinzu
            network.edges.push({
              source: doi,
              target: reference.cited,
              type: 'cites',
              oci: reference.oci,
              source: 'opencitations'
            });
          } else {
            // Füge die OCI-Information zur bestehenden Kante hinzu
            existingEdge.oci = reference.oci;
            existingEdge.source = existingEdge.source ? `${existingEdge.source},opencitations` : 'opencitations';
          }
        }
      }

      // Wenn die Tiefe größer als 1 ist und noch Platz für Knoten ist, erweitere rekursiv
      if (depth > 1 && network.nodes.length < maxNodes) {
        // Beschränke die Anzahl der zu erweiternden Knoten, um Überlastung zu vermeiden
        const nodesToExpand = network.nodes
          .filter(node => node.id !== doi && !node.expanded)
          .slice(0, Math.min(5, maxNodes - network.nodes.length));

        for (const node of nodesToExpand) {
          // Markiere den Knoten als erweitert
          node.expanded = true;

          // Rekursiv erweitern mit reduzierter Tiefe
          await this.enhanceNetworkWithOpenCitations(
            network,
            node.id,
            depth - 1,
            maxNodes
          );

          // Wenn die maximale Knotenzahl erreicht ist, breche ab
          if (network.nodes.length >= maxNodes) {
            break;
          }
        }
      }

      logger.info(`Zitationsnetzwerk für DOI ${doi} mit OpenCitations-Daten erweitert`, {
        nodesCount: network.nodes.length,
        edgesCount: network.edges.length
      });
    } catch (error) {
      logger.error('Fehler bei der Erweiterung des Netzwerks mit OpenCitations-Daten', {
        doi,
        error: error.message
      });
    }
  }
      // Hole die Metriken für die Publikation
      const metrics = await this.databaseService.query(
        'SELECT * FROM publication_metrics WHERE doi = ?',
        [doi]
      );
       doi,
            metrics.hIndex,
            metrics.i10Index,
            metrics.totalCitations,
            metrics.averageCitationsPerYear,
            JSON.stringify(metrics.citationsPerYear),
            JSON.stringify(metrics.externalCitationSources),
            metrics.openCitationsVerified ? 1 : 0
          ]

        );// Finde den OCI (Open Citation Identifier) für diese Zitation, falls verfügbar
          let oci = null;
          if (openCitationsData && openCitationsData.success) {
            const reference = openCitationsData.references.find(ref => ref.cited === pub.doi);
            if (reference) {
              oci = reference.oci;
            },

     // Füge Zitationsmetriken hinzu, wenn verfügbar
      if (metrics && metrics.length > 0) {
        metadata.attributes.push({
          trait_type: 'Citation Count',
          value: metrics[0].total_citations
        });

        metadata.attributes.push({
          trait_type: 'h-Index',
          value: metrics[0].h_index
        });
      } else if (openCitationsData && openCitationsData.success) {
        // Wenn keine Metriken in der Datenbank, aber OpenCitations-Daten verfügbar sind
        metadata.attributes.push({
          trait_type: 'Citation Count',
          value: openCitationsData.citationCount
        });
      }

      // Füge OpenCitations-Verifizierung hinzu, wenn verfügbar
      if (openCitationsData && openCitationsData.success) {
        metadata.attributes.push({
          trait_type: 'OpenCitations Verified',
          value: 'Yes'
        });

        // Füge OCIs (Open Citation Identifiers) hinzu, wenn verfügbar
        if (openCitationsData.citations && openCitationsData.citations.length > 0) {
          metadata.opencitations = {
            citations: openCitationsData.citations.map(citation => ({
              oci: citation.oci,
              citing: citation.citing
            })),
            references: openCitationsData.references.map(reference => ({
              oci: reference.oci,
              cited: reference.cited
            }))
          };
        }

       oci
          }
    const useOpenCitations = options.useOpenCitations !== false; // Standardmäßig true

      logger.info(`Metriken für DOI ${doi} gespeichert`);
    } catch (error) {
      logger.error('Fehler beim Speichern von Metriken', {
        doi,
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Verknüpft Zitationen mit NFTs (DOIs zu NFTs)
   *
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Ergebnis der Verknüpfung
   */
  async linkCitationsToNFTs(doi) {
    try {
      logger.info(`Verknüpfe Zitationen mit NFTs für Publikation mit DOI ${doi}`);

      // Hole die zitierten Publikationen mit den lokalen Daten
      const citedPublications = await this.getCitedPublications(doi);

      // Hole OpenCitations-Daten für die zitierende Publikation
      const openCitationsData = await this.fetchOpenCitationsData(doi);

      if (citedPublications.length,
        openCitationsVerified: openCitationsData && openCitationsData.success === 0) {
        return {
          success: true,
          doi,
          linkedCitations: [],
          totalCitations: 0,
          message: 'Keine zitierten Publikationen gefunden'
        };

      // Verknüpfe jede zitierte Publikation mit einem NFT
      const linkedCitations = [];

      for (const pub of citedPublications) {
        try {
          // Prüfe, ob die Publikation bereits mit einem NFT verknüpft ist
          const existingNft = await this.databaseService.query(
            'SELECT nft_id FROM doi_nft_mapping WHERE doi = ?',
            [pub.doi]
          );

          let nftId;

          if (existingNft && existingNft.length > 0) {
            // Verwende die bestehende NFT-ID
            nftId = existingNft[0].nft_id;
            logger.debug(`Publikation mit DOI ${pub.doi} ist bereits mit NFT ${nftId} verknüpft`);
          } else {

     // Wenn keine OpenCitations-Daten übergeben wurden, hole sie
      if (!openCitationsData) {
        openCitationsData = await this.fetchOpenCitationsData(doi);
      }

      // Hole die Metriken für die Publikation
      const metrics = await this.databaseService.query(
        'SELECT * FROM publication_metrics WHERE doi = ?',
        [doi]
      );
       // Hole OpenCitations-Daten für die zitierte Publikation
            const citedOpenCitationsData = await this.fetchOpenCitationsData(pub.doi);

            // Erstelle ein neues NFT für die Publikation mit OpenCitations-Daten
            const nftResult = await this.createNFTForDOI(pub.doi, citedOpenCitationsData);

            if (nftResult.success) {
              nftId = nftResult.nftId;
              logger.info(`Neues NFT ${nftId} für Publikation mit DOI ${pub.doi} erstellt`);
            } else {
              logger.warn(`Konnte kein NFT für Publikation mit DOI ${pub.doi} erstellen: ${nftResult.message}`);
              continue;
            }
          }

          // Finde den OCI (Open Citation Identifier) für diese Zitation, falls verfügbar
          let oci = null;
          if (openCitationsData && openCitationsData.success) {
            const reference = openCitationsData.references.find(ref => ref.cited === pub.doi);
            if (reference) {
              oci = reference.oci;
            }
          }


   // Füge Zitationsmetriken hinzu, wenn verfügbar
      if (metrics && metrics.length > 0) {
        metadata.attributes.push({
          trait_type: 'Citation Count',
          value: metrics[0].total_citations
        });

        metadata.attributes.push({
          trait_type: 'h-Index',
          value: metrics[0].h_index
        });
      } else if (openCitationsData && openCitationsData.success) {
        // Wenn keine Metriken in der Datenbank, aber OpenCitations-Daten verfügbar sind
        metadata.attributes.push({
          trait_type: 'Citation Count',
          value: openCitationsData.citationCount
        });
      }

      // Füge OpenCitations-Verifizierung hinzu, wenn verfügbar
      if (openCitationsData && openCitationsData.success) {
        metadata.attributes.push({
          trait_type: 'OpenCitations Verified',
          value: 'Yes'
        });

        // Füge OCIs (Open Citation Identifiers) hinzu, wenn verfügbar
        if (openCitationsData.citations && openCitationsData.citations.length, OpenCitations: ${useOpenCitations})`);
    metadata.opencitations = {
            citations: openCitationsData.citations.map(citation => ({
              oci: citation.oci,
              citing: citation.citing
            })),
            references: openCitationsData.references.map(reference => ({
              oci: reference.oci,
              cited: reference.cited
            }))
          };
        }

       // Speichere die Verknüpfung in der Datenbank
          await this.databaseService.query(
            'INSERT INTO citation_nft_links (citing_doi, cited_doi, nft_id, oci, created_at) VALUES (?, ?, ?, ?, NOW())',
            [doi, pub.doi, nftId, oci]
          );

    linkedCitations.push({
            doi: pub.doi,
            title: pub.title,
            nftId,
            oci
          });
  } catch (error) {
          logger.error(`Fehler bei der Verknüpfung von Zitation mit NFT`, {
            citingDoi: doi,
            citedDoi: pub.doi,
            error: error.message
          });
          // Fahre mit der nächsten Zitation fort
        }
      }

      // Registriere die Verknüpfungen auf der Blockchain
      if (linkedCitations.length > 0) {
        await this.registerCitationLinksOnBlockchain(doi, linkedCitations);
      }

      return {
        success: true,
        doi,
        linkedCitations,
        totalCitations: citedPublications.length,
        openCitationsVerified: openCitationsData && openCitationsData.success
      };
    } catch (error) {
      logger.error('Fehler bei der Verknüpfung von Zitationen mit NFTs', {
        doi,
        error: error.message
      });

      return {
        success: false,
        doi,
        error: error.message
      };
    }
  }
  
  /**
   * Erstellt ein NFT für eine DOI
   *
   * @param {string} doi - DOI der Publikation
   * @param {Object} [openCitationsData] - OpenCitations-Daten für die Publikation
   * @returns {Promise<Object>} Ergebnis der NFT-Erstellung
   */
  async createNFTForDOI(doi, openCitationsData = null) {
    try {
      // Hole die Publikationsdaten
      const publication = await this.getPublicationData(doi);

      if (!publication) {
        return {
          success: false,
          doi,
          message: 'Publikation nicht gefunden'
        };
      }

      // Wenn keine OpenCitations-Daten übergeben wurden, hole sie
      if (!openCitationsData) {
        openCitationsData = await this.fetchOpenCitationsData(doi);
      }

      // Hole die Metriken für die Publikation
      const metrics = await this.databaseService.query(
        'SELECT * FROM publication_metrics WHERE doi = ?',
        [doi]
      );

      // Erstelle Metadaten für das NFT
      const metadata = {
        name: publication.title,
        description: publication.abstract || 'Wissenschaftliche Publikation',
        image: `${doiNftConfig.metadata.imageBaseUrl}/${encodeURIComponent(doi)}`,
        external_url: `https://doi.org/${doi}`,
        attributes: [
          {
            trait_type: 'Authors',
            value: publication.authors
          },
          {
            trait_type: 'Publication Date',
            value: publication.publication_date
          },
          {
            trait_type: 'Journal',
            value: publication.journal || 'N/A'
          },
          {
            display_type: 'date',
            trait_type: 'Published',
            value: Math.floor(new Date(publication.publication_date).getTime() / 1000)
          }
        ]
      };

      // Füge Zitationsmetriken hinzu, wenn verfügbar
      if (metrics && metrics.length > 0) {
        metadata.attributes.push({
          trait_type: 'Citation Count',
          value: metrics[0].total_citations
        });

        metadata.attributes.push({
          trait_type: 'h-Index',
          value: metrics[0].h_index
        });
      } else if (openCitationsData && openCitationsData.success) {
        // Wenn keine Metriken in der Datenbank, aber OpenCitations-Daten verfügbar sind
        metadata.attributes.push({
          trait_type: 'Citation Count',
          value: openCitationsData.citationCount
        });
      }

      // Füge OpenCitations-Verifizierung hinzu, wenn verfügbar
      if (openCitationsData && openCitationsData.success) {
        metadata.attributes.push({
          trait_type: 'OpenCitations Verified',
          value: 'Yes'
        }, OpenCitations: ${useOpenCitations})`);
  // Füge OCIs (Open Citation Identifiers) hinzu, wenn verfügbar
        if (openCitationsData.citations && openCitationsData.citations.length > 0) {
          metadata.opencitations = {
            citations: openCitationsData.citations.map(citation => ({
              oci: citation.oci,
              citing: citation.citing
      })),
            references: openCitationsData.references.map(reference => ({
              oci: reference.oci,
              cited: reference.cited
            }))
          };
   
     }
// Erweitere das Netzwerk mit OpenCitations-Daten, wenn gewünscht
      if (useOpenCitations) {
        await this.enhanceNetworkWithOpenCitations(network, doi, depth, maxNodes);,
        openCitationsEnhanced: useOpenCitations
      };
    } catch (error) {
      logger.error('Fehler bei der Erstellung eines Zitationsnetzwerks', {
        doi,
        error: error.message
      });

      return {
        success: false,
        doi,
        error: error.message
      };
    }

  /**
   * Erweitert ein Zitationsnetzwerk mit Daten von OpenCitations
   *
   * @private
   * @param {Object} network - Das zu erweiternde Netzwerk
   * @param {string} doi - DOI der Publikation
   * @param {number} depth - Tiefe des Netzwerks
   * @param {number} maxNodes - Maximale Anzahl von Knoten
   */
  async enhanceNetworkWithOpenCitations(network, doi, depth, maxNodes) {
    try {
      logger.info(`Erweitere Zitationsnetzwerk für DOI ${doi} mit OpenCitations-Daten`);

      // Hole OpenCitations-Daten für die Publikation
      const openCitationsData = await this.fetchOpenCitationsData(doi);

      if (!openCitationsData.success) {
        logger.warn(`Keine OpenCitations-Daten für DOI ${doi} verfügbar`);
        return;
      }

      // Füge eingehende Zitationen zum Netzwerk hinzu
      for (const citation of openCitationsData.citations) {
        // Prüfe, ob der Knoten bereits existiert
        const existingNode = network.nodes.find(node => node.id === citation.citing);

        if (!existingNode && network.nodes.length < maxNodes) {
          // Füge den neuen Knoten hinzu
          network.nodes.push({
            id: citation.citing,
            label: citation.citing,
            type: 'citing',
            oci: citation.oci,
            source: 'opencitations'
          });

          // Füge die Kante hinzu
          network.edges.push({
            source: citation.citing,
            target: doi,
            type: 'cites',
            oci: citation.oci,
            source: 'opencitations'
          });
        } else if (existingNode) {
          // Füge die OCI-Information zum bestehenden Knoten hinzu
          existingNode.oci = citation.oci;
          existingNode.source = existingNode.source ? `${existingNode.source},opencitations` : 'opencitations';

          // Prüfe, ob die Kante bereits existiert
          const existingEdge = network.edges.find(edge =>
            edge.source === citation.citing && edge.target === doi
          );

          if (!existingEdge) {
            // Füge die Kante hinzu
            network.edges.push({
              source: citation.citing,
              target: doi,
              type: 'cites',
              oci: citation.oci,
              source: 'opencitations'
            });
          } else {
            // Füge die OCI-Information zur bestehenden Kante hinzu
            existingEdge.oci = citation.oci;
            existingEdge.source = existingEdge.source ? `${existingEdge.source},opencitations` : 'opencitations';
          }
        }
      }

      // Füge ausgehende Zitationen zum Netzwerk hinzu
      for (const reference of openCitationsData.references) {
        // Prüfe, ob der Knoten bereits existiert
        const existingNode = network.nodes.find(node => node.id === reference.cited);

        if (!existingNode && network.nodes.length < maxNodes) {
          // Füge den neuen Knoten hinzu
          network.nodes.push({
            id: reference.cited,
            label: reference.cited,
            type: 'cited',
            oci: reference.oci,
            source: 'opencitations'
          });

          // Füge die Kante hinzu
          network.edges.push({
            source: doi,
            target: reference.cited,
            type: 'cites',
            oci: reference.oci,
            source: 'opencitations'
          });
        } else if (existingNode) {
          // Füge die OCI-Information zum bestehenden Knoten hinzu
          existingNode.oci = reference.oci;
          existingNode.source = existingNode.source ? `${existingNode.source},opencitations` : 'opencitations';

          // Prüfe, ob die Kante bereits existiert
          const existingEdge = network.edges.find(edge =>
            edge.source === doi && edge.target === reference.cited
          );

          if (!existingEdge) {
            // Füge die Kante hinzu
            network.edges.push({
              source: doi,
              target: reference.cited,
              type: 'cites',
              oci: reference.oci,
              source: 'opencitations'
            });
          } else {
            // Füge die OCI-Information zur bestehenden Kante hinzu
            existingEdge.oci = reference.oci;
            existingEdge.source = existingEdge.source ? `${existingEdge.source},opencitations` : 'opencitations';
          }
        }
      }

      // Wenn die Tiefe größer als 1 ist und noch Platz für Knoten ist, erweitere rekursiv
      if (depth > 1 && network.nodes.length < maxNodes) {
        // Beschränke die Anzahl der zu erweiternden Knoten, um Überlastung zu vermeiden
        const nodesToExpand = network.nodes
          .filter(node => node.id !== doi && !node.expanded)
          .slice(0, Math.min(5, maxNodes - network.nodes.length));

        for (const node of nodesToExpand) {
          // Markiere den Knoten als erweitert
          node.expanded = true;

          // Rekursiv erweitern mit reduzierter Tiefe
          await this.enhanceNetworkWithOpenCitations(
            network,
            node.id,
            depth - 1,
            maxNodes
          );

          // Wenn die maximale Knotenzahl erreicht ist, breche ab
          if (network.nodes.length >= maxNodes) {
            break;
          }
        }
      }

      logger.info(`Zitationsnetzwerk für DOI ${doi} mit OpenCitations-Daten erweitert`, {
        nodesCount: network.nodes.length,
        edgesCount: network.edges.length
      });
    } catch (error) {
      logger.error('Fehler bei der Erweiterung des Netzwerks mit OpenCitations-Daten', {
        doi,
        error: error.message
      });
    }
  }
      // Speichere die Metadaten in IPFS mit den lokalen Daten
      const ipfsResult = await this.ipfsService.addContent(JSON.stringify(metadata));

      if (!ipfsResult.success) {
        return {
          success: false,
          doi,
          message: 'Fehler beim Speichern der Metadaten in IPFS'
        };
      }

      // Präge das NFT auf der Blockchain
      const mintResult = await this.blockchainService.mintNFT({
        contractAddress: doiNftConfig.nft.contractAddress,
        metadataUri: `ipfs://${ipfsResult.hash}`,
        recipient: publication.author_address || this.blockchainService.getDefaultAddress(),
        tokenType: 'DOI',
        identifier: doi
      });

      if (!mintResult.success) {
        return {
          success: false,
          doi,
          message: `Fehler beim Prägen des NFT: ${mintResult.error}`
        };
      }

      // Speichere die DOI-zu-NFT-Zuordnung in der Datenbank
      await this.databaseService.query(
        'INSERT INTO doi_nft_mapping (doi, nft_id, contract_address, token_id, metadata_uri, opencitations_verified, created_at) VALUES (?, ?, ?, ?, ?, ?, NOW())',
        [
          doi,
          mintResult.nftId,
          mintResult.contractAddress,
          mintResult.tokenId,
          `ipfs://${ipfsResult.hash}`,
          openCitationsData && openCitationsData.success ? 1 : 0
        ]
      );

      return {
        success: true,
        doi,
        nftId: mintResult.nftId,
        contractAddress: mintResult.contractAddress,
        tokenId: mintResult.tokenId,
        metadataUri: `ipfs://${ipfsResult.hash}`,
        openCitationsVerified: openCitationsData && openCitationsData.success
      };
    } catch (error) {
      logger.error('Fehler bei der Erstellung eines NFT für DOI', {
        doi,
        error: error.message
      });
      
      return {
        success: false,
        doi,
        message: error.message
      };
    }
  }
  
  /**
   * Registriert Zitationsverknüpfungen auf der Blockchain
   * 
   * @param {string} citingDoi - DOI der zitierenden Publikation
   * @param {Array<Object>} linkedCitations - Verknüpfte Zitationen
   */
  async registerCitationLinksOnBlockchain(citingDoi, linkedCitations) {
    try {
      // Hole die NFT-ID der zitierenden Publikation
      const citingNft = await this.databaseService.query(
        'SELECT nft_id FROM doi_nft_mapping WHERE doi = ?',
        [citingDoi]
      );
      
      if (!citingNft || citingNft.length === 0) {
        logger.warn(`Keine NFT-ID für zitierende Publikation mit DOI ${citingDoi} gefunden`);
        return;
      }
      
      const citingNftId = citingNft[0].nft_id;
      
      // Erstelle ein Batch von Zitationsverknüpfungen
      const citationLinks = linkedCitations.map(citation => ({
        citingNftId,
        citedNftId: citation.nftId,
        timestamp: Math.floor(Date.now() / 1000)
      }));
      
      // Registriere die Verknüpfungen auf der Blockchain
      const result = await this.blockchainService.registerCitationLinks(citationLinks);
      
      if (result.success) {
        logger.info(`Zitationsverknüpfungen für DOI ${citingDoi} auf der Blockchain registriert`, {
          transactionHash: result.transactionHash
        });
      } else {
        logger.warn(`Fehler bei der Registrierung von Zitationsverknüpfungen auf der Blockchain: ${result.error}`);
      }
    } catch (error) {
      logger.error('Fehler bei der Registrierung von Zitationsverknüpfungen auf der Blockchain', {
        citingDoi,
        error: error.message
      });
    }
  }
  
  /**
   * Berechnet den Einfluss einer Publikation
   *
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Ergebnis der Einflussberechnung
   */
  async calculatePublicationInfluence(doi) {
    try {
      logger.info(`Berechne Einfluss der Publikation mit DOI ${doi}`);
      
      // Hole die Publikationsdaten
      const publication = await this.getPublicationData(doi);

      if (!publication) {
        throw new Error(`Publikation mit DOI ${doi} nicht gefunden`);
      }

      // Hole die Zitationsmetriken
      const metrics = await this.databaseService.query(
        'SELECT * FROM publication_metrics WHERE doi = ?',
        [doi]
, OpenCitations: ${useOpenCitations})`);

      if (!metrics || metrics.length === 0) {
        // Analysiere die Zitationen, wenn noch keine Metriken vorhanden sind mit den lokalen Daten
        const analysisResult = await this.analyzeCitations(doi);

        // Erweitere das Netzwerk mit OpenCitations-Daten, wenn gewünscht
      if (useOpenCitations) {
        await this.enhanceNetworkWithOpenCitations(network, doi, depth, maxNodes);,
        openCitationsEnhanced: useOpenCitations
      };
    } catch (error) {
      logger.error('Fehler bei der Erstellung eines Zitationsnetzwerks', {
        doi,
        error: error.message
      });

      return {
        success: false,
        doi,
        error: error.message
      };
    }

  /**
   * Erweitert ein Zitationsnetzwerk mit Daten von OpenCitations
   *
   * @private
   * @param {Object} network - Das zu erweiternde Netzwerk
   * @param {string} doi - DOI der Publikation
   * @param {number} depth - Tiefe des Netzwerks
   * @param {number} maxNodes - Maximale Anzahl von Knoten
   */
  async enhanceNetworkWithOpenCitations(network, doi, depth, maxNodes) {
    try {
      logger.info(`Erweitere Zitationsnetzwerk für DOI ${doi} mit OpenCitations-Daten`);

      // Hole OpenCitations-Daten für die Publikation
      const openCitationsData = await this.fetchOpenCitationsData(doi);

      if (!openCitationsData.success) {
        logger.warn(`Keine OpenCitations-Daten für DOI ${doi} verfügbar`);
        return;
      }

      // Füge eingehende Zitationen zum Netzwerk hinzu
      for (const citation of openCitationsData.citations) {
        // Prüfe, ob der Knoten bereits existiert
        const existingNode = network.nodes.find(node => node.id === citation.citing);

        if (!existingNode && network.nodes.length < maxNodes) {
          // Füge den neuen Knoten hinzu
          network.nodes.push({
            id: citation.citing,
            label: citation.citing,
            type: 'citing',
            oci: citation.oci,
            source: 'opencitations'
          });

          // Füge die Kante hinzu
          network.edges.push({
            source: citation.citing,
            target: doi,
            type: 'cites',
            oci: citation.oci,
            source: 'opencitations'
          });
        } else if (existingNode) {
          // Füge die OCI-Information zum bestehenden Knoten hinzu
          existingNode.oci = citation.oci;
          existingNode.source = existingNode.source ? `${existingNode.source},opencitations` : 'opencitations';

          // Prüfe, ob die Kante bereits existiert
          const existingEdge = network.edges.find(edge =>
            edge.source === citation.citing && edge.target === doi
          );

          if (!existingEdge) {
            // Füge die Kante hinzu
            network.edges.push({
              source: citation.citing,
              target: doi,
              type: 'cites',
              oci: citation.oci,
              source: 'opencitations'
            });
          } else {
            // Füge die OCI-Information zur bestehenden Kante hinzu
            existingEdge.oci = citation.oci;
            existingEdge.source = existingEdge.source ? `${existingEdge.source},opencitations` : 'opencitations';
          }
        }
      }

      // Füge ausgehende Zitationen zum Netzwerk hinzu
      for (const reference of openCitationsData.references) {
        // Prüfe, ob der Knoten bereits existiert
        const existingNode = network.nodes.find(node => node.id === reference.cited);

        if (!existingNode && network.nodes.length < maxNodes) {
          // Füge den neuen Knoten hinzu
          network.nodes.push({
            id: reference.cited,
            label: reference.cited,
            type: 'cited',
            oci: reference.oci,
            source: 'opencitations'
          });

          // Füge die Kante hinzu
          network.edges.push({
            source: doi,
            target: reference.cited,
            type: 'cites',
            oci: reference.oci,
            source: 'opencitations'
          });
        } else if (existingNode) {
          // Füge die OCI-Information zum bestehenden Knoten hinzu
          existingNode.oci = reference.oci;
          existingNode.source = existingNode.source ? `${existingNode.source},opencitations` : 'opencitations';

          // Prüfe, ob die Kante bereits existiert
          const existingEdge = network.edges.find(edge =>
            edge.source === doi && edge.target === reference.cited
          );

          if (!existingEdge) {
            // Füge die Kante hinzu
            network.edges.push({
              source: doi,
              target: reference.cited,
              type: 'cites',
              oci: reference.oci,
              source: 'opencitations'
            });
          } else {
            // Füge die OCI-Information zur bestehenden Kante hinzu
            existingEdge.oci = reference.oci;
            existingEdge.source = existingEdge.source ? `${existingEdge.source},opencitations` : 'opencitations';
          }
        }
      }

      // Wenn die Tiefe größer als 1 ist und noch Platz für Knoten ist, erweitere rekursiv
      if (depth > 1 && network.nodes.length < maxNodes) {
        // Beschränke die Anzahl der zu erweiternden Knoten, um Überlastung zu vermeiden
        const nodesToExpand = network.nodes
          .filter(node => node.id !== doi && !node.expanded)
          .slice(0, Math.min(5, maxNodes - network.nodes.length));

        for (const node of nodesToExpand) {
          // Markiere den Knoten als erweitert
          node.expanded = true;

          // Rekursiv erweitern mit reduzierter Tiefe
          await this.enhanceNetworkWithOpenCitations(
            network,
            node.id,
            depth - 1,
            maxNodes
          );

          // Wenn die maximale Knotenzahl erreicht ist, breche ab
          if (network.nodes.length >= maxNodes) {
            break;
          }
        }
      }

      logger.info(`Zitationsnetzwerk für DOI ${doi} mit OpenCitations-Daten erweitert`, {
        nodesCount: network.nodes.length,
        edgesCount: network.edges.length
      });
    } catch (error) {
      logger.error('Fehler bei der Erweiterung des Netzwerks mit OpenCitations-Daten', {
        doi,
        error: error.message
      });
    }
  }
        if (!analysisResult.success) {
          throw new Error(`Fehler bei der Analyse von Zitationen: ${analysisResult.error}`);
        }
      }
      
      // Hole die aktualisierten Metriken
      const updatedMetrics = await this.databaseService.query(
        'SELECT * FROM publication_metrics WHERE doi = ?',
        [doi]
      );
      
      if (!updatedMetrics || updatedMetrics.length === 0) {
        throw new Error('Keine Metriken gefunden');
      }
      
      // Berechne den Einfluss basierend auf verschiedenen Faktoren
      const totalCitations = updatedMetrics[0].total_citations;
      const publicationYear = new Date(publication.publication_date).getFullYear();
      const currentYear = new Date().getFullYear();
      const yearsPublished = Math.max(1, currentYear - publicationYear);
      
      // Berechne den Einfluss
      // Formel: (Zitationen / Jahre seit Veröffentlichung) * (1 + log(h-Index))
      const influenceScore = (totalCitations / yearsPublished) * (1 + Math.log(updatedMetrics[0].h_index + 1));
      
      // Berechne das Perzentil im Vergleich zu anderen Publikationen im gleichen Fachgebiet
      const percentile = await this.calculatePercentile(doi, influenceScore, publication.field);
      
      // Speichere den Einfluss in der Datenbank
      await this.databaseService.query(
        'UPDATE publication_metrics SET influence_score = ?, percentile = ?, updated_at = NOW() WHERE doi = ?',
        [influenceScore, percentile, doi]
      );
      
      return {
        success: true,
        doi,
        influenceScore,
        percentile,
        totalCitations,
        yearsPublished
      };
    } catch (error) {
      logger.error('Fehler bei der Berechnung des Einflusses einer Publikation', {
        doi,
        error: error.message
      });
      
      return {
        success: false,
        doi,
        error: error.message
      };
    }
  }
  
  /**
   * Berechnet das Perzentil einer Publikation im Vergleich zu anderen im gleichen Fachgebiet
   * 
   * @param {string} doi - DOI der Publikation
   * @param {number} influenceScore - Einflusswert der Publikation
   * @param {string} field - Fachgebiet der Publikation
   * @returns {Promise<number>} Perzentil
   */
  async calculatePercentile(doi, influenceScore, field) {
    try {
      // Hole alle Einflussmetriken für Publikationen im gleichen Fachgebiet
      const fieldPublications = await this.databaseService.query(
        'SELECT pm.influence_score FROM publication_metrics pm JOIN publications p ON pm.doi = p.doi WHERE p.field = ? AND pm.doi != ?',
        [field, doi]
      );
      
      if (!fieldPublications || fieldPublications.length === 0) {
        return 100; // Wenn keine anderen Publikationen im Fachgebiet vorhanden sind
      }
      
      // Extrahiere die Einflusswerte
      const scores = fieldPublications.map(pub => pub.influence_score).filter(score => score !== null);
      
      if (scores.length === 0) {
        return 100;
      }
      
      // Zähle, wie viele Publikationen einen niedrigeren Einflusswert haben
      const lowerScores = scores.filter(score => score < influenceScore);
      
      // Berechne das Perzentil
      const percentile = (lowerScores.length / scores.length) * 100;
      
      return percentile;
    } catch (error) {
      logger.error('Fehler bei der Berechnung des Perzentils', {
        doi,
        field,
        error: error.message
      });
      return 50; // Standardwert bei Fehlern
    }
  }
  
  /**
   * Erstellt ein Zitationsnetzwerk für eine Publikation
   *
   * @param {string} doi - DOI der Publikation
   * @param {Object} options - Optionen für das Netzwerk
   * @param {number} [options.depth=1] - Tiefe des Netzwerks
   * @param {number} [options.maxNodes=50] - Maximale Anzahl von Knoten
   * @param {boolean} [options.useOpenCitations=true] - Ob OpenCitations-Daten verwendet werden sollen
   * @returns {Promise<Object>} Ergebnis der Netzwerkerstellung
   */
  async createCitationNetwork(doi, options = {}) {
    try {
      const depth = options.depth || 1;
      const maxNodes = options.maxNodes || 50;
      const useOpenCitations = options.useOpenCitations !== false; // Standardmäßig true

      logger.info(`Erstelle Zitationsnetzwerk für Publikation mit DOI ${doi} (Tiefe: ${depth}, Max. Knoten: ${maxNodes}, OpenCitations: ${useOpenCitations})`);

      // Hole die Publikationsdaten
      const publication = await this.getPublicationData(doi);

      if (!publication) {
        throw new Error(`Publikation mit DOI ${doi} nicht gefunden`);
      }

      // Erstelle das Netzwerk mit den lokalen Daten
      const network = await createCitationGraph(doi, this.databaseService, depth, maxNodes);

      // Erweitere das Netzwerk mit OpenCitations-Daten, wenn gewünscht
      if (useOpenCitations) {
        await this.enhanceNetworkWithOpenCitations(network, doi, depth, maxNodes);
      }

      // Speichere das Netzwerk in der Datenbank
      await this.storeCitationNetwork(doi, network);

      return {
        success: true,
        doi,
        network,
        depth,
        maxNodes,
        openCitationsEnhanced: useOpenCitations
      };
    } catch (error) {
      logger.error('Fehler bei der Erstellung eines Zitationsnetzwerks', {
        doi,
        error: error.message
      });

      return {
        success: false,
        doi,
        error: error.message
      };
    }

  /**
   * Erweitert ein Zitationsnetzwerk mit Daten von OpenCitations
   *
   * @private
   * @param {Object} network - Das zu erweiternde Netzwerk
   * @param {string} doi - DOI der Publikation
   * @param {number} depth - Tiefe des Netzwerks
   * @param {number} maxNodes - Maximale Anzahl von Knoten
   */
  async enhanceNetworkWithOpenCitations(network, doi, depth, maxNodes) {
    try {
      logger.info(`Erweitere Zitationsnetzwerk für DOI ${doi} mit OpenCitations-Daten`);

      // Hole OpenCitations-Daten für die Publikation
      const openCitationsData = await this.fetchOpenCitationsData(doi);

      if (!openCitationsData.success) {
        logger.warn(`Keine OpenCitations-Daten für DOI ${doi} verfügbar`);
        return;
      }

      // Füge eingehende Zitationen zum Netzwerk hinzu
      for (const citation of openCitationsData.citations) {
        // Prüfe, ob der Knoten bereits existiert
        const existingNode = network.nodes.find(node => node.id === citation.citing);

        if (!existingNode && network.nodes.length < maxNodes) {
          // Füge den neuen Knoten hinzu
          network.nodes.push({
            id: citation.citing,
            label: citation.citing,
            type: 'citing',
            oci: citation.oci,
            source: 'opencitations'
          });

          // Füge die Kante hinzu
          network.edges.push({
            source: citation.citing,
            target: doi,
            type: 'cites',
            oci: citation.oci,
            source: 'opencitations'
          });
        } else if (existingNode) {
          // Füge die OCI-Information zum bestehenden Knoten hinzu
          existingNode.oci = citation.oci;
          existingNode.source = existingNode.source ? `${existingNode.source},opencitations` : 'opencitations';

          // Prüfe, ob die Kante bereits existiert
          const existingEdge = network.edges.find(edge =>
            edge.source === citation.citing && edge.target === doi
          );

          if (!existingEdge) {
            // Füge die Kante hinzu
            network.edges.push({
              source: citation.citing,
              target: doi,
              type: 'cites',
              oci: citation.oci,
              source: 'opencitations'
            });
          } else {
            // Füge die OCI-Information zur bestehenden Kante hinzu
            existingEdge.oci = citation.oci;
            existingEdge.source = existingEdge.source ? `${existingEdge.source},opencitations` : 'opencitations';
          }
        }
      }

      // Füge ausgehende Zitationen zum Netzwerk hinzu
      for (const reference of openCitationsData.references) {
        // Prüfe, ob der Knoten bereits existiert
        const existingNode = network.nodes.find(node => node.id === reference.cited);

        if (!existingNode && network.nodes.length < maxNodes) {
          // Füge den neuen Knoten hinzu
          network.nodes.push({
            id: reference.cited,
            label: reference.cited,
            type: 'cited',
            oci: reference.oci,
            source: 'opencitations'
          });

          // Füge die Kante hinzu
          network.edges.push({
            source: doi,
            target: reference.cited,
            type: 'cites',
            oci: reference.oci,
            source: 'opencitations'
          });
        } else if (existingNode) {
          // Füge die OCI-Information zum bestehenden Knoten hinzu
          existingNode.oci = reference.oci;
          existingNode.source = existingNode.source ? `${existingNode.source},opencitations` : 'opencitations';

          // Prüfe, ob die Kante bereits existiert
          const existingEdge = network.edges.find(edge =>
            edge.source === doi && edge.target === reference.cited
          );

          if (!existingEdge) {
            // Füge die Kante hinzu
            network.edges.push({
              source: doi,
              target: reference.cited,
              type: 'cites',
              oci: reference.oci,
              source: 'opencitations'
            });
          } else {
            // Füge die OCI-Information zur bestehenden Kante hinzu
            existingEdge.oci = reference.oci;
            existingEdge.source = existingEdge.source ? `${existingEdge.source},opencitations` : 'opencitations';
          }
        }
      }

      // Wenn die Tiefe größer als 1 ist und noch Platz für Knoten ist, erweitere rekursiv
      if (depth > 1 && network.nodes.length < maxNodes) {
        // Beschränke die Anzahl der zu erweiternden Knoten, um Überlastung zu vermeiden
        const nodesToExpand = network.nodes
          .filter(node => node.id !== doi && !node.expanded)
          .slice(0, Math.min(5, maxNodes - network.nodes.length));

        for (const node of nodesToExpand) {
          // Markiere den Knoten als erweitert
          node.expanded = true;

          // Rekursiv erweitern mit reduzierter Tiefe
          await this.enhanceNetworkWithOpenCitations(
            network,
            node.id,
            depth - 1,
            maxNodes
          );

          // Wenn die maximale Knotenzahl erreicht ist, breche ab
          if (network.nodes.length >= maxNodes) {
            break;
          }
        }
      }

      logger.info(`Zitationsnetzwerk für DOI ${doi} mit OpenCitations-Daten erweitert`, {
        nodesCount: network.nodes.length,
        edgesCount: network.edges.length
      });
    } catch (error) {
      logger.error('Fehler bei der Erweiterung des Netzwerks mit OpenCitations-Daten', {
        doi,
        error: error.message
      });
    }
  }
    } catch (error) {
      logger.error('Fehler bei der Erstellung eines Zitationsnetzwerks', {
        doi,
        error: error.message
      });
      
      return {
        success: false,
        doi,
        error: error.message
      };
    }
  }
  
  /**
   * Speichert ein Zitationsnetzwerk in der Datenbank
   * 
   * @param {string} doi - DOI der Publikation
   * @param {Object} network - Zitationsnetzwerk
   */
  async storeCitationNetwork(doi, network) {
    try {
      // Prüfe, ob bereits ein Netzwerk für diese Publikation existiert
      const existingNetwork = await this.databaseService.query(
        'SELECT id FROM citation_networks WHERE doi = ?',
        [doi]
      );
      
      const networkJson = JSON.stringify(network);
      
      if (existingNetwork && existingNetwork.length > 0) {
        // Aktualisiere das bestehende Netzwerk
        await this.databaseService.query(
          'UPDATE citation_networks SET network_data = ?, nodes_count = ?, edges_count = ?, updated_at = NOW() WHERE doi = ?',
          [
            networkJson,
            network.nodes.length,
            network.edges.length,
            doi
          ]
        );
      } else {
        // Füge ein neues Netzwerk hinzu
        await this.databaseService.query(
          'INSERT INTO citation_networks (doi, network_data, nodes_count, edges_count, created_at, updated_at) VALUES (?, ?, ?, ?, NOW(), NOW())',
          [
            doi,
            networkJson,
            network.nodes.length,
            network.edges.length
          ]
        );
      }
      
      logger.info(`Zitationsnetzwerk für DOI ${doi} gespeichert`);
    } catch (error) {
      logger.error('Fehler beim Speichern des Zitationsnetzwerks', {
        doi,
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Identifiziert Schlüsselreferenzen für eine Publikation
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Ergebnis der Schlüsselreferenzidentifikation
   */
  async identifyKeyReferences(doi) {
    try {
      logger.info(`Identifiziere Schlüsselreferenzen für Publikation mit DOI ${doi}`);
      
      // Hole die zitierten Publikationen
      const citedPublications = await this.getCitedPublications(doi);
      
      if (citedPublications.length === 0) {
        return {
          success: true,
          doi,
          keyReferences: [],
          message: 'Keine zitierten Publikationen gefunden'
        };
      }
      
      // Hole die Zitationskontexte
      const citationContexts = await this.databaseService.query(
        'SELECT cited_text, context FROM citations WHERE citing_doi = ?',
        [doi]
      );
      
      // Berechne die Wichtigkeit jeder Referenz basierend auf verschiedenen Faktoren
      const references = [];
      
      for (const pub of citedPublications) {
        try {
          // Finde die Kontexte für diese Publikation
          const contexts = citationContexts.filter(ctx => {
            const parsedData = ctx.parsed_data ? JSON.parse(ctx.parsed_data) : {};
            return parsedData.doi === pub.doi;
          });
          
          // Hole die Zitationsmetriken für diese Publikation
          const metrics = await this.databaseService.query(
            'SELECT * FROM publication_metrics WHERE doi = ?',
            [pub.doi]
          );
          
          // Berechne die Wichtigkeit
          const importance = this.calculateReferenceImportance(pub, contexts, metrics[0]);
          
          references.push({
            doi: pub.doi,
            title: pub.title,
            authors: pub.authors,
            year: new Date(pub.publication_date).getFullYear(),
            importance,
            contexts: contexts.map(ctx => ctx.context)
          });
        } catch (error) {
          logger.error(`Fehler bei der Berechnung der Wichtigkeit einer Referenz`, {
            citingDoi: doi,
            citedDoi: pub.doi,
            error: error.message
          });
          // Fahre mit der nächsten Referenz fort
        }
      }
      
      // Sortiere die Referenzen nach Wichtigkeit
      const sortedReferences = references.sort((a, b) => b.importance - a.importance);
      
      // Wähle die Top-Referenzen
      const keyReferences = sortedReferences.slice(0, 10);
      
      return {
        success: true,
        doi,
        keyReferences,
        totalReferences: references.length
      };
    } catch (error) {
      logger.error('Fehler bei der Identifikation von Schlüsselreferenzen', {
        doi,
        error: error.message
      });
      
      return {
        success: false,
        doi,
        error: error.message
      };
    }
  }
  
  /**
   * Berechnet die Wichtigkeit einer Referenz
   * 
   * @param {Object} publication - Publikationsdaten
   * @param {Array<Object>} contexts - Zitationskontexte
   * @param {Object} metrics - Zitationsmetriken
   * @returns {number} Wichtigkeit der Referenz
   */
  calculateReferenceImportance(publication, contexts, metrics) {
    // Faktoren für die Wichtigkeit
    const contextCount = contexts.length; // Häufigkeit der Zitation
    const totalCitations = metrics ? metrics.total_citations : 0; // Einfluss der zitierten Publikation
    const publicationYear = new Date(publication.publication_date).getFullYear();
    const currentYear = new Date().getFullYear();
    const age = currentYear - publicationYear; // Alter der Publikation
    
    // Gewichte für die verschiedenen Faktoren
    const contextWeight = 0.4;
    const citationWeight = 0.4;
    const ageWeight = 0.2;
    
    // Normalisiere die Werte
    const normalizedContextCount = Math.min(contextCount / 5, 1); // Maximal 5 Kontexte
    const normalizedCitations = Math.min(totalCitations / 100, 1); // Maximal 100 Zitationen
    const normalizedAge = Math.max(0, 1 - (age / 20)); // Neuere Publikationen sind wichtiger
    
    // Berechne die Gesamtwichtigkeit
    const importance = (
      normalizedContextCount * contextWeight +
      normalizedCitations * citationWeight +
      normalizedAge * ageWeight
    );
    
    return importance;
  }
  
  /**
   * Extrahiert Patent-Zitationen aus einer Publikation
   * 
   * @param {string} doi - DOI der Publikation
   * @param {string} fullText - Volltext der Publikation
   * @returns {Promise<Object>} Ergebnis der Patent-Zitationsextraktion
   */
  async extractPatentCitations(doi, fullText) {
    try {
      logger.info(`Extrahiere Patent-Zitationen aus Publikation mit DOI ${doi}`);
      
      // Reguläre Ausdrücke für verschiedene Patentformate
      const patentRegexes = [
        // USPTO-Format: US XXXXXXX oder US XXXXXXX A
        /US\s+(\d{6,9})(?:\s+[A-Z]\d?)?/g,
        // EPO-Format: EP XXXXXXX oder EP XXXXXXX A1
        /EP\s+(\d{6,9})(?:\s+[A-Z]\d?)?/g,
        // WIPO-Format: WO XXXX/XXXXXX oder WO XXXX/XXXXXX A1
        /WO\s+(\d{4}\/\d{6})(?:\s+[A-Z]\d?)?/g,
        // Allgemeines Format: Patent No. XXXXXXX
        /Patent\s+No\.\s+(\d{6,9})/gi,
        // Allgemeines Format: Patent [US/EP/WO] XXXXXXX
        /Patent\s+([A-Z]{2})\s+(\d{6,9})/gi
      ];
      
      // Extrahiere alle Patent-IDs
      const patentCitations = [];
      let match;
      
      for (const regex of patentRegexes) {
        while ((match = regex.exec(fullText)) !== null) {
          // Extrahiere den Kontext um die Patent-ID
          const startIndex = Math.max(0, match.index - 100);
          const endIndex = Math.min(fullText.length, match.index + match[0].length + 100);
          const context = fullText.substring(startIndex, endIndex);
          
          // Bestimme das Patentamt
          let office;
          let patentId;
          
          if (match[0].startsWith('US')) {
            office = 'USPTO';
            patentId = `US${match[1]}`;
          } else if (match[0].startsWith('EP')) {
            office = 'EPO';
            patentId = `EP${match[1]}`;
          } else if (match[0].startsWith('WO')) {
            office = 'WIPO';
            patentId = `WO${match[1]}`;
          } else if (match[1]) {
            office = match[1];
            patentId = `${match[1]}${match[2]}`;
          } else {
            office = 'Unknown';
            patentId = match[1];
          }
          
          patentCitations.push({
            patentId,
            office,
            context,
            position: match.index
          });
        }
      }
      
      // Speichere die Patent-Zitationen in der Datenbank
      await this.storePatentCitations(doi, patentCitations);
      
      return {
        success: true,
        doi,
        patentCitations,
        count: patentCitations.length
      };
    } catch (error) {
      logger.error('Fehler bei der Extraktion von Patent-Zitationen', {
        doi,
        error: error.message
      });
      
      return {
        success: false,
        doi,
        error: error.message
      };
    }
  }
  
  /**
   * Speichert Patent-Zitationen in der Datenbank
   * 
   * @param {string} doi - DOI der Publikation
   * @param {Array<Object>} patentCitations - Extrahierte Patent-Zitationen
   */
  async storePatentCitations(doi, patentCitations) {
    try {
      // Speichere jede Patent-Zitation in der Datenbank
      for (const citation of patentCitations) {
        // Prüfe, ob die Patent-Zitation bereits in der Datenbank existiert
        const existingCitation = await this.databaseService.query(
          'SELECT id FROM patent_citations WHERE citing_doi = ? AND patent_id = ?',
          [doi, citation.patentId]
        );
        
        if (existingCitation && existingCitation.length > 0) {
          // Aktualisiere die bestehende Patent-Zitation
          await this.databaseService.query(
            'UPDATE patent_citations SET context = ?, position = ?, updated_at = NOW() WHERE id = ?',
            [
              citation.context,
              citation.position,
              existingCitation[0].id
            ]
          );
        } else {
          // Füge eine neue Patent-Zitation hinzu
          await this.databaseService.query(
            'INSERT INTO patent_citations (citing_doi, patent_id, patent_office, context, position, created_at, updated_at) VALUES (?, ?, ?, ?, ?, NOW(), NOW())',
            [
              doi,
              citation.patentId,
              citation.office,
              citation.context,
              citation.position
            ]
          );
        }
      }
      
      logger.info(`${patentCitations.length} Patent-Zitationen für DOI ${doi} gespeichert`);
    } catch (error) {
      logger.error('Fehler beim Speichern von Patent-Zitationen', {
        doi,
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Verknüpft Patent-Zitationen mit NFTs (Patent-IDs zu NFTs)
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Ergebnis der Verknüpfung
   */
  async linkPatentCitationsToNFTs(doi) {
    try {
      logger.info(`Verknüpfe Patent-Zitationen mit NFTs für Publikation mit DOI ${doi}`);
      
      // Hole die Patent-Zitationen
      const patentCitations = await this.databaseService.query(
        'SELECT * FROM patent_citations WHERE citing_doi = ?',
        [doi]
      );
      
      if (!patentCitations || patentCitations.length === 0) {
        return {
          success: true,
          doi,
          linkedPatentCitations: [],
          totalPatentCitations: 0,
          message: 'Keine Patent-Zitationen gefunden'
        };
      }
      
      // Verknüpfe jede Patent-Zitation mit einem NFT
      const linkedPatentCitations = [];
      
      for (const citation of patentCitations) {
        try {
          // Prüfe, ob das Patent bereits mit einem NFT verknüpft ist
          const existingNft = await this.databaseService.query(
            'SELECT nft_id FROM patent_nft_mapping WHERE patent_id = ?',
            [citation.patent_id]
          );
          
          let nftId;
          
          if (existingNft && existingNft.length > 0) {
            // Verwende die bestehende NFT-ID
            nftId = existingNft[0].nft_id;
            logger.debug(`Patent mit ID ${citation.patent_id} ist bereits mit NFT ${nftId} verknüpft`);
          } else {
            // Hole zusätzliche Patentdaten von externen Quellen (Semantic Scholar, Scinapse)
            const externalPatentData = await this.externalCitationService.getPatentData(
              citation.patent_id, 
              citation.patent_office
            );
            
            // Aktualisiere die Patentdaten in der Datenbank, wenn externe Daten verfügbar sind
            if (externalPatentData.success) {
              await this.externalCitationService.updatePatentData(
                citation.patent_id, 
                citation.patent_office, 
                this.databaseService
              );
            }
            
            // Erstelle ein neues NFT für das Patent
            const nftResult = await this.createNFTForPatent(citation.patent_id, citation.patent_office);
            
            if (nftResult.success) {
              nftId = nftResult.nftId;
              logger.info(`Neues NFT ${nftId} für Patent mit ID ${citation.patent_id} erstellt`);
            } else {
              logger.warn(`Konnte kein NFT für Patent mit ID ${citation.patent_id} erstellen: ${nftResult.message}`);
              continue;
            }
          }
          
          // Speichere die Verknüpfung in der Datenbank
          await this.databaseService.query(
            'INSERT INTO patent_citation_nft_links (citing_doi, patent_id, nft_id, created_at) VALUES (?, ?, ?, NOW())',
            [doi, citation.patent_id, nftId]
          );
          
          linkedPatentCitations.push({
            patentId: citation.patent_id,
            patentOffice: citation.patent_office,
            nftId
          });
        } catch (error) {
          logger.error(`Fehler bei der Verknüpfung von Patent-Zitation mit NFT`, {
            citingDoi: doi,
            patentId: citation.patent_id,
            error: error.message
          });
          // Fahre mit der nächsten Patent-Zitation fort
        }
      }
      
      // Registriere die Verknüpfungen auf der Blockchain
      if (linkedPatentCitations.length > 0) {
        await this.registerPatentCitationLinksOnBlockchain(doi, linkedPatentCitations);
      }
      
      return {
        success: true,
        doi,
        linkedPatentCitations,
        totalPatentCitations: patentCitations.length
      };
    } catch (error) {
      logger.error('Fehler bei der Verknüpfung von Patent-Zitationen mit NFTs', {
        doi,
        error: error.message
      });
      
      return {
        success: false,
        doi,
        error: error.message
      };
    }
  }
  
  /**
   * Erstellt ein NFT für eine Patent-ID
   * 
   * @param {string} patentId - Patent-ID
   * @param {string} patentOffice - Patentamt
   * @returns {Promise<Object>} Ergebnis der NFT-Erstellung
   */
  async createNFTForPatent(patentId, patentOffice) {
    try {
      // Hole die Patentdaten
      const patents = await this.databaseService.query(
        'SELECT * FROM patents WHERE patent_id = ?',
        [patentId]
      );
      
      let patent;
      
      if (!patents || patents.length === 0) {
        // Wenn das Patent nicht in der Datenbank ist, hole die Daten vom Patentamt
        const patentData = await this.fetchPatentData(patentId, patentOffice);
        
        if (!patentData.success) {
          return {
            success: false,
            patentId,
            message: `Konnte keine Patentdaten abrufen: ${patentData.message}`
          };
        }
        
        patent = patentData.patent;
        
        // Speichere das Patent in der Datenbank
        await this.databaseService.query(
          'INSERT INTO patents (patent_id, patent_office, title, inventors, assignee, filing_date, issue_date, abstract, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())',
          [
            patentId,
            patentOffice,
            patent.title,
            patent.inventors,
            patent.assignee,
            patent.filingDate,
            patent.issueDate,
            patent.abstract
          ]
        );
      } else {
        patent = patents[0];
      }
      
      // Erstelle Metadaten für das NFT
      const metadata = {
        name: patent.title,
        description: patent.abstract || 'Patent',
        image: `${doiNftConfig.metadata.imageBaseUrl}/patent/${encodeURIComponent(patentId)}`,
        external_url: this.getPatentUrl(patentId, patentOffice),
        attributes: [
          {
            trait_type: 'Inventors',
            value: patent.inventors
          },
          {
            trait_type: 'Assignee',
            value: patent.assignee
          },
          {
            trait_type: 'Patent Office',
            value: patentOffice
          },
          {
            display_type: 'date',
            trait_type: 'Filing Date',
            value: Math.floor(new Date(patent.filing_date).getTime() / 1000)
          },
          {
            display_type: 'date',
            trait_type: 'Issue Date',
            value: Math.floor(new Date(patent.issue_date).getTime() / 1000)
          }
        ]
      };
      
      // Speichere die Metadaten in IPFS
      const ipfsResult = await this.ipfsService.addContent(JSON.stringify(metadata));
      
      if (!ipfsResult.success) {
        return {
          success: false,
          patentId,
          message: 'Fehler beim Speichern der Metadaten in IPFS'
        };
      }
      
      // Präge das NFT auf der Blockchain
      const mintResult = await this.blockchainService.mintNFT({
        contractAddress: doiNftConfig.nft.contractAddress,
        metadataUri: `ipfs://${ipfsResult.hash}`,
        recipient: this.blockchainService.getDefaultAddress(),
        tokenType: 'PATENT',
        identifier: patentId
      });
      
      if (!mintResult.success) {
        return {
          success: false,
          patentId,
          message: `Fehler beim Prägen des NFT: ${mintResult.error}`
        };
      }
      
      // Speichere die Patent-ID-zu-NFT-Zuordnung in der Datenbank
      await this.databaseService.query(
        'INSERT INTO patent_nft_mapping (patent_id, patent_office, nft_id, contract_address, token_id, metadata_uri, created_at) VALUES (?, ?, ?, ?, ?, ?, NOW())',
        [
          patentId,
          patentOffice,
          mintResult.nftId,
          mintResult.contractAddress,
          mintResult.tokenId,
          `ipfs://${ipfsResult.hash}`
        ]
      );
      
      return {
        success: true,
        patentId,
        patentOffice,
        nftId: mintResult.nftId,
        contractAddress: mintResult.contractAddress,
        tokenId: mintResult.tokenId,
        metadataUri: `ipfs://${ipfsResult.hash}`
      };
    } catch (error) {
      logger.error('Fehler bei der Erstellung eines NFT für Patent', {
        patentId,
        patentOffice,
        error: error.message
      });
      
      return {
        success: false,
        patentId,
        message: error.message
      };
    }
  }
  
  /**
   * Holt Patentdaten von einem Patentamt
   * 
   * @param {string} patentId - Patent-ID
   * @param {string} patentOffice - Patentamt
   * @returns {Promise<Object>} Patentdaten
   */
  async fetchPatentData(patentId, patentOffice) {
    try {
      // Implementierung der Patentdatenabruf-Logik
      // Dies würde normalerweise eine API-Anfrage an das entsprechende Patentamt beinhalten
      
      // Beispielimplementierung
      return {
        success: true,
        patent: {
          title: `Patent ${patentId}`,
          inventors: 'Erfinder nicht verfügbar',
          assignee: 'Inhaber nicht verfügbar',
          filingDate: new Date().toISOString().split('T')[0],
          issueDate: new Date().toISOString().split('T')[0],
          abstract: 'Zusammenfassung nicht verfügbar'
        }
      };
    } catch (error) {
      logger.error('Fehler beim Abrufen von Patentdaten', {
        patentId,
        patentOffice,
        error: error.message
      });
      
      return {
        success: false,
        message: error.message
      };
    }
  }
  
  /**
   * Gibt die URL für ein Patent zurück
   * 
   * @param {string} patentId - Patent-ID
   * @param {string} patentOffice - Patentamt
   * @returns {string} Patent-URL
   */
  getPatentUrl(patentId, patentOffice) {
    switch (patentOffice) {
      case 'USPTO':
        return `https://patents.google.com/patent/US${patentId}`;
      case 'EPO':
        return `https://patents.google.com/patent/EP${patentId}`;
      case 'WIPO':
        return `https://patents.google.com/patent/WO${patentId}`;
      default:
        return `https://patents.google.com/patent/${patentId}`;
    }
  }
  
  /**
   * Registriert Patent-Zitationsverknüpfungen auf der Blockchain
   * 
   * @param {string} citingDoi - DOI der zitierenden Publikation
   * @param {Array<Object>} linkedPatentCitations - Verknüpfte Patent-Zitationen
   */
  async registerPatentCitationLinksOnBlockchain(citingDoi, linkedPatentCitations) {
    try {
      // Hole die NFT-ID der zitierenden Publikation
      const citingNft = await this.databaseService.query(
        'SELECT nft_id FROM doi_nft_mapping WHERE doi = ?',
        [citingDoi]
      );
      
      if (!citingNft || citingNft.length === 0) {
        logger.warn(`Keine NFT-ID für zitierende Publikation mit DOI ${citingDoi} gefunden`);
        return;
      }
      
      const citingNftId = citingNft[0].nft_id;
      
      // Erstelle ein Batch von Patent-Zitationsverknüpfungen
      const patentCitationLinks = linkedPatentCitations.map(citation => ({
        citingNftId,
        citedPatentNftId: citation.nftId,
        timestamp: Math.floor(Date.now() / 1000)
      }));
      
      // Registriere die Verknüpfungen auf der Blockchain
      const result = await this.blockchainService.registerPatentCitationLinks(patentCitationLinks);
      
      if (result.success) {
        logger.info(`Patent-Zitationsverknüpfungen für DOI ${citingDoi} auf der Blockchain registriert`, {
          transactionHash: result.transactionHash
        });
      } else {
        logger.warn(`Fehler bei der Registrierung von Patent-Zitationsverknüpfungen auf der Blockchain: ${result.error}`);
      }
    } catch (error) {
      logger.error('Fehler bei der Registrierung von Patent-Zitationsverknüpfungen auf der Blockchain', {
        citingDoi,
        error: error.message
      });
    }
  }
  
  /**
   * Analysiert den Zitationskontext
   * 
   * @param {string} doi - DOI der Publikation
   * @returns {Promise<Object>} Ergebnis der Kontextanalyse
   */
  async analyzeCitationContext(doi) {
    try {
      logger.info(`Analysiere Zitationskontext für Publikation mit DOI ${doi}`);
      
      // Hole die Zitationskontexte
      const citationContexts = await this.databaseService.query(
        'SELECT context FROM citations WHERE citing_doi = ?',
        [doi]
      );
      
      if (!citationContexts || citationContexts.length === 0) {
        return {
          success: true,
          doi,
          contextsAnalyzed: 0,
          topContexts: [],
          message: 'Keine Zitationskontexte gefunden'
        };
      }
      
      // Analysiere die Kontexte
      const contextTypes = {
        'background': 0,
        'methodology': 0,
        'results': 0,
        'discussion': 0,
        'comparison': 0,
        'critique': 0,
        'support': 0,
        'extension': 0,
        'other': 0
      };
      
      for (const ctx of citationContexts) {
        const context = ctx.context.toLowerCase();
        
        // Einfache Klassifikation basierend auf Schlüsselwörtern
        if (context.includes('background') || context.includes('introduction') || context.includes('previously')) {
          contextTypes.background++;
        } else if (context.includes('method') || context.includes('approach') || context.includes('technique')) {
          contextTypes.methodology++;
        } else if (context.includes('result') || context.includes('found') || context.includes('showed')) {
          contextTypes.results++;
        } else if (context.includes('discuss') || context.includes('suggest') || context.includes('implication')) {
          contextTypes.discussion++;
        } else if (context.includes('compare') || context.includes('similar') || context.includes('contrast')) {
          contextTypes.comparison++;
        } else if (context.includes('critique') || context.includes('limitation') || context.includes('problem')) {
          contextTypes.critique++;
        } else if (context.includes('support') || context.includes('confirm') || context.includes('agree')) {
          contextTypes.support++;
        } else if (context.includes('extend') || context.includes('build') || context.includes('improve')) {
          contextTypes.extension++;
        } else {
          contextTypes.other++;
        }
      }
      
      // Berechne die Prozentsätze
      const total = citationContexts.length;
      const contextPercentages = {};
      
      for (const type in contextTypes) {
        contextPercentages[type] = (contextTypes[type] / total) * 100;
      }
      
      // Erstelle eine sortierte Liste der Kontexttypen
      const topContexts = Object.entries(contextTypes)
        .map(([type, count]) => ({
          type,
          count,
          percentage: contextPercentages[type]
        }))
        .sort((a, b) => b.count - a.count);
      
      return {
        success: true,
        doi,
        contextsAnalyzed: total,
        topContexts,
        contextPercentages
      };
    } catch (error) {
      logger.error('Fehler bei der Analyse des Zitationskontexts', {
        doi,
        error: error.message
      });
      
      return {
        success: false,
        doi,
        error: error.message
      };
    }
  }
}

export default CitationAnalysisAgent;