# DeSci-Scholar AI Agents

Dieses Verzeichnis enthält die KI-Agenten für das DeSci-Scholar-Projekt, die verschiedene Aufgaben im Zusammenhang mit wissenschaftlichen Publikationen, Patenten und deren Verknüpfung mit NFTs automatisieren.

## Überblick

DeSci-Scholar ist eine Plattform, die wissenschaftliche Publikationen und Patente mit der Blockchain-Technologie verbindet, um eine dezentrale, transparente und offene Wissenschaftsinfrastruktur zu schaffen. Die KI-Agenten in diesem Verzeichnis unterstützen diese Mission durch automatisierte Analyse, Verarbeitung und Verknüpfung von wissenschaftlichen Inhalten.

## Agenten

### BaseAgent

Die Basisklasse für alle Agenten, die grundlegende Funktionalitäten wie Textgenerierung und Logging bereitstellt.

**Datei:** `index.js`

### CitationAnalysisAgent

Analysiert Zitationen und Referenzen in wissenschaftlichen Publikationen, berechnet Einflussmetriken und identifiziert Schlüsselreferenzen und -konzepte. Unterstützt auch die Verknüpfung von DOIs und Patent-IDs mit NFTs.

**Datei:** `CitationAnalysisAgent.js`

### EnhancedPatentIntegrationAgent

Verbindet sich mit den APIs großer Patentämter, integriert Patentdaten in die DeSci-Scholar-Plattform und verknüpft sie mit DOIs und NFTs. Nutzt den PatentCitationAnalyzer für erweiterte Zitationsanalysen.

**Datei:** `EnhancedPatentIntegrationAgent.js`

### PatentCitationAnalyzer

Analysiert Zitationsbeziehungen zwischen Patenten und wissenschaftlichen Publikationen, um Einblicke in Forschungstrends und Technologietransfer zu gewinnen.

**Datei:** `PatentCitationAnalyzer.js`

### StorageAgent

Verwaltet die dezentrale Speicherung von wissenschaftlichen Publikationen, Patenten und zugehörigen Metadaten. Nutzt verschiedene Speicherlösungen wie IPFS für Metadaten und BitTorrent für große Dateien, um optimale Effizienz und Zugänglichkeit zu gewährleisten.

**Datei:** `StorageAgent.js`

### IPNFTAgent

Implementiert die Funktionalität für IP-NFTs (Intellectual Property NFTs), die es ermöglichen, geistiges Eigentum wie wissenschaftliche Publikationen und Patente als NFTs zu tokenisieren und zu verwalten. Basierend auf dem Molecule-Protokoll für IP-NFTs.

**Datei:** `IPNFTAgent.js`

### PeerReviewAgent

Automatisiert den Peer-Review-Prozess für wissenschaftliche Publikationen, indem er Papiere analysiert und Bewertungen generiert.

**Datei:** `index.js`

### ProposalAgent

Bewertet Forschungsvorschläge und generiert Verbesserungsvorschläge.

**Datei:** `index.js`

### CollaborationAgent

Findet potenzielle Kollaborateure für Forscher und Projekte basierend auf Expertise und Forschungsinteressen.

**Datei:** `index.js`

### ResearchLearningAgent

Lernt aus wissenschaftlichen Publikationen, erkennt Zusammenhänge zwischen Forschungsgebieten, identifiziert Trends und unterstützt Forscher bei ihrer Arbeit.

**Datei:** `ResearchLearningAgent.js`

### SpecializedAgents

Enthält spezialisierte Agenten für bestimmte Aufgaben, wie z.B. die Bewertung von Forschungsvorschlägen.

**Datei:** `SpecializedAgents.js`

## Integration mit DeSci-Technologien

Die Agenten integrieren sich mit verschiedenen DeSci-Technologien:

1. **IPFS/Filecoin**: Für die dezentrale Speicherung von Metadaten und kleineren Dateien.
2. **BitTorrent**: Für die effiziente Verteilung großer Dateien wie Publikationen und Patente.
3. **Ethereum/Polygon/L2s**: Für die Erstellung und Verwaltung von NFTs und IP-NFTs mit Fokus auf Energieeffizienz.
4. **Molecule-Protokoll**: Für die Tokenisierung von geistigem Eigentum als IP-NFTs.
5. **OpenAI API**: Für die Textgenerierung und -analyse.

## Verwendung

Die Agenten können über die entsprechenden Klassen instanziiert und verwendet werden. Jeder Agent benötigt bestimmte Dienste und Konfigurationen, die über den Konstruktor übergeben werden.

Beispiel:

```javascript
import { CitationAnalysisAgent } from './ai/agents/CitationAnalysisAgent.js';
import { databaseService, blockchainService, storageManager } from './services/index.js';

const citationAgent = new CitationAnalysisAgent({
  databaseService,
  blockchainService,
  storageManager
});

await citationAgent.initialize();

const analysisResult = await citationAgent.analyzeCitations({
  doi: '10.1234/example.doi',
  title: 'Example Paper',
  abstract: 'This is an example abstract...',
  fullText: '...'
});

console.log(analysisResult);
```

## Konfiguration

Die Agenten können über Umgebungsvariablen und Konfigurationsobjekte konfiguriert werden. Wichtige Umgebungsvariablen sind:

- `OPENAI_API_KEY`: API-Schlüssel für OpenAI
- `AI_AGENT_MODEL`: Zu verwendendes Sprachmodell (Standard: 'gpt-4')
- `IP_NFT_CONTRACT_ADDRESS`: Adresse des IP-NFT-Vertrags
- `DEFAULT_NFT_RECIPIENT`: Standardadresse für NFT-Empfänger
- `IPFS_API_URL`: URL des IPFS-API
- `ETHEREUM_PROVIDER_URL`: URL des Ethereum-Providers
- `STORAGE_SIZE_THRESHOLD`: Schwellenwert für die Auswahl des Speicheradapters (in Bytes)

## Erweiterung

Um einen neuen Agenten hinzuzufügen:

1. Erstelle eine neue Datei für den Agenten
2. Importiere die `BaseAgent`-Klasse aus `index.js`
3. Erstelle eine neue Klasse, die von `BaseAgent` erbt
4. Implementiere die erforderlichen Methoden
5. Exportiere die Klasse

## Ressourcen

- [DeSci Labs](https://www.desci.com/)
- [Molecule Protocol](https://www.molecule.xyz/)
- [IPFS](https://ipfs.tech/)
- [Filecoin](https://filecoin.io/)
- [BitTorrent](https://www.bittorrent.com/)
- [Ethereum](https://ethereum.org/)
- [Polygon](https://polygon.technology/)
- [OpenAI API](https://openai.com/api/)# DeSci-Scholar AI Agents

Dieses Verzeichnis enthält die KI-Agenten für das DeSci-Scholar-Projekt, die verschiedene Aufgaben im Zusammenhang mit wissenschaftlichen Publikationen, Patenten und deren Verknüpfung mit NFTs automatisieren.

## Überblick

DeSci-Scholar ist eine Plattform, die wissenschaftliche Publikationen und Patente mit der Blockchain-Technologie verbindet, um eine dezentrale, transparente und offene Wissenschaftsinfrastruktur zu schaffen. Die KI-Agenten in diesem Verzeichnis unterstützen diese Mission durch automatisierte Analyse, Verarbeitung und Verknüpfung von wissenschaftlichen Inhalten.

## Agenten

### BaseAgent

Die Basisklasse für alle Agenten, die grundlegende Funktionalitäten wie Textgenerierung und Logging bereitstellt.

**Datei:** `index.js`

### CitationAnalysisAgent

Analysiert Zitationen und Referenzen in wissenschaftlichen Publikationen, berechnet Einflussmetriken und identifiziert Schlüsselreferenzen und -konzepte. Unterstützt auch die Verknüpfung von DOIs und Patent-IDs mit NFTs.

**Datei:** `CitationAnalysisAgent.js`

### EnhancedPatentIntegrationAgent

Verbindet sich mit den APIs großer Patentämter, integriert Patentdaten in die DeSci-Scholar-Plattform und verknüpft sie mit DOIs und NFTs. Nutzt den PatentCitationAnalyzer für erweiterte Zitationsanalysen.

**Datei:** `EnhancedPatentIntegrationAgent.js`

### PatentCitationAnalyzer

Analysiert Zitationsbeziehungen zwischen Patenten und wissenschaftlichen Publikationen, um Einblicke in Forschungstrends und Technologietransfer zu gewinnen.

**Datei:** `PatentCitationAnalyzer.js`

### StorageAgent

Verwaltet die dezentrale Speicherung von wissenschaftlichen Publikationen, Patenten und zugehörigen Metadaten. Nutzt verschiedene Speicherlösungen wie IPFS für Metadaten und BitTorrent für große Dateien, um optimale Effizienz und Zugänglichkeit zu gewährleisten.

**Datei:** `StorageAgent.js`

### IPNFTAgent

Implementiert die Funktionalität für IP-NFTs (Intellectual Property NFTs), die es ermöglichen, geistiges Eigentum wie wissenschaftliche Publikationen und Patente als NFTs zu tokenisieren und zu verwalten. Basierend auf dem Molecule-Protokoll für IP-NFTs.

**Datei:** `IPNFTAgent.js`

### PeerReviewAgent

Automatisiert den Peer-Review-Prozess für wissenschaftliche Publikationen, indem er Papiere analysiert und Bewertungen generiert.

**Datei:** `index.js`

### ProposalAgent

Bewertet Forschungsvorschläge und generiert Verbesserungsvorschläge.

**Datei:** `index.js`

### CollaborationAgent

Findet potenzielle Kollaborateure für Forscher und Projekte basierend auf Expertise und Forschungsinteressen.

**Datei:** `index.js`

### ResearchLearningAgent

Lernt aus wissenschaftlichen Publikationen, erkennt Zusammenhänge zwischen Forschungsgebieten, identifiziert Trends und unterstützt Forscher bei ihrer Arbeit.

**Datei:** `ResearchLearningAgent.js`

### SpecializedAgents

Enthält spezialisierte Agenten für bestimmte Aufgaben, wie z.B. die Bewertung von Forschungsvorschlägen.

**Datei:** `SpecializedAgents.js`

## Integration mit DeSci-Technologien

Die Agenten integrieren sich mit verschiedenen DeSci-Technologien:

1. **IPFS/Filecoin**: Für die dezentrale Speicherung von Metadaten und kleineren Dateien.
2. **BitTorrent**: Für die effiziente Verteilung großer Dateien wie Publikationen und Patente.
3. **Ethereum/Polygon/L2s**: Für die Erstellung und Verwaltung von NFTs und IP-NFTs mit Fokus auf Energieeffizienz.
4. **Molecule-Protokoll**: Für die Tokenisierung von geistigem Eigentum als IP-NFTs.
5. **OpenAI API**: Für die Textgenerierung und -analyse.

## Verwendung

Die Agenten können über die entsprechenden Klassen instanziiert und verwendet werden. Jeder Agent benötigt bestimmte Dienste und Konfigurationen, die über den Konstruktor übergeben werden.

Beispiel:

```javascript
import { CitationAnalysisAgent } from './ai/agents/CitationAnalysisAgent.js';
import { databaseService, blockchainService, storageManager } from './services/index.js';

const citationAgent = new CitationAnalysisAgent({
  databaseService,
  blockchainService,
  storageManager
});

await citationAgent.initialize();

const analysisResult = await citationAgent.analyzeCitations({
  doi: '10.1234/example.doi',
  title: 'Example Paper',
  abstract: 'This is an example abstract...',
  fullText: '...'
});

console.log(analysisResult);
```

## Konfiguration

Die Agenten können über Umgebungsvariablen und Konfigurationsobjekte konfiguriert werden. Wichtige Umgebungsvariablen sind:

- `OPENAI_API_KEY`: API-Schlüssel für OpenAI
- `AI_AGENT_MODEL`: Zu verwendendes Sprachmodell (Standard: 'gpt-4')
- `IP_NFT_CONTRACT_ADDRESS`: Adresse des IP-NFT-Vertrags
- `DEFAULT_NFT_RECIPIENT`: Standardadresse für NFT-Empfänger
- `IPFS_API_URL`: URL des IPFS-API
- `ETHEREUM_PROVIDER_URL`: URL des Ethereum-Providers
- `STORAGE_SIZE_THRESHOLD`: Schwellenwert für die Auswahl des Speicheradapters (in Bytes)

## Erweiterung

Um einen neuen Agenten hinzuzufügen:

1. Erstelle eine neue Datei für den Agenten
2. Importiere die `BaseAgent`-Klasse aus `index.js`
3. Erstelle eine neue Klasse, die von `BaseAgent` erbt
4. Implementiere die erforderlichen Methoden
5. Exportiere die Klasse

## Ressourcen

- [DeSci Labs](https://www.desci.com/)
- [Molecule Protocol](https://www.molecule.xyz/)
- [IPFS](https://ipfs.tech/)
- [Filecoin](https://filecoin.io/)
- [BitTorrent](https://www.bittorrent.com/)
- [Ethereum](https://ethereum.org/)
- [Polygon](https://polygon.technology/)
- [OpenAI API](https://openai.com/api/)# DeSci-Scholar AI Agents

Dieses Verzeichnis enthält die KI-Agenten für das DeSci-Scholar-Projekt, die verschiedene Aufgaben im Zusammenhang mit wissenschaftlichen Publikationen, Patenten und deren Verknüpfung mit NFTs automatisieren.

## Überblick

DeSci-Scholar ist eine Plattform, die wissenschaftliche Publikationen und Patente mit der Blockchain-Technologie verbindet, um eine dezentrale, transparente und offene Wissenschaftsinfrastruktur zu schaffen. Die KI-Agenten in diesem Verzeichnis unterstützen diese Mission durch automatisierte Analyse, Verarbeitung und Verknüpfung von wissenschaftlichen Inhalten.

## Agenten

### BaseAgent

Die Basisklasse für alle Agenten, die grundlegende Funktionalitäten wie Textgenerierung und Logging bereitstellt.

**Datei:** `index.js`

### CitationAnalysisAgent

Analysiert Zitationen und Referenzen in wissenschaftlichen Publikationen, berechnet Einflussmetriken und identifiziert Schlüsselreferenzen und -konzepte. Unterstützt auch die Verknüpfung von DOIs und Patent-IDs mit NFTs.

**Datei:** `CitationAnalysisAgent.js`

### EnhancedPatentIntegrationAgent

Verbindet sich mit den APIs großer Patentämter, integriert Patentdaten in die DeSci-Scholar-Plattform und verknüpft sie mit DOIs und NFTs. Nutzt den PatentCitationAnalyzer für erweiterte Zitationsanalysen.

**Datei:** `EnhancedPatentIntegrationAgent.js`

### PatentCitationAnalyzer

Analysiert Zitationsbeziehungen zwischen Patenten und wissenschaftlichen Publikationen, um Einblicke in Forschungstrends und Technologietransfer zu gewinnen.

**Datei:** `PatentCitationAnalyzer.js`

### StorageAgent

Verwaltet die dezentrale Speicherung von wissenschaftlichen Publikationen, Patenten und zugehörigen Metadaten. Nutzt verschiedene Speicherlösungen wie IPFS für Metadaten und BitTorrent für große Dateien, um optimale Effizienz und Zugänglichkeit zu gewährleisten.

**Datei:** `StorageAgent.js`

### IPNFTAgent

Implementiert die Funktionalität für IP-NFTs (Intellectual Property NFTs), die es ermöglichen, geistiges Eigentum wie wissenschaftliche Publikationen und Patente als NFTs zu tokenisieren und zu verwalten. Basierend auf dem Molecule-Protokoll für IP-NFTs.

**Datei:** `IPNFTAgent.js`

### PeerReviewAgent

Automatisiert den Peer-Review-Prozess für wissenschaftliche Publikationen, indem er Papiere analysiert und Bewertungen generiert.

**Datei:** `index.js`

### ProposalAgent

Bewertet Forschungsvorschläge und generiert Verbesserungsvorschläge.

**Datei:** `index.js`

### CollaborationAgent

Findet potenzielle Kollaborateure für Forscher und Projekte basierend auf Expertise und Forschungsinteressen.

**Datei:** `index.js`

### ResearchLearningAgent

Lernt aus wissenschaftlichen Publikationen, erkennt Zusammenhänge zwischen Forschungsgebieten, identifiziert Trends und unterstützt Forscher bei ihrer Arbeit.

**Datei:** `ResearchLearningAgent.js`

### SpecializedAgents

Enthält spezialisierte Agenten für bestimmte Aufgaben, wie z.B. die Bewertung von Forschungsvorschlägen.

**Datei:** `SpecializedAgents.js`

## Integration mit DeSci-Technologien

Die Agenten integrieren sich mit verschiedenen DeSci-Technologien:

1. **IPFS/Filecoin**: Für die dezentrale Speicherung von Metadaten und kleineren Dateien.
2. **BitTorrent**: Für die effiziente Verteilung großer Dateien wie Publikationen und Patente.
3. **Ethereum/Polygon/L2s**: Für die Erstellung und Verwaltung von NFTs und IP-NFTs mit Fokus auf Energieeffizienz.
4. **Molecule-Protokoll**: Für die Tokenisierung von geistigem Eigentum als IP-NFTs.
5. **OpenAI API**: Für die Textgenerierung und -analyse.

## Verwendung

Die Agenten können über die entsprechenden Klassen instanziiert und verwendet werden. Jeder Agent benötigt bestimmte Dienste und Konfigurationen, die über den Konstruktor übergeben werden.

Beispiel:

```javascript
import { CitationAnalysisAgent } from './ai/agents/CitationAnalysisAgent.js';
import { databaseService, blockchainService, storageManager } from './services/index.js';

const citationAgent = new CitationAnalysisAgent({
  databaseService,
  blockchainService,
  storageManager
});

await citationAgent.initialize();

const analysisResult = await citationAgent.analyzeCitations({
  doi: '10.1234/example.doi',
  title: 'Example Paper',
  abstract: 'This is an example abstract...',
  fullText: '...'
});

console.log(analysisResult);
```

## Konfiguration

Die Agenten können über Umgebungsvariablen und Konfigurationsobjekte konfiguriert werden. Wichtige Umgebungsvariablen sind:

- `OPENAI_API_KEY`: API-Schlüssel für OpenAI
- `AI_AGENT_MODEL`: Zu verwendendes Sprachmodell (Standard: 'gpt-4')
- `IP_NFT_CONTRACT_ADDRESS`: Adresse des IP-NFT-Vertrags
- `DEFAULT_NFT_RECIPIENT`: Standardadresse für NFT-Empfänger
- `IPFS_API_URL`: URL des IPFS-API
- `ETHEREUM_PROVIDER_URL`: URL des Ethereum-Providers
- `STORAGE_SIZE_THRESHOLD`: Schwellenwert für die Auswahl des Speicheradapters (in Bytes)

## Erweiterung

Um einen neuen Agenten hinzuzufügen:

1. Erstelle eine neue Datei für den Agenten
2. Importiere die `BaseAgent`-Klasse aus `index.js`
3. Erstelle eine neue Klasse, die von `BaseAgent` erbt
4. Implementiere die erforderlichen Methoden
5. Exportiere die Klasse

## Ressourcen

- [DeSci Labs](https://www.desci.com/)
- [Molecule Protocol](https://www.molecule.xyz/)
- [IPFS](https://ipfs.tech/)
- [Filecoin](https://filecoin.io/)
- [BitTorrent](https://www.bittorrent.com/)
- [Ethereum](https://ethereum.org/)
- [Polygon](https://polygon.technology/)
- [OpenAI API](https://openai.com/api/)# DeSci-Scholar AI Agents

Dieses Verzeichnis enthält die KI-Agenten für das DeSci-Scholar-Projekt, die verschiedene Aufgaben im Zusammenhang mit wissenschaftlichen Publikationen, Patenten und deren Verknüpfung mit NFTs automatisieren.

## Überblick

DeSci-Scholar ist eine Plattform, die wissenschaftliche Publikationen und Patente mit der Blockchain-Technologie verbindet, um eine dezentrale, transparente und offene Wissenschaftsinfrastruktur zu schaffen. Die KI-Agenten in diesem Verzeichnis unterstützen diese Mission durch automatisierte Analyse, Verarbeitung und Verknüpfung von wissenschaftlichen Inhalten.

## Agenten

### BaseAgent

Die Basisklasse für alle Agenten, die grundlegende Funktionalitäten wie Textgenerierung und Logging bereitstellt.

**Datei:** `index.js`

### CitationAnalysisAgent

Analysiert Zitationen und Referenzen in wissenschaftlichen Publikationen, berechnet Einflussmetriken und identifiziert Schlüsselreferenzen und -konzepte. Unterstützt auch die Verknüpfung von DOIs und Patent-IDs mit NFTs.

**Datei:** `CitationAnalysisAgent.js`

### EnhancedPatentIntegrationAgent

Verbindet sich mit den APIs großer Patentämter, integriert Patentdaten in die DeSci-Scholar-Plattform und verknüpft sie mit DOIs und NFTs. Nutzt den PatentCitationAnalyzer für erweiterte Zitationsanalysen.

**Datei:** `EnhancedPatentIntegrationAgent.js`

### PatentCitationAnalyzer

Analysiert Zitationsbeziehungen zwischen Patenten und wissenschaftlichen Publikationen, um Einblicke in Forschungstrends und Technologietransfer zu gewinnen.

**Datei:** `PatentCitationAnalyzer.js`

### StorageAgent

Verwaltet die dezentrale Speicherung von wissenschaftlichen Publikationen, Patenten und zugehörigen Metadaten. Nutzt verschiedene Speicherlösungen wie IPFS für Metadaten und BitTorrent für große Dateien, um optimale Effizienz und Zugänglichkeit zu gewährleisten.

**Datei:** `StorageAgent.js`

### IPNFTAgent

Implementiert die Funktionalität für IP-NFTs (Intellectual Property NFTs), die es ermöglichen, geistiges Eigentum wie wissenschaftliche Publikationen und Patente als NFTs zu tokenisieren und zu verwalten. Basierend auf dem Molecule-Protokoll für IP-NFTs.

**Datei:** `IPNFTAgent.js`

### PeerReviewAgent

Automatisiert den Peer-Review-Prozess für wissenschaftliche Publikationen, indem er Papiere analysiert und Bewertungen generiert.

**Datei:** `index.js`

### ProposalAgent

Bewertet Forschungsvorschläge und generiert Verbesserungsvorschläge.

**Datei:** `index.js`

### CollaborationAgent

Findet potenzielle Kollaborateure für Forscher und Projekte basierend auf Expertise und Forschungsinteressen.

**Datei:** `index.js`

### ResearchLearningAgent

Lernt aus wissenschaftlichen Publikationen, erkennt Zusammenhänge zwischen Forschungsgebieten, identifiziert Trends und unterstützt Forscher bei ihrer Arbeit.

**Datei:** `ResearchLearningAgent.js`

### SpecializedAgents

Enthält spezialisierte Agenten für bestimmte Aufgaben, wie z.B. die Bewertung von Forschungsvorschlägen.

**Datei:** `SpecializedAgents.js`

## Integration mit DeSci-Technologien

Die Agenten integrieren sich mit verschiedenen DeSci-Technologien:

1. **IPFS/Filecoin**: Für die dezentrale Speicherung von Metadaten und kleineren Dateien.
2. **BitTorrent**: Für die effiziente Verteilung großer Dateien wie Publikationen und Patente.
3. **Ethereum/Polygon/L2s**: Für die Erstellung und Verwaltung von NFTs und IP-NFTs mit Fokus auf Energieeffizienz.
4. **Molecule-Protokoll**: Für die Tokenisierung von geistigem Eigentum als IP-NFTs.
5. **OpenAI API**: Für die Textgenerierung und -analyse.

## Verwendung

Die Agenten können über die entsprechenden Klassen instanziiert und verwendet werden. Jeder Agent benötigt bestimmte Dienste und Konfigurationen, die über den Konstruktor übergeben werden.

Beispiel:

```javascript
import { CitationAnalysisAgent } from './ai/agents/CitationAnalysisAgent.js';
import { databaseService, blockchainService, storageManager } from './services/index.js';

const citationAgent = new CitationAnalysisAgent({
  databaseService,
  blockchainService,
  storageManager
});

await citationAgent.initialize();

const analysisResult = await citationAgent.analyzeCitations({
  doi: '10.1234/example.doi',
  title: 'Example Paper',
  abstract: 'This is an example abstract...',
  fullText: '...'
});

console.log(analysisResult);
```

## Konfiguration

Die Agenten können über Umgebungsvariablen und Konfigurationsobjekte konfiguriert werden. Wichtige Umgebungsvariablen sind:

- `OPENAI_API_KEY`: API-Schlüssel für OpenAI
- `AI_AGENT_MODEL`: Zu verwendendes Sprachmodell (Standard: 'gpt-4')
- `IP_NFT_CONTRACT_ADDRESS`: Adresse des IP-NFT-Vertrags
- `DEFAULT_NFT_RECIPIENT`: Standardadresse für NFT-Empfänger
- `IPFS_API_URL`: URL des IPFS-API
- `ETHEREUM_PROVIDER_URL`: URL des Ethereum-Providers
- `STORAGE_SIZE_THRESHOLD`: Schwellenwert für die Auswahl des Speicheradapters (in Bytes)

## Erweiterung

Um einen neuen Agenten hinzuzufügen:

1. Erstelle eine neue Datei für den Agenten
2. Importiere die `BaseAgent`-Klasse aus `index.js`
3. Erstelle eine neue Klasse, die von `BaseAgent` erbt
4. Implementiere die erforderlichen Methoden
5. Exportiere die Klasse

## Ressourcen

- [DeSci Labs](https://www.desci.com/)
- [Molecule Protocol](https://www.molecule.xyz/)
- [IPFS](https://ipfs.tech/)
- [Filecoin](https://filecoin.io/)
- [BitTorrent](https://www.bittorrent.com/)
- [Ethereum](https://ethereum.org/)
- [Polygon](https://polygon.technology/)
- [OpenAI API](https://openai.com/api/)# DeSci-Scholar AI Agents

Dieses Verzeichnis enthält die KI-Agenten für das DeSci-Scholar-Projekt, die verschiedene Aufgaben im Zusammenhang mit wissenschaftlichen Publikationen, Patenten und deren Verknüpfung mit NFTs automatisieren.

## Überblick

DeSci-Scholar ist eine Plattform, die wissenschaftliche Publikationen und Patente mit der Blockchain-Technologie verbindet, um eine dezentrale, transparente und offene Wissenschaftsinfrastruktur zu schaffen. Die KI-Agenten in diesem Verzeichnis unterstützen diese Mission durch automatisierte Analyse, Verarbeitung und Verknüpfung von wissenschaftlichen Inhalten.

## Agenten

### BaseAgent

Die Basisklasse für alle Agenten, die grundlegende Funktionalitäten wie Textgenerierung und Logging bereitstellt.

**Datei:** `index.js`

### CitationAnalysisAgent

Analysiert Zitationen und Referenzen in wissenschaftlichen Publikationen, berechnet Einflussmetriken und identifiziert Schlüsselreferenzen und -konzepte. Unterstützt auch die Verknüpfung von DOIs und Patent-IDs mit NFTs.

**Datei:** `CitationAnalysisAgent.js`

### EnhancedPatentIntegrationAgent

Verbindet sich mit den APIs großer Patentämter, integriert Patentdaten in die DeSci-Scholar-Plattform und verknüpft sie mit DOIs und NFTs. Nutzt den PatentCitationAnalyzer für erweiterte Zitationsanalysen.

**Datei:** `EnhancedPatentIntegrationAgent.js`

### PatentCitationAnalyzer

Analysiert Zitationsbeziehungen zwischen Patenten und wissenschaftlichen Publikationen, um Einblicke in Forschungstrends und Technologietransfer zu gewinnen.

**Datei:** `PatentCitationAnalyzer.js`

### StorageAgent

Verwaltet die dezentrale Speicherung von wissenschaftlichen Publikationen, Patenten und zugehörigen Metadaten. Nutzt verschiedene Speicherlösungen wie IPFS für Metadaten und BitTorrent für große Dateien, um optimale Effizienz und Zugänglichkeit zu gewährleisten.

**Datei:** `StorageAgent.js`

### IPNFTAgent

Implementiert die Funktionalität für IP-NFTs (Intellectual Property NFTs), die es ermöglichen, geistiges Eigentum wie wissenschaftliche Publikationen und Patente als NFTs zu tokenisieren und zu verwalten. Basierend auf dem Molecule-Protokoll für IP-NFTs.

**Datei:** `IPNFTAgent.js`

### PeerReviewAgent

Automatisiert den Peer-Review-Prozess für wissenschaftliche Publikationen, indem er Papiere analysiert und Bewertungen generiert.

**Datei:** `index.js`

### ProposalAgent

Bewertet Forschungsvorschläge und generiert Verbesserungsvorschläge.

**Datei:** `index.js`

### CollaborationAgent

Findet potenzielle Kollaborateure für Forscher und Projekte basierend auf Expertise und Forschungsinteressen.

**Datei:** `index.js`

### ResearchLearningAgent

Lernt aus wissenschaftlichen Publikationen, erkennt Zusammenhänge zwischen Forschungsgebieten, identifiziert Trends und unterstützt Forscher bei ihrer Arbeit.

**Datei:** `ResearchLearningAgent.js`

### SpecializedAgents

Enthält spezialisierte Agenten für bestimmte Aufgaben, wie z.B. die Bewertung von Forschungsvorschlägen.

**Datei:** `SpecializedAgents.js`

## Integration mit DeSci-Technologien

Die Agenten integrieren sich mit verschiedenen DeSci-Technologien:

1. **IPFS/Filecoin**: Für die dezentrale Speicherung von Metadaten und kleineren Dateien.
2. **BitTorrent**: Für die effiziente Verteilung großer Dateien wie Publikationen und Patente.
3. **Ethereum/Polygon/L2s**: Für die Erstellung und Verwaltung von NFTs und IP-NFTs mit Fokus auf Energieeffizienz.
4. **Molecule-Protokoll**: Für die Tokenisierung von geistigem Eigentum als IP-NFTs.
5. **OpenAI API**: Für die Textgenerierung und -analyse.

## Verwendung

Die Agenten können über die entsprechenden Klassen instanziiert und verwendet werden. Jeder Agent benötigt bestimmte Dienste und Konfigurationen, die über den Konstruktor übergeben werden.

Beispiel:

```javascript
import { CitationAnalysisAgent } from './ai/agents/CitationAnalysisAgent.js';
import { databaseService, blockchainService, storageManager } from './services/index.js';

const citationAgent = new CitationAnalysisAgent({
  databaseService,
  blockchainService,
  storageManager
});

await citationAgent.initialize();

const analysisResult = await citationAgent.analyzeCitations({
  doi: '10.1234/example.doi',
  title: 'Example Paper',
  abstract: 'This is an example abstract...',
  fullText: '...'
});

console.log(analysisResult);
```

## Konfiguration

Die Agenten können über Umgebungsvariablen und Konfigurationsobjekte konfiguriert werden. Wichtige Umgebungsvariablen sind:

- `OPENAI_API_KEY`: API-Schlüssel für OpenAI
- `AI_AGENT_MODEL`: Zu verwendendes Sprachmodell (Standard: 'gpt-4')
- `IP_NFT_CONTRACT_ADDRESS`: Adresse des IP-NFT-Vertrags
- `DEFAULT_NFT_RECIPIENT`: Standardadresse für NFT-Empfänger
- `IPFS_API_URL`: URL des IPFS-API
- `ETHEREUM_PROVIDER_URL`: URL des Ethereum-Providers
- `STORAGE_SIZE_THRESHOLD`: Schwellenwert für die Auswahl des Speicheradapters (in Bytes)

## Erweiterung

Um einen neuen Agenten hinzuzufügen:

1. Erstelle eine neue Datei für den Agenten
2. Importiere die `BaseAgent`-Klasse aus `index.js`
3. Erstelle eine neue Klasse, die von `BaseAgent` erbt
4. Implementiere die erforderlichen Methoden
5. Exportiere die Klasse

## Ressourcen

- [DeSci Labs](https://www.desci.com/)
- [Molecule Protocol](https://www.molecule.xyz/)
- [IPFS](https://ipfs.tech/)
- [Filecoin](https://filecoin.io/)
- [BitTorrent](https://www.bittorrent.com/)
- [Ethereum](https://ethereum.org/)
- [Polygon](https://polygon.technology/)
- [OpenAI API](https://openai.com/api/)# DeSci-Scholar AI Agents

Dieses Verzeichnis enthält die KI-Agenten für das DeSci-Scholar-Projekt, die verschiedene Aufgaben im Zusammenhang mit wissenschaftlichen Publikationen, Patenten und deren Verknüpfung mit NFTs automatisieren.

## Überblick

DeSci-Scholar ist eine Plattform, die wissenschaftliche Publikationen und Patente mit der Blockchain-Technologie verbindet, um eine dezentrale, transparente und offene Wissenschaftsinfrastruktur zu schaffen. Die KI-Agenten in diesem Verzeichnis unterstützen diese Mission durch automatisierte Analyse, Verarbeitung und Verknüpfung von wissenschaftlichen Inhalten.

## Agenten

### BaseAgent

Die Basisklasse für alle Agenten, die grundlegende Funktionalitäten wie Textgenerierung und Logging bereitstellt.

**Datei:** `index.js`

### CitationAnalysisAgent

Analysiert Zitationen und Referenzen in wissenschaftlichen Publikationen, berechnet Einflussmetriken und identifiziert Schlüsselreferenzen und -konzepte. Unterstützt auch die Verknüpfung von DOIs und Patent-IDs mit NFTs.

**Datei:** `CitationAnalysisAgent.js`

### EnhancedPatentIntegrationAgent

Verbindet sich mit den APIs großer Patentämter, integriert Patentdaten in die DeSci-Scholar-Plattform und verknüpft sie mit DOIs und NFTs. Nutzt den PatentCitationAnalyzer für erweiterte Zitationsanalysen.

**Datei:** `EnhancedPatentIntegrationAgent.js`

### PatentCitationAnalyzer

Analysiert Zitationsbeziehungen zwischen Patenten und wissenschaftlichen Publikationen, um Einblicke in Forschungstrends und Technologietransfer zu gewinnen.

**Datei:** `PatentCitationAnalyzer.js`

### StorageAgent

Verwaltet die dezentrale Speicherung von wissenschaftlichen Publikationen, Patenten und zugehörigen Metadaten. Nutzt verschiedene Speicherlösungen wie IPFS für Metadaten und BitTorrent für große Dateien, um optimale Effizienz und Zugänglichkeit zu gewährleisten.

**Datei:** `StorageAgent.js`

### IPNFTAgent

Implementiert die Funktionalität für IP-NFTs (Intellectual Property NFTs), die es ermöglichen, geistiges Eigentum wie wissenschaftliche Publikationen und Patente als NFTs zu tokenisieren und zu verwalten. Basierend auf dem Molecule-Protokoll für IP-NFTs.

**Datei:** `IPNFTAgent.js`

### PeerReviewAgent

Automatisiert den Peer-Review-Prozess für wissenschaftliche Publikationen, indem er Papiere analysiert und Bewertungen generiert.

**Datei:** `index.js`

### ProposalAgent

Bewertet Forschungsvorschläge und generiert Verbesserungsvorschläge.

**Datei:** `index.js`

### CollaborationAgent

Findet potenzielle Kollaborateure für Forscher und Projekte basierend auf Expertise und Forschungsinteressen.

**Datei:** `index.js`

### ResearchLearningAgent

Lernt aus wissenschaftlichen Publikationen, erkennt Zusammenhänge zwischen Forschungsgebieten, identifiziert Trends und unterstützt Forscher bei ihrer Arbeit.

**Datei:** `ResearchLearningAgent.js`

### SpecializedAgents

Enthält spezialisierte Agenten für bestimmte Aufgaben, wie z.B. die Bewertung von Forschungsvorschlägen.

**Datei:** `SpecializedAgents.js`

## Integration mit DeSci-Technologien

Die Agenten integrieren sich mit verschiedenen DeSci-Technologien:

1. **IPFS/Filecoin**: Für die dezentrale Speicherung von Metadaten und kleineren Dateien.
2. **BitTorrent**: Für die effiziente Verteilung großer Dateien wie Publikationen und Patente.
3. **Ethereum/Polygon/L2s**: Für die Erstellung und Verwaltung von NFTs und IP-NFTs mit Fokus auf Energieeffizienz.
4. **Molecule-Protokoll**: Für die Tokenisierung von geistigem Eigentum als IP-NFTs.
5. **OpenAI API**: Für die Textgenerierung und -analyse.

## Verwendung

Die Agenten können über die entsprechenden Klassen instanziiert und verwendet werden. Jeder Agent benötigt bestimmte Dienste und Konfigurationen, die über den Konstruktor übergeben werden.

Beispiel:

```javascript
import { CitationAnalysisAgent } from './ai/agents/CitationAnalysisAgent.js';
import { databaseService, blockchainService, storageManager } from './services/index.js';

const citationAgent = new CitationAnalysisAgent({
  databaseService,
  blockchainService,
  storageManager
});

await citationAgent.initialize();

const analysisResult = await citationAgent.analyzeCitations({
  doi: '10.1234/example.doi',
  title: 'Example Paper',
  abstract: 'This is an example abstract...',
  fullText: '...'
});

console.log(analysisResult);
```

## Konfiguration

Die Agenten können über Umgebungsvariablen und Konfigurationsobjekte konfiguriert werden. Wichtige Umgebungsvariablen sind:

- `OPENAI_API_KEY`: API-Schlüssel für OpenAI
- `AI_AGENT_MODEL`: Zu verwendendes Sprachmodell (Standard: 'gpt-4')
- `IP_NFT_CONTRACT_ADDRESS`: Adresse des IP-NFT-Vertrags
- `DEFAULT_NFT_RECIPIENT`: Standardadresse für NFT-Empfänger
- `IPFS_API_URL`: URL des IPFS-API
- `ETHEREUM_PROVIDER_URL`: URL des Ethereum-Providers
- `STORAGE_SIZE_THRESHOLD`: Schwellenwert für die Auswahl des Speicheradapters (in Bytes)

## Erweiterung

Um einen neuen Agenten hinzuzufügen:

1. Erstelle eine neue Datei für den Agenten
2. Importiere die `BaseAgent`-Klasse aus `index.js`
3. Erstelle eine neue Klasse, die von `BaseAgent` erbt
4. Implementiere die erforderlichen Methoden
5. Exportiere die Klasse

## Ressourcen

- [DeSci Labs](https://www.desci.com/)
- [Molecule Protocol](https://www.molecule.xyz/)
- [IPFS](https://ipfs.tech/)
- [Filecoin](https://filecoin.io/)
- [BitTorrent](https://www.bittorrent.com/)
- [Ethereum](https://ethereum.org/)
- [Polygon](https://polygon.technology/)
- [OpenAI API](https://openai.com/api/)