import { logger } from '../utils/logger.js';
import { VectorDatabase } from './VectorDatabase.js';
import { TextEmbeddingService } from './TextEmbeddingService.js';
import { DocumentProcessor } from './DocumentProcessor.js';

/**
 * Service zur Verwaltung einer Wissensdatenbank für wissenschaftliche Publikationen
 * Ermöglicht das Hinzufügen, Abfragen und Lernen aus wissenschaftlichen Inhalten
 */
export class KnowledgeBaseService {
  /**
   * Erstellt eine neue KnowledgeBaseService-Instanz
   * @param {Object} options - Konfigurationsoptionen
   * @param {Object} options.vectorDbConfig - Konfiguration für die Vektordatenbank
   * @param {Object} options.embeddingConfig - Konfiguration für den Embedding-Dienst
   * @param {Object} options.processorConfig - Konfiguration für die Dokumentverarbeitung
   * @param {boolean} options.enableLearning - Ob das kontinuierliche Lernen aktiviert werden soll
   */
  constructor(options = {}) {
    const {
      vectorDbConfig = {},
      embeddingConfig = {},
      processorConfig = {},
      enableLearning = true
    } = options;
    
    this.vectorDb = new VectorDatabase(vectorDbConfig);
    this.embeddingService = new TextEmbeddingService(embeddingConfig);
    this.documentProcessor = new DocumentProcessor(processorConfig);
    this.enableLearning = enableLearning;
    
    this.stats = {
      documentsAdded: 0,
      documentsUpdated: 0,
      documentsDeleted: 0,
      queriesPerformed: 0,
      learningIterations: 0,
      errors: 0
    };
  }
  
  /**
   * Initialisiert den KnowledgeBaseService
   * @returns {Promise<boolean>} Erfolgsstatus
   */
  async initialize() {
    try {
      await this.vectorDb.initialize();
      await this.embeddingService.initialize();
      await this.documentProcessor.initialize();
      
      logger.info('KnowledgeBaseService: Erfolgreich initialisiert');
      
      // Starte kontinuierliches Lernen, falls aktiviert
      if (this.enableLearning) {
        this._startContinuousLearning();
      }
      
      return true;
    } catch (error) {
      logger.error('KnowledgeBaseService: Fehler bei der Initialisierung', {
        error: error.message,
        stack: error.stack
      });
      
      return false;
    }
  }
  
  /**
   * Fügt ein Dokument zur Wissensdatenbank hinzu
   * @param {Object} document - Dokument, das hinzugefügt werden soll
   * @returns {Promise<Object>} Ergebnis des Hinzufügens
   */
  async addDocument(document) {
    try {
      logger.info('KnowledgeBaseService: Füge Dokument hinzu', {
        id: document.id,
        title: document.title
      });
      
      // Dokument vorverarbeiten
      const processedDocument = await this.documentProcessor.processDocument(document);
      
      // Text in Chunks aufteilen für bessere Verarbeitung
      const chunks = this.documentProcessor.splitIntoChunks(processedDocument);
      
      // Embeddings für jeden Chunk erstellen
      const embeddedChunks = await Promise.all(
        chunks.map(async (chunk) => {
          const embedding = await this.embeddingService.getEmbedding(chunk.text);
          return {
            ...chunk,
            embedding
          };
        })
      );
      
      // Chunks in der Vektordatenbank speichern
      const result = await this.vectorDb.addDocumentChunks(document.id, embeddedChunks, {
        metadata: {
          title: document.title,
          authors: document.authors,
          abstract: document.abstract,
          doi: document.doi,
          arxivId: document.arxivId,
          published: document.published,
          categories: document.categories,
          url: document.url,
          type: document.type,
          source: document.source
        }
      });
      
      if (result.success) {
        this.stats.documentsAdded++;
        
        logger.info('KnowledgeBaseService: Dokument erfolgreich hinzugefügt', {
          id: document.id,
          title: document.title,
          chunks: chunks.length
        });
      }
      
      return {
        success: result.success,
        id: document.id,
        chunks: chunks.length,
        message: result.success ? 'Dokument erfolgreich hinzugefügt' : 'Fehler beim Hinzufügen des Dokuments',
        error: result.error
      };
    } catch (error) {
      this.stats.errors++;
      
      logger.error('KnowledgeBaseService: Fehler beim Hinzufügen des Dokuments', {
        error: error.message,
        stack: error.stack,
        id: document.id,
        title: document.title
      });
      
      return {
        success: false,
        id: document.id,
        error: error.message
      };
    }
  }
  
  /**
   * Aktualisiert ein Dokument in der Wissensdatenbank
   * @param {string} documentId - ID des zu aktualisierenden Dokuments
   * @param {Object} document - Aktualisierte Dokumentdaten
   * @returns {Promise<Object>} Aktualisierungsergebnis
   */
  async updateDocument(documentId, document) {
    try {
      logger.info('KnowledgeBaseService: Aktualisiere Dokument', {
        id: documentId,
        title: document.title
      });
      
      // Zuerst das alte Dokument löschen
      await this.vectorDb.deleteDocument(documentId);
      
      // Dann das aktualisierte Dokument hinzufügen
      const result = await this.addDocument({
        ...document,
        id: documentId
      });
      
      if (result.success) {
        this.stats.documentsUpdated++;
      }
      
      return result;
    } catch (error) {
      this.stats.errors++;
      
      logger.error('KnowledgeBaseService: Fehler beim Aktualisieren des Dokuments', {
        error: error.message,
        stack: error.stack,
        id: documentId
      });
      
      return {
        success: false,
        id: documentId,
        error: error.message
      };
    }
  }
  
  /**
   * Löscht ein Dokument aus der Wissensdatenbank
   * @param {string} documentId - ID des zu löschenden Dokuments
   * @returns {Promise<Object>} Löschergebnis
   */
  async deleteDocument(documentId) {
    try {
      logger.info('KnowledgeBaseService: Lösche Dokument', { id: documentId });
      
      const result = await this.vectorDb.deleteDocument(documentId);
      
      if (result.success) {
        this.stats.documentsDeleted++;
        
        logger.info('KnowledgeBaseService: Dokument erfolgreich gelöscht', {
          id: documentId
        });
      }
      
      return result;
    } catch (error) {
      this.stats
