<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NFT Minting Demo - DeSci-Scholar</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .blockchain-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 10px;
        }
        
        .blockchain-option {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .blockchain-option:hover {
            border-color: #667eea;
            background: #e7f3ff;
        }
        
        .blockchain-option.selected {
            border-color: #667eea;
            background: #667eea;
            color: white;
        }
        
        .blockchain-option .icon {
            font-size: 2rem;
            margin-bottom: 8px;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1.1rem;
            width: 100%;
            transition: background 0.3s ease;
            margin-top: 20px;
        }
        
        .btn:hover {
            background: #5a6fd8;
        }
        
        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        
        .result-section {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .result-section h3 {
            color: #667eea;
            margin-bottom: 15px;
        }
        
        .result-content {
            background: white;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.9rem;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .back-link {
            display: inline-block;
            margin-bottom: 20px;
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
        }
        
        .back-link:hover {
            text-decoration: underline;
        }
        
        .success {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        
        .error {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="/" class="back-link">← Zurück zur Hauptseite</a>
        
        <div class="header">
            <h1>🎨 NFT Minting Demo</h1>
            <p>Wandeln Sie eine wissenschaftliche DOI in ein einzigartiges NFT um</p>
        </div>
        
        <form id="mintForm">
            <div class="form-group">
                <label for="doi">DOI (Digital Object Identifier)</label>
                <input 
                    type="text" 
                    id="doi" 
                    name="doi" 
                    placeholder="z.B. 10.1000/example.1" 
                    value="10.1000/example.1"
                    required
                >
                <small>Geben Sie eine gültige DOI ein oder verwenden Sie die Beispiel-DOI</small>
            </div>
            
            <div class="form-group">
                <label>Blockchain auswählen</label>
                <div class="blockchain-options">
                    <div class="blockchain-option selected" data-blockchain="polkadot">
                        <div class="icon">🟣</div>
                        <div>Polkadot</div>
                    </div>
                    <div class="blockchain-option" data-blockchain="ethereum">
                        <div class="icon">💎</div>
                        <div>Ethereum</div>
                    </div>
                    <div class="blockchain-option" data-blockchain="polygon">
                        <div class="icon">🟪</div>
                        <div>Polygon</div>
                    </div>
                    <div class="blockchain-option" data-blockchain="optimism">
                        <div class="icon">🔴</div>
                        <div>Optimism</div>
                    </div>
                    <div class="blockchain-option" data-blockchain="solana">
                        <div class="icon">🟢</div>
                        <div>Solana</div>
                    </div>
                </div>
            </div>
            
            <button type="submit" class="btn" id="mintButton">
                🎨 NFT erstellen
            </button>
        </form>
        
        <div id="resultSection" class="result-section" style="display: none;">
            <h3>Ergebnis</h3>
            <div id="resultContent" class="result-content"></div>
        </div>
    </div>
    
    <script>
        let selectedBlockchain = 'polkadot';
        
        // Blockchain-Auswahl
        document.querySelectorAll('.blockchain-option').forEach(option => {
            option.addEventListener('click', () => {
                // Entferne 'selected' von allen Optionen
                document.querySelectorAll('.blockchain-option').forEach(opt => {
                    opt.classList.remove('selected');
                });
                
                // Füge 'selected' zur geklickten Option hinzu
                option.classList.add('selected');
                selectedBlockchain = option.dataset.blockchain;
            });
        });
        
        // Form-Submit
        document.getElementById('mintForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const doi = document.getElementById('doi').value.trim();
            const mintButton = document.getElementById('mintButton');
            const resultSection = document.getElementById('resultSection');
            const resultContent = document.getElementById('resultContent');
            
            if (!doi) {
                alert('Bitte geben Sie eine DOI ein');
                return;
            }
            
            // Loading-Zustand
            mintButton.disabled = true;
            mintButton.textContent = '⏳ NFT wird erstellt...';
            
            resultSection.style.display = 'block';
            resultSection.className = 'result-section';
            resultContent.innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    <div>NFT wird auf ${selectedBlockchain.charAt(0).toUpperCase() + selectedBlockchain.slice(1)} erstellt...</div>
                </div>
            `;
            
            try {
                const response = await fetch('/api/nft/mint', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        doi: doi,
                        blockchain: selectedBlockchain
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultSection.className = 'result-section success';
                    resultContent.textContent = JSON.stringify(data, null, 2);
                } else {
                    resultSection.className = 'result-section error';
                    resultContent.textContent = `Fehler: ${data.error || 'Unbekannter Fehler'}`;
                }
            } catch (error) {
                resultSection.className = 'result-section error';
                resultContent.textContent = `Netzwerk-Fehler: ${error.message}`;
            } finally {
                // Reset button
                mintButton.disabled = false;
                mintButton.textContent = '🎨 NFT erstellen';
            }
        });
    </script>
</body>
</html>
