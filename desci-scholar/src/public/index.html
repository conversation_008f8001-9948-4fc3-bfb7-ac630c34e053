<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeSci-Scholar - Dezentrale Wissenschaftsplattform</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .main-content {
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 30px;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }
        
        .feature {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            border: 2px solid #e9ecef;
            transition: transform 0.3s ease;
        }
        
        .feature:hover {
            transform: translateY(-5px);
            border-color: #667eea;
        }
        
        .feature h3 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.5rem;
        }
        
        .demo-section {
            background: white;
            padding: 30px;
            border-radius: 10px;
            margin: 30px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .demo-buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1rem;
            transition: background 0.3s ease;
        }
        
        .btn:hover {
            background: #5a6fd8;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .result-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 20px;
            margin-top: 20px;
            min-height: 100px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.9rem;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #28a745;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .footer {
            text-align: center;
            color: white;
            opacity: 0.8;
            margin-top: 40px;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .main-content {
                padding: 20px;
            }
            
            .demo-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🔬 DeSci-Scholar</h1>
            <p>Dezentrale Open-Source-Plattform für wissenschaftliche Publikationen</p>
            <p><span class="status-indicator"></span>Server läuft</p>
        </header>
        
        <main class="main-content">
            <h2>Willkommen bei DeSci-Scholar</h2>
            <p>Eine revolutionäre Plattform, die wissenschaftliche Publikationen mit Blockchain-Technologie verbindet und DOIs in NFTs umwandelt.</p>
            
            <div class="features">
                <div class="feature">
                    <h3>📚 Publikationen</h3>
                    <p>Verwalten und durchsuchen Sie wissenschaftliche Publikationen in einer dezentralen Umgebung.</p>
                </div>
                
                <div class="feature">
                    <h3>🔍 Intelligente Suche</h3>
                    <p>Erweiterte Suchfunktionen mit Facettierung und Relevanz-Scoring für präzise Ergebnisse.</p>
                </div>
                
                <div class="feature">
                    <h3>🎨 DOI zu NFT</h3>
                    <p>Wandeln Sie wissenschaftliche DOIs in einzigartige NFTs um und nutzen Sie Blockchain-Technologie.</p>
                </div>
                
                <div class="feature">
                    <h3>🌐 Multi-Blockchain</h3>
                    <p>Unterstützung für Polkadot, Ethereum, Polygon, Optimism und Solana Blockchains.</p>
                </div>
            </div>
        </main>
        
        <section class="demo-section">
            <h2>🚀 Live-Demo</h2>
            <p>Testen Sie die API-Funktionen direkt in Ihrem Browser:</p>
            
            <div class="demo-buttons">
                <button class="btn" onclick="testAPI('/api/publications')">📚 Publikationen laden</button>
                <button class="btn" onclick="testAPI('/api/search?q=blockchain')">🔍 Nach "blockchain" suchen</button>
                <button class="btn" onclick="testAPI('/api/doi/10.1000/example.1')">🔗 DOI Lookup</button>
                <button class="btn" onclick="testAPI('/health')">💚 Health Check</button>
                <button class="btn" onclick="window.open('/nft-demo', '_blank')">🎨 NFT Demo öffnen</button>
                <button class="btn btn-secondary" onclick="clearResults()">🗑️ Ergebnisse löschen</button>
            </div>
            
            <div id="results" class="result-area">
Klicken Sie auf einen der Buttons oben, um die API zu testen...
            </div>
        </section>
        
        <footer class="footer">
            <p>&copy; 2024 DeSci-Scholar - Open Source Wissenschaftsplattform</p>
            <p>Powered by Node.js, Express.js und Blockchain-Technologie</p>
        </footer>
    </div>
    
    <script>
        async function testAPI(endpoint) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.textContent = 'Lade Daten...';
            
            try {
                const response = await fetch(endpoint);
                const data = await response.json();
                
                resultsDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                resultsDiv.textContent = `Fehler: ${error.message}`;
            }
        }
        
        function clearResults() {
            document.getElementById('results').textContent = 'Ergebnisse gelöscht. Klicken Sie auf einen Button, um die API zu testen...';
        }
        
        // Status-Check beim Laden der Seite
        window.addEventListener('load', async () => {
            try {
                const response = await fetch('/health');
                const data = await response.json();
                console.log('Server Status:', data);
            } catch (error) {
                console.error('Server nicht erreichbar:', error);
            }
        });
    </script>
</body>
</html>
