import { create } from 'ipfs-http-client.js';
import { <PERSON><PERSON><PERSON> } from 'buffer.js';

class IpfsStorage {
  constructor(ipfsConfig = {
    host: 'ipfs.infura.io',
    port: 5001,
    protocol: 'https'
  }) {
    this.ipfs = create(ipfsConfig);
  }

  async uploadPublication(publication) {
    try {
      // Metadaten vorbereiten
      const metadata = {
        title: publication.title,
        authors: publication.authors,
        abstract: publication.abstract,
        doi: publication.doi,
        timestamp: Date.now(),
        version: '1.0'
      };

      // Publikation und Metadaten als Buffer
      const content = Buffer.from(JSON.stringify({
        metadata,
        content: publication.content
      }));

      // Auf IPFS hochladen
      const result = await this.ipfs.add(content);
      
      return {
        ipfsHash: result.path,
        size: result.size,
        metadata
      };
    } catch (error) {
      throw new Error(`IPFS Upload fehlgeschlagen: ${error.message}`);
    }
  }

  async getPublication(ipfsHash) {
    try {
      const chunks = [];
      
      // Daten von <PERSON>FS abrufen
      for await (const chunk of this.ipfs.cat(ipfsHash)) {
        chunks.push(chunk);
      }
      
      // Buffer zu String konvertieren und parsen
      const content = Buffer.concat(chunks).toString();
      return JSON.parse(content);
    } catch (error) {
      throw new Error(`IPFS Abruf fehlgeschlagen: ${error.message}`);
    }
  }

  async pinPublication(ipfsHash) {
    try {
      // Publikation auf IPFS pinnen für dauerhafte Verfügbarkeit
      await this.ipfs.pin.add(ipfsHash);
      return true;
    } catch (error) {
      throw new Error(`IPFS Pinning fehlgeschlagen: ${error.message}`);
    }
  }

  async updatePublication(ipfsHash, publication) {
    try {
      // Bestehende Publikation abrufen
      const existing = await this.getPublication(ipfsHash);
      
      // Neue Version erstellen
      const updatedContent = {
        ...existing,
        content: publication.content,
        metadata: {
          ...existing.metadata,
          version: (parseFloat(existing.metadata.version) + 0.1).toFixed(1),
          updateTimestamp: Date.now()
        }
      };

      // Neue Version hochladen
      const result = await this.uploadPublication(updatedContent);
      
      // Alte Version unpinnen
      await this.ipfs.pin.rm(ipfsHash);
      
      return result;
    } catch (error) {
      throw new Error(`IPFS Update fehlgeschlagen: ${error.message}`);
    }
  }

  async listVersions(doi) {
    try {
      // Alle Versionen einer Publikation finden
      const versions = [];
      
      for await (const pin of this.ipfs.pin.ls()) {
        try {
          const content = await this.getPublication(pin.cid.toString());
          if (content.metadata.doi === doi) {
            versions.push({
              ipfsHash: pin.cid.toString(),
              version: content.metadata.version,
              timestamp: content.metadata.timestamp
            });
          }
        } catch (error) {
          console.warn(`Fehler beim Abrufen von Version ${pin.cid.toString()}: ${error.message}`);
        }
      }
      
      return versions.sort((a, b) => b.timestamp - a.timestamp);
    } catch (error) {
      throw new Error(`IPFS Versionsabfrage fehlgeschlagen: ${error.message}`);
    }
  }

  async deletePublication(ipfsHash) {
    try {
      // Publikation von IPFS entfernen (unpinnen)
      await this.ipfs.pin.rm(ipfsHash);
      return true;
    } catch (error) {
      throw new Error(`IPFS Löschung fehlgeschlagen: ${error.message}`);
    }
  }
}

export default IpfsStorage;