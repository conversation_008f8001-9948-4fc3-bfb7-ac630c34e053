/**
 * FileDistributionManager - Spezialisierte Komponente für die effiziente Verteilung von Forschungsdateien
 * Optimiert für die Verteilung großer wissenschaftlicher Datensätze und Patentdokumente über BitTorrent
 */

import BTManager from './BTManager.js';
import path from 'path';
import fs from 'fs';
import { promisify } from 'util';

// Wandle fs-Callback-Funktionen in Promise-basierte Funktionen um
const writeFile = promisify(fs.writeFile);
const readFile = promisify(fs.readFile);
const mkdir = promisify(fs.mkdir);
const access = promisify(fs.access);

class FileDistributionManager {
  /**
   * Konstruktor für den File-Distribution-Manager
   * @param {Object} config Konfigurationsobjekt
   * @param {Object} config.bittorrent BTManager-Konfiguration
   * @param {string} config.dataDir Verzeichnis für temporäre Dateien
   * @param {number} config.chunkSize Größe der Chunks für chunked Torrents (in Bytes)
   * @param {boolean} config.privateMode Private Torrents erstellen (Standard: false)
   * @param {boolean} config.enableDHT DHT für bessere Peer-Discovery aktivieren (Standard: true)
   */
  constructor(config = {}) {
    this.config = {
      bittorrent: {},
      dataDir: './data',
      chunkSize: 1024 * 1024, // 1MB Chunks standardmäßig
      privateMode: false,
      enableDHT: true,
      ...config
    };
    
    this.btManager = new BTManager({
      ...this.config.bittorrent,
      dht: this.config.enableDHT
    });
    
    this.activeDistributions = new Map();
    this.fileRegistry = new Map();
  }
  
  /**
   * Initialisiert den File-Distribution-Manager
   * @returns {Promise<boolean>} Initialisierungsstatus
   */
  async initialize() {
    try {
      // Initialisiere den BitTorrent-Manager
      const btInitialized = await this.btManager.initialize();
      
      if (!btInitialized) {
        throw new Error('BitTorrent-Manager konnte nicht initialisiert werden');
      }
      
      // Stelle sicher, dass das Datenverzeichnis existiert
      await this._ensureDataDir();
      
      console.log('FileDistributionManager erfolgreich initialisiert');
      return true;
    } catch (error) {
      console.error('Fehler bei der Initialisierung des FileDistributionManager:', error);
      return false;
    }
  }
  
  /**
   * Stellt eine Datei zur Verteilung bereit
   * @param {Buffer|string} fileData Dateiinhalt
   * @param {Object} fileInfo Dateiinformationen
   * @param {string} fileInfo.name Dateiname
   * @param {string} fileInfo.type MIME-Typ
   * @param {number} fileInfo.size Dateigröße (optional)
   * @param {Object} options Verteilungsoptionen
   * @returns {Promise<Object>} Verteilungsergebnis
   */
  async distributeFile(fileData, fileInfo, options = {}) {
    try {
      if (!fileInfo || !fileInfo.name) {
        throw new Error('Dateiname ist erforderlich');
      }
      
      // Bereite Buffer vor
      const buffer = typeof fileData === 'string'
        ? Buffer.from(fileData)
        : fileData;
      
      // Bestimme Dateigröße
      const fileSize = fileInfo.size || buffer.length;
      
      // Entscheide, ob die Datei in Chunks aufgeteilt werden soll
      const useChunking = fileSize > this.config.chunkSize * 10;
      
      let torrentResult;
      
      if (useChunking) {
        // Für große Dateien: Teile in Chunks auf und erstelle ein Multi-File-Torrent
        torrentResult = await this._distributeChunkedFile(buffer, fileInfo, options);
      } else {
        // Für kleinere Dateien: Erstelle ein einfaches Torrent
        torrentResult = await this._distributeSimpleFile(buffer, fileInfo, options);
      }
      
      if (!torrentResult.success) {
        throw new Error(`Torrent-Erstellungsfehler: ${torrentResult.error}`);
      }
      
      // Speichere Informationen zur Datei für spätere Verwaltung
      this._registerFile(fileInfo.name, {
        infoHash: torrentResult.infoHash,
        magnetLink: torrentResult.magnetLink,
        fileSize,
        chunks: torrentResult.chunks || 0,
        createdAt: new Date(),
        distributionType: useChunking ? 'chunked' : 'simple'
      });
      
      return {
        success: true,
        infoHash: torrentResult.infoHash,
        magnetLink: torrentResult.magnetLink,
        trackers: torrentResult.trackers,
        fileSize,
        chunked: useChunking,
        chunks: torrentResult.chunks || 0
      };
    } catch (error) {
      console.error('Fehler bei der Dateiverteilung:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * Verteilt eine große Datei in Chunks über BitTorrent
   * @param {Buffer} buffer Dateiinhalt
   * @param {Object} fileInfo Dateiinformationen
   * @param {Object} options Optionen
   * @returns {Promise<Object>} Verteilungsergebnis
   * @private
   */
  async _distributeChunkedFile(buffer, fileInfo, options) {
    const chunkDir = path.join(this.config.dataDir, `${fileInfo.name}-chunks`);
    
    try {
      // Erstelle Verzeichnis für Chunks
      await mkdir(chunkDir, { recursive: true });
      
      // Teile Datei in Chunks auf
      const chunkSize = this.config.chunkSize;
      const chunks = [];
      let chunkCount = 0;
      
      for (let i = 0; i < buffer.length; i += chunkSize) {
        const end = Math.min(i + chunkSize, buffer.length);
        const chunkBuffer = buffer.slice(i, end);
        
        const chunkFileName = `${fileInfo.name}.part${chunkCount.toString().padStart(5, '0')}`;
        const chunkPath = path.join(chunkDir, chunkFileName);
        
        // Speichere Chunk
        await writeFile(chunkPath, chunkBuffer);
        
        chunks.push({
          path: chunkPath,
          name: chunkFileName,
          size: chunkBuffer.length
        });
        
        chunkCount++;
      }
      
      // Erstelle Manifest-Datei
      const manifest = {
        originalFileName: fileInfo.name,
        fileType: fileInfo.type,
        fileSize: buffer.length,
        chunkCount,
        chunks: chunks.map(c => c.name),
        createdAt: new Date().toISOString()
      };
      
      const manifestPath = path.join(chunkDir, `${fileInfo.name}.manifest.json`);
      await writeFile(manifestPath, JSON.stringify(manifest, null, 2));
      
      // Erstelle Torrent mit allen Chunks und dem Manifest
      const torrentOptions = {
        name: `${fileInfo.name} (chunked)`,
        comment: `Chunked file distribution for ${fileInfo.name}`,
        createdBy: 'DeSci Scholar File Distribution Manager',
        private: this.config.privateMode,
        ...options
      };
      
      const torrentResult = await this.btManager.createAndSeedTorrent(chunkDir, torrentOptions);
      
      return {
        ...torrentResult,
        chunks: chunkCount
      };
    } catch (error) {
      console.error('Fehler bei der Chunk-Verteilung:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * Verteilt eine einfache Datei über BitTorrent
   * @param {Buffer} buffer Dateiinhalt
   * @param {Object} fileInfo Dateiinformationen
   * @param {Object} options Optionen
   * @returns {Promise<Object>} Verteilungsergebnis
   * @private
   */
  async _distributeSimpleFile(buffer, fileInfo, options) {
    try {
      // Speichere die Datei temporär
      const filePath = path.join(this.config.dataDir, fileInfo.name);
      await writeFile(filePath, buffer);
      
      // Erstelle und seede das Torrent
      const torrentOptions = {
        name: fileInfo.name,
        comment: `File distribution for ${fileInfo.name}`,
        createdBy: 'DeSci Scholar File Distribution Manager',
        private: this.config.privateMode,
        ...options
      };
      
      return await this.btManager.createAndSeedTorrent(filePath, torrentOptions);
    } catch (error) {
      console.error('Fehler bei der einfachen Dateiverteilung:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * Ruft eine Datei aus dem BitTorrent-Netzwerk ab
   * @param {string} infoHash InfoHash der Datei
   * @param {Object} options Abrufoptionen
   * @returns {Promise<Object>} Abrufergebnis mit Dateiinhalt
   */
  async retrieveFile(infoHash, options = {}) {
    try {
      // Überprüfe, ob wir diese Datei bereits kennen
      const fileInfo = Array.from(this.fileRegistry.values())
        .find(info => info.infoHash === infoHash);
      
      const isChunked = fileInfo && fileInfo.distributionType === 'chunked';
      
      // Lade die Datei über BitTorrent
      const downloadResult = await this.btManager.downloadTorrent(infoHash, {
        ...options,
        timeout: options.timeout || 60000, // 1 Minute Standardtimeout
      });
      
      if (!downloadResult.success) {
        throw new Error(`Download-Fehler: ${downloadResult.error}`);
      }
      
      // Verarbeite das Ergebnis basierend auf dem Typ (einfach oder chunked)
      if (isChunked) {
        return await this._processChunkedDownload(downloadResult.files, fileInfo);
      } else {
        return {
          success: true,
          data: downloadResult.data,
          fileName: downloadResult.fileName || (fileInfo ? fileInfo.originalFileName : 'unknown'),
          fileType: fileInfo ? fileInfo.fileType : 'application/octet-stream'
        };
      }
    } catch (error) {
      console.error('Fehler beim Dateiabruf:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * Verarbeitet einen Download von einem chunked Torrent
   * @param {Array} files Heruntergeladene Dateien
   * @param {Object} fileInfo Dateiinformationen
   * @returns {Promise<Object>} Verarbeitungsergebnis
   * @private
   */
  async _processChunkedDownload(files, fileInfo) {
    try {
      // Suche nach der Manifest-Datei
      const manifestFile = files.find(f => f.name.endsWith('.manifest.json'));
      
      if (!manifestFile) {
        throw new Error('Keine Manifest-Datei im Torrent gefunden');
      }
      
      // Lese und parse das Manifest
      let manifest;
      try {
        const manifestContent = await readFile(manifestFile.path, 'utf8');
        manifest = JSON.parse(manifestContent);
      } catch (error) {
        throw new Error(`Fehler beim Lesen des Manifests: ${error.message}`);
      }
      
      // Prüfe, ob alle Chunks vorhanden sind
      const chunkFiles = files.filter(f => {
        const fileName = path.basename(f.path);
        return manifest.chunks.includes(fileName);
      });
      
      if (chunkFiles.length !== manifest.chunkCount) {
        throw new Error(`Unvollständiger Download: ${chunkFiles.length} von ${manifest.chunkCount} Chunks gefunden`);
      }
      
      // Sortiere Chunks in der richtigen Reihenfolge
      chunkFiles.sort((a, b) => {
        const aIndex = manifest.chunks.indexOf(path.basename(a.path));
        const bIndex = manifest.chunks.indexOf(path.basename(b.path));
        return aIndex - bIndex;
      });
      
      // Kombiniere alle Chunks zu einer einzigen Datei
      const buffers = await Promise.all(chunkFiles.map(async (chunk) => {
        return await readFile(chunk.path);
      }));
      
      const combinedBuffer = Buffer.concat(buffers);
      
      return {
        success: true,
        data: combinedBuffer,
        fileName: manifest.originalFileName,
        fileType: manifest.fileType,
        fileSize: combinedBuffer.length,
        chunked: true,
        chunkCount: manifest.chunkCount
      };
    } catch (error) {
      console.error('Fehler bei der Verarbeitung des Chunk-Downloads:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * Registriert eine Datei im Registry
   * @param {string} fileName Dateiname
   * @param {Object} info Dateiinformationen
   * @private
   */
  _registerFile(fileName, info) {
    this.fileRegistry.set(fileName, info);
  }
  
  /**
   * Überprüft die Verteilungsstatistik einer Datei
   * @param {string} infoHash InfoHash der Datei
   * @returns {Promise<Object>} Verteilungsstatistik
   */
  async getDistributionStats(infoHash) {
    try {
      const torrentInfo = await this.btManager.getTorrentInfo(infoHash);
      
      if (!torrentInfo.success) {
        throw new Error(`Fehler beim Abrufen der Torrent-Informationen: ${torrentInfo.error}`);
      }
      
      const fileInfo = Array.from(this.fileRegistry.values())
        .find(info => info.infoHash === infoHash);
      
      return {
        success: true,
        infoHash,
        seeders: torrentInfo.numSeeders,
        leechers: torrentInfo.numLeechers,
        downloaded: torrentInfo.downloaded,
        uploaded: torrentInfo.uploaded,
        downloadSpeed: torrentInfo.downloadSpeed,
        uploadSpeed: torrentInfo.uploadSpeed,
        fileName: fileInfo ? fileInfo.originalFileName : undefined,
        distributionType: fileInfo ? fileInfo.distributionType : 'unknown',
        createdAt: fileInfo ? fileInfo.createdAt : undefined
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Verteilungsstatistik:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * Stellt sicher, dass das Datenverzeichnis existiert
   * @returns {Promise<void>}
   * @private
   */
  async _ensureDataDir() {
    try {
      await access(this.config.dataDir, fs.constants.F_OK);
    } catch (error) {
      // Verzeichnis existiert nicht, erstelle es
      await mkdir(this.config.dataDir, { recursive: true });
    }
  }
  
  /**
   * Gibt eine Liste aller aktiv verteilten Dateien zurück
   * @returns {Promise<Object>} Liste der Dateien
   */
  async listDistributedFiles() {
    try {
      const activeTorrents = await this.btManager.listActiveTorrents();
      
      return {
        success: true,
        files: Array.from(this.fileRegistry.entries()).map(([fileName, info]) => {
          const torrent = activeTorrents.torrents.find(t => t.infoHash === info.infoHash);
          
          return {
            fileName,
            infoHash: info.infoHash,
            magnetLink: info.magnetLink,
            fileSize: info.fileSize,
            distributionType: info.distributionType,
            chunks: info.chunks,
            createdAt: info.createdAt,
            active: !!torrent,
            stats: torrent ? {
              seeders: torrent.numSeeders,
              leechers: torrent.numLeechers,
              downloaded: torrent.downloaded,
              uploaded: torrent.uploaded
            } : null
          };
        })
      };
    } catch (error) {
      console.error('Fehler beim Auflisten der verteilten Dateien:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * Beendet den FileDistributionManager
   * @returns {Promise<boolean>} Erfolgsstatus
   */
  async shutdown() {
    try {
      // Beende den BitTorrent-Manager
      await this.btManager.shutdown();
      return true;
    } catch (error) {
      console.error('Fehler beim Herunterfahren des FileDistributionManager:', error);
      return false;
    }
  }
}

export default FileDistributionManager;