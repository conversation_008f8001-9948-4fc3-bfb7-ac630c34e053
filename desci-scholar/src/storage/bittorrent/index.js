/**
 * @fileoverview BitTorrent integration for large file storage in DeSci-Scholar
 * This module handles the creation and management of torrents for large scientific datasets and documents
 */

const WebTorrent = require('webtorrent');
const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');
require('dotenv').config();

/**
 * BitTorrentClient class for managing torrent creation and downloads
 */
class BitTorrentClient {
  /**
   * Creates an instance of BitTorrentClient
   */
  constructor() {
    this.port = process.env.BITTORRENT_PORT || 6881;
    this.client = null;
    this.downloadPath = path.join(process.cwd(), 'downloads');
  }

  /**
   * Initialize the WebTorrent client
   * @returns {Object} Initialized WebTorrent client
   */
  init() {
    if (this.client) {
      return this.client;
    }

    // Create a WebTorrent client
    this.client = new WebTorrent({
      dht: true, // Use the DHT to find peers
      tracker: true // Use trackers to find peers
    });

    // Setup event listeners
    this.client.on('error', (err) => {
      console.error('WebTorrent client error:', err);
    });

    console.log(`BitTorrent client initialized on port ${this.port}`);
    return this.client;
  }

  /**
   * Create a torrent from a file or directory
   * @param {string} inputPath - Path to file or directory to create torrent from
   * @param {Object} options - Torrent creation options
   * @returns {Promise<Object>} Torrent information including magnet URI
   */
  createTorrent(inputPath, options = {}) {
    this.init();
    
    return new Promise((resolve, reject) => {
      // Ensure the input path exists
      fs.access(inputPath).catch(() => {
        reject(new Error(`Input path does not exist: ${inputPath}`));
      });
      
      const opts = {
        comment: options.comment || 'Created by DeSci-Scholar',
        createdBy: options.createdBy || 'DeSci-Scholar BitTorrent Client',
        private: options.private || false,
        announceList: options.announceList || [
          ['udp://tracker.opentrackr.org:1337/announce'],
          ['udp://tracker.leechers-paradise.org:6969/announce'],
          ['udp://tracker.coppersurfer.tk:6969/announce']
        ]
      };
      
      // Create the torrent
      this.client.seed(inputPath, opts, (torrent) => {
        console.log(`Torrent created: ${torrent.name}`);
        console.log(`Magnet URI: ${torrent.magnetURI}`);
        
        // Save torrent file if requested
        if (options.saveTorrentFile) {
          const torrentFilePath = options.torrentFilePath || 
            path.join(process.cwd(), 'torrents', `${torrent.name}.torrent`);
          
          // Ensure directory exists
          const torrentDir = path.dirname(torrentFilePath);
          fs.mkdir(torrentDir, { recursive: true })
            .then(() => {
              fs.writeFile(torrentFilePath, torrent.torrentFile);
              console.log(`Torrent file saved to ${torrentFilePath}`);
            })
            .catch(err => {
              console.error(`Failed to save torrent file: ${err.message}`);
            });
        }
        
        resolve({
          name: torrent.name,
          infoHash: torrent.infoHash,
          magnetURI: torrent.magnetURI,
          length: torrent.length,
          files: torrent.files.map(file => ({
            name: file.name,
            path: file.path,
            length: file.length
          }))
        });
      });
    });
  }

  /**
   * Download a torrent from a magnet URI or torrent file
   * @param {string} magnetURI - Magnet URI or path to torrent file
   * @param {string} outputDir - Directory to save downloaded files
   * @returns {Promise<Object>} Download progress and result
   */
  downloadTorrent(magnetURI, outputDir = null) {
    this.init();
    
    return new Promise((resolve, reject) => {
      // Set output directory
      const downloadDir = outputDir || this.downloadPath;
      
      // Ensure download directory exists
      fs.mkdir(downloadDir, { recursive: true }).catch(err => {
        reject(new Error(`Failed to create download directory: ${err.message}`));
      });
      
      // Start downloading the torrent
      this.client.add(magnetURI, { path: downloadDir }, (torrent) => {
        console.log(`Downloading torrent: ${torrent.name}`);
        
        // Track download progress
        torrent.on('download', (bytes) => {
          console.log(`Progress: ${(torrent.progress * 100).toFixed(2)}%`);
        });
        
        // Handle download completion
        torrent.on('done', () => {
          console.log(`Torrent download complete: ${torrent.name}`);
          resolve({
            name: torrent.name,
            infoHash: torrent.infoHash,
            size: torrent.length,
            downloadPath: path.join(downloadDir, torrent.name),
            files: torrent.files.map(file => ({
              name: file.name,
              path: file.path,
              size: file.length
            }))
          });
        });
        
        // Handle download errors
        torrent.on('error', (err) => {
          console.error(`Torrent download error: ${err.message}`);
          reject(err);
        });
      });
    });
  }

  /**
   * Create a scientific dataset torrent with metadata
   * @param {string} datasetPath - Path to the dataset
   * @param {Object} metadata - Dataset metadata
   * @returns {Promise<Object>} Torrent info and metadata
   */
  async createDatasetTorrent(datasetPath, metadata) {
    // Validate required metadata
    const requiredFields = ['title', 'description', 'creator', 'license', 'doi'];
    for (const field of requiredFields) {
      if (!metadata[field]) {
        throw new Error(`Missing required metadata field: ${field}`);
      }
    }
    
    // Add timestamp and identifier
    const enhancedMetadata = {
      ...metadata,
      timestamp: new Date().toISOString(),
      id: crypto.randomUUID()
    };
    
    // Create a JSON file with the metadata
    const metadataDir = path.join(process.cwd(), 'temp', enhancedMetadata.id);
    await fs.mkdir(metadataDir, { recursive: true });
    
    const metadataPath = path.join(metadataDir, 'metadata.json');
    await fs.writeFile(metadataPath, JSON.stringify(enhancedMetadata, null, 2));
    
    // Create a combined directory with dataset and metadata
    const combinedDir = path.join(metadataDir, 'dataset');
    await fs.mkdir(combinedDir, { recursive: true });
    
    // Copy dataset to combined directory
    const stats = await fs.stat(datasetPath);
    if (stats.isDirectory()) {
      // TODO: Implement recursive directory copy
      // For simplicity, we're assuming it's a file for now
      console.warn('Directory datasets not fully implemented yet');
    } else {
      // Copy the file
      const datasetFileName = path.basename(datasetPath);
      const destPath = path.join(combinedDir, datasetFileName);
      await fs.copyFile(datasetPath, destPath);
    }
    
    // Create torrent from the combined directory
    const torrentInfo = await this.createTorrent(metadataDir, {
      comment: metadata.description,
      createdBy: `DeSci-Scholar - ${metadata.creator}`,
      saveTorrentFile: true
    });
    
    // Clean up temporary directory
    // await fs.rm(metadataDir, { recursive: true, force: true });
    
    return {
      ...torrentInfo,
      metadata: enhancedMetadata
    };
  }

  /**
   * Stop all torrents and close the client
   */
  async destroy() {
    if (this.client) {
      return new Promise((resolve) => {
        this.client.destroy(() => {
          console.log('BitTorrent client destroyed');
          this.client = null;
          resolve();
        });
      });
    }
  }
}

module.exports = BitTorrentClient;
