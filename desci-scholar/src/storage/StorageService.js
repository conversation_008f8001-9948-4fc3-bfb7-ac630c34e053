/**
 * StorageService - Eine einheitliche API für dezentrale Datenspeicherung
 * Integriert IPFS und BitTorrent für maximale Verfügbarkeit und Redundanz
 */

import IPFSManager from './ipfs/IPFSManager.js';
import BTManager from './bittorrent/BTManager.js';
import { parseBuffer } from '../utils/buffer.js';

/**
 * Enum für unterstützte Speicherprotokolle
 * @enum {string}
 */
export const StorageProtocol = {
  IPFS: 'ipfs',
  BITTORRENT: 'bittorrent',
  BOTH: 'both' // Verwendet beide Protokolle für Redundanz
};

class StorageService {
  /**
   * Konstruktor für den Storage-Service
   * @param {Object} config Konfigurationsobjekt
   * @param {Object} config.ipfs IPFS-Konfiguration
   * @param {Object} config.bittorrent BitTorrent-Konfiguration
   * @param {StorageProtocol} config.defaultProtocol Standardprotokoll (Standard: BOTH)
   */
  constructor(config = {}) {
    this.config = {
      ipfs: {},
      bittorrent: {},
      defaultProtocol: StorageProtocol.BOTH,
      ...config
    };
    
    this.ipfs = new IPFSManager(this.config.ipfs);
    this.bt = new BTManager(this.config.bittorrent);
    
    this.storageMap = new Map(); // Speichert Referenzen zwischen Protokollen
  }
  
  /**
   * Initialisiert alle Speicherprotokolle
   * @returns {Promise<boolean>} Erfolgsstatus
   */
  async initialize() {
    try {
      const ipfsResult = await this.ipfs.connect();
      if (!ipfsResult) {
        console.error('IPFS-Verbindung konnte nicht hergestellt werden');
      }

      const btResult = await this.bt.initialize();
      if (!btResult) {
        console.error('BitTorrent-Client konnte nicht initialisiert werden');
      }

      const success = ipfsResult && btResult;
      if (success) {
        console.log('StorageService erfolgreich initialisiert');
      } else {
        console.warn('StorageService teilweise initialisiert mit Einschränkungen');
      }

      return success;
    } catch (error) {
      console.error('Fehler bei der Initialisierung des Storage-Service:', error);
      return false;
    }
  }
  
  /**
   * Speichert Daten mit dem gewählten Protokoll
   * @param {Buffer|string|Object} data Zu speichernde Daten
   * @param {Object} options Optionen für die Speicherung
   * @param {StorageProtocol} options.protocol Zu verwendendes Protokoll
   * @param {string} options.filename Dateiname
   * @param {Object} options.metadata Zusätzliche Metadaten
   * @param {Object} options.ipfsOptions Spezifische IPFS-Optionen
   * @param {Object} options.btOptions Spezifische BitTorrent-Optionen
   * @returns {Promise<Object>} Speicherergebnis mit ID und URLs
   */
  async storeData(data, options = {}) {
    // Verwende das Standard- oder angegebene Protokoll
    const protocol = options.protocol || this.config.defaultProtocol;
    const metadata = options.metadata || {};
    const filename = options.filename || `file-${Date.now()}`;
    
    try {
      let ipfsResult = null;
      let btResult = null;
      
      // Je nach Protokoll speichern
      if (protocol === StorageProtocol.IPFS || protocol === StorageProtocol.BOTH) {
        ipfsResult = await this.ipfs.uploadFile(data, {
          ...options.ipfsOptions,
          filename,
          pin: true
        });
        
        if (!ipfsResult.success) {
          throw new Error(`IPFS-Speicherfehler: ${ipfsResult.error}`);
        }
      }
      
      if (protocol === StorageProtocol.BITTORRENT || protocol === StorageProtocol.BOTH) {
        btResult = await this.bt.createAndSeedTorrent(data, {
          ...options.btOptions,
          name: filename,
          metadata
        });
        
        if (!btResult.success) {
          throw new Error(`BitTorrent-Speicherfehler: ${btResult.error}`);
        }
      }
      
      // Speichere Referenzen zwischen den Protokollen in der Map
      if (ipfsResult && btResult) {
        this.storageMap.set(ipfsResult.cid, btResult.torrent.infoHash);
        this.storageMap.set(btResult.torrent.infoHash, ipfsResult.cid);
      }
      
      // Erstelle Ergebnisobjekt
      return {
        success: true,
        protocol,
        id: protocol === StorageProtocol.IPFS ? ipfsResult.cid :
            protocol === StorageProtocol.BITTORRENT ? btResult.torrent.infoHash :
            { ipfs: ipfsResult.cid, bittorrent: btResult.torrent.infoHash },
        urls: {
          ipfs: ipfsResult ? ipfsResult.url : null,
          bittorrent: btResult ? btResult.torrent.magnetURI : null
        },
        metadata,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Fehler beim Speichern der Daten:', error);
      return {
        success: false,
        error: error.message,
        protocol
      };
    }
  }
  
  /**
   * Ruft Daten mit dem gewählten Protokoll ab
   * @param {string|Object} id IPFS-CID, BitTorrent-Infohash oder Objekt mit beiden
   * @param {Object} options Optionen für den Abruf
   * @param {StorageProtocol} options.protocol Zu verwendendes Protokoll
   * @param {string} options.format Rückgabeformat ('buffer', 'text', 'json')
   * @param {Object} options.ipfsOptions Spezifische IPFS-Optionen
   * @param {Object} options.btOptions Spezifische BitTorrent-Optionen
   * @returns {Promise<Object>} Abrufergebnis mit Daten
   */
  async retrieveData(id, options = {}) {
    // Verwende das Standard- oder angegebene Protokoll
    const protocol = options.protocol || this.config.defaultProtocol;
    
    try {
      // Bestimme die tatsächlichen IDs basierend auf dem Eingabeparameter
      let ipfsCid = null;
      let btInfoHash = null;
      
      if (typeof id === 'string') {
        // Wenn ID ein String ist, interpretiere je nach Protokoll
        if (protocol === StorageProtocol.IPFS) {
          ipfsCid = id;
          btInfoHash = this.storageMap.get(id); // Versuche, die entsprechende BT-ID zu finden
        } else if (protocol === StorageProtocol.BITTORRENT) {
          btInfoHash = id;
          ipfsCid = this.storageMap.get(id); // Versuche, die entsprechende IPFS-ID zu finden
        } else {
          // Bei BOTH, versuche zu erraten, welches Format es ist
          if (id.startsWith('Qm') || id.startsWith('bafy')) {
            ipfsCid = id;
            btInfoHash = this.storageMap.get(id);
          } else {
            btInfoHash = id;
            ipfsCid = this.storageMap.get(id);
          }
        }
      } else if (typeof id === 'object') {
        // Wenn ID ein Objekt ist, extrahiere beide IDs
        ipfsCid = id.ipfs;
        btInfoHash = id.bittorrent;
      }
      
      // Je nach Protokoll und verfügbaren IDs abrufen
      if (protocol === StorageProtocol.IPFS && ipfsCid) {
        const ipfsResult = await this.ipfs.getFile(ipfsCid, {
          ...options.ipfsOptions,
          format: options.format || 'buffer'
        });
        
        if (!ipfsResult.success) {
          throw new Error(`IPFS-Abruffehler: ${ipfsResult.error}`);
        }
        
        return {
          success: true,
          protocol: StorageProtocol.IPFS,
          data: ipfsResult.content,
          metadata: {
            cid: ipfsCid,
            url: ipfsResult.url,
            format: ipfsResult.format
          }
        };
      } else if (protocol === StorageProtocol.BITTORRENT && btInfoHash) {
        // Bei BitTorrent müssen wir zuerst den Torrent herunterladen und dann die Datei lesen
        const btResult = await this.bt.downloadTorrent(btInfoHash, {
          ...options.btOptions
        });
        
        if (!btResult.success) {
          throw new Error(`BitTorrent-Abruffehler: ${btResult.error}`);
        }
        
        // In einer echten Implementierung würden wir die tatsächlichen Dateien lesen
        // Hier simulieren wir das
        const torrent = btResult.torrent;
        const fileContent = Buffer.from(`Simulierte Datei aus Torrent ${torrent.name}`);
        
        // Parsen des Inhalts entsprechend des gewünschten Formats
        const parsedContent = parseBuffer(fileContent, options.format || 'buffer');
        
        return {
          success: true,
          protocol: StorageProtocol.BITTORRENT,
          data: parsedContent,
          metadata: {
            infoHash: btInfoHash,
            magnetURI: torrent.magnetURI,
            name: torrent.name
          }
        };
      } else if (protocol === StorageProtocol.BOTH) {
        // Versuche zuerst IPFS, dann BitTorrent
        if (ipfsCid) {
          try {
            const ipfsResult = await this.ipfs.getFile(ipfsCid, {
              ...options.ipfsOptions,
              format: options.format || 'buffer'
            });
            
            if (ipfsResult.success) {
              return {
                success: true,
                protocol: StorageProtocol.IPFS,
                data: ipfsResult.content,
                metadata: {
                  cid: ipfsCid,
                  url: ipfsResult.url,
                  format: ipfsResult.format,
                  alternativeProtocol: btInfoHash ? {
                    protocol: StorageProtocol.BITTORRENT,
                    id: btInfoHash
                  } : null
                }
              };
            }
          } catch (error) {
            console.warn('IPFS-Abruf fehlgeschlagen, versuche BitTorrent:', error);
          }
        }
        
        if (btInfoHash) {
          const btResult = await this.bt.downloadTorrent(btInfoHash, {
            ...options.btOptions
          });
          
          if (!btResult.success) {
            throw new Error(`Beide Protokollabrufe fehlgeschlagen. BitTorrent-Fehler: ${btResult.error}`);
          }
          
          const torrent = btResult.torrent;
          const fileContent = Buffer.from(`Simulierte Datei aus Torrent ${torrent.name}`);
          const parsedContent = parseBuffer(fileContent, options.format || 'buffer');
          
          return {
            success: true,
            protocol: StorageProtocol.BITTORRENT,
            data: parsedContent,
            metadata: {
              infoHash: btInfoHash,
              magnetURI: torrent.magnetURI,
              name: torrent.name,
              alternativeProtocol: ipfsCid ? {
                protocol: StorageProtocol.IPFS,
                id: ipfsCid
              } : null
            }
          };
        }
      }
      
      throw new Error('Konnte keine gültige ID für das gewählte Protokoll finden');
    } catch (error) {
      console.error('Fehler beim Abrufen der Daten:', error);
      return {
        success: false,
        error: error.message,
        protocol
      };
    }
  }
  
  /**
   * Löscht Daten aus dem Speicher (wo möglich)
   * @param {string|Object} id IPFS-CID, BitTorrent-Infohash oder Objekt mit beiden
   * @param {Object} options Optionen für die Löschung
   * @param {StorageProtocol} options.protocol Zu verwendendes Protokoll
   * @returns {Promise<Object>} Löschergebnis
   */
  async deleteData(id, options = {}) {
    // Verwende das Standard- oder angegebene Protokoll
    const protocol = options.protocol || this.config.defaultProtocol;
    
    try {
      // Bestimme die tatsächlichen IDs basierend auf dem Eingabeparameter
      let ipfsCid = null;
      let btInfoHash = null;
      
      if (typeof id === 'string') {
        // Wenn ID ein String ist, interpretiere je nach Protokoll
        if (protocol === StorageProtocol.IPFS) {
          ipfsCid = id;
          btInfoHash = this.storageMap.get(id);
        } else if (protocol === StorageProtocol.BITTORRENT) {
          btInfoHash = id;
          ipfsCid = this.storageMap.get(id);
        } else {
          // Bei BOTH, versuche zu erraten, welches Format es ist
          if (id.startsWith('Qm') || id.startsWith('bafy')) {
            ipfsCid = id;
            btInfoHash = this.storageMap.get(id);
          } else {
            btInfoHash = id;
            ipfsCid = this.storageMap.get(id);
          }
        }
      } else if (typeof id === 'object') {
        // Wenn ID ein Objekt ist, extrahiere beide IDs
        ipfsCid = id.ipfs;
        btInfoHash = id.bittorrent;
      }
      
      const results = {
        success: true,
        protocol,
        ipfs: null,
        bittorrent: null
      };
      
      // Je nach Protokoll löschen
      if ((protocol === StorageProtocol.IPFS || protocol === StorageProtocol.BOTH) && ipfsCid) {
        // Bei IPFS können wir nur das Pinning entfernen
        const unpinResult = await this.ipfs.unpinFile(ipfsCid);
        results.ipfs = unpinResult;
        
        if (!unpinResult.success) {
          results.success = false;
        }
      }
      
      if ((protocol === StorageProtocol.BITTORRENT || protocol === StorageProtocol.BOTH) && btInfoHash) {
        // Bei BitTorrent können wir den Torrent aus dem Client entfernen
        const removeResult = await this.bt.removeTorrent(btInfoHash, true);
        results.bittorrent = removeResult;
        
        if (!removeResult.success) {
          results.success = false;
        }
      }
      
      // Entferne Referenzen aus der Map
      if (ipfsCid) this.storageMap.delete(ipfsCid);
      if (btInfoHash) this.storageMap.delete(btInfoHash);
      
      return results;
    } catch (error) {
      console.error('Fehler beim Löschen der Daten:', error);
      return {
        success: false,
        error: error.message,
        protocol
      };
    }
  }
  
  /**
   * Prüft, ob Daten verfügbar sind
   * @param {string|Object} id IPFS-CID, BitTorrent-Infohash oder Objekt mit beiden
   * @param {Object} options Optionen für die Prüfung
   * @param {StorageProtocol} options.protocol Zu verwendendes Protokoll
   * @returns {Promise<Object>} Verfügbarkeitsergebnis
   */
  async checkAvailability(id, options = {}) {
    // Verwende das Standard- oder angegebene Protokoll
    const protocol = options.protocol || this.config.defaultProtocol;
    
    try {
      // Bestimme die tatsächlichen IDs basierend auf dem Eingabeparameter
      let ipfsCid = null;
      let btInfoHash = null;
      
      if (typeof id === 'string') {
        // Interpretiere ID je nach Protokoll
        if (protocol === StorageProtocol.IPFS) {
          ipfsCid = id;
          btInfoHash = this.storageMap.get(id);
        } else if (protocol === StorageProtocol.BITTORRENT) {
          btInfoHash = id;
          ipfsCid = this.storageMap.get(id);
        } else {
          // Bei BOTH, versuche zu erraten, welches Format es ist
          if (id.startsWith('Qm') || id.startsWith('bafy')) {
            ipfsCid = id;
            btInfoHash = this.storageMap.get(id);
          } else {
            btInfoHash = id;
            ipfsCid = this.storageMap.get(id);
          }
        }
      } else if (typeof id === 'object') {
        // Wenn ID ein Objekt ist, extrahiere beide IDs
        ipfsCid = id.ipfs;
        btInfoHash = id.bittorrent;
      }
      
      const results = {
        success: true,
        protocol,
        available: false,
        details: {}
      };
      
      // Je nach Protokoll Verfügbarkeit prüfen
      if ((protocol === StorageProtocol.IPFS || protocol === StorageProtocol.BOTH) && ipfsCid) {
        try {
          // Versuche, nur die Header der Datei zu laden, um zu prüfen, ob sie existiert
          const ipfsResult = await this.ipfs.getFile(ipfsCid);
          results.details.ipfs = {
            available: ipfsResult.success,
            cid: ipfsCid
          };
          
          if (ipfsResult.success) {
            results.available = true;
          }
        } catch (error) {
          results.details.ipfs = {
            available: false,
            error: error.message,
            cid: ipfsCid
          };
        }
      }
      
      if ((protocol === StorageProtocol.BITTORRENT || protocol === StorageProtocol.BOTH) && btInfoHash) {
        try {
          // Prüfe, ob Torrent-Infos abgerufen werden können
          const btResult = await this.bt.getTorrentInfo(btInfoHash);
          results.details.bittorrent = {
            available: btResult.success,
            infoHash: btInfoHash
          };
          
          if (btResult.success) {
            results.available = true;
          }
        } catch (error) {
          results.details.bittorrent = {
            available: false,
            error: error.message,
            infoHash: btInfoHash
          };
        }
      }
      
      return results;
    } catch (error) {
      console.error('Fehler bei der Verfügbarkeitsprüfung:', error);
      return {
        success: false,
        error: error.message,
        protocol,
        available: false
      };
    }
  }
  
  /**
   * Schließt alle Speicherprotokolle
   * @returns {Promise<boolean>} Erfolg
   */
  async shutdown() {
    try {
      await this.bt.shutdown();
      // IPFS hat keine explizite Shutdown-Methode
      return true;
    } catch (error) {
      console.error('Fehler beim Herunterfahren des Storage-Service:', error);
      return false;
    }
  }
}

export default StorageService;
