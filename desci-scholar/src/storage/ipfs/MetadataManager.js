/**
 * MetadataManager - Spezialisierte Komponente für die Verwaltung von Metadaten in IPFS
 * Optimiert für die Speicherung wissenschaftlicher Metadaten mit effizienter Abrufbarkeit
 */

import IPFSManager from './IPFSManager';

class MetadataManager {
  /**
   * Konstruktor für den Metadaten-Manager
   * @param {Object} config Konfigurationsobjekt
   * @param {Object} config.ipfs IPFS-Konfigurationsobjekt
   * @param {boolean} config.enableCompression Komprimierung aktivieren (Standard: true)
   * @param {boolean} config.pinMetadata Metadaten automatisch pinnen (Standard: true)
   * @param {boolean} config.redundantStorage Redundante Speicherung (Standard: true)
   */
  constructor(config = {}) {
    this.config = {
      ipfs: {},
      enableCompression: true,
      pinMetadata: true,
      redundantStorage: true,
      ...config
    };
    
    this.ipfsManager = new IPFSManager(this.config.ipfs);
    this.metadataRegistry = new Map(); // Lokaler Cache für Metadaten-CIDs
  }
  
  /**
   * Initialisiert den Metadaten-Manager
   * @returns {Promise<boolean>} Initialisierungsstatus
   */
  async initialize() {
    try {
      const connected = await this.ipfsManager.connect();
      
      if (connected) {
        console.log('MetadataManager erfolgreich initialisiert');
        return true;
      } else {
        console.error('Fehler beim Verbinden mit IPFS');
        return false;
      }
    } catch (error) {
      console.error('Fehler bei der Initialisierung des MetadataManager:', error);
      return false;
    }
  }
  
  /**
   * Speichert Metadaten einer Publikation in IPFS
   * 
   * @param {Object} metadata Metadatenobjekt
   * @param {string} publicationId Publikations-ID
   * @param {Object} options Speicheroptionen
   * @returns {Promise<Object>} Speicherergebnis mit CID
   */
  async storeMetadata(metadata, publicationId, options = {}) {
    try {
      // Formatiere Metadaten für IPFS-Speicherung
      const formattedMetadata = this._formatMetadata(metadata, publicationId);
      
      // Optional: Komprimiere Metadaten für effiziente Speicherung
      const processedData = this.config.enableCompression
        ? await this._compressMetadata(formattedMetadata)
        : JSON.stringify(formattedMetadata);
      
      // Speichere in IPFS
      const result = await this.ipfsManager.uploadFile(processedData, {
        filename: `${publicationId}-metadata.json`,
        pin: this.config.pinMetadata,
        ...options
      });
      
      if (!result.success) {
        throw new Error(`IPFS-Speicherfehler: ${result.error}`);
      }
      
      // Füge zum lokalen Registry hinzu
      this.metadataRegistry.set(publicationId, {
        cid: result.cid,
        timestamp: new Date(),
        version: metadata.version || '1.0.0'
      });
      
      return {
        success: true,
        cid: result.cid,
        url: result.url,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Fehler beim Speichern der Metadaten:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * Ruft Metadaten aus IPFS ab
   * 
   * @param {string} cid Content-ID der Metadaten in IPFS
   * @param {Object} options Abrufoptionen
   * @returns {Promise<Object>} Metadaten
   */
  async retrieveMetadata(cid, options = {}) {
    try {
      const result = await this.ipfsManager.getFile(cid, {
        format: 'json',
        ...options
      });
      
      if (!result.success) {
        throw new Error(`IPFS-Abruffehler: ${result.error}`);
      }
      
      // Wenn komprimiert, dekomprimiere
      let metadata = result.data;
      if (this.config.enableCompression && metadata._compressed) {
        metadata = await this._decompressMetadata(metadata);
      }
      
      return {
        success: true,
        metadata,
        source: 'ipfs'
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Metadaten:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * Aktualisiert Metadaten in IPFS und erstellt einen Versionsverlauf
   * 
   * @param {string} cid Vorherige Content-ID
   * @param {Object} metadata Neue Metadaten
   * @param {string} publicationId Publikations-ID
   * @param {boolean} createNewVersion Neue Version erstellen
   * @returns {Promise<Object>} Aktualisierungsergebnis
   */
  async updateMetadata(cid, metadata, publicationId, createNewVersion = false) {
    try {
      // Lade vorherige Metadaten, um Versionsinformationen zu erhalten
      const previousResult = await this.retrieveMetadata(cid);
      
      if (!previousResult.success) {
        throw new Error(`Konnte vorherige Metadaten nicht abrufen: ${previousResult.error}`);
      }
      
      const previousMetadata = previousResult.metadata;
      
      // Erstelle aktualisierte Metadaten
      const updatedMetadata = {
        ...previousMetadata,
        ...metadata,
        updated: new Date().toISOString()
      };
      
      // Versionierung
      if (createNewVersion) {
        const currentVersion = previousMetadata.version || '1.0.0';
        const newVersion = this._incrementVersion(currentVersion);
        
        updatedMetadata.version = newVersion;
        updatedMetadata.previousVersions = updatedMetadata.previousVersions || [];
        updatedMetadata.previousVersions.push({
          cid,
          version: currentVersion,
          timestamp: previousMetadata.timestamp || new Date().toISOString()
        });
      }
      
      // Speichere aktualisierte Metadaten
      return await this.storeMetadata(updatedMetadata, publicationId);
    } catch (error) {
      console.error('Fehler beim Aktualisieren der Metadaten:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * Formatiert Metadaten für die IPFS-Speicherung
   * @param {Object} metadata Metadaten
   * @param {string} publicationId Publikations-ID
   * @returns {Object} Formatierte Metadaten
   * @private
   */
  _formatMetadata(metadata, publicationId) {
    return {
      ...metadata,
      id: publicationId,
      timestamp: new Date().toISOString(),
      schema: 'desci-scholar-metadata-v1'
    };
  }
  
  /**
   * Komprimiert Metadaten für effiziente Speicherung
   * @param {Object} metadata Metadaten-Objekt
   * @returns {string} Komprimierte Metadaten
   * @private
   */
  async _compressMetadata(metadata) {
    // In einer realen Implementierung würde hier eine Komprimierung stattfinden
    // z.B. mit zlib oder pako
    
    // Für dieses Beispiel markieren wir es nur als komprimiert
    return JSON.stringify({
      ...metadata,
      _compressed: true
    });
  }
  
  /**
   * Dekomprimiert Metadaten
   * @param {Object} compressedData Komprimierte Metadaten
   * @returns {Object} Dekomprimierte Metadaten
   * @private
   */
  async _decompressMetadata(compressedData) {
    // In einer realen Implementierung würde hier eine Dekomprimierung stattfinden
    
    // Entferne Kompressionsflag
    const { _compressed, ...metadata } = compressedData;
    return metadata;
  }
  
  /**
   * Inkrementiert die Versionsnummer
   * @param {string} version Aktuelle Version (semver)
   * @returns {string} Neue Version
   * @private
   */
  _incrementVersion(version) {
    const parts = version.split('.');
    let [major, minor, patch] = parts.map(p => parseInt(p, 10));
    
    patch += 1;
    if (patch > 99) {
      patch = 0;
      minor += 1;
    }
    
    if (minor > 99) {
      minor = 0;
      major += 1;
    }
    
    return `${major}.${minor}.${patch}`;
  }
  
  /**
   * Führt eine Metadatenabfrage durch (für zukünftige IPFS-Indizierung)
   * @param {Object} query Abfrageparameter
   * @returns {Promise<Object>} Abfrageergebnisse
   */
  async queryMetadata(query = {}) {
    // Diese Methode würde in einer realen Implementierung IPFS-Indizierungsdienste nutzen
    // oder mit einer lokalen Datenbank arbeiten, die IPFS-CIDs indiziert
    
    return {
      success: true,
      results: Array.from(this.metadataRegistry.entries())
        .filter(([id, entry]) => {
          // Einfache Filterlogik basierend auf Publikations-ID
          if (query.id && id !== query.id) return false;
          
          // Weitere Filterlogik könnte hier implementiert werden
          return true;
        })
        .map(([id, entry]) => ({ id, ...entry }))
    };
  }
  
  /**
   * Beendet den Metadaten-Manager
   * @returns {Promise<boolean>} Erfolgsstatus
   */
  async shutdown() {
    try {
      // Bereinigungsoperationen hier durchführen
      return true;
    } catch (error) {
      console.error('Fehler beim Herunterfahren des MetadataManager:', error);
      return false;
    }
  }
}

export default MetadataManager; 