/**
 * @fileoverview IPFS integration for decentralized storage in DeSci-Scholar
 * This module handles interactions with IPFS for storing and retrieving metadata
 */

const { create } = require('ipfs-http-client');
const fs = require('fs').promises;
const path = require('path');
require('dotenv').config();

/**
 * IPFSClient class for managing IPFS interactions
 */
class IPFSClient {
  /**
   * Creates an instance of IPFSClient
   */
  constructor() {
    this.apiUrl = process.env.IPFS_API_URL || 'http://localhost:5001';
    this.gateway = process.env.IPFS_GATEWAY || 'http://localhost:8080';
    this.client = null;
  }

  /**
   * Initialize the IPFS client
   * @returns {Object} Initialized IPFS client
   */
  init() {
    if (this.client) {
      return this.client;
    }

    try {
      // Create an IPFS client connected to the API URL
      this.client = create(new URL(this.apiUrl));
      console.log(`Connected to IPFS node at ${this.apiUrl}`);
      return this.client;
    } catch (error) {
      console.error('Failed to connect to IPFS node:', error);
      throw error;
    }
  }

  /**
   * Add content to IPFS
   * @param {string|Buffer|Object} content - Content to store on IPFS
   * @returns {Promise<string>} IPFS content identifier (CID)
   */
  async add(content) {
    this.init();
    
    try {
      let data;
      
      // Handle different content types
      if (typeof content === 'object' && !Buffer.isBuffer(content)) {
        // Convert object to JSON string
        data = JSON.stringify(content);
      } else {
        data = content;
      }
      
      // Add the content to IPFS
      const result = await this.client.add(data);
      console.log(`Content added to IPFS with CID: ${result.path}`);
      
      return result.path;
    } catch (error) {
      console.error('Failed to add content to IPFS:', error);
      throw error;
    }
  }

  /**
   * Add a file to IPFS
   * @param {string} filePath - Path to the file to store
   * @returns {Promise<string>} IPFS content identifier (CID)
   */
  async addFile(filePath) {
    this.init();
    
    try {
      // Read file content
      const fileContent = await fs.readFile(filePath);
      
      // Add the file to IPFS
      const result = await this.client.add({
        path: path.basename(filePath),
        content: fileContent
      });
      
      console.log(`File added to IPFS with CID: ${result.path}`);
      return result.path;
    } catch (error) {
      console.error('Failed to add file to IPFS:', error);
      throw error;
    }
  }

  /**
   * Get content from IPFS by its CID
   * @param {string} cid - Content identifier
   * @returns {Promise<Buffer>} Content data
   */
  async get(cid) {
    this.init();
    
    try {
      const chunks = [];
      
      // Retrieve data chunks from IPFS
      for await (const chunk of this.client.cat(cid)) {
        chunks.push(chunk);
      }
      
      // Concatenate chunks into a single buffer
      return Buffer.concat(chunks);
    } catch (error) {
      console.error(`Failed to get content with CID ${cid} from IPFS:`, error);
      throw error;
    }
  }

  /**
   * Get JSON content from IPFS and parse it
   * @param {string} cid - Content identifier
   * @returns {Promise<Object>} Parsed JSON data
   */
  async getJSON(cid) {
    const data = await this.get(cid);
    
    try {
      return JSON.parse(data.toString());
    } catch (error) {
      console.error(`Failed to parse JSON from CID ${cid}:`, error);
      throw error;
    }
  }

  /**
   * Pin content to ensure persistence
   * @param {string} cid - Content identifier to pin
   * @returns {Promise<Object>} Pin status
   */
  async pin(cid) {
    this.init();
    
    try {
      await this.client.pin.add(cid);
      console.log(`Content with CID ${cid} pinned successfully`);
      return { cid, pinned: true };
    } catch (error) {
      console.error(`Failed to pin content with CID ${cid}:`, error);
      throw error;
    }
  }

  /**
   * Unpin content
   * @param {string} cid - Content identifier to unpin
   * @returns {Promise<Object>} Unpin status
   */
  async unpin(cid) {
    this.init();
    
    try {
      await this.client.pin.rm(cid);
      console.log(`Content with CID ${cid} unpinned successfully`);
      return { cid, pinned: false };
    } catch (error) {
      console.error(`Failed to unpin content with CID ${cid}:`, error);
      throw error;
    }
  }

  /**
   * Generate a gateway URL for a CID
   * @param {string} cid - Content identifier
   * @returns {string} Gateway URL for the content
   */
  getGatewayUrl(cid) {
    return `${this.gateway}/ipfs/${cid}`;
  }

  /**
   * Store metadata for scientific publication
   * @param {Object} metadata - Publication metadata
   * @returns {Promise<string>} IPFS CID of stored metadata
   */
  async storePublicationMetadata(metadata) {
    // Ensure required fields are present
    const requiredFields = ['title', 'authors', 'abstract', 'doi', 'date'];
    for (const field of requiredFields) {
      if (!metadata[field]) {
        throw new Error(`Missing required metadata field: ${field}`);
      }
    }
    
    // Add timestamp and schema version
    const enhancedMetadata = {
      ...metadata,
      schemaVersion: '1.0',
      timestamp: new Date().toISOString(),
      type: 'scientific-publication'
    };
    
    // Store the metadata on IPFS
    return this.add(enhancedMetadata);
  }

  /**
   * Store metadata for a patent
   * @param {Object} metadata - Patent metadata
   * @returns {Promise<string>} IPFS CID of stored metadata
   */
  async storePatentMetadata(metadata) {
    // Ensure required fields are present
    const requiredFields = ['title', 'inventors', 'patentNumber', 'filingDate', 'issuedDate', 'abstract'];
    for (const field of requiredFields) {
      if (!metadata[field]) {
        throw new Error(`Missing required metadata field: ${field}`);
      }
    }
    
    // Add timestamp and schema version
    const enhancedMetadata = {
      ...metadata,
      schemaVersion: '1.0',
      timestamp: new Date().toISOString(),
      type: 'patent'
    };
    
    // Store the metadata on IPFS
    return this.add(enhancedMetadata);
  }
}

module.exports = IPFSClient;
