/**
 * @fileoverview Integrated storage module for DeSci-Scholar
 * This module provides a unified interface for storage operations,
 * using IPFS for metadata and BitTorrent for large files
 */

const IPFSClient = require('./ipfs.js');
const BitTorrentClient = require('./bittorrent.js');
const path = require('path');
const fs = require('fs').promises;
const os = require('os');

/**
 * StorageManager class that integrates IPFS and BitTorrent
 * IPFS is used for metadata storage (small files)
 * BitTorrent is used for large file distribution
 */
class StorageManager {
  /**
   * Creates an instance of StorageManager
   */
  constructor() {
    this.ipfs = new IPFSClient();
    this.bittorrent = new BitTorrentClient();
    this.tempDir = path.join(os.tmpdir(), 'desci-scholar-storage');
    
    // Size threshold for using BitTorrent instead of IPFS (in bytes)
    // Files larger than this threshold will use BitTorrent
    // Default: 10MB
    this.sizeThreshold = process.env.STORAGE_SIZE_THRESHOLD || 10 * 1024 * 1024;
  }

  /**
   * Initialize the storage systems
   */
  async init() {
    // Initialize both storage systems
    this.ipfs.init();
    this.bittorrent.init();
    
    // Ensure temp directory exists
    await fs.mkdir(this.tempDir, { recursive: true });
    
    console.log('Storage manager initialized with:');
    console.log(`- IPFS for metadata and files smaller than ${this.formatSize(this.sizeThreshold)}`);
    console.log('- BitTorrent for large files distribution');
  }

  /**
   * Format size in bytes to human-readable format
   * @param {number} bytes - Size in bytes
   * @returns {string} Human-readable size
   */
  formatSize(bytes) {
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(2)} ${units[unitIndex]}`;
  }

  /**
   * Store publication metadata and reference to content
   * @param {Object} metadata - Publication metadata
   * @param {string} contentPath - Path to the publication content file
   * @returns {Promise<Object>} Storage information
   */
  async storePublication(metadata, contentPath) {
    try {
      // Check if content file exists
      const stats = await fs.stat(contentPath);
      const fileSize = stats.size;
      
      // Store metadata on IPFS
      const metadataCid = await this.ipfs.storePublicationMetadata(metadata);
      console.log(`Publication metadata stored on IPFS with CID: ${metadataCid}`);
      
      let contentReference;
      
      // Determine storage method based on file size
      if (fileSize > this.sizeThreshold) {
        // Large file - use BitTorrent
        console.log(`File size (${this.formatSize(fileSize)}) exceeds threshold, using BitTorrent`);
        
        // Create torrent with minimal metadata (full metadata is already on IPFS)
        const torrentInfo = await this.bittorrent.createTorrent(contentPath, {
          comment: `DeSci-Scholar Publication: ${metadata.title}`,
          createdBy: 'DeSci-Scholar',
          saveTorrentFile: true
        });
        
        contentReference = {
          type: 'bittorrent',
          magnetURI: torrentInfo.magnetURI,
          infoHash: torrentInfo.infoHash,
          size: torrentInfo.length
        };
      } else {
        // Small file - use IPFS
        console.log(`File size (${this.formatSize(fileSize)}) within threshold, using IPFS`);
        const contentCid = await this.ipfs.addFile(contentPath);
        
        contentReference = {
          type: 'ipfs',
          cid: contentCid,
          size: fileSize,
          gatewayUrl: this.ipfs.getGatewayUrl(contentCid)
        };
      }
      
      // Update metadata with content reference
      const updatedMetadata = {
        ...metadata,
        content: contentReference,
        metadataCid
      };
      
      // Store updated metadata on IPFS
      const finalMetadataCid = await this.ipfs.storePublicationMetadata(updatedMetadata);
      console.log(`Updated publication metadata stored on IPFS with CID: ${finalMetadataCid}`);
      
      // Pin the metadata to ensure persistence
      await this.ipfs.pin(finalMetadataCid);
      
      return {
        metadataCid: finalMetadataCid,
        contentReference,
        ipfsGatewayUrl: this.ipfs.getGatewayUrl(finalMetadataCid)
      };
    } catch (error) {
      console.error('Failed to store publication:', error);
      throw error;
    }
  }

  /**
   * Store dataset with metadata
   * @param {Object} metadata - Dataset metadata
   * @param {string} datasetPath - Path to the dataset file or directory
   * @returns {Promise<Object>} Storage information
   */
  async storeDataset(metadata, datasetPath) {
    try {
      // Check if dataset exists
      const stats = await fs.stat(datasetPath);
      const datasetSize = stats.isDirectory() ? await this.calculateDirectorySize(datasetPath) : stats.size;
      
      // Store metadata on IPFS
      const enhancedMetadata = {
        ...metadata,
        size: datasetSize,
        sizeFormatted: this.formatSize(datasetSize),
        timestamp: new Date().toISOString(),
        type: 'dataset'
      };
      
      const metadataCid = await this.ipfs.add(enhancedMetadata);
      console.log(`Dataset metadata stored on IPFS with CID: ${metadataCid}`);
      
      // Large datasets always use BitTorrent
      console.log(`Creating BitTorrent for dataset (${this.formatSize(datasetSize)})`);
      
      // Create dataset torrent with metadata
      const torrentInfo = await this.bittorrent.createDatasetTorrent(datasetPath, enhancedMetadata);
      
      // Update metadata with torrent information
      const torrentMetadata = {
        ...enhancedMetadata,
        torrent: {
          magnetURI: torrentInfo.magnetURI,
          infoHash: torrentInfo.infoHash,
          files: torrentInfo.files
        },
        metadataCid
      };
      
      // Store updated metadata on IPFS
      const finalMetadataCid = await this.ipfs.add(torrentMetadata);
      console.log(`Updated dataset metadata stored on IPFS with CID: ${finalMetadataCid}`);
      
      // Pin the metadata to ensure persistence
      await this.ipfs.pin(finalMetadataCid);
      
      return {
        metadataCid: finalMetadataCid,
        torrentInfo: {
          magnetURI: torrentInfo.magnetURI,
          infoHash: torrentInfo.infoHash
        },
        ipfsGatewayUrl: this.ipfs.getGatewayUrl(finalMetadataCid)
      };
    } catch (error) {
      console.error('Failed to store dataset:', error);
      throw error;
    }
  }

  /**
   * Calculate the total size of a directory
   * @param {string} directoryPath - Path to the directory
   * @returns {Promise<number>} Total size in bytes
   */
  async calculateDirectorySize(directoryPath) {
    let totalSize = 0;
    
    // Read directory contents
    const items = await fs.readdir(directoryPath, { withFileTypes: true });
    
    // Process each item
    for (const item of items) {
      const itemPath = path.join(directoryPath, item.name);
      
      if (item.isDirectory()) {
        // Recursively calculate subdirectory size
        totalSize += await this.calculateDirectorySize(itemPath);
      } else {
        // Add file size
        const stats = await fs.stat(itemPath);
        totalSize += stats.size;
      }
    }
    
    return totalSize;
  }

  /**
   * Retrieve content by its reference
   * @param {Object} contentReference - Content reference object
   * @param {string} outputPath - Path to save the retrieved content
   * @returns {Promise<string>} Path to the retrieved content
   */
  async retrieveContent(contentReference, outputPath) {
    if (!contentReference || !contentReference.type) {
      throw new Error('Invalid content reference');
    }
    
    try {
      if (contentReference.type === 'ipfs') {
        // Retrieve from IPFS
        const content = await this.ipfs.get(contentReference.cid);
        
        // Ensure output directory exists
        await fs.mkdir(path.dirname(outputPath), { recursive: true });
        
        // Write content to file
        await fs.writeFile(outputPath, content);
        console.log(`Content retrieved from IPFS and saved to ${outputPath}`);
        
        return outputPath;
      } else if (contentReference.type === 'bittorrent') {
        // Retrieve from BitTorrent
        const outputDir = path.dirname(outputPath);
        
        // Download torrent
        const result = await this.bittorrent.downloadTorrent(
          contentReference.magnetURI,
          outputDir
        );
        
        console.log(`Content retrieved from BitTorrent and saved to ${result.downloadPath}`);
        return result.downloadPath;
      } else {
        throw new Error(`Unsupported content reference type: ${contentReference.type}`);
      }
    } catch (error) {
      console.error('Failed to retrieve content:', error);
      throw error;
    }
  }

  /**
   * Cleanup and shutdown storage systems
   */
  async cleanup() {
    try {
      // Destroy BitTorrent client
      await this.bittorrent.destroy();
      
      // Clean up temp directory
      await fs.rm(this.tempDir, { recursive: true, force: true });
      
      console.log('Storage manager cleaned up successfully');
    } catch (error) {
      console.error('Error during storage cleanup:', error);
    }
  }
}

module.exports = StorageManager;
