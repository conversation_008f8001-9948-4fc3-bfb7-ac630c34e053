/**
 * @fileoverview Test-Server für Handle System Integration
 */

import express from 'express';
import cors from 'cors';
import axios from 'axios';

const app = express();
const PORT = process.env.PORT || 3004;

// Middleware
app.use(cors());
app.use(express.json());

// Logger
const logger = {
  info: (msg, data) => console.log(`[INFO] ${msg}`, data || ''),
  error: (msg, data) => console.error(`[ERROR] ${msg}`, data || ''),
  warn: (msg, data) => console.warn(`[WARN] ${msg}`, data || '')
};

/**
 * Parse HTML Handle Response von hdl.handle.net
 */
function parseHTMLHandleResponse(htmlData, handle, options) {
  try {
    const values = [];
    
    // Regex für Tabellenzeilen mit Handle-Werten
    const rowRegex = /<tr><td><b>(\d+)<\/b><\/td><td><b>(?:<a[^>]*>)?([^<]+)(?:<\/a>)?<\/b><\/td><td[^>]*>([^<]+)<\/td>\s*<td[^>]*>([^<]+)<\/td><\/tr>/g;
    
    let match;
    while ((match = rowRegex.exec(htmlData)) !== null) {
      const [, index, type, timestamp, data] = match;
      
      // Bereinige HTML-Tags und Entities
      const cleanData = data.trim()
        .replace(/<[^>]*>/g, '')
        .replace(/&nbsp;/g, ' ')
        .replace(/&amp;/g, '&')
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>');
      
      values.push({
        index: parseInt(index),
        type: type.trim(),
        timestamp: timestamp.trim().replace(/&nbsp;/g, ' '),
        data: cleanData,
        raw: data.trim()
      });
    }

    // Extrahiere spezielle Werte
    const urlValues = values.filter(v => v.type === 'URL');
    const adminValues = values.filter(v => v.type === 'HS_ADMIN');
    const primaryUrl = urlValues.length > 0 ? urlValues[0].data : null;

    // Generiere NFT-URL-Vorschlag
    const nftUrlSuggestion = {
      suggestedURL: handle.replace(/[\/\.]/g, '-').toLowerCase() + '.desci',
      domain: '.desci',
      originalHandle: handle,
      originalUrl: primaryUrl,
      benefits: [
        'blockchain_ownership',
        'no_annual_fees',
        'automatic_royalties', 
        'decentralized_resolution',
        'immutable_metadata'
      ]
    };

    return {
      handle,
      resolved: values.length > 0,
      timestamp: new Date().toISOString(),
      values,
      metadata: {
        primaryUrl,
        totalValues: values.length,
        urlCount: urlValues.length,
        adminInfo: adminValues.length > 0 ? adminValues[0].data : null,
        lastModified: values.length > 0 ? values[0].timestamp : null
      },
      source: 'hdl.handle.net_html',
      options,
      nftUrlSuggestion
    };
  } catch (error) {
    logger.warn(`Fehler beim Parsen der HTML Handle Response: ${error.message}`);
    return {
      handle,
      resolved: false,
      error: error.message,
      timestamp: new Date().toISOString(),
      source: 'hdl.handle.net_parse_error',
      options
    };
  }
}

/**
 * Echte Handle System Auflösung über hdl.handle.net
 */
async function resolveRealHandle(handle, options = {}) {
  try {
    // Baue Query-Parameter wie hdl.handle.net
    const params = {};
    if (options.noredirect) params.noredirect = 'true';
    if (options.auth) params.auth = 'true';
    if (options.noalias) params.noalias = 'true';
    if (options.type) params.type = options.type;

    logger.info(`Echte Handle-Auflösung: ${handle}`, { params });

    // Direkte Anfrage an hdl.handle.net
    const response = await axios.get(`http://hdl.handle.net/${handle}`, {
      timeout: 10000,
      headers: {
        'Accept': options.type === 'JSON' ? 'application/json' : 'text/html',
        'User-Agent': 'DeSci-Scholar/2.0 HandleSystemBridge'
      },
      params,
      validateStatus: (status) => status < 500 // Akzeptiere auch 404, etc.
    });

    if (response.status === 200) {
      return parseHTMLHandleResponse(response.data, handle, options);
    } else {
      return {
        handle,
        resolved: false,
        error: `HTTP ${response.status}`,
        timestamp: new Date().toISOString(),
        source: 'hdl.handle.net',
        options
      };
    }
  } catch (error) {
    logger.warn(`Handle-Auflösung fehlgeschlagen: ${error.message}`);
    return {
      handle,
      resolved: false,
      error: error.message,
      timestamp: new Date().toISOString(),
      source: 'hdl.handle.net_error',
      options
    };
  }
}

// Routes
app.get('/', (req, res) => {
  res.json({
    name: 'DeSci-Scholar Handle System Test',
    version: '1.0.0',
    description: 'Test-Server für vollständige hdl.handle.net Integration',
    endpoints: {
      handle: '/api/handle/{handle}?noredirect=true&auth=true&type=JSON'
    }
  });
});

// Handle System Resolution - Vollständige hdl.handle.net Integration
app.get('/api/handle/:handle(*)', async (req, res) => {
  const handle = req.params.handle;
  const { noredirect, auth, noalias, type } = req.query;

  if (!handle) {
    return res.status(400).json({
      error: 'Handle parameter is required',
      usage: 'GET /api/handle/{handle}?noredirect=true&auth=true&type=JSON',
      parameters: {
        noredirect: 'Don\'t redirect to URLs (like hdl.handle.net)',
        auth: 'Authoritative query (bypass cache)',
        noalias: 'Don\'t follow aliases',
        type: 'Response type (JSON, XML, HTML)'
      }
    });
  }

  try {
    logger.info(`Handle System Auflösung für: ${handle}`, { 
      noredirect: !!noredirect, 
      auth: !!auth, 
      noalias: !!noalias, 
      type 
    });

    // Echte Handle System Integration mit hdl.handle.net
    const handleOptions = {
      noredirect: noredirect === 'true',
      auth: auth === 'true', 
      noalias: noalias === 'true',
      type: type || 'HTML'
    };

    const handleData = await resolveRealHandle(handle, handleOptions);

    res.json({
      service: 'Handle System (hdl.handle.net)',
      handle,
      proxyURL: `http://hdl.handle.net/${handle}`,
      status: handleData.resolved ? 'resolved' : 'not_found',
      options: handleOptions,
      resolution: handleData,
      
      // DeSci-Scholar NFT-URL Bridge Integration
      nftBridge: {
        available: true,
        nftUrlSuggestion: handleData.nftUrlSuggestion,
        migration: {
          endpoint: '/api/bridge/doi-to-nft-url',
          preserveHandle: true,
          bidirectional: true,
          features: [
            'metadata_embedding',
            'citation_royalties',
            'decentralized_storage',
            'blockchain_ownership'
          ]
        }
      }
    });

  } catch (error) {
    logger.error(`Fehler bei Handle-Auflösung für ${handle}:`, error);
    res.status(500).json({
      error: 'Handle resolution failed',
      handle,
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Health Check
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'DeSci-Scholar Handle System Test'
  });
});

// Start Server
app.listen(PORT, () => {
  logger.info(`DeSci-Scholar Handle System Test Server läuft auf Port ${PORT}`);
  logger.info(`Test-URL: http://localhost:${PORT}/api/handle/10.1038/nature12373?noredirect=true&auth=true`);
});
