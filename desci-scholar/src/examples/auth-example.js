/**
 * Authentifizierungsbeispiel für DeSci-Scholar
 * 
 * Demonstriert die Verwendung des AuthService für die Benutzerregistrierung,
 * Anmeldung, Tokenvalidierung und Berechtigungsprüfung.
 */

import { AuthService, USER_ROLES, PERMISSIONS } from '../auth/AuthService.js';
import DatabaseService from '../db/DatabaseService.js';

// Hilfsfunktion für formatierte Konsolenausgabe
function logSection(title) {
  console.log('\n' + '='.repeat(50));
  console.log(`${title}`);
  console.log('='.repeat(50));
}

/**
 * Hauptfunktion des Beispiels
 */
async function runAuthExample() {
  logSection('AuthService Beispiel');
  
  try {
    // 1. Datenbankdienst initialisieren (in-memory für das Beispiel)
    const dbService = new DatabaseService({
      useInMemory: true,
      dbName: 'desci-auth-example'
    });
    
    await dbService.connect();
    console.log('Datenbankverbindung hergestellt');
    
    // 2. AuthService initialisieren
    const authService = new AuthService({
      databaseService: dbService,
      jwtSecret: 'beispiel-jwt-geheimnis-nicht-in-produktion-verwenden',
      tokenExpiresIn: 3600,           // 1 Stunde
      refreshTokenExpiresIn: 86400,   // 1 Tag
      saltRounds: 10
    });
    
    await authService.initialize();
    console.log('AuthService initialisiert');
    
    // Event-Listener für Authentifizierungsereignisse registrieren
    authService.on('user.register', (data) => {
      console.log(`Event: Benutzer registriert - ${data.email}`);
    });
    
    authService.on('user.login', (data) => {
      console.log(`Event: Benutzer angemeldet - ${data.user.email}`);
    });
    
    // 3. Benutzer registrieren
    logSection('Benutzerregistrierung');
    
    // Autor registrieren
    const authorData = {
      email: '<EMAIL>',
      password: 'Sicher123!',
      name: 'Max Mustermann',
      institution: 'Universität Hamburg',
      roles: [USER_ROLES.AUTHOR]
    };
    
    const author = await authService.registerUser(authorData);
    console.log('Autor registriert:', author);
    
    // In einer echten Anwendung würde hier die E-Mail-Verifizierung erfolgen.
    // Für das Beispiel setzen wir den Benutzer direkt auf verifiziert:
    await dbService.getCollection('users').updateOne(
      { _id: dbService.createObjectId(author._id) },
      { $set: { verified: true } }
    );
    console.log('Autor als verifiziert markiert');
    
    // Reviewer registrieren
    const reviewerData = {
      email: '<EMAIL>',
      password: 'Sicher456!',
      name: 'Anna Schmidt',
      institution: 'Technische Universität Berlin',
      roles: [USER_ROLES.REVIEWER]
    };
    
    const reviewer = await authService.registerUser(reviewerData);
    console.log('Reviewer registriert:', reviewer);
    
    // Auch den Reviewer direkt verifizieren
    await dbService.getCollection('users').updateOne(
      { _id: dbService.createObjectId(reviewer._id) },
      { $set: { verified: true } }
    );
    console.log('Reviewer als verifiziert markiert');
    
    // 4. Benutzeranmeldung
    logSection('Benutzeranmeldung');
    
    // Autor anmelden
    const authorLogin = await authService.loginUser(authorData.email, authorData.password, {
      deviceInfo: 'Chrome, macOS'
    });
    
    console.log('Autor angemeldet:', {
      sessionId: authorLogin.sessionId,
      userName: authorLogin.user.name,
      userRoles: authorLogin.user.roles,
      // Token anzeigen (in einer echten Anwendung nie loggen!)
      tokenPreview: authorLogin.token.substring(0, 20) + '...'
    });
    
    // Reviewer anmelden
    const reviewerLogin = await authService.loginUser(reviewerData.email, reviewerData.password, {
      deviceInfo: 'Firefox, Windows'
    });
    
    console.log('Reviewer angemeldet:', {
      sessionId: reviewerLogin.sessionId,
      userName: reviewerLogin.user.name,
      userRoles: reviewerLogin.user.roles
    });
    
    // 5. Token validieren
    logSection('Token-Validierung');
    
    const validatedToken = await authService.validateToken(authorLogin.token);
    console.log('Validiertes Token für Autor:', {
      userId: validatedToken.userId,
      email: validatedToken.email,
      roles: validatedToken.roles,
      permissions: validatedToken.permissions
    });
    
    // 6. Berechtigungsprüfung
    logSection('Berechtigungsprüfung');
    
    // Autor sollte Publikationen erstellen können
    const authorCanCreate = await authService.hasPermission(
      author._id, 
      PERMISSIONS.CREATE_PUBLICATION
    );
    console.log(`Kann der Autor Publikationen erstellen? ${authorCanCreate}`);
    
    // Autor sollte keine Publikationen genehmigen können
    const authorCanApprove = await authService.hasPermission(
      author._id, 
      PERMISSIONS.APPROVE_PUBLICATION
    );
    console.log(`Kann der Autor Publikationen genehmigen? ${authorCanApprove}`);
    
    // Reviewer sollte Publikationen reviewen können
    const reviewerCanReview = await authService.hasPermission(
      reviewer._id, 
      PERMISSIONS.REVIEW_PUBLICATION
    );
    console.log(`Kann der Reviewer Publikationen reviewen? ${reviewerCanReview}`);
    
    // 7. Benutzerabmeldung
    logSection('Benutzerabmeldung');
    
    // Autor abmelden
    await authService.logoutUser(authorLogin.sessionId);
    console.log('Autor abgemeldet');
    
    // 8. Administrator finden
    logSection('Administrator-Informationen');
    
    const adminUser = await dbService.getCollection('users').findOne({ 
      roles: USER_ROLES.ADMIN 
    });
    
    if (adminUser) {
      console.log('Administrator gefunden:', {
        email: adminUser.email,
        name: adminUser.name,
        roles: adminUser.roles
      });
      
      // Administratorberechtigungen prüfen
      const adminPermissions = authService._getUserPermissions(adminUser.roles);
      console.log('Administrator-Berechtigungen:', adminPermissions);
    }
    
    // 9. Aufräumen und Beispiel beenden
    logSection('Beispiel beendet');
    await dbService.disconnect();
    console.log('Datenbankverbindung getrennt');
    
  } catch (error) {
    console.error('Fehler im Authentifizierungsbeispiel:', error);
  }
}

// Beispiel ausführen
runAuthExample().catch(error => {
  console.error('Unbehandelte Ausnahme:', error);
}); 