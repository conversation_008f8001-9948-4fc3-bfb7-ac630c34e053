/**
 * publication-stats-integration.js
 * 
 * Dieses Be<PERSON>piel demonstriert die Integration von PublicationController und StatsService
 * zur Erfassung und Analyse von Publikationsstatistiken in DeSci-Scholar.
 */

import { fileURLToPath } from 'url';
import path from 'path';
import fs from 'fs/promises';
import crypto from 'crypto';

import PublicationController from '../controllers/PublicationController.js';
import StatsService from '../stats/ice.js';
import DatabaseService from '../db/DatabaseService.js';
import EnhancedStorageService from '../storage/EnhancedStorageService.js';

// Pfad für temporäre Dateien
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const TEMP_DIR = path.join(__dirname, 'temp');
const DOWNLOAD_DIR = path.join(__dirname, 'downloads');

// Hilfsfunktion zum Generieren einer PDF-Datei für Tests
async function generateSamplePDF(filename, sizeInMB = 3) {
  console.log(`Generiere PDF-Datei mit Größe ca. ${sizeInMB} MB...`);
  
  // Stelle sicher, dass das Verzeichnis existiert
  await fs.mkdir(path.dirname(filename), { recursive: true });
  
  // Erstelle einen Stream zum Schreiben
  const writeStream = await fs.open(filename, 'w');
  
  try {
    // Schreibe PDF-Header
    await writeStream.write('%PDF-1.7\n');
    
    // Berechne, wie viele Blöcke wir schreiben müssen
    const blockSize = 1024; // 1KB Blöcke
    const numBlocks = Math.ceil((sizeInMB * 1024 * 1024) / blockSize);
    
    // Generiere zufällige Daten
    for (let i = 0; i < numBlocks; i++) {
      const randomData = crypto.randomBytes(blockSize);
      await writeStream.write(randomData);
      
      // Fortschrittsanzeige
      if (i % 1000 === 0) {
        process.stdout.write('.');
      }
    }
    
    // Schreibe PDF-Footer
    await writeStream.write('\n%%EOF\n');
    
    console.log(`\nPDF-Datei ${filename} erstellt.`);
  } finally {
    await writeStream.close();
  }
  
  return filename;
}

// Hilfsfunktion zum Simulieren von Zugriffsereignissen
async function simulatePublicationActivity(statsService, publicationId, numEvents = 50) {
  console.log(`Simuliere ${numEvents} Zugriffsereignisse für Publikation ${publicationId}...`);
  
  const eventTypes = ['view', 'download', 'citation'];
  const countries = ['Deutschland', 'USA', 'Frankreich', 'Japan', 'Brasilien', 'Indien', 'Australien'];
  const browsers = ['Chrome', 'Firefox', 'Safari', 'Edge'];
  const platforms = ['Desktop', 'Mobile', 'Tablet'];
  
  for (let i = 0; i < numEvents; i++) {
    const eventType = eventTypes[Math.floor(Math.random() * eventTypes.length)];
    const country = countries[Math.floor(Math.random() * countries.length)];
    const browser = browsers[Math.floor(Math.random() * browsers.length)];
    const platform = platforms[Math.floor(Math.random() * platforms.length)];
    
    // Simuliere unterschiedliche Zeitpunkte über die letzten 30 Tage
    const daysAgo = Math.floor(Math.random() * 30);
    const accessDate = new Date();
    accessDate.setDate(accessDate.getDate() - daysAgo);
    
    await statsService.recordAccess({
      publicationId,
      type: eventType,
      userId: i % 5 === 0 ? null : `user${i % 10 + 1}`, // Einige anonyme Zugriffe
      metadata: {
        timestamp: accessDate,
        country,
        browser,
        platform
      }
    });
    
    if (i % 10 === 0) {
      process.stdout.write('.');
    }
  }
  
  console.log('\nSimulation abgeschlossen.');
}

// Hauptfunktion
async function main() {
  console.log('=== DeSci-Scholar PublicationController und StatsService Integration ===');
  
  // Deklariere Variablen im äußeren Bereich, damit sie in finally zugänglich sind
  let databaseService, storageService, statsService, publicationController;
  
  try {
    // Stelle sicher, dass Verzeichnisse existieren
    await fs.mkdir(TEMP_DIR, { recursive: true });
    await fs.mkdir(DOWNLOAD_DIR, { recursive: true });
    
    // Initialisiere Dienste
    console.log('Initialisiere Datenbankdienst...');
    databaseService = new DatabaseService({
      uri: 'mongodb://localhost:27017',
      dbName: 'desci_scholar_demo',
      inMemory: true
    });
    await databaseService.connect();
    
    console.log('Initialisiere Speicherdienst...');
    storageService = new EnhancedStorageService({
      storageDir: TEMP_DIR,
      tempDir: TEMP_DIR,
      ipfsConfig: {
        host: 'localhost',
        port: 5001,
        protocol: 'http'
      }
    });
    await storageService.initialize();
    
    console.log('Initialisiere PublicationController...');
    publicationController = new PublicationController({
      databaseService,
      storageService
    });
    await publicationController.initialize();
    
    console.log('Initialisiere StatsService...');
    statsService = new StatsService({
      databaseService,
      storageService
    });
    await statsService.initialize();
    
    // 1. Generiere eine Beispieldatei
    const pdfPath = path.join(TEMP_DIR, 'sample-publication.pdf');
    await generateSamplePDF(pdfPath, 2);
    
    // 2. Erstelle mehrere Publikationen
    console.log('\nErstelle mehrere Publikationen...');
    const publications = [];
    
    for (let i = 0; i < 5; i++) {
      const publication = {
        title: `Publikation zur Forschung in ${['Quantenphysik', 'Klimawissenschaft', 'Neurowissenschaft', 'Genetik', 'Künstliche Intelligenz'][i]}`,
        authors: [
          { id: `author${i+1}`, name: `Dr. Forscher ${i+1}`, affiliation: `Universität ${i+1}` }
        ],
        abstract: `Dies ist eine Zusammenfassung der Forschungsarbeit zu ${['Quantenphysik', 'Klimawissenschaft', 'Neurowissenschaft', 'Genetik', 'Künstliche Intelligenz'][i]}.`,
        keywords: ['wissenschaft', 'forschung', 'desci', `thema-${i+1}`],
        license: 'CC-BY-4.0',
        version: '1.0.0'
      };
      
      console.log(`Erstelle Publikation ${i+1}: ${publication.title}`);
      const createdPub = await publicationController.createPublication(publication);
      publications.push(createdPub);
      
      // Lade die Datei hoch
      const fileResult = await publicationController.uploadPublicationFile(
        createdPub.id,
        pdfPath,
        { 
          description: `Forschungspapier ${i+1}`,
          generatePreview: true 
        }
      );
      
      console.log(`Datei erfolgreich hochgeladen: ${fileResult.file.name}`);
    }
    
    // 3. Simuliere Publikationsaktivitäten für Statistiken
    for (const pub of publications) {
      await simulatePublicationActivity(statsService, pub.id, 100);
    }
    
    // 4. Analysiere Statistiken
    console.log('\n=== GESAMTSTATISTIKEN DER PLATTFORM ===');
    const totalStats = await statsService.getTotalStats();
    console.log(`Gesamtzahl der Publikationen: ${totalStats.publications}`);
    console.log(`Gesamtzahl der Aufrufe: ${totalStats.views}`);
    console.log(`Gesamtzahl der Downloads: ${totalStats.downloads}`);
    console.log(`Durchschnittliche Downloads pro Publikation: ${totalStats.avgDownloadsPerPublication}`);
    console.log(`Gesamtspeichernutzung: ${totalStats.totalStorageFormatted}`);
    
    // 5. Trend-Publikationen
    console.log('\n=== TREND-PUBLIKATIONEN DER LETZTEN 30 TAGE ===');
    const trendingPubs = await statsService.getTrendingPublications({ limit: 3 });
    for (const pub of trendingPubs) {
      console.log(`- ${pub.title}`);
      console.log(`  Autoren: ${pub.authors.map(a => a.name).join(', ')}`);
      console.log(`  Impact-Score: ${pub.metrics.score}`);
      console.log(`  Aufrufe: ${pub.metrics.views}, Downloads: ${pub.metrics.downloads}, Zitierungen: ${pub.metrics.citations}`);
    }
    
    // 6. Detaillierte Statistiken für eine Publikation
    console.log('\n=== DETAILLIERTE STATISTIKEN FÜR EINE PUBLIKATION ===');
    const detailedStats = await statsService.getPublicationStats(publications[0].id);
    console.log(`Titel: ${detailedStats.title}`);
    console.log(`DOI: ${detailedStats.doi || 'Nicht verfügbar'}`);
    console.log('Metriken:');
    console.log(`- Aufrufe: ${detailedStats.metrics.views}`);
    console.log(`- Downloads: ${detailedStats.metrics.downloads}`);
    console.log(`- Zitierungen: ${detailedStats.metrics.citations}`);
    console.log(`- Impact-Score: ${detailedStats.metrics.impactScore}`);
    console.log(`- Download-Konversionsrate: ${detailedStats.metrics.downloadConversionRate}`);
    
    // Geografische Verteilung
    if (detailedStats.geoDistribution && detailedStats.geoDistribution.length > 0) {
      console.log('\nGeografische Verteilung der Zugriffe:');
      for (const geo of detailedStats.geoDistribution.slice(0, 5)) {
        console.log(`- ${geo.country}: ${geo.count} Zugriffe (${geo.percentage})`);
      }
    }
    
    // 7. Download-Statistiken über Zeit
    console.log('\n=== DOWNLOAD-STATISTIKEN DER LETZTEN 30 TAGE ===');
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 30);
    
    const downloadStats = await statsService.getDownloadStats({
      startDate,
      groupBy: 'week'
    });
    
    for (const stat of downloadStats) {
      console.log(`Woche ${stat.period}: ${stat.totalDownloads} Downloads von ${stat.uniquePublications} Publikationen`);
    }
    
    // 8. Speicherstatistiken
    console.log('\n=== SPEICHERSTATISTIKEN ===');
    const storageStats = await statsService.getStorageStats();
    console.log(`Gesamtspeichernutzung: ${storageStats.totalFormatted}`);
    
    if (storageStats.protocols) {
      console.log('Speichernutzung nach Protokoll:');
      for (const [protocol, bytes] of Object.entries(storageStats.protocols)) {
        const byteStr = typeof bytes === 'number' ? 
                       `${(bytes / (1024 * 1024)).toFixed(2)} MB` : 
                       bytes;
        console.log(`- ${protocol}: ${byteStr}`);
      }
    }
    
    if (storageStats.fileTypes) {
      console.log('\nSpeichernutzung nach Dateityp:');
      for (const type of storageStats.fileTypes) {
        console.log(`- ${type.mimeType}: ${type.formattedSize} (${type.count} Dateien, ${type.percentageOfTotal})`);
      }
    }
    
  } catch (error) {
    console.error('Fehler im Beispiel:', error);
  } finally {
    console.log('\nBereinige Ressourcen...');
    
    // Schließe Dienste und bereinige
    try {
      if (databaseService) await databaseService.close();
      if (storageService) await storageService.shutdown();
      console.log('Dienste erfolgreich heruntergefahren.');
    } catch (err) {
      console.error('Fehler beim Herunterfahren der Dienste:', err);
    }
    
    console.log('Beispiel abgeschlossen.');
  }
}

// Führe das Beispiel aus
main().catch(err => console.error('Unbehandelter Fehler:', err)); 