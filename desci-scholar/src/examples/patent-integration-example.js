/**
 * @fileoverview Beispiel für die Verwendung des PatentIntegrationAgent
 * 
 * Dieses Beispiel zeigt, wie der PatentIntegrationAgent verwendet werden kann,
 * um Patentdaten zu integrieren und zu verarbeiten.
 */

import { PatentIntegrationAgent } from '../ai/agents/PatentIntegrationAgent.js';
import { DatabaseService } from '../services/DatabaseService.js';
import { BlockchainService } from '../services/BlockchainService.js';
import { IPFSService } from '../services/IPFSService.js';
import { logger } from '../utils/logger.js';

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  try {
    logger.info('Starte Patent-Integrations-Beispiel');
    
    // Dienste initialisieren
    const databaseService = new DatabaseService({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'desci_scholar'
    });
    
    await databaseService.connect();
    logger.info('Datenbankverbindung hergestellt');
    
    const blockchainService = new BlockchainService({
      provider: process.env.BLOCKCHAIN_PROVIDER || 'http://localhost:8545',
      privateKey: process.env.PRIVATE_KEY,
      contractAddresses: {
        patentNFT: process.env.PATENT_NFT_CONTRACT_ADDRESS
      }
    });
    
    await blockchainService.initialize();
    logger.info('Blockchain-Service initialisiert');
    
    const ipfsService = new IPFSService({
      gateway: process.env.IPFS_GATEWAY || 'http://localhost:5001',
      apiKey: process.env.IPFS_API_KEY
    });
    
    await ipfsService.initialize();
    logger.info('IPFS-Service initialisiert');
    
    // PatentIntegrationAgent erstellen
    const patentIntegrationAgent = new PatentIntegrationAgent({
      databaseService,
      blockchainService,
      ipfsService,
      patentApiConfig: {
        usptoApiKey: process.env.USPTO_API_KEY,
        epoApiKey: process.env.EPO_API_KEY,
        wipoApiKey: process.env.WIPO_API_KEY
      },
      config: {
        energyEfficient: true,
        batchSize: 10,
        maxRetries: 3
      }
    });
    
    // Agent initialisieren
    const initResult = await patentIntegrationAgent.initialize();
    
    if (!initResult) {
      logger.error('Fehler bei der Initialisierung des PatentIntegrationAgent');
      process.exit(1);
    }
    
    logger.info('PatentIntegrationAgent erfolgreich initialisiert');
    
    // Beispiel 1: Patentdaten aktualisieren
    logger.info('Beispiel 1: Patentdaten aktualisieren');
    
    const updateResult = await patentIntegrationAgent.updatePatentData();
    
    logger.info('Patentdaten aktualisiert', {
      success: updateResult.success,
      total: updateResult.results?.total || 0,
      uspto: updateResult.results?.uspto.count || 0,
      epo: updateResult.results?.epo.count || 0,
      wipo: updateResult.results?.wipo.count || 0
    });
    
    // Beispiel 2: Ein einzelnes Patent abrufen
    logger.info('Beispiel 2: Ein einzelnes Patent abrufen');
    
    // Wir nehmen an, dass wir mindestens ein Patent in der Datenbank haben
    const patents = await databaseService.query(
      'SELECT * FROM patents ORDER BY grant_date DESC LIMIT 1'
    );
    
    if (!patents || patents.length === 0) {
      logger.warn('Keine Patente in der Datenbank gefunden. Füge ein Beispielpatent hinzu.');
      
      // Füge ein Beispielpatent hinzu
      await databaseService.execute(
        `INSERT INTO patents (
          id, title, abstract, inventors, assignees, 
          filing_date, grant_date, patent_office, patent_type, status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          'US12345678',
          'Verfahren und Vorrichtung zur Energiegewinnung aus erneuerbaren Quellen',
          'Die Erfindung betrifft ein Verfahren und eine Vorrichtung zur effizienten Gewinnung von Energie aus erneuerbaren Quellen wie Sonne und Wind.',
          'Max Mustermann, Maria Musterfrau',
          'Muster GmbH',
          '2020-01-01',
          '2022-01-01',
          'USPTO',
          'utility',
          'granted'
        ]
      );
      
      logger.info('Beispielpatent hinzugefügt');
      
      // Patent erneut abrufen
      const newPatents = await databaseService.query(
        'SELECT * FROM patents ORDER BY grant_date DESC LIMIT 1'
      );
      
      if (!newPatents || newPatents.length === 0) {
        logger.error('Konnte kein Patent abrufen oder erstellen');
        process.exit(1);
      }
      
      patents[0] = newPatents[0];
    }
    
    const patent = patents[0];
    
    logger.info('Patent abgerufen', {
      id: patent.id,
      title: patent.title,
      patentOffice: patent.patent_office,
      grantDate: patent.grant_date
    });
    
    // Beispiel 3: NFT für ein Patent erstellen
    logger.info('Beispiel 3: NFT für ein Patent erstellen');
    
    const nftResult = await patentIntegrationAgent.createPatentNFT(patent.id);
    
    logger.info('NFT für Patent erstellt', {
      success: nftResult.success,
      patentId: patent.id,
      nftId: nftResult.success ? nftResult.nftId : null,
      message: nftResult.message || ''
    });
    
    // Beispiel 4: Patent mit einer wissenschaftlichen Publikation verknüpfen
    logger.info('Beispiel 4: Patent mit einer wissenschaftlichen Publikation verknüpfen');
    
    // Wir nehmen an, dass wir eine Publikation in der Datenbank haben
    const publications = await databaseService.query(
      'SELECT * FROM publications ORDER BY publication_date DESC LIMIT 1'
    );
    
    if (!publications || publications.length === 0) {
      logger.warn('Keine Publikationen in der Datenbank gefunden. Füge eine Beispielpublikation hinzu.');
      
      // Füge eine Beispielpublikation hinzu
      await databaseService.execute(
        `INSERT INTO publications (
          doi, title, authors, abstract, publication_date, journal, url
        ) VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [
          '10.1234/example-doi-2022',
          'Fortschritte in der erneuerbaren Energiegewinnung',
          'Max Mustermann, Maria Musterfrau',
          'Diese Publikation beschreibt neue Methoden zur effizienten Gewinnung von Energie aus erneuerbaren Quellen.',
          '2022-02-01',
          'Journal of Renewable Energy',
          'https://example.com/publication'
        ]
      );
      
      logger.info('Beispielpublikation hinzugefügt');
      
      // Publikation erneut abrufen
      const newPublications = await databaseService.query(
        'SELECT * FROM publications ORDER BY publication_date DESC LIMIT 1'
      );
      
      if (!newPublications || newPublications.length === 0) {
        logger.error('Konnte keine Publikation abrufen oder erstellen');
        process.exit(1);
      }
      
      publications[0] = newPublications[0];
    }
    
    const publication = publications[0];
    
    // Verknüpfe Patent mit Publikation
    await databaseService.execute(
      'INSERT INTO patent_publication_links (patent_id, publication_doi, link_type) VALUES (?, ?, ?)',
      [patent.id, publication.doi, 'implementation']
    );
    
    logger.info('Patent mit Publikation verknüpft', {
      patentId: patent.id,
      doi: publication.doi,
      linkType: 'implementation'
    });
    
    // Beispiel 5: Patentlizenz erstellen
    logger.info('Beispiel 5: Patentlizenz erstellen');
    
    // Wir nehmen an, dass wir einen Forscher in der Datenbank haben
    const researchers = await databaseService.query(
      'SELECT * FROM researchers LIMIT 1'
    );
    
    if (!researchers || researchers.length === 0) {
      logger.warn('Keine Forscher in der Datenbank gefunden. Füge einen Beispielforscher hinzu.');
      
      // Füge einen Beispielforscher hinzu
      await databaseService.execute(
        `INSERT INTO researchers (
          id, name, email, institution, orcid
        ) VALUES (?, ?, ?, ?, ?)`,
        [
          'researcher-123',
          'Dr. Anna Schmidt',
          '<EMAIL>',
          'Universität Beispiel',
          '0000-0001-2345-6789'
        ]
      );
      
      logger.info('Beispielforscher hinzugefügt');
      
      // Forscher erneut abrufen
      const newResearchers = await databaseService.query(
        'SELECT * FROM researchers LIMIT 1'
      );
      
      if (!newResearchers || newResearchers.length === 0) {
        logger.error('Konnte keinen Forscher abrufen oder erstellen');
        process.exit(1);
      }
      
      researchers[0] = newResearchers[0];
    }
    
    const researcher = researchers[0];
    
    // Erstelle eine Patentlizenz
    await databaseService.execute(
      `INSERT INTO patent_licenses (
        patent_id, licensee_id, license_type, start_date, end_date, royalty_rate, status
      ) VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [
        patent.id,
        researcher.id,
        'non-exclusive',
        new Date().toISOString().split('T')[0],
        new Date(new Date().setFullYear(new Date().getFullYear() + 5)).toISOString().split('T')[0],
        2.5,
        'active'
      ]
    );
    
    logger.info('Patentlizenz erstellt', {
      patentId: patent.id,
      licenseeId: researcher.id,
      licenseType: 'non-exclusive',
      royaltyRate: 2.5
    });
    
    logger.info('Patent-Integrations-Beispiel erfolgreich abgeschlossen');
    
    // Verbindungen schließen
    await databaseService.disconnect();
    await blockchainService.disconnect();
    await ipfsService.disconnect();
    
    process.exit(0);
  } catch (error) {
    logger.error('Fehler im Patent-Integrations-Beispiel', {
      error: error.message,
      stack: error.stack
    });
    
    process.exit(1);
  }
}

// Führe das Beispiel aus
main();