/**
 * @fileoverview Beispiel für die Verwendung des PatentCitationAnalyzer
 * 
 * Dieses <PERSON> zeigt, wie der PatentCitationAnalyzer verwendet werden kann, um
 * Zitationsbeziehungen zwischen Patenten und wissenschaftlichen Publikationen zu analysieren.
 */

import PatentCitationAnalyzer from '../ai/agents/PatentCitationAnalyzer.js';
import { DatabaseService } from '../database/DatabaseService.js';
import { TextAnalysisService } from '../ai/services/TextAnalysisService.js';
import { logger } from '../utils/logger.js';

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  try {
    logger.info('Starte PatentCitationAnalyzer-Beispiel');
    
    // Initialisiere Dienste
    const databaseService = new DatabaseService();
    await databaseService.initialize();
    
    const textAnalysisService = new TextAnalysisService({
      apiKey: process.env.TEXT_ANALYSIS_API_KEY
    });
    await textAnalysisService.initialize();
    
    // Erstelle und initialisiere den PatentCitationAnalyzer
    const patentCitationAnalyzer = new PatentCitationAnalyzer({
      databaseService,
      textAnalysisService,
      config: {
        maxKeywords: 15,
        minKeywordLength: 4,
        energyEfficient: true
      }
    });
    
    await patentCitationAnalyzer.initialize();
    
    // Suche nach einem Patent für die Analyse
    const patents = await databaseService.query(
      'SELECT id, title FROM patents ORDER BY grant_date DESC LIMIT 1'
    );
    
    if (!patents || patents.length === 0) {
      logger.error('Kein Patent für die Analyse gefunden');
      return;
    }
    
    const patentId = patents[0].id;
    logger.info(`Patent für die Analyse gefunden: ${patents[0].title} (ID: ${patentId})`);
    
    // Beispiel 1: Analysiere Patentzitationen
    logger.info('Beispiel 1: Analysiere Patentzitationen');
    
    const citationAnalysis = await patentCitationAnalyzer.analyzePatentCitations(patentId);
    
    logger.info('Patentzitationsanalyse abgeschlossen', {
      success: citationAnalysis.success,
      patentId,
      citationCount: citationAnalysis.success ? citationAnalysis.metrics.citationCount : 0,
      influenceScore: citationAnalysis.success ? citationAnalysis.metrics.influenceScore : 0
    });
    
    if (citationAnalysis.success && citationAnalysis.aiAnalysis) {
      logger.info('KI-Analyse der Patentzitationen', {
        impact: citationAnalysis.aiAnalysis.impact,
        trends: citationAnalysis.aiAnalysis.trends,
        recommendations: citationAnalysis.aiAnalysis.recommendations,
        keywords: citationAnalysis.aiAnalysis.keywords
      });
    }
    
    // Beispiel 2: Analysiere Patent-Publikations-Beziehungen
    logger.info('Beispiel 2: Analysiere Patent-Publikations-Beziehungen');
    
    const relationshipAnalysis = await patentCitationAnalyzer.analyzePatentPublicationRelationships(patentId);
    
    logger.info('Patent-Publikations-Beziehungsanalyse abgeschlossen', {
      success: relationshipAnalysis.success,
      patentId,
      linkedPublicationsCount: relationshipAnalysis.success ? relationshipAnalysis.linkedPublications.length : 0,
      similarPublicationsCount: relationshipAnalysis.success ? relationshipAnalysis.similarPublications.length : 0
    });
    
    if (relationshipAnalysis.success && relationshipAnalysis.analysis) {
      logger.info('Analyse der Patent-Publikations-Beziehungen', {
        summary: relationshipAnalysis.analysis.summary,
        recommendations: relationshipAnalysis.analysis.recommendations
      });
      
      // Zeige verknüpfte Publikationen
      if (relationshipAnalysis.linkedPublications && relationshipAnalysis.linkedPublications.length > 0) {
        logger.info('Verknüpfte Publikationen', {
          count: relationshipAnalysis.linkedPublications.length,
          publications: relationshipAnalysis.linkedPublications.map(pub => ({
            doi: pub.publication_doi,
            title: pub.title,
            linkType: pub.link_type
          }))
        });
      }
      
      // Zeige ähnliche Publikationen
      if (relationshipAnalysis.similarPublications && relationshipAnalysis.similarPublications.length > 0) {
        logger.info('Ähnliche Publikationen', {
          count: relationshipAnalysis.similarPublications.length,
          publications: relationshipAnalysis.similarPublications.map(pub => ({
            doi: pub.doi,
            title: pub.title
          }))
        });
      }
    }
    
    // Beispiel 3: Analysiere Technologietransfer
    logger.info('Beispiel 3: Analysiere Technologietransfer');
    
    const transferAnalysis = await patentCitationAnalyzer.analyzeTechnologyTransfer({
      fromDate: '2018-01-01',
      limit: 50
    });
    
    logger.info('Technologietransfer-Analyse abgeschlossen', {
      success: transferAnalysis.success,
      patentsCount: transferAnalysis.success ? transferAnalysis.patentsCount : 0
    });
    
    if (transferAnalysis.success) {
      // Zeige Zeitabstandsanalyse
      if (transferAnalysis.timeGapAnalysis) {
        logger.info('Zeitabstandsanalyse zwischen Publikationen und Patenten', {
          averageGapInDays: transferAnalysis.timeGapAnalysis.averageGapInDays,
          medianGapInDays: transferAnalysis.timeGapAnalysis.medianGapInDays,
          directionCounts: transferAnalysis.timeGapAnalysis.directionCounts,
          gapDistribution: transferAnalysis.timeGapAnalysis.gapDistribution
        });
      }
      
      // Zeige Top-Forschungsbereiche
      if (transferAnalysis.researchFieldsAnalysis && transferAnalysis.researchFieldsAnalysis.topFields) {
        logger.info('Top-Forschungsbereiche mit hohem Technologietransfer', {
          fields: transferAnalysis.researchFieldsAnalysis.topFields.map(field => ({
            name: field.field,
            linkedPatentsCount: field.linkedPatentsCount,
            topJournals: field.topJournals.map(j => j.journal)
          }))
        });
      }
      
      // Zeige Top-Institutionen
      if (transferAnalysis.institutionsAnalysis && transferAnalysis.institutionsAnalysis.topInstitutions) {
        logger.info('Top-Institutionen mit hohem Technologietransfer', {
          institutions: transferAnalysis.institutionsAnalysis.topInstitutions.map(inst => ({
            name: inst.institution,
            transferScore: inst.transferScore,
            patentCount: inst.patentCount,
            publicationCount: inst.publicationCount
          }))
        });
      }
      
      // Zeige Top-Patente
      if (transferAnalysis.topPatents) {
        logger.info('Top-Patente mit hohem Technologietransfer', {
          patents: transferAnalysis.topPatents.map(patent => ({
            id: patent.id,
            title: patent.title,
            linkedPublicationsCount: patent.linkedPublicationsCount
          }))
        });
      }
    }
    
    logger.info('PatentCitationAnalyzer-Beispiel abgeschlossen');
  } catch (error) {
    logger.error('Fehler im PatentCitationAnalyzer-Beispiel', {
      error: error.message,
      stack: error.stack
    });
  }
}

// Führe das Beispiel aus
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});/**
 * @fileoverview Beispiel für die Verwendung des PatentCitationAnalyzer
 * 
 * Dieses Beispiel zeigt, wie der PatentCitationAnalyzer verwendet werden kann, um
 * Zitationsbeziehungen zwischen Patenten und wissenschaftlichen Publikationen zu analysieren.
 */

import PatentCitationAnalyzer from '../ai/agents/PatentCitationAnalyzer.js';
import { DatabaseService } from '../database/DatabaseService.js';
import { TextAnalysisService } from '../ai/services/TextAnalysisService.js';
import { logger } from '../utils/logger.js';

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  try {
    logger.info('Starte PatentCitationAnalyzer-Beispiel');
    
    // Initialisiere Dienste
    const databaseService = new DatabaseService();
    await databaseService.initialize();
    
    const textAnalysisService = new TextAnalysisService({
      apiKey: process.env.TEXT_ANALYSIS_API_KEY
    });
    await textAnalysisService.initialize();
    
    // Erstelle und initialisiere den PatentCitationAnalyzer
    const patentCitationAnalyzer = new PatentCitationAnalyzer({
      databaseService,
      textAnalysisService,
      config: {
        maxKeywords: 15,
        minKeywordLength: 4,
        energyEfficient: true
      }
    });
    
    await patentCitationAnalyzer.initialize();
    
    // Suche nach einem Patent für die Analyse
    const patents = await databaseService.query(
      'SELECT id, title FROM patents ORDER BY grant_date DESC LIMIT 1'
    );
    
    if (!patents || patents.length === 0) {
      logger.error('Kein Patent für die Analyse gefunden');
      return;
    }
    
    const patentId = patents[0].id;
    logger.info(`Patent für die Analyse gefunden: ${patents[0].title} (ID: ${patentId})`);
    
    // Beispiel 1: Analysiere Patentzitationen
    logger.info('Beispiel 1: Analysiere Patentzitationen');
    
    const citationAnalysis = await patentCitationAnalyzer.analyzePatentCitations(patentId);
    
    logger.info('Patentzitationsanalyse abgeschlossen', {
      success: citationAnalysis.success,
      patentId,
      citationCount: citationAnalysis.success ? citationAnalysis.metrics.citationCount : 0,
      influenceScore: citationAnalysis.success ? citationAnalysis.metrics.influenceScore : 0
    });
    
    if (citationAnalysis.success && citationAnalysis.aiAnalysis) {
      logger.info('KI-Analyse der Patentzitationen', {
        impact: citationAnalysis.aiAnalysis.impact,
        trends: citationAnalysis.aiAnalysis.trends,
        recommendations: citationAnalysis.aiAnalysis.recommendations,
        keywords: citationAnalysis.aiAnalysis.keywords
      });
    }
    
    // Beispiel 2: Analysiere Patent-Publikations-Beziehungen
    logger.info('Beispiel 2: Analysiere Patent-Publikations-Beziehungen');
    
    const relationshipAnalysis = await patentCitationAnalyzer.analyzePatentPublicationRelationships(patentId);
    
    logger.info('Patent-Publikations-Beziehungsanalyse abgeschlossen', {
      success: relationshipAnalysis.success,
      patentId,
      linkedPublicationsCount: relationshipAnalysis.success ? relationshipAnalysis.linkedPublications.length : 0,
      similarPublicationsCount: relationshipAnalysis.success ? relationshipAnalysis.similarPublications.length : 0
    });
    
    if (relationshipAnalysis.success && relationshipAnalysis.analysis) {
      logger.info('Analyse der Patent-Publikations-Beziehungen', {
        summary: relationshipAnalysis.analysis.summary,
        recommendations: relationshipAnalysis.analysis.recommendations
      });
      
      // Zeige verknüpfte Publikationen
      if (relationshipAnalysis.linkedPublications && relationshipAnalysis.linkedPublications.length > 0) {
        logger.info('Verknüpfte Publikationen', {
          count: relationshipAnalysis.linkedPublications.length,
          publications: relationshipAnalysis.linkedPublications.map(pub => ({
            doi: pub.publication_doi,
            title: pub.title,
            linkType: pub.link_type
          }))
        });
      }
      
      // Zeige ähnliche Publikationen
      if (relationshipAnalysis.similarPublications && relationshipAnalysis.similarPublications.length > 0) {
        logger.info('Ähnliche Publikationen', {
          count: relationshipAnalysis.similarPublications.length,
          publications: relationshipAnalysis.similarPublications.map(pub => ({
            doi: pub.doi,
            title: pub.title
          }))
        });
      }
    }
    
    // Beispiel 3: Analysiere Technologietransfer
    logger.info('Beispiel 3: Analysiere Technologietransfer');
    
    const transferAnalysis = await patentCitationAnalyzer.analyzeTechnologyTransfer({
      fromDate: '2018-01-01',
      limit: 50
    });
    
    logger.info('Technologietransfer-Analyse abgeschlossen', {
      success: transferAnalysis.success,
      patentsCount: transferAnalysis.success ? transferAnalysis.patentsCount : 0
    });
    
    if (transferAnalysis.success) {
      // Zeige Zeitabstandsanalyse
      if (transferAnalysis.timeGapAnalysis) {
        logger.info('Zeitabstandsanalyse zwischen Publikationen und Patenten', {
          averageGapInDays: transferAnalysis.timeGapAnalysis.averageGapInDays,
          medianGapInDays: transferAnalysis.timeGapAnalysis.medianGapInDays,
          directionCounts: transferAnalysis.timeGapAnalysis.directionCounts,
          gapDistribution: transferAnalysis.timeGapAnalysis.gapDistribution
        });
      }
      
      // Zeige Top-Forschungsbereiche
      if (transferAnalysis.researchFieldsAnalysis && transferAnalysis.researchFieldsAnalysis.topFields) {
        logger.info('Top-Forschungsbereiche mit hohem Technologietransfer', {
          fields: transferAnalysis.researchFieldsAnalysis.topFields.map(field => ({
            name: field.field,
            linkedPatentsCount: field.linkedPatentsCount,
            topJournals: field.topJournals.map(j => j.journal)
          }))
        });
      }
      
      // Zeige Top-Institutionen
      if (transferAnalysis.institutionsAnalysis && transferAnalysis.institutionsAnalysis.topInstitutions) {
        logger.info('Top-Institutionen mit hohem Technologietransfer', {
          institutions: transferAnalysis.institutionsAnalysis.topInstitutions.map(inst => ({
            name: inst.institution,
            transferScore: inst.transferScore,
            patentCount: inst.patentCount,
            publicationCount: inst.publicationCount
          }))
        });
      }
      
      // Zeige Top-Patente
      if (transferAnalysis.topPatents) {
        logger.info('Top-Patente mit hohem Technologietransfer', {
          patents: transferAnalysis.topPatents.map(patent => ({
            id: patent.id,
            title: patent.title,
            linkedPublicationsCount: patent.linkedPublicationsCount
          }))
        });
      }
    }
    
    logger.info('PatentCitationAnalyzer-Beispiel abgeschlossen');
  } catch (error) {
    logger.error('Fehler im PatentCitationAnalyzer-Beispiel', {
      error: error.message,
      stack: error.stack
    });
  }
}

// Führe das Beispiel aus
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});/**
 * @fileoverview Beispiel für die Verwendung des PatentCitationAnalyzer
 * 
 * Dieses Beispiel zeigt, wie der PatentCitationAnalyzer verwendet werden kann, um
 * Zitationsbeziehungen zwischen Patenten und wissenschaftlichen Publikationen zu analysieren.
 */

import PatentCitationAnalyzer from '../ai/agents/PatentCitationAnalyzer.js';
import { DatabaseService } from '../database/DatabaseService.js';
import { TextAnalysisService } from '../ai/services/TextAnalysisService.js';
import { logger } from '../utils/logger.js';

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  try {
    logger.info('Starte PatentCitationAnalyzer-Beispiel');
    
    // Initialisiere Dienste
    const databaseService = new DatabaseService();
    await databaseService.initialize();
    
    const textAnalysisService = new TextAnalysisService({
      apiKey: process.env.TEXT_ANALYSIS_API_KEY
    });
    await textAnalysisService.initialize();
    
    // Erstelle und initialisiere den PatentCitationAnalyzer
    const patentCitationAnalyzer = new PatentCitationAnalyzer({
      databaseService,
      textAnalysisService,
      config: {
        maxKeywords: 15,
        minKeywordLength: 4,
        energyEfficient: true
      }
    });
    
    await patentCitationAnalyzer.initialize();
    
    // Suche nach einem Patent für die Analyse
    const patents = await databaseService.query(
      'SELECT id, title FROM patents ORDER BY grant_date DESC LIMIT 1'
    );
    
    if (!patents || patents.length === 0) {
      logger.error('Kein Patent für die Analyse gefunden');
      return;
    }
    
    const patentId = patents[0].id;
    logger.info(`Patent für die Analyse gefunden: ${patents[0].title} (ID: ${patentId})`);
    
    // Beispiel 1: Analysiere Patentzitationen
    logger.info('Beispiel 1: Analysiere Patentzitationen');
    
    const citationAnalysis = await patentCitationAnalyzer.analyzePatentCitations(patentId);
    
    logger.info('Patentzitationsanalyse abgeschlossen', {
      success: citationAnalysis.success,
      patentId,
      citationCount: citationAnalysis.success ? citationAnalysis.metrics.citationCount : 0,
      influenceScore: citationAnalysis.success ? citationAnalysis.metrics.influenceScore : 0
    });
    
    if (citationAnalysis.success && citationAnalysis.aiAnalysis) {
      logger.info('KI-Analyse der Patentzitationen', {
        impact: citationAnalysis.aiAnalysis.impact,
        trends: citationAnalysis.aiAnalysis.trends,
        recommendations: citationAnalysis.aiAnalysis.recommendations,
        keywords: citationAnalysis.aiAnalysis.keywords
      });
    }
    
    // Beispiel 2: Analysiere Patent-Publikations-Beziehungen
    logger.info('Beispiel 2: Analysiere Patent-Publikations-Beziehungen');
    
    const relationshipAnalysis = await patentCitationAnalyzer.analyzePatentPublicationRelationships(patentId);
    
    logger.info('Patent-Publikations-Beziehungsanalyse abgeschlossen', {
      success: relationshipAnalysis.success,
      patentId,
      linkedPublicationsCount: relationshipAnalysis.success ? relationshipAnalysis.linkedPublications.length : 0,
      similarPublicationsCount: relationshipAnalysis.success ? relationshipAnalysis.similarPublications.length : 0
    });
    
    if (relationshipAnalysis.success && relationshipAnalysis.analysis) {
      logger.info('Analyse der Patent-Publikations-Beziehungen', {
        summary: relationshipAnalysis.analysis.summary,
        recommendations: relationshipAnalysis.analysis.recommendations
      });
      
      // Zeige verknüpfte Publikationen
      if (relationshipAnalysis.linkedPublications && relationshipAnalysis.linkedPublications.length > 0) {
        logger.info('Verknüpfte Publikationen', {
          count: relationshipAnalysis.linkedPublications.length,
          publications: relationshipAnalysis.linkedPublications.map(pub => ({
            doi: pub.publication_doi,
            title: pub.title,
            linkType: pub.link_type
          }))
        });
      }
      
      // Zeige ähnliche Publikationen
      if (relationshipAnalysis.similarPublications && relationshipAnalysis.similarPublications.length > 0) {
        logger.info('Ähnliche Publikationen', {
          count: relationshipAnalysis.similarPublications.length,
          publications: relationshipAnalysis.similarPublications.map(pub => ({
            doi: pub.doi,
            title: pub.title
          }))
        });
      }
    }
    
    // Beispiel 3: Analysiere Technologietransfer
    logger.info('Beispiel 3: Analysiere Technologietransfer');
    
    const transferAnalysis = await patentCitationAnalyzer.analyzeTechnologyTransfer({
      fromDate: '2018-01-01',
      limit: 50
    });
    
    logger.info('Technologietransfer-Analyse abgeschlossen', {
      success: transferAnalysis.success,
      patentsCount: transferAnalysis.success ? transferAnalysis.patentsCount : 0
    });
    
    if (transferAnalysis.success) {
      // Zeige Zeitabstandsanalyse
      if (transferAnalysis.timeGapAnalysis) {
        logger.info('Zeitabstandsanalyse zwischen Publikationen und Patenten', {
          averageGapInDays: transferAnalysis.timeGapAnalysis.averageGapInDays,
          medianGapInDays: transferAnalysis.timeGapAnalysis.medianGapInDays,
          directionCounts: transferAnalysis.timeGapAnalysis.directionCounts,
          gapDistribution: transferAnalysis.timeGapAnalysis.gapDistribution
        });
      }
      
      // Zeige Top-Forschungsbereiche
      if (transferAnalysis.researchFieldsAnalysis && transferAnalysis.researchFieldsAnalysis.topFields) {
        logger.info('Top-Forschungsbereiche mit hohem Technologietransfer', {
          fields: transferAnalysis.researchFieldsAnalysis.topFields.map(field => ({
            name: field.field,
            linkedPatentsCount: field.linkedPatentsCount,
            topJournals: field.topJournals.map(j => j.journal)
          }))
        });
      }
      
      // Zeige Top-Institutionen
      if (transferAnalysis.institutionsAnalysis && transferAnalysis.institutionsAnalysis.topInstitutions) {
        logger.info('Top-Institutionen mit hohem Technologietransfer', {
          institutions: transferAnalysis.institutionsAnalysis.topInstitutions.map(inst => ({
            name: inst.institution,
            transferScore: inst.transferScore,
            patentCount: inst.patentCount,
            publicationCount: inst.publicationCount
          }))
        });
      }
      
      // Zeige Top-Patente
      if (transferAnalysis.topPatents) {
        logger.info('Top-Patente mit hohem Technologietransfer', {
          patents: transferAnalysis.topPatents.map(patent => ({
            id: patent.id,
            title: patent.title,
            linkedPublicationsCount: patent.linkedPublicationsCount
          }))
        });
      }
    }
    
    logger.info('PatentCitationAnalyzer-Beispiel abgeschlossen');
  } catch (error) {
    logger.error('Fehler im PatentCitationAnalyzer-Beispiel', {
      error: error.message,
      stack: error.stack
    });
  }
}

// Führe das Beispiel aus
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});