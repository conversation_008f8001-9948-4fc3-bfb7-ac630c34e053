/**
 * Beispiel für die Verwendung des PublicationController mit Datenbankintegration
 * Demonstriert die API-Funktionen für Publikationsverwaltung mit dezentraler Speicherung
 * und MongoDB-Integration
 */

import PublicationController from '../controllers/PublicationController.js';
import { toBuffer } from '../utils/buffer.js';
import fs from 'fs';
import path from 'path';
import crypto from 'crypto';

/**
 * Hauptfunktion für das Controller-Beispiel mit Datenbankintegration
 */
async function controllerDatabaseExample() {
  // console.log('DeSci-Scholar Controller mit Datenbankintegration - Beispiel');
  // console.log('--------------------------------------------------------');

  // Erstelle Publikationscontroller mit Testmodus für DataCite
  const controller = new PublicationController({
    dataCite: { testMode: true },
    database: {
      uri: process.env.MONGO_URI || 'mongodb://localhost:27017',
      dbName: process.env.MONGO_DB_NAME || 'desci-scholar-test'
    }
  });

  try {
    // Initialisiere Controller
    // console.log('Initialisiere Controller...');
    await controller.initialize();
    // console.log('Controller initialisiert');

    // 1. Erstelle eine neue Publikation
    // console.log('\n1. Erstelle eine neue Publikation...');
    const createResult = await controller.createPublication({
      title: 'Dezentrale Speicherlösungen für wissenschaftliche Publikationen',
      authors: ['Maria Schmidt', 'Thomas Weber', 'Sarah Müller'],
      abstract: 'Diese Studie untersucht dezentrale Speicherlösungen für wissenschaftliche Publikationen und deren Vorteile für die wissenschaftliche Gemeinschaft.',
      content: 'Einführung\n\nDie Veröffentlichung und der Austausch wissenschaftlicher Erkenntnisse ist für den Fortschritt der Forschung unerlässlich. Traditionelle zentralisierte Veröffentlichungsmodelle haben jedoch Nachteile wie hohe Kosten, eingeschränkten Zugang und Zensurrisiken. Diese Studie untersucht dezentrale Speicherlösungen als Alternative.\n\nMethoden\n\nWir haben IPFS und BitTorrent als dezentrale Speicherlösungen implementiert und getestet. Die Leistung wurde anhand von Verfügbarkeit, Latenz und Redundanz gemessen.\n\nErgebnisse\n\nUnsere Ergebnisse zeigen, dass dezentrale Speicherlösungen eine höhere Verfügbarkeit und bessere Langzeitarchivierung bieten als zentralisierte Lösungen. Die Kombination von IPFS und BitTorrent bietet optimale Redundanz und Verfügbarkeit.\n\nFazit\n\nDezentrale Speicherlösungen bieten eine vielversprechende Alternative für die Veröffentlichung wissenschaftlicher Arbeiten, die Kosten senken und den offenen Zugang verbessern kann.',
      keywords: ['dezentrale Speicherung', 'IPFS', 'BitTorrent', 'wissenschaftliche Publikationen', 'Open Access'],
      license: 'CC-BY-4.0',
      status: 'draft'
    });

    if (!createResult.success) {
      throw new Error(`Fehler beim Erstellen der Publikation: ${createResult.message}`);
    }

    // console.log(`Publikation erstellt: ID ${createResult.id}`);
    const publicationId = createResult.id;

    // 2. Füge eine PDF-Datei hinzu
    // console.log('\n2. Füge Datei zur Publikation hinzu...');

    // Erstelle eine simulierte PDF-Datei mit Testinhalt
    const pdfContent = Buffer.from(`%PDF-1.5
1 0 obj
<< /Type /Catalog /Pages 2 0 R >>
endobj
2 0 obj
<< /Type /Pages /Kids [3 0 R] /Count 1 >>
endobj
3 0 obj
<< /Type /Page /Parent 2 0 R /Resources 4 0 R /MediaBox [0 0 595 842] /Contents 6 0 R >>
endobj
4 0 obj
<< /Font << /F1 5 0 R >> >>
endobj
5 0 obj
<< /Type /Font /Subtype /Type1 /BaseFont /Helvetica >>
endobj
6 0 obj
<< /Length 68 >>
stream
BT
/F1 12 Tf
100 700 Td
(Dezentrale Speicherlösungen für wissenschaftliche Publikationen) Tj
ET
endstream
endobj
xref
0 7
********** 65535 f
0000000009 00000 n
0000000058 00000 n
0000000115 00000 n
0000000210 00000 n
0000000251 00000 n
0000000318 00000 n
trailer
<< /Size 7 /Root 1 0 R >>
startxref
437
%%EOF`);

    const fileUploadResult = await controller.uploadPublicationFile(
      publicationId,
      pdfContent,
      {
        name: 'dezentrale-speicherlösungen.pdf',
        type: 'application/pdf',
        size: pdfContent.length
      }
    );

    if (!fileUploadResult.success) {
      throw new Error(`Fehler beim Hochladen der Datei: ${fileUploadResult.message}`);
    }

    // console.log('Datei hochgeladen:', fileUploadResult.file.name);
    // console.log('Verfügbar unter:', fileUploadResult.urls);

    // 3. Publikation veröffentlichen und DOI registrieren
    // console.log('\n3. Publikation veröffentlichen mit DOI-Registrierung...');
    const publishResult = await controller.publishPublication(publicationId, true);

    if (!publishResult.success) {
      throw new Error(`Fehler beim Veröffentlichen: ${publishResult.message}`);
    }

    console.log(`Publikation veröffentlicht mit DOI: ${publishResult.doi}`);
    console.log(`DOI-Registrierung: ${publishResult.doiRegistered ? 'Erfolgreich' : 'Fehlgeschlagen'}`);
    if (publishResult.doiMessage) {
      console.log(`DOI-Nachricht: ${publishResult.doiMessage}`);
    }

    // 4. Publikationsdaten abrufen
    console.log('\n4. Publikationsdetails abrufen...');
    const getResult = await controller.getPublication(publicationId);

    if (!getResult.success) {
      throw new Error(`Fehler beim Abrufen der Publikation: ${getResult.message}`);
    }

    console.log('Publikationsdetails:');
    console.log(`- Titel: ${getResult.publication.title}`);
    console.log(`- Autoren: ${getResult.publication.authors.join(', ')}`);
    console.log(`- Status: ${getResult.publication.status}`);
    console.log(`- DOI: ${getResult.publication.doi}`);
    console.log(`- Version: ${getResult.publication.version}`);
    console.log(`- Aus Cache geladen: ${getResult.fromCache ? 'Ja' : 'Nein'}`);
    console.log(`- Aus Datenbank geladen: ${getResult.fromDb ? 'Ja' : 'Nein'}`);

    // 5. Statistiken aktualisieren und abrufen
    console.log('\n5. Statistiken aktualisieren und abrufen...');

    // Aktualisiere verschiedene Statistiktypen
    await controller.updateStatistics(publicationId, 'view');
    await controller.updateStatistics(publicationId, 'view');
    await controller.updateStatistics(publicationId, 'view');
    await controller.updateStatistics(publicationId, 'download');
    await controller.updateStatistics(publicationId, 'cite');

    // Rufe Statistiken ab
    const statsResult = await controller.getStatistics(publicationId);

    if (!statsResult.success) {
      throw new Error(`Fehler beim Abrufen der Statistiken: ${statsResult.message}`);
    }

    console.log('Publikationsstatistiken:');
    console.log(`- Ansichten: ${statsResult.statistics.viewCount || 0}`);
    console.log(`- Downloads: ${statsResult.statistics.downloadCount || 0}`);
    console.log(`- Zitationen: ${statsResult.statistics.citeCount || 0}`);

    // 6. Publiktation aktualisieren um eine neue Version zu erstellen
    console.log('\n6. Neue Version der Publikation erstellen...');
    const updateResult = await controller.updatePublication(
      publicationId,
      {
        abstract: 'Diese überarbeitete Studie untersucht und vergleicht verschiedene dezentrale Speicherlösungen für wissenschaftliche Publikationen und deren Vorteile für die wissenschaftliche Gemeinschaft.',
        keywords: ['dezentrale Speicherung', 'IPFS', 'BitTorrent', 'wissenschaftliche Publikationen', 'Open Access', 'DeSci']
      },
      true // Neue Version erstellen
    );

    if (!updateResult.success) {
      throw new Error(`Fehler beim Aktualisieren der Publikation: ${updateResult.message}`);
    }

    console.log(`Neue Version (${updateResult.version}) der Publikation erstellt`);

    // 7. Nach Publikationen suchen
    console.log('\n7. Nach Publikationen suchen...');
    const searchResult = await controller.searchPublications(
      {
        keywords: ['dezentrale Speicherung', 'DeSci'],
        authors: ['Maria Schmidt']
      },
      {
        limit: 10,
        offset: 0
      }
    );

    if (!searchResult.success) {
      throw new Error(`Fehler bei der Suche: ${searchResult.message}`);
    }

    console.log(`${searchResult.results.length} von ${searchResult.total} Ergebnissen gefunden:`);
    searchResult.results.forEach((pub, idx) => {
      console.log(`\nErgebnis ${idx + 1}:`);
      console.log(`- ID: ${pub.id}`);
      console.log(`- Titel: ${pub.title}`);
      console.log(`- Autoren: ${pub.authors.join(', ')}`);
      console.log(`- Status: ${pub.status}`);
      console.log(`- Version: ${pub.version}`);
    });

    // 8. Datei herunterladen
    console.log('\n8. Publikationsdatei herunterladen...');
    const downloadResult = await controller.downloadPublicationFile(publicationId);

    if (!downloadResult.success) {
      throw new Error(`Fehler beim Herunterladen: ${downloadResult.message}`);
    }

    console.log(`Datei '${downloadResult.filename}' heruntergeladen (${downloadResult.fileSize} Bytes)`);

    // Berechne MD5-Hash der heruntergeladenen Datei zur Verifikation
    const md5sum = crypto.createHash('md5').update(downloadResult.data).digest('hex');
    console.log(`Datei-MD5: ${md5sum}`);

    // Optional: Speichere Datei lokal
    const downloadPath = path.join(__dirname, 'downloads');
    if (!fs.existsSync(downloadPath)) {
      fs.mkdirSync(downloadPath, { recursive: true });
    }

    const filePath = path.join(downloadPath, downloadResult.filename);
    fs.writeFileSync(filePath, downloadResult.data);
    console.log(`Datei lokal gespeichert unter: ${filePath}`);

    // 9. Zitationsdaten abrufen
    console.log('\n9. Zitationsdaten abrufen...');
    const citationsResult = await controller.getCitations(publicationId);

    if (!citationsResult.success) {
      throw new Error(`Fehler beim Abrufen der Zitationen: ${citationsResult.message}`);
    }

    console.log(`Diese Publikation wird von ${citationsResult.citedBy.length} anderen Publikationen zitiert`);
    console.log(`Diese Publikation zitiert ${citationsResult.cites.length} andere Publikationen`);

    console.log('\nBeispiel erfolgreich abgeschlossen!');

  } catch (error) {
    console.error('Fehler im Beispiel:', error);
  } finally {
    // Controller ordnungsgemäß herunterfahren
    console.log('\nFahre Controller herunter...');
    await controller.shutdown();
    console.log('Controller heruntergefahren');
  }
}

// Führe das Beispiel aus, wenn die Datei direkt ausgeführt wird
// In ESM wird dies anders gehandhabt als in CommonJS
const isMainModule = import.meta.url === `file://${process.argv[1]}`;
if (isMainModule) {
  controllerDatabaseExample().catch(error => {
    console.error('Unbehandelter Fehler:', error);
    process.exit(1);
  });
}

export default controllerDatabaseExample;
