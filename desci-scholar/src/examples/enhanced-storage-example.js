/**
 * <PERSON><PERSON>ci Scholar - Beispiel für optimierte dezentrale Speicherung
 * 
 * Dieses Beispiel demonstriert die Verwendung des EnhancedStorageService,
 * der IPFS für Metadaten und BitTorrent für große Dateien optimiert.
 */

import EnhancedStorageService from '../storage/EnhancedStorageService.js';
import fs from 'fs';
import path from 'path';
import crypto from 'crypto';
import { fileURLToPath } from 'url';

// ESM dirname-Äquivalent
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Hilfsfunktion zum Erstellen eines Beispiel-Datensatzes
const createSampleDataset = (size = 5 * 1024 * 1024) => { // 5MB Standardgröße
  const buffer = Buffer.alloc(size);
  // Füllt den Buffer mit Pseudozufallszahlen für realistischere Daten
  crypto.randomFillSync(buffer);
  return buffer;
};

// Beispiel-Metadaten für eine wissenschaftliche Publikation
const sampleMetadata = {
  title: 'Optimierte dezentrale Speicherarchitektur für wissenschaftliche Datensätze',
  authors: [
    { name: 'Dr. <PERSON>', affiliation: 'Universität Heidelberg', orcid: '0000-0002-1234-5678' },
    { name: 'Prof. Johan Müller', affiliation: 'TU München', orcid: '0000-0001-8765-4321' }
  ],
  abstract: 'Diese Studie untersucht die Effizienz verschiedener dezentraler Speicherprotokolle für große wissenschaftliche Datensätze. Die Ergebnisse zeigen, dass eine hybride Architektur, die IPFS für Metadaten und BitTorrent für große Dateien verwendet, optimale Leistung bietet.',
  keywords: ['dezentrale Speicherung', 'IPFS', 'BitTorrent', 'wissenschaftliche Daten', 'hybride Architektur'],
  doi: '10.5281/zenodo.1234567',
  license: 'CC-BY-4.0',
  date: new Date().toISOString()
};

// Beispielinhalt (kleinere Textdatei, die direkt über IPFS gespeichert werden könnte)
const sampleContent = `# Einführung

Dezentrale Speicherlösungen bieten Wissenschaftlern zahlreiche Vorteile:
- Zensurresistenz
- Erhöhte Verfügbarkeit durch redundante Speicherung
- Bessere Langzeitarchivierung
- Verifizierbarkeit der Datenintegrität

Unsere Studie zeigt, dass eine protokollspezifische Optimierung weitere Vorteile bietet...`;

// Hauptfunktion
async function main() {
  // console.log("DeSci Scholar - Beispiel für optimierte dezentrale Speicherung");
  // console.log("===========================================================");
  
  try {
    // Storage Service initialisieren
    // console.log("\n[1] Initialisiere EnhancedStorageService...");
    const storage = new EnhancedStorageService({
      ipfs: {
        gateway: 'https://ipfs.io',
        apiUrl: 'http://localhost:5001/api/v0',
        enablePinning: true
      },
      bittorrent: {
        downloadPath: path.join(__dirname, '../temp/downloads'),
        trackers: [
          'udp://tracker.opentrackr.org:1337/announce',
          'udp://tracker.openbittorrent.com:6969/announce'
        ],
        dht: true
      },
      metadata: {
        compressMetadata: true,
        automaticPinning: true
      },
      chunkLargeFiles: true
    });
    
    await storage.initialize();
    // console.log("✓ EnhancedStorageService erfolgreich initialisiert");
    
    // Beispieldatei erstellen (5MB für einen kleinen Forschungsdatensatz)
    // console.log("\n[2] Erstelle Beispiel-Forschungsdatensatz (5MB)...");
    const sampleDataset = createSampleDataset();
    // console.log(`✓ Beispieldatensatz erstellt: ${(sampleDataset.length / 1024 / 1024).toFixed(2)}MB`);

    // Als Demonstrationszweck einen kleinen Datensatz in eine temporäre Datei schreiben
    const datasetPath = path.join(__dirname, '../temp/sample-dataset.bin');
    const tempDir = path.dirname(datasetPath);
    
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }
    
    fs.writeFileSync(datasetPath, sampleDataset);
    // console.log(`✓ Datensatz zur Demonstration gespeichert: ${datasetPath}`);
    
    // Wissenschaftliche Daten mit optimierter Protokollauswahl speichern
    // console.log("\n[3] Speichere wissenschaftliche Daten mit optimaler Protokollauswahl...");
    const result = await storage.storeScientificData({
      metadata: sampleMetadata,
      content: sampleContent,
      file: sampleDataset,
      fileInfo: {
        name: 'forschungsdatensatz.bin',
        type: 'application/octet-stream',
        description: 'Beispiel für einen Forschungsdatensatz' 
      }
    });
    
    // console.log("✓ Wissenschaftliche Daten erfolgreich gespeichert");
    // console.log("\nSpeicherdetails:");
    // console.log(`- Datensatz-ID: ${result.id}`);
    // console.log(`- Metadaten (IPFS CID): ${result.storage.metadata.cid}`);
    // console.log(`- Inhalt (IPFS CID): ${result.storage.content.cid}`);
    // console.log(`- Datensatz (BitTorrent InfoHash): ${result.storage.file.infoHash}`);
    // console.log(`- Magnet-Link: ${result.storage.file.magnetLink}`);
    
    // Metadaten-Details anzeigen
    // console.log("\n[4] Metadatendetails aus dem Storage Registry...");
    const storageInfo = storage.getStorageInfo(result.id);
    // console.log(`- Protokoll für Metadaten: ${storageInfo.metadata.protocol}`);
    // console.log(`- Protokoll für Inhalt: ${storageInfo.content.protocol}`);
    // console.log(`- Protokoll für Datei: ${storageInfo.file.protocol}`);
    // console.log(`- Zeitstempel: ${new Date(storageInfo.timestamp).toLocaleString()}`);
    
    // Metadaten abrufen
    // console.log("\n[5] Abrufen der gespeicherten Metadaten aus IPFS...");
    const retrievedData = await storage.retrieveScientificData(result.id, { includeMetadata: true });
    // console.log("✓ Metadaten erfolgreich abgerufen");
    // console.log(`- Titel: ${retrievedData.metadata.title}`);
    // console.log(`- Autoren: ${retrievedData.metadata.authors.map(a => a.name).join(', ')}`);
    // console.log(`- DOI: ${retrievedData.metadata.doi}`);
    
    // Verteilungsstatistiken abrufen (in einer realen Umgebung würde dies Peers zeigen)
    // console.log("\n[6] Abrufen der Verteilungsstatistiken...");
    const stats = await storage.getDistributionStatistics(result.id);
    // console.log("Statistiken für Dateiverteilung:");
    // console.log(`- Aktive Peers: ${stats.peers || 0}`);
    // console.log(`- Upload (gesamt): ${stats.uploaded || 0} Bytes`);
    // console.log(`- Download (gesamt): ${stats.downloaded || 0} Bytes`);
    // console.log(`- Verfügbarkeitswert: ${stats.availability || 0}`);

    // Dateien, die aktiv verteilt werden, auflisten
    // console.log("\n[7] Liste der aktiv verteilten Dateien:");
    const distributedFiles = await storage.listDistributedFiles();
    // console.log(`Anzahl der aktiv verteilten Dateien: ${distributedFiles.length}`);
    distributedFiles.forEach((file, index) => {
      // console.log(`\n[Datei ${index + 1}]`);
      // console.log(`- Name: ${file.name}`);
      // console.log(`- InfoHash: ${file.infoHash}`);
      // console.log(`- Status: ${file.status}`);
      // console.log(`- Größe: ${(file.size / 1024 / 1024).toFixed(2)} MB`);
    });
    
    // Aufräumen und Beenden
    // console.log("\n[8] Aufräumen und Storage Service herunterfahren...");
    await storage.shutdown();
    // console.log("✓ Storage Service erfolgreich heruntergefahren");
    
    
  } catch (error) {
    console.error('Fehler beim Ausführen des Beispiels:', error);
  }
}

// Ausführen des Beispiels
main().catch(console.error);
