/**
 * De<PERSON>ci Scholar - Beispiel für die Integration von PublicationController mit EnhancedStorageService
 * 
 * Dieses Beispiel demonstriert die erweiterte Nutzung des PublicationControllers
 * mit der optimierten Speicherlösung für wissenschaftliche Publikationen.
 */

import PublicationController from '../controllers/PublicationController';
import EnhancedStorageService from '../storage/EnhancedStorageService';
import DatabaseService from '../db/DatabaseService';
import { fileURLToPath } from 'url/promises';
import path from 'path/promises';
import fs from 'fs/promises';
import crypto from 'crypto';

// ESM dirname-Äquivalent
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Hilfsfunktion zum Generieren einer Beispiel-PDF
// In einer echten Anwendung würde hier eine tatsächliche PDF-Datei geladen werden
function generateSamplePDF(sizeInMB = 3) {
  const sizeInBytes = sizeInMB * 1024 * 1024;
  const buffer = Buffer.alloc(sizeInBytes);
  crypto.randomFillSync(buffer);
  
  // Füge einen Header hinzu, damit es wie ein PDF aussieht (für Demonstrationszwecke)
  const pdfHeader = Buffer.from('%PDF-1.5\n%¥±ë\n');
  pdfHeader.copy(buffer);
  
  return buffer;
}

// Beispiel für eine wissenschaftliche Publikation
const samplePublication = {
  title: 'Verbesserte Protokollauswahl für dezentrale wissenschaftliche Datenverteilung',
  authors: [
    { name: 'Dr. Thomas Schmidt', affiliation: 'TU Berlin', orcid: '0000-0001-2345-6789' },
    { name: 'Prof. Claudia Weber', affiliation: 'ETH Zürich', orcid: '0000-0002-3456-7890' }
  ],
  abstract: 'Diese Arbeit präsentiert eine neue Methode zur automatischen Auswahl des optimalen Protokolls für die dezentrale Speicherung und Verteilung wissenschaftlicher Daten. Unsere Ergebnisse zeigen signifikante Verbesserungen in Geschwindigkeit, Zuverlässigkeit und Ressourcenverbrauch im Vergleich zu herkömmlichen Ansätzen.',
  keywords: ['dezentrale Speicherung', 'Protokolloptimierung', 'wissenschaftliche Daten', 'IPFS', 'BitTorrent'],
  doi: null, // Wird automatisch zugewiesen, wenn die Publikation registriert wird
  license: 'CC-BY-4.0',
  version: '1.0',
  publicationDate: new Date().toISOString()
};

// Hauptfunktion
async function main() {
  console.log('DeSci Scholar - Beispiel für optimierte PublicationController-Integration');
  console.log('=====================================================================');
  
  try {
    // 1. Initialisiere die Dienste
    console.log('\n[1] Initialisiere Dienste...');
    
    // EnhancedStorageService initialisieren
    const storageService = new EnhancedStorageService({
      ipfs: {
        gateway: 'https://ipfs.io',
        apiUrl: 'http://localhost:5001/api/v0',
        enablePinning: true
      },
      bittorrent: {
        downloadPath: path.join(__dirname, '../temp/downloads'),
        trackers: [
          'udp://tracker.opentrackr.org:1337/announce',
          'udp://tracker.openbittorrent.com:6969/announce'
        ]
      },
      metadata: {
        compressMetadata: true
      },
      chunkLargeFiles: true
    });
    
    // DatabaseService initialisieren (mit In-Memory-Modus für das Beispiel)
    const dbService = new DatabaseService({
      inMemory: true, // Für das Beispiel nutzen wir eine In-Memory-Datenbank
      uri: 'mongodb://localhost:27017/desci-scholar-example',
      collections: {
        publications: 'publications',
        users: 'users',
        citations: 'citations',
        stats: 'stats'
      }
    });
    
    // PublicationController mit unseren optimierten Diensten initialisieren
    const publicationController = new PublicationController({
      storageService: storageService,
      databaseService: dbService,
      // Weitere Konfigurationen...
    });
    
    await storageService.initialize();
    await dbService.initialize();
    await publicationController.initialize();
    
    console.log('✓ Alle Dienste erfolgreich initialisiert');
    
    // 2. Beispiel für die Nutzung von fs zum Schreiben einer Datei
    console.log('\n[2] Beispiel für die Nutzung von fs...');
    const tempFilePath = path.join(__dirname, 'temp-file.txt');
    const fileContent = 'Hallo Welt - Dies ist ein Beispiel für fs.writeFile';
await     await fs.writeFile(tempFilePath, fileContent);
    console.log(`✓ Datei mit fs.writeFile geschrieben: ${tempFilePath}`);
    
    // 3. Generiere eine Beispiel-PDF
    console.log('\n[3] Generiere Beispiel-PDF (3MB)...');
    const pdfBuffer = generateSamplePDF(3);
    console.log(`✓ Beispiel-PDF generiert: ${(pdfBuffer.length / 1024 / 1024).toFixed(2)}MB`);
    
    // 4. Publikation erstellen
    console.log('\n[4] Publikation erstellen...');
    const publication = await publicationController.createPublication(samplePublication);
    console.log('✓ Publikation erstellt:');
    console.log(`- ID: ${publication.id}`);
    console.log(`- Titel: ${publication.title}`);
    console.log(`- Autoren: ${publication.authors.map(a => a.name).join(', ')}`);
    
    // 4. PDF-Datei zur Publikation hochladen
    console.log('\n[4] PDF-Datei zur Publikation hochladen...');
    const uploadResult = await publicationController.uploadPublicationFile(
      publication.id,
      pdfBuffer,
      {
        filename: 'research-paper.pdf',
        mimetype: 'application/pdf',
        description: 'Hauptdokument der Forschungsarbeit'
      }
    );
    
    console.log('✓ Datei erfolgreich hochgeladen');
    console.log(`- Storage-Protokoll: ${uploadResult.storageProtocol}`);
    console.log(`- IPFS CID: ${uploadResult.ipfsCid || 'N/A'}`);
    console.log(`- BitTorrent InfoHash: ${uploadResult.torrentInfoHash || 'N/A'}`);
    console.log(`- Magnetlink: ${uploadResult.magnetLink || 'N/A'}`);
    
    // 5. Publikation veröffentlichen
    console.log('\n[5] Publikation veröffentlichen...');
    const publishedPublication = await publicationController.publishPublication(publication.id);
    
    console.log('✓ Publikation erfolgreich veröffentlicht');
    console.log(`- Status: ${publishedPublication.status}`);
    console.log(`- Veröffentlichungsdatum: ${new Date(publishedPublication.publishedDate).toLocaleString()}`);
    console.log(`- DOI: ${publishedPublication.doi || 'Ausstehend'}`);
    
    // 6. Publikationen suchen
    console.log('\n[6] Publikationssuche...');
    const searchResults = await publicationController.searchPublications({
      query: 'dezentral protokoll',
      limit: 10
    });
    
    console.log(`✓ ${searchResults.length} Publikation(en) gefunden:`);
    searchResults.forEach((pub, index) => {
      console.log(`\n[Ergebnis ${index + 1}]`);
      console.log(`- Titel: ${pub.title}`);
      console.log(`- Autoren: ${pub.authors.map(a => a.name).join(', ')}`);
      console.log(`- ID: ${pub.id}`);
    });
    
    // 7. Publikation abrufen
    console.log('\n[7] Publikation und Metadaten abrufen...');
    const retrievedPublication = await publicationController.getPublicationDetails(publication.id);
    
    console.log('✓ Publikation erfolgreich abgerufen');
    console.log(`- Titel: ${retrievedPublication.title}`);
    console.log(`- Abstract (Auszug): ${retrievedPublication.abstract.substring(0, 100)}...`);
    console.log(`- Speicher-Metadaten: ${JSON.stringify(retrievedPublication.storageInfo, null, 2)}`);
    
    // 8. Publikationsdatei herunterladen (simuliert)
    console.log('\n[8] Publikationsdatei herunterladen...');
    console.log('Starte Download der PDF-Datei...');
    
    const fileResult = await publicationController.downloadPublicationFile(
      publication.id,
      'research-paper.pdf'
    );
    
    console.log('✓ Datei erfolgreich heruntergeladen');
    console.log(`- Dateityp: ${fileResult.mimetype}`);
    console.log(`- Dateigröße: ${(fileResult.data.length / 1024 / 1024).toFixed(2)}MB`);
    
    // Verifiziere die Integrität
    const originalHash = crypto.createHash('sha256').update(pdfBuffer).digest('hex');
    const downloadedHash = crypto.createHash('sha256').update(fileResult.data).digest('hex');
    
    console.log('Dateiintegrität:');
    console.log(`- Original-Hash: ${originalHash.substring(0, 16)}...`);
    console.log(`- Download-Hash: ${downloadedHash.substring(0, 16)}...`);
    console.log(`- Integrität: ${originalHash === downloadedHash ? 'VERIFIZIERT ✓' : 'FEHLER ✗'}`);
    
    // 9. Statistiken und Verteilungsinformationen
    console.log('\n[9] Abruf von Statistik- und Verteilungsinformationen...');
    
    const stats = await publicationController.getPublicationStats(publication.id);
    console.log('Statistiken:');
    console.log(`- Aufrufe: ${stats.views}`);
    console.log(`- Downloads: ${stats.downloads}`);
    console.log(`- Zitierungen: ${stats.citations}`);
    
    const distributionInfo = await publicationController.getDistributionInfo(publication.id);
    console.log('\nVerteilungsinformationen:');
    console.log(`- Aktive Peers: ${distributionInfo.peers}`);
    console.log(`- Verfügbarkeit: ${distributionInfo.availability}%`);
    console.log(`- Gesamter Upload: ${(distributionInfo.uploaded / 1024 / 1024).toFixed(2)}MB`);
    
    // 10. Aufräumen und Beenden
    console.log('\n[10] Aufräumen und Dienste herunterfahren...');
    await publicationController.shutdown();
    await dbService.shutdown();
    await storageService.shutdown();
    
    console.log('✓ Alle Dienste erfolgreich heruntergefahren');
    console.log('\nBeispiel erfolgreich abgeschlossen!');
    
  } catch (error) {
    console.error('Fehler beim Ausführen des Beispiels:', error);
  }
}

// Ausführen des Beispiels
main().catch(console.error);
