/**
 * @fileoverview Beispiel für die Verwendung des CitationAnalysisAgent
 * 
 * Dieses Be<PERSON> zeigt, wie der CitationAnalysisAgent in die DeSci-Scholar-Plattform
 * integriert werden kann, um Zitationen zu analysieren und mit NFTs zu verknüpfen.
 */

import CitationAnalysisAgent from '../ai/agents/CitationAnalysisAgent.js';
import { DatabaseService } from '../database/DatabaseService.js';
import { BlockchainService } from '../blockchain/BlockchainService.js';
import { VectorDatabase } from '../database/VectorDatabase.js';
import { TextEmbeddingService } from '../ai/TextEmbeddingService.js';
import { logger } from '../utils/logger.js';
import fs from 'fs/promises';
import path from 'path';

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  try {
    logger.info('Starte CitationAnalysisAgent-Beispiel');
    
    // Initialisiere Dienste
    const databaseService = new DatabaseService();
    await databaseService.initialize();
    
    const blockchainService = new BlockchainService({
      nodeUrl: process.env.BLOCKCHAIN_NODE_URL,
      privateKey: process.env.BLOCKCHAIN_PRIVATE_KEY
    });
    await blockchainService.initialize();
    
    const vectorDatabase = new VectorDatabase();
    await vectorDatabase.initialize();
    
    const textEmbeddingService = new TextEmbeddingService();
    await textEmbeddingService.initialize();
    
    // Erstelle und initialisiere den CitationAnalysisAgent
    const citationAnalysisAgent = new CitationAnalysisAgent({
      databaseService,
      blockchainService,
      vectorDatabase,
      textEmbeddingService,
      config: {
        maxCitationsPerAnalysis: 50,
        minCitationWeight: 0.2,
        maxDepth: 2,
        energyEfficient: true
      }
    });
    
    await citationAnalysisAgent.initialize();
    
    // Hole eine Beispielpublikation
    const publications = await databaseService.query(
      'SELECT * FROM publications ORDER BY citation_count DESC LIMIT 1'
    );
    
    if (!publications || publications.length === 0) {
      logger.error('Keine Publikationen gefunden');
      return;
    }
    
    const publication = publications[0];
    const doi = publication.doi;
    
    // Beispiel 1: Extrahiere Zitationen aus einer Publikation
    logger.info('Beispiel 1: Extrahiere Zitationen aus einer Publikation');
    
    // Lade den Volltext der Publikation (simuliert)
    let fullText = '';
    try {
      // In einer realen Anwendung würde der Volltext aus einer Datenbank oder einem Dateisystem geladen
      fullText = await fs.readFile(path.join(__dirname, '../test/data/sample-publication.txt'), 'utf-8');
    } catch (error) {
      // Fallback: Verwende einen simulierten Volltext
      fullText = `
Title: ${publication.title}
Authors: <AUTHORS>
Abstract: ${publication.abstract}

Introduction:
This paper builds on the work of Smith et al. (DOI: 10.1000/example-doi-1) who demonstrated that...
The theoretical framework was developed by Johnson (DOI: 10.1000/example-doi-2) and extended by...

Methodology:
We applied the method described by Williams and Brown (DOI: 10.1000/example-doi-3) with modifications...
The experimental setup was similar to that used by Garcia et al. (DOI: 10.1000/example-doi-4)...

Results:
Our findings confirm the results reported by Lee (DOI: 10.1000/example-doi-5) but contradict...
The statistical analysis followed the approach of Chen and Wang (DOI: 10.1000/example-doi-6)...

Discussion:
These results support the theory proposed by Miller (DOI: 10.1000/example-doi-7)...
However, they differ from the conclusions drawn by Taylor et al. (DOI: 10.1000/example-doi-8)...

Conclusion:
This work extends the findings of previous studies (DOI: 10.1000/example-doi-9, DOI: 10.1000/example-doi-10)...
Future research should explore the directions suggested by Rodriguez (DOI: 10.1000/example-doi-11)...

References:
1. Smith et al. (2018). Title of the paper. Journal Name, 10(2), 123-145. DOI: 10.1000/example-doi-1
2. Johnson, A. (2019). Title of the paper. Journal Name, 11(3), 234-256. DOI: 10.1000/example-doi-2
...
`;
    }
    
    const extractionResult = await citationAnalysisAgent.extractCitations(doi, fullText);
    
    logger.info('Zitationen extrahiert', {
      success: extractionResult.success,
      doi,
      extractedCount: extractionResult.success ? extractionResult.extractedCitations.length : 0,
      savedCount: extractionResult.success ? extractionResult.savedCount : 0
    });
    
    // Beispiel 2: Analysiere Zitationen
    logger.info('Beispiel 2: Analysiere Zitationen');
    
    const analysisResult = await citationAnalysisAgent.analyzeCitations(doi);
    
    logger.info('Zitationsanalyse abgeschlossen', {
      success: analysisResult.success,
      doi,
      citationCount: analysisResult.success ? analysisResult.analysis.metrics.citationCount : 0,
      referenceCount: analysisResult.success ? analysisResult.analysis.metrics.referenceCount : 0,
      influenceScore: analysisResult.success ? analysisResult.analysis.metrics.influenceScore : 0
    });
    
    // Beispiel 3: Verknüpfe Zitationen mit NFTs
    logger.info('Beispiel 3: Verknüpfe Zitationen mit NFTs');
    
    const linkResult = await citationAnalysisAgent.linkCitationsToNFTs(doi);
    
    logger.info('Zitationen mit NFTs verknüpft', {
      success: linkResult.success,
      doi,
      linkedCount: linkResult.success ? linkResult.linkedCount : 0,
      totalCount: linkResult.success ? linkResult.totalCount : 0
    });
    
    // Beispiel 4: Berechne den Einfluss einer Publikation
    logger.info('Beispiel 4: Berechne den Einfluss einer Publikation');
    
    const influenceResult = await citationAnalysisAgent.calculatePublicationInfluence(doi);
    
    logger.info('Einfluss berechnet', {
      success: influenceResult.success,
      doi,
      overallInfluence: influenceResult.success ? 
        influenceResult.influenceMetrics.advancedMetrics.overallInfluence : 0,
      citationCount: influenceResult.success ? 
        influenceResult.influenceMetrics.basicMetrics.citationCount : 0,
      blockchainSuccess: influenceResult.success && influenceResult.blockchain ? 
        influenceResult.blockchain.success : false
    });
    
    // Beispiel 5: Erstelle ein Zitationsnetzwerk mit größerer Tiefe
    logger.info('Beispiel 5: Erstelle ein Zitationsnetzwerk');
    
    // Diese Methode ist privat, aber wir können sie über die analyzeCitations-Methode mit einer größeren Tiefe aufrufen
    const networkResult = await citationAnalysisAgent.analyzeCitations(doi, {
      forceRefresh: true,
      depth: 2
    });
    
    logger.info('Zitationsnetzwerk erstellt', {
      success: networkResult.success,
      doi,
      nodeCount: networkResult.success ? networkResult.analysis.citationNetwork.nodes.length : 0,
      edgeCount: networkResult.success ? networkResult.analysis.citationNetwork.edges.length : 0
    });
    
    logger.info('CitationAnalysisAgent-Beispiel abgeschlossen');
  } catch (error) {
    logger.error('Fehler im CitationAnalysisAgent-Beispiel', {
      error: error.message,
      stack: error.stack
    });
  }
}

// Führe das Beispiel aus
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});