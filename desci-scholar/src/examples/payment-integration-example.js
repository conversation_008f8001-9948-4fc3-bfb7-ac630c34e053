/**
 * @fileoverview Example usage of the DeSci-Scholar payment integration
 * This script demonstrates how to use the PaymentProcessor for various payment methods
 */

import PaymentProcessor from '../blockchain/payment/index.js';
import dotenv from 'dotenv';
dotenv.config();

// Example configuration for different payment methods
const config = {
  // Polkadot payment example
  polkadot: {
    fromAddress: process.env.EXAMPLE_POLKADOT_ADDRESS || '5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY',
    toAddress: process.env.DESCI_PLATFORM_ADDRESS || '5FHneW46xGXgs5mUiveU4sbTyGBzmstUspZC92UhjJM694ty',
    privateKey: process.env.EXAMPLE_PRIVATE_KEY || '//Alice', // Development key, NEVER use in production
    amount: 1.5, // DOT amount
    reference: 'Access fee for research paper DOI:10.1234/example.2023.001'
  },
  
  // Stripe payment example
  stripe: {
    token: 'tok_visa', // Example test token from Stripe
    amount: 2500, // $25.00 in cents
    currency: 'usd',
    description: 'DeSci-Scholar premium access subscription'
  },
  
  // PayPal payment example
  paypal: {
    amount: 25.00,
    currency: 'USD',
    description: 'DeSci-Scholar premium access subscription',
    returnUrl: 'https://desci-scholar.example.com/payments/success',
    cancelUrl: 'https://desci-scholar.example.com/payments/cancel'
  }
};

/**
 * Run payment examples
 */
async function runPaymentExamples() {
  const paymentProcessor = new PaymentProcessor();
  await paymentProcessor.init();
  
  try {
    // Example 1: Calculate DOT equivalent for USD amount
    const usdAmount = 25.00;
    const dotAmount = await paymentProcessor.calculateDOTAmount(usdAmount);
    console.log(`USD ${usdAmount} is approximately ${dotAmount} DOT`);
    
    // Example 2: Generate a QR code for Polkadot payment
    const qrCodeUrl = paymentProcessor.generatePolkadotPaymentQRCode(
      config.polkadot.toAddress,
      dotAmount,
      config.polkadot.reference
    );
    console.log(`Polkadot payment QR code URL: ${qrCodeUrl}`);
    
    // Example 3: Process a Polkadot payment (only works on development chain)
    console.log('\nProcessing Polkadot payment...');
    try {
      const polkadotResult = await paymentProcessor.processPolkadotPayment(config.polkadot);
      console.log('Polkadot payment successful:', polkadotResult);
      
      // Record the payment on the blockchain
      const recordResult = await paymentProcessor.recordPaymentOnBlockchain(
        {
          paymentId: `pol-${Date.now()}`,
          method: 'polkadot',
          from: config.polkadot.fromAddress,
          to: config.polkadot.toAddress,
          amount: config.polkadot.amount,
          currency: 'DOT',
          reference: config.polkadot.reference
        },
        config.polkadot.privateKey
      );
      console.log('Payment recorded on blockchain:', recordResult);
    } catch (error) {
      console.error('Polkadot payment error:', error.message);
      console.log('Note: Polkadot payments require a running development chain');
    }
    
    // Example 4: Process a Stripe payment
    console.log('\nProcessing Stripe payment...');
    try {
      const stripeResult = await paymentProcessor.processStripePayment(config.stripe);
      console.log('Stripe payment successful:', stripeResult);
    } catch (error) {
      console.error('Stripe payment error:', error.message);
      console.log('Note: Stripe payments require valid API keys');
    }
    
    // Example 5: Create a PayPal payment
    console.log('\nCreating PayPal payment...');
    try {
      const paypalResult = await paymentProcessor.createPayPalPayment(config.paypal);
      console.log('PayPal payment created:', paypalResult);
      console.log(`Approval URL: ${paypalResult.approvalUrl}`);
      
      // Note: In a real application, the user would visit the approval URL
      // and then you would capture the payment using:
      // const captureResult = await paymentProcessor.capturePayPalPayment(paypalResult.id);
    } catch (error) {
      console.error('PayPal payment error:', error.message);
      console.log('Note: PayPal payments require valid API credentials');
    }
    
  } catch (error) {
    console.error('Payment example error:', error.message);
  } finally {
    // Disconnect from the Polkadot node
    await paymentProcessor.polkadotClient.disconnect();
  }
}

// Run the examples if this file is executed directly
// In ESM this is handled differently than in CommonJS
const isMainModule = import.meta.url === `file://${process.argv[1]}`;
if (isMainModule) {
  runPaymentExamples()
    .then(() => console.log('Payment examples completed'))
    .catch(error => console.error('Error running payment examples:', error));
}

export { runPaymentExamples };
