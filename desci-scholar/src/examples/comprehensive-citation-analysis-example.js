/**
 * @fileoverview Beispiel für die Verwendung des ComprehensiveCitationAnalyzer
 * 
 * Dieses <PERSON> zeigt, wie der ComprehensiveCitationAnalyzer verwendet werden kann, um
 * umfassende Zitationsanalysen durchzuführen, die Autoren, Institutionen, Fachgebiete und
 * Publikationen berücksichtigen.
 */

import ComprehensiveCitationAnalyzer from '../ai/agents/ComprehensiveCitationAnalyzer.js';
import { DatabaseService } from '../database/DatabaseService.js';
import { TextAnalysisService } from '../ai/services/TextAnalysisService.js';
import { logger } from '../utils/logger.js';

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  try {
    logger.info('Starte ComprehensiveCitationAnalyzer-Beispiel');
    
    // Initialisiere Dienste
    const databaseService = new DatabaseService();
    await databaseService.initialize();
    
    const textAnalysisService = new TextAnalysisService({
      apiKey: process.env.TEXT_ANALYSIS_API_KEY
    });
    await textAnalysisService.initialize();
    
    // Erstelle und initialisiere den ComprehensiveCitationAnalyzer
    const citationAnalyzer = new ComprehensiveCitationAnalyzer({
      databaseService,
      textAnalysisService,
      config: {
        maxKeywords: 20,
        minKeywordLength: 4,
        energyEfficient: true
      }
    });
    
    await citationAnalyzer.initialize();
    
    // Beispiel 1: Analyse eines bestimmten Forschungsfeldes
    logger.info('Beispiel 1: Analyse eines bestimmten Forschungsfeldes');
    
    const fieldAnalysis = await citationAnalyzer.performComprehensiveCitationAnalysis({
      researchField: 'Quantum Computing',
      fromDate: '2018-01-01',
      limit: 100
    });
    
    if (fieldAnalysis.success) {
      logger.info('Umfassende Zitationsanalyse für Forschungsfeld abgeschlossen', {
        researchField: 'Quantum Computing',
        publicationsCount: fieldAnalysis.publicationsCount,
        patentsCount: fieldAnalysis.patentsCount
      });
      
      // Zeige Zusammenfassung
      logger.info('Analysezusammenfassung', {
        summary: fieldAnalysis.summary.overallSummary
      });
      
      // Zeige Top-Autoren
      if (fieldAnalysis.authorAnalysis && !fieldAnalysis.authorAnalysis.error) {
        logger.info('Top-Autoren nach Zitationen', {
          authors: fieldAnalysis.authorAnalysis.topAuthorsByTotalCitations.slice(0, 5).map(author => ({
            name: author.name,
            totalCitations: author.totalCitations,
            hIndex: author.hIndex,
            publicationCount: author.publicationCount
          }))
        });
      }
      
      // Zeige Top-Institutionen
      if (fieldAnalysis.institutionAnalysis && !fieldAnalysis.institutionAnalysis.error) {
        logger.info('Top-Institutionen nach Zitationen', {
          institutions: fieldAnalysis.institutionAnalysis.topInstitutionsByTotalCitations.slice(0, 5).map(inst => ({
            name: inst.name,
            totalCitations: inst.totalCitations,
            publicationCount: inst.publicationCount,
            patentCount: inst.patentCount
          }))
        });
      }
      
      // Zeige Forschungstrends
      if (fieldAnalysis.trendAnalysis && !fieldAnalysis.trendAnalysis.error && fieldAnalysis.trendAnalysis.fieldTrends) {
        logger.info('Forschungstrends', {
          risingFields: fieldAnalysis.trendAnalysis.fieldTrends.risingFields.slice(0, 3).map(field => field.field),
          decliningFields: fieldAnalysis.trendAnalysis.fieldTrends.decliningFields.slice(0, 3).map(field => field.field)
        });
      }
      
      // Zeige Technologietransfer
      if (fieldAnalysis.techTransferAnalysis && !fieldAnalysis.techTransferAnalysis.error) {
        logger.info('Technologietransfer', {
          linkedCount: fieldAnalysis.techTransferAnalysis.linkedCount,
          avgTimeGapInDays: fieldAnalysis.techTransferAnalysis.timeGapAnalysis ? 
            fieldAnalysis.techTransferAnalysis.timeGapAnalysis.averageGapInDays : null
        });
      }
    } else {
      logger.error('Fehler bei der Analyse des Forschungsfeldes', {
        error: fieldAnalysis.error
      });
    }
    
    // Beispiel 2: Analyse einer bestimmten Institution
    logger.info('Beispiel 2: Analyse einer bestimmten Institution');
    
    const institutionAnalysis = await citationAnalyzer.performComprehensiveCitationAnalysis({
      institution: 'MIT',
      fromDate: '2018-01-01',
      limit: 100
    });
    
    if (institutionAnalysis.success) {
      logger.info('Umfassende Zitationsanalyse für Institution abgeschlossen', {
        institution: 'MIT',
        publicationsCount: institutionAnalysis.publicationsCount,
        patentsCount: institutionAnalysis.patentsCount
      });
      
      // Zeige Zusammenfassung
      logger.info('Analysezusammenfassung', {
        summary: institutionAnalysis.summary.institutionSummary
      });
      
      // Zeige Forschungsschwerpunkte
      if (institutionAnalysis.fieldAnalysis && !institutionAnalysis.fieldAnalysis.error) {
        logger.info('Forschungsschwerpunkte der Institution', {
          fields: institutionAnalysis.fieldAnalysis.topFieldsByTotalCitations.slice(0, 5).map(field => ({
            name: field.name,
            totalCitations: field.totalCitations,
            publicationCount: field.publicationCount
          }))
        });
      }
      
      // Zeige Kollaborationsnetzwerk
      if (institutionAnalysis.networkAnalysis && 
          institutionAnalysis.networkAnalysis.institutionNetwork &&
          institutionAnalysis.networkAnalysis.institutionNetwork.topCollaborators) {
        logger.info('Top-Kollaborationspartner der Institution', {
          collaborators: institutionAnalysis.networkAnalysis.institutionNetwork.topCollaborators.slice(0, 5).map(collab => ({
            institution: collab.institution,
            collaborationCount: collab.totalCollaborations
          }))
        });
      }
    } else {
      logger.error('Fehler bei der Analyse der Institution', {
        error: institutionAnalysis.error
      });
    }
    
    // Beispiel 3: Analyse eines bestimmten Autors
    logger.info('Beispiel 3: Analyse eines bestimmten Autors');
    
    const authorAnalysis = await citationAnalyzer.performComprehensiveCitationAnalysis({
      authorName: 'John Smith',
      limit: 50
    });
    
    if (authorAnalysis.success) {
      logger.info('Umfassende Zitationsanalyse für Autor abgeschlossen', {
        authorName: 'John Smith',
        publicationsCount: authorAnalysis.publicationsCount,
        patentsCount: authorAnalysis.patentsCount
      });
      
      // Zeige Zusammenfassung
      logger.info('Analysezusammenfassung', {
        summary: authorAnalysis.summary.authorSummary
      });
      
      // Zeige Publikationsmetriken
      if (authorAnalysis.authorAnalysis && !authorAnalysis.authorAnalysis.error) {
        const authorData = authorAnalysis.authorAnalysis.topAuthorsByTotalCitations.find(a => a.name.includes('John Smith'));
        
        if (authorData) {
          logger.info('Publikationsmetriken des Autors', {
            totalCitations: authorData.totalCitations,
            hIndex: authorData.hIndex,
            publicationCount: authorData.publicationCount,
            patentCount: authorData.patentCount
          });
        }
      }
      
      // Zeige Kollaborationsnetzwerk
      if (authorAnalysis.networkAnalysis && 
          authorAnalysis.networkAnalysis.authorNetwork &&
          authorAnalysis.networkAnalysis.authorNetwork.topCollaborators) {
        const authorCollabs = authorAnalysis.networkAnalysis.authorNetwork.topCollaborators.find(a => a.author.includes('John Smith'));
        
        if (authorCollabs) {
          logger.info('Top-Kollaborationspartner des Autors', {
            collaborators: authorCollabs.topCollaborators.map(collab => ({
              collaborator: collab.collaborator,
              collaborationCount: collab.count
            }))
          });
        }
      }
    } else {
      logger.error('Fehler bei der Analyse des Autors', {
        error: authorAnalysis.error
      });
    }
    
    // Beispiel 4: Analyse einer Fachzeitschrift
    logger.info('Beispiel 4: Analyse einer Fachzeitschrift');
    
    const journalAnalysis = await citationAnalyzer.performComprehensiveCitationAnalysis({
      journal: 'Nature',
      fromDate: '2020-01-01',
      limit: 100
    });
    
    if (journalAnalysis.success) {
      logger.info('Umfassende Zitationsanalyse für Fachzeitschrift abgeschlossen', {
        journal: 'Nature',
        publicationsCount: journalAnalysis.publicationsCount
      });
      
      // Zeige Journalmetriken
      if (journalAnalysis.journalAnalysis && !journalAnalysis.journalAnalysis.error) {
        const journalData = journalAnalysis.journalAnalysis.topJournalsByImpactFactor.find(j => j.name.includes('Nature'));
        
        if (journalData) {
          logger.info('Metriken der Fachzeitschrift', {
            impactFactor: journalData.impactFactor,
            totalCitations: journalData.totalCitations,
            publicationCount: journalData.publicationCount,
            avgCitationsPerPublication: journalData.avgCitationsPerPublication
          });
          
          if (journalData.researchFields && journalData.researchFields.length > 0) {
            logger.info('Hauptforschungsfelder der Fachzeitschrift', {
              fields: journalData.researchFields.map(field => ({
                field: field.field,
                count: field.count
              }))
            });
          }
        }
      }
    } else {
      logger.error('Fehler bei der Analyse der Fachzeitschrift', {
        error: journalAnalysis.error
      });
    }
    
    // Beispiel 5: Analyse des Technologietransfers in einem Zeitraum
    logger.info('Beispiel 5: Analyse des Technologietransfers in einem Zeitraum');
    
    const transferAnalysis = await citationAnalyzer.performComprehensiveCitationAnalysis({
      fromDate: '2018-01-01',
      toDate: '2023-12-31',
      limit: 200
    });
    
    if (transferAnalysis.success) {
      logger.info('Umfassende Analyse des Technologietransfers abgeschlossen', {
        publicationsCount: transferAnalysis.publicationsCount,
        patentsCount: transferAnalysis.patentsCount
      });
      
      // Zeige Zusammenfassung
      logger.info('Analysezusammenfassung', {
        summary: transferAnalysis.summary.techTransferSummary
      });
      
      // Zeige Technologietransfer
      if (transferAnalysis.techTransferAnalysis && !transferAnalysis.techTransferAnalysis.error) {
        logger.info('Technologietransfer-Metriken', {
          linkedCount: transferAnalysis.techTransferAnalysis.linkedCount,
          linkTypes: transferAnalysis.techTransferAnalysis.linkTypes
        });
        
        if (transferAnalysis.techTransferAnalysis.timeGapAnalysis) {
          logger.info('Zeitabstandsanalyse zwischen Publikationen und Patenten', {
            averageGapInDays: transferAnalysis.techTransferAnalysis.timeGapAnalysis.averageGapInDays,
            medianGapInDays: transferAnalysis.techTransferAnalysis.timeGapAnalysis.medianGapInDays,
            directionCounts: transferAnalysis.techTransferAnalysis.timeGapAnalysis.directionCounts,
            gapDistribution: transferAnalysis.techTransferAnalysis.timeGapAnalysis.gapDistribution
          });
        }
        
        if (transferAnalysis.techTransferAnalysis.topPublicationsWithPatents && 
            transferAnalysis.techTransferAnalysis.topPublicationsWithPatents.length > 0) {
          logger.info('Top-Publikationen mit Patentverknüpfungen', {
            publications: transferAnalysis.techTransferAnalysis.topPublicationsWithPatents.slice(0, 5).map(pub => ({
              title: pub.title,
              linkedPatentsCount: pub.linkedPatentsCount
            }))
          });
        }
        
        if (transferAnalysis.techTransferAnalysis.topPatentsWithPublications && 
            transferAnalysis.techTransferAnalysis.topPatentsWithPublications.length > 0) {
          logger.info('Top-Patente mit Publikationsverknüpfungen', {
            patents: transferAnalysis.techTransferAnalysis.topPatentsWithPublications.slice(0, 5).map(patent => ({
              title: patent.title,
              linkedPublicationsCount: patent.linkedPublicationsCount
            }))
          });
        }
      }
    } else {
      logger.error('Fehler bei der Analyse des Technologietransfers', {
        error: transferAnalysis.error
      });
    }
    
    logger.info('ComprehensiveCitationAnalyzer-Beispiel abgeschlossen');
  } catch (error) {
    logger.error('Fehler im ComprehensiveCitationAnalyzer-Beispiel', {
      error: error.message,
      stack: error.stack
    });
  }
}

// Führe das Beispiel aus
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});/**
 * @fileoverview Beispiel für die Verwendung des ComprehensiveCitationAnalyzer
 * 
 * Dieses Beispiel zeigt, wie der ComprehensiveCitationAnalyzer verwendet werden kann, um
 * umfassende Zitationsanalysen durchzuführen, die Autoren, Institutionen, Fachgebiete und
 * Publikationen berücksichtigen.
 */

import ComprehensiveCitationAnalyzer from '../ai/agents/ComprehensiveCitationAnalyzer.js';
import { DatabaseService } from '../database/DatabaseService.js';
import { TextAnalysisService } from '../ai/services/TextAnalysisService.js';
import { logger } from '../utils/logger.js';

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  try {
    logger.info('Starte ComprehensiveCitationAnalyzer-Beispiel');
    
    // Initialisiere Dienste
    const databaseService = new DatabaseService();
    await databaseService.initialize();
    
    const textAnalysisService = new TextAnalysisService({
      apiKey: process.env.TEXT_ANALYSIS_API_KEY
    });
    await textAnalysisService.initialize();
    
    // Erstelle und initialisiere den ComprehensiveCitationAnalyzer
    const citationAnalyzer = new ComprehensiveCitationAnalyzer({
      databaseService,
      textAnalysisService,
      config: {
        maxKeywords: 20,
        minKeywordLength: 4,
        energyEfficient: true
      }
    });
    
    await citationAnalyzer.initialize();
    
    // Beispiel 1: Analyse eines bestimmten Forschungsfeldes
    logger.info('Beispiel 1: Analyse eines bestimmten Forschungsfeldes');
    
    const fieldAnalysis = await citationAnalyzer.performComprehensiveCitationAnalysis({
      researchField: 'Quantum Computing',
      fromDate: '2018-01-01',
      limit: 100
    });
    
    if (fieldAnalysis.success) {
      logger.info('Umfassende Zitationsanalyse für Forschungsfeld abgeschlossen', {
        researchField: 'Quantum Computing',
        publicationsCount: fieldAnalysis.publicationsCount,
        patentsCount: fieldAnalysis.patentsCount
      });
      
      // Zeige Zusammenfassung
      logger.info('Analysezusammenfassung', {
        summary: fieldAnalysis.summary.overallSummary
      });
      
      // Zeige Top-Autoren
      if (fieldAnalysis.authorAnalysis && !fieldAnalysis.authorAnalysis.error) {
        logger.info('Top-Autoren nach Zitationen', {
          authors: fieldAnalysis.authorAnalysis.topAuthorsByTotalCitations.slice(0, 5).map(author => ({
            name: author.name,
            totalCitations: author.totalCitations,
            hIndex: author.hIndex,
            publicationCount: author.publicationCount
          }))
        });
      }
      
      // Zeige Top-Institutionen
      if (fieldAnalysis.institutionAnalysis && !fieldAnalysis.institutionAnalysis.error) {
        logger.info('Top-Institutionen nach Zitationen', {
          institutions: fieldAnalysis.institutionAnalysis.topInstitutionsByTotalCitations.slice(0, 5).map(inst => ({
            name: inst.name,
            totalCitations: inst.totalCitations,
            publicationCount: inst.publicationCount,
            patentCount: inst.patentCount
          }))
        });
      }
      
      // Zeige Forschungstrends
      if (fieldAnalysis.trendAnalysis && !fieldAnalysis.trendAnalysis.error && fieldAnalysis.trendAnalysis.fieldTrends) {
        logger.info('Forschungstrends', {
          risingFields: fieldAnalysis.trendAnalysis.fieldTrends.risingFields.slice(0, 3).map(field => field.field),
          decliningFields: fieldAnalysis.trendAnalysis.fieldTrends.decliningFields.slice(0, 3).map(field => field.field)
        });
      }
      
      // Zeige Technologietransfer
      if (fieldAnalysis.techTransferAnalysis && !fieldAnalysis.techTransferAnalysis.error) {
        logger.info('Technologietransfer', {
          linkedCount: fieldAnalysis.techTransferAnalysis.linkedCount,
          avgTimeGapInDays: fieldAnalysis.techTransferAnalysis.timeGapAnalysis ? 
            fieldAnalysis.techTransferAnalysis.timeGapAnalysis.averageGapInDays : null
        });
      }
    } else {
      logger.error('Fehler bei der Analyse des Forschungsfeldes', {
        error: fieldAnalysis.error
      });
    }
    
    // Beispiel 2: Analyse einer bestimmten Institution
    logger.info('Beispiel 2: Analyse einer bestimmten Institution');
    
    const institutionAnalysis = await citationAnalyzer.performComprehensiveCitationAnalysis({
      institution: 'MIT',
      fromDate: '2018-01-01',
      limit: 100
    });
    
    if (institutionAnalysis.success) {
      logger.info('Umfassende Zitationsanalyse für Institution abgeschlossen', {
        institution: 'MIT',
        publicationsCount: institutionAnalysis.publicationsCount,
        patentsCount: institutionAnalysis.patentsCount
      });
      
      // Zeige Zusammenfassung
      logger.info('Analysezusammenfassung', {
        summary: institutionAnalysis.summary.institutionSummary
      });
      
      // Zeige Forschungsschwerpunkte
      if (institutionAnalysis.fieldAnalysis && !institutionAnalysis.fieldAnalysis.error) {
        logger.info('Forschungsschwerpunkte der Institution', {
          fields: institutionAnalysis.fieldAnalysis.topFieldsByTotalCitations.slice(0, 5).map(field => ({
            name: field.name,
            totalCitations: field.totalCitations,
            publicationCount: field.publicationCount
          }))
        });
      }
      
      // Zeige Kollaborationsnetzwerk
      if (institutionAnalysis.networkAnalysis && 
          institutionAnalysis.networkAnalysis.institutionNetwork &&
          institutionAnalysis.networkAnalysis.institutionNetwork.topCollaborators) {
        logger.info('Top-Kollaborationspartner der Institution', {
          collaborators: institutionAnalysis.networkAnalysis.institutionNetwork.topCollaborators.slice(0, 5).map(collab => ({
            institution: collab.institution,
            collaborationCount: collab.totalCollaborations
          }))
        });
      }
    } else {
      logger.error('Fehler bei der Analyse der Institution', {
        error: institutionAnalysis.error
      });
    }
    
    // Beispiel 3: Analyse eines bestimmten Autors
    logger.info('Beispiel 3: Analyse eines bestimmten Autors');
    
    const authorAnalysis = await citationAnalyzer.performComprehensiveCitationAnalysis({
      authorName: 'John Smith',
      limit: 50
    });
    
    if (authorAnalysis.success) {
      logger.info('Umfassende Zitationsanalyse für Autor abgeschlossen', {
        authorName: 'John Smith',
        publicationsCount: authorAnalysis.publicationsCount,
        patentsCount: authorAnalysis.patentsCount
      });
      
      // Zeige Zusammenfassung
      logger.info('Analysezusammenfassung', {
        summary: authorAnalysis.summary.authorSummary
      });
      
      // Zeige Publikationsmetriken
      if (authorAnalysis.authorAnalysis && !authorAnalysis.authorAnalysis.error) {
        const authorData = authorAnalysis.authorAnalysis.topAuthorsByTotalCitations.find(a => a.name.includes('John Smith'));
        
        if (authorData) {
          logger.info('Publikationsmetriken des Autors', {
            totalCitations: authorData.totalCitations,
            hIndex: authorData.hIndex,
            publicationCount: authorData.publicationCount,
            patentCount: authorData.patentCount
          });
        }
      }
      
      // Zeige Kollaborationsnetzwerk
      if (authorAnalysis.networkAnalysis && 
          authorAnalysis.networkAnalysis.authorNetwork &&
          authorAnalysis.networkAnalysis.authorNetwork.topCollaborators) {
        const authorCollabs = authorAnalysis.networkAnalysis.authorNetwork.topCollaborators.find(a => a.author.includes('John Smith'));
        
        if (authorCollabs) {
          logger.info('Top-Kollaborationspartner des Autors', {
            collaborators: authorCollabs.topCollaborators.map(collab => ({
              collaborator: collab.collaborator,
              collaborationCount: collab.count
            }))
          });
        }
      }
    } else {
      logger.error('Fehler bei der Analyse des Autors', {
        error: authorAnalysis.error
      });
    }
    
    // Beispiel 4: Analyse einer Fachzeitschrift
    logger.info('Beispiel 4: Analyse einer Fachzeitschrift');
    
    const journalAnalysis = await citationAnalyzer.performComprehensiveCitationAnalysis({
      journal: 'Nature',
      fromDate: '2020-01-01',
      limit: 100
    });
    
    if (journalAnalysis.success) {
      logger.info('Umfassende Zitationsanalyse für Fachzeitschrift abgeschlossen', {
        journal: 'Nature',
        publicationsCount: journalAnalysis.publicationsCount
      });
      
      // Zeige Journalmetriken
      if (journalAnalysis.journalAnalysis && !journalAnalysis.journalAnalysis.error) {
        const journalData = journalAnalysis.journalAnalysis.topJournalsByImpactFactor.find(j => j.name.includes('Nature'));
        
        if (journalData) {
          logger.info('Metriken der Fachzeitschrift', {
            impactFactor: journalData.impactFactor,
            totalCitations: journalData.totalCitations,
            publicationCount: journalData.publicationCount,
            avgCitationsPerPublication: journalData.avgCitationsPerPublication
          });
          
          if (journalData.researchFields && journalData.researchFields.length > 0) {
            logger.info('Hauptforschungsfelder der Fachzeitschrift', {
              fields: journalData.researchFields.map(field => ({
                field: field.field,
                count: field.count
              }))
            });
          }
        }
      }
    } else {
      logger.error('Fehler bei der Analyse der Fachzeitschrift', {
        error: journalAnalysis.error
      });
    }
    
    // Beispiel 5: Analyse des Technologietransfers in einem Zeitraum
    logger.info('Beispiel 5: Analyse des Technologietransfers in einem Zeitraum');
    
    const transferAnalysis = await citationAnalyzer.performComprehensiveCitationAnalysis({
      fromDate: '2018-01-01',
      toDate: '2023-12-31',
      limit: 200
    });
    
    if (transferAnalysis.success) {
      logger.info('Umfassende Analyse des Technologietransfers abgeschlossen', {
        publicationsCount: transferAnalysis.publicationsCount,
        patentsCount: transferAnalysis.patentsCount
      });
      
      // Zeige Zusammenfassung
      logger.info('Analysezusammenfassung', {
        summary: transferAnalysis.summary.techTransferSummary
      });
      
      // Zeige Technologietransfer
      if (transferAnalysis.techTransferAnalysis && !transferAnalysis.techTransferAnalysis.error) {
        logger.info('Technologietransfer-Metriken', {
          linkedCount: transferAnalysis.techTransferAnalysis.linkedCount,
          linkTypes: transferAnalysis.techTransferAnalysis.linkTypes
        });
        
        if (transferAnalysis.techTransferAnalysis.timeGapAnalysis) {
          logger.info('Zeitabstandsanalyse zwischen Publikationen und Patenten', {
            averageGapInDays: transferAnalysis.techTransferAnalysis.timeGapAnalysis.averageGapInDays,
            medianGapInDays: transferAnalysis.techTransferAnalysis.timeGapAnalysis.medianGapInDays,
            directionCounts: transferAnalysis.techTransferAnalysis.timeGapAnalysis.directionCounts,
            gapDistribution: transferAnalysis.techTransferAnalysis.timeGapAnalysis.gapDistribution
          });
        }
        
        if (transferAnalysis.techTransferAnalysis.topPublicationsWithPatents && 
            transferAnalysis.techTransferAnalysis.topPublicationsWithPatents.length > 0) {
          logger.info('Top-Publikationen mit Patentverknüpfungen', {
            publications: transferAnalysis.techTransferAnalysis.topPublicationsWithPatents.slice(0, 5).map(pub => ({
              title: pub.title,
              linkedPatentsCount: pub.linkedPatentsCount
            }))
          });
        }
        
        if (transferAnalysis.techTransferAnalysis.topPatentsWithPublications && 
            transferAnalysis.techTransferAnalysis.topPatentsWithPublications.length > 0) {
          logger.info('Top-Patente mit Publikationsverknüpfungen', {
            patents: transferAnalysis.techTransferAnalysis.topPatentsWithPublications.slice(0, 5).map(patent => ({
              title: patent.title,
              linkedPublicationsCount: patent.linkedPublicationsCount
            }))
          });
        }
      }
    } else {
      logger.error('Fehler bei der Analyse des Technologietransfers', {
        error: transferAnalysis.error
      });
    }
    
    logger.info('ComprehensiveCitationAnalyzer-Beispiel abgeschlossen');
  } catch (error) {
    logger.error('Fehler im ComprehensiveCitationAnalyzer-Beispiel', {
      error: error.message,
      stack: error.stack
    });
  }
}

// Führe das Beispiel aus
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});/**
 * @fileoverview Beispiel für die Verwendung des ComprehensiveCitationAnalyzer
 * 
 * Dieses Beispiel zeigt, wie der ComprehensiveCitationAnalyzer verwendet werden kann, um
 * umfassende Zitationsanalysen durchzuführen, die Autoren, Institutionen, Fachgebiete und
 * Publikationen berücksichtigen.
 */

import ComprehensiveCitationAnalyzer from '../ai/agents/ComprehensiveCitationAnalyzer.js';
import { DatabaseService } from '../database/DatabaseService.js';
import { TextAnalysisService } from '../ai/services/TextAnalysisService.js';
import { logger } from '../utils/logger.js';

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  try {
    logger.info('Starte ComprehensiveCitationAnalyzer-Beispiel');
    
    // Initialisiere Dienste
    const databaseService = new DatabaseService();
    await databaseService.initialize();
    
    const textAnalysisService = new TextAnalysisService({
      apiKey: process.env.TEXT_ANALYSIS_API_KEY
    });
    await textAnalysisService.initialize();
    
    // Erstelle und initialisiere den ComprehensiveCitationAnalyzer
    const citationAnalyzer = new ComprehensiveCitationAnalyzer({
      databaseService,
      textAnalysisService,
      config: {
        maxKeywords: 20,
        minKeywordLength: 4,
        energyEfficient: true
      }
    });
    
    await citationAnalyzer.initialize();
    
    // Beispiel 1: Analyse eines bestimmten Forschungsfeldes
    logger.info('Beispiel 1: Analyse eines bestimmten Forschungsfeldes');
    
    const fieldAnalysis = await citationAnalyzer.performComprehensiveCitationAnalysis({
      researchField: 'Quantum Computing',
      fromDate: '2018-01-01',
      limit: 100
    });
    
    if (fieldAnalysis.success) {
      logger.info('Umfassende Zitationsanalyse für Forschungsfeld abgeschlossen', {
        researchField: 'Quantum Computing',
        publicationsCount: fieldAnalysis.publicationsCount,
        patentsCount: fieldAnalysis.patentsCount
      });
      
      // Zeige Zusammenfassung
      logger.info('Analysezusammenfassung', {
        summary: fieldAnalysis.summary.overallSummary
      });
      
      // Zeige Top-Autoren
      if (fieldAnalysis.authorAnalysis && !fieldAnalysis.authorAnalysis.error) {
        logger.info('Top-Autoren nach Zitationen', {
          authors: fieldAnalysis.authorAnalysis.topAuthorsByTotalCitations.slice(0, 5).map(author => ({
            name: author.name,
            totalCitations: author.totalCitations,
            hIndex: author.hIndex,
            publicationCount: author.publicationCount
          }))
        });
      }
      
      // Zeige Top-Institutionen
      if (fieldAnalysis.institutionAnalysis && !fieldAnalysis.institutionAnalysis.error) {
        logger.info('Top-Institutionen nach Zitationen', {
          institutions: fieldAnalysis.institutionAnalysis.topInstitutionsByTotalCitations.slice(0, 5).map(inst => ({
            name: inst.name,
            totalCitations: inst.totalCitations,
            publicationCount: inst.publicationCount,
            patentCount: inst.patentCount
          }))
        });
      }
      
      // Zeige Forschungstrends
      if (fieldAnalysis.trendAnalysis && !fieldAnalysis.trendAnalysis.error && fieldAnalysis.trendAnalysis.fieldTrends) {
        logger.info('Forschungstrends', {
          risingFields: fieldAnalysis.trendAnalysis.fieldTrends.risingFields.slice(0, 3).map(field => field.field),
          decliningFields: fieldAnalysis.trendAnalysis.fieldTrends.decliningFields.slice(0, 3).map(field => field.field)
        });
      }
      
      // Zeige Technologietransfer
      if (fieldAnalysis.techTransferAnalysis && !fieldAnalysis.techTransferAnalysis.error) {
        logger.info('Technologietransfer', {
          linkedCount: fieldAnalysis.techTransferAnalysis.linkedCount,
          avgTimeGapInDays: fieldAnalysis.techTransferAnalysis.timeGapAnalysis ? 
            fieldAnalysis.techTransferAnalysis.timeGapAnalysis.averageGapInDays : null
        });
      }
    } else {
      logger.error('Fehler bei der Analyse des Forschungsfeldes', {
        error: fieldAnalysis.error
      });
    }
    
    // Beispiel 2: Analyse einer bestimmten Institution
    logger.info('Beispiel 2: Analyse einer bestimmten Institution');
    
    const institutionAnalysis = await citationAnalyzer.performComprehensiveCitationAnalysis({
      institution: 'MIT',
      fromDate: '2018-01-01',
      limit: 100
    });
    
    if (institutionAnalysis.success) {
      logger.info('Umfassende Zitationsanalyse für Institution abgeschlossen', {
        institution: 'MIT',
        publicationsCount: institutionAnalysis.publicationsCount,
        patentsCount: institutionAnalysis.patentsCount
      });
      
      // Zeige Zusammenfassung
      logger.info('Analysezusammenfassung', {
        summary: institutionAnalysis.summary.institutionSummary
      });
      
      // Zeige Forschungsschwerpunkte
      if (institutionAnalysis.fieldAnalysis && !institutionAnalysis.fieldAnalysis.error) {
        logger.info('Forschungsschwerpunkte der Institution', {
          fields: institutionAnalysis.fieldAnalysis.topFieldsByTotalCitations.slice(0, 5).map(field => ({
            name: field.name,
            totalCitations: field.totalCitations,
            publicationCount: field.publicationCount
          }))
        });
      }
      
      // Zeige Kollaborationsnetzwerk
      if (institutionAnalysis.networkAnalysis && 
          institutionAnalysis.networkAnalysis.institutionNetwork &&
          institutionAnalysis.networkAnalysis.institutionNetwork.topCollaborators) {
        logger.info('Top-Kollaborationspartner der Institution', {
          collaborators: institutionAnalysis.networkAnalysis.institutionNetwork.topCollaborators.slice(0, 5).map(collab => ({
            institution: collab.institution,
            collaborationCount: collab.totalCollaborations
          }))
        });
      }
    } else {
      logger.error('Fehler bei der Analyse der Institution', {
        error: institutionAnalysis.error
      });
    }
    
    // Beispiel 3: Analyse eines bestimmten Autors
    logger.info('Beispiel 3: Analyse eines bestimmten Autors');
    
    const authorAnalysis = await citationAnalyzer.performComprehensiveCitationAnalysis({
      authorName: 'John Smith',
      limit: 50
    });
    
    if (authorAnalysis.success) {
      logger.info('Umfassende Zitationsanalyse für Autor abgeschlossen', {
        authorName: 'John Smith',
        publicationsCount: authorAnalysis.publicationsCount,
        patentsCount: authorAnalysis.patentsCount
      });
      
      // Zeige Zusammenfassung
      logger.info('Analysezusammenfassung', {
        summary: authorAnalysis.summary.authorSummary
      });
      
      // Zeige Publikationsmetriken
      if (authorAnalysis.authorAnalysis && !authorAnalysis.authorAnalysis.error) {
        const authorData = authorAnalysis.authorAnalysis.topAuthorsByTotalCitations.find(a => a.name.includes('John Smith'));
        
        if (authorData) {
          logger.info('Publikationsmetriken des Autors', {
            totalCitations: authorData.totalCitations,
            hIndex: authorData.hIndex,
            publicationCount: authorData.publicationCount,
            patentCount: authorData.patentCount
          });
        }
      }
      
      // Zeige Kollaborationsnetzwerk
      if (authorAnalysis.networkAnalysis && 
          authorAnalysis.networkAnalysis.authorNetwork &&
          authorAnalysis.networkAnalysis.authorNetwork.topCollaborators) {
        const authorCollabs = authorAnalysis.networkAnalysis.authorNetwork.topCollaborators.find(a => a.author.includes('John Smith'));
        
        if (authorCollabs) {
          logger.info('Top-Kollaborationspartner des Autors', {
            collaborators: authorCollabs.topCollaborators.map(collab => ({
              collaborator: collab.collaborator,
              collaborationCount: collab.count
            }))
          });
        }
      }
    } else {
      logger.error('Fehler bei der Analyse des Autors', {
        error: authorAnalysis.error
      });
    }
    
    // Beispiel 4: Analyse einer Fachzeitschrift
    logger.info('Beispiel 4: Analyse einer Fachzeitschrift');
    
    const journalAnalysis = await citationAnalyzer.performComprehensiveCitationAnalysis({
      journal: 'Nature',
      fromDate: '2020-01-01',
      limit: 100
    });
    
    if (journalAnalysis.success) {
      logger.info('Umfassende Zitationsanalyse für Fachzeitschrift abgeschlossen', {
        journal: 'Nature',
        publicationsCount: journalAnalysis.publicationsCount
      });
      
      // Zeige Journalmetriken
      if (journalAnalysis.journalAnalysis && !journalAnalysis.journalAnalysis.error) {
        const journalData = journalAnalysis.journalAnalysis.topJournalsByImpactFactor.find(j => j.name.includes('Nature'));
        
        if (journalData) {
          logger.info('Metriken der Fachzeitschrift', {
            impactFactor: journalData.impactFactor,
            totalCitations: journalData.totalCitations,
            publicationCount: journalData.publicationCount,
            avgCitationsPerPublication: journalData.avgCitationsPerPublication
          });
          
          if (journalData.researchFields && journalData.researchFields.length > 0) {
            logger.info('Hauptforschungsfelder der Fachzeitschrift', {
              fields: journalData.researchFields.map(field => ({
                field: field.field,
                count: field.count
              }))
            });
          }
        }
      }
    } else {
      logger.error('Fehler bei der Analyse der Fachzeitschrift', {
        error: journalAnalysis.error
      });
    }
    
    // Beispiel 5: Analyse des Technologietransfers in einem Zeitraum
    logger.info('Beispiel 5: Analyse des Technologietransfers in einem Zeitraum');
    
    const transferAnalysis = await citationAnalyzer.performComprehensiveCitationAnalysis({
      fromDate: '2018-01-01',
      toDate: '2023-12-31',
      limit: 200
    });
    
    if (transferAnalysis.success) {
      logger.info('Umfassende Analyse des Technologietransfers abgeschlossen', {
        publicationsCount: transferAnalysis.publicationsCount,
        patentsCount: transferAnalysis.patentsCount
      });
      
      // Zeige Zusammenfassung
      logger.info('Analysezusammenfassung', {
        summary: transferAnalysis.summary.techTransferSummary
      });
      
      // Zeige Technologietransfer
      if (transferAnalysis.techTransferAnalysis && !transferAnalysis.techTransferAnalysis.error) {
        logger.info('Technologietransfer-Metriken', {
          linkedCount: transferAnalysis.techTransferAnalysis.linkedCount,
          linkTypes: transferAnalysis.techTransferAnalysis.linkTypes
        });
        
        if (transferAnalysis.techTransferAnalysis.timeGapAnalysis) {
          logger.info('Zeitabstandsanalyse zwischen Publikationen und Patenten', {
            averageGapInDays: transferAnalysis.techTransferAnalysis.timeGapAnalysis.averageGapInDays,
            medianGapInDays: transferAnalysis.techTransferAnalysis.timeGapAnalysis.medianGapInDays,
            directionCounts: transferAnalysis.techTransferAnalysis.timeGapAnalysis.directionCounts,
            gapDistribution: transferAnalysis.techTransferAnalysis.timeGapAnalysis.gapDistribution
          });
        }
        
        if (transferAnalysis.techTransferAnalysis.topPublicationsWithPatents && 
            transferAnalysis.techTransferAnalysis.topPublicationsWithPatents.length > 0) {
          logger.info('Top-Publikationen mit Patentverknüpfungen', {
            publications: transferAnalysis.techTransferAnalysis.topPublicationsWithPatents.slice(0, 5).map(pub => ({
              title: pub.title,
              linkedPatentsCount: pub.linkedPatentsCount
            }))
          });
        }
        
        if (transferAnalysis.techTransferAnalysis.topPatentsWithPublications && 
            transferAnalysis.techTransferAnalysis.topPatentsWithPublications.length > 0) {
          logger.info('Top-Patente mit Publikationsverknüpfungen', {
            patents: transferAnalysis.techTransferAnalysis.topPatentsWithPublications.slice(0, 5).map(patent => ({
              title: patent.title,
              linkedPublicationsCount: patent.linkedPublicationsCount
            }))
          });
        }
      }
    } else {
      logger.error('Fehler bei der Analyse des Technologietransfers', {
        error: transferAnalysis.error
      });
    }
    
    logger.info('ComprehensiveCitationAnalyzer-Beispiel abgeschlossen');
  } catch (error) {
    logger.error('Fehler im ComprehensiveCitationAnalyzer-Beispiel', {
      error: error.message,
      stack: error.stack
    });
  }
}

// Führe das Beispiel aus
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});/**
 * @fileoverview Beispiel für die Verwendung des ComprehensiveCitationAnalyzer
 * 
 * Dieses Beispiel zeigt, wie der ComprehensiveCitationAnalyzer verwendet werden kann, um
 * umfassende Zitationsanalysen durchzuführen, die Autoren, Institutionen, Fachgebiete und
 * Publikationen berücksichtigen.
 */

import ComprehensiveCitationAnalyzer from '../ai/agents/ComprehensiveCitationAnalyzer.js';
import { DatabaseService } from '../database/DatabaseService.js';
import { TextAnalysisService } from '../ai/services/TextAnalysisService.js';
import { logger } from '../utils/logger.js';

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  try {
    logger.info('Starte ComprehensiveCitationAnalyzer-Beispiel');
    
    // Initialisiere Dienste
    const databaseService = new DatabaseService();
    await databaseService.initialize();
    
    const textAnalysisService = new TextAnalysisService({
      apiKey: process.env.TEXT_ANALYSIS_API_KEY
    });
    await textAnalysisService.initialize();
    
    // Erstelle und initialisiere den ComprehensiveCitationAnalyzer
    const citationAnalyzer = new ComprehensiveCitationAnalyzer({
      databaseService,
      textAnalysisService,
      config: {
        maxKeywords: 20,
        minKeywordLength: 4,
        energyEfficient: true
      }
    });
    
    await citationAnalyzer.initialize();
    
    // Beispiel 1: Analyse eines bestimmten Forschungsfeldes
    logger.info('Beispiel 1: Analyse eines bestimmten Forschungsfeldes');
    
    const fieldAnalysis = await citationAnalyzer.performComprehensiveCitationAnalysis({
      researchField: 'Quantum Computing',
      fromDate: '2018-01-01',
      limit: 100
    });
    
    if (fieldAnalysis.success) {
      logger.info('Umfassende Zitationsanalyse für Forschungsfeld abgeschlossen', {
        researchField: 'Quantum Computing',
        publicationsCount: fieldAnalysis.publicationsCount,
        patentsCount: fieldAnalysis.patentsCount
      });
      
      // Zeige Zusammenfassung
      logger.info('Analysezusammenfassung', {
        summary: fieldAnalysis.summary.overallSummary
      });
      
      // Zeige Top-Autoren
      if (fieldAnalysis.authorAnalysis && !fieldAnalysis.authorAnalysis.error) {
        logger.info('Top-Autoren nach Zitationen', {
          authors: fieldAnalysis.authorAnalysis.topAuthorsByTotalCitations.slice(0, 5).map(author => ({
            name: author.name,
            totalCitations: author.totalCitations,
            hIndex: author.hIndex,
            publicationCount: author.publicationCount
          }))
        });
      }
      
      // Zeige Top-Institutionen
      if (fieldAnalysis.institutionAnalysis && !fieldAnalysis.institutionAnalysis.error) {
        logger.info('Top-Institutionen nach Zitationen', {
          institutions: fieldAnalysis.institutionAnalysis.topInstitutionsByTotalCitations.slice(0, 5).map(inst => ({
            name: inst.name,
            totalCitations: inst.totalCitations,
            publicationCount: inst.publicationCount,
            patentCount: inst.patentCount
          }))
        });
      }
      
      // Zeige Forschungstrends
      if (fieldAnalysis.trendAnalysis && !fieldAnalysis.trendAnalysis.error && fieldAnalysis.trendAnalysis.fieldTrends) {
        logger.info('Forschungstrends', {
          risingFields: fieldAnalysis.trendAnalysis.fieldTrends.risingFields.slice(0, 3).map(field => field.field),
          decliningFields: fieldAnalysis.trendAnalysis.fieldTrends.decliningFields.slice(0, 3).map(field => field.field)
        });
      }
      
      // Zeige Technologietransfer
      if (fieldAnalysis.techTransferAnalysis && !fieldAnalysis.techTransferAnalysis.error) {
        logger.info('Technologietransfer', {
          linkedCount: fieldAnalysis.techTransferAnalysis.linkedCount,
          avgTimeGapInDays: fieldAnalysis.techTransferAnalysis.timeGapAnalysis ? 
            fieldAnalysis.techTransferAnalysis.timeGapAnalysis.averageGapInDays : null
        });
      }
    } else {
      logger.error('Fehler bei der Analyse des Forschungsfeldes', {
        error: fieldAnalysis.error
      });
    }
    
    // Beispiel 2: Analyse einer bestimmten Institution
    logger.info('Beispiel 2: Analyse einer bestimmten Institution');
    
    const institutionAnalysis = await citationAnalyzer.performComprehensiveCitationAnalysis({
      institution: 'MIT',
      fromDate: '2018-01-01',
      limit: 100
    });
    
    if (institutionAnalysis.success) {
      logger.info('Umfassende Zitationsanalyse für Institution abgeschlossen', {
        institution: 'MIT',
        publicationsCount: institutionAnalysis.publicationsCount,
        patentsCount: institutionAnalysis.patentsCount
      });
      
      // Zeige Zusammenfassung
      logger.info('Analysezusammenfassung', {
        summary: institutionAnalysis.summary.institutionSummary
      });
      
      // Zeige Forschungsschwerpunkte
      if (institutionAnalysis.fieldAnalysis && !institutionAnalysis.fieldAnalysis.error) {
        logger.info('Forschungsschwerpunkte der Institution', {
          fields: institutionAnalysis.fieldAnalysis.topFieldsByTotalCitations.slice(0, 5).map(field => ({
            name: field.name,
            totalCitations: field.totalCitations,
            publicationCount: field.publicationCount
          }))
        });
      }
      
      // Zeige Kollaborationsnetzwerk
      if (institutionAnalysis.networkAnalysis && 
          institutionAnalysis.networkAnalysis.institutionNetwork &&
          institutionAnalysis.networkAnalysis.institutionNetwork.topCollaborators) {
        logger.info('Top-Kollaborationspartner der Institution', {
          collaborators: institutionAnalysis.networkAnalysis.institutionNetwork.topCollaborators.slice(0, 5).map(collab => ({
            institution: collab.institution,
            collaborationCount: collab.totalCollaborations
          }))
        });
      }
    } else {
      logger.error('Fehler bei der Analyse der Institution', {
        error: institutionAnalysis.error
      });
    }
    
    // Beispiel 3: Analyse eines bestimmten Autors
    logger.info('Beispiel 3: Analyse eines bestimmten Autors');
    
    const authorAnalysis = await citationAnalyzer.performComprehensiveCitationAnalysis({
      authorName: 'John Smith',
      limit: 50
    });
    
    if (authorAnalysis.success) {
      logger.info('Umfassende Zitationsanalyse für Autor abgeschlossen', {
        authorName: 'John Smith',
        publicationsCount: authorAnalysis.publicationsCount,
        patentsCount: authorAnalysis.patentsCount
      });
      
      // Zeige Zusammenfassung
      logger.info('Analysezusammenfassung', {
        summary: authorAnalysis.summary.authorSummary
      });
      
      // Zeige Publikationsmetriken
      if (authorAnalysis.authorAnalysis && !authorAnalysis.authorAnalysis.error) {
        const authorData = authorAnalysis.authorAnalysis.topAuthorsByTotalCitations.find(a => a.name.includes('John Smith'));
        
        if (authorData) {
          logger.info('Publikationsmetriken des Autors', {
            totalCitations: authorData.totalCitations,
            hIndex: authorData.hIndex,
            publicationCount: authorData.publicationCount,
            patentCount: authorData.patentCount
          });
        }
      }
      
      // Zeige Kollaborationsnetzwerk
      if (authorAnalysis.networkAnalysis && 
          authorAnalysis.networkAnalysis.authorNetwork &&
          authorAnalysis.networkAnalysis.authorNetwork.topCollaborators) {
        const authorCollabs = authorAnalysis.networkAnalysis.authorNetwork.topCollaborators.find(a => a.author.includes('John Smith'));
        
        if (authorCollabs) {
          logger.info('Top-Kollaborationspartner des Autors', {
            collaborators: authorCollabs.topCollaborators.map(collab => ({
              collaborator: collab.collaborator,
              collaborationCount: collab.count
            }))
          });
        }
      }
    } else {
      logger.error('Fehler bei der Analyse des Autors', {
        error: authorAnalysis.error
      });
    }
    
    // Beispiel 4: Analyse einer Fachzeitschrift
    logger.info('Beispiel 4: Analyse einer Fachzeitschrift');
    
    const journalAnalysis = await citationAnalyzer.performComprehensiveCitationAnalysis({
      journal: 'Nature',
      fromDate: '2020-01-01',
      limit: 100
    });
    
    if (journalAnalysis.success) {
      logger.info('Umfassende Zitationsanalyse für Fachzeitschrift abgeschlossen', {
        journal: 'Nature',
        publicationsCount: journalAnalysis.publicationsCount
      });
      
      // Zeige Journalmetriken
      if (journalAnalysis.journalAnalysis && !journalAnalysis.journalAnalysis.error) {
        const journalData = journalAnalysis.journalAnalysis.topJournalsByImpactFactor.find(j => j.name.includes('Nature'));
        
        if (journalData) {
          logger.info('Metriken der Fachzeitschrift', {
            impactFactor: journalData.impactFactor,
            totalCitations: journalData.totalCitations,
            publicationCount: journalData.publicationCount,
            avgCitationsPerPublication: journalData.avgCitationsPerPublication
          });
          
          if (journalData.researchFields && journalData.researchFields.length > 0) {
            logger.info('Hauptforschungsfelder der Fachzeitschrift', {
              fields: journalData.researchFields.map(field => ({
                field: field.field,
                count: field.count
              }))
            });
          }
        }
      }
    } else {
      logger.error('Fehler bei der Analyse der Fachzeitschrift', {
        error: journalAnalysis.error
      });
    }
    
    // Beispiel 5: Analyse des Technologietransfers in einem Zeitraum
    logger.info('Beispiel 5: Analyse des Technologietransfers in einem Zeitraum');
    
    const transferAnalysis = await citationAnalyzer.performComprehensiveCitationAnalysis({
      fromDate: '2018-01-01',
      toDate: '2023-12-31',
      limit: 200
    });
    
    if (transferAnalysis.success) {
      logger.info('Umfassende Analyse des Technologietransfers abgeschlossen', {
        publicationsCount: transferAnalysis.publicationsCount,
        patentsCount: transferAnalysis.patentsCount
      });
      
      // Zeige Zusammenfassung
      logger.info('Analysezusammenfassung', {
        summary: transferAnalysis.summary.techTransferSummary
      });
      
      // Zeige Technologietransfer
      if (transferAnalysis.techTransferAnalysis && !transferAnalysis.techTransferAnalysis.error) {
        logger.info('Technologietransfer-Metriken', {
          linkedCount: transferAnalysis.techTransferAnalysis.linkedCount,
          linkTypes: transferAnalysis.techTransferAnalysis.linkTypes
        });
        
        if (transferAnalysis.techTransferAnalysis.timeGapAnalysis) {
          logger.info('Zeitabstandsanalyse zwischen Publikationen und Patenten', {
            averageGapInDays: transferAnalysis.techTransferAnalysis.timeGapAnalysis.averageGapInDays,
            medianGapInDays: transferAnalysis.techTransferAnalysis.timeGapAnalysis.medianGapInDays,
            directionCounts: transferAnalysis.techTransferAnalysis.timeGapAnalysis.directionCounts,
            gapDistribution: transferAnalysis.techTransferAnalysis.timeGapAnalysis.gapDistribution
          });
        }
        
        if (transferAnalysis.techTransferAnalysis.topPublicationsWithPatents && 
            transferAnalysis.techTransferAnalysis.topPublicationsWithPatents.length > 0) {
          logger.info('Top-Publikationen mit Patentverknüpfungen', {
            publications: transferAnalysis.techTransferAnalysis.topPublicationsWithPatents.slice(0, 5).map(pub => ({
              title: pub.title,
              linkedPatentsCount: pub.linkedPatentsCount
            }))
          });
        }
        
        if (transferAnalysis.techTransferAnalysis.topPatentsWithPublications && 
            transferAnalysis.techTransferAnalysis.topPatentsWithPublications.length > 0) {
          logger.info('Top-Patente mit Publikationsverknüpfungen', {
            patents: transferAnalysis.techTransferAnalysis.topPatentsWithPublications.slice(0, 5).map(patent => ({
              title: patent.title,
              linkedPublicationsCount: patent.linkedPublicationsCount
            }))
          });
        }
      }
    } else {
      logger.error('Fehler bei der Analyse des Technologietransfers', {
        error: transferAnalysis.error
      });
    }
    
    logger.info('ComprehensiveCitationAnalyzer-Beispiel abgeschlossen');
  } catch (error) {
    logger.error('Fehler im ComprehensiveCitationAnalyzer-Beispiel', {
      error: error.message,
      stack: error.stack
    });
  }
}

// Führe das Beispiel aus
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});