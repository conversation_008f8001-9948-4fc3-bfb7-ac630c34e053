/**
 * @fileoverview Beispiel für die Verwendung des ResearchLearningAgent
 * 
 * Dieses Beispiel zeigt, wie der ResearchLearningAgent in die DeSci-Scholar-Plattform
 * integriert werden kann, um aus wissenschaftlichen Publikationen zu lernen und
 * Forschern bei ihrer Arbeit zu helfen.
 */

import ResearchLearningAgent from '../ai/agents/ResearchLearningAgent.js';
import { DatabaseService } from '../database/DatabaseService.js';
import { VectorDatabase } from '../database/VectorDatabase.js';
import { TextEmbeddingService } from '../ai/TextEmbeddingService.js';
import { logger } from '../utils/logger.js';

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  try {
    logger.info('Starte ResearchLearningAgent-Beispiel');
    
    // Initialisiere Dienste
    const databaseService = new DatabaseService();
    await databaseService.initialize();
    
    const vectorDatabase = new VectorDatabase();
    await vectorDatabase.initialize();
    
    const textEmbeddingService = new TextEmbeddingService();
    await textEmbeddingService.initialize();
    
    // Erstelle und initialisiere den ResearchLearningAgent
    const researchLearningAgent = new ResearchLearningAgent({
      databaseService,
      vectorDatabase,
      textEmbeddingService,
      config: {
        maxContextSize: 10000,
        minRelevanceScore: 0.65,
        maxPublicationsPerQuery: 15
      }
    });
    
    await researchLearningAgent.initialize();
    
    // Beispiel 1: Aktualisiere die Wissensbasis mit neuen Publikationen
    logger.info('Beispiel 1: Aktualisiere die Wissensbasis');
    
    // Lade die neuesten Publikationen
    const recentPublications = await databaseService.query(
      'SELECT * FROM publications ORDER BY published_at DESC LIMIT 50'
    );
    
    // Aktualisiere die Wissensbasis
    const updateResult = await researchLearningAgent.updateKnowledgeBase(recentPublications);
    
    logger.info('Wissensbasis aktualisiert', updateResult);
    
    // Beispiel 2: Beantworte eine Forschungsfrage
    logger.info('Beispiel 2: Beantworte eine Forschungsfrage');
    
    const question = 'Welche Fortschritte gibt es bei der Anwendung von Quantencomputing in der Materialwissenschaft?';
    
    const answerResult = await researchLearningAgent.answerResearchQuestion(question, {
      temperature: 0.3,
      maxTokens: 2000
    });
    
    logger.info('Antwort auf Forschungsfrage', {
      question,
      answer: answerResult.answer.substring(0, 100) + '...',
      sourcesCount: answerResult.sources.length
    });
    
    // Beispiel 3: Identifiziere Forschungstrends
    logger.info('Beispiel 3: Identifiziere Forschungstrends');
    
    const trendsResult = await researchLearningAgent.identifyResearchTrends({
      limit: 100
    });
    
    logger.info('Identifizierte Forschungstrends', {
      emergingTrendsCount: trendsResult.trends.emergingTrends.length,
      methodologicalTrendsCount: trendsResult.trends.methodologicalTrends.length,
      interdisciplinaryConnectionsCount: trendsResult.trends.interdisciplinaryConnections.length,
      futureTrendsCount: trendsResult.trends.futureTrends.length
    });
    
    // Beispiel 4: Generiere Forschungsempfehlungen für einen Forscher
    logger.info('Beispiel 4: Generiere Forschungsempfehlungen');
    
    // Lade einen Forscher
    const researcher = await databaseService.query(
      'SELECT * FROM researchers WHERE id = ?',
      [1] // Beispiel-ID
    );
    
    if (researcher && researcher.length > 0) {
      const recommendationsResult = await researchLearningAgent.generateResearchRecommendations(researcher[0]);
      
      logger.info('Generierte Forschungsempfehlungen', {
        researcherId: researcher[0].id,
        researchDirectionsCount: recommendationsResult.recommendations.researchDirections.length,
        methodologicalRecommendationsCount: recommendationsResult.recommendations.methodologicalRecommendations.length,
        researchQuestionsCount: recommendationsResult.recommendations.researchQuestions.length,
        collaborationOpportunitiesCount: recommendationsResult.recommendations.collaborationOpportunities.length
      });
    }
    
    logger.info('ResearchLearningAgent-Beispiel abgeschlossen');
  } catch (error) {
    logger.error('Fehler im ResearchLearningAgent-Beispiel', {
      error: error.message,
      stack: error.stack
    });
  }
}

// Führe das Beispiel aus
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});/**
 * @fileoverview Beispiel für die Verwendung des ResearchLearningAgent
 * 
 * Dieses Beispiel zeigt, wie der ResearchLearningAgent in die DeSci-Scholar-Plattform
 * integriert werden kann, um aus wissenschaftlichen Publikationen zu lernen und
 * Forschern bei ihrer Arbeit zu helfen.
 */

import ResearchLearningAgent from '../ai/agents/ResearchLearningAgent.js';
import { DatabaseService } from '../database/DatabaseService.js';
import { VectorDatabase } from '../database/VectorDatabase.js';
import { TextEmbeddingService } from '../ai/TextEmbeddingService.js';
import { logger } from '../utils/logger.js';

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  try {
    logger.info('Starte ResearchLearningAgent-Beispiel');
    
    // Initialisiere Dienste
    const databaseService = new DatabaseService();
    await databaseService.initialize();
    
    const vectorDatabase = new VectorDatabase();
    await vectorDatabase.initialize();
    
    const textEmbeddingService = new TextEmbeddingService();
    await textEmbeddingService.initialize();
    
    // Erstelle und initialisiere den ResearchLearningAgent
    const researchLearningAgent = new ResearchLearningAgent({
      databaseService,
      vectorDatabase,
      textEmbeddingService,
      config: {
        maxContextSize: 10000,
        minRelevanceScore: 0.65,
        maxPublicationsPerQuery: 15
      }
    });
    
    await researchLearningAgent.initialize();
    
    // Beispiel 1: Aktualisiere die Wissensbasis mit neuen Publikationen
    logger.info('Beispiel 1: Aktualisiere die Wissensbasis');
    
    // Lade die neuesten Publikationen
    const recentPublications = await databaseService.query(
      'SELECT * FROM publications ORDER BY published_at DESC LIMIT 50'
    );
    
    // Aktualisiere die Wissensbasis
    const updateResult = await researchLearningAgent.updateKnowledgeBase(recentPublications);
    
    logger.info('Wissensbasis aktualisiert', updateResult);
    
    // Beispiel 2: Beantworte eine Forschungsfrage
    logger.info('Beispiel 2: Beantworte eine Forschungsfrage');
    
    const question = 'Welche Fortschritte gibt es bei der Anwendung von Quantencomputing in der Materialwissenschaft?';
    
    const answerResult = await researchLearningAgent.answerResearchQuestion(question, {
      temperature: 0.3,
      maxTokens: 2000
    });
    
    logger.info('Antwort auf Forschungsfrage', {
      question,
      answer: answerResult.answer.substring(0, 100) + '...',
      sourcesCount: answerResult.sources.length
    });
    
    // Beispiel 3: Identifiziere Forschungstrends
    logger.info('Beispiel 3: Identifiziere Forschungstrends');
    
    const trendsResult = await researchLearningAgent.identifyResearchTrends({
      limit: 100
    });
    
    logger.info('Identifizierte Forschungstrends', {
      emergingTrendsCount: trendsResult.trends.emergingTrends.length,
      methodologicalTrendsCount: trendsResult.trends.methodologicalTrends.length,
      interdisciplinaryConnectionsCount: trendsResult.trends.interdisciplinaryConnections.length,
      futureTrendsCount: trendsResult.trends.futureTrends.length
    });
    
    // Beispiel 4: Generiere Forschungsempfehlungen für einen Forscher
    logger.info('Beispiel 4: Generiere Forschungsempfehlungen');
    
    // Lade einen Forscher
    const researcher = await databaseService.query(
      'SELECT * FROM researchers WHERE id = ?',
      [1] // Beispiel-ID
    );
    
    if (researcher && researcher.length > 0) {
      const recommendationsResult = await researchLearningAgent.generateResearchRecommendations(researcher[0]);
      
      logger.info('Generierte Forschungsempfehlungen', {
        researcherId: researcher[0].id,
        researchDirectionsCount: recommendationsResult.recommendations.researchDirections.length,
        methodologicalRecommendationsCount: recommendationsResult.recommendations.methodologicalRecommendations.length,
        researchQuestionsCount: recommendationsResult.recommendations.researchQuestions.length,
        collaborationOpportunitiesCount: recommendationsResult.recommendations.collaborationOpportunities.length
      });
    }
    
    logger.info('ResearchLearningAgent-Beispiel abgeschlossen');
  } catch (error) {
    logger.error('Fehler im ResearchLearningAgent-Beispiel', {
      error: error.message,
      stack: error.stack
    });
  }
}

// Führe das Beispiel aus
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});/**
 * @fileoverview Beispiel für die Verwendung des ResearchLearningAgent
 * 
 * Dieses Beispiel zeigt, wie der ResearchLearningAgent in die DeSci-Scholar-Plattform
 * integriert werden kann, um aus wissenschaftlichen Publikationen zu lernen und
 * Forschern bei ihrer Arbeit zu helfen.
 */

import ResearchLearningAgent from '../ai/agents/ResearchLearningAgent.js';
import { DatabaseService } from '../database/DatabaseService.js';
import { VectorDatabase } from '../database/VectorDatabase.js';
import { TextEmbeddingService } from '../ai/TextEmbeddingService.js';
import { logger } from '../utils/logger.js';

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  try {
    logger.info('Starte ResearchLearningAgent-Beispiel');
    
    // Initialisiere Dienste
    const databaseService = new DatabaseService();
    await databaseService.initialize();
    
    const vectorDatabase = new VectorDatabase();
    await vectorDatabase.initialize();
    
    const textEmbeddingService = new TextEmbeddingService();
    await textEmbeddingService.initialize();
    
    // Erstelle und initialisiere den ResearchLearningAgent
    const researchLearningAgent = new ResearchLearningAgent({
      databaseService,
      vectorDatabase,
      textEmbeddingService,
      config: {
        maxContextSize: 10000,
        minRelevanceScore: 0.65,
        maxPublicationsPerQuery: 15
      }
    });
    
    await researchLearningAgent.initialize();
    
    // Beispiel 1: Aktualisiere die Wissensbasis mit neuen Publikationen
    logger.info('Beispiel 1: Aktualisiere die Wissensbasis');
    
    // Lade die neuesten Publikationen
    const recentPublications = await databaseService.query(
      'SELECT * FROM publications ORDER BY published_at DESC LIMIT 50'
    );
    
    // Aktualisiere die Wissensbasis
    const updateResult = await researchLearningAgent.updateKnowledgeBase(recentPublications);
    
    logger.info('Wissensbasis aktualisiert', updateResult);
    
    // Beispiel 2: Beantworte eine Forschungsfrage
    logger.info('Beispiel 2: Beantworte eine Forschungsfrage');
    
    const question = 'Welche Fortschritte gibt es bei der Anwendung von Quantencomputing in der Materialwissenschaft?';
    
    const answerResult = await researchLearningAgent.answerResearchQuestion(question, {
      temperature: 0.3,
      maxTokens: 2000
    });
    
    logger.info('Antwort auf Forschungsfrage', {
      question,
      answer: answerResult.answer.substring(0, 100) + '...',
      sourcesCount: answerResult.sources.length
    });
    
    // Beispiel 3: Identifiziere Forschungstrends
    logger.info('Beispiel 3: Identifiziere Forschungstrends');
    
    const trendsResult = await researchLearningAgent.identifyResearchTrends({
      limit: 100
    });
    
    logger.info('Identifizierte Forschungstrends', {
      emergingTrendsCount: trendsResult.trends.emergingTrends.length,
      methodologicalTrendsCount: trendsResult.trends.methodologicalTrends.length,
      interdisciplinaryConnectionsCount: trendsResult.trends.interdisciplinaryConnections.length,
      futureTrendsCount: trendsResult.trends.futureTrends.length
    });
    
    // Beispiel 4: Generiere Forschungsempfehlungen für einen Forscher
    logger.info('Beispiel 4: Generiere Forschungsempfehlungen');
    
    // Lade einen Forscher
    const researcher = await databaseService.query(
      'SELECT * FROM researchers WHERE id = ?',
      [1] // Beispiel-ID
    );
    
    if (researcher && researcher.length > 0) {
      const recommendationsResult = await researchLearningAgent.generateResearchRecommendations(researcher[0]);
      
      logger.info('Generierte Forschungsempfehlungen', {
        researcherId: researcher[0].id,
        researchDirectionsCount: recommendationsResult.recommendations.researchDirections.length,
        methodologicalRecommendationsCount: recommendationsResult.recommendations.methodologicalRecommendations.length,
        researchQuestionsCount: recommendationsResult.recommendations.researchQuestions.length,
        collaborationOpportunitiesCount: recommendationsResult.recommendations.collaborationOpportunities.length
      });
    }
    
    logger.info('ResearchLearningAgent-Beispiel abgeschlossen');
  } catch (error) {
    logger.error('Fehler im ResearchLearningAgent-Beispiel', {
      error: error.message,
      stack: error.stack
    });
  }
}

// Führe das Beispiel aus
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});