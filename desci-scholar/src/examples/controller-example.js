/**
 * Beispiel für die Verwendung des PublicationControllers
 * Demonstriert die API-Funktionen für Publikationsverwaltung mit dezentraler Speicherung
 */

import PublicationController from '../controllers/PublicationController.js.js';
import { toBuffer } from '../utils/buffer.js.js';

/**
 * Hauptfunktion für das PublicationController-Beispiel
 */
async function controllerExample() {
  console.log('=== DeSci Scholar - PublicationController-Demo ===');
  
  // Erstelle und initialisiere den PublicationController
  const controller = new PublicationController({
    // Konfigurationsoptionen können hier überschrieben werden
    dataCite: {
      testMode: true // Verwende den Test-Modus für DataCite
    }
  });
  
  console.log('Initialisiere Controller...');
  await controller.initialize();
  
  try {
    // 1. Erstelle eine neue Publikation
    console.log('\n1. Erstelle eine neue Publikation');
    const createResult = await controller.createPublication({
      title: 'Anwendung dezentraler Speichernetze in der wissenschaftlichen Kommunikation',
      authors: ['Dr. <PERSON>', 'Prof. <PERSON>'],
      abstract: 'Diese Arbeit untersucht die Potenziale dezentraler Speichertechnologien für wissenschaftliche Publikationen und Forschungsdaten, insbesondere im Hinblick auf verbesserte Verfügbarkeit, reduzierte Kosten und erhöhte Zensurresistenz.',
      content: `# Einleitung

Die wissenschaftliche Kommunikation basiert seit Jahrhunderten auf dem Prinzip des Teilens von Erkenntnissen durch Publikationen. Während sich die Medien von Druckerzeugnissen zu digitalen Formaten gewandelt haben, blieb ein grundlegendes Problem bestehen: die Zentralisierung der Speicherung und Verteilung.

## Dezentrale Ansätze

Dezentrale Speichertechnologien wie IPFS (InterPlanetary File System) und BitTorrent bieten vielversprechende Alternativen zu zentralisierten Repositorien. Diese Technologien verteilen die Speicherlast auf zahlreiche Knoten und erhöhen damit die Ausfallsicherheit bei gleichzeitiger Kostenreduktion.

## Methodik

In unserer Studie haben wir ein hybrides Speichersystem entwickelt, das sowohl IPFS als auch BitTorrent nutzt, um wissenschaftliche Publikationen und zugehörige Daten zu speichern und zu verteilen. Wir haben die Leistung des Systems in verschiedenen Szenarien getestet und mit traditionellen zentralisierten Lösungen verglichen.`,
      keywords: ['IPFS', 'BitTorrent', 'Dezentrale Systeme', 'Open Science', 'Wissenschaftliche Kommunikation'],
      license: 'CC BY 4.0'
    });
    
    if (!createResult.success) {
      throw new Error(`Publikation konnte nicht erstellt werden: ${createResult.message}`);
    }
    
    console.log('Publikation erfolgreich erstellt:');
    console.log('ID:', createResult.id);
    console.log('Titel:', createResult.publication.title);
    
    // Speichere die Publikations-ID für späteren Gebrauch
    const publicationId = createResult.id;
    
    // 2. Füge eine Datei zur Publikation hinzu
    console.log('\n2. Füge eine Datei zur Publikation hinzu');
    
    // Simuliere eine PDF-Datei
    const pdfContent = toBuffer(`%PDF-1.4
1 0 obj
<</Type/Catalog/Pages 2 0 R>>
endobj
2 0 obj
<</Type/Pages/Kids[3 0 R]/Count 1>>
endobj
3 0 obj
<</Type/Page/MediaBox[0 0 595 842]/Resources<<>>>>
endobj
xref
0 4
********** 65535 f
********** 00000 n
********** 00000 n
********** 00000 n
trailer
<</Size 4/Root 1 0 R>>
startxref
178
%%EOF`);
    
    const fileInfo = {
      name: 'dezentrale-speichernetze.pdf',
      type: 'application/pdf'
      // Größe wird automatisch berechnet
    };
    
    const fileResult = await controller.uploadPublicationFile(
      publicationId,
      pdfContent,
      fileInfo
    );
    
    if (!fileResult.success) {
      throw new Error(`Datei konnte nicht hochgeladen werden: ${fileResult.message}`);
    }
    
    console.log('Datei erfolgreich hochgeladen:');
    console.log('Dateiname:', fileResult.file.name);
    console.log('IPFS URL:', fileResult.urls.ipfs);
    console.log('BitTorrent Magnet-Link:', fileResult.urls.bittorrent);
    
    // 3. Veröffentliche die Publikation mit DOI-Registrierung
    console.log('\n3. Veröffentliche die Publikation mit DOI-Registrierung');
    
    const publishResult = await controller.publishPublication(publicationId, true);
    
    if (!publishResult.success) {
      throw new Error(`Publikation konnte nicht veröffentlicht werden: ${publishResult.message}`);
    }
    
    console.log('Publikation erfolgreich veröffentlicht:');
    console.log('Status:', publishResult.publication.status);
    console.log('DOI:', publishResult.doi);
    console.log('DOI registriert:', publishResult.doiRegistered);
    if (publishResult.doiMessage) {
      console.log('DOI-Nachricht:', publishResult.doiMessage);
    }
    
    // 4. Hole Publikationsdetails
    console.log('\n4. Hole Publikationsdetails');
    
    const getResult = await controller.getPublication(publicationId, true);
    
    if (!getResult.success) {
      throw new Error(`Publikation konnte nicht abgerufen werden: ${getResult.message}`);
    }
    
    console.log('Publikation erfolgreich abgerufen:');
    console.log('Titel:', getResult.publication.title);
    console.log('Autoren:', getResult.publication.authors.join(', '));
    console.log('DOI:', getResult.publication.doi);
    console.log('Status:', getResult.publication.status);
    console.log('Aus Cache geladen:', getResult.fromCache || false);
    console.log('Content-Länge:', getResult.publication.content.length, 'Zeichen');
    
    // 5. Aktualisiere die Publikation
    console.log('\n5. Aktualisiere die Publikation (neue Version)');
    
    const updateResult = await controller.updatePublication(
      publicationId,
      {
        abstract: 'Diese überarbeitete Studie präsentiert neue Erkenntnisse zu dezentralen Speichertechnologien und ihrer Anwendung in der wissenschaftlichen Kommunikation. Die Ergebnisse unserer Langzeitbeobachtungen unterstreichen die Vorteile dieser Technologien.',
        keywords: [...getResult.publication.keywords, 'Nachhaltigkeit', 'Langzeitarchivierung']
      },
      true // Erstelle eine neue Version
    );
    
    if (!updateResult.success) {
      throw new Error(`Publikation konnte nicht aktualisiert werden: ${updateResult.message}`);
    }
    
    console.log('Publikation erfolgreich aktualisiert:');
    console.log('Neue Version:', updateResult.version);
    console.log('Abstract:', updateResult.publication.abstract.substring(0, 100) + '...');
    console.log('Schlüsselwörter:', updateResult.publication.keywords.join(', '));
    
    // 6. Lade die Publikationsdatei herunter
    console.log('\n6. Lade die Publikationsdatei herunter');
    
    const downloadResult = await controller.downloadPublicationFile(publicationId, 'buffer');
    
    if (!downloadResult.success) {
      throw new Error(`Datei konnte nicht heruntergeladen werden: ${downloadResult.message}`);
    }
    
    console.log('Datei erfolgreich heruntergeladen:');
    console.log('Dateiname:', downloadResult.filename);
    console.log('Größe:', downloadResult.fileSize, 'Bytes');
    console.log('Typ:', downloadResult.fileType);
    console.log('Format:', downloadResult.format);
    console.log('Inhalt (Ausschnitt):', downloadResult.data.toString().substring(0, 50) + '...');
    
    // 7. Suche nach Publikationen
    console.log('\n7. Suche nach Publikationen');
    
    const searchResults = await controller.searchPublications({
      keywords: ['Dezentrale Systeme', 'IPFS']
    });
    
    if (!searchResults.success) {
      throw new Error(`Suche fehlgeschlagen: ${searchResults.message}`);
    }
    
    console.log('Suchergebnisse:');
    console.log('Anzahl Ergebnisse:', searchResults.results.length);
    
    searchResults.results.forEach((pub, index) => {
      console.log(`\nErgebnis #${index + 1}:`);
      console.log('Titel:', pub.title);
      console.log('Autoren:', pub.authors.join(', '));
      console.log('Status:', pub.status);
      console.log('DOI:', pub.doi);
    });
    
  } catch (error) {
    console.error('Fehler im Controller-Beispiel:', error);
  } finally {
    // Beende den Controller ordnungsgemäß
    console.log('\nBeende den PublicationController...');
    await controller.shutdown();
    
    console.log('\nController-Demo abgeschlossen!');
  }
}

// Starte das Beispiel, wenn das Skript direkt ausgeführt wird
// In ESM wird dies anders gehandhabt als in CommonJS
const isMainModule = import.meta.url === `file://${process.argv[1]}`;
if (isMainModule) {
  controllerExample().catch(error => {
    console.error('Unbehandelter Fehler in der Controller-Demo:', error);
    process.exit(1);
  });
}

export default controllerExample; 