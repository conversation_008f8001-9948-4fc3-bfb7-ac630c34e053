/**
 * @fileoverview Beispiel für die Verwendung des CitationAnalysisAgent
 * 
 * Dieses Be<PERSON> zeigt, wie der CitationAnalysisAgent verwendet werden kann,
 * um Zitationen zu analysieren und mit NFTs zu verknüpfen.
 */

import { CitationAnalysisAgent } from '../ai/agents/CitationAnalysisAgent.js';
import { DatabaseService } from '../services/DatabaseService.js';
import { BlockchainService } from '../services/BlockchainService.js';
import { VectorDatabaseService } from '../services/VectorDatabaseService.js';
import { TextEmbeddingService } from '../services/TextEmbeddingService.js';
import { IPFSService } from '../services/IPFSService.js';
import { logger } from '../utils/logger.js';
import fs from 'fs/promises';
import path from 'path';

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  try {
    logger.info('Starte CitationAnalysisAgent-Beispiel');
    
    // Initialisiere Dienste
    const databaseService = new DatabaseService();
    await databaseService.initialize();
    
    const blockchainService = new BlockchainService({
      nodeUrl: process.env.BLOCKCHAIN_NODE_URL,
      privateKey: process.env.BLOCKCHAIN_PRIVATE_KEY
    });
    await blockchainService.initialize();
    
    const vectorDatabase = new VectorDatabaseService({
      url: process.env.VECTOR_DB_URL,
      apiKey: process.env.VECTOR_DB_API_KEY
    });
    await vectorDatabase.initialize();
    
    const textEmbeddingService = new TextEmbeddingService({
      apiKey: process.env.TEXT_EMBEDDING_API_KEY
    });
    await textEmbeddingService.initialize();
    
    const ipfsService = new IPFSService({
      host: process.env.IPFS_HOST || 'ipfs.infura.io',
      port: parseInt(process.env.IPFS_PORT || '5001'),
      protocol: process.env.IPFS_PROTOCOL || 'https'
    });
    await ipfsService.initialize();
    
    // Erstelle und initialisiere den CitationAnalysisAgent
    const citationAnalysisAgent = new CitationAnalysisAgent({
      databaseService,
      blockchainService,
      vectorDatabase,
      textEmbeddingService,
      ipfsService,
      config: {
        batchSize: 25,
        energyEfficient: true
      }
    });
    
    await citationAnalysisAgent.initialize();
    
    // Beispiel 1: Extrahiere Zitationen aus einer Publikation
    logger.info('Beispiel 1: Extrahiere Zitationen aus einer Publikation');
    
    // Suche nach einer Publikation
    const publications = await databaseService.query(
      'SELECT doi, title, full_text_path FROM publications LIMIT 1'
    );
    
    if (publications && publications.length > 0) {
      const publication = publications[0];
      
      // Lade den Volltext der Publikation
      const fullTextPath = publication.full_text_path;
      const fullText = await fs.readFile(fullTextPath, 'utf-8');
      
      const extractionResult = await citationAnalysisAgent.extractCitations(publication.doi, fullText);
      
      logger.info('Zitationen extrahiert', {
        success: extractionResult.success,
        doi: publication.doi,
        citationsCount: extractionResult.success ? extractionResult.citations.length : 0
      });
      
      if (extractionResult.success && extractionResult.citations.length > 0) {
        // Beispiel 2: Analysiere Zitationen
        logger.info('Beispiel 2: Analysiere Zitationen');
        
        const analysisResult = await citationAnalysisAgent.analyzeCitations(publication.doi);
        
        logger.info('Zitationen analysiert', {
          success: analysisResult.success,
          doi: publication.doi,
          metrics: analysisResult.success ? {
            hIndex: analysisResult.metrics.hIndex,
            i10Index: analysisResult.metrics.i10Index,
            totalCitations: analysisResult.metrics.totalCitations,
            averageCitationsPerYear: analysisResult.metrics.averageCitationsPerYear
          } : null
        });
        
        // Beispiel 3: Verknüpfe Zitationen mit NFTs (DOIs zu NFTs)
        logger.info('Beispiel 3: Verknüpfe Zitationen mit NFTs (DOIs zu NFTs)');
        
        const linkResult = await citationAnalysisAgent.linkCitationsToNFTs(publication.doi);
        
        logger.info('Zitationen mit NFTs verknüpft', {
          success: linkResult.success,
          doi: publication.doi,
          linkedCount: linkResult.success ? linkResult.linkedCitations.length : 0,
          totalCount: linkResult.success ? linkResult.totalCitations : 0
        });
        
        // Beispiel 4: Berechne den Einfluss einer Publikation
        logger.info('Beispiel 4: Berechne den Einfluss einer Publikation');
        
        const influenceResult = await citationAnalysisAgent.calculatePublicationInfluence(publication.doi);
        
        logger.info('Einfluss der Publikation berechnet', {
          success: influenceResult.success,
          doi: publication.doi,
          influenceScore: influenceResult.success ? influenceResult.influenceScore : null,
          percentile: influenceResult.success ? influenceResult.percentile : null
        });
        
        // Beispiel 5: Erstelle ein Zitationsnetzwerk
        logger.info('Beispiel 5: Erstelle ein Zitationsnetzwerk');
        
        const networkResult = await citationAnalysisAgent.createCitationNetwork(publication.doi, {
          depth: 2,
          maxNodes: 100
        });
        
        logger.info('Zitationsnetzwerk erstellt', {
          success: networkResult.success,
          doi: publication.doi,
          nodesCount: networkResult.success ? networkResult.network.nodes.length : 0,
          edgesCount: networkResult.success ? networkResult.network.edges.length : 0
        });
        
        if (networkResult.success) {
          // Speichere das Netzwerk in IPFS
          const networkJson = JSON.stringify(networkResult.network);
          const ipfsResult = await ipfsService.addContent(networkJson);
          
          logger.info('Zitationsnetzwerk in IPFS gespeichert', {
            success: ipfsResult.success,
            hash: ipfsResult.success ? ipfsResult.hash : null
          });
        }
        
        // Beispiel 6: Identifiziere Schlüsselreferenzen
        logger.info('Beispiel 6: Identifiziere Schlüsselreferenzen');
        
        const keyReferencesResult = await citationAnalysisAgent.identifyKeyReferences(publication.doi);
        
        logger.info('Schlüsselreferenzen identifiziert', {
          success: keyReferencesResult.success,
          doi: publication.doi,
          keyReferencesCount: keyReferencesResult.success ? keyReferencesResult.keyReferences.length : 0
        });
        
        // Beispiel 7: Verknüpfe Patent-Zitationen mit NFTs (Patent-IDs zu NFTs)
        logger.info('Beispiel 7: Verknüpfe Patent-Zitationen mit NFTs (Patent-IDs zu NFTs)');
        
        // Suche nach Patentzitationen in der Publikation
        const patentCitationsResult = await citationAnalysisAgent.extractPatentCitations(publication.doi, fullText);
        
        logger.info('Patent-Zitationen extrahiert', {
          success: patentCitationsResult.success,
          doi: publication.doi,
          patentCitationsCount: patentCitationsResult.success ? patentCitationsResult.patentCitations.length : 0
        });
        
        if (patentCitationsResult.success && patentCitationsResult.patentCitations.length > 0) {
          const linkPatentResult = await citationAnalysisAgent.linkPatentCitationsToNFTs(publication.doi);
          
          logger.info('Patent-Zitationen mit NFTs verknüpft', {
            success: linkPatentResult.success,
            doi: publication.doi,
            linkedCount: linkPatentResult.success ? linkPatentResult.linkedPatentCitations.length : 0,
            totalCount: linkPatentResult.success ? linkPatentResult.totalPatentCitations : 0
          });
        }
        
        // Beispiel 8: Analysiere Zitationskontext
        logger.info('Beispiel 8: Analysiere Zitationskontext');
        
        const contextResult = await citationAnalysisAgent.analyzeCitationContext(publication.doi);
        
        logger.info('Zitationskontext analysiert', {
          success: contextResult.success,
          doi: publication.doi,
          contextsAnalyzed: contextResult.success ? contextResult.contextsAnalyzed : 0
        });
        
        if (contextResult.success) {
          logger.info('Top-Zitationskontexte', {
            contexts: contextResult.topContexts.slice(0, 3).map(context => ({
              type: context.type,
              count: context.count,
              percentage: context.percentage
            }))
          });
        }
      }
    } else {
      logger.info('Keine Publikation gefunden');
    }
    
    logger.info('CitationAnalysisAgent-Beispiel abgeschlossen');
  } catch (error) {
    logger.error('Fehler im CitationAnalysisAgent-Beispiel', {
      error: error.message,
      stack: error.stack
    });
  }
}

// Führe das Beispiel aus
main();/**
 * @fileoverview Beispiel für die Verwendung des CitationAnalysisAgent
 * 
 * Dieses Beispiel zeigt, wie der CitationAnalysisAgent verwendet werden kann,
 * um Zitationen zu analysieren und mit NFTs zu verknüpfen.
 */

import { CitationAnalysisAgent } from '../ai/agents/CitationAnalysisAgent.js';
import { DatabaseService } from '../services/DatabaseService.js';
import { BlockchainService } from '../services/BlockchainService.js';
import { VectorDatabaseService } from '../services/VectorDatabaseService.js';
import { TextEmbeddingService } from '../services/TextEmbeddingService.js';
import { IPFSService } from '../services/IPFSService.js';
import { logger } from '../utils/logger.js';
import fs from 'fs/promises';
import path from 'path';

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  try {
    logger.info('Starte CitationAnalysisAgent-Beispiel');
    
    // Initialisiere Dienste
    const databaseService = new DatabaseService();
    await databaseService.initialize();
    
    const blockchainService = new BlockchainService({
      nodeUrl: process.env.BLOCKCHAIN_NODE_URL,
      privateKey: process.env.BLOCKCHAIN_PRIVATE_KEY
    });
    await blockchainService.initialize();
    
    const vectorDatabase = new VectorDatabaseService({
      url: process.env.VECTOR_DB_URL,
      apiKey: process.env.VECTOR_DB_API_KEY
    });
    await vectorDatabase.initialize();
    
    const textEmbeddingService = new TextEmbeddingService({
      apiKey: process.env.TEXT_EMBEDDING_API_KEY
    });
    await textEmbeddingService.initialize();
    
    const ipfsService = new IPFSService({
      host: process.env.IPFS_HOST || 'ipfs.infura.io',
      port: parseInt(process.env.IPFS_PORT || '5001'),
      protocol: process.env.IPFS_PROTOCOL || 'https'
    });
    await ipfsService.initialize();
    
    // Erstelle und initialisiere den CitationAnalysisAgent
    const citationAnalysisAgent = new CitationAnalysisAgent({
      databaseService,
      blockchainService,
      vectorDatabase,
      textEmbeddingService,
      ipfsService,
      config: {
        batchSize: 25,
        energyEfficient: true
      }
    });
    
    await citationAnalysisAgent.initialize();
    
    // Beispiel 1: Extrahiere Zitationen aus einer Publikation
    logger.info('Beispiel 1: Extrahiere Zitationen aus einer Publikation');
    
    // Suche nach einer Publikation
    const publications = await databaseService.query(
      'SELECT doi, title, full_text_path FROM publications LIMIT 1'
    );
    
    if (publications && publications.length > 0) {
      const publication = publications[0];
      
      // Lade den Volltext der Publikation
      const fullTextPath = publication.full_text_path;
      const fullText = await fs.readFile(fullTextPath, 'utf-8');
      
      const extractionResult = await citationAnalysisAgent.extractCitations(publication.doi, fullText);
      
      logger.info('Zitationen extrahiert', {
        success: extractionResult.success,
        doi: publication.doi,
        citationsCount: extractionResult.success ? extractionResult.citations.length : 0
      });
      
      if (extractionResult.success && extractionResult.citations.length > 0) {
        // Beispiel 2: Analysiere Zitationen
        logger.info('Beispiel 2: Analysiere Zitationen');
        
        const analysisResult = await citationAnalysisAgent.analyzeCitations(publication.doi);
        
        logger.info('Zitationen analysiert', {
          success: analysisResult.success,
          doi: publication.doi,
          metrics: analysisResult.success ? {
            hIndex: analysisResult.metrics.hIndex,
            i10Index: analysisResult.metrics.i10Index,
            totalCitations: analysisResult.metrics.totalCitations,
            averageCitationsPerYear: analysisResult.metrics.averageCitationsPerYear
          } : null
        });
        
        // Beispiel 3: Verknüpfe Zitationen mit NFTs (DOIs zu NFTs)
        logger.info('Beispiel 3: Verknüpfe Zitationen mit NFTs (DOIs zu NFTs)');
        
        const linkResult = await citationAnalysisAgent.linkCitationsToNFTs(publication.doi);
        
        logger.info('Zitationen mit NFTs verknüpft', {
          success: linkResult.success,
          doi: publication.doi,
          linkedCount: linkResult.success ? linkResult.linkedCitations.length : 0,
          totalCount: linkResult.success ? linkResult.totalCitations : 0
        });
        
        // Beispiel 4: Berechne den Einfluss einer Publikation
        logger.info('Beispiel 4: Berechne den Einfluss einer Publikation');
        
        const influenceResult = await citationAnalysisAgent.calculatePublicationInfluence(publication.doi);
        
        logger.info('Einfluss der Publikation berechnet', {
          success: influenceResult.success,
          doi: publication.doi,
          influenceScore: influenceResult.success ? influenceResult.influenceScore : null,
          percentile: influenceResult.success ? influenceResult.percentile : null
        });
        
        // Beispiel 5: Erstelle ein Zitationsnetzwerk
        logger.info('Beispiel 5: Erstelle ein Zitationsnetzwerk');
        
        const networkResult = await citationAnalysisAgent.createCitationNetwork(publication.doi, {
          depth: 2,
          maxNodes: 100
        });
        
        logger.info('Zitationsnetzwerk erstellt', {
          success: networkResult.success,
          doi: publication.doi,
          nodesCount: networkResult.success ? networkResult.network.nodes.length : 0,
          edgesCount: networkResult.success ? networkResult.network.edges.length : 0
        });
        
        if (networkResult.success) {
          // Speichere das Netzwerk in IPFS
          const networkJson = JSON.stringify(networkResult.network);
          const ipfsResult = await ipfsService.addContent(networkJson);
          
          logger.info('Zitationsnetzwerk in IPFS gespeichert', {
            success: ipfsResult.success,
            hash: ipfsResult.success ? ipfsResult.hash : null
          });
        }
        
        // Beispiel 6: Identifiziere Schlüsselreferenzen
        logger.info('Beispiel 6: Identifiziere Schlüsselreferenzen');
        
        const keyReferencesResult = await citationAnalysisAgent.identifyKeyReferences(publication.doi);
        
        logger.info('Schlüsselreferenzen identifiziert', {
          success: keyReferencesResult.success,
          doi: publication.doi,
          keyReferencesCount: keyReferencesResult.success ? keyReferencesResult.keyReferences.length : 0
        });
        
        // Beispiel 7: Verknüpfe Patent-Zitationen mit NFTs (Patent-IDs zu NFTs)
        logger.info('Beispiel 7: Verknüpfe Patent-Zitationen mit NFTs (Patent-IDs zu NFTs)');
        
        // Suche nach Patentzitationen in der Publikation
        const patentCitationsResult = await citationAnalysisAgent.extractPatentCitations(publication.doi, fullText);
        
        logger.info('Patent-Zitationen extrahiert', {
          success: patentCitationsResult.success,
          doi: publication.doi,
          patentCitationsCount: patentCitationsResult.success ? patentCitationsResult.patentCitations.length : 0
        });
        
        if (patentCitationsResult.success && patentCitationsResult.patentCitations.length > 0) {
          const linkPatentResult = await citationAnalysisAgent.linkPatentCitationsToNFTs(publication.doi);
          
          logger.info('Patent-Zitationen mit NFTs verknüpft', {
            success: linkPatentResult.success,
            doi: publication.doi,
            linkedCount: linkPatentResult.success ? linkPatentResult.linkedPatentCitations.length : 0,
            totalCount: linkPatentResult.success ? linkPatentResult.totalPatentCitations : 0
          });
        }
        
        // Beispiel 8: Analysiere Zitationskontext
        logger.info('Beispiel 8: Analysiere Zitationskontext');
        
        const contextResult = await citationAnalysisAgent.analyzeCitationContext(publication.doi);
        
        logger.info('Zitationskontext analysiert', {
          success: contextResult.success,
          doi: publication.doi,
          contextsAnalyzed: contextResult.success ? contextResult.contextsAnalyzed : 0
        });
        
        if (contextResult.success) {
          logger.info('Top-Zitationskontexte', {
            contexts: contextResult.topContexts.slice(0, 3).map(context => ({
              type: context.type,
              count: context.count,
              percentage: context.percentage
            }))
          });
        }
      }
    } else {
      logger.info('Keine Publikation gefunden');
    }
    
    logger.info('CitationAnalysisAgent-Beispiel abgeschlossen');
  } catch (error) {
    logger.error('Fehler im CitationAnalysisAgent-Beispiel', {
      error: error.message,
      stack: error.stack
    });
  }
}

// Führe das Beispiel aus
main();/**
 * @fileoverview Beispiel für die Verwendung des CitationAnalysisAgent
 * 
 * Dieses Beispiel zeigt, wie der CitationAnalysisAgent verwendet werden kann,
 * um Zitationen zu analysieren und mit NFTs zu verknüpfen.
 */

import { CitationAnalysisAgent } from '../ai/agents/CitationAnalysisAgent.js';
import { DatabaseService } from '../services/DatabaseService.js';
import { BlockchainService } from '../services/BlockchainService.js';
import { VectorDatabaseService } from '../services/VectorDatabaseService.js';
import { TextEmbeddingService } from '../services/TextEmbeddingService.js';
import { IPFSService } from '../services/IPFSService.js';
import { logger } from '../utils/logger.js';
import fs from 'fs/promises';
import path from 'path';

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  try {
    logger.info('Starte CitationAnalysisAgent-Beispiel');
    
    // Initialisiere Dienste
    const databaseService = new DatabaseService();
    await databaseService.initialize();
    
    const blockchainService = new BlockchainService({
      nodeUrl: process.env.BLOCKCHAIN_NODE_URL,
      privateKey: process.env.BLOCKCHAIN_PRIVATE_KEY
    });
    await blockchainService.initialize();
    
    const vectorDatabase = new VectorDatabaseService({
      url: process.env.VECTOR_DB_URL,
      apiKey: process.env.VECTOR_DB_API_KEY
    });
    await vectorDatabase.initialize();
    
    const textEmbeddingService = new TextEmbeddingService({
      apiKey: process.env.TEXT_EMBEDDING_API_KEY
    });
    await textEmbeddingService.initialize();
    
    const ipfsService = new IPFSService({
      host: process.env.IPFS_HOST || 'ipfs.infura.io',
      port: parseInt(process.env.IPFS_PORT || '5001'),
      protocol: process.env.IPFS_PROTOCOL || 'https'
    });
    await ipfsService.initialize();
    
    // Erstelle und initialisiere den CitationAnalysisAgent
    const citationAnalysisAgent = new CitationAnalysisAgent({
      databaseService,
      blockchainService,
      vectorDatabase,
      textEmbeddingService,
      ipfsService,
      config: {
        batchSize: 25,
        energyEfficient: true
      }
    });
    
    await citationAnalysisAgent.initialize();
    
    // Beispiel 1: Extrahiere Zitationen aus einer Publikation
    logger.info('Beispiel 1: Extrahiere Zitationen aus einer Publikation');
    
    // Suche nach einer Publikation
    const publications = await databaseService.query(
      'SELECT doi, title, full_text_path FROM publications LIMIT 1'
    );
    
    if (publications && publications.length > 0) {
      const publication = publications[0];
      
      // Lade den Volltext der Publikation
      const fullTextPath = publication.full_text_path;
      const fullText = await fs.readFile(fullTextPath, 'utf-8');
      
      const extractionResult = await citationAnalysisAgent.extractCitations(publication.doi, fullText);
      
      logger.info('Zitationen extrahiert', {
        success: extractionResult.success,
        doi: publication.doi,
        citationsCount: extractionResult.success ? extractionResult.citations.length : 0
      });
      
      if (extractionResult.success && extractionResult.citations.length > 0) {
        // Beispiel 2: Analysiere Zitationen
        logger.info('Beispiel 2: Analysiere Zitationen');
        
        const analysisResult = await citationAnalysisAgent.analyzeCitations(publication.doi);
        
        logger.info('Zitationen analysiert', {
          success: analysisResult.success,
          doi: publication.doi,
          metrics: analysisResult.success ? {
            hIndex: analysisResult.metrics.hIndex,
            i10Index: analysisResult.metrics.i10Index,
            totalCitations: analysisResult.metrics.totalCitations,
            averageCitationsPerYear: analysisResult.metrics.averageCitationsPerYear
          } : null
        });
        
        // Beispiel 3: Verknüpfe Zitationen mit NFTs (DOIs zu NFTs)
        logger.info('Beispiel 3: Verknüpfe Zitationen mit NFTs (DOIs zu NFTs)');
        
        const linkResult = await citationAnalysisAgent.linkCitationsToNFTs(publication.doi);
        
        logger.info('Zitationen mit NFTs verknüpft', {
          success: linkResult.success,
          doi: publication.doi,
          linkedCount: linkResult.success ? linkResult.linkedCitations.length : 0,
          totalCount: linkResult.success ? linkResult.totalCitations : 0
        });
        
        // Beispiel 4: Berechne den Einfluss einer Publikation
        logger.info('Beispiel 4: Berechne den Einfluss einer Publikation');
        
        const influenceResult = await citationAnalysisAgent.calculatePublicationInfluence(publication.doi);
        
        logger.info('Einfluss der Publikation berechnet', {
          success: influenceResult.success,
          doi: publication.doi,
          influenceScore: influenceResult.success ? influenceResult.influenceScore : null,
          percentile: influenceResult.success ? influenceResult.percentile : null
        });
        
        // Beispiel 5: Erstelle ein Zitationsnetzwerk
        logger.info('Beispiel 5: Erstelle ein Zitationsnetzwerk');
        
        const networkResult = await citationAnalysisAgent.createCitationNetwork(publication.doi, {
          depth: 2,
          maxNodes: 100
        });
        
        logger.info('Zitationsnetzwerk erstellt', {
          success: networkResult.success,
          doi: publication.doi,
          nodesCount: networkResult.success ? networkResult.network.nodes.length : 0,
          edgesCount: networkResult.success ? networkResult.network.edges.length : 0
        });
        
        if (networkResult.success) {
          // Speichere das Netzwerk in IPFS
          const networkJson = JSON.stringify(networkResult.network);
          const ipfsResult = await ipfsService.addContent(networkJson);
          
          logger.info('Zitationsnetzwerk in IPFS gespeichert', {
            success: ipfsResult.success,
            hash: ipfsResult.success ? ipfsResult.hash : null
          });
        }
        
        // Beispiel 6: Identifiziere Schlüsselreferenzen
        logger.info('Beispiel 6: Identifiziere Schlüsselreferenzen');
        
        const keyReferencesResult = await citationAnalysisAgent.identifyKeyReferences(publication.doi);
        
        logger.info('Schlüsselreferenzen identifiziert', {
          success: keyReferencesResult.success,
          doi: publication.doi,
          keyReferencesCount: keyReferencesResult.success ? keyReferencesResult.keyReferences.length : 0
        });
        
        // Beispiel 7: Verknüpfe Patent-Zitationen mit NFTs (Patent-IDs zu NFTs)
        logger.info('Beispiel 7: Verknüpfe Patent-Zitationen mit NFTs (Patent-IDs zu NFTs)');
        
        // Suche nach Patentzitationen in der Publikation
        const patentCitationsResult = await citationAnalysisAgent.extractPatentCitations(publication.doi, fullText);
        
        logger.info('Patent-Zitationen extrahiert', {
          success: patentCitationsResult.success,
          doi: publication.doi,
          patentCitationsCount: patentCitationsResult.success ? patentCitationsResult.patentCitations.length : 0
        });
        
        if (patentCitationsResult.success && patentCitationsResult.patentCitations.length > 0) {
          const linkPatentResult = await citationAnalysisAgent.linkPatentCitationsToNFTs(publication.doi);
          
          logger.info('Patent-Zitationen mit NFTs verknüpft', {
            success: linkPatentResult.success,
            doi: publication.doi,
            linkedCount: linkPatentResult.success ? linkPatentResult.linkedPatentCitations.length : 0,
            totalCount: linkPatentResult.success ? linkPatentResult.totalPatentCitations : 0
          });
        }
        
        // Beispiel 8: Analysiere Zitationskontext
        logger.info('Beispiel 8: Analysiere Zitationskontext');
        
        const contextResult = await citationAnalysisAgent.analyzeCitationContext(publication.doi);
        
        logger.info('Zitationskontext analysiert', {
          success: contextResult.success,
          doi: publication.doi,
          contextsAnalyzed: contextResult.success ? contextResult.contextsAnalyzed : 0
        });
        
        if (contextResult.success) {
          logger.info('Top-Zitationskontexte', {
            contexts: contextResult.topContexts.slice(0, 3).map(context => ({
              type: context.type,
              count: context.count,
              percentage: context.percentage
            }))
          });
        }
      }
    } else {
      logger.info('Keine Publikation gefunden');
    }
    
    logger.info('CitationAnalysisAgent-Beispiel abgeschlossen');
  } catch (error) {
    logger.error('Fehler im CitationAnalysisAgent-Beispiel', {
      error: error.message,
      stack: error.stack
    });
  }
}

// Führe das Beispiel aus
main();/**
 * @fileoverview Beispiel für die Verwendung des CitationAnalysisAgent
 * 
 * Dieses Beispiel zeigt, wie der CitationAnalysisAgent verwendet werden kann,
 * um Zitationen zu analysieren und mit NFTs zu verknüpfen.
 */

import { CitationAnalysisAgent } from '../ai/agents/CitationAnalysisAgent.js';
import { DatabaseService } from '../services/DatabaseService.js';
import { BlockchainService } from '../services/BlockchainService.js';
import { VectorDatabaseService } from '../services/VectorDatabaseService.js';
import { TextEmbeddingService } from '../services/TextEmbeddingService.js';
import { IPFSService } from '../services/IPFSService.js';
import { logger } from '../utils/logger.js';
import fs from 'fs/promises';
import path from 'path';

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  try {
    logger.info('Starte CitationAnalysisAgent-Beispiel');
    
    // Initialisiere Dienste
    const databaseService = new DatabaseService();
    await databaseService.initialize();
    
    const blockchainService = new BlockchainService({
      nodeUrl: process.env.BLOCKCHAIN_NODE_URL,
      privateKey: process.env.BLOCKCHAIN_PRIVATE_KEY
    });
    await blockchainService.initialize();
    
    const vectorDatabase = new VectorDatabaseService({
      url: process.env.VECTOR_DB_URL,
      apiKey: process.env.VECTOR_DB_API_KEY
    });
    await vectorDatabase.initialize();
    
    const textEmbeddingService = new TextEmbeddingService({
      apiKey: process.env.TEXT_EMBEDDING_API_KEY
    });
    await textEmbeddingService.initialize();
    
    const ipfsService = new IPFSService({
      host: process.env.IPFS_HOST || 'ipfs.infura.io',
      port: parseInt(process.env.IPFS_PORT || '5001'),
      protocol: process.env.IPFS_PROTOCOL || 'https'
    });
    await ipfsService.initialize();
    
    // Erstelle und initialisiere den CitationAnalysisAgent
    const citationAnalysisAgent = new CitationAnalysisAgent({
      databaseService,
      blockchainService,
      vectorDatabase,
      textEmbeddingService,
      ipfsService,
      config: {
        batchSize: 25,
        energyEfficient: true
      }
    });
    
    await citationAnalysisAgent.initialize();
    
    // Beispiel 1: Extrahiere Zitationen aus einer Publikation
    logger.info('Beispiel 1: Extrahiere Zitationen aus einer Publikation');
    
    // Suche nach einer Publikation
    const publications = await databaseService.query(
      'SELECT doi, title, full_text_path FROM publications LIMIT 1'
    );
    
    if (publications && publications.length > 0) {
      const publication = publications[0];
      
      // Lade den Volltext der Publikation
      const fullTextPath = publication.full_text_path;
      const fullText = await fs.readFile(fullTextPath, 'utf-8');
      
      const extractionResult = await citationAnalysisAgent.extractCitations(publication.doi, fullText);
      
      logger.info('Zitationen extrahiert', {
        success: extractionResult.success,
        doi: publication.doi,
        citationsCount: extractionResult.success ? extractionResult.citations.length : 0
      });
      
      if (extractionResult.success && extractionResult.citations.length > 0) {
        // Beispiel 2: Analysiere Zitationen
        logger.info('Beispiel 2: Analysiere Zitationen');
        
        const analysisResult = await citationAnalysisAgent.analyzeCitations(publication.doi);
        
        logger.info('Zitationen analysiert', {
          success: analysisResult.success,
          doi: publication.doi,
          metrics: analysisResult.success ? {
            hIndex: analysisResult.metrics.hIndex,
            i10Index: analysisResult.metrics.i10Index,
            totalCitations: analysisResult.metrics.totalCitations,
            averageCitationsPerYear: analysisResult.metrics.averageCitationsPerYear
          } : null
        });
        
        // Beispiel 3: Verknüpfe Zitationen mit NFTs (DOIs zu NFTs)
        logger.info('Beispiel 3: Verknüpfe Zitationen mit NFTs (DOIs zu NFTs)');
        
        const linkResult = await citationAnalysisAgent.linkCitationsToNFTs(publication.doi);
        
        logger.info('Zitationen mit NFTs verknüpft', {
          success: linkResult.success,
          doi: publication.doi,
          linkedCount: linkResult.success ? linkResult.linkedCitations.length : 0,
          totalCount: linkResult.success ? linkResult.totalCitations : 0
        });
        
        // Beispiel 4: Berechne den Einfluss einer Publikation
        logger.info('Beispiel 4: Berechne den Einfluss einer Publikation');
        
        const influenceResult = await citationAnalysisAgent.calculatePublicationInfluence(publication.doi);
        
        logger.info('Einfluss der Publikation berechnet', {
          success: influenceResult.success,
          doi: publication.doi,
          influenceScore: influenceResult.success ? influenceResult.influenceScore : null,
          percentile: influenceResult.success ? influenceResult.percentile : null
        });
        
        // Beispiel 5: Erstelle ein Zitationsnetzwerk
        logger.info('Beispiel 5: Erstelle ein Zitationsnetzwerk');
        
        const networkResult = await citationAnalysisAgent.createCitationNetwork(publication.doi, {
          depth: 2,
          maxNodes: 100
        });
        
        logger.info('Zitationsnetzwerk erstellt', {
          success: networkResult.success,
          doi: publication.doi,
          nodesCount: networkResult.success ? networkResult.network.nodes.length : 0,
          edgesCount: networkResult.success ? networkResult.network.edges.length : 0
        });
        
        if (networkResult.success) {
          // Speichere das Netzwerk in IPFS
          const networkJson = JSON.stringify(networkResult.network);
          const ipfsResult = await ipfsService.addContent(networkJson);
          
          logger.info('Zitationsnetzwerk in IPFS gespeichert', {
            success: ipfsResult.success,
            hash: ipfsResult.success ? ipfsResult.hash : null
          });
        }
        
        // Beispiel 6: Identifiziere Schlüsselreferenzen
        logger.info('Beispiel 6: Identifiziere Schlüsselreferenzen');
        
        const keyReferencesResult = await citationAnalysisAgent.identifyKeyReferences(publication.doi);
        
        logger.info('Schlüsselreferenzen identifiziert', {
          success: keyReferencesResult.success,
          doi: publication.doi,
          keyReferencesCount: keyReferencesResult.success ? keyReferencesResult.keyReferences.length : 0
        });
        
        // Beispiel 7: Verknüpfe Patent-Zitationen mit NFTs (Patent-IDs zu NFTs)
        logger.info('Beispiel 7: Verknüpfe Patent-Zitationen mit NFTs (Patent-IDs zu NFTs)');
        
        // Suche nach Patentzitationen in der Publikation
        const patentCitationsResult = await citationAnalysisAgent.extractPatentCitations(publication.doi, fullText);
        
        logger.info('Patent-Zitationen extrahiert', {
          success: patentCitationsResult.success,
          doi: publication.doi,
          patentCitationsCount: patentCitationsResult.success ? patentCitationsResult.patentCitations.length : 0
        });
        
        if (patentCitationsResult.success && patentCitationsResult.patentCitations.length > 0) {
          const linkPatentResult = await citationAnalysisAgent.linkPatentCitationsToNFTs(publication.doi);
          
          logger.info('Patent-Zitationen mit NFTs verknüpft', {
            success: linkPatentResult.success,
            doi: publication.doi,
            linkedCount: linkPatentResult.success ? linkPatentResult.linkedPatentCitations.length : 0,
            totalCount: linkPatentResult.success ? linkPatentResult.totalPatentCitations : 0
          });
        }
        
        // Beispiel 8: Analysiere Zitationskontext
        logger.info('Beispiel 8: Analysiere Zitationskontext');
        
        const contextResult = await citationAnalysisAgent.analyzeCitationContext(publication.doi);
        
        logger.info('Zitationskontext analysiert', {
          success: contextResult.success,
          doi: publication.doi,
          contextsAnalyzed: contextResult.success ? contextResult.contextsAnalyzed : 0
        });
        
        if (contextResult.success) {
          logger.info('Top-Zitationskontexte', {
            contexts: contextResult.topContexts.slice(0, 3).map(context => ({
              type: context.type,
              count: context.count,
              percentage: context.percentage
            }))
          });
        }
      }
    } else {
      logger.info('Keine Publikation gefunden');
    }
    
    logger.info('CitationAnalysisAgent-Beispiel abgeschlossen');
  } catch (error) {
    logger.error('Fehler im CitationAnalysisAgent-Beispiel', {
      error: error.message,
      stack: error.stack
    });
  }
}

// Führe das Beispiel aus
main();/**
 * @fileoverview Beispiel für die Verwendung des CitationAnalysisAgent
 * 
 * Dieses Beispiel zeigt, wie der CitationAnalysisAgent verwendet werden kann,
 * um Zitationen zu analysieren und mit NFTs zu verknüpfen.
 */

import { CitationAnalysisAgent } from '../ai/agents/CitationAnalysisAgent.js';
import { DatabaseService } from '../services/DatabaseService.js';
import { BlockchainService } from '../services/BlockchainService.js';
import { VectorDatabaseService } from '../services/VectorDatabaseService.js';
import { TextEmbeddingService } from '../services/TextEmbeddingService.js';
import { IPFSService } from '../services/IPFSService.js';
import { logger } from '../utils/logger.js';
import fs from 'fs/promises';
import path from 'path';

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  try {
    logger.info('Starte CitationAnalysisAgent-Beispiel');
    
    // Initialisiere Dienste
    const databaseService = new DatabaseService();
    await databaseService.initialize();
    
    const blockchainService = new BlockchainService({
      nodeUrl: process.env.BLOCKCHAIN_NODE_URL,
      privateKey: process.env.BLOCKCHAIN_PRIVATE_KEY
    });
    await blockchainService.initialize();
    
    const vectorDatabase = new VectorDatabaseService({
      url: process.env.VECTOR_DB_URL,
      apiKey: process.env.VECTOR_DB_API_KEY
    });
    await vectorDatabase.initialize();
    
    const textEmbeddingService = new TextEmbeddingService({
      apiKey: process.env.TEXT_EMBEDDING_API_KEY
    });
    await textEmbeddingService.initialize();
    
    const ipfsService = new IPFSService({
      host: process.env.IPFS_HOST || 'ipfs.infura.io',
      port: parseInt(process.env.IPFS_PORT || '5001'),
      protocol: process.env.IPFS_PROTOCOL || 'https'
    });
    await ipfsService.initialize();
    
    // Erstelle und initialisiere den CitationAnalysisAgent
    const citationAnalysisAgent = new CitationAnalysisAgent({
      databaseService,
      blockchainService,
      vectorDatabase,
      textEmbeddingService,
      ipfsService,
      config: {
        batchSize: 25,
        energyEfficient: true
      }
    });
    
    await citationAnalysisAgent.initialize();
    
    // Beispiel 1: Extrahiere Zitationen aus einer Publikation
    logger.info('Beispiel 1: Extrahiere Zitationen aus einer Publikation');
    
    // Suche nach einer Publikation
    const publications = await databaseService.query(
      'SELECT doi, title, full_text_path FROM publications LIMIT 1'
    );
    
    if (publications && publications.length > 0) {
      const publication = publications[0];
      
      // Lade den Volltext der Publikation
      const fullTextPath = publication.full_text_path;
      const fullText = await fs.readFile(fullTextPath, 'utf-8');
      
      const extractionResult = await citationAnalysisAgent.extractCitations(publication.doi, fullText);
      
      logger.info('Zitationen extrahiert', {
        success: extractionResult.success,
        doi: publication.doi,
        citationsCount: extractionResult.success ? extractionResult.citations.length : 0
      });
      
      if (extractionResult.success && extractionResult.citations.length > 0) {
        // Beispiel 2: Analysiere Zitationen
        logger.info('Beispiel 2: Analysiere Zitationen');
        
        const analysisResult = await citationAnalysisAgent.analyzeCitations(publication.doi);
        
        logger.info('Zitationen analysiert', {
          success: analysisResult.success,
          doi: publication.doi,
          metrics: analysisResult.success ? {
            hIndex: analysisResult.metrics.hIndex,
            i10Index: analysisResult.metrics.i10Index,
            totalCitations: analysisResult.metrics.totalCitations,
            averageCitationsPerYear: analysisResult.metrics.averageCitationsPerYear
          } : null
        });
        
        // Beispiel 3: Verknüpfe Zitationen mit NFTs (DOIs zu NFTs)
        logger.info('Beispiel 3: Verknüpfe Zitationen mit NFTs (DOIs zu NFTs)');
        
        const linkResult = await citationAnalysisAgent.linkCitationsToNFTs(publication.doi);
        
        logger.info('Zitationen mit NFTs verknüpft', {
          success: linkResult.success,
          doi: publication.doi,
          linkedCount: linkResult.success ? linkResult.linkedCitations.length : 0,
          totalCount: linkResult.success ? linkResult.totalCitations : 0
        });
        
        // Beispiel 4: Berechne den Einfluss einer Publikation
        logger.info('Beispiel 4: Berechne den Einfluss einer Publikation');
        
        const influenceResult = await citationAnalysisAgent.calculatePublicationInfluence(publication.doi);
        
        logger.info('Einfluss der Publikation berechnet', {
          success: influenceResult.success,
          doi: publication.doi,
          influenceScore: influenceResult.success ? influenceResult.influenceScore : null,
          percentile: influenceResult.success ? influenceResult.percentile : null
        });
        
        // Beispiel 5: Erstelle ein Zitationsnetzwerk
        logger.info('Beispiel 5: Erstelle ein Zitationsnetzwerk');
        
        const networkResult = await citationAnalysisAgent.createCitationNetwork(publication.doi, {
          depth: 2,
          maxNodes: 100
        });
        
        logger.info('Zitationsnetzwerk erstellt', {
          success: networkResult.success,
          doi: publication.doi,
          nodesCount: networkResult.success ? networkResult.network.nodes.length : 0,
          edgesCount: networkResult.success ? networkResult.network.edges.length : 0
        });
        
        if (networkResult.success) {
          // Speichere das Netzwerk in IPFS
          const networkJson = JSON.stringify(networkResult.network);
          const ipfsResult = await ipfsService.addContent(networkJson);
          
          logger.info('Zitationsnetzwerk in IPFS gespeichert', {
            success: ipfsResult.success,
            hash: ipfsResult.success ? ipfsResult.hash : null
          });
        }
        
        // Beispiel 6: Identifiziere Schlüsselreferenzen
        logger.info('Beispiel 6: Identifiziere Schlüsselreferenzen');
        
        const keyReferencesResult = await citationAnalysisAgent.identifyKeyReferences(publication.doi);
        
        logger.info('Schlüsselreferenzen identifiziert', {
          success: keyReferencesResult.success,
          doi: publication.doi,
          keyReferencesCount: keyReferencesResult.success ? keyReferencesResult.keyReferences.length : 0
        });
        
        // Beispiel 7: Verknüpfe Patent-Zitationen mit NFTs (Patent-IDs zu NFTs)
        logger.info('Beispiel 7: Verknüpfe Patent-Zitationen mit NFTs (Patent-IDs zu NFTs)');
        
        // Suche nach Patentzitationen in der Publikation
        const patentCitationsResult = await citationAnalysisAgent.extractPatentCitations(publication.doi, fullText);
        
        logger.info('Patent-Zitationen extrahiert', {
          success: patentCitationsResult.success,
          doi: publication.doi,
          patentCitationsCount: patentCitationsResult.success ? patentCitationsResult.patentCitations.length : 0
        });
        
        if (patentCitationsResult.success && patentCitationsResult.patentCitations.length > 0) {
          const linkPatentResult = await citationAnalysisAgent.linkPatentCitationsToNFTs(publication.doi);
          
          logger.info('Patent-Zitationen mit NFTs verknüpft', {
            success: linkPatentResult.success,
            doi: publication.doi,
            linkedCount: linkPatentResult.success ? linkPatentResult.linkedPatentCitations.length : 0,
            totalCount: linkPatentResult.success ? linkPatentResult.totalPatentCitations : 0
          });
        }
        
        // Beispiel 8: Analysiere Zitationskontext
        logger.info('Beispiel 8: Analysiere Zitationskontext');
        
        const contextResult = await citationAnalysisAgent.analyzeCitationContext(publication.doi);
        
        logger.info('Zitationskontext analysiert', {
          success: contextResult.success,
          doi: publication.doi,
          contextsAnalyzed: contextResult.success ? contextResult.contextsAnalyzed : 0
        });
        
        if (contextResult.success) {
          logger.info('Top-Zitationskontexte', {
            contexts: contextResult.topContexts.slice(0, 3).map(context => ({
              type: context.type,
              count: context.count,
              percentage: context.percentage
            }))
          });
        }
      }
    } else {
      logger.info('Keine Publikation gefunden');
    }
    
    logger.info('CitationAnalysisAgent-Beispiel abgeschlossen');
  } catch (error) {
    logger.error('Fehler im CitationAnalysisAgent-Beispiel', {
      error: error.message,
      stack: error.stack
    });
  }
}

// Führe das Beispiel aus
main();/**
 * @fileoverview Beispiel für die Verwendung des CitationAnalysisAgent
 * 
 * Dieses Beispiel zeigt, wie der CitationAnalysisAgent verwendet werden kann,
 * um Zitationen zu analysieren und mit NFTs zu verknüpfen.
 */

import { CitationAnalysisAgent } from '../ai/agents/CitationAnalysisAgent.js';
import { DatabaseService } from '../services/DatabaseService.js';
import { BlockchainService } from '../services/BlockchainService.js';
import { VectorDatabaseService } from '../services/VectorDatabaseService.js';
import { TextEmbeddingService } from '../services/TextEmbeddingService.js';
import { IPFSService } from '../services/IPFSService.js';
import { logger } from '../utils/logger.js';
import fs from 'fs/promises';
import path from 'path';

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  try {
    logger.info('Starte CitationAnalysisAgent-Beispiel');
    
    // Initialisiere Dienste
    const databaseService = new DatabaseService();
    await databaseService.initialize();
    
    const blockchainService = new BlockchainService({
      nodeUrl: process.env.BLOCKCHAIN_NODE_URL,
      privateKey: process.env.BLOCKCHAIN_PRIVATE_KEY
    });
    await blockchainService.initialize();
    
    const vectorDatabase = new VectorDatabaseService({
      url: process.env.VECTOR_DB_URL,
      apiKey: process.env.VECTOR_DB_API_KEY
    });
    await vectorDatabase.initialize();
    
    const textEmbeddingService = new TextEmbeddingService({
      apiKey: process.env.TEXT_EMBEDDING_API_KEY
    });
    await textEmbeddingService.initialize();
    
    const ipfsService = new IPFSService({
      host: process.env.IPFS_HOST || 'ipfs.infura.io',
      port: parseInt(process.env.IPFS_PORT || '5001'),
      protocol: process.env.IPFS_PROTOCOL || 'https'
    });
    await ipfsService.initialize();
    
    // Erstelle und initialisiere den CitationAnalysisAgent
    const citationAnalysisAgent = new CitationAnalysisAgent({
      databaseService,
      blockchainService,
      vectorDatabase,
      textEmbeddingService,
      ipfsService,
      config: {
        batchSize: 25,
        energyEfficient: true
      }
    });
    
    await citationAnalysisAgent.initialize();
    
    // Beispiel 1: Extrahiere Zitationen aus einer Publikation
    logger.info('Beispiel 1: Extrahiere Zitationen aus einer Publikation');
    
    // Suche nach einer Publikation
    const publications = await databaseService.query(
      'SELECT doi, title, full_text_path FROM publications LIMIT 1'
    );
    
    if (publications && publications.length > 0) {
      const publication = publications[0];
      
      // Lade den Volltext der Publikation
      const fullTextPath = publication.full_text_path;
      const fullText = await fs.readFile(fullTextPath, 'utf-8');
      
      const extractionResult = await citationAnalysisAgent.extractCitations(publication.doi, fullText);
      
      logger.info('Zitationen extrahiert', {
        success: extractionResult.success,
        doi: publication.doi,
        citationsCount: extractionResult.success ? extractionResult.citations.length : 0
      });
      
      if (extractionResult.success && extractionResult.citations.length > 0) {
        // Beispiel 2: Analysiere Zitationen
        logger.info('Beispiel 2: Analysiere Zitationen');
        
        const analysisResult = await citationAnalysisAgent.analyzeCitations(publication.doi);
        
        logger.info('Zitationen analysiert', {
          success: analysisResult.success,
          doi: publication.doi,
          metrics: analysisResult.success ? {
            hIndex: analysisResult.metrics.hIndex,
            i10Index: analysisResult.metrics.i10Index,
            totalCitations: analysisResult.metrics.totalCitations,
            averageCitationsPerYear: analysisResult.metrics.averageCitationsPerYear
          } : null
        });
        
        // Beispiel 3: Verknüpfe Zitationen mit NFTs (DOIs zu NFTs)
        logger.info('Beispiel 3: Verknüpfe Zitationen mit NFTs (DOIs zu NFTs)');
        
        const linkResult = await citationAnalysisAgent.linkCitationsToNFTs(publication.doi);
        
        logger.info('Zitationen mit NFTs verknüpft', {
          success: linkResult.success,
          doi: publication.doi,
          linkedCount: linkResult.success ? linkResult.linkedCitations.length : 0,
          totalCount: linkResult.success ? linkResult.totalCitations : 0
        });
        
        // Beispiel 4: Berechne den Einfluss einer Publikation
        logger.info('Beispiel 4: Berechne den Einfluss einer Publikation');
        
        const influenceResult = await citationAnalysisAgent.calculatePublicationInfluence(publication.doi);
        
        logger.info('Einfluss der Publikation berechnet', {
          success: influenceResult.success,
          doi: publication.doi,
          influenceScore: influenceResult.success ? influenceResult.influenceScore : null,
          percentile: influenceResult.success ? influenceResult.percentile : null
        });
        
        // Beispiel 5: Erstelle ein Zitationsnetzwerk
        logger.info('Beispiel 5: Erstelle ein Zitationsnetzwerk');
        
        const networkResult = await citationAnalysisAgent.createCitationNetwork(publication.doi, {
          depth: 2,
          maxNodes: 100
        });
        
        logger.info('Zitationsnetzwerk erstellt', {
          success: networkResult.success,
          doi: publication.doi,
          nodesCount: networkResult.success ? networkResult.network.nodes.length : 0,
          edgesCount: networkResult.success ? networkResult.network.edges.length : 0
        });
        
        if (networkResult.success) {
          // Speichere das Netzwerk in IPFS
          const networkJson = JSON.stringify(networkResult.network);
          const ipfsResult = await ipfsService.addContent(networkJson);
          
          logger.info('Zitationsnetzwerk in IPFS gespeichert', {
            success: ipfsResult.success,
            hash: ipfsResult.success ? ipfsResult.hash : null
          });
        }
        
        // Beispiel 6: Identifiziere Schlüsselreferenzen
        logger.info('Beispiel 6: Identifiziere Schlüsselreferenzen');
        
        const keyReferencesResult = await citationAnalysisAgent.identifyKeyReferences(publication.doi);
        
        logger.info('Schlüsselreferenzen identifiziert', {
          success: keyReferencesResult.success,
          doi: publication.doi,
          keyReferencesCount: keyReferencesResult.success ? keyReferencesResult.keyReferences.length : 0
        });
        
        // Beispiel 7: Verknüpfe Patent-Zitationen mit NFTs (Patent-IDs zu NFTs)
        logger.info('Beispiel 7: Verknüpfe Patent-Zitationen mit NFTs (Patent-IDs zu NFTs)');
        
        // Suche nach Patentzitationen in der Publikation
        const patentCitationsResult = await citationAnalysisAgent.extractPatentCitations(publication.doi, fullText);
        
        logger.info('Patent-Zitationen extrahiert', {
          success: patentCitationsResult.success,
          doi: publication.doi,
          patentCitationsCount: patentCitationsResult.success ? patentCitationsResult.patentCitations.length : 0
        });
        
        if (patentCitationsResult.success && patentCitationsResult.patentCitations.length > 0) {
          const linkPatentResult = await citationAnalysisAgent.linkPatentCitationsToNFTs(publication.doi);
          
          logger.info('Patent-Zitationen mit NFTs verknüpft', {
            success: linkPatentResult.success,
            doi: publication.doi,
            linkedCount: linkPatentResult.success ? linkPatentResult.linkedPatentCitations.length : 0,
            totalCount: linkPatentResult.success ? linkPatentResult.totalPatentCitations : 0
          });
        }
        
        // Beispiel 8: Analysiere Zitationskontext
        logger.info('Beispiel 8: Analysiere Zitationskontext');
        
        const contextResult = await citationAnalysisAgent.analyzeCitationContext(publication.doi);
        
        logger.info('Zitationskontext analysiert', {
          success: contextResult.success,
          doi: publication.doi,
          contextsAnalyzed: contextResult.success ? contextResult.contextsAnalyzed : 0
        });
        
        if (contextResult.success) {
          logger.info('Top-Zitationskontexte', {
            contexts: contextResult.topContexts.slice(0, 3).map(context => ({
              type: context.type,
              count: context.count,
              percentage: context.percentage
            }))
          });
        }
      }
    } else {
      logger.info('Keine Publikation gefunden');
    }
    
    logger.info('CitationAnalysisAgent-Beispiel abgeschlossen');
  } catch (error) {
    logger.error('Fehler im CitationAnalysisAgent-Beispiel', {
      error: error.message,
      stack: error.stack
    });
  }
}

// Führe das Beispiel aus
main();/**
 * @fileoverview Beispiel für die Verwendung des CitationAnalysisAgent
 * 
 * Dieses Beispiel zeigt, wie der CitationAnalysisAgent verwendet werden kann,
 * um Zitationen zu analysieren und mit NFTs zu verknüpfen.
 */

import { CitationAnalysisAgent } from '../ai/agents/CitationAnalysisAgent.js';
import { DatabaseService } from '../services/DatabaseService.js';
import { BlockchainService } from '../services/BlockchainService.js';
import { VectorDatabaseService } from '../services/VectorDatabaseService.js';
import { TextEmbeddingService } from '../services/TextEmbeddingService.js';
import { IPFSService } from '../services/IPFSService.js';
import { logger } from '../utils/logger.js';
import fs from 'fs/promises';
import path from 'path';

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  try {
    logger.info('Starte CitationAnalysisAgent-Beispiel');
    
    // Initialisiere Dienste
    const databaseService = new DatabaseService();
    await databaseService.initialize();
    
    const blockchainService = new BlockchainService({
      nodeUrl: process.env.BLOCKCHAIN_NODE_URL,
      privateKey: process.env.BLOCKCHAIN_PRIVATE_KEY
    });
    await blockchainService.initialize();
    
    const vectorDatabase = new VectorDatabaseService({
      url: process.env.VECTOR_DB_URL,
      apiKey: process.env.VECTOR_DB_API_KEY
    });
    await vectorDatabase.initialize();
    
    const textEmbeddingService = new TextEmbeddingService({
      apiKey: process.env.TEXT_EMBEDDING_API_KEY
    });
    await textEmbeddingService.initialize();
    
    const ipfsService = new IPFSService({
      host: process.env.IPFS_HOST || 'ipfs.infura.io',
      port: parseInt(process.env.IPFS_PORT || '5001'),
      protocol: process.env.IPFS_PROTOCOL || 'https'
    });
    await ipfsService.initialize();
    
    // Erstelle und initialisiere den CitationAnalysisAgent
    const citationAnalysisAgent = new CitationAnalysisAgent({
      databaseService,
      blockchainService,
      vectorDatabase,
      textEmbeddingService,
      ipfsService,
      config: {
        batchSize: 25,
        energyEfficient: true
      }
    });
    
    await citationAnalysisAgent.initialize();
    
    // Beispiel 1: Extrahiere Zitationen aus einer Publikation
    logger.info('Beispiel 1: Extrahiere Zitationen aus einer Publikation');
    
    // Suche nach einer Publikation
    const publications = await databaseService.query(
      'SELECT doi, title, full_text_path FROM publications LIMIT 1'
    );
    
    if (publications && publications.length > 0) {
      const publication = publications[0];
      
      // Lade den Volltext der Publikation
      const fullTextPath = publication.full_text_path;
      const fullText = await fs.readFile(fullTextPath, 'utf-8');
      
      const extractionResult = await citationAnalysisAgent.extractCitations(publication.doi, fullText);
      
      logger.info('Zitationen extrahiert', {
        success: extractionResult.success,
        doi: publication.doi,
        citationsCount: extractionResult.success ? extractionResult.citations.length : 0
      });
      
      if (extractionResult.success && extractionResult.citations.length > 0) {
        // Beispiel 2: Analysiere Zitationen
        logger.info('Beispiel 2: Analysiere Zitationen');
        
        const analysisResult = await citationAnalysisAgent.analyzeCitations(publication.doi);
        
        logger.info('Zitationen analysiert', {
          success: analysisResult.success,
          doi: publication.doi,
          metrics: analysisResult.success ? {
            hIndex: analysisResult.metrics.hIndex,
            i10Index: analysisResult.metrics.i10Index,
            totalCitations: analysisResult.metrics.totalCitations,
            averageCitationsPerYear: analysisResult.metrics.averageCitationsPerYear
          } : null
        });
        
        // Beispiel 3: Verknüpfe Zitationen mit NFTs (DOIs zu NFTs)
        logger.info('Beispiel 3: Verknüpfe Zitationen mit NFTs (DOIs zu NFTs)');
        
        const linkResult = await citationAnalysisAgent.linkCitationsToNFTs(publication.doi);
        
        logger.info('Zitationen mit NFTs verknüpft', {
          success: linkResult.success,
          doi: publication.doi,
          linkedCount: linkResult.success ? linkResult.linkedCitations.length : 0,
          totalCount: linkResult.success ? linkResult.totalCitations : 0
        });
        
        // Beispiel 4: Berechne den Einfluss einer Publikation
        logger.info('Beispiel 4: Berechne den Einfluss einer Publikation');
        
        const influenceResult = await citationAnalysisAgent.calculatePublicationInfluence(publication.doi);
        
        logger.info('Einfluss der Publikation berechnet', {
          success: influenceResult.success,
          doi: publication.doi,
          influenceScore: influenceResult.success ? influenceResult.influenceScore : null,
          percentile: influenceResult.success ? influenceResult.percentile : null
        });
        
        // Beispiel 5: Erstelle ein Zitationsnetzwerk
        logger.info('Beispiel 5: Erstelle ein Zitationsnetzwerk');
        
        const networkResult = await citationAnalysisAgent.createCitationNetwork(publication.doi, {
          depth: 2,
          maxNodes: 100
        });
        
        logger.info('Zitationsnetzwerk erstellt', {
          success: networkResult.success,
          doi: publication.doi,
          nodesCount: networkResult.success ? networkResult.network.nodes.length : 0,
          edgesCount: networkResult.success ? networkResult.network.edges.length : 0
        });
        
        if (networkResult.success) {
          // Speichere das Netzwerk in IPFS
          const networkJson = JSON.stringify(networkResult.network);
          const ipfsResult = await ipfsService.addContent(networkJson);
          
          logger.info('Zitationsnetzwerk in IPFS gespeichert', {
            success: ipfsResult.success,
            hash: ipfsResult.success ? ipfsResult.hash : null
          });
        }
        
        // Beispiel 6: Identifiziere Schlüsselreferenzen
        logger.info('Beispiel 6: Identifiziere Schlüsselreferenzen');
        
        const keyReferencesResult = await citationAnalysisAgent.identifyKeyReferences(publication.doi);
        
        logger.info('Schlüsselreferenzen identifiziert', {
          success: keyReferencesResult.success,
          doi: publication.doi,
          keyReferencesCount: keyReferencesResult.success ? keyReferencesResult.keyReferences.length : 0
        });
        
        // Beispiel 7: Verknüpfe Patent-Zitationen mit NFTs (Patent-IDs zu NFTs)
        logger.info('Beispiel 7: Verknüpfe Patent-Zitationen mit NFTs (Patent-IDs zu NFTs)');
        
        // Suche nach Patentzitationen in der Publikation
        const patentCitationsResult = await citationAnalysisAgent.extractPatentCitations(publication.doi, fullText);
        
        logger.info('Patent-Zitationen extrahiert', {
          success: patentCitationsResult.success,
          doi: publication.doi,
          patentCitationsCount: patentCitationsResult.success ? patentCitationsResult.patentCitations.length : 0
        });
        
        if (patentCitationsResult.success && patentCitationsResult.patentCitations.length > 0) {
          const linkPatentResult = await citationAnalysisAgent.linkPatentCitationsToNFTs(publication.doi);
          
          logger.info('Patent-Zitationen mit NFTs verknüpft', {
            success: linkPatentResult.success,
            doi: publication.doi,
            linkedCount: linkPatentResult.success ? linkPatentResult.linkedPatentCitations.length : 0,
            totalCount: linkPatentResult.success ? linkPatentResult.totalPatentCitations : 0
          });
        }
        
        // Beispiel 8: Analysiere Zitationskontext
        logger.info('Beispiel 8: Analysiere Zitationskontext');
        
        const contextResult = await citationAnalysisAgent.analyzeCitationContext(publication.doi);
        
        logger.info('Zitationskontext analysiert', {
          success: contextResult.success,
          doi: publication.doi,
          contextsAnalyzed: contextResult.success ? contextResult.contextsAnalyzed : 0
        });
        
        if (contextResult.success) {
          logger.info('Top-Zitationskontexte', {
            contexts: contextResult.topContexts.slice(0, 3).map(context => ({
              type: context.type,
              count: context.count,
              percentage: context.percentage
            }))
          });
        }
      }
    } else {
      logger.info('Keine Publikation gefunden');
    }
    
    logger.info('CitationAnalysisAgent-Beispiel abgeschlossen');
  } catch (error) {
    logger.error('Fehler im CitationAnalysisAgent-Beispiel', {
      error: error.message,
      stack: error.stack
    });
  }
}

// Führe das Beispiel aus
main();/**
 * @fileoverview Beispiel für die Verwendung des CitationAnalysisAgent
 * 
 * Dieses Beispiel zeigt, wie der CitationAnalysisAgent verwendet werden kann,
 * um Zitationen zu analysieren und mit NFTs zu verknüpfen.
 */

import { CitationAnalysisAgent } from '../ai/agents/CitationAnalysisAgent.js';
import { DatabaseService } from '../services/DatabaseService.js';
import { BlockchainService } from '../services/BlockchainService.js';
import { VectorDatabaseService } from '../services/VectorDatabaseService.js';
import { TextEmbeddingService } from '../services/TextEmbeddingService.js';
import { IPFSService } from '../services/IPFSService.js';
import { logger } from '../utils/logger.js';
import fs from 'fs/promises';
import path from 'path';

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  try {
    logger.info('Starte CitationAnalysisAgent-Beispiel');
    
    // Initialisiere Dienste
    const databaseService = new DatabaseService();
    await databaseService.initialize();
    
    const blockchainService = new BlockchainService({
      nodeUrl: process.env.BLOCKCHAIN_NODE_URL,
      privateKey: process.env.BLOCKCHAIN_PRIVATE_KEY
    });
    await blockchainService.initialize();
    
    const vectorDatabase = new VectorDatabaseService({
      url: process.env.VECTOR_DB_URL,
      apiKey: process.env.VECTOR_DB_API_KEY
    });
    await vectorDatabase.initialize();
    
    const textEmbeddingService = new TextEmbeddingService({
      apiKey: process.env.TEXT_EMBEDDING_API_KEY
    });
    await textEmbeddingService.initialize();
    
    const ipfsService = new IPFSService({
      host: process.env.IPFS_HOST || 'ipfs.infura.io',
      port: parseInt(process.env.IPFS_PORT || '5001'),
      protocol: process.env.IPFS_PROTOCOL || 'https'
    });
    await ipfsService.initialize();
    
    // Erstelle und initialisiere den CitationAnalysisAgent
    const citationAnalysisAgent = new CitationAnalysisAgent({
      databaseService,
      blockchainService,
      vectorDatabase,
      textEmbeddingService,
      ipfsService,
      config: {
        batchSize: 25,
        energyEfficient: true
      }
    });
    
    await citationAnalysisAgent.initialize();
    
    // Beispiel 1: Extrahiere Zitationen aus einer Publikation
    logger.info('Beispiel 1: Extrahiere Zitationen aus einer Publikation');
    
    // Suche nach einer Publikation
    const publications = await databaseService.query(
      'SELECT doi, title, full_text_path FROM publications LIMIT 1'
    );
    
    if (publications && publications.length > 0) {
      const publication = publications[0];
      
      // Lade den Volltext der Publikation
      const fullTextPath = publication.full_text_path;
      const fullText = await fs.readFile(fullTextPath, 'utf-8');
      
      const extractionResult = await citationAnalysisAgent.extractCitations(publication.doi, fullText);
      
      logger.info('Zitationen extrahiert', {
        success: extractionResult.success,
        doi: publication.doi,
        citationsCount: extractionResult.success ? extractionResult.citations.length : 0
      });
      
      if (extractionResult.success && extractionResult.citations.length > 0) {
        // Beispiel 2: Analysiere Zitationen
        logger.info('Beispiel 2: Analysiere Zitationen');
        
        const analysisResult = await citationAnalysisAgent.analyzeCitations(publication.doi);
        
        logger.info('Zitationen analysiert', {
          success: analysisResult.success,
          doi: publication.doi,
          metrics: analysisResult.success ? {
            hIndex: analysisResult.metrics.hIndex,
            i10Index: analysisResult.metrics.i10Index,
            totalCitations: analysisResult.metrics.totalCitations,
            averageCitationsPerYear: analysisResult.metrics.averageCitationsPerYear
          } : null
        });
        
        // Beispiel 3: Verknüpfe Zitationen mit NFTs (DOIs zu NFTs)
        logger.info('Beispiel 3: Verknüpfe Zitationen mit NFTs (DOIs zu NFTs)');
        
        const linkResult = await citationAnalysisAgent.linkCitationsToNFTs(publication.doi);
        
        logger.info('Zitationen mit NFTs verknüpft', {
          success: linkResult.success,
          doi: publication.doi,
          linkedCount: linkResult.success ? linkResult.linkedCitations.length : 0,
          totalCount: linkResult.success ? linkResult.totalCitations : 0
        });
        
        // Beispiel 4: Berechne den Einfluss einer Publikation
        logger.info('Beispiel 4: Berechne den Einfluss einer Publikation');
        
        const influenceResult = await citationAnalysisAgent.calculatePublicationInfluence(publication.doi);
        
        logger.info('Einfluss der Publikation berechnet', {
          success: influenceResult.success,
          doi: publication.doi,
          influenceScore: influenceResult.success ? influenceResult.influenceScore : null,
          percentile: influenceResult.success ? influenceResult.percentile : null
        });
        
        // Beispiel 5: Erstelle ein Zitationsnetzwerk
        logger.info('Beispiel 5: Erstelle ein Zitationsnetzwerk');
        
        const networkResult = await citationAnalysisAgent.createCitationNetwork(publication.doi, {
          depth: 2,
          maxNodes: 100
        });
        
        logger.info('Zitationsnetzwerk erstellt', {
          success: networkResult.success,
          doi: publication.doi,
          nodesCount: networkResult.success ? networkResult.network.nodes.length : 0,
          edgesCount: networkResult.success ? networkResult.network.edges.length : 0
        });
        
        if (networkResult.success) {
          // Speichere das Netzwerk in IPFS
          const networkJson = JSON.stringify(networkResult.network);
          const ipfsResult = await ipfsService.addContent(networkJson);
          
          logger.info('Zitationsnetzwerk in IPFS gespeichert', {
            success: ipfsResult.success,
            hash: ipfsResult.success ? ipfsResult.hash : null
          });
        }
        
        // Beispiel 6: Identifiziere Schlüsselreferenzen
        logger.info('Beispiel 6: Identifiziere Schlüsselreferenzen');
        
        const keyReferencesResult = await citationAnalysisAgent.identifyKeyReferences(publication.doi);
        
        logger.info('Schlüsselreferenzen identifiziert', {
          success: keyReferencesResult.success,
          doi: publication.doi,
          keyReferencesCount: keyReferencesResult.success ? keyReferencesResult.keyReferences.length : 0
        });
        
        // Beispiel 7: Verknüpfe Patent-Zitationen mit NFTs (Patent-IDs zu NFTs)
        logger.info('Beispiel 7: Verknüpfe Patent-Zitationen mit NFTs (Patent-IDs zu NFTs)');
        
        // Suche nach Patentzitationen in der Publikation
        const patentCitationsResult = await citationAnalysisAgent.extractPatentCitations(publication.doi, fullText);
        
        logger.info('Patent-Zitationen extrahiert', {
          success: patentCitationsResult.success,
          doi: publication.doi,
          patentCitationsCount: patentCitationsResult.success ? patentCitationsResult.patentCitations.length : 0
        });
        
        if (patentCitationsResult.success && patentCitationsResult.patentCitations.length > 0) {
          const linkPatentResult = await citationAnalysisAgent.linkPatentCitationsToNFTs(publication.doi);
          
          logger.info('Patent-Zitationen mit NFTs verknüpft', {
            success: linkPatentResult.success,
            doi: publication.doi,
            linkedCount: linkPatentResult.success ? linkPatentResult.linkedPatentCitations.length : 0,
            totalCount: linkPatentResult.success ? linkPatentResult.totalPatentCitations : 0
          });
        }
        
        // Beispiel 8: Analysiere Zitationskontext
        logger.info('Beispiel 8: Analysiere Zitationskontext');
        
        const contextResult = await citationAnalysisAgent.analyzeCitationContext(publication.doi);
        
        logger.info('Zitationskontext analysiert', {
          success: contextResult.success,
          doi: publication.doi,
          contextsAnalyzed: contextResult.success ? contextResult.contextsAnalyzed : 0
        });
        
        if (contextResult.success) {
          logger.info('Top-Zitationskontexte', {
            contexts: contextResult.topContexts.slice(0, 3).map(context => ({
              type: context.type,
              count: context.count,
              percentage: context.percentage
            }))
          });
        }
      }
    } else {
      logger.info('Keine Publikation gefunden');
    }
    
    logger.info('CitationAnalysisAgent-Beispiel abgeschlossen');
  } catch (error) {
    logger.error('Fehler im CitationAnalysisAgent-Beispiel', {
      error: error.message,
      stack: error.stack
    });
  }
}

// Führe das Beispiel aus
main();