/**
 * @fileoverview Beispiel für die Verwendung des EnhancedPatentIntegrationAgent
 * 
 * Dieses <PERSON> zeigt, wie der EnhancedPatentIntegrationAgent verwendet werden kann,
 * um erweiterte Patentintegrationen und -analysen durchzuführen.
 */

import { EnhancedPatentIntegrationAgent } from '../ai/agents/EnhancedPatentIntegrationAgent.js';
import { DatabaseService } from '../services/DatabaseService.js';
import { BlockchainService } from '../services/BlockchainService.js';
import { IPFSService } from '../services/IPFSService.js';
import { TextAnalysisService } from '../services/TextAnalysisService.js';
import { logger } from '../utils/logger.js';
import fs from 'fs/promises';
import path from 'path';

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  try {
    logger.info('Starte EnhancedPatentIntegrationAgent-Beispiel');
    
    // Initialisiere Dienste
    const databaseService = new DatabaseService();
    await databaseService.initialize();
    
    const blockchainService = new BlockchainService({
      nodeUrl: process.env.BLOCKCHAIN_NODE_URL,
      privateKey: process.env.BLOCKCHAIN_PRIVATE_KEY
    });
    await blockchainService.initialize();
    
    const ipfsService = new IPFSService({
      host: process.env.IPFS_HOST || 'ipfs.infura.io',
      port: parseInt(process.env.IPFS_PORT || '5001'),
      protocol: process.env.IPFS_PROTOCOL || 'https'
    });
    await ipfsService.initialize();
    
    const textAnalysisService = new TextAnalysisService({
      apiKey: process.env.TEXT_ANALYSIS_API_KEY
    });
    await textAnalysisService.initialize();
    
    // Erstelle und initialisiere den EnhancedPatentIntegrationAgent
    const patentIntegrationAgent = new EnhancedPatentIntegrationAgent({
      databaseService,
      blockchainService,
      ipfsService,
      textAnalysisService,
      patentApiConfig: {
        usptoApiKey: process.env.USPTO_API_KEY,
        epoApiKey: process.env.EPO_API_KEY,
        wipoApiKey: process.env.WIPO_API_KEY
      },
      config: {
        updateInterval: 12 * 60 * 60 * 1000, // 12 Stunden
        batchSize: 25,
        energyEfficient: true
      }
    });
    
    await patentIntegrationAgent.initialize();
    
    // Beispiel 1: Aktualisiere Patentdaten
    logger.info('Beispiel 1: Aktualisiere Patentdaten');
    
    const updateResult = await patentIntegrationAgent.updatePatentData();
    
    logger.info('Patentdaten aktualisiert', {
      success: updateResult.success,
      total: updateResult.results ? updateResult.results.total : 0,
      uspto: updateResult.results && updateResult.results.uspto ? updateResult.results.uspto.count : 0,
      epo: updateResult.results && updateResult.results.epo ? updateResult.results.epo.count : 0,
      wipo: updateResult.results && updateResult.results.wipo ? updateResult.results.wipo.count : 0
    });
    
    // Beispiel 2: Suche nach Patenten
    logger.info('Beispiel 2: Suche nach Patenten');
    
    const searchResult = await patentIntegrationAgent.searchPatents({
      title: 'quantum',
      fromDate: '2020-01-01',
      limit: 10
    });
    
    logger.info('Patentsuche abgeschlossen', {
      success: searchResult.success,
      total: searchResult.total,
      found: searchResult.patents ? searchResult.patents.length : 0
    });
    
    // Wenn Patente gefunden wurden, verwende das erste für weitere Beispiele
    if (searchResult.success && searchResult.patents && searchResult.patents.length > 0) {
      const patent = searchResult.patents[0];
      
      // Beispiel 3: Verknüpfe Patent mit Publikation
      logger.info('Beispiel 3: Verknüpfe Patent mit Publikation');
      
      // Suche nach einer passenden Publikation
      const publications = await databaseService.query(
        'SELECT doi FROM publications WHERE title LIKE ? LIMIT 1',
        [`%${patent.title.split(' ').slice(0, 3).join(' ')}%`]
      );
      
      if (publications && publications.length > 0) {
        const linkResult = await patentIntegrationAgent.linkPatentToPublication(
          patent.id,
          publications[0].doi,
          'implementation'
        );
        
        logger.info('Patent mit Publikation verknüpft', {
          success: linkResult.success,
          patentId: patent.id,
          doi: publications[0].doi,
          analysis: linkResult.analysis ? 'verfügbar' : 'nicht verfügbar'
        });
        
        if (linkResult.success && linkResult.analysis) {
          logger.info('Analyse der Patent-Publikations-Beziehung', {
            summary: linkResult.analysis.summary,
            recommendations: linkResult.analysis.recommendations
          });
        }
      } else {
        logger.info('Keine passende Publikation gefunden');
      }
      
      // Beispiel 4: Erstelle einen Smart Contract für die Patentlizenzierung
      logger.info('Beispiel 4: Erstelle einen Smart Contract für die Patentlizenzierung');
      
      // Suche nach einem Forscher
      const researchers = await databaseService.query(
        'SELECT id FROM researchers LIMIT 1'
      );
      
      if (researchers && researchers.length > 0) {
        const licenseResult = await patentIntegrationAgent.createPatentLicenseContract({
          patentId: patent.id,
          licenseeId: researchers[0].id,
          licenseType: 'non-exclusive',
          startDate: new Date().toISOString().split('T')[0],
          endDate: new Date(new Date().setFullYear(new Date().getFullYear() + 2)).toISOString().split('T')[0],
          royaltyRate: 3.5
        });
        
        logger.info('Smart Contract für Patentlizenzierung erstellt', {
          success: licenseResult.success,
          patentId: patent.id,
          licenseeId: researchers[0].id,
          contractAddress: licenseResult.success ? licenseResult.contractAddress : null
        });
      } else {
        logger.info('Kein Forscher gefunden');
      }
      
      // Beispiel 5: Analysiere Patentzitationen
      logger.info('Beispiel 5: Analysiere Patentzitationen');
      
      const citationResult = await patentIntegrationAgent.analyzePatentCitations(patent.id);
      
      logger.info('Patentzitationen analysiert', {
        success: citationResult.success,
        patentId: patent.id,
        citationCount: citationResult.success ? citationResult.metrics.citationCount : 0,
        influenceScore: citationResult.success ? citationResult.metrics.influenceScore : 0
      });
      
      if (citationResult.success && citationResult.aiAnalysis) {
        logger.info('KI-Analyse der Patentzitationen', {
          impact: citationResult.aiAnalysis.impact,
          trends: citationResult.aiAnalysis.trends,
          recommendations: citationResult.aiAnalysis.recommendations,
          keywords: citationResult.aiAnalysis.keywords
        });
      }
      
      // Beispiel 6: Finde ähnliche Patente
      logger.info('Beispiel 6: Finde ähnliche Patente');
      
      const similarResult = await patentIntegrationAgent.findSimilarPatents(patent.id, {
        limit: 5,
        minSimilarity: 0.6
      });
      
      logger.info('Ähnliche Patente gefunden', {
        success: similarResult.success,
        patentId: patent.id,
        count: similarResult.success ? similarResult.similarPatents.length : 0,
        method: similarResult.success ? similarResult.method : null
      });
      
      // Beispiel 7: Erstelle NFT für Patent
      logger.info('Beispiel 7: Erstelle NFT für Patent');
      
      const nftResult = await patentIntegrationAgent.createPatentNFT(patent.id);
      
      logger.info('NFT für Patent erstellt', {
        success: nftResult.success,
        patentId: patent.id,
        originalPatentId: nftResult.success ? nftResult.originalPatentId : null,
        patentOffice: nftResult.success ? nftResult.patentOffice : null,
        nftId: nftResult.success && nftResult.nftId ? nftResult.nftId : null,
        message: nftResult.message || ''
      });
      
      // Beispiel 8: Analysiere Patent-Publikations-Beziehungen
      logger.info('Beispiel 8: Analysiere Patent-Publikations-Beziehungen');
      
      const relationshipResult = await patentIntegrationAgent.analyzePatentPublicationRelationships(patent.id);
      
      logger.info('Patent-Publikations-Beziehungen analysiert', {
        success: relationshipResult.success,
        patentId: patent.id,
        linkedPublicationsCount: relationshipResult.success ? relationshipResult.linkedPublications.length : 0,
        similarPublicationsCount: relationshipResult.success ? relationshipResult.similarPublications.length : 0
      });
      
      if (relationshipResult.success && relationshipResult.analysis) {
        logger.info('Analyse der Patent-Publikations-Beziehungen', {
          summary: relationshipResult.analysis.summary,
          recommendations: relationshipResult.analysis.recommendations
        });
      }
      
      // Beispiel 9: Analysiere Technologietransfer
      logger.info('Beispiel 9: Analysiere Technologietransfer');
      
      const transferResult = await patentIntegrationAgent.analyzeTechnologyTransfer({
        fromDate: '2018-01-01',
        limit: 50
      });
      
      logger.info('Technologietransfer analysiert', {
        success: transferResult.success,
        patentsCount: transferResult.success ? transferResult.patentsCount : 0
      });
      
      if (transferResult.success && transferResult.timeGapAnalysis) {
        logger.info('Zeitabstandsanalyse zwischen Publikationen und Patenten', {
          averageGapInDays: transferResult.timeGapAnalysis.averageGapInDays,
          medianGapInDays: transferResult.timeGapAnalysis.medianGapInDays,
          directionCounts: transferResult.timeGapAnalysis.directionCounts
        });
      }
      
      if (transferResult.success && transferResult.researchFieldsAnalysis) {
        logger.info('Top-Forschungsbereiche mit hohem Technologietransfer', {
          fields: transferResult.researchFieldsAnalysis.topFields.slice(0, 3).map(field => field.field)
        });
      }
    }
    
    logger.info('EnhancedPatentIntegrationAgent-Beispiel abgeschlossen');
  } catch (error) {
    logger.error('Fehler im EnhancedPatentIntegrationAgent-Beispiel', {
      error: error.message,
      stack: error.stack
    });
  }
}

// Führe das Beispiel aus
main();