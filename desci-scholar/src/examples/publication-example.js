/**
 * Beispiel für die Verwendung des Publikationsmodells mit dezentraler Speicherung
 * Demonstriert, wie wissenschaftliche Publikationen erstellt, gespeichert und abgerufen werden können
 */

import StorageService, { StorageProtocol } from '../storage/StorageService.js.js';
import Publication from '../models/Publication.js.js';
import { toBuffer } from '../utils/buffer.js.js';

/**
 * Hauptfunktion für das Publikationsbeispiel
 */
async function publicationExample() {
  console.log('=== DeSci Scholar - Publikationsmodell-Demo ===');
  
  // Erstelle und initialisiere den Storage-Service
  const storage = new StorageService({
    defaultProtocol: StorageProtocol.BOTH,
    ipfs: {
      gateway: 'https://ipfs.io/ipfs/',
      pinningService: 'local'
    },
    bittorrent: {
      downloadPath: './downloads',
      dht: true
    }
  });
  
  console.log('Initialisiere Speicherdienste...');
  await storage.initialize();
  
  try {
    // 1. Erstelle eine neue Publikation
    console.log('\n1. Erstelle eine neue Publikation');
    const publication = new Publication({
      title: 'Dezentrale Speicherlösungen für wissenschaftliche Daten',
      authors: ['Dr. Julia Weber', 'Prof. Martin Schmidt'],
      abstract: 'Diese Studie untersucht die Vor- und Nachteile dezentraler Speicherlösungen wie IPFS und BitTorrent für die langfristige Archivierung und den Austausch wissenschaftlicher Daten im Kontext der Open-Science-Bewegung.',
      content: `# Einleitung

Wissenschaftliche Publikationen und Forschungsdaten sind das Herzstück des akademischen Fortschritts. Traditionell werden diese Inhalte in zentralen Repositorien gespeichert und über verschiedene Plattformen verbreitet. Diese Zentralisierung bringt jedoch Herausforderungen mit sich, darunter hohe Kosten, Single Points of Failure und potenzielle Zensur.

## Dezentrale Speicherlösungen

Dezentrale Speicherlösungen wie IPFS (InterPlanetary File System) und BitTorrent bieten eine Alternative, die diese Probleme adressieren kann. Durch die Verteilung von Inhalten auf mehrere Knoten im Netzwerk erhöhen sie die Ausfallsicherheit, senken Kosten und erschweren Zensurversuche.

### IPFS

IPFS ist ein Protokoll und ein Netzwerk, das entwickelt wurde, um Dateien, Websites, Anwendungen und Daten dezentral zu speichern und zu teilen. Anstatt Inhalte über ihre Speicherorte zu identifizieren, verwendet IPFS einen inhaltsbezogenen Adressierungsmechanismus, der es ermöglicht, Inhalte anhand ihres kryptografischen Hashes zu identifizieren und zu finden.

### BitTorrent

BitTorrent ist ein Protokoll für Peer-to-Peer-Dateifreigabe, das für die effiziente Verteilung großer Datenmengen konzipiert ist. Es ermöglicht Nutzern, Dateien direkt untereinander zu teilen, ohne dass ein zentraler Server erforderlich ist, was die Skalierbarkeit und Robustheit erhöht.

## Methodik

In dieser Studie haben wir eine Hybrid-Architektur entwickelt, die sowohl IPFS als auch BitTorrent für die Speicherung und Verteilung wissenschaftlicher Inhalte nutzt. Wir haben die Leistung dieser Architektur anhand verschiedener Metriken evaluiert, darunter Zugriffszeit, Verfügbarkeit und Beständigkeit der Daten.`,
      keywords: ['IPFS', 'BitTorrent', 'Dezentrale Speicherung', 'Open Science', 'Wissenschaftliche Publikationen'],
      license: 'CC BY 4.0',
      doi: '10.xxxx/example.2023.01',
      publishedDate: new Date('2023-06-15')
    });
    
    console.log('Neue Publikation erstellt:');
    console.log('Titel:', publication.title);
    console.log('Autoren:', publication.authors.join(', '));
    console.log('ID:', publication.id);
    
    // 2. Validiere die Publikation
    console.log('\n2. Validiere die Publikation');
    const validation = publication.validate();
    
    if (validation.valid) {
      console.log('Publikation ist gültig!');
    } else {
      console.error('Publikation ist ungültig:', validation.errors.join(', '));
      return;
    }
    
    // 3. Speichere die Publikation (als Entwurf)
    console.log('\n3. Speichere die Publikation als Entwurf');
    const saveResult = await publication.save(storage, {
      includeContent: true // Volltext einschließen
    });
    
    if (saveResult.success) {
      console.log('Publikation erfolgreich gespeichert!');
      console.log('IPFS CID:', publication.storage.ipfs);
      console.log('BitTorrent InfoHash:', publication.storage.bittorrent);
    } else {
      console.error('Fehler beim Speichern der Publikation:', saveResult.error);
      return;
    }
    
    // 4. Füge eine Datei zur Publikation hinzu
    console.log('\n4. Füge eine Datei zur Publikation hinzu');
    
    // Simuliere eine PDF-Datei
    const pdfContent = toBuffer(`%PDF-1.4
1 0 obj
<</Type/Catalog/Pages 2 0 R>>
endobj
2 0 obj
<</Type/Pages/Kids[3 0 R]/Count 1>>
endobj
3 0 obj
<</Type/Page/MediaBox[0 0 595 842]/Resources<<>>>>
endobj
xref
0 4
0000000000 65535 f
0000000010 00000 n
0000000056 00000 n
0000000107 00000 n
trailer
<</Size 4/Root 1 0 R>>
startxref
178
%%EOF`);
    
    const fileInfo = {
      name: 'dezentrale-speicherung.pdf',
      type: 'application/pdf',
      size: pdfContent.length
    };
    
    const fileResult = await publication.saveFile(pdfContent, fileInfo, storage);
    
    if (fileResult.success) {
      console.log('Datei erfolgreich zur Publikation hinzugefügt!');
      console.log('Dateiname:', publication.file.name);
      console.log('IPFS CID:', publication.file.storage.ipfs);
      console.log('BitTorrent InfoHash:', publication.file.storage.bittorrent);
    } else {
      console.error('Fehler beim Hinzufügen der Datei:', fileResult.error);
    }
    
    // 5. Veröffentliche die Publikation
    console.log('\n5. Veröffentliche die Publikation');
    
    const publishResult = await publication.publish(storage);
    
    if (publishResult.success) {
      console.log('Publikation erfolgreich veröffentlicht!');
      console.log('Status:', publication.status);
      console.log('Version:', publication.version);
      console.log('Letzte Versionsverlauf-Einträge:', publication.versionHistory.length);
    } else {
      console.error('Fehler beim Veröffentlichen der Publikation:', publishResult.error);
    }
    
    // 6. Lade die Publikation
    console.log('\n6. Lade die Publikation aus dem dezentralen Speicher');
    
    // Speichere die IDs für später
    const publicationId = {
      ipfs: publication.storage.ipfs,
      bittorrent: publication.storage.bittorrent
    };
    
    // Lade über IPFS
    const loadedPublication = await Publication.load(publicationId.ipfs, storage, StorageProtocol.IPFS);
    
    console.log('Publikation erfolgreich geladen!');
    console.log('ID:', loadedPublication.id);
    console.log('Titel:', loadedPublication.title);
    console.log('Status:', loadedPublication.status);
    console.log('DOI:', loadedPublication.doi);
    
    // 7. Lade die zugehörige Datei
    console.log('\n7. Lade die zugehörige Datei');
    
    if (loadedPublication.file) {
      const loadedFile = await Publication.loadFile(
        loadedPublication.file.storage, 
        storage, 
        'buffer'
      );
      
      if (loadedFile.success) {
        console.log('Datei erfolgreich geladen!');
        console.log('Dateigröße:', loadedFile.data.length, 'Bytes');
        console.log('Dateiname:', loadedPublication.file.name);
      } else {
        console.error('Fehler beim Laden der Datei:', loadedFile.error);
      }
    } else {
      console.log('Keine Datei mit der Publikation verknüpft');
    }
    
    // 8. Erstelle eine neue Version der Publikation
    console.log('\n8. Erstelle eine neue Version der Publikation');
    
    loadedPublication.createNewVersion({
      abstract: 'Diese aktualisierte Studie präsentiert neue Erkenntnisse zu dezentralen Speicherlösungen und deren Anwendung in der wissenschaftlichen Gemeinschaft. Wir haben unsere ursprüngliche Untersuchung erweitert und neue Benchmarks durchgeführt.',
      content: loadedPublication.content + `\n\n## Aktualisierung (${new Date().toISOString().split('T')[0]})

In dieser aktualisierten Version haben wir zusätzliche Analysen durchgeführt, die unsere ursprünglichen Ergebnisse bestätigen und erweitern. Die neuen Daten zeigen eine verbesserte Leistung bei gleichzeitigen Downloads aus mehreren geografischen Regionen.`
    });
    
    console.log('Neue Version erstellt!');
    console.log('Neue Versionsnummer:', loadedPublication.version);
    console.log('Anzahl der Versionen im Verlauf:', loadedPublication.versionHistory.length);
    
    // 9. Speichere die neue Version
    console.log('\n9. Speichere und veröffentliche die neue Version');
    
    const newVersionResult = await loadedPublication.publish(storage);
    
    if (newVersionResult.success) {
      console.log('Neue Version erfolgreich veröffentlicht!');
      console.log('Neue IPFS CID:', loadedPublication.storage.ipfs);
      console.log('Neue BitTorrent InfoHash:', loadedPublication.storage.bittorrent);
    } else {
      console.error('Fehler beim Veröffentlichen der neuen Version:', newVersionResult.error);
    }
    
    // 10. Zeige Versionsverlauf
    console.log('\n10. Zeige Versionsverlauf der Publikation');
    
    console.log(`Versionsverlauf für "${loadedPublication.title}":`);
    loadedPublication.versionHistory.forEach((version, index) => {
      console.log(`${index + 1}. Version: ${version.version} (${new Date(version.timestamp).toLocaleDateString()})`);
      console.log(`   IPFS: ${version.storage.ipfs}`);
      console.log(`   BitTorrent: ${version.storage.bittorrent}`);
    });
  } catch (error) {
    console.error('Fehler im Publikationsbeispiel:', error);
  } finally {
    // Fahre den Storage-Service ordnungsgemäß herunter
    console.log('\nFahre den Storage-Service herunter');
    await storage.shutdown();
    
    console.log('\nPublikations-Demo abgeschlossen!');
  }
}

// Starte das Beispiel, wenn das Skript direkt ausgeführt wird
// In ESM wird dies anders gehandhabt als in CommonJS
const isMainModule = import.meta.url === `file://${process.argv[1]}`;
if (isMainModule) {
  publicationExample().catch(error => {
    console.error('Unbehandelter Fehler in der Publikations-Demo:', error);
    process.exit(1);
  });
}

export default publicationExample; 