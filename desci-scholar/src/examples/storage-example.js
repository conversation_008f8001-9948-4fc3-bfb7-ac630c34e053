/**
 * Beispiel für die Verwendung der dezentralen Speicherkomponenten
 * Demonstriert, wie Daten gespeichert und abgerufen werden können
 */

import StorageService, { StorageProtocol } from '../storage/StorageService.js.js';
import { toBuffer, bufferToUtf8String } from '../utils/buffer.js.js';
import crypto from 'crypto';
import crypto from 'crypto';

/**
 * Hauptfunktion für das Storage-Beispiel
 */
async function storageExample() {
  console.log('=== DeSci Scholar - Dezentrale Speicher-Demo ===');
  
  // Erstelle und initialisiere den Storage-Service
  const storage = new StorageService({
    defaultProtocol: StorageProtocol.BOTH,
    ipfs: {
      // IPFS-Konfiguration
      gateway: 'https://ipfs.io/ipfs/',
      pinningService: 'local'
    },
    bittorrent: {
      // BitTorrent-Konfiguration
      downloadPath: './downloads',
      dht: true
    }
  });
  
  console.log('Initialisiere Speicherdienste...');
  await storage.initialize();
  
  // 1. Speichere ein einfaches JSON-Objekt
  console.log('\n1. Speichere ein JSON-Objekt mit IPFS');
  const publicationData = {
    title: 'Dezentrale Speichermechanismen für wissenschaftliche Publikationen',
    authors: ['Maria Schmidt', 'Alexander Müller'],
    abstract: 'Diese Arbeit untersucht die Anwendung dezentraler Speichertechnologien wie IPFS und BitTorrent für die langfristige Archivierung wissenschaftlicher Publikationen.',
    keywords: ['IPFS', 'BitTorrent', 'Dezentralisierung', 'Open Science'],
    date: new Date().toISOString(),
    version: '1.0.0'
  };
  
  const jsonResult = await storage.storeData(publicationData, {
    protocol: StorageProtocol.IPFS,
    filename: 'publication.json',
    metadata: {
      contentType: 'application/json',
      version: '1.0'
    }
  });
  
  console.log('Speicherergebnis:', jsonResult);
  console.log('IPFS-URL:', jsonResult.urls.ipfs);
  
  // 2. Speichere Textdaten mit BitTorrent
  console.log('\n2. Speichere Textdaten mit BitTorrent');
  const textData = `
# Forschungsdaten

Diese Datei enthält Forschungsdaten für das Projekt "Dezentrale Wissenschaft".

## Messungen

- Messung 1: 42.5
- Messung 2: 38.2
- Messung 3: 44.1

## Beobachtungen

Die Daten zeigen einen Trend in Richtung dezentraler Systeme.
  `;
  
  const textResult = await storage.storeData(textData, {
    protocol: StorageProtocol.BITTORRENT,
    filename: 'research-data.md',
    metadata: {
      contentType: 'text/markdown',
      version: '1.0'
    }
  });
  
  console.log('Speicherergebnis:', textResult);
  console.log('BitTorrent Magnet-Link:', textResult.urls.bittorrent);
  
  // 3. Speichere Binärdaten mit beiden Protokollen
  console.log('\n3. Speichere Binärdaten mit beiden Protokollen');
  // Simuliere binäre Daten
  const binaryData = Buffer.from(Array.from({length: 1000}, () => Math.floor(Math.random() * 256)));
  
  const binaryResult = await storage.storeData(binaryData, {
    protocol: StorageProtocol.BOTH,
    filename: 'data.bin',
    metadata: {
      contentType: 'application/octet-stream',
      size: binaryData.length,
      description: 'Simulierte Binärdaten'
    }
  });
  
  console.log('Speicherergebnis:', binaryResult);
  console.log('IPFS-URL:', binaryResult.urls.ipfs);
  console.log('BitTorrent Magnet-Link:', binaryResult.urls.bittorrent);
  
  // 4. Lade die JSON-Daten von IPFS
  console.log('\n4. Lade die JSON-Daten von IPFS');
  const retrievedJson = await storage.retrieveData(jsonResult.id, {
    format: 'json'
  });
  
  console.log('Abgerufene Daten:', retrievedJson.data);
  
  // 5. Lade die Textdaten von BitTorrent
  console.log('\n5. Lade die Textdaten von BitTorrent');
  const retrievedText = await storage.retrieveData(textResult.id, {
    protocol: StorageProtocol.BITTORRENT,
    format: 'text'
  });
  
  console.log('Abgerufene Daten (Ausschnitt):', retrievedText.data.substring(0, 50) + '...');
  
  // 6. Lade die Binärdaten zuerst über IPFS, dann über BitTorrent
  console.log('\n6. Lade die Binärdaten über beide Protokolle');
  
  // Zuerst über IPFS
  const retrievedBinaryIpfs = await storage.retrieveData(binaryResult.id.ipfs, {
    protocol: StorageProtocol.IPFS,
    format: 'buffer'
  });
  
  console.log('Von IPFS abgerufene Binärdaten:',
    `${retrievedBinaryIpfs.data.length} Bytes, ` +
    `MD5: ${crypto.createHash('md5').update(retrievedBinaryIpfs.data).digest('hex')}`);
  
  // Dann über BitTorrent
  const retrievedBinaryBt = await storage.retrieveData(binaryResult.id.bittorrent, {
    protocol: StorageProtocol.BITTORRENT,
    format: 'buffer'
  });
  
  console.log('Von BitTorrent abgerufene Binärdaten:', 
    `${retrievedBinaryBt.data.length} Bytes`);
  
  // 7. Prüfe die Verfügbarkeit aller gespeicherten Daten
  console.log('\n7. Prüfe die Verfügbarkeit aller gespeicherten Daten');
  
  const jsonAvailability = await storage.checkAvailability(jsonResult.id);
  console.log('JSON-Daten verfügbar:', jsonAvailability.available);
  
  const textAvailability = await storage.checkAvailability(textResult.id);
  console.log('Text-Daten verfügbar:', textAvailability.available);
  
  const binaryAvailability = await storage.checkAvailability(binaryResult.id);
  console.log('Binärdaten verfügbar:', binaryAvailability.available);
  
  // 8. Entferne die gespeicherten Daten (falls gewünscht)
  if (process.env.CLEANUP === 'true') {
    console.log('\n8. Entferne die gespeicherten Daten');
    
    await storage.deleteData(jsonResult.id);
    console.log('JSON-Daten entfernt');
    
    await storage.deleteData(textResult.id);
    console.log('Text-Daten entfernt');
    
    await storage.deleteData(binaryResult.id);
    console.log('Binärdaten entfernt');
  }
  
  // 9. Fahre den Storage-Service ordnungsgemäß herunter
  console.log('\n9. Fahre den Storage-Service herunter');
  await storage.shutdown();
  
  console.log('\nStorage-Demo abgeschlossen!');
}

// Starte das Beispiel, wenn das Skript direkt ausgeführt wird
if (import.meta.url === `file://${process.argv[1]}`) {
  storageExample().catch(error => {
    console.error('Fehler in der Storage-Demo:', error);
    process.exit(1);
  });
}

export default storageExample;
