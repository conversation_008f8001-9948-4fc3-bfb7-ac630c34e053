/**
 * @fileoverview Example demonstrating how to use the StorageManager
 * This example shows how to store publications and datasets using
 * the integrated storage approach (IPFS for metadata, BitTorrent for large files)
 */

import StorageManager from '../storage/index.js/index.js';
import path from 'path';

// Sample usage of the StorageManager
async function demonstrateStorage() {
  // Initialize the storage manager
  const storageManager = new StorageManager();
  await storageManager.init();
  
  try {
    console.log('=== DeSci-Scholar Storage Demo ===');
    
    // Example 1: Store a small publication (will use IPFS)
    console.log('\n--- Example 1: Storing a small publication PDF (IPFS) ---');
    const publicationMetadata = {
      title: 'Decentralized Science: A New Paradigm',
      authors: ['Dr. <PERSON>', 'Prof. <PERSON>'],
      abstract: 'This paper explores how blockchain technologies can transform scientific research and publishing.',
      journal: 'Blockchain & Science',
      date: '2025-01-15',
      doi: '10.1234/desci.2025.001',
      keywords: ['blockchain', 'decentralized science', 'open access']
    };
    
    // Path to a small PDF file (<10MB)
    const smallPdfPath = path.join(__dirname, 'assets', 'sample-small-paper.pdf');
    
    // Store the publication
    const smallPubResult = await storageManager.storePublication(publicationMetadata, smallPdfPath);
    console.log('Publication stored successfully:');
    console.log(`- Metadata CID: ${smallPubResult.metadataCid}`);
    console.log(`- Content type: ${smallPubResult.contentReference.type}`);
    if (smallPubResult.contentReference.type === 'ipfs') {
      console.log(`- Content CID: ${smallPubResult.contentReference.cid}`);
      console.log(`- Gateway URL: ${smallPubResult.contentReference.gatewayUrl}`);
    }
    
    // Example 2: Store a large publication (will use BitTorrent)
    console.log('\n--- Example 2: Storing a large publication PDF (BitTorrent) ---');
    const largePubMetadata = {
      title: 'Comprehensive Analysis of Decentralized Storage Solutions',
      authors: ['Dr. Carlos Rodriguez', 'Dr. Diana Chen'],
      abstract: 'An extensive analysis of various decentralized storage solutions including IPFS, BitTorrent, and others.',
      journal: 'Decentralized Computing',
      date: '2025-02-20',
      doi: '10.5678/decomputing.2025.002',
      keywords: ['decentralized storage', 'IPFS', 'BitTorrent', 'performance analysis']
    };
    
    // Path to a large PDF file (>10MB)
    const largePdfPath = path.join(__dirname, 'assets', 'sample-large-paper.pdf');
    
    // Store the large publication
    const largePubResult = await storageManager.storePublication(largePubMetadata, largePdfPath);
    console.log('Large publication stored successfully:');
    console.log(`- Metadata CID: ${largePubResult.metadataCid}`);
    console.log(`- Content type: ${largePubResult.contentReference.type}`);
    if (largePubResult.contentReference.type === 'bittorrent') {
      console.log(`- Magnet URI: ${largePubResult.contentReference.magnetURI}`);
      console.log(`- Info Hash: ${largePubResult.contentReference.infoHash}`);
    }
    
    // Example 3: Store a research dataset (always uses BitTorrent)
    console.log('\n--- Example 3: Storing a research dataset (BitTorrent) ---');
    const datasetMetadata = {
      title: 'Climate Change Impact Dataset 2020-2025',
      description: 'Comprehensive dataset of climate measurements from 1000 stations worldwide',
      creator: 'Climate Research Institute',
      license: 'CC-BY-4.0',
      doi: '10.9012/dataset.2025.005',
      subjects: ['climate change', 'meteorology', 'environmental science'],
      format: 'CSV, NetCDF'
    };
    
    // Path to a dataset directory
    const datasetPath = path.join(__dirname, 'assets', 'climate-dataset');
    
    // Store the dataset
    const datasetResult = await storageManager.storeDataset(datasetMetadata, datasetPath);
    console.log('Dataset stored successfully:');
    console.log(`- Metadata CID: ${datasetResult.metadataCid}`);
    console.log(`- Magnet URI: ${datasetResult.torrentInfo.magnetURI}`);
    console.log(`- Info Hash: ${datasetResult.torrentInfo.infoHash}`);
    
    // Example 4: Retrieve content from different storage solutions
    console.log('\n--- Example 4: Retrieving content ---');
    
    // 4.1: Retrieve IPFS content
    if (smallPubResult.contentReference.type === 'ipfs') {
      const outputPath = path.join(__dirname, 'downloads', 'retrieved-paper-ipfs.pdf');
      const retrievedPath = await storageManager.retrieveContent(smallPubResult.contentReference, outputPath);
      console.log(`Retrieved IPFS content to: ${retrievedPath}`);
    }
    
    // 4.2: Retrieve BitTorrent content
    if (largePubResult.contentReference.type === 'bittorrent') {
      const outputPath = path.join(__dirname, 'downloads', 'retrieved-paper-bittorrent.pdf');
      const retrievedPath = await storageManager.retrieveContent(largePubResult.contentReference, outputPath);
      console.log(`Retrieved BitTorrent content to: ${retrievedPath}`);
    }
    
  } catch (error) {
    console.error('Error in storage demonstration:', error);
  } finally {
    // Cleanup
    await storageManager.cleanup();
    console.log('\nStorage demonstration completed');
  }
}

// Run the demonstration if this script is executed directly
// In ESM wird dies anders gehandhabt als in CommonJS
const isMainModule = import.meta.url === `file://${process.argv[1]}`;
if (isMainModule) {
  demonstrateStorage().catch(console.error);
}

export { demonstrateStorage };
