/**
 * @fileoverview Beispiel für die Verwendung der Semantic Scholar API
 * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});/**
 * @fileoverview Beispiel für die Verwendung der Semantic Scholar API
 * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});/**
 * @fileoverview Beispiel für die Verwendung der Semantic Scholar API
 * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});/**
 * @fileoverview Beispiel für die Verwendung der Semantic Scholar API
 * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});/**
 * @fileoverview Beispiel für die Verwendung der Semantic Scholar API
 * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});/**
 * @fileoverview Beispiel für die Verwendung der Semantic Scholar API
 * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});/**
 * @fileoverview Beispiel für die Verwendung der Semantic Scholar API
 * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});/**
 * @fileoverview Beispiel für die Verwendung der Semantic Scholar API
 * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});/**
 * @fileoverview Beispiel für die Verwendung der Semantic Scholar API
 * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});/**
 * @fileoverview Beispiel für die Verwendung der Semantic Scholar API
 * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});/**
 * @fileoverview Beispiel für die Verwendung der Semantic Scholar API
 * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});/**
 * @fileoverview Beispiel für die Verwendung der Semantic Scholar API
 * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});/**
 * @fileoverview Beispiel für die Verwendung der Semantic Scholar API
 * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});/**
 * @fileoverview Beispiel für die Verwendung der Semantic Scholar API
 * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});/**
 * @fileoverview Beispiel für die Verwendung der Semantic Scholar API
 * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});/**
 * @fileoverview Beispiel für die Verwendung der Semantic Scholar API
 * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});/**
 * @fileoverview Beispiel für die Verwendung der Semantic Scholar API
 * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});/**
 * @fileoverview Beispiel für die Verwendung der Semantic Scholar API
 * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});/**
 * @fileoverview Beispiel für die Verwendung der Semantic Scholar API
 * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});/**
 * @fileoverview Beispiel für die Verwendung der Semantic Scholar API
 * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});/**
 * @fileoverview Beispiel für die Verwendung der Semantic Scholar API
 * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});/**
 * @fileoverview Beispiel für die Verwendung der Semantic Scholar API
 * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});}); * @fileoverview Beispiel für die Verwendung der Semantic Scholar API
 * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
}); * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
}); * @fileoverview Beispiel für die Verwendung der Semantic Scholar API
 * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
}); * @fileoverview Beispiel für die Verwendung der Semantic Scholar API
 * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});});}); * @fileoverview Beispiel für die Verwendung der Semantic Scholar API
 * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});}); * @fileoverview Beispiel für die Verwendung der Semantic Scholar API
 * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
}); * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
}); * @fileoverview Beispiel für die Verwendung der Semantic Scholar API
 * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});});});}); * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
}); * @fileoverview Beispiel für die Verwendung der Semantic Scholar API
 * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});});});});});});});});}); * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
}); * @fileoverview Beispiel für die Verwendung der Semantic Scholar API
 * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});});});});});});});});});});});});}); * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
}); * @fileoverview Beispiel für die Verwendung der Semantic Scholar API
 * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});}); * @fileoverview Beispiel für die Verwendung der Semantic Scholar API
 * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
}); * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});}); * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});});  process.exit(1);
}); * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});});});});}); * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});});});});  process.exit(1);
});});}); * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});});});  process.exit(1);
}); * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});});});});}); * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});  process.exit(1);
});});});});}); * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});});});}); * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});  process.exit(1);
});});});});}); * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});  process.exit(1);
});});});});}); * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});});});});  process.exit(1);
});});});});});}); * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});  process.exit(1);
});});});});}); * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});  process.exit(1);
});});});});}); * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});});});});});  process.exit(1);
});});});});}); * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});});});});});});});});  process.exit(1);
});});});});});}); * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});  process.exit(1);
});});});});}); * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});  process.exit(1);
});});});});}); * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});});});  process.exit(1);
});});});});}); * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});});});});});});}); * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});});  process.exit(1);
});});});});});});}); * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});});});});  process.exit(1);
});});});});});});}); * 
 * Dieses Beispiel zeigt, wie die Semantic Scholar API in DeSci-Scholar
 * verwendet werden kann, um wissenschaftliche Publikationen zu suchen,
 * Details abzurufen und Zitationen zu analysieren.
 */

import SemanticScholarAPI from '../api/integrations/SemanticScholarAPI.js';

// Konfiguration
const USE_MCP_SERVER = true; // Auf false setzen, um direkt die offizielle API zu verwenden
const API_KEY = process.env.S2_API_KEY; // Optional: API-Schlüssel für höhere Rate-Limits

/**
 * Hauptfunktion für das Beispiel
 */
async function main() {
  console.log('Semantic Scholar API Beispiel');
  console.log('============================');
  
  // API-Client initialisieren
  const api = new SemanticScholarAPI({
    apiKey: API_KEY,
    useMcpServer: USE_MCP_SERVER,
    mcpServerUrl: 'http://localhost:3001'
  });
  
  try {
    // API initialisieren und Verfügbarkeit prüfen
    const isInitialized = await api.initialize();
    if (!isInitialized) {
      console.error('Fehler: API konnte nicht initialisiert werden');
      return;
    }
    
    // 1. Publikationen suchen
    console.log('\n1. Publikationen suchen:');
    const searchResults = await api.searchPapers({
      query: 'quantum computing',
      limit: 5,
      fields: 'title,authors,year,abstract,url'
    });
    
    console.log(`Gefunden: ${searchResults.total} Publikationen`);
    searchResults.data.forEach((paper, index) => {
      console.log(`\n[${index + 1}] ${paper.title} (${paper.year})`);
      console.log(`   Autoren: ${paper.authors.map(a => a.name).join(', ')}`);
      console.log(`   URL: ${paper.url}`);
    });
    
    // Erste Publikation für weitere Beispiele verwenden
    const examplePaperId = searchResults.data[0].paperId;
    
    // 2. Publikationsdetails abrufen
    console.log('\n\n2. Publikationsdetails:');
    const paperDetails = await api.getPaper(examplePaperId, 'title,abstract,authors,year,venue,citationCount');
    
    console.log(`Titel: ${paperDetails.title}`);
    console.log(`Jahr: ${paperDetails.year}`);
    console.log(`Venue: ${paperDetails.venue}`);
    console.log(`Zitationen: ${paperDetails.citationCount}`);
    console.log(`Abstract: ${paperDetails.abstract?.substring(0, 200)}...`);
    
    // 3. Zitationen abrufen
    console.log('\n\n3. Zitationen:');
    const citations = await api.getCitations(examplePaperId, {
      limit: 5,
      fields: 'contexts,citingPaper.title,citingPaper.year'
    });
    
    console.log(`Zitationen gefunden: ${citations.total}`);
    citations.data.forEach((citation, index) => {
      console.log(`\n[${index + 1}] ${citation.citingPaper.title} (${citation.citingPaper.year})`);
      if (citation.contexts && citation.contexts.length > 0) {
        console.log(`   Kontext: "${citation.contexts[0].substring(0, 100)}..."`);
      }
    });
    
    // 4. Autorensuche
    console.log('\n\n4. Autorensuche:');
    const authorName = paperDetails.authors[0].name;
    const authorResults = await api.searchAuthors({
      query: authorName,
      limit: 3,
      fields: 'name,paperCount,citationCount,hIndex'
    });
    
    console.log(`Autoren gefunden: ${authorResults.total}`);
    authorResults.data.forEach((author, index) => {
      console.log(`\n[${index + 1}] ${author.name}`);
      console.log(`   Publikationen: ${author.paperCount}`);
      console.log(`   Zitationen: ${author.citationCount}`);
      console.log(`   h-Index: ${author.hIndex}`);
    });
    
    // 5. Textausschnitte suchen
    console.log('\n\n5. Textausschnitte:');
    const snippetResults = await api.searchSnippets({
      query: 'quantum computing applications',
      limit: 3
    });
    
    if (snippetResults.data && snippetResults.data.length > 0) {
      snippetResults.data.forEach((result, index) => {
        console.log(`\n[${index + 1}] ${result.paper.title}`);
        console.log(`   Ausschnitt: "${result.snippet.text.substring(0, 150)}..."`);
      });
    } else {
      console.log('Keine Textausschnitte gefunden');
    }
    
    // API-Statistiken anzeigen
    console.log('\n\nAPI-Statistiken:');
    const stats = api.getStats();
    console.log(`Gesamtanfragen: ${stats.totalRequests}`);
    console.log(`Erfolgreiche Anfragen: ${stats.successfulRequests}`);
    console.log(`Fehlgeschlagene Anfragen: ${stats.failedRequests}`);
    console.log(`Erfolgsrate: ${stats.successRate}`);
    
  } catch (error) {
    console.error('Fehler bei der Ausführung des Beispiels:', error.message);
  }
}

// Beispiel ausführen
main().catch(error => {
  console.error('Unbehandelter Fehler:', error);
  process.exit(1);
});});