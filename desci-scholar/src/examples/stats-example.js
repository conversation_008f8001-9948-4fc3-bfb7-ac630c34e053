/**
 * stats-example.js
 * 
 * Dieses Beispiel demonstriert die Verwendung des StatsService für die Erfassung und
 * Analyse von Plattformstatistiken im DeSci-Scholar-System.
 */

import { fileURLToPath } from 'url';
import path from 'path';
import { StatsService } from '../stats/index.js';
import { DatabaseService } from '../database/index.js';
import { StorageService } from '../storage/index.js';
import { PublicationController } from '../publications/index.js';
import fs from 'fs/promises';

// Pfad für temporäre Dateien
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const TEMP_DIR = path.join(__dirname, 'temp');

// Hilfsfunktion zum Generieren einer zufälligen ID
const generateId = () => Math.random().toString(36).substring(2, 15);

// Hilfsfunktion zum Erstellen von Beispieldaten
async function generateSampleData(db, count = 5) {
  console.log(`Generiere ${count} Beispielpublikationen...`);
  
  // Beispielautoren
  const authors = [
    { id: generateId(), name: 'Dr. Emma Schmidt', affiliation: 'Universität Berlin' },
    { id: generateId(), name: 'Prof. Michael Chen', affiliation: 'MIT' },
    { id: generateId(), name: 'Dr. Sofia Rodriguez', affiliation: 'CERN' },
    { id: generateId(), name: 'Prof. James Wilson', affiliation: 'Oxford University' },
  ];
  
  // Erstelle Beispielpublikationen
  const publications = [];
  for (let i = 0; i < count; i++) {
    const publicationAuthors = [];
    const authorCount = 1 + Math.floor(Math.random() * 3); // 1-3 Autoren pro Publikation
    
    for (let j = 0; j < authorCount; j++) {
      const randomAuthor = authors[Math.floor(Math.random() * authors.length)];
      if (!publicationAuthors.some(a => a.id === randomAuthor.id)) {
        publicationAuthors.push(randomAuthor);
      }
    }
    
    const publication = {
      title: `Forschungsergebnisse zur ${['Quantenphysik', 'Klimaforschung', 'Genetik', 'Neurowissenschaft', 'Künstlichen Intelligenz'][i % 5]} #${i + 1}`,
      authors: publicationAuthors,
      abstract: `Dies ist eine Zusammenfassung der Forschungsarbeit #${i + 1}, die wichtige Ergebnisse in diesem Bereich präsentiert.`,
      keywords: ['wissenschaft', 'forschung', `thema-${i % 5}`],
      doi: `10.1234/desci-${generateId()}`,
      license: 'CC-BY-4.0',
      publicationDate: new Date(Date.now() - Math.random() * 10000000000), // Zufälliges Datum der letzten ~4 Monate
      stats: {
        views: Math.floor(Math.random() * 1000),
        downloads: Math.floor(Math.random() * 500),
        citations: Math.floor(Math.random() * 50)
      },
      files: [
        {
          name: `paper-${i + 1}.pdf`,
          size: 1024 * 1024 * (1 + Math.floor(Math.random() * 10)), // 1-10 MB
          mimeType: 'application/pdf',
          downloads: Math.floor(Math.random() * 300)
        },
        {
          name: `data-${i + 1}.csv`,
          size: 1024 * 512 * (1 + Math.floor(Math.random() * 5)), // 0.5-2.5 MB
          mimeType: 'text/csv',
          downloads: Math.floor(Math.random() * 200)
        }
      ]
    };
    
    publications.push(publication);
    const result = await db.insertOne('publications', publication);
    console.log(`Erstellt: Publikation #${i + 1} mit ID ${result.insertedId}`);
  }
  
  return { publications, authors };
}

// Hilfsfunktion zum Simulieren von Zugriffen auf Publikationen
async function simulateAccess(statsService, publications, count = 100) {
  console.log(`Simuliere ${count} Zugriffe auf Publikationen...`);
  
  const accessTypes = ['view', 'download', 'citation'];
  const countries = ['Deutschland', 'USA', 'Großbritannien', 'Japan', 'Frankreich', 'Kanada', 'Australien', 'Brasilien'];
  
  for (let i = 0; i < count; i++) {
    const randomPub = publications[Math.floor(Math.random() * publications.length)];
    const accessType = accessTypes[Math.floor(Math.random() * accessTypes.length)];
    const country = countries[Math.floor(Math.random() * countries.length)];
    
    // Simuliere Zugriffe über verschiedene Zeitpunkte (letzte 90 Tage)
    const randomDaysAgo = Math.floor(Math.random() * 90);
    const accessDate = new Date();
    accessDate.setDate(accessDate.getDate() - randomDaysAgo);
    
    await statsService.recordAccess({
      publicationId: randomPub._id,
      type: accessType,
      userId: i % 10 === 0 ? null : `user${i % 20}`, // Manchmal anonyme Zugriffe
      metadata: {
        timestamp: accessDate, // Überschreibe den automatischen Zeitstempel
        country,
        browser: ['Chrome', 'Firefox', 'Safari'][i % 3],
        platform: ['Desktop', 'Mobile', 'Tablet'][i % 3]
      }
    });
    
    if (i % 20 === 0) {
      process.stdout.write('.');
    }
  }
  console.log('\nZugriffssimulation abgeschlossen.');
}

// Hauptfunktion
async function main() {
  console.log('=== DeSci-Scholar StatsService Beispiel ===');
  
  // Stelle sicher, dass das temporäre Verzeichnis existiert
  try {
    await fs.mkdir(TEMP_DIR, { recursive: true });
  } catch (err) {
    // Verzeichnis existiert bereits oder kann nicht erstellt werden
  }
  
  // Initialisiere Dienste
  const databaseService = new DatabaseService({
    uri: 'mongodb://localhost:27017',
    dbName: 'desci_scholar_demo',
    inMemory: true
  });
  
  const storageService = new StorageService({
    storageDir: TEMP_DIR,
    tempDir: TEMP_DIR
  });
  
  const statsService = new StatsService({
    databaseService,
    storageService,
    config: {
      trendingPeriodDays: 30,
      cacheTimeMs: 1000 * 60 * 5 // 5 Minuten Cache
    }
  });
  
  try {
    // Initialisiere die Dienste
    console.log('Initialisiere Datenbankdienst...');
    await databaseService.connect();
    
    console.log('Initialisiere Speicherdienst...');
    await storageService.initialize();
    
    console.log('Initialisiere Statistikdienst...');
    await statsService.initialize();
    
    // Generiere Beispieldaten, wenn die Datenbank leer ist
    const pubCount = await databaseService.countDocuments('publications', {});
    
    let sampleData;
    if (pubCount === 0) {
      sampleData = await generateSampleData(databaseService, 10);
      
      // Simuliere Zugriffe
      await simulateAccess(statsService, sampleData.publications, 500);
    } else {
      console.log(`Die Datenbank enthält bereits ${pubCount} Publikationen. Überspringe Datengenerierung.`);
      sampleData = {
        publications: await databaseService.find('publications', {}, { limit: 10 }),
      };
    }
    
    // Rufe verschiedene Statistiken ab und zeige sie an
    console.log('\n=== GESAMTSTATISTIKEN ===');
    const totalStats = await statsService.getTotalStats();
    console.log('Gesamtpublikationen:', totalStats.publications);
    console.log('Gesamtaufrufe:', totalStats.views);
    console.log('Gesamtdownloads:', totalStats.downloads);
    console.log('Gesamtspeichernutzung:', totalStats.totalStorageFormatted);
    console.log('Durchschnittliche Downloads pro Publikation:', totalStats.avgDownloadsPerPublication);
    
    console.log('\n=== TREND-PUBLIKATIONEN ===');
    const trendingPubs = await statsService.getTrendingPublications({ limit: 3 });
    for (const pub of trendingPubs) {
      console.log(`${pub.title} (${pub.authors.map(a => a.name).join(', ')})`);
      console.log(`  Score: ${pub.metrics.score}, Aufrufe: ${pub.metrics.views}, Downloads: ${pub.metrics.downloads}`);
    }
    
    console.log('\n=== SPEICHERSTATISTIKEN ===');
    const storageStats = await statsService.getStorageStats();
    console.log('Gesamt:', storageStats.totalFormatted);
    console.log('Dateitypen:');
    for (const type of storageStats.fileTypes) {
      console.log(`  ${type.mimeType}: ${type.formattedSize} (${type.count} Dateien, ${type.percentageOfTotal})`);
    }
    
    console.log('\n=== DOWNLOADZAHLEN DER LETZTEN 4 WOCHEN ===');
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 28);
    const downloadStats = await statsService.getDownloadStats({ 
      startDate,
      groupBy: 'week' 
    });
    for (const stat of downloadStats) {
      console.log(`Woche ${stat.period}: ${stat.totalDownloads} Downloads von ${stat.uniquePublications} Publikationen`);
    }
    
    console.log('\n=== DETAILLIERTE PUBLIKATIONSSTATISTIKEN ===');
    if (sampleData.publications.length > 0) {
      const randomPub = sampleData.publications[0];
      const pubStats = await statsService.getPublicationStats(randomPub._id);
      
      console.log(`Titel: ${pubStats.title}`);
      console.log(`Autoren: ${pubStats.authors.map(a => a.name).join(', ')}`);
      console.log('Metriken:');
      console.log(`  Aufrufe: ${pubStats.metrics.views}`);
      console.log(`  Downloads: ${pubStats.metrics.downloads}`);
      console.log(`  Zitierungen: ${pubStats.metrics.citations}`);
      console.log(`  Impact-Score: ${pubStats.metrics.impactScore}`);
      console.log(`  Download-Konversionsrate: ${pubStats.metrics.downloadConversionRate}`);
      
      console.log('\nZeitreihe (letzte 7 Tage):');
      const lastWeek = pubStats.timeSeries.slice(-7);
      for (const day of lastWeek) {
        console.log(`  ${day.date}: ${day.views} Aufrufe, ${day.downloads} Downloads`);
      }
      
      if (pubStats.geoDistribution.length > 0) {
        console.log('\nGeografische Verteilung (Top 3):');
        for (const geo of pubStats.geoDistribution.slice(0, 3)) {
          console.log(`  ${geo.country}: ${geo.count} Zugriffe (${geo.percentage})`);
        }
      }
    }
    
  } catch (error) {
    console.error('Fehler im StatsService-Beispiel:', error);
  } finally {
    console.log('\nBereinige Ressourcen...');
    
    // Schließe Dienste
    await databaseService.disconnect();
    await storageService.shutdown();
    
    console.log('StatsService-Beispiel erfolgreich abgeschlossen.');
  }
}

// Starte das Beispiel
main().catch(err => console.error('Unbehandelter Fehler im Hauptprogramm:', err)); 