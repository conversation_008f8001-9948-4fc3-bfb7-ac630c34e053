import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import AdapterConfiguration from '../../components/adapters/AdapterConfiguration';
import { adapterProfiles } from '../../config/ui-config';
import './AdapterSettingsPage.css';

/**
 * Seite für die Adapter-Einstellungen
 * 
 * @component
 * @returns {JSX.Element} AdapterSettingsPage-Komponente
 */
const AdapterSettingsPage = () => {
  const navigate = useNavigate();
  
  // Zustand für die Adapter-Konfiguration
  const [adapterConfig, setAdapterConfig] = useState({});
  
  // Zustand für das Erfahrungslevel des Nutzers
  const [experienceLevel, setExperienceLevel] = useState('intermediate');
  
  // Zustand für den Speicherstatus
  const [saveStatus, setSaveStatus] = useState({
    saving: false,
    success: false,
    error: null
  });
  
  // Lade die gespeicherte Konfiguration beim <PERSON>f
  useEffect(() => {
    const loadSavedConfig = async () => {
      try {
        // In einer echten Anwendung würde hier die Konfiguration aus dem Backend geladen
        // Für dieses Beispiel verwenden wir localStorage
        const savedConfig = localStorage.getItem('adapterConfig');
        const savedExperienceLevel = localStorage.getItem('experienceLevel');
        
        if (savedConfig) {
          setAdapterConfig(JSON.parse(savedConfig));
        } else {
          // Standardkonfiguration verwenden
          setAdapterConfig(adapterProfiles.default.adapters);
        }
        
        if (savedExperienceLevel) {
          setExperienceLevel(savedExperienceLevel);
        }
      } catch (error) {
        console.error('Fehler beim Laden der Konfiguration:', error);
      }
    };
    
    loadSavedConfig();
  }, []);
  
  // Handler für Änderungen an der Adapter-Konfiguration
  const handleConfigurationChange = (newConfig) => {
    setAdapterConfig(newConfig);
  };
  
  // Handler für Änderungen am Erfahrungslevel
  const handleExperienceLevelChange = (event) => {
    setExperienceLevel(event.target.value);
    localStorage.setItem('experienceLevel', event.target.value);
  };
  
  // Konfiguration speichern
  const saveConfiguration = async () => {
    setSaveStatus({
      saving: true,
      success: false,
      error: null
    });
    
    try {
      // In einer echten Anwendung würde hier die Konfiguration an das Backend gesendet
      // Für dieses Beispiel speichern wir in localStorage
      localStorage.setItem('adapterConfig', JSON.stringify(adapterConfig));
      
      // Simuliere eine Verzögerung für das Speichern
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setSaveStatus({
        saving: false,
        success: true,
        error: null
      });
      
      // Erfolgsmeldung nach 3 Sekunden ausblenden
      setTimeout(() => {
        setSaveStatus(prev => ({
          ...prev,
          success: false
        }));
      }, 3000);
    } catch (error) {
      console.error('Fehler beim Speichern der Konfiguration:', error);
      
      setSaveStatus({
        saving: false,
        success: false,
        error: 'Fehler beim Speichern der Konfiguration. Bitte versuchen Sie es erneut.'
      });
    }
  };
  
  // Zurück zur vorherigen Seite
  const handleCancel = () => {
    navigate(-1);
  };
  
  return (
    <div className="adapter-settings-page">
      <div className="settings-header">
        <h1>Adapter-Einstellungen</h1>
        <p className="settings-description">
          Konfigurieren Sie die Blockchain-, Speicher- und Wallet-Adapter für Ihre DeSci-Scholar-Instanz.
          Die Adapter bestimmen, wie Ihre wissenschaftlichen Arbeiten als NFTs gespeichert und verwaltet werden.
        </p>
        
        <div className="experience-level-selector">
          <label htmlFor="experienceLevel">Erfahrungslevel:</label>
          <select 
            id="experienceLevel" 
            value={experienceLevel} 
            onChange={handleExperienceLevelChange}
          >
            <option value="beginner">Anfänger</option>
            <option value="intermediate">Fortgeschritten</option>
            <option value="expert">Experte</option>
          </select>
          
          <div className="experience-level-info">
            {experienceLevel === 'beginner' && (
              <p>Als Anfänger sehen Sie eine vereinfachte Oberfläche mit Standardoptionen.</p>
            )}
            {experienceLevel === 'intermediate' && (
              <p>Als fortgeschrittener Nutzer haben Sie Zugriff auf alle Adapter-Optionen.</p>
            )}
            {experienceLevel === 'expert' && (
              <p>Als Experte haben Sie Zugriff auf alle Adapter-Optionen und erweiterte Konfigurationsmöglichkeiten.</p>
            )}
          </div>
        </div>
      </div>
      
      <div className="settings-content">
        <AdapterConfiguration
          initialAdapters={adapterConfig}
          onConfigurationChange={handleConfigurationChange}
          experienceLevel={experienceLevel}
        />
      </div>
      
      <div className="settings-actions">
        <button 
          className="cancel-button"
          onClick={handleCancel}
        >
          Abbrechen
        </button>
        
        <button 
          className={`save-button ${saveStatus.saving ? 'saving' : ''}`}
          onClick={saveConfiguration}
          disabled={saveStatus.saving}
        >
          {saveStatus.saving ? 'Speichern...' : 'Einstellungen speichern'}
        </button>
      </div>
      
      {saveStatus.success && (
        <div className="save-success-message">
          Konfiguration erfolgreich gespeichert!
        </div>
      )}
      
      {saveStatus.error && (
        <div className="save-error-message">
          {saveStatus.error}
        </div>
      )}
    </div>
  );
};

export default AdapterSettingsPage;
