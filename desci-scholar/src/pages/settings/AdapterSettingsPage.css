.adapter-settings-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.settings-header {
  margin-bottom: 2rem;
}

.settings-header h1 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 2rem;
}

.settings-description {
  color: #666;
  line-height: 1.5;
  max-width: 800px;
  margin-bottom: 1.5rem;
}

.experience-level-selector {
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 6px;
  margin-bottom: 1.5rem;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 1rem;
}

.experience-level-selector label {
  font-weight: 500;
  color: #333;
}

.experience-level-selector select {
  padding: 0.5rem;
  border-radius: 4px;
  border: 1px solid #ccc;
  background-color: #fff;
  font-size: 1rem;
  color: #333;
}

.experience-level-info {
  flex-basis: 100%;
  margin-top: 0.5rem;
}

.experience-level-info p {
  margin: 0;
  font-size: 0.9rem;
  color: #666;
  font-style: italic;
}

.settings-content {
  margin-bottom: 2rem;
}

.settings-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e0e0e0;
}

.cancel-button,
.save-button {
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.cancel-button {
  background-color: #f0f0f0;
  color: #333;
}

.cancel-button:hover {
  background-color: #e0e0e0;
}

.save-button {
  background-color: #4a6cf7;
  color: white;
  min-width: 180px;
}

.save-button:hover {
  background-color: #3a5ce5;
}

.save-button.saving {
  background-color: #3a5ce5;
  cursor: wait;
  position: relative;
}

.save-button.saving::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.save-success-message,
.save-error-message {
  margin-top: 1rem;
  padding: 0.75rem 1rem;
  border-radius: 4px;
  animation: fadeIn 0.3s ease;
}

.save-success-message {
  background-color: #e8f5e9;
  color: #2e7d32;
  border: 1px solid #c8e6c9;
}

.save-error-message {
  background-color: #ffebee;
  color: #c62828;
  border: 1px solid #ffcdd2;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .adapter-settings-page {
    padding: 1rem;
  }
  
  .settings-actions {
    flex-direction: column-reverse;
    gap: 0.75rem;
  }
  
  .cancel-button,
  .save-button {
    width: 100%;
  }
}
