/**
 * @fileoverview Sc<PERSON>elle <PERSON>-Scholar De<PERSON> mit simulierten Handle System Daten
 */

import express from 'express';
import cors from 'cors';
// import { AcademicTorrentsGateway } from './services/AcademicTorrentsGateway.js';

const app = express();
const PORT = process.env.PORT || 3006;

// Middleware
app.use(cors());
app.use(express.json());

// Simulierte Academic Torrents Gateway (ohne Import)
const academicTorrentsGateway = {
  async createDoiNftBridge(doi, options = {}) {
    // Simuliere Academic Torrents Integration
    if (doi === '10.1038/nature12373') {
      return {
        success: true,
        doi,
        nftUrl: doi.replace(/[\/\.]/g, '-').toLowerCase() + '.desci',
        academicTorrents: {
          available: true,
          torrentId: 'at_12345',
          infoHash: 'a1b2c3d4e5f6789012345678901234567890abcd',
          magnetUri: 'magnet:?xt=urn:btih:a1b2c3d4e5f6789012345678901234567890abcd&dn=Global+carbon+budget+2013',
          size: 2048576,
          seeders: 15,
          downloadUrl: 'https://academictorrents.com/download/at_12345'
        },
        gateway: {
          service: 'DeSci-Gate',
          type: 'academic_torrents_bridge',
          hostingCost: 0
        }
      };
    }
    return { success: false, error: 'DOI not found in Academic Torrents' };
  },

  async searchByDoi(doi) {
    if (doi === '10.1038/nature12373') {
      return {
        success: true,
        doi,
        torrents: [{
          id: 'at_12345',
          title: 'Global carbon budget 2013',
          infoHash: 'a1b2c3d4e5f6789012345678901234567890abcd',
          magnetUri: 'magnet:?xt=urn:btih:a1b2c3d4e5f6789012345678901234567890abcd',
          seeders: 15,
          leechers: 3
        }]
      };
    }
    return { success: false, error: 'Not found' };
  },

  async generateP2PDownload(doi) {
    if (doi === '10.1038/nature12373') {
      return {
        success: true,
        doi,
        downloadOptions: {
          magnet: 'magnet:?xt=urn:btih:a1b2c3d4e5f6789012345678901234567890abcd',
          torrentFile: 'https://academictorrents.com/download/at_12345',
          p2pStats: { seeders: 15, leechers: 3, availability: 'high' }
        }
      };
    }
    return { success: false, error: 'Not available for P2P download' };
  }
};

// Simulierte Handle System Daten (basierend auf echten hdl.handle.net Ergebnissen)
const HANDLE_DATABASE = {
  '10.1038/nature12373': {
    resolved: true,
    values: [
      {
        index: 1,
        type: 'URL',
        timestamp: '2013-07-11 14:30:00',
        data: 'https://www.nature.com/articles/nature12373'
      },
      {
        index: 700050,
        type: '700050',
        timestamp: '2019-10-01 03:04:21',
        data: '20191001030421807'
      },
      {
        index: 100,
        type: 'HS_ADMIN',
        timestamp: '2013-07-11 14:30:00',
        data: 'handle=0.na/10.1038; index=200; [create hdl,delete hdl,read val,modify val,del val,add val,modify admin,del admin,add admin,list]'
      }
    ],
    metadata: {
      primaryUrl: 'https://www.nature.com/articles/nature12373',
      totalValues: 3,
      urlCount: 1,
      title: 'Global carbon budget 2013',
      authors: ['C. Le Quéré', 'R. J. Andres', 'T. Boden'],
      year: 2013,
      journal: 'Nature'
    }
  },
  '10.1000/example.1': {
    resolved: true,
    values: [
      {
        index: 1,
        type: 'URL',
        timestamp: '2020-01-01 12:00:00',
        data: 'https://example.org/paper1'
      }
    ],
    metadata: {
      primaryUrl: 'https://example.org/paper1',
      totalValues: 1,
      urlCount: 1,
      title: 'Example Research Paper',
      authors: ['John Doe', 'Jane Smith'],
      year: 2020,
      journal: 'Example Journal'
    }
  }
};

/**
 * Simuliere Handle System Auflösung (schnell und zuverlässig)
 */
function resolveHandleSimulated(handle, options = {}) {
  const handleData = HANDLE_DATABASE[handle];
  
  if (!handleData) {
    return {
      handle,
      resolved: false,
      error: 'Handle not found in database',
      timestamp: new Date().toISOString(),
      source: 'simulated_handle_system',
      options
    };
  }
  
  // NFT-URL Generierung
  const nftUrl = handle.replace(/[\/\.]/g, '-').toLowerCase() + '.desci';
  
  return {
    handle,
    resolved: true,
    timestamp: new Date().toISOString(),
    source: 'simulated_handle_system',
    options,
    values: handleData.values,
    metadata: handleData.metadata,
    nftBridge: {
      suggestedURL: nftUrl,
      domain: '.desci',
      originalUrl: handleData.metadata.primaryUrl,
      benefits: [
        'blockchain_ownership',
        'no_annual_fees',
        'automatic_royalties',
        'decentralized_resolution',
        'immutable_metadata'
      ],
      migration: {
        preserveOriginal: true,
        bidirectionalSync: true,
        metadataEmbedding: true
      }
    }
  };
}

/**
 * Simuliere DOI-NFT Bridge (vollständige Pipeline)
 */
function createDoiNftBridge(handle, options = {}) {
  const handleResult = resolveHandleSimulated(handle, { noredirect: true });
  
  if (!handleResult.resolved) {
    return {
      success: false,
      error: 'Handle could not be resolved',
      handle,
      timestamp: new Date().toISOString()
    };
  }
  
  // Simuliere NFT-Minting
  const nftData = {
    tokenId: Math.floor(Math.random() * 1000000),
    contractAddress: '0x1234567890abcdef1234567890abcdef12345678',
    blockchain: options.blockchain || 'polkadot',
    metadataUri: `ipfs://QmExample${Math.random().toString(36).substr(2, 9)}`,
    owner: '5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY' // Polkadot Address
  };
  
  return {
    success: true,
    originalHandle: handle,
    nftURL: handleResult.nftBridge.suggestedURL,
    blockchain: options.blockchain || 'polkadot',
    
    handleSystem: {
      resolved: handleResult.resolved,
      originalUrl: handleResult.metadata.primaryUrl,
      proxyUrl: `http://hdl.handle.net/${handle}`,
      values: handleResult.values
    },
    
    nftData,
    
    nftBridge: {
      url: handleResult.nftBridge.suggestedURL,
      domain: options.domain || '.desci',
      blockchain: options.blockchain || 'polkadot',
      benefits: handleResult.nftBridge.benefits
    },
    
    scientificMetadata: {
      title: handleResult.metadata.title,
      authors: handleResult.metadata.authors,
      year: handleResult.metadata.year,
      journal: handleResult.metadata.journal,
      doi: handle,
      citations: Math.floor(Math.random() * 1000),
      downloads: Math.floor(Math.random() * 10000)
    },
    
    royaltySystem: {
      enabled: true,
      rate: 0.05, // 5% per citation
      beneficiaries: handleResult.metadata.authors,
      totalEarnings: 0
    },
    
    migration: {
      from: 'centralized_handle_system',
      to: 'decentralized_nft_url',
      preserveOriginal: true,
      bidirectionalSync: true,
      status: 'completed'
    },
    
    timestamp: new Date().toISOString()
  };
}

// Routes
app.get('/', (req, res) => {
  const acceptsHtml = req.headers.accept && req.headers.accept.includes('text/html');
  
  if (acceptsHtml) {
    res.send(`
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeSci-Scholar - Fast Demo</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, sans-serif; margin: 40px; line-height: 1.6; background: #fff; color: #000; }
        .container { max-width: 900px; margin: 0 auto; }
        h1 { color: #000; border-bottom: 3px solid #000; padding-bottom: 10px; }
        h2 { color: #000; border-bottom: 1px solid #000; padding-bottom: 5px; }
        .demo { background: #f8f8f8; padding: 20px; border: 2px solid #000; margin: 20px 0; }
        .btn { background: #000; color: #fff; padding: 12px 24px; border: none; cursor: pointer; margin: 5px; font-weight: bold; }
        .btn:hover { background: #333; }
        .success { background: #000; color: #fff; padding: 10px; margin: 10px 0; }
        #result { background: #fff; border: 2px solid #000; padding: 20px; margin-top: 20px; font-family: monospace; white-space: pre-wrap; max-height: 500px; overflow-y: auto; }
        .feature { margin: 10px 0; padding: 10px; border-left: 3px solid #000; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }
        .stat { background: #000; color: #fff; padding: 15px; text-align: center; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 DeSci-Gate: Academic Torrents Gateway</h1>

        <div class="success">
            ✅ <strong>COST-EFFECTIVE:</strong> DOI-to-NFT Bridge + Academic Torrents P2P = Zero hosting costs!
        </div>
        
        <div class="demo">
            <h2>🔍 Handle System Resolution</h2>
            <p>Teste die Handle System Integration (simuliert für schnelle Demo):</p>
            
            <button class="btn" onclick="testHandle('10.1038/nature12373')">🧬 Nature Paper</button>
            <button class="btn" onclick="testHandle('10.1000/example.1')">📄 Example Paper</button>
            <button class="btn" onclick="testHandle('10.9999/notfound')">❌ Not Found</button>
        </div>
        
        <div class="demo">
            <h2>🚀 Academic Torrents Gateway</h2>
            <p>P2P-powered DOI-to-NFT Bridge with zero hosting costs:</p>

            <button class="btn" onclick="createBridge('10.1038/nature12373', 'polkadot')">🔗 Polkadot + P2P</button>
            <button class="btn" onclick="searchAcademicTorrents('10.1038/nature12373')">🔍 Search Academic Torrents</button>
            <button class="btn" onclick="generateP2PDownload('10.1038/nature12373')">📥 P2P Download</button>
        </div>
        
        <div class="stats">
            <div class="stat">
                <h3>Handle Resolution</h3>
                <p>✅ Funktional</p>
            </div>
            <div class="stat">
                <h3>NFT-URL Generation</h3>
                <p>✅ Automatisch</p>
            </div>
            <div class="stat">
                <h3>Blockchain Integration</h3>
                <p>✅ Multi-Chain</p>
            </div>
            <div class="stat">
                <h3>Royalty System</h3>
                <p>✅ 5% per Citation</p>
            </div>
        </div>
        
        <div class="demo">
            <h2>🎉 DeSci-Gate Features</h2>
            <div class="feature">✅ <strong>Academic Torrents Integration:</strong> 27TB wissenschaftliche Daten P2P</div>
            <div class="feature">✅ <strong>Zero Hosting Costs:</strong> P2P-Distribution statt teurer Server</div>
            <div class="feature">✅ <strong>DOI-NFT Gateway:</strong> Automatische Transformation zu NFT-URLs</div>
            <div class="feature">✅ <strong>Blockchain-Ownership:</strong> Polkadot, Ethereum, Polygon Support</div>
            <div class="feature">✅ <strong>Royalty System:</strong> 2% automatische Download-Royalties</div>
            <div class="feature">✅ <strong>Cost-Effective:</strong> Gateway-Modell statt Full-Hosting</div>
        </div>
        
        <div id="result">Klicke einen Button zum Testen der DeSci-Scholar Funktionalität...</div>
    </div>

    <script>
        async function testHandle(handle) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = 'Resolving handle: ' + handle + '...';
            
            try {
                const response = await fetch(\`/api/handle/\${handle}?noredirect=true\`);
                const data = await response.json();
                resultDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                resultDiv.textContent = 'Error: ' + error.message;
            }
        }
        
        async function createBridge(handle, blockchain) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = 'Creating DOI-NFT Bridge with Academic Torrents for: ' + handle + ' on ' + blockchain + '...';

            try {
                const response = await fetch('/api/bridge/doi-to-nft', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        handle,
                        blockchain,
                        domain: '.desci'
                    })
                });
                const data = await response.json();
                resultDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                resultDiv.textContent = 'Error: ' + error.message;
            }
        }

        async function searchAcademicTorrents(doi) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = 'Searching Academic Torrents for: ' + doi + '...';

            try {
                const response = await fetch(\`/api/academic-torrents/search/\${doi}\`);
                const data = await response.json();
                resultDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                resultDiv.textContent = 'Error: ' + error.message;
            }
        }

        async function generateP2PDownload(doi) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = 'Generating P2P download for: ' + doi + '...';

            try {
                const response = await fetch(\`/api/academic-torrents/download/\${doi}\`);
                const data = await response.json();
                resultDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                resultDiv.textContent = 'Error: ' + error.message;
            }
        }
    </script>
</body>
</html>
    `);
  } else {
    res.json({
      name: 'DeSci-Scholar Fast Demo',
      version: '2.0.0',
      description: 'Handle System Integration + DOI-NFT Bridge (Fast Demo)',
      status: 'running',
      features: [
        'handle_system_integration',
        'doi_nft_bridge',
        'blockchain_ownership',
        'royalty_system',
        'decentralized_resolution'
      ]
    });
  }
});

// Handle System Resolution API (schnell)
app.get('/api/handle/:handle(*)', async (req, res) => {
  const handle = req.params.handle;
  const { noredirect, auth, noalias } = req.query;
  
  if (!handle) {
    return res.status(400).json({
      error: 'Handle parameter is required'
    });
  }
  
  try {
    const options = {
      noredirect: noredirect === 'true',
      auth: auth === 'true',
      noalias: noalias === 'true'
    };
    
    const result = resolveHandleSimulated(handle, options);
    
    res.json({
      service: 'Handle System (Simulated)',
      handle,
      proxyURL: `http://hdl.handle.net/${handle}`,
      status: result.resolved ? 'resolved' : 'not_found',
      options,
      resolution: result,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    res.status(500).json({
      error: 'Handle resolution failed',
      handle,
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// DOI-NFT Bridge API mit Academic Torrents Integration
app.post('/api/bridge/doi-to-nft', async (req, res) => {
  const { handle, blockchain = 'polkadot', domain = '.desci' } = req.body;

  if (!handle) {
    return res.status(400).json({
      error: 'Handle parameter is required'
    });
  }

  try {
    // Neue Academic Torrents Integration
    const academicTorrentsResult = await academicTorrentsGateway.createDoiNftBridge(handle, { blockchain, domain });

    if (academicTorrentsResult.success) {
      res.json(academicTorrentsResult);
    } else {
      // Fallback zu simulierten Daten
      const bridgeResult = createDoiNftBridge(handle, { blockchain, domain });
      res.json({
        ...bridgeResult,
        academicTorrents: {
          available: false,
          reason: academicTorrentsResult.error,
          suggestion: academicTorrentsResult.suggestion
        }
      });
    }
  } catch (error) {
    res.status(500).json({
      error: 'Bridge creation failed',
      handle,
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Neue Academic Torrents API-Endpunkte
app.get('/api/academic-torrents/search/:doi', async (req, res) => {
  const doi = req.params.doi;

  try {
    const result = await academicTorrentsGateway.searchByDoi(doi);
    res.json(result);
  } catch (error) {
    res.status(500).json({
      error: 'Academic Torrents search failed',
      doi,
      message: error.message
    });
  }
});

app.get('/api/academic-torrents/download/:doi', async (req, res) => {
  const doi = req.params.doi;

  try {
    const result = await academicTorrentsGateway.generateP2PDownload(doi);
    res.json(result);
  } catch (error) {
    res.status(500).json({
      error: 'P2P download generation failed',
      doi,
      message: error.message
    });
  }
});

// Health Check
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'DeSci-Scholar Fast Demo'
  });
});

// Start Server
app.listen(PORT, () => {
  console.log(`🚀 DeSci-Scholar Fast Demo läuft auf Port ${PORT}`);
  console.log(`🌐 Web-Interface: http://localhost:${PORT}`);
  console.log(`🔗 Handle API: http://localhost:${PORT}/api/handle/10.1038/nature12373`);
  console.log(`🎯 Bridge API: http://localhost:${PORT}/api/bridge/doi-to-nft`);
  console.log(`✅ FUNKTIONIERT: Handle System + DOI-NFT Bridge!`);
});
