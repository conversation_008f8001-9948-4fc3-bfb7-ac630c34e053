/**
 * Zero-Knowledge-Proof-Verifizierer für wissenschaftliche Publikationen
 * Ermöglicht die Verifizierung von Autorschaft und Datenintegrität ohne sensible Daten preiszugeben
 */

import crypto from 'crypto';

class ZkVerifier {
  constructor(options = {}) {
    this.options = {
      hashAlgorithm: 'sha256',
      ...options
    };
  }
  
  /**
   * Generiert einen Beweis für die Autorschaft einer Publikation
   * @param {Object} publication - Publikationsdaten
   * @param {Object} author - Autorschaftsdaten
   * @param {string} privateKey - Privater Schlüssel des Autors
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateAuthorshipProof(publication, author, privateKey) {
    try {
      // 1. Erstelle einen Hash der Publikation
      const publicationHash = this.hashPublication(publication);
      
      // 2. E<PERSON>elle einen Hash der Autorschaftsdaten
      const authorshipHash = this.hashAuthorship(publication.doi, author);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(publicationHash, authorshipHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. <PERSON><PERSON><PERSON> den Beweis
      return {
        publicationHash,
        authorshipClaim: {
          authorId: author.id,
          authorName: author.name,
          authorRole: author.role || 'author',
          authorshipHash
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Autorschaftsbeweises:', error);
      throw new Error(`Autorschaftsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Autorschaftsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} publication - Publikationsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel des Autors
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyAuthorshipProof(proof, publication, publicKey) {
    try {
      // 1. Berechne den Publikationshash
      const publicationHash = this.hashPublication(publication);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (publicationHash !== proof.publicationHash) {
        console.warn('Publikationshash stimmt nicht überein');
        return false;
      }
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(
        proof.publicationHash,
        proof.authorshipClaim.authorshipHash
      );
      
      // 4. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Autorschaftsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Integrität von Forschungsdaten
   * @param {Object} data - Forschungsdaten
   * @param {string} privateKey - Privater Schlüssel
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateDataIntegrityProof(data, privateKey) {
    try {
      // 1. Erstelle einen Hash der Daten
      const dataHash = this.hashData(data);
      
      // 2. Erstelle Metadaten über die Daten (ohne die eigentlichen Daten)
      const metadata = this.extractDataMetadata(data);
      
      // 3. Signiere den Hash mit dem privaten Schlüssel
      const signature = this.sign(dataHash, privateKey);
      
      // 4. Erstelle den Beweis
      return {
        dataHash,
        metadata,
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Datenintegritätsbeweises:', error);
      throw new Error(`Datenintegritätsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Datenintegritätsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} data - Forschungsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyDataIntegrityProof(proof, data, publicKey) {
    try {
      // 1. Berechne den Datenhash
      const dataHash = this.hashData(data);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (dataHash !== proof.dataHash) {
        console.warn('Datenhash stimmt nicht überein');
        return false;
      }
      
      // 3. Verifiziere die Signatur
      const isSignatureValid = this.verify(dataHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Datenintegritätsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Peer-Review einer Publikation
   * @param {Object} review - Review-Daten
   * @param {string} publicationDoi - DOI der Publikation
   * @param {string} privateKey - Privater Schlüssel des Reviewers
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generatePeerReviewProof(review, publicationDoi, privateKey) {
    try {
      // 1. Erstelle einen Hash des Reviews
      const reviewHash = this.hashReview(review);
      
      // 2. Erstelle einen Hash der Publikations-DOI
      const publicationHash = this.hashString(publicationDoi);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        reviewHash,
        publicationDoi,
        reviewerInfo: {
          id: review.reviewerId,
          name: review.reviewerName,
          institution: review.reviewerInstitution
        },
        reviewMetadata: {
          date: review.date,
          recommendation: review.recommendation,
          confidence: review.confidence
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Peer-Review-Beweises:', error);
      throw new Error(`Peer-Review-Beweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Peer-Review-Beweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} review - Review-Daten
   * @param {string} publicKey - Öffentlicher Schlüssel des Reviewers
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyPeerReviewProof(proof, review, publicKey) {
    try {
      // 1. Berechne den Review-Hash
      const reviewHash = this.hashReview(review);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (reviewHash !== proof.reviewHash) {
        console.warn('Review-Hash stimmt nicht überein');
        return false;
      }
      
      // 3. Berechne den Publikations-Hash
      const publicationHash = this.hashString(proof.publicationDoi);
      
      // 4. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 5. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Peer-Review-Beweises:', error);
      return false;
    }
  }
  
  /**
   * Hasht eine Publikation
   * @private
   */
  hashPublication(publication) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      doi: publication.doi,
      title: publication.title,
      authors: publication.authors.map(author => ({
        name: author.name,
        orcid: author.orcid
      })),
      abstract: publication.abstract,
      publicationYear: publication.publicationYear
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht Autorschaftsdaten
   * @private
   */
  hashAuthorship(doi, author) {
    const authorshipData = {
      doi,
      authorId: author.id,
      authorName: author.name,
      authorRole: author.role || 'author'
    };
    
    return this.hashObject(authorshipData);
  }
  
  /**
   * Hasht Forschungsdaten
   * @private
   */
  hashData(data) {
    return this.hashObject(data);
  }
  
  /**
   * Extrahiert Metadaten aus Forschungsdaten
   * @private
   */
  extractDataMetadata(data) {
    // Extrahiere Metadaten ohne die eigentlichen Daten
    const metadata = {
      dataType: typeof data,
      isArray: Array.isArray(data),
      length: Array.isArray(data) ? data.length : undefined,
      keys: typeof data === 'object' ? Object.keys(data) : undefined,
      timestamp: Date.now()
    };
    
    return metadata;
  }
  
  /**
   * Hasht Review-Daten
   * @private
   */
  hashReview(review) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      reviewerId: review.reviewerId,
      publicationDoi: review.publicationDoi,
      date: review.date,
      recommendation: review.recommendation,
      confidence: review.confidence,
      comments: review.comments
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht ein Objekt
   * @private
   */
  hashObject(obj) {
    const jsonString = JSON.stringify(obj);
    return this.hashString(jsonString);
  }
  
  /**
   * Hasht einen String
   * @private
   */
  hashString(str) {
    return crypto
      .createHash(this.options.hashAlgorithm)
      .update(str)
      .digest('hex');
  }
  
  /**
   * Kombiniert zwei Hashes
   * @private
   */
  combineHashes(hash1, hash2) {
    return this.hashString(hash1 + hash2);
  }
  
  /**
   * Signiert einen Hash mit einem privaten Schlüssel
   * @private
   */
  sign(hash, privateKey) {
    // In einer realen Implementierung würde hier eine echte Signatur erstellt
    // Für diese Beispielimplementierung simulieren wir die Signatur
    const signer = crypto.createSign('RSA-SHA256');
    signer.update(hash);
    
    try {
      // Versuche, eine echte Signatur zu erstellen, falls ein gültiger Schlüssel vorliegt
      return signer.sign(privateKey, 'hex');
    } catch (error) {
      console.warn('Simuliere Signatur, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Signatur
      return crypto
        .createHash('sha256')
        .update(hash + privateKey)
        .digest('hex');
    }
  }
  
  /**
   * Verifiziert eine Signatur mit einem öffentlichen Schlüssel
   * @private
   */
  verify(hash, signature, publicKey) {
    // In einer realen Implementierung würde hier eine echte Signaturverifizierung durchgeführt
    // Für diese Beispielimplementierung simulieren wir die Verifizierung
    try {
      // Versuche, eine echte Verifizierung durchzuführen, falls ein gültiger Schlüssel vorliegt
      const verifier = crypto.createVerify('RSA-SHA256');
      verifier.update(hash);
      return verifier.verify(publicKey, signature, 'hex');
    } catch (error) {
      console.warn('Simuliere Verifizierung, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Verifizierung
      const simulatedSignature = crypto
        .createHash('sha256')
        .update(hash + publicKey)
        .digest('hex');
      
      return simulatedSignature === signature;
    }
  }
}

export default ZkVerifier;/**
 * Zero-Knowledge-Proof-Verifizierer für wissenschaftliche Publikationen
 * Ermöglicht die Verifizierung von Autorschaft und Datenintegrität ohne sensible Daten preiszugeben
 */

import crypto from 'crypto';

class ZkVerifier {
  constructor(options = {}) {
    this.options = {
      hashAlgorithm: 'sha256',
      ...options
    };
  }
  
  /**
   * Generiert einen Beweis für die Autorschaft einer Publikation
   * @param {Object} publication - Publikationsdaten
   * @param {Object} author - Autorschaftsdaten
   * @param {string} privateKey - Privater Schlüssel des Autors
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateAuthorshipProof(publication, author, privateKey) {
    try {
      // 1. Erstelle einen Hash der Publikation
      const publicationHash = this.hashPublication(publication);
      
      // 2. Erstelle einen Hash der Autorschaftsdaten
      const authorshipHash = this.hashAuthorship(publication.doi, author);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(publicationHash, authorshipHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        publicationHash,
        authorshipClaim: {
          authorId: author.id,
          authorName: author.name,
          authorRole: author.role || 'author',
          authorshipHash
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Autorschaftsbeweises:', error);
      throw new Error(`Autorschaftsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Autorschaftsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} publication - Publikationsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel des Autors
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyAuthorshipProof(proof, publication, publicKey) {
    try {
      // 1. Berechne den Publikationshash
      const publicationHash = this.hashPublication(publication);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (publicationHash !== proof.publicationHash) {
        console.warn('Publikationshash stimmt nicht überein');
        return false;
      }
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(
        proof.publicationHash,
        proof.authorshipClaim.authorshipHash
      );
      
      // 4. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Autorschaftsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Integrität von Forschungsdaten
   * @param {Object} data - Forschungsdaten
   * @param {string} privateKey - Privater Schlüssel
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateDataIntegrityProof(data, privateKey) {
    try {
      // 1. Erstelle einen Hash der Daten
      const dataHash = this.hashData(data);
      
      // 2. Erstelle Metadaten über die Daten (ohne die eigentlichen Daten)
      const metadata = this.extractDataMetadata(data);
      
      // 3. Signiere den Hash mit dem privaten Schlüssel
      const signature = this.sign(dataHash, privateKey);
      
      // 4. Erstelle den Beweis
      return {
        dataHash,
        metadata,
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Datenintegritätsbeweises:', error);
      throw new Error(`Datenintegritätsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Datenintegritätsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} data - Forschungsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyDataIntegrityProof(proof, data, publicKey) {
    try {
      // 1. Berechne den Datenhash
      const dataHash = this.hashData(data);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (dataHash !== proof.dataHash) {
        console.warn('Datenhash stimmt nicht überein');
        return false;
      }
      
      // 3. Verifiziere die Signatur
      const isSignatureValid = this.verify(dataHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Datenintegritätsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Peer-Review einer Publikation
   * @param {Object} review - Review-Daten
   * @param {string} publicationDoi - DOI der Publikation
   * @param {string} privateKey - Privater Schlüssel des Reviewers
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generatePeerReviewProof(review, publicationDoi, privateKey) {
    try {
      // 1. Erstelle einen Hash des Reviews
      const reviewHash = this.hashReview(review);
      
      // 2. Erstelle einen Hash der Publikations-DOI
      const publicationHash = this.hashString(publicationDoi);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        reviewHash,
        publicationDoi,
        reviewerInfo: {
          id: review.reviewerId,
          name: review.reviewerName,
          institution: review.reviewerInstitution
        },
        reviewMetadata: {
          date: review.date,
          recommendation: review.recommendation,
          confidence: review.confidence
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Peer-Review-Beweises:', error);
      throw new Error(`Peer-Review-Beweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Peer-Review-Beweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} review - Review-Daten
   * @param {string} publicKey - Öffentlicher Schlüssel des Reviewers
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyPeerReviewProof(proof, review, publicKey) {
    try {
      // 1. Berechne den Review-Hash
      const reviewHash = this.hashReview(review);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (reviewHash !== proof.reviewHash) {
        console.warn('Review-Hash stimmt nicht überein');
        return false;
      }
      
      // 3. Berechne den Publikations-Hash
      const publicationHash = this.hashString(proof.publicationDoi);
      
      // 4. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 5. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Peer-Review-Beweises:', error);
      return false;
    }
  }
  
  /**
   * Hasht eine Publikation
   * @private
   */
  hashPublication(publication) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      doi: publication.doi,
      title: publication.title,
      authors: publication.authors.map(author => ({
        name: author.name,
        orcid: author.orcid
      })),
      abstract: publication.abstract,
      publicationYear: publication.publicationYear
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht Autorschaftsdaten
   * @private
   */
  hashAuthorship(doi, author) {
    const authorshipData = {
      doi,
      authorId: author.id,
      authorName: author.name,
      authorRole: author.role || 'author'
    };
    
    return this.hashObject(authorshipData);
  }
  
  /**
   * Hasht Forschungsdaten
   * @private
   */
  hashData(data) {
    return this.hashObject(data);
  }
  
  /**
   * Extrahiert Metadaten aus Forschungsdaten
   * @private
   */
  extractDataMetadata(data) {
    // Extrahiere Metadaten ohne die eigentlichen Daten
    const metadata = {
      dataType: typeof data,
      isArray: Array.isArray(data),
      length: Array.isArray(data) ? data.length : undefined,
      keys: typeof data === 'object' ? Object.keys(data) : undefined,
      timestamp: Date.now()
    };
    
    return metadata;
  }
  
  /**
   * Hasht Review-Daten
   * @private
   */
  hashReview(review) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      reviewerId: review.reviewerId,
      publicationDoi: review.publicationDoi,
      date: review.date,
      recommendation: review.recommendation,
      confidence: review.confidence,
      comments: review.comments
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht ein Objekt
   * @private
   */
  hashObject(obj) {
    const jsonString = JSON.stringify(obj);
    return this.hashString(jsonString);
  }
  
  /**
   * Hasht einen String
   * @private
   */
  hashString(str) {
    return crypto
      .createHash(this.options.hashAlgorithm)
      .update(str)
      .digest('hex');
  }
  
  /**
   * Kombiniert zwei Hashes
   * @private
   */
  combineHashes(hash1, hash2) {
    return this.hashString(hash1 + hash2);
  }
  
  /**
   * Signiert einen Hash mit einem privaten Schlüssel
   * @private
   */
  sign(hash, privateKey) {
    // In einer realen Implementierung würde hier eine echte Signatur erstellt
    // Für diese Beispielimplementierung simulieren wir die Signatur
    const signer = crypto.createSign('RSA-SHA256');
    signer.update(hash);
    
    try {
      // Versuche, eine echte Signatur zu erstellen, falls ein gültiger Schlüssel vorliegt
      return signer.sign(privateKey, 'hex');
    } catch (error) {
      console.warn('Simuliere Signatur, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Signatur
      return crypto
        .createHash('sha256')
        .update(hash + privateKey)
        .digest('hex');
    }
  }
  
  /**
   * Verifiziert eine Signatur mit einem öffentlichen Schlüssel
   * @private
   */
  verify(hash, signature, publicKey) {
    // In einer realen Implementierung würde hier eine echte Signaturverifizierung durchgeführt
    // Für diese Beispielimplementierung simulieren wir die Verifizierung
    try {
      // Versuche, eine echte Verifizierung durchzuführen, falls ein gültiger Schlüssel vorliegt
      const verifier = crypto.createVerify('RSA-SHA256');
      verifier.update(hash);
      return verifier.verify(publicKey, signature, 'hex');
    } catch (error) {
      console.warn('Simuliere Verifizierung, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Verifizierung
      const simulatedSignature = crypto
        .createHash('sha256')
        .update(hash + publicKey)
        .digest('hex');
      
      return simulatedSignature === signature;
    }
  }
}

export default ZkVerifier;/**
 * Zero-Knowledge-Proof-Verifizierer für wissenschaftliche Publikationen
 * Ermöglicht die Verifizierung von Autorschaft und Datenintegrität ohne sensible Daten preiszugeben
 */

import crypto from 'crypto';

class ZkVerifier {
  constructor(options = {}) {
    this.options = {
      hashAlgorithm: 'sha256',
      ...options
    };
  }
  
  /**
   * Generiert einen Beweis für die Autorschaft einer Publikation
   * @param {Object} publication - Publikationsdaten
   * @param {Object} author - Autorschaftsdaten
   * @param {string} privateKey - Privater Schlüssel des Autors
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateAuthorshipProof(publication, author, privateKey) {
    try {
      // 1. Erstelle einen Hash der Publikation
      const publicationHash = this.hashPublication(publication);
      
      // 2. Erstelle einen Hash der Autorschaftsdaten
      const authorshipHash = this.hashAuthorship(publication.doi, author);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(publicationHash, authorshipHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        publicationHash,
        authorshipClaim: {
          authorId: author.id,
          authorName: author.name,
          authorRole: author.role || 'author',
          authorshipHash
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Autorschaftsbeweises:', error);
      throw new Error(`Autorschaftsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Autorschaftsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} publication - Publikationsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel des Autors
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyAuthorshipProof(proof, publication, publicKey) {
    try {
      // 1. Berechne den Publikationshash
      const publicationHash = this.hashPublication(publication);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (publicationHash !== proof.publicationHash) {
        console.warn('Publikationshash stimmt nicht überein');
        return false;
      }
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(
        proof.publicationHash,
        proof.authorshipClaim.authorshipHash
      );
      
      // 4. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Autorschaftsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Integrität von Forschungsdaten
   * @param {Object} data - Forschungsdaten
   * @param {string} privateKey - Privater Schlüssel
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateDataIntegrityProof(data, privateKey) {
    try {
      // 1. Erstelle einen Hash der Daten
      const dataHash = this.hashData(data);
      
      // 2. Erstelle Metadaten über die Daten (ohne die eigentlichen Daten)
      const metadata = this.extractDataMetadata(data);
      
      // 3. Signiere den Hash mit dem privaten Schlüssel
      const signature = this.sign(dataHash, privateKey);
      
      // 4. Erstelle den Beweis
      return {
        dataHash,
        metadata,
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Datenintegritätsbeweises:', error);
      throw new Error(`Datenintegritätsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Datenintegritätsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} data - Forschungsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyDataIntegrityProof(proof, data, publicKey) {
    try {
      // 1. Berechne den Datenhash
      const dataHash = this.hashData(data);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (dataHash !== proof.dataHash) {
        console.warn('Datenhash stimmt nicht überein');
        return false;
      }
      
      // 3. Verifiziere die Signatur
      const isSignatureValid = this.verify(dataHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Datenintegritätsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Peer-Review einer Publikation
   * @param {Object} review - Review-Daten
   * @param {string} publicationDoi - DOI der Publikation
   * @param {string} privateKey - Privater Schlüssel des Reviewers
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generatePeerReviewProof(review, publicationDoi, privateKey) {
    try {
      // 1. Erstelle einen Hash des Reviews
      const reviewHash = this.hashReview(review);
      
      // 2. Erstelle einen Hash der Publikations-DOI
      const publicationHash = this.hashString(publicationDoi);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        reviewHash,
        publicationDoi,
        reviewerInfo: {
          id: review.reviewerId,
          name: review.reviewerName,
          institution: review.reviewerInstitution
        },
        reviewMetadata: {
          date: review.date,
          recommendation: review.recommendation,
          confidence: review.confidence
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Peer-Review-Beweises:', error);
      throw new Error(`Peer-Review-Beweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Peer-Review-Beweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} review - Review-Daten
   * @param {string} publicKey - Öffentlicher Schlüssel des Reviewers
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyPeerReviewProof(proof, review, publicKey) {
    try {
      // 1. Berechne den Review-Hash
      const reviewHash = this.hashReview(review);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (reviewHash !== proof.reviewHash) {
        console.warn('Review-Hash stimmt nicht überein');
        return false;
      }
      
      // 3. Berechne den Publikations-Hash
      const publicationHash = this.hashString(proof.publicationDoi);
      
      // 4. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 5. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Peer-Review-Beweises:', error);
      return false;
    }
  }
  
  /**
   * Hasht eine Publikation
   * @private
   */
  hashPublication(publication) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      doi: publication.doi,
      title: publication.title,
      authors: publication.authors.map(author => ({
        name: author.name,
        orcid: author.orcid
      })),
      abstract: publication.abstract,
      publicationYear: publication.publicationYear
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht Autorschaftsdaten
   * @private
   */
  hashAuthorship(doi, author) {
    const authorshipData = {
      doi,
      authorId: author.id,
      authorName: author.name,
      authorRole: author.role || 'author'
    };
    
    return this.hashObject(authorshipData);
  }
  
  /**
   * Hasht Forschungsdaten
   * @private
   */
  hashData(data) {
    return this.hashObject(data);
  }
  
  /**
   * Extrahiert Metadaten aus Forschungsdaten
   * @private
   */
  extractDataMetadata(data) {
    // Extrahiere Metadaten ohne die eigentlichen Daten
    const metadata = {
      dataType: typeof data,
      isArray: Array.isArray(data),
      length: Array.isArray(data) ? data.length : undefined,
      keys: typeof data === 'object' ? Object.keys(data) : undefined,
      timestamp: Date.now()
    };
    
    return metadata;
  }
  
  /**
   * Hasht Review-Daten
   * @private
   */
  hashReview(review) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      reviewerId: review.reviewerId,
      publicationDoi: review.publicationDoi,
      date: review.date,
      recommendation: review.recommendation,
      confidence: review.confidence,
      comments: review.comments
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht ein Objekt
   * @private
   */
  hashObject(obj) {
    const jsonString = JSON.stringify(obj);
    return this.hashString(jsonString);
  }
  
  /**
   * Hasht einen String
   * @private
   */
  hashString(str) {
    return crypto
      .createHash(this.options.hashAlgorithm)
      .update(str)
      .digest('hex');
  }
  
  /**
   * Kombiniert zwei Hashes
   * @private
   */
  combineHashes(hash1, hash2) {
    return this.hashString(hash1 + hash2);
  }
  
  /**
   * Signiert einen Hash mit einem privaten Schlüssel
   * @private
   */
  sign(hash, privateKey) {
    // In einer realen Implementierung würde hier eine echte Signatur erstellt
    // Für diese Beispielimplementierung simulieren wir die Signatur
    const signer = crypto.createSign('RSA-SHA256');
    signer.update(hash);
    
    try {
      // Versuche, eine echte Signatur zu erstellen, falls ein gültiger Schlüssel vorliegt
      return signer.sign(privateKey, 'hex');
    } catch (error) {
      console.warn('Simuliere Signatur, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Signatur
      return crypto
        .createHash('sha256')
        .update(hash + privateKey)
        .digest('hex');
    }
  }
  
  /**
   * Verifiziert eine Signatur mit einem öffentlichen Schlüssel
   * @private
   */
  verify(hash, signature, publicKey) {
    // In einer realen Implementierung würde hier eine echte Signaturverifizierung durchgeführt
    // Für diese Beispielimplementierung simulieren wir die Verifizierung
    try {
      // Versuche, eine echte Verifizierung durchzuführen, falls ein gültiger Schlüssel vorliegt
      const verifier = crypto.createVerify('RSA-SHA256');
      verifier.update(hash);
      return verifier.verify(publicKey, signature, 'hex');
    } catch (error) {
      console.warn('Simuliere Verifizierung, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Verifizierung
      const simulatedSignature = crypto
        .createHash('sha256')
        .update(hash + publicKey)
        .digest('hex');
      
      return simulatedSignature === signature;
    }
  }
}

export default ZkVerifier;/**
 * Zero-Knowledge-Proof-Verifizierer für wissenschaftliche Publikationen
 * Ermöglicht die Verifizierung von Autorschaft und Datenintegrität ohne sensible Daten preiszugeben
 */

import crypto from 'crypto';

class ZkVerifier {
  constructor(options = {}) {
    this.options = {
      hashAlgorithm: 'sha256',
      ...options
    };
  }
  
  /**
   * Generiert einen Beweis für die Autorschaft einer Publikation
   * @param {Object} publication - Publikationsdaten
   * @param {Object} author - Autorschaftsdaten
   * @param {string} privateKey - Privater Schlüssel des Autors
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateAuthorshipProof(publication, author, privateKey) {
    try {
      // 1. Erstelle einen Hash der Publikation
      const publicationHash = this.hashPublication(publication);
      
      // 2. Erstelle einen Hash der Autorschaftsdaten
      const authorshipHash = this.hashAuthorship(publication.doi, author);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(publicationHash, authorshipHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        publicationHash,
        authorshipClaim: {
          authorId: author.id,
          authorName: author.name,
          authorRole: author.role || 'author',
          authorshipHash
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Autorschaftsbeweises:', error);
      throw new Error(`Autorschaftsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Autorschaftsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} publication - Publikationsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel des Autors
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyAuthorshipProof(proof, publication, publicKey) {
    try {
      // 1. Berechne den Publikationshash
      const publicationHash = this.hashPublication(publication);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (publicationHash !== proof.publicationHash) {
        console.warn('Publikationshash stimmt nicht überein');
        return false;
      }
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(
        proof.publicationHash,
        proof.authorshipClaim.authorshipHash
      );
      
      // 4. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Autorschaftsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Integrität von Forschungsdaten
   * @param {Object} data - Forschungsdaten
   * @param {string} privateKey - Privater Schlüssel
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateDataIntegrityProof(data, privateKey) {
    try {
      // 1. Erstelle einen Hash der Daten
      const dataHash = this.hashData(data);
      
      // 2. Erstelle Metadaten über die Daten (ohne die eigentlichen Daten)
      const metadata = this.extractDataMetadata(data);
      
      // 3. Signiere den Hash mit dem privaten Schlüssel
      const signature = this.sign(dataHash, privateKey);
      
      // 4. Erstelle den Beweis
      return {
        dataHash,
        metadata,
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Datenintegritätsbeweises:', error);
      throw new Error(`Datenintegritätsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Datenintegritätsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} data - Forschungsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyDataIntegrityProof(proof, data, publicKey) {
    try {
      // 1. Berechne den Datenhash
      const dataHash = this.hashData(data);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (dataHash !== proof.dataHash) {
        console.warn('Datenhash stimmt nicht überein');
        return false;
      }
      
      // 3. Verifiziere die Signatur
      const isSignatureValid = this.verify(dataHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Datenintegritätsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Peer-Review einer Publikation
   * @param {Object} review - Review-Daten
   * @param {string} publicationDoi - DOI der Publikation
   * @param {string} privateKey - Privater Schlüssel des Reviewers
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generatePeerReviewProof(review, publicationDoi, privateKey) {
    try {
      // 1. Erstelle einen Hash des Reviews
      const reviewHash = this.hashReview(review);
      
      // 2. Erstelle einen Hash der Publikations-DOI
      const publicationHash = this.hashString(publicationDoi);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        reviewHash,
        publicationDoi,
        reviewerInfo: {
          id: review.reviewerId,
          name: review.reviewerName,
          institution: review.reviewerInstitution
        },
        reviewMetadata: {
          date: review.date,
          recommendation: review.recommendation,
          confidence: review.confidence
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Peer-Review-Beweises:', error);
      throw new Error(`Peer-Review-Beweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Peer-Review-Beweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} review - Review-Daten
   * @param {string} publicKey - Öffentlicher Schlüssel des Reviewers
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyPeerReviewProof(proof, review, publicKey) {
    try {
      // 1. Berechne den Review-Hash
      const reviewHash = this.hashReview(review);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (reviewHash !== proof.reviewHash) {
        console.warn('Review-Hash stimmt nicht überein');
        return false;
      }
      
      // 3. Berechne den Publikations-Hash
      const publicationHash = this.hashString(proof.publicationDoi);
      
      // 4. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 5. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Peer-Review-Beweises:', error);
      return false;
    }
  }
  
  /**
   * Hasht eine Publikation
   * @private
   */
  hashPublication(publication) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      doi: publication.doi,
      title: publication.title,
      authors: publication.authors.map(author => ({
        name: author.name,
        orcid: author.orcid
      })),
      abstract: publication.abstract,
      publicationYear: publication.publicationYear
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht Autorschaftsdaten
   * @private
   */
  hashAuthorship(doi, author) {
    const authorshipData = {
      doi,
      authorId: author.id,
      authorName: author.name,
      authorRole: author.role || 'author'
    };
    
    return this.hashObject(authorshipData);
  }
  
  /**
   * Hasht Forschungsdaten
   * @private
   */
  hashData(data) {
    return this.hashObject(data);
  }
  
  /**
   * Extrahiert Metadaten aus Forschungsdaten
   * @private
   */
  extractDataMetadata(data) {
    // Extrahiere Metadaten ohne die eigentlichen Daten
    const metadata = {
      dataType: typeof data,
      isArray: Array.isArray(data),
      length: Array.isArray(data) ? data.length : undefined,
      keys: typeof data === 'object' ? Object.keys(data) : undefined,
      timestamp: Date.now()
    };
    
    return metadata;
  }
  
  /**
   * Hasht Review-Daten
   * @private
   */
  hashReview(review) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      reviewerId: review.reviewerId,
      publicationDoi: review.publicationDoi,
      date: review.date,
      recommendation: review.recommendation,
      confidence: review.confidence,
      comments: review.comments
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht ein Objekt
   * @private
   */
  hashObject(obj) {
    const jsonString = JSON.stringify(obj);
    return this.hashString(jsonString);
  }
  
  /**
   * Hasht einen String
   * @private
   */
  hashString(str) {
    return crypto
      .createHash(this.options.hashAlgorithm)
      .update(str)
      .digest('hex');
  }
  
  /**
   * Kombiniert zwei Hashes
   * @private
   */
  combineHashes(hash1, hash2) {
    return this.hashString(hash1 + hash2);
  }
  
  /**
   * Signiert einen Hash mit einem privaten Schlüssel
   * @private
   */
  sign(hash, privateKey) {
    // In einer realen Implementierung würde hier eine echte Signatur erstellt
    // Für diese Beispielimplementierung simulieren wir die Signatur
    const signer = crypto.createSign('RSA-SHA256');
    signer.update(hash);
    
    try {
      // Versuche, eine echte Signatur zu erstellen, falls ein gültiger Schlüssel vorliegt
      return signer.sign(privateKey, 'hex');
    } catch (error) {
      console.warn('Simuliere Signatur, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Signatur
      return crypto
        .createHash('sha256')
        .update(hash + privateKey)
        .digest('hex');
    }
  }
  
  /**
   * Verifiziert eine Signatur mit einem öffentlichen Schlüssel
   * @private
   */
  verify(hash, signature, publicKey) {
    // In einer realen Implementierung würde hier eine echte Signaturverifizierung durchgeführt
    // Für diese Beispielimplementierung simulieren wir die Verifizierung
    try {
      // Versuche, eine echte Verifizierung durchzuführen, falls ein gültiger Schlüssel vorliegt
      const verifier = crypto.createVerify('RSA-SHA256');
      verifier.update(hash);
      return verifier.verify(publicKey, signature, 'hex');
    } catch (error) {
      console.warn('Simuliere Verifizierung, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Verifizierung
      const simulatedSignature = crypto
        .createHash('sha256')
        .update(hash + publicKey)
        .digest('hex');
      
      return simulatedSignature === signature;
    }
  }
}

export default ZkVerifier;/**
 * Zero-Knowledge-Proof-Verifizierer für wissenschaftliche Publikationen
 * Ermöglicht die Verifizierung von Autorschaft und Datenintegrität ohne sensible Daten preiszugeben
 */

import crypto from 'crypto';

class ZkVerifier {
  constructor(options = {}) {
    this.options = {
      hashAlgorithm: 'sha256',
      ...options
    };
  }
  
  /**
   * Generiert einen Beweis für die Autorschaft einer Publikation
   * @param {Object} publication - Publikationsdaten
   * @param {Object} author - Autorschaftsdaten
   * @param {string} privateKey - Privater Schlüssel des Autors
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateAuthorshipProof(publication, author, privateKey) {
    try {
      // 1. Erstelle einen Hash der Publikation
      const publicationHash = this.hashPublication(publication);
      
      // 2. Erstelle einen Hash der Autorschaftsdaten
      const authorshipHash = this.hashAuthorship(publication.doi, author);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(publicationHash, authorshipHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        publicationHash,
        authorshipClaim: {
          authorId: author.id,
          authorName: author.name,
          authorRole: author.role || 'author',
          authorshipHash
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Autorschaftsbeweises:', error);
      throw new Error(`Autorschaftsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Autorschaftsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} publication - Publikationsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel des Autors
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyAuthorshipProof(proof, publication, publicKey) {
    try {
      // 1. Berechne den Publikationshash
      const publicationHash = this.hashPublication(publication);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (publicationHash !== proof.publicationHash) {
        console.warn('Publikationshash stimmt nicht überein');
        return false;
      }
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(
        proof.publicationHash,
        proof.authorshipClaim.authorshipHash
      );
      
      // 4. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Autorschaftsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Integrität von Forschungsdaten
   * @param {Object} data - Forschungsdaten
   * @param {string} privateKey - Privater Schlüssel
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateDataIntegrityProof(data, privateKey) {
    try {
      // 1. Erstelle einen Hash der Daten
      const dataHash = this.hashData(data);
      
      // 2. Erstelle Metadaten über die Daten (ohne die eigentlichen Daten)
      const metadata = this.extractDataMetadata(data);
      
      // 3. Signiere den Hash mit dem privaten Schlüssel
      const signature = this.sign(dataHash, privateKey);
      
      // 4. Erstelle den Beweis
      return {
        dataHash,
        metadata,
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Datenintegritätsbeweises:', error);
      throw new Error(`Datenintegritätsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Datenintegritätsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} data - Forschungsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyDataIntegrityProof(proof, data, publicKey) {
    try {
      // 1. Berechne den Datenhash
      const dataHash = this.hashData(data);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (dataHash !== proof.dataHash) {
        console.warn('Datenhash stimmt nicht überein');
        return false;
      }
      
      // 3. Verifiziere die Signatur
      const isSignatureValid = this.verify(dataHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Datenintegritätsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Peer-Review einer Publikation
   * @param {Object} review - Review-Daten
   * @param {string} publicationDoi - DOI der Publikation
   * @param {string} privateKey - Privater Schlüssel des Reviewers
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generatePeerReviewProof(review, publicationDoi, privateKey) {
    try {
      // 1. Erstelle einen Hash des Reviews
      const reviewHash = this.hashReview(review);
      
      // 2. Erstelle einen Hash der Publikations-DOI
      const publicationHash = this.hashString(publicationDoi);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        reviewHash,
        publicationDoi,
        reviewerInfo: {
          id: review.reviewerId,
          name: review.reviewerName,
          institution: review.reviewerInstitution
        },
        reviewMetadata: {
          date: review.date,
          recommendation: review.recommendation,
          confidence: review.confidence
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Peer-Review-Beweises:', error);
      throw new Error(`Peer-Review-Beweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Peer-Review-Beweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} review - Review-Daten
   * @param {string} publicKey - Öffentlicher Schlüssel des Reviewers
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyPeerReviewProof(proof, review, publicKey) {
    try {
      // 1. Berechne den Review-Hash
      const reviewHash = this.hashReview(review);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (reviewHash !== proof.reviewHash) {
        console.warn('Review-Hash stimmt nicht überein');
        return false;
      }
      
      // 3. Berechne den Publikations-Hash
      const publicationHash = this.hashString(proof.publicationDoi);
      
      // 4. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 5. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Peer-Review-Beweises:', error);
      return false;
    }
  }
  
  /**
   * Hasht eine Publikation
   * @private
   */
  hashPublication(publication) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      doi: publication.doi,
      title: publication.title,
      authors: publication.authors.map(author => ({
        name: author.name,
        orcid: author.orcid
      })),
      abstract: publication.abstract,
      publicationYear: publication.publicationYear
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht Autorschaftsdaten
   * @private
   */
  hashAuthorship(doi, author) {
    const authorshipData = {
      doi,
      authorId: author.id,
      authorName: author.name,
      authorRole: author.role || 'author'
    };
    
    return this.hashObject(authorshipData);
  }
  
  /**
   * Hasht Forschungsdaten
   * @private
   */
  hashData(data) {
    return this.hashObject(data);
  }
  
  /**
   * Extrahiert Metadaten aus Forschungsdaten
   * @private
   */
  extractDataMetadata(data) {
    // Extrahiere Metadaten ohne die eigentlichen Daten
    const metadata = {
      dataType: typeof data,
      isArray: Array.isArray(data),
      length: Array.isArray(data) ? data.length : undefined,
      keys: typeof data === 'object' ? Object.keys(data) : undefined,
      timestamp: Date.now()
    };
    
    return metadata;
  }
  
  /**
   * Hasht Review-Daten
   * @private
   */
  hashReview(review) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      reviewerId: review.reviewerId,
      publicationDoi: review.publicationDoi,
      date: review.date,
      recommendation: review.recommendation,
      confidence: review.confidence,
      comments: review.comments
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht ein Objekt
   * @private
   */
  hashObject(obj) {
    const jsonString = JSON.stringify(obj);
    return this.hashString(jsonString);
  }
  
  /**
   * Hasht einen String
   * @private
   */
  hashString(str) {
    return crypto
      .createHash(this.options.hashAlgorithm)
      .update(str)
      .digest('hex');
  }
  
  /**
   * Kombiniert zwei Hashes
   * @private
   */
  combineHashes(hash1, hash2) {
    return this.hashString(hash1 + hash2);
  }
  
  /**
   * Signiert einen Hash mit einem privaten Schlüssel
   * @private
   */
  sign(hash, privateKey) {
    // In einer realen Implementierung würde hier eine echte Signatur erstellt
    // Für diese Beispielimplementierung simulieren wir die Signatur
    const signer = crypto.createSign('RSA-SHA256');
    signer.update(hash);
    
    try {
      // Versuche, eine echte Signatur zu erstellen, falls ein gültiger Schlüssel vorliegt
      return signer.sign(privateKey, 'hex');
    } catch (error) {
      console.warn('Simuliere Signatur, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Signatur
      return crypto
        .createHash('sha256')
        .update(hash + privateKey)
        .digest('hex');
    }
  }
  
  /**
   * Verifiziert eine Signatur mit einem öffentlichen Schlüssel
   * @private
   */
  verify(hash, signature, publicKey) {
    // In einer realen Implementierung würde hier eine echte Signaturverifizierung durchgeführt
    // Für diese Beispielimplementierung simulieren wir die Verifizierung
    try {
      // Versuche, eine echte Verifizierung durchzuführen, falls ein gültiger Schlüssel vorliegt
      const verifier = crypto.createVerify('RSA-SHA256');
      verifier.update(hash);
      return verifier.verify(publicKey, signature, 'hex');
    } catch (error) {
      console.warn('Simuliere Verifizierung, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Verifizierung
      const simulatedSignature = crypto
        .createHash('sha256')
        .update(hash + publicKey)
        .digest('hex');
      
      return simulatedSignature === signature;
    }
  }
}

export default ZkVerifier;/**
 * Zero-Knowledge-Proof-Verifizierer für wissenschaftliche Publikationen
 * Ermöglicht die Verifizierung von Autorschaft und Datenintegrität ohne sensible Daten preiszugeben
 */

import crypto from 'crypto';

class ZkVerifier {
  constructor(options = {}) {
    this.options = {
      hashAlgorithm: 'sha256',
      ...options
    };
  }
  
  /**
   * Generiert einen Beweis für die Autorschaft einer Publikation
   * @param {Object} publication - Publikationsdaten
   * @param {Object} author - Autorschaftsdaten
   * @param {string} privateKey - Privater Schlüssel des Autors
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateAuthorshipProof(publication, author, privateKey) {
    try {
      // 1. Erstelle einen Hash der Publikation
      const publicationHash = this.hashPublication(publication);
      
      // 2. Erstelle einen Hash der Autorschaftsdaten
      const authorshipHash = this.hashAuthorship(publication.doi, author);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(publicationHash, authorshipHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        publicationHash,
        authorshipClaim: {
          authorId: author.id,
          authorName: author.name,
          authorRole: author.role || 'author',
          authorshipHash
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Autorschaftsbeweises:', error);
      throw new Error(`Autorschaftsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Autorschaftsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} publication - Publikationsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel des Autors
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyAuthorshipProof(proof, publication, publicKey) {
    try {
      // 1. Berechne den Publikationshash
      const publicationHash = this.hashPublication(publication);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (publicationHash !== proof.publicationHash) {
        console.warn('Publikationshash stimmt nicht überein');
        return false;
      }
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(
        proof.publicationHash,
        proof.authorshipClaim.authorshipHash
      );
      
      // 4. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Autorschaftsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Integrität von Forschungsdaten
   * @param {Object} data - Forschungsdaten
   * @param {string} privateKey - Privater Schlüssel
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateDataIntegrityProof(data, privateKey) {
    try {
      // 1. Erstelle einen Hash der Daten
      const dataHash = this.hashData(data);
      
      // 2. Erstelle Metadaten über die Daten (ohne die eigentlichen Daten)
      const metadata = this.extractDataMetadata(data);
      
      // 3. Signiere den Hash mit dem privaten Schlüssel
      const signature = this.sign(dataHash, privateKey);
      
      // 4. Erstelle den Beweis
      return {
        dataHash,
        metadata,
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Datenintegritätsbeweises:', error);
      throw new Error(`Datenintegritätsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Datenintegritätsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} data - Forschungsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyDataIntegrityProof(proof, data, publicKey) {
    try {
      // 1. Berechne den Datenhash
      const dataHash = this.hashData(data);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (dataHash !== proof.dataHash) {
        console.warn('Datenhash stimmt nicht überein');
        return false;
      }
      
      // 3. Verifiziere die Signatur
      const isSignatureValid = this.verify(dataHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Datenintegritätsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Peer-Review einer Publikation
   * @param {Object} review - Review-Daten
   * @param {string} publicationDoi - DOI der Publikation
   * @param {string} privateKey - Privater Schlüssel des Reviewers
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generatePeerReviewProof(review, publicationDoi, privateKey) {
    try {
      // 1. Erstelle einen Hash des Reviews
      const reviewHash = this.hashReview(review);
      
      // 2. Erstelle einen Hash der Publikations-DOI
      const publicationHash = this.hashString(publicationDoi);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        reviewHash,
        publicationDoi,
        reviewerInfo: {
          id: review.reviewerId,
          name: review.reviewerName,
          institution: review.reviewerInstitution
        },
        reviewMetadata: {
          date: review.date,
          recommendation: review.recommendation,
          confidence: review.confidence
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Peer-Review-Beweises:', error);
      throw new Error(`Peer-Review-Beweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Peer-Review-Beweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} review - Review-Daten
   * @param {string} publicKey - Öffentlicher Schlüssel des Reviewers
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyPeerReviewProof(proof, review, publicKey) {
    try {
      // 1. Berechne den Review-Hash
      const reviewHash = this.hashReview(review);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (reviewHash !== proof.reviewHash) {
        console.warn('Review-Hash stimmt nicht überein');
        return false;
      }
      
      // 3. Berechne den Publikations-Hash
      const publicationHash = this.hashString(proof.publicationDoi);
      
      // 4. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 5. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Peer-Review-Beweises:', error);
      return false;
    }
  }
  
  /**
   * Hasht eine Publikation
   * @private
   */
  hashPublication(publication) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      doi: publication.doi,
      title: publication.title,
      authors: publication.authors.map(author => ({
        name: author.name,
        orcid: author.orcid
      })),
      abstract: publication.abstract,
      publicationYear: publication.publicationYear
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht Autorschaftsdaten
   * @private
   */
  hashAuthorship(doi, author) {
    const authorshipData = {
      doi,
      authorId: author.id,
      authorName: author.name,
      authorRole: author.role || 'author'
    };
    
    return this.hashObject(authorshipData);
  }
  
  /**
   * Hasht Forschungsdaten
   * @private
   */
  hashData(data) {
    return this.hashObject(data);
  }
  
  /**
   * Extrahiert Metadaten aus Forschungsdaten
   * @private
   */
  extractDataMetadata(data) {
    // Extrahiere Metadaten ohne die eigentlichen Daten
    const metadata = {
      dataType: typeof data,
      isArray: Array.isArray(data),
      length: Array.isArray(data) ? data.length : undefined,
      keys: typeof data === 'object' ? Object.keys(data) : undefined,
      timestamp: Date.now()
    };
    
    return metadata;
  }
  
  /**
   * Hasht Review-Daten
   * @private
   */
  hashReview(review) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      reviewerId: review.reviewerId,
      publicationDoi: review.publicationDoi,
      date: review.date,
      recommendation: review.recommendation,
      confidence: review.confidence,
      comments: review.comments
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht ein Objekt
   * @private
   */
  hashObject(obj) {
    const jsonString = JSON.stringify(obj);
    return this.hashString(jsonString);
  }
  
  /**
   * Hasht einen String
   * @private
   */
  hashString(str) {
    return crypto
      .createHash(this.options.hashAlgorithm)
      .update(str)
      .digest('hex');
  }
  
  /**
   * Kombiniert zwei Hashes
   * @private
   */
  combineHashes(hash1, hash2) {
    return this.hashString(hash1 + hash2);
  }
  
  /**
   * Signiert einen Hash mit einem privaten Schlüssel
   * @private
   */
  sign(hash, privateKey) {
    // In einer realen Implementierung würde hier eine echte Signatur erstellt
    // Für diese Beispielimplementierung simulieren wir die Signatur
    const signer = crypto.createSign('RSA-SHA256');
    signer.update(hash);
    
    try {
      // Versuche, eine echte Signatur zu erstellen, falls ein gültiger Schlüssel vorliegt
      return signer.sign(privateKey, 'hex');
    } catch (error) {
      console.warn('Simuliere Signatur, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Signatur
      return crypto
        .createHash('sha256')
        .update(hash + privateKey)
        .digest('hex');
    }
  }
  
  /**
   * Verifiziert eine Signatur mit einem öffentlichen Schlüssel
   * @private
   */
  verify(hash, signature, publicKey) {
    // In einer realen Implementierung würde hier eine echte Signaturverifizierung durchgeführt
    // Für diese Beispielimplementierung simulieren wir die Verifizierung
    try {
      // Versuche, eine echte Verifizierung durchzuführen, falls ein gültiger Schlüssel vorliegt
      const verifier = crypto.createVerify('RSA-SHA256');
      verifier.update(hash);
      return verifier.verify(publicKey, signature, 'hex');
    } catch (error) {
      console.warn('Simuliere Verifizierung, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Verifizierung
      const simulatedSignature = crypto
        .createHash('sha256')
        .update(hash + publicKey)
        .digest('hex');
      
      return simulatedSignature === signature;
    }
  }
}

export default ZkVerifier;/**
 * Zero-Knowledge-Proof-Verifizierer für wissenschaftliche Publikationen
 * Ermöglicht die Verifizierung von Autorschaft und Datenintegrität ohne sensible Daten preiszugeben
 */

import crypto from 'crypto';

class ZkVerifier {
  constructor(options = {}) {
    this.options = {
      hashAlgorithm: 'sha256',
      ...options
    };
  }
  
  /**
   * Generiert einen Beweis für die Autorschaft einer Publikation
   * @param {Object} publication - Publikationsdaten
   * @param {Object} author - Autorschaftsdaten
   * @param {string} privateKey - Privater Schlüssel des Autors
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateAuthorshipProof(publication, author, privateKey) {
    try {
      // 1. Erstelle einen Hash der Publikation
      const publicationHash = this.hashPublication(publication);
      
      // 2. Erstelle einen Hash der Autorschaftsdaten
      const authorshipHash = this.hashAuthorship(publication.doi, author);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(publicationHash, authorshipHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        publicationHash,
        authorshipClaim: {
          authorId: author.id,
          authorName: author.name,
          authorRole: author.role || 'author',
          authorshipHash
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Autorschaftsbeweises:', error);
      throw new Error(`Autorschaftsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Autorschaftsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} publication - Publikationsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel des Autors
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyAuthorshipProof(proof, publication, publicKey) {
    try {
      // 1. Berechne den Publikationshash
      const publicationHash = this.hashPublication(publication);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (publicationHash !== proof.publicationHash) {
        console.warn('Publikationshash stimmt nicht überein');
        return false;
      }
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(
        proof.publicationHash,
        proof.authorshipClaim.authorshipHash
      );
      
      // 4. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Autorschaftsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Integrität von Forschungsdaten
   * @param {Object} data - Forschungsdaten
   * @param {string} privateKey - Privater Schlüssel
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateDataIntegrityProof(data, privateKey) {
    try {
      // 1. Erstelle einen Hash der Daten
      const dataHash = this.hashData(data);
      
      // 2. Erstelle Metadaten über die Daten (ohne die eigentlichen Daten)
      const metadata = this.extractDataMetadata(data);
      
      // 3. Signiere den Hash mit dem privaten Schlüssel
      const signature = this.sign(dataHash, privateKey);
      
      // 4. Erstelle den Beweis
      return {
        dataHash,
        metadata,
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Datenintegritätsbeweises:', error);
      throw new Error(`Datenintegritätsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Datenintegritätsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} data - Forschungsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyDataIntegrityProof(proof, data, publicKey) {
    try {
      // 1. Berechne den Datenhash
      const dataHash = this.hashData(data);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (dataHash !== proof.dataHash) {
        console.warn('Datenhash stimmt nicht überein');
        return false;
      }
      
      // 3. Verifiziere die Signatur
      const isSignatureValid = this.verify(dataHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Datenintegritätsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Peer-Review einer Publikation
   * @param {Object} review - Review-Daten
   * @param {string} publicationDoi - DOI der Publikation
   * @param {string} privateKey - Privater Schlüssel des Reviewers
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generatePeerReviewProof(review, publicationDoi, privateKey) {
    try {
      // 1. Erstelle einen Hash des Reviews
      const reviewHash = this.hashReview(review);
      
      // 2. Erstelle einen Hash der Publikations-DOI
      const publicationHash = this.hashString(publicationDoi);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        reviewHash,
        publicationDoi,
        reviewerInfo: {
          id: review.reviewerId,
          name: review.reviewerName,
          institution: review.reviewerInstitution
        },
        reviewMetadata: {
          date: review.date,
          recommendation: review.recommendation,
          confidence: review.confidence
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Peer-Review-Beweises:', error);
      throw new Error(`Peer-Review-Beweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Peer-Review-Beweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} review - Review-Daten
   * @param {string} publicKey - Öffentlicher Schlüssel des Reviewers
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyPeerReviewProof(proof, review, publicKey) {
    try {
      // 1. Berechne den Review-Hash
      const reviewHash = this.hashReview(review);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (reviewHash !== proof.reviewHash) {
        console.warn('Review-Hash stimmt nicht überein');
        return false;
      }
      
      // 3. Berechne den Publikations-Hash
      const publicationHash = this.hashString(proof.publicationDoi);
      
      // 4. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 5. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Peer-Review-Beweises:', error);
      return false;
    }
  }
  
  /**
   * Hasht eine Publikation
   * @private
   */
  hashPublication(publication) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      doi: publication.doi,
      title: publication.title,
      authors: publication.authors.map(author => ({
        name: author.name,
        orcid: author.orcid
      })),
      abstract: publication.abstract,
      publicationYear: publication.publicationYear
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht Autorschaftsdaten
   * @private
   */
  hashAuthorship(doi, author) {
    const authorshipData = {
      doi,
      authorId: author.id,
      authorName: author.name,
      authorRole: author.role || 'author'
    };
    
    return this.hashObject(authorshipData);
  }
  
  /**
   * Hasht Forschungsdaten
   * @private
   */
  hashData(data) {
    return this.hashObject(data);
  }
  
  /**
   * Extrahiert Metadaten aus Forschungsdaten
   * @private
   */
  extractDataMetadata(data) {
    // Extrahiere Metadaten ohne die eigentlichen Daten
    const metadata = {
      dataType: typeof data,
      isArray: Array.isArray(data),
      length: Array.isArray(data) ? data.length : undefined,
      keys: typeof data === 'object' ? Object.keys(data) : undefined,
      timestamp: Date.now()
    };
    
    return metadata;
  }
  
  /**
   * Hasht Review-Daten
   * @private
   */
  hashReview(review) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      reviewerId: review.reviewerId,
      publicationDoi: review.publicationDoi,
      date: review.date,
      recommendation: review.recommendation,
      confidence: review.confidence,
      comments: review.comments
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht ein Objekt
   * @private
   */
  hashObject(obj) {
    const jsonString = JSON.stringify(obj);
    return this.hashString(jsonString);
  }
  
  /**
   * Hasht einen String
   * @private
   */
  hashString(str) {
    return crypto
      .createHash(this.options.hashAlgorithm)
      .update(str)
      .digest('hex');
  }
  
  /**
   * Kombiniert zwei Hashes
   * @private
   */
  combineHashes(hash1, hash2) {
    return this.hashString(hash1 + hash2);
  }
  
  /**
   * Signiert einen Hash mit einem privaten Schlüssel
   * @private
   */
  sign(hash, privateKey) {
    // In einer realen Implementierung würde hier eine echte Signatur erstellt
    // Für diese Beispielimplementierung simulieren wir die Signatur
    const signer = crypto.createSign('RSA-SHA256');
    signer.update(hash);
    
    try {
      // Versuche, eine echte Signatur zu erstellen, falls ein gültiger Schlüssel vorliegt
      return signer.sign(privateKey, 'hex');
    } catch (error) {
      console.warn('Simuliere Signatur, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Signatur
      return crypto
        .createHash('sha256')
        .update(hash + privateKey)
        .digest('hex');
    }
  }
  
  /**
   * Verifiziert eine Signatur mit einem öffentlichen Schlüssel
   * @private
   */
  verify(hash, signature, publicKey) {
    // In einer realen Implementierung würde hier eine echte Signaturverifizierung durchgeführt
    // Für diese Beispielimplementierung simulieren wir die Verifizierung
    try {
      // Versuche, eine echte Verifizierung durchzuführen, falls ein gültiger Schlüssel vorliegt
      const verifier = crypto.createVerify('RSA-SHA256');
      verifier.update(hash);
      return verifier.verify(publicKey, signature, 'hex');
    } catch (error) {
      console.warn('Simuliere Verifizierung, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Verifizierung
      const simulatedSignature = crypto
        .createHash('sha256')
        .update(hash + publicKey)
        .digest('hex');
      
      return simulatedSignature === signature;
    }
  }
}

export default ZkVerifier;/**
 * Zero-Knowledge-Proof-Verifizierer für wissenschaftliche Publikationen
 * Ermöglicht die Verifizierung von Autorschaft und Datenintegrität ohne sensible Daten preiszugeben
 */

import crypto from 'crypto';

class ZkVerifier {
  constructor(options = {}) {
    this.options = {
      hashAlgorithm: 'sha256',
      ...options
    };
  }
  
  /**
   * Generiert einen Beweis für die Autorschaft einer Publikation
   * @param {Object} publication - Publikationsdaten
   * @param {Object} author - Autorschaftsdaten
   * @param {string} privateKey - Privater Schlüssel des Autors
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateAuthorshipProof(publication, author, privateKey) {
    try {
      // 1. Erstelle einen Hash der Publikation
      const publicationHash = this.hashPublication(publication);
      
      // 2. Erstelle einen Hash der Autorschaftsdaten
      const authorshipHash = this.hashAuthorship(publication.doi, author);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(publicationHash, authorshipHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        publicationHash,
        authorshipClaim: {
          authorId: author.id,
          authorName: author.name,
          authorRole: author.role || 'author',
          authorshipHash
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Autorschaftsbeweises:', error);
      throw new Error(`Autorschaftsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Autorschaftsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} publication - Publikationsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel des Autors
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyAuthorshipProof(proof, publication, publicKey) {
    try {
      // 1. Berechne den Publikationshash
      const publicationHash = this.hashPublication(publication);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (publicationHash !== proof.publicationHash) {
        console.warn('Publikationshash stimmt nicht überein');
        return false;
      }
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(
        proof.publicationHash,
        proof.authorshipClaim.authorshipHash
      );
      
      // 4. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Autorschaftsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Integrität von Forschungsdaten
   * @param {Object} data - Forschungsdaten
   * @param {string} privateKey - Privater Schlüssel
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateDataIntegrityProof(data, privateKey) {
    try {
      // 1. Erstelle einen Hash der Daten
      const dataHash = this.hashData(data);
      
      // 2. Erstelle Metadaten über die Daten (ohne die eigentlichen Daten)
      const metadata = this.extractDataMetadata(data);
      
      // 3. Signiere den Hash mit dem privaten Schlüssel
      const signature = this.sign(dataHash, privateKey);
      
      // 4. Erstelle den Beweis
      return {
        dataHash,
        metadata,
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Datenintegritätsbeweises:', error);
      throw new Error(`Datenintegritätsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Datenintegritätsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} data - Forschungsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyDataIntegrityProof(proof, data, publicKey) {
    try {
      // 1. Berechne den Datenhash
      const dataHash = this.hashData(data);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (dataHash !== proof.dataHash) {
        console.warn('Datenhash stimmt nicht überein');
        return false;
      }
      
      // 3. Verifiziere die Signatur
      const isSignatureValid = this.verify(dataHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Datenintegritätsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Peer-Review einer Publikation
   * @param {Object} review - Review-Daten
   * @param {string} publicationDoi - DOI der Publikation
   * @param {string} privateKey - Privater Schlüssel des Reviewers
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generatePeerReviewProof(review, publicationDoi, privateKey) {
    try {
      // 1. Erstelle einen Hash des Reviews
      const reviewHash = this.hashReview(review);
      
      // 2. Erstelle einen Hash der Publikations-DOI
      const publicationHash = this.hashString(publicationDoi);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        reviewHash,
        publicationDoi,
        reviewerInfo: {
          id: review.reviewerId,
          name: review.reviewerName,
          institution: review.reviewerInstitution
        },
        reviewMetadata: {
          date: review.date,
          recommendation: review.recommendation,
          confidence: review.confidence
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Peer-Review-Beweises:', error);
      throw new Error(`Peer-Review-Beweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Peer-Review-Beweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} review - Review-Daten
   * @param {string} publicKey - Öffentlicher Schlüssel des Reviewers
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyPeerReviewProof(proof, review, publicKey) {
    try {
      // 1. Berechne den Review-Hash
      const reviewHash = this.hashReview(review);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (reviewHash !== proof.reviewHash) {
        console.warn('Review-Hash stimmt nicht überein');
        return false;
      }
      
      // 3. Berechne den Publikations-Hash
      const publicationHash = this.hashString(proof.publicationDoi);
      
      // 4. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 5. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Peer-Review-Beweises:', error);
      return false;
    }
  }
  
  /**
   * Hasht eine Publikation
   * @private
   */
  hashPublication(publication) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      doi: publication.doi,
      title: publication.title,
      authors: publication.authors.map(author => ({
        name: author.name,
        orcid: author.orcid
      })),
      abstract: publication.abstract,
      publicationYear: publication.publicationYear
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht Autorschaftsdaten
   * @private
   */
  hashAuthorship(doi, author) {
    const authorshipData = {
      doi,
      authorId: author.id,
      authorName: author.name,
      authorRole: author.role || 'author'
    };
    
    return this.hashObject(authorshipData);
  }
  
  /**
   * Hasht Forschungsdaten
   * @private
   */
  hashData(data) {
    return this.hashObject(data);
  }
  
  /**
   * Extrahiert Metadaten aus Forschungsdaten
   * @private
   */
  extractDataMetadata(data) {
    // Extrahiere Metadaten ohne die eigentlichen Daten
    const metadata = {
      dataType: typeof data,
      isArray: Array.isArray(data),
      length: Array.isArray(data) ? data.length : undefined,
      keys: typeof data === 'object' ? Object.keys(data) : undefined,
      timestamp: Date.now()
    };
    
    return metadata;
  }
  
  /**
   * Hasht Review-Daten
   * @private
   */
  hashReview(review) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      reviewerId: review.reviewerId,
      publicationDoi: review.publicationDoi,
      date: review.date,
      recommendation: review.recommendation,
      confidence: review.confidence,
      comments: review.comments
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht ein Objekt
   * @private
   */
  hashObject(obj) {
    const jsonString = JSON.stringify(obj);
    return this.hashString(jsonString);
  }
  
  /**
   * Hasht einen String
   * @private
   */
  hashString(str) {
    return crypto
      .createHash(this.options.hashAlgorithm)
      .update(str)
      .digest('hex');
  }
  
  /**
   * Kombiniert zwei Hashes
   * @private
   */
  combineHashes(hash1, hash2) {
    return this.hashString(hash1 + hash2);
  }
  
  /**
   * Signiert einen Hash mit einem privaten Schlüssel
   * @private
   */
  sign(hash, privateKey) {
    // In einer realen Implementierung würde hier eine echte Signatur erstellt
    // Für diese Beispielimplementierung simulieren wir die Signatur
    const signer = crypto.createSign('RSA-SHA256');
    signer.update(hash);
    
    try {
      // Versuche, eine echte Signatur zu erstellen, falls ein gültiger Schlüssel vorliegt
      return signer.sign(privateKey, 'hex');
    } catch (error) {
      console.warn('Simuliere Signatur, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Signatur
      return crypto
        .createHash('sha256')
        .update(hash + privateKey)
        .digest('hex');
    }
  }
  
  /**
   * Verifiziert eine Signatur mit einem öffentlichen Schlüssel
   * @private
   */
  verify(hash, signature, publicKey) {
    // In einer realen Implementierung würde hier eine echte Signaturverifizierung durchgeführt
    // Für diese Beispielimplementierung simulieren wir die Verifizierung
    try {
      // Versuche, eine echte Verifizierung durchzuführen, falls ein gültiger Schlüssel vorliegt
      const verifier = crypto.createVerify('RSA-SHA256');
      verifier.update(hash);
      return verifier.verify(publicKey, signature, 'hex');
    } catch (error) {
      console.warn('Simuliere Verifizierung, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Verifizierung
      const simulatedSignature = crypto
        .createHash('sha256')
        .update(hash + publicKey)
        .digest('hex');
      
      return simulatedSignature === signature;
    }
  }
}

export default ZkVerifier;/**
 * Zero-Knowledge-Proof-Verifizierer für wissenschaftliche Publikationen
 * Ermöglicht die Verifizierung von Autorschaft und Datenintegrität ohne sensible Daten preiszugeben
 */

import crypto from 'crypto';

class ZkVerifier {
  constructor(options = {}) {
    this.options = {
      hashAlgorithm: 'sha256',
      ...options
    };
  }
  
  /**
   * Generiert einen Beweis für die Autorschaft einer Publikation
   * @param {Object} publication - Publikationsdaten
   * @param {Object} author - Autorschaftsdaten
   * @param {string} privateKey - Privater Schlüssel des Autors
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateAuthorshipProof(publication, author, privateKey) {
    try {
      // 1. Erstelle einen Hash der Publikation
      const publicationHash = this.hashPublication(publication);
      
      // 2. Erstelle einen Hash der Autorschaftsdaten
      const authorshipHash = this.hashAuthorship(publication.doi, author);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(publicationHash, authorshipHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        publicationHash,
        authorshipClaim: {
          authorId: author.id,
          authorName: author.name,
          authorRole: author.role || 'author',
          authorshipHash
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Autorschaftsbeweises:', error);
      throw new Error(`Autorschaftsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Autorschaftsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} publication - Publikationsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel des Autors
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyAuthorshipProof(proof, publication, publicKey) {
    try {
      // 1. Berechne den Publikationshash
      const publicationHash = this.hashPublication(publication);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (publicationHash !== proof.publicationHash) {
        console.warn('Publikationshash stimmt nicht überein');
        return false;
      }
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(
        proof.publicationHash,
        proof.authorshipClaim.authorshipHash
      );
      
      // 4. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Autorschaftsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Integrität von Forschungsdaten
   * @param {Object} data - Forschungsdaten
   * @param {string} privateKey - Privater Schlüssel
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateDataIntegrityProof(data, privateKey) {
    try {
      // 1. Erstelle einen Hash der Daten
      const dataHash = this.hashData(data);
      
      // 2. Erstelle Metadaten über die Daten (ohne die eigentlichen Daten)
      const metadata = this.extractDataMetadata(data);
      
      // 3. Signiere den Hash mit dem privaten Schlüssel
      const signature = this.sign(dataHash, privateKey);
      
      // 4. Erstelle den Beweis
      return {
        dataHash,
        metadata,
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Datenintegritätsbeweises:', error);
      throw new Error(`Datenintegritätsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Datenintegritätsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} data - Forschungsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyDataIntegrityProof(proof, data, publicKey) {
    try {
      // 1. Berechne den Datenhash
      const dataHash = this.hashData(data);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (dataHash !== proof.dataHash) {
        console.warn('Datenhash stimmt nicht überein');
        return false;
      }
      
      // 3. Verifiziere die Signatur
      const isSignatureValid = this.verify(dataHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Datenintegritätsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Peer-Review einer Publikation
   * @param {Object} review - Review-Daten
   * @param {string} publicationDoi - DOI der Publikation
   * @param {string} privateKey - Privater Schlüssel des Reviewers
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generatePeerReviewProof(review, publicationDoi, privateKey) {
    try {
      // 1. Erstelle einen Hash des Reviews
      const reviewHash = this.hashReview(review);
      
      // 2. Erstelle einen Hash der Publikations-DOI
      const publicationHash = this.hashString(publicationDoi);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        reviewHash,
        publicationDoi,
        reviewerInfo: {
          id: review.reviewerId,
          name: review.reviewerName,
          institution: review.reviewerInstitution
        },
        reviewMetadata: {
          date: review.date,
          recommendation: review.recommendation,
          confidence: review.confidence
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Peer-Review-Beweises:', error);
      throw new Error(`Peer-Review-Beweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Peer-Review-Beweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} review - Review-Daten
   * @param {string} publicKey - Öffentlicher Schlüssel des Reviewers
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyPeerReviewProof(proof, review, publicKey) {
    try {
      // 1. Berechne den Review-Hash
      const reviewHash = this.hashReview(review);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (reviewHash !== proof.reviewHash) {
        console.warn('Review-Hash stimmt nicht überein');
        return false;
      }
      
      // 3. Berechne den Publikations-Hash
      const publicationHash = this.hashString(proof.publicationDoi);
      
      // 4. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 5. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Peer-Review-Beweises:', error);
      return false;
    }
  }
  
  /**
   * Hasht eine Publikation
   * @private
   */
  hashPublication(publication) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      doi: publication.doi,
      title: publication.title,
      authors: publication.authors.map(author => ({
        name: author.name,
        orcid: author.orcid
      })),
      abstract: publication.abstract,
      publicationYear: publication.publicationYear
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht Autorschaftsdaten
   * @private
   */
  hashAuthorship(doi, author) {
    const authorshipData = {
      doi,
      authorId: author.id,
      authorName: author.name,
      authorRole: author.role || 'author'
    };
    
    return this.hashObject(authorshipData);
  }
  
  /**
   * Hasht Forschungsdaten
   * @private
   */
  hashData(data) {
    return this.hashObject(data);
  }
  
  /**
   * Extrahiert Metadaten aus Forschungsdaten
   * @private
   */
  extractDataMetadata(data) {
    // Extrahiere Metadaten ohne die eigentlichen Daten
    const metadata = {
      dataType: typeof data,
      isArray: Array.isArray(data),
      length: Array.isArray(data) ? data.length : undefined,
      keys: typeof data === 'object' ? Object.keys(data) : undefined,
      timestamp: Date.now()
    };
    
    return metadata;
  }
  
  /**
   * Hasht Review-Daten
   * @private
   */
  hashReview(review) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      reviewerId: review.reviewerId,
      publicationDoi: review.publicationDoi,
      date: review.date,
      recommendation: review.recommendation,
      confidence: review.confidence,
      comments: review.comments
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht ein Objekt
   * @private
   */
  hashObject(obj) {
    const jsonString = JSON.stringify(obj);
    return this.hashString(jsonString);
  }
  
  /**
   * Hasht einen String
   * @private
   */
  hashString(str) {
    return crypto
      .createHash(this.options.hashAlgorithm)
      .update(str)
      .digest('hex');
  }
  
  /**
   * Kombiniert zwei Hashes
   * @private
   */
  combineHashes(hash1, hash2) {
    return this.hashString(hash1 + hash2);
  }
  
  /**
   * Signiert einen Hash mit einem privaten Schlüssel
   * @private
   */
  sign(hash, privateKey) {
    // In einer realen Implementierung würde hier eine echte Signatur erstellt
    // Für diese Beispielimplementierung simulieren wir die Signatur
    const signer = crypto.createSign('RSA-SHA256');
    signer.update(hash);
    
    try {
      // Versuche, eine echte Signatur zu erstellen, falls ein gültiger Schlüssel vorliegt
      return signer.sign(privateKey, 'hex');
    } catch (error) {
      console.warn('Simuliere Signatur, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Signatur
      return crypto
        .createHash('sha256')
        .update(hash + privateKey)
        .digest('hex');
    }
  }
  
  /**
   * Verifiziert eine Signatur mit einem öffentlichen Schlüssel
   * @private
   */
  verify(hash, signature, publicKey) {
    // In einer realen Implementierung würde hier eine echte Signaturverifizierung durchgeführt
    // Für diese Beispielimplementierung simulieren wir die Verifizierung
    try {
      // Versuche, eine echte Verifizierung durchzuführen, falls ein gültiger Schlüssel vorliegt
      const verifier = crypto.createVerify('RSA-SHA256');
      verifier.update(hash);
      return verifier.verify(publicKey, signature, 'hex');
    } catch (error) {
      console.warn('Simuliere Verifizierung, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Verifizierung
      const simulatedSignature = crypto
        .createHash('sha256')
        .update(hash + publicKey)
        .digest('hex');
      
      return simulatedSignature === signature;
    }
  }
}

export default ZkVerifier;/**
 * Zero-Knowledge-Proof-Verifizierer für wissenschaftliche Publikationen
 * Ermöglicht die Verifizierung von Autorschaft und Datenintegrität ohne sensible Daten preiszugeben
 */

import crypto from 'crypto';

class ZkVerifier {
  constructor(options = {}) {
    this.options = {
      hashAlgorithm: 'sha256',
      ...options
    };
  }
  
  /**
   * Generiert einen Beweis für die Autorschaft einer Publikation
   * @param {Object} publication - Publikationsdaten
   * @param {Object} author - Autorschaftsdaten
   * @param {string} privateKey - Privater Schlüssel des Autors
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateAuthorshipProof(publication, author, privateKey) {
    try {
      // 1. Erstelle einen Hash der Publikation
      const publicationHash = this.hashPublication(publication);
      
      // 2. Erstelle einen Hash der Autorschaftsdaten
      const authorshipHash = this.hashAuthorship(publication.doi, author);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(publicationHash, authorshipHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        publicationHash,
        authorshipClaim: {
          authorId: author.id,
          authorName: author.name,
          authorRole: author.role || 'author',
          authorshipHash
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Autorschaftsbeweises:', error);
      throw new Error(`Autorschaftsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Autorschaftsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} publication - Publikationsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel des Autors
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyAuthorshipProof(proof, publication, publicKey) {
    try {
      // 1. Berechne den Publikationshash
      const publicationHash = this.hashPublication(publication);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (publicationHash !== proof.publicationHash) {
        console.warn('Publikationshash stimmt nicht überein');
        return false;
      }
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(
        proof.publicationHash,
        proof.authorshipClaim.authorshipHash
      );
      
      // 4. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Autorschaftsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Integrität von Forschungsdaten
   * @param {Object} data - Forschungsdaten
   * @param {string} privateKey - Privater Schlüssel
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateDataIntegrityProof(data, privateKey) {
    try {
      // 1. Erstelle einen Hash der Daten
      const dataHash = this.hashData(data);
      
      // 2. Erstelle Metadaten über die Daten (ohne die eigentlichen Daten)
      const metadata = this.extractDataMetadata(data);
      
      // 3. Signiere den Hash mit dem privaten Schlüssel
      const signature = this.sign(dataHash, privateKey);
      
      // 4. Erstelle den Beweis
      return {
        dataHash,
        metadata,
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Datenintegritätsbeweises:', error);
      throw new Error(`Datenintegritätsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Datenintegritätsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} data - Forschungsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyDataIntegrityProof(proof, data, publicKey) {
    try {
      // 1. Berechne den Datenhash
      const dataHash = this.hashData(data);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (dataHash !== proof.dataHash) {
        console.warn('Datenhash stimmt nicht überein');
        return false;
      }
      
      // 3. Verifiziere die Signatur
      const isSignatureValid = this.verify(dataHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Datenintegritätsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Peer-Review einer Publikation
   * @param {Object} review - Review-Daten
   * @param {string} publicationDoi - DOI der Publikation
   * @param {string} privateKey - Privater Schlüssel des Reviewers
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generatePeerReviewProof(review, publicationDoi, privateKey) {
    try {
      // 1. Erstelle einen Hash des Reviews
      const reviewHash = this.hashReview(review);
      
      // 2. Erstelle einen Hash der Publikations-DOI
      const publicationHash = this.hashString(publicationDoi);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        reviewHash,
        publicationDoi,
        reviewerInfo: {
          id: review.reviewerId,
          name: review.reviewerName,
          institution: review.reviewerInstitution
        },
        reviewMetadata: {
          date: review.date,
          recommendation: review.recommendation,
          confidence: review.confidence
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Peer-Review-Beweises:', error);
      throw new Error(`Peer-Review-Beweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Peer-Review-Beweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} review - Review-Daten
   * @param {string} publicKey - Öffentlicher Schlüssel des Reviewers
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyPeerReviewProof(proof, review, publicKey) {
    try {
      // 1. Berechne den Review-Hash
      const reviewHash = this.hashReview(review);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (reviewHash !== proof.reviewHash) {
        console.warn('Review-Hash stimmt nicht überein');
        return false;
      }
      
      // 3. Berechne den Publikations-Hash
      const publicationHash = this.hashString(proof.publicationDoi);
      
      // 4. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 5. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Peer-Review-Beweises:', error);
      return false;
    }
  }
  
  /**
   * Hasht eine Publikation
   * @private
   */
  hashPublication(publication) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      doi: publication.doi,
      title: publication.title,
      authors: publication.authors.map(author => ({
        name: author.name,
        orcid: author.orcid
      })),
      abstract: publication.abstract,
      publicationYear: publication.publicationYear
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht Autorschaftsdaten
   * @private
   */
  hashAuthorship(doi, author) {
    const authorshipData = {
      doi,
      authorId: author.id,
      authorName: author.name,
      authorRole: author.role || 'author'
    };
    
    return this.hashObject(authorshipData);
  }
  
  /**
   * Hasht Forschungsdaten
   * @private
   */
  hashData(data) {
    return this.hashObject(data);
  }
  
  /**
   * Extrahiert Metadaten aus Forschungsdaten
   * @private
   */
  extractDataMetadata(data) {
    // Extrahiere Metadaten ohne die eigentlichen Daten
    const metadata = {
      dataType: typeof data,
      isArray: Array.isArray(data),
      length: Array.isArray(data) ? data.length : undefined,
      keys: typeof data === 'object' ? Object.keys(data) : undefined,
      timestamp: Date.now()
    };
    
    return metadata;
  }
  
  /**
   * Hasht Review-Daten
   * @private
   */
  hashReview(review) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      reviewerId: review.reviewerId,
      publicationDoi: review.publicationDoi,
      date: review.date,
      recommendation: review.recommendation,
      confidence: review.confidence,
      comments: review.comments
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht ein Objekt
   * @private
   */
  hashObject(obj) {
    const jsonString = JSON.stringify(obj);
    return this.hashString(jsonString);
  }
  
  /**
   * Hasht einen String
   * @private
   */
  hashString(str) {
    return crypto
      .createHash(this.options.hashAlgorithm)
      .update(str)
      .digest('hex');
  }
  
  /**
   * Kombiniert zwei Hashes
   * @private
   */
  combineHashes(hash1, hash2) {
    return this.hashString(hash1 + hash2);
  }
  
  /**
   * Signiert einen Hash mit einem privaten Schlüssel
   * @private
   */
  sign(hash, privateKey) {
    // In einer realen Implementierung würde hier eine echte Signatur erstellt
    // Für diese Beispielimplementierung simulieren wir die Signatur
    const signer = crypto.createSign('RSA-SHA256');
    signer.update(hash);
    
    try {
      // Versuche, eine echte Signatur zu erstellen, falls ein gültiger Schlüssel vorliegt
      return signer.sign(privateKey, 'hex');
    } catch (error) {
      console.warn('Simuliere Signatur, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Signatur
      return crypto
        .createHash('sha256')
        .update(hash + privateKey)
        .digest('hex');
    }
  }
  
  /**
   * Verifiziert eine Signatur mit einem öffentlichen Schlüssel
   * @private
   */
  verify(hash, signature, publicKey) {
    // In einer realen Implementierung würde hier eine echte Signaturverifizierung durchgeführt
    // Für diese Beispielimplementierung simulieren wir die Verifizierung
    try {
      // Versuche, eine echte Verifizierung durchzuführen, falls ein gültiger Schlüssel vorliegt
      const verifier = crypto.createVerify('RSA-SHA256');
      verifier.update(hash);
      return verifier.verify(publicKey, signature, 'hex');
    } catch (error) {
      console.warn('Simuliere Verifizierung, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Verifizierung
      const simulatedSignature = crypto
        .createHash('sha256')
        .update(hash + publicKey)
        .digest('hex');
      
      return simulatedSignature === signature;
    }
  }
}

export default ZkVerifier;/**
 * Zero-Knowledge-Proof-Verifizierer für wissenschaftliche Publikationen
 * Ermöglicht die Verifizierung von Autorschaft und Datenintegrität ohne sensible Daten preiszugeben
 */

import crypto from 'crypto';

class ZkVerifier {
  constructor(options = {}) {
    this.options = {
      hashAlgorithm: 'sha256',
      ...options
    };
  }
  
  /**
   * Generiert einen Beweis für die Autorschaft einer Publikation
   * @param {Object} publication - Publikationsdaten
   * @param {Object} author - Autorschaftsdaten
   * @param {string} privateKey - Privater Schlüssel des Autors
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateAuthorshipProof(publication, author, privateKey) {
    try {
      // 1. Erstelle einen Hash der Publikation
      const publicationHash = this.hashPublication(publication);
      
      // 2. Erstelle einen Hash der Autorschaftsdaten
      const authorshipHash = this.hashAuthorship(publication.doi, author);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(publicationHash, authorshipHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        publicationHash,
        authorshipClaim: {
          authorId: author.id,
          authorName: author.name,
          authorRole: author.role || 'author',
          authorshipHash
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Autorschaftsbeweises:', error);
      throw new Error(`Autorschaftsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Autorschaftsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} publication - Publikationsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel des Autors
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyAuthorshipProof(proof, publication, publicKey) {
    try {
      // 1. Berechne den Publikationshash
      const publicationHash = this.hashPublication(publication);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (publicationHash !== proof.publicationHash) {
        console.warn('Publikationshash stimmt nicht überein');
        return false;
      }
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(
        proof.publicationHash,
        proof.authorshipClaim.authorshipHash
      );
      
      // 4. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Autorschaftsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Integrität von Forschungsdaten
   * @param {Object} data - Forschungsdaten
   * @param {string} privateKey - Privater Schlüssel
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateDataIntegrityProof(data, privateKey) {
    try {
      // 1. Erstelle einen Hash der Daten
      const dataHash = this.hashData(data);
      
      // 2. Erstelle Metadaten über die Daten (ohne die eigentlichen Daten)
      const metadata = this.extractDataMetadata(data);
      
      // 3. Signiere den Hash mit dem privaten Schlüssel
      const signature = this.sign(dataHash, privateKey);
      
      // 4. Erstelle den Beweis
      return {
        dataHash,
        metadata,
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Datenintegritätsbeweises:', error);
      throw new Error(`Datenintegritätsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Datenintegritätsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} data - Forschungsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyDataIntegrityProof(proof, data, publicKey) {
    try {
      // 1. Berechne den Datenhash
      const dataHash = this.hashData(data);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (dataHash !== proof.dataHash) {
        console.warn('Datenhash stimmt nicht überein');
        return false;
      }
      
      // 3. Verifiziere die Signatur
      const isSignatureValid = this.verify(dataHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Datenintegritätsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Peer-Review einer Publikation
   * @param {Object} review - Review-Daten
   * @param {string} publicationDoi - DOI der Publikation
   * @param {string} privateKey - Privater Schlüssel des Reviewers
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generatePeerReviewProof(review, publicationDoi, privateKey) {
    try {
      // 1. Erstelle einen Hash des Reviews
      const reviewHash = this.hashReview(review);
      
      // 2. Erstelle einen Hash der Publikations-DOI
      const publicationHash = this.hashString(publicationDoi);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        reviewHash,
        publicationDoi,
        reviewerInfo: {
          id: review.reviewerId,
          name: review.reviewerName,
          institution: review.reviewerInstitution
        },
        reviewMetadata: {
          date: review.date,
          recommendation: review.recommendation,
          confidence: review.confidence
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Peer-Review-Beweises:', error);
      throw new Error(`Peer-Review-Beweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Peer-Review-Beweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} review - Review-Daten
   * @param {string} publicKey - Öffentlicher Schlüssel des Reviewers
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyPeerReviewProof(proof, review, publicKey) {
    try {
      // 1. Berechne den Review-Hash
      const reviewHash = this.hashReview(review);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (reviewHash !== proof.reviewHash) {
        console.warn('Review-Hash stimmt nicht überein');
        return false;
      }
      
      // 3. Berechne den Publikations-Hash
      const publicationHash = this.hashString(proof.publicationDoi);
      
      // 4. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 5. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Peer-Review-Beweises:', error);
      return false;
    }
  }
  
  /**
   * Hasht eine Publikation
   * @private
   */
  hashPublication(publication) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      doi: publication.doi,
      title: publication.title,
      authors: publication.authors.map(author => ({
        name: author.name,
        orcid: author.orcid
      })),
      abstract: publication.abstract,
      publicationYear: publication.publicationYear
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht Autorschaftsdaten
   * @private
   */
  hashAuthorship(doi, author) {
    const authorshipData = {
      doi,
      authorId: author.id,
      authorName: author.name,
      authorRole: author.role || 'author'
    };
    
    return this.hashObject(authorshipData);
  }
  
  /**
   * Hasht Forschungsdaten
   * @private
   */
  hashData(data) {
    return this.hashObject(data);
  }
  
  /**
   * Extrahiert Metadaten aus Forschungsdaten
   * @private
   */
  extractDataMetadata(data) {
    // Extrahiere Metadaten ohne die eigentlichen Daten
    const metadata = {
      dataType: typeof data,
      isArray: Array.isArray(data),
      length: Array.isArray(data) ? data.length : undefined,
      keys: typeof data === 'object' ? Object.keys(data) : undefined,
      timestamp: Date.now()
    };
    
    return metadata;
  }
  
  /**
   * Hasht Review-Daten
   * @private
   */
  hashReview(review) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      reviewerId: review.reviewerId,
      publicationDoi: review.publicationDoi,
      date: review.date,
      recommendation: review.recommendation,
      confidence: review.confidence,
      comments: review.comments
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht ein Objekt
   * @private
   */
  hashObject(obj) {
    const jsonString = JSON.stringify(obj);
    return this.hashString(jsonString);
  }
  
  /**
   * Hasht einen String
   * @private
   */
  hashString(str) {
    return crypto
      .createHash(this.options.hashAlgorithm)
      .update(str)
      .digest('hex');
  }
  
  /**
   * Kombiniert zwei Hashes
   * @private
   */
  combineHashes(hash1, hash2) {
    return this.hashString(hash1 + hash2);
  }
  
  /**
   * Signiert einen Hash mit einem privaten Schlüssel
   * @private
   */
  sign(hash, privateKey) {
    // In einer realen Implementierung würde hier eine echte Signatur erstellt
    // Für diese Beispielimplementierung simulieren wir die Signatur
    const signer = crypto.createSign('RSA-SHA256');
    signer.update(hash);
    
    try {
      // Versuche, eine echte Signatur zu erstellen, falls ein gültiger Schlüssel vorliegt
      return signer.sign(privateKey, 'hex');
    } catch (error) {
      console.warn('Simuliere Signatur, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Signatur
      return crypto
        .createHash('sha256')
        .update(hash + privateKey)
        .digest('hex');
    }
  }
  
  /**
   * Verifiziert eine Signatur mit einem öffentlichen Schlüssel
   * @private
   */
  verify(hash, signature, publicKey) {
    // In einer realen Implementierung würde hier eine echte Signaturverifizierung durchgeführt
    // Für diese Beispielimplementierung simulieren wir die Verifizierung
    try {
      // Versuche, eine echte Verifizierung durchzuführen, falls ein gültiger Schlüssel vorliegt
      const verifier = crypto.createVerify('RSA-SHA256');
      verifier.update(hash);
      return verifier.verify(publicKey, signature, 'hex');
    } catch (error) {
      console.warn('Simuliere Verifizierung, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Verifizierung
      const simulatedSignature = crypto
        .createHash('sha256')
        .update(hash + publicKey)
        .digest('hex');
      
      return simulatedSignature === signature;
    }
  }
}

export default ZkVerifier;/**
 * Zero-Knowledge-Proof-Verifizierer für wissenschaftliche Publikationen
 * Ermöglicht die Verifizierung von Autorschaft und Datenintegrität ohne sensible Daten preiszugeben
 */

import crypto from 'crypto';

class ZkVerifier {
  constructor(options = {}) {
    this.options = {
      hashAlgorithm: 'sha256',
      ...options
    };
  }
  
  /**
   * Generiert einen Beweis für die Autorschaft einer Publikation
   * @param {Object} publication - Publikationsdaten
   * @param {Object} author - Autorschaftsdaten
   * @param {string} privateKey - Privater Schlüssel des Autors
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateAuthorshipProof(publication, author, privateKey) {
    try {
      // 1. Erstelle einen Hash der Publikation
      const publicationHash = this.hashPublication(publication);
      
      // 2. Erstelle einen Hash der Autorschaftsdaten
      const authorshipHash = this.hashAuthorship(publication.doi, author);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(publicationHash, authorshipHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        publicationHash,
        authorshipClaim: {
          authorId: author.id,
          authorName: author.name,
          authorRole: author.role || 'author',
          authorshipHash
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Autorschaftsbeweises:', error);
      throw new Error(`Autorschaftsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Autorschaftsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} publication - Publikationsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel des Autors
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyAuthorshipProof(proof, publication, publicKey) {
    try {
      // 1. Berechne den Publikationshash
      const publicationHash = this.hashPublication(publication);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (publicationHash !== proof.publicationHash) {
        console.warn('Publikationshash stimmt nicht überein');
        return false;
      }
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(
        proof.publicationHash,
        proof.authorshipClaim.authorshipHash
      );
      
      // 4. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Autorschaftsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Integrität von Forschungsdaten
   * @param {Object} data - Forschungsdaten
   * @param {string} privateKey - Privater Schlüssel
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateDataIntegrityProof(data, privateKey) {
    try {
      // 1. Erstelle einen Hash der Daten
      const dataHash = this.hashData(data);
      
      // 2. Erstelle Metadaten über die Daten (ohne die eigentlichen Daten)
      const metadata = this.extractDataMetadata(data);
      
      // 3. Signiere den Hash mit dem privaten Schlüssel
      const signature = this.sign(dataHash, privateKey);
      
      // 4. Erstelle den Beweis
      return {
        dataHash,
        metadata,
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Datenintegritätsbeweises:', error);
      throw new Error(`Datenintegritätsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Datenintegritätsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} data - Forschungsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyDataIntegrityProof(proof, data, publicKey) {
    try {
      // 1. Berechne den Datenhash
      const dataHash = this.hashData(data);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (dataHash !== proof.dataHash) {
        console.warn('Datenhash stimmt nicht überein');
        return false;
      }
      
      // 3. Verifiziere die Signatur
      const isSignatureValid = this.verify(dataHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Datenintegritätsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Peer-Review einer Publikation
   * @param {Object} review - Review-Daten
   * @param {string} publicationDoi - DOI der Publikation
   * @param {string} privateKey - Privater Schlüssel des Reviewers
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generatePeerReviewProof(review, publicationDoi, privateKey) {
    try {
      // 1. Erstelle einen Hash des Reviews
      const reviewHash = this.hashReview(review);
      
      // 2. Erstelle einen Hash der Publikations-DOI
      const publicationHash = this.hashString(publicationDoi);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        reviewHash,
        publicationDoi,
        reviewerInfo: {
          id: review.reviewerId,
          name: review.reviewerName,
          institution: review.reviewerInstitution
        },
        reviewMetadata: {
          date: review.date,
          recommendation: review.recommendation,
          confidence: review.confidence
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Peer-Review-Beweises:', error);
      throw new Error(`Peer-Review-Beweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Peer-Review-Beweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} review - Review-Daten
   * @param {string} publicKey - Öffentlicher Schlüssel des Reviewers
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyPeerReviewProof(proof, review, publicKey) {
    try {
      // 1. Berechne den Review-Hash
      const reviewHash = this.hashReview(review);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (reviewHash !== proof.reviewHash) {
        console.warn('Review-Hash stimmt nicht überein');
        return false;
      }
      
      // 3. Berechne den Publikations-Hash
      const publicationHash = this.hashString(proof.publicationDoi);
      
      // 4. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 5. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Peer-Review-Beweises:', error);
      return false;
    }
  }
  
  /**
   * Hasht eine Publikation
   * @private
   */
  hashPublication(publication) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      doi: publication.doi,
      title: publication.title,
      authors: publication.authors.map(author => ({
        name: author.name,
        orcid: author.orcid
      })),
      abstract: publication.abstract,
      publicationYear: publication.publicationYear
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht Autorschaftsdaten
   * @private
   */
  hashAuthorship(doi, author) {
    const authorshipData = {
      doi,
      authorId: author.id,
      authorName: author.name,
      authorRole: author.role || 'author'
    };
    
    return this.hashObject(authorshipData);
  }
  
  /**
   * Hasht Forschungsdaten
   * @private
   */
  hashData(data) {
    return this.hashObject(data);
  }
  
  /**
   * Extrahiert Metadaten aus Forschungsdaten
   * @private
   */
  extractDataMetadata(data) {
    // Extrahiere Metadaten ohne die eigentlichen Daten
    const metadata = {
      dataType: typeof data,
      isArray: Array.isArray(data),
      length: Array.isArray(data) ? data.length : undefined,
      keys: typeof data === 'object' ? Object.keys(data) : undefined,
      timestamp: Date.now()
    };
    
    return metadata;
  }
  
  /**
   * Hasht Review-Daten
   * @private
   */
  hashReview(review) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      reviewerId: review.reviewerId,
      publicationDoi: review.publicationDoi,
      date: review.date,
      recommendation: review.recommendation,
      confidence: review.confidence,
      comments: review.comments
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht ein Objekt
   * @private
   */
  hashObject(obj) {
    const jsonString = JSON.stringify(obj);
    return this.hashString(jsonString);
  }
  
  /**
   * Hasht einen String
   * @private
   */
  hashString(str) {
    return crypto
      .createHash(this.options.hashAlgorithm)
      .update(str)
      .digest('hex');
  }
  
  /**
   * Kombiniert zwei Hashes
   * @private
   */
  combineHashes(hash1, hash2) {
    return this.hashString(hash1 + hash2);
  }
  
  /**
   * Signiert einen Hash mit einem privaten Schlüssel
   * @private
   */
  sign(hash, privateKey) {
    // In einer realen Implementierung würde hier eine echte Signatur erstellt
    // Für diese Beispielimplementierung simulieren wir die Signatur
    const signer = crypto.createSign('RSA-SHA256');
    signer.update(hash);
    
    try {
      // Versuche, eine echte Signatur zu erstellen, falls ein gültiger Schlüssel vorliegt
      return signer.sign(privateKey, 'hex');
    } catch (error) {
      console.warn('Simuliere Signatur, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Signatur
      return crypto
        .createHash('sha256')
        .update(hash + privateKey)
        .digest('hex');
    }
  }
  
  /**
   * Verifiziert eine Signatur mit einem öffentlichen Schlüssel
   * @private
   */
  verify(hash, signature, publicKey) {
    // In einer realen Implementierung würde hier eine echte Signaturverifizierung durchgeführt
    // Für diese Beispielimplementierung simulieren wir die Verifizierung
    try {
      // Versuche, eine echte Verifizierung durchzuführen, falls ein gültiger Schlüssel vorliegt
      const verifier = crypto.createVerify('RSA-SHA256');
      verifier.update(hash);
      return verifier.verify(publicKey, signature, 'hex');
    } catch (error) {
      console.warn('Simuliere Verifizierung, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Verifizierung
      const simulatedSignature = crypto
        .createHash('sha256')
        .update(hash + publicKey)
        .digest('hex');
      
      return simulatedSignature === signature;
    }
  }
}

export default ZkVerifier;/**
 * Zero-Knowledge-Proof-Verifizierer für wissenschaftliche Publikationen
 * Ermöglicht die Verifizierung von Autorschaft und Datenintegrität ohne sensible Daten preiszugeben
 */

import crypto from 'crypto';

class ZkVerifier {
  constructor(options = {}) {
    this.options = {
      hashAlgorithm: 'sha256',
      ...options
    };
  }
  
  /**
   * Generiert einen Beweis für die Autorschaft einer Publikation
   * @param {Object} publication - Publikationsdaten
   * @param {Object} author - Autorschaftsdaten
   * @param {string} privateKey - Privater Schlüssel des Autors
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateAuthorshipProof(publication, author, privateKey) {
    try {
      // 1. Erstelle einen Hash der Publikation
      const publicationHash = this.hashPublication(publication);
      
      // 2. Erstelle einen Hash der Autorschaftsdaten
      const authorshipHash = this.hashAuthorship(publication.doi, author);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(publicationHash, authorshipHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        publicationHash,
        authorshipClaim: {
          authorId: author.id,
          authorName: author.name,
          authorRole: author.role || 'author',
          authorshipHash
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Autorschaftsbeweises:', error);
      throw new Error(`Autorschaftsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Autorschaftsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} publication - Publikationsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel des Autors
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyAuthorshipProof(proof, publication, publicKey) {
    try {
      // 1. Berechne den Publikationshash
      const publicationHash = this.hashPublication(publication);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (publicationHash !== proof.publicationHash) {
        console.warn('Publikationshash stimmt nicht überein');
        return false;
      }
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(
        proof.publicationHash,
        proof.authorshipClaim.authorshipHash
      );
      
      // 4. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Autorschaftsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Integrität von Forschungsdaten
   * @param {Object} data - Forschungsdaten
   * @param {string} privateKey - Privater Schlüssel
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateDataIntegrityProof(data, privateKey) {
    try {
      // 1. Erstelle einen Hash der Daten
      const dataHash = this.hashData(data);
      
      // 2. Erstelle Metadaten über die Daten (ohne die eigentlichen Daten)
      const metadata = this.extractDataMetadata(data);
      
      // 3. Signiere den Hash mit dem privaten Schlüssel
      const signature = this.sign(dataHash, privateKey);
      
      // 4. Erstelle den Beweis
      return {
        dataHash,
        metadata,
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Datenintegritätsbeweises:', error);
      throw new Error(`Datenintegritätsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Datenintegritätsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} data - Forschungsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyDataIntegrityProof(proof, data, publicKey) {
    try {
      // 1. Berechne den Datenhash
      const dataHash = this.hashData(data);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (dataHash !== proof.dataHash) {
        console.warn('Datenhash stimmt nicht überein');
        return false;
      }
      
      // 3. Verifiziere die Signatur
      const isSignatureValid = this.verify(dataHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Datenintegritätsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Peer-Review einer Publikation
   * @param {Object} review - Review-Daten
   * @param {string} publicationDoi - DOI der Publikation
   * @param {string} privateKey - Privater Schlüssel des Reviewers
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generatePeerReviewProof(review, publicationDoi, privateKey) {
    try {
      // 1. Erstelle einen Hash des Reviews
      const reviewHash = this.hashReview(review);
      
      // 2. Erstelle einen Hash der Publikations-DOI
      const publicationHash = this.hashString(publicationDoi);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        reviewHash,
        publicationDoi,
        reviewerInfo: {
          id: review.reviewerId,
          name: review.reviewerName,
          institution: review.reviewerInstitution
        },
        reviewMetadata: {
          date: review.date,
          recommendation: review.recommendation,
          confidence: review.confidence
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Peer-Review-Beweises:', error);
      throw new Error(`Peer-Review-Beweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Peer-Review-Beweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} review - Review-Daten
   * @param {string} publicKey - Öffentlicher Schlüssel des Reviewers
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyPeerReviewProof(proof, review, publicKey) {
    try {
      // 1. Berechne den Review-Hash
      const reviewHash = this.hashReview(review);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (reviewHash !== proof.reviewHash) {
        console.warn('Review-Hash stimmt nicht überein');
        return false;
      }
      
      // 3. Berechne den Publikations-Hash
      const publicationHash = this.hashString(proof.publicationDoi);
      
      // 4. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 5. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Peer-Review-Beweises:', error);
      return false;
    }
  }
  
  /**
   * Hasht eine Publikation
   * @private
   */
  hashPublication(publication) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      doi: publication.doi,
      title: publication.title,
      authors: publication.authors.map(author => ({
        name: author.name,
        orcid: author.orcid
      })),
      abstract: publication.abstract,
      publicationYear: publication.publicationYear
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht Autorschaftsdaten
   * @private
   */
  hashAuthorship(doi, author) {
    const authorshipData = {
      doi,
      authorId: author.id,
      authorName: author.name,
      authorRole: author.role || 'author'
    };
    
    return this.hashObject(authorshipData);
  }
  
  /**
   * Hasht Forschungsdaten
   * @private
   */
  hashData(data) {
    return this.hashObject(data);
  }
  
  /**
   * Extrahiert Metadaten aus Forschungsdaten
   * @private
   */
  extractDataMetadata(data) {
    // Extrahiere Metadaten ohne die eigentlichen Daten
    const metadata = {
      dataType: typeof data,
      isArray: Array.isArray(data),
      length: Array.isArray(data) ? data.length : undefined,
      keys: typeof data === 'object' ? Object.keys(data) : undefined,
      timestamp: Date.now()
    };
    
    return metadata;
  }
  
  /**
   * Hasht Review-Daten
   * @private
   */
  hashReview(review) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      reviewerId: review.reviewerId,
      publicationDoi: review.publicationDoi,
      date: review.date,
      recommendation: review.recommendation,
      confidence: review.confidence,
      comments: review.comments
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht ein Objekt
   * @private
   */
  hashObject(obj) {
    const jsonString = JSON.stringify(obj);
    return this.hashString(jsonString);
  }
  
  /**
   * Hasht einen String
   * @private
   */
  hashString(str) {
    return crypto
      .createHash(this.options.hashAlgorithm)
      .update(str)
      .digest('hex');
  }
  
  /**
   * Kombiniert zwei Hashes
   * @private
   */
  combineHashes(hash1, hash2) {
    return this.hashString(hash1 + hash2);
  }
  
  /**
   * Signiert einen Hash mit einem privaten Schlüssel
   * @private
   */
  sign(hash, privateKey) {
    // In einer realen Implementierung würde hier eine echte Signatur erstellt
    // Für diese Beispielimplementierung simulieren wir die Signatur
    const signer = crypto.createSign('RSA-SHA256');
    signer.update(hash);
    
    try {
      // Versuche, eine echte Signatur zu erstellen, falls ein gültiger Schlüssel vorliegt
      return signer.sign(privateKey, 'hex');
    } catch (error) {
      console.warn('Simuliere Signatur, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Signatur
      return crypto
        .createHash('sha256')
        .update(hash + privateKey)
        .digest('hex');
    }
  }
  
  /**
   * Verifiziert eine Signatur mit einem öffentlichen Schlüssel
   * @private
   */
  verify(hash, signature, publicKey) {
    // In einer realen Implementierung würde hier eine echte Signaturverifizierung durchgeführt
    // Für diese Beispielimplementierung simulieren wir die Verifizierung
    try {
      // Versuche, eine echte Verifizierung durchzuführen, falls ein gültiger Schlüssel vorliegt
      const verifier = crypto.createVerify('RSA-SHA256');
      verifier.update(hash);
      return verifier.verify(publicKey, signature, 'hex');
    } catch (error) {
      console.warn('Simuliere Verifizierung, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Verifizierung
      const simulatedSignature = crypto
        .createHash('sha256')
        .update(hash + publicKey)
        .digest('hex');
      
      return simulatedSignature === signature;
    }
  }
}

export default ZkVerifier;/**
 * Zero-Knowledge-Proof-Verifizierer für wissenschaftliche Publikationen
 * Ermöglicht die Verifizierung von Autorschaft und Datenintegrität ohne sensible Daten preiszugeben
 */

import crypto from 'crypto';

class ZkVerifier {
  constructor(options = {}) {
    this.options = {
      hashAlgorithm: 'sha256',
      ...options
    };
  }
  
  /**
   * Generiert einen Beweis für die Autorschaft einer Publikation
   * @param {Object} publication - Publikationsdaten
   * @param {Object} author - Autorschaftsdaten
   * @param {string} privateKey - Privater Schlüssel des Autors
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateAuthorshipProof(publication, author, privateKey) {
    try {
      // 1. Erstelle einen Hash der Publikation
      const publicationHash = this.hashPublication(publication);
      
      // 2. Erstelle einen Hash der Autorschaftsdaten
      const authorshipHash = this.hashAuthorship(publication.doi, author);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(publicationHash, authorshipHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        publicationHash,
        authorshipClaim: {
          authorId: author.id,
          authorName: author.name,
          authorRole: author.role || 'author',
          authorshipHash
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Autorschaftsbeweises:', error);
      throw new Error(`Autorschaftsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Autorschaftsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} publication - Publikationsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel des Autors
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyAuthorshipProof(proof, publication, publicKey) {
    try {
      // 1. Berechne den Publikationshash
      const publicationHash = this.hashPublication(publication);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (publicationHash !== proof.publicationHash) {
        console.warn('Publikationshash stimmt nicht überein');
        return false;
      }
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(
        proof.publicationHash,
        proof.authorshipClaim.authorshipHash
      );
      
      // 4. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Autorschaftsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Integrität von Forschungsdaten
   * @param {Object} data - Forschungsdaten
   * @param {string} privateKey - Privater Schlüssel
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateDataIntegrityProof(data, privateKey) {
    try {
      // 1. Erstelle einen Hash der Daten
      const dataHash = this.hashData(data);
      
      // 2. Erstelle Metadaten über die Daten (ohne die eigentlichen Daten)
      const metadata = this.extractDataMetadata(data);
      
      // 3. Signiere den Hash mit dem privaten Schlüssel
      const signature = this.sign(dataHash, privateKey);
      
      // 4. Erstelle den Beweis
      return {
        dataHash,
        metadata,
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Datenintegritätsbeweises:', error);
      throw new Error(`Datenintegritätsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Datenintegritätsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} data - Forschungsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyDataIntegrityProof(proof, data, publicKey) {
    try {
      // 1. Berechne den Datenhash
      const dataHash = this.hashData(data);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (dataHash !== proof.dataHash) {
        console.warn('Datenhash stimmt nicht überein');
        return false;
      }
      
      // 3. Verifiziere die Signatur
      const isSignatureValid = this.verify(dataHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Datenintegritätsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Peer-Review einer Publikation
   * @param {Object} review - Review-Daten
   * @param {string} publicationDoi - DOI der Publikation
   * @param {string} privateKey - Privater Schlüssel des Reviewers
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generatePeerReviewProof(review, publicationDoi, privateKey) {
    try {
      // 1. Erstelle einen Hash des Reviews
      const reviewHash = this.hashReview(review);
      
      // 2. Erstelle einen Hash der Publikations-DOI
      const publicationHash = this.hashString(publicationDoi);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        reviewHash,
        publicationDoi,
        reviewerInfo: {
          id: review.reviewerId,
          name: review.reviewerName,
          institution: review.reviewerInstitution
        },
        reviewMetadata: {
          date: review.date,
          recommendation: review.recommendation,
          confidence: review.confidence
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Peer-Review-Beweises:', error);
      throw new Error(`Peer-Review-Beweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Peer-Review-Beweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} review - Review-Daten
   * @param {string} publicKey - Öffentlicher Schlüssel des Reviewers
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyPeerReviewProof(proof, review, publicKey) {
    try {
      // 1. Berechne den Review-Hash
      const reviewHash = this.hashReview(review);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (reviewHash !== proof.reviewHash) {
        console.warn('Review-Hash stimmt nicht überein');
        return false;
      }
      
      // 3. Berechne den Publikations-Hash
      const publicationHash = this.hashString(proof.publicationDoi);
      
      // 4. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 5. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Peer-Review-Beweises:', error);
      return false;
    }
  }
  
  /**
   * Hasht eine Publikation
   * @private
   */
  hashPublication(publication) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      doi: publication.doi,
      title: publication.title,
      authors: publication.authors.map(author => ({
        name: author.name,
        orcid: author.orcid
      })),
      abstract: publication.abstract,
      publicationYear: publication.publicationYear
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht Autorschaftsdaten
   * @private
   */
  hashAuthorship(doi, author) {
    const authorshipData = {
      doi,
      authorId: author.id,
      authorName: author.name,
      authorRole: author.role || 'author'
    };
    
    return this.hashObject(authorshipData);
  }
  
  /**
   * Hasht Forschungsdaten
   * @private
   */
  hashData(data) {
    return this.hashObject(data);
  }
  
  /**
   * Extrahiert Metadaten aus Forschungsdaten
   * @private
   */
  extractDataMetadata(data) {
    // Extrahiere Metadaten ohne die eigentlichen Daten
    const metadata = {
      dataType: typeof data,
      isArray: Array.isArray(data),
      length: Array.isArray(data) ? data.length : undefined,
      keys: typeof data === 'object' ? Object.keys(data) : undefined,
      timestamp: Date.now()
    };
    
    return metadata;
  }
  
  /**
   * Hasht Review-Daten
   * @private
   */
  hashReview(review) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      reviewerId: review.reviewerId,
      publicationDoi: review.publicationDoi,
      date: review.date,
      recommendation: review.recommendation,
      confidence: review.confidence,
      comments: review.comments
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht ein Objekt
   * @private
   */
  hashObject(obj) {
    const jsonString = JSON.stringify(obj);
    return this.hashString(jsonString);
  }
  
  /**
   * Hasht einen String
   * @private
   */
  hashString(str) {
    return crypto
      .createHash(this.options.hashAlgorithm)
      .update(str)
      .digest('hex');
  }
  
  /**
   * Kombiniert zwei Hashes
   * @private
   */
  combineHashes(hash1, hash2) {
    return this.hashString(hash1 + hash2);
  }
  
  /**
   * Signiert einen Hash mit einem privaten Schlüssel
   * @private
   */
  sign(hash, privateKey) {
    // In einer realen Implementierung würde hier eine echte Signatur erstellt
    // Für diese Beispielimplementierung simulieren wir die Signatur
    const signer = crypto.createSign('RSA-SHA256');
    signer.update(hash);
    
    try {
      // Versuche, eine echte Signatur zu erstellen, falls ein gültiger Schlüssel vorliegt
      return signer.sign(privateKey, 'hex');
    } catch (error) {
      console.warn('Simuliere Signatur, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Signatur
      return crypto
        .createHash('sha256')
        .update(hash + privateKey)
        .digest('hex');
    }
  }
  
  /**
   * Verifiziert eine Signatur mit einem öffentlichen Schlüssel
   * @private
   */
  verify(hash, signature, publicKey) {
    // In einer realen Implementierung würde hier eine echte Signaturverifizierung durchgeführt
    // Für diese Beispielimplementierung simulieren wir die Verifizierung
    try {
      // Versuche, eine echte Verifizierung durchzuführen, falls ein gültiger Schlüssel vorliegt
      const verifier = crypto.createVerify('RSA-SHA256');
      verifier.update(hash);
      return verifier.verify(publicKey, signature, 'hex');
    } catch (error) {
      console.warn('Simuliere Verifizierung, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Verifizierung
      const simulatedSignature = crypto
        .createHash('sha256')
        .update(hash + publicKey)
        .digest('hex');
      
      return simulatedSignature === signature;
    }
  }
}

export default ZkVerifier;/**
 * Zero-Knowledge-Proof-Verifizierer für wissenschaftliche Publikationen
 * Ermöglicht die Verifizierung von Autorschaft und Datenintegrität ohne sensible Daten preiszugeben
 */

import crypto from 'crypto';

class ZkVerifier {
  constructor(options = {}) {
    this.options = {
      hashAlgorithm: 'sha256',
      ...options
    };
  }
  
  /**
   * Generiert einen Beweis für die Autorschaft einer Publikation
   * @param {Object} publication - Publikationsdaten
   * @param {Object} author - Autorschaftsdaten
   * @param {string} privateKey - Privater Schlüssel des Autors
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateAuthorshipProof(publication, author, privateKey) {
    try {
      // 1. Erstelle einen Hash der Publikation
      const publicationHash = this.hashPublication(publication);
      
      // 2. Erstelle einen Hash der Autorschaftsdaten
      const authorshipHash = this.hashAuthorship(publication.doi, author);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(publicationHash, authorshipHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        publicationHash,
        authorshipClaim: {
          authorId: author.id,
          authorName: author.name,
          authorRole: author.role || 'author',
          authorshipHash
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Autorschaftsbeweises:', error);
      throw new Error(`Autorschaftsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Autorschaftsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} publication - Publikationsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel des Autors
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyAuthorshipProof(proof, publication, publicKey) {
    try {
      // 1. Berechne den Publikationshash
      const publicationHash = this.hashPublication(publication);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (publicationHash !== proof.publicationHash) {
        console.warn('Publikationshash stimmt nicht überein');
        return false;
      }
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(
        proof.publicationHash,
        proof.authorshipClaim.authorshipHash
      );
      
      // 4. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Autorschaftsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Integrität von Forschungsdaten
   * @param {Object} data - Forschungsdaten
   * @param {string} privateKey - Privater Schlüssel
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateDataIntegrityProof(data, privateKey) {
    try {
      // 1. Erstelle einen Hash der Daten
      const dataHash = this.hashData(data);
      
      // 2. Erstelle Metadaten über die Daten (ohne die eigentlichen Daten)
      const metadata = this.extractDataMetadata(data);
      
      // 3. Signiere den Hash mit dem privaten Schlüssel
      const signature = this.sign(dataHash, privateKey);
      
      // 4. Erstelle den Beweis
      return {
        dataHash,
        metadata,
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Datenintegritätsbeweises:', error);
      throw new Error(`Datenintegritätsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Datenintegritätsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} data - Forschungsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyDataIntegrityProof(proof, data, publicKey) {
    try {
      // 1. Berechne den Datenhash
      const dataHash = this.hashData(data);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (dataHash !== proof.dataHash) {
        console.warn('Datenhash stimmt nicht überein');
        return false;
      }
      
      // 3. Verifiziere die Signatur
      const isSignatureValid = this.verify(dataHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Datenintegritätsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Peer-Review einer Publikation
   * @param {Object} review - Review-Daten
   * @param {string} publicationDoi - DOI der Publikation
   * @param {string} privateKey - Privater Schlüssel des Reviewers
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generatePeerReviewProof(review, publicationDoi, privateKey) {
    try {
      // 1. Erstelle einen Hash des Reviews
      const reviewHash = this.hashReview(review);
      
      // 2. Erstelle einen Hash der Publikations-DOI
      const publicationHash = this.hashString(publicationDoi);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        reviewHash,
        publicationDoi,
        reviewerInfo: {
          id: review.reviewerId,
          name: review.reviewerName,
          institution: review.reviewerInstitution
        },
        reviewMetadata: {
          date: review.date,
          recommendation: review.recommendation,
          confidence: review.confidence
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Peer-Review-Beweises:', error);
      throw new Error(`Peer-Review-Beweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Peer-Review-Beweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} review - Review-Daten
   * @param {string} publicKey - Öffentlicher Schlüssel des Reviewers
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyPeerReviewProof(proof, review, publicKey) {
    try {
      // 1. Berechne den Review-Hash
      const reviewHash = this.hashReview(review);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (reviewHash !== proof.reviewHash) {
        console.warn('Review-Hash stimmt nicht überein');
        return false;
      }
      
      // 3. Berechne den Publikations-Hash
      const publicationHash = this.hashString(proof.publicationDoi);
      
      // 4. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 5. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Peer-Review-Beweises:', error);
      return false;
    }
  }
  
  /**
   * Hasht eine Publikation
   * @private
   */
  hashPublication(publication) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      doi: publication.doi,
      title: publication.title,
      authors: publication.authors.map(author => ({
        name: author.name,
        orcid: author.orcid
      })),
      abstract: publication.abstract,
      publicationYear: publication.publicationYear
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht Autorschaftsdaten
   * @private
   */
  hashAuthorship(doi, author) {
    const authorshipData = {
      doi,
      authorId: author.id,
      authorName: author.name,
      authorRole: author.role || 'author'
    };
    
    return this.hashObject(authorshipData);
  }
  
  /**
   * Hasht Forschungsdaten
   * @private
   */
  hashData(data) {
    return this.hashObject(data);
  }
  
  /**
   * Extrahiert Metadaten aus Forschungsdaten
   * @private
   */
  extractDataMetadata(data) {
    // Extrahiere Metadaten ohne die eigentlichen Daten
    const metadata = {
      dataType: typeof data,
      isArray: Array.isArray(data),
      length: Array.isArray(data) ? data.length : undefined,
      keys: typeof data === 'object' ? Object.keys(data) : undefined,
      timestamp: Date.now()
    };
    
    return metadata;
  }
  
  /**
   * Hasht Review-Daten
   * @private
   */
  hashReview(review) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      reviewerId: review.reviewerId,
      publicationDoi: review.publicationDoi,
      date: review.date,
      recommendation: review.recommendation,
      confidence: review.confidence,
      comments: review.comments
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht ein Objekt
   * @private
   */
  hashObject(obj) {
    const jsonString = JSON.stringify(obj);
    return this.hashString(jsonString);
  }
  
  /**
   * Hasht einen String
   * @private
   */
  hashString(str) {
    return crypto
      .createHash(this.options.hashAlgorithm)
      .update(str)
      .digest('hex');
  }
  
  /**
   * Kombiniert zwei Hashes
   * @private
   */
  combineHashes(hash1, hash2) {
    return this.hashString(hash1 + hash2);
  }
  
  /**
   * Signiert einen Hash mit einem privaten Schlüssel
   * @private
   */
  sign(hash, privateKey) {
    // In einer realen Implementierung würde hier eine echte Signatur erstellt
    // Für diese Beispielimplementierung simulieren wir die Signatur
    const signer = crypto.createSign('RSA-SHA256');
    signer.update(hash);
    
    try {
      // Versuche, eine echte Signatur zu erstellen, falls ein gültiger Schlüssel vorliegt
      return signer.sign(privateKey, 'hex');
    } catch (error) {
      console.warn('Simuliere Signatur, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Signatur
      return crypto
        .createHash('sha256')
        .update(hash + privateKey)
        .digest('hex');
    }
  }
  
  /**
   * Verifiziert eine Signatur mit einem öffentlichen Schlüssel
   * @private
   */
  verify(hash, signature, publicKey) {
    // In einer realen Implementierung würde hier eine echte Signaturverifizierung durchgeführt
    // Für diese Beispielimplementierung simulieren wir die Verifizierung
    try {
      // Versuche, eine echte Verifizierung durchzuführen, falls ein gültiger Schlüssel vorliegt
      const verifier = crypto.createVerify('RSA-SHA256');
      verifier.update(hash);
      return verifier.verify(publicKey, signature, 'hex');
    } catch (error) {
      console.warn('Simuliere Verifizierung, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Verifizierung
      const simulatedSignature = crypto
        .createHash('sha256')
        .update(hash + publicKey)
        .digest('hex');
      
      return simulatedSignature === signature;
    }
  }
}

export default ZkVerifier;/**
 * Zero-Knowledge-Proof-Verifizierer für wissenschaftliche Publikationen
 * Ermöglicht die Verifizierung von Autorschaft und Datenintegrität ohne sensible Daten preiszugeben
 */

import crypto from 'crypto';

class ZkVerifier {
  constructor(options = {}) {
    this.options = {
      hashAlgorithm: 'sha256',
      ...options
    };
  }
  
  /**
   * Generiert einen Beweis für die Autorschaft einer Publikation
   * @param {Object} publication - Publikationsdaten
   * @param {Object} author - Autorschaftsdaten
   * @param {string} privateKey - Privater Schlüssel des Autors
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateAuthorshipProof(publication, author, privateKey) {
    try {
      // 1. Erstelle einen Hash der Publikation
      const publicationHash = this.hashPublication(publication);
      
      // 2. Erstelle einen Hash der Autorschaftsdaten
      const authorshipHash = this.hashAuthorship(publication.doi, author);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(publicationHash, authorshipHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        publicationHash,
        authorshipClaim: {
          authorId: author.id,
          authorName: author.name,
          authorRole: author.role || 'author',
          authorshipHash
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Autorschaftsbeweises:', error);
      throw new Error(`Autorschaftsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Autorschaftsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} publication - Publikationsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel des Autors
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyAuthorshipProof(proof, publication, publicKey) {
    try {
      // 1. Berechne den Publikationshash
      const publicationHash = this.hashPublication(publication);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (publicationHash !== proof.publicationHash) {
        console.warn('Publikationshash stimmt nicht überein');
        return false;
      }
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(
        proof.publicationHash,
        proof.authorshipClaim.authorshipHash
      );
      
      // 4. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Autorschaftsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Integrität von Forschungsdaten
   * @param {Object} data - Forschungsdaten
   * @param {string} privateKey - Privater Schlüssel
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateDataIntegrityProof(data, privateKey) {
    try {
      // 1. Erstelle einen Hash der Daten
      const dataHash = this.hashData(data);
      
      // 2. Erstelle Metadaten über die Daten (ohne die eigentlichen Daten)
      const metadata = this.extractDataMetadata(data);
      
      // 3. Signiere den Hash mit dem privaten Schlüssel
      const signature = this.sign(dataHash, privateKey);
      
      // 4. Erstelle den Beweis
      return {
        dataHash,
        metadata,
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Datenintegritätsbeweises:', error);
      throw new Error(`Datenintegritätsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Datenintegritätsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} data - Forschungsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyDataIntegrityProof(proof, data, publicKey) {
    try {
      // 1. Berechne den Datenhash
      const dataHash = this.hashData(data);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (dataHash !== proof.dataHash) {
        console.warn('Datenhash stimmt nicht überein');
        return false;
      }
      
      // 3. Verifiziere die Signatur
      const isSignatureValid = this.verify(dataHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Datenintegritätsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Peer-Review einer Publikation
   * @param {Object} review - Review-Daten
   * @param {string} publicationDoi - DOI der Publikation
   * @param {string} privateKey - Privater Schlüssel des Reviewers
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generatePeerReviewProof(review, publicationDoi, privateKey) {
    try {
      // 1. Erstelle einen Hash des Reviews
      const reviewHash = this.hashReview(review);
      
      // 2. Erstelle einen Hash der Publikations-DOI
      const publicationHash = this.hashString(publicationDoi);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        reviewHash,
        publicationDoi,
        reviewerInfo: {
          id: review.reviewerId,
          name: review.reviewerName,
          institution: review.reviewerInstitution
        },
        reviewMetadata: {
          date: review.date,
          recommendation: review.recommendation,
          confidence: review.confidence
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Peer-Review-Beweises:', error);
      throw new Error(`Peer-Review-Beweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Peer-Review-Beweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} review - Review-Daten
   * @param {string} publicKey - Öffentlicher Schlüssel des Reviewers
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyPeerReviewProof(proof, review, publicKey) {
    try {
      // 1. Berechne den Review-Hash
      const reviewHash = this.hashReview(review);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (reviewHash !== proof.reviewHash) {
        console.warn('Review-Hash stimmt nicht überein');
        return false;
      }
      
      // 3. Berechne den Publikations-Hash
      const publicationHash = this.hashString(proof.publicationDoi);
      
      // 4. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 5. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Peer-Review-Beweises:', error);
      return false;
    }
  }
  
  /**
   * Hasht eine Publikation
   * @private
   */
  hashPublication(publication) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      doi: publication.doi,
      title: publication.title,
      authors: publication.authors.map(author => ({
        name: author.name,
        orcid: author.orcid
      })),
      abstract: publication.abstract,
      publicationYear: publication.publicationYear
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht Autorschaftsdaten
   * @private
   */
  hashAuthorship(doi, author) {
    const authorshipData = {
      doi,
      authorId: author.id,
      authorName: author.name,
      authorRole: author.role || 'author'
    };
    
    return this.hashObject(authorshipData);
  }
  
  /**
   * Hasht Forschungsdaten
   * @private
   */
  hashData(data) {
    return this.hashObject(data);
  }
  
  /**
   * Extrahiert Metadaten aus Forschungsdaten
   * @private
   */
  extractDataMetadata(data) {
    // Extrahiere Metadaten ohne die eigentlichen Daten
    const metadata = {
      dataType: typeof data,
      isArray: Array.isArray(data),
      length: Array.isArray(data) ? data.length : undefined,
      keys: typeof data === 'object' ? Object.keys(data) : undefined,
      timestamp: Date.now()
    };
    
    return metadata;
  }
  
  /**
   * Hasht Review-Daten
   * @private
   */
  hashReview(review) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      reviewerId: review.reviewerId,
      publicationDoi: review.publicationDoi,
      date: review.date,
      recommendation: review.recommendation,
      confidence: review.confidence,
      comments: review.comments
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht ein Objekt
   * @private
   */
  hashObject(obj) {
    const jsonString = JSON.stringify(obj);
    return this.hashString(jsonString);
  }
  
  /**
   * Hasht einen String
   * @private
   */
  hashString(str) {
    return crypto
      .createHash(this.options.hashAlgorithm)
      .update(str)
      .digest('hex');
  }
  
  /**
   * Kombiniert zwei Hashes
   * @private
   */
  combineHashes(hash1, hash2) {
    return this.hashString(hash1 + hash2);
  }
  
  /**
   * Signiert einen Hash mit einem privaten Schlüssel
   * @private
   */
  sign(hash, privateKey) {
    // In einer realen Implementierung würde hier eine echte Signatur erstellt
    // Für diese Beispielimplementierung simulieren wir die Signatur
    const signer = crypto.createSign('RSA-SHA256');
    signer.update(hash);
    
    try {
      // Versuche, eine echte Signatur zu erstellen, falls ein gültiger Schlüssel vorliegt
      return signer.sign(privateKey, 'hex');
    } catch (error) {
      console.warn('Simuliere Signatur, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Signatur
      return crypto
        .createHash('sha256')
        .update(hash + privateKey)
        .digest('hex');
    }
  }
  
  /**
   * Verifiziert eine Signatur mit einem öffentlichen Schlüssel
   * @private
   */
  verify(hash, signature, publicKey) {
    // In einer realen Implementierung würde hier eine echte Signaturverifizierung durchgeführt
    // Für diese Beispielimplementierung simulieren wir die Verifizierung
    try {
      // Versuche, eine echte Verifizierung durchzuführen, falls ein gültiger Schlüssel vorliegt
      const verifier = crypto.createVerify('RSA-SHA256');
      verifier.update(hash);
      return verifier.verify(publicKey, signature, 'hex');
    } catch (error) {
      console.warn('Simuliere Verifizierung, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Verifizierung
      const simulatedSignature = crypto
        .createHash('sha256')
        .update(hash + publicKey)
        .digest('hex');
      
      return simulatedSignature === signature;
    }
  }
}

export default ZkVerifier;/**
 * Zero-Knowledge-Proof-Verifizierer für wissenschaftliche Publikationen
 * Ermöglicht die Verifizierung von Autorschaft und Datenintegrität ohne sensible Daten preiszugeben
 */

import crypto from 'crypto';

class ZkVerifier {
  constructor(options = {}) {
    this.options = {
      hashAlgorithm: 'sha256',
      ...options
    };
  }
  
  /**
   * Generiert einen Beweis für die Autorschaft einer Publikation
   * @param {Object} publication - Publikationsdaten
   * @param {Object} author - Autorschaftsdaten
   * @param {string} privateKey - Privater Schlüssel des Autors
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateAuthorshipProof(publication, author, privateKey) {
    try {
      // 1. Erstelle einen Hash der Publikation
      const publicationHash = this.hashPublication(publication);
      
      // 2. Erstelle einen Hash der Autorschaftsdaten
      const authorshipHash = this.hashAuthorship(publication.doi, author);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(publicationHash, authorshipHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        publicationHash,
        authorshipClaim: {
          authorId: author.id,
          authorName: author.name,
          authorRole: author.role || 'author',
          authorshipHash
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Autorschaftsbeweises:', error);
      throw new Error(`Autorschaftsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Autorschaftsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} publication - Publikationsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel des Autors
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyAuthorshipProof(proof, publication, publicKey) {
    try {
      // 1. Berechne den Publikationshash
      const publicationHash = this.hashPublication(publication);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (publicationHash !== proof.publicationHash) {
        console.warn('Publikationshash stimmt nicht überein');
        return false;
      }
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(
        proof.publicationHash,
        proof.authorshipClaim.authorshipHash
      );
      
      // 4. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Autorschaftsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Integrität von Forschungsdaten
   * @param {Object} data - Forschungsdaten
   * @param {string} privateKey - Privater Schlüssel
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateDataIntegrityProof(data, privateKey) {
    try {
      // 1. Erstelle einen Hash der Daten
      const dataHash = this.hashData(data);
      
      // 2. Erstelle Metadaten über die Daten (ohne die eigentlichen Daten)
      const metadata = this.extractDataMetadata(data);
      
      // 3. Signiere den Hash mit dem privaten Schlüssel
      const signature = this.sign(dataHash, privateKey);
      
      // 4. Erstelle den Beweis
      return {
        dataHash,
        metadata,
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Datenintegritätsbeweises:', error);
      throw new Error(`Datenintegritätsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Datenintegritätsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} data - Forschungsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyDataIntegrityProof(proof, data, publicKey) {
    try {
      // 1. Berechne den Datenhash
      const dataHash = this.hashData(data);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (dataHash !== proof.dataHash) {
        console.warn('Datenhash stimmt nicht überein');
        return false;
      }
      
      // 3. Verifiziere die Signatur
      const isSignatureValid = this.verify(dataHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Datenintegritätsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Peer-Review einer Publikation
   * @param {Object} review - Review-Daten
   * @param {string} publicationDoi - DOI der Publikation
   * @param {string} privateKey - Privater Schlüssel des Reviewers
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generatePeerReviewProof(review, publicationDoi, privateKey) {
    try {
      // 1. Erstelle einen Hash des Reviews
      const reviewHash = this.hashReview(review);
      
      // 2. Erstelle einen Hash der Publikations-DOI
      const publicationHash = this.hashString(publicationDoi);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        reviewHash,
        publicationDoi,
        reviewerInfo: {
          id: review.reviewerId,
          name: review.reviewerName,
          institution: review.reviewerInstitution
        },
        reviewMetadata: {
          date: review.date,
          recommendation: review.recommendation,
          confidence: review.confidence
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Peer-Review-Beweises:', error);
      throw new Error(`Peer-Review-Beweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Peer-Review-Beweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} review - Review-Daten
   * @param {string} publicKey - Öffentlicher Schlüssel des Reviewers
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyPeerReviewProof(proof, review, publicKey) {
    try {
      // 1. Berechne den Review-Hash
      const reviewHash = this.hashReview(review);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (reviewHash !== proof.reviewHash) {
        console.warn('Review-Hash stimmt nicht überein');
        return false;
      }
      
      // 3. Berechne den Publikations-Hash
      const publicationHash = this.hashString(proof.publicationDoi);
      
      // 4. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 5. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Peer-Review-Beweises:', error);
      return false;
    }
  }
  
  /**
   * Hasht eine Publikation
   * @private
   */
  hashPublication(publication) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      doi: publication.doi,
      title: publication.title,
      authors: publication.authors.map(author => ({
        name: author.name,
        orcid: author.orcid
      })),
      abstract: publication.abstract,
      publicationYear: publication.publicationYear
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht Autorschaftsdaten
   * @private
   */
  hashAuthorship(doi, author) {
    const authorshipData = {
      doi,
      authorId: author.id,
      authorName: author.name,
      authorRole: author.role || 'author'
    };
    
    return this.hashObject(authorshipData);
  }
  
  /**
   * Hasht Forschungsdaten
   * @private
   */
  hashData(data) {
    return this.hashObject(data);
  }
  
  /**
   * Extrahiert Metadaten aus Forschungsdaten
   * @private
   */
  extractDataMetadata(data) {
    // Extrahiere Metadaten ohne die eigentlichen Daten
    const metadata = {
      dataType: typeof data,
      isArray: Array.isArray(data),
      length: Array.isArray(data) ? data.length : undefined,
      keys: typeof data === 'object' ? Object.keys(data) : undefined,
      timestamp: Date.now()
    };
    
    return metadata;
  }
  
  /**
   * Hasht Review-Daten
   * @private
   */
  hashReview(review) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      reviewerId: review.reviewerId,
      publicationDoi: review.publicationDoi,
      date: review.date,
      recommendation: review.recommendation,
      confidence: review.confidence,
      comments: review.comments
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht ein Objekt
   * @private
   */
  hashObject(obj) {
    const jsonString = JSON.stringify(obj);
    return this.hashString(jsonString);
  }
  
  /**
   * Hasht einen String
   * @private
   */
  hashString(str) {
    return crypto
      .createHash(this.options.hashAlgorithm)
      .update(str)
      .digest('hex');
  }
  
  /**
   * Kombiniert zwei Hashes
   * @private
   */
  combineHashes(hash1, hash2) {
    return this.hashString(hash1 + hash2);
  }
  
  /**
   * Signiert einen Hash mit einem privaten Schlüssel
   * @private
   */
  sign(hash, privateKey) {
    // In einer realen Implementierung würde hier eine echte Signatur erstellt
    // Für diese Beispielimplementierung simulieren wir die Signatur
    const signer = crypto.createSign('RSA-SHA256');
    signer.update(hash);
    
    try {
      // Versuche, eine echte Signatur zu erstellen, falls ein gültiger Schlüssel vorliegt
      return signer.sign(privateKey, 'hex');
    } catch (error) {
      console.warn('Simuliere Signatur, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Signatur
      return crypto
        .createHash('sha256')
        .update(hash + privateKey)
        .digest('hex');
    }
  }
  
  /**
   * Verifiziert eine Signatur mit einem öffentlichen Schlüssel
   * @private
   */
  verify(hash, signature, publicKey) {
    // In einer realen Implementierung würde hier eine echte Signaturverifizierung durchgeführt
    // Für diese Beispielimplementierung simulieren wir die Verifizierung
    try {
      // Versuche, eine echte Verifizierung durchzuführen, falls ein gültiger Schlüssel vorliegt
      const verifier = crypto.createVerify('RSA-SHA256');
      verifier.update(hash);
      return verifier.verify(publicKey, signature, 'hex');
    } catch (error) {
      console.warn('Simuliere Verifizierung, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Verifizierung
      const simulatedSignature = crypto
        .createHash('sha256')
        .update(hash + publicKey)
        .digest('hex');
      
      return simulatedSignature === signature;
    }
  }
}

export default ZkVerifier;/**
 * Zero-Knowledge-Proof-Verifizierer für wissenschaftliche Publikationen
 * Ermöglicht die Verifizierung von Autorschaft und Datenintegrität ohne sensible Daten preiszugeben
 */

import crypto from 'crypto';

class ZkVerifier {
  constructor(options = {}) {
    this.options = {
      hashAlgorithm: 'sha256',
      ...options
    };
  }
  
  /**
   * Generiert einen Beweis für die Autorschaft einer Publikation
   * @param {Object} publication - Publikationsdaten
   * @param {Object} author - Autorschaftsdaten
   * @param {string} privateKey - Privater Schlüssel des Autors
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateAuthorshipProof(publication, author, privateKey) {
    try {
      // 1. Erstelle einen Hash der Publikation
      const publicationHash = this.hashPublication(publication);
      
      // 2. Erstelle einen Hash der Autorschaftsdaten
      const authorshipHash = this.hashAuthorship(publication.doi, author);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(publicationHash, authorshipHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        publicationHash,
        authorshipClaim: {
          authorId: author.id,
          authorName: author.name,
          authorRole: author.role || 'author',
          authorshipHash
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Autorschaftsbeweises:', error);
      throw new Error(`Autorschaftsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Autorschaftsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} publication - Publikationsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel des Autors
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyAuthorshipProof(proof, publication, publicKey) {
    try {
      // 1. Berechne den Publikationshash
      const publicationHash = this.hashPublication(publication);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (publicationHash !== proof.publicationHash) {
        console.warn('Publikationshash stimmt nicht überein');
        return false;
      }
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(
        proof.publicationHash,
        proof.authorshipClaim.authorshipHash
      );
      
      // 4. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Autorschaftsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Integrität von Forschungsdaten
   * @param {Object} data - Forschungsdaten
   * @param {string} privateKey - Privater Schlüssel
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateDataIntegrityProof(data, privateKey) {
    try {
      // 1. Erstelle einen Hash der Daten
      const dataHash = this.hashData(data);
      
      // 2. Erstelle Metadaten über die Daten (ohne die eigentlichen Daten)
      const metadata = this.extractDataMetadata(data);
      
      // 3. Signiere den Hash mit dem privaten Schlüssel
      const signature = this.sign(dataHash, privateKey);
      
      // 4. Erstelle den Beweis
      return {
        dataHash,
        metadata,
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Datenintegritätsbeweises:', error);
      throw new Error(`Datenintegritätsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Datenintegritätsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} data - Forschungsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyDataIntegrityProof(proof, data, publicKey) {
    try {
      // 1. Berechne den Datenhash
      const dataHash = this.hashData(data);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (dataHash !== proof.dataHash) {
        console.warn('Datenhash stimmt nicht überein');
        return false;
      }
      
      // 3. Verifiziere die Signatur
      const isSignatureValid = this.verify(dataHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Datenintegritätsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Peer-Review einer Publikation
   * @param {Object} review - Review-Daten
   * @param {string} publicationDoi - DOI der Publikation
   * @param {string} privateKey - Privater Schlüssel des Reviewers
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generatePeerReviewProof(review, publicationDoi, privateKey) {
    try {
      // 1. Erstelle einen Hash des Reviews
      const reviewHash = this.hashReview(review);
      
      // 2. Erstelle einen Hash der Publikations-DOI
      const publicationHash = this.hashString(publicationDoi);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        reviewHash,
        publicationDoi,
        reviewerInfo: {
          id: review.reviewerId,
          name: review.reviewerName,
          institution: review.reviewerInstitution
        },
        reviewMetadata: {
          date: review.date,
          recommendation: review.recommendation,
          confidence: review.confidence
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Peer-Review-Beweises:', error);
      throw new Error(`Peer-Review-Beweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Peer-Review-Beweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} review - Review-Daten
   * @param {string} publicKey - Öffentlicher Schlüssel des Reviewers
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyPeerReviewProof(proof, review, publicKey) {
    try {
      // 1. Berechne den Review-Hash
      const reviewHash = this.hashReview(review);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (reviewHash !== proof.reviewHash) {
        console.warn('Review-Hash stimmt nicht überein');
        return false;
      }
      
      // 3. Berechne den Publikations-Hash
      const publicationHash = this.hashString(proof.publicationDoi);
      
      // 4. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 5. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Peer-Review-Beweises:', error);
      return false;
    }
  }
  
  /**
   * Hasht eine Publikation
   * @private
   */
  hashPublication(publication) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      doi: publication.doi,
      title: publication.title,
      authors: publication.authors.map(author => ({
        name: author.name,
        orcid: author.orcid
      })),
      abstract: publication.abstract,
      publicationYear: publication.publicationYear
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht Autorschaftsdaten
   * @private
   */
  hashAuthorship(doi, author) {
    const authorshipData = {
      doi,
      authorId: author.id,
      authorName: author.name,
      authorRole: author.role || 'author'
    };
    
    return this.hashObject(authorshipData);
  }
  
  /**
   * Hasht Forschungsdaten
   * @private
   */
  hashData(data) {
    return this.hashObject(data);
  }
  
  /**
   * Extrahiert Metadaten aus Forschungsdaten
   * @private
   */
  extractDataMetadata(data) {
    // Extrahiere Metadaten ohne die eigentlichen Daten
    const metadata = {
      dataType: typeof data,
      isArray: Array.isArray(data),
      length: Array.isArray(data) ? data.length : undefined,
      keys: typeof data === 'object' ? Object.keys(data) : undefined,
      timestamp: Date.now()
    };
    
    return metadata;
  }
  
  /**
   * Hasht Review-Daten
   * @private
   */
  hashReview(review) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      reviewerId: review.reviewerId,
      publicationDoi: review.publicationDoi,
      date: review.date,
      recommendation: review.recommendation,
      confidence: review.confidence,
      comments: review.comments
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht ein Objekt
   * @private
   */
  hashObject(obj) {
    const jsonString = JSON.stringify(obj);
    return this.hashString(jsonString);
  }
  
  /**
   * Hasht einen String
   * @private
   */
  hashString(str) {
    return crypto
      .createHash(this.options.hashAlgorithm)
      .update(str)
      .digest('hex');
  }
  
  /**
   * Kombiniert zwei Hashes
   * @private
   */
  combineHashes(hash1, hash2) {
    return this.hashString(hash1 + hash2);
  }
  
  /**
   * Signiert einen Hash mit einem privaten Schlüssel
   * @private
   */
  sign(hash, privateKey) {
    // In einer realen Implementierung würde hier eine echte Signatur erstellt
    // Für diese Beispielimplementierung simulieren wir die Signatur
    const signer = crypto.createSign('RSA-SHA256');
    signer.update(hash);
    
    try {
      // Versuche, eine echte Signatur zu erstellen, falls ein gültiger Schlüssel vorliegt
      return signer.sign(privateKey, 'hex');
    } catch (error) {
      console.warn('Simuliere Signatur, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Signatur
      return crypto
        .createHash('sha256')
        .update(hash + privateKey)
        .digest('hex');
    }
  }
  
  /**
   * Verifiziert eine Signatur mit einem öffentlichen Schlüssel
   * @private
   */
  verify(hash, signature, publicKey) {
    // In einer realen Implementierung würde hier eine echte Signaturverifizierung durchgeführt
    // Für diese Beispielimplementierung simulieren wir die Verifizierung
    try {
      // Versuche, eine echte Verifizierung durchzuführen, falls ein gültiger Schlüssel vorliegt
      const verifier = crypto.createVerify('RSA-SHA256');
      verifier.update(hash);
      return verifier.verify(publicKey, signature, 'hex');
    } catch (error) {
      console.warn('Simuliere Verifizierung, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Verifizierung
      const simulatedSignature = crypto
        .createHash('sha256')
        .update(hash + publicKey)
        .digest('hex');
      
      return simulatedSignature === signature;
    }
  }
}

export default ZkVerifier;/**
 * Zero-Knowledge-Proof-Verifizierer für wissenschaftliche Publikationen
 * Ermöglicht die Verifizierung von Autorschaft und Datenintegrität ohne sensible Daten preiszugeben
 */

import crypto from 'crypto';

class ZkVerifier {
  constructor(options = {}) {
    this.options = {
      hashAlgorithm: 'sha256',
      ...options
    };
  }
  
  /**
   * Generiert einen Beweis für die Autorschaft einer Publikation
   * @param {Object} publication - Publikationsdaten
   * @param {Object} author - Autorschaftsdaten
   * @param {string} privateKey - Privater Schlüssel des Autors
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateAuthorshipProof(publication, author, privateKey) {
    try {
      // 1. Erstelle einen Hash der Publikation
      const publicationHash = this.hashPublication(publication);
      
      // 2. Erstelle einen Hash der Autorschaftsdaten
      const authorshipHash = this.hashAuthorship(publication.doi, author);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(publicationHash, authorshipHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        publicationHash,
        authorshipClaim: {
          authorId: author.id,
          authorName: author.name,
          authorRole: author.role || 'author',
          authorshipHash
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Autorschaftsbeweises:', error);
      throw new Error(`Autorschaftsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Autorschaftsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} publication - Publikationsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel des Autors
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyAuthorshipProof(proof, publication, publicKey) {
    try {
      // 1. Berechne den Publikationshash
      const publicationHash = this.hashPublication(publication);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (publicationHash !== proof.publicationHash) {
        console.warn('Publikationshash stimmt nicht überein');
        return false;
      }
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(
        proof.publicationHash,
        proof.authorshipClaim.authorshipHash
      );
      
      // 4. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Autorschaftsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Integrität von Forschungsdaten
   * @param {Object} data - Forschungsdaten
   * @param {string} privateKey - Privater Schlüssel
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateDataIntegrityProof(data, privateKey) {
    try {
      // 1. Erstelle einen Hash der Daten
      const dataHash = this.hashData(data);
      
      // 2. Erstelle Metadaten über die Daten (ohne die eigentlichen Daten)
      const metadata = this.extractDataMetadata(data);
      
      // 3. Signiere den Hash mit dem privaten Schlüssel
      const signature = this.sign(dataHash, privateKey);
      
      // 4. Erstelle den Beweis
      return {
        dataHash,
        metadata,
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Datenintegritätsbeweises:', error);
      throw new Error(`Datenintegritätsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Datenintegritätsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} data - Forschungsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyDataIntegrityProof(proof, data, publicKey) {
    try {
      // 1. Berechne den Datenhash
      const dataHash = this.hashData(data);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (dataHash !== proof.dataHash) {
        console.warn('Datenhash stimmt nicht überein');
        return false;
      }
      
      // 3. Verifiziere die Signatur
      const isSignatureValid = this.verify(dataHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Datenintegritätsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Peer-Review einer Publikation
   * @param {Object} review - Review-Daten
   * @param {string} publicationDoi - DOI der Publikation
   * @param {string} privateKey - Privater Schlüssel des Reviewers
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generatePeerReviewProof(review, publicationDoi, privateKey) {
    try {
      // 1. Erstelle einen Hash des Reviews
      const reviewHash = this.hashReview(review);
      
      // 2. Erstelle einen Hash der Publikations-DOI
      const publicationHash = this.hashString(publicationDoi);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        reviewHash,
        publicationDoi,
        reviewerInfo: {
          id: review.reviewerId,
          name: review.reviewerName,
          institution: review.reviewerInstitution
        },
        reviewMetadata: {
          date: review.date,
          recommendation: review.recommendation,
          confidence: review.confidence
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Peer-Review-Beweises:', error);
      throw new Error(`Peer-Review-Beweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Peer-Review-Beweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} review - Review-Daten
   * @param {string} publicKey - Öffentlicher Schlüssel des Reviewers
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyPeerReviewProof(proof, review, publicKey) {
    try {
      // 1. Berechne den Review-Hash
      const reviewHash = this.hashReview(review);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (reviewHash !== proof.reviewHash) {
        console.warn('Review-Hash stimmt nicht überein');
        return false;
      }
      
      // 3. Berechne den Publikations-Hash
      const publicationHash = this.hashString(proof.publicationDoi);
      
      // 4. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 5. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Peer-Review-Beweises:', error);
      return false;
    }
  }
  
  /**
   * Hasht eine Publikation
   * @private
   */
  hashPublication(publication) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      doi: publication.doi,
      title: publication.title,
      authors: publication.authors.map(author => ({
        name: author.name,
        orcid: author.orcid
      })),
      abstract: publication.abstract,
      publicationYear: publication.publicationYear
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht Autorschaftsdaten
   * @private
   */
  hashAuthorship(doi, author) {
    const authorshipData = {
      doi,
      authorId: author.id,
      authorName: author.name,
      authorRole: author.role || 'author'
    };
    
    return this.hashObject(authorshipData);
  }
  
  /**
   * Hasht Forschungsdaten
   * @private
   */
  hashData(data) {
    return this.hashObject(data);
  }
  
  /**
   * Extrahiert Metadaten aus Forschungsdaten
   * @private
   */
  extractDataMetadata(data) {
    // Extrahiere Metadaten ohne die eigentlichen Daten
    const metadata = {
      dataType: typeof data,
      isArray: Array.isArray(data),
      length: Array.isArray(data) ? data.length : undefined,
      keys: typeof data === 'object' ? Object.keys(data) : undefined,
      timestamp: Date.now()
    };
    
    return metadata;
  }
  
  /**
   * Hasht Review-Daten
   * @private
   */
  hashReview(review) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      reviewerId: review.reviewerId,
      publicationDoi: review.publicationDoi,
      date: review.date,
      recommendation: review.recommendation,
      confidence: review.confidence,
      comments: review.comments
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht ein Objekt
   * @private
   */
  hashObject(obj) {
    const jsonString = JSON.stringify(obj);
    return this.hashString(jsonString);
  }
  
  /**
   * Hasht einen String
   * @private
   */
  hashString(str) {
    return crypto
      .createHash(this.options.hashAlgorithm)
      .update(str)
      .digest('hex');
  }
  
  /**
   * Kombiniert zwei Hashes
   * @private
   */
  combineHashes(hash1, hash2) {
    return this.hashString(hash1 + hash2);
  }
  
  /**
   * Signiert einen Hash mit einem privaten Schlüssel
   * @private
   */
  sign(hash, privateKey) {
    // In einer realen Implementierung würde hier eine echte Signatur erstellt
    // Für diese Beispielimplementierung simulieren wir die Signatur
    const signer = crypto.createSign('RSA-SHA256');
    signer.update(hash);
    
    try {
      // Versuche, eine echte Signatur zu erstellen, falls ein gültiger Schlüssel vorliegt
      return signer.sign(privateKey, 'hex');
    } catch (error) {
      console.warn('Simuliere Signatur, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Signatur
      return crypto
        .createHash('sha256')
        .update(hash + privateKey)
        .digest('hex');
    }
  }
  
  /**
   * Verifiziert eine Signatur mit einem öffentlichen Schlüssel
   * @private
   */
  verify(hash, signature, publicKey) {
    // In einer realen Implementierung würde hier eine echte Signaturverifizierung durchgeführt
    // Für diese Beispielimplementierung simulieren wir die Verifizierung
    try {
      // Versuche, eine echte Verifizierung durchzuführen, falls ein gültiger Schlüssel vorliegt
      const verifier = crypto.createVerify('RSA-SHA256');
      verifier.update(hash);
      return verifier.verify(publicKey, signature, 'hex');
    } catch (error) {
      console.warn('Simuliere Verifizierung, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Verifizierung
      const simulatedSignature = crypto
        .createHash('sha256')
        .update(hash + publicKey)
        .digest('hex');
      
      return simulatedSignature === signature;
    }
  }
}

export default ZkVerifier;/**
 * Zero-Knowledge-Proof-Verifizierer für wissenschaftliche Publikationen
 * Ermöglicht die Verifizierung von Autorschaft und Datenintegrität ohne sensible Daten preiszugeben
 */

import crypto from 'crypto';

class ZkVerifier {
  constructor(options = {}) {
    this.options = {
      hashAlgorithm: 'sha256',
      ...options
    };
  }
  
  /**
   * Generiert einen Beweis für die Autorschaft einer Publikation
   * @param {Object} publication - Publikationsdaten
   * @param {Object} author - Autorschaftsdaten
   * @param {string} privateKey - Privater Schlüssel des Autors
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateAuthorshipProof(publication, author, privateKey) {
    try {
      // 1. Erstelle einen Hash der Publikation
      const publicationHash = this.hashPublication(publication);
      
      // 2. Erstelle einen Hash der Autorschaftsdaten
      const authorshipHash = this.hashAuthorship(publication.doi, author);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(publicationHash, authorshipHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        publicationHash,
        authorshipClaim: {
          authorId: author.id,
          authorName: author.name,
          authorRole: author.role || 'author',
          authorshipHash
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Autorschaftsbeweises:', error);
      throw new Error(`Autorschaftsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Autorschaftsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} publication - Publikationsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel des Autors
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyAuthorshipProof(proof, publication, publicKey) {
    try {
      // 1. Berechne den Publikationshash
      const publicationHash = this.hashPublication(publication);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (publicationHash !== proof.publicationHash) {
        console.warn('Publikationshash stimmt nicht überein');
        return false;
      }
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(
        proof.publicationHash,
        proof.authorshipClaim.authorshipHash
      );
      
      // 4. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Autorschaftsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Integrität von Forschungsdaten
   * @param {Object} data - Forschungsdaten
   * @param {string} privateKey - Privater Schlüssel
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateDataIntegrityProof(data, privateKey) {
    try {
      // 1. Erstelle einen Hash der Daten
      const dataHash = this.hashData(data);
      
      // 2. Erstelle Metadaten über die Daten (ohne die eigentlichen Daten)
      const metadata = this.extractDataMetadata(data);
      
      // 3. Signiere den Hash mit dem privaten Schlüssel
      const signature = this.sign(dataHash, privateKey);
      
      // 4. Erstelle den Beweis
      return {
        dataHash,
        metadata,
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Datenintegritätsbeweises:', error);
      throw new Error(`Datenintegritätsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Datenintegritätsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} data - Forschungsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyDataIntegrityProof(proof, data, publicKey) {
    try {
      // 1. Berechne den Datenhash
      const dataHash = this.hashData(data);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (dataHash !== proof.dataHash) {
        console.warn('Datenhash stimmt nicht überein');
        return false;
      }
      
      // 3. Verifiziere die Signatur
      const isSignatureValid = this.verify(dataHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Datenintegritätsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Peer-Review einer Publikation
   * @param {Object} review - Review-Daten
   * @param {string} publicationDoi - DOI der Publikation
   * @param {string} privateKey - Privater Schlüssel des Reviewers
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generatePeerReviewProof(review, publicationDoi, privateKey) {
    try {
      // 1. Erstelle einen Hash des Reviews
      const reviewHash = this.hashReview(review);
      
      // 2. Erstelle einen Hash der Publikations-DOI
      const publicationHash = this.hashString(publicationDoi);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        reviewHash,
        publicationDoi,
        reviewerInfo: {
          id: review.reviewerId,
          name: review.reviewerName,
          institution: review.reviewerInstitution
        },
        reviewMetadata: {
          date: review.date,
          recommendation: review.recommendation,
          confidence: review.confidence
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Peer-Review-Beweises:', error);
      throw new Error(`Peer-Review-Beweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Peer-Review-Beweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} review - Review-Daten
   * @param {string} publicKey - Öffentlicher Schlüssel des Reviewers
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyPeerReviewProof(proof, review, publicKey) {
    try {
      // 1. Berechne den Review-Hash
      const reviewHash = this.hashReview(review);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (reviewHash !== proof.reviewHash) {
        console.warn('Review-Hash stimmt nicht überein');
        return false;
      }
      
      // 3. Berechne den Publikations-Hash
      const publicationHash = this.hashString(proof.publicationDoi);
      
      // 4. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 5. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Peer-Review-Beweises:', error);
      return false;
    }
  }
  
  /**
   * Hasht eine Publikation
   * @private
   */
  hashPublication(publication) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      doi: publication.doi,
      title: publication.title,
      authors: publication.authors.map(author => ({
        name: author.name,
        orcid: author.orcid
      })),
      abstract: publication.abstract,
      publicationYear: publication.publicationYear
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht Autorschaftsdaten
   * @private
   */
  hashAuthorship(doi, author) {
    const authorshipData = {
      doi,
      authorId: author.id,
      authorName: author.name,
      authorRole: author.role || 'author'
    };
    
    return this.hashObject(authorshipData);
  }
  
  /**
   * Hasht Forschungsdaten
   * @private
   */
  hashData(data) {
    return this.hashObject(data);
  }
  
  /**
   * Extrahiert Metadaten aus Forschungsdaten
   * @private
   */
  extractDataMetadata(data) {
    // Extrahiere Metadaten ohne die eigentlichen Daten
    const metadata = {
      dataType: typeof data,
      isArray: Array.isArray(data),
      length: Array.isArray(data) ? data.length : undefined,
      keys: typeof data === 'object' ? Object.keys(data) : undefined,
      timestamp: Date.now()
    };
    
    return metadata;
  }
  
  /**
   * Hasht Review-Daten
   * @private
   */
  hashReview(review) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      reviewerId: review.reviewerId,
      publicationDoi: review.publicationDoi,
      date: review.date,
      recommendation: review.recommendation,
      confidence: review.confidence,
      comments: review.comments
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht ein Objekt
   * @private
   */
  hashObject(obj) {
    const jsonString = JSON.stringify(obj);
    return this.hashString(jsonString);
  }
  
  /**
   * Hasht einen String
   * @private
   */
  hashString(str) {
    return crypto
      .createHash(this.options.hashAlgorithm)
      .update(str)
      .digest('hex');
  }
  
  /**
   * Kombiniert zwei Hashes
   * @private
   */
  combineHashes(hash1, hash2) {
    return this.hashString(hash1 + hash2);
  }
  
  /**
   * Signiert einen Hash mit einem privaten Schlüssel
   * @private
   */
  sign(hash, privateKey) {
    // In einer realen Implementierung würde hier eine echte Signatur erstellt
    // Für diese Beispielimplementierung simulieren wir die Signatur
    const signer = crypto.createSign('RSA-SHA256');
    signer.update(hash);
    
    try {
      // Versuche, eine echte Signatur zu erstellen, falls ein gültiger Schlüssel vorliegt
      return signer.sign(privateKey, 'hex');
    } catch (error) {
      console.warn('Simuliere Signatur, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Signatur
      return crypto
        .createHash('sha256')
        .update(hash + privateKey)
        .digest('hex');
    }
  }
  
  /**
   * Verifiziert eine Signatur mit einem öffentlichen Schlüssel
   * @private
   */
  verify(hash, signature, publicKey) {
    // In einer realen Implementierung würde hier eine echte Signaturverifizierung durchgeführt
    // Für diese Beispielimplementierung simulieren wir die Verifizierung
    try {
      // Versuche, eine echte Verifizierung durchzuführen, falls ein gültiger Schlüssel vorliegt
      const verifier = crypto.createVerify('RSA-SHA256');
      verifier.update(hash);
      return verifier.verify(publicKey, signature, 'hex');
    } catch (error) {
      console.warn('Simuliere Verifizierung, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Verifizierung
      const simulatedSignature = crypto
        .createHash('sha256')
        .update(hash + publicKey)
        .digest('hex');
      
      return simulatedSignature === signature;
    }
  }
}

export default ZkVerifier;/**
 * Zero-Knowledge-Proof-Verifizierer für wissenschaftliche Publikationen
 * Ermöglicht die Verifizierung von Autorschaft und Datenintegrität ohne sensible Daten preiszugeben
 */

import crypto from 'crypto';

class ZkVerifier {
  constructor(options = {}) {
    this.options = {
      hashAlgorithm: 'sha256',
      ...options
    };
  }
  
  /**
   * Generiert einen Beweis für die Autorschaft einer Publikation
   * @param {Object} publication - Publikationsdaten
   * @param {Object} author - Autorschaftsdaten
   * @param {string} privateKey - Privater Schlüssel des Autors
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateAuthorshipProof(publication, author, privateKey) {
    try {
      // 1. Erstelle einen Hash der Publikation
      const publicationHash = this.hashPublication(publication);
      
      // 2. Erstelle einen Hash der Autorschaftsdaten
      const authorshipHash = this.hashAuthorship(publication.doi, author);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(publicationHash, authorshipHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        publicationHash,
        authorshipClaim: {
          authorId: author.id,
          authorName: author.name,
          authorRole: author.role || 'author',
          authorshipHash
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Autorschaftsbeweises:', error);
      throw new Error(`Autorschaftsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Autorschaftsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} publication - Publikationsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel des Autors
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyAuthorshipProof(proof, publication, publicKey) {
    try {
      // 1. Berechne den Publikationshash
      const publicationHash = this.hashPublication(publication);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (publicationHash !== proof.publicationHash) {
        console.warn('Publikationshash stimmt nicht überein');
        return false;
      }
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(
        proof.publicationHash,
        proof.authorshipClaim.authorshipHash
      );
      
      // 4. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Autorschaftsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Integrität von Forschungsdaten
   * @param {Object} data - Forschungsdaten
   * @param {string} privateKey - Privater Schlüssel
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateDataIntegrityProof(data, privateKey) {
    try {
      // 1. Erstelle einen Hash der Daten
      const dataHash = this.hashData(data);
      
      // 2. Erstelle Metadaten über die Daten (ohne die eigentlichen Daten)
      const metadata = this.extractDataMetadata(data);
      
      // 3. Signiere den Hash mit dem privaten Schlüssel
      const signature = this.sign(dataHash, privateKey);
      
      // 4. Erstelle den Beweis
      return {
        dataHash,
        metadata,
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Datenintegritätsbeweises:', error);
      throw new Error(`Datenintegritätsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Datenintegritätsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} data - Forschungsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyDataIntegrityProof(proof, data, publicKey) {
    try {
      // 1. Berechne den Datenhash
      const dataHash = this.hashData(data);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (dataHash !== proof.dataHash) {
        console.warn('Datenhash stimmt nicht überein');
        return false;
      }
      
      // 3. Verifiziere die Signatur
      const isSignatureValid = this.verify(dataHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Datenintegritätsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Peer-Review einer Publikation
   * @param {Object} review - Review-Daten
   * @param {string} publicationDoi - DOI der Publikation
   * @param {string} privateKey - Privater Schlüssel des Reviewers
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generatePeerReviewProof(review, publicationDoi, privateKey) {
    try {
      // 1. Erstelle einen Hash des Reviews
      const reviewHash = this.hashReview(review);
      
      // 2. Erstelle einen Hash der Publikations-DOI
      const publicationHash = this.hashString(publicationDoi);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        reviewHash,
        publicationDoi,
        reviewerInfo: {
          id: review.reviewerId,
          name: review.reviewerName,
          institution: review.reviewerInstitution
        },
        reviewMetadata: {
          date: review.date,
          recommendation: review.recommendation,
          confidence: review.confidence
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Peer-Review-Beweises:', error);
      throw new Error(`Peer-Review-Beweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Peer-Review-Beweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} review - Review-Daten
   * @param {string} publicKey - Öffentlicher Schlüssel des Reviewers
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyPeerReviewProof(proof, review, publicKey) {
    try {
      // 1. Berechne den Review-Hash
      const reviewHash = this.hashReview(review);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (reviewHash !== proof.reviewHash) {
        console.warn('Review-Hash stimmt nicht überein');
        return false;
      }
      
      // 3. Berechne den Publikations-Hash
      const publicationHash = this.hashString(proof.publicationDoi);
      
      // 4. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 5. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Peer-Review-Beweises:', error);
      return false;
    }
  }
  
  /**
   * Hasht eine Publikation
   * @private
   */
  hashPublication(publication) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      doi: publication.doi,
      title: publication.title,
      authors: publication.authors.map(author => ({
        name: author.name,
        orcid: author.orcid
      })),
      abstract: publication.abstract,
      publicationYear: publication.publicationYear
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht Autorschaftsdaten
   * @private
   */
  hashAuthorship(doi, author) {
    const authorshipData = {
      doi,
      authorId: author.id,
      authorName: author.name,
      authorRole: author.role || 'author'
    };
    
    return this.hashObject(authorshipData);
  }
  
  /**
   * Hasht Forschungsdaten
   * @private
   */
  hashData(data) {
    return this.hashObject(data);
  }
  
  /**
   * Extrahiert Metadaten aus Forschungsdaten
   * @private
   */
  extractDataMetadata(data) {
    // Extrahiere Metadaten ohne die eigentlichen Daten
    const metadata = {
      dataType: typeof data,
      isArray: Array.isArray(data),
      length: Array.isArray(data) ? data.length : undefined,
      keys: typeof data === 'object' ? Object.keys(data) : undefined,
      timestamp: Date.now()
    };
    
    return metadata;
  }
  
  /**
   * Hasht Review-Daten
   * @private
   */
  hashReview(review) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      reviewerId: review.reviewerId,
      publicationDoi: review.publicationDoi,
      date: review.date,
      recommendation: review.recommendation,
      confidence: review.confidence,
      comments: review.comments
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht ein Objekt
   * @private
   */
  hashObject(obj) {
    const jsonString = JSON.stringify(obj);
    return this.hashString(jsonString);
  }
  
  /**
   * Hasht einen String
   * @private
   */
  hashString(str) {
    return crypto
      .createHash(this.options.hashAlgorithm)
      .update(str)
      .digest('hex');
  }
  
  /**
   * Kombiniert zwei Hashes
   * @private
   */
  combineHashes(hash1, hash2) {
    return this.hashString(hash1 + hash2);
  }
  
  /**
   * Signiert einen Hash mit einem privaten Schlüssel
   * @private
   */
  sign(hash, privateKey) {
    // In einer realen Implementierung würde hier eine echte Signatur erstellt
    // Für diese Beispielimplementierung simulieren wir die Signatur
    const signer = crypto.createSign('RSA-SHA256');
    signer.update(hash);
    
    try {
      // Versuche, eine echte Signatur zu erstellen, falls ein gültiger Schlüssel vorliegt
      return signer.sign(privateKey, 'hex');
    } catch (error) {
      console.warn('Simuliere Signatur, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Signatur
      return crypto
        .createHash('sha256')
        .update(hash + privateKey)
        .digest('hex');
    }
  }
  
  /**
   * Verifiziert eine Signatur mit einem öffentlichen Schlüssel
   * @private
   */
  verify(hash, signature, publicKey) {
    // In einer realen Implementierung würde hier eine echte Signaturverifizierung durchgeführt
    // Für diese Beispielimplementierung simulieren wir die Verifizierung
    try {
      // Versuche, eine echte Verifizierung durchzuführen, falls ein gültiger Schlüssel vorliegt
      const verifier = crypto.createVerify('RSA-SHA256');
      verifier.update(hash);
      return verifier.verify(publicKey, signature, 'hex');
    } catch (error) {
      console.warn('Simuliere Verifizierung, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Verifizierung
      const simulatedSignature = crypto
        .createHash('sha256')
        .update(hash + publicKey)
        .digest('hex');
      
      return simulatedSignature === signature;
    }
  }
}

export default ZkVerifier;/**
 * Zero-Knowledge-Proof-Verifizierer für wissenschaftliche Publikationen
 * Ermöglicht die Verifizierung von Autorschaft und Datenintegrität ohne sensible Daten preiszugeben
 */

import crypto from 'crypto';

class ZkVerifier {
  constructor(options = {}) {
    this.options = {
      hashAlgorithm: 'sha256',
      ...options
    };
  }
  
  /**
   * Generiert einen Beweis für die Autorschaft einer Publikation
   * @param {Object} publication - Publikationsdaten
   * @param {Object} author - Autorschaftsdaten
   * @param {string} privateKey - Privater Schlüssel des Autors
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateAuthorshipProof(publication, author, privateKey) {
    try {
      // 1. Erstelle einen Hash der Publikation
      const publicationHash = this.hashPublication(publication);
      
      // 2. Erstelle einen Hash der Autorschaftsdaten
      const authorshipHash = this.hashAuthorship(publication.doi, author);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(publicationHash, authorshipHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        publicationHash,
        authorshipClaim: {
          authorId: author.id,
          authorName: author.name,
          authorRole: author.role || 'author',
          authorshipHash
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Autorschaftsbeweises:', error);
      throw new Error(`Autorschaftsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Autorschaftsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} publication - Publikationsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel des Autors
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyAuthorshipProof(proof, publication, publicKey) {
    try {
      // 1. Berechne den Publikationshash
      const publicationHash = this.hashPublication(publication);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (publicationHash !== proof.publicationHash) {
        console.warn('Publikationshash stimmt nicht überein');
        return false;
      }
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(
        proof.publicationHash,
        proof.authorshipClaim.authorshipHash
      );
      
      // 4. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Autorschaftsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Integrität von Forschungsdaten
   * @param {Object} data - Forschungsdaten
   * @param {string} privateKey - Privater Schlüssel
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateDataIntegrityProof(data, privateKey) {
    try {
      // 1. Erstelle einen Hash der Daten
      const dataHash = this.hashData(data);
      
      // 2. Erstelle Metadaten über die Daten (ohne die eigentlichen Daten)
      const metadata = this.extractDataMetadata(data);
      
      // 3. Signiere den Hash mit dem privaten Schlüssel
      const signature = this.sign(dataHash, privateKey);
      
      // 4. Erstelle den Beweis
      return {
        dataHash,
        metadata,
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Datenintegritätsbeweises:', error);
      throw new Error(`Datenintegritätsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Datenintegritätsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} data - Forschungsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyDataIntegrityProof(proof, data, publicKey) {
    try {
      // 1. Berechne den Datenhash
      const dataHash = this.hashData(data);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (dataHash !== proof.dataHash) {
        console.warn('Datenhash stimmt nicht überein');
        return false;
      }
      
      // 3. Verifiziere die Signatur
      const isSignatureValid = this.verify(dataHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Datenintegritätsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Peer-Review einer Publikation
   * @param {Object} review - Review-Daten
   * @param {string} publicationDoi - DOI der Publikation
   * @param {string} privateKey - Privater Schlüssel des Reviewers
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generatePeerReviewProof(review, publicationDoi, privateKey) {
    try {
      // 1. Erstelle einen Hash des Reviews
      const reviewHash = this.hashReview(review);
      
      // 2. Erstelle einen Hash der Publikations-DOI
      const publicationHash = this.hashString(publicationDoi);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        reviewHash,
        publicationDoi,
        reviewerInfo: {
          id: review.reviewerId,
          name: review.reviewerName,
          institution: review.reviewerInstitution
        },
        reviewMetadata: {
          date: review.date,
          recommendation: review.recommendation,
          confidence: review.confidence
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Peer-Review-Beweises:', error);
      throw new Error(`Peer-Review-Beweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Peer-Review-Beweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} review - Review-Daten
   * @param {string} publicKey - Öffentlicher Schlüssel des Reviewers
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyPeerReviewProof(proof, review, publicKey) {
    try {
      // 1. Berechne den Review-Hash
      const reviewHash = this.hashReview(review);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (reviewHash !== proof.reviewHash) {
        console.warn('Review-Hash stimmt nicht überein');
        return false;
      }
      
      // 3. Berechne den Publikations-Hash
      const publicationHash = this.hashString(proof.publicationDoi);
      
      // 4. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 5. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Peer-Review-Beweises:', error);
      return false;
    }
  }
  
  /**
   * Hasht eine Publikation
   * @private
   */
  hashPublication(publication) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      doi: publication.doi,
      title: publication.title,
      authors: publication.authors.map(author => ({
        name: author.name,
        orcid: author.orcid
      })),
      abstract: publication.abstract,
      publicationYear: publication.publicationYear
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht Autorschaftsdaten
   * @private
   */
  hashAuthorship(doi, author) {
    const authorshipData = {
      doi,
      authorId: author.id,
      authorName: author.name,
      authorRole: author.role || 'author'
    };
    
    return this.hashObject(authorshipData);
  }
  
  /**
   * Hasht Forschungsdaten
   * @private
   */
  hashData(data) {
    return this.hashObject(data);
  }
  
  /**
   * Extrahiert Metadaten aus Forschungsdaten
   * @private
   */
  extractDataMetadata(data) {
    // Extrahiere Metadaten ohne die eigentlichen Daten
    const metadata = {
      dataType: typeof data,
      isArray: Array.isArray(data),
      length: Array.isArray(data) ? data.length : undefined,
      keys: typeof data === 'object' ? Object.keys(data) : undefined,
      timestamp: Date.now()
    };
    
    return metadata;
  }
  
  /**
   * Hasht Review-Daten
   * @private
   */
  hashReview(review) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      reviewerId: review.reviewerId,
      publicationDoi: review.publicationDoi,
      date: review.date,
      recommendation: review.recommendation,
      confidence: review.confidence,
      comments: review.comments
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht ein Objekt
   * @private
   */
  hashObject(obj) {
    const jsonString = JSON.stringify(obj);
    return this.hashString(jsonString);
  }
  
  /**
   * Hasht einen String
   * @private
   */
  hashString(str) {
    return crypto
      .createHash(this.options.hashAlgorithm)
      .update(str)
      .digest('hex');
  }
  
  /**
   * Kombiniert zwei Hashes
   * @private
   */
  combineHashes(hash1, hash2) {
    return this.hashString(hash1 + hash2);
  }
  
  /**
   * Signiert einen Hash mit einem privaten Schlüssel
   * @private
   */
  sign(hash, privateKey) {
    // In einer realen Implementierung würde hier eine echte Signatur erstellt
    // Für diese Beispielimplementierung simulieren wir die Signatur
    const signer = crypto.createSign('RSA-SHA256');
    signer.update(hash);
    
    try {
      // Versuche, eine echte Signatur zu erstellen, falls ein gültiger Schlüssel vorliegt
      return signer.sign(privateKey, 'hex');
    } catch (error) {
      console.warn('Simuliere Signatur, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Signatur
      return crypto
        .createHash('sha256')
        .update(hash + privateKey)
        .digest('hex');
    }
  }
  
  /**
   * Verifiziert eine Signatur mit einem öffentlichen Schlüssel
   * @private
   */
  verify(hash, signature, publicKey) {
    // In einer realen Implementierung würde hier eine echte Signaturverifizierung durchgeführt
    // Für diese Beispielimplementierung simulieren wir die Verifizierung
    try {
      // Versuche, eine echte Verifizierung durchzuführen, falls ein gültiger Schlüssel vorliegt
      const verifier = crypto.createVerify('RSA-SHA256');
      verifier.update(hash);
      return verifier.verify(publicKey, signature, 'hex');
    } catch (error) {
      console.warn('Simuliere Verifizierung, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Verifizierung
      const simulatedSignature = crypto
        .createHash('sha256')
        .update(hash + publicKey)
        .digest('hex');
      
      return simulatedSignature === signature;
    }
  }
}

export default ZkVerifier;/**
 * Zero-Knowledge-Proof-Verifizierer für wissenschaftliche Publikationen
 * Ermöglicht die Verifizierung von Autorschaft und Datenintegrität ohne sensible Daten preiszugeben
 */

import crypto from 'crypto';

class ZkVerifier {
  constructor(options = {}) {
    this.options = {
      hashAlgorithm: 'sha256',
      ...options
    };
  }
  
  /**
   * Generiert einen Beweis für die Autorschaft einer Publikation
   * @param {Object} publication - Publikationsdaten
   * @param {Object} author - Autorschaftsdaten
   * @param {string} privateKey - Privater Schlüssel des Autors
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateAuthorshipProof(publication, author, privateKey) {
    try {
      // 1. Erstelle einen Hash der Publikation
      const publicationHash = this.hashPublication(publication);
      
      // 2. Erstelle einen Hash der Autorschaftsdaten
      const authorshipHash = this.hashAuthorship(publication.doi, author);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(publicationHash, authorshipHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        publicationHash,
        authorshipClaim: {
          authorId: author.id,
          authorName: author.name,
          authorRole: author.role || 'author',
          authorshipHash
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Autorschaftsbeweises:', error);
      throw new Error(`Autorschaftsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Autorschaftsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} publication - Publikationsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel des Autors
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyAuthorshipProof(proof, publication, publicKey) {
    try {
      // 1. Berechne den Publikationshash
      const publicationHash = this.hashPublication(publication);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (publicationHash !== proof.publicationHash) {
        console.warn('Publikationshash stimmt nicht überein');
        return false;
      }
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(
        proof.publicationHash,
        proof.authorshipClaim.authorshipHash
      );
      
      // 4. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Autorschaftsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Integrität von Forschungsdaten
   * @param {Object} data - Forschungsdaten
   * @param {string} privateKey - Privater Schlüssel
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateDataIntegrityProof(data, privateKey) {
    try {
      // 1. Erstelle einen Hash der Daten
      const dataHash = this.hashData(data);
      
      // 2. Erstelle Metadaten über die Daten (ohne die eigentlichen Daten)
      const metadata = this.extractDataMetadata(data);
      
      // 3. Signiere den Hash mit dem privaten Schlüssel
      const signature = this.sign(dataHash, privateKey);
      
      // 4. Erstelle den Beweis
      return {
        dataHash,
        metadata,
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Datenintegritätsbeweises:', error);
      throw new Error(`Datenintegritätsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Datenintegritätsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} data - Forschungsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyDataIntegrityProof(proof, data, publicKey) {
    try {
      // 1. Berechne den Datenhash
      const dataHash = this.hashData(data);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (dataHash !== proof.dataHash) {
        console.warn('Datenhash stimmt nicht überein');
        return false;
      }
      
      // 3. Verifiziere die Signatur
      const isSignatureValid = this.verify(dataHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Datenintegritätsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Peer-Review einer Publikation
   * @param {Object} review - Review-Daten
   * @param {string} publicationDoi - DOI der Publikation
   * @param {string} privateKey - Privater Schlüssel des Reviewers
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generatePeerReviewProof(review, publicationDoi, privateKey) {
    try {
      // 1. Erstelle einen Hash des Reviews
      const reviewHash = this.hashReview(review);
      
      // 2. Erstelle einen Hash der Publikations-DOI
      const publicationHash = this.hashString(publicationDoi);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        reviewHash,
        publicationDoi,
        reviewerInfo: {
          id: review.reviewerId,
          name: review.reviewerName,
          institution: review.reviewerInstitution
        },
        reviewMetadata: {
          date: review.date,
          recommendation: review.recommendation,
          confidence: review.confidence
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Peer-Review-Beweises:', error);
      throw new Error(`Peer-Review-Beweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Peer-Review-Beweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} review - Review-Daten
   * @param {string} publicKey - Öffentlicher Schlüssel des Reviewers
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyPeerReviewProof(proof, review, publicKey) {
    try {
      // 1. Berechne den Review-Hash
      const reviewHash = this.hashReview(review);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (reviewHash !== proof.reviewHash) {
        console.warn('Review-Hash stimmt nicht überein');
        return false;
      }
      
      // 3. Berechne den Publikations-Hash
      const publicationHash = this.hashString(proof.publicationDoi);
      
      // 4. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 5. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Peer-Review-Beweises:', error);
      return false;
    }
  }
  
  /**
   * Hasht eine Publikation
   * @private
   */
  hashPublication(publication) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      doi: publication.doi,
      title: publication.title,
      authors: publication.authors.map(author => ({
        name: author.name,
        orcid: author.orcid
      })),
      abstract: publication.abstract,
      publicationYear: publication.publicationYear
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht Autorschaftsdaten
   * @private
   */
  hashAuthorship(doi, author) {
    const authorshipData = {
      doi,
      authorId: author.id,
      authorName: author.name,
      authorRole: author.role || 'author'
    };
    
    return this.hashObject(authorshipData);
  }
  
  /**
   * Hasht Forschungsdaten
   * @private
   */
  hashData(data) {
    return this.hashObject(data);
  }
  
  /**
   * Extrahiert Metadaten aus Forschungsdaten
   * @private
   */
  extractDataMetadata(data) {
    // Extrahiere Metadaten ohne die eigentlichen Daten
    const metadata = {
      dataType: typeof data,
      isArray: Array.isArray(data),
      length: Array.isArray(data) ? data.length : undefined,
      keys: typeof data === 'object' ? Object.keys(data) : undefined,
      timestamp: Date.now()
    };
    
    return metadata;
  }
  
  /**
   * Hasht Review-Daten
   * @private
   */
  hashReview(review) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      reviewerId: review.reviewerId,
      publicationDoi: review.publicationDoi,
      date: review.date,
      recommendation: review.recommendation,
      confidence: review.confidence,
      comments: review.comments
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht ein Objekt
   * @private
   */
  hashObject(obj) {
    const jsonString = JSON.stringify(obj);
    return this.hashString(jsonString);
  }
  
  /**
   * Hasht einen String
   * @private
   */
  hashString(str) {
    return crypto
      .createHash(this.options.hashAlgorithm)
      .update(str)
      .digest('hex');
  }
  
  /**
   * Kombiniert zwei Hashes
   * @private
   */
  combineHashes(hash1, hash2) {
    return this.hashString(hash1 + hash2);
  }
  
  /**
   * Signiert einen Hash mit einem privaten Schlüssel
   * @private
   */
  sign(hash, privateKey) {
    // In einer realen Implementierung würde hier eine echte Signatur erstellt
    // Für diese Beispielimplementierung simulieren wir die Signatur
    const signer = crypto.createSign('RSA-SHA256');
    signer.update(hash);
    
    try {
      // Versuche, eine echte Signatur zu erstellen, falls ein gültiger Schlüssel vorliegt
      return signer.sign(privateKey, 'hex');
    } catch (error) {
      console.warn('Simuliere Signatur, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Signatur
      return crypto
        .createHash('sha256')
        .update(hash + privateKey)
        .digest('hex');
    }
  }
  
  /**
   * Verifiziert eine Signatur mit einem öffentlichen Schlüssel
   * @private
   */
  verify(hash, signature, publicKey) {
    // In einer realen Implementierung würde hier eine echte Signaturverifizierung durchgeführt
    // Für diese Beispielimplementierung simulieren wir die Verifizierung
    try {
      // Versuche, eine echte Verifizierung durchzuführen, falls ein gültiger Schlüssel vorliegt
      const verifier = crypto.createVerify('RSA-SHA256');
      verifier.update(hash);
      return verifier.verify(publicKey, signature, 'hex');
    } catch (error) {
      console.warn('Simuliere Verifizierung, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Verifizierung
      const simulatedSignature = crypto
        .createHash('sha256')
        .update(hash + publicKey)
        .digest('hex');
      
      return simulatedSignature === signature;
    }
  }
}

export default ZkVerifier;/**
 * Zero-Knowledge-Proof-Verifizierer für wissenschaftliche Publikationen
 * Ermöglicht die Verifizierung von Autorschaft und Datenintegrität ohne sensible Daten preiszugeben
 */

import crypto from 'crypto';

class ZkVerifier {
  constructor(options = {}) {
    this.options = {
      hashAlgorithm: 'sha256',
      ...options
    };
  }
  
  /**
   * Generiert einen Beweis für die Autorschaft einer Publikation
   * @param {Object} publication - Publikationsdaten
   * @param {Object} author - Autorschaftsdaten
   * @param {string} privateKey - Privater Schlüssel des Autors
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateAuthorshipProof(publication, author, privateKey) {
    try {
      // 1. Erstelle einen Hash der Publikation
      const publicationHash = this.hashPublication(publication);
      
      // 2. Erstelle einen Hash der Autorschaftsdaten
      const authorshipHash = this.hashAuthorship(publication.doi, author);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(publicationHash, authorshipHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        publicationHash,
        authorshipClaim: {
          authorId: author.id,
          authorName: author.name,
          authorRole: author.role || 'author',
          authorshipHash
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Autorschaftsbeweises:', error);
      throw new Error(`Autorschaftsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Autorschaftsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} publication - Publikationsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel des Autors
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyAuthorshipProof(proof, publication, publicKey) {
    try {
      // 1. Berechne den Publikationshash
      const publicationHash = this.hashPublication(publication);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (publicationHash !== proof.publicationHash) {
        console.warn('Publikationshash stimmt nicht überein');
        return false;
      }
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(
        proof.publicationHash,
        proof.authorshipClaim.authorshipHash
      );
      
      // 4. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Autorschaftsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Integrität von Forschungsdaten
   * @param {Object} data - Forschungsdaten
   * @param {string} privateKey - Privater Schlüssel
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateDataIntegrityProof(data, privateKey) {
    try {
      // 1. Erstelle einen Hash der Daten
      const dataHash = this.hashData(data);
      
      // 2. Erstelle Metadaten über die Daten (ohne die eigentlichen Daten)
      const metadata = this.extractDataMetadata(data);
      
      // 3. Signiere den Hash mit dem privaten Schlüssel
      const signature = this.sign(dataHash, privateKey);
      
      // 4. Erstelle den Beweis
      return {
        dataHash,
        metadata,
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Datenintegritätsbeweises:', error);
      throw new Error(`Datenintegritätsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Datenintegritätsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} data - Forschungsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyDataIntegrityProof(proof, data, publicKey) {
    try {
      // 1. Berechne den Datenhash
      const dataHash = this.hashData(data);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (dataHash !== proof.dataHash) {
        console.warn('Datenhash stimmt nicht überein');
        return false;
      }
      
      // 3. Verifiziere die Signatur
      const isSignatureValid = this.verify(dataHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Datenintegritätsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Peer-Review einer Publikation
   * @param {Object} review - Review-Daten
   * @param {string} publicationDoi - DOI der Publikation
   * @param {string} privateKey - Privater Schlüssel des Reviewers
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generatePeerReviewProof(review, publicationDoi, privateKey) {
    try {
      // 1. Erstelle einen Hash des Reviews
      const reviewHash = this.hashReview(review);
      
      // 2. Erstelle einen Hash der Publikations-DOI
      const publicationHash = this.hashString(publicationDoi);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        reviewHash,
        publicationDoi,
        reviewerInfo: {
          id: review.reviewerId,
          name: review.reviewerName,
          institution: review.reviewerInstitution
        },
        reviewMetadata: {
          date: review.date,
          recommendation: review.recommendation,
          confidence: review.confidence
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Peer-Review-Beweises:', error);
      throw new Error(`Peer-Review-Beweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Peer-Review-Beweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} review - Review-Daten
   * @param {string} publicKey - Öffentlicher Schlüssel des Reviewers
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyPeerReviewProof(proof, review, publicKey) {
    try {
      // 1. Berechne den Review-Hash
      const reviewHash = this.hashReview(review);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (reviewHash !== proof.reviewHash) {
        console.warn('Review-Hash stimmt nicht überein');
        return false;
      }
      
      // 3. Berechne den Publikations-Hash
      const publicationHash = this.hashString(proof.publicationDoi);
      
      // 4. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 5. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Peer-Review-Beweises:', error);
      return false;
    }
  }
  
  /**
   * Hasht eine Publikation
   * @private
   */
  hashPublication(publication) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      doi: publication.doi,
      title: publication.title,
      authors: publication.authors.map(author => ({
        name: author.name,
        orcid: author.orcid
      })),
      abstract: publication.abstract,
      publicationYear: publication.publicationYear
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht Autorschaftsdaten
   * @private
   */
  hashAuthorship(doi, author) {
    const authorshipData = {
      doi,
      authorId: author.id,
      authorName: author.name,
      authorRole: author.role || 'author'
    };
    
    return this.hashObject(authorshipData);
  }
  
  /**
   * Hasht Forschungsdaten
   * @private
   */
  hashData(data) {
    return this.hashObject(data);
  }
  
  /**
   * Extrahiert Metadaten aus Forschungsdaten
   * @private
   */
  extractDataMetadata(data) {
    // Extrahiere Metadaten ohne die eigentlichen Daten
    const metadata = {
      dataType: typeof data,
      isArray: Array.isArray(data),
      length: Array.isArray(data) ? data.length : undefined,
      keys: typeof data === 'object' ? Object.keys(data) : undefined,
      timestamp: Date.now()
    };
    
    return metadata;
  }
  
  /**
   * Hasht Review-Daten
   * @private
   */
  hashReview(review) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      reviewerId: review.reviewerId,
      publicationDoi: review.publicationDoi,
      date: review.date,
      recommendation: review.recommendation,
      confidence: review.confidence,
      comments: review.comments
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht ein Objekt
   * @private
   */
  hashObject(obj) {
    const jsonString = JSON.stringify(obj);
    return this.hashString(jsonString);
  }
  
  /**
   * Hasht einen String
   * @private
   */
  hashString(str) {
    return crypto
      .createHash(this.options.hashAlgorithm)
      .update(str)
      .digest('hex');
  }
  
  /**
   * Kombiniert zwei Hashes
   * @private
   */
  combineHashes(hash1, hash2) {
    return this.hashString(hash1 + hash2);
  }
  
  /**
   * Signiert einen Hash mit einem privaten Schlüssel
   * @private
   */
  sign(hash, privateKey) {
    // In einer realen Implementierung würde hier eine echte Signatur erstellt
    // Für diese Beispielimplementierung simulieren wir die Signatur
    const signer = crypto.createSign('RSA-SHA256');
    signer.update(hash);
    
    try {
      // Versuche, eine echte Signatur zu erstellen, falls ein gültiger Schlüssel vorliegt
      return signer.sign(privateKey, 'hex');
    } catch (error) {
      console.warn('Simuliere Signatur, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Signatur
      return crypto
        .createHash('sha256')
        .update(hash + privateKey)
        .digest('hex');
    }
  }
  
  /**
   * Verifiziert eine Signatur mit einem öffentlichen Schlüssel
   * @private
   */
  verify(hash, signature, publicKey) {
    // In einer realen Implementierung würde hier eine echte Signaturverifizierung durchgeführt
    // Für diese Beispielimplementierung simulieren wir die Verifizierung
    try {
      // Versuche, eine echte Verifizierung durchzuführen, falls ein gültiger Schlüssel vorliegt
      const verifier = crypto.createVerify('RSA-SHA256');
      verifier.update(hash);
      return verifier.verify(publicKey, signature, 'hex');
    } catch (error) {
      console.warn('Simuliere Verifizierung, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Verifizierung
      const simulatedSignature = crypto
        .createHash('sha256')
        .update(hash + publicKey)
        .digest('hex');
      
      return simulatedSignature === signature;
    }
  }
}

export default ZkVerifier;/**
 * Zero-Knowledge-Proof-Verifizierer für wissenschaftliche Publikationen
 * Ermöglicht die Verifizierung von Autorschaft und Datenintegrität ohne sensible Daten preiszugeben
 */

import crypto from 'crypto';

class ZkVerifier {
  constructor(options = {}) {
    this.options = {
      hashAlgorithm: 'sha256',
      ...options
    };
  }
  
  /**
   * Generiert einen Beweis für die Autorschaft einer Publikation
   * @param {Object} publication - Publikationsdaten
   * @param {Object} author - Autorschaftsdaten
   * @param {string} privateKey - Privater Schlüssel des Autors
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateAuthorshipProof(publication, author, privateKey) {
    try {
      // 1. Erstelle einen Hash der Publikation
      const publicationHash = this.hashPublication(publication);
      
      // 2. Erstelle einen Hash der Autorschaftsdaten
      const authorshipHash = this.hashAuthorship(publication.doi, author);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(publicationHash, authorshipHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        publicationHash,
        authorshipClaim: {
          authorId: author.id,
          authorName: author.name,
          authorRole: author.role || 'author',
          authorshipHash
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Autorschaftsbeweises:', error);
      throw new Error(`Autorschaftsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Autorschaftsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} publication - Publikationsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel des Autors
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyAuthorshipProof(proof, publication, publicKey) {
    try {
      // 1. Berechne den Publikationshash
      const publicationHash = this.hashPublication(publication);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (publicationHash !== proof.publicationHash) {
        console.warn('Publikationshash stimmt nicht überein');
        return false;
      }
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(
        proof.publicationHash,
        proof.authorshipClaim.authorshipHash
      );
      
      // 4. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Autorschaftsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Integrität von Forschungsdaten
   * @param {Object} data - Forschungsdaten
   * @param {string} privateKey - Privater Schlüssel
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateDataIntegrityProof(data, privateKey) {
    try {
      // 1. Erstelle einen Hash der Daten
      const dataHash = this.hashData(data);
      
      // 2. Erstelle Metadaten über die Daten (ohne die eigentlichen Daten)
      const metadata = this.extractDataMetadata(data);
      
      // 3. Signiere den Hash mit dem privaten Schlüssel
      const signature = this.sign(dataHash, privateKey);
      
      // 4. Erstelle den Beweis
      return {
        dataHash,
        metadata,
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Datenintegritätsbeweises:', error);
      throw new Error(`Datenintegritätsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Datenintegritätsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} data - Forschungsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyDataIntegrityProof(proof, data, publicKey) {
    try {
      // 1. Berechne den Datenhash
      const dataHash = this.hashData(data);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (dataHash !== proof.dataHash) {
        console.warn('Datenhash stimmt nicht überein');
        return false;
      }
      
      // 3. Verifiziere die Signatur
      const isSignatureValid = this.verify(dataHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Datenintegritätsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Peer-Review einer Publikation
   * @param {Object} review - Review-Daten
   * @param {string} publicationDoi - DOI der Publikation
   * @param {string} privateKey - Privater Schlüssel des Reviewers
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generatePeerReviewProof(review, publicationDoi, privateKey) {
    try {
      // 1. Erstelle einen Hash des Reviews
      const reviewHash = this.hashReview(review);
      
      // 2. Erstelle einen Hash der Publikations-DOI
      const publicationHash = this.hashString(publicationDoi);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        reviewHash,
        publicationDoi,
        reviewerInfo: {
          id: review.reviewerId,
          name: review.reviewerName,
          institution: review.reviewerInstitution
        },
        reviewMetadata: {
          date: review.date,
          recommendation: review.recommendation,
          confidence: review.confidence
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Peer-Review-Beweises:', error);
      throw new Error(`Peer-Review-Beweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Peer-Review-Beweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} review - Review-Daten
   * @param {string} publicKey - Öffentlicher Schlüssel des Reviewers
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyPeerReviewProof(proof, review, publicKey) {
    try {
      // 1. Berechne den Review-Hash
      const reviewHash = this.hashReview(review);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (reviewHash !== proof.reviewHash) {
        console.warn('Review-Hash stimmt nicht überein');
        return false;
      }
      
      // 3. Berechne den Publikations-Hash
      const publicationHash = this.hashString(proof.publicationDoi);
      
      // 4. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 5. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Peer-Review-Beweises:', error);
      return false;
    }
  }
  
  /**
   * Hasht eine Publikation
   * @private
   */
  hashPublication(publication) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      doi: publication.doi,
      title: publication.title,
      authors: publication.authors.map(author => ({
        name: author.name,
        orcid: author.orcid
      })),
      abstract: publication.abstract,
      publicationYear: publication.publicationYear
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht Autorschaftsdaten
   * @private
   */
  hashAuthorship(doi, author) {
    const authorshipData = {
      doi,
      authorId: author.id,
      authorName: author.name,
      authorRole: author.role || 'author'
    };
    
    return this.hashObject(authorshipData);
  }
  
  /**
   * Hasht Forschungsdaten
   * @private
   */
  hashData(data) {
    return this.hashObject(data);
  }
  
  /**
   * Extrahiert Metadaten aus Forschungsdaten
   * @private
   */
  extractDataMetadata(data) {
    // Extrahiere Metadaten ohne die eigentlichen Daten
    const metadata = {
      dataType: typeof data,
      isArray: Array.isArray(data),
      length: Array.isArray(data) ? data.length : undefined,
      keys: typeof data === 'object' ? Object.keys(data) : undefined,
      timestamp: Date.now()
    };
    
    return metadata;
  }
  
  /**
   * Hasht Review-Daten
   * @private
   */
  hashReview(review) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      reviewerId: review.reviewerId,
      publicationDoi: review.publicationDoi,
      date: review.date,
      recommendation: review.recommendation,
      confidence: review.confidence,
      comments: review.comments
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht ein Objekt
   * @private
   */
  hashObject(obj) {
    const jsonString = JSON.stringify(obj);
    return this.hashString(jsonString);
  }
  
  /**
   * Hasht einen String
   * @private
   */
  hashString(str) {
    return crypto
      .createHash(this.options.hashAlgorithm)
      .update(str)
      .digest('hex');
  }
  
  /**
   * Kombiniert zwei Hashes
   * @private
   */
  combineHashes(hash1, hash2) {
    return this.hashString(hash1 + hash2);
  }
  
  /**
   * Signiert einen Hash mit einem privaten Schlüssel
   * @private
   */
  sign(hash, privateKey) {
    // In einer realen Implementierung würde hier eine echte Signatur erstellt
    // Für diese Beispielimplementierung simulieren wir die Signatur
    const signer = crypto.createSign('RSA-SHA256');
    signer.update(hash);
    
    try {
      // Versuche, eine echte Signatur zu erstellen, falls ein gültiger Schlüssel vorliegt
      return signer.sign(privateKey, 'hex');
    } catch (error) {
      console.warn('Simuliere Signatur, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Signatur
      return crypto
        .createHash('sha256')
        .update(hash + privateKey)
        .digest('hex');
    }
  }
  
  /**
   * Verifiziert eine Signatur mit einem öffentlichen Schlüssel
   * @private
   */
  verify(hash, signature, publicKey) {
    // In einer realen Implementierung würde hier eine echte Signaturverifizierung durchgeführt
    // Für diese Beispielimplementierung simulieren wir die Verifizierung
    try {
      // Versuche, eine echte Verifizierung durchzuführen, falls ein gültiger Schlüssel vorliegt
      const verifier = crypto.createVerify('RSA-SHA256');
      verifier.update(hash);
      return verifier.verify(publicKey, signature, 'hex');
    } catch (error) {
      console.warn('Simuliere Verifizierung, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Verifizierung
      const simulatedSignature = crypto
        .createHash('sha256')
        .update(hash + publicKey)
        .digest('hex');
      
      return simulatedSignature === signature;
    }
  }
}

export default ZkVerifier;/**
 * Zero-Knowledge-Proof-Verifizierer für wissenschaftliche Publikationen
 * Ermöglicht die Verifizierung von Autorschaft und Datenintegrität ohne sensible Daten preiszugeben
 */

import crypto from 'crypto';

class ZkVerifier {
  constructor(options = {}) {
    this.options = {
      hashAlgorithm: 'sha256',
      ...options
    };
  }
  
  /**
   * Generiert einen Beweis für die Autorschaft einer Publikation
   * @param {Object} publication - Publikationsdaten
   * @param {Object} author - Autorschaftsdaten
   * @param {string} privateKey - Privater Schlüssel des Autors
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateAuthorshipProof(publication, author, privateKey) {
    try {
      // 1. Erstelle einen Hash der Publikation
      const publicationHash = this.hashPublication(publication);
      
      // 2. Erstelle einen Hash der Autorschaftsdaten
      const authorshipHash = this.hashAuthorship(publication.doi, author);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(publicationHash, authorshipHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        publicationHash,
        authorshipClaim: {
          authorId: author.id,
          authorName: author.name,
          authorRole: author.role || 'author',
          authorshipHash
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Autorschaftsbeweises:', error);
      throw new Error(`Autorschaftsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Autorschaftsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} publication - Publikationsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel des Autors
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyAuthorshipProof(proof, publication, publicKey) {
    try {
      // 1. Berechne den Publikationshash
      const publicationHash = this.hashPublication(publication);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (publicationHash !== proof.publicationHash) {
        console.warn('Publikationshash stimmt nicht überein');
        return false;
      }
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(
        proof.publicationHash,
        proof.authorshipClaim.authorshipHash
      );
      
      // 4. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Autorschaftsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Integrität von Forschungsdaten
   * @param {Object} data - Forschungsdaten
   * @param {string} privateKey - Privater Schlüssel
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateDataIntegrityProof(data, privateKey) {
    try {
      // 1. Erstelle einen Hash der Daten
      const dataHash = this.hashData(data);
      
      // 2. Erstelle Metadaten über die Daten (ohne die eigentlichen Daten)
      const metadata = this.extractDataMetadata(data);
      
      // 3. Signiere den Hash mit dem privaten Schlüssel
      const signature = this.sign(dataHash, privateKey);
      
      // 4. Erstelle den Beweis
      return {
        dataHash,
        metadata,
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Datenintegritätsbeweises:', error);
      throw new Error(`Datenintegritätsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Datenintegritätsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} data - Forschungsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyDataIntegrityProof(proof, data, publicKey) {
    try {
      // 1. Berechne den Datenhash
      const dataHash = this.hashData(data);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (dataHash !== proof.dataHash) {
        console.warn('Datenhash stimmt nicht überein');
        return false;
      }
      
      // 3. Verifiziere die Signatur
      const isSignatureValid = this.verify(dataHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Datenintegritätsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Peer-Review einer Publikation
   * @param {Object} review - Review-Daten
   * @param {string} publicationDoi - DOI der Publikation
   * @param {string} privateKey - Privater Schlüssel des Reviewers
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generatePeerReviewProof(review, publicationDoi, privateKey) {
    try {
      // 1. Erstelle einen Hash des Reviews
      const reviewHash = this.hashReview(review);
      
      // 2. Erstelle einen Hash der Publikations-DOI
      const publicationHash = this.hashString(publicationDoi);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        reviewHash,
        publicationDoi,
        reviewerInfo: {
          id: review.reviewerId,
          name: review.reviewerName,
          institution: review.reviewerInstitution
        },
        reviewMetadata: {
          date: review.date,
          recommendation: review.recommendation,
          confidence: review.confidence
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Peer-Review-Beweises:', error);
      throw new Error(`Peer-Review-Beweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Peer-Review-Beweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} review - Review-Daten
   * @param {string} publicKey - Öffentlicher Schlüssel des Reviewers
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyPeerReviewProof(proof, review, publicKey) {
    try {
      // 1. Berechne den Review-Hash
      const reviewHash = this.hashReview(review);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (reviewHash !== proof.reviewHash) {
        console.warn('Review-Hash stimmt nicht überein');
        return false;
      }
      
      // 3. Berechne den Publikations-Hash
      const publicationHash = this.hashString(proof.publicationDoi);
      
      // 4. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 5. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Peer-Review-Beweises:', error);
      return false;
    }
  }
  
  /**
   * Hasht eine Publikation
   * @private
   */
  hashPublication(publication) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      doi: publication.doi,
      title: publication.title,
      authors: publication.authors.map(author => ({
        name: author.name,
        orcid: author.orcid
      })),
      abstract: publication.abstract,
      publicationYear: publication.publicationYear
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht Autorschaftsdaten
   * @private
   */
  hashAuthorship(doi, author) {
    const authorshipData = {
      doi,
      authorId: author.id,
      authorName: author.name,
      authorRole: author.role || 'author'
    };
    
    return this.hashObject(authorshipData);
  }
  
  /**
   * Hasht Forschungsdaten
   * @private
   */
  hashData(data) {
    return this.hashObject(data);
  }
  
  /**
   * Extrahiert Metadaten aus Forschungsdaten
   * @private
   */
  extractDataMetadata(data) {
    // Extrahiere Metadaten ohne die eigentlichen Daten
    const metadata = {
      dataType: typeof data,
      isArray: Array.isArray(data),
      length: Array.isArray(data) ? data.length : undefined,
      keys: typeof data === 'object' ? Object.keys(data) : undefined,
      timestamp: Date.now()
    };
    
    return metadata;
  }
  
  /**
   * Hasht Review-Daten
   * @private
   */
  hashReview(review) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      reviewerId: review.reviewerId,
      publicationDoi: review.publicationDoi,
      date: review.date,
      recommendation: review.recommendation,
      confidence: review.confidence,
      comments: review.comments
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht ein Objekt
   * @private
   */
  hashObject(obj) {
    const jsonString = JSON.stringify(obj);
    return this.hashString(jsonString);
  }
  
  /**
   * Hasht einen String
   * @private
   */
  hashString(str) {
    return crypto
      .createHash(this.options.hashAlgorithm)
      .update(str)
      .digest('hex');
  }
  
  /**
   * Kombiniert zwei Hashes
   * @private
   */
  combineHashes(hash1, hash2) {
    return this.hashString(hash1 + hash2);
  }
  
  /**
   * Signiert einen Hash mit einem privaten Schlüssel
   * @private
   */
  sign(hash, privateKey) {
    // In einer realen Implementierung würde hier eine echte Signatur erstellt
    // Für diese Beispielimplementierung simulieren wir die Signatur
    const signer = crypto.createSign('RSA-SHA256');
    signer.update(hash);
    
    try {
      // Versuche, eine echte Signatur zu erstellen, falls ein gültiger Schlüssel vorliegt
      return signer.sign(privateKey, 'hex');
    } catch (error) {
      console.warn('Simuliere Signatur, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Signatur
      return crypto
        .createHash('sha256')
        .update(hash + privateKey)
        .digest('hex');
    }
  }
  
  /**
   * Verifiziert eine Signatur mit einem öffentlichen Schlüssel
   * @private
   */
  verify(hash, signature, publicKey) {
    // In einer realen Implementierung würde hier eine echte Signaturverifizierung durchgeführt
    // Für diese Beispielimplementierung simulieren wir die Verifizierung
    try {
      // Versuche, eine echte Verifizierung durchzuführen, falls ein gültiger Schlüssel vorliegt
      const verifier = crypto.createVerify('RSA-SHA256');
      verifier.update(hash);
      return verifier.verify(publicKey, signature, 'hex');
    } catch (error) {
      console.warn('Simuliere Verifizierung, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Verifizierung
      const simulatedSignature = crypto
        .createHash('sha256')
        .update(hash + publicKey)
        .digest('hex');
      
      return simulatedSignature === signature;
    }
  }
}

export default ZkVerifier;/**
 * Zero-Knowledge-Proof-Verifizierer für wissenschaftliche Publikationen
 * Ermöglicht die Verifizierung von Autorschaft und Datenintegrität ohne sensible Daten preiszugeben
 */

import crypto from 'crypto';

class ZkVerifier {
  constructor(options = {}) {
    this.options = {
      hashAlgorithm: 'sha256',
      ...options
    };
  }
  
  /**
   * Generiert einen Beweis für die Autorschaft einer Publikation
   * @param {Object} publication - Publikationsdaten
   * @param {Object} author - Autorschaftsdaten
   * @param {string} privateKey - Privater Schlüssel des Autors
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateAuthorshipProof(publication, author, privateKey) {
    try {
      // 1. Erstelle einen Hash der Publikation
      const publicationHash = this.hashPublication(publication);
      
      // 2. Erstelle einen Hash der Autorschaftsdaten
      const authorshipHash = this.hashAuthorship(publication.doi, author);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(publicationHash, authorshipHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        publicationHash,
        authorshipClaim: {
          authorId: author.id,
          authorName: author.name,
          authorRole: author.role || 'author',
          authorshipHash
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Autorschaftsbeweises:', error);
      throw new Error(`Autorschaftsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Autorschaftsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} publication - Publikationsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel des Autors
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyAuthorshipProof(proof, publication, publicKey) {
    try {
      // 1. Berechne den Publikationshash
      const publicationHash = this.hashPublication(publication);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (publicationHash !== proof.publicationHash) {
        console.warn('Publikationshash stimmt nicht überein');
        return false;
      }
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(
        proof.publicationHash,
        proof.authorshipClaim.authorshipHash
      );
      
      // 4. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Autorschaftsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Integrität von Forschungsdaten
   * @param {Object} data - Forschungsdaten
   * @param {string} privateKey - Privater Schlüssel
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateDataIntegrityProof(data, privateKey) {
    try {
      // 1. Erstelle einen Hash der Daten
      const dataHash = this.hashData(data);
      
      // 2. Erstelle Metadaten über die Daten (ohne die eigentlichen Daten)
      const metadata = this.extractDataMetadata(data);
      
      // 3. Signiere den Hash mit dem privaten Schlüssel
      const signature = this.sign(dataHash, privateKey);
      
      // 4. Erstelle den Beweis
      return {
        dataHash,
        metadata,
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Datenintegritätsbeweises:', error);
      throw new Error(`Datenintegritätsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Datenintegritätsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} data - Forschungsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyDataIntegrityProof(proof, data, publicKey) {
    try {
      // 1. Berechne den Datenhash
      const dataHash = this.hashData(data);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (dataHash !== proof.dataHash) {
        console.warn('Datenhash stimmt nicht überein');
        return false;
      }
      
      // 3. Verifiziere die Signatur
      const isSignatureValid = this.verify(dataHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Datenintegritätsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Peer-Review einer Publikation
   * @param {Object} review - Review-Daten
   * @param {string} publicationDoi - DOI der Publikation
   * @param {string} privateKey - Privater Schlüssel des Reviewers
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generatePeerReviewProof(review, publicationDoi, privateKey) {
    try {
      // 1. Erstelle einen Hash des Reviews
      const reviewHash = this.hashReview(review);
      
      // 2. Erstelle einen Hash der Publikations-DOI
      const publicationHash = this.hashString(publicationDoi);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        reviewHash,
        publicationDoi,
        reviewerInfo: {
          id: review.reviewerId,
          name: review.reviewerName,
          institution: review.reviewerInstitution
        },
        reviewMetadata: {
          date: review.date,
          recommendation: review.recommendation,
          confidence: review.confidence
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Peer-Review-Beweises:', error);
      throw new Error(`Peer-Review-Beweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Peer-Review-Beweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} review - Review-Daten
   * @param {string} publicKey - Öffentlicher Schlüssel des Reviewers
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyPeerReviewProof(proof, review, publicKey) {
    try {
      // 1. Berechne den Review-Hash
      const reviewHash = this.hashReview(review);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (reviewHash !== proof.reviewHash) {
        console.warn('Review-Hash stimmt nicht überein');
        return false;
      }
      
      // 3. Berechne den Publikations-Hash
      const publicationHash = this.hashString(proof.publicationDoi);
      
      // 4. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 5. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Peer-Review-Beweises:', error);
      return false;
    }
  }
  
  /**
   * Hasht eine Publikation
   * @private
   */
  hashPublication(publication) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      doi: publication.doi,
      title: publication.title,
      authors: publication.authors.map(author => ({
        name: author.name,
        orcid: author.orcid
      })),
      abstract: publication.abstract,
      publicationYear: publication.publicationYear
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht Autorschaftsdaten
   * @private
   */
  hashAuthorship(doi, author) {
    const authorshipData = {
      doi,
      authorId: author.id,
      authorName: author.name,
      authorRole: author.role || 'author'
    };
    
    return this.hashObject(authorshipData);
  }
  
  /**
   * Hasht Forschungsdaten
   * @private
   */
  hashData(data) {
    return this.hashObject(data);
  }
  
  /**
   * Extrahiert Metadaten aus Forschungsdaten
   * @private
   */
  extractDataMetadata(data) {
    // Extrahiere Metadaten ohne die eigentlichen Daten
    const metadata = {
      dataType: typeof data,
      isArray: Array.isArray(data),
      length: Array.isArray(data) ? data.length : undefined,
      keys: typeof data === 'object' ? Object.keys(data) : undefined,
      timestamp: Date.now()
    };
    
    return metadata;
  }
  
  /**
   * Hasht Review-Daten
   * @private
   */
  hashReview(review) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      reviewerId: review.reviewerId,
      publicationDoi: review.publicationDoi,
      date: review.date,
      recommendation: review.recommendation,
      confidence: review.confidence,
      comments: review.comments
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht ein Objekt
   * @private
   */
  hashObject(obj) {
    const jsonString = JSON.stringify(obj);
    return this.hashString(jsonString);
  }
  
  /**
   * Hasht einen String
   * @private
   */
  hashString(str) {
    return crypto
      .createHash(this.options.hashAlgorithm)
      .update(str)
      .digest('hex');
  }
  
  /**
   * Kombiniert zwei Hashes
   * @private
   */
  combineHashes(hash1, hash2) {
    return this.hashString(hash1 + hash2);
  }
  
  /**
   * Signiert einen Hash mit einem privaten Schlüssel
   * @private
   */
  sign(hash, privateKey) {
    // In einer realen Implementierung würde hier eine echte Signatur erstellt
    // Für diese Beispielimplementierung simulieren wir die Signatur
    const signer = crypto.createSign('RSA-SHA256');
    signer.update(hash);
    
    try {
      // Versuche, eine echte Signatur zu erstellen, falls ein gültiger Schlüssel vorliegt
      return signer.sign(privateKey, 'hex');
    } catch (error) {
      console.warn('Simuliere Signatur, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Signatur
      return crypto
        .createHash('sha256')
        .update(hash + privateKey)
        .digest('hex');
    }
  }
  
  /**
   * Verifiziert eine Signatur mit einem öffentlichen Schlüssel
   * @private
   */
  verify(hash, signature, publicKey) {
    // In einer realen Implementierung würde hier eine echte Signaturverifizierung durchgeführt
    // Für diese Beispielimplementierung simulieren wir die Verifizierung
    try {
      // Versuche, eine echte Verifizierung durchzuführen, falls ein gültiger Schlüssel vorliegt
      const verifier = crypto.createVerify('RSA-SHA256');
      verifier.update(hash);
      return verifier.verify(publicKey, signature, 'hex');
    } catch (error) {
      console.warn('Simuliere Verifizierung, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Verifizierung
      const simulatedSignature = crypto
        .createHash('sha256')
        .update(hash + publicKey)
        .digest('hex');
      
      return simulatedSignature === signature;
    }
  }
}

export default ZkVerifier;/**
 * Zero-Knowledge-Proof-Verifizierer für wissenschaftliche Publikationen
 * Ermöglicht die Verifizierung von Autorschaft und Datenintegrität ohne sensible Daten preiszugeben
 */

import crypto from 'crypto';

class ZkVerifier {
  constructor(options = {}) {
    this.options = {
      hashAlgorithm: 'sha256',
      ...options
    };
  }
  
  /**
   * Generiert einen Beweis für die Autorschaft einer Publikation
   * @param {Object} publication - Publikationsdaten
   * @param {Object} author - Autorschaftsdaten
   * @param {string} privateKey - Privater Schlüssel des Autors
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateAuthorshipProof(publication, author, privateKey) {
    try {
      // 1. Erstelle einen Hash der Publikation
      const publicationHash = this.hashPublication(publication);
      
      // 2. Erstelle einen Hash der Autorschaftsdaten
      const authorshipHash = this.hashAuthorship(publication.doi, author);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(publicationHash, authorshipHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        publicationHash,
        authorshipClaim: {
          authorId: author.id,
          authorName: author.name,
          authorRole: author.role || 'author',
          authorshipHash
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Autorschaftsbeweises:', error);
      throw new Error(`Autorschaftsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Autorschaftsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} publication - Publikationsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel des Autors
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyAuthorshipProof(proof, publication, publicKey) {
    try {
      // 1. Berechne den Publikationshash
      const publicationHash = this.hashPublication(publication);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (publicationHash !== proof.publicationHash) {
        console.warn('Publikationshash stimmt nicht überein');
        return false;
      }
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(
        proof.publicationHash,
        proof.authorshipClaim.authorshipHash
      );
      
      // 4. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Autorschaftsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Integrität von Forschungsdaten
   * @param {Object} data - Forschungsdaten
   * @param {string} privateKey - Privater Schlüssel
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateDataIntegrityProof(data, privateKey) {
    try {
      // 1. Erstelle einen Hash der Daten
      const dataHash = this.hashData(data);
      
      // 2. Erstelle Metadaten über die Daten (ohne die eigentlichen Daten)
      const metadata = this.extractDataMetadata(data);
      
      // 3. Signiere den Hash mit dem privaten Schlüssel
      const signature = this.sign(dataHash, privateKey);
      
      // 4. Erstelle den Beweis
      return {
        dataHash,
        metadata,
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Datenintegritätsbeweises:', error);
      throw new Error(`Datenintegritätsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Datenintegritätsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} data - Forschungsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyDataIntegrityProof(proof, data, publicKey) {
    try {
      // 1. Berechne den Datenhash
      const dataHash = this.hashData(data);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (dataHash !== proof.dataHash) {
        console.warn('Datenhash stimmt nicht überein');
        return false;
      }
      
      // 3. Verifiziere die Signatur
      const isSignatureValid = this.verify(dataHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Datenintegritätsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Peer-Review einer Publikation
   * @param {Object} review - Review-Daten
   * @param {string} publicationDoi - DOI der Publikation
   * @param {string} privateKey - Privater Schlüssel des Reviewers
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generatePeerReviewProof(review, publicationDoi, privateKey) {
    try {
      // 1. Erstelle einen Hash des Reviews
      const reviewHash = this.hashReview(review);
      
      // 2. Erstelle einen Hash der Publikations-DOI
      const publicationHash = this.hashString(publicationDoi);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        reviewHash,
        publicationDoi,
        reviewerInfo: {
          id: review.reviewerId,
          name: review.reviewerName,
          institution: review.reviewerInstitution
        },
        reviewMetadata: {
          date: review.date,
          recommendation: review.recommendation,
          confidence: review.confidence
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Peer-Review-Beweises:', error);
      throw new Error(`Peer-Review-Beweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Peer-Review-Beweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} review - Review-Daten
   * @param {string} publicKey - Öffentlicher Schlüssel des Reviewers
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyPeerReviewProof(proof, review, publicKey) {
    try {
      // 1. Berechne den Review-Hash
      const reviewHash = this.hashReview(review);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (reviewHash !== proof.reviewHash) {
        console.warn('Review-Hash stimmt nicht überein');
        return false;
      }
      
      // 3. Berechne den Publikations-Hash
      const publicationHash = this.hashString(proof.publicationDoi);
      
      // 4. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 5. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Peer-Review-Beweises:', error);
      return false;
    }
  }
  
  /**
   * Hasht eine Publikation
   * @private
   */
  hashPublication(publication) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      doi: publication.doi,
      title: publication.title,
      authors: publication.authors.map(author => ({
        name: author.name,
        orcid: author.orcid
      })),
      abstract: publication.abstract,
      publicationYear: publication.publicationYear
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht Autorschaftsdaten
   * @private
   */
  hashAuthorship(doi, author) {
    const authorshipData = {
      doi,
      authorId: author.id,
      authorName: author.name,
      authorRole: author.role || 'author'
    };
    
    return this.hashObject(authorshipData);
  }
  
  /**
   * Hasht Forschungsdaten
   * @private
   */
  hashData(data) {
    return this.hashObject(data);
  }
  
  /**
   * Extrahiert Metadaten aus Forschungsdaten
   * @private
   */
  extractDataMetadata(data) {
    // Extrahiere Metadaten ohne die eigentlichen Daten
    const metadata = {
      dataType: typeof data,
      isArray: Array.isArray(data),
      length: Array.isArray(data) ? data.length : undefined,
      keys: typeof data === 'object' ? Object.keys(data) : undefined,
      timestamp: Date.now()
    };
    
    return metadata;
  }
  
  /**
   * Hasht Review-Daten
   * @private
   */
  hashReview(review) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      reviewerId: review.reviewerId,
      publicationDoi: review.publicationDoi,
      date: review.date,
      recommendation: review.recommendation,
      confidence: review.confidence,
      comments: review.comments
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht ein Objekt
   * @private
   */
  hashObject(obj) {
    const jsonString = JSON.stringify(obj);
    return this.hashString(jsonString);
  }
  
  /**
   * Hasht einen String
   * @private
   */
  hashString(str) {
    return crypto
      .createHash(this.options.hashAlgorithm)
      .update(str)
      .digest('hex');
  }
  
  /**
   * Kombiniert zwei Hashes
   * @private
   */
  combineHashes(hash1, hash2) {
    return this.hashString(hash1 + hash2);
  }
  
  /**
   * Signiert einen Hash mit einem privaten Schlüssel
   * @private
   */
  sign(hash, privateKey) {
    // In einer realen Implementierung würde hier eine echte Signatur erstellt
    // Für diese Beispielimplementierung simulieren wir die Signatur
    const signer = crypto.createSign('RSA-SHA256');
    signer.update(hash);
    
    try {
      // Versuche, eine echte Signatur zu erstellen, falls ein gültiger Schlüssel vorliegt
      return signer.sign(privateKey, 'hex');
    } catch (error) {
      console.warn('Simuliere Signatur, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Signatur
      return crypto
        .createHash('sha256')
        .update(hash + privateKey)
        .digest('hex');
    }
  }
  
  /**
   * Verifiziert eine Signatur mit einem öffentlichen Schlüssel
   * @private
   */
  verify(hash, signature, publicKey) {
    // In einer realen Implementierung würde hier eine echte Signaturverifizierung durchgeführt
    // Für diese Beispielimplementierung simulieren wir die Verifizierung
    try {
      // Versuche, eine echte Verifizierung durchzuführen, falls ein gültiger Schlüssel vorliegt
      const verifier = crypto.createVerify('RSA-SHA256');
      verifier.update(hash);
      return verifier.verify(publicKey, signature, 'hex');
    } catch (error) {
      console.warn('Simuliere Verifizierung, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Verifizierung
      const simulatedSignature = crypto
        .createHash('sha256')
        .update(hash + publicKey)
        .digest('hex');
      
      return simulatedSignature === signature;
    }
  }
}

export default ZkVerifier;/**
 * Zero-Knowledge-Proof-Verifizierer für wissenschaftliche Publikationen
 * Ermöglicht die Verifizierung von Autorschaft und Datenintegrität ohne sensible Daten preiszugeben
 */

import crypto from 'crypto';

class ZkVerifier {
  constructor(options = {}) {
    this.options = {
      hashAlgorithm: 'sha256',
      ...options
    };
  }
  
  /**
   * Generiert einen Beweis für die Autorschaft einer Publikation
   * @param {Object} publication - Publikationsdaten
   * @param {Object} author - Autorschaftsdaten
   * @param {string} privateKey - Privater Schlüssel des Autors
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateAuthorshipProof(publication, author, privateKey) {
    try {
      // 1. Erstelle einen Hash der Publikation
      const publicationHash = this.hashPublication(publication);
      
      // 2. Erstelle einen Hash der Autorschaftsdaten
      const authorshipHash = this.hashAuthorship(publication.doi, author);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(publicationHash, authorshipHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        publicationHash,
        authorshipClaim: {
          authorId: author.id,
          authorName: author.name,
          authorRole: author.role || 'author',
          authorshipHash
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Autorschaftsbeweises:', error);
      throw new Error(`Autorschaftsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Autorschaftsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} publication - Publikationsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel des Autors
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyAuthorshipProof(proof, publication, publicKey) {
    try {
      // 1. Berechne den Publikationshash
      const publicationHash = this.hashPublication(publication);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (publicationHash !== proof.publicationHash) {
        console.warn('Publikationshash stimmt nicht überein');
        return false;
      }
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(
        proof.publicationHash,
        proof.authorshipClaim.authorshipHash
      );
      
      // 4. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Autorschaftsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Integrität von Forschungsdaten
   * @param {Object} data - Forschungsdaten
   * @param {string} privateKey - Privater Schlüssel
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateDataIntegrityProof(data, privateKey) {
    try {
      // 1. Erstelle einen Hash der Daten
      const dataHash = this.hashData(data);
      
      // 2. Erstelle Metadaten über die Daten (ohne die eigentlichen Daten)
      const metadata = this.extractDataMetadata(data);
      
      // 3. Signiere den Hash mit dem privaten Schlüssel
      const signature = this.sign(dataHash, privateKey);
      
      // 4. Erstelle den Beweis
      return {
        dataHash,
        metadata,
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Datenintegritätsbeweises:', error);
      throw new Error(`Datenintegritätsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Datenintegritätsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} data - Forschungsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyDataIntegrityProof(proof, data, publicKey) {
    try {
      // 1. Berechne den Datenhash
      const dataHash = this.hashData(data);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (dataHash !== proof.dataHash) {
        console.warn('Datenhash stimmt nicht überein');
        return false;
      }
      
      // 3. Verifiziere die Signatur
      const isSignatureValid = this.verify(dataHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Datenintegritätsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Peer-Review einer Publikation
   * @param {Object} review - Review-Daten
   * @param {string} publicationDoi - DOI der Publikation
   * @param {string} privateKey - Privater Schlüssel des Reviewers
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generatePeerReviewProof(review, publicationDoi, privateKey) {
    try {
      // 1. Erstelle einen Hash des Reviews
      const reviewHash = this.hashReview(review);
      
      // 2. Erstelle einen Hash der Publikations-DOI
      const publicationHash = this.hashString(publicationDoi);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        reviewHash,
        publicationDoi,
        reviewerInfo: {
          id: review.reviewerId,
          name: review.reviewerName,
          institution: review.reviewerInstitution
        },
        reviewMetadata: {
          date: review.date,
          recommendation: review.recommendation,
          confidence: review.confidence
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Peer-Review-Beweises:', error);
      throw new Error(`Peer-Review-Beweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Peer-Review-Beweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} review - Review-Daten
   * @param {string} publicKey - Öffentlicher Schlüssel des Reviewers
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyPeerReviewProof(proof, review, publicKey) {
    try {
      // 1. Berechne den Review-Hash
      const reviewHash = this.hashReview(review);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (reviewHash !== proof.reviewHash) {
        console.warn('Review-Hash stimmt nicht überein');
        return false;
      }
      
      // 3. Berechne den Publikations-Hash
      const publicationHash = this.hashString(proof.publicationDoi);
      
      // 4. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 5. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Peer-Review-Beweises:', error);
      return false;
    }
  }
  
  /**
   * Hasht eine Publikation
   * @private
   */
  hashPublication(publication) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      doi: publication.doi,
      title: publication.title,
      authors: publication.authors.map(author => ({
        name: author.name,
        orcid: author.orcid
      })),
      abstract: publication.abstract,
      publicationYear: publication.publicationYear
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht Autorschaftsdaten
   * @private
   */
  hashAuthorship(doi, author) {
    const authorshipData = {
      doi,
      authorId: author.id,
      authorName: author.name,
      authorRole: author.role || 'author'
    };
    
    return this.hashObject(authorshipData);
  }
  
  /**
   * Hasht Forschungsdaten
   * @private
   */
  hashData(data) {
    return this.hashObject(data);
  }
  
  /**
   * Extrahiert Metadaten aus Forschungsdaten
   * @private
   */
  extractDataMetadata(data) {
    // Extrahiere Metadaten ohne die eigentlichen Daten
    const metadata = {
      dataType: typeof data,
      isArray: Array.isArray(data),
      length: Array.isArray(data) ? data.length : undefined,
      keys: typeof data === 'object' ? Object.keys(data) : undefined,
      timestamp: Date.now()
    };
    
    return metadata;
  }
  
  /**
   * Hasht Review-Daten
   * @private
   */
  hashReview(review) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      reviewerId: review.reviewerId,
      publicationDoi: review.publicationDoi,
      date: review.date,
      recommendation: review.recommendation,
      confidence: review.confidence,
      comments: review.comments
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht ein Objekt
   * @private
   */
  hashObject(obj) {
    const jsonString = JSON.stringify(obj);
    return this.hashString(jsonString);
  }
  
  /**
   * Hasht einen String
   * @private
   */
  hashString(str) {
    return crypto
      .createHash(this.options.hashAlgorithm)
      .update(str)
      .digest('hex');
  }
  
  /**
   * Kombiniert zwei Hashes
   * @private
   */
  combineHashes(hash1, hash2) {
    return this.hashString(hash1 + hash2);
  }
  
  /**
   * Signiert einen Hash mit einem privaten Schlüssel
   * @private
   */
  sign(hash, privateKey) {
    // In einer realen Implementierung würde hier eine echte Signatur erstellt
    // Für diese Beispielimplementierung simulieren wir die Signatur
    const signer = crypto.createSign('RSA-SHA256');
    signer.update(hash);
    
    try {
      // Versuche, eine echte Signatur zu erstellen, falls ein gültiger Schlüssel vorliegt
      return signer.sign(privateKey, 'hex');
    } catch (error) {
      console.warn('Simuliere Signatur, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Signatur
      return crypto
        .createHash('sha256')
        .update(hash + privateKey)
        .digest('hex');
    }
  }
  
  /**
   * Verifiziert eine Signatur mit einem öffentlichen Schlüssel
   * @private
   */
  verify(hash, signature, publicKey) {
    // In einer realen Implementierung würde hier eine echte Signaturverifizierung durchgeführt
    // Für diese Beispielimplementierung simulieren wir die Verifizierung
    try {
      // Versuche, eine echte Verifizierung durchzuführen, falls ein gültiger Schlüssel vorliegt
      const verifier = crypto.createVerify('RSA-SHA256');
      verifier.update(hash);
      return verifier.verify(publicKey, signature, 'hex');
    } catch (error) {
      console.warn('Simuliere Verifizierung, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Verifizierung
      const simulatedSignature = crypto
        .createHash('sha256')
        .update(hash + publicKey)
        .digest('hex');
      
      return simulatedSignature === signature;
    }
  }
}

export default ZkVerifier;/**
 * Zero-Knowledge-Proof-Verifizierer für wissenschaftliche Publikationen
 * Ermöglicht die Verifizierung von Autorschaft und Datenintegrität ohne sensible Daten preiszugeben
 */

import crypto from 'crypto';

class ZkVerifier {
  constructor(options = {}) {
    this.options = {
      hashAlgorithm: 'sha256',
      ...options
    };
  }
  
  /**
   * Generiert einen Beweis für die Autorschaft einer Publikation
   * @param {Object} publication - Publikationsdaten
   * @param {Object} author - Autorschaftsdaten
   * @param {string} privateKey - Privater Schlüssel des Autors
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateAuthorshipProof(publication, author, privateKey) {
    try {
      // 1. Erstelle einen Hash der Publikation
      const publicationHash = this.hashPublication(publication);
      
      // 2. Erstelle einen Hash der Autorschaftsdaten
      const authorshipHash = this.hashAuthorship(publication.doi, author);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(publicationHash, authorshipHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        publicationHash,
        authorshipClaim: {
          authorId: author.id,
          authorName: author.name,
          authorRole: author.role || 'author',
          authorshipHash
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Autorschaftsbeweises:', error);
      throw new Error(`Autorschaftsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Autorschaftsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} publication - Publikationsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel des Autors
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyAuthorshipProof(proof, publication, publicKey) {
    try {
      // 1. Berechne den Publikationshash
      const publicationHash = this.hashPublication(publication);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (publicationHash !== proof.publicationHash) {
        console.warn('Publikationshash stimmt nicht überein');
        return false;
      }
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(
        proof.publicationHash,
        proof.authorshipClaim.authorshipHash
      );
      
      // 4. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Autorschaftsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Integrität von Forschungsdaten
   * @param {Object} data - Forschungsdaten
   * @param {string} privateKey - Privater Schlüssel
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateDataIntegrityProof(data, privateKey) {
    try {
      // 1. Erstelle einen Hash der Daten
      const dataHash = this.hashData(data);
      
      // 2. Erstelle Metadaten über die Daten (ohne die eigentlichen Daten)
      const metadata = this.extractDataMetadata(data);
      
      // 3. Signiere den Hash mit dem privaten Schlüssel
      const signature = this.sign(dataHash, privateKey);
      
      // 4. Erstelle den Beweis
      return {
        dataHash,
        metadata,
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Datenintegritätsbeweises:', error);
      throw new Error(`Datenintegritätsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Datenintegritätsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} data - Forschungsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyDataIntegrityProof(proof, data, publicKey) {
    try {
      // 1. Berechne den Datenhash
      const dataHash = this.hashData(data);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (dataHash !== proof.dataHash) {
        console.warn('Datenhash stimmt nicht überein');
        return false;
      }
      
      // 3. Verifiziere die Signatur
      const isSignatureValid = this.verify(dataHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Datenintegritätsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Peer-Review einer Publikation
   * @param {Object} review - Review-Daten
   * @param {string} publicationDoi - DOI der Publikation
   * @param {string} privateKey - Privater Schlüssel des Reviewers
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generatePeerReviewProof(review, publicationDoi, privateKey) {
    try {
      // 1. Erstelle einen Hash des Reviews
      const reviewHash = this.hashReview(review);
      
      // 2. Erstelle einen Hash der Publikations-DOI
      const publicationHash = this.hashString(publicationDoi);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        reviewHash,
        publicationDoi,
        reviewerInfo: {
          id: review.reviewerId,
          name: review.reviewerName,
          institution: review.reviewerInstitution
        },
        reviewMetadata: {
          date: review.date,
          recommendation: review.recommendation,
          confidence: review.confidence
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Peer-Review-Beweises:', error);
      throw new Error(`Peer-Review-Beweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Peer-Review-Beweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} review - Review-Daten
   * @param {string} publicKey - Öffentlicher Schlüssel des Reviewers
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyPeerReviewProof(proof, review, publicKey) {
    try {
      // 1. Berechne den Review-Hash
      const reviewHash = this.hashReview(review);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (reviewHash !== proof.reviewHash) {
        console.warn('Review-Hash stimmt nicht überein');
        return false;
      }
      
      // 3. Berechne den Publikations-Hash
      const publicationHash = this.hashString(proof.publicationDoi);
      
      // 4. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 5. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Peer-Review-Beweises:', error);
      return false;
    }
  }
  
  /**
   * Hasht eine Publikation
   * @private
   */
  hashPublication(publication) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      doi: publication.doi,
      title: publication.title,
      authors: publication.authors.map(author => ({
        name: author.name,
        orcid: author.orcid
      })),
      abstract: publication.abstract,
      publicationYear: publication.publicationYear
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht Autorschaftsdaten
   * @private
   */
  hashAuthorship(doi, author) {
    const authorshipData = {
      doi,
      authorId: author.id,
      authorName: author.name,
      authorRole: author.role || 'author'
    };
    
    return this.hashObject(authorshipData);
  }
  
  /**
   * Hasht Forschungsdaten
   * @private
   */
  hashData(data) {
    return this.hashObject(data);
  }
  
  /**
   * Extrahiert Metadaten aus Forschungsdaten
   * @private
   */
  extractDataMetadata(data) {
    // Extrahiere Metadaten ohne die eigentlichen Daten
    const metadata = {
      dataType: typeof data,
      isArray: Array.isArray(data),
      length: Array.isArray(data) ? data.length : undefined,
      keys: typeof data === 'object' ? Object.keys(data) : undefined,
      timestamp: Date.now()
    };
    
    return metadata;
  }
  
  /**
   * Hasht Review-Daten
   * @private
   */
  hashReview(review) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      reviewerId: review.reviewerId,
      publicationDoi: review.publicationDoi,
      date: review.date,
      recommendation: review.recommendation,
      confidence: review.confidence,
      comments: review.comments
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht ein Objekt
   * @private
   */
  hashObject(obj) {
    const jsonString = JSON.stringify(obj);
    return this.hashString(jsonString);
  }
  
  /**
   * Hasht einen String
   * @private
   */
  hashString(str) {
    return crypto
      .createHash(this.options.hashAlgorithm)
      .update(str)
      .digest('hex');
  }
  
  /**
   * Kombiniert zwei Hashes
   * @private
   */
  combineHashes(hash1, hash2) {
    return this.hashString(hash1 + hash2);
  }
  
  /**
   * Signiert einen Hash mit einem privaten Schlüssel
   * @private
   */
  sign(hash, privateKey) {
    // In einer realen Implementierung würde hier eine echte Signatur erstellt
    // Für diese Beispielimplementierung simulieren wir die Signatur
    const signer = crypto.createSign('RSA-SHA256');
    signer.update(hash);
    
    try {
      // Versuche, eine echte Signatur zu erstellen, falls ein gültiger Schlüssel vorliegt
      return signer.sign(privateKey, 'hex');
    } catch (error) {
      console.warn('Simuliere Signatur, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Signatur
      return crypto
        .createHash('sha256')
        .update(hash + privateKey)
        .digest('hex');
    }
  }
  
  /**
   * Verifiziert eine Signatur mit einem öffentlichen Schlüssel
   * @private
   */
  verify(hash, signature, publicKey) {
    // In einer realen Implementierung würde hier eine echte Signaturverifizierung durchgeführt
    // Für diese Beispielimplementierung simulieren wir die Verifizierung
    try {
      // Versuche, eine echte Verifizierung durchzuführen, falls ein gültiger Schlüssel vorliegt
      const verifier = crypto.createVerify('RSA-SHA256');
      verifier.update(hash);
      return verifier.verify(publicKey, signature, 'hex');
    } catch (error) {
      console.warn('Simuliere Verifizierung, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Verifizierung
      const simulatedSignature = crypto
        .createHash('sha256')
        .update(hash + publicKey)
        .digest('hex');
      
      return simulatedSignature === signature;
    }
  }
}

export default ZkVerifier; * Ermöglicht die Verifizierung von Autorschaft und Datenintegrität ohne sensible Daten preiszugeben
 */

import crypto from 'crypto';

class ZkVerifier {
  constructor(options = {}) {
    this.options = {
      hashAlgorithm: 'sha256',
      ...options
    };
  }
  
  /**
   * Generiert einen Beweis für die Autorschaft einer Publikation
   * @param {Object} publication - Publikationsdaten
   * @param {Object} author - Autorschaftsdaten
   * @param {string} privateKey - Privater Schlüssel des Autors
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateAuthorshipProof(publication, author, privateKey) {
    try {
      // 1. Erstelle einen Hash der Publikation
      const publicationHash = this.hashPublication(publication);
      
      // 2. Erstelle einen Hash der Autorschaftsdaten
      const authorshipHash = this.hashAuthorship(publication.doi, author);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(publicationHash, authorshipHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        publicationHash,
        authorshipClaim: {
          authorId: author.id,
          authorName: author.name,
          authorRole: author.role || 'author',
          authorshipHash
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Autorschaftsbeweises:', error);
      throw new Error(`Autorschaftsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Autorschaftsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} publication - Publikationsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel des Autors
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyAuthorshipProof(proof, publication, publicKey) {
    try {
      // 1. Berechne den Publikationshash
      const publicationHash = this.hashPublication(publication);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (publicationHash !== proof.publicationHash) {
        console.warn('Publikationshash stimmt nicht überein');
        return false;
      }
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(
        proof.publicationHash,
        proof.authorshipClaim.authorshipHash
      );
      
      // 4. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Autorschaftsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Integrität von Forschungsdaten
   * @param {Object} data - Forschungsdaten
   * @param {string} privateKey - Privater Schlüssel
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateDataIntegrityProof(data, privateKey) {
    try {
      // 1. Erstelle einen Hash der Daten
      const dataHash = this.hashData(data);
      
      // 2. Erstelle Metadaten über die Daten (ohne die eigentlichen Daten)
      const metadata = this.extractDataMetadata(data);
      
      // 3. Signiere den Hash mit dem privaten Schlüssel
      const signature = this.sign(dataHash, privateKey);
      
      // 4. Erstelle den Beweis
      return {
        dataHash,
        metadata,
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Datenintegritätsbeweises:', error);
      throw new Error(`Datenintegritätsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Datenintegritätsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} data - Forschungsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyDataIntegrityProof(proof, data, publicKey) {
    try {
      // 1. Berechne den Datenhash
      const dataHash = this.hashData(data);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (dataHash !== proof.dataHash) {
        console.warn('Datenhash stimmt nicht überein');
        return false;
      }
      
      // 3. Verifiziere die Signatur
      const isSignatureValid = this.verify(dataHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Datenintegritätsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Peer-Review einer Publikation
   * @param {Object} review - Review-Daten
   * @param {string} publicationDoi - DOI der Publikation
   * @param {string} privateKey - Privater Schlüssel des Reviewers
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generatePeerReviewProof(review, publicationDoi, privateKey) {
    try {
      // 1. Erstelle einen Hash des Reviews
      const reviewHash = this.hashReview(review);
      
      // 2. Erstelle einen Hash der Publikations-DOI
      const publicationHash = this.hashString(publicationDoi);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        reviewHash,
        publicationDoi,
        reviewerInfo: {
          id: review.reviewerId,
          name: review.reviewerName,
          institution: review.reviewerInstitution
        },
        reviewMetadata: {
          date: review.date,
          recommendation: review.recommendation,
          confidence: review.confidence
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Peer-Review-Beweises:', error);
      throw new Error(`Peer-Review-Beweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Peer-Review-Beweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} review - Review-Daten
   * @param {string} publicKey - Öffentlicher Schlüssel des Reviewers
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyPeerReviewProof(proof, review, publicKey) {
    try {
      // 1. Berechne den Review-Hash
      const reviewHash = this.hashReview(review);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (reviewHash !== proof.reviewHash) {
        console.warn('Review-Hash stimmt nicht überein');
        return false;
      }
      
      // 3. Berechne den Publikations-Hash
      const publicationHash = this.hashString(proof.publicationDoi);
      
      // 4. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 5. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Peer-Review-Beweises:', error);
      return false;
    }
  }
  
  /**
   * Hasht eine Publikation
   * @private
   */
  hashPublication(publication) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      doi: publication.doi,
      title: publication.title,
      authors: publication.authors.map(author => ({
        name: author.name,
        orcid: author.orcid
      })),
      abstract: publication.abstract,
      publicationYear: publication.publicationYear
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht Autorschaftsdaten
   * @private
   */
  hashAuthorship(doi, author) {
    const authorshipData = {
      doi,
      authorId: author.id,
      authorName: author.name,
      authorRole: author.role || 'author'
    };
    
    return this.hashObject(authorshipData);
  }
  
  /**
   * Hasht Forschungsdaten
   * @private
   */
  hashData(data) {
    return this.hashObject(data);
  }
  
  /**
   * Extrahiert Metadaten aus Forschungsdaten
   * @private
   */
  extractDataMetadata(data) {
    // Extrahiere Metadaten ohne die eigentlichen Daten
    const metadata = {
      dataType: typeof data,
      isArray: Array.isArray(data),
      length: Array.isArray(data) ? data.length : undefined,
      keys: typeof data === 'object' ? Object.keys(data) : undefined,
      timestamp: Date.now()
    };
    
    return metadata;
  }
  
  /**
   * Hasht Review-Daten
   * @private
   */
  hashReview(review) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      reviewerId: review.reviewerId,
      publicationDoi: review.publicationDoi,
      date: review.date,
      recommendation: review.recommendation,
      confidence: review.confidence,
      comments: review.comments
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht ein Objekt
   * @private
   */
  hashObject(obj) {
    const jsonString = JSON.stringify(obj);
    return this.hashString(jsonString);
  }
  
  /**
   * Hasht einen String
   * @private
   */
  hashString(str) {
    return crypto
      .createHash(this.options.hashAlgorithm)
      .update(str)
      .digest('hex');
  }
  
  /**
   * Kombiniert zwei Hashes
   * @private
   */
  combineHashes(hash1, hash2) {
    return this.hashString(hash1 + hash2);
  }
  
  /**
   * Signiert einen Hash mit einem privaten Schlüssel
   * @private
   */
  sign(hash, privateKey) {
    // In einer realen Implementierung würde hier eine echte Signatur erstellt
    // Für diese Beispielimplementierung simulieren wir die Signatur
    const signer = crypto.createSign('RSA-SHA256');
    signer.update(hash);
    
    try {
      // Versuche, eine echte Signatur zu erstellen, falls ein gültiger Schlüssel vorliegt
      return signer.sign(privateKey, 'hex');
    } catch (error) {
      console.warn('Simuliere Signatur, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Signatur
      return crypto
        .createHash('sha256')
        .update(hash + privateKey)
        .digest('hex');
    }
  }
  
  /**
   * Verifiziert eine Signatur mit einem öffentlichen Schlüssel
   * @private
   */
  verify(hash, signature, publicKey) {
    // In einer realen Implementierung würde hier eine echte Signaturverifizierung durchgeführt
    // Für diese Beispielimplementierung simulieren wir die Verifizierung
    try {
      // Versuche, eine echte Verifizierung durchzuführen, falls ein gültiger Schlüssel vorliegt
      const verifier = crypto.createVerify('RSA-SHA256');
      verifier.update(hash);
      return verifier.verify(publicKey, signature, 'hex');
    } catch (error) {
      console.warn('Simuliere Verifizierung, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Verifizierung
      const simulatedSignature = crypto
        .createHash('sha256')
        .update(hash + publicKey)
        .digest('hex');
      
      return simulatedSignature === signature;
    }
  }
}

export default ZkVerifier;
export default ZkVerifier;export default ZkVerifier; */

import crypto from 'crypto';

class ZkVerifier {
  constructor(options = {}) {
    this.options = {
      hashAlgorithm: 'sha256',
      ...options
    };
  }
  
  /**
   * Generiert einen Beweis für die Autorschaft einer Publikation
   * @param {Object} publication - Publikationsdaten
   * @param {Object} author - Autorschaftsdaten
   * @param {string} privateKey - Privater Schlüssel des Autors
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateAuthorshipProof(publication, author, privateKey) {
    try {
      // 1. Erstelle einen Hash der Publikation
      const publicationHash = this.hashPublication(publication);
      
      // 2. Erstelle einen Hash der Autorschaftsdaten
      const authorshipHash = this.hashAuthorship(publication.doi, author);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(publicationHash, authorshipHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        publicationHash,
        authorshipClaim: {
          authorId: author.id,
          authorName: author.name,
          authorRole: author.role || 'author',
          authorshipHash
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Autorschaftsbeweises:', error);
      throw new Error(`Autorschaftsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Autorschaftsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} publication - Publikationsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel des Autors
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyAuthorshipProof(proof, publication, publicKey) {
    try {
      // 1. Berechne den Publikationshash
      const publicationHash = this.hashPublication(publication);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (publicationHash !== proof.publicationHash) {
        console.warn('Publikationshash stimmt nicht überein');
        return false;
      }
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(
        proof.publicationHash,
        proof.authorshipClaim.authorshipHash
      );
      
      // 4. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Autorschaftsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Integrität von Forschungsdaten
   * @param {Object} data - Forschungsdaten
   * @param {string} privateKey - Privater Schlüssel
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateDataIntegrityProof(data, privateKey) {
    try {
      // 1. Erstelle einen Hash der Daten
      const dataHash = this.hashData(data);
      
      // 2. Erstelle Metadaten über die Daten (ohne die eigentlichen Daten)
      const metadata = this.extractDataMetadata(data);
      
      // 3. Signiere den Hash mit dem privaten Schlüssel
      const signature = this.sign(dataHash, privateKey);
      
      // 4. Erstelle den Beweis
      return {
        dataHash,
        metadata,
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Datenintegritätsbeweises:', error);
      throw new Error(`Datenintegritätsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Datenintegritätsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} data - Forschungsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyDataIntegrityProof(proof, data, publicKey) {
    try {
      // 1. Berechne den Datenhash
      const dataHash = this.hashData(data);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (dataHash !== proof.dataHash) {
        console.warn('Datenhash stimmt nicht überein');
        return false;
      }
      
      // 3. Verifiziere die Signatur
      const isSignatureValid = this.verify(dataHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Datenintegritätsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Peer-Review einer Publikation
   * @param {Object} review - Review-Daten
   * @param {string} publicationDoi - DOI der Publikation
   * @param {string} privateKey - Privater Schlüssel des Reviewers
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generatePeerReviewProof(review, publicationDoi, privateKey) {
    try {
      // 1. Erstelle einen Hash des Reviews
      const reviewHash = this.hashReview(review);
      
      // 2. Erstelle einen Hash der Publikations-DOI
      const publicationHash = this.hashString(publicationDoi);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        reviewHash,
        publicationDoi,
        reviewerInfo: {
          id: review.reviewerId,
          name: review.reviewerName,
          institution: review.reviewerInstitution
        },
        reviewMetadata: {
          date: review.date,
          recommendation: review.recommendation,
          confidence: review.confidence
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Peer-Review-Beweises:', error);
      throw new Error(`Peer-Review-Beweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Peer-Review-Beweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} review - Review-Daten
   * @param {string} publicKey - Öffentlicher Schlüssel des Reviewers
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyPeerReviewProof(proof, review, publicKey) {
    try {
      // 1. Berechne den Review-Hash
      const reviewHash = this.hashReview(review);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (reviewHash !== proof.reviewHash) {
        console.warn('Review-Hash stimmt nicht überein');
        return false;
      }
      
      // 3. Berechne den Publikations-Hash
      const publicationHash = this.hashString(proof.publicationDoi);
      
      // 4. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 5. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Peer-Review-Beweises:', error);
      return false;
    }
  }
  
  /**
   * Hasht eine Publikation
   * @private
   */
  hashPublication(publication) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      doi: publication.doi,
      title: publication.title,
      authors: publication.authors.map(author => ({
        name: author.name,
        orcid: author.orcid
      })),
      abstract: publication.abstract,
      publicationYear: publication.publicationYear
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht Autorschaftsdaten
   * @private
   */
  hashAuthorship(doi, author) {
    const authorshipData = {
      doi,
      authorId: author.id,
      authorName: author.name,
      authorRole: author.role || 'author'
    };
    
    return this.hashObject(authorshipData);
  }
  
  /**
   * Hasht Forschungsdaten
   * @private
   */
  hashData(data) {
    return this.hashObject(data);
  }
  
  /**
   * Extrahiert Metadaten aus Forschungsdaten
   * @private
   */
  extractDataMetadata(data) {
    // Extrahiere Metadaten ohne die eigentlichen Daten
    const metadata = {
      dataType: typeof data,
      isArray: Array.isArray(data),
      length: Array.isArray(data) ? data.length : undefined,
      keys: typeof data === 'object' ? Object.keys(data) : undefined,
      timestamp: Date.now()
    };
    
    return metadata;
  }
  
  /**
   * Hasht Review-Daten
   * @private
   */
  hashReview(review) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      reviewerId: review.reviewerId,
      publicationDoi: review.publicationDoi,
      date: review.date,
      recommendation: review.recommendation,
      confidence: review.confidence,
      comments: review.comments
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht ein Objekt
   * @private
   */
  hashObject(obj) {
    const jsonString = JSON.stringify(obj);
    return this.hashString(jsonString);
  }
  
  /**
   * Hasht einen String
   * @private
   */
  hashString(str) {
    return crypto
      .createHash(this.options.hashAlgorithm)
      .update(str)
      .digest('hex');
  }
  
  /**
   * Kombiniert zwei Hashes
   * @private
   */
  combineHashes(hash1, hash2) {
    return this.hashString(hash1 + hash2);
  }
  
  /**
   * Signiert einen Hash mit einem privaten Schlüssel
   * @private
   */
  sign(hash, privateKey) {
    // In einer realen Implementierung würde hier eine echte Signatur erstellt
    // Für diese Beispielimplementierung simulieren wir die Signatur
    const signer = crypto.createSign('RSA-SHA256');
    signer.update(hash);
    
    try {
      // Versuche, eine echte Signatur zu erstellen, falls ein gültiger Schlüssel vorliegt
      return signer.sign(privateKey, 'hex');
    } catch (error) {
      console.warn('Simuliere Signatur, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Signatur
      return crypto
        .createHash('sha256')
        .update(hash + privateKey)
        .digest('hex');
    }
  }
  
  /**
   * Verifiziert eine Signatur mit einem öffentlichen Schlüssel
   * @private
   */
  verify(hash, signature, publicKey) {
    // In einer realen Implementierung würde hier eine echte Signaturverifizierung durchgeführt
    // Für diese Beispielimplementierung simulieren wir die Verifizierung
    try {
      // Versuche, eine echte Verifizierung durchzuführen, falls ein gültiger Schlüssel vorliegt
      const verifier = crypto.createVerify('RSA-SHA256');
      verifier.update(hash);
      return verifier.verify(publicKey, signature, 'hex');
    } catch (error) {
      console.warn('Simuliere Verifizierung, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Verifizierung
      const simulatedSignature = crypto
        .createHash('sha256')
        .update(hash + publicKey)
        .digest('hex');
      
      return simulatedSignature === signature;
    }
  }
}

export default ZkVerifier;
export default ZkVerifier;export default ZkVerifier;export default ZkVerifier; */

import crypto from 'crypto';

class ZkVerifier {
  constructor(options = {}) {
    this.options = {
      hashAlgorithm: 'sha256',
      ...options
    };
  }
  
  /**
   * Generiert einen Beweis für die Autorschaft einer Publikation
   * @param {Object} publication - Publikationsdaten
   * @param {Object} author - Autorschaftsdaten
   * @param {string} privateKey - Privater Schlüssel des Autors
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateAuthorshipProof(publication, author, privateKey) {
    try {
      // 1. Erstelle einen Hash der Publikation
      const publicationHash = this.hashPublication(publication);
      
      // 2. Erstelle einen Hash der Autorschaftsdaten
      const authorshipHash = this.hashAuthorship(publication.doi, author);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(publicationHash, authorshipHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        publicationHash,
        authorshipClaim: {
          authorId: author.id,
          authorName: author.name,
          authorRole: author.role || 'author',
          authorshipHash
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Autorschaftsbeweises:', error);
      throw new Error(`Autorschaftsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Autorschaftsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} publication - Publikationsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel des Autors
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyAuthorshipProof(proof, publication, publicKey) {
    try {
      // 1. Berechne den Publikationshash
      const publicationHash = this.hashPublication(publication);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (publicationHash !== proof.publicationHash) {
        console.warn('Publikationshash stimmt nicht überein');
        return false;
      }
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(
        proof.publicationHash,
        proof.authorshipClaim.authorshipHash
      );
      
      // 4. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Autorschaftsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Integrität von Forschungsdaten
   * @param {Object} data - Forschungsdaten
   * @param {string} privateKey - Privater Schlüssel
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateDataIntegrityProof(data, privateKey) {
    try {
      // 1. Erstelle einen Hash der Daten
      const dataHash = this.hashData(data);
      
      // 2. Erstelle Metadaten über die Daten (ohne die eigentlichen Daten)
      const metadata = this.extractDataMetadata(data);
      
      // 3. Signiere den Hash mit dem privaten Schlüssel
      const signature = this.sign(dataHash, privateKey);
      
      // 4. Erstelle den Beweis
      return {
        dataHash,
        metadata,
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Datenintegritätsbeweises:', error);
      throw new Error(`Datenintegritätsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Datenintegritätsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} data - Forschungsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyDataIntegrityProof(proof, data, publicKey) {
    try {
      // 1. Berechne den Datenhash
      const dataHash = this.hashData(data);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (dataHash !== proof.dataHash) {
        console.warn('Datenhash stimmt nicht überein');
        return false;
      }
      
      // 3. Verifiziere die Signatur
      const isSignatureValid = this.verify(dataHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Datenintegritätsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Peer-Review einer Publikation
   * @param {Object} review - Review-Daten
   * @param {string} publicationDoi - DOI der Publikation
   * @param {string} privateKey - Privater Schlüssel des Reviewers
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generatePeerReviewProof(review, publicationDoi, privateKey) {
    try {
      // 1. Erstelle einen Hash des Reviews
      const reviewHash = this.hashReview(review);
      
      // 2. Erstelle einen Hash der Publikations-DOI
      const publicationHash = this.hashString(publicationDoi);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        reviewHash,
        publicationDoi,
        reviewerInfo: {
          id: review.reviewerId,
          name: review.reviewerName,
          institution: review.reviewerInstitution
        },
        reviewMetadata: {
          date: review.date,
          recommendation: review.recommendation,
          confidence: review.confidence
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Peer-Review-Beweises:', error);
      throw new Error(`Peer-Review-Beweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Peer-Review-Beweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} review - Review-Daten
   * @param {string} publicKey - Öffentlicher Schlüssel des Reviewers
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyPeerReviewProof(proof, review, publicKey) {
    try {
      // 1. Berechne den Review-Hash
      const reviewHash = this.hashReview(review);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (reviewHash !== proof.reviewHash) {
        console.warn('Review-Hash stimmt nicht überein');
        return false;
      }
      
      // 3. Berechne den Publikations-Hash
      const publicationHash = this.hashString(proof.publicationDoi);
      
      // 4. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 5. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Peer-Review-Beweises:', error);
      return false;
    }
  }
  
  /**
   * Hasht eine Publikation
   * @private
   */
  hashPublication(publication) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      doi: publication.doi,
      title: publication.title,
      authors: publication.authors.map(author => ({
        name: author.name,
        orcid: author.orcid
      })),
      abstract: publication.abstract,
      publicationYear: publication.publicationYear
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht Autorschaftsdaten
   * @private
   */
  hashAuthorship(doi, author) {
    const authorshipData = {
      doi,
      authorId: author.id,
      authorName: author.name,
      authorRole: author.role || 'author'
    };
    
    return this.hashObject(authorshipData);
  }
  
  /**
   * Hasht Forschungsdaten
   * @private
   */
  hashData(data) {
    return this.hashObject(data);
  }
  
  /**
   * Extrahiert Metadaten aus Forschungsdaten
   * @private
   */
  extractDataMetadata(data) {
    // Extrahiere Metadaten ohne die eigentlichen Daten
    const metadata = {
      dataType: typeof data,
      isArray: Array.isArray(data),
      length: Array.isArray(data) ? data.length : undefined,
      keys: typeof data === 'object' ? Object.keys(data) : undefined,
      timestamp: Date.now()
    };
    
    return metadata;
  }
  
  /**
   * Hasht Review-Daten
   * @private
   */
  hashReview(review) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      reviewerId: review.reviewerId,
      publicationDoi: review.publicationDoi,
      date: review.date,
      recommendation: review.recommendation,
      confidence: review.confidence,
      comments: review.comments
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht ein Objekt
   * @private
   */
  hashObject(obj) {
    const jsonString = JSON.stringify(obj);
    return this.hashString(jsonString);
  }
  
  /**
   * Hasht einen String
   * @private
   */
  hashString(str) {
    return crypto
      .createHash(this.options.hashAlgorithm)
      .update(str)
      .digest('hex');
  }
  
  /**
   * Kombiniert zwei Hashes
   * @private
   */
  combineHashes(hash1, hash2) {
    return this.hashString(hash1 + hash2);
  }
  
  /**
   * Signiert einen Hash mit einem privaten Schlüssel
   * @private
   */
  sign(hash, privateKey) {
    // In einer realen Implementierung würde hier eine echte Signatur erstellt
    // Für diese Beispielimplementierung simulieren wir die Signatur
    const signer = crypto.createSign('RSA-SHA256');
    signer.update(hash);
    
    try {
      // Versuche, eine echte Signatur zu erstellen, falls ein gültiger Schlüssel vorliegt
      return signer.sign(privateKey, 'hex');
    } catch (error) {
      console.warn('Simuliere Signatur, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Signatur
      return crypto
        .createHash('sha256')
        .update(hash + privateKey)
        .digest('hex');
    }
  }
  
  /**
   * Verifiziert eine Signatur mit einem öffentlichen Schlüssel
   * @private
   */
  verify(hash, signature, publicKey) {
    // In einer realen Implementierung würde hier eine echte Signaturverifizierung durchgeführt
    // Für diese Beispielimplementierung simulieren wir die Verifizierung
    try {
      // Versuche, eine echte Verifizierung durchzuführen, falls ein gültiger Schlüssel vorliegt
      const verifier = crypto.createVerify('RSA-SHA256');
      verifier.update(hash);
      return verifier.verify(publicKey, signature, 'hex');
    } catch (error) {
      console.warn('Simuliere Verifizierung, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Verifizierung
      const simulatedSignature = crypto
        .createHash('sha256')
        .update(hash + publicKey)
        .digest('hex');
      
      return simulatedSignature === signature;
    }
  }
}

export default ZkVerifier;
export default ZkVerifier;export default ZkVerifier;
export default ZkVerifier;export default ZkVerifier;export default ZkVerifier; */

import crypto from 'crypto';

class ZkVerifier {
  constructor(options = {}) {
    this.options = {
      hashAlgorithm: 'sha256',
      ...options
    };
  }
  
  /**
   * Generiert einen Beweis für die Autorschaft einer Publikation
   * @param {Object} publication - Publikationsdaten
   * @param {Object} author - Autorschaftsdaten
   * @param {string} privateKey - Privater Schlüssel des Autors
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateAuthorshipProof(publication, author, privateKey) {
    try {
      // 1. Erstelle einen Hash der Publikation
      const publicationHash = this.hashPublication(publication);
      
      // 2. Erstelle einen Hash der Autorschaftsdaten
      const authorshipHash = this.hashAuthorship(publication.doi, author);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(publicationHash, authorshipHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        publicationHash,
        authorshipClaim: {
          authorId: author.id,
          authorName: author.name,
          authorRole: author.role || 'author',
          authorshipHash
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Autorschaftsbeweises:', error);
      throw new Error(`Autorschaftsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Autorschaftsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} publication - Publikationsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel des Autors
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyAuthorshipProof(proof, publication, publicKey) {
    try {
      // 1. Berechne den Publikationshash
      const publicationHash = this.hashPublication(publication);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (publicationHash !== proof.publicationHash) {
        console.warn('Publikationshash stimmt nicht überein');
        return false;
      }
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(
        proof.publicationHash,
        proof.authorshipClaim.authorshipHash
      );
      
      // 4. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Autorschaftsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Integrität von Forschungsdaten
   * @param {Object} data - Forschungsdaten
   * @param {string} privateKey - Privater Schlüssel
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generateDataIntegrityProof(data, privateKey) {
    try {
      // 1. Erstelle einen Hash der Daten
      const dataHash = this.hashData(data);
      
      // 2. Erstelle Metadaten über die Daten (ohne die eigentlichen Daten)
      const metadata = this.extractDataMetadata(data);
      
      // 3. Signiere den Hash mit dem privaten Schlüssel
      const signature = this.sign(dataHash, privateKey);
      
      // 4. Erstelle den Beweis
      return {
        dataHash,
        metadata,
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Datenintegritätsbeweises:', error);
      throw new Error(`Datenintegritätsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Datenintegritätsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} data - Forschungsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyDataIntegrityProof(proof, data, publicKey) {
    try {
      // 1. Berechne den Datenhash
      const dataHash = this.hashData(data);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (dataHash !== proof.dataHash) {
        console.warn('Datenhash stimmt nicht überein');
        return false;
      }
      
      // 3. Verifiziere die Signatur
      const isSignatureValid = this.verify(dataHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Datenintegritätsbeweises:', error);
      return false;
    }
  }
  
  /**
   * Generiert einen Beweis für die Peer-Review einer Publikation
   * @param {Object} review - Review-Daten
   * @param {string} publicationDoi - DOI der Publikation
   * @param {string} privateKey - Privater Schlüssel des Reviewers
   * @returns {Object} - Zero-Knowledge-Beweis
   */
  generatePeerReviewProof(review, publicationDoi, privateKey) {
    try {
      // 1. Erstelle einen Hash des Reviews
      const reviewHash = this.hashReview(review);
      
      // 2. Erstelle einen Hash der Publikations-DOI
      const publicationHash = this.hashString(publicationDoi);
      
      // 3. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 4. Signiere den kombinierten Hash mit dem privaten Schlüssel
      const signature = this.sign(combinedHash, privateKey);
      
      // 5. Erstelle den Beweis
      return {
        reviewHash,
        publicationDoi,
        reviewerInfo: {
          id: review.reviewerId,
          name: review.reviewerName,
          institution: review.reviewerInstitution
        },
        reviewMetadata: {
          date: review.date,
          recommendation: review.recommendation,
          confidence: review.confidence
        },
        signature,
        timestamp: Date.now(),
        version: '1.0'
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Peer-Review-Beweises:', error);
      throw new Error(`Peer-Review-Beweis konnte nicht generiert werden: ${error.message}`);
    }
  }
  
  /**
   * Verifiziert einen Peer-Review-Beweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} review - Review-Daten
   * @param {string} publicKey - Öffentlicher Schlüssel des Reviewers
   * @returns {boolean} - Verifizierungsergebnis
   */
  verifyPeerReviewProof(proof, review, publicKey) {
    try {
      // 1. Berechne den Review-Hash
      const reviewHash = this.hashReview(review);
      
      // 2. Prüfe, ob der Hash mit dem im Beweis übereinstimmt
      if (reviewHash !== proof.reviewHash) {
        console.warn('Review-Hash stimmt nicht überein');
        return false;
      }
      
      // 3. Berechne den Publikations-Hash
      const publicationHash = this.hashString(proof.publicationDoi);
      
      // 4. Kombiniere die Hashes
      const combinedHash = this.combineHashes(reviewHash, publicationHash);
      
      // 5. Verifiziere die Signatur
      const isSignatureValid = this.verify(combinedHash, proof.signature, publicKey);
      
      return isSignatureValid;
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Peer-Review-Beweises:', error);
      return false;
    }
  }
  
  /**
   * Hasht eine Publikation
   * @private
   */
  hashPublication(publication) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      doi: publication.doi,
      title: publication.title,
      authors: publication.authors.map(author => ({
        name: author.name,
        orcid: author.orcid
      })),
      abstract: publication.abstract,
      publicationYear: publication.publicationYear
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht Autorschaftsdaten
   * @private
   */
  hashAuthorship(doi, author) {
    const authorshipData = {
      doi,
      authorId: author.id,
      authorName: author.name,
      authorRole: author.role || 'author'
    };
    
    return this.hashObject(authorshipData);
  }
  
  /**
   * Hasht Forschungsdaten
   * @private
   */
  hashData(data) {
    return this.hashObject(data);
  }
  
  /**
   * Extrahiert Metadaten aus Forschungsdaten
   * @private
   */
  extractDataMetadata(data) {
    // Extrahiere Metadaten ohne die eigentlichen Daten
    const metadata = {
      dataType: typeof data,
      isArray: Array.isArray(data),
      length: Array.isArray(data) ? data.length : undefined,
      keys: typeof data === 'object' ? Object.keys(data) : undefined,
      timestamp: Date.now()
    };
    
    return metadata;
  }
  
  /**
   * Hasht Review-Daten
   * @private
   */
  hashReview(review) {
    // Extrahiere relevante Felder für den Hash
    const relevantData = {
      reviewerId: review.reviewerId,
      publicationDoi: review.publicationDoi,
      date: review.date,
      recommendation: review.recommendation,
      confidence: review.confidence,
      comments: review.comments
    };
    
    return this.hashObject(relevantData);
  }
  
  /**
   * Hasht ein Objekt
   * @private
   */
  hashObject(obj) {
    const jsonString = JSON.stringify(obj);
    return this.hashString(jsonString);
  }
  
  /**
   * Hasht einen String
   * @private
   */
  hashString(str) {
    return crypto
      .createHash(this.options.hashAlgorithm)
      .update(str)
      .digest('hex');
  }
  
  /**
   * Kombiniert zwei Hashes
   * @private
   */
  combineHashes(hash1, hash2) {
    return this.hashString(hash1 + hash2);
  }
  
  /**
   * Signiert einen Hash mit einem privaten Schlüssel
   * @private
   */
  sign(hash, privateKey) {
    // In einer realen Implementierung würde hier eine echte Signatur erstellt
    // Für diese Beispielimplementierung simulieren wir die Signatur
    const signer = crypto.createSign('RSA-SHA256');
    signer.update(hash);
    
    try {
      // Versuche, eine echte Signatur zu erstellen, falls ein gültiger Schlüssel vorliegt
      return signer.sign(privateKey, 'hex');
    } catch (error) {
      console.warn('Simuliere Signatur, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Signatur
      return crypto
        .createHash('sha256')
        .update(hash + privateKey)
        .digest('hex');
    }
  }
  
  /**
   * Verifiziert eine Signatur mit einem öffentlichen Schlüssel
   * @private
   */
  verify(hash, signature, publicKey) {
    // In einer realen Implementierung würde hier eine echte Signaturverifizierung durchgeführt
    // Für diese Beispielimplementierung simulieren wir die Verifizierung
    try {
      // Versuche, eine echte Verifizierung durchzuführen, falls ein gültiger Schlüssel vorliegt
      const verifier = crypto.createVerify('RSA-SHA256');
      verifier.update(hash);
      return verifier.verify(publicKey, signature, 'hex');
    } catch (error) {
      console.warn('Simuliere Verifizierung, da kein gültiger Schlüssel vorliegt:', error.message);
      
      // Simulierte Verifizierung
      const simulatedSignature = crypto
        .createHash('sha256')
        .update(hash + publicKey)
        .digest('hex');
      
      return simulatedSignature === signature;
    }
  }
}

export default ZkVerifier;
export default ZkVerifier;