import crypto from 'crypto';
import fetch from 'node-fetch';
import { extractCitationsFromDoi, extractMetricsFromDoi } from '../../ai/utils/citationExtractor.js';
import ZkVerifier from '../verification/ZkVerifier.js';
import { extractCitationsFromDoi, extractMetricsFromDoi } from '../../ai/utils/citationExtractor.js';
import ZkVerifier from '../verification/ZkVerifier.js';
import { extractCitationsFromDoi, extractMetricsFromDoi } from '../../ai/utils/citationExtractor.js';
import ZkVerifier from '../verification/ZkVerifier.js';
import { extractCitationsFromDoi, extractMetricsFromDoi } from '../../ai/utils/citationExtractor.js';
import ZkVerifier from '../verification/ZkVerifier.js';
import { extractCitationsFromDoi, extractMetricsFromDoi } from '../../ai/utils/citationExtractor.js';
import ZkVerifier from '../verification/ZkVerifier.js';
import { extractCitationsFromDoi, extractMetricsFromDoi } from '../../ai/utils/citationExtractor.js';
import ZkVerifier from '../verification/ZkVerifier.js';
import { extractCitationsFromDoi, extractMetricsFromDoi } from '../../ai/utils/citationExtractor.js';
import ZkVerifier from '../verification/ZkVerifier.js';
import { extractCitationsFromDoi, extractMetricsFromDoi } from '../../ai/utils/citationExtractor.js';
import ZkVerifier from '../verification/ZkVerifier.js';
import { extractCitationsFromDoi, extractMetricsFromDoi } from '../../ai/utils/citationExtractor.js';
import ZkVerifier from '../verification/ZkVerifier.js';
import { extractCitationsFromDoi, extractMetricsFromDoi } from '../../ai/utils/citationExtractor.js';
import ZkVerifier from '../verification/ZkVerifier.js';
import { extractCitationsFromDoi, extractMetricsFromDoi } from '../../ai/utils/citationExtractor.js';
import ZkVerifier from '../verification/ZkVerifier.js';
import { extractCitationsFromDoi, extractMetricsFromDoi } from '../../ai/utils/citationExtractor.js';
import ZkVerifier from '../verification/ZkVerifier.js';
import { extractCitationsFromDoi, extractMetricsFromDoi } from '../../ai/utils/citationExtractor.js';
import ZkVerifier from '../verification/ZkVerifier.js';
import { extractCitationsFromDoi, extractMetricsFromDoi } from '../../ai/utils/citationExtractor.js';
import ZkVerifier from '../verification/ZkVerifier.js';
import { extractCitationsFromDoi, extractMetricsFromDoi } from '../../ai/utils/citationExtractor.js';
import ZkVerifier from '../verification/ZkVerifier.js';
import { extractCitationsFromDoi, extractMetricsFromDoi } from '../../ai/utils/citationExtractor.js';
import ZkVerifier from '../verification/ZkVerifier.js';
import { extractCitationsFromDoi, extractMetricsFromDoi } from '../../ai/utils/citationExtractor.js';
import ZkVerifier from '../verification/ZkVerifier.js';
import { extractCitationsFromDoi, extractMetricsFromDoi } from '../../ai/utils/citationExtractor.js';
import ZkVerifier from '../verification/ZkVerifier.js';
import { extractCitationsFromDoi, extractMetricsFromDoi } from '../../ai/utils/citationExtractor.js';
import ZkVerifier from '../verification/ZkVerifier.js';

/**
 * PID-Manager für dezentrale Wissenschafts-IDs
 * Unterstützt DOI, ORCID, Handles und NFT-Konversion
 */
class PidManager {
  constructor() {
    // DOI-Präfix für DeSci-Scholar
    this.doiPrefix = process.env.DOI_PREFIX || '10.21249';

    // Handle-System-Konfiguration
    this.handlePrefix = process.env.HANDLE_PREFIX || '20.500.12345';
    this.handleProxyUrl = 'https://hdl.handle.net';
    this.handleApiUrl = 'https://hdl.handle.net/api/handles';

    // Initialisiere den Zero-Knowledge-Verifizierer
    this.zkVerifier = new ZkVerifier();

    // Initialisiere den Zero-Knowledge-Verifizierer
    this.zkVerifier = new ZkVerifier();

    // Initialisiere den Zero-Knowledge-Verifizierer
    this.zkVerifier = new ZkVerifier();
    
    // DOI-Provider-Konfiguration
    this.doiProviders = {
      crossref: {
        apiUrl: 'https://api.crossref.org',
        contentTypes: ['article', 'book', 'chapter', 'conference', 'preprint'],
        depositorName: process.env.CROSSREF_DEPOSITOR || 'DeSci Scholar',
        username: process.env.CROSSREF_USERNAME,
        password: process.env.CROSSREF_PASSWORD
      },
      datacite: {
        apiUrl: 'https://api.datacite.org',
        contentTypes: ['dataset', 'software', 'workflow', 'protocol'],
        repositoryPrefix: process.env.DATACITE_PREFIX || '10.XXXXX',
        username: process.env.DATACITE_USERNAME,
        password: process.env.DATACITE_PASSWORD
      }
    };

    // ORCID-Konfiguration
    this.orcidConfig = {
      apiUrl: 'https://pub.orcid.org/v3.0',
      clientId: process.env.ORCID_CLIENT_ID,
      clientSecret: process.env.ORCID_CLIENT_SECRET,
      redirectUri: process.env.ORCID_REDIRECT_URI || 'https://desci-scholar.org/orcid/callback'
    };
    
    // Blockchain-Konfiguration für NFTs
    this.blockchainConfig = {
      provider: process.env.BLOCKCHAIN_PROVIDER || 'ethereum',
      network: process.env.BLOCKCHAIN_NETWORK || 'goerli',
      contractAddress: process.env.NFT_CONTRACT_ADDRESS,
      apiKey: process.env.BLOCKCHAIN_API_KEY
    };
  }

  /**
   * Generiert eine neue DOI für eine Publikation
   * @param {Object} publication - Publikationsdaten
   * @returns {string} - Generierte DOI
   */
  generateDoi(publication) {
    // Publikationstyp bestimmen für die richtige DOI-Agency
    const contentType = publication.type || 'article';
    const provider = this.getDoiProviderForContentType(contentType);
    const prefix = provider === 'datacite' 
      ? this.doiProviders.datacite.repositoryPrefix 
      : this.doiPrefix;
    
    // Deterministischen Hash aus den Publikationsdaten erstellen
    const hash = crypto
      .createHash('sha256')
      .update(JSON.stringify({
        title: publication.title,
        authors: publication.authors,
        timestamp: Date.now()
      }))
      .digest('hex')
      .substring(0, 8);

    // DOI Format: 10.21249/desci.YYYY.hash
    const year = new Date().getFullYear();
    return `${prefix}/desci.${year}.${hash}`;
  }

  /**
   * Bestimmt den richtigen DOI-Provider basierend auf dem Content-Typ
   * @private
   */
  getDoiProviderForContentType(contentType) {
    // Prüfen, ob der Content-Typ zu DataCite gehört
    if (this.doiProviders.datacite.contentTypes.includes(contentType)) {
      return 'datacite';
    }
    // Standard ist Crossref
    return 'crossref';
  }

  /**
   * Validiert eine DOI nach ISO 26324
   * @param {string} doi - DOI zur Validierung
   * @returns {boolean} - Validierungsergebnis
   */
  validateDoi(doi) {
    // DOI-Format nach ISO 26324
    const doiRegex = /^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/;
    return doiRegex.test(doi);
  }

  /**
   * Validiert eine ORCID nach ORCID-Standard
   * @param {string} orcid - ORCID zur Validierung
   * @returns {boolean} - Validierungsergebnis
   */
  validateOrcid(orcid) {
    // ORCID-Format: 0000-0002-1825-0097
    const orcidRegex = /^\d{4}-\d{4}-\d{4}-\d{3}[\dX]$/;
    if (!orcidRegex.test(orcid)) {
      return false;
    }

    // Prüfziffer validieren
    const digits = orcid.replace(/-/g, '').split('');
    const checkDigit = digits.pop();
    
    let total = 0;
    digits.forEach((digit) => {
      total = (total + parseInt(digit)) * 2;
    });
    
    const remainder = total % 11;
    const result = (12 - remainder) % 11;
    const expectedCheckDigit = result === 10 ? 'X' : result.toString();
    
    return checkDigit === expectedCheckDigit;
  }

  /**
   * Ruft Metadaten zu einer DOI von Crossref oder DataCite ab
   * @param {string} doi - DOI
   * @returns {Promise<Object>} - DOI-Metadaten
   */
  async getDoiMetadata(doi) {
    try {
      // DOI validieren
      if (!this.validateDoi(doi)) {
        throw new Error('Ungültige DOI');
      }
      
      // DOI-Provider bestimmen
      const isDataCite = doi.startsWith(this.doiProviders.datacite.repositoryPrefix);
      
      // API-URL zusammenstellen
      const apiUrl = isDataCite
        ? `${this.doiProviders.datacite.apiUrl}/dois/${encodeURIComponent(doi)}`
        : `https://api.crossref.org/works/${encodeURIComponent(doi)}`;
      
      // HTTP-Anfrage
      const response = await fetch(apiUrl, {
        headers: {
          'Accept': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error(`DOI-Metadaten konnten nicht abgerufen werden: ${response.status}`);
      }
      
      const data = await response.json();
      
      // Metadaten standardisieren
      return this.standardizeDoiMetadata(data, isDataCite);
    } catch (error) {
      throw new Error(`DOI-Metadatenabruf fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Standardisiert DOI-Metadaten von verschiedenen Providern mit erweiterter Metadaten-Anreicherung
   * @private
   */
  standardizeDoiMetadata(rawData, isDataCite) {
    // Extrahiert und standardisiert die Metadaten je nach Provider
    if (isDataCite) {
      // DataCite-Metadaten verarbeiten
      const data = rawData.data || {};
      return {
        doi: data.id,
        title: data.attributes?.title,
        authors: (data.attributes?.creators || []).map(creator => ({
          name: creator.name,
          orcid: creator.nameIdentifiers?.find(id => id.nameIdentifierScheme === 'ORCID')?.nameIdentifier

        publicationYear: data.attributes?.publicationYear,
        publisher: data.attributes?.publisher,
        resourceType: data.attributes?.resourceType?.resourceTypeGeneral,
        url: data.attributes?.url

      // Crossref-Metadaten verarbeiten
      const data = rawData.message || {};
      return
        doi: data.DOI,
        title: Array.isArray(data.title) ? data.title[0] : data.title,
        authors: (data.author || []).map(author => ({
    name: `${author.given || ''} ${author.family || ''}`.trim(),
          orcid: author.ORCID

        publicationYear: data.published?.['date-parts']?.[0]?.[0],
        publisher: data.publisher
        resourceType: data.type,
        url: data.URL || `https://doi.org/${data.DOI}`

    }
  }

  /**
   * Ruft ORCID-Profildaten ab mit erweiterter Metadaten-Anreicherung
   * @param {string} orcid - ORCID
   * @returns {Promise<Object>} - ORCID-Profil
   */
  async getOrcidProfile(or
      // ORCID validieren
      if (!this.validateOrcid(orcid)     throw new Error('Ungültige ORCID');

      // ORCID API-Anfrage
      const response = await fetch(`${this.orcidConfig.apiUrl}/${orcid}/recor
        headers: {
    'Accept': 'application/json'
  

      if (!response.ok) {
        throw new Error(`ORCID-Profil konnte nicht abgerufen werden: ${response.status}`);

      const data = await response.json();

      // Profildaten extrahieren
     
orcid: data.orcid,
name: `${data.person?.name?.['given-names']?.value || ''} ${data.person?.name?.['family-name']?.value || ''}`.trim(),
        affiliations: (data.person?.employments?.['affiliation-group'] || []).map(affiliation => ({
    organization: affiliation?.summaries?.[0]?.['employment-summary']?.organization?.name,
          role: affiliation?.summaries?.[0]?.['employment-summary']?.['role-title'],
    startDate: affiliation?.summaries?.[0]?.['employment-summary']?.['start-date']

        works: (data.activities?.works?.['work-summary'] || []).map(work =>
          title: work?.title?.title?.value,
          type: work?.type,
          publicationYear: work?.['publication-date']?.year?.value,
    doi: work?.['external-ids']?.['external-id']?.find(id => id['external-id-type'] === 'doi')?.['external-id-value']

    } catch (error) {
      throw new Error(`ORCID-Profilabruf fehlgeschlagen: ${error.message}`);

  }

  /**
   * Konvertiert eine DOI zu einem NFT mit erweiterter Metadaten-Anreicherung
   * @param {string} doi - DOI der Publikation
   * @param {Object} options - Optionen für die NFT-Erstellung
   * @returns {Promise<Object>} - NFT-Metadaten und Transaktionsinformationen
   */
  async convertDoiToNft(doi, options = {})
 {
etadaten abrufen und validiere
      const doiMetadata = await this.getDoiMetadata(doi);

      6if (!doiMetadata) {
        throw new Error('DOI-Metadaten konnten nicht abgerufen werden');

      // 2. Autorschaft über ORCID validieren (falls angegeben)
      const validatedAuthors = [];

      if (options.requireOrcidValidation) {
        for (const author of doiMetadata.authors) {,
          journal: doiMetadata.journal,
          keywords: doiMetadata.keywords || [],
    // Erweiterte Metadaten
          citations: citationsData.citations || [],
          references: citationsData.references || [],
          metrics: {
            citation_count: citationsData.citations ? citationsData.citations.length : 0,
            reference_count: citationsData.references ? citationsData.references.length : 0,
            altmetric: metricsData.altmetric || null,
            dimensions: metricsData.dimensions || null
            i          title: doiMetadata.title,
          abstract: doiMetadata.abstract,
          title: doiMetadata.title,
          abstract: doiMetadata.abstract,
f (author.orcid) {
const orcidProfile = await this.getOrcidProfile(author.orcid);

// Prüfen, ob das Werk im ORCID-Profil vorkommt
            const workInProfile = orcidProfile.works.some(work =>
              {
          {
          {
            trait_type: 'CitationCount',
            value: citationsData.citations ? citationsData.citations.length : 0
          },
          {
            trait_type: 'ReferenceCount',
            value: citationsData.references ? citationsData.references.length
          },
      8      trait_type: 'CitationCount',
      value: citationsData.citations ? citationsData.citations.leng
          },
          {
      trait_type: 'ReferenceCount',
      value: citationsData.references ? citationsData.references.length : 0
            },
       // Blockchain-spezifische Metadaten
  blockchain:
          timestamp: Date.now(),
    version: '1.0',
    standard: 'DeSci-Scholar-v1',
          network: process.env.BLOCKCHAIN_NETWORK || 'polkadot'

      };
       trait_type: 'CitationCount',
      value: citationsData.citations ? citationsData.citations.length
          },
          {
 8     trait_type: 'ReferenceCount',
      value: citationsData.references ? citationsData.references.length : 0
        },      // Blockchain-spezifische Metadaten
  blockchain
          timestamp: Date.now(),
    version: '1.0',
          standard: 'DeSci-Scholar-v1',
          network: process.env.BLOCKCHAIN_NETWORK || 'polkadot'
        }
      };
},
          title: doiMetadata.title,,
          journal: doiMetadata.journal,
          keywords: doiMetadata.keywords || [],
          // Erweiterte Metadaten
          citations: citationsData.citations || [],
          references: citationsData.references || [],
          metrics: {
            citation_count: citationsData.citations ? citationsData.citations.length : 0,
            reference_count: citationsData.references ? citationsData.references.length : 0,
            altmetric: metricsData.altmetric || null,
      dimensions: metricsData.dimensions ||   },
         // Blockchain-spezifische Metadaten
        blockchain: {
          timestamp: Date.now(),
          version: '1.0',
          standard: 'DeSci-Scholar-v1',
          network: process.env.BLOCKCHAIN_NETWORK || 'polkadot'

      };
     abstract: doiMetadata.abstract,
    work.doi === doi || work.title === doiMetadata.title,
          journal: doiMetadata.journal,
          keywords: doiMetadata.keywords      // Erweiterte Metadaten
    citations: citationsData.citations || [],
        8  references: citationsData.references || [],
          metrics: {
            citation_count: citationsData.citations ? citationsData.citations.length : 0,
      reference_count: citationsData.references ? citationsData.references.length : 0,
            altmetric: metricsData.altmetric || null,
            dimensions: metricsData.dimensions || null
              );

validatedAuthors.push({
              ...author,,
        citations: {
          count: citationsData.citations ? citationsData.citations.length : 0,
          source: citationsData.source || 'none'
        },
        metrics: {
          altmetric: metricsData.altmetric ? true : false,
          dimensions: metricsData.dimensions ? true : false
        }
      };
    } catch (error) {
      console.error('DOI-zu-NFT-Konvertierung fehlgeschlagen:', error);
      throw new Error(`DOI-zu-NFT-Konvertierung fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Speichert NFT-Metadaten in IPFS
   * @private
   * @param {Object} metadata - Die zu speichernden Metadaten
   * @returns {Promise<string>} - Die IPFS Content ID (CID)
   */
  async storeNftMetadataToIpfs(metadata) {
    try {
      // Importiere den IpfsManager
      const IpfsManager = require('../storage/IpfsManager').default;

      // Konfiguration aus der Umgebung oder Standardwerten
      const ipfsConfig = {
        host: process.env.IPFS_HOST || 'ipfs.infura.io',
        port: process.env.IPFS_PORT || 5001,
        protocol: process.env.IPFS_PROTOCOL || 'https',
        auth: process.env.IPFS_AUTH || ''
      };

      // Initialisiere IPFS Manager
      const ipfsManager = new IpfsManager(ipfsConfig);

      // Speichere Metadaten auf IPFS
      const cid = await ipfsManager.storeMetadata(metadata);

      // Pinne die Metadaten für dauerhafte Speicherung
      await ipfsManager.pinMetadata(cid);

      // Gib die vollständige IPFS-URI zurück
      return ipfsManager.createIpfsLink(cid);
    } catch (error) {
      console.error('Fehler beim Speichern der Metadaten auf IPFS:', error);
      throw new Error(`IPFS-Speicherung fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Prägt ein NFT auf der Polkadot-Blockchain
   * @private
   * @param {string} metadataUri - URI der Metadaten (IPFS-Link)
   * @param {string} recipient - Empfänger-Adresse
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async mintNft(metadataUri, recipient) {
    try {
      // Importiere die Polkadot-Integration
      const { ApiPromise, WsProvider, Keyring } = require('@polkadot/api');
      const { cryptoWaitReady } = require('@polkadot/util-crypto');

      // Importiere die benötigten Contracts
      const DoiNftContract = require('../contracts/DoiNftContract').default;
      const DoiRoyaltyContract = require('../contracts/DoiRoyaltyContract').default;

      // Konfiguration aus der Umgebung oder Standardwerten
      const nodeUrl = process.env.POLKADOT_NODE_URL || 'wss://rpc.polkadot.io';
      const contractAddress = process.env.NFT_CONTRACT_ADDRESS;

      if (!contractAddress) {
        throw new Error('NFT-Vertragsadresse nicht konfiguriert');
      }

      // Warte auf die Initialisierung der Kryptographie
      await cryptoWaitReady();

      // Verbinde mit dem Polkadot-Netzwerk
      const provider = new WsProvider(nodeUrl);
      const api = await ApiPromise.create({ provider });

      // Initialisiere den Keyring für die Signatur
      const keyring = new Keyring({ type: 'sr25519' });

      // Verwende den konfigurierten Schlüssel oder einen Standardschlüssel für Tests,
        citations: {
          count: citationsData.citations ? citationsData.citations.length : 0,
          source: citationsData.source || 'none'
        },
        metrics: {
          altmetric: metricsData.altmetric ? true : false,
          dimensions: metricsData.dimensions ? true : false
        }
      };
    } catch (error) {
      console.error('DOI-zu-NFT-Konvertierung fehlgeschlagen:', error);
      throw new Error(`DOI-zu-NFT-Konvertierung fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Speichert NFT-Metadaten in IPFS
   * @private
   * @param {Object} metadata - Die zu speichernden Metadaten
   * @returns {Promise<string>} - Die IPFS Content ID (CID)
   */
  async storeNftMetadataToIpfs(metadata) {
    try {
      // Importiere den IpfsManager
      const IpfsManager = require('../storage/IpfsManager').default;

      // Konfiguration aus der Umgebung oder Standardwerten
      const ipfsConfig = {
        host: process.env.IPFS_HOST || 'ipfs.infura.io',
        port: process.env.IPFS_PORT || 5001,
        protocol: process.env.IPFS_PROTOCOL || 'https',
        auth: process.env.IPFS_AUTH || ''
      };

      // Initialisiere IPFS Manager
      const ipfsManager = new IpfsManager(ipfsConfig);

      // Speichere Metadaten auf IPFS
      const cid = await ipfsManager.storeMetadata(metadata);

      // Pinne die Metadaten für dauerhafte Speicherung
      await ipfsManager.pinMetadata(cid);

      // Gib die vollständige IPFS-URI zurück
      return ipfsManager.createIpfsLink(cid);
    } catch (error) {
      console.error('Fehler beim Speichern der Metadaten auf IPFS:', error);
      throw new Error(`IPFS-Speicherung fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Prägt ein NFT auf der Polkadot-Blockchain
   * @private
   * @param {string} metadataUri - URI der Metadaten (IPFS-Link)
   * @param {string} recipient - Empfänger-Adresse
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async mintNft(metadataUri, recipient) {
    try {
      // Importiere die Polkadot-Integration
      const { ApiPromise, WsProvider, Keyring } = require('@polkadot/api');
      const { cryptoWaitReady } = require('@polkadot/util-crypto');

      // Importiere die benötigten Contracts
      const DoiNftContract = require('../contracts/DoiNftContract').default;
      const DoiRoyaltyContract = require('../contracts/DoiRoyaltyContract').default;

      // Konfiguration aus der Umgebung oder Standardwerten
      const nodeUrl = process.env.POLKADOT_NODE_URL || 'wss://rpc.polkadot.io';
      const contractAddress = process.env.NFT_CONTRACT_ADDRESS;

      if (!contractAddress) {
        throw new Error('NFT-Vertragsadresse nicht konfiguriert');
      }

      // Warte auf die Initialisierung der Kryptographie
      await cryptoWaitReady();

      // Verbinde mit dem Polkadot-Netzwerk
      const provider = new WsProvider(nodeUrl);
      const api = await ApiPromise.create({ provider });

      // Initialisiere den Keyring für die Signatur
      const keyring = new Keyring({ type: 'sr25519' });

      // Verwende den konfigurierten Schlüssel oder einen Standardschlüssel für Tests
      const signerSeed = process.env.POLKADOT_SIGNER_SEED;
      if (!signerSeed) {
        throw new Error('Kein Signaturschlüssel konfiguriert');
      }

      const signer = keyring.addFromUri(signerSeed);

      // Initialisiere den NFT-Vertrag
      const nftContract = new DoiNftContract(api, contractAddress, {
        ipfs: {
          host: process.env.IPFS_HOST || 'ipfs.infura.io',
          port: process.env.IPFS_PORT || 5001,
          protocol: process.env.IPFS_PROTOCOL || 'https',
          auth: process.env.IPFS_AUTH || ''
        }
      });

      // Erstelle die Publikationsdaten aus den Metadaten
      const publicationData = {
        metadataUri,
        recipient
      };

      // Präge das NFT
      const mintResult = await nftContract.mint(publicationData);

      // Signiere und sende die Transaktion
      const tx = await mintResult.signAndSend(signer);

      // Warte auf die Bestätigung der Transaktion
      return new Promise((resolve, reject) => {
        tx.on('transactionHash', (hash) => {
          console.log('Transaktion gesendet:', hash);
        })
        .on('receipt', (receipt) => {
          console.log('Transaktion bestätigt:', receipt.transactionHash);
          resolve({
            transaction: receipt.transactionHash,
            tokenId: receipt.events.DoiNftMinted.returnValues.tokenId,
            owner: recipient,
            metadataUri
          });
        })
        .on('error', (error) => {
          console.error('Fehler beim Minting:', error);
          reject(error);
        });
      });
    } catch (error) {
      console.error('Fehler beim NFT-Minting:', error);
      throw new Error(`NFT-Minting fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Verifiziert ein bestehendes DOI-NFT
   * @param {string} tokenId - NFT-Token-ID
   * @returns {Promise<Object>} - Verifizierungsergebnis
   */
  async verifyDoiNft(tokenId) {
    try {
      // 1. NFT-Metadaten abrufen (Mock-Implementation)
      const nftData = await this.getNftMetadata(tokenId);
      
      if (!nftData || !nftData.scientific_metadata?.doi) {
        throw new Error('NFT enthält keine DOI-Informationen');
      }
      
      // 2. DOI-Metadaten abrufen
      const doi = nftData.scientific_metadata.doi;
      const doiMetadata = await this.getDoiMetadata(doi);
      
      // 3. Metadaten vergleichen
      const doiAuthors = doiMetadata.authors.map(a => a.name).sort();
      const nftAuthors = nftData.scientific_metadata.authors.map(a => a.name).sort();
      
      const metadataMatch = 
        doiMetadata.title === nftData.name &&
        doiMetadata.publicationYear === nftData.scientific_metadata.publication_year &&
        JSON.stringify(doiAuthors) === JSON.stringify(nftAuthors);
      
      // 4. ORCID-Validierung
      const orcidValidation = nftData.scientific_metadata.authors
        .filter(a => a.orcid && a.orcidValidated)
        .length > 0;
      
      return {
        tokenId,
        doi,
        verified: metadataMatch,
        orcidValidated: orcidValidation,
        owner: nftData.owner,
        metadataMatch,
        doiMetadata,
        nftMetadata: nftData
      };
    } catch (error) {
      throw new Error(`NFT-Verifizierung fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Ruft NFT-Metadaten von der Blockchain und IPFS ab
   * @private
   * @param {string} tokenId - Die Token-ID des NFTs
   * @returns {Promise<Object>} - Die vollständigen Metadaten
   */
  async getNftMetadata(tokenId) {
    try {
      // Importiere die Polkadot-Integration
      const { ApiPromise, WsProvider } = require('@polkadot/api');

      // Importiere die benötigten Contracts und Manager
      const DoiNftContract = require('../contracts/DoiNftContract').default;
      const DoiRoyaltyContract = require('../contracts/DoiRoyaltyContract').default;
      const IpfsManager = require('../storage/IpfsManager').default;

      // Konfiguration aus der Umgebung oder Standardwerten
      const nodeUrl = process.env.POLKADOT_NODE_URL || 'wss://rpc.polkadot.io';
      const contractAddress = process.env.NFT_CONTRACT_ADDRESS;

      if (!contractAddress) {
        throw new Error('NFT-Vertragsadresse nicht konfiguriert');
      }

      // Verbinde mit dem Polkadot-Netzwerk
      const provider = new WsProvider(nodeUrl);
      const api = await ApiPromise.create({ provider });

      // Initialisiere den NFT-Vertrag
      const nftContract = new DoiNftContract(api, contractAddress, {
        ipfs: {
          host: process.env.IPFS_HOST || 'ipfs.infura.io',
          port: process.env.IPFS_PORT || 5001,
          protocol: process.env.IPFS_PROTOCOL || 'https',
          auth: process.env.IPFS_AUTH || ''
        }
      });

      // Rufe die Token-Informationen vom Smart Contract ab
      const tokenInfo = await nftContract.getTokenInfo(tokenId);

      // Extrahiere die IPFS-URI
      const metadataUri = tokenInfo.metadata;

      // Wenn die Metadaten direkt im Smart Contract gespeichert sind
      if (!metadataUri.startsWith('ipfs://')) {
        return JSON.parse(metadataUri);
      }

      // Extrahiere die IPFS-CID
      const cid = metadataUri.replace('ipfs://', '');

      // Initialisiere den IPFS-Manager
      const ipfsManager = new IpfsManager({
        host: process.env.IPFS_HOST || 'ipfs.infura.io',
        port: process.env.IPFS_PORT || 5001,
        protocol: process.env.IPFS_PROTOCOL || 'https',
        auth: process.env.IPFS_AUTH || ''
      });

      // Rufe die Metadaten von IPFS ab
      const metadata = await ipfsManager.getMetadata(cid);

      // Füge Blockchain-spezifische Informationen hinzu
      return {
        ...metadata,
        tokenId,
        owner: tokenInfo.owner,
        ipfs: {
          cid,
          url: ipfsManager.getHttpUrl(cid)
        }
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der NFT-Metadaten:', error);
      throw new Error(`NFT-Metadatenabruf fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Setzt Lizenz- und Royalty-Informationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} licenseInfo - Lizenzinformationen
   * @param {Object} royaltyInfo - Royalty-Informationen
   * @returns {Promise<Object>} - Ergebnis der Operation
   */
  async setNftLicenseAndRoyalty(tokenId, licenseInfo, royaltyInfo) {
    try {
      // Importiere die Polkadot-Integration
      const { ApiPromise, WsProvider, Keyring } = require('@polkadot/api');
      const { cryptoWaitReady } = require('@polkadot/util-crypto');

      // Importiere den DoiRoyaltyContract
      const DoiRoyaltyContract = require('../contracts/DoiRoyaltyContract').default;

      // Konfiguration aus der Umgebung oder Standardwerten
      const nodeUrl = process.env.POLKADOT_NODE_URL || 'wss://rpc.polkadot.io';
      const contractAddress = process.env.ROYALTY_CONTRACT_ADDRESS || process.env.NFT_CONTRACT_ADDRESS;

      if (!contractAddress) {
        throw new Error('Vertragsadresse nicht konfiguriert');
      }

      // Warte auf die Initialisierung der Kryptographie
      await cryptoWaitReady();

      // Verbinde mit dem Polkadot-Netzwerk
      const provider = new WsProvider(nodeUrl);
      const api = await ApiPromise.create({ provider });

      // Initialisiere den Keyring für die Signatur
      const keyring = new Keyring({ type: 'sr25519' });

      // Verwende den konfigurierten Schlüssel oder einen Standardschlüssel für Tests
      const signerSeed = process.env.POLKADOT_SIGNER_SEED;
      if (!signerSeed) {
        throw new Error('Kein Signaturschlüssel konfiguriert');
      }

      const signer = keyring.addFromUri(signerSeed);

      // Initialisiere den Royalty-Vertrag
      const royaltyContract = new DoiRoyaltyContract(api, contractAddress);

      // Setze Lizenzinformationen
      const licenseResult = await royaltyContract.setLicenseInfo(tokenId, licenseInfo);

      // Setze Royalty-Informationen, falls angegeben
      let royaltyResult = null;
      if (royaltyInfo && royaltyInfo.recipients && royaltyInfo.shares) {
        royaltyResult = await royaltyContract.setRoyaltyInfo(
          tokenId,
          royaltyInfo.recipients,
          royaltyInfo.shares
        );
      }

      return {
        tokenId,
        license: licenseResult.license,
        royalty: royaltyResult ? {
          recipients: royaltyResult.recipients,
          shares: royaltyResult.shares,
          royaltyRate: royaltyResult.royaltyRate
        } : null,
        transactions: {
          license: licenseResult.transaction,
          royalty: royaltyResult ? royaltyResult.transaction : null
        }
      };
    } catch (error) {
      console.error('Fehler beim Setzen von Lizenz- und Royalty-Informationen:', error);
      throw new Error(`Lizenz- und Royalty-Konfiguration fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Generiert einen Zero-Knowledge-Beweis für die Autorschaft einer Publikation
   * @param {string} doi - DOI der Publikation
   * @param {Object} author - Autorschaftsdaten
   * @param {string} privateKey - Privater Schlüssel des Autors
   * @returns {Promise<Object>} - Zero-Knowledge-Beweis
   */
  async generateAuthorshipProof(doi, author, privateKey) {
    try {
      // 1. Rufe die Publikationsdaten ab
      const publication = await this.getDoiMetadata(doi);

      if (!publication) {
        throw new Error('Publikationsdaten konnten nicht abgerufen werden');
      }

      // 2. Generiere den Beweis
      const proof = this.zkVerifier.generateAuthorshipProof(publication, author, privateKey);

      // 3. Speichere den Beweis auf IPFS
      const proofJson = JSON.stringify(proof);
      const ipfsCid = await this.storeNftMetadataToIpfs({ proof });

      return {
        doi,
        authorId: author.id,
        proof,
        ipfsCid,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Autorschaftsbeweises:', error);
      throw new Error(`Autorschaftsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }

  /**
   * Verifiziert einen Autorschaftsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {string} doi - DOI der Publikation
   * @param {string} publicKey - Öffentlicher Schlüssel des Autors
   * @returns {Promise<Object>} - Verifizierungsergebnis
   */
  async verifyAuthorshipProof(proof, doi, publicKey) {
    try {
      // 1. Rufe die Publikationsdaten ab
      const publication = await this.getDoiMetadata(doi);

      if (!publication) {
        throw new Error('Publikationsdaten konnten nicht abgerufen werden');
      }

      // 2. Verifiziere den Beweis
      const isValid = this.zkVerifier.verifyAuthorshipProof(proof, publication, publicKey);

      return {
        doi,
        authorId: proof.authorshipClaim.authorId,
        isValid,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Autorschaftsbeweises:', error);
      return {
        doi,
        isValid: false,
        error: error.message,
        timestamp: Date.now()
      };
    }
  }

  /**
   * Generiert einen Zero-Knowledge-Beweis für die Integrität von Forschungsdaten
   * @param {string} doi - DOI der Publikation
   * @param {Object} data - Forschungsdaten
   * @param {string} privateKey - Privater Schlüssel
   * @returns {Promise<Object>} - Zero-Knowledge-Beweis
   */
  async generateDataIntegrityProof(doi, data, privateKey) {
    try {
      // 1. Generiere den Beweis
      const proof = this.zkVerifier.generateDataIntegrityProof(data, privateKey);

      // 2. Speichere den Beweis auf IPFS
      const proofJson = JSON.stringify(proof);
      const ipfsCid = await this.storeNftMetadataToIpfs({ proof });

      return {
        doi,
        dataHash: proof.dataHash,
        proof,
        ipfsCid,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Datenintegritätsbeweises:', error);
      throw new Error(`Datenintegritätsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }

  /**
   * Verifiziert einen Datenintegritätsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} data - Forschungsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel
   * @returns {Promise<Object>} - Verifizierungsergebnis
   */
  async verifyDataIntegrityProof(proof, data, publicKey) {
    try {
      // Verifiziere den Beweis
      const isValid = this.zkVerifier.verifyDataIntegrityProof(proof, data, publicKey);

      return {
        dataHash: proof.dataHash,
        isValid,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Datenintegritätsbeweises:', error);
      return {
        isValid: false,
        error: error.message,
        timestamp: Date.now()
      };
    }
      const signerSeed = process.env.POLKADOT_SIGNER_SEED;
      if (!signerSeed) {
        throw new Error('Kein Signaturschlüssel konfiguriert');
      }

      const signer = keyring.addFromUri(signerSeed);

      // Initialisiere den NFT-Vertrag
      const nftContract = new DoiNftContract(api, contractAddress, {
        ipfs: {
          host: process.env.IPFS_HOST || 'ipfs.infura.io',
          port: process.env.IPFS_PORT || 5001,
          protocol: process.env.IPFS_PROTOCOL || 'https',
          auth: process.env.IPFS_AUTH || ''
        }
      });

      // Erstelle die Publikationsdaten aus den Metadaten
      const publicationData = {
        metadataUri,
        recipient
      };

      // Präge das NFT
      const mintResult = await nftContract.mint(publicationData);

      // Signiere und sende die Transaktion
      const tx = await mintResult.signAndSend(signer);

      // Warte auf die Bestätigung der Transaktion
      return new Promise((resolve, reject) => {
        tx.on('transactionHash', (hash) => {
          console.log('Transaktion gesendet:', hash);
        })
        .on('receipt', (receipt) => {
          console.log('Transaktion bestätigt:', receipt.transactionHash);
          resolve({
            transaction: receipt.transactionHash,
            tokenId: receipt.events.DoiNftMinted.returnValues.tokenId,
            owner: recipient,
            metadataUri
          });
        })
        .on('error', (error) => {
          console.error('Fehler beim Minting:', error);
          reject(error);
        });
      });
    } catch (error) {
      console.error('Fehler beim NFT-Minting:', error);
      throw new Error(`NFT-Minting fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Verifiziert ein bestehendes DOI-NFT
   * @param {string} tokenId - NFT-Token-ID
   * @returns {Promise<Object>} - Verifizierungsergebnis
   */
  async verifyDoiNft(tokenId) {
    try {
      // 1. NFT-Metadaten abrufen (Mock-Implementation)
      const nftData = await this.getNftMetadata(tokenId);
      
      if (!nftData || !nftData.scientific_metadata?.doi) {
        throw new Error('NFT enthält keine DOI-Informationen');
      }
      
      // 2. DOI-Metadaten abrufen
      const doi = nftData.scientific_metadata.doi;
      const doiMetadata = await this.getDoiMetadata(doi);
      
      // 3. Metadaten vergleichen
      const doiAuthors = doiMetadata.authors.map(a => a.name).sort();
      const nftAuthors = nftData.scientific_metadata.authors.map(a => a.name).sort();
      
      const metadataMatch = 
        doiMetadata.title === nftData.name &&
        doiMetadata.publicationYear === nftData.scientific_metadata.publication_year &&
        JSON.stringify(doiAuthors) === JSON.stringify(nftAuthors);
      
      // 4. ORCID-Validierung
      const orcidValidation = nftData.scientific_metadata.authors
        .filter(a => a.orcid && a.orcidValidated)
        .length > 0;
      
      return {
        tokenId,
        doi,
        verified: metadataMatch,
        orcidValidated: orcidValidation,
        owner: nftData.owner,
        metadataMatch,
        doiMetadata,
        nftMetadata: nftData
      };
    } catch (error) {
      throw new Error(`NFT-Verifizierung fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Ruft NFT-Metadaten von der Blockchain und IPFS ab
   * @private
   * @param {string} tokenId - Die Token-ID des NFTs
   * @returns {Promise<Object>} - Die vollständigen Metadaten
   */
  async getNftMetadata(tokenId) {
    try {
      // Importiere die Polkadot-Integration
      const { ApiPromise, WsProvider } = require('@polkadot/api');

      // Importiere die benötigten Contracts und Manager
      const DoiNftContract = require('../contracts/DoiNftContract').default;
      const DoiRoyaltyContract = require('../contracts/DoiRoyaltyContract').default;
      const IpfsManager = require('../storage/IpfsManager').default;

      // Konfiguration aus der Umgebung oder Standardwerten
      const nodeUrl = process.env.POLKADOT_NODE_URL || 'wss://rpc.polkadot.io';
      const contractAddress = process.env.NFT_CONTRACT_ADDRESS;

      if (!contractAddress) {
        throw new Error('NFT-Vertragsadresse nicht konfiguriert');
      }

      // Verbinde mit dem Polkadot-Netzwerk
      const provider = new WsProvider(nodeUrl);
      const api = await ApiPromise.create({ provider });

      // Initialisiere den NFT-Vertrag
      const nftContract = new DoiNftContract(api, contractAddress, {
        ipfs: {
          host: process.env.IPFS_HOST || 'ipfs.infura.io',
          port: process.env.IPFS_PORT || 5001,
          protocol: process.env.IPFS_PROTOCOL || 'https',
          auth: process.env.IPFS_AUTH || ''
        }
      });

      // Rufe die Token-Informationen vom Smart Contract ab
      const tokenInfo = await nftContract.getTokenInfo(tokenId);

      // Extrahiere die IPFS-URI
      const metadataUri = tokenInfo.metadata;

      // Wenn die Metadaten direkt im Smart Contract gespeichert sind
      if (!metadataUri.startsWith('ipfs://')) {
        return JSON.parse(metadataUri);
      }

      // Extrahiere die IPFS-CID
      const cid = metadataUri.replace('ipfs://', '');

      // Initialisiere den IPFS-Manager
      const ipfsManager = new IpfsManager({
        host: process.env.IPFS_HOST || 'ipfs.infura.io',
        port: process.env.IPFS_PORT || 5001,
        protocol: process.env.IPFS_PROTOCOL || 'https',
        auth: process.env.IPFS_AUTH || ''
      });

      // Rufe die Metadaten von IPFS ab
      const metadata = await ipfsManager.getMetadata(cid);

      // Füge Blockchain-spezifische Informationen hinzu
      return {
        ...metadata,
        tokenId,
        owner: tokenInfo.owner,
        ipfs: {
          cid,
          url: ipfsManager.getHttpUrl(cid)
        }
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der NFT-Metadaten:', error);
      throw new Error(`NFT-Metadatenabruf fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Setzt Lizenz- und Royalty-Informationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} licenseInfo - Lizenzinformationen
   * @param {Object} royaltyInfo - Royalty-Informationen
   * @returns {Promise<Object>} - Ergebnis der Operation
   */
  async setNftLicenseAndRoyalty(tokenId, licenseInfo, royaltyInfo) {
    try {
      // Importiere die Polkadot-Integration
      const { ApiPromise, WsProvider, Keyring } = require('@polkadot/api');
      const { cryptoWaitReady } = require('@polkadot/util-crypto');

      // Importiere den DoiRoyaltyContract
      const DoiRoyaltyContract = require('../contracts/DoiRoyaltyContract').default;

      // Konfiguration aus der Umgebung oder Standardwerten
      const nodeUrl = process.env.POLKADOT_NODE_URL || 'wss://rpc.polkadot.io';
      const contractAddress = process.env.ROYALTY_CONTRACT_ADDRESS || process.env.NFT_CONTRACT_ADDRESS;

      if (!contractAddress) {
        throw new Error('Vertragsadresse nicht konfiguriert');
      }

      // Warte auf die Initialisierung der Kryptographie
      await cryptoWaitReady();

      // Verbinde mit dem Polkadot-Netzwerk
      const provider = new WsProvider(nodeUrl);
      const api = await ApiPromise.create({ provider });

      // Initialisiere den Keyring für die Signatur
      const keyring = new Keyring({ type: 'sr25519' });

      // Verwende den konfigurierten Schlüssel oder einen Standardschlüssel für Tests
      const signerSeed = process.env.POLKADOT_SIGNER_SEED;
      if (!signerSeed) {
        throw new Error('Kein Signaturschlüssel konfiguriert');
      }

      const signer = keyring.addFromUri(signerSeed);

      // Initialisiere den Royalty-Vertrag
      const royaltyContract = new DoiRoyaltyContract(api, contractAddress);

      // Setze Lizenzinformationen
      const licenseResult = await royaltyContract.setLicenseInfo(tokenId, licenseInfo);

      // Setze Royalty-Informationen, falls angegeben
      let royaltyResult = null;
      if (royaltyInfo && royaltyInfo.recipients && royaltyInfo.shares) {
        royaltyResult = await royaltyContract.setRoyaltyInfo(
          tokenId,
          royaltyInfo.recipients,
          royaltyInfo.shares
        );
      }

      return {
        tokenId,
        license: licenseResult.license,
        royalty: royaltyResult ? {
          recipients: royaltyResult.recipients,
          shares: royaltyResult.shares,
          royaltyRate: royaltyResult.royaltyRate
        } : null,
        transactions: {
          license: licenseResult.transaction,
          royalty: royaltyResult ? royaltyResult.transaction : null
        }
      };
    } catch (error) {
      console.error('Fehler beim Setzen von Lizenz- und Royalty-Informationen:', error);
      throw new Error(`Lizenz- und Royalty-Konfiguration fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Generiert einen Zero-Knowledge-Beweis für die Autorschaft einer Publikation
   * @param {string} doi - DOI der Publikation
   * @param {Object} author - Autorschaftsdaten
   * @param {string} privateKey - Privater Schlüssel des Autors
   * @returns {Promise<Object>} - Zero-Knowledge-Beweis
   */
  async generateAuthorshipProof(doi, author, privateKey) {
    try {
      // 1. Rufe die Publikationsdaten ab
      const publication = await this.getDoiMetadata(doi);

      if (!publication) {
        throw new Error('Publikationsdaten konnten nicht abgerufen werden');
      }

      // 2. Generiere den Beweis
      const proof = this.zkVerifier.generateAuthorshipProof(publication, author, privateKey);

      // 3. Speichere den Beweis auf IPFS
      const proofJson = JSON.stringify(proof);
      const ipfsCid = await this.storeNftMetadataToIpfs({ proof });

      return {
        doi,
        authorId: author.id,
        proof,
        ipfsCid,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Autorschaftsbeweises:', error);
      throw new Error(`Autorschaftsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }

  /**
   * Verifiziert einen Autorschaftsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {string} doi - DOI der Publikation
   * @param {string} publicKey - Öffentlicher Schlüssel des Autors
   * @returns {Promise<Object>} - Verifizierungsergebnis
   */
  async verifyAuthorshipProof(proof, doi, publicKey) {
    try {
      // 1. Rufe die Publikationsdaten ab
      const publication = await this.getDoiMetadata(doi);

      if (!publication) {
        throw new Error('Publikationsdaten konnten nicht abgerufen werden');
      }

      // 2. Verifiziere den Beweis
      const isValid = this.zkVerifier.verifyAuthorshipProof(proof, publication, publicKey);

      return {
        doi,
        authorId: proof.authorshipClaim.authorId,
        isValid,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Autorschaftsbeweises:', error);
      return {
        doi,
        isValid: false,
        error: error.message,
        timestamp: Date.now()
      };
    }
  }

  /**
   * Generiert einen Zero-Knowledge-Beweis für die Integrität von Forschungsdaten
   * @param {string} doi - DOI der Publikation
   * @param {Object} data - Forschungsdaten
   * @param {string} privateKey - Privater Schlüssel
   * @returns {Promise<Object>} - Zero-Knowledge-Beweis
   */
  async generateDataIntegrityProof(doi, data, privateKey) {
    try {
      // 1. Generiere den Beweis
      const proof = this.zkVerifier.generateDataIntegrityProof(data, privateKey);

      // 2. Speichere den Beweis auf IPFS
      const proofJson = JSON.stringify(proof);
      const ipfsCid = await this.storeNftMetadataToIpfs({ proof });

      return {
        doi,
        dataHash: proof.dataHash,
        proof,
        ipfsCid,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Datenintegritätsbeweises:', error);
      throw new Error(`Datenintegritätsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }

  /**
   * Verifiziert einen Datenintegritätsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} data - Forschungsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel
   * @returns {Promise<Object>} - Verifizierungsergebnis
   */
  async verifyDataIntegrityProof(proof, data, publicKey) {
    try {
      // Verifiziere den Beweis
      const isValid = this.zkVerifier.verifyDataIntegrityProof(proof, data, publicKey);

      return {
        dataHash: proof.dataHash,
        isValid,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Datenintegritätsbeweises:', error);
      return {
        isValid: false,
        error: error.message,
        timestamp: Date.now()
      };
    }
              orcidValidated: workInProfile
              },
       // Blockchain-spezifische Metadaten
        blockchain: {
          timestamp: Date.now(),
          version: '1.0',
          standard: 'DeSci-Scholar-v1',
          network: process.env.BLOCKCHAIN_NETWORK || 'polkadot'
        }
      };
     } else {
            validatedAuthors.push({
              ...author
              orcidValidated: false
      });
          }
          {
          title: doiMetadata.title,
          abstract: doiMetadata.abstract,
    title: doiMetadata.title,
          abstract: doiMetadata.abstract,
          title: doiMetadata.title
          abstract: doiMetadata.abstract,,
          journal: doiMetadata.journal,
    keywords: doiMetadata.keywords || [],
          // Erweiterte Metadaten
          citations: citationsData.citations || [],
          references: citationsData.references || [],
          metrics: {
            citation_count: citationsData.citations ? citationsData.citations.length : 0,
            reference_count: citationsData.references ? citationsData.references.length : 0,
            altmetric: metricsData.altmetric || null,
            dimensions: metricsData.dimensions || null
              trait_type: 'CitationCount',
            value: citationsData.citations ? citationsData.citations.length : 0
          },
         },
         // Blockchain-spezifische Metadaten
        blockchain: {
          timestamp: Date.now(),
          version: '1.0',
          standard: 'DeSci-Scholar-v1',
          network: process.env.BLOCKCHAIN_NETWORK || 'polkadot'
        }
      };
     trait_type: 'ReferenceCount',
            value: citationsData.references ? citationsData.references.length : 0
          },
          {
            trait_type: 'CitationCount',
      value: citationsData.citations ? citationsData.citations.length
          },
          {
          title: doiMetadata.title
          abstract: doiMetadata.abstract,
      trait_type: 'ReferenceCount',
      value: citationsData.references ? citationsData.references.length : 0
          },
        }
      } else {
        validatedAuthors.push(...doiMetadata.authors);,          journal: doiMetadata.journal,
          keywords: doiMetadata.keywords || [],
          // Erweiterte Metadaten
          citations: citationsData.citations || [],
          references: citationsData.references || [],
          metrics: {
            citation_count: citationsData.citations ? citationsData.citations.length : 0,
            reference_count: citationsData.references ? citationsData.references.length : 0,
            altmetric: metricsData.altmetric || null,
            dimensions: metricsData.dimensions || null
    },
        // Blockchain-spezifische Metadaten
        blockchain: {
          timestamp: Date.now(),
          version: '1.0',
          standard: 'DeSci-Scholar-v1',
          network: process.env.BLOCKCHAIN_NETWORK || 'polkadot'

      };
}

      // 8. Zitationen und Referenzen automatisch extrahiere
      let citationsData = { citations: [], references: [] };
      if (options.extractCitations !== false
        console.log(`Extrahiere Zitationen für DOI: ${doi}`);
        citationsData = await extractCitationsFromDoi(doi);
      }

          {
          {
            trait_type: 'CitationCount',
      value: citationsData.citations ? citationsData.citations.length : 0
          },
         },
         // Blockchain-spezifische Metadaten
        blockchain: {
          timestamp: Date.now(),
          version: '1.0',
          standard: 'DeSci-Scholar-v1',
          network: process.env.BLOCKCHAIN_NETWORK || 'polkadot'
        }
      };
     trait_type: 'ReferenceCount',
            value: citationsData.references ? citationsData.references.length : 0
          },
          title: doiMetadata.title,
          abstract: doiMetadata.abstract,,
          journal: doiMetadata.journal,
          keywords: doiMetadata.keywords || [],
          // Erweiterte Metadaten
          citations: citationsData.citations || [],
          references: citationsData.references || [],
          metrics: {
            citation_count: citationsData.citations ? citationsData.citations.length : 0,
            reference_count: citationsData.references ? citationsData.references.length : 0,
            altmetric: metricsData.altmetric || null,
            dimensions: metricsData.dimensions || null
              trait_type: 'CitationCount',
      value: citationsData.citations ? citationsData.citations.length : 0
            },
       // Blockchain-spezifische Metadaten
  blockchain: {
          timestamp: Date.now(),
    version: '1.0',
          standard: 'DeSci-Scholar-v1',
          network: process.env.BLOCKCHAIN_NETWORK || 'polkadot'
        }
      };

      // 6Blockchain-spezifische Metadaten
        blockchain: {
          timestamp: Date.now(),
    version: '1.0',
          standard: 'DeSci-Scholar-v1',
          network: process.env.BLOCKCHAIN_NETWORK || 'polkadot'

      };
      8  trait_type: 'ReferenceCount',
            value: citationsData.references ? citationsData.references.length : 0
          },
          title: doiMetadata.title,
          abstract: doiMetadata.abstract,
      // 4. Metriken extrahieren (Impact-Faktoren, Altmetrics, etc.)
      let metricsData = {};
      if (options.extractMetrics !== false) {
        console.log(`Extrahiere Metriken für DOI: ${doi}`);
        metricsData = await extractMetricsFromDoi(doi);
        },  // Blockchain-spezifische Metadaten
        blockchain: {
          timestamp: Date.now(),
          version: '1.0',
          standard: 'DeSci-Scholar-v1',
          network: process.env.BLOCKCHAIN_NETWORK || 'polkadot'
        }
      };


      // 8. NFT-Metadaten erstellen mit erweiterten Informatione
      const nftMetadata
        name: doiMetadata.title,
        description: `NFT für wissenschaftliche Publikation: ${doiMetadata.title}`,
        image: options.image || `https://desci-scholar.org/api/publications/${encodeURIComponent(doi)}/image`,
        external_url: doiMetadata.url,
        attributes: [
          {
          title: doiMetadata.title,
          abstract: doiMetadata.abstract,
          title: doiMetadata.title,
          abstract: doiMetadata.abstract,,
          journal: doiMetadata.journal,
          keywords: doiMetadata.keywords || [],
          // Erweiterte Metadaten
          citations: citationsData.citations || [],
          references: citationsData.references || [],
          metrics: {
      citation_count: citationsData.citations ? citationsData.citations.length : 0,
            reference_count: citationsData.references ? citationsData.references.leng
      altmetric: metricsData.altmetric || null,
      dimensions: metricsData.dimensions || null
              trait_type: 'DOI',
            value: doi
          },
         },
         // Blockchain-spezifische Metadaten
        blockchain: {
          timestamp: Date.now(),
          version: '1.0',
          standard: 'DeSci-Scholar-v1',
          network: process.env.BLOCKCHAIN_NETWORK || 'polkadot'
        }
      };
     trait_type: 'PublicationType',
            value: doiMetadata.resourceType
          },
          {
            trait_type: 'PublicationYear',
      value: doiMetadata.publicationYear
          },
          {
          title: doiMetadata.title,
          abstract: doiMetadata.abstract,
            trait_type: 'Publisher
      value: doiMetadata.publishe
          journal: doiMetadata.journal,
          keywords: doiMetadata.keywords || [],
          // Erweiterte Metadaten
    citations: citationsData.citations || [],
          references: citationsData.references || [],
          metrics: {
            citation_count: citationsData.citations ? citationsData.citations.length : 0,
            reference_count: citationsData.references ? citationsData.references.length : 0,
            altmetric: metricsData.altmetric || null,
            dimensions: metricsData.dimensions || null
            },
         },
         // Blockchain-spezifische Metadaten
        blockchain: {
          timestamp: Date.now(),
          version: '1.0',
          standard: 'DeSci-Scholar-v1',
          network: process.env.BLOCKCHAIN_NETWORK || 'polkadot'
        }
      };
     trait_type: 'CitationCount',
            value: citationsData.citations ? citationsData.citations.length : 0
          },
          {
            trait_type: 'ReferenceCount',
      value: citationsData.references ? citationsData.references.length
          },
          ...validatedAuthors.map((author, index) => ({
            trait_type: `Author${index + 1}`,
      value: author.name
          })),
          ...validatedAuthors.filter(a => a.orcid).map((author, index) => ({
      trait_type: `AuthorORCID${index + 1}`,
            value: author.orcid
          }))
        ],
        // Zusätzliche wissenschaftliche Metadaten
        scientific_metadata: {
          doi,
          title: doiMetadata.title,,
          journal: doiMetadata.journal,
          keywords: doiMetadata.keywords || [],
          // Erweiterte Metadaten
          citations: citationsData.citations || [],
          references: citationsData.references || [],
          metrics: {
            citation_count: citationsData.citations ? citationsData.citations.length : 0,
            reference_count: citationsData.references ? citationsData.references.length : 0,
            altmetric: metricsData.altmetric || null,
            dimensions: metricsData.dimensions ||   },
         // Blockchain-spezifische Metadaten
        blockchain: {
          timestamp: Date.now(),
          version: '1.0',
          standard: 'DeSci-Scholar-v1',
          network: process.env.BLOCKCHAIN_NETWORK || 'polkadot'
        }
      };
     abstract: doiMetadata.abstract,
          authors: validatedAuthors,,
        citations: {
          count: citationsData.citations ? citationsData.citations.length : 0,
          source: citationsData.source || 'none'
        },
        metrics: {
          altmetric: metricsData.altmetric ? true : false,
          dimensions: metricsData.dimensions ? true : false
        }
      };
    } catch (error) {
      console.error('DOI-zu-NFT-Konvertierung fehlgeschlagen:', error);
      throw new Error(`DOI-zu-NFT-Konvertierung fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Speichert NFT-Metadaten in IPFS
   * @private
   * @param {Object} metadata - Die zu speichernden Metadaten
   * @returns {Promise<string>} - Die IPFS Content ID (CID)
   */
  async storeNftMetadataToIpfs(metadata) {
    try {
      // Importiere den IpfsManager
      const IpfsManager = require('../storage/IpfsManager').default;

      // Konfiguration aus der Umgebung oder Standardwerten
      const ipfsConfig = {
        host: process.env.IPFS_HOST || 'ipfs.infura.io',
        port: process.env.IPFS_PORT || 5001,
        protocol: process.env.IPFS_PROTOCOL || 'https',
        auth: process.env.IPFS_AUTH || ''
      };

      // Initialisiere IPFS Manager
      const ipfsManager = new IpfsManager(ipfsConfig);

      // Speichere Metadaten auf IPFS
      const cid = await ipfsManager.storeMetadata(metadata);

      // Pinne die Metadaten für dauerhafte Speicherung
      await ipfsManager.pinMetadata(cid);

      // Gib die vollständige IPFS-URI zurück
      return ipfsManager.createIpfsLink(cid);
    } catch (error) {
      console.error('Fehler beim Speichern der Metadaten auf IPFS:', error);
      throw new Error(`IPFS-Speicherung fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Prägt ein NFT auf der Polkadot-Blockchain
   * @private
   * @param {string} metadataUri - URI der Metadaten (IPFS-Link)
   * @param {string} recipient - Empfänger-Adresse
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async mintNft(metadataUri, recipient) {
    try {
      // Importiere die Polkadot-Integration
      const { ApiPromise, WsProvider, Keyring } = require('@polkadot/api');
      const { cryptoWaitReady } = require('@polkadot/util-crypto');

      // Importiere die benötigten Contracts
      const DoiNftContract = require('../contracts/DoiNftContract').default;
      const DoiRoyaltyContract = require('../contracts/DoiRoyaltyContract').default;

      // Konfiguration aus der Umgebung oder Standardwerten
      const nodeUrl = process.env.POLKADOT_NODE_URL || 'wss://rpc.polkadot.io';
      const contractAddress = process.env.NFT_CONTRACT_ADDRESS;

      if (!contractAddress) {
        throw new Error('NFT-Vertragsadresse nicht konfiguriert');
      }

      // Warte auf die Initialisierung der Kryptographie
      await cryptoWaitReady();

      // Verbinde mit dem Polkadot-Netzwerk
      const provider = new WsProvider(nodeUrl);
      const api = await ApiPromise.create({ provider });

      // Initialisiere den Keyring für die Signatur
      const keyring = new Keyring({ type: 'sr25519' });

      // Verwende den konfigurierten Schlüssel oder einen Standardschlüssel für Tests
      const signerSeed = process.env.POLKADOT_SIGNER_SEED;
      if (!signerSeed) {
        throw new Error('Kein Signaturschlüssel konfiguriert');
      }

      const signer = keyring.addFromUri(signerSeed);

      // Initialisiere den NFT-Vertrag
      const nftContract = new DoiNftContract(api, contractAddress, {
        ipfs: {
          host: process.env.IPFS_HOST || 'ipfs.infura.io',
          port: process.env.IPFS_PORT || 5001,
          protocol: process.env.IPFS_PROTOCOL || 'https',
          auth: process.env.IPFS_AUTH || ''
        }
      });

      // Erstelle die Publikationsdaten aus den Metadaten
      const publicationData = {
        metadataUri,
        recipient
      };

      // Präge das NFT
      const mintResult = await nftContract.mint(publicationData);

      // Signiere und sende die Transaktion
      const tx = await mintResult.signAndSend(signer);

      // Warte auf die Bestätigung der Transaktion
      return new Promise((resolve, reject) => {
        tx.on('transactionHash', (hash) => {
          console.log('Transaktion gesendet:', hash);
        })
        .on('receipt', (receipt) => {
          console.log('Transaktion bestätigt:', receipt.transactionHash);
          resolve({
            transaction: receipt.transactionHash,
            tokenId: receipt.events.DoiNftMinted.returnValues.tokenId,
            owner: recipient,
            metadataUri
          });
        })
        .on('error', (error) => {
          console.error('Fehler beim Minting:', error);
          reject(error);
        });
      });
    } catch (error) {
      console.error('Fehler beim NFT-Minting:', error);
      throw new Error(`NFT-Minting fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Verifiziert ein bestehendes DOI-NFT
   * @param {string} tokenId - NFT-Token-ID
   * @returns {Promise<Object>} - Verifizierungsergebnis
   */
  async verifyDoiNft(tokenId) {
    try {
      // 1. NFT-Metadaten abrufen (Mock-Implementation)
      const nftData = await this.getNftMetadata(tokenId);
      
      if (!nftData || !nftData.scientific_metadata?.doi) {
        throw new Error('NFT enthält keine DOI-Informationen');
      }
      
      // 2. DOI-Metadaten abrufen
      const doi = nftData.scientific_metadata.doi;
      const doiMetadata = await this.getDoiMetadata(doi);
      
      // 3. Metadaten vergleichen
      const doiAuthors = doiMetadata.authors.map(a => a.name).sort();
      const nftAuthors = nftData.scientific_metadata.authors.map(a => a.name).sort();
      
      const metadataMatch = 
        doiMetadata.title === nftData.name &&
        doiMetadata.publicationYear === nftData.scientific_metadata.publication_year &&
        JSON.stringify(doiAuthors) === JSON.stringify(nftAuthors);
      
      // 4. ORCID-Validierung
      const orcidValidation = nftData.scientific_metadata.authors
        .filter(a => a.orcid && a.orcidValidated)
        .length > 0;
      
      return {
        tokenId,
        doi,
        verified: metadataMatch,
        orcidValidated: orcidValidation,
        owner: nftData.owner,
        metadataMatch,
        doiMetadata,
        nftMetadata: nftData
      };
    } catch (error) {
      throw new Error(`NFT-Verifizierung fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Ruft NFT-Metadaten von der Blockchain und IPFS ab
   * @private
   * @param {string} tokenId - Die Token-ID des NFTs
   * @returns {Promise<Object>} - Die vollständigen Metadaten
   */
  async getNftMetadata(tokenId) {
    try {
      // Importiere die Polkadot-Integration
      const { ApiPromise, WsProvider } = require('@polkadot/api');

      // Importiere die benötigten Contracts und Manager
      const DoiNftContract = require('../contracts/DoiNftContract').default;
      const DoiRoyaltyContract = require('../contracts/DoiRoyaltyContract').default;
      const IpfsManager = require('../storage/IpfsManager').default;

      // Konfiguration aus der Umgebung oder Standardwerten
      const nodeUrl = process.env.POLKADOT_NODE_URL || 'wss://rpc.polkadot.io';
      const contractAddress = process.env.NFT_CONTRACT_ADDRESS;

      if (!contractAddress) {
        throw new Error('NFT-Vertragsadresse nicht konfiguriert');
      }

      // Verbinde mit dem Polkadot-Netzwerk
      const provider = new WsProvider(nodeUrl);
      const api = await ApiPromise.create({ provider });

      // Initialisiere den NFT-Vertrag
      const nftContract = new DoiNftContract(api, contractAddress, {
        ipfs: {
          host: process.env.IPFS_HOST || 'ipfs.infura.io',
          port: process.env.IPFS_PORT || 5001,
          protocol: process.env.IPFS_PROTOCOL || 'https',
          auth: process.env.IPFS_AUTH || ''
        }
      });

      // Rufe die Token-Informationen vom Smart Contract ab
      const tokenInfo = await nftContract.getTokenInfo(tokenId);

      // Extrahiere die IPFS-URI
      const metadataUri = tokenInfo.metadata;

      // Wenn die Metadaten direkt im Smart Contract gespeichert sind
      if (!metadataUri.startsWith('ipfs://')) {
        return JSON.parse(metadataUri);
      }

      // Extrahiere die IPFS-CID
      const cid = metadataUri.replace('ipfs://', '');

      // Initialisiere den IPFS-Manager
      const ipfsManager = new IpfsManager({
        host: process.env.IPFS_HOST || 'ipfs.infura.io',
        port: process.env.IPFS_PORT || 5001,
        protocol: process.env.IPFS_PROTOCOL || 'https',
        auth: process.env.IPFS_AUTH || ''
      });

      // Rufe die Metadaten von IPFS ab
      const metadata = await ipfsManager.getMetadata(cid);

      // Füge Blockchain-spezifische Informationen hinzu
      return {
        ...metadata,
        tokenId,
        owner: tokenInfo.owner,
        ipfs: {
          cid,
          url: ipfsManager.getHttpUrl(cid)
        }
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der NFT-Metadaten:', error);
      throw new Error(`NFT-Metadatenabruf fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Setzt Lizenz- und Royalty-Informationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} licenseInfo - Lizenzinformationen
   * @param {Object} royaltyInfo - Royalty-Informationen
   * @returns {Promise<Object>} - Ergebnis der Operation
   */
  async setNftLicenseAndRoyalty(tokenId, licenseInfo, royaltyInfo) {
    try {
      // Importiere die Polkadot-Integration
      const { ApiPromise, WsProvider, Keyring } = require('@polkadot/api');
      const { cryptoWaitReady } = require('@polkadot/util-crypto');

      // Importiere den DoiRoyaltyContract
      const DoiRoyaltyContract = require('../contracts/DoiRoyaltyContract').default;

      // Konfiguration aus der Umgebung oder Standardwerten
      const nodeUrl = process.env.POLKADOT_NODE_URL || 'wss://rpc.polkadot.io';
      const contractAddress = process.env.ROYALTY_CONTRACT_ADDRESS || process.env.NFT_CONTRACT_ADDRESS;

      if (!contractAddress) {
        throw new Error('Vertragsadresse nicht konfiguriert');
      }

      // Warte auf die Initialisierung der Kryptographie
      await cryptoWaitReady();

      // Verbinde mit dem Polkadot-Netzwerk
      const provider = new WsProvider(nodeUrl);
      const api = await ApiPromise.create({ provider });

      // Initialisiere den Keyring für die Signatur
      const keyring = new Keyring({ type: 'sr25519' });

      // Verwende den konfigurierten Schlüssel oder einen Standardschlüssel für Tests
      const signerSeed = process.env.POLKADOT_SIGNER_SEED;
      if (!signerSeed) {
        throw new Error('Kein Signaturschlüssel konfiguriert');
      }

      const signer = keyring.addFromUri(signerSeed);

      // Initialisiere den Royalty-Vertrag
      const royaltyContract = new DoiRoyaltyContract(api, contractAddress);

      // Setze Lizenzinformationen
      const licenseResult = await royaltyContract.setLicenseInfo(tokenId, licenseInfo);

      // Setze Royalty-Informationen, falls angegeben
      let royaltyResult = null;
      if (royaltyInfo && royaltyInfo.recipients && royaltyInfo.shares) {
        royaltyResult = await royaltyContract.setRoyaltyInfo(
          tokenId,
          royaltyInfo.recipients,
          royaltyInfo.shares
        );
      }

      return {
        tokenId,
        license: licenseResult.license,
        royalty: royaltyResult ? {
          recipients: royaltyResult.recipients,
          shares: royaltyResult.shares,
          royaltyRate: royaltyResult.royaltyRate
        } : null,
        transactions: {
          license: licenseResult.transaction,
          royalty: royaltyResult ? royaltyResult.transaction : null
        }
      };
    } catch (error) {
      console.error('Fehler beim Setzen von Lizenz- und Royalty-Informationen:', error);
      throw new Error(`Lizenz- und Royalty-Konfiguration fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Generiert einen Zero-Knowledge-Beweis für die Autorschaft einer Publikation
   * @param {string} doi - DOI der Publikation
   * @param {Object} author - Autorschaftsdaten
   * @param {string} privateKey - Privater Schlüssel des Autors
   * @returns {Promise<Object>} - Zero-Knowledge-Beweis
   */
  async generateAuthorshipProof(doi, author, privateKey) {
    try {
      // 1. Rufe die Publikationsdaten ab
      const publication = await this.getDoiMetadata(doi);

      if (!publication) {
        throw new Error('Publikationsdaten konnten nicht abgerufen werden');
      }

      // 2. Generiere den Beweis
      const proof = this.zkVerifier.generateAuthorshipProof(publication, author, privateKey);

      // 3. Speichere den Beweis auf IPFS
      const proofJson = JSON.stringify(proof);
      const ipfsCid = await this.storeNftMetadataToIpfs({ proof });

      return {
        doi,
        authorId: author.id,
        proof,
        ipfsCid,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Autorschaftsbeweises:', error);
      throw new Error(`Autorschaftsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }

  /**
   * Verifiziert einen Autorschaftsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {string} doi - DOI der Publikation
   * @param {string} publicKey - Öffentlicher Schlüssel des Autors
   * @returns {Promise<Object>} - Verifizierungsergebnis
   */
  async verifyAuthorshipProof(proof, doi, publicKey) {
    try {
      // 1. Rufe die Publikationsdaten ab
      const publication = await this.getDoiMetadata(doi);

      if (!publication) {
        throw new Error('Publikationsdaten konnten nicht abgerufen werden');
      }

      // 2. Verifiziere den Beweis
      const isValid = this.zkVerifier.verifyAuthorshipProof(proof, publication, publicKey);

      return {
        doi,
        authorId: proof.authorshipClaim.authorId,
        isValid,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Autorschaftsbeweises:', error);
      return {
        doi,
        isValid: false,
        error: error.message,
        timestamp: Date.now()
      };
    }
  }

  /**
   * Generiert einen Zero-Knowledge-Beweis für die Integrität von Forschungsdaten
   * @param {string} doi - DOI der Publikation
   * @param {Object} data - Forschungsdaten
   * @param {string} privateKey - Privater Schlüssel
   * @returns {Promise<Object>} - Zero-Knowledge-Beweis
   */
  async generateDataIntegrityProof(doi, data, privateKey) {
    try {
      // 1. Generiere den Beweis
      const proof = this.zkVerifier.generateDataIntegrityProof(data, privateKey);

      // 2. Speichere den Beweis auf IPFS
      const proofJson = JSON.stringify(proof);
      const ipfsCid = await this.storeNftMetadataToIpfs({ proof });

      return {
        doi,
        dataHash: proof.dataHash,
        proof,
        ipfsCid,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Datenintegritätsbeweises:', error);
      throw new Error(`Datenintegritätsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }

  /**
   * Verifiziert einen Datenintegritätsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} data - Forschungsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel
   * @returns {Promise<Object>} - Verifizierungsergebnis
   */
  async verifyDataIntegrityProof(proof, data, publicKey) {
    try {
      // Verifiziere den Beweis
      const isValid = this.zkVerifier.verifyDataIntegrityProof(proof, data, publicKey);

      return {
        dataHash: proof.dataHash,
        isValid,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Datenintegritätsbeweises:', error);
      return {
        isValid: false,
        error: error.message,
        timestamp: Date.now()
      };
    }
          publication_year: doiMetadata.publicationYear,
    resource_type: doiMetadata.resourceType,
          publisher: doiMetadata.publishe
          journal: doiMetadata.journal,
    keywords: doiMetadata.keywords || [],
          // 8Erweiterte Metadaten
    citations: citationsData.citations || [],
          references: citationsData.references || [],
          metrics: {
            citation_count: citationsData.citations ? citationsData.citations.length : 0,
            reference_count: citationsData.references ? citationsData.references.length : 0,
            altmetric: metricsData.altmetric || null,
            dimensions: metricsData.dimensions || null
          }
        },
        // Blockchain-spezifische Metadaten
        blockchain: {
          timestamp: Date.now(),
          version: '1.0',
          standard: 'DeSci-Scholar-v1',
          network: process.env.BLOCKCHAIN_NETWORK || 'polkadot'
        }
      };

      // 6. Metadaten-Hash generieren
      const metadataHash = crypto
        .createHash('sha256')
        .update(JSON.stringify(nftMetadata))
        .digest('hex');

      // 7. NFT-Metadaten in IPFS speichern
      const ipfsCid = await this.storeNftMetadataToIpfs(nftMetadata);

      // 8. NFT prägen
      const mintResult = await this.mintNft(ipfsCid, options.recipient || options.owner);

      return {
        doi,
        nftMetadata,
        metadataHash,
        ipfsCid,
        transaction: mintResult.transaction,
        tokenId: mintResult.tokenId,
        owner: options.recipient || options.owner,
        created: Date.now(),
        validatedAuthors,,
        citations: {
          count: citationsData.citations ? citationsData.citations.length : 0,
          source: citationsData.source || 'none'
        },
        metrics: {
          altmetric: metricsData.altmetric ? true : false,
          dimensions: metricsData.dimensions ? true : false
        }
      };
    } catch (error) {
      console.error('DOI-zu-NFT-Konvertierung fehlgeschlagen:', error);
      throw new Error(`DOI-zu-NFT-Konvertierung fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Speichert NFT-Metadaten in IPFS
   * @private
   * @param {Object} metadata - Die zu speichernden Metadaten
   * @returns {Promise<string>} - Die IPFS Content ID (CID)
   */
  async storeNftMetadataToIpfs(metadata) {
    try {
      // Importiere den IpfsManager
      const IpfsManager = require('../storage/IpfsManager').default;

      // Konfiguration aus der Umgebung oder Standardwerten
      const ipfsConfig = {
        host: process.env.IPFS_HOST || 'ipfs.infura.io',
        port: process.env.IPFS_PORT || 5001,
        protocol: process.env.IPFS_PROTOCOL || 'https',
        auth: process.env.IPFS_AUTH || ''
      };

      // Initialisiere IPFS Manager
      const ipfsManager = new IpfsManager(ipfsConfig);

      // Speichere Metadaten auf IPFS
      const cid = await ipfsManager.storeMetadata(metadata);

      // Pinne die Metadaten für dauerhafte Speicherung
      await ipfsManager.pinMetadata(cid);

      // Gib die vollständige IPFS-URI zurück
      return ipfsManager.createIpfsLink(cid);
    } catch (error) {
      console.error('Fehler beim Speichern der Metadaten auf IPFS:', error);
      throw new Error(`IPFS-Speicherung fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Prägt ein NFT auf der Polkadot-Blockchain
   * @private
   * @param {string} metadataUri - URI der Metadaten (IPFS-Link)
   * @param {string} recipient - Empfänger-Adresse
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async mintNft(metadataUri, recipient) {
    try {
      // Importiere die Polkadot-Integration
      const { ApiPromise, WsProvider, Keyring } = require('@polkadot/api');
      const { cryptoWaitReady } = require('@polkadot/util-crypto');

      // Importiere die benötigten Contracts
      const DoiNftContract = require('../contracts/DoiNftContract').default;
      const DoiRoyaltyContract = require('../contracts/DoiRoyaltyContract').default;

      // Konfiguration aus der Umgebung oder Standardwerten
      const nodeUrl = process.env.POLKADOT_NODE_URL || 'wss://rpc.polkadot.io';
      const contractAddress = process.env.NFT_CONTRACT_ADDRESS;

      if (!contractAddress) {
        throw new Error('NFT-Vertragsadresse nicht konfiguriert');
      }

      // Warte auf die Initialisierung der Kryptographie
      await cryptoWaitReady();

      // Verbinde mit dem Polkadot-Netzwerk
      const provider = new WsProvider(nodeUrl);
      const api = await ApiPromise.create({ provider });

      // Initialisiere den Keyring für die Signatur
      const keyring = new Keyring({ type: 'sr25519' });

      // Verwende den konfigurierten Schlüssel oder einen Standardschlüssel für Tests
      const signerSeed = process.env.POLKADOT_SIGNER_SEED;
      if (!signerSeed) {
        throw new Error('Kein Signaturschlüssel konfiguriert');
      }

      const signer = keyring.addFromUri(signerSeed);

      // Initialisiere den NFT-Vertrag
      const nftContract = new DoiNftContract(api, contractAddress, {
        ipfs: {
          host: process.env.IPFS_HOST || 'ipfs.infura.io',
          port: process.env.IPFS_PORT || 5001,
          protocol: process.env.IPFS_PROTOCOL || 'https',
          auth: process.env.IPFS_AUTH || ''
        }
      });

      // Erstelle die Publikationsdaten aus den Metadaten
      const publicationData = {
        metadataUri,
        recipient
      };

      // Präge das NFT
      const mintResult = await nftContract.mint(publicationData);

      // Signiere und sende die Transaktion
      const tx = await mintResult.signAndSend(signer);

      // Warte auf die Bestätigung der Transaktion
      return new Promise((resolve, reject) => {
        tx.on('transactionHash', (hash) => {
          console.log('Transaktion gesendet:', hash);
        })
        .on('receipt', (receipt) => {
          console.log('Transaktion bestätigt:', receipt.transactionHash);
          resolve({
            transaction: receipt.transactionHash,
            tokenId: receipt.events.DoiNftMinted.returnValues.tokenId,
            owner: recipient,
            metadataUri
          });
        })
        .on('error', (error) => {
          console.error('Fehler beim Minting:', error);
          reject(error);
        });
      });
    } catch (error) {
      console.error('Fehler beim NFT-Minting:', error);
      throw new Error(`NFT-Minting fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Verifiziert ein bestehendes DOI-NFT
   * @param {string} tokenId - NFT-Token-ID
   * @returns {Promise<Object>} - Verifizierungsergebnis
   */
  async verifyDoiNft(tokenId) {
    try {
      // 1. NFT-Metadaten abrufen (Mock-Implementation)
      const nftData = await this.getNftMetadata(tokenId);
      
      if (!nftData || !nftData.scientific_metadata?.doi) {
        throw new Error('NFT enthält keine DOI-Informationen');
      }
      
      // 2. DOI-Metadaten abrufen
      const doi = nftData.scientific_metadata.doi;
      const doiMetadata = await this.getDoiMetadata(doi);
      
      // 3. Metadaten vergleichen
      const doiAuthors = doiMetadata.authors.map(a => a.name).sort();
      const nftAuthors = nftData.scientific_metadata.authors.map(a => a.name).sort();
      
      const metadataMatch = 
        doiMetadata.title === nftData.name &&
        doiMetadata.publicationYear === nftData.scientific_metadata.publication_year &&
        JSON.stringify(doiAuthors) === JSON.stringify(nftAuthors);
      
      // 4. ORCID-Validierung
      const orcidValidation = nftData.scientific_metadata.authors
        .filter(a => a.orcid && a.orcidValidated)
        .length > 0;
      
      return {
        tokenId,
        doi,
        verified: metadataMatch,
        orcidValidated: orcidValidation,
        owner: nftData.owner,
        metadataMatch,
        doiMetadata,
        nftMetadata: nftData
      };
    } catch (error) {
      throw new Error(`NFT-Verifizierung fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Ruft NFT-Metadaten von der Blockchain und IPFS ab
   * @private
   * @param {string} tokenId - Die Token-ID des NFTs
   * @returns {Promise<Object>} - Die vollständigen Metadaten
   */
  async getNftMetadata(tokenId) {
    try {
      // Importiere die Polkadot-Integration
      const { ApiPromise, WsProvider } = require('@polkadot/api');

      // Importiere die benötigten Contracts und Manager
      const DoiNftContract = require('../contracts/DoiNftContract').default;
      const DoiRoyaltyContract = require('../contracts/DoiRoyaltyContract').default;
      const IpfsManager = require('../storage/IpfsManager').default;

      // Konfiguration aus der Umgebung oder Standardwerten
      const nodeUrl = process.env.POLKADOT_NODE_URL || 'wss://rpc.polkadot.io';
      const contractAddress = process.env.NFT_CONTRACT_ADDRESS;

      if (!contractAddress) {
        throw new Error('NFT-Vertragsadresse nicht konfiguriert');
      }

      // Verbinde mit dem Polkadot-Netzwerk
      const provider = new WsProvider(nodeUrl);
      const api = await ApiPromise.create({ provider });

      // Initialisiere den NFT-Vertrag
      const nftContract = new DoiNftContract(api, contractAddress, {
        ipfs: {
          host: process.env.IPFS_HOST || 'ipfs.infura.io',
          port: process.env.IPFS_PORT || 5001,
          protocol: process.env.IPFS_PROTOCOL || 'https',
          auth: process.env.IPFS_AUTH || ''
        }
      });

      // Rufe die Token-Informationen vom Smart Contract ab
      const tokenInfo = await nftContract.getTokenInfo(tokenId);

      // Extrahiere die IPFS-URI
      const metadataUri = tokenInfo.metadata;

      // Wenn die Metadaten direkt im Smart Contract gespeichert sind
      if (!metadataUri.startsWith('ipfs://')) {
        return JSON.parse(metadataUri);
      }

      // Extrahiere die IPFS-CID
      const cid = metadataUri.replace('ipfs://', '');

      // Initialisiere den IPFS-Manager
      const ipfsManager = new IpfsManager({
        host: process.env.IPFS_HOST || 'ipfs.infura.io',
        port: process.env.IPFS_PORT || 5001,
        protocol: process.env.IPFS_PROTOCOL || 'https',
        auth: process.env.IPFS_AUTH || ''
      });

      // Rufe die Metadaten von IPFS ab
      const metadata = await ipfsManager.getMetadata(cid);

      // Füge Blockchain-spezifische Informationen hinzu
      return {
        ...metadata,
        tokenId,
        owner: tokenInfo.owner,
        ipfs: {
          cid,
          url: ipfsManager.getHttpUrl(cid)
        }
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der NFT-Metadaten:', error);
      throw new Error(`NFT-Metadatenabruf fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Setzt Lizenz- und Royalty-Informationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} licenseInfo - Lizenzinformationen
   * @param {Object} royaltyInfo - Royalty-Informationen
   * @returns {Promise<Object>} - Ergebnis der Operation
   */
  async setNftLicenseAndRoyalty(tokenId, licenseInfo, royaltyInfo) {
    try {
      // Importiere die Polkadot-Integration
      const { ApiPromise, WsProvider, Keyring } = require('@polkadot/api');
      const { cryptoWaitReady } = require('@polkadot/util-crypto');

      // Importiere den DoiRoyaltyContract
      const DoiRoyaltyContract = require('../contracts/DoiRoyaltyContract').default;

      // Konfiguration aus der Umgebung oder Standardwerten
      const nodeUrl = process.env.POLKADOT_NODE_URL || 'wss://rpc.polkadot.io';
      const contractAddress = process.env.ROYALTY_CONTRACT_ADDRESS || process.env.NFT_CONTRACT_ADDRESS;

      if (!contractAddress) {
        throw new Error('Vertragsadresse nicht konfiguriert');
      }

      // Warte auf die Initialisierung der Kryptographie
      await cryptoWaitReady();

      // Verbinde mit dem Polkadot-Netzwerk
      const provider = new WsProvider(nodeUrl);
      const api = await ApiPromise.create({ provider });

      // Initialisiere den Keyring für die Signatur
      const keyring = new Keyring({ type: 'sr25519' });

      // Verwende den konfigurierten Schlüssel oder einen Standardschlüssel für Tests
      const signerSeed = process.env.POLKADOT_SIGNER_SEED;
      if (!signerSeed) {
        throw new Error('Kein Signaturschlüssel konfiguriert');
      }

      const signer = keyring.addFromUri(signerSeed);

      // Initialisiere den Royalty-Vertrag
      const royaltyContract = new DoiRoyaltyContract(api, contractAddress);

      // Setze Lizenzinformationen
      const licenseResult = await royaltyContract.setLicenseInfo(tokenId, licenseInfo);

      // Setze Royalty-Informationen, falls angegeben
      let royaltyResult = null;
      if (royaltyInfo && royaltyInfo.recipients && royaltyInfo.shares) {
        royaltyResult = await royaltyContract.setRoyaltyInfo(
          tokenId,
          royaltyInfo.recipients,
          royaltyInfo.shares
        );
      }

      return {
        tokenId,
        license: licenseResult.license,
        royalty: royaltyResult ? {
          recipients: royaltyResult.recipients,
          shares: royaltyResult.shares,
          royaltyRate: royaltyResult.royaltyRate
        } : null,
        transactions: {
          license: licenseResult.transaction,
          royalty: royaltyResult ? royaltyResult.transaction : null
        }
      };
    } catch (error) {
      console.error('Fehler beim Setzen von Lizenz- und Royalty-Informationen:', error);
      throw new Error(`Lizenz- und Royalty-Konfiguration fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Generiert einen Zero-Knowledge-Beweis für die Autorschaft einer Publikation
   * @param {string} doi - DOI der Publikation
   * @param {Object} author - Autorschaftsdaten
   * @param {string} privateKey - Privater Schlüssel des Autors
   * @returns {Promise<Object>} - Zero-Knowledge-Beweis
   */
  async generateAuthorshipProof(doi, author, privateKey) {
    try {
      // 1. Rufe die Publikationsdaten ab
      const publication = await this.getDoiMetadata(doi);

      if (!publication) {
        throw new Error('Publikationsdaten konnten nicht abgerufen werden');
      }

      // 2. Generiere den Beweis
      const proof = this.zkVerifier.generateAuthorshipProof(publication, author, privateKey);

      // 3. Speichere den Beweis auf IPFS
      const proofJson = JSON.stringify(proof);
      const ipfsCid = await this.storeNftMetadataToIpfs({ proof });

      return {
        doi,
        authorId: author.id,
        proof,
        ipfsCid,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Autorschaftsbeweises:', error);
      throw new Error(`Autorschaftsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }

  /**
   * Verifiziert einen Autorschaftsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {string} doi - DOI der Publikation
   * @param {string} publicKey - Öffentlicher Schlüssel des Autors
   * @returns {Promise<Object>} - Verifizierungsergebnis
   */
  async verifyAuthorshipProof(proof, doi, publicKey) {
    try {
      // 1. Rufe die Publikationsdaten ab
      const publication = await this.getDoiMetadata(doi);

      if (!publication) {
        throw new Error('Publikationsdaten konnten nicht abgerufen werden');
      }

      // 2. Verifiziere den Beweis
      const isValid = this.zkVerifier.verifyAuthorshipProof(proof, publication, publicKey);

      return {
        doi,
        authorId: proof.authorshipClaim.authorId,
        isValid,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Autorschaftsbeweises:', error);
      return {
        doi,
        isValid: false,
        error: error.message,
        timestamp: Date.now()
      };
    }
  }

  /**
   * Generiert einen Zero-Knowledge-Beweis für die Integrität von Forschungsdaten
   * @param {string} doi - DOI der Publikation
   * @param {Object} data - Forschungsdaten
   * @param {string} privateKey - Privater Schlüssel
   * @returns {Promise<Object>} - Zero-Knowledge-Beweis
   */
  async generateDataIntegrityProof(doi, data, privateKey) {
    try {
      // 1. Generiere den Beweis
      const proof = this.zkVerifier.generateDataIntegrityProof(data, privateKey);

      // 2. Speichere den Beweis auf IPFS
      const proofJson = JSON.stringify(proof);
      const ipfsCid = await this.storeNftMetadataToIpfs({ proof });

      return {
        doi,
        dataHash: proof.dataHash,
        proof,
        ipfsCid,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Datenintegritätsbeweises:', error);
      throw new Error(`Datenintegritätsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }

  /**
   * Verifiziert einen Datenintegritätsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} data - Forschungsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel
   * @returns {Promise<Object>} - Verifizierungsergebnis
   */
  async verifyDataIntegrityProof(proof, data, publicKey) {
    try {
      // Verifiziere den Beweis
      const isValid = this.zkVerifier.verifyDataIntegrityProof(proof, data, publicKey);

      return {
        dataHash: proof.dataHash,
        isValid,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Datenintegritätsbeweises:', error);
      return {
        isValid: false,
        error: error.message,
        timestamp: Date.now()
      };
    }
        citations: {
          count: citationsData.citations ? citationsData.citations.length : 0,
          source: citationsData.source || 'none'
        },
        metrics: {
          altmetric: metricsData.altmetric ? true : false,
          dimensions: metricsData.dimensions ? true : false
        }
      };
    } catch (error) {
      console.error('DOI-zu-NFT-Konvertierung fehlgeschlagen:', error);
      throw new Error(`DOI-zu-NFT-Konvertierung fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Speichert NFT-Metadaten in IPFS
   * @private
   * @param {Object} metadata - Die zu speichernden Metadaten
   * @returns {Promise<string>} - Die IPFS Content ID (CID)
   */
  async storeNftMetadataToIpfs(metadata) {
    try {
      // Importiere den IpfsManager
      const IpfsManager = require('../storage/IpfsManager').default;

      // Konfiguration aus der Umgebung oder Standardwerten
      const ipfsConfig = {
        host: process.env.IPFS_HOST || 'ipfs.infura.io',
        port: process.env.IPFS_PORT || 5001,
        protocol: process.env.IPFS_PROTOCOL || 'https',
        auth: process.env.IPFS_AUTH || ''
      };

      // Initialisiere IPFS Manager
      const ipfsManager = new IpfsManager(ipfsConfig);

      // Speichere Metadaten auf IPFS
      const cid = await ipfsManager.storeMetadata(metadata);

      // Pinne die Metadaten für dauerhafte Speicherung
      await ipfsManager.pinMetadata(cid);

      // Gib die vollständige IPFS-URI zurück
      return ipfsManager.createIpfsLink(cid);
    } catch (error) {
      console.error('Fehler beim Speichern der Metadaten auf IPFS:', error);
      throw new Error(`IPFS-Speicherung fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Prägt ein NFT auf der Polkadot-Blockchain
   * @private
   * @param {string} metadataUri - URI der Metadaten (IPFS-Link)
   * @param {string} recipient - Empfänger-Adresse
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async mintNft(metadataUri, recipient) {
    try {
      // Importiere die Polkadot-Integration
      const { ApiPromise, WsProvider, Keyring } = require('@polkadot/api');
      const { cryptoWaitReady } = require('@polkadot/util-crypto');

      // Importiere die benötigten Contracts
      const DoiNftContract = require('../contracts/DoiNftContract').default;
      const DoiRoyaltyContract = require('../contracts/DoiRoyaltyContract').default;

      // Konfiguration aus der Umgebung oder Standardwerten
      const nodeUrl = process.env.POLKADOT_NODE_URL || 'wss://rpc.polkadot.io';
      const contractAddress = process.env.NFT_CONTRACT_ADDRESS;

      if (!contractAddress) {
        throw new Error('NFT-Vertragsadresse nicht konfiguriert');
      }

      // Warte auf die Initialisierung der Kryptographie
      await cryptoWaitReady();

      // Verbinde mit dem Polkadot-Netzwerk
      const provider = new WsProvider(nodeUrl);
      const api = await ApiPromise.create({ provider });

      // Initialisiere den Keyring für die Signatur
      const keyring = new Keyring({ type: 'sr25519' });

      // Verwende den konfigurierten Schlüssel oder einen Standardschlüssel für Tests
      const signerSeed = process.env.POLKADOT_SIGNER_SEED;
      if (!signerSeed) {
        throw new Error('Kein Signaturschlüssel konfiguriert');
      }

      const signer = keyring.addFromUri(signerSeed);

      // Initialisiere den NFT-Vertrag
      const nftContract = new DoiNftContract(api, contractAddress, {
        ipfs: {
          host: process.env.IPFS_HOST || 'ipfs.infura.io',
          port: process.env.IPFS_PORT || 5001,
          protocol: process.env.IPFS_PROTOCOL || 'https',
          auth: process.env.IPFS_AUTH || ''
        }
      });

      // Erstelle die Publikationsdaten aus den Metadaten
      const publicationData = {
        metadataUri,
        recipient
      };

      // Präge das NFT
      const mintResult = await nftContract.mint(publicationData);

      // Signiere und sende die Transaktion
      const tx = await mintResult.signAndSend(signer);

      // Warte auf die Bestätigung der Transaktion
      return new Promise((resolve, reject) => {
        tx.on('transactionHash', (hash) => {
          console.log('Transaktion gesendet:', hash);
        })
        .on('receipt', (receipt) => {
          console.log('Transaktion bestätigt:', receipt.transactionHash);
          resolve({
            transaction: receipt.transactionHash,
            tokenId: receipt.events.DoiNftMinted.returnValues.tokenId,
            owner: recipient,
            metadataUri
          });
        })
        .on('error', (error) => {
          console.error('Fehler beim Minting:', error);
          reject(error);
        });
      });
    } catch (error) {
      console.error('Fehler beim NFT-Minting:', error);
      throw new Error(`NFT-Minting fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Verifiziert ein bestehendes DOI-NFT
   * @param {string} tokenId - NFT-Token-ID
   * @returns {Promise<Object>} - Verifizierungsergebnis
   */
  async verifyDoiNft(tokenId) {
    try {
      // 1. NFT-Metadaten abrufen (Mock-Implementation)
      const nftData = await this.getNftMetadata(tokenId);
      
      if (!nftData || !nftData.scientific_metadata?.doi) {
        throw new Error('NFT enthält keine DOI-Informationen');
      }
      
      // 2. DOI-Metadaten abrufen
      const doi = nftData.scientific_metadata.doi;
      const doiMetadata = await this.getDoiMetadata(doi);
      
      // 3. Metadaten vergleichen
      const doiAuthors = doiMetadata.authors.map(a => a.name).sort();
      const nftAuthors = nftData.scientific_metadata.authors.map(a => a.name).sort();
      
      const metadataMatch = 
        doiMetadata.title === nftData.name &&
        doiMetadata.publicationYear === nftData.scientific_metadata.publication_year &&
        JSON.stringify(doiAuthors) === JSON.stringify(nftAuthors);
      
      // 4. ORCID-Validierung
      const orcidValidation = nftData.scientific_metadata.authors
        .filter(a => a.orcid && a.orcidValidated)
        .length > 0;
      
      return {
        tokenId,
        doi,
        verified: metadataMatch,
        orcidValidated: orcidValidation,
        owner: nftData.owner,
        metadataMatch,
        doiMetadata,
        nftMetadata: nftData
      };
    } catch (error) {
      throw new Error(`NFT-Verifizierung fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Ruft NFT-Metadaten von der Blockchain und IPFS ab
   * @private
   * @param {string} tokenId - Die Token-ID des NFTs
   * @returns {Promise<Object>} - Die vollständigen Metadaten
   */
  async getNftMetadata(tokenId) {
    try {
      // Importiere die Polkadot-Integration
      const { ApiPromise, WsProvider } = require('@polkadot/api');

      // Importiere die benötigten Contracts und Manager
      const DoiNftContract = require('../contracts/DoiNftContract').default;
      const DoiRoyaltyContract = require('../contracts/DoiRoyaltyContract').default;
      const IpfsManager = require('../storage/IpfsManager').default;

      // Konfiguration aus der Umgebung oder Standardwerten
      const nodeUrl = process.env.POLKADOT_NODE_URL || 'wss://rpc.polkadot.io';
      const contractAddress = process.env.NFT_CONTRACT_ADDRESS;

      if (!contractAddress) {
        throw new Error('NFT-Vertragsadresse nicht konfiguriert');
      }

      // Verbinde mit dem Polkadot-Netzwerk
      const provider = new WsProvider(nodeUrl);
      const api = await ApiPromise.create({ provider });

      // Initialisiere den NFT-Vertrag
      const nftContract = new DoiNftContract(api, contractAddress, {
        ipfs: {
          host: process.env.IPFS_HOST || 'ipfs.infura.io',
          port: process.env.IPFS_PORT || 5001,
          protocol: process.env.IPFS_PROTOCOL || 'https',
          auth: process.env.IPFS_AUTH || ''
        }
      });

      // Rufe die Token-Informationen vom Smart Contract ab
      const tokenInfo = await nftContract.getTokenInfo(tokenId);

      // Extrahiere die IPFS-URI
      const metadataUri = tokenInfo.metadata;

      // Wenn die Metadaten direkt im Smart Contract gespeichert sind
      if (!metadataUri.startsWith('ipfs://')) {
        return JSON.parse(metadataUri);
      }

      // Extrahiere die IPFS-CID
      const cid = metadataUri.replace('ipfs://', '');

      // Initialisiere den IPFS-Manager
      const ipfsManager = new IpfsManager({
        host: process.env.IPFS_HOST || 'ipfs.infura.io',
        port: process.env.IPFS_PORT || 5001,
        protocol: process.env.IPFS_PROTOCOL || 'https',
        auth: process.env.IPFS_AUTH || ''
      });

      // Rufe die Metadaten von IPFS ab
      const metadata = await ipfsManager.getMetadata(cid);

      // Füge Blockchain-spezifische Informationen hinzu
      return {
        ...metadata,
        tokenId,
        owner: tokenInfo.owner,
        ipfs: {
          cid,
          url: ipfsManager.getHttpUrl(cid)
        }
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der NFT-Metadaten:', error);
      throw new Error(`NFT-Metadatenabruf fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Setzt Lizenz- und Royalty-Informationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} licenseInfo - Lizenzinformationen
   * @param {Object} royaltyInfo - Royalty-Informationen
   * @returns {Promise<Object>} - Ergebnis der Operation
   */
  async setNftLicenseAndRoyalty(tokenId, licenseInfo, royaltyInfo) {
    try {
      // Importiere die Polkadot-Integration
      const { ApiPromise, WsProvider, Keyring } = require('@polkadot/api');
      const { cryptoWaitReady } = require('@polkadot/util-crypto');

      // Importiere den DoiRoyaltyContract
      const DoiRoyaltyContract = require('../contracts/DoiRoyaltyContract').default;

      // Konfiguration aus der Umgebung oder Standardwerten
      const nodeUrl = process.env.POLKADOT_NODE_URL || 'wss://rpc.polkadot.io';
      const contractAddress = process.env.ROYALTY_CONTRACT_ADDRESS || process.env.NFT_CONTRACT_ADDRESS;

      if (!contractAddress) {
        throw new Error('Vertragsadresse nicht konfiguriert');
      }

      // Warte auf die Initialisierung der Kryptographie
      await cryptoWaitReady();

      // Verbinde mit dem Polkadot-Netzwerk
      const provider = new WsProvider(nodeUrl);
      const api = await ApiPromise.create({ provider });

      // Initialisiere den Keyring für die Signatur
      const keyring = new Keyring({ type: 'sr25519' });

      // Verwende den konfigurierten Schlüssel oder einen Standardschlüssel für Tests
      const signerSeed = process.env.POLKADOT_SIGNER_SEED;
      if (!signerSeed) {
        throw new Error('Kein Signaturschlüssel konfiguriert');
      }

      const signer = keyring.addFromUri(signerSeed);

      // Initialisiere den Royalty-Vertrag
      const royaltyContract = new DoiRoyaltyContract(api, contractAddress);

      // Setze Lizenzinformationen
      const licenseResult = await royaltyContract.setLicenseInfo(tokenId, licenseInfo);

      // Setze Royalty-Informationen, falls angegeben
      let royaltyResult = null;
      if (royaltyInfo && royaltyInfo.recipients && royaltyInfo.shares) {
        royaltyResult = await royaltyContract.setRoyaltyInfo(
          tokenId,
          royaltyInfo.recipients,
          royaltyInfo.shares
        );
      }

      return {
        tokenId,
        license: licenseResult.license,
        royalty: royaltyResult ? {
          recipients: royaltyResult.recipients,
          shares: royaltyResult.shares,
          royaltyRate: royaltyResult.royaltyRate
        } : null,
        transactions: {
          license: licenseResult.transaction,
          royalty: royaltyResult ? royaltyResult.transaction : null
        }
      };
    } catch (error) {
      console.error('Fehler beim Setzen von Lizenz- und Royalty-Informationen:', error);
      throw new Error(`Lizenz- und Royalty-Konfiguration fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Generiert einen Zero-Knowledge-Beweis für die Autorschaft einer Publikation
   * @param {string} doi - DOI der Publikation
   * @param {Object} author - Autorschaftsdaten
   * @param {string} privateKey - Privater Schlüssel des Autors
   * @returns {Promise<Object>} - Zero-Knowledge-Beweis
   */
  async generateAuthorshipProof(doi, author, privateKey) {
    try {
      // 1. Rufe die Publikationsdaten ab
      const publication = await this.getDoiMetadata(doi);

      if (!publication) {
        throw new Error('Publikationsdaten konnten nicht abgerufen werden');
      }

      // 2. Generiere den Beweis
      const proof = this.zkVerifier.generateAuthorshipProof(publication, author, privateKey);

      // 3. Speichere den Beweis auf IPFS
      const proofJson = JSON.stringify(proof);
      const ipfsCid = await this.storeNftMetadataToIpfs({ proof });

      return {
        doi,
        authorId: author.id,
        proof,
        ipfsCid,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Autorschaftsbeweises:', error);
      throw new Error(`Autorschaftsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }

  /**
   * Verifiziert einen Autorschaftsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {string} doi - DOI der Publikation
   * @param {string} publicKey - Öffentlicher Schlüssel des Autors
   * @returns {Promise<Object>} - Verifizierungsergebnis
   */
  async verifyAuthorshipProof(proof, doi, publicKey) {
    try {
      // 1. Rufe die Publikationsdaten ab
      const publication = await this.getDoiMetadata(doi);

      if (!publication) {
        throw new Error('Publikationsdaten konnten nicht abgerufen werden');
      }

      // 2. Verifiziere den Beweis
      const isValid = this.zkVerifier.verifyAuthorshipProof(proof, publication, publicKey);

      return {
        doi,
        authorId: proof.authorshipClaim.authorId,
        isValid,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Autorschaftsbeweises:', error);
      return {
        doi,
        isValid: false,
        error: error.message,
        timestamp: Date.now()
      };
    }
  }

  /**
   * Generiert einen Zero-Knowledge-Beweis für die Integrität von Forschungsdaten
   * @param {string} doi - DOI der Publikation
   * @param {Object} data - Forschungsdaten
   * @param {string} privateKey - Privater Schlüssel
   * @returns {Promise<Object>} - Zero-Knowledge-Beweis
   */
  async generateDataIntegrityProof(doi, data, privateKey) {
    try {
      // 1. Generiere den Beweis
      const proof = this.zkVerifier.generateDataIntegrityProof(data, privateKey);

      // 2. Speichere den Beweis auf IPFS
      const proofJson = JSON.stringify(proof);
      const ipfsCid = await this.storeNftMetadataToIpfs({ proof });

      return {
        doi,
        dataHash: proof.dataHash,
        proof,
        ipfsCid,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Datenintegritätsbeweises:', error);
      throw new Error(`Datenintegritätsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }

  /**
   * Verifiziert einen Datenintegritätsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} data - Forschungsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel
   * @returns {Promise<Object>} - Verifizierungsergebnis
   */
  async verifyDataIntegrityProof(proof, data, publicKey) {
    try {
      // Verifiziere den Beweis
      const isValid = this.zkVerifier.verifyDataIntegrityProof(proof, data, publicKey);

      return {
        dataHash: proof.dataHash,
        isValid,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Datenintegritätsbeweises:', error);
      return {
        isValid: false,
        error: error.message,
        timestamp: Date.now()
      };
    }
  }

  /**
   * Registriert eine DOI bei der entsprechenden Agentur (Crossref/DataCite)
   * @param {Object} publication - Publikationsdaten
   * @returns {Promise<Object>} - Registrierungsergebnis
   */
  async registerDoi(publication) {
    // Publikationstyp bestimmen
    const contentType = publication.type || 'article';
    const provider = this.getDoiProviderForContentType(contentType);
    
    // Je nach Provider die richtige Registrierungsmethode aufrufen
    if (provider === 'datacite') {
      return this.registerDataciteDoi(publication);
    } else {
      return this.registerCrossrefDoi(publication);
    }
  }
  
  /**
   * Registriert eine DOI bei Crossref
   * @private
   */
  async registerCrossrefDoi(publication) {
    try {
      // DOI generieren oder vorhandene verwenden
      const doi = publication.doi || this.generateDoi(publication);
      
      // Crossref-XML erstellen
      const crossrefXml = this.createCrossrefXml(doi, publication);
      
      // Authentifizierungsdaten
      const username = this.doiProviders.crossref.username;
      const password = this.doiProviders.crossref.password;
      
      if (!username || !password) {
        throw new Error('Crossref-Anmeldedaten fehlen');
      }
      
      // Crossref-Registrierungsanfrage
      const response = await fetch('https://doi.crossref.org/servlet/deposit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/vnd.crossref.deposit+xml'
        },
        body: crossrefXml,
        // Basic-Auth
        credentials: 'include',
        headers: {
          'Authorization': 'Basic ' + Buffer.from(`${username}:${password}`).toString('base64')
        }
      });
      
      if (!response.ok) {
        throw new Error(`Crossref-Registrierung fehlgeschlagen: ${response.status}`);
      }
      
      const responseData = await response.text();
      
      // Erfolg prüfen
      if (responseData.includes('<msg:status>Success</msg:status>')) {
        return {
          doi,
          status: 'success',
          provider: 'crossref',
          timestamp: Date.now()
        };
      } else {
        throw new Error('Crossref-Registrierung fehlgeschlagen: ' + responseData);
      }
    } catch (error) {
      throw new Error(`Crossref-DOI-Registrierung fehlgeschlagen: ${error.message}`);
    }
  }
  
  /**
   * Registriert eine DOI bei DataCite
   * @private
   */
  async registerDataciteDoi(publication) {
    try {
      // DOI generieren oder vorhandene verwenden
      const doi = publication.doi || this.generateDoi(publication);
      
      // DataCite-JSON erstellen
      const dataciteJson = this.createDataciteJson(doi, publication);
      
      // Authentifizierungsdaten
      const username = this.doiProviders.datacite.username;
      const password = this.doiProviders.datacite.password;
      
      if (!username || !password) {
        throw new Error('DataCite-Anmeldedaten fehlen');
      }
      
      // DataCite-Registrierungsanfrage
      const response = await fetch('https://api.datacite.org/dois', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/vnd.api+json',
          'Authorization': 'Basic ' + Buffer.from(`${username}:${password}`).toString('base64')
        },
        body: JSON.stringify(dataciteJson)
      });
      
      if (!response.ok) {
        throw new Error(`DataCite-Registrierung fehlgeschlagen: ${response.status}`);
      }
      
      const responseData = await response.json();
      
      return {
        doi,
        status: 'success',
        provider: 'datacite',
        timestamp: Date.now(),
        response: responseData
      };
    } catch (error) {
      throw new Error(`DataCite-DOI-Registrierung fehlgeschlagen: ${error.message}`);
    }
  }
  
  /**
   * Erstellt Crossref-XML für DOI-Registrierung
   * @private
   */
  createCrossrefXml(doi, publication) {
    // In einer realen Implementierung würde hier ein vollständiges Crossref-XML erstellt
    // Vereinfachtes Beispiel:
    return `<?xml version="1.0" encoding="UTF-8"?>
<doi_batch xmlns="http://www.crossref.org/schema/4.4.2">
  <head>
    <doi_batch_id>${crypto.randomBytes(8).toString('hex')}</doi_batch_id>
    <timestamp>${Date.now()}</timestamp>
    <depositor>
      <depositor_name>${this.doiProviders.crossref.depositorName}</depositor_name>
      <email_address><EMAIL></email_address>
    </depositor>
    <registrant>DeSci Scholar</registrant>
  </head>
  <body>
    <journal>
      <journal_metadata>
        <full_title>DeSci Scholar Journal</full_title>
      </journal_metadata>
      <journal_article publication_type="full_text">
        <titles>
          <title>${publication.title}</title>
        </titles>
        <contributors>
          ${publication.authors.map(author => `
            <person_name sequence="first" contributor_role="author">
              <given_name>${author.firstName || ''}</given_name>
              <surname>${author.lastName || author.name || ''}</surname>
              ${author.orcid ? `<ORCID>https://orcid.org/${author.orcid}</ORCID>` : ''}
            </person_name>
          `).join('')}
        </contributors>
        <publication_date>
          <year>${publication.year || new Date().getFullYear()}</year>
        </publication_date>
        <doi_data>
          <doi>${doi}</doi>
          <resource>${publication.url || `https://desci-scholar.org/publications/${encodeURIComponent(doi)}`}</resource>
        </doi_data>
      </journal_article>
    </journal>
  </body>
</doi_batch>`;
  }
  
  /**
   * Erstellt DataCite-JSON für DOI-Registrierung
   * @private
   */
  createDataciteJson(doi, publication) {
    return {
      data: {
        type: 'dois',
        attributes: {
          doi: doi,
          creators: publication.authors.map(author => {
            const nameParts = author.name ? author.name.split(' ') : [];
            const lastName = author.lastName || (nameParts.length > 0 ? nameParts.pop() : '');
            const firstName = author.firstName || (nameParts.length > 0 ? nameParts.join(' ') : '');
            
            return {
              name: `${lastName}, ${firstName}`,
              nameType: 'Personal',
              givenName: firstName,
              familyName: lastName,
              nameIdentifiers: author.orcid ? [
                {
                  nameIdentifier: `https://orcid.org/${author.orcid}`,
                  nameIdentifierScheme: 'ORCID',
                  schemeUri: 'https://orcid.org'
                }
              ] : []
            };
          }),
          titles: [
            {
              title: publication.title
            }
          ],
          publisher: 'DeSci Scholar',
          publicationYear: publication.year || new Date().getFullYear(),
          types: {
            resourceTypeGeneral: this.mapPublicationTypeToDataCite(publication.type)
          },
          url: publication.url || `https://desci-scholar.org/publications/${encodeURIComponent(doi)}`,
          schemaVersion: 'http://datacite.org/schema/kernel-4'
        }
      }
    };
  }
  
  /**
   * Konvertiert Publikationstyp ins DataCite-Format
   * @private
   */
  mapPublicationTypeToDataCite(type) {
    const typeMap = {
      'article': 'JournalArticle',
      'dataset': 'Dataset',
      'software': 'Software',
      'book': 'Book',
      'conference': 'ConferencePaper',
      'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager;         }
      }
    };
  }
  
  /**
   * Konvertiert Publikationstyp ins DataCite-Format
   * @private
   */
  mapPublicationTypeToDataCite(type) {
    const typeMap = {
      'article': 'JournalArticle',
      'dataset': 'Dataset',
      'software': 'Software',
      'book': 'Book',
      'conference': 'ConferencePaper',
      'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager;     };
  }
  
  /**
   * Konvertiert Publikationstyp ins DataCite-Format
   * @private
   */
  mapPublicationTypeToDataCite(type) {
    const typeMap = {
      'article': 'JournalArticle',
      'dataset': 'Dataset',
      'software': 'Software',
      'book': 'Book',
      'conference': 'ConferencePaper',
      'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager;         }
      }
    };
  }
  
  /**
   * Konvertiert Publikationstyp ins DataCite-Format
   * @private
   */
  mapPublicationTypeToDataCite(type) {
    const typeMap = {
      'article': 'JournalArticle',
      'dataset': 'Dataset',
      'software': 'Software',
      'book': 'Book',
      'conference': 'ConferencePaper',
      'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager; export default PidManager; 
export default PidManager;       'software': 'Software',
      'book': 'Book',
      'conference': 'ConferencePaper',
      'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager;       'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager;         }
      }
    };
  }
  
  /**
   * Konvertiert Publikationstyp ins DataCite-Format
   * @private
   */
  mapPublicationTypeToDataCite(type) {
    const typeMap = {
      'article': 'JournalArticle',
      'dataset': 'Dataset',
      'software': 'Software',
      'book': 'Book',
      'conference': 'ConferencePaper',
      'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager;   
  /**
   * Konvertiert Publikationstyp ins DataCite-Format
   * @private
   */
  mapPublicationTypeToDataCite(type) {
    const typeMap = {
      'article': 'JournalArticle',
      'dataset': 'Dataset',
      'software': 'Software',
      'book': 'Book',
      'conference': 'ConferencePaper',
      'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager; export default PidManager;         royaltyResult = await royaltyContract.setRoyaltyInfo(
          tokenId,
          royaltyInfo.recipients,
          royaltyInfo.shares
        );
      }

      return {
        tokenId,
        license: licenseResult.license,
        royalty: royaltyResult ? {
          recipients: royaltyResult.recipients,
          shares: royaltyResult.shares,
          royaltyRate: royaltyResult.royaltyRate
        } : null,
        transactions: {
          license: licenseResult.transaction,
          royalty: royaltyResult ? royaltyResult.transaction : null
        }
      };
    } catch (error) {
      console.error('Fehler beim Setzen von Lizenz- und Royalty-Informationen:', error);
      throw new Error(`Lizenz- und Royalty-Konfiguration fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Generiert einen Zero-Knowledge-Beweis für die Autorschaft einer Publikation
   * @param {string} doi - DOI der Publikation
   * @param {Object} author - Autorschaftsdaten
   * @param {string} privateKey - Privater Schlüssel des Autors
   * @returns {Promise<Object>} - Zero-Knowledge-Beweis
   */
  async generateAuthorshipProof(doi, author, privateKey) {
    try {
      // 1. Rufe die Publikationsdaten ab
      const publication = await this.getDoiMetadata(doi);

      if (!publication) {
        throw new Error('Publikationsdaten konnten nicht abgerufen werden');
      }

      // 2. Generiere den Beweis
      const proof = this.zkVerifier.generateAuthorshipProof(publication, author, privateKey);

      // 3. Speichere den Beweis auf IPFS
      const proofJson = JSON.stringify(proof);
      const ipfsCid = await this.storeNftMetadataToIpfs({ proof });

      return {
        doi,
        authorId: author.id,
        proof,
        ipfsCid,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Autorschaftsbeweises:', error);
      throw new Error(`Autorschaftsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }

  /**
   * Verifiziert einen Autorschaftsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {string} doi - DOI der Publikation
   * @param {string} publicKey - Öffentlicher Schlüssel des Autors
   * @returns {Promise<Object>} - Verifizierungsergebnis
   */
  async verifyAuthorshipProof(proof, doi, publicKey) {
    try {
      // 1. Rufe die Publikationsdaten ab
      const publication = await this.getDoiMetadata(doi);

      if (!publication) {
        throw new Error('Publikationsdaten konnten nicht abgerufen werden');
      }

      // 2. Verifiziere den Beweis
      const isValid = this.zkVerifier.verifyAuthorshipProof(proof, publication, publicKey);

      return {
        doi,
        authorId: proof.authorshipClaim.authorId,
        isValid,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Autorschaftsbeweises:', error);
      return {
        doi,
        isValid: false,
        error: error.message,
        timestamp: Date.now()
      };
    }
  }

  /**
   * Generiert einen Zero-Knowledge-Beweis für die Integrität von Forschungsdaten
   * @param {string} doi - DOI der Publikation
   * @param {Object} data - Forschungsdaten
   * @param {string} privateKey - Privater Schlüssel
   * @returns {Promise<Object>} - Zero-Knowledge-Beweis
   */
  async generateDataIntegrityProof(doi, data, privateKey) {
    try {
      // 1. Generiere den Beweis
      const proof = this.zkVerifier.generateDataIntegrityProof(data, privateKey);

      // 2. Speichere den Beweis auf IPFS
      const proofJson = JSON.stringify(proof);
      const ipfsCid = await this.storeNftMetadataToIpfs({ proof });

      return {
        doi,
        dataHash: proof.dataHash,
        proof,
        ipfsCid,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Datenintegritätsbeweises:', error);
      throw new Error(`Datenintegritätsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }

  /**
   * Verifiziert einen Datenintegritätsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} data - Forschungsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel
   * @returns {Promise<Object>} - Verifizierungsergebnis
   */
  async verifyDataIntegrityProof(proof, data, publicKey) {
    try {
      // Verifiziere den Beweis
      const isValid = this.zkVerifier.verifyDataIntegrityProof(proof, data, publicKey);

      return {
        dataHash: proof.dataHash,
        isValid,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Datenintegritätsbeweises:', error);
      return {
        isValid: false,
        error: error.message,
        timestamp: Date.now()
      };
    }
  }

  /**
   * Registriert eine DOI bei der entsprechenden Agentur (Crossref/DataCite)
   * @param {Object} publication - Publikationsdaten
   * @returns {Promise<Object>} - Registrierungsergebnis
   */
  async registerDoi(publication) {
    // Publikationstyp bestimmen
    const contentType = publication.type || 'article';
    const provider = this.getDoiProviderForContentType(contentType);
    
    // Je nach Provider die richtige Registrierungsmethode aufrufen
    if (provider === 'datacite') {
      return this.registerDataciteDoi(publication);
    } else {
      return this.registerCrossrefDoi(publication);
    }
  }
  
  /**
   * Registriert eine DOI bei Crossref
   * @private
   */
  async registerCrossrefDoi(publication) {
    try {
      // DOI generieren oder vorhandene verwenden
      const doi = publication.doi || this.generateDoi(publication);
      
      // Crossref-XML erstellen
      const crossrefXml = this.createCrossrefXml(doi, publication);
      
      // Authentifizierungsdaten
      const username = this.doiProviders.crossref.username;
      const password = this.doiProviders.crossref.password;
      
      if (!username || !password) {
        throw new Error('Crossref-Anmeldedaten fehlen');
      }
      
      // Crossref-Registrierungsanfrage
      const response = await fetch('https://doi.crossref.org/servlet/deposit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/vnd.crossref.deposit+xml'
        },
        body: crossrefXml,
        // Basic-Auth
        credentials: 'include',
        headers: {
          'Authorization': 'Basic ' + Buffer.from(`${username}:${password}`).toString('base64')
        }
      });
      
      if (!response.ok) {
        throw new Error(`Crossref-Registrierung fehlgeschlagen: ${response.status}`);
      }
      
      const responseData = await response.text();
      
      // Erfolg prüfen
      if (responseData.includes('<msg:status>Success</msg:status>')) {
        return {
          doi,
          status: 'success',
          provider: 'crossref',
          timestamp: Date.now()
        };
      } else {
        throw new Error('Crossref-Registrierung fehlgeschlagen: ' + responseData);
      }
    } catch (error) {
      throw new Error(`Crossref-DOI-Registrierung fehlgeschlagen: ${error.message}`);
    }
  }
  
  /**
   * Registriert eine DOI bei DataCite
   * @private
   */
  async registerDataciteDoi(publication) {
    try {
      // DOI generieren oder vorhandene verwenden
      const doi = publication.doi || this.generateDoi(publication);
      
      // DataCite-JSON erstellen
      const dataciteJson = this.createDataciteJson(doi, publication);
      
      // Authentifizierungsdaten
      const username = this.doiProviders.datacite.username;
      const password = this.doiProviders.datacite.password;
      
      if (!username || !password) {
        throw new Error('DataCite-Anmeldedaten fehlen');
      }
      
      // DataCite-Registrierungsanfrage
      const response = await fetch('https://api.datacite.org/dois', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/vnd.api+json',
          'Authorization': 'Basic ' + Buffer.from(`${username}:${password}`).toString('base64')
        },
        body: JSON.stringify(dataciteJson)
      });
      
      if (!response.ok) {
        throw new Error(`DataCite-Registrierung fehlgeschlagen: ${response.status}`);
      }
      
      const responseData = await response.json();
      
      return {
        doi,
        status: 'success',
        provider: 'datacite',
        timestamp: Date.now(),
        response: responseData
      };
    } catch (error) {
      throw new Error(`DataCite-DOI-Registrierung fehlgeschlagen: ${error.message}`);
    }
  }
  
  /**
   * Erstellt Crossref-XML für DOI-Registrierung
   * @private
   */
  createCrossrefXml(doi, publication) {
    // In einer realen Implementierung würde hier ein vollständiges Crossref-XML erstellt
    // Vereinfachtes Beispiel:
    return `<?xml version="1.0" encoding="UTF-8"?>
<doi_batch xmlns="http://www.crossref.org/schema/4.4.2">
  <head>
    <doi_batch_id>${crypto.randomBytes(8).toString('hex')}</doi_batch_id>
    <timestamp>${Date.now()}</timestamp>
    <depositor>
      <depositor_name>${this.doiProviders.crossref.depositorName}</depositor_name>
      <email_address><EMAIL></email_address>
    </depositor>
    <registrant>DeSci Scholar</registrant>
  </head>
  <body>
    <journal>
      <journal_metadata>
        <full_title>DeSci Scholar Journal</full_title>
      </journal_metadata>
      <journal_article publication_type="full_text">
        <titles>
          <title>${publication.title}</title>
        </titles>
        <contributors>
          ${publication.authors.map(author => `
            <person_name sequence="first" contributor_role="author">
              <given_name>${author.firstName || ''}</given_name>
              <surname>${author.lastName || author.name || ''}</surname>
              ${author.orcid ? `<ORCID>https://orcid.org/${author.orcid}</ORCID>` : ''}
            </person_name>
          `).join('')}
        </contributors>
        <publication_date>
          <year>${publication.year || new Date().getFullYear()}</year>
        </publication_date>
        <doi_data>
          <doi>${doi}</doi>
          <resource>${publication.url || `https://desci-scholar.org/publications/${encodeURIComponent(doi)}`}</resource>
        </doi_data>
      </journal_article>
    </journal>
  </body>
</doi_batch>`;
  }
  
  /**
   * Erstellt DataCite-JSON für DOI-Registrierung
   * @private
   */
  createDataciteJson(doi, publication) {
    return {
      data: {
        type: 'dois',
        attributes: {
          doi: doi,
          creators: publication.authors.map(author => {
            const nameParts = author.name ? author.name.split(' ') : [];
            const lastName = author.lastName || (nameParts.length > 0 ? nameParts.pop() : '');
            const firstName = author.firstName || (nameParts.length > 0 ? nameParts.join(' ') : '');
            
            return {
              name: `${lastName}, ${firstName}`,
              nameType: 'Personal',
              givenName: firstName,
              familyName: lastName,
              nameIdentifiers: author.orcid ? [
                {
                  nameIdentifier: `https://orcid.org/${author.orcid}`,
                  nameIdentifierScheme: 'ORCID',
                  schemeUri: 'https://orcid.org'
                }
              ] : []
            };
          }),
          titles: [
            {
              title: publication.title
            }
          ],
          publisher: 'DeSci Scholar',
          publicationYear: publication.year || new Date().getFullYear(),
          types: {
            resourceTypeGeneral: this.mapPublicationTypeToDataCite(publication.type)
          },
          url: publication.url || `https://desci-scholar.org/publications/${encodeURIComponent(doi)}`,
          schemaVersion: 'http://datacite.org/schema/kernel-4'
        }
      }
    };
  }
  
  /**
   * Konvertiert Publikationstyp ins DataCite-Format
   * @private
   */
  mapPublicationTypeToDataCite(type) {
    const typeMap = {
      'article': 'JournalArticle',
      'dataset': 'Dataset',
      'software': 'Software',
      'book': 'Book',
      'conference': 'ConferencePaper',
      'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager;         }
      }
    };
  }
  
  /**
   * Konvertiert Publikationstyp ins DataCite-Format
   * @private
   */
  mapPublicationTypeToDataCite(type) {
    const typeMap = {
      'article': 'JournalArticle',
      'dataset': 'Dataset',
      'software': 'Software',
      'book': 'Book',
      'conference': 'ConferencePaper',
      'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager;     };
  }
  
  /**
   * Konvertiert Publikationstyp ins DataCite-Format
   * @private
   */
  mapPublicationTypeToDataCite(type) {
    const typeMap = {
      'article': 'JournalArticle',
      'dataset': 'Dataset',
      'software': 'Software',
      'book': 'Book',
      'conference': 'ConferencePaper',
      'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager;         }
      }
    };
  }
  
  /**
   * Konvertiert Publikationstyp ins DataCite-Format
   * @private
   */
  mapPublicationTypeToDataCite(type) {
    const typeMap = {
      'article': 'JournalArticle',
      'dataset': 'Dataset',
      'software': 'Software',
      'book': 'Book',
      'conference': 'ConferencePaper',
      'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager; export default PidManager; 
export default PidManager;       'software': 'Software',
      'book': 'Book',
      'conference': 'ConferencePaper',
      'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager;       'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager;         }
      }
    };
  }
  
  /**
   * Konvertiert Publikationstyp ins DataCite-Format
   * @private
   */
  mapPublicationTypeToDataCite(type) {
    const typeMap = {
      'article': 'JournalArticle',
      'dataset': 'Dataset',
      'software': 'Software',
      'book': 'Book',
      'conference': 'ConferencePaper',
      'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager;       const proofJson = JSON.stringify(proof);
      const ipfsCid = await this.storeNftMetadataToIpfs({ proof });

      return {
        doi,
        authorId: author.id,
        proof,
        ipfsCid,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Autorschaftsbeweises:', error);
      throw new Error(`Autorschaftsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }

  /**
   * Verifiziert einen Autorschaftsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {string} doi - DOI der Publikation
   * @param {string} publicKey - Öffentlicher Schlüssel des Autors
   * @returns {Promise<Object>} - Verifizierungsergebnis
   */
  async verifyAuthorshipProof(proof, doi, publicKey) {
    try {
      // 1. Rufe die Publikationsdaten ab
      const publication = await this.getDoiMetadata(doi);

      if (!publication) {
        throw new Error('Publikationsdaten konnten nicht abgerufen werden');
      }

      // 2. Verifiziere den Beweis
      const isValid = this.zkVerifier.verifyAuthorshipProof(proof, publication, publicKey);

      return {
        doi,
        authorId: proof.authorshipClaim.authorId,
        isValid,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Autorschaftsbeweises:', error);
      return {
        doi,
        isValid: false,
        error: error.message,
        timestamp: Date.now()
      };
    }
  }

  /**
   * Generiert einen Zero-Knowledge-Beweis für die Integrität von Forschungsdaten
   * @param {string} doi - DOI der Publikation
   * @param {Object} data - Forschungsdaten
   * @param {string} privateKey - Privater Schlüssel
   * @returns {Promise<Object>} - Zero-Knowledge-Beweis
   */
  async generateDataIntegrityProof(doi, data, privateKey) {
    try {
      // 1. Generiere den Beweis
      const proof = this.zkVerifier.generateDataIntegrityProof(data, privateKey);

      // 2. Speichere den Beweis auf IPFS
      const proofJson = JSON.stringify(proof);
      const ipfsCid = await this.storeNftMetadataToIpfs({ proof });

      return {
        doi,
        dataHash: proof.dataHash,
        proof,
        ipfsCid,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Datenintegritätsbeweises:', error);
      throw new Error(`Datenintegritätsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }

  /**
   * Verifiziert einen Datenintegritätsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} data - Forschungsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel
   * @returns {Promise<Object>} - Verifizierungsergebnis
   */
  async verifyDataIntegrityProof(proof, data, publicKey) {
    try {
      // Verifiziere den Beweis
      const isValid = this.zkVerifier.verifyDataIntegrityProof(proof, data, publicKey);

      return {
        dataHash: proof.dataHash,
        isValid,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Datenintegritätsbeweises:', error);
      return {
        isValid: false,
        error: error.message,
        timestamp: Date.now()
      };
    }
  }

  /**
   * Registriert eine DOI bei der entsprechenden Agentur (Crossref/DataCite)
   * @param {Object} publication - Publikationsdaten
   * @returns {Promise<Object>} - Registrierungsergebnis
   */
  async registerDoi(publication) {
    // Publikationstyp bestimmen
    const contentType = publication.type || 'article';
    const provider = this.getDoiProviderForContentType(contentType);
    
    // Je nach Provider die richtige Registrierungsmethode aufrufen
    if (provider === 'datacite') {
      return this.registerDataciteDoi(publication);
    } else {
      return this.registerCrossrefDoi(publication);
    }
  }
  
  /**
   * Registriert eine DOI bei Crossref
   * @private
   */
  async registerCrossrefDoi(publication) {
    try {
      // DOI generieren oder vorhandene verwenden
      const doi = publication.doi || this.generateDoi(publication);
      
      // Crossref-XML erstellen
      const crossrefXml = this.createCrossrefXml(doi, publication);
      
      // Authentifizierungsdaten
      const username = this.doiProviders.crossref.username;
      const password = this.doiProviders.crossref.password;
      
      if (!username || !password) {
        throw new Error('Crossref-Anmeldedaten fehlen');
      }
      
      // Crossref-Registrierungsanfrage
      const response = await fetch('https://doi.crossref.org/servlet/deposit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/vnd.crossref.deposit+xml'
        },
        body: crossrefXml,
        // Basic-Auth
        credentials: 'include',
        headers: {
          'Authorization': 'Basic ' + Buffer.from(`${username}:${password}`).toString('base64')
        }
      });
      
      if (!response.ok) {
        throw new Error(`Crossref-Registrierung fehlgeschlagen: ${response.status}`);
      }
      
      const responseData = await response.text();
      
      // Erfolg prüfen
      if (responseData.includes('<msg:status>Success</msg:status>')) {
        return {
          doi,
          status: 'success',
          provider: 'crossref',
          timestamp: Date.now()
        };
      } else {
        throw new Error('Crossref-Registrierung fehlgeschlagen: ' + responseData);
      }
    } catch (error) {
      throw new Error(`Crossref-DOI-Registrierung fehlgeschlagen: ${error.message}`);
    }
  }
  
  /**
   * Registriert eine DOI bei DataCite
   * @private
   */
  async registerDataciteDoi(publication) {
    try {
      // DOI generieren oder vorhandene verwenden
      const doi = publication.doi || this.generateDoi(publication);
      
      // DataCite-JSON erstellen
      const dataciteJson = this.createDataciteJson(doi, publication);
      
      // Authentifizierungsdaten
      const username = this.doiProviders.datacite.username;
      const password = this.doiProviders.datacite.password;
      
      if (!username || !password) {
        throw new Error('DataCite-Anmeldedaten fehlen');
      }
      
      // DataCite-Registrierungsanfrage
      const response = await fetch('https://api.datacite.org/dois', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/vnd.api+json',
          'Authorization': 'Basic ' + Buffer.from(`${username}:${password}`).toString('base64')
        },
        body: JSON.stringify(dataciteJson)
      });
      
      if (!response.ok) {
        throw new Error(`DataCite-Registrierung fehlgeschlagen: ${response.status}`);
      }
      
      const responseData = await response.json();
      
      return {
        doi,
        status: 'success',
        provider: 'datacite',
        timestamp: Date.now(),
        response: responseData
      };
    } catch (error) {
      throw new Error(`DataCite-DOI-Registrierung fehlgeschlagen: ${error.message}`);
    }
  }
  
  /**
   * Erstellt Crossref-XML für DOI-Registrierung
   * @private
   */
  createCrossrefXml(doi, publication) {
    // In einer realen Implementierung würde hier ein vollständiges Crossref-XML erstellt
    // Vereinfachtes Beispiel:
    return `<?xml version="1.0" encoding="UTF-8"?>
<doi_batch xmlns="http://www.crossref.org/schema/4.4.2">
  <head>
    <doi_batch_id>${crypto.randomBytes(8).toString('hex')}</doi_batch_id>
    <timestamp>${Date.now()}</timestamp>
    <depositor>
      <depositor_name>${this.doiProviders.crossref.depositorName}</depositor_name>
      <email_address><EMAIL></email_address>
    </depositor>
    <registrant>DeSci Scholar</registrant>
  </head>
  <body>
    <journal>
      <journal_metadata>
        <full_title>DeSci Scholar Journal</full_title>
      </journal_metadata>
      <journal_article publication_type="full_text">
        <titles>
          <title>${publication.title}</title>
        </titles>
        <contributors>
          ${publication.authors.map(author => `
            <person_name sequence="first" contributor_role="author">
              <given_name>${author.firstName || ''}</given_name>
              <surname>${author.lastName || author.name || ''}</surname>
              ${author.orcid ? `<ORCID>https://orcid.org/${author.orcid}</ORCID>` : ''}
            </person_name>
          `).join('')}
        </contributors>
        <publication_date>
          <year>${publication.year || new Date().getFullYear()}</year>
        </publication_date>
        <doi_data>
          <doi>${doi}</doi>
          <resource>${publication.url || `https://desci-scholar.org/publications/${encodeURIComponent(doi)}`}</resource>
        </doi_data>
      </journal_article>
    </journal>
  </body>
</doi_batch>`;
  }
  
  /**
   * Erstellt DataCite-JSON für DOI-Registrierung
   * @private
   */
  createDataciteJson(doi, publication) {
    return {
      data: {
        type: 'dois',
        attributes: {
          doi: doi,
          creators: publication.authors.map(author => {
            const nameParts = author.name ? author.name.split(' ') : [];
            const lastName = author.lastName || (nameParts.length > 0 ? nameParts.pop() : '');
            const firstName = author.firstName || (nameParts.length > 0 ? nameParts.join(' ') : '');
            
            return {
              name: `${lastName}, ${firstName}`,
              nameType: 'Personal',
              givenName: firstName,
              familyName: lastName,
              nameIdentifiers: author.orcid ? [
                {
                  nameIdentifier: `https://orcid.org/${author.orcid}`,
                  nameIdentifierScheme: 'ORCID',
                  schemeUri: 'https://orcid.org'
                }
              ] : []
            };
          }),
          titles: [
            {
              title: publication.title
            }
          ],
          publisher: 'DeSci Scholar',
          publicationYear: publication.year || new Date().getFullYear(),
          types: {
            resourceTypeGeneral: this.mapPublicationTypeToDataCite(publication.type)
          },
          url: publication.url || `https://desci-scholar.org/publications/${encodeURIComponent(doi)}`,
          schemaVersion: 'http://datacite.org/schema/kernel-4'
        }
      }
    };
  }
  
  /**
   * Konvertiert Publikationstyp ins DataCite-Format
   * @private
   */
  mapPublicationTypeToDataCite(type) {
    const typeMap = {
      'article': 'JournalArticle',
      'dataset': 'Dataset',
      'software': 'Software',
      'book': 'Book',
      'conference': 'ConferencePaper',
      'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager;         }
      }
    };
  }
  
  /**
   * Konvertiert Publikationstyp ins DataCite-Format
   * @private
   */
  mapPublicationTypeToDataCite(type) {
    const typeMap = {
      'article': 'JournalArticle',
      'dataset': 'Dataset',
      'software': 'Software',
      'book': 'Book',
      'conference': 'ConferencePaper',
      'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager;     };
  }
  
  /**
   * Konvertiert Publikationstyp ins DataCite-Format
   * @private
   */
  mapPublicationTypeToDataCite(type) {
    const typeMap = {
      'article': 'JournalArticle',
      'dataset': 'Dataset',
      'software': 'Software',
      'book': 'Book',
      'conference': 'ConferencePaper',
      'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager;         }
      }
    };
  }
  
  /**
   * Konvertiert Publikationstyp ins DataCite-Format
   * @private
   */
  mapPublicationTypeToDataCite(type) {
    const typeMap = {
      'article': 'JournalArticle',
      'dataset': 'Dataset',
      'software': 'Software',
      'book': 'Book',
      'conference': 'ConferencePaper',
      'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager; export default PidManager; 
export default PidManager;       'software': 'Software',
      'book': 'Book',
      'conference': 'ConferencePaper',
      'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager;       'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager;         }
      }
    };
  }
  
  /**
   * Konvertiert Publikationstyp ins DataCite-Format
   * @private
   */
  mapPublicationTypeToDataCite(type) {
    const typeMap = {
      'article': 'JournalArticle',
      'dataset': 'Dataset',
      'software': 'Software',
      'book': 'Book',
      'conference': 'ConferencePaper',
      'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager; export default PidManager;       'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager;       'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager;         }
      }
    };
  }
  
  /**
   * Konvertiert Publikationstyp ins DataCite-Format
   * @private
   */
  mapPublicationTypeToDataCite(type) {
    const typeMap = {
      'article': 'JournalArticle',
      'dataset': 'Dataset',
      'software': 'Software',
      'book': 'Book',
      'conference': 'ConferencePaper',
      'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager;   }
  
  /**
   * Konvertiert Publikationstyp ins DataCite-Format
   * @private
   */
  mapPublicationTypeToDataCite(type) {
    const typeMap = {
      'article': 'JournalArticle',
      'dataset': 'Dataset',
      'software': 'Software',
      'book': 'Book',
      'conference': 'ConferencePaper',
      'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager;       'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager;       'software': 'Software',
      'book': 'Book',
      'conference': 'ConferencePaper',
      'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager;       'software': 'Software',
      'book': 'Book',
      'conference': 'ConferencePaper',
      'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager;       'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager;       'software': 'Software',
      'book': 'Book',
      'conference': 'ConferencePaper',
      'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager; export default PidManager;         proof,
        ipfsCid,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Fehler bei der Generierung des Datenintegritätsbeweises:', error);
      throw new Error(`Datenintegritätsbeweis konnte nicht generiert werden: ${error.message}`);
    }
  }

  /**
   * Verifiziert einen Datenintegritätsbeweis
   * @param {Object} proof - Zero-Knowledge-Beweis
   * @param {Object} data - Forschungsdaten
   * @param {string} publicKey - Öffentlicher Schlüssel
   * @returns {Promise<Object>} - Verifizierungsergebnis
   */
  async verifyDataIntegrityProof(proof, data, publicKey) {
    try {
      // Verifiziere den Beweis
      const isValid = this.zkVerifier.verifyDataIntegrityProof(proof, data, publicKey);

      return {
        dataHash: proof.dataHash,
        isValid,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Fehler bei der Verifizierung des Datenintegritätsbeweises:', error);
      return {
        isValid: false,
        error: error.message,
        timestamp: Date.now()
      };
    }
  }

  /**
   * Registriert eine DOI bei der entsprechenden Agentur (Crossref/DataCite)
   * @param {Object} publication - Publikationsdaten
   * @returns {Promise<Object>} - Registrierungsergebnis
   */
  async registerDoi(publication) {
    // Publikationstyp bestimmen
    const contentType = publication.type || 'article';
    const provider = this.getDoiProviderForContentType(contentType);
    
    // Je nach Provider die richtige Registrierungsmethode aufrufen
    if (provider === 'datacite') {
      return this.registerDataciteDoi(publication);
    } else {
      return this.registerCrossrefDoi(publication);
    }
  }
  
  /**
   * Registriert eine DOI bei Crossref
   * @private
   */
  async registerCrossrefDoi(publication) {
    try {
      // DOI generieren oder vorhandene verwenden
      const doi = publication.doi || this.generateDoi(publication);
      
      // Crossref-XML erstellen
      const crossrefXml = this.createCrossrefXml(doi, publication);
      
      // Authentifizierungsdaten
      const username = this.doiProviders.crossref.username;
      const password = this.doiProviders.crossref.password;
      
      if (!username || !password) {
        throw new Error('Crossref-Anmeldedaten fehlen');
      }
      
      // Crossref-Registrierungsanfrage
      const response = await fetch('https://doi.crossref.org/servlet/deposit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/vnd.crossref.deposit+xml'
        },
        body: crossrefXml,
        // Basic-Auth
        credentials: 'include',
        headers: {
          'Authorization': 'Basic ' + Buffer.from(`${username}:${password}`).toString('base64')
        }
      });
      
      if (!response.ok) {
        throw new Error(`Crossref-Registrierung fehlgeschlagen: ${response.status}`);
      }
      
      const responseData = await response.text();
      
      // Erfolg prüfen
      if (responseData.includes('<msg:status>Success</msg:status>')) {
        return {
          doi,
          status: 'success',
          provider: 'crossref',
          timestamp: Date.now()
        };
      } else {
        throw new Error('Crossref-Registrierung fehlgeschlagen: ' + responseData);
      }
    } catch (error) {
      throw new Error(`Crossref-DOI-Registrierung fehlgeschlagen: ${error.message}`);
    }
  }
  
  /**
   * Registriert eine DOI bei DataCite
   * @private
   */
  async registerDataciteDoi(publication) {
    try {
      // DOI generieren oder vorhandene verwenden
      const doi = publication.doi || this.generateDoi(publication);
      
      // DataCite-JSON erstellen
      const dataciteJson = this.createDataciteJson(doi, publication);
      
      // Authentifizierungsdaten
      const username = this.doiProviders.datacite.username;
      const password = this.doiProviders.datacite.password;
      
      if (!username || !password) {
        throw new Error('DataCite-Anmeldedaten fehlen');
      }
      
      // DataCite-Registrierungsanfrage
      const response = await fetch('https://api.datacite.org/dois', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/vnd.api+json',
          'Authorization': 'Basic ' + Buffer.from(`${username}:${password}`).toString('base64')
        },
        body: JSON.stringify(dataciteJson)
      });
      
      if (!response.ok) {
        throw new Error(`DataCite-Registrierung fehlgeschlagen: ${response.status}`);
      }
      
      const responseData = await response.json();
      
      return {
        doi,
        status: 'success',
        provider: 'datacite',
        timestamp: Date.now(),
        response: responseData
      };
    } catch (error) {
      throw new Error(`DataCite-DOI-Registrierung fehlgeschlagen: ${error.message}`);
    }
  }
  
  /**
   * Erstellt Crossref-XML für DOI-Registrierung
   * @private
   */
  createCrossrefXml(doi, publication) {
    // In einer realen Implementierung würde hier ein vollständiges Crossref-XML erstellt
    // Vereinfachtes Beispiel:
    return `<?xml version="1.0" encoding="UTF-8"?>
<doi_batch xmlns="http://www.crossref.org/schema/4.4.2">
  <head>
    <doi_batch_id>${crypto.randomBytes(8).toString('hex')}</doi_batch_id>
    <timestamp>${Date.now()}</timestamp>
    <depositor>
      <depositor_name>${this.doiProviders.crossref.depositorName}</depositor_name>
      <email_address><EMAIL></email_address>
    </depositor>
    <registrant>DeSci Scholar</registrant>
  </head>
  <body>
    <journal>
      <journal_metadata>
        <full_title>DeSci Scholar Journal</full_title>
      </journal_metadata>
      <journal_article publication_type="full_text">
        <titles>
          <title>${publication.title}</title>
        </titles>
        <contributors>
          ${publication.authors.map(author => `
            <person_name sequence="first" contributor_role="author">
              <given_name>${author.firstName || ''}</given_name>
              <surname>${author.lastName || author.name || ''}</surname>
              ${author.orcid ? `<ORCID>https://orcid.org/${author.orcid}</ORCID>` : ''}
            </person_name>
          `).join('')}
        </contributors>
        <publication_date>
          <year>${publication.year || new Date().getFullYear()}</year>
        </publication_date>
        <doi_data>
          <doi>${doi}</doi>
          <resource>${publication.url || `https://desci-scholar.org/publications/${encodeURIComponent(doi)}`}</resource>
        </doi_data>
      </journal_article>
    </journal>
  </body>
</doi_batch>`;
  }
  
  /**
   * Erstellt DataCite-JSON für DOI-Registrierung
   * @private
   */
  createDataciteJson(doi, publication) {
    return {
      data: {
        type: 'dois',
        attributes: {
          doi: doi,
          creators: publication.authors.map(author => {
            const nameParts = author.name ? author.name.split(' ') : [];
            const lastName = author.lastName || (nameParts.length > 0 ? nameParts.pop() : '');
            const firstName = author.firstName || (nameParts.length > 0 ? nameParts.join(' ') : '');
            
            return {
              name: `${lastName}, ${firstName}`,
              nameType: 'Personal',
              givenName: firstName,
              familyName: lastName,
              nameIdentifiers: author.orcid ? [
                {
                  nameIdentifier: `https://orcid.org/${author.orcid}`,
                  nameIdentifierScheme: 'ORCID',
                  schemeUri: 'https://orcid.org'
                }
              ] : []
            };
          }),
          titles: [
            {
              title: publication.title
            }
          ],
          publisher: 'DeSci Scholar',
          publicationYear: publication.year || new Date().getFullYear(),
          types: {
            resourceTypeGeneral: this.mapPublicationTypeToDataCite(publication.type)
          },
          url: publication.url || `https://desci-scholar.org/publications/${encodeURIComponent(doi)}`,
          schemaVersion: 'http://datacite.org/schema/kernel-4'
        }
      }
    };
  }
  
  /**
   * Konvertiert Publikationstyp ins DataCite-Format
   * @private
   */
  mapPublicationTypeToDataCite(type) {
    const typeMap = {
      'article': 'JournalArticle',
      'dataset': 'Dataset',
      'software': 'Software',
      'book': 'Book',
      'conference': 'ConferencePaper',
      'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager;         }
      }
    };
  }
  
  /**
   * Konvertiert Publikationstyp ins DataCite-Format
   * @private
   */
  mapPublicationTypeToDataCite(type) {
    const typeMap = {
      'article': 'JournalArticle',
      'dataset': 'Dataset',
      'software': 'Software',
      'book': 'Book',
      'conference': 'ConferencePaper',
      'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager;     };
  }
  
  /**
   * Konvertiert Publikationstyp ins DataCite-Format
   * @private
   */
  mapPublicationTypeToDataCite(type) {
    const typeMap = {
      'article': 'JournalArticle',
      'dataset': 'Dataset',
      'software': 'Software',
      'book': 'Book',
      'conference': 'ConferencePaper',
      'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager;         }
      }
    };
  }
  
  /**
   * Konvertiert Publikationstyp ins DataCite-Format
   * @private
   */
  mapPublicationTypeToDataCite(type) {
    const typeMap = {
      'article': 'JournalArticle',
      'dataset': 'Dataset',
      'software': 'Software',
      'book': 'Book',
      'conference': 'ConferencePaper',
      'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager; export default PidManager; 
export default PidManager;       'software': 'Software',
      'book': 'Book',
      'conference': 'ConferencePaper',
      'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager;       'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager;         }
      }
    };
  }
  
  /**
   * Konvertiert Publikationstyp ins DataCite-Format
   * @private
   */
  mapPublicationTypeToDataCite(type) {
    const typeMap = {
      'article': 'JournalArticle',
      'dataset': 'Dataset',
      'software': 'Software',
      'book': 'Book',
      'conference': 'ConferencePaper',
      'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager; export default PidManager;       'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager;       'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager;         }
      }
    };
  }
  
  /**
   * Konvertiert Publikationstyp ins DataCite-Format
   * @private
   */
  mapPublicationTypeToDataCite(type) {
    const typeMap = {
      'article': 'JournalArticle',
      'dataset': 'Dataset',
      'software': 'Software',
      'book': 'Book',
      'conference': 'ConferencePaper',
      'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager;   }
  
  /**
   * Konvertiert Publikationstyp ins DataCite-Format
   * @private
   */
  mapPublicationTypeToDataCite(type) {
    const typeMap = {
      'article': 'JournalArticle',
      'dataset': 'Dataset',
      'software': 'Software',
      'book': 'Book',
      'conference': 'ConferencePaper',
      'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager;       'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager;       'software': 'Software',
      'book': 'Book',
      'conference': 'ConferencePaper',
      'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager;       'software': 'Software',
      'book': 'Book',
      'conference': 'ConferencePaper',
      'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager;       'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager;       'software': 'Software',
      'book': 'Book',
      'conference': 'ConferencePaper',
      'preprint': 'Preprint'
    };
    
    return typeMap[type] || 'Text';
  }
}

export default PidManager; export default PidManager; export default PidManager; export default PidManager; export default PidManager; export default PidManager; export default PidManager; export default PidManager; export default PidManager; export default PidManager; export default PidManager; export default PidManager; 