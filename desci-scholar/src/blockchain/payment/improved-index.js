/**
 * @fileoverview Verbesserte Zahlungsintegration für DeSci-Scholar
 * Dieses Modul verarbeitet Zahlungen mit Kryptowährungen (Polkadot/DOT) 
 * und traditionellen Zahlungsmethoden (Stripe, PayPal) mit erhöhter Zuverlässigkeit
 * und besserer Fehlerbehandlung.
 */

import { decodeAddress, encodeAddress } from '@polkadot/util-crypto';
import { hexToU8a, isHex } from '@polkadot/util';
import PolkadotClient from '../polkadot-improved/index.js';
import axios from 'axios';
import { createLogger, format, transports } from 'winston';
import { LRUCache } from 'lru-cache';
import { setTimeout } from 'timers/promises';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Logger konfigurieren
const logger = createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: format.combine(
    format.timestamp(),
    format.json()
  ),
  defaultMeta: { service: 'payment-processor' },
  transports: [
    new transports.Console({
      format: format.combine(
        format.colorize(),
        format.simple()
      )
    }),
    new transports.File({ 
      filename: 'logs/payment-error.log', 
      level: 'error' 
    }),
    new transports.File({ 
      filename: 'logs/payment-combined.log' 
    })
  ]
});

// Konfiguration für externe API-Aufrufe
const API_CONFIG = {
  maxRetries: 3,
  retryDelay: 1000, // ms
  timeout: 10000, // ms
};

// Cache für Wechselkurse
const exchangeRateCache = new LRUCache({
  max: 10, // Anzahl der zu speichernden Einträge
  ttl: 300000, // 5 Minuten in Millisekunden
});

/**
 * Verbesserte PaymentProcessor-Klasse für die Verarbeitung verschiedener Zahlungsmethoden
 */
class PaymentProcessor {
  /**
   * Erstellt eine Instanz von PaymentProcessor.
   * @param {Object} options - Optionale Konfigurationsparameter
   */
  constructor(options = {}) {
    this.polkadotClient = new PolkadotClient(options.polkadotOptions);
    
    // API-Keys für traditionelle Zahlungsabwickler
    this.stripeSecretKey = process.env.STRIPE_SECRET_KEY;
    this.paypalClientId = process.env.PAYPAL_CLIENT_ID;
    this.paypalSecret = process.env.PAYPAL_SECRET;
    
    // Bestimme PayPal-Umgebung mit Fallback auf Sandbox
    const paypalEnvironment = (process.env.PAYPAL_ENVIRONMENT || 'sandbox').toLowerCase();
    
    // Zahlungs-Gateway-URLs
    this.stripeApiUrl = options.stripeApiUrl || 'https://api.stripe.com/v1';
    this.paypalApiUrl = paypalEnvironment === 'production' 
      ? 'https://api.paypal.com' 
      : 'https://api.sandbox.paypal.com';
      
    // Wechselkurs-API für DOT-Konvertierung
    this.exchangeRateApiUrl = options.exchangeRateApiUrl || 'https://api.coingecko.com/api/v3';
    
    // Verarbeitungsstatus speichern
    this.isInitialized = false;
    
    // Optionen zur Konfiguration des Verhaltens
    this.options = {
      maxRetries: options.maxRetries || API_CONFIG.maxRetries,
      retryDelay: options.retryDelay || API_CONFIG.retryDelay,
      timeout: options.timeout || API_CONFIG.timeout,
      ...options
    };
    
    // Statistik-Tracking
    this.stats = {
      paymentsProcessed: {
        polkadot: 0,
        stripe: 0,
        paypal: 0
      },
      apiCalls: {
        stripe: 0,
        paypal: 0,
        exchangeRate: 0
      },
      errors: {
        polkadot: 0,
        stripe: 0,
        paypal: 0,
        exchangeRate: 0
      }
    };
  }
  
  /**
   * Zahlungsabwickler initialisieren
   * @returns {Promise<void>}
   */
  async init() {
    if (this.isInitialized) {
      logger.debug('PaymentProcessor ist bereits initialisiert');
      return;
    }
    
    try {
      // Polkadot-Client initialisieren
      await this.polkadotClient.init();
      this.isInitialized = true;
      logger.info('PaymentProcessor erfolgreich initialisiert');
    } catch (error) {
      logger.error(`Fehler bei PaymentProcessor-Initialisierung: ${error.message}`, { error });
      throw new Error(`PaymentProcessor-Initialisierung fehlgeschlagen: ${error.message}`);
    }
  }
  
  /**
   * Führt einen HTTP-Request mit Wiederholungslogik durch
   * @param {Function} requestFn - Die auszuführende Request-Funktion
   * @param {String} operationType - Typ der Operation für Fehlertracking
   * @returns {Promise<any>} Antwortdaten
   */
  async executeWithRetry(requestFn, operationType) {
    let lastError = null;
    
    for (let attempt = 0; attempt < this.options.maxRetries; attempt++) {
      try {
        // Bei Wiederholungsversuchen warten
        if (attempt > 0) {
          const delay = this.options.retryDelay * Math.pow(2, attempt - 1);
          logger.debug(`Wiederhole ${operationType} (Versuch ${attempt+1}/${this.options.maxRetries}) nach ${delay}ms`);
          await setTimeout(delay);
        }
        
        // Anfrage ausführen
        return await requestFn();
      } catch (error) {
        lastError = error;
        
        // Überprüfen, ob der Fehler wiederholbar ist
        const isRetryable = 
          error.code === 'ECONNRESET' || 
          error.code === 'ETIMEDOUT' ||
          error.message.includes('timeout') ||
          error.message.includes('rate limit') ||
          (error.response && (
            error.response.status === 429 || // Zu viele Anfragen
            error.response.status === 503 || // Service nicht verfügbar
            error.response.status === 500 || // Interner Serverfehler
            error.response.status === 502 || // Bad Gateway
            error.response.status === 504)); // Gateway Timeout
        
        if (!isRetryable) {
          logger.error(`Nicht wiederholbarer Fehler bei ${operationType}`, { 
            error: error.message, 
            response: error.response?.data 
          });
          this.stats.errors[operationType] = (this.stats.errors[operationType] || 0) + 1;
          throw error;
        }
        
        this.stats.errors[operationType] = (this.stats.errors[operationType] || 0) + 1;
        logger.warn(`${operationType} fehlgeschlagen, Versuch ${attempt+1}/${this.options.maxRetries}`, { 
          error: error.message,
          response: error.response?.data
        });
      }
    }
    
    // Alle Wiederholungsversuche erschöpft
    logger.error(`${operationType} fehlgeschlagen nach ${this.options.maxRetries} Versuchen`, { 
      error: lastError.message 
    });
    throw lastError || new Error(`${operationType} fehlgeschlagen nach Wiederholungsversuchen`);
  }
  
  /**
   * Validiert eine Polkadot-Adresse
   * @param {string} address - Zu validierende Polkadot-Adresse
   * @returns {boolean} Ob die Adresse gültig ist
   */
  isValidPolkadotAddress(address) {
    try {
      encodeAddress(
        isHex(address)
          ? hexToU8a(address)
          : decodeAddress(address)
      );
      return true;
    } catch (error) {
      logger.debug(`Ungültige Polkadot-Adresse: ${address}`, { error: error.message });
      return false;
    }
  }
  
  /**
   * Aktuellen Wechselkurs DOT zu USD abrufen
   * @returns {Promise<number>} Aktueller DOT-zu-USD-Wechselkurs
   */
  async getDOTtoUSDRate() {
    // Zuerst im Cache nachsehen
    const cachedRate = exchangeRateCache.get('DOT_USD');
    if (cachedRate) {
      logger.debug('DOT-zu-USD-Wechselkurs aus Cache abgerufen', { rate: cachedRate });
      return cachedRate;
    }
    
    const operation = async () => {
      this.stats.apiCalls.exchangeRate++;
      
      const response = await axios.get(
        `${this.exchangeRateApiUrl}/simple/price?ids=polkadot&vs_currencies=usd`,
        { timeout: this.options.timeout }
      );
      
      if (!response.data?.polkadot?.usd) {
        throw new Error('Ungültiges Antwortformat von der Wechselkurs-API');
      }
      
      const rate = response.data.polkadot.usd;
      
      // Rate im Cache speichern
      exchangeRateCache.set('DOT_USD', rate);
      
      return rate;
    };
    
    try {
      return await this.executeWithRetry(operation, 'exchangeRate');
    } catch (error) {
      logger.error(`Fehler beim Abrufen des DOT-zu-USD-Wechselkurses: ${error.message}`, { error });
      throw new Error(`Wechselkurs-Abruf fehlgeschlagen: ${error.message}`);
    }
  }
  
  /**
   * DOT-Menge basierend auf USD-Preis berechnen
   * @param {number} usdAmount - Betrag in USD
   * @returns {Promise<number>} Äquivalente Menge in DOT
   */
  async calculateDOTAmount(usdAmount) {
    if (typeof usdAmount !== 'number' || isNaN(usdAmount) || usdAmount <= 0) {
      throw new Error('Ungültiger USD-Betrag. Muss eine positive Zahl sein.');
    }
    
    try {
      const rate = await this.getDOTtoUSDRate();
      // 6 Dezimalstellen für DOT-Beträge verwenden
      return parseFloat((usdAmount / rate).toFixed(6));
    } catch (error) {
      logger.error(`DOT-Betragsberechnung fehlgeschlagen: ${error.message}`, { usdAmount, error });
      throw new Error(`DOT-Betragsberechnung fehlgeschlagen: ${error.message}`);
    }
  }
  
  /**
   * DOT-Zahlung auf der Polkadot-Blockchain verarbeiten
   * @param {Object} paymentDetails - Zahlungsdetails
   * @param {string} paymentDetails.fromAddress - Absender-Polkadot-Adresse
   * @param {string} paymentDetails.toAddress - Empfänger-Polkadot-Adresse
   * @param {number} paymentDetails.amount - Betrag in DOT
   * @param {string} paymentDetails.privateKey - Private Key des Absenders
   * @param {string} paymentDetails.reference - Zahlungsreferenz/Memo
   * @returns {Promise<Object>} Transaktionsergebnis
   */
  async processPolkadotPayment(paymentDetails) {
    await this.init();
    
    const { fromAddress, toAddress, amount, privateKey, reference } = paymentDetails;
    
    // Parameter validieren
    if (!fromAddress || !toAddress) {
      throw new Error('Absender- und Empfängeradresse sind erforderlich');
    }
    
    if (!privateKey) {
      throw new Error('Private Key ist erforderlich');
    }
    
    if (typeof amount !== 'number' || isNaN(amount) || amount <= 0) {
      throw new Error('Ungültiger Betrag. Muss eine positive Zahl sein.');
    }
    
    // Adressen validieren
    if (!this.isValidPolkadotAddress(fromAddress) || !this.isValidPolkadotAddress(toAddress)) {
      throw new Error('Ungültige Polkadot-Adresse');
    }
    
    try {
      logger.info(`Verarbeite DOT-Zahlung: ${amount} DOT von ${fromAddress} an ${toAddress}`);
      
      const account = this.polkadotClient.getAccount(privateKey);
      
      // Überprüfen, ob der private Schlüssel zur Absenderadresse passt
      if (account.address !== fromAddress) {
        throw new Error('Private Key passt nicht zur Absenderadresse');
      }
      
      // Nonce für das Konto abrufen
      const nonce = await this.polkadotClient.api.rpc.system.accountNextIndex(account.address);
      
      // Überweisung erstellen
      const transfer = this.polkadotClient.api.tx.balances.transfer(
        toAddress, 
        this.polkadotClient.api.createType('Balance', amount * 1e10)
      );
      
      // Referenz hinzufügen (über system.remark)
      const txs = [
        transfer,
        this.polkadotClient.api.tx.system.remark(`Payment: ${reference || 'DeSci-Scholar payment'} | Time: ${new Date().toISOString()}`)
      ];
      
      // Transaktionen als Batch senden
      const batchTx = this.polkadotClient.api.tx.utility.batch(txs);
      
      // Transaktion mit verbesserter Wiederholungslogik senden
      const result = await this.polkadotClient.sendTransactionWithRetry(batchTx, account, {
        nonce,
        maxRetries: this.options.maxRetries,
        retryDelay: this.options.retryDelay
      });
      
      // Erfolgreiche Zahlung
      this.stats.paymentsProcessed.polkadot++;
      
      return {
        status: 'success',
        blockHash: result.blockHash,
        transactionHash: result.transactionHash,
        from: fromAddress,
        to: toAddress,
        amount,
        reference,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.stats.errors.polkadot++;
      logger.error(`Fehler bei DOT-Zahlung: ${error.message}`, { error, fromAddress, toAddress });
      throw new Error(`DOT-Zahlung fehlgeschlagen: ${error.message}`);
    }
  }
  
  /**
   * Zahlung mit Stripe verarbeiten
   * @param {Object} paymentDetails - Zahlungsdetails
   * @param {string} paymentDetails.token - Stripe-Zahlungstoken
   * @param {string} paymentDetails.amount - Betrag in USD-Cent
   * @param {string} paymentDetails.currency - Währungscode (Standard: USD)
   * @param {string} paymentDetails.description - Zahlungsbeschreibung
   * @returns {Promise<Object>} Stripe-Zahlungsergebnis
   */
  async processStripePayment(paymentDetails) {
    await this.init();
    
    const { token, amount, currency = 'usd', description } = paymentDetails;
    
    if (!this.stripeSecretKey) {
      throw new Error('Stripe Secret Key nicht konfiguriert');
    }
    
    if (!token) {
      throw new Error('Stripe-Token ist erforderlich');
    }
    
    if (typeof amount !== 'number' || isNaN(amount) || amount <= 0) {
      throw new Error('Ungültiger Betrag. Muss eine positive Zahl sein.');
    }
    
    const operation = async () => {
      this.stats.apiCalls.stripe++;
      
      // Payment Intent mit Stripe erstellen
      const response = await axios.post(
        `${this.stripeApiUrl}/payment_intents`,
        new URLSearchParams({
          amount: Math.round(amount), // Sicherstellen, dass der Betrag eine ganze Zahl ist
          currency,
          payment_method: token,
          description: description || 'DeSci-Scholar Zahlung',
          confirm: true,
          return_url: process.env.STRIPE_RETURN_URL || 'https://desci-scholar.example.com/payment/success'
        }).toString(),
        {
          headers: {
            'Authorization': `Bearer ${this.stripeSecretKey}`,
            'Content-Type': 'application/x-www-form-urlencoded'
          },
          timeout: this.options.timeout
        }
      );
      
      return {
        status: response.data.status,
        id: response.data.id,
        amount: response.data.amount,
        currency: response.data.currency,
        description: response.data.description,
        paymentMethod: response.data.payment_method,
        timestamp: new Date().toISOString()
      };
    };
    
    try {
      logger.info(`Verarbeite Stripe-Zahlung: ${amount / 100} ${currency.toUpperCase()}`);
      const result = await this.executeWithRetry(operation, 'stripe');
      
      // Erfolgreiche Zahlung
      this.stats.paymentsProcessed.stripe++;
      
      return result;
    } catch (error) {
      const errorMsg = error.response?.data?.error?.message || error.message;
      logger.error(`Stripe-Zahlungsfehler: ${errorMsg}`, { 
        error: errorMsg,
        details: error.response?.data
      });
      throw new Error(`Stripe-Zahlung fehlgeschlagen: ${errorMsg}`);
    }
  }
  
  /**
   * Zahlung mit PayPal erstellen
   * @param {Object} paymentDetails - Zahlungsdetails
   * @param {number} paymentDetails.amount - Betrag
   * @param {string} paymentDetails.currency - Währungscode (Standard: USD)
   * @param {string} paymentDetails.description - Zahlungsbeschreibung
   * @param {string} paymentDetails.returnUrl - URL für Rückkehr nach Zahlung
   * @param {string} paymentDetails.cancelUrl - URL für Rückkehr bei abgebrochener Zahlung
   * @returns {Promise<Object>} PayPal-Zahlungsergebnisse mit Approval-URL
   */
  async createPayPalPayment(paymentDetails) {
    await this.init();
    
    const { 
      amount, 
      currency = 'USD', 
      description, 
      returnUrl = process.env.PAYPAL_RETURN_URL || 'https://desci-scholar.example.com/payment/success', 
      cancelUrl = process.env.PAYPAL_CANCEL_URL || 'https://desci-scholar.example.com/payment/cancel'
    } = paymentDetails;
    
    if (!this.paypalClientId || !this.paypalSecret) {
      throw new Error('PayPal-Zugangsdaten nicht konfiguriert');
    }
    
    if (typeof amount !== 'number' || isNaN(amount) || amount <= 0) {
      throw new Error('Ungültiger Betrag. Muss eine positive Zahl sein.');
    }
    
    // Tokenanfrage erstellen
    const getAccessToken = async () => {
      this.stats.apiCalls.paypal++;
      
      const authResponse = await axios.post(
        `${this.paypalApiUrl}/v1/oauth2/token`,
        new URLSearchParams({
          grant_type: 'client_credentials'
        }).toString(),
        {
          auth: {
            username: this.paypalClientId,
            password: this.paypalSecret
          },
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          },
          timeout: this.options.timeout
        }
      );
      
      return authResponse.data.access_token;
    };
    
    // PayPal-Zahlung erstellen
    const createOrder = async (accessToken) => {
      this.stats.apiCalls.paypal++;
      
      const paymentResponse = await axios.post(
        `${this.paypalApiUrl}/v2/checkout/orders`,
        {
          intent: 'CAPTURE',
          purchase_units: [{
            amount: {
              currency_code: currency,
              value: amount.toFixed(2)
            },
            description: description || 'DeSci-Scholar Zahlung'
          }],
          application_context: {
            return_url: returnUrl,
            cancel_url: cancelUrl,
            brand_name: 'DeSci-Scholar',
            locale: 'de-DE',
            landing_page: 'BILLING',
            user_action: 'PAY_NOW'
          }
        },
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          },
          timeout: this.options.timeout
        }
      );
      
      // Approval-URL finden
      const approvalUrl = paymentResponse.data.links.find(link => link.rel === 'approve').href;
      
      return {
        status: paymentResponse.data.status,
        id: paymentResponse.data.id,
        amount,
        currency,
        description: description || 'DeSci-Scholar Zahlung',
        approvalUrl,
        timestamp: new Date().toISOString()
      };
    };
    
    try {
      logger.info(`Erstelle PayPal-Zahlung: ${amount} ${currency}`);
      
      const accessToken = await this.executeWithRetry(getAccessToken, 'paypal');
      const result = await this.executeWithRetry(() => createOrder(accessToken), 'paypal');
      
      // Erfolgreiche Zahlungserstellung
      this.stats.paymentsProcessed.paypal++;
      
      return result;
    } catch (error) {
      const errorMsg = error.response?.data?.message || 
                      error.response?.data?.error_description || 
                      error.message;
      
      logger.error(`PayPal-Zahlungsfehler: ${errorMsg}`, {
        error: errorMsg,
        details: error.response?.data
      });
      
      throw new Error(`PayPal-Zahlungserstellung fehlgeschlagen: ${errorMsg}`);
    }
  }
  
  /**
   * Zuvor erstellte PayPal-Zahlung erfassen (nach Benutzerfreigabe)
   * @param {string} orderId - PayPal-Bestellnummer
   * @returns {Promise<Object>} PayPal-Erfassungsergebnis
   */
  async capturePayPalPayment(orderId) {
    await this.init();
    
    if (!this.paypalClientId || !this.paypalSecret) {
      throw new Error('PayPal-Zugangsdaten nicht konfiguriert');
    }
    
    if (!orderId) {
      throw new Error('PayPal-Bestellnummer ist erforderlich');
    }
    
    // Token abrufen
    const getAccessToken = async () => {
      this.stats.apiCalls.paypal++;
      
      const authResponse = await axios.post(
        `${this.paypalApiUrl}/v1/oauth2/token`,
        new URLSearchParams({
          grant_type: 'client_credentials'
        }).toString(),
        {
          auth: {
            username: this.paypalClientId,
            password: this.paypalSecret
          },
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          },
          timeout: this.options.timeout
        }
      );
      
      return authResponse.data.access_token;
    };
    
    // Zahlungserfassung
    const captureOrder = async (accessToken) => {
      this.stats.apiCalls.paypal++;
      
      const captureResponse = await axios.post(
        `${this.paypalApiUrl}/v2/checkout/orders/${orderId}/capture`,
        {},
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
            'PayPal-Request-Id': `desci-capture-${Date.now()}-${Math.floor(Math.random() * 1000)}`
          },
          timeout: this.options.timeout
        }
      );
      
      // Detaillierte Informationen zur Erfassung
      return {
        status: captureResponse.data.status,
        id: captureResponse.data.id,
        captureId: captureResponse.data.purchase_units[0].payments.captures[0].id,
        amount: captureResponse.data.purchase_units[0].payments.captures[0].amount.value,
        currency: captureResponse.data.purchase_units[0].payments.captures[0].amount.currency_code,
        payerEmail: captureResponse.data.payer?.email_address,
        payerName: captureResponse.data.payer?.name 
          ? `${captureResponse.data.payer.name.given_name} ${captureResponse.data.payer.name.surname}`
          : 'Unbekannt',
        timestamp: new Date().toISOString()
      };
    };
    
    try {
      logger.info(`Erfasse PayPal-Zahlung für Bestellung: ${orderId}`);
      
      const accessToken = await this.executeWithRetry(getAccessToken, 'paypal');
      const result = await this.executeWithRetry(() => captureOrder(accessToken), 'paypal');
      
      return result;
    } catch (error) {
      const errorMsg = error.response?.data?.message || 
                      error.response?.data?.error_description || 
                      error.message;
      
      logger.error(`PayPal-Erfassungsfehler: ${errorMsg}`, {
        error: errorMsg,
        details: error.response?.data,
        orderId
      });
      
      throw new Error(`PayPal-Zahlungserfassung fehlgeschlagen: ${errorMsg}`);
    }
  }
  
  /**
   * Zahlung auf der Blockchain aufzeichnen (unveränderlichen Datensatz erstellen)
   * @param {Object} paymentRecord - Zahlungsdatensätze
   * @param {string} paymentRecord.paymentId - Eindeutige Zahlungskennung
   * @param {string} paymentRecord.method - Zahlungsmethode (polkadot, stripe, paypal)
   * @param {string} paymentRecord.from - Zahlerkennzeichnung
   * @param {string} paymentRecord.to - Empfängerkennzeichnung
   * @param {number} paymentRecord.amount - Zahlungsbetrag
   * @param {string} paymentRecord.currency - Zahlungswährung
   * @param {string} paymentRecord.reference - Zahlungsreferenz/Beschreibung
   * @param {string} privateKey - Private Key zum Signieren der Transaktion
   * @returns {Promise<Object>} Transaktionsergebnis
   */
  async recordPaymentOnBlockchain(paymentRecord, privateKey) {
    await this.init();
    
    if (!privateKey) {
      throw new Error('Private Key ist erforderlich, um die Zahlung aufzuzeichnen');
    }
    
    if (!paymentRecord || !paymentRecord.paymentId) {
      throw new Error('Gültiger Zahlungsdatensatz mit paymentId ist erforderlich');
    }
    
    try {
      logger.info(`Zeichne Zahlung ${paymentRecord.paymentId} auf der Blockchain auf`);
      
      const account = this.polkadotClient.getAccount(privateKey);
      
      // Zahlungsdatensatz zu JSON konvertieren
      const paymentRecordJSON = JSON.stringify({
        ...paymentRecord,
        recordedAt: new Date().toISOString(),
        recordedBy: account.address
      });
      
      // Transaktion mit angepasster Wiederholungslogik erstellen und senden
      // Annahme: Ein benutzerdefiniertes Pallet mit recordPayment-Methode
      try {
        // Moderne Methode mit Substrate-Pallet
        const tx = this.polkadotClient.api.tx.desciScholar.recordPayment(
          paymentRecord.paymentId,
          paymentRecordJSON
        );
        
        return await this.polkadotClient.sendTransactionWithRetry(tx, account, {
          maxRetries: this.options.maxRetries,
          retryDelay: this.options.retryDelay
        });
      } catch (error) {
        // Fallback für ältere Chains ohne desciScholar-Pallet
        logger.warn('Pallet nicht gefunden, versuche Fallback-Methode', { error: error.message });
        
        // Verwende system.remark als Fallback
        const remarkTx = this.polkadotClient.api.tx.system.remark(
          `PAYMENT:${paymentRecord.paymentId}:${paymentRecordJSON}`
        );
        
        return await this.polkadotClient.sendTransactionWithRetry(remarkTx, account, {
          maxRetries: this.options.maxRetries,
          retryDelay: this.options.retryDelay
        });
      }
    } catch (error) {
      logger.error(`Fehler beim Aufzeichnen der Zahlung auf der Blockchain: ${error.message}`, { error, paymentId: paymentRecord.paymentId });
      throw new Error(`Zahlungsaufzeichnung fehlgeschlagen: ${error.message}`);
    }
  }
  
  /**
   * Zahlungsinformationen von der Blockchain abrufen
   * @param {string} paymentId - Zahlungskennung
   * @returns {Promise<Object>} Zahlungsdaten von der Blockchain
   */
  async getPaymentRecord(paymentId) {
    await this.init();
    
    if (!paymentId) {
      throw new Error('paymentId ist erforderlich');
    }
    
    try {
      logger.info(`Rufe Zahlungsdatensatz für ${paymentId} ab`);
      
      // Versuche zuerst, mit Pallet-Methode abzurufen
      try {
        const paymentData = await this.polkadotClient.api.query.desciScholar.payments(paymentId);
        
        if (paymentData.isEmpty) {
          throw new Error(`Zahlung ${paymentId} nicht auf der Blockchain gefunden`);
        }
        
        return JSON.parse(paymentData.toString());
      } catch (error) {
        // Fallback: Durchsuche system.remark-Events
        logger.warn('Pallet-Abfrage fehlgeschlagen, suche in system.remark-Events', { 
          error: error.message,
          paymentId 
        });
        
        // Diese Implementierung ist vereinfacht und müsste in der Praxis durch eine
        // effizientere Ereignissuche ersetzt werden
        throw new Error('Fallback-Methode für Zahlungsabfrage nicht implementiert');
      }
    } catch (error) {
      logger.error(`Fehler beim Abrufen der Zahlungsdaten: ${error.message}`, { error, paymentId });
      throw new Error(`Zahlungsdatenabruf fehlgeschlagen: ${error.message}`);
    }
  }
  
  /**
   * Generiere einen Zahlungs-QR-Code für DOT-Zahlungen
   * @param {string} address - Polkadot-Adresse für den Zahlungsempfang
   * @param {number} amount - Betrag in DOT
   * @param {string} reference - Zahlungsreferenz
   * @returns {string} URL für QR-Code-Generierung
   */
  generatePolkadotPaymentQRCode(address, amount, reference) {
    if (!address) {
      throw new Error('Polkadot-Adresse ist erforderlich');
    }
    
    if (!this.isValidPolkadotAddress(address)) {
      throw new Error('Ungültige Polkadot-Adresse');
    }
    
    if (typeof amount !== 'number' || isNaN(amount) || amount <= 0) {
      throw new Error('Ungültiger Betrag. Muss eine positive Zahl sein.');
    }
    
    // Format für Polkadot.js-Wallet und andere Wallets
    const polkadotUrl = `polkadot:${address}?amount=${amount}&reference=${encodeURIComponent(reference || 'DeSci-Scholar payment')}`;
    
    // URL zurückgeben, die in QR-Code umgewandelt werden kann
    return `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(polkadotUrl)}`;
  }
  
  /**
   * Zahlungsstatistiken abrufen
   * @returns {Object} Aktuelle Zahlungsstatistiken
   */
  getStats() {
    return {
      ...this.stats,
      timestamp: new Date().toISOString()
    };
  }
  
  /**
   * Zahlungsabwickler schließen
   * @returns {Promise<void>}
   */
  async shutdown() {
    try {
      if (this.polkadotClient) {
        await this.polkadotClient.disconnect();
      }
      logger.info('PaymentProcessor erfolgreich heruntergefahren');
    } catch (error) {
      logger.error(`Fehler beim Herunterfahren des PaymentProcessor: ${error.message}`, { error });
      throw new Error(`PaymentProcessor-Herunterfahren fehlgeschlagen: ${error.message}`);
    }
  }
}

export default PaymentProcessor;
