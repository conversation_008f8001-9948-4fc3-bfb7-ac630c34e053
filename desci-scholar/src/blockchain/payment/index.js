/**
 * @fileoverview Payment integration for DeSci-Scholar
 * This module handles payment processing using both cryptocurrency (Polkadot/DOT) 
 * and traditional payment methods (<PERSON>e, PayPal)
 */

import { decodeAddress, encodeAddress } from '@polkadot/util-crypto';
import { hexToU8a, isHex } from '@polkadot/util';
import PolkadotClient from '../polkadot/index.js';
import axios from 'axios';
import dotenv from 'dotenv';

dotenv.config();

/**
 * PaymentProcessor class for handling various payment methods
 */
class PaymentProcessor {
  /**
   * Creates an instance of PaymentProcessor.
   */
  constructor() {
    this.polkadotClient = new PolkadotClient();
    
    // API keys for traditional payment processors
    this.stripeSecretKey = process.env.STRIPE_SECRET_KEY;
    this.paypalClientId = process.env.PAYPAL_CLIENT_ID;
    this.paypalSecret = process.env.PAYPAL_SECRET;
    
    // Payment gateway URLs
    this.stripeApiUrl = 'https://api.stripe.com/v1';
    this.paypalApiUrl = process.env.PAYPAL_ENVIRONMENT === 'production' 
      ? 'https://api.paypal.com' 
      : 'https://api.sandbox.paypal.com';
      
    // Exchange rate API for DOT conversion
    this.exchangeRateApiUrl = 'https://api.coingecko.com/api/v3';
  }
  
  /**
   * Initialize the payment processor
   */
  async init() {
    // Initialize Polkadot client
    await this.polkadotClient.init();
    console.log('Payment processor initialized');
  }
  
  /**
   * Validate a Polkadot address
   * @param {string} address - Polkadot address to validate
   * @returns {boolean} Whether the address is valid
   */
  isValidPolkadotAddress(address) {
    try {
      encodeAddress(
        isHex(address)
          ? hexToU8a(address)
          : decodeAddress(address)
      );
      return true;
    } catch (error) {
      return false;
    }
  }
  
  /**
   * Get current DOT to USD exchange rate
   * @returns {Promise<number>} Current DOT to USD exchange rate
   */
  async getDOTtoUSDRate() {
    try {
      const response = await axios.get(`${this.exchangeRateApiUrl}/simple/price?ids=polkadot&vs_currencies=usd`);
      return response.data.polkadot.usd;
    } catch (error) {
      console.error('Error fetching DOT to USD exchange rate:', error.message);
      throw new Error('Failed to fetch exchange rate');
    }
  }
  
  /**
   * Calculate DOT amount based on USD price
   * @param {number} usdAmount - Amount in USD
   * @returns {Promise<number>} Equivalent amount in DOT
   */
  async calculateDOTAmount(usdAmount) {
    const rate = await this.getDOTtoUSDRate();
    return parseFloat((usdAmount / rate).toFixed(6));
  }
  
  /**
   * Process a DOT payment on Polkadot blockchain
   * @param {Object} paymentDetails - Payment details
   * @param {string} paymentDetails.fromAddress - Sender's Polkadot address
   * @param {string} paymentDetails.toAddress - Recipient's Polkadot address
   * @param {number} paymentDetails.amount - Amount in DOT
   * @param {string} paymentDetails.privateKey - Sender's private key
   * @param {string} paymentDetails.reference - Payment reference/memo
   * @returns {Promise<Object>} Transaction result
   */
  async processPolkadotPayment(paymentDetails) {
    const { fromAddress, toAddress, amount, privateKey, reference } = paymentDetails;
    
    // Validate addresses
    if (!this.isValidPolkadotAddress(fromAddress) || !this.isValidPolkadotAddress(toAddress)) {
      throw new Error('Invalid Polkadot address');
    }
    
    await this.polkadotClient.init();
    const account = this.polkadotClient.getAccount(privateKey);
    
    // Verify the private key matches the fromAddress
    if (account.address !== fromAddress) {
      throw new Error('Private key does not match sender address');
    }
    
    // Get the current nonce for the account
    const nonce = await this.polkadotClient.api.rpc.system.accountNextIndex(account.address);
    
    // Create the transfer transaction
    const transfer = this.polkadotClient.api.tx.balances.transfer(toAddress, this.polkadotClient.api.createType('Balance', amount * 1e10));
    
    // Process the payment with reference (using system.remark)
    const txs = [
      transfer,
      this.polkadotClient.api.tx.system.remark(`Payment for: ${reference}`)
    ];
    
    // Create a batch of transactions
    const batchTx = this.polkadotClient.api.tx.utility.batch(txs);
    
    // Sign and send the transaction
    return new Promise((resolve, reject) => {
      batchTx.signAndSend(account, { nonce }, ({ status, events, dispatchError }) => {
        if (dispatchError) {
          if (dispatchError.isModule) {
            // Extract error information from the dispatch error
            const { section, method, documentation } = 
              this.polkadotClient.api.registry.findMetaError(dispatchError.asModule);
            reject(new Error(`${section}.${method}: ${documentation.join(' ')}`));
          } else {
            // Other errors
            reject(new Error(dispatchError.toString()));
          }
        }
        
        if (status.isFinalized) {
          // Check for success events
          let success = false;
          let transferEvent = null;
          
          events.forEach(({ event }) => {
            if (this.polkadotClient.api.events.system.ExtrinsicSuccess.is(event)) {
              success = true;
            }
            if (this.polkadotClient.api.events.balances.Transfer.is(event)) {
              transferEvent = event;
            }
          });
          
          if (success && transferEvent) {
            resolve({
              status: 'success',
              blockHash: status.asFinalized.toHex(),
              from: fromAddress,
              to: toAddress,
              amount,
              reference,
              timestamp: new Date().toISOString()
            });
          } else {
            reject(new Error('Transaction finalized but transfer failed'));
          }
        }
      });
    });
  }
  
  /**
   * Process a payment using Stripe
   * @param {Object} paymentDetails - Payment details
   * @param {string} paymentDetails.token - Stripe payment token
   * @param {string} paymentDetails.amount - Amount in USD cents
   * @param {string} paymentDetails.currency - Currency code (default: USD)
   * @param {string} paymentDetails.description - Payment description
   * @returns {Promise<Object>} Stripe payment result
   */
  async processStripePayment(paymentDetails) {
    const { token, amount, currency = 'usd', description } = paymentDetails;

    if (!this.stripeSecretKey) {
      throw new Error('Stripe secret key not configured');
    }

    try {
      // Create a payment intent with Stripe
      const response = await axios.post(
        `${this.stripeApiUrl}/payment_intents`,
        new URLSearchParams({
          amount,
          currency,
          payment_method: token,
          description,
          confirm: true,
          return_url: process.env.STRIPE_RETURN_URL || 'https://desci-scholar.example.com/payment/success'
        }).toString(),
        {
          headers: {
            'Authorization': `Bearer ${this.stripeSecretKey}`,
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        }
      );

      return {
        status: response.data.status,
        id: response.data.id,
        amount: response.data.amount,
        currency: response.data.currency,
        description,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Stripe payment error:', error.response ? error.response.data : error.message);
      throw new Error(`Stripe payment failed: ${error.message}`);
    }
  }
  
  /**
   * Process a payment using PayPal
   * @param {Object} paymentDetails - Payment details
   * @param {number} paymentDetails.amount - Amount
   * @param {string} paymentDetails.currency - Currency code (default: USD)
   * @param {string} paymentDetails.description - Payment description
   * @param {string} paymentDetails.returnUrl - URL to return to after payment
   * @param {string} paymentDetails.cancelUrl - URL to return to if payment is cancelled
   * @returns {Promise<Object>} PayPal payment creation result with approval URL
   */
  async createPayPalPayment(paymentDetails) {
    const { amount, currency = 'USD', description, returnUrl, cancelUrl } = paymentDetails;
    
    if (!this.paypalClientId || !this.paypalSecret) {
      throw new Error('PayPal credentials not configured');
    }
    
    try {
      // Get PayPal access token
      const authResponse = await axios.post(
        `${this.paypalApiUrl}/v1/oauth2/token`,
        'grant_type=client_credentials',
        {
          auth: {
            username: this.paypalClientId,
            password: this.paypalSecret
          },
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        }
      );
      
      const accessToken = authResponse.data.access_token;
      
      // Create PayPal payment
      const paymentResponse = await axios.post(
        `${this.paypalApiUrl}/v2/checkout/orders`,
        {
          intent: 'CAPTURE',
          purchase_units: [{
            amount: {
              currency_code: currency,
              value: amount.toFixed(2)
            },
            description
          }],
          application_context: {
            return_url: returnUrl,
            cancel_url: cancelUrl
          }
        },
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );
      
      // Find the approval URL
      const approvalUrl = paymentResponse.data.links.find(link => link.rel === 'approve').href;
      
      return {
        status: paymentResponse.data.status,
        id: paymentResponse.data.id,
        amount,
        currency,
        description,
        approvalUrl,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('PayPal payment error:', error.response ? error.response.data : error.message);
      throw new Error(`PayPal payment creation failed: ${error.message}`);
    }
  }
  
  /**
   * Capture a previously created PayPal payment (after user approval)
   * @param {string} orderId - PayPal order ID
   * @returns {Promise<Object>} PayPal capture result
   */
  async capturePayPalPayment(orderId) {
    if (!this.paypalClientId || !this.paypalSecret) {
      throw new Error('PayPal credentials not configured');
    }
    
    try {
      // Get PayPal access token
      const authResponse = await axios.post(
        `${this.paypalApiUrl}/v1/oauth2/token`,
        'grant_type=client_credentials',
        {
          auth: {
            username: this.paypalClientId,
            password: this.paypalSecret
          },
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        }
      );
      
      const accessToken = authResponse.data.access_token;
      
      // Capture the payment
      const captureResponse = await axios.post(
        `${this.paypalApiUrl}/v2/checkout/orders/${orderId}/capture`,
        {},
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );
      
      return {
        status: captureResponse.data.status,
        id: captureResponse.data.id,
        captureId: captureResponse.data.purchase_units[0].payments.captures[0].id,
        amount: captureResponse.data.purchase_units[0].payments.captures[0].amount.value,
        currency: captureResponse.data.purchase_units[0].payments.captures[0].amount.currency_code,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('PayPal capture error:', error.response ? error.response.data : error.message);
      throw new Error(`PayPal payment capture failed: ${error.message}`);
    }
  }
  
  /**
   * Record a payment on the blockchain (create a permanent record)
   * @param {Object} paymentRecord - Payment record details
   * @param {string} paymentRecord.paymentId - Unique payment identifier
   * @param {string} paymentRecord.method - Payment method (polkadot, stripe, paypal)
   * @param {string} paymentRecord.from - Payer identifier
   * @param {string} paymentRecord.to - Recipient identifier
   * @param {number} paymentRecord.amount - Payment amount
   * @param {string} paymentRecord.currency - Payment currency
   * @param {string} paymentRecord.reference - Payment reference/description
   * @param {string} privateKey - Private key to sign the transaction
   * @returns {Promise<Object>} Transaction result
   */
  async recordPaymentOnBlockchain(paymentRecord, privateKey) {
    await this.polkadotClient.init();
    const account = this.polkadotClient.getAccount(privateKey);
    
    // Convert payment record to JSON
    const paymentRecordJSON = JSON.stringify({
      ...paymentRecord,
      timestamp: new Date().toISOString()
    });
    
    // Create and sign the transaction
    // Note: This assumes a custom pallet with a recordPayment method
    const tx = this.polkadotClient.api.tx.desciScholar.recordPayment(
      paymentRecord.paymentId,
      paymentRecordJSON
    );
    
    // Sign and send the transaction
    return new Promise((resolve, reject) => {
      tx.signAndSend(account, ({ status, events, dispatchError }) => {
        if (dispatchError) {
          if (dispatchError.isModule) {
            // Extract error information from the dispatch error
            const { section, method, documentation } = 
              this.polkadotClient.api.registry.findMetaError(dispatchError.asModule);
            reject(new Error(`${section}.${method}: ${documentation.join(' ')}`));
          } else {
            // Other errors
            reject(new Error(dispatchError.toString()));
          }
        }
        
        if (status.isFinalized) {
          // Process events when the transaction is finalized
          events
            .filter(({ event }) => this.polkadotClient.api.events.system.ExtrinsicSuccess.is(event))
            .forEach(() => {
              console.log(`Payment ${paymentRecord.paymentId} successfully recorded on blockchain`);
            });
          
          resolve({
            status: 'finalized',
            blockHash: status.asFinalized.toHex(),
            paymentId: paymentRecord.paymentId
          });
        }
      });
    });
  }
  
  /**
   * Get payment information from the blockchain
   * @param {string} paymentId - Payment identifier
   * @returns {Promise<Object>} Payment data from blockchain
   */
  async getPaymentRecord(paymentId) {
    await this.polkadotClient.init();
    
    // This assumes a custom pallet query method to get payment data
    const paymentData = await this.polkadotClient.api.query.desciScholar.payments(paymentId);
    
    if (paymentData.isEmpty) {
      throw new Error(`Payment ${paymentId} not found on blockchain`);
    }
    
    return JSON.parse(paymentData.toString());
  }
  
  /**
   * Generate a payment QR code for DOT payments
   * @param {string} address - Polkadot address to receive payment
   * @param {number} amount - Amount in DOT
   * @param {string} reference - Payment reference
   * @returns {string} URL for QR code generation
   */
  generatePolkadotPaymentQRCode(address, amount, reference) {
    // Format for Polkadot.js wallet and other wallets
    const polkadotUrl = `polkadot:${address}?amount=${amount}&reference=${encodeURIComponent(reference)}`;
    
    // Return URL that can be converted to QR code
    return `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(polkadotUrl)}`;
  }
}

export default PaymentProcessor;
