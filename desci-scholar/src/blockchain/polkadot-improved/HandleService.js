/**
 * @fileoverview Handle-System-Service für DeSci-Scholar
 * 
 * Diese Klasse bietet eine einheitliche Schnittstelle für alle Interaktionen
 * mit dem Handle-System, einschließlich DOI-Auflösung, -Registrierung und
 * -Validierung. Es nutzt die zentrale HandleConfig und implementiert
 * sowohl Blockchain- als auch direkte API-Interaktionen.
 */

import axios from 'aioimpor
import HandleConfig from './HandleConfig.js';
import winston from 'winston';

// Logger konfigurieren
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: { service: 'handle-service' },
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    }),
    new winston.transports.File({ 
      filename: 'logs/handle-error.log', 
      level: 'error' 
    }),
    new winston.transports.File({ 
      filename: 'logs/handle-combined.log' 
    })
  ]
});

/**
 * HandleService-Klasse für die Interaktion mit dem Handle-System
 */
class HandleService {
  /**
   * Erstellt eine neue Instanz des HandleService
   * @param {Object} polkadotClient - Eine Instanz des PolkadotClient (optional, falls Blockchain-Integration gewünscht)
   * @param {Object} options - Konfigurationsoptionen
   */
  constructor(polkadotClient = null, options = {}) {
    this.client = polkadotClient;
    this.config = HandleConfig;
    this.useBlockchain = !!polkadotClient;
    
    // HTTP-Client für API-Anfragen
    this.httpClient = axios.create({
      timeout: options.timeout || this.config.params.requestTimeout,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...options.headers
      }
    });
    
    // Authentifizierungsdaten für privilegierte Handle-Operationen
    this.auth = options.auth || null;
  }
  
  /**
   * Normalisiert einen DOI oder Handle zu einem standardisierten Format
   * @param {string} identifier - Der zu normalisierende Identifier (DOI oder Handle)
   * @param {boolean} asDoi - True, um als DOI zu formatieren, False für Handle
   * @returns {string} Der normalisierte Identifier
   */
  normalizeIdentifier(identifier, asDoi = true) {
    if (!identifier) {
      throw new Error('Identifier darf nicht leer sein');
    }
    
    // DOI aus verschiedenen Formaten extrahieren
    const doiMatch = identifier.match(this.config.uriFormats.doiPattern);
    if (doiMatch && doiMatch[3]) {
      const normalizedId = doiMatch[3];
      return asDoi ? `${this.config.uriFormats.doi}${normalizedId}` : normalizedId;
    }
    
    // Handle-Format
    if (identifier.startsWith(this.config.uriFormats.handle)) {
      const handleId = identifier.substring(this.config.uriFormats.handle.length);
      return asDoi ? `${this.config.uriFormats.doi}${handleId}` : handleId;
    }
    
    // Fallback: Identifier unverändert zurückgeben
    return asDoi ? `${this.config.uriFormats.doi}${identifier}` : identifier;
  }
  
  /**
   * Validiert einen DOI gegen das DOI-Pattern
   * @param {string} doi - Der zu validierende DOI
   * @returns {boolean} true, wenn der DOI gültig ist
   */
  validateDoi(doi) {
    const normalizedDoi = this.normalizeIdentifier(doi, false);
    return this.config.uriFormats.prefixPattern.test(normalizedDoi);
  }
  
  /**
   * Löst einen Handle oder DOI auf und gibt die Metadaten zurück
   * @param {string} handle - Der aufzulösende Handle oder DOI
   * @param {Object} options - Optionen für die Auflösung
   * @returns {Promise<Object>} Die aufgelösten Metadaten
   */
  async resolveHandle(handle, options = {}) {
    const normalizedHandle = this.normalizeIdentifier(handle, false);
    logger.info(`Löse Handle auf: ${normalizedHandle}`);
    
    // Zuerst versuchen, vom Handle-System direkt aufzulösen
    try {
      const handleResponse = await this.httpClient.get(
        `${this.config.endpoints.api.proxy}/${encodeURIComponent(normalizedHandle)}`,
        {
          params: {
            auth: options.auth || this.config.params.defaultAuth,
            ...options.additionalParams
          }
        }
      );
      
      if (handleResponse.status === 200 && handleResponse.data) {
        logger.debug('Handle erfolgreich aufgelöst', { handle: normalizedHandle });
        
        // Metadaten aus den Handle-Werten extrahieren
        const metadata = this.extractMetadataFromHandleValues(handleResponse.data.values);
        return {
          handle: normalizedHandle,
          source: 'handle',
          timestamp: new Date().toISOString(),
          metadata
        };
      }
    } catch (error) {
      logger.warn(`Handle-Auflösung über API fehlgeschlagen: ${error.message}`, { handle: normalizedHandle });
      // Fehlschlag ignorieren und mit nächster Methode fortfahren
    }
    
    // Als DOI versuchen, falls die Handle-Auflösung fehlgeschlagen ist
    if (this.validateDoi(normalizedHandle)) {
      try {
        return await this.resolveDoi(normalizedHandle, options);
      } catch (error) {
        logger.warn(`DOI-Auflösung fehlgeschlagen: ${error.message}`, { doi: normalizedHandle });
      }
    }
    
    // Wenn Blockchain-Client verfügbar ist, auf der Blockchain suchen
    if (this.useBlockchain) {
      try {
        logger.debug('Versuche Auflösung auf der Blockchain', { handle: normalizedHandle });
        const blockchainData = await this.client.getDOI(normalizedHandle);
        
        if (blockchainData) {
          logger.info('Handle auf der Blockchain gefunden', { handle: normalizedHandle });
          return {
            handle: normalizedHandle,
            source: 'blockchain',
            timestamp: new Date().toISOString(),
            metadata: blockchainData
          };
        }
      } catch (error) {
        logger.warn(`Blockchain-Auflösung fehlgeschlagen: ${error.message}`, { handle: normalizedHandle });
      }
    }
    
    // Wenn keine Methode erfolgreich war, Fehler werfen
    throw new Error(`Handle konnte nicht aufgelöst werden: ${handle}`);
  }
  
  /*
  // Als ROR-ID versuchen, falls Handle- und DOI-Auflösung fehlschlagen
  const rorIdPattern = ''; // Beispiel ROR-ID-Pattern (beginnt mit 0, 11 Ziffern)
  if (rorIdPattern.test(normalizedHandle)) {
    try {
      return await this.resolveRor(normalizedHandle, options);
    } catch (error) {
      logger.warn(`ROR-ID-Auflösung fehlgeschlagen: ${error.message}`, { rorId: normalizedHandle });
    }
  }
  */
  
  /**
   * Löst einen DOI auf und gibt die Metadaten zurück
   * @param {string} doi - Der aufzulösende DOI
   * @param {Object} options - Optionen für die Auflösung
   * @returns {Promise<Object>} Die aufgelösten Metadaten
   */
  async resolveDoi(doi, options = {}) {
    const normalizedDoi = this.normalizeIdentifier(doi, false);
    logger.info(`Löse DOI auf: ${normalizedDoi}`);
    
    // Zuerst versuchen, von Crossref aufzulösen
    try {
      const crossrefResponse = await this.httpClient.get(
        `${this.config.endpoints.doi.crossref}/${encodeURIComponent(normalizedDoi)}`,
        { params: options.additionalParams }
      );
      
      if (crossrefResponse.status === 200 && crossrefResponse.data) {
        logger.debug('DOI erfolgreich über Crossref aufgelöst', { doi: normalizedDoi });
        const metadata = this.formatDoiMetadata(crossrefResponse.data.message);
        return {
          handle: normalizedDoi,
          source: 'crossref',
          timestamp: new Date().toISOString(),
          metadata
        };
     }
   } catch (error) {
     logger.warn(`Crossref-Auflösung fehlgeschlagen: ${error.message}`, { doi: normalizedDoi });
     // Fehlschlag ignorieren und mit nächster Methode fortfahren
   }
    
    // Als Fallback DataCite versuchen
    try {
      const dataciteResponse = await this.httpClient.get(
        `${this.config.endpoints.doi.datacite}/${encodeURIComponent(normalizedDoi)}`,
        { params: options.additionalParams }
      );
      
      if (dataciteResponse.status === 200 && dataciteResponse.data) {
        logger.debug('DOI erfolgreich über DataCite aufgelöst', { doi: normalizedDoi });
        const metadata = this.formatDoiMetadata(dataciteResponse.data.data.attributes);
        return {
          handle: normalizedDoi,
          source: 'datacite',
          timestamp: new Date().toISOString(),
          metadata
        };
      }
    } catch (error) {
      logger.warn(`DataCite-Auflösung fehlgeschlagen: ${error.message}`, { doi: normalizedDoi });
    }
    
    throw new Error(`DOI konnte nicht aufgelöst werden: ${doi}`);
  }
  
  /**
   * Löst eine ROR-ID auf und gibt die Metadaten zurück
   * @param {string} rorId - Die aufzulösende ROR-ID
   * @param {Object} options - Optionen für die Auflösung
   * @returns {Promise<Object>} Die aufgelösten ROR-Metadaten
   */
  async resolveRor(rorId, options = {}) {
    logger.info(`Löse ROR-ID auf: ${rorId}`);
    
    try {
      const rorResponse = await this.httpClient.get(
        `${this.config.endpoints.ror.api}/${encodeURIComponent(rorId)}`,
        { params: options.additionalParams }
      );
      
      if (rorResponse.status === 200 && rorResponse.data) {
        logger.debug('ROR-ID erfolgreich aufgelöst', { rorId });
        const metadata = rorResponse.data; // ROR API response is already in desired format
        return {
          handle: rorId, // using handle field for rorId for consistency
          source: 'ror',
          timestamp: new Date().toISOString(),
          metadata
        };
      }
    } catch (error) {
      logger.warn(`ROR-ID-Auflösung fehlgeschlagen: ${error.message}`, { rorId });
      throw new Error(`ROR-ID konnte nicht aufgelöst werden: ${rorId}`);
    }
    
    throw new Error(`ROR-ID konnte nicht aufgelöst werden: ${rorId}`);
  }
  
  /**
   * Extrahiert und formatiert Metadaten aus Handle-Werten
   * @param {Array} handleValues - Die Werte aus der Handle-Auflösung
   * @returns {Object} Die formatierte Metadaten
   */
  extractMetadataFromHandleValues(handleValues) {
    if (!Array.isArray(handleValues) || handleValues.length === 0) {
      return {};
    }
    
    const metadata = {};
    
    // Durch die Werte iterieren und nach bekannten Typen suchen
    handleValues.forEach(value => {
      switch (value.type) {
        case 'URL':
        case this.config.types.URL:
          metadata.url = value.data.value;
          break;
        case 'DESC':
        case this.config.types.DESC:
          metadata.description = value.data.value;
          break;
        case 'METADATA':
        case this.config.types.METADATA:
        case this.config.types.DOI_METADATA:
          try {
            // Versuchen, JSON-Metadaten zu parsen
            const parsedMetadata = JSON.parse(value.data.value);
            Object.assign(metadata, parsedMetadata);
          } catch (error) {
            // Wenn kein gültiges JSON, als String speichern
            metadata.rawMetadata = value.data.value;
          }
          break;
      }
    });
    
    return metadata;
  }
  
  /**
   * Formatiert DOI-Metadaten von Crossref oder DataCite
   * @param {Object} rawMetadata - Die rohen Metadaten
   * @returns {Object} Die formatierte Metadaten
   */
  formatDoiMetadata(rawMetadata) {
    if (!rawMetadata) {
      return {};
    }
    
    const metadata = {
      title: rawMetadata.title || rawMetadata.titles?.[0]?.title,
      publicationDate: rawMetadata.created?.['date-time'] || rawMetadata.published || rawMetadata.issued?.['date-parts']?.[0]?.join('-')
    };
    
    // Autoren extrahieren und formatieren
    if (rawMetadata.author || rawMetadata.authors) {
      const authors = rawMetadata.author || rawMetadata.authors || [];
      metadata.authors = authors.map(author => {
        const name = author.given && author.family ? 
          `${author.given} ${author.family}` : 
          author.name || 'Unbekannter Autor';
        
        return {
          name,
          orcid: author.ORCID || author.orcid,
          affiliation: author.affiliation?.[0]?.name || author.affiliation
        };
      });
    }
    
    // Weitere allgemeine Felder
    if (rawMetadata.abstract) metadata.abstract = rawMetadata.abstract;
    if (rawMetadata.publisher) metadata.publisher = rawMetadata.publisher;
    if (rawMetadata.container || rawMetadata['container-title']) {
      metadata.journal = rawMetadata.container || rawMetadata['container-title'];
    }
    
    // Referenzen extrahieren, falls vorhanden
    if (rawMetadata.reference || rawMetadata.references) {
      const references = rawMetadata.reference || rawMetadata.references || [];
      metadata.citations = references.map(ref => ({
        doi: ref.DOI || ref.doi,
        title: ref.title || ref['article-title'],
        authors: ref.author,
        year: ref.year
      })).filter(ref => ref.doi || ref.title); // Nur Referenzen mit DOI oder Titel behalten
    }
    
    return metadata;
  }
  
  /**
   * Registriert einen neuen Handle
   * @param {string} handle - Der zu registrierende Handle
   * @param {Object} metadata - Die Metadaten für den Handle
   * @param {Object} options - Optionen für die Registrierung
   * @returns {Promise<Object>} Das Ergebnis der Registrierung
   */
  async registerHandle(handle, metadata, options = {}) {
    const normalizedHandle = this.normalizeIdentifier(handle, false);
    logger.info(`Registriere Handle: ${normalizedHandle}`);
    
    // Authentifizierung prüfen
    const auth = options.auth || this.auth;
    if (!auth && !options.bypassAuthCheck) {
      throw new Error('Authentifizierung erforderlich für Handle-Registrierung');
    }
    
    // Metadaten validieren
    this.validateMetadata(metadata);
    
    // Wenn Blockchain-Client verfügbar ist, zuerst auf der Blockchain registrieren
    if (this.useBlockchain && options.registerOnBlockchain !== false) {
      try {
        const account = options.account || (await this.client.getWallet()).getAccount();
        
        if (!account) {
          throw new Error('Kein Account für Blockchain-Registrierung verfügbar');
        }
        
        // IPFS-Hash für Metadaten generieren (oder simulieren)
        const ipfsHash = await this.uploadMetadataToIpfs(metadata);
        
        // Auf der Blockchain registrieren
        const blockchainResult = await this.client.registerDOI(normalizedHandle, ipfsHash, account, {
          gasLimit: options.gasLimit,
          ...options.blockchainOptions
        });
        
        logger.info('Handle erfolgreich auf Blockchain registriert', { 
          handle: normalizedHandle,
          blockHash: blockchainResult.blockHash
        });
      } catch (error) {
        logger.error(`Blockchain-Registrierung fehlgeschlagen: ${error.message}`, { 
          handle: normalizedHandle,
          error
        });
        
        if (options.requireBlockchain) {
          throw new Error(`Blockchain-Registrierung fehlgeschlagen: ${error.message}`);
        }
      }
    }
    
    // Handle im Handle-System registrieren, falls API-Registrierung gewünscht
    if (options.registerViaApi !== false) {
      try {
        // Handle-Werte vorbereiten
        const handleValues = this.prepareHandleValues(metadata);
        
        // Handle über API registrieren
        const apiResponse = await this.httpClient.put(
          `${this.config.endpoints.api.admin}/${encodeURIComponent(normalizedHandle)}`,
          {
            values: handleValues,
            ttl: options.ttl || this.config.params.defaultTtl
          },
          {
            headers: auth ? {
              'Authorization': `Basic ${Buffer.from(auth.username + ':' + auth.password).toString('base64')}`
            } : undefined
          }
        );
        
        if (apiResponse.status === 201 || apiResponse.status === 200) {
          logger.info('Handle erfolgreich über API registriert', { handle: normalizedHandle });
          
          return {
            handle: normalizedHandle,
            registered: true,
            sources: ['api', this.useBlockchain ? 'blockchain' : null].filter(Boolean),
            timestamp: new Date().toISOString()
          };
        } else {
          logger.warn('Unerwarteter Status bei Handle-API-Registrierung', { 
            handle: normalizedHandle,
            status: apiResponse.status 
          });
          throw new Error(`Unerwarteter API-Status: ${apiResponse.status}`);
        }
      } catch (error) {
        logger.error(`API-Registrierung fehlgeschlagen: ${error.message}`, { 
          handle: normalizedHandle,
          error
        });
        
        // Wenn Blockchain-Registrierung erfolgt ist, Teilerfolg melden
        if (this.useBlockchain && options.registerOnBlockchain !== false && !options.requireApi) {
          return {
            handle: normalizedHandle,
            registered: true,
            sources: ['blockchain'],
            timestamp: new Date().toISOString(),
            warnings: [`API-Registrierung fehlgeschlagen: ${error.message}`]
          };
        }
        
        throw new Error(`Handle-Registrierung fehlgeschlagen: ${error.message}`);
      }
    } else if (this.useBlockchain && options.registerOnBlockchain !== false) {
      // Nur Blockchain-Registrierung wurde durchgeführt
      return {
        handle: normalizedHandle,
        registered: true,
        sources: ['blockchain'],
        timestamp: new Date().toISOString()
      };
    }
    
    throw new Error('Keine Registrierungsmethode (Blockchain oder API) aktiviert');
  }
  
  /**
   * Validiert Metadaten gegen die konfigurierten Regeln
   * @param {Object} metadata - Die zu validierenden Metadaten
   * @throws {Error} Wenn Pflichtfelder fehlen
   */
  validateMetadata(metadata) {
    const { required } = this.config.metadataRules;
    const missingFields = [];
    
    required.forEach(field => {
      if (!metadata[field]) {
        missingFields.push(field);
      }
    });
    
    if (missingFields.length > 0) {
      throw new Error(`Fehlende Pflichtfelder: ${missingFields.join(', ')}`);
    }
  }
  
  /**
   * Bereitet Handle-Werte für die Registrierung vor
   * @param {Object} metadata - Die Metadaten für den Handle
   * @returns {Array} Die Handle-Werte im API-Format
   */
  prepareHandleValues(metadata) {
    const values = [];
    
    // URL hinzufügen, wenn vorhanden
    if (metadata.url) {
      values.push({
        index: this.config.types.URL,
        type: 'URL',
        data: { format: 'string', value: metadata.url }
      });
    }
    
    // Beschreibung hinzufügen, wenn vorhanden
    if (metadata.abstract || metadata.description) {
      values.push({
        index: this.config.types.DESC,
        type: 'DESC',
        data: { format: 'string', value: metadata.abstract || metadata.description }
      });
    }
    
    // Vollständige Metadaten als JSON
    values.push({
      index: this.config.types.METADATA,
      type: 'METADATA',
      data: { format: 'string', value: JSON.stringify(metadata) }
    });
    
    return values;
  }
  
  /**
   * Lädt Metadaten in IPFS hoch
   * @param {Object} metadata - Die hochzuladenden Metadaten
   * @returns {Promise<string>} Der IPFS-Hash
   */
  async uploadMetadataToIpfs(metadata) {
    // In einer realen Implementierung würde dies IPFS-Uploading beinhalten
    // Diese Dummy-Implementierung simuliert dies
    logger.debug('Simuliere IPFS-Upload für Metadaten', { metadata });
    return `QmSimulatedHash${Date.now().toString(36)}`;
  }
  
  /**
   * Löst einen Batch von Handles auf
   * @param {Array<string>} handles - Die aufzulösenden Handles
   * @param {Object} options - Optionen für die Batch-Auflösung
   * @returns {Promise<Object>} Die Ergebnisse der Batch-Auflösung
   */
  async resolveBatchHandles(handles, options = {}) {
    const batchSize = options.batchSize || this.config.params.batchSize;
    const batchDelay = options.batchDelay || this.config.params.batchDelay;
    const maxRetries = options.maxRetries || this.config.params.maxRetries;
    
    const results = [];
    const failures = [];
    let processingCount = 0;
    
    // Handles in Batches verarbeiten
    for (let i = 0; i < handles.length; i += batchSize) {
      const batch = handles.slice(i, i + batchSize);
      processingCount += batch.length;
      
      logger.info(`Verarbeite Batch ${i/batchSize + 1} mit ${batch.length} Handles (${processingCount}/${handles.length})`);
      
      // Batch parallel verarbeiten
      const batchPromises = batch.map(async handle => {
        let retries = 0;
        
        while (retries <= maxRetries) {
          try {
            const result = await this.resolveHandle(handle, options);
            return { success: true, handle, result };
          } catch (error) {
            retries++;
            if (retries > maxRetries) {
              return { 
                success: false, 
                handle, 
                error: error.message, 
                retries 
              };
            }
            
            // Kurze Verzögerung vor erneuten Versuchen
            await new Promise(resolve => setTimeout(resolve, 500 * retries));
          }
        }
      });
      
      const batchResults = await Promise.all(batchPromises);
      
      // Ergebnisse in erfolgreiche und fehlgeschlagene aufteilen
      batchResults.forEach(item => {
        if (item.success) {
          results.push(item.result);
        } else {
          failures.push({
            handle: item.handle,
            error: item.error,
            retries: item.retries
          });
        }
      });
      
      // Verzögerung zwischen Batches, um Rate-Limiting zu vermeiden
      if (i + batchSize < handles.length) {
        logger.debug(`Warte ${batchDelay}ms vor dem nächsten Batch`);
        await new Promise(resolve => setTimeout(resolve, batchDelay));
      }
    }
    
    return {
      successful: results.length,
      failed: failures.length,
      total: handles.length,
      results,
      failures,
      timestamp: new Date().toISOString()
    };
  }
  
  /**
   * Prüft die Verfügbarkeit eines Handles
   * @param {string} handle - Der zu prüfende Handle
   * @returns {Promise<Object>} Das Ergebnis der Verfügbarkeitsprüfung
   */
  async checkHandleAvailability(handle) {
    const normalizedHandle = this.normalizeIdentifier(handle, false);
    logger.info(`Prüfe Verfügbarkeit von Handle: ${normalizedHandle}`);
    
    try {
      // Versuchen, den Handle aufzulösen
      await this.resolveHandle(normalizedHandle);
      
      // Wenn keine Fehler, existiert der Handle bereits
      return {
        handle: normalizedHandle,
        available: false,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      // Handle nicht gefunden bedeutet, er ist verfügbar
      if (error.message.includes('nicht aufgelöst werden')) {
        return {
          handle: normalizedHandle,
          available: true,
          timestamp: new Date().toISOString()
        };
      }
      
      // Andere Fehler weiterleiten
      throw error;
    }
  }
  
  /**
   * Generiert einen DOI nach bewährten Praktiken
   * @param {Object} metadata - Metadaten für das Objekt
   * @param {Object} options - Optionen für die DOI-Generierung
   * @returns {string} Der generierte DOI
   */
  generateDoi(metadata, options = {}) {
    // Prefix festlegen (sollte konfigurierbar sein)
    const prefix = options.prefix || '10.5555'; // Beispiel-Präfix für Tests
    
    // Jahr aus Publikationsdatum oder aktuellem Jahr
    const year = metadata.publicationDate ? 
                new Date(metadata.publicationDate).getFullYear() : 
                new Date().getFullYear();
    
    // Zufälliger Teil oder basierend auf Metadaten
    let suffix;
    
    if (options.useTitleHash && metadata.title) {
      // Hash aus Titel erzeugen
      const titleHash = this.simpleHash(metadata.title).substring(0, 8);
      suffix = `${year}.${titleHash}`;
    } else if (options.useScheme) {
      // Schema wie '2023.123456' verwenden
      const randomPart = Math.floor(100000 + Math.random() * 900000);
      suffix = `${year}.${randomPart}`;
    } else {
      // UUID-ähnlichen String für Eindeutigkeit
      const uuid = 'xxxx-xxxx-xxxx-xxxx'.replace(/x/g, () => {
        return Math.floor(Math.random() * 16).toString(16);
      });
      suffix = uuid;
    }
    
    return `${prefix}/${suffix}`;
  }
  
  /**
   * Erstellt einen einfachen Hash aus einem String
   * @param {string} str - Der zu hashende String
   * @returns {string} Der Hash
   */
  simpleHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash).toString(16);
  }
}

export default HandleService;
