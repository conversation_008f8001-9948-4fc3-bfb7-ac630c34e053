/**
 * @fileoverview Zentralisierter IPFS-Service für dezentrale Speicherung
 * 
 * Diese Klasse stellt eine einheitliche Schnittstelle für alle IPFS-Operationen bereit
 * und eliminiert Redundanzen in der IPFS-Implementierung im gesamten Projekt.
 */

const axios = require('axios');
const FormData = require('form-data');
const { create: createIPFS } = require('ipfs-http-client');
const { Buff<PERSON> } = require('buffer');
const winston = require('winston');
const BlockchainConfig = require('./BlockchainConfig');

// Logger konfigurieren
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: { service: 'ipfs-service' },
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    }),
    new winston.transports.File({ 
      filename: 'logs/ipfs-error.log', 
      level: 'error' 
    }),
    new winston.transports.File({ 
      filename: 'logs/ipfs-combined.log' 
    })
  ]
});

/**
 * IPFSService-Klasse für einheitliche IPFS-Interaktionen
 */
class IPFSService {
  /**
   * Erstellt eine neue Instanz des IPFSService
   * @param {Object} options - Konfigurationsoptionen für IPFS
   */
  constructor(options = {}) {
    // Konfiguration aus BlockchainConfig oder übergebenen Optionen verwenden
    const ipfsConfig = BlockchainConfig.SERVICES.IPFS;
    
    this.config = {
      gateway: options.gateway || ipfsConfig.GATEWAY,
      apiUrl: options.apiUrl || ipfsConfig.API,
      timeout: options.timeout || ipfsConfig.TIMEOUT,
      basicAuth: options.basicAuth,
      maxRetries: options.maxRetries || 3
    };
    
    // HTTP-Client für API-Anfragen
    this.httpClient = axios.create({
      timeout: this.config.timeout,
      headers: {
        'Accept': 'application/json'
      }
    });
    
    // IPFS-Client initialisieren, wenn API-URL angegeben
    if (this.config.apiUrl) {
      try {
        const auth = this.config.basicAuth ? 
          `${this.config.basicAuth.username}:${this.config.basicAuth.password}` : 
          undefined;
          
        this.ipfsClient = createIPFS({
          url: this.config.apiUrl,
          ...(auth && { headers: { authorization: `Basic ${Buffer.from(auth).toString('base64')}` } })
        });
        
        logger.info('IPFS-Client erfolgreich initialisiert', { endpoint: this.config.apiUrl });
      } catch (error) {
        logger.error('Fehler bei der Initialisierung des IPFS-Clients', { 
          error: error.message,
          endpoint: this.config.apiUrl
        });
      }
    }
  }
  
  /**
   * Lädt Metadaten in IPFS hoch
   * @param {Object} metadata - Die hochzuladenden Metadaten
   * @param {Object} options - Optionen für den Upload
   * @returns {Promise<string>} Der IPFS-Hash (CID)
   */
  async uploadMetadata(metadata, options = {}) {
    logger.info('Lade Metadaten in IPFS hoch', { metadata: options.logMetadata ? metadata : 'Metadaten nicht geloggt' });
    
    // Optionen vorbereiten
    const uploadOptions = {
      pin: options.pin !== false, // Standardmäßig pinnen
      wrapWithDirectory: options.wrapWithDirectory === true,
      timeout: options.timeout || this.config.timeout
    };
    
    try {
      // Metadaten in JSON umwandeln (falls noch nicht geschehen)
      const content = typeof metadata === 'string' ? 
        metadata : 
        JSON.stringify(metadata, null, 2);
      
      let cid;
      
      // Direkte Verbindung zu IPFS-Node verwenden, wenn verfügbar
      if (this.ipfsClient) {
        // Hochladen und pinnen
        const result = await this.ipfsClient.add(
          Buffer.from(content),
          uploadOptions
        );
        
        cid = result.cid.toString();
        
        // Explizit pinnen, wenn gewünscht
        if (uploadOptions.pin) {
          await this.ipfsClient.pin.add(cid);
        }
      } else {
        // Fallback: Upload über HTTP API (z.B. Infura)
        cid = await this.uploadViaHTTP(content, uploadOptions);
      }
      
      logger.info('Metadaten erfolgreich in IPFS hochgeladen', { cid });
      return cid;
    } catch (error) {
      logger.error('Fehler beim Hochladen von Metadaten in IPFS', { 
        error: error.message,
        metadata: options.logMetadata ? metadata : 'Metadaten nicht geloggt'
      });
      
      // Erneut versuchen mit Infura-Fallback, wenn direkter Upload fehlgeschlagen ist
      if (this.ipfsClient && options.retry !== false) {
        logger.warn('Versuche Fallback-Upload über HTTP API');
        try {
          const content = typeof metadata === 'string' ? 
            metadata : 
            JSON.stringify(metadata, null, 2);
          
          const cid = await this.uploadViaHTTP(content, uploadOptions);
          logger.info('Metadaten erfolgreich über Fallback in IPFS hochgeladen', { cid });
          return cid;
        } catch (fallbackError) {
          logger.error('Auch Fallback-Upload fehlgeschlagen', { error: fallbackError.message });
        }
      }
      
      // In einer Produktionsumgebung Dummy-Hash vermeiden
      if (process.env.NODE_ENV === 'production') {
        throw new Error(`IPFS-Upload fehlgeschlagen: ${error.message}`);
      }
      
      // Im Entwicklungsmodus simulierten Hash zurückgeben
      logger.warn('Gebe simulierten IPFS-Hash zurück');
      return `QmSimulatedHash${Date.now().toString(36)}`;
    }
  }
  
  /**
   * Lädt Inhalte über die HTTP-API hoch (als Fallback)
   * @param {string} content - Der hochzuladende Inhalt
   * @param {Object} options - Optionen für den Upload
   * @returns {Promise<string>} Der IPFS-Hash (CID)
   * @private
   */
  async uploadViaHTTP(content, options = {}) {
    // FormData für Multipart-Upload erstellen
    const formData = new FormData();
    formData.append('file', Buffer.from(content));
    
    // Upload-Parameter hinzufügen
    if (options.pin !== false) {
      formData.append('pin', 'true');
    }
    
    // Headers für Content-Type setzen FormData korrekt übermitteln
    const headers = formData.getHeaders ? formData.getHeaders() : {
      'Content-Type': 'multipart/form-data'
    };
    
    // Basic Auth hinzufügen, falls konfiguriert
    if (this.config.basicAuth) {
      const auth = Buffer.from(`${this.config.basicAuth.username}:${this.config.basicAuth.password}`).toString('base64');
      headers['Authorization'] = `Basic ${auth}`;
    }
    
    // HTTP-Request für Upload durchführen
    const response = await this.httpClient.post(
      `${this.config.apiUrl}/api/v0/add`,
      formData,
      { 
        headers,
        timeout: options.timeout || this.config.timeout
      }
    );
    
    // CID aus Antwort extrahieren
    if (response.status === 200 && response.data && response.data.Hash) {
      return response.data.Hash;
    }
    
    throw new Error('Ungültige Antwort vom IPFS-Server');
  }
  
  /**
   * Ruft Metadaten von IPFS ab
   * @param {string} cid - Der IPFS-Hash (CID)
   * @param {Object} options - Optionen für den Abruf
   * @returns {Promise<Object>} Die abgerufenen Metadaten
   */
  async getMetadata(cid, options = {}) {
    if (!cid) {
      throw new Error('Kein gültiger IPFS-Hash (CID) angegeben');
    }
    
    logger.info('Rufe Metadaten von IPFS ab', { cid });
    
    // Versuche bis zu maxRetries Mal
    let lastError;
    const maxRetries = options.maxRetries || this.config.maxRetries;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        let data;
        
        // Direkte Verbindung zu IPFS-Node verwenden, wenn verfügbar
        if (this.ipfsClient && options.forceGateway !== true) {
          const chunks = [];
          
          // Daten in Chunks auslesen
          for await (const chunk of this.ipfsClient.cat(cid, options)) {
            chunks.push(chunk);
          }
          
          // Chunks zusammenfügen
          const content = Buffer.concat(chunks).toString('utf8');
          
          // JSON parsen
          try {
            data = JSON.parse(content);
          } catch (parseError) {
            logger.warn('Konnte Daten nicht als JSON parsen, gebe Rohdaten zurück', { 
              cid, 
              error: parseError.message 
            });
            data = { rawContent: content };
          }
        } else {
          // Über Gateway abrufen
          const gatewayUrl = `${this.config.gateway}${cid}`;
          const response = await this.httpClient.get(gatewayUrl, {
            timeout: options.timeout || this.config.timeout
          });
          
          if (response.status === 200) {
            // Prüfen, ob Antwort bereits JSON ist
            if (typeof response.data === 'object') {
              data = response.data;
            } else {
              // Versuchen, Antwort als JSON zu parsen
              try {
                data = JSON.parse(response.data);
              } catch (parseError) {
                logger.warn('Konnte Daten nicht als JSON parsen, gebe Rohdaten zurück', { 
                  cid, 
                  error: parseError.message 
                });
                data = { rawContent: response.data };
              }
            }
          } else {
            throw new Error(`Unerwarteter Statuscode: ${response.status}`);
          }
        }
        
        logger.info('Metadaten erfolgreich von IPFS abgerufen', { cid });
        return data;
      } catch (error) {
        lastError = error;
        
        // Wenn nicht der letzte Versuch, warten und erneut versuchen
        if (attempt < maxRetries) {
          const delay = Math.pow(2, attempt) * 1000; // Exponentielles Backoff
          logger.warn(`Wiederholungsversuch ${attempt + 1}/${maxRetries} in ${delay}ms`, { 
            cid, 
            error: error.message 
          });
          
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
    
    // Alle Versuche fehlgeschlagen
    logger.error('Alle Versuche zum Abrufen der Metadaten fehlgeschlagen', { 
      cid, 
      error: lastError.message,
      maxRetries 
    });
    
    throw new Error(`Konnte Metadaten nicht abrufen: ${lastError.message}`);
  }
  
  /**
   * Prüft, ob ein IPFS-Hash (CID) gültig ist
   * @param {string} cid - Der zu prüfende IPFS-Hash
   * @returns {boolean} true, wenn der Hash gültig ist
   */
  isValidCid(cid) {
    if (!cid || typeof cid !== 'string') {
      return false;
    }
    
    // Einfache Validierung: Prüfen, ob der CID mit Qm (v0) oder b oder z (v1) beginnt
    return /^(Qm[1-9A-Za-z]{44}|[bz][1-9A-HJ-NP-Za-km-z]{48,58})$/.test(cid);
  }
  
  /**
   * Erstellt einen Gateway-URL für einen IPFS-Hash
   * @param {string} cid - Der IPFS-Hash (CID)
   * @param {string} gateway - Optionaler alternativer Gateway
   * @returns {string} Die vollständige Gateway-URL
   */
  getGatewayUrl(cid, gateway = null) {
    if (!this.isValidCid(cid)) {
      throw new Error(`Ungültiger IPFS-Hash: ${cid}`);
    }
    
    const gatewayUrl = gateway || this.config.gateway;
    return `${gatewayUrl}${cid}`;
  }
  
  /**
   * Pinnt einen IPFS-Hash, um ihn dauerhaft verfügbar zu halten
   * @param {string} cid - Der zu pinnende IPFS-Hash (CID)
   * @param {Object} options - Optionen für das Pinning
   * @returns {Promise<boolean>} true, wenn erfolgreich gepinnt
   */
  async pinContent(cid, options = {}) {
    if (!this.isValidCid(cid)) {
      throw new Error(`Ungültiger IPFS-Hash: ${cid}`);
    }
    
    logger.info('Pinne Inhalt in IPFS', { cid });
    
    try {
      // Direkte Verbindung zu IPFS-Node verwenden, wenn verfügbar
      if (this.ipfsClient) {
        await this.ipfsClient.pin.add(cid, options);
      } else {
        // Über HTTP API pinnen
        const headers = {};
        
        // Basic Auth hinzufügen, falls konfiguriert
        if (this.config.basicAuth) {
          const auth = Buffer.from(`${this.config.basicAuth.username}:${this.config.basicAuth.password}`).toString('base64');
          headers['Authorization'] = `Basic ${auth}`;
        }
        
        await this.httpClient.post(
          `${this.config.apiUrl}/api/v0/pin/add?arg=${cid}`,
          null,
          { 
            headers,
            timeout: options.timeout || this.config.timeout
          }
        );
      }
      
      logger.info('Inhalt erfolgreich in IPFS gepinnt', { cid });
      return true;
    } catch (error) {
      logger.error('Fehler beim Pinnen von Inhalt in IPFS', { 
        cid, 
        error: error.message 
      });
      
      // In einer Produktionsumgebung den Fehler weiterleiten
      if (process.env.NODE_ENV === 'production') {
        throw new Error(`IPFS-Pinning fehlgeschlagen: ${error.message}`);
      }
      
      // Im Entwicklungsmodus simulierten Erfolg zurückgeben
      logger.warn('Simuliere erfolgreichen Pin-Vorgang für Entwicklungsumgebung');
      return true;
    }
  }
  
  /**
   * Lädt eine Datei in IPFS hoch
   * @param {Buffer|string} fileData - Die hochzuladende Datei als Buffer oder Pfad
   * @param {Object} options - Optionen für den Upload
   * @returns {Promise<string>} Der IPFS-Hash (CID)
   */
  async uploadFile(fileData, options = {}) {
    // Implementation ähnlich zur uploadMetadata-Methode,
    // aber optimiert für Binärdaten und größere Dateien
    logger.info('Lade Datei in IPFS hoch');
    
    // Hier würde die tatsächliche Implementierung stehen...
    // Diese Methode wäre eine Erweiterung der uploadMetadata-Methode,
    // speziell für größere Dateien und andere Datentypen optimiert.
    
    // Für diese Beispielimplementierung simulieren wir das Hochladen
    logger.warn('Datei-Upload simuliert (Demo-Implementation)');
    return `QmFileUploadSimulation${Date.now().toString(36)}`;
  }
}

module.exports = IPFSService; 