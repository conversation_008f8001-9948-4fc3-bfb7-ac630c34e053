/**
 * @fileoverview Zentralisierte HTTP-Client-Implementierung
 * 
 * Diese Klasse bietet einen einheitlichen HTTP-Client für alle Services,
 * der Retry-Logik, Fehlerbehandlung und Logging umfasst.
 */

const axios = require('axios');
const LoggerFactory = require('./LoggerFactory');

// Logger für den HttpClient
const logger = LoggerFactory.createLogger('http-client');

/**
 * Konfigurierter HTTP-Client mit erweiterter Funktionalität
 */
class HttpClient {
  /**
   * Erstellt eine neue HttpClient-Instanz
   * @param {Object} config - Basis-Konfiguration für den Client
   */
  constructor(config = {}) {
    this.config = {
      timeout: config.timeout || 30000,
      retries: config.retries || 3,
      retryDelay: config.retryDelay || 1000,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...config.headers
      },
      validateStatus: status => status >= 200 && status < 300,
      ...config
    };
    
    // Axios-Instanz initialisieren
    this.client = axios.create(this.config);
    
    // Request-Interceptor für Logging
    this.client.interceptors.request.use(request => {
      logger.debug('HTTP-Anfrage gesendet', {
        method: request.method?.toUpperCase(),
        url: request.url,
        data: this._sanitizeData(request.data)
      });
      return request;
    });
    
    // Response-Interceptor für Logging
    this.client.interceptors.response.use(
      response => {
        logger.debug('HTTP-Antwort erhalten', {
          status: response.status,
          url: response.config.url,
          size: this._getResponseSize(response)
        });
        return response;
      },
      error => {
        if (error.response) {
          logger.warn('HTTP-Fehler erhalten', {
            status: error.response.status,
            url: error.config?.url,
            data: error.response.data
          });
        } else if (error.request) {
          logger.error('Keine Antwort erhalten', {
            url: error.config?.url,
            message: error.message
          });
        } else {
          logger.error('Fehler bei HTTP-Anfrage', {
            message: error.message
          });
        }
        return Promise.reject(error);
      }
    );
  }
  
  /**
   * Führt eine HTTP-Anfrage mit Retry-Funktionalität aus
   * @param {Object} config - Axios-Konfiguration für die Anfrage
   * @returns {Promise<Object>} - Die HTTP-Antwort
   */
  async request(config) {
    let lastError;
    const maxRetries = config.retries || this.config.retries;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await this.client.request(config);
      } catch (error) {
        lastError = error;
        
        // Prüfen, ob ein Retry sinnvoll ist
        if (!this._shouldRetry(error) || attempt >= maxRetries) {
          break;
        }
        
        // Exponentielles Backoff
        const delay = (config.retryDelay || this.config.retryDelay) * Math.pow(2, attempt);
        logger.info(`Wiederholungsversuch ${attempt + 1}/${maxRetries} in ${delay}ms`, {
          url: config.url,
          method: config.method
        });
        
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    throw lastError;
  }
  
  /**
   * Führt eine GET-Anfrage durch
   * @param {string} url - Ziel-URL
   * @param {Object} config - Zusätzliche Konfiguration
   * @returns {Promise<Object>} - Die HTTP-Antwort
   */
  async get(url, config = {}) {
    return this.request({
      method: 'get',
      url,
      ...config
    });
  }
  
  /**
   * Führt eine POST-Anfrage durch
   * @param {string} url - Ziel-URL
   * @param {Object} data - Zu sendende Daten
   * @param {Object} config - Zusätzliche Konfiguration
   * @returns {Promise<Object>} - Die HTTP-Antwort
   */
  async post(url, data, config = {}) {
    return this.request({
      method: 'post',
      url,
      data,
      ...config
    });
  }
  
  /**
   * Führt eine PUT-Anfrage durch
   * @param {string} url - Ziel-URL
   * @param {Object} data - Zu sendende Daten
   * @param {Object} config - Zusätzliche Konfiguration
   * @returns {Promise<Object>} - Die HTTP-Antwort
   */
  async put(url, data, config = {}) {
    return this.request({
      method: 'put',
      url,
      data,
      ...config
    });
  }
  
  /**
   * Führt eine DELETE-Anfrage durch
   * @param {string} url - Ziel-URL
   * @param {Object} config - Zusätzliche Konfiguration
   * @returns {Promise<Object>} - Die HTTP-Antwort
   */
  async delete(url, config = {}) {
    return this.request({
      method: 'delete',
      url,
      ...config
    });
  }
  
  /**
   * Entscheidet, ob ein Retry für den gegebenen Fehler durchgeführt werden soll
   * @param {Error} error - Der aufgetretene Fehler
   * @returns {boolean} - true, wenn Retry sinnvoll ist
   * @private
   */
  _shouldRetry(error) {
    // Netzwerkfehler immer wiederholen
    if (!error.response) {
      return true;
    }
    
    // Status-Codes, die für Retries geeignet sind (meist temporäre Fehler)
    const retryableStatusCodes = [408, 429, 500, 502, 503, 504];
    return retryableStatusCodes.includes(error.response.status);
  }
  
  /**
   * Anonymisiert sensible Daten für das Logging
   * @param {Object} data - Die zu anonymisierenden Daten
   * @returns {Object} - Anonymisierte Daten
   * @private
   */
  _sanitizeData(data) {
    if (!data) {
      return null;
    }
    
    // Kopie erstellen, um das Original nicht zu verändern
    const sanitized = { ...data };
    
    // Sensible Felder anonymisieren
    const sensitiveFields = ['password', 'token', 'auth', 'key', 'secret', 'credentials'];
    
    Object.keys(sanitized).forEach(key => {
      if (sensitiveFields.some(field => key.toLowerCase().includes(field))) {
        sanitized[key] = '[REDACTED]';
      } else if (typeof sanitized[key] === 'object' && sanitized[key] !== null) {
        sanitized[key] = this._sanitizeData(sanitized[key]);
      }
    });
    
    return sanitized;
  }
  
  /**
   * Berechnet die ungefähre Größe der Antwort
   * @param {Object} response - Die HTTP-Antwort
   * @returns {string} - Formatierte Größe
   * @private
   */
  _getResponseSize(response) {
    const sizeBytes = JSON.stringify(response.data).length;
    
    if (sizeBytes < 1024) {
      return `${sizeBytes} B`;
    } else if (sizeBytes < 1024 * 1024) {
      return `${(sizeBytes / 1024).toFixed(2)} KB`;
    } else {
      return `${(sizeBytes / (1024 * 1024)).toFixed(2)} MB`;
    }
  }
}

module.exports = HttpClient; 