/**
 * @fileoverview Zentrale Fehlerklassen für einheitliches Fehlerhandling
 * 
 * Diese Fehlerklassen ermöglichen eine standardisierte Fehlerbehandlung in
 * allen Services und bieten eine klare Kategorisierung von Fehlern.
 */

/**
 * Basisklasse für alle Service-Fehler
 */
class ServiceError extends Error {
  /**
   * Erstellt einen neuen ServiceError
   * @param {string} message - Fehlermeldung
   * @param {string} code - Fehlercode
   * @param {Object} details - Zusätzliche Details zum Fehler
   */
  constructor(message, code = 'SERVICE_ERROR', details = {}) {
    super(message);
    this.name = this.constructor.name;
    this.code = code;
    this.details = details;
    this.timestamp = new Date().toISOString();
    this.isServiceError = true;
    
    // Stack-Trace für bessere Fehlerverfolgung
    Error.captureStackTrace(this, this.constructor);
  }
  
  /**
   * Gibt eine JSON-Repräsentation des Fehlers zurück
   * @returns {Object} JSON-Objekt mit allen Fehlerinformationen
   */
  toJSON() {
    return {
      error: true,
      name: this.name,
      code: this.code,
      message: this.message,
      details: this.details,
      timestamp: this.timestamp,
      stack: process.env.NODE_ENV === 'development' ? this.stack : undefined
    };
  }
  
  /**
   * Gibt eine stringifizierte Repräsentation des Fehlers zurück
   * @returns {string} String-Repräsentation
   */
  toString() {
    return `${this.name} [${this.code}]: ${this.message}`;
  }
}

/**
 * Fehler für ungültige Eingabeparameter
 */
class ValidationError extends ServiceError {
  /**
   * Erstellt einen neuen ValidationError
   * @param {string} message - Fehlermeldung
   * @param {Object} validationErrors - Validierungsfehler
   */
  constructor(message, validationErrors = {}) {
    super(message, 'VALIDATION_ERROR', { validationErrors });
  }
  
  /**
   * Fügt Validierungsfehler für ein bestimmtes Feld hinzu
   * @param {string} field - Name des Feldes
   * @param {string} message - Fehlermeldung für das Feld
   */
  addFieldError(field, message) {
    if (!this.details.validationErrors) {
      this.details.validationErrors = {};
    }
    this.details.validationErrors[field] = message;
  }
}

/**
 * Fehler bei Netzwerkoperationen
 */
class NetworkError extends ServiceError {
  /**
   * Erstellt einen neuen NetworkError
   * @param {string} message - Fehlermeldung
   * @param {Object} networkDetails - Details zum Netzwerkfehler
   */
  constructor(message, networkDetails = {}) {
    super(message, 'NETWORK_ERROR', networkDetails);
  }
}

/**
 * Fehler bei Blockchain-Operationen
 */
class BlockchainError extends ServiceError {
  /**
   * Erstellt einen neuen BlockchainError
   * @param {string} message - Fehlermeldung
   * @param {Object} txDetails - Details zur Transaktion
   */
  constructor(message, txDetails = {}) {
    super(message, 'BLOCKCHAIN_ERROR', txDetails);
  }
}

/**
 * Fehler bei IPFS-Operationen
 */
class IpfsError extends ServiceError {
  /**
   * Erstellt einen neuen IpfsError
   * @param {string} message - Fehlermeldung
   * @param {Object} ipfsDetails - Details zum IPFS-Fehler
   */
  constructor(message, ipfsDetails = {}) {
    super(message, 'IPFS_ERROR', ipfsDetails);
  }
}

/**
 * Fehler bei BitTorrent-Operationen
 */
class TorrentError extends ServiceError {
  /**
   * Erstellt einen neuen TorrentError
   * @param {string} message - Fehlermeldung
   * @param {Object} torrentDetails - Details zum Torrent-Fehler
   */
  constructor(message, torrentDetails = {}) {
    super(message, 'TORRENT_ERROR', torrentDetails);
  }
}

/**
 * Fehler bei DOI/Handle-Operationen
 */
class HandleError extends ServiceError {
  /**
   * Erstellt einen neuen HandleError
   * @param {string} message - Fehlermeldung
   * @param {Object} handleDetails - Details zum Handle-Fehler
   */
  constructor(message, handleDetails = {}) {
    super(message, 'HANDLE_ERROR', handleDetails);
  }
}

/**
 * Fehler, wenn ein angefordertes Element nicht gefunden wurde
 */
class NotFoundError extends ServiceError {
  /**
   * Erstellt einen neuen NotFoundError
   * @param {string} message - Fehlermeldung
   * @param {string} itemType - Typ des nicht gefundenen Elements
   * @param {string} identifier - Identifikator des gesuchten Elements
   */
  constructor(message, itemType, identifier) {
    super(message, 'NOT_FOUND_ERROR', { itemType, identifier });
  }
}

/**
 * Fehler bei Timeout-Situationen
 */
class TimeoutError extends ServiceError {
  /**
   * Erstellt einen neuen TimeoutError
   * @param {string} message - Fehlermeldung
   * @param {number} timeout - Timeout in Millisekunden
   * @param {string} operation - Die zeitüberschreitende Operation
   */
  constructor(message, timeout, operation) {
    super(message, 'TIMEOUT_ERROR', { timeout, operation });
  }
}

/**
 * Helper-Funktion zur Prüfung, ob ein Fehler ein ServiceError ist
 * @param {Error} error - Der zu prüfende Fehler
 * @returns {boolean} true, wenn es sich um einen ServiceError handelt
 */
function isServiceError(error) {
  return error && error.isServiceError === true;
}

/**
 * Konvertiert einen Standard-Error in einen ServiceError
 * @param {Error} error - Der zu konvertierende Fehler
 * @param {string} defaultMessage - Standardmeldung, falls der Fehler keine hat
 * @param {string} defaultCode - Standard-Fehlercode
 * @returns {ServiceError} Der konvertierte ServiceError
 */
function toServiceError(error, defaultMessage = 'Ein unbekannter Fehler ist aufgetreten', defaultCode = 'UNKNOWN_ERROR') {
  if (isServiceError(error)) {
    return error; // Bereits ein ServiceError
  }
  
  // Fehlerdetails extrahieren
  const details = {
    originalError: {
      name: error.name,
      message: error.message
    }
  };
  
  // Stack nur in Entwicklungsumgebung hinzufügen
  if (process.env.NODE_ENV === 'development') {
    details.originalError.stack = error.stack;
  }
  
  // Basierend auf Fehlertyp den passenden ServiceError erstellen
  if (error.code === 'ETIMEDOUT' || error.code === 'ESOCKETTIMEDOUT') {
    return new TimeoutError(error.message || 'Zeitüberschreitung bei der Operation', error.timeout, error.operation);
  }
  
  if (error.code === 'ENOTFOUND' || error.code === 'ENOENT') {
    return new NotFoundError(error.message || 'Ressource nicht gefunden', 'resource', error.path || error.hostname);
  }
  
  if (error.code && (error.code.startsWith('E') || /^[A-Z_]+$/.test(error.code))) {
    // Standard-Netzwerk- oder Systemfehler
    return new NetworkError(error.message || 'Netzwerk- oder Systemfehler', {
      code: error.code,
      syscall: error.syscall,
      address: error.address,
      port: error.port
    });
  }
  
  // Generischer ServiceError für sonstige Fehler
  return new ServiceError(error.message || defaultMessage, defaultCode, details);
}

module.exports = {
  ServiceError,
  ValidationError,
  NetworkError,
  BlockchainError,
  IpfsError,
  TorrentError,
  HandleError,
  NotFoundError,
  TimeoutError,
  isServiceError,
  toServiceError
}; 