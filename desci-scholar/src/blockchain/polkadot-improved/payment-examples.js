/**
 * @fileoverview Wallet und Payment Beispiele für die Polkadot-Integration
 * 
 * Diese Datei enthält Beispiele für die Verwendung der PolkadotWallet-Klasse
 * für verschiedene Wallet- und Zahlungsoperationen.
 */

const { polkadotConnection } = require('./index');
const PolkadotWallet = require('./PolkadotWallet');

/**
 * Basis-Wallet-Operationen demonstrieren
 */
async function demonstrateWalletBasics() {
  try {
    console.log('=== Wallet Grundoperationen ===');
    
    // Polkadot-Verbindung initialisieren
    const client = await polkadotConnection.initialize({
      network: process.env.POLKADOT_NETWORK || 'development'
    });
    
    // Wallet erstellen und initialisieren
    const wallet = new PolkadotWallet(client);
    await wallet.init();
    
    // Neuen Account erstellen
    console.log('\n1. Neuen Account erstellen:');
    const newAccount = wallet.createAccount();
    console.log(`Adresse: ${newAccount.address}`);
    console.log(`Mnemonik: ${newAccount.mnemonic}`);
    
    // Account aus Mnemonik importieren
    console.log('\n2. Account aus Mnemonik importieren:');
    const importedAccount = wallet.importAccount(
      'bottom drive obey lake curtain smoke basket hold race lonely fit walk',
      'Importierter Test-Account'
    );
    console.log(`Adresse: ${importedAccount.address}`);
    console.log(`Name: ${importedAccount.name}`);
    
    // Verfügbare Accounts auflisten
    console.log('\n3. Verfügbare Accounts:');
    const accounts = wallet.listAccounts();
    accounts.forEach(acc => {
      console.log(`- ${acc.address} ${acc.isDefault ? '(Standard)' : ''} ${acc.name ? `(${acc.name})` : ''}`);
    });
    
    // Standard-Account festlegen
    console.log('\n4. Standard-Account festlegen:');
    wallet.setDefaultAccount(importedAccount.address);
    console.log(`Neuer Standard-Account: ${importedAccount.address}`);
    
    return wallet;
  } catch (error) {
    console.error('Fehler bei Wallet-Grundoperationen:', error.message);
    throw error;
  }
}

/**
 * Guthaben-Management demonstrieren
 */
async function demonstrateBalanceManagement(wallet) {
  try {
    console.log('\n=== Guthaben-Management ===');
    
    // Guthaben des Standard-Accounts abrufen
    console.log('\n1. Guthaben des Standard-Accounts:');
    const defaultBalance = await wallet.getBalance();
    console.log(`Adresse: ${defaultBalance.address}`);
    console.log(`Verfügbar: ${defaultBalance.free.formatted} ${defaultBalance.token}`);
    console.log(`Reserviert: ${defaultBalance.reserved.formatted} ${defaultBalance.token}`);
    console.log(`Gesamt: ${defaultBalance.total.formatted} ${defaultBalance.token}`);
    
    // Account in Testumgebung mit Guthaben ausstatten
    if (wallet.client.network === 'development') {
      console.log('\n2. Account in Testumgebung mit Guthaben ausstatten:');
      const accounts = wallet.listAccounts();
      const targetAccount = accounts[0].address;
      
      try {
        const fundingResult = await wallet.fundAccountForOperations(targetAccount, '100');
        console.log(`Guthaben bereitgestellt: ${fundingResult.fundingDetails.amount.formatted} ${fundingResult.fundingDetails.token}`);
        console.log(`Für Account: ${fundingResult.fundingDetails.address}`);
      } catch (error) {
        console.log(`Info: ${error.message} (Dies ist nur in Testumgebungen mit sudo-Zugriff verfügbar)`);
      }
    }
    
    // Zahlungsfähigkeit für Operationen prüfen
    console.log('\n3. Zahlungsfähigkeit für DOI-Registrierung prüfen:');
    const accounts = wallet.listAccounts();
    const checkAddress = accounts[0].address;
    
    try {
      const viabilityCheck = await wallet.checkPaymentViability(checkAddress, 'registerDoi');
      
      console.log(`Account: ${viabilityCheck.address}`);
      console.log(`Operation: ${viabilityCheck.operationType}`);
      console.log(`Zahlungsfähig: ${viabilityCheck.viable ? 'Ja' : 'Nein'}`);
      console.log(`Aktuelles Guthaben: ${viabilityCheck.balance.free.formatted} ${viabilityCheck.token}`);
      console.log(`Benötigte Gebühren: ${viabilityCheck.requiredFees.formatted} ${viabilityCheck.token}`);
      console.log(`Minimale Guthabensanforderung: ${viabilityCheck.existentialDeposit.formatted} ${viabilityCheck.token}`);
    } catch (error) {
      console.log(`Prüfung fehlgeschlagen: ${error.message}`);
    }
    
    return wallet;
  } catch (error) {
    console.error('Fehler bei Guthaben-Management:', error.message);
    throw error;
  }
}

/**
 * Zahlungsoperationen demonstrieren
 */
async function demonstratePaymentOperations(wallet) {
  try {
    console.log('\n=== Zahlungsoperationen ===');
    
    // Um eine echte Zahlung zu simulieren, erstellen wir einen Zielaccount
    console.log('\n1. Zielaccount für Zahlungsdemo erstellen:');
    const destinationAccount = wallet.createAccount();
    console.log(`Zieladresse: ${destinationAccount.address}`);
    
    // QR-Code für Zahlungen generieren
    console.log('\n2. Zahlungs-QR-Code generieren:');
    const qrCode = await wallet.generateAddressQrCode(destinationAccount.address);
    console.log(`QR-Code generiert (Base64): ${qrCode.substring(0, 40)}...`);
    
    // Zahlung durchführen
    if (wallet.client.network === 'development') {
      console.log('\n3. Zahlung durchführen:');
      
      const accounts = wallet.listAccounts();
      // Wir nehmen den ersten Account, der nicht der Zielaccount ist
      const sourceAccount = accounts.find(acc => acc.address !== destinationAccount.address);
      
      if (sourceAccount) {
        try {
          // Wir stellen zuerst sicher, dass der Quellaccount genug Guthaben hat
          await wallet.fundAccountForOperations(sourceAccount.address, '10').catch(() => {});
          
          // Wir setzen den Quellaccount als Standard
          wallet.setDefaultAccount(sourceAccount.address);
          
          // Zahlung mit optionalem Memo
          const paymentResult = await wallet.sendPayment(
            destinationAccount.address,
            '1.5', // 1.5 Token überweisen
            {
              memo: 'Zahlungsdemo für DeSci-Scholar'
            }
          );
          
          console.log('Zahlung erfolgreich:');
          console.log(`Von: ${paymentResult.paymentDetails.from}`);
          console.log(`An: ${paymentResult.paymentDetails.to}`);
          console.log(`Betrag: ${paymentResult.paymentDetails.amount.formatted} ${paymentResult.paymentDetails.token}`);
          console.log(`Gebühren: ${paymentResult.paymentDetails.fees.formatted} ${paymentResult.paymentDetails.token}`);
          console.log(`Referenz: ${paymentResult.paymentDetails.memo}`);
          console.log(`Block: ${paymentResult.blockHash}`);
          
          // Guthaben des Zielaccounts nach der Zahlung prüfen
          const destinationBalance = await wallet.getBalance(destinationAccount.address);
          console.log(`\nGuthaben des Empfängers nach Zahlung: ${destinationBalance.free.formatted} ${destinationBalance.token}`);
          
        } catch (error) {
          console.log(`Zahlung fehlgeschlagen: ${error.message}`);
          console.log('Info: Dies ist möglicherweise auf unzureichendes Guthaben zurückzuführen.');
        }
      } else {
        console.log('Kein geeigneter Quellaccount für die Zahlungsdemo gefunden.');
      }
    } else {
      console.log('Zahlungsdemo wird übersprungen, da wir nicht in einer Testumgebung sind.');
    }
    
  } catch (error) {
    console.error('Fehler bei Zahlungsoperationen:', error.message);
    throw error;
  }
}

/**
 * Zeigt, wie die Wallet zur NFT-Zahlung verwendet wird
 */
async function demonstrateNftPayments(wallet) {
  try {
    console.log('\n=== NFT & DOI-Zahlungen ===');
    
    console.log('\n1. Zahlungsfähigkeit für DOI-NFT-Registrierung prüfen:');
    
    // Wir nehmen den Standard-Account
    const defaultAccount = wallet.defaultAccount;
    
    if (defaultAccount) {
      // Zahlungsfähigkeit prüfen
      const viabilityCheck = await wallet.checkPaymentViability(defaultAccount, 'registerDoi');
      
      console.log(`Account: ${viabilityCheck.address}`);
      console.log(`Zahlungsfähig für DOI-NFT: ${viabilityCheck.viable ? 'Ja' : 'Nein'}`);
      
      if (!viabilityCheck.viable && wallet.client.network === 'development') {
        console.log('\nStelle Guthaben für NFT-Operationen bereit...');
        try {
          await wallet.fundAccountForOperations(defaultAccount, '100');
          console.log('Guthaben erfolgreich bereitgestellt!');
        } catch (error) {
          console.log(`Fondsbereitstellung fehlgeschlagen: ${error.message}`);
        }
      }
      
      console.log('\n2. DOI-NFT Kosten schätzen und bezahlen:');
      
      const feeEstimation = `0.0${Math.floor(Math.random() * 10) + 1}${Math.floor(Math.random() * 10)}`;
      console.log(`Geschätzte Kosten für DOI-NFT: ${feeEstimation} ${viabilityCheck.token}`);
      
      console.log('\n3. Automatisierter Zahlungsprozess für DOI-NFT würde hier stattfinden');
      console.log('   Die Zahlung würde automatisch beim Aufruf der createDoiNft-Methode durchgeführt werden');
    } else {
      console.log('Kein Standard-Account verfügbar für NFT-Zahlungsdemonstration.');
    }
    
  } catch (error) {
    console.error('Fehler bei NFT-Zahlungsdemo:', error.message);
  }
}

/**
 * Hauptfunktion zum Ausführen aller Demos
 */
async function runWalletDemos() {
  try {
    console.log('=== DeSci-Scholar Polkadot Wallet & Payment Demo ===\n');
    
    // Wallet-Grundoperationen demonstrieren
    const wallet = await demonstrateWalletBasics();
    
    // Guthaben-Management demonstrieren
    await demonstrateBalanceManagement(wallet);
    
    // Zahlungsoperationen demonstrieren
    await demonstratePaymentOperations(wallet);
    
    // NFT-Zahlungen demonstrieren
    await demonstrateNftPayments(wallet);
    
    // Verbindung trennen
    console.log('\nDemo abgeschlossen. Verbindung wird getrennt...');
    await polkadotConnection.disconnect();
    
  } catch (error) {
    console.error('\nFehler bei der Wallet & Payment Demo:', error.message);
  }
}

// Führe die Demos aus, wenn die Datei direkt ausgeführt wird
if (require.main === module) {
  runWalletDemos()
    .catch(console.error)
    .finally(() => {
      // Stellen Sie sicher, dass der Prozess beendet wird
      setTimeout(() => process.exit(0), 100);
    });
} 