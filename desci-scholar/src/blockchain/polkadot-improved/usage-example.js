/**
 * @fileoverview Beispiel für die Verwendung der verbesserten Polkadot-Integration
 * 
 * Dieses Skript demonstriert, wie die verschiedenen Komponenten der verbesserten
 * Polkadot-Integration für das DeSci-Scholar Projekt verwendet werden können.
 */

// Erforderliche Module importieren
const { 
  polkadotConnection, 
  DoiNftManager,
  PatentLicenseManager
} = require('./index');
const DoiNftContract = require('./DoiNftContract');

// Beispiel-Funktion zur DOI-Registrierung als NFT
async function registerDoiAsNft() {
  try {
    console.log('Starte DOI-NFT-Registrierungsprozess...');
    
    // Polkadot-Verbindung initialisieren
    const client = await polkadotConnection.initialize({
      network: process.env.POLKADOT_NETWORK || 'development'
    });
    
    console.log(`Verbunden mit Netzwerk: ${(await client.getChainInfo()).chain}`);
    
    // Account erstellen (im echten Szenario würde dies aus einer sicheren Quelle kommen)
    const account = client.getAccount(process.env.POLKADOT_SEED || '//Alice');
    console.log(`Verwende Account: ${account.address}`);
    
    // DOI-Manager initialisieren
    const doiManager = new DoiNftManager(client);
    
    // Beispiel-DOI und Metadaten
    const doi = '10.1234/desci-scholar-example-2023';
    const metadata = {
      title: 'Integration von Blockchain-Technologie in wissenschaftliche Veröffentlichungsprozesse',
      authors: ['Dr. Alice Wissenschaftler', 'Prof. Bob Forscher'],
      abstract: 'Diese Studie untersucht die Anwendung von Blockchain-Technologie zur Verbesserung der Transparenz und Nachvollziehbarkeit wissenschaftlicher Veröffentlichungen...',
      publicationDate: '2023-06-15',
      journal: 'Journal of Decentralized Science',
      keywords: ['blockchain', 'wissenschaftliche publikationen', 'dezentralisierung', 'transparenz'],
      references: [
        '10.1109/access.2020.2968794',
        '10.3390/info10120387'
      ]
    };
    
    // Prüfen, ob DOI bereits existiert
    const doesExist = await doiManager.doesDoiNftExist(doi).catch(() => false);
    
    if (doesExist) {
      console.log(`DOI ${doi} existiert bereits als NFT`);
      return;
    }
    
    // DOI als NFT registrieren
    console.log(`Registriere DOI ${doi} als NFT...`);
    const result = await doiManager.createDoiNft(doi, metadata, account);
    
    console.log('NFT erfolgreich erstellt!');
    console.log(`Transaktion finalisiert in Block: ${result.blockHash}`);
    console.log(`DOI: ${doi}`);
    
    // Direkte Interaktion mit dem NFT-Contract demonstrieren
    demonstrateContractInteraction(client, doi, account);
    
  } catch (error) {
    console.error('Fehler bei der DOI-NFT-Registrierung:', error.message);
    throw error;
  }
}

// Funktion zur Demonstration der direkten Contract-Interaktion
async function demonstrateContractInteraction(client, doi, account) {
  try {
    console.log('\nDemonstriere direkte Contract-Interaktion...');
    
    // Contract-Instanz erstellen
    const contractAddress = client.contracts.doiNft;
    const doiContract = new DoiNftContract(client, contractAddress);
    
    // Eine Zitation hinzufügen
    const citedDoi = '10.1109/access.2020.2968794';
    console.log(`Füge Zitation von ${doi} zu ${citedDoi} hinzu...`);
    
    await doiContract.addCitation(doi, citedDoi, account);
    console.log('Zitation erfolgreich hinzugefügt!');
    
    // Verbindung mit ORCID herstellen
    const orcid = '0000-0002-1825-0097'; // Beispiel-ORCID
    console.log(`Verbinde DOI ${doi} mit ORCID ${orcid}...`);
    
    await doiContract.linkOrcid(doi, orcid, account);
    console.log('ORCID-Verknüpfung erfolgreich!');
    
  } catch (error) {
    console.error('Fehler bei der Contract-Interaktion:', error.message);
  }
}

// Beispiel für die Verwaltung von Patentlizenzen
async function managePatenLicense() {
  try {
    console.log('\nStarte Patent-Lizenz-Verwaltung...');
    
    // Client-Referenz abrufen
    const client = polkadotConnection.getClient();
    
    // Account erstellen
    const account = client.getAccount(process.env.POLKADOT_SEED || '//Alice');
    
    // Patent-Lizenz-Manager initialisieren
    const licenseManager = new PatentLicenseManager(client);
    
    // Beispiel-Patent und Lizenzbedingungen
    const patentId = 'US12345678A';
    const licenseTerms = {
      title: 'Nicht-exklusive Lizenz für akademische Nutzung',
      description: 'Diese Lizenz gewährt das nicht-exklusive Recht zur Nutzung des Patents für akademische und Forschungszwecke.',
      licenseType: 'non-exclusive',
      validFrom: '2023-07-01',
      validUntil: '2028-06-30',
      restrictions: [
        'Keine kommerzielle Nutzung',
        'Keine Unterlizenzierung'
      ],
      royaltyPercentage: 0, // Keine Lizenzgebühren für akademische Nutzung
      territorialScope: 'global'
    };
    
    // Lizenz erstellen
    console.log(`Erstelle Lizenz für Patent ${patentId}...`);
    
    await licenseManager.createLicense(patentId, licenseTerms, account)
      .then(result => {
        console.log('Lizenz erfolgreich erstellt!');
        console.log(`Transaktion finalisiert in Block: ${result.blockHash}`);
      })
      .catch(error => {
        // Bei der aktuellen Dummy-Implementierung wird dies einen Fehler werfen
        console.log('Hinweis: Die Lizenzübertragung ist noch nicht implementiert.');
      });
    
  } catch (error) {
    console.error('Fehler bei der Patent-Lizenz-Verwaltung:', error.message);
  }
}

// Batch-Operationen demonstrieren
async function demonstrateBatchOperations() {
  try {
    console.log('\nDemonstriere Batch-Operationen...');
    
    // Client-Referenz abrufen
    const client = polkadotConnection.getClient();
    
    // Account erstellen
    const account = client.getAccount(process.env.POLKADOT_SEED || '//Alice');
    
    // Beispiel-DOIs für Batch-Operationen
    const sourceDoi = '10.1234/desci-scholar-example-2023';
    const citedDois = [
      '10.1109/access.2020.2968794',
      '10.3390/info10120387',
      '10.1038/s41467-020-19644-6'
    ];
    
    // Batch-Operationen vorbereiten
    const operations = citedDois.map(citedDoi => ({
      type: 'addCitation',
      sourceDoi,
      targetDoi: citedDoi
    }));
    
    // Auch einen Identifier-Link hinzufügen
    operations.push({
      type: 'linkIdentifier',
      doi: sourceDoi,
      identifierType: 'orcid',
      identifierValue: '0000-0002-1825-0097'
    });
    
    console.log(`Führe ${operations.length} Operationen in einer Batch-Transaktion aus...`);
    
    const result = await client.batchOperations(operations, account);
    
    console.log('Batch-Operationen erfolgreich abgeschlossen!');
    console.log(`Transaktion finalisiert in Block: ${result.blockHash}`);
    
  } catch (error) {
    console.error('Fehler bei Batch-Operationen:', error.message);
  }
}

// Hauptfunktion zum Ausführen der Beispiele
async function runExamples() {
  try {
    console.log('=== DeSci-Scholar Polkadot-Integration Beispiele ===\n');
    
    // DOI-NFT-Registrierung demonstrieren
    await registerDoiAsNft();
    
    // Patent-Lizenz-Verwaltung demonstrieren
    await managePatenLicense();
    
    // Batch-Operationen demonstrieren
    await demonstrateBatchOperations();
    
    // Polkadot-Verbindung trennen
    await polkadotConnection.disconnect();
    console.log('\nVerbindung getrennt. Beispiele abgeschlossen.');
    
  } catch (error) {
    console.error('\nFehler bei Ausführung der Beispiele:', error.message);
  }
}

// Beispiele ausführen, wenn die Datei direkt aufgerufen wird
if (require.main === module) {
  runExamples().catch(console.error);
} 