/**
 * @fileoverview Service-Container für zentrale Service-Verwaltung und Dependency Injection
 * 
 * Dieser Service-Container ermöglicht eine zentrale Registrierung und Verwaltung aller
 * Services, um Abhängigkeiten zu verwalten und Services einfach zu teilen.
 */

import LoggerFactory from './LoggerFactory.js.js.js.j;

// Logger für den ServiceContainer
const logger = LoggerFactory.createLogger('service-container');

/**
 * Service-Container mit Dependency Injection
 */
class ServiceContainer {
  /**
   * Erstellt einen neuen ServiceContainer
   */
  constructor() {
    // Map zur Speicherung aller Service-Instanzen
    this.services = new Map();
    
    // Map zur Speicherung von Service-Fabriken
    this.factories = new Map();
    
    // Map zur Speicherung von Service-Abhängigkeiten
    this.dependencies = new Map();
    
    // Map zur Speicherung von Service-Konfigurationen
    this.configurations = new Map();
    
    logger.debug('ServiceContainer initialisiert');
  }
  
  /**
   * Registriert einen Service im Container
   * @param {string} name - Name des Services
   * @param {Object|Function} service - Service-Instanz oder Factory-Funktion
   * @param {Array<string>} dependencies - Abhängigkeiten des Services
   * @param {Object} config - Konfiguration für den Service
   * @returns {ServiceContainer} this für Method-Chaining
   */
  register(name, service, dependencies = [], config = {}) {
    if (this.services.has(name) || this.factories.has(name)) {
      logger.warn(`Service "${name}" wird überschrieben`);
    }
    
    if (typeof service === 'function') {
      // Service-Factory registrieren
      this.factories.set(name, service);
    } else {
      // Service-Instanz direkt registrieren
      this.services.set(name, service);
    }
    
    // Abhängigkeiten und Konfiguration speichern
    this.dependencies.set(name, dependencies);
    this.configurations.set(name, config);
    
    logger.debug(`Service "${name}" registriert`, { 
      dependencies, 
      isFactory: typeof service === 'function' 
    });
    
    return this;
  }
  
  /**
   * Registriert mehrere Services gleichzeitig
   * @param {Object} servicesMap - Map mit Servicenamen als Schlüssel und Service-Definitionen als Werte
   * @returns {ServiceContainer} this für Method-Chaining
   */
  registerMultiple(servicesMap) {
    Object.entries(servicesMap).forEach(([name, definition]) => {
      const { service, dependencies, config } = definition;
      this.register(name, service, dependencies, config);
    });
    
    return this;
  }
  
  /**
   * Ruft einen Service aus dem Container ab
   * @param {string} name - Name des Services
   * @returns {Object} Die Service-Instanz
   * @throws {Error} Wenn der Service nicht gefunden wird oder nicht initialisiert werden kann
   */
  get(name) {
    // Prüfen, ob der Service bereits instanziiert wurde
    if (this.services.has(name)) {
      return this.services.get(name);
    }
    
    // Prüfen, ob eine Factory für den Service existiert
    if (!this.factories.has(name)) {
      throw new Error(`Service "${name}" nicht gefunden`);
    }
    
    logger.debug(`Initialisiere Service "${name}"`);
    
    // Abhängigkeiten abrufen
    const dependencies = this.dependencies.get(name) || [];
    const resolvedDependencies = dependencies.map(dep => this.get(dep));
    
    // Konfiguration abrufen
    const config = this.configurations.get(name) || {};
    
    try {
      // Service instanziieren
      const factory = this.factories.get(name);
      const service = factory(...resolvedDependencies, config);
      
      // Service im Container speichern
      this.services.set(name, service);
      
      logger.debug(`Service "${name}" erfolgreich initialisiert`);
      
      return service;
    } catch (error) {
      logger.error(`Fehler bei der Initialisierung von Service "${name}"`, { 
        error: error.message,
        stack: error.stack
      });
      
      throw new Error(`Konnte Service "${name}" nicht initialisieren: ${error.message}`);
    }
  }
  
  /**
   * Prüft, ob ein Service im Container existiert
   * @param {string} name - Name des Services
   * @returns {boolean} true, wenn der Service existiert
   */
  has(name) {
    return this.services.has(name) || this.factories.has(name);
  }
  
  /**
   * Entfernt einen Service aus dem Container
   * @param {string} name - Name des zu entfernenden Services
   * @returns {boolean} true, wenn der Service entfernt wurde
   */
  remove(name) {
    const hadService = this.services.delete(name);
    const hadFactory = this.factories.delete(name);
    this.dependencies.delete(name);
    this.configurations.delete(name);
    
    if (hadService || hadFactory) {
      logger.debug(`Service "${name}" entfernt`);
      return true;
    }
    
    return false;
  }
  
  /**
   * Setzt alle Services zurück
   */
  reset() {
    this.services.clear();
    this.factories.clear();
    this.dependencies.clear();
    this.configurations.clear();
    
    logger.debug('ServiceContainer zurückgesetzt');
  }
  
  /**
   * Initialisiert alle registrierten Services
   * @returns {Object} Map mit allen initialisierten Services
   */
  initializeAll() {
    const factoryNames = Array.from(this.factories.keys());
    
    logger.info(`Initialisiere ${factoryNames.length} Services`);
    
    // Alle Factories initialisieren
    factoryNames.forEach(name => {
      try {
        this.get(name);
      } catch (error) {
        logger.error(`Fehler bei der Initialisierung von Service "${name}"`, { 
          error: error.message 
        });
      }
    });
    
    return this.services;
  }
}

// Singleton-Instanz
let container = null;

/**
 * Gibt die Singleton-Instanz des ServiceContainers zurück
 * @returns {ServiceContainer} Die Singleton-Instanz
 */
function getContainer() {
  if (!container) {
    container = new ServiceContainer();
  }
  return container;
}

port {
  ServiceContainer,
  getContainer
}; 