/**
 * @fileoverview Optimierte Polkadot-Blockchain-Integration für DeSci-Scholar
 * Diese Modul behandelt optimierte Interaktionen mit dem Polkadot-Blockchain-Netzwerk
 * inklusive sicherer Verbindung, Fehlerbehebung, und effizienten Transaktionen.
 */

const { ApiPromise, WsProvider } = require('@polkadot/api');
const { Keyring } = require('@polkadot/keyring');
const { cryptoWaitReady } = require('@polkadot/util-crypto');
const winston = require('winston');
require('dotenv').config();

// Konfiguration für verschiedene Substrate-Chains
const CHAIN_CONFIGS = {
  polkadot: {
    nodeUrl: 'wss://rpc.polkadot.io',
    decimals: 10,
    contracts: {
      doiNft: process.env.POLKADOT_DOI_NFT_CONTRACT,
      patentLicense: process.env.POLKADOT_PATENT_LICENSE_CONTRACT
    }
  },
  kusama: {
    nodeUrl: 'wss://kusama-rpc.polkadot.io',
    decimals: 12,
    contracts: {
      doiNft: process.env.KUSAMA_DOI_NFT_CONTRACT,
      patentLicense: process.env.KUSAMA_PATENT_LICENSE_CONTRACT
    }
  },
  astar: {
    nodeUrl: 'wss://astar-rpc.dwellir.com',
    decimals: 18,
    contracts: {
      doiNft: process.env.ASTAR_DOI_NFT_CONTRACT,
      patentLicense: process.env.ASTAR_PATENT_LICENSE_CONTRACT
    }
  },
  development: {
    nodeUrl: 'ws://localhost:9944',
    decimals: 12,
    contracts: {
      doiNft: process.env.DEV_DOI_NFT_CONTRACT,
      patentLicense: process.env.DEV_PATENT_LICENSE_CONTRACT
    }
  }
};

// Logger konfigurieren
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: { service: 'polkadot-client' },
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    }),
    new winston.transports.File({ 
      filename: 'logs/polkadot-error.log', 
      level: 'error' 
    }),
    new winston.transports.File({ 
      filename: 'logs/polkadot-combined.log' 
    })
  ]
});

// Gas-Limit Defaults
const GAS_LIMITS = {
  DOI_REGISTRATION: 200000000, // Angepasst je nach Bedarf
  PSP34_MINT: 300000000,
  CONTRACT_CALL: 200000000
};

/**
 * Optimierte PolkadotClient-Klasse für Blockchain-Interaktionen
 */
class PolkadotClient {
  /**
   * Erstellt eine Instanz von PolkadotClient.
   * @param {Object} options - Konfigurationsoptionen
   * @param {string} options.network - Netzwerk (polkadot, kusama, astar, development)
   * @param {Object} options.customProviderOptions - Angepasste Provider-Optionen
   */
  constructor(options = {}) {
    this.network = options.network || process.env.POLKADOT_NETWORK || 'development';
    
    // Chainspezifische Konfiguration anwenden
    const chainConfig = CHAIN_CONFIGS[this.network] || CHAIN_CONFIGS.development;
    this.nodeUrl = options.nodeUrl || chainConfig.nodeUrl;
    this.decimals = chainConfig.decimals;
    this.contracts = chainConfig.contracts;
    
    // Custom Provider-Optionen
    this.providerOptions = {
      reconnect: true,
      maxReconnectAttempts: 5,
      reconnectInterval: 1000,
      ...options.customProviderOptions
    };
    
    this.api = null;
    this.keyring = null;
    this.provider = null;
    this.isConnecting = false;
    
    // Verbindungsstatistiken
    this.stats = {
      reconnectAttempts: 0,
      lastConnected: null,
      transactionCount: 0
    };
    
    // Verbindungs-Timeout
    this.connectionTimeout = options.connectionTimeout || 30000; // 30 Sekunden
  }

  /**
   * Initialisiert die Polkadot API-Verbindung mit automatischer Wiederverbindung
   * @returns {Promise<ApiPromise>} Initialisierte API-Instanz
   */
  async init() {
    if (this.isConnecting) {
      logger.info('Verbindungsaufbau bereits in Bearbeitung, warte auf Abschluss');
      return new Promise((resolve) => {
        const checkInterval = setInterval(() => {
          if (this.api && !this.isConnecting) {
            clearInterval(checkInterval);
            resolve(this.api);
          }
        }, 100);
      });
    }
    
    if (this.api && this.api.isConnected) {
      logger.debug('Bereits mit Polkadot-Node verbunden');
      return this.api;
    }
    
    this.isConnecting = true;
    
    try {
      logger.info(`Verbinde mit Polkadot-Node: ${this.nodeUrl}`);
      
      // Warte auf Crypto-Initialisierung
      await cryptoWaitReady();
      
      // Provider mit Wiederverbindungsfunktion erstellen
      this.provider = new WsProvider(this.nodeUrl, this.providerOptions);
      
      // Event-Handler hinzufügen
      this.setupProviderEventHandlers();
      
      // Verbindungs-Timeout
      const connectionPromise = ApiPromise.create({ provider: this.provider });
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Verbindungstimeout erreicht')), this.connectionTimeout);
      });
      
      // Promise-Race zwischen API-Verbindung und Timeout
      this.api = await Promise.race([connectionPromise, timeoutPromise]);
      
      // Keyring initialisieren
      this.keyring = new Keyring({ type: 'sr25519' });
      
      const chainInfo = await this.api.rpc.system.chain();
      logger.info(`Verbunden mit Chain: ${chainInfo.toString()}`);
      
      this.stats.lastConnected = new Date();
      this.stats.reconnectAttempts = 0;
      this.isConnecting = false;
      
      return this.api;
    } catch (error) {
      this.isConnecting = false;
      logger.error(`Fehler bei Polkadot-Verbindung: ${error.message}`, { error });
      
      if (this.stats.reconnectAttempts < this.providerOptions.maxReconnectAttempts) {
        this.stats.reconnectAttempts++;
        logger.info(`Versuche Wiederverbindung ${this.stats.reconnectAttempts}/${this.providerOptions.maxReconnectAttempts}`);
        
        // Exponentielles Backoff für Wiederverbindung
        const backoffTime = this.providerOptions.reconnectInterval * Math.pow(2, this.stats.reconnectAttempts - 1);
        
        await new Promise(resolve => setTimeout(resolve, backoffTime));
        return this.init();
      }
      
      throw new Error(`Maximale Wiederverbindungsversuche erreicht: ${error.message}`);
    }
  }
  
  /**
   * Event-Handler für WebSocket-Provider einrichten
   */
  setupProviderEventHandlers() {
    this.provider.on('connected', () => {
      logger.info('WebSocket-Verbindung hergestellt');
    });
    
    this.provider.on('error', (error) => {
      logger.error(`WebSocket-Fehler: ${error.message}`, { error });
    });
    
    this.provider.on('disconnected', () => {
      logger.warn('WebSocket-Verbindung getrennt');
    });
  }

  /**
   * Account aus Mnemonik oder privatem Schlüssel abrufen
   * @param {string} seedOrPrivateKey - Mnemonik oder privater Schlüssel
   * @returns {Object} Das Account-Paar
   */
  getAccount(seedOrPrivateKey) {
    if (!this.keyring) {
      throw new Error('Keyring nicht initialisiert. Rufen Sie zuerst init() auf.');
    }
    
    try {
      return this.keyring.addFromUri(seedOrPrivateKey);
    } catch (error) {
      logger.error(`Fehler beim Erstellen des Accounts: ${error.message}`, { error });
      throw new Error(`Account-Erstellung fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Gas für eine Transaktion schätzen
   * @param {Object} tx - Transaktion
   * @param {Object} account - Kontopaar zum Signieren
   * @returns {Promise<Object>} Gas-Schätzung
   */
  async estimateGas(tx, account) {
    await this.init();
    
    try {
      const info = await tx.paymentInfo(account);
      
      logger.debug('Gas-Schätzung', {
        partialFee: info.partialFee.toHuman(),
        weight: info.weight.toHuman(),
        class: info.class.toHuman()
      });
      
      return {
        partialFee: info.partialFee,
        weight: info.weight,
        class: info.class
      };
    } catch (error) {
      logger.error(`Fehler bei Gas-Schätzung: ${error.message}`, { error });
      throw new Error(`Gas-Schätzung fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * DOI mit PSP34-Standard als NFT registrieren
   * @param {string} doi - Digitaler Objekt-Identifikator
   * @param {string} ipfsHash - IPFS-Hash der Metadaten
   * @param {Object} account - Kontopaar zum Signieren
   * @param {Object} options - Optionale Parameter
   * @returns {Promise<Object>} Transaktions-Ergebnis
   */
  async registerDOI(doi, ipfsHash, account, options = {}) {
    await this.init();
    
    try {
      logger.info(`Registriere DOI ${doi} mit IPFS-Hash ${ipfsHash}`);
      
      // Nonce für das Konto abrufen
      const nonce = await this.api.rpc.system.accountNextIndex(account.address);
      
      // Bei PSP34 NFT unterstützenden Chains (z.B. Astar):
      if (this.contracts.doiNft) {
        // Mit PSP34-kompatiblem Contract interagieren
        const psp34Tx = this.api.tx.contracts.call(
          this.contracts.doiNft,
          0, // 0 Token übertragen
          options.gasLimit || GAS_LIMITS.PSP34_MINT,
          this.api.createType('Bytes', 'mint').toHex(),
          this.api.createType('Bytes', JSON.stringify({
            doi,
            ipfsHash,
            timestamp: Date.now()
          })).toHex()
        );
        
        return this.sendTransactionWithRetry(psp34Tx, account, {
          nonce,
          ...options
        });
      } 
      
      // Fallback für Chains ohne Smart-Contract-Unterstützung
      // Dies nutzt einen hypothetischen desciScholar-Pallet
      const tx = this.api.tx.desciScholar.registerDOI(doi, ipfsHash);
      
      return this.sendTransactionWithRetry(tx, account, {
        nonce,
        ...options
      });
    } catch (error) {
      logger.error(`Fehler bei DOI-Registrierung: ${error.message}`, { error, doi });
      throw new Error(`DOI-Registrierung fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Transaktion mit Wiederholungsmechanismus senden
   * @param {Object} tx - Transaktion
   * @param {Object} account - Kontopaar zum Signieren
   * @param {Object} options - Optionen wie Nonce, Wiederholungen etc.
   * @returns {Promise<Object>} Transaktions-Ergebnis
   */
  async sendTransactionWithRetry(tx, account, options = {}) {
    const maxRetries = options.maxRetries || 3;
    const retryDelay = options.retryDelay || 2000;
    
    let lastError = null;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        // Beim ersten Versuch nicht verzögern
        if (attempt > 0) {
          logger.info(`Wiederhole Transaktion (Versuch ${attempt}/${maxRetries})`);
          await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
        }
        
        const result = await this.sendTransaction(tx, account, options);
        this.stats.transactionCount++;
        return result;
      } catch (error) {
        lastError = error;
        
        // Bestimme, ob der Fehler wiederholbar ist
        const isRetryable = error.message.includes('1010') || // Temporärer Fehler
                           error.message.includes('Connection') ||
                           error.message.includes('timeout');
        
        if (!isRetryable) {
          logger.error('Nicht wiederholbarer Fehler, Abbruch', { error: error.message });
          break;
        }
        
        logger.warn(`Transaktion fehlgeschlagen, Versuch ${attempt}/${maxRetries}`, { 
          error: error.message, 
          retry: attempt < maxRetries 
        });
      }
    }
    
    throw lastError || new Error('Transaktion fehlgeschlagen nach Wiederholungsversuchen');
  }

  /**
   * Transaktion mit robusten Fehlerbehandlung senden
   * @param {Object} tx - Transaktion
   * @param {Object} account - Kontopaar zum Signieren
   * @param {Object} options - Signatur-Optionen wie Nonce etc.
   * @returns {Promise<Object>} Transaktions-Ergebnis
   */
  async sendTransaction(tx, account, options = {}) {
    return new Promise((resolve, reject) => {
      const signOptions = {};
      
      // Nonce hinzufügen, falls vorhanden
      if (options.nonce !== undefined) {
        signOptions.nonce = options.nonce;
      }
      
      // Gas-Limit hinzufügen, falls vorhanden
      if (options.gasLimit) {
        signOptions.gasLimit = this.api.registry.createType('WeightV2', options.gasLimit);
      }
      
      // Transaktionstimeout
      const timeout = setTimeout(() => {
        reject(new Error('Transaktions-Timeout erreicht'));
      }, options.timeout || 120000); // 2 Minuten Timeout als Standard
      
      tx.signAndSend(account, signOptions, ({ status, events, dispatchError }) => {
        // Status-Logging
        if (status.isInBlock) {
          logger.info(`Transaktion in Block: ${status.asInBlock.toHex()}`);
        } else if (status.isFinalized) {
          logger.info(`Transaktion finalisiert in Block: ${status.asFinalized.toHex()}`);
          clearTimeout(timeout);
          
          // Dispatch-Fehler verarbeiten
          if (dispatchError) {
            if (dispatchError.isModule) {
              try {
                // Fehlerinformationen aus dem Dispatch-Fehler extrahieren
                const { section, method, documentation } = 
                  this.api.registry.findMetaError(dispatchError.asModule);
                const errorMessage = `${section}.${method}: ${documentation.join(' ')}`;
                logger.error('Modul-Fehler in Transaktion', { error: errorMessage });
                reject(new Error(errorMessage));
              } catch (error) {
                logger.error('Fehler beim Dekodieren des Modul-Fehlers', { error });
                reject(new Error(`Unbekannter Modul-Fehler: ${dispatchError.toString()}`));
              }
            } else {
              // Andere Fehler
              const errorMessage = dispatchError.toString();
              logger.error('Dispatch-Fehler in Transaktion', { error: errorMessage });
              reject(new Error(errorMessage));
            }
            return;
          }
          
          // Erfolgreiche Ereignisse verarbeiten
          const successEvent = events.find(({ event }) => 
            this.api.events.system.ExtrinsicSuccess.is(event)
          );
          
          if (successEvent) {
            // Je nach Transaktion zusätzlich spezifische Ereignisse verarbeiten
            // z.B. DOI-Registrierung, NFT-Prägung etc.
            const customEvents = {};
            
            events.forEach(({ event }) => {
              // Bekannte Event-Typen prüfen und hinzufügen
              const eventSection = event.section.toString();
              const eventMethod = event.method.toString();
              
              if ((eventSection === 'desciScholar' || 
                   eventSection === 'contracts') && 
                  !customEvents[`${eventSection}.${eventMethod}`]) {
                customEvents[`${eventSection}.${eventMethod}`] = event.data.toJSON();
              }
            });
            
            resolve({
              status: 'finalized',
              blockHash: status.asFinalized.toHex(),
              events: customEvents,
              transactionHash: tx.hash.toHex()
            });
          } else {
            // Kein Erfolgs-Event gefunden, aber Transaktion wurde finalisiert
            // Dies kann in seltenen Fällen auftreten
            resolve({
              status: 'finalized',
              blockHash: status.asFinalized.toHex(),
              transactionHash: tx.hash.toHex(),
              warning: 'Kein ExtrinsicSuccess-Event gefunden'
            });
          }
        }
      }).catch(error => {
        clearTimeout(timeout);
        logger.error('Fehler beim Senden der Transaktion', { error: error.message });
        reject(error);
      });
    });
  }

  /**
   * Mehrere Operationen in einer Batch-Transaktion ausführen
   * @param {Array<Object>} operations - Liste von Operationen
   * @param {Object} account - Kontopaar zum Signieren
   * @param {Object} options - Optionen für die Transaktion
   * @returns {Promise<Object>} Transaktions-Ergebnis
   */
  async batchOperations(operations, account, options = {}) {
    await this.init();
    
    try {
      logger.info(`Führe ${operations.length} Batch-Operationen aus`);
      
      const txs = operations.map(op => {
        switch (op.type) {
          case 'registerDoi':
            return this.api.tx.desciScholar.registerDOI(op.doi, op.ipfsHash);
          case 'addCitation':
            return this.api.tx.desciScholar.addCitation(op.sourceDoi, op.targetDoi);
          case 'linkIdentifier':
            return this.api.tx.desciScholar.linkIdentifier(op.doi, op.identifierType, op.identifierValue);
          case 'updateMetadata':
            return this.api.tx.desciScholar.updateMetadata(op.doi, op.ipfsHash);
          default:
            throw new Error(`Unbekannter Operationstyp: ${op.type}`);
        }
      });
      
      // Verwende batchAll für atomare Ausführung (entweder alle oder keine)
      const batchTx = this.api.tx.utility.batchAll(txs);
      
      // Für große Batches, Gas-Schätzung durchführen
      if (operations.length > 3 && !options.gasLimit) {
        const gasEstimate = await this.estimateGas(batchTx, account);
        options.gasLimit = gasEstimate.partialFee.muln(1.3); // 30% Puffer
      }
      
      return this.sendTransactionWithRetry(batchTx, account, options);
    } catch (error) {
      logger.error(`Fehler bei Batch-Operationen: ${error.message}`, { error });
      throw new Error(`Batch-Operationen fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Patent-Lizenz-Smart-Contract erstellen
   * @param {string} patentId - Patent-Identifikator
   * @param {Object} licenseTerms - Lizenzbedingungen
   * @param {Object} account - Kontopaar zum Signieren
   * @param {Object} options - Optionen für die Transaktion
   * @returns {Promise<Object>} Transaktions-Ergebnis mit Vertragsadresse
   */
  async createPatentLicense(patentId, licenseTerms, account, options = {}) {
    await this.init();
    
    try {
      logger.info(`Erstelle Patent-Lizenz für Patent ${patentId}`);
      
      const licenseTermsJSON = JSON.stringify(licenseTerms);
      
      // Für Smart-Contract-fähige Chains (z.B. Astar)
      if (this.contracts.patentLicense) {
        const contractTx = this.api.tx.contracts.call(
          this.contracts.patentLicense,
          0, // 0 Token übertragen
          options.gasLimit || GAS_LIMITS.CONTRACT_CALL,
          this.api.createType('Bytes', 'createLicense').toHex(),
          this.api.createType('Bytes', JSON.stringify({
            patentId,
            licenseTerms: licenseTermsJSON,
            timestamp: Date.now()
          })).toHex()
        );
        
        return this.sendTransactionWithRetry(contractTx, account, options);
      }
      
      // Fallback für Chains ohne Smart-Contract-Unterstützung
      const tx = this.api.tx.desciScholar.createPatentLicense(patentId, licenseTermsJSON);
      
      return this.sendTransactionWithRetry(tx, account, options);
    } catch (error) {
      logger.error(`Fehler beim Erstellen der Patent-Lizenz: ${error.message}`, { error });
      throw new Error(`Patent-Lizenz-Erstellung fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * DOI-Daten von der Blockchain abrufen
   * @param {string} doi - Digitaler Objekt-Identifikator
   * @returns {Promise<Object>} DOI-Daten von der Blockchain
   */
  async getDOI(doi) {
    await this.init();
    
    try {
      logger.info(`Rufe DOI-Daten ab für ${doi}`);
      
      // Für Smart-Contract-fähige Chains (z.B. Astar)
      if (this.contracts.doiNft) {
        // Hier würde ein Vertragsaufruf erfolgen, um DOI-Daten abzurufen
        // Beispiel für Implementierung in zukünftiger Version
      }
      
      // Für Chains mit speziellem desciScholar-Pallet
      const doiData = await this.api.query.desciScholar.dois(doi);
      
      if (doiData.isEmpty) {
        logger.warn(`DOI ${doi} nicht in der Blockchain gefunden`);
        throw new Error(`DOI ${doi} nicht in der Blockchain gefunden`);
      }
      
      return doiData.toJSON();
    } catch (error) {
      logger.error(`Fehler beim Abrufen von DOI-Daten: ${error.message}`, { error, doi });
      throw new Error(`DOI-Daten-Abruf fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Neue DOI-Registrierungen abonnieren
   * @param {Function} callback - Callback-Funktion bei Registrierung neuer DOIs
   * @returns {Promise<Function>} Unsubscribe-Funktion
   */
  async subscribeToDOIs(callback) {
    await this.init();
    
    try {
      logger.info('Abonniere DOI-Registrierungen');
      
      return this.api.query.system.events((events) => {
        events.forEach(({ event }) => {
          // Prüfe auf DOI-Registrierungsereignisse
          if (this.api.events.desciScholar &&
              this.api.events.desciScholar.DOIRegistered &&
              this.api.events.desciScholar.DOIRegistered.is(event)) {
            const [doi, ipfsHash, registrar] = event.data;
            
            logger.debug('Neuer DOI registriert', {
              doi: doi.toString(),
              ipfsHash: ipfsHash.toString(),
              registrar: registrar.toString()
            });
            
            callback({
              doi: doi.toString(),
              ipfsHash: ipfsHash.toString(),
              registrar: registrar.toString(),
              timestamp: Date.now()
            });
          }
        });
      });
    } catch (error) {
      logger.error(`Fehler beim Abonnieren von DOI-Registrierungen: ${error.message}`, { error });
      throw new Error(`DOI-Abonnement fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Chain-Informationen abrufen
   * @returns {Promise<Object>} Informationen zur aktuellen Chain
   */
  async getChainInfo() {
    await this.init();
    
    const [chain, nodeName, nodeVersion, properties] = await Promise.all([
      this.api.rpc.system.chain(),
      this.api.rpc.system.name(),
      this.api.rpc.system.version(),
      this.api.rpc.system.properties()
    ]);
    
    const tokenSymbol = properties.tokenSymbol.isNone
      ? 'Unknown'
      : properties.tokenSymbol.value[0].toString();
    
    const tokenDecimals = properties.tokenDecimals.isNone
      ? this.decimals
      : properties.tokenDecimals.value[0].toNumber();
    
    return {
      chain: chain.toString(),
      nodeName: nodeName.toString(),
      nodeVersion: nodeVersion.toString(),
      tokenSymbol,
      tokenDecimals,
      network: this.network
    };
  }

  /**
   * Verbindung zur Polkadot-Node trennen
   */
  async disconnect() {
    if (this.api) {
      try {
        await this.api.disconnect();
        this.api = null;
        logger.info('Verbindung zur Polkadot-Node getrennt');
      } catch (error) {
        logger.error(`Fehler beim Trennen der Verbindung: ${error.message}`, { error });
      }
    }
  }
}

export default PolkadotClient;

