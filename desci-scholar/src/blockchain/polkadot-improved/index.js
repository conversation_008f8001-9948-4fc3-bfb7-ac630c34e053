/**
 * @fileoverview Verbesserte Polkadot-Integration für DeSci-Scholar
 *
 * Dieser Index exportiert alle Komponenten der optimierten Polkadot-Integration,
 * einschließlich Client, Wallet und verschiedener Dienste. Die Implementierung bietet
 * umfassende Unterstützung für Blockchain-Interaktionen, DOI-Verwaltung und dezentrale Speicherung.
 */

// Konfiguration
import BlockchainConfig from './BlockchainConfig.js';

// Core-Komponenten
import PolkadotClient from './PolkadotClient.js';
import PolkadotWallet from './PolkadotWallet.js';

// Services
import HandleService from './HandleService.js';
import HandleConfig from './HandleConfig.js';
import IPFSService from './IPFSService.js';
import TorrentService from './TorrentService.js';

// Utilities
import CommonUtils from './utils/CommonUtils.js';
import LoggerFactory from './LoggerFactory.js';
import { getContainer as getServiceContainer } from './ServiceContainer.js';
import * as Errors from './errors/ServiceError.js';

// Service Container initialisieren
const ServiceContainer = getServiceContainer();

// Factories
import ServiceFactory from './factories/ServiceFactory.js';

// Manager-Klassen, die beibehalten werden
class DoiNftManager {
  /**
   * Erstellt eine neue DoiNftManager-Instanz
   * @param {PolkadotConnection|Object} connection - Die Polkadot-Verbindung oder Konfiguration
   */
  constructor(connection) {
    if (connection instanceof Object && 'getClient' in connection) {
      this.connection = connection;
    } else {
      // Bei Verwendung von ServiceFactory statt direkter Instanziierung
      this.connection = ServiceFactory.createPolkadotConnection(
        new PolkadotClient(connection),
        new PolkadotWallet(new PolkadotClient(connection), connection),
        new HandleService(new PolkadotClient(connection), connection.handleOptions || {}),
        new IPFSService(connection.ipfsOptions || {})
      );
    }
            
    // Services abrufen
    this.client = this.connection.getClient();
    this.wallet = this.connection.getWallet();
    this.handleService = this.connection.getHandleService();
    this.ipfsService = this.connection.getIpfsService();
  }
      
  /**
   * Registriert einen DOI als NFT mit optimierter Implementierung
   * @param {string} doi - Der zu registrierende DOI
   * @param {Object} metadata - Die Metadaten für den DOI
   * @param {Object} options - Optionen für die Registrierung
   * @returns {Promise<Object>} Das Ergebnis der Registrierung
   */
  async registerDoiAsNft(doi, metadata, options = {}) {
    // DOI validieren
    if (!CommonUtils.isValidDoi(doi)) {
      throw new Errors.ValidationError(`Ungültiger DOI: ${doi}`);
    }
            
    // Metadaten für IPFS aufbereiten
    const nftMetadata = {
      name: metadata.title || `DOI: ${doi}`,
      description: metadata.abstract || metadata.description || `NFT für DOI: ${doi}`,
      image: metadata.image || null,
      properties: {
        doi: doi,
        publicationDate: metadata.publicationDate || new Date().toISOString(),
        authors: metadata.authors || [],
        journal: metadata.journal || metadata.publisher || null,
        ...metadata.properties
      }
    };
            
    // Metadaten in IPFS hochladen (über den zentralen Service)
    const ipfsHash = await this.ipfsService.uploadMetadata(nftMetadata, {
      pin: true,
      ...options.ipfsOptions
    });
            
    // Account für die Transaktion abrufen
    const account = options.account || await this.wallet.getAccount();
            
    if (!account) {
      throw new Errors.ValidationError('Kein Account für DOI-NFT-Registrierung verfügbar');
    }
            
    // DOI auf der Blockchain registrieren
    const result = await this.client.registerDOI(doi, ipfsHash, account, {
      gasLimit: options.gasLimit,
      ...options.transactionOptions
    });
            
    // Ergebnisobjekt mit allen relevanten Informationen zurückgeben
    return {
      doi,
      ipfsHash,
      transactionHash: result.transactionHash || result.blockHash,
      nftId: result.nftId || null,
      metadata: nftMetadata,
      registered: true,
      timestamp: new Date().toISOString()
    };
  }
      
  /**
   * Verknüpft einen DOI mit einer Publikationsdatei über BitTorrent
   * @param {string} doi - Der DOI
   * @param {string} filePath - Pfad zur Publikationsdatei
   * @param {Object} options - Optionen für die Verknüpfung
   * @returns {Promise<Object>} Das Ergebnis der Verknüpfung
   */
  async linkDoiToFile(doi, filePath, options = {}) {
    // TorrentService initialisieren, falls noch nicht geschehen
    const torrentService = this.connection.getTorrentService(options.torrentOptions);
            
    // Torrent aus der Datei erstellen
    const torrentInfo = await torrentService.createTorrent(filePath, {
      name: options.name || `${doi.replace('/', '_')}_publication`,
      comment: options.comment || `Publikation für DOI: ${doi}`,
      doi: doi,
      ...options.createOptions
    });
            
    // Torrent mit DOI verknüpfen (falls noch nicht geschehen)
    if (!torrentInfo.doi) {
      await torrentService.linkTorrentToDoi(torrentInfo.magnetURI, doi, {
        publicationDate: options.publicationDate || new Date().toISOString(),
        title: options.title,
        ...options.metadata
      });
    }
            
    return {
      doi,
      magnetURI: torrentInfo.magnetURI,
      infoHash: torrentInfo.infoHash,
      files: torrentInfo.files,
      linked: true,
      timestamp: new Date().toISOString()
    };
  }
      
  /**
   * Lädt eine Publikation anhand ihres DOI herunter
   * @param {string} doi - Der DOI der Publikation
   * @param {Object} options - Optionen für den Download
   * @returns {Promise<Object>} Informationen über den Download
   */
  async downloadPublication(doi, options = {}) {
    // TorrentService initialisieren, falls noch nicht geschehen
    const torrentService = this.connection.getTorrentService(options.torrentOptions);
            
    // Download über DOI starten
    return torrentService.downloadTorrent(doi, options);
  }
}

/**
 * ResearchDataManager-Klasse für die Verwaltung von Forschungsdatensätzen
 */
class ResearchDataManager {
  /**
   * Erstellt eine neue ResearchDataManager-Instanz
   * @param {PolkadotConnection|Object} connection - Die Polkadot-Verbindung oder Konfiguration
   */
  constructor(connection) {
    if (connection instanceof Object && 'getClient' in connection) {
      this.connection = connection;
    } else {
      // Bei Verwendung von ServiceFactory statt direkter Instanziierung
      this.connection = ServiceFactory.createPolkadotConnection(
        new PolkadotClient(connection),
        new PolkadotWallet(new PolkadotClient(connection), connection),
        new HandleService(new PolkadotClient(connection), connection.handleOptions || {}),
        new IPFSService(connection.ipfsOptions || {})
      );
    }
            
    // Services abrufen
    this.client = this.connection.getClient();
    this.wallet = this.connection.getWallet();
    this.ipfsService = this.connection.getIpfsService();
  }
      
  /**
   * Veröffentlicht einen Forschungsdatensatz
   * @param {Object} dataset - Informationen über den Datensatz
   * @param {string} dataDir - Verzeichnis mit den Datensatzdateien
   * @param {Object} options - Optionen für die Veröffentlichung
   * @returns {Promise<Object>} Das Ergebnis der Veröffentlichung
   */
  async publishDataset(dataset, dataDir, options = {}) {
    // Erforderliche Felder prüfen
    const requiredFields = ['name', 'description'];
    const missingFields = CommonUtils.getMissingRequiredFields(dataset, requiredFields);
            
    if (missingFields.length > 0) {
      throw new Errors.ValidationError(
        `Fehlende Pflichtfelder: ${missingFields.join(', ')}`,
        { missingFields }
      );
    }
            
    // TorrentService initialisieren, falls noch nicht geschehen
    const torrentService = this.connection.getTorrentService(options.torrentOptions);
            
    // Metadaten für den Datensatz vorbereiten
    const datasetMetadata = {
      name: dataset.name,
      description: dataset.description,
      authors: dataset.authors || [],
      license: dataset.license,
      publicationDate: dataset.publicationDate || new Date().toISOString(),
      version: dataset.version || '1.0.0',
      keywords: dataset.keywords || [],
      relatedPublications: dataset.relatedPublications || [],
      ...dataset.additionalMetadata
    };
            
    // Metadaten in IPFS hochladen
    const ipfsHash = await this.ipfsService.uploadMetadata(datasetMetadata, {
      pin: true,
      ...options.ipfsOptions
    });
            
    // Torrent aus dem Datenverzeichnis erstellen
    const torrentInfo = await torrentService.createTorrent(dataDir, {
      name: dataset.name,
      comment: dataset.description,
      ...options.createOptions
    });
            
    // Ergebnisobjekt vorbereiten
    const result = {
      name: dataset.name,
      doi: dataset.doi,
      ipfsHash,
      magnetURI: torrentInfo.magnetURI,
      infoHash: torrentInfo.infoHash,
      files: torrentInfo.files,
      published: true,
      timestamp: new Date().toISOString()
    };
            
    // Datensatz auf der Blockchain registrieren (falls DOI vorhanden)
    if (dataset.doi) {
      // Account für die Transaktion abrufen
      const account = options.account || await this.wallet.getAccount();
                  
      if (!account) {
        throw new Errors.ValidationError('Kein Account für Datensatzregistrierung verfügbar');
      }
                  
      const blockchainResult = await this.client.registerDOI(dataset.doi, ipfsHash, account, {
        gasLimit: options.gasLimit,
        ...options.transactionOptions
      });
                  
      // Torrent mit DOI verknüpfen
      await torrentService.linkTorrentToDoi(torrentInfo.magnetURI, dataset.doi, datasetMetadata);
                  
      // Blockchain-Ergebnis hinzufügen
      result.transactionHash = blockchainResult.transactionHash || blockchainResult.blockHash;
    }
        }

// Export aller Komponenten
export {
  // Konfiguration
  BlockchainConfig,

  // Core-Komponenten
  PolkadotClient,
  PolkadotWallet,

  // Services
  HandleService,
  HandleConfig,
  IPFSService,
  TorrentService,

  // Manager-Klassen
  DoiNftManager,
  ResearchDataManager,

  // Utilities
  CommonUtils,
  LoggerFactory,
  Errors,

  // Service Container
  ServiceContainer,

  // Factory
  ServiceFactory
};

// Factory-Methoden für einfachen Zugriff
export const createPolkadotConnection = ServiceFactory.createPolkadotConnection;
export const createPolkadotIntegration = ServiceFactory.createPolkadotIntegration;