/**
 * @fileoverview Gemeinsame Utility-Funktionen für alle Services
 * 
 * Diese Datei enthält Hilfsfunktionen, die in mehreren Services verwendet werden,
 * um Codewiederholungen zu vermeiden und Konsistenz zu gewährleisten.
 */

const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

/**
 * Formatiert Bytes in menschenlesbare Größen
 * @param {number} bytes - Die zu formatierenden Bytes
 * @param {number} decimals - Anzahl der Dezimalstellen (Standard: 2)
 * @returns {string} Formatierte Größe mit Einheit
 */
function formatBytes(bytes, decimals = 2) {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(decimals)) + ' ' + sizes[i];
}

/**
 * Generiert einen Hash für einen String mit dem angegebenen Algorithmus
 * @param {string} data - Die zu hashenden Daten
 * @param {string} algorithm - Der zu verwendende Hash-Algorithmus (Standard: sha256)
 * @returns {string} Der generierte Hash im Hex-Format
 */
function generateHash(data, algorithm = 'sha256') {
  const hash = crypto.createHash(algorithm);
  hash.update(data);
  return hash.digest('hex');
}

/**
 * Prüft, ob ein String eine gültige URL ist
 * @param {string} url - Die zu prüfende URL
 * @returns {boolean} true, wenn die URL gültig ist
 */
function isValidUrl(url) {
  try {
    // Versuche, eine URL zu erstellen (wirft bei ungültiger URL eine Ausnahme)
    new URL(url);
    return true;
  } catch (error) {
    return false;
  }
}

/**
 * Stellt sicher, dass ein Verzeichnis existiert, und erstellt es bei Bedarf
 * @param {string} dirPath - Pfad zum Verzeichnis
 * @returns {string} Der Pfad zum Verzeichnis
 */
function ensureDirectoryExists(dirPath) {
  const absolutePath = path.isAbsolute(dirPath) ? 
    dirPath : 
    path.resolve(process.cwd(), dirPath);
  
  if (!fs.existsSync(absolutePath)) {
    fs.mkdirSync(absolutePath, { recursive: true });
  }
  
  return absolutePath;
}

/**
 * Wartet für eine bestimmte Zeit
 * @param {number} ms - Millisekunden zu warten
 * @returns {Promise<void>} Promise, die nach der angegebenen Zeit aufgelöst wird
 */
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Konvertiert einen Wert in BigInt für Blockchain-Transaktionen
 * @param {number|string} value - Zu konvertierender Wert
 * @returns {BigInt} Der konvertierte BigInt-Wert
 */
function toBigInt(value) {
  if (typeof value === 'bigint') return value;
  if (typeof value === 'number') return BigInt(Math.floor(value));
  if (typeof value === 'string') {
    // Entferne alle Nicht-Ziffern, außer Dezimalpunkt
    const cleaned = value.replace(/[^\d.]/g, '');
    // Entferne Dezimalteil für BigInt
    const integer = cleaned.includes('.') ? 
      cleaned.substring(0, cleaned.indexOf('.')) : 
      cleaned;
    return integer ? BigInt(integer) : BigInt(0);
  }
  return BigInt(0);
}

/**
 * Kürzt einen String in der Mitte und ersetzt den mittleren Teil durch Ellipsis
 * @param {string} str - Der zu kürzende String
 * @param {number} maxLength - Maximale Gesamtlänge (Standard: 20)
 * @returns {string} Der gekürzte String
 */
function truncateMiddle(str, maxLength = 20) {
  if (!str || str.length <= maxLength) return str;
  
  const ellipsis = '...';
  const charsToShow = maxLength - ellipsis.length;
  const frontChars = Math.ceil(charsToShow / 2);
  const backChars = Math.floor(charsToShow / 2);
  
  return str.substring(0, frontChars) + ellipsis + str.substring(str.length - backChars);
}

/**
 * Validiert einen DOI nach dem Standard-Format (10.XXXX/YYYY)
 * @param {string} doi - Der zu validierende DOI
 * @returns {boolean} true, wenn der DOI gültig ist
 */
function isValidDoi(doi) {
  // Allgemeines DOI-Format: 10.XXXX/YYYY
  const doiPattern = /^10\.\d{4,}(\.\d+)*\/[-._;()/:A-Z0-9]+$/i;
  return doiPattern.test(doi);
}

/**
 * Validiert eine ORCID nach dem Standard-Format (0000-0000-0000-0000)
 * @param {string} orcid - Die zu validierende ORCID
 * @returns {boolean} true, wenn die ORCID gültig ist
 */
function isValidOrcid(orcid) {
  // ORCID-Format: 0000-0000-0000-0000 oder https://orcid.org/0000-0000-0000-0000
  const orcidPattern = /^(\d{4}-){3}\d{3}(\d|X)$|^https?:\/\/orcid\.org\/(\d{4}-){3}\d{3}(\d|X)$/i;
  return orcidPattern.test(orcid);
}

/**
 * Validiert, ob ein String eine gültige IPFS-CID (Content Identifier) ist
 * @param {string} cid - Die zu validierende CID
 * @returns {boolean} true, wenn die CID gültig ist
 */
function isValidIpfsCid(cid) {
  if (!cid || typeof cid !== 'string') {
    return false;
  }
  
  // CIDv0 beginnt mit "Qm" und hat 46 Zeichen
  if (/^Qm[1-9A-Za-z]{44}$/.test(cid)) {
    return true;
  }
  
  // CIDv1 beginnt häufig mit "b" oder "z" und kann unterschiedliche Längen haben
  if (/^[bz][1-9A-HJ-NP-Za-km-z]{48,58}$/.test(cid)) {
    return true;
  }
  
  return false;
}

/**
 * Normalisiert einen DOI zu seinem kanonischen Format
 * @param {string} doi - Der zu normalisierende DOI
 * @returns {string} Der normalisierte DOI
 */
function normalizeDoi(doi) {
  if (!doi) return '';
  
  // Entferne 'doi:' Präfix, falls vorhanden
  let normalizedDoi = doi.replace(/^doi:/i, '');
  
  // Entferne URL-Präfix, falls vorhanden
  normalizedDoi = normalizedDoi.replace(/^https?:\/\/doi\.org\//i, '');
  
  // Trimme whitespace
  normalizedDoi = normalizedDoi.trim();
  
  return normalizedDoi;
}

/**
 * Generiert einen zufälligen alphanumerischen String
 * @param {number} length - Länge des zu generierenden Strings (Standard: 8)
 * @returns {string} Der generierte String
 */
function generateRandomString(length = 8) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  
  return result;
}

/**
 * Prüft, ob ein Objekt alle erforderlichen Felder enthält
 * @param {Object} obj - Das zu prüfende Objekt
 * @param {Array<string>} requiredFields - Array mit den Namen der erforderlichen Felder
 * @returns {Array<string>} Array mit den Namen der fehlenden Felder
 */
function getMissingRequiredFields(obj, requiredFields) {
  if (!obj || typeof obj !== 'object') {
    return requiredFields;
  }
  
  return requiredFields.filter(field => {
    const value = obj[field];
    return value === undefined || value === null || value === '';
  });
}

/**
 * Gruppiert ein Array von Objekten nach einem bestimmten Schlüssel
 * @param {Array<Object>} array - Das zu gruppierende Array
 * @param {string|Function} key - Der Schlüssel oder eine Funktion, die den Schlüssel extrahiert
 * @returns {Object} Gruppiertes Objekt mit Schlüsseln als Eigenschaften
 */
function groupBy(array, key) {
  return array.reduce((result, item) => {
    const groupKey = typeof key === 'function' ? key(item) : item[key];
    // Stelle sicher, dass der Schlüssel als String verwendet wird
    const groupKeyStr = String(groupKey || 'undefined');
    
    if (!result[groupKeyStr]) {
      result[groupKeyStr] = [];
    }
    
    result[groupKeyStr].push(item);
    return result;
  }, {});
}

module.exports = {
  formatBytes,
  generateHash,
  isValidUrl,
  ensureDirectoryExists,
  sleep,
  toBigInt,
  truncateMiddle,
  isValidDoi,
  isValidOrcid,
  isValidIpfsCid,
  normalizeDoi,
  generateRandomString,
  getMissingRequiredFields,
  groupBy
}; 