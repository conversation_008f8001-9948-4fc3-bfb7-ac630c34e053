/**
 * @fileoverview TorrentService für DeSci-Scholar
 * 
 * Diese Klasse bietet eine einheitliche Schnittstelle für die Verteilung von
 * Publikationsdateien und Forschungsdatensätzen über das BitTorrent-Protokoll.
 */

const WebTorrent = require('webtorrent');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const winston = require('winston');
const BlockchainConfig = require('./BlockchainConfig');

// Logger konfigurieren
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: { service: 'torrent-service' },
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    }),
    new winston.transports.File({ 
      filename: 'logs/torrent-error.log', 
      level: 'error' 
    }),
    new winston.transports.File({ 
      filename: 'logs/torrent-combined.log' 
    })
  ]
});

/**
 * TorrentService-Klasse für die Verwaltung von Torrents für wissenschaftliche Ressourcen
 */
class TorrentService {
  /**
   * Erstellt eine neue Instanz des TorrentService
   * @param {Object} options - Konfigurationsoptionen
   */
  constructor(options = {}) {
    // Konfiguration aus BlockchainConfig oder übergebenen Optionen verwenden
    const btConfig = BlockchainConfig.SERVICES.BITTORRENT;
    
    this.config = {
      trackers: options.trackers || btConfig.TRACKERS,
      seedDuration: options.seedDuration || btConfig.SEED_DURATION,
      downloadLocation: options.downloadLocation || btConfig.DOWNLOAD_LOCATION,
      uploadSpeedLimit: options.uploadSpeedLimit || 0, // 0 = unbegrenzt
      downloadSpeedLimit: options.downloadSpeedLimit || 0, // 0 = unbegrenzt
      maxConnections: options.maxConnections || 55, // Standard-WebTorrent-Maximum
      dhtPort: options.dhtPort || 0, // 0 = zufälliger Port
      torrentPort: options.torrentPort || 0 // 0 = zufälliger Port
    };
    
    // WebTorrent-Client initialisieren
    this.client = new WebTorrent({
      maxConns: this.config.maxConnections,
      utp: true, // µTP-Protokoll verwenden
      dht: { port: this.config.dhtPort }, // DHT aktivieren
      tracker: true,
      port: this.config.torrentPort
    });
    
    // Protokoll zur Verknüpfung von Torrents mit DOIs
    this.doiTorrentMap = new Map();
    
    // Verzeichnisse für Torrents und Downloads erstellen, falls nicht vorhanden
    this.torrentDir = path.resolve(process.cwd(), 'torrents');
    this.downloadDir = path.resolve(process.cwd(), this.config.downloadLocation);
    
    if (!fs.existsSync(this.torrentDir)) {
      fs.mkdirSync(this.torrentDir, { recursive: true });
    }
    
    if (!fs.existsSync(this.downloadDir)) {
      fs.mkdirSync(this.downloadDir, { recursive: true });
    }
    
    // Event-Listener registrieren
    this._setupEventListeners();
    
    logger.info('TorrentService erfolgreich initialisiert', { 
      trackers: this.config.trackers.length,
      downloadLocation: this.downloadDir,
      torrentDir: this.torrentDir
    });
  }
  
  /**
   * Richtet Event-Listener für den WebTorrent-Client ein
   * @private
   */
  _setupEventListeners() {
    this.client.on('error', (err) => {
      logger.error('WebTorrent-Client-Fehler', { error: err.message });
    });
    
    this.client.on('warning', (warn) => {
      logger.warn('WebTorrent-Client-Warnung', { warning: warn.message });
    });
    
    // Weitere Event-Listener könnten hier hinzugefügt werden...
  }
  
  /**
   * Generiert einen deterministischen infoHash auf Basis eines DOI
   * @param {string} doi - Der DOI als Basis für den Hash
   * @param {string} salt - Ein optionaler Salt für den Hash
   * @returns {string} Der generierte Hash im Hex-Format
   * @private
   */
  _generateInfoHashFromDoi(doi, salt = '') {
    if (!doi) {
      throw new Error('DOI ist erforderlich, um einen infoHash zu generieren');
    }
    
    // Einen deterministischen Hash erstellen, der als Basis für den infoHash dienen kann
    const hash = crypto.createHash('sha1');
    hash.update(`desci-scholar:${doi}:${salt}`);
    return hash.digest('hex');
  }
  
  /**
   * Speichert die Verknüpfung zwischen DOI und Torrent
   * @param {string} doi - Der DOI
   * @param {Object} torrentInfo - Informationen über den Torrent
   * @private
   */
  async _storeTorrentDoiMapping(doi, torrentInfo) {
    // In einer realen Implementierung würde dies in einer Datenbank gespeichert
    this.doiTorrentMap.set(doi, torrentInfo);
    
    // In einer JSON-Datei für Persistenz speichern
    const mappingFile = path.join(this.torrentDir, 'doi-torrent-mapping.json');
    
    try {
      let mappings = {};
      
      // Vorhandene Mappings laden, falls die Datei existiert
      if (fs.existsSync(mappingFile)) {
        const fileContent = fs.readFileSync(mappingFile, 'utf8');
        mappings = JSON.parse(fileContent);
      }
      
      // Neues Mapping hinzufügen
      mappings[doi] = {
        ...torrentInfo,
        updatedAt: new Date().toISOString()
      };
      
      // Zurück in die Datei schreiben
      fs.writeFileSync(mappingFile, JSON.stringify(mappings, null, 2), 'utf8');
      
      logger.info('DOI-Torrent-Mapping gespeichert', { doi, magnetURI: torrentInfo.magnetURI });
    } catch (error) {
      logger.error('Fehler beim Speichern des DOI-Torrent-Mappings', { 
        doi, 
        error: error.message 
      });
    }
  }
  
  /**
   * Ruft das Torrent-Mapping für einen DOI ab
   * @param {string} doi - Der DOI
   * @returns {Object|null} Die Torrent-Informationen oder null, wenn nicht gefunden
   */
  async getTorrentInfoForDoi(doi) {
    // Zuerst aus dem In-Memory-Cache abrufen
    if (this.doiTorrentMap.has(doi)) {
      return this.doiTorrentMap.get(doi);
    }
    
    // Sonst aus der Datei laden
    const mappingFile = path.join(this.torrentDir, 'doi-torrent-mapping.json');
    
    try {
      if (fs.existsSync(mappingFile)) {
        const fileContent = fs.readFileSync(mappingFile, 'utf8');
        const mappings = JSON.parse(fileContent);
        
        if (mappings[doi]) {
          // Im In-Memory-Cache speichern und zurückgeben
          this.doiTorrentMap.set(doi, mappings[doi]);
          return mappings[doi];
        }
      }
    } catch (error) {
      logger.error('Fehler beim Abrufen des DOI-Torrent-Mappings', { 
        doi, 
        error: error.message 
      });
    }
    
    // Nicht gefunden
    return null;
  }
  
  /**
   * Erstellt einen Torrent aus einer Datei oder einem Verzeichnis
   * @param {string} inputPath - Pfad zur Datei oder zum Verzeichnis
   * @param {Object} options - Optionen für die Torrent-Erstellung
   * @returns {Promise<Object>} Informationen über den erstellten Torrent
   */
  async createTorrent(inputPath, options = {}) {
    if (!fs.existsSync(inputPath)) {
      throw new Error(`Die angegebene Datei oder das Verzeichnis existiert nicht: ${inputPath}`);
    }
    
    logger.info('Erstelle Torrent', { inputPath });
    
    const stat = fs.statSync(inputPath);
    const isDirectory = stat.isDirectory();
    
    // Metadaten für den Torrent vorbereiten
    const name = options.name || path.basename(inputPath);
    const comment = options.comment || 'Erstellt von DeSci-Scholar';
    const createdBy = options.createdBy || 'DeSci-Scholar TorrentService';
    
    // Trackers aus der Konfiguration oder den Optionen verwenden
    const trackers = options.trackers || this.config.trackers;
    const announceList = [trackers];
    
    return new Promise((resolve, reject) => {
      // Torrent über WebTorrent erstellen
      this.client.seed(inputPath, {
        name,
        comment,
        createdBy,
        announceList,
        private: options.private === true, // Standardmäßig öffentlich
        pieceLength: options.pieceLength, // Standard von WebTorrent verwenden, wenn nicht angegeben
        urlList: options.urlList || [] // Web-Seeds
      }, (torrent) => {
        logger.info('Torrent erfolgreich erstellt', { 
          name: torrent.name,
          infoHash: torrent.infoHash,
          size: torrent.length
        });
        
        // Pfad zur .torrent-Datei
        const torrentFilePath = path.join(this.torrentDir, `${torrent.infoHash}.torrent`);
        
        // .torrent-Datei speichern
        try {
          fs.writeFileSync(torrentFilePath, torrent.torrentFile);
          logger.info('Torrent-Datei gespeichert', { path: torrentFilePath });
        } catch (error) {
          logger.error('Fehler beim Speichern der Torrent-Datei', { 
            path: torrentFilePath, 
            error: error.message 
          });
        }
        
        // Torrent-Info-Objekt erstellen
        const torrentInfo = {
          infoHash: torrent.infoHash,
          magnetURI: torrent.magnetURI,
          name: torrent.name,
          length: torrent.length,
          files: torrent.files.map(file => ({
            name: file.name,
            path: file.path,
            length: file.length
          })),
          torrentFilePath,
          createdAt: new Date().toISOString()
        };
        
        // DOI zuordnen, falls angegeben
        if (options.doi) {
          this._storeTorrentDoiMapping(options.doi, torrentInfo);
        }
        
        // Wenn auto-stop aktiviert ist, Seeden nach der angegebenen Zeit stoppen
        if (options.autoStopSeeding !== false) {
          const seedDuration = options.seedDuration || this.config.seedDuration;
          
          setTimeout(() => {
            logger.info('Automatisches Stoppen des Seeds nach Zeitablauf', { 
              infoHash: torrent.infoHash, 
              duration: seedDuration 
            });
            torrent.destroy();
          }, seedDuration);
        }
        
        resolve(torrentInfo);
      })
      .on('error', (err) => {
        logger.error('Fehler beim Erstellen des Torrents', { 
          inputPath, 
          error: err.message 
        });
        reject(err);
      });
    });
  }
  
  /**
   * Verknüpft einen Torrent mit einem DOI
   * @param {string} magnetURI - Der Magnet-URI des Torrents
   * @param {string} doi - Der zu verknüpfende DOI
   * @param {Object} metadata - Zusätzliche Metadaten für die Verknüpfung
   * @returns {Promise<Object>} Die gespeicherte Verknüpfung
   */
  async linkTorrentToDoi(magnetURI, doi, metadata = {}) {
    if (!magnetURI) {
      throw new Error('Magnet-URI ist erforderlich');
    }
    
    if (!doi) {
      throw new Error('DOI ist erforderlich');
    }
    
    logger.info('Verknüpfe Torrent mit DOI', { magnetURI, doi });
    
    try {
      // Torrent-Infos extrahieren
      const matches = magnetURI.match(/xt=urn:btih:([^&]*)/i);
      
      if (!matches || !matches[1]) {
        throw new Error('Konnte infoHash nicht aus Magnet-URI extrahieren');
      }
      
      const infoHash = matches[1].toLowerCase();
      
      // Name aus Magnet-URI extrahieren (falls vorhanden)
      const nameMatch = magnetURI.match(/dn=([^&]*)/i);
      const name = nameMatch && nameMatch[1] ? 
        decodeURIComponent(nameMatch[1]) : 
        `Torrent für DOI ${doi}`;
      
      // Torrent-Info-Objekt erstellen
      const torrentInfo = {
        infoHash,
        magnetURI,
        name,
        doi,
        metadata: {
          ...metadata,
          linkedAt: new Date().toISOString()
        }
      };
      
      // Verknüpfung speichern
      await this._storeTorrentDoiMapping(doi, torrentInfo);
      
      return torrentInfo;
    } catch (error) {
      logger.error('Fehler bei der Verknüpfung des Torrents mit DOI', { 
        magnetURI, 
        doi, 
        error: error.message 
      });
      throw error;
    }
  }
  
  /**
   * Lädt einen Torrent anhand seiner Magnet-URI oder seines DOI herunter
   * @param {string} identifier - Magnet-URI oder DOI
   * @param {Object} options - Optionen für den Download
   * @returns {Promise<Object>} Informationen über den Download
   */
  async downloadTorrent(identifier, options = {}) {
    let magnetURI = identifier;
    
    // Prüfen, ob es sich um einen DOI handelt
    if (identifier.includes('10.') || identifier.includes('doi:')) {
      logger.info('Identifier scheint ein DOI zu sein, suche nach verknüpftem Torrent', { doi: identifier });
      
      const torrentInfo = await this.getTorrentInfoForDoi(identifier);
      
      if (!torrentInfo || !torrentInfo.magnetURI) {
        throw new Error(`Kein verknüpfter Torrent für DOI gefunden: ${identifier}`);
      }
      
      magnetURI = torrentInfo.magnetURI;
      logger.info('Verknüpften Torrent gefunden', { doi: identifier, magnetURI });
    }
    
    // Optionen vorbereiten
    const downloadPath = options.path || this.downloadDir;
    
    logger.info('Starte Torrent-Download', { magnetURI, downloadPath });
    
    return new Promise((resolve, reject) => {
      // Prüfen, ob der Torrent bereits heruntergeladen wird
      const existingTorrent = this.client.torrents.find(t => 
        t.magnetURI === magnetURI || t.infoHash === magnetURI
      );
      
      if (existingTorrent) {
        logger.info('Torrent wird bereits heruntergeladen', { infoHash: existingTorrent.infoHash });
        
        // Wenn der Download bereits abgeschlossen ist
        if (existingTorrent.done) {
          logger.info('Torrent bereits vollständig heruntergeladen', { infoHash: existingTorrent.infoHash });
          
          return resolve({
            infoHash: existingTorrent.infoHash,
            magnetURI: existingTorrent.magnetURI,
            name: existingTorrent.name,
            files: existingTorrent.files.map(file => ({
              name: file.name,
              path: file.path,
              length: file.length
            })),
            path: existingTorrent.path,
            done: true,
            progress: 1
          });
        }
        
        // Sonst den aktuellen Download verwenden
        return this._handleTorrentDownload(existingTorrent, resolve, reject);
      }
      
      // Neuen Download starten
      this.client.add(magnetURI, { path: downloadPath }, (torrent) => {
        logger.info('Torrent zum Client hinzugefügt', { 
          infoHash: torrent.infoHash,
          name: torrent.name
        });
        
        this._handleTorrentDownload(torrent, resolve, reject);
      })
      .on('error', (err) => {
        logger.error('Fehler beim Hinzufügen des Torrents', { magnetURI, error: err.message });
        reject(err);
      });
    });
  }
  
  /**
   * Kümmert sich um den Download-Prozess eines Torrents
   * @param {Object} torrent - Das Torrent-Objekt
   * @param {Function} resolve - Resolve-Funktion der Promise
   * @param {Function} reject - Reject-Funktion der Promise
   * @private
   */
  _handleTorrentDownload(torrent, resolve, reject) {
    // Fortschritt protokollieren
    let lastProgress = 0;
    
    const progressInterval = setInterval(() => {
      // Nur protokollieren, wenn signifikante Änderung
      if (Math.abs(torrent.progress - lastProgress) > 0.05) {
        logger.info('Download-Fortschritt', {
          infoHash: torrent.infoHash,
          progress: Math.floor(torrent.progress * 100) + '%',
          downloadSpeed: this._formatBytes(torrent.downloadSpeed) + '/s',
          uploadSpeed: this._formatBytes(torrent.uploadSpeed) + '/s',
          peers: torrent.numPeers
        });
        
        lastProgress = torrent.progress;
      }
    }, 5000);
    
    // Event-Listener für Download-Abschluss
    torrent.on('done', () => {
      clearInterval(progressInterval);
      
      logger.info('Torrent-Download abgeschlossen', { 
        infoHash: torrent.infoHash,
        name: torrent.name,
        path: torrent.path
      });
      
      // Information über heruntergeladene Dateien
      const downloadResult = {
        infoHash: torrent.infoHash,
        magnetURI: torrent.magnetURI,
        name: torrent.name,
        files: torrent.files.map(file => ({
          name: file.name,
          path: file.path,
          length: file.length
        })),
        path: torrent.path,
        done: true,
        progress: 1
      };
      
      resolve(downloadResult);
    });
    
    // Event-Listener für Fehler
    torrent.on('error', (err) => {
      clearInterval(progressInterval);
      
      logger.error('Fehler beim Torrent-Download', { 
        infoHash: torrent.infoHash,
        error: err.message
      });
      
      reject(err);
    });
    
    // Nach Ablauf der Timeout-Zeit abbrechen, falls angegeben
    if (torrent.timeRemaining && torrent.timeRemaining !== Infinity) {
      // Timeout mit Sicherheitspuffer
      const timeout = torrent.timeRemaining * 1.5;
      
      setTimeout(() => {
        // Prüfen, ob der Download noch läuft und nicht abgeschlossen ist
        if (!torrent.done) {
          logger.warn('Download-Timeout erreicht', { infoHash: torrent.infoHash });
          
          clearInterval(progressInterval);
          
          // Torrent-Infos sammeln, bevor der Download abgebrochen wird
          const timeoutResult = {
            infoHash: torrent.infoHash,
            magnetURI: torrent.magnetURI,
            name: torrent.name,
            files: torrent.files.map(file => ({
              name: file.name,
              path: file.path,
              length: file.length
            })),
            path: torrent.path,
            done: false,
            progress: torrent.progress,
            error: 'Download-Timeout erreicht'
          };
          
          resolve(timeoutResult);
        }
      }, timeout);
    }
  }
  
  /**
   * Formatiert Bytes in lesbare Größen
   * @param {number} bytes - Die zu formatierenden Bytes
   * @returns {string} Die formatierte Größe
   * @private
   */
  _formatBytes(bytes) {
    if (bytes === 0) return '0 B';
    
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    
    return parseFloat((bytes / Math.pow(1024, i)).toFixed(2)) + ' ' + sizes[i];
  }
  
  /**
   * Sucht nach Torrents basierend auf DOIs oder anderen Kriterien
   * @param {Object} criteria - Suchkriterien
   * @returns {Promise<Array>} Gefundene Torrents
   */
  async searchTorrents(criteria = {}) {
    logger.info('Suche nach Torrents', { criteria });
    
    // In einer realen Implementierung würde dies eine Datenbankabfrage sein
    // Für diese Demo verwenden wir die gespeicherten Mappings
    const mappingFile = path.join(this.torrentDir, 'doi-torrent-mapping.json');
    
    try {
      if (!fs.existsSync(mappingFile)) {
        return [];
      }
      
      const fileContent = fs.readFileSync(mappingFile, 'utf8');
      const mappings = JSON.parse(fileContent);
      
      // Alle Mappings in ein Array umwandeln
      const allTorrents = Object.entries(mappings).map(([doi, info]) => ({
        doi,
        ...info
      }));
      
      // Nach Kriterien filtern
      return allTorrents.filter(torrent => {
        // Nach DOI filtern
        if (criteria.doi) {
          return torrent.doi === criteria.doi || torrent.doi.includes(criteria.doi);
        }
        
        // Nach Namen filtern
        if (criteria.name) {
          return torrent.name && torrent.name.toLowerCase().includes(criteria.name.toLowerCase());
        }
        
        // Nach infoHash filtern
        if (criteria.infoHash) {
          return torrent.infoHash === criteria.infoHash;
        }
        
        // Nach Metadaten filtern
        if (criteria.metadata) {
          const metadataKeys = Object.keys(criteria.metadata);
          return metadataKeys.every(key => 
            torrent.metadata && 
            torrent.metadata[key] === criteria.metadata[key]
          );
        }
        
        // Wenn keine Kriterien angegeben, alle Torrents zurückgeben
        return true;
      });
    } catch (error) {
      logger.error('Fehler bei der Torrent-Suche', { criteria, error: error.message });
      return [];
    }
  }
  
  /**
   * Stoppt den Seeding-Prozess für einen Torrent
   * @param {string} identifier - infoHash, Magnet-URI oder DOI
   * @returns {Promise<boolean>} true, wenn erfolgreich gestoppt
   */
  async stopSeeding(identifier) {
    logger.info('Stoppe Seeding für Torrent', { identifier });
    
    let torrent;
    
    // Prüfen, ob es sich um einen DOI handelt
    if (identifier.includes('10.') || identifier.includes('doi:')) {
      const torrentInfo = await this.getTorrentInfoForDoi(identifier);
      
      if (!torrentInfo) {
        logger.warn('Kein verknüpfter Torrent für DOI gefunden', { doi: identifier });
        return false;
      }
      
      // Torrent anhand des infoHash suchen
      torrent = this.client.torrents.find(t => t.infoHash === torrentInfo.infoHash);
    } else {
      // Direkt nach infoHash oder magnetURI suchen
      torrent = this.client.torrents.find(t => 
        t.infoHash === identifier || t.magnetURI === identifier
      );
    }
    
    if (!torrent) {
      logger.warn('Torrent nicht gefunden oder nicht aktiv', { identifier });
      return false;
    }
    
    // Torrent zerstören (Seeding stoppen)
    torrent.destroy();
    logger.info('Torrent-Seeding erfolgreich gestoppt', { infoHash: torrent.infoHash });
    
    return true;
  }
  
  /**
   * Gibt Statistiken über aktive Torrents zurück
   * @returns {Object} Statistiken zu aktiven Torrents
   */
  getStats() {
    const torrents = this.client.torrents;
    
    const stats = {
      activeTorrents: torrents.length,
      downloadSpeed: this._formatBytes(this.client.downloadSpeed) + '/s',
      uploadSpeed: this._formatBytes(this.client.uploadSpeed) + '/s',
      totalDownloaded: this._formatBytes(this.client.downloaded),
      totalUploaded: this._formatBytes(this.client.uploaded),
      ratio: this.client.downloaded ? (this.client.uploaded / this.client.downloaded).toFixed(2) : 0,
      torrents: torrents.map(t => ({
        infoHash: t.infoHash,
        name: t.name,
        progress: Math.floor(t.progress * 100) + '%',
        downloadSpeed: this._formatBytes(t.downloadSpeed) + '/s',
        uploadSpeed: this._formatBytes(t.uploadSpeed) + '/s',
        numPeers: t.numPeers,
        done: t.done
      }))
    };
    
    return stats;
  }
  
  /**
   * Bereinigt alle aktiven Torrents und schließt den Client
   */
  async destroy() {
    logger.info('Beende TorrentService und bereinige Torrents');
    
    // Alle aktiven Torrents zerstören
    this.client.torrents.forEach(torrent => {
      torrent.destroy();
    });
    
    // Warten, bis Client heruntergefahren wurde
    return new Promise(resolve => {
      this.client.destroy(err => {
        if (err) {
          logger.error('Fehler beim Beenden des WebTorrent-Clients', { error: err.message });
        } else {
          logger.info('WebTorrent-Client erfolgreich beendet');
        }
        
        resolve();
      });
    });
  }
}

module.exports = TorrentService; 