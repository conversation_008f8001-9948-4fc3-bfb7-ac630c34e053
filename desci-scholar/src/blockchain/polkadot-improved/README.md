# Optimierte Polkadot-Integration für DeSci-Scholar

Diese Implementierung bietet eine umfassende und optimierte Integration der Polkadot-Blockchain für das DeSci-Scholar-Projekt. Sie beinhaltet eine vollständige Suite von Werkzeugen für Blockchain-Interaktionen, DOI-Verwaltung und dezentralisierte Datenspeicherung.

## Architektur

Die Architektur des optimierten Polkadot-Moduls ist modular aufgebaut und vermeidet Redundanzen durch zentrale Dienste und klare Zuständigkeiten:

```
src/blockchain/polkadot-improved/
├── BlockchainConfig.js      # Zentrale Konfiguration für alle Komponenten
├── PolkadotClient.js        # Kern-Client für Blockchain-Interaktionen
├── PolkadotWallet.js        # Wallet-Management und Zahlungen
├── HandleService.js         # DOI/Handle-System-Integration
├── HandleConfig.js          # Handle-System-Konfiguration
├── IPFSService.js           # IPFS-Metadatenspeicherung
├── TorrentService.js        # BitTorrent-Dateispeicherung
├── integration-example.js   # Beispiel für die Integration aller Komponenten
├── index.js                 # Hauptexport aller Komponenten
└── README.md                # Diese Datei
```

## Zentrale Komponenten

### 1. Blockchain-Integration (Polkadot)

- **PolkadotClient**: Bietet robuste Interaktionen mit der Polkadot-Blockchain
- **PolkadotWallet**: Verwaltet Konten, Transaktionen und Zahlungen
- **PolkadotConnection**: Zentrale Singleton-Klasse, die alle Services koordiniert

### 2. DOI-Verwaltung

- **HandleService**: Einheitliche Schnittstelle für alle DOI/Handle-System-Interaktionen
- **HandleConfig**: Zentrale Konfiguration aller DOI-bezogenen Endpunkte und Parameter

### 3. Dezentrale Speicherung

- **IPFSService**: Optimierte Metadatenspeicherung auf IPFS
- **TorrentService**: Effiziente Verteilung von Publikationsdateien über BitTorrent

### 4. Manager-Klassen

- **DoiNftManager**: Verwaltet die Erstellung und Verknüpfung von DOI-NFTs
- **ResearchDataManager**: Spezialisiert auf Forschungsdatensätze

## Hauptvorteile

1. **Redundanzfreiheit**: Alle gemeinsamen Funktionen wurden in spezialisierte Services konsolidiert
2. **Zentrale Konfiguration**: Alle Einstellungen werden an einem Ort verwaltet
3. **Optimierte Integration**: Nahtlose Zusammenarbeit zwischen Blockchain, IPFS und BitTorrent
4. **Ressourceneffizienz**: Lazy-Loading für ressourcenintensive Komponenten wie TorrentService
5. **Modulare Architektur**: Klare Trennung der Verantwortlichkeiten

## Verwendung

### Grundlegende Initialisierung

```javascript
const { createPolkadotConnection } = require('./blockchain/polkadot-improved');

// Erstellen und Initialisieren einer Verbindung
const connection = createPolkadotConnection({
  network: 'WESTEND',  // Testnetz verwenden
  endpoint: 'wss://westend-rpc.polkadot.io'
});

// Verbindung und Services initialisieren
await connection.initialize();

// Services abrufen
const client = connection.getClient();
const wallet = connection.getWallet();
const handleService = connection.getHandleService();
const ipfsService = connection.getIpfsService();
```

### DOI-NFT-Workflow

```javascript
// DoiNftManager erstellen
const doiManager = new DoiNftManager(connection);

// DOI als NFT registrieren
const registrationResult = await doiManager.registerDoiAsNft(
  '10.5555/2023.demo123',  // DOI
  {
    title: 'Beispiel-Publikation',
    abstract: 'Zusammenfassung der Publikation...',
    authors: [{ name: 'Dr. Max Mustermann', orcid: '0000-0002-1825-0097' }],
    publicationDate: '2023-09-01'
  }
);

// Publikationsdatei mit DOI verknüpfen
const linkResult = await doiManager.linkDoiToFile(
  '10.5555/2023.demo123',  // DOI
  '/path/to/publication.pdf',  // Dateipfad
  { title: 'Publikationstitel' }
);
```

### Forschungsdatensatz veröffentlichen

```javascript
// ResearchDataManager erstellen
const dataManager = new ResearchDataManager(connection);

// Datensatz veröffentlichen
const datasetResult = await dataManager.publishDataset(
  {
    name: 'Beispiel-Datensatz',
    description: 'Beschreibung des Datensatzes',
    authors: [{ name: 'Dr. Erika Musterfrau' }],
    license: 'CC-BY-4.0'
  },
  '/path/to/dataset/directory'
);
```

### Publikation herunterladen

```javascript
// Publikation anhand des DOI herunterladen
const downloadResult = await doiManager.downloadPublication('10.5555/2023.demo123');
console.log(`Heruntergeladen nach: ${downloadResult.path}`);
```

## Vollständiges Beispiel

Siehe `integration-example.js` für ein vollständiges Beispiel, das den gesamten Workflow demonstriert:

1. DOI-Registrierung
2. NFT-Erstellung
3. Metadaten-Speicherung in IPFS
4. Publikationsverteilung über BitTorrent
5. Forschungsdaten-Management

## Installation

1. Klonen Sie das Repository
2. Installieren Sie die Abhängigkeiten:

```bash
npm install @polkadot/api webtorrent ipfs-http-client axios winston
```

3. Konfigurieren Sie die Umgebungsvariablen (optional):

```
POLKADOT_ENDPOINT=wss://westend-rpc.polkadot.io
IPFS_GATEWAY=https://ipfs.io/ipfs/
```

## Optimierungen zur Redundanzvermeidung

Diese Implementierung reduziert Redundanzen in folgenden Bereichen:

1. **DOI-Validierung und -Auflösung**: Zentralisiert im HandleService
2. **IPFS-Interaktionen**: Konsolidiert im IPFSService
3. **Blockchain-Transaktionen**: Optimiert im PolkadotClient
4. **Konfigurationsparameter**: Zentralisiert in BlockchainConfig
5. **Wallet-Management**: Vereinheitlicht im PolkadotWallet

## Autoren

DeSci-Scholar Team

## Lizenz

MIT 