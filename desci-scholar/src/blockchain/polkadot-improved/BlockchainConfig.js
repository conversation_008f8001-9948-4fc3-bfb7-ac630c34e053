/**
 * @fileoverview Zentrale Konfiguration für alle Blockchain-bezogenen Operationen
 * 
 * Diese Datei bündelt alle Konfigurationsparameter für die Blockchain-Integration,
 * um Redundanzen zu vermeiden und eine einheitliche Konfiguration sicherzustellen.
 */

/**
 * Blockchain-Netzwerkkonfigurationen
 */
const NETWORKS = {
  POLKADOT: {
    name: '<PERSON><PERSON><PERSON>',
    endpoint: process.env.POLKADOT_ENDPOINT || 'wss://rpc.polkadot.io',
    chainId: '0x91b171bb158e2d3848fa23a9f1c25182fb8e20313b2c1eb49219da7a70ce90c3'
  },
  KUSAMA: {
    name: '<PERSON><PERSON><PERSON>',
    endpoint: process.env.KUSAMA_ENDPOINT || 'wss://kusama-rpc.polkadot.io',
    chainId: '0xb0a8d493285c2df73290dfb7e61f870f17b41801197a149ca93654499ea3dafe'
  },
  ASTAR: {
    name: 'Astar',
    endpoint: process.env.ASTAR_ENDPOINT || 'wss://rpc.astar.network',
    chainId: '0x9eb76c5184c4ab8679d2d5d819fdf90b9c001403e9e17da2e14b6d8aec4029c6'
  },
  // Testnetze
  WESTEND: {
    name: 'Westend',
    endpoint: process.env.WESTEND_ENDPOINT || 'wss://westend-rpc.polkadot.io',
    chainId: '0xe143f23803ac50e8f6f8e62695d1ce9e4e1d68aa36c1cd2cfd15340213f3423e'
  },
  ROCOCO: {
    name: 'Rococo',
    endpoint: process.env.ROCOCO_ENDPOINT || 'wss://rococo-rpc.polkadot.io',
    chainId: '0x6408de7737c59c238890533af25896a2c20608d8b380bb01029acb392781e3cf'
  },
  // Entwicklung
  LOCAL: {
    name: 'Local Development',
    endpoint: process.env.LOCAL_ENDPOINT || 'ws://127.0.0.1:9944',
    chainId: null
  }
};

/**
 * Smart Contract-Adressen
 */
const CONTRACTS = {
  // DOI NFT-Verträge
  DOI_NFT: {
    MAINNET: process.env.DOI_NFT_CONTRACT_MAINNET || '5G1234567890123456789012345678901234567890',
    TESTNET: process.env.DOI_NFT_CONTRACT_TESTNET || '5G1234567890123456789012345678901234567890',
    LOCAL: process.env.DOI_NFT_CONTRACT_LOCAL || '5G1234567890123456789012345678901234567890'
  },
  // Patentlizenz-Verträge
  PATENT_LICENSE: {
    MAINNET: process.env.PATENT_LICENSE_CONTRACT_MAINNET || '5G1234567890123456789012345678901234567890',
    TESTNET: process.env.PATENT_LICENSE_CONTRACT_TESTNET || '5G1234567890123456789012345678901234567890',
    LOCAL: process.env.PATENT_LICENSE_CONTRACT_LOCAL || '5G1234567890123456789012345678901234567890'
  }
};

/**
 * Gas- und Transaktionsbezogene Konfigurationen
 */
const TRANSACTION = {
  // Standardwerte für Gas und Transaktionen
  DEFAULT_GAS_LIMIT: process.env.DEFAULT_GAS_LIMIT || 1000000,
  DEFAULT_GAS_PRICE: process.env.DEFAULT_GAS_PRICE || **********,
  // Signaturoptionen
  DEFAULT_ERA: process.env.DEFAULT_ERA || 64,
  // Wiederholungsversuche und Timeouts
  MAX_RETRIES: 3,
  RETRY_DELAY: 2000, // ms
  TRANSACTION_TIMEOUT: 120000, // ms
  // Batch-Parameter
  BATCH_SIZE: 10
};

/**
 * NFT-bezogene Konfigurationen
 */
const NFT = {
  // NFT-Standards
  STANDARDS: {
    PSP34: 'psp34',
    RMRK: 'rmrk'
  },
  // Metadatenformate
  METADATA_SCHEMA: {
    NAME: 'name',
    DESCRIPTION: 'description',
    IMAGE: 'image',
    PROPERTIES: 'properties'
  },
  // DOI-spezifische NFT-Eigenschaften
  DOI_PROPERTY_KEYS: {
    DOI: 'doi',
    ORCID: 'orcid',
    PUBLICATION_DATE: 'publicationDate',
    AUTHORS: 'authors',
    JOURNAL: 'journal',
    IPFS_HASH: 'ipfsHash'
  }
};

/**
 * Dienst-bezogene Konfigurationen
 */
const SERVICES = {
  // IPFS-Konfiguration
  IPFS: {
    GATEWAY: process.env.IPFS_GATEWAY || 'https://ipfs.io/ipfs/',
    API: process.env.IPFS_API || 'https://ipfs.infura.io:5001',
    TIMEOUT: 30000 // ms
  },
  // BitTorrent-Konfiguration
  BITTORRENT: {
    TRACKERS: [
      'udp://tracker.opentrackr.org:1337/announce',
      'udp://tracker.leechers-paradise.org:6969/announce',
      'udp://tracker.coppersurfer.tk:6969/announce'
    ],
    SEED_DURATION: 86400000, // 24 Stunden in ms
    DOWNLOAD_LOCATION: process.env.TORRENT_DOWNLOAD_LOCATION || './downloads'
  }
};

module.exports = {
  NETWORKS,
  CONTRACTS,
  TRANSACTION,
  NFT,
  SERVICES,
  // Helfer-Methode zur Netzwerkauswahl
  getNetwork: (network = 'POLKADOT') => NETWORKS[network] || NETWORKS.POLKADOT,
  // Helfer-Methode zur Vertragsauswahl
  getContract: (contract, environment = 'MAINNET') => {
    const contractConfig = CONTRACTS[contract];
    return contractConfig ? contractConfig[environment] : null;
  }
}; 