/**
 * @fileoverview Zentralisierter Metadaten-Service
 * 
 * Dieser Service konsolidiert alle Funktionen zur Metadatenvalidierung
 * und -formatierung, die zuvor über verschiedene Klassen verteilt waren.
 */

const LoggerFactory = require('./LoggerFactory');
const logger = LoggerFactory.createLogger('metadata-service');

/**
 * Service für Metadatenvalidierung und -formatierung
 */
class MetadataService {
  /**
   * Erstellt eine neue MetadataService-Instanz
   * @param {Object} options - Konfigurationsoptionen
   */
  constructor(options = {}) {
    // Standardregeln für DOI-Metadaten
    this.doiRules = options.doiRules || {
      required: ['title'],
      recommended: ['authors', 'publicationDate', 'abstract'],
      dateFormat: /^\d{4}-\d{2}-\d{2}(T\d{2}:\d{2}:\d{2}(\.\d{3})?Z)?$/
    };
    
    // Standardregeln für NFT-Metadaten
    this.nftRules = options.nftRules || {
      required: ['name'],
      recommended: ['description', 'image'],
      maxNameLength: 100,
      maxDescriptionLength: 5000
    };
    
    // Standardregeln für Datensatz-Metadaten
    this.datasetRules = options.datasetRules || {
      required: ['name', 'description'],
      recommended: ['authors', 'license', 'version'],
      versionFormat: /^\d+\.\d+\.\d+$/
    };
  }
  
  /**
   * Validiert DOI-Metadaten
   * @param {Object} metadata - Die zu validierenden Metadaten
   * @param {Object} options - Validierungsoptionen
   * @returns {Object} Validierungsergebnis
   */
  validateDoiMetadata(metadata, options = {}) {
    const rules = options.rules || this.doiRules;
    const strictMode = options.strict !== undefined ? options.strict : false;
    
    const validationResult = {
      valid: true,
      missing: [],
      recommended: [],
      errors: [],
      warnings: []
    };
    
    // Pflichtfelder prüfen
    rules.required.forEach(field => {
      if (!metadata[field]) {
        validationResult.valid = false;
        validationResult.missing.push(field);
        validationResult.errors.push(`Pflichtfeld fehlt: ${field}`);
      }
    });
    
    // Empfohlene Felder prüfen
    rules.recommended.forEach(field => {
      if (!metadata[field]) {
        validationResult.recommended.push(field);
        validationResult.warnings.push(`Empfohlenes Feld fehlt: ${field}`);
      }
    });
    
    // Datumsformat prüfen
    if (metadata.publicationDate && !rules.dateFormat.test(metadata.publicationDate)) {
      validationResult.warnings.push(
        `Ungültiges Datumsformat: ${metadata.publicationDate}. Erwartet: YYYY-MM-DD oder YYYY-MM-DDThh:mm:ss.mmmZ`
      );
    }
    
    // Autorenliste prüfen
    if (metadata.authors && Array.isArray(metadata.authors)) {
      metadata.authors.forEach((author, index) => {
        if (!author.name) {
          validationResult.warnings.push(`Autor an Position ${index} hat keinen Namen`);
        }
      });
    }
    
    logger.debug('DOI-Metadaten validiert', {
      valid: validationResult.valid,
      missing: validationResult.missing,
      recommended: validationResult.recommended
    });
    
    // Im strengen Modus zu Fehlern
    if (strictMode && validationResult.warnings.length > 0) {
      validationResult.valid = false;
      validationResult.errors = [...validationResult.errors, ...validationResult.warnings];
    }
    
    return validationResult;
  }
  
  /**
   * Validiert NFT-Metadaten
   * @param {Object} metadata - Die zu validierenden Metadaten
   * @param {Object} options - Validierungsoptionen
   * @returns {Object} Validierungsergebnis
   */
  validateNftMetadata(metadata, options = {}) {
    const rules = options.rules || this.nftRules;
    const strictMode = options.strict !== undefined ? options.strict : false;
    
    const validationResult = {
      valid: true,
      missing: [],
      recommended: [],
      errors: [],
      warnings: []
    };
    
    // Pflichtfelder prüfen
    rules.required.forEach(field => {
      if (!metadata[field]) {
        validationResult.valid = false;
        validationResult.missing.push(field);
        validationResult.errors.push(`Pflichtfeld fehlt: ${field}`);
      }
    });
    
    // Empfohlene Felder prüfen
    rules.recommended.forEach(field => {
      if (!metadata[field]) {
        validationResult.recommended.push(field);
        validationResult.warnings.push(`Empfohlenes Feld fehlt: ${field}`);
      }
    });
    
    // Längen überprüfen
    if (metadata.name && metadata.name.length > rules.maxNameLength) {
      validationResult.warnings.push(
        `Name zu lang: ${metadata.name.length} Zeichen. Maximum: ${rules.maxNameLength}`
      );
    }
    
    if (metadata.description && metadata.description.length > rules.maxDescriptionLength) {
      validationResult.warnings.push(
        `Beschreibung zu lang: ${metadata.description.length} Zeichen. Maximum: ${rules.maxDescriptionLength}`
      );
    }
    
    // Properties validieren, falls vorhanden
    if (metadata.properties && typeof metadata.properties === 'object') {
      // Spezielle Validierung für NFT-Properties kann hier hinzugefügt werden
    }
    
    logger.debug('NFT-Metadaten validiert', {
      valid: validationResult.valid,
      missing: validationResult.missing,
      recommended: validationResult.recommended
    });
    
    // Im strengen Modus zu Fehlern
    if (strictMode && validationResult.warnings.length > 0) {
      validationResult.valid = false;
      validationResult.errors = [...validationResult.errors, ...validationResult.warnings];
    }
    
    return validationResult;
  }
  
  /**
   * Validiert Datensatz-Metadaten
   * @param {Object} metadata - Die zu validierenden Metadaten
   * @param {Object} options - Validierungsoptionen
   * @returns {Object} Validierungsergebnis
   */
  validateDatasetMetadata(metadata, options = {}) {
    const rules = options.rules || this.datasetRules;
    const strictMode = options.strict !== undefined ? options.strict : false;
    
    const validationResult = {
      valid: true,
      missing: [],
      recommended: [],
      errors: [],
      warnings: []
    };
    
    // Pflichtfelder prüfen
    rules.required.forEach(field => {
      if (!metadata[field]) {
        validationResult.valid = false;
        validationResult.missing.push(field);
        validationResult.errors.push(`Pflichtfeld fehlt: ${field}`);
      }
    });
    
    // Empfohlene Felder prüfen
    rules.recommended.forEach(field => {
      if (!metadata[field]) {
        validationResult.recommended.push(field);
        validationResult.warnings.push(`Empfohlenes Feld fehlt: ${field}`);
      }
    });
    
    // Versionformat prüfen
    if (metadata.version && !rules.versionFormat.test(metadata.version)) {
      validationResult.warnings.push(
        `Ungültiges Versionsformat: ${metadata.version}. Erwartet: x.y.z (z.B. 1.0.0)`
      );
    }
    
    logger.debug('Datensatz-Metadaten validiert', {
      valid: validationResult.valid,
      missing: validationResult.missing,
      recommended: validationResult.recommended
    });
    
    // Im strengen Modus zu Fehlern
    if (strictMode && validationResult.warnings.length > 0) {
      validationResult.valid = false;
      validationResult.errors = [...validationResult.errors, ...validationResult.warnings];
    }
    
    return validationResult;
  }
  
  /**
   * Formatiert DOI-Metadaten in NFT-Metadaten
   * @param {Object} doiMetadata - Die DOI-Metadaten
   * @param {Object} options - Formatierungsoptionen
   * @returns {Object} NFT-konforme Metadaten
   */
  formatDoiToNftMetadata(doiMetadata, options = {}) {
    logger.debug('Formatiere DOI-Metadaten in NFT-Format', { doi: options.doi });
    
    // Grundlegende NFT-Metadaten erstellen
    const nftMetadata = {
      name: doiMetadata.title || `DOI: ${options.doi || 'Unbekannt'}`,
      description: doiMetadata.abstract || doiMetadata.description || 
                   `Wissenschaftliche Publikation mit DOI: ${options.doi || 'Unbekannt'}`,
      image: doiMetadata.image || options.defaultImage,
      
      // Erweiterte Attribute für die Blockchain
      properties: {
        doi: options.doi,
        publicationDate: doiMetadata.publicationDate || new Date().toISOString(),
        license: doiMetadata.license || doiMetadata.properties?.license || 'Unbekannt',
        
        // Zusätzliche Metadaten
        ...this._formatAdditionalMetadata(doiMetadata)
      }
    };
    
    // Autoren hinzufügen, falls vorhanden
    if (doiMetadata.authors && Array.isArray(doiMetadata.authors)) {
      nftMetadata.properties.authors = doiMetadata.authors.map(author => {
        // Autoreninformationen normalisieren
        const formattedAuthor = {
          name: author.name || (author.given && author.family ? 
                 `${author.given} ${author.family}` : 'Unbekannter Autor')
        };
        
        // Zusätzliche Felder hinzufügen, falls vorhanden
        if (author.orcid) formattedAuthor.orcid = author.orcid;
        if (author.affiliation) formattedAuthor.affiliation = author.affiliation;
        
        return formattedAuthor;
      });
    }
    
    // Journal/Publikation hinzufügen, falls vorhanden
    if (doiMetadata.journal || doiMetadata.publisher) {
      nftMetadata.properties.journal = doiMetadata.journal || doiMetadata.publisher;
    }
    
    // Keywords/Topics hinzufügen, falls vorhanden
    if (doiMetadata.keywords && Array.isArray(doiMetadata.keywords)) {
      nftMetadata.properties.keywords = doiMetadata.keywords;
    }
    
    // Externe Links
    nftMetadata.external_url = doiMetadata.url || 
                              `https://doi.org/${options.doi}`;
    
    logger.debug('DOI-Metadaten in NFT-Format formatiert', { name: nftMetadata.name });
    
    return nftMetadata;
  }
  
  /**
   * Formatiert Datensatz-Metadaten in NFT-Metadaten
   * @param {Object} datasetMetadata - Die Datensatz-Metadaten
   * @param {Object} options - Formatierungsoptionen
   * @returns {Object} NFT-konforme Metadaten
   */
  formatDatasetToNftMetadata(datasetMetadata, options = {}) {
    logger.debug('Formatiere Datensatz-Metadaten in NFT-Format');
    
    // Grundlegende NFT-Metadaten erstellen
    const nftMetadata = {
      name: datasetMetadata.name || `Datensatz: ${options.doi || 'Unbekannt'}`,
      description: datasetMetadata.description || 
                  `Wissenschaftlicher Datensatz${options.doi ? ` mit DOI: ${options.doi}` : ''}`,
      image: datasetMetadata.image || options.defaultImage,
      
      // Erweiterte Attribute für die Blockchain
      properties: {
        type: 'dataset',
        version: datasetMetadata.version || '1.0.0',
        license: datasetMetadata.license || 'Unbekannt',
        created: datasetMetadata.created || datasetMetadata.publicationDate || new Date().toISOString(),
        
        // DOI hinzufügen, falls vorhanden
        ...(options.doi && { doi: options.doi }),
        
        // Relationen zu Publikationen, falls vorhanden
        ...(datasetMetadata.relatedPublications && { 
          relatedPublications: datasetMetadata.relatedPublications 
        }),
        
        // Zusätzliche Metadaten
        ...this._formatAdditionalMetadata(datasetMetadata)
      }
    };
    
    // Autoren hinzufügen, falls vorhanden
    if (datasetMetadata.authors && Array.isArray(datasetMetadata.authors)) {
      nftMetadata.properties.authors = this._formatAuthorsList(datasetMetadata.authors);
    }
    
    // Keywords hinzufügen, falls vorhanden
    if (datasetMetadata.keywords && Array.isArray(datasetMetadata.keywords)) {
      nftMetadata.properties.keywords = datasetMetadata.keywords;
    }
    
    logger.debug('Datensatz-Metadaten in NFT-Format formatiert', { name: nftMetadata.name });
    
    return nftMetadata;
  }
  
  /**
   * Formatiert eine Liste von Autoren in ein standardisiertes Format
   * @param {Array} authors - Die Autorenliste
   * @returns {Array} Formatierte Autorenliste
   * @private
   */
  _formatAuthorsList(authors) {
    return authors.map(author => {
      // Falls author ein String ist, in Objekt umwandeln
      if (typeof author === 'string') {
        return { name: author };
      }
      
      // Autoreninformationen normalisieren
      const formattedAuthor = {
        name: author.name || (author.given && author.family ? 
               `${author.given} ${author.family}` : 'Unbekannter Autor')
      };
      
      // Zusätzliche Felder hinzufügen, falls vorhanden
      if (author.orcid) formattedAuthor.orcid = author.orcid;
      if (author.affiliation) formattedAuthor.affiliation = author.affiliation;
      
      return formattedAuthor;
    });
  }
  
  /**
   * Formatiert zusätzliche Metadaten für die Aufnahme in Properties
   * @param {Object} metadata - Die ursprünglichen Metadaten
   * @returns {Object} Formatierte zusätzliche Metadaten
   * @private
   */
  _formatAdditionalMetadata(metadata) {
    const additionalProps = {};
    
    // Bestimmte Felder von der direkten Aufnahme ausschließen
    const excludedKeys = [
      'title', 'name', 'description', 'abstract', 'authors', 
      'publicationDate', 'journal', 'publisher', 'keywords', 
      'image', 'properties', 'url', 'license'
    ];
    
    // Zusätzliche Metadaten hinzufügen, die nicht explizit behandelt werden
    Object.keys(metadata).forEach(key => {
      if (!excludedKeys.includes(key) && metadata[key] !== undefined && metadata[key] !== null) {
        additionalProps[key] = metadata[key];
      }
    });
    
    // Falls es vorhandene Properties gibt, diese hinzufügen (ohne Überschreibung)
    if (metadata.properties && typeof metadata.properties === 'object') {
      Object.keys(metadata.properties).forEach(key => {
        // Nur hinzufügen, wenn noch nicht vorhanden
        if (additionalProps[key] === undefined) {
          additionalProps[key] = metadata.properties[key];
        }
      });
    }
    
    return additionalProps;
  }
  
  /**
   * Extrahiert und formatiert Metadaten aus einer DOI-Antwort
   * @param {Object} response - Die Antwort von CrossRef, DataCite oder ähnlichen Diensten
   * @param {Object} options - Formatierungsoptionen
   * @returns {Object} Formatierte Metadaten
   */
  extractDoiResponseMetadata(response, options = {}) {
    if (!response) {
      logger.warn('Keine DOI-Antwort zum Extrahieren der Metadaten vorhanden');
      return {};
    }
    
    // Die relevanten Daten aus der Antwort extrahieren (abhängig vom Format)
    const data = response.message || response.data?.attributes || response.data || response;
    
    logger.debug('Extrahiere Metadaten aus DOI-Antwort');
    
    const metadata = {
      title: this._extractTitle(data),
      publicationDate: this._extractPublicationDate(data),
      authors: this._extractAuthors(data),
      abstract: data.abstract,
      publisher: data.publisher,
      journal: data['container-title'] || data.container || data.journal,
      url: data.url || data.resource?.primary?.URL,
      license: this._extractLicense(data)
    };
    
    // Referenzen/Zitationen extrahieren, falls vorhanden
    if (data.reference || data.references) {
      metadata.citations = this._extractCitations(data.reference || data.references);
    }
    
    // Keywords/Subjects extrahieren, falls vorhanden
    if (data.subject || data.subjects || data.keywords) {
      metadata.keywords = data.subject || data.subjects || data.keywords || [];
    }
    
    // Alle undefined-Werte entfernen
    Object.keys(metadata).forEach(key => {
      if (metadata[key] === undefined) {
        delete metadata[key];
      }
    });
    
    logger.debug('Metadaten aus DOI-Antwort extrahiert', { title: metadata.title });
    
    return metadata;
  }
  
  /**
   * Extrahiert den Titel aus DOI-Metadaten
   * @param {Object} data - Die DOI-Daten
   * @returns {string} Der extrahierte Titel
   * @private
   */
  _extractTitle(data) {
    // Bei Arrays den ersten Eintrag nehmen oder Stringifizieren
    if (data.title) {
      return Array.isArray(data.title) ? data.title[0] : data.title;
    }
    
    // Alternative Titelquellen
    if (data.titles && Array.isArray(data.titles) && data.titles.length > 0) {
      return data.titles[0].title;
    }
    
    return undefined;
  }
  
  /**
   * Extrahiert das Publikationsdatum aus DOI-Metadaten
   * @param {Object} data - Die DOI-Daten
   * @returns {string} Das extrahierte Publikationsdatum
   * @private
   */
  _extractPublicationDate(data) {
    // Mögliche Datumsfelder in Prioritätsreihenfolge
    const dateFields = [
      // Direkte Datumsangaben
      'published', 'published-online', 'published-print',
      
      // DataCite-Format
      'created',
      
      // CrossRef-Format mit Teilen
      'issued', 'published-online', 'published-print'
    ];
    
    // Durch die möglichen Felder iterieren
    for (const field of dateFields) {
      const dateData = data[field];
      
      if (!dateData) continue;
      
      // ISO-String direkt verwenden
      if (typeof dateData === 'string') {
        return dateData;
      }
      
      // DataCite-Format mit date-time
      if (dateData['date-time']) {
        return dateData['date-time'];
      }
      
      // CrossRef-Format mit date-parts
      if (dateData['date-parts'] && Array.isArray(dateData['date-parts']) && 
          dateData['date-parts'][0] && Array.isArray(dateData['date-parts'][0])) {
        
        const parts = dateData['date-parts'][0];
        
        // Jahr, Monat, Tag
        if (parts.length >= 3) {
          return `${parts[0]}-${parts[1].toString().padStart(2, '0')}-${parts[2].toString().padStart(2, '0')}`;
        }
        
        // Jahr, Monat
        if (parts.length >= 2) {
          return `${parts[0]}-${parts[1].toString().padStart(2, '0')}-01`;
        }
        
        // Nur Jahr
        if (parts.length >= 1) {
          return `${parts[0]}-01-01`;
        }
      }
    }
    
    return undefined;
  }
  
  /**
   * Extrahiert Autoren aus DOI-Metadaten
   * @param {Object} data - Die DOI-Daten
   * @returns {Array} Die extrahierten Autoren
   * @private
   */
  _extractAuthors(data) {
    const authorData = data.author || data.authors;
    
    if (!authorData || !Array.isArray(authorData)) {
      return undefined;
    }
    
    return authorData.map(author => {
      // CrossRef-Format
      if (author.given && author.family) {
        return {
          name: `${author.given} ${author.family}`,
          orcid: author.ORCID,
          affiliation: author.affiliation ? 
                      (Array.isArray(author.affiliation) ? 
                        author.affiliation[0]?.name : 
                        author.affiliation) : 
                      undefined
        };
      }
      
      // DataCite-Format oder andere
      return {
        name: author.name || author.fullName || 'Unbekannter Autor',
        orcid: author.ORCID || author.orcid,
        affiliation: author.affiliation
      };
    });
  }
  
  /**
   * Extrahiert Lizenzinformationen aus DOI-Metadaten
   * @param {Object} data - Die DOI-Daten
   * @returns {string} Die extrahierte Lizenz
   * @private
   */
  _extractLicense(data) {
    // CrossRef-Format
    if (data.license && Array.isArray(data.license) && data.license.length > 0) {
      const license = data.license[0];
      return license.URL || license.url || license.content;
    }
    
    // DataCite-Format
    if (data.rightsList && Array.isArray(data.rightsList) && data.rightsList.length > 0) {
      return data.rightsList[0].rightsUri || data.rightsList[0].rights;
    }
    
    return undefined;
  }
  
  /**
   * Extrahiert Zitationen aus DOI-Metadaten
   * @param {Array} references - Die Referenzdaten
   * @returns {Array} Die extrahierten Zitationen
   * @private
   */
  _extractCitations(references) {
    if (!Array.isArray(references)) {
      return [];
    }
    
    return references.map(ref => ({
      doi: ref.DOI || ref.doi,
      title: ref.title || ref['article-title'],
      authors: ref.author,
      year: ref.year
    })).filter(ref => ref.doi || ref.title); // Nur Referenzen mit DOI oder Titel behalten
  }
}

module.exports = MetadataService; 