/**
 * @fileoverview Zentrale Konfiguration für das Handle-System in DeSci-Scholar
 * 
 * Diese Datei definiert alle relevanten Endpunkte und Einstellungen für die Interaktion
 * mit dem Handle-System (DOI, HDL) nach den offiziellen Standards.
 * 
 * Referenzen:
 * - Handle.net: https://www.handle.net/
 * - Hdl Resolver: http://hdl.handle.net
 * - Handle Proxy: https://www.handle.net/proxy_servlet.html
 */

/**
 * Hauptkonfiguration für das Handle-System
 */
const HandleConfig = {
  /**
   * Öffentliche Endpunkte
   */
  endpoints: {
    // Hauptresolver für Handle-Auflösung
    resolver: 'https://hdl.handle.net',
    
    // Handle.net API Endpunkte
    api: {
      proxy: 'https://hdl.handle.net/api/handles',
      native: 'https://handle.net/api/handles',
      admin: 'https://handle.net/api/handles/admin'
    },
    
    // Handle Proxy Servlet
    proxyServlet: 'https://www.handle.net/proxy_servlet.html',
    
    // DOI-spezifische Endpunkte
    doi: {
      resolver: 'https://doi.org',
      crossref: 'https://api.crossref.org/works',
      datacite: 'https://api.datacite.org/dois',
      ror: {
        resolver: 'https://ror.org',
        api: 'https://api.ror.org/v2',
        search: 'https://api.ror.org/v2/organizations'
      }
    }
  },
  
  /**
   * Handle-System Konfigurationsparameter
   */
  params: {
    // Time-to-live für Handle-Anfragen in Sekunden
    defaultTtl: 86400,
    
    // Standardmäßig keine Authentifizierung verwenden
    defaultAuth: false,
    
    // Maximale Batch-Größe für Handle-Operationen
    batchSize: 20,
    
    // Verzögerung zwischen Batch-Anfragen in Millisekunden
    batchDelay: 1000,
    
    // Timeout für Handle-Anfragen in Millisekunden
    requestTimeout: 30000,
    
    // Maximale Anzahl von Wiederholungsversuchen bei Fehlern
    maxRetries: 3
  },
  
  /**
   * URI-Präfixe und Formate
   */
  uriFormats: {
    handle: 'hdl:',
    doi: 'doi:',
    prefixPattern: /^(10\.\d{4,})\/.+$/,
    doiPattern: /^(doi:|https?:\/\/(dx\.)?doi\.org\/)?(.+)$/i
  },
  
  /**
   * Handle-Typen und Indexwerte
   */
  types: {
    URL: 1,
    EMAIL: 2,
    SITE: 3,
    DESC: 4,
    ADMIN: 100,
    METADATA: 200,
    DOI_METADATA: 300,
    SEMANTIC_METADATA: 401,
    ROR_ID: 400,
    ROR_METADATA: 401,
    CITATION: 500
  },
  
  /**
   * HTTP-Statuscodes und ihre Bedeutung im Handle-System
   */
  statusCodes: {
    200: 'OK - Anfrage erfolgreich',
    201: 'Created - Handle erfolgreich erstellt',
    204: 'No Content - Änderung erfolgreich',
    400: 'Bad Request - Ungültige Anfrage',
    401: 'Unauthorized - Authentifizierung erforderlich',
    403: 'Forbidden - Keine Berechtigung',
    404: 'Not Found - Handle nicht gefunden',
    409: 'Conflict - Handle existiert bereits',
    500: 'Internal Server Error - Serverfehler',
    503: 'Service Unavailable - Dienst nicht verfügbar',
    504: 'Gateway Timeout - Zeitüberschreitung'
  },
  
  /**
   * Standardeinstellungen für Metadaten-Validierung
   */
  metadataRules: {
    required: ['title', 'authors', 'publicationDate'],
    recommended: ['abstract', 'doi', 'journal', 'keywords'],
    optional: ['citations', 'license', 'funding', 'acknowledgements', 'rorId', 'organizationName']
  }
};

export default HandleConfig;
