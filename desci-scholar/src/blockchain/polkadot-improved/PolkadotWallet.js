/**
 * @fileoverview Wallet und Payment Management für die Polkadot-Integration
 * 
 * Diese Klasse bietet umfassende Wallet-Funktionen für die Polkadot-Blockchain,
 * einschließlich Schlüsselverwaltung, Guthaben-Abfragen und Zahlungsabwicklung.
 */

import { Keyring } from '@polkadot/keyring';
import { mnemonicGenerate, mnemonicValidate } from '@polkadot/util-crypto';
import winston from 'winston';

// Logger konfigurieren
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: { service: 'polkadot-wallet' },
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    }),
    new winston.transports.File({ 
      filename: 'logs/wallet-error.log', 
      level: 'error' 
    }),
    new winston.transports.File({ 
      filename: 'logs/wallet-combined.log' 
    })
  ]
});

/**
 * PolkadotWallet-Klasse für Wallet-Management und Zahlungsabwicklung
 */
class PolkadotWallet {
  /**
   * Erstellt eine neue Instanz des PolkadotWallet
   * @param {Object} polkadotClient - Eine Instanz von PolkadotClient
   */
  constructor(polkadotClient) {
    this.client = polkadotClient;
    this.keyring = null;
    this.accounts = new Map(); // Speichert aktive Accounts für schnellen Zugriff
    this.defaultAccount = null;
  }
  
  /**
   * Initialisiert die Wallet
   * @returns {Promise<void>}
   */
  async init() {
    await this.client.init();
    
    if (!this.keyring) {
      this.keyring = new Keyring({ type: 'sr25519' });
      logger.info('Polkadot Wallet initialisiert');
    }
  }
  
  /**
   * Erstellt einen neuen Account mit zufälligem Mnemonik
   * @returns {Object} Das neu erstellte Account-Objekt mit Mnemonik
   */
  createAccount() {
    if (!this.keyring) {
      throw new Error('Wallet nicht initialisiert. Bitte zuerst init() aufrufen.');
    }
    
    try {
      // Neuen Mnemonik generieren
      const mnemonic = mnemonicGenerate();
      
      // Keypair aus Mnemonik erstellen
      const account = this.keyring.addFromMnemonic(mnemonic);
      
      // Account in der Map speichern
      this.accounts.set(account.address, { account, mnemonic });
      
      // Falls noch kein Standard-Account existiert, diesen setzen
      if (!this.defaultAccount) {
        this.defaultAccount = account.address;
      }
      
      logger.info(`Neuer Account erstellt: ${account.address}`);
      
      // Account-Objekt mit Mnemonik zurückgeben
      return {
        address: account.address,
        mnemonic,
        publicKey: account.publicKey,
        type: account.type
      };
    } catch (error) {
      logger.error(`Fehler bei Account-Erstellung: ${error.message}`, { error });
      throw new Error(`Account-Erstellung fehlgeschlagen: ${error.message}`);
    }
  }
  
  /**
   * Importiert einen Account aus einem Mnemonik oder privaten Schlüssel
   * @param {string} seedOrMnemonic - Mnemonik oder privater Schlüssel
   * @param {string} name - Optionaler Name für den Account
   * @returns {Object} Das importierte Account-Objekt
   */
  importAccount(seedOrMnemonic, name = '') {
    if (!this.keyring) {
      throw new Error('Wallet nicht initialisiert. Bitte zuerst init() aufrufen.');
    }
    
    try {
      let account;
      let mnemonic = null;
      
      // Prüfen, ob es sich um einen Mnemonik handelt
      if (mnemonicValidate(seedOrMnemonic)) {
        account = this.keyring.addFromMnemonic(seedOrMnemonic);
        mnemonic = seedOrMnemonic;
      } else {
        // Sonst als Seed/Private Key behandeln
        account = this.keyring.addFromUri(seedOrMnemonic);
      }
      
      // Account in der Map speichern
      this.accounts.set(account.address, { 
        account, 
        mnemonic,
        name: name || `Account-${this.accounts.size + 1}`
      });
      
      // Falls noch kein Standard-Account existiert, diesen setzen
      if (!this.defaultAccount) {
        this.defaultAccount = account.address;
      }
      
      logger.info(`Account importiert: ${account.address} ${name ? `(${name})` : ''}`);
      
      return {
        address: account.address,
        name: name || `Account-${this.accounts.size}`,
        publicKey: account.publicKey,
        type: account.type,
        hasMnemonic: !!mnemonic
      };
    } catch (error) {
      logger.error(`Fehler beim Import des Accounts: ${error.message}`, { error });
      throw new Error(`Account-Import fehlgeschlagen: ${error.message}`);
    }
  }
  
  /**
   * Ruft einen Account aus der Wallet ab
   * @param {string} address - Die Adresse des Accounts
   * @returns {Object} Das Account-Objekt
   */
  getAccount(address) {
    // Wenn keine Adresse angegeben wurde, den Standard-Account verwenden
    const targetAddress = address || this.defaultAccount;
    
    if (!targetAddress) {
      throw new Error('Keine Adresse angegeben und kein Standard-Account vorhanden');
    }
    
    const accountEntry = this.accounts.get(targetAddress);
    
    if (!accountEntry) {
      throw new Error(`Account mit Adresse ${targetAddress} nicht in der Wallet gefunden`);
    }
    
    return accountEntry.account;
  }
  
  /**
   * Setzt den Standard-Account
   * @param {string} address - Die Adresse des neuen Standard-Accounts
   */
  setDefaultAccount(address) {
    if (!this.accounts.has(address)) {
      throw new Error(`Account mit Adresse ${address} nicht in der Wallet gefunden`);
    }
    
    this.defaultAccount = address;
    logger.info(`Standard-Account gesetzt: ${address}`);
  }
  
  /**
   * Gibt eine Liste aller verwalteten Accounts zurück
   * @returns {Array<Object>} Eine Liste der Account-Informationen
   */
  listAccounts() {
    const accountList = [];
    
    for (const [address, entry] of this.accounts.entries()) {
      accountList.push({
        address,
        name: entry.name || '',
        isDefault: address === this.defaultAccount,
        hasMnemonic: !!entry.mnemonic,
        type: entry.account.type
      });
    }
    
    return accountList;
  }
  
  /**
   * Ruft das Guthaben eines Accounts ab
   * @param {string} address - Die Adresse des Accounts (optional, Standard-Account wird verwendet wenn nicht angegeben)
   * @returns {Promise<Object>} Das Guthaben und zusätzliche Informationen
   */
  async getBalance(address) {
    await this.client.init();
    
    const targetAddress = address || this.defaultAccount;
    
    if (!targetAddress) {
      throw new Error('Keine Adresse angegeben und kein Standard-Account vorhanden');
    }
    
    try {
      logger.info(`Rufe Guthaben für ${targetAddress} ab`);
      
      // Abfragen aller Balances (free, reserved, misc locked)
      const { data: balance } = await this.client.api.query.system.account(targetAddress);
      
      // Chaininfo abrufen für Token-Details
      const chainInfo = await this.client.getChainInfo();
      
      // Formatieren der Ergebnisse
      const decimals = chainInfo.tokenDecimals;
      const divisor = BigInt(10) ** BigInt(decimals);
      
      const freeBalance = balance.free.toBigInt();
      const reservedBalance = balance.reserved.toBigInt();
      const totalBalance = freeBalance + reservedBalance;
      
      const formattedResults = {
        address: targetAddress,
        free: {
          raw: freeBalance.toString(),
          formatted: formatBalanceWithDecimals(freeBalance, decimals)
        },
        reserved: {
          raw: reservedBalance.toString(),
          formatted: formatBalanceWithDecimals(reservedBalance, decimals)
        },
        total: {
          raw: totalBalance.toString(),
          formatted: formatBalanceWithDecimals(totalBalance, decimals)
        },
        decimals,
        token: chainInfo.tokenSymbol
      };
      
      logger.debug('Guthaben abgerufen', formattedResults);
      
      return formattedResults;
    } catch (error) {
      logger.error(`Fehler beim Abrufen des Guthabens: ${error.message}`, { error, address: targetAddress });
      throw new Error(`Guthaben-Abruf fehlgeschlagen: ${error.message}`);
    }
  }
  
  /**
   * Führt eine Zahlung an eine andere Adresse durch
   * @param {string} toAddress - Die Empfängeradresse
   * @param {string|number|BigInt} amount - Der zu überweisende Betrag
   * @param {Object} options - Zusätzliche Optionen
   * @param {string} options.fromAddress - Die Absenderadresse (optional, Standard-Account wird verwendet wenn nicht angegeben)
   * @param {string} options.memo - Ein optionaler Memo/Referenz-Text für die Überweisung
   * @returns {Promise<Object>} Das Ergebnis der Transaktion
   */
  async sendPayment(toAddress, amount, options = {}) {
    await this.client.init();
    
    const fromAddress = options.fromAddress || this.defaultAccount;
    
    if (!fromAddress) {
      throw new Error('Keine Absenderadresse angegeben und kein Standard-Account vorhanden');
    }
    
    const account = this.getAccount(fromAddress);
    
    try {
      logger.info(`Bereite Zahlung von ${fromAddress} an ${toAddress} vor: ${amount}`);
      
      // Chaininfo abrufen für Token-Details
      const chainInfo = await this.client.getChainInfo();
      
      // Betrag in BigInt umwandeln, falls nötig
      let amountBigInt;
      
      if (typeof amount === 'string') {
        // Wenn der Betrag ein String mit Dezimalpunkt ist, konvertieren
        if (amount.includes('.')) {
          amountBigInt = convertDecimalToBigInt(amount, chainInfo.tokenDecimals);
        } else {
          amountBigInt = BigInt(amount);
        }
      } else if (typeof amount === 'number') {
        // Wenn der Betrag eine Zahl ist, konvertieren
        amountBigInt = convertDecimalToBigInt(amount.toString(), chainInfo.tokenDecimals);
      } else {
        // Ansonsten nehmen wir an, dass es bereits ein BigInt ist
        amountBigInt = BigInt(amount);
      }
      
      // Memo hinzufügen, falls vorhanden
      const memo = options.memo || '';
      
      // Transfer-Transaktion erstellen
      let tx;
      
      if (memo) {
        // Mit Memo, falls unterstützt
        if (this.client.api.tx.balances.transferWithMemo) {
          tx = this.client.api.tx.balances.transferWithMemo(toAddress, amountBigInt, memo);
        } else {
          logger.warn('transferWithMemo nicht unterstützt, verwende regulären Transfer');
          tx = this.client.api.tx.balances.transfer(toAddress, amountBigInt);
        }
      } else {
        // Standard-Transfer
        tx = this.client.api.tx.balances.transfer(toAddress, amountBigInt);
      }
      
      // Gas-Schätzung durchführen
      const gasEstimate = await this.client.estimateGas(tx, account);
      
      // Gebühren prüfen
      const fees = gasEstimate.partialFee.toBigInt();
      
      // Transaktion mit Wiederholungslogik senden
      const result = await this.client.sendTransactionWithRetry(tx, account, {
        nonce: options.nonce,
        tip: options.tip,
        ...options
      });
      
      logger.info(`Zahlung erfolgreich: ${fromAddress} -> ${toAddress}, Betrag: ${amount}, Gebühren: ${formatBalanceWithDecimals(fees, chainInfo.tokenDecimals)} ${chainInfo.tokenSymbol}`);
      
      return {
        ...result,
        paymentDetails: {
          from: fromAddress,
          to: toAddress,
          amount: {
            raw: amountBigInt.toString(),
            formatted: formatBalanceWithDecimals(amountBigInt, chainInfo.tokenDecimals)
          },
          fees: {
            raw: fees.toString(),
            formatted: formatBalanceWithDecimals(fees, chainInfo.tokenDecimals)
          },
          memo: memo || undefined,
          token: chainInfo.tokenSymbol
        }
      };
    } catch (error) {
      logger.error(`Fehler bei der Zahlung: ${error.message}`, { error, from: fromAddress, to: toAddress, amount });
      throw new Error(`Zahlung fehlgeschlagen: ${error.message}`);
    }
  }
  
  /**
   * Fügt DOI-Token zu einem Account für NFT-Operationen hinzu (für Testumgebungen)
   * @param {string} address - Die Zieladresse
   * @param {string|number} amount - Der Betrag in Token
   * @returns {Promise<Object>} Das Ergebnis der Transaktion
   */
  async fundAccountForOperations(address, amount = '1000') {
    // Diese Funktion funktioniert nur in Testumgebungen mit sudo-Zugriff
    if (this.client.network !== 'development') {
      throw new Error('Diese Funktion ist nur in Testumgebungen verfügbar');
    }
    
    await this.client.init();
    
    try {
      logger.info(`Fonds für ${address} bereitstellen: ${amount}`);
      
      // Sudo-Account abrufen (Alice in Testumgebungen)
      const sudoAccount = this.client.getAccount('//Alice');
      
      // Chaininfo abrufen für Token-Details
      const chainInfo = await this.client.getChainInfo();
      
      // Betrag umrechnen
      const amountBigInt = convertDecimalToBigInt(amount.toString(), chainInfo.tokenDecimals);
      
      // Transaktion mit sudo-Rechten erstellen
      const balanceSetTx = this.client.api.tx.balances.setBalance(address, amountBigInt, 0);
      const sudoTx = this.client.api.tx.sudo.sudo(balanceSetTx);
      
      // Transaktion senden
      const result = await this.client.sendTransactionWithRetry(sudoTx, sudoAccount);
      
      logger.info(`Fonds erfolgreich bereitgestellt: ${address}, Betrag: ${formatBalanceWithDecimals(amountBigInt, chainInfo.tokenDecimals)} ${chainInfo.tokenSymbol}`);
      
      return {
        ...result,
        fundingDetails: {
          address,
          amount: {
            raw: amountBigInt.toString(),
            formatted: formatBalanceWithDecimals(amountBigInt, chainInfo.tokenDecimals)
          },
          token: chainInfo.tokenSymbol
        }
      };
    } catch (error) {
      logger.error(`Fehler beim Bereitstellen von Fonds: ${error.message}`, { error, address, amount });
      throw new Error(`Fondsbereitstellung fehlgeschlagen: ${error.message}`);
    }
  }
  
  /**
   * Prüft die Zahlungsfähigkeit eines Accounts für eine bestimmte Operation
   * @param {string} address - Die zu prüfende Adresse
   * @param {string} operationType - Der Operationstyp (z.B. 'registerDoi', 'createLicense')
   * @returns {Promise<Object>} Das Prüfergebnis
   */
  async checkPaymentViability(address, operationType) {
    await this.client.init();
    
    try {
      logger.info(`Prüfe Zahlungsfähigkeit für ${address}, Operation: ${operationType}`);
      
      // Guthaben abrufen
      const balanceInfo = await this.getBalance(address);
      const freeBalance = BigInt(balanceInfo.free.raw);
      
      // Dummy-Transaktion für Gas-Schätzung erstellen
      let dummyTx;
      const dummyAccount = this.getAccount(address);
      
      switch (operationType) {
        case 'registerDoi':
          dummyTx = this.client.api.tx.desciScholar.registerDOI('10.1234/example', 'QmDummy');
          break;
        case 'createLicense':
          dummyTx = this.client.api.tx.desciScholar.createPatentLicense('US12345', '{}');
          break;
        case 'transfer':
          dummyTx = this.client.api.tx.balances.transfer(address, 1000);
          break;
        default:
          throw new Error(`Unbekannter Operationstyp: ${operationType}`);
      }
      
      // Gas schätzen
      const gasEstimate = await this.client.estimateGas(dummyTx, dummyAccount);
      const estimatedFees = gasEstimate.partialFee.toBigInt();
      
      // Existentielles Deposit (Minimum für einen aktiven Account)
      const existentialDeposit = await this.client.api.consts.balances.existentialDeposit.toBigInt();
      
      // Benötigtes Guthaben berechnen (Gebühren + ein kleiner Puffer)
      const buffer = estimatedFees / BigInt(5); // 20% Puffer
      const requiredBalance = estimatedFees + buffer;
      
      // Prüfen, ob genug Guthaben vorhanden ist
      const hasEnoughBalance = freeBalance > (requiredBalance + existentialDeposit);
      
      // Chaininfo abrufen für Token-Details
      const chainInfo = await this.client.getChainInfo();
      
      return {
        address,
        viable: hasEnoughBalance,
        operationType,
        balance: balanceInfo,
        requiredFees: {
          raw: estimatedFees.toString(),
          formatted: formatBalanceWithDecimals(estimatedFees, chainInfo.tokenDecimals)
        },
        existentialDeposit: {
          raw: existentialDeposit.toString(),
          formatted: formatBalanceWithDecimals(existentialDeposit, chainInfo.tokenDecimals)
        },
        requiredTotal: {
          raw: requiredBalance.toString(),
          formatted: formatBalanceWithDecimals(requiredBalance, chainInfo.tokenDecimals)
        },
        token: chainInfo.tokenSymbol
      };
    } catch (error) {
      logger.error(`Fehler bei der Zahlungsfähigkeitsprüfung: ${error.message}`, { error, address, operationType });
      throw new Error(`Zahlungsfähigkeitsprüfung fehlgeschlagen: ${error.message}`);
    }
  }
  
  /**
   * Erstellt einen QR-Code für eine Zahlungsadresse
   * @param {string} address - Die Adresse für den QR-Code
   * @param {Object} options - Optionen für den QR-Code
   * @returns {Promise<string>} Der QR-Code als Daten-URL
   */
  async generateAddressQrCode(address, options = {}) {
    // In einer realen Implementierung würde hier ein QR-Code generiert werden
    // Diese Implementierung ist nur ein Platzhalter
    return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8z8BQDwAEhQGAhKmMIQAAAABJRU5ErkJggg==';
  }
}

/**
 * Formatiert einen BigInt-Wert als Dezimalzahl mit der richtigen Anzahl von Nachkommastellen
 * @param {BigInt} value - Der zu formatierende Wert
 * @param {number} decimals - Die Anzahl der Dezimalstellen
 * @returns {string} Der formatierte Wert
 */
function formatBalanceWithDecimals(value, decimals) {
  const divisor = BigInt(10) ** BigInt(decimals);
  const integerPart = value / divisor;
  const fractionalPart = value % divisor;
  
  // Padding für führende Nullen hinzufügen
  const fractionalStr = fractionalPart.toString().padStart(decimals, '0');
  
  // Dezimalzahl formatieren
  return `${integerPart}.${fractionalStr}`;
}

/**
 * Konvertiert einen Dezimalwert in einen BigInt mit der richtigen Anzahl von Dezimalstellen
 * @param {string} decimalValue - Der Dezimalwert als String (z.B. "1.23")
 * @param {number} decimals - Die Anzahl der Dezimalstellen
 * @returns {BigInt} Der konvertierte Wert als BigInt
 */
function convertDecimalToBigInt(decimalValue, decimals) {
  const parts = decimalValue.split('.');
  const wholePart = parts[0];
  const fractionalPart = parts[1] || '';
  
  // Ganze Zahl mit Dezimalstellen multiplizieren
  let result = BigInt(wholePart) * (BigInt(10) ** BigInt(decimals));
  
  // Nachkommastellen hinzufügen, falls vorhanden
  if (fractionalPart) {
    // Auf die richtige Länge kürzen oder mit Nullen auffüllen
    const paddedFractional = fractionalPart.padEnd(decimals, '0').slice(0, decimals);
    result += BigInt(paddedFractional);
  }

  return result;
}

export default PolkadotWallet; 