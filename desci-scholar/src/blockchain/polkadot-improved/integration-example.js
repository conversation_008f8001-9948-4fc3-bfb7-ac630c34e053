/**
 * @fileoverview Beispiel für die vollständige Integration aller optimierten Komponenten
 * 
 * Dieses Beispiel demonstriert den Workflow einer wissenschaftlichen Publikation:
 * 1. DOI-Registrierung
 * 2. NFT-Erstellung auf Polkadot
 * 3. Speicherung von Metadaten in IPFS
 * 4. Verteilung der Publikation über BitTorrent
 */

const path = require('path');
const fs = require('fs');

// Importiere alle benötigten Komponenten aus dem optimierten Modul
const {
  createPolkadotConnection,
  DoiNftManager,
  ResearchDataManager,
  BlockchainConfig,
  LoggerFactory
} = require('./index');

// Logger für dieses Beispiel
const logger = LoggerFactory.createSimpleLogger('integration-example');

/**
 * Hauptfunktion, die den gesamten Prozess demonstriert
 */
async function runExample() {
  logger.info('Starte Integrations-Beispiel');
  
  try {
    // 1. Initialisiere Polkadot-Verbindung mit allen Services
    logger.info('Initialisiere Polkadot-Verbindung und Services...');
    const connection = createPolkadotConnection({
      // Verwende Westend-Testnetz für dieses Beispiel
      network: BlockchainConfig.NETWORKS.WESTEND.name,
      endpoint: BlockchainConfig.NETWORKS.WESTEND.endpoint,
      
      // IPFS-Optionen
      ipfsOptions: {
        gateway: BlockchainConfig.SERVICES.IPFS.GATEWAY
      },
      
      // Handle-Optionen
      handleOptions: {
        timeout: 10000
      },
      
      // HTTP-Optionen für alle Services
      httpOptions: {
        timeout: 15000,
        retries: 2
      }
    });
    
    // Services initialisieren
    await connection.initialize({
      initTorrentService: true, // TorrentService direkt mit initialisieren
      torrentOptions: {
        downloadLocation: path.join(process.cwd(), 'downloads')
      }
    });
    
    logger.info('Verbindung erfolgreich initialisiert');
    
    // 2. Erstelle Beispiel-Publikation
    const publication = createSamplePublication();
    logger.info(`Beispiel-Publikation erstellt: "${publication.title}"`);
    
    // Metadaten validieren
    const metadataService = connection.getMetadataService();
    const validationResult = metadataService.validateDoiMetadata(publication.metadata);
    
    if (validationResult.warnings.length > 0) {
      logger.warn(`Metadaten-Warnungen: ${validationResult.warnings.join(', ')}`);
    }
    
    // 3. Registriere DOI und erstelle NFT
    const doiManager = new DoiNftManager(connection);
    logger.info(`Registriere DOI: ${publication.doi}`);
    
    const registrationResult = await doiManager.registerDoiAsNft(
      publication.doi, 
      publication.metadata,
      { 
        transactionOptions: { waitForFinalization: true },
        strictValidation: false // Warnungen erlauben
      }
    );
    
    logger.info('DOI erfolgreich als NFT registriert:', registrationResult);
    
    // 4. Erstelle eine Beispiel-PDF-Datei
    const pdfPath = await createSamplePDF(publication);
    logger.info(`Beispiel-PDF erstellt: ${pdfPath}`);
    
    // 5. Verknüpfe DOI mit der PDF über BitTorrent
    logger.info('Verknüpfe DOI mit PDF über BitTorrent...');
    const linkResult = await doiManager.linkDoiToFile(
      publication.doi,
      pdfPath,
      {
        title: publication.title,
        comment: `Publikation: ${publication.title}`,
        createOptions: {
          private: false // Öffentlicher Torrent
        }
      }
    );
    
    logger.info('DOI erfolgreich mit Datei verknüpft:', {
      doi: linkResult.doi,
      magnetURI: linkResult.magnetURI
    });
    
    // 6. Erstelle einen Forschungsdatensatz
    const datasetDir = await createSampleDataset(publication);
    logger.info(`Beispiel-Datensatz erstellt: ${datasetDir}`);
    
    // 7. Veröffentliche den Datensatz
    const dataManager = new ResearchDataManager(connection);
    logger.info('Veröffentliche Forschungsdatensatz...');
    
    // Datensatz-Objekt erstellen
    const dataset = {
      name: `Datensatz für ${publication.title}`,
      description: `Rohdaten und Analyseskripte für die Publikation: ${publication.title}`,
      authors: publication.metadata.authors,
      license: 'CC-BY-4.0',
      relatedPublications: [publication.doi],
      version: '1.0.0',
      keywords: ['desci', 'polkadot', 'demo']
    };
    
    // Datensatz validieren
    const datasetValidation = metadataService.validateDatasetMetadata(dataset);
    
    if (datasetValidation.warnings.length > 0) {
      logger.warn(`Datensatz-Metadaten-Warnungen: ${datasetValidation.warnings.join(', ')}`);
    }
    
    const datasetResult = await dataManager.publishDataset(
      dataset,
      datasetDir,
      { 
        ipfsOptions: { pin: true },
        transactionOptions: { waitForFinalization: true }
      }
    );
    
    logger.info('Datensatz erfolgreich veröffentlicht:', {
      name: datasetResult.name,
      ipfsHash: datasetResult.ipfsHash,
      magnetURI: datasetResult.magnetURI
    });
    
    // 8. Forschungsdaten herunterladen (Demonstration)
    logger.info('Lade Forschungsdaten herunter (Demonstration)...');
    const downloadResult = await doiManager.downloadPublication(publication.doi);
    
    logger.info('Download abgeschlossen:', {
      files: downloadResult.files.map(f => f.name),
      path: downloadResult.path
    });
    
    // Statistiken anzeigen
    const torrentService = connection.getTorrentService();
    const stats = torrentService.getStats();
    
    logger.info('BitTorrent-Statistiken:', {
      activeTorrents: stats.activeTorrents,
      downloadSpeed: stats.downloadSpeed,
      uploadSpeed: stats.uploadSpeed,
      ratio: stats.ratio
    });
    
    // 9. Cleanup und Verbindung trennen
    logger.info('Bereinige Ressourcen und trenne Verbindung...');
    await connection.disconnect();
    
    logger.info('Integration erfolgreich abgeschlossen!');
    
    // Zusammenfassung ausgeben
    logger.info('ZUSAMMENFASSUNG:');
    logger.info('==============');
    logger.info(`Publikation: ${publication.title}`);
    logger.info(`DOI: ${publication.doi}`);
    logger.info(`IPFS-Hash: ${registrationResult.ipfsHash}`);
    logger.info(`Magnet-URI: ${linkResult.magnetURI}`);
    logger.info(`Datensatz-IPFS-Hash: ${datasetResult.ipfsHash}`);
    logger.info(`Datensatz-Magnet-URI: ${datasetResult.magnetURI}`);
    logger.info('==============');
    
  } catch (error) {
    logger.error('Fehler im Integrations-Beispiel:', error.message);
    logger.error(error.stack);
  }
}

/**
 * Erstellt eine Beispiel-Publikation
 * @returns {Object} Die Beispiel-Publikation
 */
function createSamplePublication() {
  const id = Date.now().toString().substr(-6);
  const doi = `10.5555/2023.demo${id}`;
  
  return {
    title: `Optimierte Blockchain-Integration für wissenschaftliche Publikationen #${id}`,
    doi,
    metadata: {
      title: `Optimierte Blockchain-Integration für wissenschaftliche Publikationen #${id}`,
      abstract: 'Diese Publikation demonstriert die optimierte Integration von DOI, NFT, IPFS und BitTorrent für dezentralisierte wissenschaftliche Publikationen auf der Polkadot-Blockchain.',
      authors: [
        {
          name: 'Dr. Max Mustermann',
          orcid: '0000-0002-1825-0097',
          affiliation: 'Universität für Blockchain und dezentrale Systeme'
        },
        {
          name: 'Dr. Erika Musterfrau',
          orcid: '0000-0001-5109-3700',
          affiliation: 'Institut für Wissenschaftliche Dezentralisierung'
        }
      ],
      publicationDate: new Date().toISOString(),
      journal: 'Journal of Blockchain Research',
      keywords: ['blockchain', 'polkadot', 'desci', 'doi', 'nft', 'ipfs', 'bittorrent'],
      properties: {
        peerReviewed: true,
        openAccess: true,
        license: 'CC-BY-4.0'
      }
    }
  };
}

/**
 * Erstellt eine Beispiel-PDF-Datei für die Publikation
 * @param {Object} publication - Die Publikationsdaten
 * @returns {Promise<string>} Der Pfad zur erstellten PDF-Datei
 */
async function createSamplePDF(publication) {
  // In einer realen Anwendung würde hier ein PDF generiert
  // Für dieses Beispiel erstellen wir eine Text-Datei, die den PDF-Inhalt simuliert
  
  const publicationDir = path.join(process.cwd(), 'publications');
  if (!fs.existsSync(publicationDir)) {
    fs.mkdirSync(publicationDir, { recursive: true });
  }
  
  const safeDoi = publication.doi.replace('/', '_');
  const filePath = path.join(publicationDir, `${safeDoi}.pdf.txt`);
  
  // Simulierten PDF-Inhalt erstellen
  const content = `
TITEL: ${publication.metadata.title}
DOI: ${publication.doi}
AUTOREN: ${publication.metadata.authors.map(a => a.name).join(', ')}
DATUM: ${publication.metadata.publicationDate}
JOURNAL: ${publication.metadata.journal}

ABSTRACT:
${publication.metadata.abstract}

KEYWORDS:
${publication.metadata.keywords.join(', ')}

INHALT:
Dies ist ein Beispielinhalt für die Publikation zur Demonstration der
optimierten Integration von DOI, NFT, IPFS und BitTorrent für
dezentralisierte wissenschaftliche Publikationen auf der Polkadot-Blockchain.

Der tatsächliche Inhalt würde hier folgen...

LIZENZ:
${publication.metadata.properties.license}
`;

  // In Datei schreiben
  fs.writeFileSync(filePath, content, 'utf8');
  return filePath;
}

/**
 * Erstellt einen Beispiel-Datensatz für die Publikation
 * @param {Object} publication - Die Publikationsdaten
 * @returns {Promise<string>} Der Pfad zum erstellten Datensatz-Verzeichnis
 */
async function createSampleDataset(publication) {
  const safeDoi = publication.doi.replace('/', '_');
  const datasetDir = path.join(process.cwd(), 'datasets', safeDoi);
  
  // Verzeichnisse erstellen
  if (!fs.existsSync(datasetDir)) {
    fs.mkdirSync(datasetDir, { recursive: true });
  }
  
  // Beispiel-Dateien erstellen:
  
  // 1. README.md
  fs.writeFileSync(
    path.join(datasetDir, 'README.md'),
    `# Datensatz für ${publication.metadata.title}\n\n` +
    'Dieser Datensatz enthält die Rohdaten und Analyseskripte für die Publikation\n' +
    `mit dem DOI ${publication.doi}.\n\n` +
    '## Inhalt\n\n' +
    '- data.csv: Simulierte Rohdaten\n' +
    '- analysis.R: Analyseskript\n' +
    '- metadata.json: Metadaten zum Datensatz\n',
    'utf8'
  );
  
  // 2. Simulierte Rohdaten (CSV)
  fs.writeFileSync(
    path.join(datasetDir, 'data.csv'),
    'id,value,category\n' +
    '1,23.5,A\n' +
    '2,17.2,B\n' +
    '3,42.1,A\n' +
    '4,31.8,C\n' +
    '5,29.4,B\n',
    'utf8'
  );
  
  // 3. Simuliertes Analyseskript
  fs.writeFileSync(
    path.join(datasetDir, 'analysis.R'),
    '# Beispiel-Analyseskript\n' +
    'data <- read.csv("data.csv")\n' +
    'summary(data)\n' +
    'plot(data$value ~ data$category)\n',
    'utf8'
  );
  
  // 4. Metadaten
  fs.writeFileSync(
    path.join(datasetDir, 'metadata.json'),
    JSON.stringify({
      title: `Datensatz für ${publication.metadata.title}`,
      description: `Rohdaten und Analyseskripte für die Publikation mit DOI ${publication.doi}`,
      authors: publication.metadata.authors,
      doi: publication.doi,
      license: 'CC-BY-4.0',
      created: new Date().toISOString()
    }, null, 2),
    'utf8'
  );
  
  return datasetDir;
}

// Führe das Beispiel aus, wenn die Datei direkt ausgeführt wird
if (require.main === module) {
  runExample().catch(error => {
    console.error('Fehler beim Ausführen des Beispiels:', error);
    process.exit(1);
  });
} 