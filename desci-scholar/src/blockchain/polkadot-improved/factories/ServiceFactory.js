/**
 * @fileoverview Zentrale Service-Factory-Funktionen
 * 
 * Diese Datei stellt Factory-Funktionen bereit, um Services konsistent zu
 * initialisieren und nach dem Dependency-Injection-Prinzip zu verknüpfen.
 */

import PolkadotClient.js';
imporm '../PolkadotClient.js.js';
import PolkadotWallet from '../PolkadotWallet.js.js';
import HandleService from '../HandleService.js.js';
import IPFSService from '../IPFSService.js.js';
import TorrentServicefrom '../TorrentService.js.js';
import { DoiNftManager, ResearchDataManager } from '../index.js';
import BlockchainConfig from '../BlockchainConfig.js.js';
import LoggerFactory from '../LoggerFactory.js';
import { getContainer } from '../ServiceContainer.js.js';
import { getContainer.js.js';
import { getContainer.js.js';
import { getContainer.js';

const ServiceContainer = getContainer();

// Logger für die Factories
const logger = LoggerFactory.createLogger('service-factory');

/**
 * Erstellt eine Polkadot-Client-Instanz
 * @param {Object} config - Konfiguration für den Client
 * @returns {PolkadotClient} Die erstellte Client-Instanz
 */
function createPolkadotClient(config = {}) {
  logger.debug('Erstelle PolkadotClient');
  
  // Ggf. Netzwerkdaten aus der BlockchainConfig übernehmen
  let clientConfig = { ...config };
  
  if (config.network && !config.endpoint) {
    const networkConfig = BlockchainConfig.getNetwork(config.network);
    clientConfig = {
      ...clientConfig,
      endpoint: networkConfig.endpoint,
      chainId: networkConfig.chainId
    };
  }
  
  return new PolkadotClient(clientConfig);
}

/**
 * Erstellt eine Polkadot-Wallet-Instanz
 * @param {PolkadotClient} client - Der zu verwendende PolkadotClient
 * @param {Object} config - Konfiguration für das Wallet
 * @returns {PolkadotWallet} Die erstellte Wallet-Instanz
 */
function createPolkadotWallet(client, config = {}) {
  logger.debug('Erstelle PolkadotWallet');
  return new PolkadotWallet(client, config);
}

/**
 * Erstellt eine HandleService-Instanz
 * @param {PolkadotClient} client - Der zu verwendende PolkadotClient
 * @param {Object} config - Konfiguration für den HandleService
 * @returns {HandleService} Die erstellte HandleService-Instanz
 */
function createHandleService(client, config = {}) {
  logger.debug('Erstelle HandleService');
  return new HandleService(client, config.handleOptions || {});
}

/**
 * Erstellt eine IPFSService-Instanz
 * @param {Object} config - Konfiguration für den IPFSService
 * @returns {IPFSService} Die erstellte IPFSService-Instanz
 */
function createIpfsService(config = {}) {
  logger.debug('Erstelle IPFSService');
  return new IPFSService(config.ipfsOptions || {});
}

/**
 * Erstellt eine TorrentService-Instanz
 * @param {Object} config - Konfiguration für den TorrentService
 * @returns {TorrentService} Die erstellte TorrentService-Instanz
 */
function createTorrentService(config = {}) {
  logger.debug('Erstelle TorrentService');
  return new TorrentService(config.torrentOptions || {});
}

/**
 * Erstellt eine PolkadotConnection-Instanz 
 * @param {PolkadotClient} client - Der zu verwendende PolkadotClient
 * @param {PolkadotWallet} wallet - Das zu verwendende PolkadotWallet
 * @param {HandleService} handleService - Der zu verwendende HandleService
 * @param {IPFSService} ipfsService - Der zu verwendende IPFSService
 * @param {Object} config - Konfiguration für die PolkadotConnection
 * @returns {Object} Die erstellte PolkadotConnection-Instanz
 */
function createPolkadotConnection(
  client,
  wallet,
  handleService,
  ipfsService,
  config = {}
) {
  logger.debug('Erstelle PolkadotConnection');
  
  // Erstelle ein optimiertes Verbindungsobjekt, das alle Services enthält
  const connection = {
    client,
    wallet,
    handleService,
    ipfsService,
    
    // TorrentService ist optional und wird bei Bedarf initialisiert
    _torrentService: null,
    
    /**
     * Initialisiert die Verbindung und alle Services
     * @param {Object} options - Optionen für die Initialisierung
     * @returns {Promise<Object>} Die initialisierte Verbindung
     */
    async initialize(options = {}) {
      await this.client.initialize(options);
      await this.wallet.initialize(options);
      
      // TorrentService bei Bedarf initialisieren
      if (options.initTorrentService) {
        this.getTorrentService(options.torrentOptions);
      }
      
      return this;
    },
    
    /**
     * Gibt den TorrentService zurück (initialisiert ihn bei Bedarf)
     * @param {Object} options - Optionen für den TorrentService
     * @returns {TorrentService} Der TorrentService
     */
    getTorrentService(options = {}) {
      if (!this._torrentService) {
        this._torrentService = createTorrentService({
          ...config,
          torrentOptions: {
            ...config.torrentOptions,
            ...options
          }
        });
      }
      return this._torrentService;
    },
    
    /**
     * Gibt den PolkadotClient zurück
     * @returns {PolkadotClient} Der PolkadotClient
     */
    getClient() {
      return this.client;
    },
    
    /**
     * Gibt das PolkadotWallet zurück
     * @returns {PolkadotWallet} Das PolkadotWallet
     */
    getWallet() {
      return this.wallet;
    },
    
    /**
     * Gibt den HandleService zurück
     * @returns {HandleService} Der HandleService
     */
    getHandleService() {
      return this.handleService;
    },
    
    /**
     * Gibt den IPFSService zurück
     * @returns {IPFSService} Der IPFSService
     */
    getIpfsService() {
      return this.ipfsService;
    },
    
    /**
     * Schließt die Verbindung und alle Services
     * @returns {Promise<void>}
     */
    async disconnect() {
      // TorrentService herunterfahren, falls vorhanden
      if (this._torrentService) {
        await this._torrentService.destroy();
        this._torrentService = null;
      }
      
      // Client trennen
      await this.client.disconnect();
    }
  };
  
  return connection;
}

/**
 * Erstellt einen DoiNftManager
 * @param {Object} connection - Die zu verwendende PolkadotConnection
 * @param {Object} config - Konfiguration für den DoiNftManager
 * @returns {DoiNftManager} Der erstellte DoiNftManager
 */
function createDoiNftManager(connection, config = {}) {
  logger.debug('Erstelle DoiNftManager');
  return new DoiNftManager(connection);
}

/**
 * Erstellt einen ResearchDataManager
 * @param {Object} connection - Die zu verwendende PolkadotConnection
 * @param {Object} config - Konfiguration für den ResearchDataManager
 * @returns {ResearchDataManager} Der erstellte ResearchDataManager
 */
function createResearchDataManager(connection, config = {}) {
  logger.debug('Erstelle ResearchDataManager');
  return new ResearchDataManager(connection);
}

/**
 * Registriert alle Service-Factories im Container
 * @param {Object} config - Basisonfiguration für alle Services
 * @returns {ServiceContainer} Der ServiceContainer
 */
function registerAllServices(config = {}) {
  logger.info('Registriere alle Services im Container');
  
  // PolkadotClient registrieren
  ServiceContainer.register('polkadotClient', createPolkadotClient, [], config);
  
  // PolkadotWallet registrieren
  ServiceContainer.register('polkadotWallet', createPolkadotWallet, ['polkadotClient'], config);
  
  // HandleService registrieren
  ServiceContainer.register('handleService', createHandleService, ['polkadotClient'], config);
  
  // IPFSService registrieren
  ServiceContainer.register('ipfsService', createIpfsService, [], config);
  
  // TorrentService registrieren (optional)
  ServiceContainer.register('torrentService', createTorrentService, [], config);
  
  // PolkadotConnection registrieren
  ServiceContainer.register(
    'polkadotConnection', 
    createPolkadotConnection, 
    ['polkadotClient', 'polkadotWallet', 'handleService', 'ipfsService'], 
    config
  );
  
  // DoiNftManager registrieren
  ServiceContainer.register('doiNftManager', createDoiNftManager, ['polkadotConnection'], config);
  
  // ResearchDataManager registrieren
  ServiceContainer.register('researchDataManager', createResearchDataManager, ['polkadotConnection'], config);
  
  return ServiceContainer;
}

/**
 * Erstellt eine komplette, initialisierte Instanz der Polkadot-Integration
 * @param {Object} config - Konfiguration für die Integration
 * @returns {Promise<Object>} Eine initialisierte PolkadotConnection und alle Manager
 */
async function createPolkadotIntegration(config = {}) {
  // Services registrieren
  registerAllServices(config);
  
  // Verbindung abrufen und initialisieren
  const connection = ServiceContainer.get('polkadotConnection');
  await connection.initialize(config);
  
  // Manager abrufen
  const doiNftManager = ServiceContainer.get('doiNftManager');
  const researchDataManager = ServiceContainer.get('researchDataManager');
  
  return {
    connection,
    doiNftManager,
    researchDataManager,
    
    /**
     * Bereinigt alle Ressourcen der Integration
     * @returns {Promise<void>}
     */
    async cleanup() {
      await connection.disconnect();
      ServiceContainer.reset();
      logger.info('Polkadot-Integration bereinigt');
    }
  };
}

export default {
  createPolkadotClient,
  createPolkadotWallet,
  createHandleService,
  createIpfsService,
  createTorrentService,
  createPolkadotConnection,
  createDoiNftManager,
  createResearchDataManager,
  registerAllServices,
  createPolkadotIntegration
}; };   createPolkadotIntegration
}; 