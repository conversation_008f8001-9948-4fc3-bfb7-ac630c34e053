/**
 * @fileoverview LoggerFactory für einheitliche Logger-Konfiguration
 * 
 * Diese Klasse bietet eine zentrale Stelle für die Erstellung von Loggern mit
 * konsistenter Konfiguration im gesamten Projekt.
 */

import winston from 'winston';imporrrrt path from 'path';
import fs from 'fs'// Sicherstellen, dass das Log-Verzeichnis existiert
const logDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

/**
 * Factory für konsistente Logger-Instanzen
 */
class LoggerFactory {
  /**
   * Erstellt einen standardisierten Logger für einen bestimmten Service
   * @param {string} serviceName - Name des Services für den Logger
   * @param {Object} options - Zusätzliche Optionen für den Logger
   * @returns {winston.Logger} - Konfigurierter Winston-Logger
   */
  static createLogger(serviceName, options = {}) {
    const defaultFormat = winston.format.combine(
      winston.format.timestamp(),
      winston.format.json()
    );

    const consoleFormat = winston.format.combine(
      winston.format.colorize(),
      winston.format.simple()
    );

    // Möglichkeit zur Überschreibung des Log-Levels über Umgebungsvariable
    const logLevel = options.level || process.env.LOG_LEVEL || 'info';
    
    // Standardmäßige Transports
    const transports = [
      new winston.transports.Console({
        format: consoleFormat
      }),
      new winston.transports.File({ 
        filename: path.join(logDir, `${serviceName}-error.log`), 
        level: 'error' 
      }),
      new winston.transports.File({ 
        filename: path.join(logDir, `${serviceName}-combined.log`) 
      })
    ];

    // Gemeinsame Transport-Datei für alle Services, wenn gewünscht
    if (options.useCommonLogs) {
      transports.push(
        new winston.transports.File({
          filename: path.join(logDir, 'all-services-error.log'),
          level: 'error'
        }),
        new winston.transports.File({
          filename: path.join(logDir, 'all-services-combined.log')
        })
      );
    }

    // Benutzerdefinierte Transports hinzufügen, falls vorhanden
    if (options.additionalTransports && Array.isArray(options.additionalTransports)) {
      transports.push(...options.additionalTransports);
    }

    return winston.createLogger({
      level: logLevel,
      format: options.format || defaultFormat,
      defaultMeta: { service: serviceName },
      transports
    });
  }

  /**
   * Erstellt einen einfachen Logger für Skripte oder Beispiele
   * @param {string} name - Name des Skripts
   * @param {string} filename - Optionaler Dateiname für Logs
   * @returns {winston.Logger} - Einfacher Logger für Skripte
   */
  static createScriptLogger(name, filename) {
    const transports = [
      new winston.transports.Console({
        format: winston.format.combine(
          winston.format.colorize(),
          winston.format.timestamp(),
          winston.format.printf(info => `${info.timestamp} ${info.level}: ${info.message}`)
        )
      })
    ];

    if (filename) {
      transports.push(
        new winston.transports.File({ 
          filename: path.join(logDir, filename) 
        })
      );
    }

    return winston.createLogger({
      level: process.env.LOG_LEVEL || 'info',
      defaultMeta: { script: name },
      transports
    });
  }
}

export default LoggerFactory; 