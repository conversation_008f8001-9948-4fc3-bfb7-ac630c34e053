/**
 * Standardisierte Metadaten-Struktur für DOI-NFTs
 * Basierend auf ERC-721 Metadata Standard mit Erweiterungen für wissenschaftliche Publikationen
 */

class DoiNftMetadata {
  static createMetadata({
    doi,
    orcid,
    patentId,
    title,
    authors,
    abstract,
    publicationDate,
    journal,
    version,
    license,
    keywords,
    citations = []
  }) {
    return {
      // Basis NFT Metadaten (ERC-721 kompatibel)
      name: title,
      description: abstract,
      image: null, // Optional: Preview-Bild der Publikation
      
      // Wissenschaftliche Identifikatoren
      identifiers: {
        doi: this.validateDoi(doi),
        orcid: this.validateOrcid(orcid),
        patentId: patentId ? this.validatePatentId(patentId) : null
      },
      
      // Publikationsdetails
      publication: {
        title,
        authors: authors.map(author => ({
          name: author.name,
          orcid: author.orcid ? this.validateOrcid(author.orcid) : null,
          affiliations: author.affiliations || []
        })),
        abstract,
        publicationDate,
        journal: journal ? {
          name: journal.name,
          issn: journal.issn,
          volume: journal.volume,
          issue: journal.issue
        } : null,
        version,
        license,
        keywords
      },
      
      // Zitationen und Referenzen
      citations: citations.map(citation => ({
        doi: this.validateDoi(citation.doi),
        title: citation.title,
        authors: citation.authors,
        year: citation.year
      })),
      
      // Blockchain-spezifische Metadaten
      blockchain: {
        timestamp: Date.now(),
        version: '1.0',
        standard: 'DeSci-Scholar-v1'
      }
    };
  }

  // DOI-Validierung nach ISO 26324
  static validateDoi(doi) {
    const doiRegex = /^10\.\d{4,9}\/[-._;()\/:a-zA-Z0-9]+$/;
    if (!doiRegex.test(doi)) {
      throw new Error('Ungültiges DOI-Format');
    }
    return doi;
  }

  // ORCID-Validierung nach ORCID-Standard
  static validateOrcid(orcid) {
    const orcidRegex = /^\d{4}-\d{4}-\d{4}-\d{3}[\dX]$/;
    if (!orcidRegex.test(orcid)) {
      throw new Error('Ungültiges ORCID-Format');
    }
    return orcid;
  }

  // Patent-ID-Validierung (Beispiel für USPTO-Format)
  static validatePatentId(patentId) {
    const patentRegex = /^US\d{7}$|^US\d{8}$|^US\d{8}[A-Z]\d$/;
    if (!patentRegex.test(patentId)) {
      throw new Error('Ungültiges Patent-ID-Format');
    }
    return patentId;
  }

  // Metadaten-Validierung
  static validateMetadata(metadata) {
    const requiredFields = ['title', 'authors', 'abstract', 'doi'];
    
    for (const field of requiredFields) {
      if (!metadata[field]) {
        throw new Error(`Fehlendes Pflichtfeld: ${field}`);
      }
    }

    // DOI validieren
    this.validateDoi(metadata.doi);

    // ORCID validieren (falls vorhanden)
    if (metadata.orcid) {
      this.validateOrcid(metadata.orcid);
    }

    // Patent-ID validieren (falls vorhanden)
    if (metadata.patentId) {
      this.validatePatentId(metadata.patentId);
    }

    return true;
  }
}

export default DoiNftMetadata; 