import { Contract } from '@polkadot/api-contract';
import DoiNftMetadata from './DoiNftMetadata';
import EnhancedTorrentManager from '../storage/EnhancedTorrentManager';
import IpfsManager from '../storage/IpfsManager';
import PidManager from '../identifiers/PidManager';
import crypto from 'crypto';

class DoiNftContract {
  constructor(api, address, options = {}) {
    this.api = api;
    this.contract = new Contract(api, DoiNftContract.abi, address);
    this.torrentManager = new EnhancedTorrentManager(options.torrent);
    this.ipfsManager = new IpfsManager(options.ipfs);
    this.pidManager = new PidManager();
  }

  static abi = {
    // Smart Contract ABI Definition
    contract: {
      name: 'DoiNft',
      version: '1.0.0',
      authors: ['DeSci-Scholar Team']
    },
    messages: {
      mint: {
        description: 'Erstellt ein neues DOI-NFT mit standardisierten Metadaten',
        selector: 0x1234,
        args: [
          { name: 'metadata', type: 'String' }
        ],
        mutates: true,
        payable: false,
        returnType: 'Hash'
      },
      linkIdentifiers: {
        description: 'Verknüpft zusätzliche Identifikatoren (ORCID, Patent-ID) mit einem NFT',
        selector: 0x2345,
        args: [
          { name: 'tokenId', type: 'Hash' },
          { name: 'identifierType', type: 'String' },
          { name: 'identifierValue', type: 'String' }
        ],
        mutates: true,
        payable: false,
        returnType: 'bool'
      },
      updateMetadata: {
        description: 'Aktualisiert die Metadaten eines NFTs',
        selector: 0x3456,
        args: [
          { name: 'tokenId', type: 'Hash' },
          { name: 'metadata', type: 'String' }
        ],
        mutates: true,
        payable: false,
        returnType: 'bool'
      },
      addCitation: {
        description: 'Fügt eine neue Zitation hinzu',
        selector: 0x4567,
        args: [
          { name: 'tokenId', type: 'Hash' },
          { name: 'citationDoi', type: 'String' }
        ],
        mutates: true,
        payable: false,
        returnType: 'bool'
      },
      setRoyalties: {
        description: 'Legt Tantiemen fest',
        selector: 0x5678,
        args: [
          { name: 'tokenId', type: 'Hash' },
          { name: 'percentage', type: 'u8' }
        ],
        mutates: true,
        payable: false,
        returnType: 'bool'
      },
      distributeRoyalties: {
        description: 'Verteilt Tantiemen',
        selector: 0x6789,
        args: [
          { name: 'tokenId', type: 'Hash' }
        ],
        mutates: true,
        payable: true,
        returnType: 'bool'
      },
      addTorrentInfo: {
        description: 'Fügt BitTorrent-Informationen zu einem NFT hinzu',
        selector: 0xD456,
        args: [
          { name: 'tokenId', type: 'Hash' },
          { name: 'magnetURI', type: 'String' },
          { name: 'infoHash', type: 'String' }
        ],
        mutates: true,
        payable: false,
        returnType: 'bool'
      },
      getTorrentInfo: {
        description: 'Ruft BitTorrent-Informationen eines NFTs ab',
        selector: 0xE567,
        args: [{ name: 'tokenId', type: 'Hash' }],
        mutates: false,
        payable: false,
        returnType: 'String'
      }
    },
    events: {
      DoiNftMinted: {
        description: 'Ein neues DOI-NFT wurde erstellt',
        args: [
          { name: 'creator', type: 'AccountId' },
          { name: 'tokenId', type: 'Hash' },
          { name: 'metadata', type: 'String' }
        ]
      },
      IdentifierLinked: {
        description: 'Ein Identifikator wurde verknüpft',
        args: [
          { name: 'tokenId', type: 'Hash' },
          { name: 'identifierType', type: 'String' },
          { name: 'identifierValue', type: 'String' }
        ]
      },
      CitationAdded: {
        description: 'Eine Zitation wurde hinzugefügt',
        args: [
          { name: 'tokenId', type: 'Hash' },
          { name: 'citationDoi', type: 'String' }
        ]
      },
      RoyaltiesDistributed: {
        description: 'Tantiemen wurden verteilt',
        args: [
          { name: 'tokenId', type: 'Hash' },
          { name: 'amount', type: 'Balance' }
        ]
      },
      TorrentInfoAdded: {
        description: 'BitTorrent-Informationen wurden hinzugefügt',
        args: [
          { name: 'tokenId', type: 'Hash' },
          { name: 'magnetURI', type: 'String' },
          { name: 'infoHash', type: 'String' }
        ]
      }
    }
  };

  async mint(publicationData) {
    try {
      // Metadaten erstellen und validieren
      const metadata = DoiNftMetadata.createMetadata(publicationData);
      DoiNftMetadata.validateMetadata(metadata);

      // NFT minten
      return await this.contract.tx.mint({
        metadata: JSON.stringify(metadata)
      });
    } catch (error) {
      throw new Error(`NFT-Minting fehlgeschlagen: ${error.message}`);
    }
  }

  async linkIdentifier(tokenId, type, value) {
    try {
      // Identifikator validieren
      switch (type) {
        case 'orcid':
          DoiNftMetadata.validateOrcid(value);
          break;
        case 'patent':
          DoiNftMetadata.validatePatentId(value);
          break;
        default:
          throw new Error(`Unbekannter Identifikator-Typ: ${type}`);
      }

      return await this.contract.tx.linkIdentifiers({
        tokenId,
        identifierType: type,
        identifierValue: value
      });
    } catch (error) {
      throw new Error(`Identifikator-Verknüpfung fehlgeschlagen: ${error.message}`);
    }
  }

  async updateMetadata(tokenId, updateData) {
    try {
      // Aktuelle Metadaten abrufen
      const currentMetadata = await this.getTokenInfo(tokenId);
      const parsedMetadata = JSON.parse(currentMetadata.metadata);

      // Metadaten aktualisieren
      const updatedMetadata = {
        ...parsedMetadata,
        ...updateData,
        blockchain: {
          ...parsedMetadata.blockchain,
          timestamp: Date.now(),
          version: (parseFloat(parsedMetadata.blockchain.version) + 0.1).toFixed(1)
        }
      };

      // Validierung der aktualisierten Metadaten
      DoiNftMetadata.validateMetadata(updatedMetadata);

      return await this.contract.tx.updateMetadata({
        tokenId,
        metadata: JSON.stringify(updatedMetadata)
      });
    } catch (error) {
      throw new Error(`Metadaten-Aktualisierung fehlgeschlagen: ${error.message}`);
    }
  }

  async addCitation(tokenId, citationDoi) {
    try {
      // DOI validieren
      DoiNftMetadata.validateDoi(citationDoi);

      return await this.contract.tx.addCitation({
        tokenId,
        citationDoi
      });
    } catch (error) {
      throw new Error(`Zitation hinzufügen fehlgeschlagen: ${error.message}`);
    }
  }

  async setRoyalties(tokenId, percentage) {
    if (percentage < 0 || percentage > 100) {
      throw new Error('Ungültiger Prozentsatz für Tantiemen');
    }
    return await this.contract.tx.setRoyalties({ tokenId, percentage });
  }

  async distributeRoyalties(tokenId, value) {
    return await this.contract.tx.distributeRoyalties({ tokenId, value });
  }

  async getTokenInfo(tokenId) {
    const result = await this.contract.query.tokenInfo(tokenId);
    if (result.output.isEmpty) {
      throw new Error('Token nicht gefunden');
    }
    return result.output.toJSON();
  }

  async getRoyaltyInfo(tokenId) {
    const result = await this.contract.query.royaltyInfo(tokenId);
    if (result.output.isEmpty) {
      throw new Error('Keine Tantiemen-Information gefunden');
    }
    return result.output.toJSON();
  }

  async verifyOwnership(tokenId, account) {
    const result = await this.contract.query.ownerOf(tokenId);
    return result.output.toString() === account;
  }

  /**
   * Erstellt ein NFT mit IPFS-Metadaten und BitTorrent-Integration
   */
  async mintWithIpfsAndTorrent(publicationData) {
    try {
      // Erstelle zuerst den Torrent
      const magnetURI = await this.torrentManager.createPublicationTorrent(publicationData);
      
      // Erstelle erweiterte Metadaten
      const metadata = DoiNftMetadata.createMetadata({
        ...publicationData,
        torrent: {
          magnetURI,
          created: Date.now()
        }
      });
      
      // Speichere Metadaten auf IPFS
      const metadataCid = await this.ipfsManager.storeMetadata(metadata);
      
      // Pinne die Metadaten
      await this.ipfsManager.pinMetadata(metadataCid);
      
      // Erstelle NFT mit IPFS-Link
      const mintResult = await this.contract.tx.mint({
        metadata: this.ipfsManager.createIpfsLink(metadataCid)
      });
      
      // Füge Torrent-Informationen hinzu
      await this.contract.tx.addTorrentInfo({
        tokenId: mintResult.tokenId,
        magnetURI,
        infoHash: mintResult.infoHash
      });
      
      return {
        tokenId: mintResult.tokenId,
        ipfs: {
          cid: metadataCid,
          url: this.ipfsManager.getHttpUrl(metadataCid)
        },
        torrent: {
          magnetURI,
          infoHash: mintResult.infoHash
        }
      };
    } catch (error) {
      throw new Error(`NFT-Minting fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Aktualisiert die Metadaten eines NFTs über IPFS
   */
  async updateMetadataWithIpfs(tokenId, updateData) {
    try {
      // Hole aktuelle IPFS-CID
      const currentMetadata = await this.getTokenInfo(tokenId);
      const currentCid = currentMetadata.metadata.replace('ipfs://', '');
      
      // Aktualisiere Metadaten auf IPFS
      const newCid = await this.ipfsManager.updateMetadata(currentCid, updateData);
      
      // Pinne neue Version
      await this.ipfsManager.pinMetadata(newCid);
      
      // Aktualisiere NFT mit neuem IPFS-Link
      return await this.contract.tx.updateMetadata({
        tokenId,
        metadata: this.ipfsManager.createIpfsLink(newCid)
      });
    } catch (error) {
      throw new Error(`Metadaten-Aktualisierung fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Ruft die vollständigen Metadaten eines NFTs ab
   */
  async getFullMetadata(tokenId) {
    try {
      const tokenInfo = await this.getTokenInfo(tokenId);
      const cid = tokenInfo.metadata.replace('ipfs://', '');
      
      // Hole Metadaten von IPFS
      const metadata = await this.ipfsManager.getMetadata(cid);
      
      // Hole Torrent-Statistiken
      const torrentStats = await this.getEnhancedAvailabilityStats(tokenId);
      
      return {
        metadata,
        torrentStats,
        ipfs: {
          cid,
          url: this.ipfsManager.getHttpUrl(cid)
        }
      };
    } catch (error) {
      throw new Error(`Metadaten-Abruf fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Lädt eine Publikation über BitTorrent herunter
   * @param {string} tokenId - ID des NFTs
   * @returns {Promise<Object>} - Heruntergeladene Publikation
   */
  async downloadPublication(tokenId) {
    try {
      // Hole Torrent-Informationen vom NFT
      const torrentInfo = await this.contract.query.getTorrentInfo(tokenId);
      if (!torrentInfo.output) {
        throw new Error('Keine Torrent-Informationen gefunden');
      }
      
      const { magnetURI } = JSON.parse(torrentInfo.output.toString());
      
      // Überprüfe Verfügbarkeit
      const isAvailable = await this.torrentManager.checkAvailability(magnetURI);
      if (!isAvailable) {
        throw new Error('Publikation ist derzeit nicht verfügbar');
      }
      
      // Starte Download
      return await this.torrentManager.downloadPublication(magnetURI);
    } catch (error) {
      throw new Error(`Download fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Ruft Statistiken über die Verfügbarkeit einer Publikation ab
   * @param {string} tokenId - ID des NFTs
   * @returns {Promise<Object>} - Verfügbarkeitsstatistiken
   */
  async getAvailabilityStats(tokenId) {
    try {
      const torrentInfo = await this.contract.query.getTorrentInfo(tokenId);
      if (!torrentInfo.output) {
        throw new Error('Keine Torrent-Informationen gefunden');
      }
      
      const { infoHash } = JSON.parse(torrentInfo.output.toString());
      return await this.torrentManager.getTorrentStats(infoHash);
    } catch (error) {
      throw new Error(`Statistikabruf fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Erstellt ein verschlüsseltes NFT mit BitTorrent-Integration
   */
  async mintEncryptedWithTorrent(publicationData, accessControl = {}) {
    try {
      // Generiere Verschlüsselungsschlüssel
      const encryptionKey = crypto.randomBytes(32).toString('hex');
      
      // Erstelle verschlüsselten Torrent
      const { magnetURI, encryptionKey: confirmedKey } = 
        await this.torrentManager.createEncryptedTorrent(publicationData, encryptionKey);
      
      // Erstelle Metadaten mit Verschlüsselungsinformationen
      const metadata = DoiNftMetadata.createMetadata({
        ...publicationData,
        torrent: {
          magnetURI,
          created: Date.now(),
          encrypted: true,
          encryptionMethod: 'AES-256-GCM'
        },
        accessControl: {
          type: accessControl.type || 'private',
          allowedUsers: accessControl.allowedUsers || [],
          price: accessControl.price || 0
        }
      });
      
      // Mint das NFT
      const mintResult = await this.contract.tx.mint({
        metadata: JSON.stringify(metadata)
      });
      
      // Speichere Verschlüsselungsschlüssel sicher
      await this.storeEncryptionKey(mintResult.tokenId, confirmedKey);
      
      return {
        tokenId: mintResult.tokenId,
        metadata,
        torrent: {
          magnetURI,
          encrypted: true
        }
      };
    } catch (error) {
      throw new Error(`Verschlüsseltes NFT-Minting fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Lädt eine verschlüsselte Publikation herunter
   */
  async downloadEncryptedPublication(tokenId, accessKey) {
    try {
      // Prüfe Zugriffsberechtigung
      await this.verifyAccess(tokenId, accessKey);
      
      // Hole Verschlüsselungsschlüssel
      const encryptionKey = await this.getEncryptionKey(tokenId, accessKey);
      
      // Hole Torrent-Informationen
      const torrentInfo = await this.contract.query.getTorrentInfo(tokenId);
      if (!torrentInfo.output) {
        throw new Error('Keine Torrent-Informationen gefunden');
      }
      
      const { magnetURI } = JSON.parse(torrentInfo.output.toString());
      
      // Lade und entschlüssele die Publikation
      return await this.torrentManager.downloadEncryptedPublication(magnetURI, encryptionKey);
    } catch (error) {
      throw new Error(`Download fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Ruft erweiterte Verfügbarkeitsstatistiken ab
   */
  async getEnhancedAvailabilityStats(tokenId) {
    try {
      const torrentInfo = await this.contract.query.getTorrentInfo(tokenId);
      if (!torrentInfo.output) {
        throw new Error('Keine Torrent-Informationen gefunden');
      }
      
      const { infoHash } = JSON.parse(torrentInfo.output.toString());
      return await this.torrentManager.getEnhancedStats(infoHash);
    } catch (error) {
      throw new Error(`Statistikabruf fehlgeschlagen: ${error.message}`);
    }
  }

  // Hilfsfunktionen für Verschlüsselung und Zugriffskontrolle
  async storeEncryptionKey(tokenId, key) {
    // Implementierung der sicheren Schlüsselspeicherung
    // (z.B. in einem verschlüsselten Smart Contract-Speicher)
  }

  async getEncryptionKey(tokenId, accessKey) {
    // Implementierung des sicheren Schlüsselabrufs
  }

  async verifyAccess(tokenId, accessKey) {
    // Implementierung der Zugriffskontrolle
  }

  /**
   * Erstellt ein vollständiges wissenschaftliches NFT
   */
  async mintScientificPublication(publicationData) {
    try {
      // Erstelle und validiere PIDs
      // Erstelle Metadaten mit DOI
      const doi = publicationData.doi || this.pidManager.generateDoi(publicationData);
      const pidMetadata = {
        ...publicationData,
        doi,
        identifiers: {
          ...publicationData.identifiers,
          doi
        }
      };

      // Validiere DOI
      if (!this.pidManager.validateDoi(doi)) {
        throw new Error(`Ungültiges DOI-Format: ${doi}`);
      }

      // Erstelle Torrent
      const magnetURI = await this.torrentManager.createPublicationTorrent({
        file: publicationData.file,
        metadata: pidMetadata
      });

      // Erweitere Metadaten
      const enrichedMetadata = {
        ...pidMetadata,
        torrent: {
          magnetURI,
          created: Date.now()
        }
      };

      // Speichere auf IPFS
      const metadataCid = await this.ipfsManager.storeMetadata(enrichedMetadata);
      await this.ipfsManager.pinMetadata(metadataCid);

      // Erstelle NFT
      const mintResult = await this.contract.tx.mint({
        metadata: this.ipfsManager.createIpfsLink(metadataCid)
      });

      // Füge Torrent-Informationen hinzu
      await this.contract.tx.addTorrentInfo({
        tokenId: mintResult.tokenId,
        magnetURI,
        infoHash: mintResult.infoHash
      });

      // Erstelle zitierbare Links
      const citationLinks = enrichedMetadata.citations || [];

      return {
        tokenId: mintResult.tokenId,
        metadata: enrichedMetadata,
        ipfs: {
          cid: metadataCid,
          url: this.ipfsManager.getHttpUrl(metadataCid)
        },
        torrent: {
          magnetURI,
          infoHash: mintResult.infoHash
        },
        citations: citationLinks
      };
    } catch (error) {
      throw new Error(`Wissenschaftliches NFT-Minting fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Aktualisiert die Metadaten mit PID-Validierung
   */
  async updateScientificMetadata(tokenId, updateData) {
    try {
      // Hole aktuelle Metadaten
      const currentMetadata = await this.getFullMetadata(tokenId);

      // Erstelle aktualisierte Metadaten
      const updatedMetadata = {
        ...currentMetadata.metadata,
        ...updateData
      };

      // Validiere DOI, falls vorhanden
      if (updatedMetadata.doi && !this.pidManager.validateDoi(updatedMetadata.doi)) {
        throw new Error(`Ungültiges DOI-Format: ${updatedMetadata.doi}`);
      }

      // Validiere ORCID, falls vorhanden
      if (updatedMetadata.orcid && !this.pidManager.validateOrcid(updatedMetadata.orcid)) {
        throw new Error(`Ungültiges ORCID-Format: ${updatedMetadata.orcid}`);
      }

      // Aktualisiere auf IPFS
      return await this.updateMetadataWithIpfs(tokenId, updatedMetadata);
    } catch (error) {
      throw new Error(`Metadaten-Aktualisierung fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Fügt eine validierte Zitation hinzu
   */
  async addValidatedCitation(tokenId, citationDoi) {
    try {
      // Validiere DOI
      if (!this.pidManager.validateDoi(citationDoi)) {
        throw new Error('Ungültige DOI für Zitation');
      }

      // Füge Zitation hinzu
      await this.contract.tx.addCitation({
        tokenId,
        citationDoi
      });

      // Aktualisiere Metadaten
      const currentMetadata = await this.getFullMetadata(tokenId);
      const updatedMetadata = {
        ...currentMetadata.metadata,
        relationships: {
          ...currentMetadata.metadata.relationships,
          citations: [
            ...currentMetadata.metadata.relationships.citations,
            {
              doi: citationDoi,
              url: `https://doi.org/${citationDoi}`,
              added: Date.now()
            }
          ]
        }
      };

      return await this.updateMetadataWithIpfs(tokenId, updatedMetadata);
    } catch (error) {
      throw new Error(`Zitation hinzufügen fehlgeschlagen: ${error.message}`);
    }
  }
}

export default DoiNftContract; 