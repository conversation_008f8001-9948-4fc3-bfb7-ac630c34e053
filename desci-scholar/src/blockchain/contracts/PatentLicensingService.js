import { ethers } from 'ethers';
import { PolkadotService } from '../polkadot/PolkadotService.js';
import { IPFSService } from '../polkadot-improved/IPFSService.js';
import { logger } from '../../utils/logger.js';
import PatentLicensingABI from './abis/PatentLicensingABI.json';

/**
 * Service für die Verwaltung von Patent-Lizenzierungen über Smart Contracts
 */
export class PatentLicensingService {
  /**
   * Erstellt eine neue Instanz des PatentLicensingService
   * @param {Object} options - Konfigurationsoptionen
   * @param {string} options.contractAddress - Adresse des Patent-Lizenzierungs-Smart-Contracts
   * @param {string} options.providerUrl - URL des Ethereum-Providers
   * @param {string} options.privateKey - Private Key für Transaktionen (optional)
   * @param {IPFSService} options.ipfsService - IPFS-Service für Metadaten-Speicherung
   * @param {PolkadotService} options.polkadotService - Polkadot-Service für Cross-Chain-Funktionalität
   */
  constructor(options = {}) {
    this.contractAddress = options.contractAddress;
    this.providerUrl = options.providerUrl;
    this.privateKey = options.privateKey;
    this.ipfsService = options.ipfsService;
    this.polkadotService = options.polkadotService;
    
    this.provider = null;
    this.wallet = null;
    this.contract = null;
    this.isInitialized = false;
    
    // Cache für Lizenzinformationen
    this.licenseCache = new Map();
  }
  
  /**
   * Initialisiert den Service und verbindet mit dem Smart Contract
   * @returns {Promise<boolean>} Erfolg der Initialisierung
   */
  async initialize() {
    try {
      if (this.isInitialized) {
        return true;
      }
      
      logger.info('Initialisiere Patent-Lizenzierungs-Service', {
        contractAddress: this.contractAddress,
        providerUrl: this.providerUrl
      });
      
      // Provider und Wallet initialisieren
      this.provider = new ethers.providers.JsonRpcProvider(this.providerUrl);
      
      if (this.privateKey) {
        this.wallet = new ethers.Wallet(this.privateKey, this.provider);
        logger.info('Wallet initialisiert', {
          address: this.wallet.address
        });
      }
      
      // Smart Contract initialisieren
      this.contract = new ethers.Contract(
        this.contractAddress,
        PatentLicensingABI,
        this.wallet || this.provider
      );
      
      // Prüfen, ob der Contract erreichbar ist
      const contractName = await this.contract.name();
      logger.info('Smart Contract verbunden', {
        name: contractName,
        address: this.contractAddress
      });
      
      // Event-Listener für Lizenzierungs-Events
      this._setupEventListeners();
      
      this.isInitialized = true;
      return true;
    } catch (error) {
      logger.error('Fehler bei der Initialisierung des Patent-Lizenzierungs-Service', {
        error: error.message,
        stack: error.stack
      });
      return false;
    }
  }
  
  /**
   * Richtet Event-Listener für den Smart Contract ein
   * @private
   */
  _setupEventListeners() {
    // Lizenzierungs-Event
    this.contract.on('LicenseCreated', (licenseId, patentId, licensor, licensee, event) => {
      logger.info('Neue Lizenz erstellt', {
        licenseId: licenseId.toString(),
        patentId: patentId.toString(),
        licensor,
        licensee,
        blockNumber: event.blockNumber
      });
      
      // Cache invalidieren
      this.licenseCache.delete(licenseId.toString());
    });
    
    // Lizenzgebühren-Event
    this.contract.on('RoyaltyPaid', (licenseId, amount, payer, payee, event) => {
      logger.info('Lizenzgebühr bezahlt', {
        licenseId: licenseId.toString(),
        amount: ethers.utils.formatEther(amount),
        payer,
        payee,
        blockNumber: event.blockNumber
      });
    });
    
    // Lizenzstatus-Event
    this.contract.on('LicenseStatusChanged', (licenseId, newStatus, event) => {
      logger.info('Lizenzstatus geändert', {
        licenseId: licenseId.toString(),
        newStatus,
        blockNumber: event.blockNumber
      });
      
      // Cache invalidieren
      this.licenseCache.delete(licenseId.toString());
    });
  }
  
  /**
   * Erstellt eine neue Patentlizenz
   * @param {Object} licenseData - Lizenzdaten
   * @param {string} licenseData.patentId - Patent-ID (z.B. USPTO-Nummer)
   * @param {string} licenseData.licensee - Adresse des Lizenznehmers
   * @param {string} licenseData.licenseType - Lizenztyp (z.B. "exclusive", "non-exclusive")
   * @param {number} licenseData.duration - Dauer der Lizenz in Sekunden
   * @param {string} licenseData.territory - Territorium der Lizenz (z.B. "global", "US", "EU")
   * @param {number} licenseData.royaltyPercentage - Lizenzgebühr in Prozent
   * @param {Object} licenseData.terms - Zusätzliche Lizenzbedingungen
   * @returns {Promise<Object>} Ergebnis der Lizenzerstellung
   */
  async createLicense(licenseData) {
    await this._ensureInitialized();
    
    try {
      logger.info('Erstelle neue Patentlizenz', {
        patentId: licenseData.patentId,
        licensee: licenseData.licensee
      });
      
      // Metadaten in IPFS speichern
      const metadataIpfsCid = await this.ipfsService.uploadMetadata({
        patentId: licenseData.patentId,
        licenseType: licenseData.licenseType,
        duration: licenseData.duration,
        territory: licenseData.territory,
        royaltyPercentage: licenseData.royaltyPercentage,
        terms: licenseData.terms,
        createdAt: new Date().toISOString()
      });
      
      // Transaktion vorbereiten und senden
      const tx = await this.contract.createLicense(
        licenseData.patentId,
        licenseData.licensee,
        licenseData.licenseType,
        licenseData.duration,
        licenseData.territory,
        ethers.utils.parseUnits(licenseData.royaltyPercentage.toString(), 2), // Prozent mit 2 Dezimalstellen
        metadataIpfsCid
      );
      
      // Auf Bestätigung warten
      const receipt = await tx.wait();
      
      // Lizenz-ID aus Event extrahieren
      const licenseCreatedEvent = receipt.events.find(e => e.event === 'LicenseCreated');
      const licenseId = licenseCreatedEvent.args.licenseId.toString();
      
      logger.info('Patentlizenz erfolgreich erstellt', {
        licenseId,
        patentId: licenseData.patentId,
        metadataIpfsCid,
        transactionHash: receipt.transactionHash
      });
      
      // Lizenz-ID auch auf Polkadot registrieren für Cross-Chain-Funktionalität
      if (this.polkadotService) {
        await this.polkadotService.registerPatentLicense(
          licenseId,
          licenseData.patentId,
          metadataIpfsCid
        );
      }
      
      return {
        licenseId,
        patentId: licenseData.patentId,
        licensee: licenseData.licensee,
        metadataIpfsCid,
        transactionHash: receipt.transactionHash,
        blockNumber: receipt.blockNumber
      };
    } catch (error) {
      logger.error('Fehler bei der Erstellung der Patentlizenz', {
        error: error.message,
        stack: error.stack,
        patentId: licenseData.patentId
      });
      throw error;
    }
  }
  
  /**
   * Zahlt Lizenzgebühren für eine Patentlizenz
   * @param {string} licenseId - ID der Lizenz
   * @param {string} amount - Betrag in Ether
   * @param {Object} paymentDetails - Zusätzliche Zahlungsdetails
   * @returns {Promise<Object>} Zahlungsergebnis
   */
  async payRoyalty(licenseId, amount, paymentDetails = {}) {
    await this._ensureInitialized();
    
    try {
      logger.info('Zahle Lizenzgebühr', {
        licenseId,
        amount
      });
      
      // Transaktion vorbereiten und senden
      const tx = await this.contract.payRoyalty(
        licenseId,
        {
          value: ethers.utils.parseEther(amount.toString()),
          gasLimit: paymentDetails.gasLimit || 200000
        }
      );
      
      // Auf Bestätigung warten
      const receipt = await tx.wait();
      
      // Zahlungs-Event extrahieren
      const royaltyPaidEvent = receipt.events.find(e => e.event === 'RoyaltyPaid');
      
      logger.info('Lizenzgebühr erfolgreich gezahlt', {
        licenseId,
        amount,
        transactionHash: receipt.transactionHash,
        blockNumber: receipt.blockNumber
      });
      
      return {
        licenseId,
        amount,
        payer: royaltyPaidEvent.args.payer,
        payee: royaltyPaidEvent.args.payee,
        transactionHash: receipt.transactionHash,
        blockNumber: receipt.blockNumber
      };
    } catch (error) {
      logger.error('Fehler bei der Zahlung der Lizenzgebühr', {
        error: error.message,
        stack: error.stack,
        licenseId
      });
      throw error;
    }
  }
  
  /**
   * Ändert den Status einer Patentlizenz
   * @param {string} licenseId - ID der Lizenz
   * @param {string} newStatus - Neuer Status (z.B. "active", "terminated", "expired")
   * @param {string} reason - Grund für die Statusänderung
   * @returns {Promise<Object>} Ergebnis der Statusänderung
   */
  async changeLicenseStatus(licenseId, newStatus, reason) {
    await this._ensureInitialized();
    
    try {
      logger.info('Ändere Lizenzstatus', {
        licenseId,
        newStatus,
        reason
      });
      
      // Metadaten für die Statusänderung in IPFS speichern
      const statusMetadataIpfsCid = await this.ipfsService.uploadMetadata({
        licenseId,
        newStatus,
        reason,
        changedAt: new Date().toISOString()
      });
      
      // Transaktion vorbereiten und senden
      const tx = await this.contract.changeLicenseStatus(
        licenseId,
        newStatus,
        statusMetadataIpfsCid
      );
      
      // Auf Bestätigung warten
      const receipt = await tx.wait();
      
      logger.info('Lizenzstatus erfolgreich geändert', {
        licenseId,
        newStatus,
        statusMetadataIpfsCid,
        transactionHash: receipt.transactionHash
      });
      
      // Cache invalidieren
      this.licenseCache.delete(licenseId);
      
      return {
        licenseId,
        newStatus,
        statusMetadataIpfsCid,
        transactionHash: receipt.transactionHash,
        blockNumber: receipt.blockNumber
      };
    } catch (error) {
      logger.error('Fehler bei der Änderung des Lizenzstatus', {
        error: error.message,
        stack: error.stack,
        licenseId
      });
      throw error;
    }
  }
  
  /**
   * Ruft Informationen zu einer Patentlizenz ab
   * @param {string} licenseId - ID der Lizenz
   * @param {boolean} forceRefresh - Cache ignorieren und Daten neu laden
   * @returns {Promise<Object>} Lizenzinformationen
   */
  async getLicenseInfo(licenseId, forceRefresh = false) {
    await this._ensureInitialized();
    
    try {
      // Prüfen, ob Daten im Cache sind
      if (!forceRefresh && this.licenseCache.has(licenseId)) {
        return this.licenseCache.get(licenseId);
      }
      
      // Lizenzinformationen vom Smart Contract abrufen
      const licenseInfo = await this.contract.getLicense(licenseId);
      
      // Metadaten von IPFS abrufen
      const metadata = await this.ipfsService.getMetadata(licenseInfo.metadataIpfsCid);
      
      // Zahlungshistorie abrufen
      const paymentHistory = await this._getLicensePaymentHistory(licenseId);
      
      // Strukturierte Lizenzinformationen erstellen
      const result = {
        licenseId,
        patentId: licenseInfo.patentId,
        licensor: licenseInfo.licensor,
        licensee: licenseInfo.licensee,
        licenseType: licenseInfo.licenseType,
        duration: licenseInfo.duration.toNumber(),
        expiryDate: new Date(licenseInfo.startDate.toNumber() * 1000 + licenseInfo.duration.toNumber() * 1000).toISOString(),
        territory: licenseInfo.territory,
        royaltyPercentage: licenseInfo.royaltyPercentage.toNumber() / 100, // Umrechnung in Prozent
        status: licenseInfo.status,
        startDate: new Date(licenseInfo.startDate.toNumber() * 1000).toISOString(),
        metadata,
        paymentHistory
      };
      
      // Im Cache speichern
      this.licenseCache.set(licenseId, result);
      
      return result;
    } catch (error) {
      logger.error('Fehler beim Abrufen der Lizenzinformationen', {
        error: error.message,
        stack: error.stack,
        licenseId
      });
      throw error;
    }
  }
  
  /**
   * Ruft die Zahlungshistorie einer Lizenz ab
   * @private
   