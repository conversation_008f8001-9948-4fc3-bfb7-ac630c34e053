/**
 * Smart Contract für die Verwaltung von Lizenzierungen und Royalties für DOI-NFTs
 * Implementiert ERC-2981 für Royalties und erweiterte Lizenzierungsfunktionen
 */

class DoiRoyaltyContract {
  constructor(api, contractAddress, options = {}) {
    this.api = api;
    this.contractAddress = contractAddress;
    this.options = {
      defaultRoyaltyPercentage: 5, // 5% Standardroyalty
      maxRoyaltyPercentage: 15,    // Maximale Royalty von 15%
      ...options
    };
    
    // Initialisiere den Contract
    this.initialize();
  }
  
  /**
   * Initialisiert den Contract
   * @private
   */
  async initialize() {
    try {
      // In einer realen Implementierung würden wir hier den Contract ABI laden
      // und eine Verbindung zum Contract herstellen
      console.log('Initialisiere DOI Royalty Contract:', this.contractAddress);
      
      // Simuliere Contract-Initialisierung
      this.initialized = true;
    } catch (error) {
      console.error('Fehler bei der Initialisierung des Royalty Contracts:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Royalty-Informationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Array} recipients - Liste der Empfänger (Adressen)
   * @param {Array} shares - Liste der Anteile in Prozent
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setRoyaltyInfo(tokenId, recipients, shares) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Eingaben
    if (!Array.isArray(recipients) || !Array.isArray(shares)) {
      throw new Error('Empfänger und Anteile müssen Arrays sein');
    }
    
    if (recipients.length !== shares.length) {
      throw new Error('Anzahl der Empfänger und Anteile muss übereinstimmen');
    }
    
    // Prüfe, ob die Summe der Anteile 100% nicht überschreitet
    const totalShares = shares.reduce((sum, share) => sum + share, 0);
    if (totalShares > 100) {
      throw new Error('Summe der Anteile darf 100% nicht überschreiten');
    }
    
    // Prüfe, ob die Royalty-Rate das Maximum nicht überschreitet
    const royaltyRate = totalShares / 100 * this.options.defaultRoyaltyPercentage;
    if (royaltyRate > this.options.maxRoyaltyPercentage) {
      throw new Error(`Royalty-Rate darf ${this.options.maxRoyaltyPercentage}% nicht überschreiten`);
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Royalty-Informationen für Token ${tokenId}:`, {
        recipients,
        shares,
        royaltyRate: `${royaltyRate}%`
      });
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        recipients,
        shares,
        royaltyRate
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Royalty-Informationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Royalty-Informationen
   */
  async getRoyaltyInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Royalty-Informationen für Token ${tokenId} ab`);
      
      // Simulierte Royalty-Informationen
      return {
        tokenId,
        recipients: ['0x1234...', '0x5678...'],
        shares: [60, 40], // 60% und 40%
        royaltyRate: this.options.defaultRoyaltyPercentage,
        totalRoyalty: this.options.defaultRoyaltyPercentage
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Berechnet die Royalty-Zahlung für einen Verkauf
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Royalty-Zahlungsinformationen
   */
  async calculateRoyaltyPayment(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Royalty-Informationen ab
      const royaltyInfo = await this.getRoyaltyInfo(tokenId);
      
      // Berechne Royalty-Betrag
      const royaltyAmount = salePrice * (royaltyInfo.royaltyRate / 100);
      
      // Berechne Zahlungen pro Empfänger
      const payments = royaltyInfo.recipients.map((recipient, index) => ({
        recipient,
        share: royaltyInfo.shares[index],
        amount: royaltyAmount * (royaltyInfo.shares[index] / 100)
      }));
      
      return {
        tokenId,
        salePrice,
        royaltyRate: royaltyInfo.royaltyRate,
        totalRoyalty: royaltyAmount,
        payments
      };
    } catch (error) {
      console.error('Fehler bei der Berechnung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Führt eine Royalty-Zahlung für einen Verkauf durch
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async payRoyalties(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Berechne Royalty-Zahlungen
      const royaltyPayment = await this.calculateRoyaltyPayment(tokenId, salePrice);
      
      // In einer realen Implementierung würden wir hier die Zahlungen durchführen
      console.log(`Führe Royalty-Zahlungen für Token ${tokenId} durch:`, royaltyPayment);
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        salePrice,
        royaltyRate: royaltyPayment.royaltyRate,
        totalRoyalty: royaltyPayment.totalRoyalty,
        payments: royaltyPayment.payments
      };
    } catch (error) {
      console.error('Fehler bei der Durchführung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Lizenzinformationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} licenseInfo - Lizenzinformationen
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setLicenseInfo(tokenId, licenseInfo) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Lizenzinformationen
    if (!licenseInfo.type) {
      throw new Error('Lizenztyp muss angegeben werden');
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Lizenzinformationen für Token ${tokenId}:`, licenseInfo);
      
      // Standardisiere Lizenzinformationen
      const standardizedLicense = {
        type: licenseInfo.type,
        name: licenseInfo.name || this.getLicenseName(licenseInfo.type),
        url: licenseInfo.url || this.getLicenseUrl(licenseInfo.type),
        commercial: licenseInfo.commercial !== undefined ? licenseInfo.commercial : true,
        derivatives: licenseInfo.derivatives !== undefined ? licenseInfo.derivatives : true,
        attribution: licenseInfo.attribution !== undefined ? licenseInfo.attribution : true
      };
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        license: standardizedLicense
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Lizenzinformationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Lizenzinformationen
   */
  async getLicenseInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Lizenzinformationen für Token ${tokenId} ab`);
      
      // Simulierte Lizenzinformationen
      return {
        tokenId,
        license: {
          type: 'cc-by',
          name: 'Creative Commons Attribution 4.0',
          url: 'https://creativecommons.org/licenses/by/4.0/',
          commercial: true,
          derivatives: true,
          attribution: true
        }
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Nutzung mit der Lizenz kompatibel ist
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} usage - Nutzungsinformationen
   * @returns {Promise<Object>} - Kompatibilitätsinformationen
   */
  async checkLicenseCompatibility(tokenId, usage) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Lizenzinformationen ab
      const licenseInfo = await this.getLicenseInfo(tokenId);
      
      // Prüfe Kompatibilität
      const isCommercialCompatible = !usage.commercial || licenseInfo.license.commercial;
      const isDerivativesCompatible = !usage.derivatives || licenseInfo.license.derivatives;
      const isAttributionCompatible = !licenseInfo.license.attribution || usage.attribution;
      
      const isCompatible = isCommercialCompatible && isDerivativesCompatible && isAttributionCompatible;
      
      return {
        tokenId,
        license: licenseInfo.license,
        usage,
        isCompatible,
        incompatibilities: {
          commercial: !isCommercialCompatible,
          derivatives: !isDerivativesCompatible,
          attribution: !isAttributionCompatible
        }
      };
    } catch (error) {
      console.error('Fehler bei der Prüfung der Lizenzkompatibilität:', error);
      throw error;
    }
  }
  
  /**
   * Gibt den Namen einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseName(type) {
    const licenseNames = {
      'cc-by': 'Creative Commons Attribution 4.0',
      'cc-by-sa': 'Creative Commons Attribution-ShareAlike 4.0',
      'cc-by-nc': 'Creative Commons Attribution-NonCommercial 4.0',
      'cc-by-nc-sa': 'Creative Commons Attribution-NonCommercial-ShareAlike 4.0',
      'cc-by-nd': 'Creative Commons Attribution-NoDerivatives 4.0',
      'cc-by-nc-nd': 'Creative Commons Attribution-NonCommercial-NoDerivatives 4.0',
      'cc0': 'Creative Commons Zero v1.0 Universal',
      'mit': 'MIT License',
      'apache-2.0': 'Apache License 2.0',
      'gpl-3.0': 'GNU General Public License v3.0'
    };
    
    return licenseNames[type] || 'Custom License';
  }
  
  /**
   * Gibt die URL einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseUrl(type) {
    const licenseUrls = {
      'cc-by': 'https://creativecommons.org/licenses/by/4.0/',
      'cc-by-sa': 'https://creativecommons.org/licenses/by-sa/4.0/',
      'cc-by-nc': 'https://creativecommons.org/licenses/by-nc/4.0/',
      'cc-by-nc-sa': 'https://creativecommons.org/licenses/by-nc-sa/4.0/',
      'cc-by-nd': 'https://creativecommons.org/licenses/by-nd/4.0/',
      'cc-by-nc-nd': 'https://creativecommons.org/licenses/by-nc-nd/4.0/',
      'cc0': 'https://creativecommons.org/publicdomain/zero/1.0/',
      'mit': 'https://opensource.org/licenses/MIT',
      'apache-2.0': 'https://opensource.org/licenses/Apache-2.0',
      'gpl-3.0': 'https://www.gnu.org/licenses/gpl-3.0.en.html'
    };
    
    return licenseUrls[type] || '';
  }
}

export default DoiRoyaltyContract;/**
 * Smart Contract für die Verwaltung von Lizenzierungen und Royalties für DOI-NFTs
 * Implementiert ERC-2981 für Royalties und erweiterte Lizenzierungsfunktionen
 */

class DoiRoyaltyContract {
  constructor(api, contractAddress, options = {}) {
    this.api = api;
    this.contractAddress = contractAddress;
    this.options = {
      defaultRoyaltyPercentage: 5, // 5% Standardroyalty
      maxRoyaltyPercentage: 15,    // Maximale Royalty von 15%
      ...options
    };
    
    // Initialisiere den Contract
    this.initialize();
  }
  
  /**
   * Initialisiert den Contract
   * @private
   */
  async initialize() {
    try {
      // In einer realen Implementierung würden wir hier den Contract ABI laden
      // und eine Verbindung zum Contract herstellen
      console.log('Initialisiere DOI Royalty Contract:', this.contractAddress);
      
      // Simuliere Contract-Initialisierung
      this.initialized = true;
    } catch (error) {
      console.error('Fehler bei der Initialisierung des Royalty Contracts:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Royalty-Informationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Array} recipients - Liste der Empfänger (Adressen)
   * @param {Array} shares - Liste der Anteile in Prozent
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setRoyaltyInfo(tokenId, recipients, shares) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Eingaben
    if (!Array.isArray(recipients) || !Array.isArray(shares)) {
      throw new Error('Empfänger und Anteile müssen Arrays sein');
    }
    
    if (recipients.length !== shares.length) {
      throw new Error('Anzahl der Empfänger und Anteile muss übereinstimmen');
    }
    
    // Prüfe, ob die Summe der Anteile 100% nicht überschreitet
    const totalShares = shares.reduce((sum, share) => sum + share, 0);
    if (totalShares > 100) {
      throw new Error('Summe der Anteile darf 100% nicht überschreiten');
    }
    
    // Prüfe, ob die Royalty-Rate das Maximum nicht überschreitet
    const royaltyRate = totalShares / 100 * this.options.defaultRoyaltyPercentage;
    if (royaltyRate > this.options.maxRoyaltyPercentage) {
      throw new Error(`Royalty-Rate darf ${this.options.maxRoyaltyPercentage}% nicht überschreiten`);
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Royalty-Informationen für Token ${tokenId}:`, {
        recipients,
        shares,
        royaltyRate: `${royaltyRate}%`
      });
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        recipients,
        shares,
        royaltyRate
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Royalty-Informationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Royalty-Informationen
   */
  async getRoyaltyInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Royalty-Informationen für Token ${tokenId} ab`);
      
      // Simulierte Royalty-Informationen
      return {
        tokenId,
        recipients: ['0x1234...', '0x5678...'],
        shares: [60, 40], // 60% und 40%
        royaltyRate: this.options.defaultRoyaltyPercentage,
        totalRoyalty: this.options.defaultRoyaltyPercentage
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Berechnet die Royalty-Zahlung für einen Verkauf
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Royalty-Zahlungsinformationen
   */
  async calculateRoyaltyPayment(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Royalty-Informationen ab
      const royaltyInfo = await this.getRoyaltyInfo(tokenId);
      
      // Berechne Royalty-Betrag
      const royaltyAmount = salePrice * (royaltyInfo.royaltyRate / 100);
      
      // Berechne Zahlungen pro Empfänger
      const payments = royaltyInfo.recipients.map((recipient, index) => ({
        recipient,
        share: royaltyInfo.shares[index],
        amount: royaltyAmount * (royaltyInfo.shares[index] / 100)
      }));
      
      return {
        tokenId,
        salePrice,
        royaltyRate: royaltyInfo.royaltyRate,
        totalRoyalty: royaltyAmount,
        payments
      };
    } catch (error) {
      console.error('Fehler bei der Berechnung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Führt eine Royalty-Zahlung für einen Verkauf durch
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async payRoyalties(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Berechne Royalty-Zahlungen
      const royaltyPayment = await this.calculateRoyaltyPayment(tokenId, salePrice);
      
      // In einer realen Implementierung würden wir hier die Zahlungen durchführen
      console.log(`Führe Royalty-Zahlungen für Token ${tokenId} durch:`, royaltyPayment);
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        salePrice,
        royaltyRate: royaltyPayment.royaltyRate,
        totalRoyalty: royaltyPayment.totalRoyalty,
        payments: royaltyPayment.payments
      };
    } catch (error) {
      console.error('Fehler bei der Durchführung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Lizenzinformationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} licenseInfo - Lizenzinformationen
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setLicenseInfo(tokenId, licenseInfo) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Lizenzinformationen
    if (!licenseInfo.type) {
      throw new Error('Lizenztyp muss angegeben werden');
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Lizenzinformationen für Token ${tokenId}:`, licenseInfo);
      
      // Standardisiere Lizenzinformationen
      const standardizedLicense = {
        type: licenseInfo.type,
        name: licenseInfo.name || this.getLicenseName(licenseInfo.type),
        url: licenseInfo.url || this.getLicenseUrl(licenseInfo.type),
        commercial: licenseInfo.commercial !== undefined ? licenseInfo.commercial : true,
        derivatives: licenseInfo.derivatives !== undefined ? licenseInfo.derivatives : true,
        attribution: licenseInfo.attribution !== undefined ? licenseInfo.attribution : true
      };
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        license: standardizedLicense
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Lizenzinformationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Lizenzinformationen
   */
  async getLicenseInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Lizenzinformationen für Token ${tokenId} ab`);
      
      // Simulierte Lizenzinformationen
      return {
        tokenId,
        license: {
          type: 'cc-by',
          name: 'Creative Commons Attribution 4.0',
          url: 'https://creativecommons.org/licenses/by/4.0/',
          commercial: true,
          derivatives: true,
          attribution: true
        }
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Nutzung mit der Lizenz kompatibel ist
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} usage - Nutzungsinformationen
   * @returns {Promise<Object>} - Kompatibilitätsinformationen
   */
  async checkLicenseCompatibility(tokenId, usage) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Lizenzinformationen ab
      const licenseInfo = await this.getLicenseInfo(tokenId);
      
      // Prüfe Kompatibilität
      const isCommercialCompatible = !usage.commercial || licenseInfo.license.commercial;
      const isDerivativesCompatible = !usage.derivatives || licenseInfo.license.derivatives;
      const isAttributionCompatible = !licenseInfo.license.attribution || usage.attribution;
      
      const isCompatible = isCommercialCompatible && isDerivativesCompatible && isAttributionCompatible;
      
      return {
        tokenId,
        license: licenseInfo.license,
        usage,
        isCompatible,
        incompatibilities: {
          commercial: !isCommercialCompatible,
          derivatives: !isDerivativesCompatible,
          attribution: !isAttributionCompatible
        }
      };
    } catch (error) {
      console.error('Fehler bei der Prüfung der Lizenzkompatibilität:', error);
      throw error;
    }
  }
  
  /**
   * Gibt den Namen einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseName(type) {
    const licenseNames = {
      'cc-by': 'Creative Commons Attribution 4.0',
      'cc-by-sa': 'Creative Commons Attribution-ShareAlike 4.0',
      'cc-by-nc': 'Creative Commons Attribution-NonCommercial 4.0',
      'cc-by-nc-sa': 'Creative Commons Attribution-NonCommercial-ShareAlike 4.0',
      'cc-by-nd': 'Creative Commons Attribution-NoDerivatives 4.0',
      'cc-by-nc-nd': 'Creative Commons Attribution-NonCommercial-NoDerivatives 4.0',
      'cc0': 'Creative Commons Zero v1.0 Universal',
      'mit': 'MIT License',
      'apache-2.0': 'Apache License 2.0',
      'gpl-3.0': 'GNU General Public License v3.0'
    };
    
    return licenseNames[type] || 'Custom License';
  }
  
  /**
   * Gibt die URL einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseUrl(type) {
    const licenseUrls = {
      'cc-by': 'https://creativecommons.org/licenses/by/4.0/',
      'cc-by-sa': 'https://creativecommons.org/licenses/by-sa/4.0/',
      'cc-by-nc': 'https://creativecommons.org/licenses/by-nc/4.0/',
      'cc-by-nc-sa': 'https://creativecommons.org/licenses/by-nc-sa/4.0/',
      'cc-by-nd': 'https://creativecommons.org/licenses/by-nd/4.0/',
      'cc-by-nc-nd': 'https://creativecommons.org/licenses/by-nc-nd/4.0/',
      'cc0': 'https://creativecommons.org/publicdomain/zero/1.0/',
      'mit': 'https://opensource.org/licenses/MIT',
      'apache-2.0': 'https://opensource.org/licenses/Apache-2.0',
      'gpl-3.0': 'https://www.gnu.org/licenses/gpl-3.0.en.html'
    };
    
    return licenseUrls[type] || '';
  }
}

export default DoiRoyaltyContract;/**
 * Smart Contract für die Verwaltung von Lizenzierungen und Royalties für DOI-NFTs
 * Implementiert ERC-2981 für Royalties und erweiterte Lizenzierungsfunktionen
 */

class DoiRoyaltyContract {
  constructor(api, contractAddress, options = {}) {
    this.api = api;
    this.contractAddress = contractAddress;
    this.options = {
      defaultRoyaltyPercentage: 5, // 5% Standardroyalty
      maxRoyaltyPercentage: 15,    // Maximale Royalty von 15%
      ...options
    };
    
    // Initialisiere den Contract
    this.initialize();
  }
  
  /**
   * Initialisiert den Contract
   * @private
   */
  async initialize() {
    try {
      // In einer realen Implementierung würden wir hier den Contract ABI laden
      // und eine Verbindung zum Contract herstellen
      console.log('Initialisiere DOI Royalty Contract:', this.contractAddress);
      
      // Simuliere Contract-Initialisierung
      this.initialized = true;
    } catch (error) {
      console.error('Fehler bei der Initialisierung des Royalty Contracts:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Royalty-Informationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Array} recipients - Liste der Empfänger (Adressen)
   * @param {Array} shares - Liste der Anteile in Prozent
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setRoyaltyInfo(tokenId, recipients, shares) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Eingaben
    if (!Array.isArray(recipients) || !Array.isArray(shares)) {
      throw new Error('Empfänger und Anteile müssen Arrays sein');
    }
    
    if (recipients.length !== shares.length) {
      throw new Error('Anzahl der Empfänger und Anteile muss übereinstimmen');
    }
    
    // Prüfe, ob die Summe der Anteile 100% nicht überschreitet
    const totalShares = shares.reduce((sum, share) => sum + share, 0);
    if (totalShares > 100) {
      throw new Error('Summe der Anteile darf 100% nicht überschreiten');
    }
    
    // Prüfe, ob die Royalty-Rate das Maximum nicht überschreitet
    const royaltyRate = totalShares / 100 * this.options.defaultRoyaltyPercentage;
    if (royaltyRate > this.options.maxRoyaltyPercentage) {
      throw new Error(`Royalty-Rate darf ${this.options.maxRoyaltyPercentage}% nicht überschreiten`);
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Royalty-Informationen für Token ${tokenId}:`, {
        recipients,
        shares,
        royaltyRate: `${royaltyRate}%`
      });
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        recipients,
        shares,
        royaltyRate
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Royalty-Informationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Royalty-Informationen
   */
  async getRoyaltyInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Royalty-Informationen für Token ${tokenId} ab`);
      
      // Simulierte Royalty-Informationen
      return {
        tokenId,
        recipients: ['0x1234...', '0x5678...'],
        shares: [60, 40], // 60% und 40%
        royaltyRate: this.options.defaultRoyaltyPercentage,
        totalRoyalty: this.options.defaultRoyaltyPercentage
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Berechnet die Royalty-Zahlung für einen Verkauf
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Royalty-Zahlungsinformationen
   */
  async calculateRoyaltyPayment(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Royalty-Informationen ab
      const royaltyInfo = await this.getRoyaltyInfo(tokenId);
      
      // Berechne Royalty-Betrag
      const royaltyAmount = salePrice * (royaltyInfo.royaltyRate / 100);
      
      // Berechne Zahlungen pro Empfänger
      const payments = royaltyInfo.recipients.map((recipient, index) => ({
        recipient,
        share: royaltyInfo.shares[index],
        amount: royaltyAmount * (royaltyInfo.shares[index] / 100)
      }));
      
      return {
        tokenId,
        salePrice,
        royaltyRate: royaltyInfo.royaltyRate,
        totalRoyalty: royaltyAmount,
        payments
      };
    } catch (error) {
      console.error('Fehler bei der Berechnung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Führt eine Royalty-Zahlung für einen Verkauf durch
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async payRoyalties(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Berechne Royalty-Zahlungen
      const royaltyPayment = await this.calculateRoyaltyPayment(tokenId, salePrice);
      
      // In einer realen Implementierung würden wir hier die Zahlungen durchführen
      console.log(`Führe Royalty-Zahlungen für Token ${tokenId} durch:`, royaltyPayment);
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        salePrice,
        royaltyRate: royaltyPayment.royaltyRate,
        totalRoyalty: royaltyPayment.totalRoyalty,
        payments: royaltyPayment.payments
      };
    } catch (error) {
      console.error('Fehler bei der Durchführung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Lizenzinformationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} licenseInfo - Lizenzinformationen
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setLicenseInfo(tokenId, licenseInfo) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Lizenzinformationen
    if (!licenseInfo.type) {
      throw new Error('Lizenztyp muss angegeben werden');
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Lizenzinformationen für Token ${tokenId}:`, licenseInfo);
      
      // Standardisiere Lizenzinformationen
      const standardizedLicense = {
        type: licenseInfo.type,
        name: licenseInfo.name || this.getLicenseName(licenseInfo.type),
        url: licenseInfo.url || this.getLicenseUrl(licenseInfo.type),
        commercial: licenseInfo.commercial !== undefined ? licenseInfo.commercial : true,
        derivatives: licenseInfo.derivatives !== undefined ? licenseInfo.derivatives : true,
        attribution: licenseInfo.attribution !== undefined ? licenseInfo.attribution : true
      };
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        license: standardizedLicense
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Lizenzinformationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Lizenzinformationen
   */
  async getLicenseInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Lizenzinformationen für Token ${tokenId} ab`);
      
      // Simulierte Lizenzinformationen
      return {
        tokenId,
        license: {
          type: 'cc-by',
          name: 'Creative Commons Attribution 4.0',
          url: 'https://creativecommons.org/licenses/by/4.0/',
          commercial: true,
          derivatives: true,
          attribution: true
        }
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Nutzung mit der Lizenz kompatibel ist
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} usage - Nutzungsinformationen
   * @returns {Promise<Object>} - Kompatibilitätsinformationen
   */
  async checkLicenseCompatibility(tokenId, usage) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Lizenzinformationen ab
      const licenseInfo = await this.getLicenseInfo(tokenId);
      
      // Prüfe Kompatibilität
      const isCommercialCompatible = !usage.commercial || licenseInfo.license.commercial;
      const isDerivativesCompatible = !usage.derivatives || licenseInfo.license.derivatives;
      const isAttributionCompatible = !licenseInfo.license.attribution || usage.attribution;
      
      const isCompatible = isCommercialCompatible && isDerivativesCompatible && isAttributionCompatible;
      
      return {
        tokenId,
        license: licenseInfo.license,
        usage,
        isCompatible,
        incompatibilities: {
          commercial: !isCommercialCompatible,
          derivatives: !isDerivativesCompatible,
          attribution: !isAttributionCompatible
        }
      };
    } catch (error) {
      console.error('Fehler bei der Prüfung der Lizenzkompatibilität:', error);
      throw error;
    }
  }
  
  /**
   * Gibt den Namen einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseName(type) {
    const licenseNames = {
      'cc-by': 'Creative Commons Attribution 4.0',
      'cc-by-sa': 'Creative Commons Attribution-ShareAlike 4.0',
      'cc-by-nc': 'Creative Commons Attribution-NonCommercial 4.0',
      'cc-by-nc-sa': 'Creative Commons Attribution-NonCommercial-ShareAlike 4.0',
      'cc-by-nd': 'Creative Commons Attribution-NoDerivatives 4.0',
      'cc-by-nc-nd': 'Creative Commons Attribution-NonCommercial-NoDerivatives 4.0',
      'cc0': 'Creative Commons Zero v1.0 Universal',
      'mit': 'MIT License',
      'apache-2.0': 'Apache License 2.0',
      'gpl-3.0': 'GNU General Public License v3.0'
    };
    
    return licenseNames[type] || 'Custom License';
  }
  
  /**
   * Gibt die URL einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseUrl(type) {
    const licenseUrls = {
      'cc-by': 'https://creativecommons.org/licenses/by/4.0/',
      'cc-by-sa': 'https://creativecommons.org/licenses/by-sa/4.0/',
      'cc-by-nc': 'https://creativecommons.org/licenses/by-nc/4.0/',
      'cc-by-nc-sa': 'https://creativecommons.org/licenses/by-nc-sa/4.0/',
      'cc-by-nd': 'https://creativecommons.org/licenses/by-nd/4.0/',
      'cc-by-nc-nd': 'https://creativecommons.org/licenses/by-nc-nd/4.0/',
      'cc0': 'https://creativecommons.org/publicdomain/zero/1.0/',
      'mit': 'https://opensource.org/licenses/MIT',
      'apache-2.0': 'https://opensource.org/licenses/Apache-2.0',
      'gpl-3.0': 'https://www.gnu.org/licenses/gpl-3.0.en.html'
    };
    
    return licenseUrls[type] || '';
  }
}

export default DoiRoyaltyContract;/**
 * Smart Contract für die Verwaltung von Lizenzierungen und Royalties für DOI-NFTs
 * Implementiert ERC-2981 für Royalties und erweiterte Lizenzierungsfunktionen
 */

class DoiRoyaltyContract {
  constructor(api, contractAddress, options = {}) {
    this.api = api;
    this.contractAddress = contractAddress;
    this.options = {
      defaultRoyaltyPercentage: 5, // 5% Standardroyalty
      maxRoyaltyPercentage: 15,    // Maximale Royalty von 15%
      ...options
    };
    
    // Initialisiere den Contract
    this.initialize();
  }
  
  /**
   * Initialisiert den Contract
   * @private
   */
  async initialize() {
    try {
      // In einer realen Implementierung würden wir hier den Contract ABI laden
      // und eine Verbindung zum Contract herstellen
      console.log('Initialisiere DOI Royalty Contract:', this.contractAddress);
      
      // Simuliere Contract-Initialisierung
      this.initialized = true;
    } catch (error) {
      console.error('Fehler bei der Initialisierung des Royalty Contracts:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Royalty-Informationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Array} recipients - Liste der Empfänger (Adressen)
   * @param {Array} shares - Liste der Anteile in Prozent
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setRoyaltyInfo(tokenId, recipients, shares) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Eingaben
    if (!Array.isArray(recipients) || !Array.isArray(shares)) {
      throw new Error('Empfänger und Anteile müssen Arrays sein');
    }
    
    if (recipients.length !== shares.length) {
      throw new Error('Anzahl der Empfänger und Anteile muss übereinstimmen');
    }
    
    // Prüfe, ob die Summe der Anteile 100% nicht überschreitet
    const totalShares = shares.reduce((sum, share) => sum + share, 0);
    if (totalShares > 100) {
      throw new Error('Summe der Anteile darf 100% nicht überschreiten');
    }
    
    // Prüfe, ob die Royalty-Rate das Maximum nicht überschreitet
    const royaltyRate = totalShares / 100 * this.options.defaultRoyaltyPercentage;
    if (royaltyRate > this.options.maxRoyaltyPercentage) {
      throw new Error(`Royalty-Rate darf ${this.options.maxRoyaltyPercentage}% nicht überschreiten`);
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Royalty-Informationen für Token ${tokenId}:`, {
        recipients,
        shares,
        royaltyRate: `${royaltyRate}%`
      });
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        recipients,
        shares,
        royaltyRate
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Royalty-Informationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Royalty-Informationen
   */
  async getRoyaltyInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Royalty-Informationen für Token ${tokenId} ab`);
      
      // Simulierte Royalty-Informationen
      return {
        tokenId,
        recipients: ['0x1234...', '0x5678...'],
        shares: [60, 40], // 60% und 40%
        royaltyRate: this.options.defaultRoyaltyPercentage,
        totalRoyalty: this.options.defaultRoyaltyPercentage
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Berechnet die Royalty-Zahlung für einen Verkauf
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Royalty-Zahlungsinformationen
   */
  async calculateRoyaltyPayment(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Royalty-Informationen ab
      const royaltyInfo = await this.getRoyaltyInfo(tokenId);
      
      // Berechne Royalty-Betrag
      const royaltyAmount = salePrice * (royaltyInfo.royaltyRate / 100);
      
      // Berechne Zahlungen pro Empfänger
      const payments = royaltyInfo.recipients.map((recipient, index) => ({
        recipient,
        share: royaltyInfo.shares[index],
        amount: royaltyAmount * (royaltyInfo.shares[index] / 100)
      }));
      
      return {
        tokenId,
        salePrice,
        royaltyRate: royaltyInfo.royaltyRate,
        totalRoyalty: royaltyAmount,
        payments
      };
    } catch (error) {
      console.error('Fehler bei der Berechnung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Führt eine Royalty-Zahlung für einen Verkauf durch
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async payRoyalties(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Berechne Royalty-Zahlungen
      const royaltyPayment = await this.calculateRoyaltyPayment(tokenId, salePrice);
      
      // In einer realen Implementierung würden wir hier die Zahlungen durchführen
      console.log(`Führe Royalty-Zahlungen für Token ${tokenId} durch:`, royaltyPayment);
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        salePrice,
        royaltyRate: royaltyPayment.royaltyRate,
        totalRoyalty: royaltyPayment.totalRoyalty,
        payments: royaltyPayment.payments
      };
    } catch (error) {
      console.error('Fehler bei der Durchführung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Lizenzinformationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} licenseInfo - Lizenzinformationen
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setLicenseInfo(tokenId, licenseInfo) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Lizenzinformationen
    if (!licenseInfo.type) {
      throw new Error('Lizenztyp muss angegeben werden');
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Lizenzinformationen für Token ${tokenId}:`, licenseInfo);
      
      // Standardisiere Lizenzinformationen
      const standardizedLicense = {
        type: licenseInfo.type,
        name: licenseInfo.name || this.getLicenseName(licenseInfo.type),
        url: licenseInfo.url || this.getLicenseUrl(licenseInfo.type),
        commercial: licenseInfo.commercial !== undefined ? licenseInfo.commercial : true,
        derivatives: licenseInfo.derivatives !== undefined ? licenseInfo.derivatives : true,
        attribution: licenseInfo.attribution !== undefined ? licenseInfo.attribution : true
      };
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        license: standardizedLicense
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Lizenzinformationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Lizenzinformationen
   */
  async getLicenseInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Lizenzinformationen für Token ${tokenId} ab`);
      
      // Simulierte Lizenzinformationen
      return {
        tokenId,
        license: {
          type: 'cc-by',
          name: 'Creative Commons Attribution 4.0',
          url: 'https://creativecommons.org/licenses/by/4.0/',
          commercial: true,
          derivatives: true,
          attribution: true
        }
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Nutzung mit der Lizenz kompatibel ist
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} usage - Nutzungsinformationen
   * @returns {Promise<Object>} - Kompatibilitätsinformationen
   */
  async checkLicenseCompatibility(tokenId, usage) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Lizenzinformationen ab
      const licenseInfo = await this.getLicenseInfo(tokenId);
      
      // Prüfe Kompatibilität
      const isCommercialCompatible = !usage.commercial || licenseInfo.license.commercial;
      const isDerivativesCompatible = !usage.derivatives || licenseInfo.license.derivatives;
      const isAttributionCompatible = !licenseInfo.license.attribution || usage.attribution;
      
      const isCompatible = isCommercialCompatible && isDerivativesCompatible && isAttributionCompatible;
      
      return {
        tokenId,
        license: licenseInfo.license,
        usage,
        isCompatible,
        incompatibilities: {
          commercial: !isCommercialCompatible,
          derivatives: !isDerivativesCompatible,
          attribution: !isAttributionCompatible
        }
      };
    } catch (error) {
      console.error('Fehler bei der Prüfung der Lizenzkompatibilität:', error);
      throw error;
    }
  }
  
  /**
   * Gibt den Namen einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseName(type) {
    const licenseNames = {
      'cc-by': 'Creative Commons Attribution 4.0',
      'cc-by-sa': 'Creative Commons Attribution-ShareAlike 4.0',
      'cc-by-nc': 'Creative Commons Attribution-NonCommercial 4.0',
      'cc-by-nc-sa': 'Creative Commons Attribution-NonCommercial-ShareAlike 4.0',
      'cc-by-nd': 'Creative Commons Attribution-NoDerivatives 4.0',
      'cc-by-nc-nd': 'Creative Commons Attribution-NonCommercial-NoDerivatives 4.0',
      'cc0': 'Creative Commons Zero v1.0 Universal',
      'mit': 'MIT License',
      'apache-2.0': 'Apache License 2.0',
      'gpl-3.0': 'GNU General Public License v3.0'
    };
    
    return licenseNames[type] || 'Custom License';
  }
  
  /**
   * Gibt die URL einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseUrl(type) {
    const licenseUrls = {
      'cc-by': 'https://creativecommons.org/licenses/by/4.0/',
      'cc-by-sa': 'https://creativecommons.org/licenses/by-sa/4.0/',
      'cc-by-nc': 'https://creativecommons.org/licenses/by-nc/4.0/',
      'cc-by-nc-sa': 'https://creativecommons.org/licenses/by-nc-sa/4.0/',
      'cc-by-nd': 'https://creativecommons.org/licenses/by-nd/4.0/',
      'cc-by-nc-nd': 'https://creativecommons.org/licenses/by-nc-nd/4.0/',
      'cc0': 'https://creativecommons.org/publicdomain/zero/1.0/',
      'mit': 'https://opensource.org/licenses/MIT',
      'apache-2.0': 'https://opensource.org/licenses/Apache-2.0',
      'gpl-3.0': 'https://www.gnu.org/licenses/gpl-3.0.en.html'
    };
    
    return licenseUrls[type] || '';
  }
}

export default DoiRoyaltyContract;/**
 * Smart Contract für die Verwaltung von Lizenzierungen und Royalties für DOI-NFTs
 * Implementiert ERC-2981 für Royalties und erweiterte Lizenzierungsfunktionen
 */

class DoiRoyaltyContract {
  constructor(api, contractAddress, options = {}) {
    this.api = api;
    this.contractAddress = contractAddress;
    this.options = {
      defaultRoyaltyPercentage: 5, // 5% Standardroyalty
      maxRoyaltyPercentage: 15,    // Maximale Royalty von 15%
      ...options
    };
    
    // Initialisiere den Contract
    this.initialize();
  }
  
  /**
   * Initialisiert den Contract
   * @private
   */
  async initialize() {
    try {
      // In einer realen Implementierung würden wir hier den Contract ABI laden
      // und eine Verbindung zum Contract herstellen
      console.log('Initialisiere DOI Royalty Contract:', this.contractAddress);
      
      // Simuliere Contract-Initialisierung
      this.initialized = true;
    } catch (error) {
      console.error('Fehler bei der Initialisierung des Royalty Contracts:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Royalty-Informationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Array} recipients - Liste der Empfänger (Adressen)
   * @param {Array} shares - Liste der Anteile in Prozent
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setRoyaltyInfo(tokenId, recipients, shares) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Eingaben
    if (!Array.isArray(recipients) || !Array.isArray(shares)) {
      throw new Error('Empfänger und Anteile müssen Arrays sein');
    }
    
    if (recipients.length !== shares.length) {
      throw new Error('Anzahl der Empfänger und Anteile muss übereinstimmen');
    }
    
    // Prüfe, ob die Summe der Anteile 100% nicht überschreitet
    const totalShares = shares.reduce((sum, share) => sum + share, 0);
    if (totalShares > 100) {
      throw new Error('Summe der Anteile darf 100% nicht überschreiten');
    }
    
    // Prüfe, ob die Royalty-Rate das Maximum nicht überschreitet
    const royaltyRate = totalShares / 100 * this.options.defaultRoyaltyPercentage;
    if (royaltyRate > this.options.maxRoyaltyPercentage) {
      throw new Error(`Royalty-Rate darf ${this.options.maxRoyaltyPercentage}% nicht überschreiten`);
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Royalty-Informationen für Token ${tokenId}:`, {
        recipients,
        shares,
        royaltyRate: `${royaltyRate}%`
      });
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        recipients,
        shares,
        royaltyRate
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Royalty-Informationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Royalty-Informationen
   */
  async getRoyaltyInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Royalty-Informationen für Token ${tokenId} ab`);
      
      // Simulierte Royalty-Informationen
      return {
        tokenId,
        recipients: ['0x1234...', '0x5678...'],
        shares: [60, 40], // 60% und 40%
        royaltyRate: this.options.defaultRoyaltyPercentage,
        totalRoyalty: this.options.defaultRoyaltyPercentage
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Berechnet die Royalty-Zahlung für einen Verkauf
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Royalty-Zahlungsinformationen
   */
  async calculateRoyaltyPayment(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Royalty-Informationen ab
      const royaltyInfo = await this.getRoyaltyInfo(tokenId);
      
      // Berechne Royalty-Betrag
      const royaltyAmount = salePrice * (royaltyInfo.royaltyRate / 100);
      
      // Berechne Zahlungen pro Empfänger
      const payments = royaltyInfo.recipients.map((recipient, index) => ({
        recipient,
        share: royaltyInfo.shares[index],
        amount: royaltyAmount * (royaltyInfo.shares[index] / 100)
      }));
      
      return {
        tokenId,
        salePrice,
        royaltyRate: royaltyInfo.royaltyRate,
        totalRoyalty: royaltyAmount,
        payments
      };
    } catch (error) {
      console.error('Fehler bei der Berechnung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Führt eine Royalty-Zahlung für einen Verkauf durch
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async payRoyalties(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Berechne Royalty-Zahlungen
      const royaltyPayment = await this.calculateRoyaltyPayment(tokenId, salePrice);
      
      // In einer realen Implementierung würden wir hier die Zahlungen durchführen
      console.log(`Führe Royalty-Zahlungen für Token ${tokenId} durch:`, royaltyPayment);
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        salePrice,
        royaltyRate: royaltyPayment.royaltyRate,
        totalRoyalty: royaltyPayment.totalRoyalty,
        payments: royaltyPayment.payments
      };
    } catch (error) {
      console.error('Fehler bei der Durchführung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Lizenzinformationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} licenseInfo - Lizenzinformationen
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setLicenseInfo(tokenId, licenseInfo) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Lizenzinformationen
    if (!licenseInfo.type) {
      throw new Error('Lizenztyp muss angegeben werden');
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Lizenzinformationen für Token ${tokenId}:`, licenseInfo);
      
      // Standardisiere Lizenzinformationen
      const standardizedLicense = {
        type: licenseInfo.type,
        name: licenseInfo.name || this.getLicenseName(licenseInfo.type),
        url: licenseInfo.url || this.getLicenseUrl(licenseInfo.type),
        commercial: licenseInfo.commercial !== undefined ? licenseInfo.commercial : true,
        derivatives: licenseInfo.derivatives !== undefined ? licenseInfo.derivatives : true,
        attribution: licenseInfo.attribution !== undefined ? licenseInfo.attribution : true
      };
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        license: standardizedLicense
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Lizenzinformationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Lizenzinformationen
   */
  async getLicenseInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Lizenzinformationen für Token ${tokenId} ab`);
      
      // Simulierte Lizenzinformationen
      return {
        tokenId,
        license: {
          type: 'cc-by',
          name: 'Creative Commons Attribution 4.0',
          url: 'https://creativecommons.org/licenses/by/4.0/',
          commercial: true,
          derivatives: true,
          attribution: true
        }
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Nutzung mit der Lizenz kompatibel ist
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} usage - Nutzungsinformationen
   * @returns {Promise<Object>} - Kompatibilitätsinformationen
   */
  async checkLicenseCompatibility(tokenId, usage) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Lizenzinformationen ab
      const licenseInfo = await this.getLicenseInfo(tokenId);
      
      // Prüfe Kompatibilität
      const isCommercialCompatible = !usage.commercial || licenseInfo.license.commercial;
      const isDerivativesCompatible = !usage.derivatives || licenseInfo.license.derivatives;
      const isAttributionCompatible = !licenseInfo.license.attribution || usage.attribution;
      
      const isCompatible = isCommercialCompatible && isDerivativesCompatible && isAttributionCompatible;
      
      return {
        tokenId,
        license: licenseInfo.license,
        usage,
        isCompatible,
        incompatibilities: {
          commercial: !isCommercialCompatible,
          derivatives: !isDerivativesCompatible,
          attribution: !isAttributionCompatible
        }
      };
    } catch (error) {
      console.error('Fehler bei der Prüfung der Lizenzkompatibilität:', error);
      throw error;
    }
  }
  
  /**
   * Gibt den Namen einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseName(type) {
    const licenseNames = {
      'cc-by': 'Creative Commons Attribution 4.0',
      'cc-by-sa': 'Creative Commons Attribution-ShareAlike 4.0',
      'cc-by-nc': 'Creative Commons Attribution-NonCommercial 4.0',
      'cc-by-nc-sa': 'Creative Commons Attribution-NonCommercial-ShareAlike 4.0',
      'cc-by-nd': 'Creative Commons Attribution-NoDerivatives 4.0',
      'cc-by-nc-nd': 'Creative Commons Attribution-NonCommercial-NoDerivatives 4.0',
      'cc0': 'Creative Commons Zero v1.0 Universal',
      'mit': 'MIT License',
      'apache-2.0': 'Apache License 2.0',
      'gpl-3.0': 'GNU General Public License v3.0'
    };
    
    return licenseNames[type] || 'Custom License';
  }
  
  /**
   * Gibt die URL einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseUrl(type) {
    const licenseUrls = {
      'cc-by': 'https://creativecommons.org/licenses/by/4.0/',
      'cc-by-sa': 'https://creativecommons.org/licenses/by-sa/4.0/',
      'cc-by-nc': 'https://creativecommons.org/licenses/by-nc/4.0/',
      'cc-by-nc-sa': 'https://creativecommons.org/licenses/by-nc-sa/4.0/',
      'cc-by-nd': 'https://creativecommons.org/licenses/by-nd/4.0/',
      'cc-by-nc-nd': 'https://creativecommons.org/licenses/by-nc-nd/4.0/',
      'cc0': 'https://creativecommons.org/publicdomain/zero/1.0/',
      'mit': 'https://opensource.org/licenses/MIT',
      'apache-2.0': 'https://opensource.org/licenses/Apache-2.0',
      'gpl-3.0': 'https://www.gnu.org/licenses/gpl-3.0.en.html'
    };
    
    return licenseUrls[type] || '';
  }
}

export default DoiRoyaltyContract;/**
 * Smart Contract für die Verwaltung von Lizenzierungen und Royalties für DOI-NFTs
 * Implementiert ERC-2981 für Royalties und erweiterte Lizenzierungsfunktionen
 */

class DoiRoyaltyContract {
  constructor(api, contractAddress, options = {}) {
    this.api = api;
    this.contractAddress = contractAddress;
    this.options = {
      defaultRoyaltyPercentage: 5, // 5% Standardroyalty
      maxRoyaltyPercentage: 15,    // Maximale Royalty von 15%
      ...options
    };
    
    // Initialisiere den Contract
    this.initialize();
  }
  
  /**
   * Initialisiert den Contract
   * @private
   */
  async initialize() {
    try {
      // In einer realen Implementierung würden wir hier den Contract ABI laden
      // und eine Verbindung zum Contract herstellen
      console.log('Initialisiere DOI Royalty Contract:', this.contractAddress);
      
      // Simuliere Contract-Initialisierung
      this.initialized = true;
    } catch (error) {
      console.error('Fehler bei der Initialisierung des Royalty Contracts:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Royalty-Informationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Array} recipients - Liste der Empfänger (Adressen)
   * @param {Array} shares - Liste der Anteile in Prozent
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setRoyaltyInfo(tokenId, recipients, shares) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Eingaben
    if (!Array.isArray(recipients) || !Array.isArray(shares)) {
      throw new Error('Empfänger und Anteile müssen Arrays sein');
    }
    
    if (recipients.length !== shares.length) {
      throw new Error('Anzahl der Empfänger und Anteile muss übereinstimmen');
    }
    
    // Prüfe, ob die Summe der Anteile 100% nicht überschreitet
    const totalShares = shares.reduce((sum, share) => sum + share, 0);
    if (totalShares > 100) {
      throw new Error('Summe der Anteile darf 100% nicht überschreiten');
    }
    
    // Prüfe, ob die Royalty-Rate das Maximum nicht überschreitet
    const royaltyRate = totalShares / 100 * this.options.defaultRoyaltyPercentage;
    if (royaltyRate > this.options.maxRoyaltyPercentage) {
      throw new Error(`Royalty-Rate darf ${this.options.maxRoyaltyPercentage}% nicht überschreiten`);
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Royalty-Informationen für Token ${tokenId}:`, {
        recipients,
        shares,
        royaltyRate: `${royaltyRate}%`
      });
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        recipients,
        shares,
        royaltyRate
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Royalty-Informationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Royalty-Informationen
   */
  async getRoyaltyInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Royalty-Informationen für Token ${tokenId} ab`);
      
      // Simulierte Royalty-Informationen
      return {
        tokenId,
        recipients: ['0x1234...', '0x5678...'],
        shares: [60, 40], // 60% und 40%
        royaltyRate: this.options.defaultRoyaltyPercentage,
        totalRoyalty: this.options.defaultRoyaltyPercentage
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Berechnet die Royalty-Zahlung für einen Verkauf
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Royalty-Zahlungsinformationen
   */
  async calculateRoyaltyPayment(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Royalty-Informationen ab
      const royaltyInfo = await this.getRoyaltyInfo(tokenId);
      
      // Berechne Royalty-Betrag
      const royaltyAmount = salePrice * (royaltyInfo.royaltyRate / 100);
      
      // Berechne Zahlungen pro Empfänger
      const payments = royaltyInfo.recipients.map((recipient, index) => ({
        recipient,
        share: royaltyInfo.shares[index],
        amount: royaltyAmount * (royaltyInfo.shares[index] / 100)
      }));
      
      return {
        tokenId,
        salePrice,
        royaltyRate: royaltyInfo.royaltyRate,
        totalRoyalty: royaltyAmount,
        payments
      };
    } catch (error) {
      console.error('Fehler bei der Berechnung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Führt eine Royalty-Zahlung für einen Verkauf durch
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async payRoyalties(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Berechne Royalty-Zahlungen
      const royaltyPayment = await this.calculateRoyaltyPayment(tokenId, salePrice);
      
      // In einer realen Implementierung würden wir hier die Zahlungen durchführen
      console.log(`Führe Royalty-Zahlungen für Token ${tokenId} durch:`, royaltyPayment);
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        salePrice,
        royaltyRate: royaltyPayment.royaltyRate,
        totalRoyalty: royaltyPayment.totalRoyalty,
        payments: royaltyPayment.payments
      };
    } catch (error) {
      console.error('Fehler bei der Durchführung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Lizenzinformationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} licenseInfo - Lizenzinformationen
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setLicenseInfo(tokenId, licenseInfo) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Lizenzinformationen
    if (!licenseInfo.type) {
      throw new Error('Lizenztyp muss angegeben werden');
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Lizenzinformationen für Token ${tokenId}:`, licenseInfo);
      
      // Standardisiere Lizenzinformationen
      const standardizedLicense = {
        type: licenseInfo.type,
        name: licenseInfo.name || this.getLicenseName(licenseInfo.type),
        url: licenseInfo.url || this.getLicenseUrl(licenseInfo.type),
        commercial: licenseInfo.commercial !== undefined ? licenseInfo.commercial : true,
        derivatives: licenseInfo.derivatives !== undefined ? licenseInfo.derivatives : true,
        attribution: licenseInfo.attribution !== undefined ? licenseInfo.attribution : true
      };
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        license: standardizedLicense
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Lizenzinformationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Lizenzinformationen
   */
  async getLicenseInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Lizenzinformationen für Token ${tokenId} ab`);
      
      // Simulierte Lizenzinformationen
      return {
        tokenId,
        license: {
          type: 'cc-by',
          name: 'Creative Commons Attribution 4.0',
          url: 'https://creativecommons.org/licenses/by/4.0/',
          commercial: true,
          derivatives: true,
          attribution: true
        }
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Nutzung mit der Lizenz kompatibel ist
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} usage - Nutzungsinformationen
   * @returns {Promise<Object>} - Kompatibilitätsinformationen
   */
  async checkLicenseCompatibility(tokenId, usage) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Lizenzinformationen ab
      const licenseInfo = await this.getLicenseInfo(tokenId);
      
      // Prüfe Kompatibilität
      const isCommercialCompatible = !usage.commercial || licenseInfo.license.commercial;
      const isDerivativesCompatible = !usage.derivatives || licenseInfo.license.derivatives;
      const isAttributionCompatible = !licenseInfo.license.attribution || usage.attribution;
      
      const isCompatible = isCommercialCompatible && isDerivativesCompatible && isAttributionCompatible;
      
      return {
        tokenId,
        license: licenseInfo.license,
        usage,
        isCompatible,
        incompatibilities: {
          commercial: !isCommercialCompatible,
          derivatives: !isDerivativesCompatible,
          attribution: !isAttributionCompatible
        }
      };
    } catch (error) {
      console.error('Fehler bei der Prüfung der Lizenzkompatibilität:', error);
      throw error;
    }
  }
  
  /**
   * Gibt den Namen einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseName(type) {
    const licenseNames = {
      'cc-by': 'Creative Commons Attribution 4.0',
      'cc-by-sa': 'Creative Commons Attribution-ShareAlike 4.0',
      'cc-by-nc': 'Creative Commons Attribution-NonCommercial 4.0',
      'cc-by-nc-sa': 'Creative Commons Attribution-NonCommercial-ShareAlike 4.0',
      'cc-by-nd': 'Creative Commons Attribution-NoDerivatives 4.0',
      'cc-by-nc-nd': 'Creative Commons Attribution-NonCommercial-NoDerivatives 4.0',
      'cc0': 'Creative Commons Zero v1.0 Universal',
      'mit': 'MIT License',
      'apache-2.0': 'Apache License 2.0',
      'gpl-3.0': 'GNU General Public License v3.0'
    };
    
    return licenseNames[type] || 'Custom License';
  }
  
  /**
   * Gibt die URL einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseUrl(type) {
    const licenseUrls = {
      'cc-by': 'https://creativecommons.org/licenses/by/4.0/',
      'cc-by-sa': 'https://creativecommons.org/licenses/by-sa/4.0/',
      'cc-by-nc': 'https://creativecommons.org/licenses/by-nc/4.0/',
      'cc-by-nc-sa': 'https://creativecommons.org/licenses/by-nc-sa/4.0/',
      'cc-by-nd': 'https://creativecommons.org/licenses/by-nd/4.0/',
      'cc-by-nc-nd': 'https://creativecommons.org/licenses/by-nc-nd/4.0/',
      'cc0': 'https://creativecommons.org/publicdomain/zero/1.0/',
      'mit': 'https://opensource.org/licenses/MIT',
      'apache-2.0': 'https://opensource.org/licenses/Apache-2.0',
      'gpl-3.0': 'https://www.gnu.org/licenses/gpl-3.0.en.html'
    };
    
    return licenseUrls[type] || '';
  }
}

export default DoiRoyaltyContract;/**
 * Smart Contract für die Verwaltung von Lizenzierungen und Royalties für DOI-NFTs
 * Implementiert ERC-2981 für Royalties und erweiterte Lizenzierungsfunktionen
 */

class DoiRoyaltyContract {
  constructor(api, contractAddress, options = {}) {
    this.api = api;
    this.contractAddress = contractAddress;
    this.options = {
      defaultRoyaltyPercentage: 5, // 5% Standardroyalty
      maxRoyaltyPercentage: 15,    // Maximale Royalty von 15%
      ...options
    };
    
    // Initialisiere den Contract
    this.initialize();
  }
  
  /**
   * Initialisiert den Contract
   * @private
   */
  async initialize() {
    try {
      // In einer realen Implementierung würden wir hier den Contract ABI laden
      // und eine Verbindung zum Contract herstellen
      console.log('Initialisiere DOI Royalty Contract:', this.contractAddress);
      
      // Simuliere Contract-Initialisierung
      this.initialized = true;
    } catch (error) {
      console.error('Fehler bei der Initialisierung des Royalty Contracts:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Royalty-Informationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Array} recipients - Liste der Empfänger (Adressen)
   * @param {Array} shares - Liste der Anteile in Prozent
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setRoyaltyInfo(tokenId, recipients, shares) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Eingaben
    if (!Array.isArray(recipients) || !Array.isArray(shares)) {
      throw new Error('Empfänger und Anteile müssen Arrays sein');
    }
    
    if (recipients.length !== shares.length) {
      throw new Error('Anzahl der Empfänger und Anteile muss übereinstimmen');
    }
    
    // Prüfe, ob die Summe der Anteile 100% nicht überschreitet
    const totalShares = shares.reduce((sum, share) => sum + share, 0);
    if (totalShares > 100) {
      throw new Error('Summe der Anteile darf 100% nicht überschreiten');
    }
    
    // Prüfe, ob die Royalty-Rate das Maximum nicht überschreitet
    const royaltyRate = totalShares / 100 * this.options.defaultRoyaltyPercentage;
    if (royaltyRate > this.options.maxRoyaltyPercentage) {
      throw new Error(`Royalty-Rate darf ${this.options.maxRoyaltyPercentage}% nicht überschreiten`);
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Royalty-Informationen für Token ${tokenId}:`, {
        recipients,
        shares,
        royaltyRate: `${royaltyRate}%`
      });
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        recipients,
        shares,
        royaltyRate
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Royalty-Informationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Royalty-Informationen
   */
  async getRoyaltyInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Royalty-Informationen für Token ${tokenId} ab`);
      
      // Simulierte Royalty-Informationen
      return {
        tokenId,
        recipients: ['0x1234...', '0x5678...'],
        shares: [60, 40], // 60% und 40%
        royaltyRate: this.options.defaultRoyaltyPercentage,
        totalRoyalty: this.options.defaultRoyaltyPercentage
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Berechnet die Royalty-Zahlung für einen Verkauf
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Royalty-Zahlungsinformationen
   */
  async calculateRoyaltyPayment(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Royalty-Informationen ab
      const royaltyInfo = await this.getRoyaltyInfo(tokenId);
      
      // Berechne Royalty-Betrag
      const royaltyAmount = salePrice * (royaltyInfo.royaltyRate / 100);
      
      // Berechne Zahlungen pro Empfänger
      const payments = royaltyInfo.recipients.map((recipient, index) => ({
        recipient,
        share: royaltyInfo.shares[index],
        amount: royaltyAmount * (royaltyInfo.shares[index] / 100)
      }));
      
      return {
        tokenId,
        salePrice,
        royaltyRate: royaltyInfo.royaltyRate,
        totalRoyalty: royaltyAmount,
        payments
      };
    } catch (error) {
      console.error('Fehler bei der Berechnung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Führt eine Royalty-Zahlung für einen Verkauf durch
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async payRoyalties(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Berechne Royalty-Zahlungen
      const royaltyPayment = await this.calculateRoyaltyPayment(tokenId, salePrice);
      
      // In einer realen Implementierung würden wir hier die Zahlungen durchführen
      console.log(`Führe Royalty-Zahlungen für Token ${tokenId} durch:`, royaltyPayment);
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        salePrice,
        royaltyRate: royaltyPayment.royaltyRate,
        totalRoyalty: royaltyPayment.totalRoyalty,
        payments: royaltyPayment.payments
      };
    } catch (error) {
      console.error('Fehler bei der Durchführung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Lizenzinformationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} licenseInfo - Lizenzinformationen
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setLicenseInfo(tokenId, licenseInfo) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Lizenzinformationen
    if (!licenseInfo.type) {
      throw new Error('Lizenztyp muss angegeben werden');
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Lizenzinformationen für Token ${tokenId}:`, licenseInfo);
      
      // Standardisiere Lizenzinformationen
      const standardizedLicense = {
        type: licenseInfo.type,
        name: licenseInfo.name || this.getLicenseName(licenseInfo.type),
        url: licenseInfo.url || this.getLicenseUrl(licenseInfo.type),
        commercial: licenseInfo.commercial !== undefined ? licenseInfo.commercial : true,
        derivatives: licenseInfo.derivatives !== undefined ? licenseInfo.derivatives : true,
        attribution: licenseInfo.attribution !== undefined ? licenseInfo.attribution : true
      };
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        license: standardizedLicense
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Lizenzinformationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Lizenzinformationen
   */
  async getLicenseInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Lizenzinformationen für Token ${tokenId} ab`);
      
      // Simulierte Lizenzinformationen
      return {
        tokenId,
        license: {
          type: 'cc-by',
          name: 'Creative Commons Attribution 4.0',
          url: 'https://creativecommons.org/licenses/by/4.0/',
          commercial: true,
          derivatives: true,
          attribution: true
        }
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Nutzung mit der Lizenz kompatibel ist
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} usage - Nutzungsinformationen
   * @returns {Promise<Object>} - Kompatibilitätsinformationen
   */
  async checkLicenseCompatibility(tokenId, usage) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Lizenzinformationen ab
      const licenseInfo = await this.getLicenseInfo(tokenId);
      
      // Prüfe Kompatibilität
      const isCommercialCompatible = !usage.commercial || licenseInfo.license.commercial;
      const isDerivativesCompatible = !usage.derivatives || licenseInfo.license.derivatives;
      const isAttributionCompatible = !licenseInfo.license.attribution || usage.attribution;
      
      const isCompatible = isCommercialCompatible && isDerivativesCompatible && isAttributionCompatible;
      
      return {
        tokenId,
        license: licenseInfo.license,
        usage,
        isCompatible,
        incompatibilities: {
          commercial: !isCommercialCompatible,
          derivatives: !isDerivativesCompatible,
          attribution: !isAttributionCompatible
        }
      };
    } catch (error) {
      console.error('Fehler bei der Prüfung der Lizenzkompatibilität:', error);
      throw error;
    }
  }
  
  /**
   * Gibt den Namen einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseName(type) {
    const licenseNames = {
      'cc-by': 'Creative Commons Attribution 4.0',
      'cc-by-sa': 'Creative Commons Attribution-ShareAlike 4.0',
      'cc-by-nc': 'Creative Commons Attribution-NonCommercial 4.0',
      'cc-by-nc-sa': 'Creative Commons Attribution-NonCommercial-ShareAlike 4.0',
      'cc-by-nd': 'Creative Commons Attribution-NoDerivatives 4.0',
      'cc-by-nc-nd': 'Creative Commons Attribution-NonCommercial-NoDerivatives 4.0',
      'cc0': 'Creative Commons Zero v1.0 Universal',
      'mit': 'MIT License',
      'apache-2.0': 'Apache License 2.0',
      'gpl-3.0': 'GNU General Public License v3.0'
    };
    
    return licenseNames[type] || 'Custom License';
  }
  
  /**
   * Gibt die URL einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseUrl(type) {
    const licenseUrls = {
      'cc-by': 'https://creativecommons.org/licenses/by/4.0/',
      'cc-by-sa': 'https://creativecommons.org/licenses/by-sa/4.0/',
      'cc-by-nc': 'https://creativecommons.org/licenses/by-nc/4.0/',
      'cc-by-nc-sa': 'https://creativecommons.org/licenses/by-nc-sa/4.0/',
      'cc-by-nd': 'https://creativecommons.org/licenses/by-nd/4.0/',
      'cc-by-nc-nd': 'https://creativecommons.org/licenses/by-nc-nd/4.0/',
      'cc0': 'https://creativecommons.org/publicdomain/zero/1.0/',
      'mit': 'https://opensource.org/licenses/MIT',
      'apache-2.0': 'https://opensource.org/licenses/Apache-2.0',
      'gpl-3.0': 'https://www.gnu.org/licenses/gpl-3.0.en.html'
    };
    
    return licenseUrls[type] || '';
  }
}

export default DoiRoyaltyContract;/**
 * Smart Contract für die Verwaltung von Lizenzierungen und Royalties für DOI-NFTs
 * Implementiert ERC-2981 für Royalties und erweiterte Lizenzierungsfunktionen
 */

class DoiRoyaltyContract {
  constructor(api, contractAddress, options = {}) {
    this.api = api;
    this.contractAddress = contractAddress;
    this.options = {
      defaultRoyaltyPercentage: 5, // 5% Standardroyalty
      maxRoyaltyPercentage: 15,    // Maximale Royalty von 15%
      ...options
    };
    
    // Initialisiere den Contract
    this.initialize();
  }
  
  /**
   * Initialisiert den Contract
   * @private
   */
  async initialize() {
    try {
      // In einer realen Implementierung würden wir hier den Contract ABI laden
      // und eine Verbindung zum Contract herstellen
      console.log('Initialisiere DOI Royalty Contract:', this.contractAddress);
      
      // Simuliere Contract-Initialisierung
      this.initialized = true;
    } catch (error) {
      console.error('Fehler bei der Initialisierung des Royalty Contracts:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Royalty-Informationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Array} recipients - Liste der Empfänger (Adressen)
   * @param {Array} shares - Liste der Anteile in Prozent
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setRoyaltyInfo(tokenId, recipients, shares) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Eingaben
    if (!Array.isArray(recipients) || !Array.isArray(shares)) {
      throw new Error('Empfänger und Anteile müssen Arrays sein');
    }
    
    if (recipients.length !== shares.length) {
      throw new Error('Anzahl der Empfänger und Anteile muss übereinstimmen');
    }
    
    // Prüfe, ob die Summe der Anteile 100% nicht überschreitet
    const totalShares = shares.reduce((sum, share) => sum + share, 0);
    if (totalShares > 100) {
      throw new Error('Summe der Anteile darf 100% nicht überschreiten');
    }
    
    // Prüfe, ob die Royalty-Rate das Maximum nicht überschreitet
    const royaltyRate = totalShares / 100 * this.options.defaultRoyaltyPercentage;
    if (royaltyRate > this.options.maxRoyaltyPercentage) {
      throw new Error(`Royalty-Rate darf ${this.options.maxRoyaltyPercentage}% nicht überschreiten`);
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Royalty-Informationen für Token ${tokenId}:`, {
        recipients,
        shares,
        royaltyRate: `${royaltyRate}%`
      });
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        recipients,
        shares,
        royaltyRate
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Royalty-Informationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Royalty-Informationen
   */
  async getRoyaltyInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Royalty-Informationen für Token ${tokenId} ab`);
      
      // Simulierte Royalty-Informationen
      return {
        tokenId,
        recipients: ['0x1234...', '0x5678...'],
        shares: [60, 40], // 60% und 40%
        royaltyRate: this.options.defaultRoyaltyPercentage,
        totalRoyalty: this.options.defaultRoyaltyPercentage
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Berechnet die Royalty-Zahlung für einen Verkauf
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Royalty-Zahlungsinformationen
   */
  async calculateRoyaltyPayment(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Royalty-Informationen ab
      const royaltyInfo = await this.getRoyaltyInfo(tokenId);
      
      // Berechne Royalty-Betrag
      const royaltyAmount = salePrice * (royaltyInfo.royaltyRate / 100);
      
      // Berechne Zahlungen pro Empfänger
      const payments = royaltyInfo.recipients.map((recipient, index) => ({
        recipient,
        share: royaltyInfo.shares[index],
        amount: royaltyAmount * (royaltyInfo.shares[index] / 100)
      }));
      
      return {
        tokenId,
        salePrice,
        royaltyRate: royaltyInfo.royaltyRate,
        totalRoyalty: royaltyAmount,
        payments
      };
    } catch (error) {
      console.error('Fehler bei der Berechnung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Führt eine Royalty-Zahlung für einen Verkauf durch
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async payRoyalties(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Berechne Royalty-Zahlungen
      const royaltyPayment = await this.calculateRoyaltyPayment(tokenId, salePrice);
      
      // In einer realen Implementierung würden wir hier die Zahlungen durchführen
      console.log(`Führe Royalty-Zahlungen für Token ${tokenId} durch:`, royaltyPayment);
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        salePrice,
        royaltyRate: royaltyPayment.royaltyRate,
        totalRoyalty: royaltyPayment.totalRoyalty,
        payments: royaltyPayment.payments
      };
    } catch (error) {
      console.error('Fehler bei der Durchführung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Lizenzinformationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} licenseInfo - Lizenzinformationen
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setLicenseInfo(tokenId, licenseInfo) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Lizenzinformationen
    if (!licenseInfo.type) {
      throw new Error('Lizenztyp muss angegeben werden');
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Lizenzinformationen für Token ${tokenId}:`, licenseInfo);
      
      // Standardisiere Lizenzinformationen
      const standardizedLicense = {
        type: licenseInfo.type,
        name: licenseInfo.name || this.getLicenseName(licenseInfo.type),
        url: licenseInfo.url || this.getLicenseUrl(licenseInfo.type),
        commercial: licenseInfo.commercial !== undefined ? licenseInfo.commercial : true,
        derivatives: licenseInfo.derivatives !== undefined ? licenseInfo.derivatives : true,
        attribution: licenseInfo.attribution !== undefined ? licenseInfo.attribution : true
      };
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        license: standardizedLicense
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Lizenzinformationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Lizenzinformationen
   */
  async getLicenseInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Lizenzinformationen für Token ${tokenId} ab`);
      
      // Simulierte Lizenzinformationen
      return {
        tokenId,
        license: {
          type: 'cc-by',
          name: 'Creative Commons Attribution 4.0',
          url: 'https://creativecommons.org/licenses/by/4.0/',
          commercial: true,
          derivatives: true,
          attribution: true
        }
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Nutzung mit der Lizenz kompatibel ist
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} usage - Nutzungsinformationen
   * @returns {Promise<Object>} - Kompatibilitätsinformationen
   */
  async checkLicenseCompatibility(tokenId, usage) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Lizenzinformationen ab
      const licenseInfo = await this.getLicenseInfo(tokenId);
      
      // Prüfe Kompatibilität
      const isCommercialCompatible = !usage.commercial || licenseInfo.license.commercial;
      const isDerivativesCompatible = !usage.derivatives || licenseInfo.license.derivatives;
      const isAttributionCompatible = !licenseInfo.license.attribution || usage.attribution;
      
      const isCompatible = isCommercialCompatible && isDerivativesCompatible && isAttributionCompatible;
      
      return {
        tokenId,
        license: licenseInfo.license,
        usage,
        isCompatible,
        incompatibilities: {
          commercial: !isCommercialCompatible,
          derivatives: !isDerivativesCompatible,
          attribution: !isAttributionCompatible
        }
      };
    } catch (error) {
      console.error('Fehler bei der Prüfung der Lizenzkompatibilität:', error);
      throw error;
    }
  }
  
  /**
   * Gibt den Namen einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseName(type) {
    const licenseNames = {
      'cc-by': 'Creative Commons Attribution 4.0',
      'cc-by-sa': 'Creative Commons Attribution-ShareAlike 4.0',
      'cc-by-nc': 'Creative Commons Attribution-NonCommercial 4.0',
      'cc-by-nc-sa': 'Creative Commons Attribution-NonCommercial-ShareAlike 4.0',
      'cc-by-nd': 'Creative Commons Attribution-NoDerivatives 4.0',
      'cc-by-nc-nd': 'Creative Commons Attribution-NonCommercial-NoDerivatives 4.0',
      'cc0': 'Creative Commons Zero v1.0 Universal',
      'mit': 'MIT License',
      'apache-2.0': 'Apache License 2.0',
      'gpl-3.0': 'GNU General Public License v3.0'
    };
    
    return licenseNames[type] || 'Custom License';
  }
  
  /**
   * Gibt die URL einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseUrl(type) {
    const licenseUrls = {
      'cc-by': 'https://creativecommons.org/licenses/by/4.0/',
      'cc-by-sa': 'https://creativecommons.org/licenses/by-sa/4.0/',
      'cc-by-nc': 'https://creativecommons.org/licenses/by-nc/4.0/',
      'cc-by-nc-sa': 'https://creativecommons.org/licenses/by-nc-sa/4.0/',
      'cc-by-nd': 'https://creativecommons.org/licenses/by-nd/4.0/',
      'cc-by-nc-nd': 'https://creativecommons.org/licenses/by-nc-nd/4.0/',
      'cc0': 'https://creativecommons.org/publicdomain/zero/1.0/',
      'mit': 'https://opensource.org/licenses/MIT',
      'apache-2.0': 'https://opensource.org/licenses/Apache-2.0',
      'gpl-3.0': 'https://www.gnu.org/licenses/gpl-3.0.en.html'
    };
    
    return licenseUrls[type] || '';
  }
}

export default DoiRoyaltyContract;/**
 * Smart Contract für die Verwaltung von Lizenzierungen und Royalties für DOI-NFTs
 * Implementiert ERC-2981 für Royalties und erweiterte Lizenzierungsfunktionen
 */

class DoiRoyaltyContract {
  constructor(api, contractAddress, options = {}) {
    this.api = api;
    this.contractAddress = contractAddress;
    this.options = {
      defaultRoyaltyPercentage: 5, // 5% Standardroyalty
      maxRoyaltyPercentage: 15,    // Maximale Royalty von 15%
      ...options
    };
    
    // Initialisiere den Contract
    this.initialize();
  }
  
  /**
   * Initialisiert den Contract
   * @private
   */
  async initialize() {
    try {
      // In einer realen Implementierung würden wir hier den Contract ABI laden
      // und eine Verbindung zum Contract herstellen
      console.log('Initialisiere DOI Royalty Contract:', this.contractAddress);
      
      // Simuliere Contract-Initialisierung
      this.initialized = true;
    } catch (error) {
      console.error('Fehler bei der Initialisierung des Royalty Contracts:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Royalty-Informationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Array} recipients - Liste der Empfänger (Adressen)
   * @param {Array} shares - Liste der Anteile in Prozent
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setRoyaltyInfo(tokenId, recipients, shares) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Eingaben
    if (!Array.isArray(recipients) || !Array.isArray(shares)) {
      throw new Error('Empfänger und Anteile müssen Arrays sein');
    }
    
    if (recipients.length !== shares.length) {
      throw new Error('Anzahl der Empfänger und Anteile muss übereinstimmen');
    }
    
    // Prüfe, ob die Summe der Anteile 100% nicht überschreitet
    const totalShares = shares.reduce((sum, share) => sum + share, 0);
    if (totalShares > 100) {
      throw new Error('Summe der Anteile darf 100% nicht überschreiten');
    }
    
    // Prüfe, ob die Royalty-Rate das Maximum nicht überschreitet
    const royaltyRate = totalShares / 100 * this.options.defaultRoyaltyPercentage;
    if (royaltyRate > this.options.maxRoyaltyPercentage) {
      throw new Error(`Royalty-Rate darf ${this.options.maxRoyaltyPercentage}% nicht überschreiten`);
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Royalty-Informationen für Token ${tokenId}:`, {
        recipients,
        shares,
        royaltyRate: `${royaltyRate}%`
      });
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        recipients,
        shares,
        royaltyRate
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Royalty-Informationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Royalty-Informationen
   */
  async getRoyaltyInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Royalty-Informationen für Token ${tokenId} ab`);
      
      // Simulierte Royalty-Informationen
      return {
        tokenId,
        recipients: ['0x1234...', '0x5678...'],
        shares: [60, 40], // 60% und 40%
        royaltyRate: this.options.defaultRoyaltyPercentage,
        totalRoyalty: this.options.defaultRoyaltyPercentage
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Berechnet die Royalty-Zahlung für einen Verkauf
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Royalty-Zahlungsinformationen
   */
  async calculateRoyaltyPayment(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Royalty-Informationen ab
      const royaltyInfo = await this.getRoyaltyInfo(tokenId);
      
      // Berechne Royalty-Betrag
      const royaltyAmount = salePrice * (royaltyInfo.royaltyRate / 100);
      
      // Berechne Zahlungen pro Empfänger
      const payments = royaltyInfo.recipients.map((recipient, index) => ({
        recipient,
        share: royaltyInfo.shares[index],
        amount: royaltyAmount * (royaltyInfo.shares[index] / 100)
      }));
      
      return {
        tokenId,
        salePrice,
        royaltyRate: royaltyInfo.royaltyRate,
        totalRoyalty: royaltyAmount,
        payments
      };
    } catch (error) {
      console.error('Fehler bei der Berechnung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Führt eine Royalty-Zahlung für einen Verkauf durch
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async payRoyalties(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Berechne Royalty-Zahlungen
      const royaltyPayment = await this.calculateRoyaltyPayment(tokenId, salePrice);
      
      // In einer realen Implementierung würden wir hier die Zahlungen durchführen
      console.log(`Führe Royalty-Zahlungen für Token ${tokenId} durch:`, royaltyPayment);
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        salePrice,
        royaltyRate: royaltyPayment.royaltyRate,
        totalRoyalty: royaltyPayment.totalRoyalty,
        payments: royaltyPayment.payments
      };
    } catch (error) {
      console.error('Fehler bei der Durchführung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Lizenzinformationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} licenseInfo - Lizenzinformationen
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setLicenseInfo(tokenId, licenseInfo) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Lizenzinformationen
    if (!licenseInfo.type) {
      throw new Error('Lizenztyp muss angegeben werden');
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Lizenzinformationen für Token ${tokenId}:`, licenseInfo);
      
      // Standardisiere Lizenzinformationen
      const standardizedLicense = {
        type: licenseInfo.type,
        name: licenseInfo.name || this.getLicenseName(licenseInfo.type),
        url: licenseInfo.url || this.getLicenseUrl(licenseInfo.type),
        commercial: licenseInfo.commercial !== undefined ? licenseInfo.commercial : true,
        derivatives: licenseInfo.derivatives !== undefined ? licenseInfo.derivatives : true,
        attribution: licenseInfo.attribution !== undefined ? licenseInfo.attribution : true
      };
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        license: standardizedLicense
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Lizenzinformationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Lizenzinformationen
   */
  async getLicenseInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Lizenzinformationen für Token ${tokenId} ab`);
      
      // Simulierte Lizenzinformationen
      return {
        tokenId,
        license: {
          type: 'cc-by',
          name: 'Creative Commons Attribution 4.0',
          url: 'https://creativecommons.org/licenses/by/4.0/',
          commercial: true,
          derivatives: true,
          attribution: true
        }
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Nutzung mit der Lizenz kompatibel ist
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} usage - Nutzungsinformationen
   * @returns {Promise<Object>} - Kompatibilitätsinformationen
   */
  async checkLicenseCompatibility(tokenId, usage) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Lizenzinformationen ab
      const licenseInfo = await this.getLicenseInfo(tokenId);
      
      // Prüfe Kompatibilität
      const isCommercialCompatible = !usage.commercial || licenseInfo.license.commercial;
      const isDerivativesCompatible = !usage.derivatives || licenseInfo.license.derivatives;
      const isAttributionCompatible = !licenseInfo.license.attribution || usage.attribution;
      
      const isCompatible = isCommercialCompatible && isDerivativesCompatible && isAttributionCompatible;
      
      return {
        tokenId,
        license: licenseInfo.license,
        usage,
        isCompatible,
        incompatibilities: {
          commercial: !isCommercialCompatible,
          derivatives: !isDerivativesCompatible,
          attribution: !isAttributionCompatible
        }
      };
    } catch (error) {
      console.error('Fehler bei der Prüfung der Lizenzkompatibilität:', error);
      throw error;
    }
  }
  
  /**
   * Gibt den Namen einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseName(type) {
    const licenseNames = {
      'cc-by': 'Creative Commons Attribution 4.0',
      'cc-by-sa': 'Creative Commons Attribution-ShareAlike 4.0',
      'cc-by-nc': 'Creative Commons Attribution-NonCommercial 4.0',
      'cc-by-nc-sa': 'Creative Commons Attribution-NonCommercial-ShareAlike 4.0',
      'cc-by-nd': 'Creative Commons Attribution-NoDerivatives 4.0',
      'cc-by-nc-nd': 'Creative Commons Attribution-NonCommercial-NoDerivatives 4.0',
      'cc0': 'Creative Commons Zero v1.0 Universal',
      'mit': 'MIT License',
      'apache-2.0': 'Apache License 2.0',
      'gpl-3.0': 'GNU General Public License v3.0'
    };
    
    return licenseNames[type] || 'Custom License';
  }
  
  /**
   * Gibt die URL einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseUrl(type) {
    const licenseUrls = {
      'cc-by': 'https://creativecommons.org/licenses/by/4.0/',
      'cc-by-sa': 'https://creativecommons.org/licenses/by-sa/4.0/',
      'cc-by-nc': 'https://creativecommons.org/licenses/by-nc/4.0/',
      'cc-by-nc-sa': 'https://creativecommons.org/licenses/by-nc-sa/4.0/',
      'cc-by-nd': 'https://creativecommons.org/licenses/by-nd/4.0/',
      'cc-by-nc-nd': 'https://creativecommons.org/licenses/by-nc-nd/4.0/',
      'cc0': 'https://creativecommons.org/publicdomain/zero/1.0/',
      'mit': 'https://opensource.org/licenses/MIT',
      'apache-2.0': 'https://opensource.org/licenses/Apache-2.0',
      'gpl-3.0': 'https://www.gnu.org/licenses/gpl-3.0.en.html'
    };
    
    return licenseUrls[type] || '';
  }
}

export default DoiRoyaltyContract;/**
 * Smart Contract für die Verwaltung von Lizenzierungen und Royalties für DOI-NFTs
 * Implementiert ERC-2981 für Royalties und erweiterte Lizenzierungsfunktionen
 */

class DoiRoyaltyContract {
  constructor(api, contractAddress, options = {}) {
    this.api = api;
    this.contractAddress = contractAddress;
    this.options = {
      defaultRoyaltyPercentage: 5, // 5% Standardroyalty
      maxRoyaltyPercentage: 15,    // Maximale Royalty von 15%
      ...options
    };
    
    // Initialisiere den Contract
    this.initialize();
  }
  
  /**
   * Initialisiert den Contract
   * @private
   */
  async initialize() {
    try {
      // In einer realen Implementierung würden wir hier den Contract ABI laden
      // und eine Verbindung zum Contract herstellen
      console.log('Initialisiere DOI Royalty Contract:', this.contractAddress);
      
      // Simuliere Contract-Initialisierung
      this.initialized = true;
    } catch (error) {
      console.error('Fehler bei der Initialisierung des Royalty Contracts:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Royalty-Informationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Array} recipients - Liste der Empfänger (Adressen)
   * @param {Array} shares - Liste der Anteile in Prozent
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setRoyaltyInfo(tokenId, recipients, shares) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Eingaben
    if (!Array.isArray(recipients) || !Array.isArray(shares)) {
      throw new Error('Empfänger und Anteile müssen Arrays sein');
    }
    
    if (recipients.length !== shares.length) {
      throw new Error('Anzahl der Empfänger und Anteile muss übereinstimmen');
    }
    
    // Prüfe, ob die Summe der Anteile 100% nicht überschreitet
    const totalShares = shares.reduce((sum, share) => sum + share, 0);
    if (totalShares > 100) {
      throw new Error('Summe der Anteile darf 100% nicht überschreiten');
    }
    
    // Prüfe, ob die Royalty-Rate das Maximum nicht überschreitet
    const royaltyRate = totalShares / 100 * this.options.defaultRoyaltyPercentage;
    if (royaltyRate > this.options.maxRoyaltyPercentage) {
      throw new Error(`Royalty-Rate darf ${this.options.maxRoyaltyPercentage}% nicht überschreiten`);
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Royalty-Informationen für Token ${tokenId}:`, {
        recipients,
        shares,
        royaltyRate: `${royaltyRate}%`
      });
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        recipients,
        shares,
        royaltyRate
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Royalty-Informationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Royalty-Informationen
   */
  async getRoyaltyInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Royalty-Informationen für Token ${tokenId} ab`);
      
      // Simulierte Royalty-Informationen
      return {
        tokenId,
        recipients: ['0x1234...', '0x5678...'],
        shares: [60, 40], // 60% und 40%
        royaltyRate: this.options.defaultRoyaltyPercentage,
        totalRoyalty: this.options.defaultRoyaltyPercentage
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Berechnet die Royalty-Zahlung für einen Verkauf
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Royalty-Zahlungsinformationen
   */
  async calculateRoyaltyPayment(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Royalty-Informationen ab
      const royaltyInfo = await this.getRoyaltyInfo(tokenId);
      
      // Berechne Royalty-Betrag
      const royaltyAmount = salePrice * (royaltyInfo.royaltyRate / 100);
      
      // Berechne Zahlungen pro Empfänger
      const payments = royaltyInfo.recipients.map((recipient, index) => ({
        recipient,
        share: royaltyInfo.shares[index],
        amount: royaltyAmount * (royaltyInfo.shares[index] / 100)
      }));
      
      return {
        tokenId,
        salePrice,
        royaltyRate: royaltyInfo.royaltyRate,
        totalRoyalty: royaltyAmount,
        payments
      };
    } catch (error) {
      console.error('Fehler bei der Berechnung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Führt eine Royalty-Zahlung für einen Verkauf durch
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async payRoyalties(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Berechne Royalty-Zahlungen
      const royaltyPayment = await this.calculateRoyaltyPayment(tokenId, salePrice);
      
      // In einer realen Implementierung würden wir hier die Zahlungen durchführen
      console.log(`Führe Royalty-Zahlungen für Token ${tokenId} durch:`, royaltyPayment);
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        salePrice,
        royaltyRate: royaltyPayment.royaltyRate,
        totalRoyalty: royaltyPayment.totalRoyalty,
        payments: royaltyPayment.payments
      };
    } catch (error) {
      console.error('Fehler bei der Durchführung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Lizenzinformationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} licenseInfo - Lizenzinformationen
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setLicenseInfo(tokenId, licenseInfo) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Lizenzinformationen
    if (!licenseInfo.type) {
      throw new Error('Lizenztyp muss angegeben werden');
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Lizenzinformationen für Token ${tokenId}:`, licenseInfo);
      
      // Standardisiere Lizenzinformationen
      const standardizedLicense = {
        type: licenseInfo.type,
        name: licenseInfo.name || this.getLicenseName(licenseInfo.type),
        url: licenseInfo.url || this.getLicenseUrl(licenseInfo.type),
        commercial: licenseInfo.commercial !== undefined ? licenseInfo.commercial : true,
        derivatives: licenseInfo.derivatives !== undefined ? licenseInfo.derivatives : true,
        attribution: licenseInfo.attribution !== undefined ? licenseInfo.attribution : true
      };
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        license: standardizedLicense
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Lizenzinformationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Lizenzinformationen
   */
  async getLicenseInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Lizenzinformationen für Token ${tokenId} ab`);
      
      // Simulierte Lizenzinformationen
      return {
        tokenId,
        license: {
          type: 'cc-by',
          name: 'Creative Commons Attribution 4.0',
          url: 'https://creativecommons.org/licenses/by/4.0/',
          commercial: true,
          derivatives: true,
          attribution: true
        }
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Nutzung mit der Lizenz kompatibel ist
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} usage - Nutzungsinformationen
   * @returns {Promise<Object>} - Kompatibilitätsinformationen
   */
  async checkLicenseCompatibility(tokenId, usage) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Lizenzinformationen ab
      const licenseInfo = await this.getLicenseInfo(tokenId);
      
      // Prüfe Kompatibilität
      const isCommercialCompatible = !usage.commercial || licenseInfo.license.commercial;
      const isDerivativesCompatible = !usage.derivatives || licenseInfo.license.derivatives;
      const isAttributionCompatible = !licenseInfo.license.attribution || usage.attribution;
      
      const isCompatible = isCommercialCompatible && isDerivativesCompatible && isAttributionCompatible;
      
      return {
        tokenId,
        license: licenseInfo.license,
        usage,
        isCompatible,
        incompatibilities: {
          commercial: !isCommercialCompatible,
          derivatives: !isDerivativesCompatible,
          attribution: !isAttributionCompatible
        }
      };
    } catch (error) {
      console.error('Fehler bei der Prüfung der Lizenzkompatibilität:', error);
      throw error;
    }
  }
  
  /**
   * Gibt den Namen einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseName(type) {
    const licenseNames = {
      'cc-by': 'Creative Commons Attribution 4.0',
      'cc-by-sa': 'Creative Commons Attribution-ShareAlike 4.0',
      'cc-by-nc': 'Creative Commons Attribution-NonCommercial 4.0',
      'cc-by-nc-sa': 'Creative Commons Attribution-NonCommercial-ShareAlike 4.0',
      'cc-by-nd': 'Creative Commons Attribution-NoDerivatives 4.0',
      'cc-by-nc-nd': 'Creative Commons Attribution-NonCommercial-NoDerivatives 4.0',
      'cc0': 'Creative Commons Zero v1.0 Universal',
      'mit': 'MIT License',
      'apache-2.0': 'Apache License 2.0',
      'gpl-3.0': 'GNU General Public License v3.0'
    };
    
    return licenseNames[type] || 'Custom License';
  }
  
  /**
   * Gibt die URL einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseUrl(type) {
    const licenseUrls = {
      'cc-by': 'https://creativecommons.org/licenses/by/4.0/',
      'cc-by-sa': 'https://creativecommons.org/licenses/by-sa/4.0/',
      'cc-by-nc': 'https://creativecommons.org/licenses/by-nc/4.0/',
      'cc-by-nc-sa': 'https://creativecommons.org/licenses/by-nc-sa/4.0/',
      'cc-by-nd': 'https://creativecommons.org/licenses/by-nd/4.0/',
      'cc-by-nc-nd': 'https://creativecommons.org/licenses/by-nc-nd/4.0/',
      'cc0': 'https://creativecommons.org/publicdomain/zero/1.0/',
      'mit': 'https://opensource.org/licenses/MIT',
      'apache-2.0': 'https://opensource.org/licenses/Apache-2.0',
      'gpl-3.0': 'https://www.gnu.org/licenses/gpl-3.0.en.html'
    };
    
    return licenseUrls[type] || '';
  }
}

export default DoiRoyaltyContract;/**
 * Smart Contract für die Verwaltung von Lizenzierungen und Royalties für DOI-NFTs
 * Implementiert ERC-2981 für Royalties und erweiterte Lizenzierungsfunktionen
 */

class DoiRoyaltyContract {
  constructor(api, contractAddress, options = {}) {
    this.api = api;
    this.contractAddress = contractAddress;
    this.options = {
      defaultRoyaltyPercentage: 5, // 5% Standardroyalty
      maxRoyaltyPercentage: 15,    // Maximale Royalty von 15%
      ...options
    };
    
    // Initialisiere den Contract
    this.initialize();
  }
  
  /**
   * Initialisiert den Contract
   * @private
   */
  async initialize() {
    try {
      // In einer realen Implementierung würden wir hier den Contract ABI laden
      // und eine Verbindung zum Contract herstellen
      console.log('Initialisiere DOI Royalty Contract:', this.contractAddress);
      
      // Simuliere Contract-Initialisierung
      this.initialized = true;
    } catch (error) {
      console.error('Fehler bei der Initialisierung des Royalty Contracts:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Royalty-Informationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Array} recipients - Liste der Empfänger (Adressen)
   * @param {Array} shares - Liste der Anteile in Prozent
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setRoyaltyInfo(tokenId, recipients, shares) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Eingaben
    if (!Array.isArray(recipients) || !Array.isArray(shares)) {
      throw new Error('Empfänger und Anteile müssen Arrays sein');
    }
    
    if (recipients.length !== shares.length) {
      throw new Error('Anzahl der Empfänger und Anteile muss übereinstimmen');
    }
    
    // Prüfe, ob die Summe der Anteile 100% nicht überschreitet
    const totalShares = shares.reduce((sum, share) => sum + share, 0);
    if (totalShares > 100) {
      throw new Error('Summe der Anteile darf 100% nicht überschreiten');
    }
    
    // Prüfe, ob die Royalty-Rate das Maximum nicht überschreitet
    const royaltyRate = totalShares / 100 * this.options.defaultRoyaltyPercentage;
    if (royaltyRate > this.options.maxRoyaltyPercentage) {
      throw new Error(`Royalty-Rate darf ${this.options.maxRoyaltyPercentage}% nicht überschreiten`);
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Royalty-Informationen für Token ${tokenId}:`, {
        recipients,
        shares,
        royaltyRate: `${royaltyRate}%`
      });
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        recipients,
        shares,
        royaltyRate
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Royalty-Informationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Royalty-Informationen
   */
  async getRoyaltyInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Royalty-Informationen für Token ${tokenId} ab`);
      
      // Simulierte Royalty-Informationen
      return {
        tokenId,
        recipients: ['0x1234...', '0x5678...'],
        shares: [60, 40], // 60% und 40%
        royaltyRate: this.options.defaultRoyaltyPercentage,
        totalRoyalty: this.options.defaultRoyaltyPercentage
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Berechnet die Royalty-Zahlung für einen Verkauf
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Royalty-Zahlungsinformationen
   */
  async calculateRoyaltyPayment(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Royalty-Informationen ab
      const royaltyInfo = await this.getRoyaltyInfo(tokenId);
      
      // Berechne Royalty-Betrag
      const royaltyAmount = salePrice * (royaltyInfo.royaltyRate / 100);
      
      // Berechne Zahlungen pro Empfänger
      const payments = royaltyInfo.recipients.map((recipient, index) => ({
        recipient,
        share: royaltyInfo.shares[index],
        amount: royaltyAmount * (royaltyInfo.shares[index] / 100)
      }));
      
      return {
        tokenId,
        salePrice,
        royaltyRate: royaltyInfo.royaltyRate,
        totalRoyalty: royaltyAmount,
        payments
      };
    } catch (error) {
      console.error('Fehler bei der Berechnung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Führt eine Royalty-Zahlung für einen Verkauf durch
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async payRoyalties(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Berechne Royalty-Zahlungen
      const royaltyPayment = await this.calculateRoyaltyPayment(tokenId, salePrice);
      
      // In einer realen Implementierung würden wir hier die Zahlungen durchführen
      console.log(`Führe Royalty-Zahlungen für Token ${tokenId} durch:`, royaltyPayment);
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        salePrice,
        royaltyRate: royaltyPayment.royaltyRate,
        totalRoyalty: royaltyPayment.totalRoyalty,
        payments: royaltyPayment.payments
      };
    } catch (error) {
      console.error('Fehler bei der Durchführung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Lizenzinformationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} licenseInfo - Lizenzinformationen
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setLicenseInfo(tokenId, licenseInfo) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Lizenzinformationen
    if (!licenseInfo.type) {
      throw new Error('Lizenztyp muss angegeben werden');
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Lizenzinformationen für Token ${tokenId}:`, licenseInfo);
      
      // Standardisiere Lizenzinformationen
      const standardizedLicense = {
        type: licenseInfo.type,
        name: licenseInfo.name || this.getLicenseName(licenseInfo.type),
        url: licenseInfo.url || this.getLicenseUrl(licenseInfo.type),
        commercial: licenseInfo.commercial !== undefined ? licenseInfo.commercial : true,
        derivatives: licenseInfo.derivatives !== undefined ? licenseInfo.derivatives : true,
        attribution: licenseInfo.attribution !== undefined ? licenseInfo.attribution : true
      };
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        license: standardizedLicense
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Lizenzinformationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Lizenzinformationen
   */
  async getLicenseInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Lizenzinformationen für Token ${tokenId} ab`);
      
      // Simulierte Lizenzinformationen
      return {
        tokenId,
        license: {
          type: 'cc-by',
          name: 'Creative Commons Attribution 4.0',
          url: 'https://creativecommons.org/licenses/by/4.0/',
          commercial: true,
          derivatives: true,
          attribution: true
        }
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Nutzung mit der Lizenz kompatibel ist
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} usage - Nutzungsinformationen
   * @returns {Promise<Object>} - Kompatibilitätsinformationen
   */
  async checkLicenseCompatibility(tokenId, usage) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Lizenzinformationen ab
      const licenseInfo = await this.getLicenseInfo(tokenId);
      
      // Prüfe Kompatibilität
      const isCommercialCompatible = !usage.commercial || licenseInfo.license.commercial;
      const isDerivativesCompatible = !usage.derivatives || licenseInfo.license.derivatives;
      const isAttributionCompatible = !licenseInfo.license.attribution || usage.attribution;
      
      const isCompatible = isCommercialCompatible && isDerivativesCompatible && isAttributionCompatible;
      
      return {
        tokenId,
        license: licenseInfo.license,
        usage,
        isCompatible,
        incompatibilities: {
          commercial: !isCommercialCompatible,
          derivatives: !isDerivativesCompatible,
          attribution: !isAttributionCompatible
        }
      };
    } catch (error) {
      console.error('Fehler bei der Prüfung der Lizenzkompatibilität:', error);
      throw error;
    }
  }
  
  /**
   * Gibt den Namen einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseName(type) {
    const licenseNames = {
      'cc-by': 'Creative Commons Attribution 4.0',
      'cc-by-sa': 'Creative Commons Attribution-ShareAlike 4.0',
      'cc-by-nc': 'Creative Commons Attribution-NonCommercial 4.0',
      'cc-by-nc-sa': 'Creative Commons Attribution-NonCommercial-ShareAlike 4.0',
      'cc-by-nd': 'Creative Commons Attribution-NoDerivatives 4.0',
      'cc-by-nc-nd': 'Creative Commons Attribution-NonCommercial-NoDerivatives 4.0',
      'cc0': 'Creative Commons Zero v1.0 Universal',
      'mit': 'MIT License',
      'apache-2.0': 'Apache License 2.0',
      'gpl-3.0': 'GNU General Public License v3.0'
    };
    
    return licenseNames[type] || 'Custom License';
  }
  
  /**
   * Gibt die URL einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseUrl(type) {
    const licenseUrls = {
      'cc-by': 'https://creativecommons.org/licenses/by/4.0/',
      'cc-by-sa': 'https://creativecommons.org/licenses/by-sa/4.0/',
      'cc-by-nc': 'https://creativecommons.org/licenses/by-nc/4.0/',
      'cc-by-nc-sa': 'https://creativecommons.org/licenses/by-nc-sa/4.0/',
      'cc-by-nd': 'https://creativecommons.org/licenses/by-nd/4.0/',
      'cc-by-nc-nd': 'https://creativecommons.org/licenses/by-nc-nd/4.0/',
      'cc0': 'https://creativecommons.org/publicdomain/zero/1.0/',
      'mit': 'https://opensource.org/licenses/MIT',
      'apache-2.0': 'https://opensource.org/licenses/Apache-2.0',
      'gpl-3.0': 'https://www.gnu.org/licenses/gpl-3.0.en.html'
    };
    
    return licenseUrls[type] || '';
  }
}

export default DoiRoyaltyContract;/**
 * Smart Contract für die Verwaltung von Lizenzierungen und Royalties für DOI-NFTs
 * Implementiert ERC-2981 für Royalties und erweiterte Lizenzierungsfunktionen
 */

class DoiRoyaltyContract {
  constructor(api, contractAddress, options = {}) {
    this.api = api;
    this.contractAddress = contractAddress;
    this.options = {
      defaultRoyaltyPercentage: 5, // 5% Standardroyalty
      maxRoyaltyPercentage: 15,    // Maximale Royalty von 15%
      ...options
    };
    
    // Initialisiere den Contract
    this.initialize();
  }
  
  /**
   * Initialisiert den Contract
   * @private
   */
  async initialize() {
    try {
      // In einer realen Implementierung würden wir hier den Contract ABI laden
      // und eine Verbindung zum Contract herstellen
      console.log('Initialisiere DOI Royalty Contract:', this.contractAddress);
      
      // Simuliere Contract-Initialisierung
      this.initialized = true;
    } catch (error) {
      console.error('Fehler bei der Initialisierung des Royalty Contracts:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Royalty-Informationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Array} recipients - Liste der Empfänger (Adressen)
   * @param {Array} shares - Liste der Anteile in Prozent
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setRoyaltyInfo(tokenId, recipients, shares) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Eingaben
    if (!Array.isArray(recipients) || !Array.isArray(shares)) {
      throw new Error('Empfänger und Anteile müssen Arrays sein');
    }
    
    if (recipients.length !== shares.length) {
      throw new Error('Anzahl der Empfänger und Anteile muss übereinstimmen');
    }
    
    // Prüfe, ob die Summe der Anteile 100% nicht überschreitet
    const totalShares = shares.reduce((sum, share) => sum + share, 0);
    if (totalShares > 100) {
      throw new Error('Summe der Anteile darf 100% nicht überschreiten');
    }
    
    // Prüfe, ob die Royalty-Rate das Maximum nicht überschreitet
    const royaltyRate = totalShares / 100 * this.options.defaultRoyaltyPercentage;
    if (royaltyRate > this.options.maxRoyaltyPercentage) {
      throw new Error(`Royalty-Rate darf ${this.options.maxRoyaltyPercentage}% nicht überschreiten`);
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Royalty-Informationen für Token ${tokenId}:`, {
        recipients,
        shares,
        royaltyRate: `${royaltyRate}%`
      });
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        recipients,
        shares,
        royaltyRate
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Royalty-Informationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Royalty-Informationen
   */
  async getRoyaltyInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Royalty-Informationen für Token ${tokenId} ab`);
      
      // Simulierte Royalty-Informationen
      return {
        tokenId,
        recipients: ['0x1234...', '0x5678...'],
        shares: [60, 40], // 60% und 40%
        royaltyRate: this.options.defaultRoyaltyPercentage,
        totalRoyalty: this.options.defaultRoyaltyPercentage
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Berechnet die Royalty-Zahlung für einen Verkauf
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Royalty-Zahlungsinformationen
   */
  async calculateRoyaltyPayment(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Royalty-Informationen ab
      const royaltyInfo = await this.getRoyaltyInfo(tokenId);
      
      // Berechne Royalty-Betrag
      const royaltyAmount = salePrice * (royaltyInfo.royaltyRate / 100);
      
      // Berechne Zahlungen pro Empfänger
      const payments = royaltyInfo.recipients.map((recipient, index) => ({
        recipient,
        share: royaltyInfo.shares[index],
        amount: royaltyAmount * (royaltyInfo.shares[index] / 100)
      }));
      
      return {
        tokenId,
        salePrice,
        royaltyRate: royaltyInfo.royaltyRate,
        totalRoyalty: royaltyAmount,
        payments
      };
    } catch (error) {
      console.error('Fehler bei der Berechnung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Führt eine Royalty-Zahlung für einen Verkauf durch
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async payRoyalties(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Berechne Royalty-Zahlungen
      const royaltyPayment = await this.calculateRoyaltyPayment(tokenId, salePrice);
      
      // In einer realen Implementierung würden wir hier die Zahlungen durchführen
      console.log(`Führe Royalty-Zahlungen für Token ${tokenId} durch:`, royaltyPayment);
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        salePrice,
        royaltyRate: royaltyPayment.royaltyRate,
        totalRoyalty: royaltyPayment.totalRoyalty,
        payments: royaltyPayment.payments
      };
    } catch (error) {
      console.error('Fehler bei der Durchführung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Lizenzinformationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} licenseInfo - Lizenzinformationen
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setLicenseInfo(tokenId, licenseInfo) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Lizenzinformationen
    if (!licenseInfo.type) {
      throw new Error('Lizenztyp muss angegeben werden');
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Lizenzinformationen für Token ${tokenId}:`, licenseInfo);
      
      // Standardisiere Lizenzinformationen
      const standardizedLicense = {
        type: licenseInfo.type,
        name: licenseInfo.name || this.getLicenseName(licenseInfo.type),
        url: licenseInfo.url || this.getLicenseUrl(licenseInfo.type),
        commercial: licenseInfo.commercial !== undefined ? licenseInfo.commercial : true,
        derivatives: licenseInfo.derivatives !== undefined ? licenseInfo.derivatives : true,
        attribution: licenseInfo.attribution !== undefined ? licenseInfo.attribution : true
      };
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        license: standardizedLicense
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Lizenzinformationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Lizenzinformationen
   */
  async getLicenseInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Lizenzinformationen für Token ${tokenId} ab`);
      
      // Simulierte Lizenzinformationen
      return {
        tokenId,
        license: {
          type: 'cc-by',
          name: 'Creative Commons Attribution 4.0',
          url: 'https://creativecommons.org/licenses/by/4.0/',
          commercial: true,
          derivatives: true,
          attribution: true
        }
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Nutzung mit der Lizenz kompatibel ist
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} usage - Nutzungsinformationen
   * @returns {Promise<Object>} - Kompatibilitätsinformationen
   */
  async checkLicenseCompatibility(tokenId, usage) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Lizenzinformationen ab
      const licenseInfo = await this.getLicenseInfo(tokenId);
      
      // Prüfe Kompatibilität
      const isCommercialCompatible = !usage.commercial || licenseInfo.license.commercial;
      const isDerivativesCompatible = !usage.derivatives || licenseInfo.license.derivatives;
      const isAttributionCompatible = !licenseInfo.license.attribution || usage.attribution;
      
      const isCompatible = isCommercialCompatible && isDerivativesCompatible && isAttributionCompatible;
      
      return {
        tokenId,
        license: licenseInfo.license,
        usage,
        isCompatible,
        incompatibilities: {
          commercial: !isCommercialCompatible,
          derivatives: !isDerivativesCompatible,
          attribution: !isAttributionCompatible
        }
      };
    } catch (error) {
      console.error('Fehler bei der Prüfung der Lizenzkompatibilität:', error);
      throw error;
    }
  }
  
  /**
   * Gibt den Namen einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseName(type) {
    const licenseNames = {
      'cc-by': 'Creative Commons Attribution 4.0',
      'cc-by-sa': 'Creative Commons Attribution-ShareAlike 4.0',
      'cc-by-nc': 'Creative Commons Attribution-NonCommercial 4.0',
      'cc-by-nc-sa': 'Creative Commons Attribution-NonCommercial-ShareAlike 4.0',
      'cc-by-nd': 'Creative Commons Attribution-NoDerivatives 4.0',
      'cc-by-nc-nd': 'Creative Commons Attribution-NonCommercial-NoDerivatives 4.0',
      'cc0': 'Creative Commons Zero v1.0 Universal',
      'mit': 'MIT License',
      'apache-2.0': 'Apache License 2.0',
      'gpl-3.0': 'GNU General Public License v3.0'
    };
    
    return licenseNames[type] || 'Custom License';
  }
  
  /**
   * Gibt die URL einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseUrl(type) {
    const licenseUrls = {
      'cc-by': 'https://creativecommons.org/licenses/by/4.0/',
      'cc-by-sa': 'https://creativecommons.org/licenses/by-sa/4.0/',
      'cc-by-nc': 'https://creativecommons.org/licenses/by-nc/4.0/',
      'cc-by-nc-sa': 'https://creativecommons.org/licenses/by-nc-sa/4.0/',
      'cc-by-nd': 'https://creativecommons.org/licenses/by-nd/4.0/',
      'cc-by-nc-nd': 'https://creativecommons.org/licenses/by-nc-nd/4.0/',
      'cc0': 'https://creativecommons.org/publicdomain/zero/1.0/',
      'mit': 'https://opensource.org/licenses/MIT',
      'apache-2.0': 'https://opensource.org/licenses/Apache-2.0',
      'gpl-3.0': 'https://www.gnu.org/licenses/gpl-3.0.en.html'
    };
    
    return licenseUrls[type] || '';
  }
}

export default DoiRoyaltyContract;/**
 * Smart Contract für die Verwaltung von Lizenzierungen und Royalties für DOI-NFTs
 * Implementiert ERC-2981 für Royalties und erweiterte Lizenzierungsfunktionen
 */

class DoiRoyaltyContract {
  constructor(api, contractAddress, options = {}) {
    this.api = api;
    this.contractAddress = contractAddress;
    this.options = {
      defaultRoyaltyPercentage: 5, // 5% Standardroyalty
      maxRoyaltyPercentage: 15,    // Maximale Royalty von 15%
      ...options
    };
    
    // Initialisiere den Contract
    this.initialize();
  }
  
  /**
   * Initialisiert den Contract
   * @private
   */
  async initialize() {
    try {
      // In einer realen Implementierung würden wir hier den Contract ABI laden
      // und eine Verbindung zum Contract herstellen
      console.log('Initialisiere DOI Royalty Contract:', this.contractAddress);
      
      // Simuliere Contract-Initialisierung
      this.initialized = true;
    } catch (error) {
      console.error('Fehler bei der Initialisierung des Royalty Contracts:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Royalty-Informationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Array} recipients - Liste der Empfänger (Adressen)
   * @param {Array} shares - Liste der Anteile in Prozent
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setRoyaltyInfo(tokenId, recipients, shares) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Eingaben
    if (!Array.isArray(recipients) || !Array.isArray(shares)) {
      throw new Error('Empfänger und Anteile müssen Arrays sein');
    }
    
    if (recipients.length !== shares.length) {
      throw new Error('Anzahl der Empfänger und Anteile muss übereinstimmen');
    }
    
    // Prüfe, ob die Summe der Anteile 100% nicht überschreitet
    const totalShares = shares.reduce((sum, share) => sum + share, 0);
    if (totalShares > 100) {
      throw new Error('Summe der Anteile darf 100% nicht überschreiten');
    }
    
    // Prüfe, ob die Royalty-Rate das Maximum nicht überschreitet
    const royaltyRate = totalShares / 100 * this.options.defaultRoyaltyPercentage;
    if (royaltyRate > this.options.maxRoyaltyPercentage) {
      throw new Error(`Royalty-Rate darf ${this.options.maxRoyaltyPercentage}% nicht überschreiten`);
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Royalty-Informationen für Token ${tokenId}:`, {
        recipients,
        shares,
        royaltyRate: `${royaltyRate}%`
      });
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        recipients,
        shares,
        royaltyRate
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Royalty-Informationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Royalty-Informationen
   */
  async getRoyaltyInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Royalty-Informationen für Token ${tokenId} ab`);
      
      // Simulierte Royalty-Informationen
      return {
        tokenId,
        recipients: ['0x1234...', '0x5678...'],
        shares: [60, 40], // 60% und 40%
        royaltyRate: this.options.defaultRoyaltyPercentage,
        totalRoyalty: this.options.defaultRoyaltyPercentage
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Berechnet die Royalty-Zahlung für einen Verkauf
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Royalty-Zahlungsinformationen
   */
  async calculateRoyaltyPayment(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Royalty-Informationen ab
      const royaltyInfo = await this.getRoyaltyInfo(tokenId);
      
      // Berechne Royalty-Betrag
      const royaltyAmount = salePrice * (royaltyInfo.royaltyRate / 100);
      
      // Berechne Zahlungen pro Empfänger
      const payments = royaltyInfo.recipients.map((recipient, index) => ({
        recipient,
        share: royaltyInfo.shares[index],
        amount: royaltyAmount * (royaltyInfo.shares[index] / 100)
      }));
      
      return {
        tokenId,
        salePrice,
        royaltyRate: royaltyInfo.royaltyRate,
        totalRoyalty: royaltyAmount,
        payments
      };
    } catch (error) {
      console.error('Fehler bei der Berechnung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Führt eine Royalty-Zahlung für einen Verkauf durch
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async payRoyalties(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Berechne Royalty-Zahlungen
      const royaltyPayment = await this.calculateRoyaltyPayment(tokenId, salePrice);
      
      // In einer realen Implementierung würden wir hier die Zahlungen durchführen
      console.log(`Führe Royalty-Zahlungen für Token ${tokenId} durch:`, royaltyPayment);
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        salePrice,
        royaltyRate: royaltyPayment.royaltyRate,
        totalRoyalty: royaltyPayment.totalRoyalty,
        payments: royaltyPayment.payments
      };
    } catch (error) {
      console.error('Fehler bei der Durchführung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Lizenzinformationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} licenseInfo - Lizenzinformationen
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setLicenseInfo(tokenId, licenseInfo) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Lizenzinformationen
    if (!licenseInfo.type) {
      throw new Error('Lizenztyp muss angegeben werden');
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Lizenzinformationen für Token ${tokenId}:`, licenseInfo);
      
      // Standardisiere Lizenzinformationen
      const standardizedLicense = {
        type: licenseInfo.type,
        name: licenseInfo.name || this.getLicenseName(licenseInfo.type),
        url: licenseInfo.url || this.getLicenseUrl(licenseInfo.type),
        commercial: licenseInfo.commercial !== undefined ? licenseInfo.commercial : true,
        derivatives: licenseInfo.derivatives !== undefined ? licenseInfo.derivatives : true,
        attribution: licenseInfo.attribution !== undefined ? licenseInfo.attribution : true
      };
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        license: standardizedLicense
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Lizenzinformationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Lizenzinformationen
   */
  async getLicenseInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Lizenzinformationen für Token ${tokenId} ab`);
      
      // Simulierte Lizenzinformationen
      return {
        tokenId,
        license: {
          type: 'cc-by',
          name: 'Creative Commons Attribution 4.0',
          url: 'https://creativecommons.org/licenses/by/4.0/',
          commercial: true,
          derivatives: true,
          attribution: true
        }
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Nutzung mit der Lizenz kompatibel ist
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} usage - Nutzungsinformationen
   * @returns {Promise<Object>} - Kompatibilitätsinformationen
   */
  async checkLicenseCompatibility(tokenId, usage) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Lizenzinformationen ab
      const licenseInfo = await this.getLicenseInfo(tokenId);
      
      // Prüfe Kompatibilität
      const isCommercialCompatible = !usage.commercial || licenseInfo.license.commercial;
      const isDerivativesCompatible = !usage.derivatives || licenseInfo.license.derivatives;
      const isAttributionCompatible = !licenseInfo.license.attribution || usage.attribution;
      
      const isCompatible = isCommercialCompatible && isDerivativesCompatible && isAttributionCompatible;
      
      return {
        tokenId,
        license: licenseInfo.license,
        usage,
        isCompatible,
        incompatibilities: {
          commercial: !isCommercialCompatible,
          derivatives: !isDerivativesCompatible,
          attribution: !isAttributionCompatible
        }
      };
    } catch (error) {
      console.error('Fehler bei der Prüfung der Lizenzkompatibilität:', error);
      throw error;
    }
  }
  
  /**
   * Gibt den Namen einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseName(type) {
    const licenseNames = {
      'cc-by': 'Creative Commons Attribution 4.0',
      'cc-by-sa': 'Creative Commons Attribution-ShareAlike 4.0',
      'cc-by-nc': 'Creative Commons Attribution-NonCommercial 4.0',
      'cc-by-nc-sa': 'Creative Commons Attribution-NonCommercial-ShareAlike 4.0',
      'cc-by-nd': 'Creative Commons Attribution-NoDerivatives 4.0',
      'cc-by-nc-nd': 'Creative Commons Attribution-NonCommercial-NoDerivatives 4.0',
      'cc0': 'Creative Commons Zero v1.0 Universal',
      'mit': 'MIT License',
      'apache-2.0': 'Apache License 2.0',
      'gpl-3.0': 'GNU General Public License v3.0'
    };
    
    return licenseNames[type] || 'Custom License';
  }
  
  /**
   * Gibt die URL einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseUrl(type) {
    const licenseUrls = {
      'cc-by': 'https://creativecommons.org/licenses/by/4.0/',
      'cc-by-sa': 'https://creativecommons.org/licenses/by-sa/4.0/',
      'cc-by-nc': 'https://creativecommons.org/licenses/by-nc/4.0/',
      'cc-by-nc-sa': 'https://creativecommons.org/licenses/by-nc-sa/4.0/',
      'cc-by-nd': 'https://creativecommons.org/licenses/by-nd/4.0/',
      'cc-by-nc-nd': 'https://creativecommons.org/licenses/by-nc-nd/4.0/',
      'cc0': 'https://creativecommons.org/publicdomain/zero/1.0/',
      'mit': 'https://opensource.org/licenses/MIT',
      'apache-2.0': 'https://opensource.org/licenses/Apache-2.0',
      'gpl-3.0': 'https://www.gnu.org/licenses/gpl-3.0.en.html'
    };
    
    return licenseUrls[type] || '';
  }
}

export default DoiRoyaltyContract;/**
 * Smart Contract für die Verwaltung von Lizenzierungen und Royalties für DOI-NFTs
 * Implementiert ERC-2981 für Royalties und erweiterte Lizenzierungsfunktionen
 */

class DoiRoyaltyContract {
  constructor(api, contractAddress, options = {}) {
    this.api = api;
    this.contractAddress = contractAddress;
    this.options = {
      defaultRoyaltyPercentage: 5, // 5% Standardroyalty
      maxRoyaltyPercentage: 15,    // Maximale Royalty von 15%
      ...options
    };
    
    // Initialisiere den Contract
    this.initialize();
  }
  
  /**
   * Initialisiert den Contract
   * @private
   */
  async initialize() {
    try {
      // In einer realen Implementierung würden wir hier den Contract ABI laden
      // und eine Verbindung zum Contract herstellen
      console.log('Initialisiere DOI Royalty Contract:', this.contractAddress);
      
      // Simuliere Contract-Initialisierung
      this.initialized = true;
    } catch (error) {
      console.error('Fehler bei der Initialisierung des Royalty Contracts:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Royalty-Informationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Array} recipients - Liste der Empfänger (Adressen)
   * @param {Array} shares - Liste der Anteile in Prozent
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setRoyaltyInfo(tokenId, recipients, shares) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Eingaben
    if (!Array.isArray(recipients) || !Array.isArray(shares)) {
      throw new Error('Empfänger und Anteile müssen Arrays sein');
    }
    
    if (recipients.length !== shares.length) {
      throw new Error('Anzahl der Empfänger und Anteile muss übereinstimmen');
    }
    
    // Prüfe, ob die Summe der Anteile 100% nicht überschreitet
    const totalShares = shares.reduce((sum, share) => sum + share, 0);
    if (totalShares > 100) {
      throw new Error('Summe der Anteile darf 100% nicht überschreiten');
    }
    
    // Prüfe, ob die Royalty-Rate das Maximum nicht überschreitet
    const royaltyRate = totalShares / 100 * this.options.defaultRoyaltyPercentage;
    if (royaltyRate > this.options.maxRoyaltyPercentage) {
      throw new Error(`Royalty-Rate darf ${this.options.maxRoyaltyPercentage}% nicht überschreiten`);
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Royalty-Informationen für Token ${tokenId}:`, {
        recipients,
        shares,
        royaltyRate: `${royaltyRate}%`
      });
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        recipients,
        shares,
        royaltyRate
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Royalty-Informationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Royalty-Informationen
   */
  async getRoyaltyInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Royalty-Informationen für Token ${tokenId} ab`);
      
      // Simulierte Royalty-Informationen
      return {
        tokenId,
        recipients: ['0x1234...', '0x5678...'],
        shares: [60, 40], // 60% und 40%
        royaltyRate: this.options.defaultRoyaltyPercentage,
        totalRoyalty: this.options.defaultRoyaltyPercentage
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Berechnet die Royalty-Zahlung für einen Verkauf
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Royalty-Zahlungsinformationen
   */
  async calculateRoyaltyPayment(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Royalty-Informationen ab
      const royaltyInfo = await this.getRoyaltyInfo(tokenId);
      
      // Berechne Royalty-Betrag
      const royaltyAmount = salePrice * (royaltyInfo.royaltyRate / 100);
      
      // Berechne Zahlungen pro Empfänger
      const payments = royaltyInfo.recipients.map((recipient, index) => ({
        recipient,
        share: royaltyInfo.shares[index],
        amount: royaltyAmount * (royaltyInfo.shares[index] / 100)
      }));
      
      return {
        tokenId,
        salePrice,
        royaltyRate: royaltyInfo.royaltyRate,
        totalRoyalty: royaltyAmount,
        payments
      };
    } catch (error) {
      console.error('Fehler bei der Berechnung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Führt eine Royalty-Zahlung für einen Verkauf durch
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async payRoyalties(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Berechne Royalty-Zahlungen
      const royaltyPayment = await this.calculateRoyaltyPayment(tokenId, salePrice);
      
      // In einer realen Implementierung würden wir hier die Zahlungen durchführen
      console.log(`Führe Royalty-Zahlungen für Token ${tokenId} durch:`, royaltyPayment);
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        salePrice,
        royaltyRate: royaltyPayment.royaltyRate,
        totalRoyalty: royaltyPayment.totalRoyalty,
        payments: royaltyPayment.payments
      };
    } catch (error) {
      console.error('Fehler bei der Durchführung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Lizenzinformationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} licenseInfo - Lizenzinformationen
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setLicenseInfo(tokenId, licenseInfo) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Lizenzinformationen
    if (!licenseInfo.type) {
      throw new Error('Lizenztyp muss angegeben werden');
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Lizenzinformationen für Token ${tokenId}:`, licenseInfo);
      
      // Standardisiere Lizenzinformationen
      const standardizedLicense = {
        type: licenseInfo.type,
        name: licenseInfo.name || this.getLicenseName(licenseInfo.type),
        url: licenseInfo.url || this.getLicenseUrl(licenseInfo.type),
        commercial: licenseInfo.commercial !== undefined ? licenseInfo.commercial : true,
        derivatives: licenseInfo.derivatives !== undefined ? licenseInfo.derivatives : true,
        attribution: licenseInfo.attribution !== undefined ? licenseInfo.attribution : true
      };
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        license: standardizedLicense
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Lizenzinformationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Lizenzinformationen
   */
  async getLicenseInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Lizenzinformationen für Token ${tokenId} ab`);
      
      // Simulierte Lizenzinformationen
      return {
        tokenId,
        license: {
          type: 'cc-by',
          name: 'Creative Commons Attribution 4.0',
          url: 'https://creativecommons.org/licenses/by/4.0/',
          commercial: true,
          derivatives: true,
          attribution: true
        }
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Nutzung mit der Lizenz kompatibel ist
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} usage - Nutzungsinformationen
   * @returns {Promise<Object>} - Kompatibilitätsinformationen
   */
  async checkLicenseCompatibility(tokenId, usage) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Lizenzinformationen ab
      const licenseInfo = await this.getLicenseInfo(tokenId);
      
      // Prüfe Kompatibilität
      const isCommercialCompatible = !usage.commercial || licenseInfo.license.commercial;
      const isDerivativesCompatible = !usage.derivatives || licenseInfo.license.derivatives;
      const isAttributionCompatible = !licenseInfo.license.attribution || usage.attribution;
      
      const isCompatible = isCommercialCompatible && isDerivativesCompatible && isAttributionCompatible;
      
      return {
        tokenId,
        license: licenseInfo.license,
        usage,
        isCompatible,
        incompatibilities: {
          commercial: !isCommercialCompatible,
          derivatives: !isDerivativesCompatible,
          attribution: !isAttributionCompatible
        }
      };
    } catch (error) {
      console.error('Fehler bei der Prüfung der Lizenzkompatibilität:', error);
      throw error;
    }
  }
  
  /**
   * Gibt den Namen einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseName(type) {
    const licenseNames = {
      'cc-by': 'Creative Commons Attribution 4.0',
      'cc-by-sa': 'Creative Commons Attribution-ShareAlike 4.0',
      'cc-by-nc': 'Creative Commons Attribution-NonCommercial 4.0',
      'cc-by-nc-sa': 'Creative Commons Attribution-NonCommercial-ShareAlike 4.0',
      'cc-by-nd': 'Creative Commons Attribution-NoDerivatives 4.0',
      'cc-by-nc-nd': 'Creative Commons Attribution-NonCommercial-NoDerivatives 4.0',
      'cc0': 'Creative Commons Zero v1.0 Universal',
      'mit': 'MIT License',
      'apache-2.0': 'Apache License 2.0',
      'gpl-3.0': 'GNU General Public License v3.0'
    };
    
    return licenseNames[type] || 'Custom License';
  }
  
  /**
   * Gibt die URL einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseUrl(type) {
    const licenseUrls = {
      'cc-by': 'https://creativecommons.org/licenses/by/4.0/',
      'cc-by-sa': 'https://creativecommons.org/licenses/by-sa/4.0/',
      'cc-by-nc': 'https://creativecommons.org/licenses/by-nc/4.0/',
      'cc-by-nc-sa': 'https://creativecommons.org/licenses/by-nc-sa/4.0/',
      'cc-by-nd': 'https://creativecommons.org/licenses/by-nd/4.0/',
      'cc-by-nc-nd': 'https://creativecommons.org/licenses/by-nc-nd/4.0/',
      'cc0': 'https://creativecommons.org/publicdomain/zero/1.0/',
      'mit': 'https://opensource.org/licenses/MIT',
      'apache-2.0': 'https://opensource.org/licenses/Apache-2.0',
      'gpl-3.0': 'https://www.gnu.org/licenses/gpl-3.0.en.html'
    };
    
    return licenseUrls[type] || '';
  }
}

export default DoiRoyaltyContract;/**
 * Smart Contract für die Verwaltung von Lizenzierungen und Royalties für DOI-NFTs
 * Implementiert ERC-2981 für Royalties und erweiterte Lizenzierungsfunktionen
 */

class DoiRoyaltyContract {
  constructor(api, contractAddress, options = {}) {
    this.api = api;
    this.contractAddress = contractAddress;
    this.options = {
      defaultRoyaltyPercentage: 5, // 5% Standardroyalty
      maxRoyaltyPercentage: 15,    // Maximale Royalty von 15%
      ...options
    };
    
    // Initialisiere den Contract
    this.initialize();
  }
  
  /**
   * Initialisiert den Contract
   * @private
   */
  async initialize() {
    try {
      // In einer realen Implementierung würden wir hier den Contract ABI laden
      // und eine Verbindung zum Contract herstellen
      console.log('Initialisiere DOI Royalty Contract:', this.contractAddress);
      
      // Simuliere Contract-Initialisierung
      this.initialized = true;
    } catch (error) {
      console.error('Fehler bei der Initialisierung des Royalty Contracts:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Royalty-Informationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Array} recipients - Liste der Empfänger (Adressen)
   * @param {Array} shares - Liste der Anteile in Prozent
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setRoyaltyInfo(tokenId, recipients, shares) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Eingaben
    if (!Array.isArray(recipients) || !Array.isArray(shares)) {
      throw new Error('Empfänger und Anteile müssen Arrays sein');
    }
    
    if (recipients.length !== shares.length) {
      throw new Error('Anzahl der Empfänger und Anteile muss übereinstimmen');
    }
    
    // Prüfe, ob die Summe der Anteile 100% nicht überschreitet
    const totalShares = shares.reduce((sum, share) => sum + share, 0);
    if (totalShares > 100) {
      throw new Error('Summe der Anteile darf 100% nicht überschreiten');
    }
    
    // Prüfe, ob die Royalty-Rate das Maximum nicht überschreitet
    const royaltyRate = totalShares / 100 * this.options.defaultRoyaltyPercentage;
    if (royaltyRate > this.options.maxRoyaltyPercentage) {
      throw new Error(`Royalty-Rate darf ${this.options.maxRoyaltyPercentage}% nicht überschreiten`);
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Royalty-Informationen für Token ${tokenId}:`, {
        recipients,
        shares,
        royaltyRate: `${royaltyRate}%`
      });
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        recipients,
        shares,
        royaltyRate
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Royalty-Informationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Royalty-Informationen
   */
  async getRoyaltyInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Royalty-Informationen für Token ${tokenId} ab`);
      
      // Simulierte Royalty-Informationen
      return {
        tokenId,
        recipients: ['0x1234...', '0x5678...'],
        shares: [60, 40], // 60% und 40%
        royaltyRate: this.options.defaultRoyaltyPercentage,
        totalRoyalty: this.options.defaultRoyaltyPercentage
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Berechnet die Royalty-Zahlung für einen Verkauf
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Royalty-Zahlungsinformationen
   */
  async calculateRoyaltyPayment(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Royalty-Informationen ab
      const royaltyInfo = await this.getRoyaltyInfo(tokenId);
      
      // Berechne Royalty-Betrag
      const royaltyAmount = salePrice * (royaltyInfo.royaltyRate / 100);
      
      // Berechne Zahlungen pro Empfänger
      const payments = royaltyInfo.recipients.map((recipient, index) => ({
        recipient,
        share: royaltyInfo.shares[index],
        amount: royaltyAmount * (royaltyInfo.shares[index] / 100)
      }));
      
      return {
        tokenId,
        salePrice,
        royaltyRate: royaltyInfo.royaltyRate,
        totalRoyalty: royaltyAmount,
        payments
      };
    } catch (error) {
      console.error('Fehler bei der Berechnung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Führt eine Royalty-Zahlung für einen Verkauf durch
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async payRoyalties(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Berechne Royalty-Zahlungen
      const royaltyPayment = await this.calculateRoyaltyPayment(tokenId, salePrice);
      
      // In einer realen Implementierung würden wir hier die Zahlungen durchführen
      console.log(`Führe Royalty-Zahlungen für Token ${tokenId} durch:`, royaltyPayment);
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        salePrice,
        royaltyRate: royaltyPayment.royaltyRate,
        totalRoyalty: royaltyPayment.totalRoyalty,
        payments: royaltyPayment.payments
      };
    } catch (error) {
      console.error('Fehler bei der Durchführung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Lizenzinformationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} licenseInfo - Lizenzinformationen
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setLicenseInfo(tokenId, licenseInfo) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Lizenzinformationen
    if (!licenseInfo.type) {
      throw new Error('Lizenztyp muss angegeben werden');
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Lizenzinformationen für Token ${tokenId}:`, licenseInfo);
      
      // Standardisiere Lizenzinformationen
      const standardizedLicense = {
        type: licenseInfo.type,
        name: licenseInfo.name || this.getLicenseName(licenseInfo.type),
        url: licenseInfo.url || this.getLicenseUrl(licenseInfo.type),
        commercial: licenseInfo.commercial !== undefined ? licenseInfo.commercial : true,
        derivatives: licenseInfo.derivatives !== undefined ? licenseInfo.derivatives : true,
        attribution: licenseInfo.attribution !== undefined ? licenseInfo.attribution : true
      };
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        license: standardizedLicense
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Lizenzinformationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Lizenzinformationen
   */
  async getLicenseInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Lizenzinformationen für Token ${tokenId} ab`);
      
      // Simulierte Lizenzinformationen
      return {
        tokenId,
        license: {
          type: 'cc-by',
          name: 'Creative Commons Attribution 4.0',
          url: 'https://creativecommons.org/licenses/by/4.0/',
          commercial: true,
          derivatives: true,
          attribution: true
        }
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Nutzung mit der Lizenz kompatibel ist
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} usage - Nutzungsinformationen
   * @returns {Promise<Object>} - Kompatibilitätsinformationen
   */
  async checkLicenseCompatibility(tokenId, usage) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Lizenzinformationen ab
      const licenseInfo = await this.getLicenseInfo(tokenId);
      
      // Prüfe Kompatibilität
      const isCommercialCompatible = !usage.commercial || licenseInfo.license.commercial;
      const isDerivativesCompatible = !usage.derivatives || licenseInfo.license.derivatives;
      const isAttributionCompatible = !licenseInfo.license.attribution || usage.attribution;
      
      const isCompatible = isCommercialCompatible && isDerivativesCompatible && isAttributionCompatible;
      
      return {
        tokenId,
        license: licenseInfo.license,
        usage,
        isCompatible,
        incompatibilities: {
          commercial: !isCommercialCompatible,
          derivatives: !isDerivativesCompatible,
          attribution: !isAttributionCompatible
        }
      };
    } catch (error) {
      console.error('Fehler bei der Prüfung der Lizenzkompatibilität:', error);
      throw error;
    }
  }
  
  /**
   * Gibt den Namen einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseName(type) {
    const licenseNames = {
      'cc-by': 'Creative Commons Attribution 4.0',
      'cc-by-sa': 'Creative Commons Attribution-ShareAlike 4.0',
      'cc-by-nc': 'Creative Commons Attribution-NonCommercial 4.0',
      'cc-by-nc-sa': 'Creative Commons Attribution-NonCommercial-ShareAlike 4.0',
      'cc-by-nd': 'Creative Commons Attribution-NoDerivatives 4.0',
      'cc-by-nc-nd': 'Creative Commons Attribution-NonCommercial-NoDerivatives 4.0',
      'cc0': 'Creative Commons Zero v1.0 Universal',
      'mit': 'MIT License',
      'apache-2.0': 'Apache License 2.0',
      'gpl-3.0': 'GNU General Public License v3.0'
    };
    
    return licenseNames[type] || 'Custom License';
  }
  
  /**
   * Gibt die URL einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseUrl(type) {
    const licenseUrls = {
      'cc-by': 'https://creativecommons.org/licenses/by/4.0/',
      'cc-by-sa': 'https://creativecommons.org/licenses/by-sa/4.0/',
      'cc-by-nc': 'https://creativecommons.org/licenses/by-nc/4.0/',
      'cc-by-nc-sa': 'https://creativecommons.org/licenses/by-nc-sa/4.0/',
      'cc-by-nd': 'https://creativecommons.org/licenses/by-nd/4.0/',
      'cc-by-nc-nd': 'https://creativecommons.org/licenses/by-nc-nd/4.0/',
      'cc0': 'https://creativecommons.org/publicdomain/zero/1.0/',
      'mit': 'https://opensource.org/licenses/MIT',
      'apache-2.0': 'https://opensource.org/licenses/Apache-2.0',
      'gpl-3.0': 'https://www.gnu.org/licenses/gpl-3.0.en.html'
    };
    
    return licenseUrls[type] || '';
  }
}

export default DoiRoyaltyContract;/**
 * Smart Contract für die Verwaltung von Lizenzierungen und Royalties für DOI-NFTs
 * Implementiert ERC-2981 für Royalties und erweiterte Lizenzierungsfunktionen
 */

class DoiRoyaltyContract {
  constructor(api, contractAddress, options = {}) {
    this.api = api;
    this.contractAddress = contractAddress;
    this.options = {
      defaultRoyaltyPercentage: 5, // 5% Standardroyalty
      maxRoyaltyPercentage: 15,    // Maximale Royalty von 15%
      ...options
    };
    
    // Initialisiere den Contract
    this.initialize();
  }
  
  /**
   * Initialisiert den Contract
   * @private
   */
  async initialize() {
    try {
      // In einer realen Implementierung würden wir hier den Contract ABI laden
      // und eine Verbindung zum Contract herstellen
      console.log('Initialisiere DOI Royalty Contract:', this.contractAddress);
      
      // Simuliere Contract-Initialisierung
      this.initialized = true;
    } catch (error) {
      console.error('Fehler bei der Initialisierung des Royalty Contracts:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Royalty-Informationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Array} recipients - Liste der Empfänger (Adressen)
   * @param {Array} shares - Liste der Anteile in Prozent
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setRoyaltyInfo(tokenId, recipients, shares) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Eingaben
    if (!Array.isArray(recipients) || !Array.isArray(shares)) {
      throw new Error('Empfänger und Anteile müssen Arrays sein');
    }
    
    if (recipients.length !== shares.length) {
      throw new Error('Anzahl der Empfänger und Anteile muss übereinstimmen');
    }
    
    // Prüfe, ob die Summe der Anteile 100% nicht überschreitet
    const totalShares = shares.reduce((sum, share) => sum + share, 0);
    if (totalShares > 100) {
      throw new Error('Summe der Anteile darf 100% nicht überschreiten');
    }
    
    // Prüfe, ob die Royalty-Rate das Maximum nicht überschreitet
    const royaltyRate = totalShares / 100 * this.options.defaultRoyaltyPercentage;
    if (royaltyRate > this.options.maxRoyaltyPercentage) {
      throw new Error(`Royalty-Rate darf ${this.options.maxRoyaltyPercentage}% nicht überschreiten`);
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Royalty-Informationen für Token ${tokenId}:`, {
        recipients,
        shares,
        royaltyRate: `${royaltyRate}%`
      });
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        recipients,
        shares,
        royaltyRate
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Royalty-Informationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Royalty-Informationen
   */
  async getRoyaltyInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Royalty-Informationen für Token ${tokenId} ab`);
      
      // Simulierte Royalty-Informationen
      return {
        tokenId,
        recipients: ['0x1234...', '0x5678...'],
        shares: [60, 40], // 60% und 40%
        royaltyRate: this.options.defaultRoyaltyPercentage,
        totalRoyalty: this.options.defaultRoyaltyPercentage
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Berechnet die Royalty-Zahlung für einen Verkauf
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Royalty-Zahlungsinformationen
   */
  async calculateRoyaltyPayment(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Royalty-Informationen ab
      const royaltyInfo = await this.getRoyaltyInfo(tokenId);
      
      // Berechne Royalty-Betrag
      const royaltyAmount = salePrice * (royaltyInfo.royaltyRate / 100);
      
      // Berechne Zahlungen pro Empfänger
      const payments = royaltyInfo.recipients.map((recipient, index) => ({
        recipient,
        share: royaltyInfo.shares[index],
        amount: royaltyAmount * (royaltyInfo.shares[index] / 100)
      }));
      
      return {
        tokenId,
        salePrice,
        royaltyRate: royaltyInfo.royaltyRate,
        totalRoyalty: royaltyAmount,
        payments
      };
    } catch (error) {
      console.error('Fehler bei der Berechnung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Führt eine Royalty-Zahlung für einen Verkauf durch
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async payRoyalties(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Berechne Royalty-Zahlungen
      const royaltyPayment = await this.calculateRoyaltyPayment(tokenId, salePrice);
      
      // In einer realen Implementierung würden wir hier die Zahlungen durchführen
      console.log(`Führe Royalty-Zahlungen für Token ${tokenId} durch:`, royaltyPayment);
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        salePrice,
        royaltyRate: royaltyPayment.royaltyRate,
        totalRoyalty: royaltyPayment.totalRoyalty,
        payments: royaltyPayment.payments
      };
    } catch (error) {
      console.error('Fehler bei der Durchführung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Lizenzinformationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} licenseInfo - Lizenzinformationen
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setLicenseInfo(tokenId, licenseInfo) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Lizenzinformationen
    if (!licenseInfo.type) {
      throw new Error('Lizenztyp muss angegeben werden');
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Lizenzinformationen für Token ${tokenId}:`, licenseInfo);
      
      // Standardisiere Lizenzinformationen
      const standardizedLicense = {
        type: licenseInfo.type,
        name: licenseInfo.name || this.getLicenseName(licenseInfo.type),
        url: licenseInfo.url || this.getLicenseUrl(licenseInfo.type),
        commercial: licenseInfo.commercial !== undefined ? licenseInfo.commercial : true,
        derivatives: licenseInfo.derivatives !== undefined ? licenseInfo.derivatives : true,
        attribution: licenseInfo.attribution !== undefined ? licenseInfo.attribution : true
      };
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        license: standardizedLicense
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Lizenzinformationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Lizenzinformationen
   */
  async getLicenseInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Lizenzinformationen für Token ${tokenId} ab`);
      
      // Simulierte Lizenzinformationen
      return {
        tokenId,
        license: {
          type: 'cc-by',
          name: 'Creative Commons Attribution 4.0',
          url: 'https://creativecommons.org/licenses/by/4.0/',
          commercial: true,
          derivatives: true,
          attribution: true
        }
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Nutzung mit der Lizenz kompatibel ist
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} usage - Nutzungsinformationen
   * @returns {Promise<Object>} - Kompatibilitätsinformationen
   */
  async checkLicenseCompatibility(tokenId, usage) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Lizenzinformationen ab
      const licenseInfo = await this.getLicenseInfo(tokenId);
      
      // Prüfe Kompatibilität
      const isCommercialCompatible = !usage.commercial || licenseInfo.license.commercial;
      const isDerivativesCompatible = !usage.derivatives || licenseInfo.license.derivatives;
      const isAttributionCompatible = !licenseInfo.license.attribution || usage.attribution;
      
      const isCompatible = isCommercialCompatible && isDerivativesCompatible && isAttributionCompatible;
      
      return {
        tokenId,
        license: licenseInfo.license,
        usage,
        isCompatible,
        incompatibilities: {
          commercial: !isCommercialCompatible,
          derivatives: !isDerivativesCompatible,
          attribution: !isAttributionCompatible
        }
      };
    } catch (error) {
      console.error('Fehler bei der Prüfung der Lizenzkompatibilität:', error);
      throw error;
    }
  }
  
  /**
   * Gibt den Namen einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseName(type) {
    const licenseNames = {
      'cc-by': 'Creative Commons Attribution 4.0',
      'cc-by-sa': 'Creative Commons Attribution-ShareAlike 4.0',
      'cc-by-nc': 'Creative Commons Attribution-NonCommercial 4.0',
      'cc-by-nc-sa': 'Creative Commons Attribution-NonCommercial-ShareAlike 4.0',
      'cc-by-nd': 'Creative Commons Attribution-NoDerivatives 4.0',
      'cc-by-nc-nd': 'Creative Commons Attribution-NonCommercial-NoDerivatives 4.0',
      'cc0': 'Creative Commons Zero v1.0 Universal',
      'mit': 'MIT License',
      'apache-2.0': 'Apache License 2.0',
      'gpl-3.0': 'GNU General Public License v3.0'
    };
    
    return licenseNames[type] || 'Custom License';
  }
  
  /**
   * Gibt die URL einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseUrl(type) {
    const licenseUrls = {
      'cc-by': 'https://creativecommons.org/licenses/by/4.0/',
      'cc-by-sa': 'https://creativecommons.org/licenses/by-sa/4.0/',
      'cc-by-nc': 'https://creativecommons.org/licenses/by-nc/4.0/',
      'cc-by-nc-sa': 'https://creativecommons.org/licenses/by-nc-sa/4.0/',
      'cc-by-nd': 'https://creativecommons.org/licenses/by-nd/4.0/',
      'cc-by-nc-nd': 'https://creativecommons.org/licenses/by-nc-nd/4.0/',
      'cc0': 'https://creativecommons.org/publicdomain/zero/1.0/',
      'mit': 'https://opensource.org/licenses/MIT',
      'apache-2.0': 'https://opensource.org/licenses/Apache-2.0',
      'gpl-3.0': 'https://www.gnu.org/licenses/gpl-3.0.en.html'
    };
    
    return licenseUrls[type] || '';
  }
}

export default DoiRoyaltyContract; * Smart Contract für die Verwaltung von Lizenzierungen und Royalties für DOI-NFTs
 * Implementiert ERC-2981 für Royalties und erweiterte Lizenzierungsfunktionen
 */

class DoiRoyaltyContract {
  constructor(api, contractAddress, options = {}) {
    this.api = api;
    this.contractAddress = contractAddress;
    this.options = {
      defaultRoyaltyPercentage: 5, // 5% Standardroyalty
      maxRoyaltyPercentage: 15,    // Maximale Royalty von 15%
      ...options
    };
    
    // Initialisiere den Contract
    this.initialize();
  }
  
  /**
   * Initialisiert den Contract
   * @private
   */
  async initialize() {
    try {
      // In einer realen Implementierung würden wir hier den Contract ABI laden
      // und eine Verbindung zum Contract herstellen
      console.log('Initialisiere DOI Royalty Contract:', this.contractAddress);
      
      // Simuliere Contract-Initialisierung
      this.initialized = true;
    } catch (error) {
      console.error('Fehler bei der Initialisierung des Royalty Contracts:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Royalty-Informationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Array} recipients - Liste der Empfänger (Adressen)
   * @param {Array} shares - Liste der Anteile in Prozent
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setRoyaltyInfo(tokenId, recipients, shares) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Eingaben
    if (!Array.isArray(recipients) || !Array.isArray(shares)) {
      throw new Error('Empfänger und Anteile müssen Arrays sein');
    }
    
    if (recipients.length !== shares.length) {
      throw new Error('Anzahl der Empfänger und Anteile muss übereinstimmen');
    }
    
    // Prüfe, ob die Summe der Anteile 100% nicht überschreitet
    const totalShares = shares.reduce((sum, share) => sum + share, 0);
    if (totalShares > 100) {
      throw new Error('Summe der Anteile darf 100% nicht überschreiten');
    }
    
    // Prüfe, ob die Royalty-Rate das Maximum nicht überschreitet
    const royaltyRate = totalShares / 100 * this.options.defaultRoyaltyPercentage;
    if (royaltyRate > this.options.maxRoyaltyPercentage) {
      throw new Error(`Royalty-Rate darf ${this.options.maxRoyaltyPercentage}% nicht überschreiten`);
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Royalty-Informationen für Token ${tokenId}:`, {
        recipients,
        shares,
        royaltyRate: `${royaltyRate}%`
      });
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        recipients,
        shares,
        royaltyRate
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Royalty-Informationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Royalty-Informationen
   */
  async getRoyaltyInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Royalty-Informationen für Token ${tokenId} ab`);
      
      // Simulierte Royalty-Informationen
      return {
        tokenId,
        recipients: ['0x1234...', '0x5678...'],
        shares: [60, 40], // 60% und 40%
        royaltyRate: this.options.defaultRoyaltyPercentage,
        totalRoyalty: this.options.defaultRoyaltyPercentage
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Berechnet die Royalty-Zahlung für einen Verkauf
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Royalty-Zahlungsinformationen
   */
  async calculateRoyaltyPayment(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Royalty-Informationen ab
      const royaltyInfo = await this.getRoyaltyInfo(tokenId);
      
      // Berechne Royalty-Betrag
      const royaltyAmount = salePrice * (royaltyInfo.royaltyRate / 100);
      
      // Berechne Zahlungen pro Empfänger
      const payments = royaltyInfo.recipients.map((recipient, index) => ({
        recipient,
        share: royaltyInfo.shares[index],
        amount: royaltyAmount * (royaltyInfo.shares[index] / 100)
      }));
      
      return {
        tokenId,
        salePrice,
        royaltyRate: royaltyInfo.royaltyRate,
        totalRoyalty: royaltyAmount,
        payments
      };
    } catch (error) {
      console.error('Fehler bei der Berechnung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Führt eine Royalty-Zahlung für einen Verkauf durch
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async payRoyalties(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Berechne Royalty-Zahlungen
      const royaltyPayment = await this.calculateRoyaltyPayment(tokenId, salePrice);
      
      // In einer realen Implementierung würden wir hier die Zahlungen durchführen
      console.log(`Führe Royalty-Zahlungen für Token ${tokenId} durch:`, royaltyPayment);
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        salePrice,
        royaltyRate: royaltyPayment.royaltyRate,
        totalRoyalty: royaltyPayment.totalRoyalty,
        payments: royaltyPayment.payments
      };
    } catch (error) {
      console.error('Fehler bei der Durchführung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Lizenzinformationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} licenseInfo - Lizenzinformationen
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setLicenseInfo(tokenId, licenseInfo) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Lizenzinformationen
    if (!licenseInfo.type) {
      throw new Error('Lizenztyp muss angegeben werden');
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Lizenzinformationen für Token ${tokenId}:`, licenseInfo);
      
      // Standardisiere Lizenzinformationen
      const standardizedLicense = {
        type: licenseInfo.type,
        name: licenseInfo.name || this.getLicenseName(licenseInfo.type),
        url: licenseInfo.url || this.getLicenseUrl(licenseInfo.type),
        commercial: licenseInfo.commercial !== undefined ? licenseInfo.commercial : true,
        derivatives: licenseInfo.derivatives !== undefined ? licenseInfo.derivatives : true,
        attribution: licenseInfo.attribution !== undefined ? licenseInfo.attribution : true
      };
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        license: standardizedLicense
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Lizenzinformationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Lizenzinformationen
   */
  async getLicenseInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Lizenzinformationen für Token ${tokenId} ab`);
      
      // Simulierte Lizenzinformationen
      return {
        tokenId,
        license: {
          type: 'cc-by',
          name: 'Creative Commons Attribution 4.0',
          url: 'https://creativecommons.org/licenses/by/4.0/',
          commercial: true,
          derivatives: true,
          attribution: true
        }
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Nutzung mit der Lizenz kompatibel ist
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} usage - Nutzungsinformationen
   * @returns {Promise<Object>} - Kompatibilitätsinformationen
   */
  async checkLicenseCompatibility(tokenId, usage) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Lizenzinformationen ab
      const licenseInfo = await this.getLicenseInfo(tokenId);
      
      // Prüfe Kompatibilität
      const isCommercialCompatible = !usage.commercial || licenseInfo.license.commercial;
      const isDerivativesCompatible = !usage.derivatives || licenseInfo.license.derivatives;
      const isAttributionCompatible = !licenseInfo.license.attribution || usage.attribution;
      
      const isCompatible = isCommercialCompatible && isDerivativesCompatible && isAttributionCompatible;
      
      return {
        tokenId,
        license: licenseInfo.license,
        usage,
        isCompatible,
        incompatibilities: {
          commercial: !isCommercialCompatible,
          derivatives: !isDerivativesCompatible,
          attribution: !isAttributionCompatible
        }
      };
    } catch (error) {
      console.error('Fehler bei der Prüfung der Lizenzkompatibilität:', error);
      throw error;
    }
  }
  
  /**
   * Gibt den Namen einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseName(type) {
    const licenseNames = {
      'cc-by': 'Creative Commons Attribution 4.0',
      'cc-by-sa': 'Creative Commons Attribution-ShareAlike 4.0',
      'cc-by-nc': 'Creative Commons Attribution-NonCommercial 4.0',
      'cc-by-nc-sa': 'Creative Commons Attribution-NonCommercial-ShareAlike 4.0',
      'cc-by-nd': 'Creative Commons Attribution-NoDerivatives 4.0',
      'cc-by-nc-nd': 'Creative Commons Attribution-NonCommercial-NoDerivatives 4.0',
      'cc0': 'Creative Commons Zero v1.0 Universal',
      'mit': 'MIT License',
      'apache-2.0': 'Apache License 2.0',
      'gpl-3.0': 'GNU General Public License v3.0'
    };
    
    return licenseNames[type] || 'Custom License';
  }
  
  /**
   * Gibt die URL einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseUrl(type) {
    const licenseUrls = {
      'cc-by': 'https://creativecommons.org/licenses/by/4.0/',
      'cc-by-sa': 'https://creativecommons.org/licenses/by-sa/4.0/',
      'cc-by-nc': 'https://creativecommons.org/licenses/by-nc/4.0/',
      'cc-by-nc-sa': 'https://creativecommons.org/licenses/by-nc-sa/4.0/',
      'cc-by-nd': 'https://creativecommons.org/licenses/by-nd/4.0/',
      'cc-by-nc-nd': 'https://creativecommons.org/licenses/by-nc-nd/4.0/',
      'cc0': 'https://creativecommons.org/publicdomain/zero/1.0/',
      'mit': 'https://opensource.org/licenses/MIT',
      'apache-2.0': 'https://opensource.org/licenses/Apache-2.0',
      'gpl-3.0': 'https://www.gnu.org/licenses/gpl-3.0.en.html'
    };
    
    return licenseUrls[type] || '';
  }
}

export default DoiRoyaltyContract; * Smart Contract für die Verwaltung von Lizenzierungen und Royalties für DOI-NFTs
 * Implementiert ERC-2981 für Royalties und erweiterte Lizenzierungsfunktionen
 */

class DoiRoyaltyContract {
  constructor(api, contractAddress, options = {}) {
    this.api = api;
    this.contractAddress = contractAddress;
    this.options = {
      defaultRoyaltyPercentage: 5, // 5% Standardroyalty
      maxRoyaltyPercentage: 15,    // Maximale Royalty von 15%
      ...options
    };
    
    // Initialisiere den Contract
    this.initialize();
  }
  
  /**
   * Initialisiert den Contract
   * @private
   */
  async initialize() {
    try {
      // In einer realen Implementierung würden wir hier den Contract ABI laden
      // und eine Verbindung zum Contract herstellen
      console.log('Initialisiere DOI Royalty Contract:', this.contractAddress);
      
      // Simuliere Contract-Initialisierung
      this.initialized = true;
    } catch (error) {
      console.error('Fehler bei der Initialisierung des Royalty Contracts:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Royalty-Informationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Array} recipients - Liste der Empfänger (Adressen)
   * @param {Array} shares - Liste der Anteile in Prozent
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setRoyaltyInfo(tokenId, recipients, shares) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Eingaben
    if (!Array.isArray(recipients) || !Array.isArray(shares)) {
      throw new Error('Empfänger und Anteile müssen Arrays sein');
    }
    
    if (recipients.length !== shares.length) {
      throw new Error('Anzahl der Empfänger und Anteile muss übereinstimmen');
    }
    
    // Prüfe, ob die Summe der Anteile 100% nicht überschreitet
    const totalShares = shares.reduce((sum, share) => sum + share, 0);
    if (totalShares > 100) {
      throw new Error('Summe der Anteile darf 100% nicht überschreiten');
    }
    
    // Prüfe, ob die Royalty-Rate das Maximum nicht überschreitet
    const royaltyRate = totalShares / 100 * this.options.defaultRoyaltyPercentage;
    if (royaltyRate > this.options.maxRoyaltyPercentage) {
      throw new Error(`Royalty-Rate darf ${this.options.maxRoyaltyPercentage}% nicht überschreiten`);
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Royalty-Informationen für Token ${tokenId}:`, {
        recipients,
        shares,
        royaltyRate: `${royaltyRate}%`
      });
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        recipients,
        shares,
        royaltyRate
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Royalty-Informationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Royalty-Informationen
   */
  async getRoyaltyInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Royalty-Informationen für Token ${tokenId} ab`);
      
      // Simulierte Royalty-Informationen
      return {
        tokenId,
        recipients: ['0x1234...', '0x5678...'],
        shares: [60, 40], // 60% und 40%
        royaltyRate: this.options.defaultRoyaltyPercentage,
        totalRoyalty: this.options.defaultRoyaltyPercentage
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Berechnet die Royalty-Zahlung für einen Verkauf
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Royalty-Zahlungsinformationen
   */
  async calculateRoyaltyPayment(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Royalty-Informationen ab
      const royaltyInfo = await this.getRoyaltyInfo(tokenId);
      
      // Berechne Royalty-Betrag
      const royaltyAmount = salePrice * (royaltyInfo.royaltyRate / 100);
      
      // Berechne Zahlungen pro Empfänger
      const payments = royaltyInfo.recipients.map((recipient, index) => ({
        recipient,
        share: royaltyInfo.shares[index],
        amount: royaltyAmount * (royaltyInfo.shares[index] / 100)
      }));
      
      return {
        tokenId,
        salePrice,
        royaltyRate: royaltyInfo.royaltyRate,
        totalRoyalty: royaltyAmount,
        payments
      };
    } catch (error) {
      console.error('Fehler bei der Berechnung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Führt eine Royalty-Zahlung für einen Verkauf durch
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async payRoyalties(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Berechne Royalty-Zahlungen
      const royaltyPayment = await this.calculateRoyaltyPayment(tokenId, salePrice);
      
      // In einer realen Implementierung würden wir hier die Zahlungen durchführen
      console.log(`Führe Royalty-Zahlungen für Token ${tokenId} durch:`, royaltyPayment);
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        salePrice,
        royaltyRate: royaltyPayment.royaltyRate,
        totalRoyalty: royaltyPayment.totalRoyalty,
        payments: royaltyPayment.payments
      };
    } catch (error) {
      console.error('Fehler bei der Durchführung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Lizenzinformationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} licenseInfo - Lizenzinformationen
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setLicenseInfo(tokenId, licenseInfo) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Lizenzinformationen
    if (!licenseInfo.type) {
      throw new Error('Lizenztyp muss angegeben werden');
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Lizenzinformationen für Token ${tokenId}:`, licenseInfo);
      
      // Standardisiere Lizenzinformationen
      const standardizedLicense = {
        type: licenseInfo.type,
        name: licenseInfo.name || this.getLicenseName(licenseInfo.type),
        url: licenseInfo.url || this.getLicenseUrl(licenseInfo.type),
        commercial: licenseInfo.commercial !== undefined ? licenseInfo.commercial : true,
        derivatives: licenseInfo.derivatives !== undefined ? licenseInfo.derivatives : true,
        attribution: licenseInfo.attribution !== undefined ? licenseInfo.attribution : true
      };
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        license: standardizedLicense
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Lizenzinformationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Lizenzinformationen
   */
  async getLicenseInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Lizenzinformationen für Token ${tokenId} ab`);
      
      // Simulierte Lizenzinformationen
      return {
        tokenId,
        license: {
          type: 'cc-by',
          name: 'Creative Commons Attribution 4.0',
          url: 'https://creativecommons.org/licenses/by/4.0/',
          commercial: true,
          derivatives: true,
          attribution: true
        }
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Nutzung mit der Lizenz kompatibel ist
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} usage - Nutzungsinformationen
   * @returns {Promise<Object>} - Kompatibilitätsinformationen
   */
  async checkLicenseCompatibility(tokenId, usage) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Lizenzinformationen ab
      const licenseInfo = await this.getLicenseInfo(tokenId);
      
      // Prüfe Kompatibilität
      const isCommercialCompatible = !usage.commercial || licenseInfo.license.commercial;
      const isDerivativesCompatible = !usage.derivatives || licenseInfo.license.derivatives;
      const isAttributionCompatible = !licenseInfo.license.attribution || usage.attribution;
      
      const isCompatible = isCommercialCompatible && isDerivativesCompatible && isAttributionCompatible;
      
      return {
        tokenId,
        license: licenseInfo.license,
        usage,
        isCompatible,
        incompatibilities: {
          commercial: !isCommercialCompatible,
          derivatives: !isDerivativesCompatible,
          attribution: !isAttributionCompatible
        }
      };
    } catch (error) {
      console.error('Fehler bei der Prüfung der Lizenzkompatibilität:', error);
      throw error;
    }
  }
  
  /**
   * Gibt den Namen einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseName(type) {
    const licenseNames = {
      'cc-by': 'Creative Commons Attribution 4.0',
      'cc-by-sa': 'Creative Commons Attribution-ShareAlike 4.0',
      'cc-by-nc': 'Creative Commons Attribution-NonCommercial 4.0',
      'cc-by-nc-sa': 'Creative Commons Attribution-NonCommercial-ShareAlike 4.0',
      'cc-by-nd': 'Creative Commons Attribution-NoDerivatives 4.0',
      'cc-by-nc-nd': 'Creative Commons Attribution-NonCommercial-NoDerivatives 4.0',
      'cc0': 'Creative Commons Zero v1.0 Universal',
      'mit': 'MIT License',
      'apache-2.0': 'Apache License 2.0',
      'gpl-3.0': 'GNU General Public License v3.0'
    };
    
    return licenseNames[type] || 'Custom License';
  }
  
  /**
   * Gibt die URL einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseUrl(type) {
    const licenseUrls = {
      'cc-by': 'https://creativecommons.org/licenses/by/4.0/',
      'cc-by-sa': 'https://creativecommons.org/licenses/by-sa/4.0/',
      'cc-by-nc': 'https://creativecommons.org/licenses/by-nc/4.0/',
      'cc-by-nc-sa': 'https://creativecommons.org/licenses/by-nc-sa/4.0/',
      'cc-by-nd': 'https://creativecommons.org/licenses/by-nd/4.0/',
      'cc-by-nc-nd': 'https://creativecommons.org/licenses/by-nc-nd/4.0/',
      'cc0': 'https://creativecommons.org/publicdomain/zero/1.0/',
      'mit': 'https://opensource.org/licenses/MIT',
      'apache-2.0': 'https://opensource.org/licenses/Apache-2.0',
      'gpl-3.0': 'https://www.gnu.org/licenses/gpl-3.0.en.html'
    };
    
    return licenseUrls[type] || '';
  }
}

export default DoiRoyaltyContract; * Implementiert ERC-2981 für Royalties und erweiterte Lizenzierungsfunktionen
 */

class DoiRoyaltyContract {
  constructor(api, contractAddress, options = {}) {
    this.api = api;
    this.contractAddress = contractAddress;
    this.options = {
      defaultRoyaltyPercentage: 5, // 5% Standardroyalty
      maxRoyaltyPercentage: 15,    // Maximale Royalty von 15%
      ...options
    };
    
    // Initialisiere den Contract
    this.initialize();
  }
  
  /**
   * Initialisiert den Contract
   * @private
   */
  async initialize() {
    try {
      // In einer realen Implementierung würden wir hier den Contract ABI laden
      // und eine Verbindung zum Contract herstellen
      console.log('Initialisiere DOI Royalty Contract:', this.contractAddress);
      
      // Simuliere Contract-Initialisierung
      this.initialized = true;
    } catch (error) {
      console.error('Fehler bei der Initialisierung des Royalty Contracts:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Royalty-Informationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Array} recipients - Liste der Empfänger (Adressen)
   * @param {Array} shares - Liste der Anteile in Prozent
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setRoyaltyInfo(tokenId, recipients, shares) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Eingaben
    if (!Array.isArray(recipients) || !Array.isArray(shares)) {
      throw new Error('Empfänger und Anteile müssen Arrays sein');
    }
    
    if (recipients.length !== shares.length) {
      throw new Error('Anzahl der Empfänger und Anteile muss übereinstimmen');
    }
    
    // Prüfe, ob die Summe der Anteile 100% nicht überschreitet
    const totalShares = shares.reduce((sum, share) => sum + share, 0);
    if (totalShares > 100) {
      throw new Error('Summe der Anteile darf 100% nicht überschreiten');
    }
    
    // Prüfe, ob die Royalty-Rate das Maximum nicht überschreitet
    const royaltyRate = totalShares / 100 * this.options.defaultRoyaltyPercentage;
    if (royaltyRate > this.options.maxRoyaltyPercentage) {
      throw new Error(`Royalty-Rate darf ${this.options.maxRoyaltyPercentage}% nicht überschreiten`);
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Royalty-Informationen für Token ${tokenId}:`, {
        recipients,
        shares,
        royaltyRate: `${royaltyRate}%`
      });
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        recipients,
        shares,
        royaltyRate
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Royalty-Informationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Royalty-Informationen
   */
  async getRoyaltyInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Royalty-Informationen für Token ${tokenId} ab`);
      
      // Simulierte Royalty-Informationen
      return {
        tokenId,
        recipients: ['0x1234...', '0x5678...'],
        shares: [60, 40], // 60% und 40%
        royaltyRate: this.options.defaultRoyaltyPercentage,
        totalRoyalty: this.options.defaultRoyaltyPercentage
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Berechnet die Royalty-Zahlung für einen Verkauf
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Royalty-Zahlungsinformationen
   */
  async calculateRoyaltyPayment(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Royalty-Informationen ab
      const royaltyInfo = await this.getRoyaltyInfo(tokenId);
      
      // Berechne Royalty-Betrag
      const royaltyAmount = salePrice * (royaltyInfo.royaltyRate / 100);
      
      // Berechne Zahlungen pro Empfänger
      const payments = royaltyInfo.recipients.map((recipient, index) => ({
        recipient,
        share: royaltyInfo.shares[index],
        amount: royaltyAmount * (royaltyInfo.shares[index] / 100)
      }));
      
      return {
        tokenId,
        salePrice,
        royaltyRate: royaltyInfo.royaltyRate,
        totalRoyalty: royaltyAmount,
        payments
      };
    } catch (error) {
      console.error('Fehler bei der Berechnung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Führt eine Royalty-Zahlung für einen Verkauf durch
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async payRoyalties(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Berechne Royalty-Zahlungen
      const royaltyPayment = await this.calculateRoyaltyPayment(tokenId, salePrice);
      
      // In einer realen Implementierung würden wir hier die Zahlungen durchführen
      console.log(`Führe Royalty-Zahlungen für Token ${tokenId} durch:`, royaltyPayment);
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        salePrice,
        royaltyRate: royaltyPayment.royaltyRate,
        totalRoyalty: royaltyPayment.totalRoyalty,
        payments: royaltyPayment.payments
      };
    } catch (error) {
      console.error('Fehler bei der Durchführung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Lizenzinformationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} licenseInfo - Lizenzinformationen
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setLicenseInfo(tokenId, licenseInfo) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Lizenzinformationen
    if (!licenseInfo.type) {
      throw new Error('Lizenztyp muss angegeben werden');
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Lizenzinformationen für Token ${tokenId}:`, licenseInfo);
      
      // Standardisiere Lizenzinformationen
      const standardizedLicense = {
        type: licenseInfo.type,
        name: licenseInfo.name || this.getLicenseName(licenseInfo.type),
        url: licenseInfo.url || this.getLicenseUrl(licenseInfo.type),
        commercial: licenseInfo.commercial !== undefined ? licenseInfo.commercial : true,
        derivatives: licenseInfo.derivatives !== undefined ? licenseInfo.derivatives : true,
        attribution: licenseInfo.attribution !== undefined ? licenseInfo.attribution : true
      };
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        license: standardizedLicense
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Lizenzinformationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Lizenzinformationen
   */
  async getLicenseInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Lizenzinformationen für Token ${tokenId} ab`);
      
      // Simulierte Lizenzinformationen
      return {
        tokenId,
        license: {
          type: 'cc-by',
          name: 'Creative Commons Attribution 4.0',
          url: 'https://creativecommons.org/licenses/by/4.0/',
          commercial: true,
          derivatives: true,
          attribution: true
        }
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Nutzung mit der Lizenz kompatibel ist
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} usage - Nutzungsinformationen
   * @returns {Promise<Object>} - Kompatibilitätsinformationen
   */
  async checkLicenseCompatibility(tokenId, usage) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Lizenzinformationen ab
      const licenseInfo = await this.getLicenseInfo(tokenId);
      
      // Prüfe Kompatibilität
      const isCommercialCompatible = !usage.commercial || licenseInfo.license.commercial;
      const isDerivativesCompatible = !usage.derivatives || licenseInfo.license.derivatives;
      const isAttributionCompatible = !licenseInfo.license.attribution || usage.attribution;
      
      const isCompatible = isCommercialCompatible && isDerivativesCompatible && isAttributionCompatible;
      
      return {
        tokenId,
        license: licenseInfo.license,
        usage,
        isCompatible,
        incompatibilities: {
          commercial: !isCommercialCompatible,
          derivatives: !isDerivativesCompatible,
          attribution: !isAttributionCompatible
        }
      };
    } catch (error) {
      console.error('Fehler bei der Prüfung der Lizenzkompatibilität:', error);
      throw error;
    }
  }
  
  /**
   * Gibt den Namen einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseName(type) {
    const licenseNames = {
      'cc-by': 'Creative Commons Attribution 4.0',
      'cc-by-sa': 'Creative Commons Attribution-ShareAlike 4.0',
      'cc-by-nc': 'Creative Commons Attribution-NonCommercial 4.0',
      'cc-by-nc-sa': 'Creative Commons Attribution-NonCommercial-ShareAlike 4.0',
      'cc-by-nd': 'Creative Commons Attribution-NoDerivatives 4.0',
      'cc-by-nc-nd': 'Creative Commons Attribution-NonCommercial-NoDerivatives 4.0',
      'cc0': 'Creative Commons Zero v1.0 Universal',
      'mit': 'MIT License',
      'apache-2.0': 'Apache License 2.0',
      'gpl-3.0': 'GNU General Public License v3.0'
    };
    
    return licenseNames[type] || 'Custom License';
  }
  
  /**
   * Gibt die URL einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseUrl(type) {
    const licenseUrls = {
      'cc-by': 'https://creativecommons.org/licenses/by/4.0/',
      'cc-by-sa': 'https://creativecommons.org/licenses/by-sa/4.0/',
      'cc-by-nc': 'https://creativecommons.org/licenses/by-nc/4.0/',
      'cc-by-nc-sa': 'https://creativecommons.org/licenses/by-nc-sa/4.0/',
      'cc-by-nd': 'https://creativecommons.org/licenses/by-nd/4.0/',
      'cc-by-nc-nd': 'https://creativecommons.org/licenses/by-nc-nd/4.0/',
      'cc0': 'https://creativecommons.org/publicdomain/zero/1.0/',
      'mit': 'https://opensource.org/licenses/MIT',
      'apache-2.0': 'https://opensource.org/licenses/Apache-2.0',
      'gpl-3.0': 'https://www.gnu.org/licenses/gpl-3.0.en.html'
    };
    
    return licenseUrls[type] || '';
  }
}

export default DoiRoyaltyContract; * Smart Contract für die Verwaltung von Lizenzierungen und Royalties für DOI-NFTs
 * Implementiert ERC-2981 für Royalties und erweiterte Lizenzierungsfunktionen
 */

class DoiRoyaltyContract {
  constructor(api, contractAddress, options = {}) {
    this.api = api;
    this.contractAddress = contractAddress;
    this.options = {
      defaultRoyaltyPercentage: 5, // 5% Standardroyalty
      maxRoyaltyPercentage: 15,    // Maximale Royalty von 15%
      ...options
    };
    
    // Initialisiere den Contract
    this.initialize();
  }
  
  /**
   * Initialisiert den Contract
   * @private
   */
  async initialize() {
    try {
      // In einer realen Implementierung würden wir hier den Contract ABI laden
      // und eine Verbindung zum Contract herstellen
      console.log('Initialisiere DOI Royalty Contract:', this.contractAddress);
      
      // Simuliere Contract-Initialisierung
      this.initialized = true;
    } catch (error) {
      console.error('Fehler bei der Initialisierung des Royalty Contracts:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Royalty-Informationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Array} recipients - Liste der Empfänger (Adressen)
   * @param {Array} shares - Liste der Anteile in Prozent
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setRoyaltyInfo(tokenId, recipients, shares) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Eingaben
    if (!Array.isArray(recipients) || !Array.isArray(shares)) {
      throw new Error('Empfänger und Anteile müssen Arrays sein');
    }
    
    if (recipients.length !== shares.length) {
      throw new Error('Anzahl der Empfänger und Anteile muss übereinstimmen');
    }
    
    // Prüfe, ob die Summe der Anteile 100% nicht überschreitet
    const totalShares = shares.reduce((sum, share) => sum + share, 0);
    if (totalShares > 100) {
      throw new Error('Summe der Anteile darf 100% nicht überschreiten');
    }
    
    // Prüfe, ob die Royalty-Rate das Maximum nicht überschreitet
    const royaltyRate = totalShares / 100 * this.options.defaultRoyaltyPercentage;
    if (royaltyRate > this.options.maxRoyaltyPercentage) {
      throw new Error(`Royalty-Rate darf ${this.options.maxRoyaltyPercentage}% nicht überschreiten`);
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Royalty-Informationen für Token ${tokenId}:`, {
        recipients,
        shares,
        royaltyRate: `${royaltyRate}%`
      });
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        recipients,
        shares,
        royaltyRate
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Royalty-Informationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Royalty-Informationen
   */
  async getRoyaltyInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Royalty-Informationen für Token ${tokenId} ab`);
      
      // Simulierte Royalty-Informationen
      return {
        tokenId,
        recipients: ['0x1234...', '0x5678...'],
        shares: [60, 40], // 60% und 40%
        royaltyRate: this.options.defaultRoyaltyPercentage,
        totalRoyalty: this.options.defaultRoyaltyPercentage
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Berechnet die Royalty-Zahlung für einen Verkauf
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Royalty-Zahlungsinformationen
   */
  async calculateRoyaltyPayment(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Royalty-Informationen ab
      const royaltyInfo = await this.getRoyaltyInfo(tokenId);
      
      // Berechne Royalty-Betrag
      const royaltyAmount = salePrice * (royaltyInfo.royaltyRate / 100);
      
      // Berechne Zahlungen pro Empfänger
      const payments = royaltyInfo.recipients.map((recipient, index) => ({
        recipient,
        share: royaltyInfo.shares[index],
        amount: royaltyAmount * (royaltyInfo.shares[index] / 100)
      }));
      
      return {
        tokenId,
        salePrice,
        royaltyRate: royaltyInfo.royaltyRate,
        totalRoyalty: royaltyAmount,
        payments
      };
    } catch (error) {
      console.error('Fehler bei der Berechnung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Führt eine Royalty-Zahlung für einen Verkauf durch
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async payRoyalties(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Berechne Royalty-Zahlungen
      const royaltyPayment = await this.calculateRoyaltyPayment(tokenId, salePrice);
      
      // In einer realen Implementierung würden wir hier die Zahlungen durchführen
      console.log(`Führe Royalty-Zahlungen für Token ${tokenId} durch:`, royaltyPayment);
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        salePrice,
        royaltyRate: royaltyPayment.royaltyRate,
        totalRoyalty: royaltyPayment.totalRoyalty,
        payments: royaltyPayment.payments
      };
    } catch (error) {
      console.error('Fehler bei der Durchführung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Lizenzinformationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} licenseInfo - Lizenzinformationen
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setLicenseInfo(tokenId, licenseInfo) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Lizenzinformationen
    if (!licenseInfo.type) {
      throw new Error('Lizenztyp muss angegeben werden');
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Lizenzinformationen für Token ${tokenId}:`, licenseInfo);
      
      // Standardisiere Lizenzinformationen
      const standardizedLicense = {
        type: licenseInfo.type,
        name: licenseInfo.name || this.getLicenseName(licenseInfo.type),
        url: licenseInfo.url || this.getLicenseUrl(licenseInfo.type),
        commercial: licenseInfo.commercial !== undefined ? licenseInfo.commercial : true,
        derivatives: licenseInfo.derivatives !== undefined ? licenseInfo.derivatives : true,
        attribution: licenseInfo.attribution !== undefined ? licenseInfo.attribution : true
      };
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        license: standardizedLicense
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Lizenzinformationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Lizenzinformationen
   */
  async getLicenseInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Lizenzinformationen für Token ${tokenId} ab`);
      
      // Simulierte Lizenzinformationen
      return {
        tokenId,
        license: {
          type: 'cc-by',
          name: 'Creative Commons Attribution 4.0',
          url: 'https://creativecommons.org/licenses/by/4.0/',
          commercial: true,
          derivatives: true,
          attribution: true
        }
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Nutzung mit der Lizenz kompatibel ist
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} usage - Nutzungsinformationen
   * @returns {Promise<Object>} - Kompatibilitätsinformationen
   */
  async checkLicenseCompatibility(tokenId, usage) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Lizenzinformationen ab
      const licenseInfo = await this.getLicenseInfo(tokenId);
      
      // Prüfe Kompatibilität
      const isCommercialCompatible = !usage.commercial || licenseInfo.license.commercial;
      const isDerivativesCompatible = !usage.derivatives || licenseInfo.license.derivatives;
      const isAttributionCompatible = !licenseInfo.license.attribution || usage.attribution;
      
      const isCompatible = isCommercialCompatible && isDerivativesCompatible && isAttributionCompatible;
      
      return {
        tokenId,
        license: licenseInfo.license,
        usage,
        isCompatible,
        incompatibilities: {
          commercial: !isCommercialCompatible,
          derivatives: !isDerivativesCompatible,
          attribution: !isAttributionCompatible
        }
      };
    } catch (error) {
      console.error('Fehler bei der Prüfung der Lizenzkompatibilität:', error);
      throw error;
    }
  }
  
  /**
   * Gibt den Namen einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseName(type) {
    const licenseNames = {
      'cc-by': 'Creative Commons Attribution 4.0',
      'cc-by-sa': 'Creative Commons Attribution-ShareAlike 4.0',
      'cc-by-nc': 'Creative Commons Attribution-NonCommercial 4.0',
      'cc-by-nc-sa': 'Creative Commons Attribution-NonCommercial-ShareAlike 4.0',
      'cc-by-nd': 'Creative Commons Attribution-NoDerivatives 4.0',
      'cc-by-nc-nd': 'Creative Commons Attribution-NonCommercial-NoDerivatives 4.0',
      'cc0': 'Creative Commons Zero v1.0 Universal',
      'mit': 'MIT License',
      'apache-2.0': 'Apache License 2.0',
      'gpl-3.0': 'GNU General Public License v3.0'
    };
    
    return licenseNames[type] || 'Custom License';
  }
  
  /**
   * Gibt die URL einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseUrl(type) {
    const licenseUrls = {
      'cc-by': 'https://creativecommons.org/licenses/by/4.0/',
      'cc-by-sa': 'https://creativecommons.org/licenses/by-sa/4.0/',
      'cc-by-nc': 'https://creativecommons.org/licenses/by-nc/4.0/',
      'cc-by-nc-sa': 'https://creativecommons.org/licenses/by-nc-sa/4.0/',
      'cc-by-nd': 'https://creativecommons.org/licenses/by-nd/4.0/',
      'cc-by-nc-nd': 'https://creativecommons.org/licenses/by-nc-nd/4.0/',
      'cc0': 'https://creativecommons.org/publicdomain/zero/1.0/',
      'mit': 'https://opensource.org/licenses/MIT',
      'apache-2.0': 'https://opensource.org/licenses/Apache-2.0',
      'gpl-3.0': 'https://www.gnu.org/licenses/gpl-3.0.en.html'
    };
    
    return licenseUrls[type] || '';
  }
}

export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract; * Implementiert ERC-2981 für Royalties und erweiterte Lizenzierungsfunktionen
 */

class DoiRoyaltyContract {
  constructor(api, contractAddress, options = {}) {
    this.api = api;
    this.contractAddress = contractAddress;
    this.options = {
      defaultRoyaltyPercentage: 5, // 5% Standardroyalty
      maxRoyaltyPercentage: 15,    // Maximale Royalty von 15%
      ...options
    };
    
    // Initialisiere den Contract
    this.initialize();
  }
  
  /**
   * Initialisiert den Contract
   * @private
   */
  async initialize() {
    try {
      // In einer realen Implementierung würden wir hier den Contract ABI laden
      // und eine Verbindung zum Contract herstellen
      console.log('Initialisiere DOI Royalty Contract:', this.contractAddress);
      
      // Simuliere Contract-Initialisierung
      this.initialized = true;
    } catch (error) {
      console.error('Fehler bei der Initialisierung des Royalty Contracts:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Royalty-Informationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Array} recipients - Liste der Empfänger (Adressen)
   * @param {Array} shares - Liste der Anteile in Prozent
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setRoyaltyInfo(tokenId, recipients, shares) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Eingaben
    if (!Array.isArray(recipients) || !Array.isArray(shares)) {
      throw new Error('Empfänger und Anteile müssen Arrays sein');
    }
    
    if (recipients.length !== shares.length) {
      throw new Error('Anzahl der Empfänger und Anteile muss übereinstimmen');
    }
    
    // Prüfe, ob die Summe der Anteile 100% nicht überschreitet
    const totalShares = shares.reduce((sum, share) => sum + share, 0);
    if (totalShares > 100) {
      throw new Error('Summe der Anteile darf 100% nicht überschreiten');
    }
    
    // Prüfe, ob die Royalty-Rate das Maximum nicht überschreitet
    const royaltyRate = totalShares / 100 * this.options.defaultRoyaltyPercentage;
    if (royaltyRate > this.options.maxRoyaltyPercentage) {
      throw new Error(`Royalty-Rate darf ${this.options.maxRoyaltyPercentage}% nicht überschreiten`);
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Royalty-Informationen für Token ${tokenId}:`, {
        recipients,
        shares,
        royaltyRate: `${royaltyRate}%`
      });
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        recipients,
        shares,
        royaltyRate
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Royalty-Informationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Royalty-Informationen
   */
  async getRoyaltyInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Royalty-Informationen für Token ${tokenId} ab`);
      
      // Simulierte Royalty-Informationen
      return {
        tokenId,
        recipients: ['0x1234...', '0x5678...'],
        shares: [60, 40], // 60% und 40%
        royaltyRate: this.options.defaultRoyaltyPercentage,
        totalRoyalty: this.options.defaultRoyaltyPercentage
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Berechnet die Royalty-Zahlung für einen Verkauf
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Royalty-Zahlungsinformationen
   */
  async calculateRoyaltyPayment(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Royalty-Informationen ab
      const royaltyInfo = await this.getRoyaltyInfo(tokenId);
      
      // Berechne Royalty-Betrag
      const royaltyAmount = salePrice * (royaltyInfo.royaltyRate / 100);
      
      // Berechne Zahlungen pro Empfänger
      const payments = royaltyInfo.recipients.map((recipient, index) => ({
        recipient,
        share: royaltyInfo.shares[index],
        amount: royaltyAmount * (royaltyInfo.shares[index] / 100)
      }));
      
      return {
        tokenId,
        salePrice,
        royaltyRate: royaltyInfo.royaltyRate,
        totalRoyalty: royaltyAmount,
        payments
      };
    } catch (error) {
      console.error('Fehler bei der Berechnung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Führt eine Royalty-Zahlung für einen Verkauf durch
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async payRoyalties(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Berechne Royalty-Zahlungen
      const royaltyPayment = await this.calculateRoyaltyPayment(tokenId, salePrice);
      
      // In einer realen Implementierung würden wir hier die Zahlungen durchführen
      console.log(`Führe Royalty-Zahlungen für Token ${tokenId} durch:`, royaltyPayment);
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        salePrice,
        royaltyRate: royaltyPayment.royaltyRate,
        totalRoyalty: royaltyPayment.totalRoyalty,
        payments: royaltyPayment.payments
      };
    } catch (error) {
      console.error('Fehler bei der Durchführung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Lizenzinformationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} licenseInfo - Lizenzinformationen
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setLicenseInfo(tokenId, licenseInfo) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Lizenzinformationen
    if (!licenseInfo.type) {
      throw new Error('Lizenztyp muss angegeben werden');
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Lizenzinformationen für Token ${tokenId}:`, licenseInfo);
      
      // Standardisiere Lizenzinformationen
      const standardizedLicense = {
        type: licenseInfo.type,
        name: licenseInfo.name || this.getLicenseName(licenseInfo.type),
        url: licenseInfo.url || this.getLicenseUrl(licenseInfo.type),
        commercial: licenseInfo.commercial !== undefined ? licenseInfo.commercial : true,
        derivatives: licenseInfo.derivatives !== undefined ? licenseInfo.derivatives : true,
        attribution: licenseInfo.attribution !== undefined ? licenseInfo.attribution : true
      };
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        license: standardizedLicense
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Lizenzinformationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Lizenzinformationen
   */
  async getLicenseInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Lizenzinformationen für Token ${tokenId} ab`);
      
      // Simulierte Lizenzinformationen
      return {
        tokenId,
        license: {
          type: 'cc-by',
          name: 'Creative Commons Attribution 4.0',
          url: 'https://creativecommons.org/licenses/by/4.0/',
          commercial: true,
          derivatives: true,
          attribution: true
        }
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Nutzung mit der Lizenz kompatibel ist
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} usage - Nutzungsinformationen
   * @returns {Promise<Object>} - Kompatibilitätsinformationen
   */
  async checkLicenseCompatibility(tokenId, usage) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Lizenzinformationen ab
      const licenseInfo = await this.getLicenseInfo(tokenId);
      
      // Prüfe Kompatibilität
      const isCommercialCompatible = !usage.commercial || licenseInfo.license.commercial;
      const isDerivativesCompatible = !usage.derivatives || licenseInfo.license.derivatives;
      const isAttributionCompatible = !licenseInfo.license.attribution || usage.attribution;
      
      const isCompatible = isCommercialCompatible && isDerivativesCompatible && isAttributionCompatible;
      
      return {
        tokenId,
        license: licenseInfo.license,
        usage,
        isCompatible,
        incompatibilities: {
          commercial: !isCommercialCompatible,
          derivatives: !isDerivativesCompatible,
          attribution: !isAttributionCompatible
        }
      };
    } catch (error) {
      console.error('Fehler bei der Prüfung der Lizenzkompatibilität:', error);
      throw error;
    }
  }
  
  /**
   * Gibt den Namen einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseName(type) {
    const licenseNames = {
      'cc-by': 'Creative Commons Attribution 4.0',
      'cc-by-sa': 'Creative Commons Attribution-ShareAlike 4.0',
      'cc-by-nc': 'Creative Commons Attribution-NonCommercial 4.0',
      'cc-by-nc-sa': 'Creative Commons Attribution-NonCommercial-ShareAlike 4.0',
      'cc-by-nd': 'Creative Commons Attribution-NoDerivatives 4.0',
      'cc-by-nc-nd': 'Creative Commons Attribution-NonCommercial-NoDerivatives 4.0',
      'cc0': 'Creative Commons Zero v1.0 Universal',
      'mit': 'MIT License',
      'apache-2.0': 'Apache License 2.0',
      'gpl-3.0': 'GNU General Public License v3.0'
    };
    
    return licenseNames[type] || 'Custom License';
  }
  
  /**
   * Gibt die URL einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseUrl(type) {
    const licenseUrls = {
      'cc-by': 'https://creativecommons.org/licenses/by/4.0/',
      'cc-by-sa': 'https://creativecommons.org/licenses/by-sa/4.0/',
      'cc-by-nc': 'https://creativecommons.org/licenses/by-nc/4.0/',
      'cc-by-nc-sa': 'https://creativecommons.org/licenses/by-nc-sa/4.0/',
      'cc-by-nd': 'https://creativecommons.org/licenses/by-nd/4.0/',
      'cc-by-nc-nd': 'https://creativecommons.org/licenses/by-nc-nd/4.0/',
      'cc0': 'https://creativecommons.org/publicdomain/zero/1.0/',
      'mit': 'https://opensource.org/licenses/MIT',
      'apache-2.0': 'https://opensource.org/licenses/Apache-2.0',
      'gpl-3.0': 'https://www.gnu.org/licenses/gpl-3.0.en.html'
    };
    
    return licenseUrls[type] || '';
  }
}

export default DoiRoyaltyContract;export default DoiRoyaltyContract;
export default DoiRoyaltyContract;export default DoiRoyaltyContract; * Implementiert ERC-2981 für Royalties und erweiterte Lizenzierungsfunktionen
 */

class DoiRoyaltyContract {
  constructor(api, contractAddress, options = {}) {
    this.api = api;
    this.contractAddress = contractAddress;
    this.options = {
      defaultRoyaltyPercentage: 5, // 5% Standardroyalty
      maxRoyaltyPercentage: 15,    // Maximale Royalty von 15%
      ...options
    };
    
    // Initialisiere den Contract
    this.initialize();
  }
  
  /**
   * Initialisiert den Contract
   * @private
   */
  async initialize() {
    try {
      // In einer realen Implementierung würden wir hier den Contract ABI laden
      // und eine Verbindung zum Contract herstellen
      console.log('Initialisiere DOI Royalty Contract:', this.contractAddress);
      
      // Simuliere Contract-Initialisierung
      this.initialized = true;
    } catch (error) {
      console.error('Fehler bei der Initialisierung des Royalty Contracts:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Royalty-Informationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Array} recipients - Liste der Empfänger (Adressen)
   * @param {Array} shares - Liste der Anteile in Prozent
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setRoyaltyInfo(tokenId, recipients, shares) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Eingaben
    if (!Array.isArray(recipients) || !Array.isArray(shares)) {
      throw new Error('Empfänger und Anteile müssen Arrays sein');
    }
    
    if (recipients.length !== shares.length) {
      throw new Error('Anzahl der Empfänger und Anteile muss übereinstimmen');
    }
    
    // Prüfe, ob die Summe der Anteile 100% nicht überschreitet
    const totalShares = shares.reduce((sum, share) => sum + share, 0);
    if (totalShares > 100) {
      throw new Error('Summe der Anteile darf 100% nicht überschreiten');
    }
    
    // Prüfe, ob die Royalty-Rate das Maximum nicht überschreitet
    const royaltyRate = totalShares / 100 * this.options.defaultRoyaltyPercentage;
    if (royaltyRate > this.options.maxRoyaltyPercentage) {
      throw new Error(`Royalty-Rate darf ${this.options.maxRoyaltyPercentage}% nicht überschreiten`);
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Royalty-Informationen für Token ${tokenId}:`, {
        recipients,
        shares,
        royaltyRate: `${royaltyRate}%`
      });
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        recipients,
        shares,
        royaltyRate
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Royalty-Informationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Royalty-Informationen
   */
  async getRoyaltyInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Royalty-Informationen für Token ${tokenId} ab`);
      
      // Simulierte Royalty-Informationen
      return {
        tokenId,
        recipients: ['0x1234...', '0x5678...'],
        shares: [60, 40], // 60% und 40%
        royaltyRate: this.options.defaultRoyaltyPercentage,
        totalRoyalty: this.options.defaultRoyaltyPercentage
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Berechnet die Royalty-Zahlung für einen Verkauf
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Royalty-Zahlungsinformationen
   */
  async calculateRoyaltyPayment(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Royalty-Informationen ab
      const royaltyInfo = await this.getRoyaltyInfo(tokenId);
      
      // Berechne Royalty-Betrag
      const royaltyAmount = salePrice * (royaltyInfo.royaltyRate / 100);
      
      // Berechne Zahlungen pro Empfänger
      const payments = royaltyInfo.recipients.map((recipient, index) => ({
        recipient,
        share: royaltyInfo.shares[index],
        amount: royaltyAmount * (royaltyInfo.shares[index] / 100)
      }));
      
      return {
        tokenId,
        salePrice,
        royaltyRate: royaltyInfo.royaltyRate,
        totalRoyalty: royaltyAmount,
        payments
      };
    } catch (error) {
      console.error('Fehler bei der Berechnung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Führt eine Royalty-Zahlung für einen Verkauf durch
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async payRoyalties(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Berechne Royalty-Zahlungen
      const royaltyPayment = await this.calculateRoyaltyPayment(tokenId, salePrice);
      
      // In einer realen Implementierung würden wir hier die Zahlungen durchführen
      console.log(`Führe Royalty-Zahlungen für Token ${tokenId} durch:`, royaltyPayment);
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        salePrice,
        royaltyRate: royaltyPayment.royaltyRate,
        totalRoyalty: royaltyPayment.totalRoyalty,
        payments: royaltyPayment.payments
      };
    } catch (error) {
      console.error('Fehler bei der Durchführung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Lizenzinformationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} licenseInfo - Lizenzinformationen
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setLicenseInfo(tokenId, licenseInfo) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Lizenzinformationen
    if (!licenseInfo.type) {
      throw new Error('Lizenztyp muss angegeben werden');
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Lizenzinformationen für Token ${tokenId}:`, licenseInfo);
      
      // Standardisiere Lizenzinformationen
      const standardizedLicense = {
        type: licenseInfo.type,
        name: licenseInfo.name || this.getLicenseName(licenseInfo.type),
        url: licenseInfo.url || this.getLicenseUrl(licenseInfo.type),
        commercial: licenseInfo.commercial !== undefined ? licenseInfo.commercial : true,
        derivatives: licenseInfo.derivatives !== undefined ? licenseInfo.derivatives : true,
        attribution: licenseInfo.attribution !== undefined ? licenseInfo.attribution : true
      };
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        license: standardizedLicense
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Lizenzinformationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Lizenzinformationen
   */
  async getLicenseInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Lizenzinformationen für Token ${tokenId} ab`);
      
      // Simulierte Lizenzinformationen
      return {
        tokenId,
        license: {
          type: 'cc-by',
          name: 'Creative Commons Attribution 4.0',
          url: 'https://creativecommons.org/licenses/by/4.0/',
          commercial: true,
          derivatives: true,
          attribution: true
        }
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Nutzung mit der Lizenz kompatibel ist
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} usage - Nutzungsinformationen
   * @returns {Promise<Object>} - Kompatibilitätsinformationen
   */
  async checkLicenseCompatibility(tokenId, usage) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Lizenzinformationen ab
      const licenseInfo = await this.getLicenseInfo(tokenId);
      
      // Prüfe Kompatibilität
      const isCommercialCompatible = !usage.commercial || licenseInfo.license.commercial;
      const isDerivativesCompatible = !usage.derivatives || licenseInfo.license.derivatives;
      const isAttributionCompatible = !licenseInfo.license.attribution || usage.attribution;
      
      const isCompatible = isCommercialCompatible && isDerivativesCompatible && isAttributionCompatible;
      
      return {
        tokenId,
        license: licenseInfo.license,
        usage,
        isCompatible,
        incompatibilities: {
          commercial: !isCommercialCompatible,
          derivatives: !isDerivativesCompatible,
          attribution: !isAttributionCompatible
        }
      };
    } catch (error) {
      console.error('Fehler bei der Prüfung der Lizenzkompatibilität:', error);
      throw error;
    }
  }
  
  /**
   * Gibt den Namen einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseName(type) {
    const licenseNames = {
      'cc-by': 'Creative Commons Attribution 4.0',
      'cc-by-sa': 'Creative Commons Attribution-ShareAlike 4.0',
      'cc-by-nc': 'Creative Commons Attribution-NonCommercial 4.0',
      'cc-by-nc-sa': 'Creative Commons Attribution-NonCommercial-ShareAlike 4.0',
      'cc-by-nd': 'Creative Commons Attribution-NoDerivatives 4.0',
      'cc-by-nc-nd': 'Creative Commons Attribution-NonCommercial-NoDerivatives 4.0',
      'cc0': 'Creative Commons Zero v1.0 Universal',
      'mit': 'MIT License',
      'apache-2.0': 'Apache License 2.0',
      'gpl-3.0': 'GNU General Public License v3.0'
    };
    
    return licenseNames[type] || 'Custom License';
  }
  
  /**
   * Gibt die URL einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseUrl(type) {
    const licenseUrls = {
      'cc-by': 'https://creativecommons.org/licenses/by/4.0/',
      'cc-by-sa': 'https://creativecommons.org/licenses/by-sa/4.0/',
      'cc-by-nc': 'https://creativecommons.org/licenses/by-nc/4.0/',
      'cc-by-nc-sa': 'https://creativecommons.org/licenses/by-nc-sa/4.0/',
      'cc-by-nd': 'https://creativecommons.org/licenses/by-nd/4.0/',
      'cc-by-nc-nd': 'https://creativecommons.org/licenses/by-nc-nd/4.0/',
      'cc0': 'https://creativecommons.org/publicdomain/zero/1.0/',
      'mit': 'https://opensource.org/licenses/MIT',
      'apache-2.0': 'https://opensource.org/licenses/Apache-2.0',
      'gpl-3.0': 'https://www.gnu.org/licenses/gpl-3.0.en.html'
    };
    
    return licenseUrls[type] || '';
  }
}

export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract; * Implementiert ERC-2981 für Royalties und erweiterte Lizenzierungsfunktionen
 */

class DoiRoyaltyContract {
  constructor(api, contractAddress, options = {}) {
    this.api = api;
    this.contractAddress = contractAddress;
    this.options = {
      defaultRoyaltyPercentage: 5, // 5% Standardroyalty
      maxRoyaltyPercentage: 15,    // Maximale Royalty von 15%
      ...options
    };
    
    // Initialisiere den Contract
    this.initialize();
  }
  
  /**
   * Initialisiert den Contract
   * @private
   */
  async initialize() {
    try {
      // In einer realen Implementierung würden wir hier den Contract ABI laden
      // und eine Verbindung zum Contract herstellen
      console.log('Initialisiere DOI Royalty Contract:', this.contractAddress);
      
      // Simuliere Contract-Initialisierung
      this.initialized = true;
    } catch (error) {
      console.error('Fehler bei der Initialisierung des Royalty Contracts:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Royalty-Informationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Array} recipients - Liste der Empfänger (Adressen)
   * @param {Array} shares - Liste der Anteile in Prozent
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setRoyaltyInfo(tokenId, recipients, shares) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Eingaben
    if (!Array.isArray(recipients) || !Array.isArray(shares)) {
      throw new Error('Empfänger und Anteile müssen Arrays sein');
    }
    
    if (recipients.length !== shares.length) {
      throw new Error('Anzahl der Empfänger und Anteile muss übereinstimmen');
    }
    
    // Prüfe, ob die Summe der Anteile 100% nicht überschreitet
    const totalShares = shares.reduce((sum, share) => sum + share, 0);
    if (totalShares > 100) {
      throw new Error('Summe der Anteile darf 100% nicht überschreiten');
    }
    
    // Prüfe, ob die Royalty-Rate das Maximum nicht überschreitet
    const royaltyRate = totalShares / 100 * this.options.defaultRoyaltyPercentage;
    if (royaltyRate > this.options.maxRoyaltyPercentage) {
      throw new Error(`Royalty-Rate darf ${this.options.maxRoyaltyPercentage}% nicht überschreiten`);
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Royalty-Informationen für Token ${tokenId}:`, {
        recipients,
        shares,
        royaltyRate: `${royaltyRate}%`
      });
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        recipients,
        shares,
        royaltyRate
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Royalty-Informationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Royalty-Informationen
   */
  async getRoyaltyInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Royalty-Informationen für Token ${tokenId} ab`);
      
      // Simulierte Royalty-Informationen
      return {
        tokenId,
        recipients: ['0x1234...', '0x5678...'],
        shares: [60, 40], // 60% und 40%
        royaltyRate: this.options.defaultRoyaltyPercentage,
        totalRoyalty: this.options.defaultRoyaltyPercentage
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Berechnet die Royalty-Zahlung für einen Verkauf
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Royalty-Zahlungsinformationen
   */
  async calculateRoyaltyPayment(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Royalty-Informationen ab
      const royaltyInfo = await this.getRoyaltyInfo(tokenId);
      
      // Berechne Royalty-Betrag
      const royaltyAmount = salePrice * (royaltyInfo.royaltyRate / 100);
      
      // Berechne Zahlungen pro Empfänger
      const payments = royaltyInfo.recipients.map((recipient, index) => ({
        recipient,
        share: royaltyInfo.shares[index],
        amount: royaltyAmount * (royaltyInfo.shares[index] / 100)
      }));
      
      return {
        tokenId,
        salePrice,
        royaltyRate: royaltyInfo.royaltyRate,
        totalRoyalty: royaltyAmount,
        payments
      };
    } catch (error) {
      console.error('Fehler bei der Berechnung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Führt eine Royalty-Zahlung für einen Verkauf durch
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async payRoyalties(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Berechne Royalty-Zahlungen
      const royaltyPayment = await this.calculateRoyaltyPayment(tokenId, salePrice);
      
      // In einer realen Implementierung würden wir hier die Zahlungen durchführen
      console.log(`Führe Royalty-Zahlungen für Token ${tokenId} durch:`, royaltyPayment);
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        salePrice,
        royaltyRate: royaltyPayment.royaltyRate,
        totalRoyalty: royaltyPayment.totalRoyalty,
        payments: royaltyPayment.payments
      };
    } catch (error) {
      console.error('Fehler bei der Durchführung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Lizenzinformationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} licenseInfo - Lizenzinformationen
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setLicenseInfo(tokenId, licenseInfo) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Lizenzinformationen
    if (!licenseInfo.type) {
      throw new Error('Lizenztyp muss angegeben werden');
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Lizenzinformationen für Token ${tokenId}:`, licenseInfo);
      
      // Standardisiere Lizenzinformationen
      const standardizedLicense = {
        type: licenseInfo.type,
        name: licenseInfo.name || this.getLicenseName(licenseInfo.type),
        url: licenseInfo.url || this.getLicenseUrl(licenseInfo.type),
        commercial: licenseInfo.commercial !== undefined ? licenseInfo.commercial : true,
        derivatives: licenseInfo.derivatives !== undefined ? licenseInfo.derivatives : true,
        attribution: licenseInfo.attribution !== undefined ? licenseInfo.attribution : true
      };
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        license: standardizedLicense
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Lizenzinformationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Lizenzinformationen
   */
  async getLicenseInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Lizenzinformationen für Token ${tokenId} ab`);
      
      // Simulierte Lizenzinformationen
      return {
        tokenId,
        license: {
          type: 'cc-by',
          name: 'Creative Commons Attribution 4.0',
          url: 'https://creativecommons.org/licenses/by/4.0/',
          commercial: true,
          derivatives: true,
          attribution: true
        }
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Nutzung mit der Lizenz kompatibel ist
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} usage - Nutzungsinformationen
   * @returns {Promise<Object>} - Kompatibilitätsinformationen
   */
  async checkLicenseCompatibility(tokenId, usage) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Lizenzinformationen ab
      const licenseInfo = await this.getLicenseInfo(tokenId);
      
      // Prüfe Kompatibilität
      const isCommercialCompatible = !usage.commercial || licenseInfo.license.commercial;
      const isDerivativesCompatible = !usage.derivatives || licenseInfo.license.derivatives;
      const isAttributionCompatible = !licenseInfo.license.attribution || usage.attribution;
      
      const isCompatible = isCommercialCompatible && isDerivativesCompatible && isAttributionCompatible;
      
      return {
        tokenId,
        license: licenseInfo.license,
        usage,
        isCompatible,
        incompatibilities: {
          commercial: !isCommercialCompatible,
          derivatives: !isDerivativesCompatible,
          attribution: !isAttributionCompatible
        }
      };
    } catch (error) {
      console.error('Fehler bei der Prüfung der Lizenzkompatibilität:', error);
      throw error;
    }
  }
  
  /**
   * Gibt den Namen einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseName(type) {
    const licenseNames = {
      'cc-by': 'Creative Commons Attribution 4.0',
      'cc-by-sa': 'Creative Commons Attribution-ShareAlike 4.0',
      'cc-by-nc': 'Creative Commons Attribution-NonCommercial 4.0',
      'cc-by-nc-sa': 'Creative Commons Attribution-NonCommercial-ShareAlike 4.0',
      'cc-by-nd': 'Creative Commons Attribution-NoDerivatives 4.0',
      'cc-by-nc-nd': 'Creative Commons Attribution-NonCommercial-NoDerivatives 4.0',
      'cc0': 'Creative Commons Zero v1.0 Universal',
      'mit': 'MIT License',
      'apache-2.0': 'Apache License 2.0',
      'gpl-3.0': 'GNU General Public License v3.0'
    };
    
    return licenseNames[type] || 'Custom License';
  }
  
  /**
   * Gibt die URL einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseUrl(type) {
    const licenseUrls = {
      'cc-by': 'https://creativecommons.org/licenses/by/4.0/',
      'cc-by-sa': 'https://creativecommons.org/licenses/by-sa/4.0/',
      'cc-by-nc': 'https://creativecommons.org/licenses/by-nc/4.0/',
      'cc-by-nc-sa': 'https://creativecommons.org/licenses/by-nc-sa/4.0/',
      'cc-by-nd': 'https://creativecommons.org/licenses/by-nd/4.0/',
      'cc-by-nc-nd': 'https://creativecommons.org/licenses/by-nc-nd/4.0/',
      'cc0': 'https://creativecommons.org/publicdomain/zero/1.0/',
      'mit': 'https://opensource.org/licenses/MIT',
      'apache-2.0': 'https://opensource.org/licenses/Apache-2.0',
      'gpl-3.0': 'https://www.gnu.org/licenses/gpl-3.0.en.html'
    };
    
    return licenseUrls[type] || '';
  }
}

export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract; * Implementiert ERC-2981 für Royalties und erweiterte Lizenzierungsfunktionen
 */

class DoiRoyaltyContract {
  constructor(api, contractAddress, options = {}) {
    this.api = api;
    this.contractAddress = contractAddress;
    this.options = {
      defaultRoyaltyPercentage: 5, // 5% Standardroyalty
      maxRoyaltyPercentage: 15,    // Maximale Royalty von 15%
      ...options
    };
    
    // Initialisiere den Contract
    this.initialize();
  }
  
  /**
   * Initialisiert den Contract
   * @private
   */
  async initialize() {
    try {
      // In einer realen Implementierung würden wir hier den Contract ABI laden
      // und eine Verbindung zum Contract herstellen
      console.log('Initialisiere DOI Royalty Contract:', this.contractAddress);
      
      // Simuliere Contract-Initialisierung
      this.initialized = true;
    } catch (error) {
      console.error('Fehler bei der Initialisierung des Royalty Contracts:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Royalty-Informationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Array} recipients - Liste der Empfänger (Adressen)
   * @param {Array} shares - Liste der Anteile in Prozent
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setRoyaltyInfo(tokenId, recipients, shares) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Eingaben
    if (!Array.isArray(recipients) || !Array.isArray(shares)) {
      throw new Error('Empfänger und Anteile müssen Arrays sein');
    }
    
    if (recipients.length !== shares.length) {
      throw new Error('Anzahl der Empfänger und Anteile muss übereinstimmen');
    }
    
    // Prüfe, ob die Summe der Anteile 100% nicht überschreitet
    const totalShares = shares.reduce((sum, share) => sum + share, 0);
    if (totalShares > 100) {
      throw new Error('Summe der Anteile darf 100% nicht überschreiten');
    }
    
    // Prüfe, ob die Royalty-Rate das Maximum nicht überschreitet
    const royaltyRate = totalShares / 100 * this.options.defaultRoyaltyPercentage;
    if (royaltyRate > this.options.maxRoyaltyPercentage) {
      throw new Error(`Royalty-Rate darf ${this.options.maxRoyaltyPercentage}% nicht überschreiten`);
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Royalty-Informationen für Token ${tokenId}:`, {
        recipients,
        shares,
        royaltyRate: `${royaltyRate}%`
      });
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        recipients,
        shares,
        royaltyRate
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Royalty-Informationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Royalty-Informationen
   */
  async getRoyaltyInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Royalty-Informationen für Token ${tokenId} ab`);
      
      // Simulierte Royalty-Informationen
      return {
        tokenId,
        recipients: ['0x1234...', '0x5678...'],
        shares: [60, 40], // 60% und 40%
        royaltyRate: this.options.defaultRoyaltyPercentage,
        totalRoyalty: this.options.defaultRoyaltyPercentage
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Berechnet die Royalty-Zahlung für einen Verkauf
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Royalty-Zahlungsinformationen
   */
  async calculateRoyaltyPayment(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Royalty-Informationen ab
      const royaltyInfo = await this.getRoyaltyInfo(tokenId);
      
      // Berechne Royalty-Betrag
      const royaltyAmount = salePrice * (royaltyInfo.royaltyRate / 100);
      
      // Berechne Zahlungen pro Empfänger
      const payments = royaltyInfo.recipients.map((recipient, index) => ({
        recipient,
        share: royaltyInfo.shares[index],
        amount: royaltyAmount * (royaltyInfo.shares[index] / 100)
      }));
      
      return {
        tokenId,
        salePrice,
        royaltyRate: royaltyInfo.royaltyRate,
        totalRoyalty: royaltyAmount,
        payments
      };
    } catch (error) {
      console.error('Fehler bei der Berechnung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Führt eine Royalty-Zahlung für einen Verkauf durch
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async payRoyalties(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Berechne Royalty-Zahlungen
      const royaltyPayment = await this.calculateRoyaltyPayment(tokenId, salePrice);
      
      // In einer realen Implementierung würden wir hier die Zahlungen durchführen
      console.log(`Führe Royalty-Zahlungen für Token ${tokenId} durch:`, royaltyPayment);
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        salePrice,
        royaltyRate: royaltyPayment.royaltyRate,
        totalRoyalty: royaltyPayment.totalRoyalty,
        payments: royaltyPayment.payments
      };
    } catch (error) {
      console.error('Fehler bei der Durchführung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Lizenzinformationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} licenseInfo - Lizenzinformationen
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setLicenseInfo(tokenId, licenseInfo) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Lizenzinformationen
    if (!licenseInfo.type) {
      throw new Error('Lizenztyp muss angegeben werden');
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Lizenzinformationen für Token ${tokenId}:`, licenseInfo);
      
      // Standardisiere Lizenzinformationen
      const standardizedLicense = {
        type: licenseInfo.type,
        name: licenseInfo.name || this.getLicenseName(licenseInfo.type),
        url: licenseInfo.url || this.getLicenseUrl(licenseInfo.type),
        commercial: licenseInfo.commercial !== undefined ? licenseInfo.commercial : true,
        derivatives: licenseInfo.derivatives !== undefined ? licenseInfo.derivatives : true,
        attribution: licenseInfo.attribution !== undefined ? licenseInfo.attribution : true
      };
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        license: standardizedLicense
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Lizenzinformationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Lizenzinformationen
   */
  async getLicenseInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Lizenzinformationen für Token ${tokenId} ab`);
      
      // Simulierte Lizenzinformationen
      return {
        tokenId,
        license: {
          type: 'cc-by',
          name: 'Creative Commons Attribution 4.0',
          url: 'https://creativecommons.org/licenses/by/4.0/',
          commercial: true,
          derivatives: true,
          attribution: true
        }
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Nutzung mit der Lizenz kompatibel ist
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} usage - Nutzungsinformationen
   * @returns {Promise<Object>} - Kompatibilitätsinformationen
   */
  async checkLicenseCompatibility(tokenId, usage) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Lizenzinformationen ab
      const licenseInfo = await this.getLicenseInfo(tokenId);
      
      // Prüfe Kompatibilität
      const isCommercialCompatible = !usage.commercial || licenseInfo.license.commercial;
      const isDerivativesCompatible = !usage.derivatives || licenseInfo.license.derivatives;
      const isAttributionCompatible = !licenseInfo.license.attribution || usage.attribution;
      
      const isCompatible = isCommercialCompatible && isDerivativesCompatible && isAttributionCompatible;
      
      return {
        tokenId,
        license: licenseInfo.license,
        usage,
        isCompatible,
        incompatibilities: {
          commercial: !isCommercialCompatible,
          derivatives: !isDerivativesCompatible,
          attribution: !isAttributionCompatible
        }
      };
    } catch (error) {
      console.error('Fehler bei der Prüfung der Lizenzkompatibilität:', error);
      throw error;
    }
  }
  
  /**
   * Gibt den Namen einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseName(type) {
    const licenseNames = {
      'cc-by': 'Creative Commons Attribution 4.0',
      'cc-by-sa': 'Creative Commons Attribution-ShareAlike 4.0',
      'cc-by-nc': 'Creative Commons Attribution-NonCommercial 4.0',
      'cc-by-nc-sa': 'Creative Commons Attribution-NonCommercial-ShareAlike 4.0',
      'cc-by-nd': 'Creative Commons Attribution-NoDerivatives 4.0',
      'cc-by-nc-nd': 'Creative Commons Attribution-NonCommercial-NoDerivatives 4.0',
      'cc0': 'Creative Commons Zero v1.0 Universal',
      'mit': 'MIT License',
      'apache-2.0': 'Apache License 2.0',
      'gpl-3.0': 'GNU General Public License v3.0'
    };
    
    return licenseNames[type] || 'Custom License';
  }
  
  /**
   * Gibt die URL einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseUrl(type) {
    const licenseUrls = {
      'cc-by': 'https://creativecommons.org/licenses/by/4.0/',
      'cc-by-sa': 'https://creativecommons.org/licenses/by-sa/4.0/',
      'cc-by-nc': 'https://creativecommons.org/licenses/by-nc/4.0/',
      'cc-by-nc-sa': 'https://creativecommons.org/licenses/by-nc-sa/4.0/',
      'cc-by-nd': 'https://creativecommons.org/licenses/by-nd/4.0/',
      'cc-by-nc-nd': 'https://creativecommons.org/licenses/by-nc-nd/4.0/',
      'cc0': 'https://creativecommons.org/publicdomain/zero/1.0/',
      'mit': 'https://opensource.org/licenses/MIT',
      'apache-2.0': 'https://opensource.org/licenses/Apache-2.0',
      'gpl-3.0': 'https://www.gnu.org/licenses/gpl-3.0.en.html'
    };
    
    return licenseUrls[type] || '';
  }
}

export default DoiRoyaltyContract;
export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract; * Implementiert ERC-2981 für Royalties und erweiterte Lizenzierungsfunktionen
 */

class DoiRoyaltyContract {
  constructor(api, contractAddress, options = {}) {
    this.api = api;
    this.contractAddress = contractAddress;
    this.options = {
      defaultRoyaltyPercentage: 5, // 5% Standardroyalty
      maxRoyaltyPercentage: 15,    // Maximale Royalty von 15%
      ...options
    };
    
    // Initialisiere den Contract
    this.initialize();
  }
  
  /**
   * Initialisiert den Contract
   * @private
   */
  async initialize() {
    try {
      // In einer realen Implementierung würden wir hier den Contract ABI laden
      // und eine Verbindung zum Contract herstellen
      console.log('Initialisiere DOI Royalty Contract:', this.contractAddress);
      
      // Simuliere Contract-Initialisierung
      this.initialized = true;
    } catch (error) {
      console.error('Fehler bei der Initialisierung des Royalty Contracts:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Royalty-Informationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Array} recipients - Liste der Empfänger (Adressen)
   * @param {Array} shares - Liste der Anteile in Prozent
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setRoyaltyInfo(tokenId, recipients, shares) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Eingaben
    if (!Array.isArray(recipients) || !Array.isArray(shares)) {
      throw new Error('Empfänger und Anteile müssen Arrays sein');
    }
    
    if (recipients.length !== shares.length) {
      throw new Error('Anzahl der Empfänger und Anteile muss übereinstimmen');
    }
    
    // Prüfe, ob die Summe der Anteile 100% nicht überschreitet
    const totalShares = shares.reduce((sum, share) => sum + share, 0);
    if (totalShares > 100) {
      throw new Error('Summe der Anteile darf 100% nicht überschreiten');
    }
    
    // Prüfe, ob die Royalty-Rate das Maximum nicht überschreitet
    const royaltyRate = totalShares / 100 * this.options.defaultRoyaltyPercentage;
    if (royaltyRate > this.options.maxRoyaltyPercentage) {
      throw new Error(`Royalty-Rate darf ${this.options.maxRoyaltyPercentage}% nicht überschreiten`);
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Royalty-Informationen für Token ${tokenId}:`, {
        recipients,
        shares,
        royaltyRate: `${royaltyRate}%`
      });
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        recipients,
        shares,
        royaltyRate
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Royalty-Informationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Royalty-Informationen
   */
  async getRoyaltyInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Royalty-Informationen für Token ${tokenId} ab`);
      
      // Simulierte Royalty-Informationen
      return {
        tokenId,
        recipients: ['0x1234...', '0x5678...'],
        shares: [60, 40], // 60% und 40%
        royaltyRate: this.options.defaultRoyaltyPercentage,
        totalRoyalty: this.options.defaultRoyaltyPercentage
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Berechnet die Royalty-Zahlung für einen Verkauf
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Royalty-Zahlungsinformationen
   */
  async calculateRoyaltyPayment(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Royalty-Informationen ab
      const royaltyInfo = await this.getRoyaltyInfo(tokenId);
      
      // Berechne Royalty-Betrag
      const royaltyAmount = salePrice * (royaltyInfo.royaltyRate / 100);
      
      // Berechne Zahlungen pro Empfänger
      const payments = royaltyInfo.recipients.map((recipient, index) => ({
        recipient,
        share: royaltyInfo.shares[index],
        amount: royaltyAmount * (royaltyInfo.shares[index] / 100)
      }));
      
      return {
        tokenId,
        salePrice,
        royaltyRate: royaltyInfo.royaltyRate,
        totalRoyalty: royaltyAmount,
        payments
      };
    } catch (error) {
      console.error('Fehler bei der Berechnung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Führt eine Royalty-Zahlung für einen Verkauf durch
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async payRoyalties(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Berechne Royalty-Zahlungen
      const royaltyPayment = await this.calculateRoyaltyPayment(tokenId, salePrice);
      
      // In einer realen Implementierung würden wir hier die Zahlungen durchführen
      console.log(`Führe Royalty-Zahlungen für Token ${tokenId} durch:`, royaltyPayment);
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        salePrice,
        royaltyRate: royaltyPayment.royaltyRate,
        totalRoyalty: royaltyPayment.totalRoyalty,
        payments: royaltyPayment.payments
      };
    } catch (error) {
      console.error('Fehler bei der Durchführung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Lizenzinformationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} licenseInfo - Lizenzinformationen
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setLicenseInfo(tokenId, licenseInfo) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Lizenzinformationen
    if (!licenseInfo.type) {
      throw new Error('Lizenztyp muss angegeben werden');
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Lizenzinformationen für Token ${tokenId}:`, licenseInfo);
      
      // Standardisiere Lizenzinformationen
      const standardizedLicense = {
        type: licenseInfo.type,
        name: licenseInfo.name || this.getLicenseName(licenseInfo.type),
        url: licenseInfo.url || this.getLicenseUrl(licenseInfo.type),
        commercial: licenseInfo.commercial !== undefined ? licenseInfo.commercial : true,
        derivatives: licenseInfo.derivatives !== undefined ? licenseInfo.derivatives : true,
        attribution: licenseInfo.attribution !== undefined ? licenseInfo.attribution : true
      };
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        license: standardizedLicense
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Lizenzinformationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Lizenzinformationen
   */
  async getLicenseInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Lizenzinformationen für Token ${tokenId} ab`);
      
      // Simulierte Lizenzinformationen
      return {
        tokenId,
        license: {
          type: 'cc-by',
          name: 'Creative Commons Attribution 4.0',
          url: 'https://creativecommons.org/licenses/by/4.0/',
          commercial: true,
          derivatives: true,
          attribution: true
        }
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Nutzung mit der Lizenz kompatibel ist
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} usage - Nutzungsinformationen
   * @returns {Promise<Object>} - Kompatibilitätsinformationen
   */
  async checkLicenseCompatibility(tokenId, usage) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Lizenzinformationen ab
      const licenseInfo = await this.getLicenseInfo(tokenId);
      
      // Prüfe Kompatibilität
      const isCommercialCompatible = !usage.commercial || licenseInfo.license.commercial;
      const isDerivativesCompatible = !usage.derivatives || licenseInfo.license.derivatives;
      const isAttributionCompatible = !licenseInfo.license.attribution || usage.attribution;
      
      const isCompatible = isCommercialCompatible && isDerivativesCompatible && isAttributionCompatible;
      
      return {
        tokenId,
        license: licenseInfo.license,
        usage,
        isCompatible,
        incompatibilities: {
          commercial: !isCommercialCompatible,
          derivatives: !isDerivativesCompatible,
          attribution: !isAttributionCompatible
        }
      };
    } catch (error) {
      console.error('Fehler bei der Prüfung der Lizenzkompatibilität:', error);
      throw error;
    }
  }
  
  /**
   * Gibt den Namen einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseName(type) {
    const licenseNames = {
      'cc-by': 'Creative Commons Attribution 4.0',
      'cc-by-sa': 'Creative Commons Attribution-ShareAlike 4.0',
      'cc-by-nc': 'Creative Commons Attribution-NonCommercial 4.0',
      'cc-by-nc-sa': 'Creative Commons Attribution-NonCommercial-ShareAlike 4.0',
      'cc-by-nd': 'Creative Commons Attribution-NoDerivatives 4.0',
      'cc-by-nc-nd': 'Creative Commons Attribution-NonCommercial-NoDerivatives 4.0',
      'cc0': 'Creative Commons Zero v1.0 Universal',
      'mit': 'MIT License',
      'apache-2.0': 'Apache License 2.0',
      'gpl-3.0': 'GNU General Public License v3.0'
    };
    
    return licenseNames[type] || 'Custom License';
  }
  
  /**
   * Gibt die URL einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseUrl(type) {
    const licenseUrls = {
      'cc-by': 'https://creativecommons.org/licenses/by/4.0/',
      'cc-by-sa': 'https://creativecommons.org/licenses/by-sa/4.0/',
      'cc-by-nc': 'https://creativecommons.org/licenses/by-nc/4.0/',
      'cc-by-nc-sa': 'https://creativecommons.org/licenses/by-nc-sa/4.0/',
      'cc-by-nd': 'https://creativecommons.org/licenses/by-nd/4.0/',
      'cc-by-nc-nd': 'https://creativecommons.org/licenses/by-nc-nd/4.0/',
      'cc0': 'https://creativecommons.org/publicdomain/zero/1.0/',
      'mit': 'https://opensource.org/licenses/MIT',
      'apache-2.0': 'https://opensource.org/licenses/Apache-2.0',
      'gpl-3.0': 'https://www.gnu.org/licenses/gpl-3.0.en.html'
    };
    
    return licenseUrls[type] || '';
  }
}

export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract; * Implementiert ERC-2981 für Royalties und erweiterte Lizenzierungsfunktionen
 */

class DoiRoyaltyContract {
  constructor(api, contractAddress, options = {}) {
    this.api = api;
    this.contractAddress = contractAddress;
    this.options = {
      defaultRoyaltyPercentage: 5, // 5% Standardroyalty
      maxRoyaltyPercentage: 15,    // Maximale Royalty von 15%
      ...options
    };
    
    // Initialisiere den Contract
    this.initialize();
  }
  
  /**
   * Initialisiert den Contract
   * @private
   */
  async initialize() {
    try {
      // In einer realen Implementierung würden wir hier den Contract ABI laden
      // und eine Verbindung zum Contract herstellen
      console.log('Initialisiere DOI Royalty Contract:', this.contractAddress);
      
      // Simuliere Contract-Initialisierung
      this.initialized = true;
    } catch (error) {
      console.error('Fehler bei der Initialisierung des Royalty Contracts:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Royalty-Informationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Array} recipients - Liste der Empfänger (Adressen)
   * @param {Array} shares - Liste der Anteile in Prozent
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setRoyaltyInfo(tokenId, recipients, shares) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Eingaben
    if (!Array.isArray(recipients) || !Array.isArray(shares)) {
      throw new Error('Empfänger und Anteile müssen Arrays sein');
    }
    
    if (recipients.length !== shares.length) {
      throw new Error('Anzahl der Empfänger und Anteile muss übereinstimmen');
    }
    
    // Prüfe, ob die Summe der Anteile 100% nicht überschreitet
    const totalShares = shares.reduce((sum, share) => sum + share, 0);
    if (totalShares > 100) {
      throw new Error('Summe der Anteile darf 100% nicht überschreiten');
    }
    
    // Prüfe, ob die Royalty-Rate das Maximum nicht überschreitet
    const royaltyRate = totalShares / 100 * this.options.defaultRoyaltyPercentage;
    if (royaltyRate > this.options.maxRoyaltyPercentage) {
      throw new Error(`Royalty-Rate darf ${this.options.maxRoyaltyPercentage}% nicht überschreiten`);
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Royalty-Informationen für Token ${tokenId}:`, {
        recipients,
        shares,
        royaltyRate: `${royaltyRate}%`
      });
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        recipients,
        shares,
        royaltyRate
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Royalty-Informationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Royalty-Informationen
   */
  async getRoyaltyInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Royalty-Informationen für Token ${tokenId} ab`);
      
      // Simulierte Royalty-Informationen
      return {
        tokenId,
        recipients: ['0x1234...', '0x5678...'],
        shares: [60, 40], // 60% und 40%
        royaltyRate: this.options.defaultRoyaltyPercentage,
        totalRoyalty: this.options.defaultRoyaltyPercentage
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Berechnet die Royalty-Zahlung für einen Verkauf
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Royalty-Zahlungsinformationen
   */
  async calculateRoyaltyPayment(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Royalty-Informationen ab
      const royaltyInfo = await this.getRoyaltyInfo(tokenId);
      
      // Berechne Royalty-Betrag
      const royaltyAmount = salePrice * (royaltyInfo.royaltyRate / 100);
      
      // Berechne Zahlungen pro Empfänger
      const payments = royaltyInfo.recipients.map((recipient, index) => ({
        recipient,
        share: royaltyInfo.shares[index],
        amount: royaltyAmount * (royaltyInfo.shares[index] / 100)
      }));
      
      return {
        tokenId,
        salePrice,
        royaltyRate: royaltyInfo.royaltyRate,
        totalRoyalty: royaltyAmount,
        payments
      };
    } catch (error) {
      console.error('Fehler bei der Berechnung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Führt eine Royalty-Zahlung für einen Verkauf durch
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async payRoyalties(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Berechne Royalty-Zahlungen
      const royaltyPayment = await this.calculateRoyaltyPayment(tokenId, salePrice);
      
      // In einer realen Implementierung würden wir hier die Zahlungen durchführen
      console.log(`Führe Royalty-Zahlungen für Token ${tokenId} durch:`, royaltyPayment);
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        salePrice,
        royaltyRate: royaltyPayment.royaltyRate,
        totalRoyalty: royaltyPayment.totalRoyalty,
        payments: royaltyPayment.payments
      };
    } catch (error) {
      console.error('Fehler bei der Durchführung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Lizenzinformationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} licenseInfo - Lizenzinformationen
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setLicenseInfo(tokenId, licenseInfo) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Lizenzinformationen
    if (!licenseInfo.type) {
      throw new Error('Lizenztyp muss angegeben werden');
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Lizenzinformationen für Token ${tokenId}:`, licenseInfo);
      
      // Standardisiere Lizenzinformationen
      const standardizedLicense = {
        type: licenseInfo.type,
        name: licenseInfo.name || this.getLicenseName(licenseInfo.type),
        url: licenseInfo.url || this.getLicenseUrl(licenseInfo.type),
        commercial: licenseInfo.commercial !== undefined ? licenseInfo.commercial : true,
        derivatives: licenseInfo.derivatives !== undefined ? licenseInfo.derivatives : true,
        attribution: licenseInfo.attribution !== undefined ? licenseInfo.attribution : true
      };
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        license: standardizedLicense
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Lizenzinformationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Lizenzinformationen
   */
  async getLicenseInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Lizenzinformationen für Token ${tokenId} ab`);
      
      // Simulierte Lizenzinformationen
      return {
        tokenId,
        license: {
          type: 'cc-by',
          name: 'Creative Commons Attribution 4.0',
          url: 'https://creativecommons.org/licenses/by/4.0/',
          commercial: true,
          derivatives: true,
          attribution: true
        }
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Nutzung mit der Lizenz kompatibel ist
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} usage - Nutzungsinformationen
   * @returns {Promise<Object>} - Kompatibilitätsinformationen
   */
  async checkLicenseCompatibility(tokenId, usage) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Lizenzinformationen ab
      const licenseInfo = await this.getLicenseInfo(tokenId);
      
      // Prüfe Kompatibilität
      const isCommercialCompatible = !usage.commercial || licenseInfo.license.commercial;
      const isDerivativesCompatible = !usage.derivatives || licenseInfo.license.derivatives;
      const isAttributionCompatible = !licenseInfo.license.attribution || usage.attribution;
      
      const isCompatible = isCommercialCompatible && isDerivativesCompatible && isAttributionCompatible;
      
      return {
        tokenId,
        license: licenseInfo.license,
        usage,
        isCompatible,
        incompatibilities: {
          commercial: !isCommercialCompatible,
          derivatives: !isDerivativesCompatible,
          attribution: !isAttributionCompatible
        }
      };
    } catch (error) {
      console.error('Fehler bei der Prüfung der Lizenzkompatibilität:', error);
      throw error;
    }
  }
  
  /**
   * Gibt den Namen einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseName(type) {
    const licenseNames = {
      'cc-by': 'Creative Commons Attribution 4.0',
      'cc-by-sa': 'Creative Commons Attribution-ShareAlike 4.0',
      'cc-by-nc': 'Creative Commons Attribution-NonCommercial 4.0',
      'cc-by-nc-sa': 'Creative Commons Attribution-NonCommercial-ShareAlike 4.0',
      'cc-by-nd': 'Creative Commons Attribution-NoDerivatives 4.0',
      'cc-by-nc-nd': 'Creative Commons Attribution-NonCommercial-NoDerivatives 4.0',
      'cc0': 'Creative Commons Zero v1.0 Universal',
      'mit': 'MIT License',
      'apache-2.0': 'Apache License 2.0',
      'gpl-3.0': 'GNU General Public License v3.0'
    };
    
    return licenseNames[type] || 'Custom License';
  }
  
  /**
   * Gibt die URL einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseUrl(type) {
    const licenseUrls = {
      'cc-by': 'https://creativecommons.org/licenses/by/4.0/',
      'cc-by-sa': 'https://creativecommons.org/licenses/by-sa/4.0/',
      'cc-by-nc': 'https://creativecommons.org/licenses/by-nc/4.0/',
      'cc-by-nc-sa': 'https://creativecommons.org/licenses/by-nc-sa/4.0/',
      'cc-by-nd': 'https://creativecommons.org/licenses/by-nd/4.0/',
      'cc-by-nc-nd': 'https://creativecommons.org/licenses/by-nc-nd/4.0/',
      'cc0': 'https://creativecommons.org/publicdomain/zero/1.0/',
      'mit': 'https://opensource.org/licenses/MIT',
      'apache-2.0': 'https://opensource.org/licenses/Apache-2.0',
      'gpl-3.0': 'https://www.gnu.org/licenses/gpl-3.0.en.html'
    };
    
    return licenseUrls[type] || '';
  }
}

export default DoiRoyaltyContract;
export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract; * Implementiert ERC-2981 für Royalties und erweiterte Lizenzierungsfunktionen
 */

class DoiRoyaltyContract {
  constructor(api, contractAddress, options = {}) {
    this.api = api;
    this.contractAddress = contractAddress;
    this.options = {
      defaultRoyaltyPercentage: 5, // 5% Standardroyalty
      maxRoyaltyPercentage: 15,    // Maximale Royalty von 15%
      ...options
    };
    
    // Initialisiere den Contract
    this.initialize();
  }
  
  /**
   * Initialisiert den Contract
   * @private
   */
  async initialize() {
    try {
      // In einer realen Implementierung würden wir hier den Contract ABI laden
      // und eine Verbindung zum Contract herstellen
      console.log('Initialisiere DOI Royalty Contract:', this.contractAddress);
      
      // Simuliere Contract-Initialisierung
      this.initialized = true;
    } catch (error) {
      console.error('Fehler bei der Initialisierung des Royalty Contracts:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Royalty-Informationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Array} recipients - Liste der Empfänger (Adressen)
   * @param {Array} shares - Liste der Anteile in Prozent
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setRoyaltyInfo(tokenId, recipients, shares) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Eingaben
    if (!Array.isArray(recipients) || !Array.isArray(shares)) {
      throw new Error('Empfänger und Anteile müssen Arrays sein');
    }
    
    if (recipients.length !== shares.length) {
      throw new Error('Anzahl der Empfänger und Anteile muss übereinstimmen');
    }
    
    // Prüfe, ob die Summe der Anteile 100% nicht überschreitet
    const totalShares = shares.reduce((sum, share) => sum + share, 0);
    if (totalShares > 100) {
      throw new Error('Summe der Anteile darf 100% nicht überschreiten');
    }
    
    // Prüfe, ob die Royalty-Rate das Maximum nicht überschreitet
    const royaltyRate = totalShares / 100 * this.options.defaultRoyaltyPercentage;
    if (royaltyRate > this.options.maxRoyaltyPercentage) {
      throw new Error(`Royalty-Rate darf ${this.options.maxRoyaltyPercentage}% nicht überschreiten`);
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Royalty-Informationen für Token ${tokenId}:`, {
        recipients,
        shares,
        royaltyRate: `${royaltyRate}%`
      });
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        recipients,
        shares,
        royaltyRate
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Royalty-Informationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Royalty-Informationen
   */
  async getRoyaltyInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Royalty-Informationen für Token ${tokenId} ab`);
      
      // Simulierte Royalty-Informationen
      return {
        tokenId,
        recipients: ['0x1234...', '0x5678...'],
        shares: [60, 40], // 60% und 40%
        royaltyRate: this.options.defaultRoyaltyPercentage,
        totalRoyalty: this.options.defaultRoyaltyPercentage
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Berechnet die Royalty-Zahlung für einen Verkauf
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Royalty-Zahlungsinformationen
   */
  async calculateRoyaltyPayment(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Royalty-Informationen ab
      const royaltyInfo = await this.getRoyaltyInfo(tokenId);
      
      // Berechne Royalty-Betrag
      const royaltyAmount = salePrice * (royaltyInfo.royaltyRate / 100);
      
      // Berechne Zahlungen pro Empfänger
      const payments = royaltyInfo.recipients.map((recipient, index) => ({
        recipient,
        share: royaltyInfo.shares[index],
        amount: royaltyAmount * (royaltyInfo.shares[index] / 100)
      }));
      
      return {
        tokenId,
        salePrice,
        royaltyRate: royaltyInfo.royaltyRate,
        totalRoyalty: royaltyAmount,
        payments
      };
    } catch (error) {
      console.error('Fehler bei der Berechnung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Führt eine Royalty-Zahlung für einen Verkauf durch
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async payRoyalties(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Berechne Royalty-Zahlungen
      const royaltyPayment = await this.calculateRoyaltyPayment(tokenId, salePrice);
      
      // In einer realen Implementierung würden wir hier die Zahlungen durchführen
      console.log(`Führe Royalty-Zahlungen für Token ${tokenId} durch:`, royaltyPayment);
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        salePrice,
        royaltyRate: royaltyPayment.royaltyRate,
        totalRoyalty: royaltyPayment.totalRoyalty,
        payments: royaltyPayment.payments
      };
    } catch (error) {
      console.error('Fehler bei der Durchführung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Lizenzinformationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} licenseInfo - Lizenzinformationen
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setLicenseInfo(tokenId, licenseInfo) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Lizenzinformationen
    if (!licenseInfo.type) {
      throw new Error('Lizenztyp muss angegeben werden');
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Lizenzinformationen für Token ${tokenId}:`, licenseInfo);
      
      // Standardisiere Lizenzinformationen
      const standardizedLicense = {
        type: licenseInfo.type,
        name: licenseInfo.name || this.getLicenseName(licenseInfo.type),
        url: licenseInfo.url || this.getLicenseUrl(licenseInfo.type),
        commercial: licenseInfo.commercial !== undefined ? licenseInfo.commercial : true,
        derivatives: licenseInfo.derivatives !== undefined ? licenseInfo.derivatives : true,
        attribution: licenseInfo.attribution !== undefined ? licenseInfo.attribution : true
      };
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        license: standardizedLicense
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Lizenzinformationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Lizenzinformationen
   */
  async getLicenseInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Lizenzinformationen für Token ${tokenId} ab`);
      
      // Simulierte Lizenzinformationen
      return {
        tokenId,
        license: {
          type: 'cc-by',
          name: 'Creative Commons Attribution 4.0',
          url: 'https://creativecommons.org/licenses/by/4.0/',
          commercial: true,
          derivatives: true,
          attribution: true
        }
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Nutzung mit der Lizenz kompatibel ist
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} usage - Nutzungsinformationen
   * @returns {Promise<Object>} - Kompatibilitätsinformationen
   */
  async checkLicenseCompatibility(tokenId, usage) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Lizenzinformationen ab
      const licenseInfo = await this.getLicenseInfo(tokenId);
      
      // Prüfe Kompatibilität
      const isCommercialCompatible = !usage.commercial || licenseInfo.license.commercial;
      const isDerivativesCompatible = !usage.derivatives || licenseInfo.license.derivatives;
      const isAttributionCompatible = !licenseInfo.license.attribution || usage.attribution;
      
      const isCompatible = isCommercialCompatible && isDerivativesCompatible && isAttributionCompatible;
      
      return {
        tokenId,
        license: licenseInfo.license,
        usage,
        isCompatible,
        incompatibilities: {
          commercial: !isCommercialCompatible,
          derivatives: !isDerivativesCompatible,
          attribution: !isAttributionCompatible
        }
      };
    } catch (error) {
      console.error('Fehler bei der Prüfung der Lizenzkompatibilität:', error);
      throw error;
    }
  }
  
  /**
   * Gibt den Namen einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseName(type) {
    const licenseNames = {
      'cc-by': 'Creative Commons Attribution 4.0',
      'cc-by-sa': 'Creative Commons Attribution-ShareAlike 4.0',
      'cc-by-nc': 'Creative Commons Attribution-NonCommercial 4.0',
      'cc-by-nc-sa': 'Creative Commons Attribution-NonCommercial-ShareAlike 4.0',
      'cc-by-nd': 'Creative Commons Attribution-NoDerivatives 4.0',
      'cc-by-nc-nd': 'Creative Commons Attribution-NonCommercial-NoDerivatives 4.0',
      'cc0': 'Creative Commons Zero v1.0 Universal',
      'mit': 'MIT License',
      'apache-2.0': 'Apache License 2.0',
      'gpl-3.0': 'GNU General Public License v3.0'
    };
    
    return licenseNames[type] || 'Custom License';
  }
  
  /**
   * Gibt die URL einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseUrl(type) {
    const licenseUrls = {
      'cc-by': 'https://creativecommons.org/licenses/by/4.0/',
      'cc-by-sa': 'https://creativecommons.org/licenses/by-sa/4.0/',
      'cc-by-nc': 'https://creativecommons.org/licenses/by-nc/4.0/',
      'cc-by-nc-sa': 'https://creativecommons.org/licenses/by-nc-sa/4.0/',
      'cc-by-nd': 'https://creativecommons.org/licenses/by-nd/4.0/',
      'cc-by-nc-nd': 'https://creativecommons.org/licenses/by-nc-nd/4.0/',
      'cc0': 'https://creativecommons.org/publicdomain/zero/1.0/',
      'mit': 'https://opensource.org/licenses/MIT',
      'apache-2.0': 'https://opensource.org/licenses/Apache-2.0',
      'gpl-3.0': 'https://www.gnu.org/licenses/gpl-3.0.en.html'
    };
    
    return licenseUrls[type] || '';
  }
}

export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;
export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract; * Implementiert ERC-2981 für Royalties und erweiterte Lizenzierungsfunktionen
 */

class DoiRoyaltyContract {
  constructor(api, contractAddress, options = {}) {
    this.api = api;
    this.contractAddress = contractAddress;
    this.options = {
      defaultRoyaltyPercentage: 5, // 5% Standardroyalty
      maxRoyaltyPercentage: 15,    // Maximale Royalty von 15%
      ...options
    };
    
    // Initialisiere den Contract
    this.initialize();
  }
  
  /**
   * Initialisiert den Contract
   * @private
   */
  async initialize() {
    try {
      // In einer realen Implementierung würden wir hier den Contract ABI laden
      // und eine Verbindung zum Contract herstellen
      console.log('Initialisiere DOI Royalty Contract:', this.contractAddress);
      
      // Simuliere Contract-Initialisierung
      this.initialized = true;
    } catch (error) {
      console.error('Fehler bei der Initialisierung des Royalty Contracts:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Royalty-Informationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Array} recipients - Liste der Empfänger (Adressen)
   * @param {Array} shares - Liste der Anteile in Prozent
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setRoyaltyInfo(tokenId, recipients, shares) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Eingaben
    if (!Array.isArray(recipients) || !Array.isArray(shares)) {
      throw new Error('Empfänger und Anteile müssen Arrays sein');
    }
    
    if (recipients.length !== shares.length) {
      throw new Error('Anzahl der Empfänger und Anteile muss übereinstimmen');
    }
    
    // Prüfe, ob die Summe der Anteile 100% nicht überschreitet
    const totalShares = shares.reduce((sum, share) => sum + share, 0);
    if (totalShares > 100) {
      throw new Error('Summe der Anteile darf 100% nicht überschreiten');
    }
    
    // Prüfe, ob die Royalty-Rate das Maximum nicht überschreitet
    const royaltyRate = totalShares / 100 * this.options.defaultRoyaltyPercentage;
    if (royaltyRate > this.options.maxRoyaltyPercentage) {
      throw new Error(`Royalty-Rate darf ${this.options.maxRoyaltyPercentage}% nicht überschreiten`);
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Royalty-Informationen für Token ${tokenId}:`, {
        recipients,
        shares,
        royaltyRate: `${royaltyRate}%`
      });
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        recipients,
        shares,
        royaltyRate
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Royalty-Informationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Royalty-Informationen
   */
  async getRoyaltyInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Royalty-Informationen für Token ${tokenId} ab`);
      
      // Simulierte Royalty-Informationen
      return {
        tokenId,
        recipients: ['0x1234...', '0x5678...'],
        shares: [60, 40], // 60% und 40%
        royaltyRate: this.options.defaultRoyaltyPercentage,
        totalRoyalty: this.options.defaultRoyaltyPercentage
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Berechnet die Royalty-Zahlung für einen Verkauf
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Royalty-Zahlungsinformationen
   */
  async calculateRoyaltyPayment(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Royalty-Informationen ab
      const royaltyInfo = await this.getRoyaltyInfo(tokenId);
      
      // Berechne Royalty-Betrag
      const royaltyAmount = salePrice * (royaltyInfo.royaltyRate / 100);
      
      // Berechne Zahlungen pro Empfänger
      const payments = royaltyInfo.recipients.map((recipient, index) => ({
        recipient,
        share: royaltyInfo.shares[index],
        amount: royaltyAmount * (royaltyInfo.shares[index] / 100)
      }));
      
      return {
        tokenId,
        salePrice,
        royaltyRate: royaltyInfo.royaltyRate,
        totalRoyalty: royaltyAmount,
        payments
      };
    } catch (error) {
      console.error('Fehler bei der Berechnung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Führt eine Royalty-Zahlung für einen Verkauf durch
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async payRoyalties(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Berechne Royalty-Zahlungen
      const royaltyPayment = await this.calculateRoyaltyPayment(tokenId, salePrice);
      
      // In einer realen Implementierung würden wir hier die Zahlungen durchführen
      console.log(`Führe Royalty-Zahlungen für Token ${tokenId} durch:`, royaltyPayment);
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        salePrice,
        royaltyRate: royaltyPayment.royaltyRate,
        totalRoyalty: royaltyPayment.totalRoyalty,
        payments: royaltyPayment.payments
      };
    } catch (error) {
      console.error('Fehler bei der Durchführung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Lizenzinformationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} licenseInfo - Lizenzinformationen
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setLicenseInfo(tokenId, licenseInfo) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Lizenzinformationen
    if (!licenseInfo.type) {
      throw new Error('Lizenztyp muss angegeben werden');
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Lizenzinformationen für Token ${tokenId}:`, licenseInfo);
      
      // Standardisiere Lizenzinformationen
      const standardizedLicense = {
        type: licenseInfo.type,
        name: licenseInfo.name || this.getLicenseName(licenseInfo.type),
        url: licenseInfo.url || this.getLicenseUrl(licenseInfo.type),
        commercial: licenseInfo.commercial !== undefined ? licenseInfo.commercial : true,
        derivatives: licenseInfo.derivatives !== undefined ? licenseInfo.derivatives : true,
        attribution: licenseInfo.attribution !== undefined ? licenseInfo.attribution : true
      };
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        license: standardizedLicense
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Lizenzinformationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Lizenzinformationen
   */
  async getLicenseInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Lizenzinformationen für Token ${tokenId} ab`);
      
      // Simulierte Lizenzinformationen
      return {
        tokenId,
        license: {
          type: 'cc-by',
          name: 'Creative Commons Attribution 4.0',
          url: 'https://creativecommons.org/licenses/by/4.0/',
          commercial: true,
          derivatives: true,
          attribution: true
        }
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Nutzung mit der Lizenz kompatibel ist
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} usage - Nutzungsinformationen
   * @returns {Promise<Object>} - Kompatibilitätsinformationen
   */
  async checkLicenseCompatibility(tokenId, usage) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Lizenzinformationen ab
      const licenseInfo = await this.getLicenseInfo(tokenId);
      
      // Prüfe Kompatibilität
      const isCommercialCompatible = !usage.commercial || licenseInfo.license.commercial;
      const isDerivativesCompatible = !usage.derivatives || licenseInfo.license.derivatives;
      const isAttributionCompatible = !licenseInfo.license.attribution || usage.attribution;
      
      const isCompatible = isCommercialCompatible && isDerivativesCompatible && isAttributionCompatible;
      
      return {
        tokenId,
        license: licenseInfo.license,
        usage,
        isCompatible,
        incompatibilities: {
          commercial: !isCommercialCompatible,
          derivatives: !isDerivativesCompatible,
          attribution: !isAttributionCompatible
        }
      };
    } catch (error) {
      console.error('Fehler bei der Prüfung der Lizenzkompatibilität:', error);
      throw error;
    }
  }
  
  /**
   * Gibt den Namen einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseName(type) {
    const licenseNames = {
      'cc-by': 'Creative Commons Attribution 4.0',
      'cc-by-sa': 'Creative Commons Attribution-ShareAlike 4.0',
      'cc-by-nc': 'Creative Commons Attribution-NonCommercial 4.0',
      'cc-by-nc-sa': 'Creative Commons Attribution-NonCommercial-ShareAlike 4.0',
      'cc-by-nd': 'Creative Commons Attribution-NoDerivatives 4.0',
      'cc-by-nc-nd': 'Creative Commons Attribution-NonCommercial-NoDerivatives 4.0',
      'cc0': 'Creative Commons Zero v1.0 Universal',
      'mit': 'MIT License',
      'apache-2.0': 'Apache License 2.0',
      'gpl-3.0': 'GNU General Public License v3.0'
    };
    
    return licenseNames[type] || 'Custom License';
  }
  
  /**
   * Gibt die URL einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseUrl(type) {
    const licenseUrls = {
      'cc-by': 'https://creativecommons.org/licenses/by/4.0/',
      'cc-by-sa': 'https://creativecommons.org/licenses/by-sa/4.0/',
      'cc-by-nc': 'https://creativecommons.org/licenses/by-nc/4.0/',
      'cc-by-nc-sa': 'https://creativecommons.org/licenses/by-nc-sa/4.0/',
      'cc-by-nd': 'https://creativecommons.org/licenses/by-nd/4.0/',
      'cc-by-nc-nd': 'https://creativecommons.org/licenses/by-nc-nd/4.0/',
      'cc0': 'https://creativecommons.org/publicdomain/zero/1.0/',
      'mit': 'https://opensource.org/licenses/MIT',
      'apache-2.0': 'https://opensource.org/licenses/Apache-2.0',
      'gpl-3.0': 'https://www.gnu.org/licenses/gpl-3.0.en.html'
    };
    
    return licenseUrls[type] || '';
  }
}

export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract; */

class DoiRoyaltyContract {
  constructor(api, contractAddress, options = {}) {
    this.api = api;
    this.contractAddress = contractAddress;
    this.options = {
      defaultRoyaltyPercentage: 5, // 5% Standardroyalty
      maxRoyaltyPercentage: 15,    // Maximale Royalty von 15%
      ...options
    };
    
    // Initialisiere den Contract
    this.initialize();
  }
  
  /**
   * Initialisiert den Contract
   * @private
   */
  async initialize() {
    try {
      // In einer realen Implementierung würden wir hier den Contract ABI laden
      // und eine Verbindung zum Contract herstellen
      console.log('Initialisiere DOI Royalty Contract:', this.contractAddress);
      
      // Simuliere Contract-Initialisierung
      this.initialized = true;
    } catch (error) {
      console.error('Fehler bei der Initialisierung des Royalty Contracts:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Royalty-Informationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Array} recipients - Liste der Empfänger (Adressen)
   * @param {Array} shares - Liste der Anteile in Prozent
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setRoyaltyInfo(tokenId, recipients, shares) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Eingaben
    if (!Array.isArray(recipients) || !Array.isArray(shares)) {
      throw new Error('Empfänger und Anteile müssen Arrays sein');
    }
    
    if (recipients.length !== shares.length) {
      throw new Error('Anzahl der Empfänger und Anteile muss übereinstimmen');
    }
    
    // Prüfe, ob die Summe der Anteile 100% nicht überschreitet
    const totalShares = shares.reduce((sum, share) => sum + share, 0);
    if (totalShares > 100) {
      throw new Error('Summe der Anteile darf 100% nicht überschreiten');
    }
    
    // Prüfe, ob die Royalty-Rate das Maximum nicht überschreitet
    const royaltyRate = totalShares / 100 * this.options.defaultRoyaltyPercentage;
    if (royaltyRate > this.options.maxRoyaltyPercentage) {
      throw new Error(`Royalty-Rate darf ${this.options.maxRoyaltyPercentage}% nicht überschreiten`);
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Royalty-Informationen für Token ${tokenId}:`, {
        recipients,
        shares,
        royaltyRate: `${royaltyRate}%`
      });
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        recipients,
        shares,
        royaltyRate
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Royalty-Informationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Royalty-Informationen
   */
  async getRoyaltyInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Royalty-Informationen für Token ${tokenId} ab`);
      
      // Simulierte Royalty-Informationen
      return {
        tokenId,
        recipients: ['0x1234...', '0x5678...'],
        shares: [60, 40], // 60% und 40%
        royaltyRate: this.options.defaultRoyaltyPercentage,
        totalRoyalty: this.options.defaultRoyaltyPercentage
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Berechnet die Royalty-Zahlung für einen Verkauf
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Royalty-Zahlungsinformationen
   */
  async calculateRoyaltyPayment(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Royalty-Informationen ab
      const royaltyInfo = await this.getRoyaltyInfo(tokenId);
      
      // Berechne Royalty-Betrag
      const royaltyAmount = salePrice * (royaltyInfo.royaltyRate / 100);
      
      // Berechne Zahlungen pro Empfänger
      const payments = royaltyInfo.recipients.map((recipient, index) => ({
        recipient,
        share: royaltyInfo.shares[index],
        amount: royaltyAmount * (royaltyInfo.shares[index] / 100)
      }));
      
      return {
        tokenId,
        salePrice,
        royaltyRate: royaltyInfo.royaltyRate,
        totalRoyalty: royaltyAmount,
        payments
      };
    } catch (error) {
      console.error('Fehler bei der Berechnung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Führt eine Royalty-Zahlung für einen Verkauf durch
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async payRoyalties(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Berechne Royalty-Zahlungen
      const royaltyPayment = await this.calculateRoyaltyPayment(tokenId, salePrice);
      
      // In einer realen Implementierung würden wir hier die Zahlungen durchführen
      console.log(`Führe Royalty-Zahlungen für Token ${tokenId} durch:`, royaltyPayment);
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        salePrice,
        royaltyRate: royaltyPayment.royaltyRate,
        totalRoyalty: royaltyPayment.totalRoyalty,
        payments: royaltyPayment.payments
      };
    } catch (error) {
      console.error('Fehler bei der Durchführung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Lizenzinformationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} licenseInfo - Lizenzinformationen
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setLicenseInfo(tokenId, licenseInfo) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Lizenzinformationen
    if (!licenseInfo.type) {
      throw new Error('Lizenztyp muss angegeben werden');
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Lizenzinformationen für Token ${tokenId}:`, licenseInfo);
      
      // Standardisiere Lizenzinformationen
      const standardizedLicense = {
        type: licenseInfo.type,
        name: licenseInfo.name || this.getLicenseName(licenseInfo.type),
        url: licenseInfo.url || this.getLicenseUrl(licenseInfo.type),
        commercial: licenseInfo.commercial !== undefined ? licenseInfo.commercial : true,
        derivatives: licenseInfo.derivatives !== undefined ? licenseInfo.derivatives : true,
        attribution: licenseInfo.attribution !== undefined ? licenseInfo.attribution : true
      };
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        license: standardizedLicense
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Lizenzinformationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Lizenzinformationen
   */
  async getLicenseInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Lizenzinformationen für Token ${tokenId} ab`);
      
      // Simulierte Lizenzinformationen
      return {
        tokenId,
        license: {
          type: 'cc-by',
          name: 'Creative Commons Attribution 4.0',
          url: 'https://creativecommons.org/licenses/by/4.0/',
          commercial: true,
          derivatives: true,
          attribution: true
        }
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Nutzung mit der Lizenz kompatibel ist
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} usage - Nutzungsinformationen
   * @returns {Promise<Object>} - Kompatibilitätsinformationen
   */
  async checkLicenseCompatibility(tokenId, usage) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Lizenzinformationen ab
      const licenseInfo = await this.getLicenseInfo(tokenId);
      
      // Prüfe Kompatibilität
      const isCommercialCompatible = !usage.commercial || licenseInfo.license.commercial;
      const isDerivativesCompatible = !usage.derivatives || licenseInfo.license.derivatives;
      const isAttributionCompatible = !licenseInfo.license.attribution || usage.attribution;
      
      const isCompatible = isCommercialCompatible && isDerivativesCompatible && isAttributionCompatible;
      
      return {
        tokenId,
        license: licenseInfo.license,
        usage,
        isCompatible,
        incompatibilities: {
          commercial: !isCommercialCompatible,
          derivatives: !isDerivativesCompatible,
          attribution: !isAttributionCompatible
        }
      };
    } catch (error) {
      console.error('Fehler bei der Prüfung der Lizenzkompatibilität:', error);
      throw error;
    }
  }
  
  /**
   * Gibt den Namen einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseName(type) {
    const licenseNames = {
      'cc-by': 'Creative Commons Attribution 4.0',
      'cc-by-sa': 'Creative Commons Attribution-ShareAlike 4.0',
      'cc-by-nc': 'Creative Commons Attribution-NonCommercial 4.0',
      'cc-by-nc-sa': 'Creative Commons Attribution-NonCommercial-ShareAlike 4.0',
      'cc-by-nd': 'Creative Commons Attribution-NoDerivatives 4.0',
      'cc-by-nc-nd': 'Creative Commons Attribution-NonCommercial-NoDerivatives 4.0',
      'cc0': 'Creative Commons Zero v1.0 Universal',
      'mit': 'MIT License',
      'apache-2.0': 'Apache License 2.0',
      'gpl-3.0': 'GNU General Public License v3.0'
    };
    
    return licenseNames[type] || 'Custom License';
  }
  
  /**
   * Gibt die URL einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseUrl(type) {
    const licenseUrls = {
      'cc-by': 'https://creativecommons.org/licenses/by/4.0/',
      'cc-by-sa': 'https://creativecommons.org/licenses/by-sa/4.0/',
      'cc-by-nc': 'https://creativecommons.org/licenses/by-nc/4.0/',
      'cc-by-nc-sa': 'https://creativecommons.org/licenses/by-nc-sa/4.0/',
      'cc-by-nd': 'https://creativecommons.org/licenses/by-nd/4.0/',
      'cc-by-nc-nd': 'https://creativecommons.org/licenses/by-nc-nd/4.0/',
      'cc0': 'https://creativecommons.org/publicdomain/zero/1.0/',
      'mit': 'https://opensource.org/licenses/MIT',
      'apache-2.0': 'https://opensource.org/licenses/Apache-2.0',
      'gpl-3.0': 'https://www.gnu.org/licenses/gpl-3.0.en.html'
    };
    
    return licenseUrls[type] || '';
  }
}

export default DoiRoyaltyContract;export default DoiRoyaltyContract;}

export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract; */

class DoiRoyaltyContract {
  constructor(api, contractAddress, options = {}) {
    this.api = api;
    this.contractAddress = contractAddress;
    this.options = {
      defaultRoyaltyPercentage: 5, // 5% Standardroyalty
      maxRoyaltyPercentage: 15,    // Maximale Royalty von 15%
      ...options
    };
    
    // Initialisiere den Contract
    this.initialize();
  }
  
  /**
   * Initialisiert den Contract
   * @private
   */
  async initialize() {
    try {
      // In einer realen Implementierung würden wir hier den Contract ABI laden
      // und eine Verbindung zum Contract herstellen
      console.log('Initialisiere DOI Royalty Contract:', this.contractAddress);
      
      // Simuliere Contract-Initialisierung
      this.initialized = true;
    } catch (error) {
      console.error('Fehler bei der Initialisierung des Royalty Contracts:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Royalty-Informationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Array} recipients - Liste der Empfänger (Adressen)
   * @param {Array} shares - Liste der Anteile in Prozent
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setRoyaltyInfo(tokenId, recipients, shares) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Eingaben
    if (!Array.isArray(recipients) || !Array.isArray(shares)) {
      throw new Error('Empfänger und Anteile müssen Arrays sein');
    }
    
    if (recipients.length !== shares.length) {
      throw new Error('Anzahl der Empfänger und Anteile muss übereinstimmen');
    }
    
    // Prüfe, ob die Summe der Anteile 100% nicht überschreitet
    const totalShares = shares.reduce((sum, share) => sum + share, 0);
    if (totalShares > 100) {
      throw new Error('Summe der Anteile darf 100% nicht überschreiten');
    }
    
    // Prüfe, ob die Royalty-Rate das Maximum nicht überschreitet
    const royaltyRate = totalShares / 100 * this.options.defaultRoyaltyPercentage;
    if (royaltyRate > this.options.maxRoyaltyPercentage) {
      throw new Error(`Royalty-Rate darf ${this.options.maxRoyaltyPercentage}% nicht überschreiten`);
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Royalty-Informationen für Token ${tokenId}:`, {
        recipients,
        shares,
        royaltyRate: `${royaltyRate}%`
      });
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        recipients,
        shares,
        royaltyRate
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Royalty-Informationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Royalty-Informationen
   */
  async getRoyaltyInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Royalty-Informationen für Token ${tokenId} ab`);
      
      // Simulierte Royalty-Informationen
      return {
        tokenId,
        recipients: ['0x1234...', '0x5678...'],
        shares: [60, 40], // 60% und 40%
        royaltyRate: this.options.defaultRoyaltyPercentage,
        totalRoyalty: this.options.defaultRoyaltyPercentage
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Berechnet die Royalty-Zahlung für einen Verkauf
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Royalty-Zahlungsinformationen
   */
  async calculateRoyaltyPayment(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Royalty-Informationen ab
      const royaltyInfo = await this.getRoyaltyInfo(tokenId);
      
      // Berechne Royalty-Betrag
      const royaltyAmount = salePrice * (royaltyInfo.royaltyRate / 100);
      
      // Berechne Zahlungen pro Empfänger
      const payments = royaltyInfo.recipients.map((recipient, index) => ({
        recipient,
        share: royaltyInfo.shares[index],
        amount: royaltyAmount * (royaltyInfo.shares[index] / 100)
      }));
      
      return {
        tokenId,
        salePrice,
        royaltyRate: royaltyInfo.royaltyRate,
        totalRoyalty: royaltyAmount,
        payments
      };
    } catch (error) {
      console.error('Fehler bei der Berechnung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Führt eine Royalty-Zahlung für einen Verkauf durch
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async payRoyalties(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Berechne Royalty-Zahlungen
      const royaltyPayment = await this.calculateRoyaltyPayment(tokenId, salePrice);
      
      // In einer realen Implementierung würden wir hier die Zahlungen durchführen
      console.log(`Führe Royalty-Zahlungen für Token ${tokenId} durch:`, royaltyPayment);
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        salePrice,
        royaltyRate: royaltyPayment.royaltyRate,
        totalRoyalty: royaltyPayment.totalRoyalty,
        payments: royaltyPayment.payments
      };
    } catch (error) {
      console.error('Fehler bei der Durchführung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Lizenzinformationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} licenseInfo - Lizenzinformationen
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setLicenseInfo(tokenId, licenseInfo) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Lizenzinformationen
    if (!licenseInfo.type) {
      throw new Error('Lizenztyp muss angegeben werden');
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Lizenzinformationen für Token ${tokenId}:`, licenseInfo);
      
      // Standardisiere Lizenzinformationen
      const standardizedLicense = {
        type: licenseInfo.type,
        name: licenseInfo.name || this.getLicenseName(licenseInfo.type),
        url: licenseInfo.url || this.getLicenseUrl(licenseInfo.type),
        commercial: licenseInfo.commercial !== undefined ? licenseInfo.commercial : true,
        derivatives: licenseInfo.derivatives !== undefined ? licenseInfo.derivatives : true,
        attribution: licenseInfo.attribution !== undefined ? licenseInfo.attribution : true
      };
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        license: standardizedLicense
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Lizenzinformationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Lizenzinformationen
   */
  async getLicenseInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Lizenzinformationen für Token ${tokenId} ab`);
      
      // Simulierte Lizenzinformationen
      return {
        tokenId,
        license: {
          type: 'cc-by',
          name: 'Creative Commons Attribution 4.0',
          url: 'https://creativecommons.org/licenses/by/4.0/',
          commercial: true,
          derivatives: true,
          attribution: true
        }
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Nutzung mit der Lizenz kompatibel ist
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} usage - Nutzungsinformationen
   * @returns {Promise<Object>} - Kompatibilitätsinformationen
   */
  async checkLicenseCompatibility(tokenId, usage) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Lizenzinformationen ab
      const licenseInfo = await this.getLicenseInfo(tokenId);
      
      // Prüfe Kompatibilität
      const isCommercialCompatible = !usage.commercial || licenseInfo.license.commercial;
      const isDerivativesCompatible = !usage.derivatives || licenseInfo.license.derivatives;
      const isAttributionCompatible = !licenseInfo.license.attribution || usage.attribution;
      
      const isCompatible = isCommercialCompatible && isDerivativesCompatible && isAttributionCompatible;
      
      return {
        tokenId,
        license: licenseInfo.license,
        usage,
        isCompatible,
        incompatibilities: {
          commercial: !isCommercialCompatible,
          derivatives: !isDerivativesCompatible,
          attribution: !isAttributionCompatible
        }
      };
    } catch (error) {
      console.error('Fehler bei der Prüfung der Lizenzkompatibilität:', error);
      throw error;
    }
  }
  
  /**
   * Gibt den Namen einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseName(type) {
    const licenseNames = {
      'cc-by': 'Creative Commons Attribution 4.0',
      'cc-by-sa': 'Creative Commons Attribution-ShareAlike 4.0',
      'cc-by-nc': 'Creative Commons Attribution-NonCommercial 4.0',
      'cc-by-nc-sa': 'Creative Commons Attribution-NonCommercial-ShareAlike 4.0',
      'cc-by-nd': 'Creative Commons Attribution-NoDerivatives 4.0',
      'cc-by-nc-nd': 'Creative Commons Attribution-NonCommercial-NoDerivatives 4.0',
      'cc0': 'Creative Commons Zero v1.0 Universal',
      'mit': 'MIT License',
      'apache-2.0': 'Apache License 2.0',
      'gpl-3.0': 'GNU General Public License v3.0'
    };
    
    return licenseNames[type] || 'Custom License';
  }
  
  /**
   * Gibt die URL einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseUrl(type) {
    const licenseUrls = {
      'cc-by': 'https://creativecommons.org/licenses/by/4.0/',
      'cc-by-sa': 'https://creativecommons.org/licenses/by-sa/4.0/',
      'cc-by-nc': 'https://creativecommons.org/licenses/by-nc/4.0/',
      'cc-by-nc-sa': 'https://creativecommons.org/licenses/by-nc-sa/4.0/',
      'cc-by-nd': 'https://creativecommons.org/licenses/by-nd/4.0/',
      'cc-by-nc-nd': 'https://creativecommons.org/licenses/by-nc-nd/4.0/',
      'cc0': 'https://creativecommons.org/publicdomain/zero/1.0/',
      'mit': 'https://opensource.org/licenses/MIT',
      'apache-2.0': 'https://opensource.org/licenses/Apache-2.0',
      'gpl-3.0': 'https://www.gnu.org/licenses/gpl-3.0.en.html'
    };
    
    return licenseUrls[type] || '';
  }
}

export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;}

export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract; */

class DoiRoyaltyContract {
  constructor(api, contractAddress, options = {}) {
    this.api = api;
    this.contractAddress = contractAddress;
    this.options = {
      defaultRoyaltyPercentage: 5, // 5% Standardroyalty
      maxRoyaltyPercentage: 15,    // Maximale Royalty von 15%
      ...options
    };
    
    // Initialisiere den Contract
    this.initialize();
  }
  
  /**
   * Initialisiert den Contract
   * @private
   */
  async initialize() {
    try {
      // In einer realen Implementierung würden wir hier den Contract ABI laden
      // und eine Verbindung zum Contract herstellen
      console.log('Initialisiere DOI Royalty Contract:', this.contractAddress);
      
      // Simuliere Contract-Initialisierung
      this.initialized = true;
    } catch (error) {
      console.error('Fehler bei der Initialisierung des Royalty Contracts:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Royalty-Informationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Array} recipients - Liste der Empfänger (Adressen)
   * @param {Array} shares - Liste der Anteile in Prozent
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setRoyaltyInfo(tokenId, recipients, shares) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Eingaben
    if (!Array.isArray(recipients) || !Array.isArray(shares)) {
      throw new Error('Empfänger und Anteile müssen Arrays sein');
    }
    
    if (recipients.length !== shares.length) {
      throw new Error('Anzahl der Empfänger und Anteile muss übereinstimmen');
    }
    
    // Prüfe, ob die Summe der Anteile 100% nicht überschreitet
    const totalShares = shares.reduce((sum, share) => sum + share, 0);
    if (totalShares > 100) {
      throw new Error('Summe der Anteile darf 100% nicht überschreiten');
    }
    
    // Prüfe, ob die Royalty-Rate das Maximum nicht überschreitet
    const royaltyRate = totalShares / 100 * this.options.defaultRoyaltyPercentage;
    if (royaltyRate > this.options.maxRoyaltyPercentage) {
      throw new Error(`Royalty-Rate darf ${this.options.maxRoyaltyPercentage}% nicht überschreiten`);
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Royalty-Informationen für Token ${tokenId}:`, {
        recipients,
        shares,
        royaltyRate: `${royaltyRate}%`
      });
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        recipients,
        shares,
        royaltyRate
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Royalty-Informationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Royalty-Informationen
   */
  async getRoyaltyInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Royalty-Informationen für Token ${tokenId} ab`);
      
      // Simulierte Royalty-Informationen
      return {
        tokenId,
        recipients: ['0x1234...', '0x5678...'],
        shares: [60, 40], // 60% und 40%
        royaltyRate: this.options.defaultRoyaltyPercentage,
        totalRoyalty: this.options.defaultRoyaltyPercentage
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Berechnet die Royalty-Zahlung für einen Verkauf
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Royalty-Zahlungsinformationen
   */
  async calculateRoyaltyPayment(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Royalty-Informationen ab
      const royaltyInfo = await this.getRoyaltyInfo(tokenId);
      
      // Berechne Royalty-Betrag
      const royaltyAmount = salePrice * (royaltyInfo.royaltyRate / 100);
      
      // Berechne Zahlungen pro Empfänger
      const payments = royaltyInfo.recipients.map((recipient, index) => ({
        recipient,
        share: royaltyInfo.shares[index],
        amount: royaltyAmount * (royaltyInfo.shares[index] / 100)
      }));
      
      return {
        tokenId,
        salePrice,
        royaltyRate: royaltyInfo.royaltyRate,
        totalRoyalty: royaltyAmount,
        payments
      };
    } catch (error) {
      console.error('Fehler bei der Berechnung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Führt eine Royalty-Zahlung für einen Verkauf durch
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async payRoyalties(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Berechne Royalty-Zahlungen
      const royaltyPayment = await this.calculateRoyaltyPayment(tokenId, salePrice);
      
      // In einer realen Implementierung würden wir hier die Zahlungen durchführen
      console.log(`Führe Royalty-Zahlungen für Token ${tokenId} durch:`, royaltyPayment);
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        salePrice,
        royaltyRate: royaltyPayment.royaltyRate,
        totalRoyalty: royaltyPayment.totalRoyalty,
        payments: royaltyPayment.payments
      };
    } catch (error) {
      console.error('Fehler bei der Durchführung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Lizenzinformationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} licenseInfo - Lizenzinformationen
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setLicenseInfo(tokenId, licenseInfo) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Lizenzinformationen
    if (!licenseInfo.type) {
      throw new Error('Lizenztyp muss angegeben werden');
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Lizenzinformationen für Token ${tokenId}:`, licenseInfo);
      
      // Standardisiere Lizenzinformationen
      const standardizedLicense = {
        type: licenseInfo.type,
        name: licenseInfo.name || this.getLicenseName(licenseInfo.type),
        url: licenseInfo.url || this.getLicenseUrl(licenseInfo.type),
        commercial: licenseInfo.commercial !== undefined ? licenseInfo.commercial : true,
        derivatives: licenseInfo.derivatives !== undefined ? licenseInfo.derivatives : true,
        attribution: licenseInfo.attribution !== undefined ? licenseInfo.attribution : true
      };
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        license: standardizedLicense
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Lizenzinformationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Lizenzinformationen
   */
  async getLicenseInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Lizenzinformationen für Token ${tokenId} ab`);
      
      // Simulierte Lizenzinformationen
      return {
        tokenId,
        license: {
          type: 'cc-by',
          name: 'Creative Commons Attribution 4.0',
          url: 'https://creativecommons.org/licenses/by/4.0/',
          commercial: true,
          derivatives: true,
          attribution: true
        }
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Nutzung mit der Lizenz kompatibel ist
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} usage - Nutzungsinformationen
   * @returns {Promise<Object>} - Kompatibilitätsinformationen
   */
  async checkLicenseCompatibility(tokenId, usage) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Lizenzinformationen ab
      const licenseInfo = await this.getLicenseInfo(tokenId);
      
      // Prüfe Kompatibilität
      const isCommercialCompatible = !usage.commercial || licenseInfo.license.commercial;
      const isDerivativesCompatible = !usage.derivatives || licenseInfo.license.derivatives;
      const isAttributionCompatible = !licenseInfo.license.attribution || usage.attribution;
      
      const isCompatible = isCommercialCompatible && isDerivativesCompatible && isAttributionCompatible;
      
      return {
        tokenId,
        license: licenseInfo.license,
        usage,
        isCompatible,
        incompatibilities: {
          commercial: !isCommercialCompatible,
          derivatives: !isDerivativesCompatible,
          attribution: !isAttributionCompatible
        }
      };
    } catch (error) {
      console.error('Fehler bei der Prüfung der Lizenzkompatibilität:', error);
      throw error;
    }
  }
  
  /**
   * Gibt den Namen einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseName(type) {
    const licenseNames = {
      'cc-by': 'Creative Commons Attribution 4.0',
      'cc-by-sa': 'Creative Commons Attribution-ShareAlike 4.0',
      'cc-by-nc': 'Creative Commons Attribution-NonCommercial 4.0',
      'cc-by-nc-sa': 'Creative Commons Attribution-NonCommercial-ShareAlike 4.0',
      'cc-by-nd': 'Creative Commons Attribution-NoDerivatives 4.0',
      'cc-by-nc-nd': 'Creative Commons Attribution-NonCommercial-NoDerivatives 4.0',
      'cc0': 'Creative Commons Zero v1.0 Universal',
      'mit': 'MIT License',
      'apache-2.0': 'Apache License 2.0',
      'gpl-3.0': 'GNU General Public License v3.0'
    };
    
    return licenseNames[type] || 'Custom License';
  }
  
  /**
   * Gibt die URL einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseUrl(type) {
    const licenseUrls = {
      'cc-by': 'https://creativecommons.org/licenses/by/4.0/',
      'cc-by-sa': 'https://creativecommons.org/licenses/by-sa/4.0/',
      'cc-by-nc': 'https://creativecommons.org/licenses/by-nc/4.0/',
      'cc-by-nc-sa': 'https://creativecommons.org/licenses/by-nc-sa/4.0/',
      'cc-by-nd': 'https://creativecommons.org/licenses/by-nd/4.0/',
      'cc-by-nc-nd': 'https://creativecommons.org/licenses/by-nc-nd/4.0/',
      'cc0': 'https://creativecommons.org/publicdomain/zero/1.0/',
      'mit': 'https://opensource.org/licenses/MIT',
      'apache-2.0': 'https://opensource.org/licenses/Apache-2.0',
      'gpl-3.0': 'https://www.gnu.org/licenses/gpl-3.0.en.html'
    };
    
    return licenseUrls[type] || '';
  }
}

export default DoiRoyaltyContract;export default DoiRoyaltyContract;
export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract; */

class DoiRoyaltyContract {
  constructor(api, contractAddress, options = {}) {
    this.api = api;
    this.contractAddress = contractAddress;
    this.options = {
      defaultRoyaltyPercentage: 5, // 5% Standardroyalty
      maxRoyaltyPercentage: 15,    // Maximale Royalty von 15%
      ...options
    };
    
    // Initialisiere den Contract
    this.initialize();
  }
  
  /**
   * Initialisiert den Contract
   * @private
   */
  async initialize() {
    try {
      // In einer realen Implementierung würden wir hier den Contract ABI laden
      // und eine Verbindung zum Contract herstellen
      console.log('Initialisiere DOI Royalty Contract:', this.contractAddress);
      
      // Simuliere Contract-Initialisierung
      this.initialized = true;
    } catch (error) {
      console.error('Fehler bei der Initialisierung des Royalty Contracts:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Royalty-Informationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Array} recipients - Liste der Empfänger (Adressen)
   * @param {Array} shares - Liste der Anteile in Prozent
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setRoyaltyInfo(tokenId, recipients, shares) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Eingaben
    if (!Array.isArray(recipients) || !Array.isArray(shares)) {
      throw new Error('Empfänger und Anteile müssen Arrays sein');
    }
    
    if (recipients.length !== shares.length) {
      throw new Error('Anzahl der Empfänger und Anteile muss übereinstimmen');
    }
    
    // Prüfe, ob die Summe der Anteile 100% nicht überschreitet
    const totalShares = shares.reduce((sum, share) => sum + share, 0);
    if (totalShares > 100) {
      throw new Error('Summe der Anteile darf 100% nicht überschreiten');
    }
    
    // Prüfe, ob die Royalty-Rate das Maximum nicht überschreitet
    const royaltyRate = totalShares / 100 * this.options.defaultRoyaltyPercentage;
    if (royaltyRate > this.options.maxRoyaltyPercentage) {
      throw new Error(`Royalty-Rate darf ${this.options.maxRoyaltyPercentage}% nicht überschreiten`);
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Royalty-Informationen für Token ${tokenId}:`, {
        recipients,
        shares,
        royaltyRate: `${royaltyRate}%`
      });
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        recipients,
        shares,
        royaltyRate
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Royalty-Informationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Royalty-Informationen
   */
  async getRoyaltyInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Royalty-Informationen für Token ${tokenId} ab`);
      
      // Simulierte Royalty-Informationen
      return {
        tokenId,
        recipients: ['0x1234...', '0x5678...'],
        shares: [60, 40], // 60% und 40%
        royaltyRate: this.options.defaultRoyaltyPercentage,
        totalRoyalty: this.options.defaultRoyaltyPercentage
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Berechnet die Royalty-Zahlung für einen Verkauf
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Royalty-Zahlungsinformationen
   */
  async calculateRoyaltyPayment(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Royalty-Informationen ab
      const royaltyInfo = await this.getRoyaltyInfo(tokenId);
      
      // Berechne Royalty-Betrag
      const royaltyAmount = salePrice * (royaltyInfo.royaltyRate / 100);
      
      // Berechne Zahlungen pro Empfänger
      const payments = royaltyInfo.recipients.map((recipient, index) => ({
        recipient,
        share: royaltyInfo.shares[index],
        amount: royaltyAmount * (royaltyInfo.shares[index] / 100)
      }));
      
      return {
        tokenId,
        salePrice,
        royaltyRate: royaltyInfo.royaltyRate,
        totalRoyalty: royaltyAmount,
        payments
      };
    } catch (error) {
      console.error('Fehler bei der Berechnung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Führt eine Royalty-Zahlung für einen Verkauf durch
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async payRoyalties(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Berechne Royalty-Zahlungen
      const royaltyPayment = await this.calculateRoyaltyPayment(tokenId, salePrice);
      
      // In einer realen Implementierung würden wir hier die Zahlungen durchführen
      console.log(`Führe Royalty-Zahlungen für Token ${tokenId} durch:`, royaltyPayment);
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        salePrice,
        royaltyRate: royaltyPayment.royaltyRate,
        totalRoyalty: royaltyPayment.totalRoyalty,
        payments: royaltyPayment.payments
      };
    } catch (error) {
      console.error('Fehler bei der Durchführung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Lizenzinformationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} licenseInfo - Lizenzinformationen
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setLicenseInfo(tokenId, licenseInfo) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Lizenzinformationen
    if (!licenseInfo.type) {
      throw new Error('Lizenztyp muss angegeben werden');
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Lizenzinformationen für Token ${tokenId}:`, licenseInfo);
      
      // Standardisiere Lizenzinformationen
      const standardizedLicense = {
        type: licenseInfo.type,
        name: licenseInfo.name || this.getLicenseName(licenseInfo.type),
        url: licenseInfo.url || this.getLicenseUrl(licenseInfo.type),
        commercial: licenseInfo.commercial !== undefined ? licenseInfo.commercial : true,
        derivatives: licenseInfo.derivatives !== undefined ? licenseInfo.derivatives : true,
        attribution: licenseInfo.attribution !== undefined ? licenseInfo.attribution : true
      };
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        license: standardizedLicense
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Lizenzinformationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Lizenzinformationen
   */
  async getLicenseInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Lizenzinformationen für Token ${tokenId} ab`);
      
      // Simulierte Lizenzinformationen
      return {
        tokenId,
        license: {
          type: 'cc-by',
          name: 'Creative Commons Attribution 4.0',
          url: 'https://creativecommons.org/licenses/by/4.0/',
          commercial: true,
          derivatives: true,
          attribution: true
        }
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Nutzung mit der Lizenz kompatibel ist
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} usage - Nutzungsinformationen
   * @returns {Promise<Object>} - Kompatibilitätsinformationen
   */
  async checkLicenseCompatibility(tokenId, usage) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Lizenzinformationen ab
      const licenseInfo = await this.getLicenseInfo(tokenId);
      
      // Prüfe Kompatibilität
      const isCommercialCompatible = !usage.commercial || licenseInfo.license.commercial;
      const isDerivativesCompatible = !usage.derivatives || licenseInfo.license.derivatives;
      const isAttributionCompatible = !licenseInfo.license.attribution || usage.attribution;
      
      const isCompatible = isCommercialCompatible && isDerivativesCompatible && isAttributionCompatible;
      
      return {
        tokenId,
        license: licenseInfo.license,
        usage,
        isCompatible,
        incompatibilities: {
          commercial: !isCommercialCompatible,
          derivatives: !isDerivativesCompatible,
          attribution: !isAttributionCompatible
        }
      };
    } catch (error) {
      console.error('Fehler bei der Prüfung der Lizenzkompatibilität:', error);
      throw error;
    }
  }
  
  /**
   * Gibt den Namen einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseName(type) {
    const licenseNames = {
      'cc-by': 'Creative Commons Attribution 4.0',
      'cc-by-sa': 'Creative Commons Attribution-ShareAlike 4.0',
      'cc-by-nc': 'Creative Commons Attribution-NonCommercial 4.0',
      'cc-by-nc-sa': 'Creative Commons Attribution-NonCommercial-ShareAlike 4.0',
      'cc-by-nd': 'Creative Commons Attribution-NoDerivatives 4.0',
      'cc-by-nc-nd': 'Creative Commons Attribution-NonCommercial-NoDerivatives 4.0',
      'cc0': 'Creative Commons Zero v1.0 Universal',
      'mit': 'MIT License',
      'apache-2.0': 'Apache License 2.0',
      'gpl-3.0': 'GNU General Public License v3.0'
    };
    
    return licenseNames[type] || 'Custom License';
  }
  
  /**
   * Gibt die URL einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseUrl(type) {
    const licenseUrls = {
      'cc-by': 'https://creativecommons.org/licenses/by/4.0/',
      'cc-by-sa': 'https://creativecommons.org/licenses/by-sa/4.0/',
      'cc-by-nc': 'https://creativecommons.org/licenses/by-nc/4.0/',
      'cc-by-nc-sa': 'https://creativecommons.org/licenses/by-nc-sa/4.0/',
      'cc-by-nd': 'https://creativecommons.org/licenses/by-nd/4.0/',
      'cc-by-nc-nd': 'https://creativecommons.org/licenses/by-nc-nd/4.0/',
      'cc0': 'https://creativecommons.org/publicdomain/zero/1.0/',
      'mit': 'https://opensource.org/licenses/MIT',
      'apache-2.0': 'https://opensource.org/licenses/Apache-2.0',
      'gpl-3.0': 'https://www.gnu.org/licenses/gpl-3.0.en.html'
    };
    
    return licenseUrls[type] || '';
  }
}

export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;
export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract; */

class DoiRoyaltyContract {
  constructor(api, contractAddress, options = {}) {
    this.api = api;
    this.contractAddress = contractAddress;
    this.options = {
      defaultRoyaltyPercentage: 5, // 5% Standardroyalty
      maxRoyaltyPercentage: 15,    // Maximale Royalty von 15%
      ...options
    };
    
    // Initialisiere den Contract
    this.initialize();
  }
  
  /**
   * Initialisiert den Contract
   * @private
   */
  async initialize() {
    try {
      // In einer realen Implementierung würden wir hier den Contract ABI laden
      // und eine Verbindung zum Contract herstellen
      console.log('Initialisiere DOI Royalty Contract:', this.contractAddress);
      
      // Simuliere Contract-Initialisierung
      this.initialized = true;
    } catch (error) {
      console.error('Fehler bei der Initialisierung des Royalty Contracts:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Royalty-Informationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Array} recipients - Liste der Empfänger (Adressen)
   * @param {Array} shares - Liste der Anteile in Prozent
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setRoyaltyInfo(tokenId, recipients, shares) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Eingaben
    if (!Array.isArray(recipients) || !Array.isArray(shares)) {
      throw new Error('Empfänger und Anteile müssen Arrays sein');
    }
    
    if (recipients.length !== shares.length) {
      throw new Error('Anzahl der Empfänger und Anteile muss übereinstimmen');
    }
    
    // Prüfe, ob die Summe der Anteile 100% nicht überschreitet
    const totalShares = shares.reduce((sum, share) => sum + share, 0);
    if (totalShares > 100) {
      throw new Error('Summe der Anteile darf 100% nicht überschreiten');
    }
    
    // Prüfe, ob die Royalty-Rate das Maximum nicht überschreitet
    const royaltyRate = totalShares / 100 * this.options.defaultRoyaltyPercentage;
    if (royaltyRate > this.options.maxRoyaltyPercentage) {
      throw new Error(`Royalty-Rate darf ${this.options.maxRoyaltyPercentage}% nicht überschreiten`);
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Royalty-Informationen für Token ${tokenId}:`, {
        recipients,
        shares,
        royaltyRate: `${royaltyRate}%`
      });
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        recipients,
        shares,
        royaltyRate
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Royalty-Informationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Royalty-Informationen
   */
  async getRoyaltyInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Royalty-Informationen für Token ${tokenId} ab`);
      
      // Simulierte Royalty-Informationen
      return {
        tokenId,
        recipients: ['0x1234...', '0x5678...'],
        shares: [60, 40], // 60% und 40%
        royaltyRate: this.options.defaultRoyaltyPercentage,
        totalRoyalty: this.options.defaultRoyaltyPercentage
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Berechnet die Royalty-Zahlung für einen Verkauf
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Royalty-Zahlungsinformationen
   */
  async calculateRoyaltyPayment(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Royalty-Informationen ab
      const royaltyInfo = await this.getRoyaltyInfo(tokenId);
      
      // Berechne Royalty-Betrag
      const royaltyAmount = salePrice * (royaltyInfo.royaltyRate / 100);
      
      // Berechne Zahlungen pro Empfänger
      const payments = royaltyInfo.recipients.map((recipient, index) => ({
        recipient,
        share: royaltyInfo.shares[index],
        amount: royaltyAmount * (royaltyInfo.shares[index] / 100)
      }));
      
      return {
        tokenId,
        salePrice,
        royaltyRate: royaltyInfo.royaltyRate,
        totalRoyalty: royaltyAmount,
        payments
      };
    } catch (error) {
      console.error('Fehler bei der Berechnung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Führt eine Royalty-Zahlung für einen Verkauf durch
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async payRoyalties(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Berechne Royalty-Zahlungen
      const royaltyPayment = await this.calculateRoyaltyPayment(tokenId, salePrice);
      
      // In einer realen Implementierung würden wir hier die Zahlungen durchführen
      console.log(`Führe Royalty-Zahlungen für Token ${tokenId} durch:`, royaltyPayment);
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        salePrice,
        royaltyRate: royaltyPayment.royaltyRate,
        totalRoyalty: royaltyPayment.totalRoyalty,
        payments: royaltyPayment.payments
      };
    } catch (error) {
      console.error('Fehler bei der Durchführung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Lizenzinformationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} licenseInfo - Lizenzinformationen
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setLicenseInfo(tokenId, licenseInfo) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Lizenzinformationen
    if (!licenseInfo.type) {
      throw new Error('Lizenztyp muss angegeben werden');
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Lizenzinformationen für Token ${tokenId}:`, licenseInfo);
      
      // Standardisiere Lizenzinformationen
      const standardizedLicense = {
        type: licenseInfo.type,
        name: licenseInfo.name || this.getLicenseName(licenseInfo.type),
        url: licenseInfo.url || this.getLicenseUrl(licenseInfo.type),
        commercial: licenseInfo.commercial !== undefined ? licenseInfo.commercial : true,
        derivatives: licenseInfo.derivatives !== undefined ? licenseInfo.derivatives : true,
        attribution: licenseInfo.attribution !== undefined ? licenseInfo.attribution : true
      };
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        license: standardizedLicense
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Lizenzinformationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Lizenzinformationen
   */
  async getLicenseInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Lizenzinformationen für Token ${tokenId} ab`);
      
      // Simulierte Lizenzinformationen
      return {
        tokenId,
        license: {
          type: 'cc-by',
          name: 'Creative Commons Attribution 4.0',
          url: 'https://creativecommons.org/licenses/by/4.0/',
          commercial: true,
          derivatives: true,
          attribution: true
        }
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Nutzung mit der Lizenz kompatibel ist
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} usage - Nutzungsinformationen
   * @returns {Promise<Object>} - Kompatibilitätsinformationen
   */
  async checkLicenseCompatibility(tokenId, usage) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Lizenzinformationen ab
      const licenseInfo = await this.getLicenseInfo(tokenId);
      
      // Prüfe Kompatibilität
      const isCommercialCompatible = !usage.commercial || licenseInfo.license.commercial;
      const isDerivativesCompatible = !usage.derivatives || licenseInfo.license.derivatives;
      const isAttributionCompatible = !licenseInfo.license.attribution || usage.attribution;
      
      const isCompatible = isCommercialCompatible && isDerivativesCompatible && isAttributionCompatible;
      
      return {
        tokenId,
        license: licenseInfo.license,
        usage,
        isCompatible,
        incompatibilities: {
          commercial: !isCommercialCompatible,
          derivatives: !isDerivativesCompatible,
          attribution: !isAttributionCompatible
        }
      };
    } catch (error) {
      console.error('Fehler bei der Prüfung der Lizenzkompatibilität:', error);
      throw error;
    }
  }
  
  /**
   * Gibt den Namen einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseName(type) {
    const licenseNames = {
      'cc-by': 'Creative Commons Attribution 4.0',
      'cc-by-sa': 'Creative Commons Attribution-ShareAlike 4.0',
      'cc-by-nc': 'Creative Commons Attribution-NonCommercial 4.0',
      'cc-by-nc-sa': 'Creative Commons Attribution-NonCommercial-ShareAlike 4.0',
      'cc-by-nd': 'Creative Commons Attribution-NoDerivatives 4.0',
      'cc-by-nc-nd': 'Creative Commons Attribution-NonCommercial-NoDerivatives 4.0',
      'cc0': 'Creative Commons Zero v1.0 Universal',
      'mit': 'MIT License',
      'apache-2.0': 'Apache License 2.0',
      'gpl-3.0': 'GNU General Public License v3.0'
    };
    
    return licenseNames[type] || 'Custom License';
  }
  
  /**
   * Gibt die URL einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseUrl(type) {
    const licenseUrls = {
      'cc-by': 'https://creativecommons.org/licenses/by/4.0/',
      'cc-by-sa': 'https://creativecommons.org/licenses/by-sa/4.0/',
      'cc-by-nc': 'https://creativecommons.org/licenses/by-nc/4.0/',
      'cc-by-nc-sa': 'https://creativecommons.org/licenses/by-nc-sa/4.0/',
      'cc-by-nd': 'https://creativecommons.org/licenses/by-nd/4.0/',
      'cc-by-nc-nd': 'https://creativecommons.org/licenses/by-nc-nd/4.0/',
      'cc0': 'https://creativecommons.org/publicdomain/zero/1.0/',
      'mit': 'https://opensource.org/licenses/MIT',
      'apache-2.0': 'https://opensource.org/licenses/Apache-2.0',
      'gpl-3.0': 'https://www.gnu.org/licenses/gpl-3.0.en.html'
    };
    
    return licenseUrls[type] || '';
  }
}

export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;
export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract; */

class DoiRoyaltyContract {
  constructor(api, contractAddress, options = {}) {
    this.api = api;
    this.contractAddress = contractAddress;
    this.options = {
      defaultRoyaltyPercentage: 5, // 5% Standardroyalty
      maxRoyaltyPercentage: 15,    // Maximale Royalty von 15%
      ...options
    };
    
    // Initialisiere den Contract
    this.initialize();
  }
  
  /**
   * Initialisiert den Contract
   * @private
   */
  async initialize() {
    try {
      // In einer realen Implementierung würden wir hier den Contract ABI laden
      // und eine Verbindung zum Contract herstellen
      console.log('Initialisiere DOI Royalty Contract:', this.contractAddress);
      
      // Simuliere Contract-Initialisierung
      this.initialized = true;
    } catch (error) {
      console.error('Fehler bei der Initialisierung des Royalty Contracts:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Royalty-Informationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Array} recipients - Liste der Empfänger (Adressen)
   * @param {Array} shares - Liste der Anteile in Prozent
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setRoyaltyInfo(tokenId, recipients, shares) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Eingaben
    if (!Array.isArray(recipients) || !Array.isArray(shares)) {
      throw new Error('Empfänger und Anteile müssen Arrays sein');
    }
    
    if (recipients.length !== shares.length) {
      throw new Error('Anzahl der Empfänger und Anteile muss übereinstimmen');
    }
    
    // Prüfe, ob die Summe der Anteile 100% nicht überschreitet
    const totalShares = shares.reduce((sum, share) => sum + share, 0);
    if (totalShares > 100) {
      throw new Error('Summe der Anteile darf 100% nicht überschreiten');
    }
    
    // Prüfe, ob die Royalty-Rate das Maximum nicht überschreitet
    const royaltyRate = totalShares / 100 * this.options.defaultRoyaltyPercentage;
    if (royaltyRate > this.options.maxRoyaltyPercentage) {
      throw new Error(`Royalty-Rate darf ${this.options.maxRoyaltyPercentage}% nicht überschreiten`);
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Royalty-Informationen für Token ${tokenId}:`, {
        recipients,
        shares,
        royaltyRate: `${royaltyRate}%`
      });
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        recipients,
        shares,
        royaltyRate
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Royalty-Informationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Royalty-Informationen
   */
  async getRoyaltyInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Royalty-Informationen für Token ${tokenId} ab`);
      
      // Simulierte Royalty-Informationen
      return {
        tokenId,
        recipients: ['0x1234...', '0x5678...'],
        shares: [60, 40], // 60% und 40%
        royaltyRate: this.options.defaultRoyaltyPercentage,
        totalRoyalty: this.options.defaultRoyaltyPercentage
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Berechnet die Royalty-Zahlung für einen Verkauf
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Royalty-Zahlungsinformationen
   */
  async calculateRoyaltyPayment(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Royalty-Informationen ab
      const royaltyInfo = await this.getRoyaltyInfo(tokenId);
      
      // Berechne Royalty-Betrag
      const royaltyAmount = salePrice * (royaltyInfo.royaltyRate / 100);
      
      // Berechne Zahlungen pro Empfänger
      const payments = royaltyInfo.recipients.map((recipient, index) => ({
        recipient,
        share: royaltyInfo.shares[index],
        amount: royaltyAmount * (royaltyInfo.shares[index] / 100)
      }));
      
      return {
        tokenId,
        salePrice,
        royaltyRate: royaltyInfo.royaltyRate,
        totalRoyalty: royaltyAmount,
        payments
      };
    } catch (error) {
      console.error('Fehler bei der Berechnung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Führt eine Royalty-Zahlung für einen Verkauf durch
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async payRoyalties(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Berechne Royalty-Zahlungen
      const royaltyPayment = await this.calculateRoyaltyPayment(tokenId, salePrice);
      
      // In einer realen Implementierung würden wir hier die Zahlungen durchführen
      console.log(`Führe Royalty-Zahlungen für Token ${tokenId} durch:`, royaltyPayment);
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        salePrice,
        royaltyRate: royaltyPayment.royaltyRate,
        totalRoyalty: royaltyPayment.totalRoyalty,
        payments: royaltyPayment.payments
      };
    } catch (error) {
      console.error('Fehler bei der Durchführung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Lizenzinformationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} licenseInfo - Lizenzinformationen
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setLicenseInfo(tokenId, licenseInfo) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Lizenzinformationen
    if (!licenseInfo.type) {
      throw new Error('Lizenztyp muss angegeben werden');
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Lizenzinformationen für Token ${tokenId}:`, licenseInfo);
      
      // Standardisiere Lizenzinformationen
      const standardizedLicense = {
        type: licenseInfo.type,
        name: licenseInfo.name || this.getLicenseName(licenseInfo.type),
        url: licenseInfo.url || this.getLicenseUrl(licenseInfo.type),
        commercial: licenseInfo.commercial !== undefined ? licenseInfo.commercial : true,
        derivatives: licenseInfo.derivatives !== undefined ? licenseInfo.derivatives : true,
        attribution: licenseInfo.attribution !== undefined ? licenseInfo.attribution : true
      };
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        license: standardizedLicense
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Lizenzinformationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Lizenzinformationen
   */
  async getLicenseInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Lizenzinformationen für Token ${tokenId} ab`);
      
      // Simulierte Lizenzinformationen
      return {
        tokenId,
        license: {
          type: 'cc-by',
          name: 'Creative Commons Attribution 4.0',
          url: 'https://creativecommons.org/licenses/by/4.0/',
          commercial: true,
          derivatives: true,
          attribution: true
        }
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Nutzung mit der Lizenz kompatibel ist
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} usage - Nutzungsinformationen
   * @returns {Promise<Object>} - Kompatibilitätsinformationen
   */
  async checkLicenseCompatibility(tokenId, usage) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Lizenzinformationen ab
      const licenseInfo = await this.getLicenseInfo(tokenId);
      
      // Prüfe Kompatibilität
      const isCommercialCompatible = !usage.commercial || licenseInfo.license.commercial;
      const isDerivativesCompatible = !usage.derivatives || licenseInfo.license.derivatives;
      const isAttributionCompatible = !licenseInfo.license.attribution || usage.attribution;
      
      const isCompatible = isCommercialCompatible && isDerivativesCompatible && isAttributionCompatible;
      
      return {
        tokenId,
        license: licenseInfo.license,
        usage,
        isCompatible,
        incompatibilities: {
          commercial: !isCommercialCompatible,
          derivatives: !isDerivativesCompatible,
          attribution: !isAttributionCompatible
        }
      };
    } catch (error) {
      console.error('Fehler bei der Prüfung der Lizenzkompatibilität:', error);
      throw error;
    }
  }
  
  /**
   * Gibt den Namen einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseName(type) {
    const licenseNames = {
      'cc-by': 'Creative Commons Attribution 4.0',
      'cc-by-sa': 'Creative Commons Attribution-ShareAlike 4.0',
      'cc-by-nc': 'Creative Commons Attribution-NonCommercial 4.0',
      'cc-by-nc-sa': 'Creative Commons Attribution-NonCommercial-ShareAlike 4.0',
      'cc-by-nd': 'Creative Commons Attribution-NoDerivatives 4.0',
      'cc-by-nc-nd': 'Creative Commons Attribution-NonCommercial-NoDerivatives 4.0',
      'cc0': 'Creative Commons Zero v1.0 Universal',
      'mit': 'MIT License',
      'apache-2.0': 'Apache License 2.0',
      'gpl-3.0': 'GNU General Public License v3.0'
    };
    
    return licenseNames[type] || 'Custom License';
  }
  
  /**
   * Gibt die URL einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseUrl(type) {
    const licenseUrls = {
      'cc-by': 'https://creativecommons.org/licenses/by/4.0/',
      'cc-by-sa': 'https://creativecommons.org/licenses/by-sa/4.0/',
      'cc-by-nc': 'https://creativecommons.org/licenses/by-nc/4.0/',
      'cc-by-nc-sa': 'https://creativecommons.org/licenses/by-nc-sa/4.0/',
      'cc-by-nd': 'https://creativecommons.org/licenses/by-nd/4.0/',
      'cc-by-nc-nd': 'https://creativecommons.org/licenses/by-nc-nd/4.0/',
      'cc0': 'https://creativecommons.org/publicdomain/zero/1.0/',
      'mit': 'https://opensource.org/licenses/MIT',
      'apache-2.0': 'https://opensource.org/licenses/Apache-2.0',
      'gpl-3.0': 'https://www.gnu.org/licenses/gpl-3.0.en.html'
    };
    
    return licenseUrls[type] || '';
  }
}

export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;
export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract; */

class DoiRoyaltyContract {
  constructor(api, contractAddress, options = {}) {
    this.api = api;
    this.contractAddress = contractAddress;
    this.options = {
      defaultRoyaltyPercentage: 5, // 5% Standardroyalty
      maxRoyaltyPercentage: 15,    // Maximale Royalty von 15%
      ...options
    };
    
    // Initialisiere den Contract
    this.initialize();
  }
  
  /**
   * Initialisiert den Contract
   * @private
   */
  async initialize() {
    try {
      // In einer realen Implementierung würden wir hier den Contract ABI laden
      // und eine Verbindung zum Contract herstellen
      console.log('Initialisiere DOI Royalty Contract:', this.contractAddress);
      
      // Simuliere Contract-Initialisierung
      this.initialized = true;
    } catch (error) {
      console.error('Fehler bei der Initialisierung des Royalty Contracts:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Royalty-Informationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Array} recipients - Liste der Empfänger (Adressen)
   * @param {Array} shares - Liste der Anteile in Prozent
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setRoyaltyInfo(tokenId, recipients, shares) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Eingaben
    if (!Array.isArray(recipients) || !Array.isArray(shares)) {
      throw new Error('Empfänger und Anteile müssen Arrays sein');
    }
    
    if (recipients.length !== shares.length) {
      throw new Error('Anzahl der Empfänger und Anteile muss übereinstimmen');
    }
    
    // Prüfe, ob die Summe der Anteile 100% nicht überschreitet
    const totalShares = shares.reduce((sum, share) => sum + share, 0);
    if (totalShares > 100) {
      throw new Error('Summe der Anteile darf 100% nicht überschreiten');
    }
    
    // Prüfe, ob die Royalty-Rate das Maximum nicht überschreitet
    const royaltyRate = totalShares / 100 * this.options.defaultRoyaltyPercentage;
    if (royaltyRate > this.options.maxRoyaltyPercentage) {
      throw new Error(`Royalty-Rate darf ${this.options.maxRoyaltyPercentage}% nicht überschreiten`);
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Royalty-Informationen für Token ${tokenId}:`, {
        recipients,
        shares,
        royaltyRate: `${royaltyRate}%`
      });
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        recipients,
        shares,
        royaltyRate
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Royalty-Informationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Royalty-Informationen
   */
  async getRoyaltyInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Royalty-Informationen für Token ${tokenId} ab`);
      
      // Simulierte Royalty-Informationen
      return {
        tokenId,
        recipients: ['0x1234...', '0x5678...'],
        shares: [60, 40], // 60% und 40%
        royaltyRate: this.options.defaultRoyaltyPercentage,
        totalRoyalty: this.options.defaultRoyaltyPercentage
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Berechnet die Royalty-Zahlung für einen Verkauf
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Royalty-Zahlungsinformationen
   */
  async calculateRoyaltyPayment(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Royalty-Informationen ab
      const royaltyInfo = await this.getRoyaltyInfo(tokenId);
      
      // Berechne Royalty-Betrag
      const royaltyAmount = salePrice * (royaltyInfo.royaltyRate / 100);
      
      // Berechne Zahlungen pro Empfänger
      const payments = royaltyInfo.recipients.map((recipient, index) => ({
        recipient,
        share: royaltyInfo.shares[index],
        amount: royaltyAmount * (royaltyInfo.shares[index] / 100)
      }));
      
      return {
        tokenId,
        salePrice,
        royaltyRate: royaltyInfo.royaltyRate,
        totalRoyalty: royaltyAmount,
        payments
      };
    } catch (error) {
      console.error('Fehler bei der Berechnung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Führt eine Royalty-Zahlung für einen Verkauf durch
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async payRoyalties(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Berechne Royalty-Zahlungen
      const royaltyPayment = await this.calculateRoyaltyPayment(tokenId, salePrice);
      
      // In einer realen Implementierung würden wir hier die Zahlungen durchführen
      console.log(`Führe Royalty-Zahlungen für Token ${tokenId} durch:`, royaltyPayment);
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        salePrice,
        royaltyRate: royaltyPayment.royaltyRate,
        totalRoyalty: royaltyPayment.totalRoyalty,
        payments: royaltyPayment.payments
      };
    } catch (error) {
      console.error('Fehler bei der Durchführung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Lizenzinformationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} licenseInfo - Lizenzinformationen
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setLicenseInfo(tokenId, licenseInfo) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Lizenzinformationen
    if (!licenseInfo.type) {
      throw new Error('Lizenztyp muss angegeben werden');
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Lizenzinformationen für Token ${tokenId}:`, licenseInfo);
      
      // Standardisiere Lizenzinformationen
      const standardizedLicense = {
        type: licenseInfo.type,
        name: licenseInfo.name || this.getLicenseName(licenseInfo.type),
        url: licenseInfo.url || this.getLicenseUrl(licenseInfo.type),
        commercial: licenseInfo.commercial !== undefined ? licenseInfo.commercial : true,
        derivatives: licenseInfo.derivatives !== undefined ? licenseInfo.derivatives : true,
        attribution: licenseInfo.attribution !== undefined ? licenseInfo.attribution : true
      };
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        license: standardizedLicense
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Lizenzinformationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Lizenzinformationen
   */
  async getLicenseInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Lizenzinformationen für Token ${tokenId} ab`);
      
      // Simulierte Lizenzinformationen
      return {
        tokenId,
        license: {
          type: 'cc-by',
          name: 'Creative Commons Attribution 4.0',
          url: 'https://creativecommons.org/licenses/by/4.0/',
          commercial: true,
          derivatives: true,
          attribution: true
        }
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Nutzung mit der Lizenz kompatibel ist
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} usage - Nutzungsinformationen
   * @returns {Promise<Object>} - Kompatibilitätsinformationen
   */
  async checkLicenseCompatibility(tokenId, usage) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Lizenzinformationen ab
      const licenseInfo = await this.getLicenseInfo(tokenId);
      
      // Prüfe Kompatibilität
      const isCommercialCompatible = !usage.commercial || licenseInfo.license.commercial;
      const isDerivativesCompatible = !usage.derivatives || licenseInfo.license.derivatives;
      const isAttributionCompatible = !licenseInfo.license.attribution || usage.attribution;
      
      const isCompatible = isCommercialCompatible && isDerivativesCompatible && isAttributionCompatible;
      
      return {
        tokenId,
        license: licenseInfo.license,
        usage,
        isCompatible,
        incompatibilities: {
          commercial: !isCommercialCompatible,
          derivatives: !isDerivativesCompatible,
          attribution: !isAttributionCompatible
        }
      };
    } catch (error) {
      console.error('Fehler bei der Prüfung der Lizenzkompatibilität:', error);
      throw error;
    }
  }
  
  /**
   * Gibt den Namen einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseName(type) {
    const licenseNames = {
      'cc-by': 'Creative Commons Attribution 4.0',
      'cc-by-sa': 'Creative Commons Attribution-ShareAlike 4.0',
      'cc-by-nc': 'Creative Commons Attribution-NonCommercial 4.0',
      'cc-by-nc-sa': 'Creative Commons Attribution-NonCommercial-ShareAlike 4.0',
      'cc-by-nd': 'Creative Commons Attribution-NoDerivatives 4.0',
      'cc-by-nc-nd': 'Creative Commons Attribution-NonCommercial-NoDerivatives 4.0',
      'cc0': 'Creative Commons Zero v1.0 Universal',
      'mit': 'MIT License',
      'apache-2.0': 'Apache License 2.0',
      'gpl-3.0': 'GNU General Public License v3.0'
    };
    
    return licenseNames[type] || 'Custom License';
  }
  
  /**
   * Gibt die URL einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseUrl(type) {
    const licenseUrls = {
      'cc-by': 'https://creativecommons.org/licenses/by/4.0/',
      'cc-by-sa': 'https://creativecommons.org/licenses/by-sa/4.0/',
      'cc-by-nc': 'https://creativecommons.org/licenses/by-nc/4.0/',
      'cc-by-nc-sa': 'https://creativecommons.org/licenses/by-nc-sa/4.0/',
      'cc-by-nd': 'https://creativecommons.org/licenses/by-nd/4.0/',
      'cc-by-nc-nd': 'https://creativecommons.org/licenses/by-nc-nd/4.0/',
      'cc0': 'https://creativecommons.org/publicdomain/zero/1.0/',
      'mit': 'https://opensource.org/licenses/MIT',
      'apache-2.0': 'https://opensource.org/licenses/Apache-2.0',
      'gpl-3.0': 'https://www.gnu.org/licenses/gpl-3.0.en.html'
    };
    
    return licenseUrls[type] || '';
  }
}

export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;
export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;
export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract; */

class DoiRoyaltyContract {
  constructor(api, contractAddress, options = {}) {
    this.api = api;
    this.contractAddress = contractAddress;
    this.options = {
      defaultRoyaltyPercentage: 5, // 5% Standardroyalty
      maxRoyaltyPercentage: 15,    // Maximale Royalty von 15%
      ...options
    };
    
    // Initialisiere den Contract
    this.initialize();
  }
  
  /**
   * Initialisiert den Contract
   * @private
   */
  async initialize() {
    try {
      // In einer realen Implementierung würden wir hier den Contract ABI laden
      // und eine Verbindung zum Contract herstellen
      console.log('Initialisiere DOI Royalty Contract:', this.contractAddress);
      
      // Simuliere Contract-Initialisierung
      this.initialized = true;
    } catch (error) {
      console.error('Fehler bei der Initialisierung des Royalty Contracts:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Royalty-Informationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Array} recipients - Liste der Empfänger (Adressen)
   * @param {Array} shares - Liste der Anteile in Prozent
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setRoyaltyInfo(tokenId, recipients, shares) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Eingaben
    if (!Array.isArray(recipients) || !Array.isArray(shares)) {
      throw new Error('Empfänger und Anteile müssen Arrays sein');
    }
    
    if (recipients.length !== shares.length) {
      throw new Error('Anzahl der Empfänger und Anteile muss übereinstimmen');
    }
    
    // Prüfe, ob die Summe der Anteile 100% nicht überschreitet
    const totalShares = shares.reduce((sum, share) => sum + share, 0);
    if (totalShares > 100) {
      throw new Error('Summe der Anteile darf 100% nicht überschreiten');
    }
    
    // Prüfe, ob die Royalty-Rate das Maximum nicht überschreitet
    const royaltyRate = totalShares / 100 * this.options.defaultRoyaltyPercentage;
    if (royaltyRate > this.options.maxRoyaltyPercentage) {
      throw new Error(`Royalty-Rate darf ${this.options.maxRoyaltyPercentage}% nicht überschreiten`);
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Royalty-Informationen für Token ${tokenId}:`, {
        recipients,
        shares,
        royaltyRate: `${royaltyRate}%`
      });
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        recipients,
        shares,
        royaltyRate
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Royalty-Informationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Royalty-Informationen
   */
  async getRoyaltyInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Royalty-Informationen für Token ${tokenId} ab`);
      
      // Simulierte Royalty-Informationen
      return {
        tokenId,
        recipients: ['0x1234...', '0x5678...'],
        shares: [60, 40], // 60% und 40%
        royaltyRate: this.options.defaultRoyaltyPercentage,
        totalRoyalty: this.options.defaultRoyaltyPercentage
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Berechnet die Royalty-Zahlung für einen Verkauf
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Royalty-Zahlungsinformationen
   */
  async calculateRoyaltyPayment(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Royalty-Informationen ab
      const royaltyInfo = await this.getRoyaltyInfo(tokenId);
      
      // Berechne Royalty-Betrag
      const royaltyAmount = salePrice * (royaltyInfo.royaltyRate / 100);
      
      // Berechne Zahlungen pro Empfänger
      const payments = royaltyInfo.recipients.map((recipient, index) => ({
        recipient,
        share: royaltyInfo.shares[index],
        amount: royaltyAmount * (royaltyInfo.shares[index] / 100)
      }));
      
      return {
        tokenId,
        salePrice,
        royaltyRate: royaltyInfo.royaltyRate,
        totalRoyalty: royaltyAmount,
        payments
      };
    } catch (error) {
      console.error('Fehler bei der Berechnung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Führt eine Royalty-Zahlung für einen Verkauf durch
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async payRoyalties(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Berechne Royalty-Zahlungen
      const royaltyPayment = await this.calculateRoyaltyPayment(tokenId, salePrice);
      
      // In einer realen Implementierung würden wir hier die Zahlungen durchführen
      console.log(`Führe Royalty-Zahlungen für Token ${tokenId} durch:`, royaltyPayment);
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        salePrice,
        royaltyRate: royaltyPayment.royaltyRate,
        totalRoyalty: royaltyPayment.totalRoyalty,
        payments: royaltyPayment.payments
      };
    } catch (error) {
      console.error('Fehler bei der Durchführung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Lizenzinformationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} licenseInfo - Lizenzinformationen
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setLicenseInfo(tokenId, licenseInfo) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Lizenzinformationen
    if (!licenseInfo.type) {
      throw new Error('Lizenztyp muss angegeben werden');
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Lizenzinformationen für Token ${tokenId}:`, licenseInfo);
      
      // Standardisiere Lizenzinformationen
      const standardizedLicense = {
        type: licenseInfo.type,
        name: licenseInfo.name || this.getLicenseName(licenseInfo.type),
        url: licenseInfo.url || this.getLicenseUrl(licenseInfo.type),
        commercial: licenseInfo.commercial !== undefined ? licenseInfo.commercial : true,
        derivatives: licenseInfo.derivatives !== undefined ? licenseInfo.derivatives : true,
        attribution: licenseInfo.attribution !== undefined ? licenseInfo.attribution : true
      };
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        license: standardizedLicense
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Lizenzinformationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Lizenzinformationen
   */
  async getLicenseInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Lizenzinformationen für Token ${tokenId} ab`);
      
      // Simulierte Lizenzinformationen
      return {
        tokenId,
        license: {
          type: 'cc-by',
          name: 'Creative Commons Attribution 4.0',
          url: 'https://creativecommons.org/licenses/by/4.0/',
          commercial: true,
          derivatives: true,
          attribution: true
        }
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Nutzung mit der Lizenz kompatibel ist
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} usage - Nutzungsinformationen
   * @returns {Promise<Object>} - Kompatibilitätsinformationen
   */
  async checkLicenseCompatibility(tokenId, usage) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Lizenzinformationen ab
      const licenseInfo = await this.getLicenseInfo(tokenId);
      
      // Prüfe Kompatibilität
      const isCommercialCompatible = !usage.commercial || licenseInfo.license.commercial;
      const isDerivativesCompatible = !usage.derivatives || licenseInfo.license.derivatives;
      const isAttributionCompatible = !licenseInfo.license.attribution || usage.attribution;
      
      const isCompatible = isCommercialCompatible && isDerivativesCompatible && isAttributionCompatible;
      
      return {
        tokenId,
        license: licenseInfo.license,
        usage,
        isCompatible,
        incompatibilities: {
          commercial: !isCommercialCompatible,
          derivatives: !isDerivativesCompatible,
          attribution: !isAttributionCompatible
        }
      };
    } catch (error) {
      console.error('Fehler bei der Prüfung der Lizenzkompatibilität:', error);
      throw error;
    }
  }
  
  /**
   * Gibt den Namen einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseName(type) {
    const licenseNames = {
      'cc-by': 'Creative Commons Attribution 4.0',
      'cc-by-sa': 'Creative Commons Attribution-ShareAlike 4.0',
      'cc-by-nc': 'Creative Commons Attribution-NonCommercial 4.0',
      'cc-by-nc-sa': 'Creative Commons Attribution-NonCommercial-ShareAlike 4.0',
      'cc-by-nd': 'Creative Commons Attribution-NoDerivatives 4.0',
      'cc-by-nc-nd': 'Creative Commons Attribution-NonCommercial-NoDerivatives 4.0',
      'cc0': 'Creative Commons Zero v1.0 Universal',
      'mit': 'MIT License',
      'apache-2.0': 'Apache License 2.0',
      'gpl-3.0': 'GNU General Public License v3.0'
    };
    
    return licenseNames[type] || 'Custom License';
  }
  
  /**
   * Gibt die URL einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseUrl(type) {
    const licenseUrls = {
      'cc-by': 'https://creativecommons.org/licenses/by/4.0/',
      'cc-by-sa': 'https://creativecommons.org/licenses/by-sa/4.0/',
      'cc-by-nc': 'https://creativecommons.org/licenses/by-nc/4.0/',
      'cc-by-nc-sa': 'https://creativecommons.org/licenses/by-nc-sa/4.0/',
      'cc-by-nd': 'https://creativecommons.org/licenses/by-nd/4.0/',
      'cc-by-nc-nd': 'https://creativecommons.org/licenses/by-nc-nd/4.0/',
      'cc0': 'https://creativecommons.org/publicdomain/zero/1.0/',
      'mit': 'https://opensource.org/licenses/MIT',
      'apache-2.0': 'https://opensource.org/licenses/Apache-2.0',
      'gpl-3.0': 'https://www.gnu.org/licenses/gpl-3.0.en.html'
    };
    
    return licenseUrls[type] || '';
  }
}

export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;
export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract; */

class DoiRoyaltyContract {
  constructor(api, contractAddress, options = {}) {
    this.api = api;
    this.contractAddress = contractAddress;
    this.options = {
      defaultRoyaltyPercentage: 5, // 5% Standardroyalty
      maxRoyaltyPercentage: 15,    // Maximale Royalty von 15%
      ...options
    };
    
    // Initialisiere den Contract
    this.initialize();
  }
  
  /**
   * Initialisiert den Contract
   * @private
   */
  async initialize() {
    try {
      // In einer realen Implementierung würden wir hier den Contract ABI laden
      // und eine Verbindung zum Contract herstellen
      console.log('Initialisiere DOI Royalty Contract:', this.contractAddress);
      
      // Simuliere Contract-Initialisierung
      this.initialized = true;
    } catch (error) {
      console.error('Fehler bei der Initialisierung des Royalty Contracts:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Royalty-Informationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Array} recipients - Liste der Empfänger (Adressen)
   * @param {Array} shares - Liste der Anteile in Prozent
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setRoyaltyInfo(tokenId, recipients, shares) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Eingaben
    if (!Array.isArray(recipients) || !Array.isArray(shares)) {
      throw new Error('Empfänger und Anteile müssen Arrays sein');
    }
    
    if (recipients.length !== shares.length) {
      throw new Error('Anzahl der Empfänger und Anteile muss übereinstimmen');
    }
    
    // Prüfe, ob die Summe der Anteile 100% nicht überschreitet
    const totalShares = shares.reduce((sum, share) => sum + share, 0);
    if (totalShares > 100) {
      throw new Error('Summe der Anteile darf 100% nicht überschreiten');
    }
    
    // Prüfe, ob die Royalty-Rate das Maximum nicht überschreitet
    const royaltyRate = totalShares / 100 * this.options.defaultRoyaltyPercentage;
    if (royaltyRate > this.options.maxRoyaltyPercentage) {
      throw new Error(`Royalty-Rate darf ${this.options.maxRoyaltyPercentage}% nicht überschreiten`);
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Royalty-Informationen für Token ${tokenId}:`, {
        recipients,
        shares,
        royaltyRate: `${royaltyRate}%`
      });
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        recipients,
        shares,
        royaltyRate
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Royalty-Informationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Royalty-Informationen
   */
  async getRoyaltyInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Royalty-Informationen für Token ${tokenId} ab`);
      
      // Simulierte Royalty-Informationen
      return {
        tokenId,
        recipients: ['0x1234...', '0x5678...'],
        shares: [60, 40], // 60% und 40%
        royaltyRate: this.options.defaultRoyaltyPercentage,
        totalRoyalty: this.options.defaultRoyaltyPercentage
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Royalty-Informationen:', error);
      throw error;
    }
  }
  
  /**
   * Berechnet die Royalty-Zahlung für einen Verkauf
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Royalty-Zahlungsinformationen
   */
  async calculateRoyaltyPayment(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Royalty-Informationen ab
      const royaltyInfo = await this.getRoyaltyInfo(tokenId);
      
      // Berechne Royalty-Betrag
      const royaltyAmount = salePrice * (royaltyInfo.royaltyRate / 100);
      
      // Berechne Zahlungen pro Empfänger
      const payments = royaltyInfo.recipients.map((recipient, index) => ({
        recipient,
        share: royaltyInfo.shares[index],
        amount: royaltyAmount * (royaltyInfo.shares[index] / 100)
      }));
      
      return {
        tokenId,
        salePrice,
        royaltyRate: royaltyInfo.royaltyRate,
        totalRoyalty: royaltyAmount,
        payments
      };
    } catch (error) {
      console.error('Fehler bei der Berechnung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Führt eine Royalty-Zahlung für einen Verkauf durch
   * @param {string} tokenId - Token-ID des NFTs
   * @param {number} salePrice - Verkaufspreis
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async payRoyalties(tokenId, salePrice) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Berechne Royalty-Zahlungen
      const royaltyPayment = await this.calculateRoyaltyPayment(tokenId, salePrice);
      
      // In einer realen Implementierung würden wir hier die Zahlungen durchführen
      console.log(`Führe Royalty-Zahlungen für Token ${tokenId} durch:`, royaltyPayment);
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        salePrice,
        royaltyRate: royaltyPayment.royaltyRate,
        totalRoyalty: royaltyPayment.totalRoyalty,
        payments: royaltyPayment.payments
      };
    } catch (error) {
      console.error('Fehler bei der Durchführung der Royalty-Zahlung:', error);
      throw error;
    }
  }
  
  /**
   * Setzt Lizenzinformationen für ein DOI-NFT
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} licenseInfo - Lizenzinformationen
   * @returns {Promise<Object>} - Transaktionsinformationen
   */
  async setLicenseInfo(tokenId, licenseInfo) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere Lizenzinformationen
    if (!licenseInfo.type) {
      throw new Error('Lizenztyp muss angegeben werden');
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract aufrufen
      console.log(`Setze Lizenzinformationen für Token ${tokenId}:`, licenseInfo);
      
      // Standardisiere Lizenzinformationen
      const standardizedLicense = {
        type: licenseInfo.type,
        name: licenseInfo.name || this.getLicenseName(licenseInfo.type),
        url: licenseInfo.url || this.getLicenseUrl(licenseInfo.type),
        commercial: licenseInfo.commercial !== undefined ? licenseInfo.commercial : true,
        derivatives: licenseInfo.derivatives !== undefined ? licenseInfo.derivatives : true,
        attribution: licenseInfo.attribution !== undefined ? licenseInfo.attribution : true
      };
      
      // Simuliere Transaktion
      const txHash = '0x' + Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      
      return {
        transaction: txHash,
        tokenId,
        license: standardizedLicense
      };
    } catch (error) {
      console.error('Fehler beim Setzen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Ruft Lizenzinformationen für ein DOI-NFT ab
   * @param {string} tokenId - Token-ID des NFTs
   * @returns {Promise<Object>} - Lizenzinformationen
   */
  async getLicenseInfo(tokenId) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // In einer realen Implementierung würden wir hier den Contract abfragen
      console.log(`Rufe Lizenzinformationen für Token ${tokenId} ab`);
      
      // Simulierte Lizenzinformationen
      return {
        tokenId,
        license: {
          type: 'cc-by',
          name: 'Creative Commons Attribution 4.0',
          url: 'https://creativecommons.org/licenses/by/4.0/',
          commercial: true,
          derivatives: true,
          attribution: true
        }
      };
    } catch (error) {
      console.error('Fehler beim Abrufen der Lizenzinformationen:', error);
      throw error;
    }
  }
  
  /**
   * Prüft, ob eine Nutzung mit der Lizenz kompatibel ist
   * @param {string} tokenId - Token-ID des NFTs
   * @param {Object} usage - Nutzungsinformationen
   * @returns {Promise<Object>} - Kompatibilitätsinformationen
   */
  async checkLicenseCompatibility(tokenId, usage) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Rufe Lizenzinformationen ab
      const licenseInfo = await this.getLicenseInfo(tokenId);
      
      // Prüfe Kompatibilität
      const isCommercialCompatible = !usage.commercial || licenseInfo.license.commercial;
      const isDerivativesCompatible = !usage.derivatives || licenseInfo.license.derivatives;
      const isAttributionCompatible = !licenseInfo.license.attribution || usage.attribution;
      
      const isCompatible = isCommercialCompatible && isDerivativesCompatible && isAttributionCompatible;
      
      return {
        tokenId,
        license: licenseInfo.license,
        usage,
        isCompatible,
        incompatibilities: {
          commercial: !isCommercialCompatible,
          derivatives: !isDerivativesCompatible,
          attribution: !isAttributionCompatible
        }
      };
    } catch (error) {
      console.error('Fehler bei der Prüfung der Lizenzkompatibilität:', error);
      throw error;
    }
  }
  
  /**
   * Gibt den Namen einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseName(type) {
    const licenseNames = {
      'cc-by': 'Creative Commons Attribution 4.0',
      'cc-by-sa': 'Creative Commons Attribution-ShareAlike 4.0',
      'cc-by-nc': 'Creative Commons Attribution-NonCommercial 4.0',
      'cc-by-nc-sa': 'Creative Commons Attribution-NonCommercial-ShareAlike 4.0',
      'cc-by-nd': 'Creative Commons Attribution-NoDerivatives 4.0',
      'cc-by-nc-nd': 'Creative Commons Attribution-NonCommercial-NoDerivatives 4.0',
      'cc0': 'Creative Commons Zero v1.0 Universal',
      'mit': 'MIT License',
      'apache-2.0': 'Apache License 2.0',
      'gpl-3.0': 'GNU General Public License v3.0'
    };
    
    return licenseNames[type] || 'Custom License';
  }
  
  /**
   * Gibt die URL einer Lizenz basierend auf dem Typ zurück
   * @private
   */
  getLicenseUrl(type) {
    const licenseUrls = {
      'cc-by': 'https://creativecommons.org/licenses/by/4.0/',
      'cc-by-sa': 'https://creativecommons.org/licenses/by-sa/4.0/',
      'cc-by-nc': 'https://creativecommons.org/licenses/by-nc/4.0/',
      'cc-by-nc-sa': 'https://creativecommons.org/licenses/by-nc-sa/4.0/',
      'cc-by-nd': 'https://creativecommons.org/licenses/by-nd/4.0/',
      'cc-by-nc-nd': 'https://creativecommons.org/licenses/by-nc-nd/4.0/',
      'cc0': 'https://creativecommons.org/publicdomain/zero/1.0/',
      'mit': 'https://opensource.org/licenses/MIT',
      'apache-2.0': 'https://opensource.org/licenses/Apache-2.0',
      'gpl-3.0': 'https://www.gnu.org/licenses/gpl-3.0.en.html'
    };
    
    return licenseUrls[type] || '';
  }
}

export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;export default DoiRoyaltyContract;