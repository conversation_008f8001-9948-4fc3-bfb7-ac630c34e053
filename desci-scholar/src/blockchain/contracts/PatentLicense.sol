// SPDX-License-Identifier: MIT
pragma solidity ^0.8.17;

/**
 * @title PatentLicense
 * @dev Smart contract for managing patent licensing agreements
 */
contract PatentLicense {
    // Struct to represent a patent
    struct Patent {
        string patentId;        // Patent identifier (e.g., **********)
        string title;           // Patent title
        address owner;          // Owner of the patent
        string metadataURI;     // URI to patent metadata on IPFS
        bool active;            // Whether the patent is active
        uint256 createdAt;      // Timestamp when the patent was created
    }
    
    // Struct to represent a license
    struct License {
        uint256 licenseId;      // Unique license identifier
        string patentId;        // Associated patent identifier
        address licensee;       // Address of the entity licensing the patent
        uint256 startDate;      // Start date of the license
        uint256 endDate;        // End date of the license (0 for perpetual)
        uint256 royaltyRate;    // Royalty rate in basis points (e.g., 500 = 5%)
        uint256 upfrontFee;     // Upfront fee in wei
        LicenseStatus status;   // Current status of the license
        string termsURI;        // URI to license terms on IPFS
        uint256 createdAt;      // Timestamp when the license was created
    }
    
    // Enum for license status
    enum LicenseStatus { Pending, Active, Expired, Terminated }
    
    // Mapping from patent ID to Patent struct
    mapping(string => Patent) public patents;
    
    // Mapping from license ID to License struct
    mapping(uint256 => License) public licenses;
    
    // Pending payments that can be withdrawn (address => amount)
    mapping(address => uint256) public pendingPayments;
    
    // Array of all patent IDs
    string[] public patentIds;
    
    // Counter for license IDs
    uint256 private nextLicenseId = 1;
    
    // ReentrancyGuard state
    bool private _locked;
    
    // Events
    event PatentRegistered(string patentId, address owner, string title);
    event PatentUpdated(string patentId, string title, string metadataURI);
    event PatentTransferred(string patentId, address previousOwner, address newOwner);
    event LicenseCreated(uint256 licenseId, string patentId, address licensee);
    event LicenseUpdated(uint256 licenseId, LicenseStatus status);
    event RoyaltyPaid(uint256 licenseId, address payer, address payee, uint256 amount);
    event PaymentWithdrawn(address recipient, uint256 amount);
    
    // Modifiers
    modifier onlyPatentOwner(string memory patentId) {
        require(patents[patentId].owner == msg.sender, "Caller is not the patent owner");
        _;
    }
    
    modifier patentExists(string memory patentId) {
        require(patents[patentId].active, "Patent does not exist or is not active");
        _;
    }
    
    modifier licenseExists(uint256 licenseId) {
        require(licenses[licenseId].createdAt > 0, "License does not exist");
        _;
    }
    
    // Reentrancy guard
    modifier nonReentrant() {
        require(!_locked, "ReentrancyGuard: reentrant call");
        _locked = true;
        _;
        _locked = false;
    }
    
    /**
     * @dev Register a new patent
     * @param patentId Unique identifier for the patent
     * @param title Title of the patent
     * @param metadataURI URI pointing to the patent metadata on IPFS
     */
    function registerPatent(
        string memory patentId,
        string memory title,
        string memory metadataURI
    ) public {
        require(bytes(patentId).length > 0, "Patent ID cannot be empty");
        require(!patents[patentId].active, "Patent ID already exists");
        
        patents[patentId] = Patent({
            patentId: patentId,
            title: title,
            owner: msg.sender,
            metadataURI: metadataURI,
            active: true,
            createdAt: block.timestamp
        });
        
        patentIds.push(patentId);
        
        emit PatentRegistered(patentId, msg.sender, title);
    }
    
    /**
     * @dev Update patent metadata
     * @param patentId Patent identifier
     * @param title Updated title
     * @param metadataURI Updated metadata URI
     */
    function updatePatent(
        string memory patentId,
        string memory title,
        string memory metadataURI
    ) public onlyPatentOwner(patentId) patentExists(patentId) {
        Patent storage patent = patents[patentId];
        
        patent.title = title;
        patent.metadataURI = metadataURI;
        
        emit PatentUpdated(patentId, title, metadataURI);
    }
    
    /**
     * @dev Transfer patent ownership
     * @param patentId Patent identifier
     * @param newOwner Address of the new owner
     */
    function transferPatent(
        string memory patentId,
        address newOwner
    ) public onlyPatentOwner(patentId) patentExists(patentId) {
        require(newOwner != address(0), "New owner cannot be the zero address");
        require(newOwner != msg.sender, "New owner cannot be the current owner");
        
        address previousOwner = patents[patentId].owner;
        patents[patentId].owner = newOwner;
        
        emit PatentTransferred(patentId, previousOwner, newOwner);
    }
    
    /**
     * @dev Create a new license agreement
     * @param patentId Patent identifier
     * @param licensee Address of the licensee
     * @param startDate Start date of the license
     * @param endDate End date of the license (0 for perpetual)
     * @param royaltyRate Royalty rate in basis points
     * @param termsURI URI pointing to the license terms on IPFS
     * @return licenseId Unique identifier for the new license
     */
    function createLicense(
        string memory patentId,
        address licensee,
        uint256 startDate,
        uint256 endDate,
        uint256 royaltyRate,
        string memory termsURI
    ) public payable onlyPatentOwner(patentId) patentExists(patentId) returns (uint256) {
        require(licensee != address(0), "Licensee cannot be the zero address");
        require(startDate >= block.timestamp, "Start date must be in the future");
        require(endDate == 0 || endDate > startDate, "End date must be after start date");
        require(royaltyRate <= 10000, "Royalty rate cannot exceed 100%");
        
        uint256 licenseId = nextLicenseId++;
        
        licenses[licenseId] = License({
            licenseId: licenseId,
            patentId: patentId,
            licensee: licensee,
            startDate: startDate,
            endDate: endDate,
            royaltyRate: royaltyRate,
            upfrontFee: msg.value,
            status: LicenseStatus.Pending,
            termsURI: termsURI,
            createdAt: block.timestamp
        });
        
        emit LicenseCreated(licenseId, patentId, licensee);
        
        return licenseId;
    }
    
    /**
     * @dev Activate a pending license
     * @param licenseId License identifier
     */
    function activateLicense(
        uint256 licenseId
    ) public licenseExists(licenseId) {
        License storage license = licenses[licenseId];
        
        require(license.status == LicenseStatus.Pending, "License is not in pending status");
        require(license.licensee == msg.sender, "Only licensee can activate the license");
        require(license.startDate <= block.timestamp, "License start date is in the future");
        require(license.endDate == 0 || license.endDate > block.timestamp, "License end date has passed");
        
        license.status = LicenseStatus.Active;
        
        emit LicenseUpdated(licenseId, LicenseStatus.Active);
        
        // If there's an upfront fee, add it to the patent owner's pending payments
        if (license.upfrontFee > 0) {
            address patentOwner = patents[license.patentId].owner;
            pendingPayments[patentOwner] += license.upfrontFee;
        }
    }
    
    /**
     * @dev Pay royalties for a license
     * @param licenseId License identifier
     */
    function payRoyalty(
        uint256 licenseId
    ) public payable licenseExists(licenseId) {
        License storage license = licenses[licenseId];
        
        require(license.status == LicenseStatus.Active, "License is not active");
        require(msg.value > 0, "Royalty amount must be greater than zero");
        
        address patentOwner = patents[license.patentId].owner;
        
        emit RoyaltyPaid(licenseId, msg.sender, patentOwner, msg.value);
        
        // Add royalty to the patent owner's pending payments
        pendingPayments[patentOwner] += msg.value;
    }
    
    /**
     * @dev Withdraw pending payments
     */
    function withdrawPayments() public nonReentrant {
        uint256 amount = pendingPayments[msg.sender];
        require(amount > 0, "No pending payments");
        
        // Reset pending payments before transfer to prevent reentrancy
        pendingPayments[msg.sender] = 0;
        
        // Use call to transfer ether safely
        (bool success, ) = msg.sender.call{value: amount}("");
        require(success, "Transfer failed");
        
        emit PaymentWithdrawn(msg.sender, amount);
    }
    
    /**
     * @dev Terminate a license
     * @param licenseId License identifier
     */
    function terminateLicense(
        uint256 licenseId
    ) public licenseExists(licenseId) {
        License storage license = licenses[licenseId];
        string memory patentId = license.patentId;
        
        require(
            msg.sender == license.licensee || msg.sender == patents[patentId].owner,
            "Only licensee or patent owner can terminate the license"
        );
        
        require(license.status == LicenseStatus.Active, "License is not active");
        
        license.status = LicenseStatus.Terminated;
        
        emit LicenseUpdated(licenseId, LicenseStatus.Terminated);
    }
    
    /**
     * @dev Update license status if expired
     * @param licenseId License identifier
     */
    function checkLicenseExpiration(
        uint256 licenseId
    ) public licenseExists(licenseId) {
        License storage license = licenses[licenseId];
        
        if (license.status == LicenseStatus.Active && 
            license.endDate > 0 && 
            license.endDate <= block.timestamp) {
            license.status = LicenseStatus.Expired;
            emit LicenseUpdated(licenseId, LicenseStatus.Expired);
        }
    }
    
    /**
     * @dev Get patent details
     * @param patentId Patent identifier
     * @return Patent details
     */
    function getPatent(
        string memory patentId
    ) public view returns (
        string memory,
        string memory,
        address,
        string memory,
        bool,
        uint256
    ) {
        Patent memory patent = patents[patentId];
        return (
            patent.patentId,
            patent.title,
            patent.owner,
            patent.metadataURI,
            patent.active,
            patent.createdAt
        );
    }
    
    /**
     * @dev Get license details
     * @param licenseId License identifier
     * @return License details
     */
    function getLicense(
        uint256 licenseId
    ) public view returns (
        uint256,
        string memory,
        address,
        uint256,
        uint256,
        uint256,
        uint256,
        LicenseStatus,
        string memory,
        uint256
    ) {
        License memory license = licenses[licenseId];
        return (
            license.licenseId,
            license.patentId,
            license.licensee,
            license.startDate,
            license.endDate,
            license.royaltyRate,
            license.upfrontFee,
            license.status,
            license.termsURI,
            license.createdAt
        );
    }
    
    /**
     * @dev Get the number of registered patents
     * @return Number of patents
     */
    function getPatentCount() public view returns (uint256) {
        return patentIds.length;
    }
    
    /**
     * @dev Get pending payments amount for an address
     * @param owner Address to check
     * @return Amount of pending payments
     */
    function getPendingPayment(address owner) public view returns (uint256) {
        return pendingPayments[owner];
    }
}
