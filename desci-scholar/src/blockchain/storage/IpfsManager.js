import { create } from 'ipfs-http-client.js';
import { <PERSON><PERSON><PERSON> } from 'buffer';
import crypto from 'crypto';

class IpfsManager {
  constructor(options = {}) {
    // IPFS-Client initialisieren
    this.ipfs = create({
      host: options.host || 'ipfs.infura.io',
      port: options.port || 5001,
      protocol: options.protocol || 'https',
      headers: {
        authorization: options.auth
      }
    });
    
    // Cache für Metadaten
    this.metadataCache = new Map();
  }

  /**
   * Speichert Metadaten auf IPFS
   * @param {Object} metadata - Metadaten der Publikation
   * @returns {Promise<string>} - IPFS CID
   */
  async storeMetadata(metadata) {
    try {
      // Erstelle einen Hash der Metadaten für die Validierung
      const metadataHash = this.createMetadataHash(metadata);
      
      // Füge Validierungsinformationen hinzu
      const enrichedMetadata = {
        ...metadata,
        validation: {
          hash: metadataHash,
          timestamp: Date.now(),
          version: '1.0'
        }
      };

      // Konvertier<PERSON> zu <PERSON>
      const metadataBuffer = Buffer.from(JSON.stringify(enrichedMetadata));
      
      // Speichere auf IPFS
      const result = await this.ipfs.add(metadataBuffer);
      
      // Speichere im Cache
      this.metadataCache.set(result.cid.toString(), {
        metadata: enrichedMetadata,
        timestamp: Date.now()
      });

      return result.cid.toString();
    } catch (error) {
      throw new Error(`IPFS-Speicherung fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Ruft Metadaten von IPFS ab
   * @param {string} cid - Content Identifier
   * @returns {Promise<Object>} - Metadaten
   */
  async getMetadata(cid) {
    try {
      // Prüfe Cache
      if (this.metadataCache.has(cid)) {
        const cached = this.metadataCache.get(cid);
        // Cache für 1 Stunde gültig
        if (Date.now() - cached.timestamp < 3600000) {
          return cached.metadata;
        }
      }

      // Hole von IPFS
      const stream = await this.ipfs.cat(cid);
      let data = '';
      
      for await (const chunk of stream) {
        data += chunk.toString();
      }
      
      const metadata = JSON.parse(data);
      
      // Validiere Hash
      if (!this.validateMetadataHash(metadata)) {
        throw new Error('Metadaten-Validierung fehlgeschlagen');
      }
      
      // Aktualisiere Cache
      this.metadataCache.set(cid, {
        metadata,
        timestamp: Date.now()
      });
      
      return metadata;
    } catch (error) {
      throw new Error(`IPFS-Abruf fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Aktualisiert Metadaten auf IPFS
   * @param {string} oldCid - Alter Content Identifier
   * @param {Object} newMetadata - Neue Metadaten
   * @returns {Promise<string>} - Neuer CID
   */
  async updateMetadata(oldCid, newMetadata) {
    try {
      // Hole alte Metadaten
      const oldMetadata = await this.getMetadata(oldCid);
      
      // Erstelle Update-Historie
      const updatedMetadata = {
        ...newMetadata,
        history: [
          ...(oldMetadata.history || []),
          {
            cid: oldCid,
            timestamp: oldMetadata.validation.timestamp
          }
        ]
      };
      
      // Speichere neue Version
      return await this.storeMetadata(updatedMetadata);
    } catch (error) {
      throw new Error(`Metadaten-Update fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Pinnt Metadaten auf IPFS
   * @param {string} cid - Content Identifier
   */
  async pinMetadata(cid) {
    try {
      await this.ipfs.pin.add(cid);
    } catch (error) {
      throw new Error(`Pinning fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Erstellt einen Hash der Metadaten
   */
  createMetadataHash(metadata) {
    return crypto
      .createHash('sha256')
      .update(JSON.stringify(metadata))
      .digest('hex');
  }

  /**
   * Validiert den Metadaten-Hash
   */
  validateMetadataHash(metadata) {
    const { validation, ...rest } = metadata;
    const calculatedHash = this.createMetadataHash(rest);
    return calculatedHash === validation.hash;
  }

  /**
   * Erstellt einen IPFS-Link
   */
  createIpfsLink(cid) {
    return `ipfs://${cid}`;
  }

  /**
   * Konvertiert einen IPFS-Link in eine HTTP-URL
   */
  getHttpUrl(cid) {
    return `https://ipfs.io/ipfs/${cid}`;
  }
}

export default IpfsManager;