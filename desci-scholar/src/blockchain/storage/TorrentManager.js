import WebTorrent from 'webtorrent';
import fs from 'fs';
import path from 'path';
import crypto from 'crypto';

class TorrentManager {
  constructor(options = {}) {
    this.client = new WebTorrent();
    this.storagePath = options.storagePath || './torrent-storage';
    this.trackers = [
      'wss://tracker.openwebtorrent.com',
      'wss://tracker.btorrent.xyz',
      'wss://tracker.fastcast.nz'
    ];
    
    // <PERSON><PERSON>n <PERSON> sic<PERSON>, dass der Speicherpfad existiert
    if (!fs.existsSync(this.storagePath)) {
      fs.mkdirSync(this.storagePath, { recursive: true });
    }
  }

  /**
   * Erstellt einen Torrent aus einer wissenschaftlichen Publikation
   * @param {Object} publication - Publikationsdaten und Metadaten
   * @returns {Promise<string>} - Magnet URI des erstellten Torrents
   */
  async createPublicationTorrent(publication) {
    try {
      const { file, metadata } = publication;
      
      // <PERSON><PERSON>elle eine eindeutige ID für den Torrent
      const torrentId = crypto
        .createHash('sha256')
        .update(metadata.doi + metadata.publication.title)
        .digest('hex');
      
      // E<PERSON>elle den Torrent mit Metadaten
      return new Promise((resolve, reject) => {
        const opts = {
          name: `${torrentId}-${metadata.publication.title}`,
          comment: `DOI: ${metadata.doi}`,
          createdBy: 'DeSci-Scholar',
          announceList: this.trackers,
          private: false,
          info: {
            source: 'DeSci-Scholar',
            ...metadata
          }
        };

        this.client.seed(file, opts, (torrent) => {
          // Speichere Torrent-Informationen
          const torrentInfo = {
            magnetURI: torrent.magnetURI,
            infoHash: torrent.infoHash,
            doi: metadata.doi,
            created: Date.now()
          };

          // Speichere Torrent-Info in einer lokalen Datei
          fs.writeFileSync(
            path.join(this.storagePath, `${torrentId}.json`),
            JSON.stringify(torrentInfo, null, 2)
          );

          resolve(torrent.magnetURI);
        });
      });
    } catch (error) {
      throw new Error(`Torrent-Erstellung fehlgeschlagen: ${error.message}`);
    }
  }

  /**
   * Lädt eine Publikation über BitTorrent herunter
   * @param {string} magnetURI - Magnet URI des Torrents
   * @returns {Promise<Object>} - Heruntergeladene Datei und Metadaten
   */
  async downloadPublication(magnetURI) {
    return new Promise((resolve, reject) => {
      this.client.add(magnetURI, { path: this.storagePath }, (torrent) => {
        torrent.on('done', () => {
          const files = torrent.files.map(file => ({
            name: file.name,
            path: file.path,
            size: file.length
          }));

          resolve({
            files,
            metadata: torrent.info,
            magnetURI: torrent.magnetURI,
            infoHash: torrent.infoHash
          });
        });

        torrent.on('error', (err) => {
          reject(new Error(`Download fehlgeschlagen: ${err.message}`));
        });

        // Fortschrittsüberwachung
        torrent.on('download', (bytes) => {
          console.log('Download-Fortschritt:', {
            progress: (torrent.progress * 100).toFixed(2) + '%',
            downloadSpeed: torrent.downloadSpeed,
            peers: torrent.numPeers
          });
        });
      });
    });
  }

  /**
   * Überprüft den Seeding-Status einer Publikation
   * @param {string} infoHash - Info-Hash des Torrents
   * @returns {Object} - Seeding-Statistiken
   */
  getTorrentStats(infoHash) {
    const torrent = this.client.get(infoHash);
    if (!torrent) {
      throw new Error('Torrent nicht gefunden');
    }

    return {
      infoHash: torrent.infoHash,
      progress: torrent.progress,
      downloadSpeed: torrent.downloadSpeed,
      uploadSpeed: torrent.uploadSpeed,
      numPeers: torrent.numPeers,
      downloaded: torrent.downloaded,
      uploaded: torrent.uploaded,
      ratio: torrent.ratio,
      timeRemaining: torrent.timeRemaining
    };
  }

  /**
   * Beendet alle aktiven Torrents und schließt den Client
   */
  async destroy() {
    return new Promise((resolve, reject) => {
      this.client.destroy(err => {
        if (err) reject(err);
        else resolve();
      });
    });
  }

  /**
   * Überprüft die Verfügbarkeit eines Torrents im Netzwerk
   * @param {string} magnetURI - Magnet URI des Torrents
   * @returns {Promise<boolean>} - Verfügbarkeit des Torrents
   */
  async checkAvailability(magnetURI) {
    return new Promise((resolve) => {
      const torrent = this.client.add(magnetURI, { maxWebConns: 2 });
      
      // Warte maximal 30 Sekunden auf Peers
      const timeout = setTimeout(() => {
        torrent.destroy();
        resolve(false);
      }, 30000);

      torrent.on('peer', () => {
        clearTimeout(timeout);
        torrent.destroy();
        resolve(true);
      });
    });
  }
}

export default TorrentManager;