import { ApiPromise, WsProvider } from '@polkadot/api';
import { Keyring } from '@polkadot/keyring';
import { cryptoWaitReady } from '@polkadot/util-crypto';

class PolkadotIntegration {
  constructor() {
    this.api = null;
    this.keyring = null;
  }

  async initialize(wsEndpoint) {
    try {
      // Verbindung zur Polkadot-Chain herstellen
      const provider = new WsProvider(wsEndpoint);
      this.api = await ApiPromise.create({ provider });
      
      // Warten auf Crypto-Initialisierung
      await cryptoWaitReady();
      this.keyring = new Keyring({ type: 'sr25519' });
      
      return this.api;
    } catch (error) {
      console.error('Fehler bei der Initialisierung:', error);
      throw error;
    }
  }

  async createDoiNft(doi, metadata, signerAccount) {
    if (!this.api) throw new Error('API nicht initialisiert');

    try {
      // NFT für DOI erstellen
      const tx = this.api.tx.nft.mint({
        doi,
        metadata: JSON.stringify(metadata),
        timestamp: Date.now()
      });

      // Transaktion signieren und senden, wenn ein Konto angegeben wurde
      if (signerAccount) {
        return await this.signAndSend(tx, signerAccount);
      }

      return tx;
    } catch (error) {
      console.error('Fehler beim Erstellen des DOI-NFT:', error);
      throw error;
    }
  }

  /**
   * Signiert und sendet eine Transaktion
   * @param {Object} tx - Die zu sendende Transaktion
   * @param {Object} account - Das Konto, das die Transaktion signiert
   * @returns {Promise<Object>} - Das Ergebnis der Transaktion
   */
  async signAndSend(tx, account) {
    return new Promise((resolve, reject) => {
      tx.signAndSend(account, ({ status, events, dispatchError }) => {
        if (dispatchError) {
          if (dispatchError.isModule) {
            // Für Modul-Fehler, wir dekodieren den Fehler
            const decoded = this.api.registry.findMetaError(dispatchError.asModule);
            const { docs, name, section } = decoded;
            reject(new Error(`${section}.${name}: ${docs.join(' ')}`));
          } else {
            // Andere Fehler wie "Canonical", "Other" oder "BadOrigin"
            reject(new Error(dispatchError.toString()));
          }
        }

        if (status.isFinalized) {
          // Extrahiere relevante Ereignisse
          const eventData = events
            .filter(({ event }) => {
              if (!event) return false;
              return (
                this.api.events.system.ExtrinsicSuccess.is(event) ||
                this.api.events.system.ExtrinsicFailed.is(event)
              );
            })
            .map(({ event }) => event?.data);

          resolve(eventData);
        }
      }).catch(error => {
        reject(error);
      });
    });
  }

  async linkOrcid(doiNft, orcid, signerAccount) {
    if (!this.api) throw new Error('API nicht initialisiert');

    try {
      // ORCID mit NFT verknüpfen
      const tx = this.api.tx.identity.linkOrcid(doiNft, orcid);

      // Transaktion signieren und senden, wenn ein Konto angegeben wurde
      if (signerAccount) {
        return await this.signAndSend(tx, signerAccount);
      }

      return tx;
    } catch (error) {
      console.error('Fehler beim Verknüpfen des ORCID:', error);
      throw error;
    }
  }

  async trackCitation(citingDoi, citedDoi, signerAccount) {
    if (!this.api) throw new Error('API nicht initialisiert');

    try {
      // Zitation in der Blockchain registrieren
      const tx = this.api.tx.citations.record(citingDoi, citedDoi);

      // Transaktion signieren und senden, wenn ein Konto angegeben wurde
      if (signerAccount) {
        return await this.signAndSend(tx, signerAccount);
      }

      return tx;
    } catch (error) {
      console.error('Fehler beim Verfolgen der Zitation:', error);
      throw error;
    }
  }

  async distributeRoyalties(doiNft, amount, signerAccount) {
    if (!this.api) throw new Error('API nicht initialisiert');

    try {
      // Smart Contract für Tantiemen ausführen
      const tx = this.api.tx.smartContracts.executeRoyaltyDistribution(doiNft, amount);

      // Transaktion signieren und senden, wenn ein Konto angegeben wurde
      if (signerAccount) {
        return await this.signAndSend(tx, signerAccount);
      }

      return tx;
    } catch (error) {
      console.error('Fehler bei der Verteilung der Tantiemen:', error);
      throw error;
    }
  }

  async verifyPublication(doiNft) {
    if (!this.api) throw new Error('API nicht initialisiert');

    try {
      // Verifizierung der Publikation auf der Blockchain
      const record = await this.api.query.publications.get(doiNft);
      return record.toJSON();
    } catch (error) {
      console.error('Fehler bei der Verifizierung der Publikation:', error);
      throw error;
    }
  }

  async getPublicationHistory(doiNft) {
    if (!this.api) throw new Error('API nicht initialisiert');

    try {
      // Historie der Publikation abrufen
      const events = await this.api.query.system.events();
      return events
        .filter(({ event }) =>
          event.section === 'publications' &&
          event.data[0].toString() === doiNft
        )
        .map(({ event }) => event.data.toJSON());
    } catch (error) {
      console.error('Fehler beim Abrufen der Publikationshistorie:', error);
      throw error;
    }
  }

  /**
   * Erstellt ein Konto aus einem Seed oder einer Mnemonik
   * @param {string} seed - Der Seed oder die Mnemonik für das Konto
   * @returns {Object} - Das erstellte Konto
   */
  createAccount(seed) {
    if (!this.keyring) throw new Error('Keyring nicht initialisiert');

    try {
      return this.keyring.addFromUri(seed);
    } catch (error) {
      console.error('Fehler beim Erstellen des Kontos:', error);
      throw error;
    }
  }

  /**
   * Trennt die Verbindung zur Polkadot-Chain
   */
  async disconnect() {
    if (this.api) {
      await this.api.disconnect();
      this.api = null;
    }
  }
}

export default PolkadotIntegration;