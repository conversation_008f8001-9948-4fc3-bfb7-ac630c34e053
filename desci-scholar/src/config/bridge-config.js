/**
 * NFT-DOI Bridge Konfiguration
 * 
 * Zentrale Konfiguration für das NFT-DOI Bridge System mit
 * DataCite und Crossref Integration
 */

import dotenv from 'dotenv';
dotenv.config();

/**
 * DataCite Integration Konfiguration
 */
export const dataCiteConfig = {
  // API-Konfiguration
  apiBaseUrl: process.env.DATACITE_API_URL || 'https://api.datacite.org',
  apiKey: process.env.DATACITE_API_KEY,
  username: process.env.DATACITE_USERNAME,
  password: process.env.DATACITE_PASSWORD,
  
  // DOI-Konfiguration
  repositoryId: process.env.DATACITE_REPOSITORY_ID,
  doiPrefix: process.env.DATACITE_DOI_PREFIX || '10.5281',
  
  // API-Limits und Timeouts
  timeout: parseInt(process.env.DATACITE_API_TIMEOUT) || 10000,
  maxRetries: parseInt(process.env.DATACITE_API_MAX_RETRIES) || 3,
  rateLimit: {
    requestsPerMinute: parseInt(process.env.DATACITE_RATE_LIMIT) || 100,
    burstLimit: parseInt(process.env.DATACITE_BURST_LIMIT) || 10
  },
  
  // Caching
  cacheEnabled: process.env.DATACITE_CACHE_ENABLED !== 'false',
  cacheTtl: parseInt(process.env.DATACITE_CACHE_TTL) || 3600, // 1 Stunde
  
  // Erweiterte Features
  enableUsageStatistics: process.env.DATACITE_USAGE_STATS === 'true',
  enableRelatedIdentifiers: process.env.DATACITE_RELATED_IDS !== 'false',
  enableFundingInfo: process.env.DATACITE_FUNDING_INFO !== 'false'
};

/**
 * Crossref Integration Konfiguration
 */
export const crossrefConfig = {
  // API-Konfiguration
  apiBaseUrl: process.env.CROSSREF_API_URL || 'https://api.crossref.org',
  politePoolToken: process.env.CROSSREF_POLITE_POOL_TOKEN,
  userAgent: process.env.CROSSREF_USER_AGENT || 'DeSci-Scholar/2.0 (https://desci-scholar.org; mailto:<EMAIL>)',
  
  // Event Data API
  eventDataApiUrl: process.env.CROSSREF_EVENT_DATA_URL || 'https://api.eventdata.crossref.org',
  eventDataToken: process.env.CROSSREF_EVENT_DATA_TOKEN,
  
  // API-Limits und Timeouts
  timeout: parseInt(process.env.CROSSREF_API_TIMEOUT) || 15000,
  maxRetries: parseInt(process.env.CROSSREF_API_MAX_RETRIES) || 3,
  rateLimit: {
    requestsPerSecond: parseInt(process.env.CROSSREF_RATE_LIMIT) || 50,
    burstLimit: parseInt(process.env.CROSSREF_BURST_LIMIT) || 20
  },
  
  // Caching
  cacheEnabled: process.env.CROSSREF_CACHE_ENABLED !== 'false',
  cacheTtl: parseInt(process.env.CROSSREF_CACHE_TTL) || 1800, // 30 Minuten
  
  // Erweiterte Features
  enableEventData: process.env.CROSSREF_EVENT_DATA !== 'false',
  enableCitationTracking: process.env.CROSSREF_CITATION_TRACKING !== 'false',
  enableImpactMetrics: process.env.CROSSREF_IMPACT_METRICS !== 'false',
  
  // Event Data Kategorien
  eventDataSources: [
    'twitter', 'facebook', 'reddit', 'wikipedia', 'news', 'blog',
    'peer-review', 'datacite', 'crossref', 'orcid'
  ]
};

/**
 * ORCID Integration Konfiguration
 */
export const orcidConfig = {
  // API-Konfiguration
  apiBaseUrl: process.env.ORCID_API_URL || 'https://pub.orcid.org/v3.0',
  clientId: process.env.ORCID_CLIENT_ID,
  clientSecret: process.env.ORCID_CLIENT_SECRET,
  
  // API-Limits
  timeout: parseInt(process.env.ORCID_API_TIMEOUT) || 10000,
  maxRetries: parseInt(process.env.ORCID_API_MAX_RETRIES) || 3,
  rateLimit: {
    requestsPerMinute: parseInt(process.env.ORCID_RATE_LIMIT) || 24,
    burstLimit: parseInt(process.env.ORCID_BURST_LIMIT) || 5
  },
  
  // Caching
  cacheEnabled: process.env.ORCID_CACHE_ENABLED !== 'false',
  cacheTtl: parseInt(process.env.ORCID_CACHE_TTL) || 7200, // 2 Stunden
  
  // Features
  enableAuthorEnrichment: process.env.ORCID_AUTHOR_ENRICHMENT !== 'false',
  enableCollaborationNetwork: process.env.ORCID_COLLABORATION_NETWORK === 'true'
};

/**
 * Blockchain Integration Konfiguration
 */
export const blockchainConfig = {
  // Standard-Blockchain
  defaultBlockchain: process.env.DEFAULT_BLOCKCHAIN || 'polkadot',
  
  // Polkadot-Konfiguration
  polkadot: {
    rpcEndpoint: process.env.POLKADOT_RPC_ENDPOINT || 'wss://rpc.polkadot.io',
    contractAddress: process.env.POLKADOT_CONTRACT_ADDRESS,
    privateKey: process.env.POLKADOT_PRIVATE_KEY,
    gasLimit: parseInt(process.env.POLKADOT_GAS_LIMIT) || 300000000,
    storageDeposit: parseInt(process.env.POLKADOT_STORAGE_DEPOSIT) || 0
  },
  
  // Ethereum L2 (Polygon)
  polygon: {
    rpcEndpoint: process.env.POLYGON_RPC_ENDPOINT || 'https://polygon-rpc.com',
    contractAddress: process.env.POLYGON_CONTRACT_ADDRESS,
    privateKey: process.env.POLYGON_PRIVATE_KEY,
    gasLimit: parseInt(process.env.POLYGON_GAS_LIMIT) || 500000,
    gasPrice: process.env.POLYGON_GAS_PRICE || 'auto'
  },
  
  // Solana
  solana: {
    rpcEndpoint: process.env.SOLANA_RPC_ENDPOINT || 'https://api.mainnet-beta.solana.com',
    programId: process.env.SOLANA_PROGRAM_ID,
    privateKey: process.env.SOLANA_PRIVATE_KEY,
    commitment: process.env.SOLANA_COMMITMENT || 'confirmed'
  }
};

/**
 * NFT-Metadaten Konfiguration
 */
export const nftMetadataConfig = {
  // Standard-Attribute
  defaultAttributes: [
    'Bridge_Type',
    'DOI',
    'Blockchain',
    'DataCite_Integration',
    'Crossref_Integration',
    'ORCID_Integration',
    'Citation_Tracking',
    'Metadata_Embedding',
    'Minted_Date',
    'Bridge_Version'
  ],
  
  // Metadaten-Schema
  schema: {
    version: '2.0',
    standard: 'ERC-721',
    extensions: ['ERC-2981'] // Royalties
  },
  
  // IPFS-Konfiguration für Metadaten-Speicherung
  ipfs: {
    gateway: process.env.IPFS_GATEWAY || 'https://ipfs.io',
    apiUrl: process.env.IPFS_API_URL || 'http://localhost:5001',
    pinningService: process.env.IPFS_PINNING_SERVICE || 'local',
    timeout: parseInt(process.env.IPFS_TIMEOUT) || 30000
  }
};

/**
 * Bridge-Service Konfiguration
 */
export const bridgeServiceConfig = {
  // Standard-Optionen
  defaultOptions: {
    includeDataCite: true,
    includeCrossref: true,
    includeOrcid: true,
    enableZeroKnowledgeProofs: false,
    blockchain: 'polkadot'
  },
  
  // Synchronisation
  syncInterval: parseInt(process.env.BRIDGE_SYNC_INTERVAL) || 86400000, // 24 Stunden
  enableAutoSync: process.env.BRIDGE_AUTO_SYNC !== 'false',
  
  // Monitoring
  enableMetrics: process.env.BRIDGE_METRICS !== 'false',
  metricsEndpoint: process.env.BRIDGE_METRICS_ENDPOINT || '/metrics',
  
  // Webhooks
  enableWebhooks: process.env.BRIDGE_WEBHOOKS === 'true',
  webhookSecret: process.env.BRIDGE_WEBHOOK_SECRET,
  webhookEvents: [
    'bridge.created',
    'bridge.updated',
    'citation.added',
    'impact.threshold'
  ]
};

/**
 * Logging und Monitoring Konfiguration
 */
export const loggingConfig = {
  level: process.env.LOG_LEVEL || 'info',
  format: process.env.LOG_FORMAT || 'json',
  
  // Bridge-spezifische Logs
  bridgeLogs: {
    enabled: process.env.BRIDGE_LOGS !== 'false',
    level: process.env.BRIDGE_LOG_LEVEL || 'info',
    includeMetadata: process.env.BRIDGE_LOG_METADATA !== 'false'
  },
  
  // Performance-Monitoring
  performance: {
    enabled: process.env.PERFORMANCE_MONITORING === 'true',
    sampleRate: parseFloat(process.env.PERFORMANCE_SAMPLE_RATE) || 0.1
  }
};

/**
 * Sicherheitskonfiguration
 */
export const securityConfig = {
  // API-Sicherheit
  apiKey: {
    required: process.env.API_KEY_REQUIRED !== 'false',
    header: process.env.API_KEY_HEADER || 'Authorization',
    prefix: process.env.API_KEY_PREFIX || 'Bearer '
  },
  
  // Rate Limiting
  rateLimit: {
    enabled: process.env.RATE_LIMIT_ENABLED !== 'false',
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW) || 60000, // 1 Minute
    maxRequests: parseInt(process.env.RATE_LIMIT_MAX) || 100,
    skipSuccessfulRequests: process.env.RATE_LIMIT_SKIP_SUCCESS === 'true'
  },
  
  // CORS
  cors: {
    origin: process.env.CORS_ORIGIN || '*',
    credentials: process.env.CORS_CREDENTIALS === 'true',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-API-Key']
  }
};

/**
 * Entwicklungs- und Test-Konfiguration
 */
export const developmentConfig = {
  // Mock-Services
  enableMockServices: process.env.ENABLE_MOCK_SERVICES === 'true',
  mockDataCite: process.env.MOCK_DATACITE === 'true',
  mockCrossref: process.env.MOCK_CROSSREF === 'true',
  mockBlockchain: process.env.MOCK_BLOCKCHAIN === 'true',
  
  // Debug-Features
  enableDebugLogs: process.env.DEBUG_LOGS === 'true',
  enableApiDocs: process.env.API_DOCS !== 'false',
  enableSwagger: process.env.SWAGGER_ENABLED === 'true'
};

/**
 * Vollständige Bridge-Konfiguration
 */
export const bridgeConfig = {
  dataCite: dataCiteConfig,
  crossref: crossrefConfig,
  orcid: orcidConfig,
  blockchain: blockchainConfig,
  nftMetadata: nftMetadataConfig,
  bridgeService: bridgeServiceConfig,
  logging: loggingConfig,
  security: securityConfig,
  development: developmentConfig
};

export default bridgeConfig;
