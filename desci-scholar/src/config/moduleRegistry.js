/**
 * Modul-Registry für DeSci-Scholar
 * Zentraler Konfigurationspunkt für alle Module und deren Abhängigkeiten
 */
export class ModuleRegistry {
  constructor() {
    this.modules = new Map();
    this.instances = new Map();
    this.adapters = new Map(); // Für Adapter-Verwaltung
    this.defaultAdapters = new Map(); // Standard-Adapter pro Kategorie
  }
  
  /**
   * Registriert ein Modul
   * @param {string} name - Modulname
   * @param {object} moduleConfig - Modulkonfiguration
   */
  registerModule(name, moduleConfig) {
    if (this.modules.has(name)) {
      throw new Error(`Modul "${name}" ist bereits registriert`);
    }
    
    this.modules.set(name, moduleConfig);
  }
  
  /**
   * Gibt ein Modul zurück
   * @param {string} name - Modulname
   * @returns {object} Modulkonfiguration
   */
  getModule(name) {
    if (!this.modules.has(name)) {
      throw new Error(`Modul "${name}" ist nicht registriert`);
    }
    
    return this.modules.get(name);
  }
  
  /**
   * Initialisiert ein Modul und seine Abhängigkeiten
   * @param {string} name - Modulname
   * @returns {object} Modulinstanz
   */
  initModule(name) {
    // Wenn das Modul bereits initialisiert wurde, gib die Instanz zurück
    if (this.instances.has(name)) {
      return this.instances.get(name);
    }
    
    // Hole die Modulkonfiguration
    const moduleConfig = this.getModule(name);
    
    // Initialisiere Abhängigkeiten
    const dependencies = {};
    if (moduleConfig.dependencies) {
      for (const [depName, depKey] of Object.entries(moduleConfig.dependencies)) {
        dependencies[depKey] = this.initModule(depName);
      }
    }
    
    // Erstelle die Modulinstanz
    const ModuleClass = moduleConfig.class;
    const instance = new ModuleClass({
      ...moduleConfig.options,
      ...dependencies
    });
    
    // Speichere die Instanz
    this.instances.set(name, instance);
    
    // Initialisiere das Modul, falls erforderlich
    if (typeof instance.init === 'function') {
      instance.init();
    }
    
    return instance;
  }
  
  /**
   * Initialisiert alle Module
   * @returns {Map<string, object>} Alle Modulinstanzen
   */
  initAllModules() {
    for (const name of this.modules.keys()) {
      this.initModule(name);
    }
    
    return this.instances;
  }
  
  /**
   * Gibt eine Modulinstanz zurück
   * @param {string} name - Modulname
   * @returns {object} Modulinstanz
   */
  getInstance(name) {
    if (!this.instances.has(name)) {
      return this.initModule(name);
    }

    return this.instances.get(name);
  }

  /**
   * Registriert einen Adapter
   * @param {string} category - Adapter-Kategorie (z.B. 'blockchain', 'storage')
   * @param {string} name - Adapter-Name
   * @param {object} adapter - Adapter-Instanz
   */
  registerAdapter(category, name, adapter) {
    if (!this.adapters.has(category)) {
      this.adapters.set(category, new Map());
    }

    this.adapters.get(category).set(name, adapter);
  }

  /**
   * Setzt den Standard-Adapter für eine Kategorie
   * @param {string} category - Adapter-Kategorie
   * @param {string} name - Adapter-Name
   */
  setDefaultAdapter(category, name) {
    this.defaultAdapters.set(category, name);
  }

  /**
   * Gibt einen Adapter zurück
   * @param {string} category - Adapter-Kategorie
   * @param {string} name - Adapter-Name (optional, verwendet Standard-Adapter)
   * @returns {object} Adapter-Instanz
   */
  getAdapter(category, name = null) {
    if (!name) {
      name = this.defaultAdapters.get(category);
    }

    if (!this.adapters.has(category) || !this.adapters.get(category).has(name)) {
      throw new Error(`Adapter "${category}:${name}" ist nicht registriert`);
    }

    return this.adapters.get(category).get(name);
  }
}

export default ModuleRegistry;