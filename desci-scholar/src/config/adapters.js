/**
 * @fileoverview Konfiguration für das Adapter-System
 *
 * Diese Datei enthält die Konfiguration für das Adapter-System,
 * einschließlich Pfaden, Standard-Adaptern und externen Modulen.
 */

import path from 'path';

/**
 * Konfiguration für das Adapter-System
 */
export const adapterConfig = {
  // Konfiguration für die AdapterRegistry
  registry: {
    // Pfade, in denen nach Adaptern gesucht wird
    adapterPaths: [
      path.resolve(process.cwd(), 'src/services/blockchain'),
      path.resolve(process.cwd(), 'src/services/storage'),
      path.resolve(process.cwd(), 'src/services/wallet'),
      path.resolve(process.cwd(), 'src/services/payment'),
      path.resolve(process.cwd(), 'src/services/ai')
    ],

    // Automatische Erkennung aktivieren
    autoDiscovery: true,

    // Standard-Adapter für jede Kategorie
    defaultAdapters: {
      blockchain: 'polkadot', // Polkadot als Standard wegen Interoperabilität und Skalierbarkeit
      storage: 'ipfs',
      wallet: 'polkadot',
      payment: 'crypto',
      ai: 'openai'
    }
  },

  // Konfiguration für den AdapterLoader
  loader: {
    // Basis-Pfade für die Adapter-Suche
    basePaths: [
      path.resolve(process.cwd(), 'src/services'),
      path.resolve(process.cwd(), 'src/adapters')
    ],

    // Muster für Adapter-Dateien
    adapterPattern: '*Adapter.js',

    // Externe Module, die Adapter enthalten
    externalModules: [
      { name: '@desci-scholar/blockchain-adapters', category: 'blockchain' },
      { name: '@desci-scholar/storage-adapters', category: 'storage' }
    ]
  }
};

/**
 * Konfiguration für Blockchain-Adapter
 */
export const blockchainAdapterConfig = {
  // Polkadot als Standard-Blockchain (Interoperabilität und Skalierbarkeit)
  polkadot: {
    provider: process.env.POLKADOT_PROVIDER || 'wss://rpc.polkadot.io',
    network: process.env.POLKADOT_NETWORK || 'polkadot',
    parachains: {
      moonbeam: {
        url: process.env.MOONBEAM_PROVIDER_URL || 'wss://moonbeam.api.onfinality.io/public-ws',
        enabled: true
      },
      acala: {
        url: process.env.ACALA_PROVIDER_URL || 'wss://acala-rpc.dwellir.com',
        enabled: true
      }
    }
  },

  // Ethereum-Adapter
  ethereum: {
    provider: process.env.ETHEREUM_PROVIDER || 'https://mainnet.infura.io/v3/your-api-key',
    network: process.env.ETHEREUM_NETWORK || 'mainnet',
    gasLimit: parseInt(process.env.ETHEREUM_GAS_LIMIT || '3000000', 10),
    gasPrice: process.env.ETHEREUM_GAS_PRICE || 'auto'
  },

  // Polygon-Adapter (EVM-kompatibler Layer-2)
  polygon: {
    provider: process.env.POLYGON_PROVIDER || 'https://polygon-rpc.com',
    network: process.env.POLYGON_NETWORK || 'mainnet',
    gasLimit: parseInt(process.env.POLYGON_GAS_LIMIT || '3000000', 10),
    gasPrice: process.env.POLYGON_GAS_PRICE || 'auto'
  },

  // Optimism-Adapter (Ethereum L2)
  optimism: {
    provider: process.env.OPTIMISM_PROVIDER || 'https://mainnet.optimism.io',
    network: process.env.OPTIMISM_NETWORK || 'mainnet',
    gasLimit: parseInt(process.env.OPTIMISM_GAS_LIMIT || '3000000', 10),
    gasPrice: process.env.OPTIMISM_GAS_PRICE || 'auto'
  },

  // Solana-Adapter
  solana: {
    endpoint: process.env.SOLANA_ENDPOINT || 'https://api.mainnet-beta.solana.com',
    network: process.env.SOLANA_NETWORK || 'mainnet-beta',
    metaplex: {
      useStorageAdapter: 'bundlr'
    }
  }
};

/**
 * Konfiguration für Speicher-Adapter
 */
export const storageAdapterConfig = {
  ipfs: {
    gateway: process.env.IPFS_GATEWAY || 'https://ipfs.io/ipfs/',
    apiUrl: process.env.IPFS_API_URL || 'http://localhost:5001/api/v0',
    pinningService: process.env.IPFS_PINNING_SERVICE || 'local'
  },

  bittorrent: {
    downloadPath: process.env.BITTORRENT_DOWNLOAD_PATH || './data/bittorrent',
    trackers: process.env.BITTORRENT_TRACKERS
      ? process.env.BITTORRENT_TRACKERS.split(',')
      : undefined
  },

  arweave: {
    host: process.env.ARWEAVE_HOST || 'arweave.net',
    port: parseInt(process.env.ARWEAVE_PORT || '443', 10),
    protocol: process.env.ARWEAVE_PROTOCOL || 'https'
  },

  filecoin: {
    apiKey: process.env.FILECOIN_API_KEY,
    endpoint: process.env.FILECOIN_ENDPOINT || 'https://api.web3.storage'
  }
};

/**
 * Konfiguration für Wallet-Adapter
 */
export const walletAdapterConfig = {
  polkadot: {
    mnemonicPath: process.env.POLKADOT_MNEMONIC_PATH,
    keyringType: process.env.POLKADOT_KEYRING_TYPE || 'sr25519'
  },

  metamask: {
    network: process.env.METAMASK_NETWORK || 'mainnet'
  },

  walletconnect: {
    bridge: process.env.WALLETCONNECT_BRIDGE || 'https://bridge.walletconnect.org'
  }
};

/**
 * Konfiguration für Zahlungs-Adapter
 */
export const paymentAdapterConfig = {
  crypto: {
    supportedCurrencies: process.env.CRYPTO_SUPPORTED_CURRENCIES
      ? process.env.CRYPTO_SUPPORTED_CURRENCIES.split(',')
      : ['ETH', 'DOT', 'BTC']
  },

  stripe: {
    apiKey: process.env.STRIPE_API_KEY,
    webhookSecret: process.env.STRIPE_WEBHOOK_SECRET
  },

  paypal: {
    clientId: process.env.PAYPAL_CLIENT_ID,
    clientSecret: process.env.PAYPAL_CLIENT_SECRET,
    environment: process.env.PAYPAL_ENVIRONMENT || 'sandbox'
  }
};

/**
 * Konfiguration für KI-Adapter
 */
export const aiAdapterConfig = {
  openai: {
    apiKey: process.env.OPENAI_API_KEY,
    model: process.env.OPENAI_MODEL || 'gpt-4'
  },

  huggingface: {
    apiKey: process.env.HUGGINGFACE_API_KEY,
    model: process.env.HUGGINGFACE_MODEL || 'gpt2'
  }
};

export default {
  adapterConfig,
  blockchainAdapterConfig,
  storageAdapterConfig,
  walletAdapterConfig,
  paymentAdapterConfig,
  aiAdapterConfig
};
