/**
 * @fileoverview UI-Konfiguration für DeSci-Scholar
 *
 * Diese Datei enthält Konfigurationen für die Benutzeroberfläche,
 * einschließlich Beschreibungen und Begründungen für Standardeinstellungen.
 */

/**
 * Konfiguration für die Anzeige von Adapter-Informationen in der UI
 */
export const adapterUIConfig = {
  // Blockchain-Adapter
  blockchain: {
    title: 'Blockchain-Netzwerk',
    description: 'Wählen Sie das Blockchain-Netzwerk für die Speicherung und Verwaltung Ihrer wissenschaftlichen Arbeiten als NFTs.',
    defaultAdapter: 'polkadot',
    defaultReason: 'Polkadot bietet hervorragende Interoperabilität zwischen verschiedenen Blockchains und hohe Skalierbarkeit.',
    adapters: {
      polkadot: {
        name: '<PERSON><PERSON><PERSON>',
        description: 'Interoperables Blockchain-Netzwerk mit Parachain-Architektur und hoher Skalierbarkeit.',
        advantages: [
          'Hohe Interoperabilität mit anderen Blockchains',
          'Skalierbare Parachain-Architektur',
          'Energieeffizientes Proof-of-Stake',
          'Spezialisierte Parachains für verschiedene Anwendungsfälle',
          'Starke Governance-Mechanismen'
        ],
        disadvantages: [
          'Komplexere Architektur',
          'Geringere Entwicklerbasis im Vergleich zu Ethereum'
        ],
        icon: '/frontend/assets/icons/polkadot.svg',
        recommendedFor: ['Interoperabilität', 'Skalierbarkeit', 'Governance']
      },
      ethereum: {
        name: 'Ethereum',
        description: 'Die etablierteste Smart-Contract-Plattform mit dem größten Ökosystem.',
        advantages: [
          'Größtes Entwickler-Ökosystem',
          'Hohe Dezentralisierung',
          'Breite Unterstützung in Wallets und Tools',
          'Etablierte Standards (ERC-721, ERC-1155)'
        ],
        disadvantages: [
          'Höhere Transaktionsgebühren',
          'Geringere Skalierbarkeit der Hauptchain'
        ],
        icon: '/frontend/assets/icons/ethereum.svg',
        recommendedFor: ['Maximale Kompatibilität', 'Etablierte Standards']
      },
      polygon: {
        name: 'Polygon',
        description: 'EVM-kompatibler Layer-2 für Ethereum mit hohem Durchsatz und niedrigen Gebühren.',
        advantages: [
          'Niedrige Transaktionsgebühren',
          'Hoher Durchsatz',
          'Ethereum-Kompatibilität',
          'Wachsendes Ökosystem'
        ],
        disadvantages: [
          'Geringere Dezentralisierung als Ethereum Mainnet',
          'Abhängigkeit von Ethereum für Sicherheit'
        ],
        icon: '/frontend/assets/icons/polygon.svg',
        recommendedFor: ['Kosteneffizienz', 'Hoher Durchsatz', 'NFT-Projekte']
      },
      optimism: {
        name: 'Optimism',
        description: 'Ethereum Layer-2 mit Optimistic Rollups für schnelle und kostengünstige Transaktionen.',
        advantages: [
          'Niedrige Transaktionsgebühren',
          'Schnelle Transaktionen',
          'Volle Ethereum-Kompatibilität',
          'Starke Sicherheitsgarantien'
        ],
        disadvantages: [
          'Längere Wartezeiten für Abhebungen',
          'Geringere Dezentralisierung als Ethereum Mainnet'
        ],
        icon: '/frontend/assets/icons/optimism.svg',
        recommendedFor: ['Ethereum-Kompatibilität', 'Kosteneffizienz']
      },
      solana: {
        name: 'Solana',
        description: 'Hochleistungs-Blockchain mit sehr hohem Durchsatz und niedrigen Transaktionskosten.',
        advantages: [
          'Extrem hoher Durchsatz',
          'Sehr niedrige Transaktionsgebühren',
          'Wachsendes NFT-Ökosystem',
          'Schnelle Finalität'
        ],
        disadvantages: [
          'Höhere Hardware-Anforderungen für Validatoren',
          'Geringere Dezentralisierung',
          'Weniger etablierte Standards für wissenschaftliche NFTs'
        ],
        icon: '/frontend/assets/icons/solana.svg',
        recommendedFor: ['Maximale Performance', 'Minimale Kosten']
      }
    }
  },

  // Speicher-Adapter
  storage: {
    title: 'Dezentraler Speicher',
    description: 'Wählen Sie die Technologie für die dezentrale Speicherung Ihrer Forschungsdaten und Metadaten.',
    defaultAdapter: 'ipfs',
    defaultReason: 'IPFS ist der etablierteste Standard für dezentrale Datenspeicherung mit breiter Unterstützung.',
    adapters: {
      ipfs: {
        name: 'IPFS',
        description: 'InterPlanetary File System - ein verteiltes Dateisystem für dezentrale Speicherung.',
        advantages: [
          'Breite Unterstützung und Etablierung',
          'Content-Adressierung für Datenintegrität',
          'Flexibles Pinning-System',
          'Gute Integration mit Blockchain-Netzwerken'
        ],
        disadvantages: [
          'Benötigt Pinning-Dienste für dauerhafte Verfügbarkeit',
          'Kann langsamer sein als zentralisierte Alternativen'
        ],
        icon: '/frontend/assets/icons/ipfs.svg',
        recommendedFor: ['Allgemeine Anwendungsfälle', 'Metadaten', 'Kleinere Dateien']
      },
      arweave: {
        name: 'Arweave',
        description: 'Permanentes, dezentrales Webhosting mit einmaliger Zahlung für dauerhafte Speicherung.',
        advantages: [
          'Dauerhafte Speicherung mit einmaliger Zahlung',
          'Keine laufenden Kosten für Pinning',
          'Hohe Zensurresistenz',
          'Gute Langzeitarchivierung'
        ],
        disadvantages: [
          'Höhere initiale Kosten',
          'Weniger flexibel als IPFS'
        ],
        icon: '/frontend/assets/icons/arweave.svg',
        recommendedFor: ['Langzeitarchivierung', 'Unveränderliche Daten']
      },
      filecoin: {
        name: 'Filecoin',
        description: 'Dezentraler Speichermarkt basierend auf IPFS mit wirtschaftlichen Anreizen.',
        advantages: [
          'Wirtschaftliche Anreize für Speicheranbieter',
          'Verifizierbare Speicherung',
          'Basiert auf IPFS',
          'Gute Skalierbarkeit'
        ],
        disadvantages: [
          'Komplexeres Modell',
          'Kann teurer sein für kleine Datenmengen'
        ],
        icon: '/frontend/assets/icons/filecoin.svg',
        recommendedFor: ['Große Datenmengen', 'Langfristige Speicherung']
      },
      bittorrent: {
        name: 'BitTorrent',
        description: 'Peer-to-Peer-Filesharing-Protokoll für verteilte Dateiverteilung.',
        advantages: [
          'Sehr effiziente Verteilung großer Dateien',
          'Kein zentraler Server erforderlich',
          'Etablierte Technologie',
          'Gute Skalierbarkeit mit steigender Nutzerzahl'
        ],
        disadvantages: [
          'Abhängig von aktiven Peers für Verfügbarkeit',
          'Weniger Integration mit Blockchain-Netzwerken'
        ],
        icon: '/frontend/assets/icons/bittorrent.svg',
        recommendedFor: ['Sehr große Dateien', 'Aktiv genutzte Datensätze']
      }
    }
  },

  // Wallet-Adapter
  wallet: {
    title: 'Wallet-Integration',
    description: 'Wählen Sie die Wallet-Technologie für die Interaktion mit der Blockchain.',
    defaultAdapter: 'polkadot',
    defaultReason: 'Polkadot.js ist die Standard-Wallet für das Polkadot-Ökosystem mit umfassender Funktionalität.',
    adapters: {
      polkadot: {
        name: 'Polkadot.js',
        description: 'Die offizielle Wallet-Erweiterung für das Polkadot-Ökosystem.',
        advantages: [
          'Volle Unterstützung für Polkadot und Kusama',
          'Unterstützung für alle Parachains',
          'Umfangreiche Funktionen',
          'Aktive Entwicklung'
        ],
        disadvantages: [
          'Komplexere Benutzeroberfläche',
          'Primär für Polkadot-Ökosystem'
        ],
        icon: '/frontend/assets/icons/polkadot-js.svg',
        recommendedFor: ['Polkadot-Nutzer', 'Parachain-Interaktion']
      },
      metamask: {
        name: 'MetaMask',
        description: 'Die populärste Ethereum-Wallet mit breiter Unterstützung für EVM-kompatible Netzwerke.',
        advantages: [
          'Größte Nutzerbasis',
          'Unterstützung für Ethereum, Polygon, Optimism',
          'Einfache Benutzeroberfläche',
          'Breite dApp-Unterstützung'
        ],
        disadvantages: [
          'Keine native Unterstützung für Nicht-EVM-Blockchains',
          'Begrenzte Funktionen für fortgeschrittene Anwendungsfälle'
        ],
        icon: '/frontend/assets/icons/metamask.svg',
        recommendedFor: ['Ethereum-Nutzer', 'EVM-kompatible Chains']
      },
      walletconnect: {
        name: 'WalletConnect',
        description: 'Offenes Protokoll für die Verbindung von Wallets mit dApps über QR-Codes.',
        advantages: [
          'Unterstützung für viele verschiedene Wallets',
          'Funktioniert mit mobilen Wallets',
          'Keine Browser-Erweiterung erforderlich',
          'Breite Kompatibilität'
        ],
        disadvantages: [
          'Zusätzlicher Verbindungsschritt',
          'Abhängig von externen Wallets'
        ],
        icon: '/frontend/assets/icons/walletconnect.svg',
        recommendedFor: ['Mobile Nutzer', 'Multi-Wallet-Unterstützung']
      },
      phantom: {
        name: 'Phantom',
        description: 'Benutzerfreundliche Wallet für Solana mit integriertem NFT-Support.',
        advantages: [
          'Optimiert für Solana',
          'Integrierter NFT-Support',
          'Benutzerfreundliche Oberfläche',
          'Wachsende Funktionalität'
        ],
        disadvantages: [
          'Nur für Solana',
          'Neuere Wallet mit weniger Langzeiterfahrung'
        ],
        icon: '/frontend/assets/icons/phantom.svg',
        recommendedFor: ['Solana-Nutzer', 'NFT-Fokus']
      }
    }
  }
};

/**
 * Vordefinierte Adapter-Profile für verschiedene Anwendungsfälle
 */
export const adapterProfiles = {
  default: {
    name: 'Standard',
    description: 'Die empfohlene Konfiguration für die meisten Nutzer mit ausgewogener Performance und Funktionalität.',
    adapters: {
      blockchain: 'polkadot',
      storage: 'ipfs',
      wallet: 'polkadot'
    }
  },
  costEfficient: {
    name: 'Kosteneffizient',
    description: 'Optimiert für minimale Transaktions- und Speicherkosten.',
    adapters: {
      blockchain: 'polygon',
      storage: 'bittorrent',
      wallet: 'metamask'
    }
  },
  highPerformance: {
    name: 'Hohe Performance',
    description: 'Maximale Geschwindigkeit und Durchsatz für anspruchsvolle Anwendungen.',
    adapters: {
      blockchain: 'solana',
      storage: 'ipfs',
      wallet: 'phantom'
    }
  },
  maxDecentralization: {
    name: 'Maximale Dezentralisierung',
    description: 'Höchste Dezentralisierung und Zensurresistenz für kritische Forschungsdaten.',
    adapters: {
      blockchain: 'ethereum',
      storage: 'arweave',
      wallet: 'metamask'
    }
  },
  longTermArchive: {
    name: 'Langzeitarchivierung',
    description: 'Optimiert für die dauerhafte Speicherung und Verfügbarkeit von Forschungsdaten.',
    adapters: {
      blockchain: 'ethereum',
      storage: 'filecoin',
      wallet: 'metamask'
    }
  },
  crossChainCompatibility: {
    name: 'Chain-übergreifende Kompatibilität',
    description: 'Maximale Interoperabilität zwischen verschiedenen Blockchain-Ökosystemen.',
    adapters: {
      blockchain: 'polkadot',
      storage: 'ipfs',
      wallet: 'walletconnect'
    }
  }
};

/**
 * Konfiguration für den Guided Setup Assistenten
 */
export const guidedSetupConfig = {
  steps: [
    {
      id: 'welcome',
      title: 'Willkommen bei DeSci-Scholar',
      description: 'Dieser Assistent hilft Ihnen, DeSci-Scholar optimal für Ihre Bedürfnisse zu konfigurieren.',
      type: 'info'
    },
    {
      id: 'experience',
      title: 'Ihre Erfahrung',
      description: 'Wie vertraut sind Sie mit Blockchain-Technologie?',
      type: 'choice',
      options: [
        { id: 'beginner', label: 'Anfänger', description: 'Ich bin neu in der Blockchain-Welt' },
        { id: 'intermediate', label: 'Fortgeschritten', description: 'Ich habe bereits Erfahrung mit Kryptowährungen' },
        { id: 'expert', label: 'Experte', description: 'Ich bin vertraut mit verschiedenen Blockchains und Smart Contracts' }
      ]
    },
    {
      id: 'useCase',
      title: 'Anwendungsfall',
      description: 'Was ist Ihr primärer Anwendungsfall?',
      type: 'choice',
      options: [
        { id: 'publishing', label: 'Publikation', description: 'Veröffentlichung wissenschaftlicher Arbeiten als NFTs' },
        { id: 'archiving', label: 'Archivierung', description: 'Langfristige Archivierung von Forschungsdaten' },
        { id: 'collaboration', label: 'Zusammenarbeit', description: 'Kollaborative Forschung und Peer-Review' },
        { id: 'monetization', label: 'Monetarisierung', description: 'Monetarisierung von Forschungsergebnissen' }
      ]
    },
    {
      id: 'dataSize',
      title: 'Datengröße',
      description: 'Wie groß sind Ihre typischen Forschungsdaten?',
      type: 'choice',
      options: [
        { id: 'small', label: 'Klein', description: 'Hauptsächlich Texte und kleine Bilder (<10MB)' },
        { id: 'medium', label: 'Mittel', description: 'Größere Datensätze und Medien (10MB-1GB)' },
        { id: 'large', label: 'Groß', description: 'Sehr große Datensätze oder Medien (>1GB)' }
      ]
    },
    {
      id: 'costSensitivity',
      title: 'Kostenempfindlichkeit',
      description: 'Wie wichtig sind niedrige Transaktions- und Speicherkosten für Sie?',
      type: 'choice',
      options: [
        { id: 'high', label: 'Sehr wichtig', description: 'Kosten sollten minimal sein' },
        { id: 'medium', label: 'Wichtig', description: 'Kosten sind ein Faktor, aber nicht entscheidend' },
        { id: 'low', label: 'Weniger wichtig', description: 'Funktionalität ist wichtiger als Kosten' }
      ]
    },
    {
      id: 'recommendation',
      title: 'Unsere Empfehlung',
      description: 'Basierend auf Ihren Antworten empfehlen wir folgendes Profil:',
      type: 'recommendation'
    }
  ],

  // Logik für Empfehlungen basierend auf Antworten
  recommendationLogic: {
    // Anfänger bekommen immer das Standardprofil
    beginner: 'default',

    // Fortgeschrittene bekommen profilbasierte Empfehlungen
    intermediate: {
      publishing: {
        small: { high: 'costEfficient', medium: 'default', low: 'default' },
        medium: { high: 'costEfficient', medium: 'default', low: 'highPerformance' },
        large: { high: 'costEfficient', medium: 'highPerformance', low: 'highPerformance' }
      },
      archiving: {
        small: { high: 'costEfficient', medium: 'longTermArchive', low: 'longTermArchive' },
        medium: { high: 'costEfficient', medium: 'longTermArchive', low: 'longTermArchive' },
        large: { high: 'costEfficient', medium: 'longTermArchive', low: 'longTermArchive' }
      },
      collaboration: {
        small: { high: 'costEfficient', medium: 'crossChainCompatibility', low: 'crossChainCompatibility' },
        medium: { high: 'costEfficient', medium: 'crossChainCompatibility', low: 'crossChainCompatibility' },
        large: { high: 'costEfficient', medium: 'crossChainCompatibility', low: 'highPerformance' }
      },
      monetization: {
        small: { high: 'costEfficient', medium: 'default', low: 'maxDecentralization' },
        medium: { high: 'costEfficient', medium: 'default', low: 'maxDecentralization' },
        large: { high: 'costEfficient', medium: 'highPerformance', low: 'maxDecentralization' }
      }
    },

    // Experten bekommen spezifischere Empfehlungen
    expert: {
      publishing: {
        small: { high: 'costEfficient', medium: 'default', low: 'maxDecentralization' },
        medium: { high: 'costEfficient', medium: 'highPerformance', low: 'maxDecentralization' },
        large: { high: 'costEfficient', medium: 'highPerformance', low: 'maxDecentralization' }
      },
      archiving: {
        small: { high: 'costEfficient', medium: 'longTermArchive', low: 'longTermArchive' },
        medium: { high: 'costEfficient', medium: 'longTermArchive', low: 'longTermArchive' },
        large: { high: 'costEfficient', medium: 'longTermArchive', low: 'longTermArchive' }
      },
      collaboration: {
        small: { high: 'costEfficient', medium: 'crossChainCompatibility', low: 'crossChainCompatibility' },
        medium: { high: 'costEfficient', medium: 'crossChainCompatibility', low: 'crossChainCompatibility' },
        large: { high: 'costEfficient', medium: 'crossChainCompatibility', low: 'highPerformance' }
      },
      monetization: {
        small: { high: 'costEfficient', medium: 'default', low: 'maxDecentralization' },
        medium: { high: 'costEfficient', medium: 'default', low: 'maxDecentralization' },
        large: { high: 'costEfficient', medium: 'highPerformance', low: 'maxDecentralization' }
      }
    }
  }
};
