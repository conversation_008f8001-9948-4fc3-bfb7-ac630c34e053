/**
 * @fileoverview Konfiguration für die Integration mit OpenCitations
 * 
 * Diese Konfigurationsdatei enthält Einstellungen für die Integration mit OpenCitations,
 * einer unabhängigen Infrastrukturorganisation, die offene bibliografische und Zitationsdaten bereitstellt.
 */

const opencitationsConfig = {
  // API-Konfiguration
  api: {
    baseUrl: process.env.OPENCITATIONS_API_URL || 'https://opencitations.net/index/api/v1',
    accessToken: process.env.OPENCITATIONS_ACCESS_TOKEN,
    timeout: 10000, // Timeout in Millisekunden
    maxRetries: 3 // Maximale Anzahl von Wiederholungsversuchen
  },
  
  // Einstellungen für die Zitationsanalyse
  citationAnalysis: {
    // Ob OpenCitations-Daten standardmäßig für die Zitationsanalyse verwendet werden sollen
    useByDefault: true,
    
    // Maximale Anzahl von DOIs, die in einer Anfrage abgefragt werden können
    maxBatchSize: 50,
    
    // Ob Zitationsdaten aus OpenCitations Vorrang vor lokalen Daten haben sollen
    prioritizeOverLocalData: true
  },
  
  // Einstellungen für die NFT-Erstellung
  nftCreation: {
    // Ob OpenCitations-Daten standardmäßig in NFT-Metadaten aufgenommen werden sollen
    includeInMetadata: true,
    
    // Ob OCIs (Open Citation Identifiers) in NFT-Metadaten aufgenommen werden sollen
    includeOCIs: true,
    
    // Ob ein OpenCitations-Verifizierungsbadge in NFT-Metadaten aufgenommen werden soll
    includeVerificationBadge: true
  },
  
  // Einstellungen für die Netzwerkanalyse
  networkAnalysis: {
    // Ob OpenCitations-Daten standardmäßig für die Netzwerkanalyse verwendet werden sollen
    useByDefault: true,
    
    // Maximale Tiefe für die rekursive Erweiterung des Netzwerks mit OpenCitations-Daten
    maxDepth: 2,
    
    // Maximale Anzahl von Knoten im Netzwerk
    maxNodes: 100
  }
};

export default opencitationsConfig;/**
 * @fileoverview Konfiguration für die Integration mit OpenCitations
 * 
 * Diese Konfigurationsdatei enthält Einstellungen für die Integration mit OpenCitations,
 * einer unabhängigen Infrastrukturorganisation, die offene bibliografische und Zitationsdaten bereitstellt.
 */

const opencitationsConfig = {
  // API-Konfiguration
  api: {
    baseUrl: process.env.OPENCITATIONS_API_URL || 'https://opencitations.net/index/api/v1',
    accessToken: process.env.OPENCITATIONS_ACCESS_TOKEN,
    timeout: 10000, // Timeout in Millisekunden
    maxRetries: 3 // Maximale Anzahl von Wiederholungsversuchen
  },
  
  // Einstellungen für die Zitationsanalyse
  citationAnalysis: {
    // Ob OpenCitations-Daten standardmäßig für die Zitationsanalyse verwendet werden sollen
    useByDefault: true,
    
    // Maximale Anzahl von DOIs, die in einer Anfrage abgefragt werden können
    maxBatchSize: 50,
    
    // Ob Zitationsdaten aus OpenCitations Vorrang vor lokalen Daten haben sollen
    prioritizeOverLocalData: true
  },
  
  // Einstellungen für die NFT-Erstellung
  nftCreation: {
    // Ob OpenCitations-Daten standardmäßig in NFT-Metadaten aufgenommen werden sollen
    includeInMetadata: true,
    
    // Ob OCIs (Open Citation Identifiers) in NFT-Metadaten aufgenommen werden sollen
    includeOCIs: true,
    
    // Ob ein OpenCitations-Verifizierungsbadge in NFT-Metadaten aufgenommen werden soll
    includeVerificationBadge: true
  },
  
  // Einstellungen für die Netzwerkanalyse
  networkAnalysis: {
    // Ob OpenCitations-Daten standardmäßig für die Netzwerkanalyse verwendet werden sollen
    useByDefault: true,
    
    // Maximale Tiefe für die rekursive Erweiterung des Netzwerks mit OpenCitations-Daten
    maxDepth: 2,
    
    // Maximale Anzahl von Knoten im Netzwerk
    maxNodes: 100
  }
};

export default opencitationsConfig;/**
 * @fileoverview Konfiguration für die Integration mit OpenCitations
 * 
 * Diese Konfigurationsdatei enthält Einstellungen für die Integration mit OpenCitations,
 * einer unabhängigen Infrastrukturorganisation, die offene bibliografische und Zitationsdaten bereitstellt.
 */

const opencitationsConfig = {
  // API-Konfiguration
  api: {
    baseUrl: process.env.OPENCITATIONS_API_URL || 'https://opencitations.net/index/api/v1',
    accessToken: process.env.OPENCITATIONS_ACCESS_TOKEN,
    timeout: 10000, // Timeout in Millisekunden
    maxRetries: 3 // Maximale Anzahl von Wiederholungsversuchen
  },
  
  // Einstellungen für die Zitationsanalyse
  citationAnalysis: {
    // Ob OpenCitations-Daten standardmäßig für die Zitationsanalyse verwendet werden sollen
    useByDefault: true,
    
    // Maximale Anzahl von DOIs, die in einer Anfrage abgefragt werden können
    maxBatchSize: 50,
    
    // Ob Zitationsdaten aus OpenCitations Vorrang vor lokalen Daten haben sollen
    prioritizeOverLocalData: true
  },
  
  // Einstellungen für die NFT-Erstellung
  nftCreation: {
    // Ob OpenCitations-Daten standardmäßig in NFT-Metadaten aufgenommen werden sollen
    includeInMetadata: true,
    
    // Ob OCIs (Open Citation Identifiers) in NFT-Metadaten aufgenommen werden sollen
    includeOCIs: true,
    
    // Ob ein OpenCitations-Verifizierungsbadge in NFT-Metadaten aufgenommen werden soll
    includeVerificationBadge: true
  },
  
  // Einstellungen für die Netzwerkanalyse
  networkAnalysis: {
    // Ob OpenCitations-Daten standardmäßig für die Netzwerkanalyse verwendet werden sollen
    useByDefault: true,
    
    // Maximale Tiefe für die rekursive Erweiterung des Netzwerks mit OpenCitations-Daten
    maxDepth: 2,
    
    // Maximale Anzahl von Knoten im Netzwerk
    maxNodes: 100
  }
};

export default opencitationsConfig;/**
 * @fileoverview Konfiguration für die Integration mit OpenCitations
 * 
 * Diese Konfigurationsdatei enthält Einstellungen für die Integration mit OpenCitations,
 * einer unabhängigen Infrastrukturorganisation, die offene bibliografische und Zitationsdaten bereitstellt.
 */

const opencitationsConfig = {
  // API-Konfiguration
  api: {
    baseUrl: process.env.OPENCITATIONS_API_URL || 'https://opencitations.net/index/api/v1',
    accessToken: process.env.OPENCITATIONS_ACCESS_TOKEN,
    timeout: 10000, // Timeout in Millisekunden
    maxRetries: 3 // Maximale Anzahl von Wiederholungsversuchen
  },
  
  // Einstellungen für die Zitationsanalyse
  citationAnalysis: {
    // Ob OpenCitations-Daten standardmäßig für die Zitationsanalyse verwendet werden sollen
    useByDefault: true,
    
    // Maximale Anzahl von DOIs, die in einer Anfrage abgefragt werden können
    maxBatchSize: 50,
    
    // Ob Zitationsdaten aus OpenCitations Vorrang vor lokalen Daten haben sollen
    prioritizeOverLocalData: true
  },
  
  // Einstellungen für die NFT-Erstellung
  nftCreation: {
    // Ob OpenCitations-Daten standardmäßig in NFT-Metadaten aufgenommen werden sollen
    includeInMetadata: true,
    
    // Ob OCIs (Open Citation Identifiers) in NFT-Metadaten aufgenommen werden sollen
    includeOCIs: true,
    
    // Ob ein OpenCitations-Verifizierungsbadge in NFT-Metadaten aufgenommen werden soll
    includeVerificationBadge: true
  },
  
  // Einstellungen für die Netzwerkanalyse
  networkAnalysis: {
    // Ob OpenCitations-Daten standardmäßig für die Netzwerkanalyse verwendet werden sollen
    useByDefault: true,
    
    // Maximale Tiefe für die rekursive Erweiterung des Netzwerks mit OpenCitations-Daten
    maxDepth: 2,
    
    // Maximale Anzahl von Knoten im Netzwerk
    maxNodes: 100
  }
};

export default opencitationsConfig;/**
 * @fileoverview Konfiguration für die Integration mit OpenCitations
 * 
 * Diese Konfigurationsdatei enthält Einstellungen für die Integration mit OpenCitations,
 * einer unabhängigen Infrastrukturorganisation, die offene bibliografische und Zitationsdaten bereitstellt.
 */

const opencitationsConfig = {
  // API-Konfiguration
  api: {
    baseUrl: process.env.OPENCITATIONS_API_URL || 'https://opencitations.net/index/api/v1',
    accessToken: process.env.OPENCITATIONS_ACCESS_TOKEN,
    timeout: 10000, // Timeout in Millisekunden
    maxRetries: 3 // Maximale Anzahl von Wiederholungsversuchen
  },
  
  // Einstellungen für die Zitationsanalyse
  citationAnalysis: {
    // Ob OpenCitations-Daten standardmäßig für die Zitationsanalyse verwendet werden sollen
    useByDefault: true,
    
    // Maximale Anzahl von DOIs, die in einer Anfrage abgefragt werden können
    maxBatchSize: 50,
    
    // Ob Zitationsdaten aus OpenCitations Vorrang vor lokalen Daten haben sollen
    prioritizeOverLocalData: true
  },
  
  // Einstellungen für die NFT-Erstellung
  nftCreation: {
    // Ob OpenCitations-Daten standardmäßig in NFT-Metadaten aufgenommen werden sollen
    includeInMetadata: true,
    
    // Ob OCIs (Open Citation Identifiers) in NFT-Metadaten aufgenommen werden sollen
    includeOCIs: true,
    
    // Ob ein OpenCitations-Verifizierungsbadge in NFT-Metadaten aufgenommen werden soll
    includeVerificationBadge: true
  },
  
  // Einstellungen für die Netzwerkanalyse
  networkAnalysis: {
    // Ob OpenCitations-Daten standardmäßig für die Netzwerkanalyse verwendet werden sollen
    useByDefault: true,
    
    // Maximale Tiefe für die rekursive Erweiterung des Netzwerks mit OpenCitations-Daten
    maxDepth: 2,
    
    // Maximale Anzahl von Knoten im Netzwerk
    maxNodes: 100
  }
};

export default opencitationsConfig;/**
 * @fileoverview Konfiguration für die Integration mit OpenCitations
 * 
 * Diese Konfigurationsdatei enthält Einstellungen für die Integration mit OpenCitations,
 * einer unabhängigen Infrastrukturorganisation, die offene bibliografische und Zitationsdaten bereitstellt.
 */

const opencitationsConfig = {
  // API-Konfiguration
  api: {
    baseUrl: process.env.OPENCITATIONS_API_URL || 'https://opencitations.net/index/api/v1',
    accessToken: process.env.OPENCITATIONS_ACCESS_TOKEN,
    timeout: 10000, // Timeout in Millisekunden
    maxRetries: 3 // Maximale Anzahl von Wiederholungsversuchen
  },
  
  // Einstellungen für die Zitationsanalyse
  citationAnalysis: {
    // Ob OpenCitations-Daten standardmäßig für die Zitationsanalyse verwendet werden sollen
    useByDefault: true,
    
    // Maximale Anzahl von DOIs, die in einer Anfrage abgefragt werden können
    maxBatchSize: 50,
    
    // Ob Zitationsdaten aus OpenCitations Vorrang vor lokalen Daten haben sollen
    prioritizeOverLocalData: true
  },
  
  // Einstellungen für die NFT-Erstellung
  nftCreation: {
    // Ob OpenCitations-Daten standardmäßig in NFT-Metadaten aufgenommen werden sollen
    includeInMetadata: true,
    
    // Ob OCIs (Open Citation Identifiers) in NFT-Metadaten aufgenommen werden sollen
    includeOCIs: true,
    
    // Ob ein OpenCitations-Verifizierungsbadge in NFT-Metadaten aufgenommen werden soll
    includeVerificationBadge: true
  },
  
  // Einstellungen für die Netzwerkanalyse
  networkAnalysis: {
    // Ob OpenCitations-Daten standardmäßig für die Netzwerkanalyse verwendet werden sollen
    useByDefault: true,
    
    // Maximale Tiefe für die rekursive Erweiterung des Netzwerks mit OpenCitations-Daten
    maxDepth: 2,
    
    // Maximale Anzahl von Knoten im Netzwerk
    maxNodes: 100
  }
};

export default opencitationsConfig;/**
 * @fileoverview Konfiguration für die Integration mit OpenCitations
 * 
 * Diese Konfigurationsdatei enthält Einstellungen für die Integration mit OpenCitations,
 * einer unabhängigen Infrastrukturorganisation, die offene bibliografische und Zitationsdaten bereitstellt.
 */

const opencitationsConfig = {
  // API-Konfiguration
  api: {
    baseUrl: process.env.OPENCITATIONS_API_URL || 'https://opencitations.net/index/api/v1',
    accessToken: process.env.OPENCITATIONS_ACCESS_TOKEN,
    timeout: 10000, // Timeout in Millisekunden
    maxRetries: 3 // Maximale Anzahl von Wiederholungsversuchen
  },
  
  // Einstellungen für die Zitationsanalyse
  citationAnalysis: {
    // Ob OpenCitations-Daten standardmäßig für die Zitationsanalyse verwendet werden sollen
    useByDefault: true,
    
    // Maximale Anzahl von DOIs, die in einer Anfrage abgefragt werden können
    maxBatchSize: 50,
    
    // Ob Zitationsdaten aus OpenCitations Vorrang vor lokalen Daten haben sollen
    prioritizeOverLocalData: true
  },
  
  // Einstellungen für die NFT-Erstellung
  nftCreation: {
    // Ob OpenCitations-Daten standardmäßig in NFT-Metadaten aufgenommen werden sollen
    includeInMetadata: true,
    
    // Ob OCIs (Open Citation Identifiers) in NFT-Metadaten aufgenommen werden sollen
    includeOCIs: true,
    
    // Ob ein OpenCitations-Verifizierungsbadge in NFT-Metadaten aufgenommen werden soll
    includeVerificationBadge: true
  },
  
  // Einstellungen für die Netzwerkanalyse
  networkAnalysis: {
    // Ob OpenCitations-Daten standardmäßig für die Netzwerkanalyse verwendet werden sollen
    useByDefault: true,
    
    // Maximale Tiefe für die rekursive Erweiterung des Netzwerks mit OpenCitations-Daten
    maxDepth: 2,
    
    // Maximale Anzahl von Knoten im Netzwerk
    maxNodes: 100
  }
};

export default opencitationsConfig;/**
 * @fileoverview Konfiguration für die Integration mit OpenCitations
 * 
 * Diese Konfigurationsdatei enthält Einstellungen für die Integration mit OpenCitations,
 * einer unabhängigen Infrastrukturorganisation, die offene bibliografische und Zitationsdaten bereitstellt.
 */

const opencitationsConfig = {
  // API-Konfiguration
  api: {
    baseUrl: process.env.OPENCITATIONS_API_URL || 'https://opencitations.net/index/api/v1',
    accessToken: process.env.OPENCITATIONS_ACCESS_TOKEN,
    timeout: 10000, // Timeout in Millisekunden
    maxRetries: 3 // Maximale Anzahl von Wiederholungsversuchen
  },
  
  // Einstellungen für die Zitationsanalyse
  citationAnalysis: {
    // Ob OpenCitations-Daten standardmäßig für die Zitationsanalyse verwendet werden sollen
    useByDefault: true,
    
    // Maximale Anzahl von DOIs, die in einer Anfrage abgefragt werden können
    maxBatchSize: 50,
    
    // Ob Zitationsdaten aus OpenCitations Vorrang vor lokalen Daten haben sollen
    prioritizeOverLocalData: true
  },
  
  // Einstellungen für die NFT-Erstellung
  nftCreation: {
    // Ob OpenCitations-Daten standardmäßig in NFT-Metadaten aufgenommen werden sollen
    includeInMetadata: true,
    
    // Ob OCIs (Open Citation Identifiers) in NFT-Metadaten aufgenommen werden sollen
    includeOCIs: true,
    
    // Ob ein OpenCitations-Verifizierungsbadge in NFT-Metadaten aufgenommen werden soll
    includeVerificationBadge: true
  },
  
  // Einstellungen für die Netzwerkanalyse
  networkAnalysis: {
    // Ob OpenCitations-Daten standardmäßig für die Netzwerkanalyse verwendet werden sollen
    useByDefault: true,
    
    // Maximale Tiefe für die rekursive Erweiterung des Netzwerks mit OpenCitations-Daten
    maxDepth: 2,
    
    // Maximale Anzahl von Knoten im Netzwerk
    maxNodes: 100
  }
};

export default opencitationsConfig;/**
 * @fileoverview Konfiguration für die Integration mit OpenCitations
 * 
 * Diese Konfigurationsdatei enthält Einstellungen für die Integration mit OpenCitations,
 * einer unabhängigen Infrastrukturorganisation, die offene bibliografische und Zitationsdaten bereitstellt.
 */

const opencitationsConfig = {
  // API-Konfiguration
  api: {
    baseUrl: process.env.OPENCITATIONS_API_URL || 'https://opencitations.net/index/api/v1',
    accessToken: process.env.OPENCITATIONS_ACCESS_TOKEN,
    timeout: 10000, // Timeout in Millisekunden
    maxRetries: 3 // Maximale Anzahl von Wiederholungsversuchen
  },
  
  // Einstellungen für die Zitationsanalyse
  citationAnalysis: {
    // Ob OpenCitations-Daten standardmäßig für die Zitationsanalyse verwendet werden sollen
    useByDefault: true,
    
    // Maximale Anzahl von DOIs, die in einer Anfrage abgefragt werden können
    maxBatchSize: 50,
    
    // Ob Zitationsdaten aus OpenCitations Vorrang vor lokalen Daten haben sollen
    prioritizeOverLocalData: true
  },
  
  // Einstellungen für die NFT-Erstellung
  nftCreation: {
    // Ob OpenCitations-Daten standardmäßig in NFT-Metadaten aufgenommen werden sollen
    includeInMetadata: true,
    
    // Ob OCIs (Open Citation Identifiers) in NFT-Metadaten aufgenommen werden sollen
    includeOCIs: true,
    
    // Ob ein OpenCitations-Verifizierungsbadge in NFT-Metadaten aufgenommen werden soll
    includeVerificationBadge: true
  },
  
  // Einstellungen für die Netzwerkanalyse
  networkAnalysis: {
    // Ob OpenCitations-Daten standardmäßig für die Netzwerkanalyse verwendet werden sollen
    useByDefault: true,
    
    // Maximale Tiefe für die rekursive Erweiterung des Netzwerks mit OpenCitations-Daten
    maxDepth: 2,
    
    // Maximale Anzahl von Knoten im Netzwerk
    maxNodes: 100
  }
};

export default opencitationsConfig;/**
 * @fileoverview Konfiguration für die Integration mit OpenCitations
 * 
 * Diese Konfigurationsdatei enthält Einstellungen für die Integration mit OpenCitations,
 * einer unabhängigen Infrastrukturorganisation, die offene bibliografische und Zitationsdaten bereitstellt.
 */

const opencitationsConfig = {
  // API-Konfiguration
  api: {
    baseUrl: process.env.OPENCITATIONS_API_URL || 'https://opencitations.net/index/api/v1',
    accessToken: process.env.OPENCITATIONS_ACCESS_TOKEN,
    timeout: 10000, // Timeout in Millisekunden
    maxRetries: 3 // Maximale Anzahl von Wiederholungsversuchen
  },
  
  // Einstellungen für die Zitationsanalyse
  citationAnalysis: {
    // Ob OpenCitations-Daten standardmäßig für die Zitationsanalyse verwendet werden sollen
    useByDefault: true,
    
    // Maximale Anzahl von DOIs, die in einer Anfrage abgefragt werden können
    maxBatchSize: 50,
    
    // Ob Zitationsdaten aus OpenCitations Vorrang vor lokalen Daten haben sollen
    prioritizeOverLocalData: true
  },
  
  // Einstellungen für die NFT-Erstellung
  nftCreation: {
    // Ob OpenCitations-Daten standardmäßig in NFT-Metadaten aufgenommen werden sollen
    includeInMetadata: true,
    
    // Ob OCIs (Open Citation Identifiers) in NFT-Metadaten aufgenommen werden sollen
    includeOCIs: true,
    
    // Ob ein OpenCitations-Verifizierungsbadge in NFT-Metadaten aufgenommen werden soll
    includeVerificationBadge: true
  },
  
  // Einstellungen für die Netzwerkanalyse
  networkAnalysis: {
    // Ob OpenCitations-Daten standardmäßig für die Netzwerkanalyse verwendet werden sollen
    useByDefault: true,
    
    // Maximale Tiefe für die rekursive Erweiterung des Netzwerks mit OpenCitations-Daten
    maxDepth: 2,
    
    // Maximale Anzahl von Knoten im Netzwerk
    maxNodes: 100
  }
};

export default opencitationsConfig; * @fileoverview Konfiguration für die Integration mit OpenCitations
 * 
 * Diese Konfigurationsdatei enthält Einstellungen für die Integration mit OpenCitations,
 * einer unabhängigen Infrastrukturorganisation, die offene bibliografische und Zitationsdaten bereitstellt.
 */

const opencitationsConfig = {
  // API-Konfiguration
  api: {
    baseUrl: process.env.OPENCITATIONS_API_URL || 'https://opencitations.net/index/api/v1',
    accessToken: process.env.OPENCITATIONS_ACCESS_TOKEN,
    timeout: 10000, // Timeout in Millisekunden
    maxRetries: 3 // Maximale Anzahl von Wiederholungsversuchen
  },
  
  // Einstellungen für die Zitationsanalyse
  citationAnalysis: {
    // Ob OpenCitations-Daten standardmäßig für die Zitationsanalyse verwendet werden sollen
    useByDefault: true,
    
    // Maximale Anzahl von DOIs, die in einer Anfrage abgefragt werden können
    maxBatchSize: 50,
    
    // Ob Zitationsdaten aus OpenCitations Vorrang vor lokalen Daten haben sollen
    prioritizeOverLocalData: true
  },
  
  // Einstellungen für die NFT-Erstellung
  nftCreation: {
    // Ob OpenCitations-Daten standardmäßig in NFT-Metadaten aufgenommen werden sollen
    includeInMetadata: true,
    
    // Ob OCIs (Open Citation Identifiers) in NFT-Metadaten aufgenommen werden sollen
    includeOCIs: true,
    
    // Ob ein OpenCitations-Verifizierungsbadge in NFT-Metadaten aufgenommen werden soll
    includeVerificationBadge: true
  },
  
  // Einstellungen für die Netzwerkanalyse
  networkAnalysis: {
    // Ob OpenCitations-Daten standardmäßig für die Netzwerkanalyse verwendet werden sollen
    useByDefault: true,
    
    // Maximale Tiefe für die rekursive Erweiterung des Netzwerks mit OpenCitations-Daten
    maxDepth: 2,
    
    // Maximale Anzahl von Knoten im Netzwerk
    maxNodes: 100
  }
};

export default opencitationsConfig;