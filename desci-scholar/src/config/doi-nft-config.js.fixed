/**
 * @fileoverview Konfiguration für die DOI-zu-NFT und Patent-ID-zu-NFT-Funktionalität
 * 
 * Diese Datei enthält die Konfiguration für die DOI-zu-NFT und Patent-ID-zu-NFT-Funktionalität,
 * einschließlich der Konfiguration für die Integration mit DataCite, Crossref und Patentämtern.
 */

/**
 * Konfiguration für die DOI-zu-NFT und Patent-ID-zu-NFT-Funktionalität
 */
const doiNftConfig = {
  /**
   * Konfiguration für die DataCite-Integration
   */
  dataCite: {
    /**
     * API-Basis-URL
     */
    apiBaseUrl: process.env.DATACITE_API_URL || 'https://api.datacite.org',
    
    /**
     * API-Schlüssel für die Authentifizierung
     */
    apiKey: process.env.DATACITE_API_KEY,
    
    /**
     * Repository-ID für die DOI-Registrierung
     */
    repositoryId: process.env.DATACITE_REPOSITORY_ID,
    
    /**
     * Präfix für DOIs
     */
    doiPrefix: process.env.DATACITE_DOI_PREFIX || '10.5281',
    
    /**
     * Timeout für API-Anfragen in Millisekunden
     */
    timeout: parseInt(process.env.DATACITE_API_TIMEOUT) || 10000,
    
    /**
     * Maximale Anzahl von Wiederholungsversuchen bei Fehlern
     */
    maxRetries: parseInt(process.env.DATACITE_API_MAX_RETRIES) || 3
  },
  
  /**
   * Konfiguration für die Crossref-Integration
   */
  crossref: {
    /**
     * API-Basis-URL
     */
    apiBaseUrl: process.env.CROSSREF_API_URL || 'https://api.crossref.org',
    
    /**
     * E-Mail-Adresse für die Authentifizierung (für bessere Rate Limits)
     */
    email: process.env.CROSSREF_EMAIL,
    
    /**
     * API-Token für die Authentifizierung
     */
    token: process.env.CROSSREF_API_TOKEN,
    
    /**
     * Timeout für API-Anfragen in Millisekunden
     */
    timeout: parseInt(process.env.CROSSREF_API_TIMEOUT) || 10000,
    
    /**
     * Maximale Anzahl von Wiederholungsversuchen bei Fehlern
     */
    maxRetries: parseInt(process.env.CROSSREF_API_MAX_RETRIES) || 3
  },
  
  /**
   * Konfiguration für die Patent-Integration
   */
  patent: {
    /**
     * Direkte Verknüpfung von Patent-IDs mit NFTs aktivieren
     */
    directNftLinking: process.env.PATENT_DIRECT_NFT_LINKING !== 'false',
    
    /**
     * Patentämter-Konfiguration
     */
    offices: {
      uspto: {
        baseUrl: process.env.USPTO_API_URL || 'https://developer.uspto.gov/ibd-api/v1',
        apiKey: process.env.USPTO_API_KEY
      },
      epo: {
        baseUrl: process.env.EPO_API_URL || 'https://data.epo.org/api/v1',
        apiKey: process.env.EPO_API_KEY
      },
      wipo: {
        baseUrl: process.env.WIPO_API_URL || 'https://patentscope.wipo.int/api',
        apiKey: process.env.WIPO_API_KEY
      }
    }
  },
  
  /**
   * Konfiguration für die NFT-Prägung
   */
  nft: {
    /**
     * Standardmäßig Metadaten auf IPFS speichern
     */
    storeOnIpfs: process.env.DOI_NFT_STORE_ON_IPFS !== 'false',
    
    /**
     * Standardmäßig Torrents für Publikationen erstellen
     */
    createTorrent: process.env.DOI_NFT_CREATE_TORRENT === 'true',
    
    /**
     * Blockchain-Netzwerk für die NFT-Prägung
     */
    network: process.env.DOI_NFT_NETWORK || 'polkadot',
    
    /**
     * Smart-Contract-Adresse für die NFT-Prägung
     */
    contractAddress: process.env.DOI_NFT_CONTRACT_ADDRESS,
    
    /**
     * Verwende den erweiterten NFT-Contract
     */
    useEnhancedContract: process.env.DOI_NFT_USE_ENHANCED_CONTRACT !== 'false',
    
    /**
     * Gas-Limit für Transaktionen
     */
    gasLimit: parseInt(process.env.DOI_NFT_GAS_LIMIT) || 3000000,
    
    /**
     * Maximale Gas-Gebühr
     */
    maxFeePerGas: process.env.DOI_NFT_MAX_FEE_PER_GAS || '50000000000'
  },
  
  /**
   * Konfiguration für die Metadaten
   */
  metadata: {
    /**
     * Basis-URL für Bilder
     */
    imageBaseUrl: process.env.DOI_NFT_IMAGE_BASE_URL || 'https://desci-scholar.org/api/paper-image',
    
    /**
     * Schema-URL für Metadaten
     */
    schemaUrl: process.env.DOI_NFT_SCHEMA_URL || 'https://desci-scholar.org/schemas/doi-nft/v1',
    
    /**
     * Standardlizenz für Metadaten
     */
    defaultLicense: process.env.DOI_NFT_DEFAULT_LICENSE || 'CC-BY-4.0'
  },
  
  /**
   * Konfiguration für die Speicherung
   */
  storage: {
    /**
     * Speichere DOI-zu-NFT-Zuordnungen in der Datenbank
     */
    storeMapping: process.env.DOI_NFT_STORE_MAPPING !== 'false',
    
    /**
     * Speichere Patent-ID-zu-NFT-Zuordnungen in der Datenbank
     */
    storePatentMapping: process.env.PATENT_NFT_STORE_MAPPING !== 'false',
    
    /**
     * Cache-Größe für DOI-Metadaten
     */
    metadataCacheSize: parseInt(process.env.DOI_NFT_METADATA_CACHE_SIZE) || 1000,
    
    /**
     * Cache-TTL für DOI-Metadaten in Millisekunden
     */
    metadataCacheTtl: parseInt(process.env.DOI_NFT_METADATA_CACHE_TTL) || 3600000 // 1 Stunde
  }
};

export default doiNftConfig;/**
 * @fileoverview Konfiguration für die DOI-zu-NFT und Patent-ID-zu-NFT-Funktionalität
 * 
 * Diese Datei enthält die Konfiguration für die DOI-zu-NFT und Patent-ID-zu-NFT-Funktionalität,
 * einschließlich der Konfiguration für die Integration mit DataCite, Crossref und Patentämtern.
 */

/**
 * Konfiguration für die DOI-zu-NFT und Patent-ID-zu-NFT-Funktionalität
 */
const doiNftConfig = {
  /**
   * Konfiguration für die DataCite-Integration
   */
  dataCite: {
    /**
     * API-Basis-URL
     */
    apiBaseUrl: process.env.DATACITE_API_URL || 'https://api.datacite.org',
    
    /**
     * API-Schlüssel für die Authentifizierung
     */
    apiKey: process.env.DATACITE_API_KEY,
    
    /**
     * Repository-ID für die DOI-Registrierung
     */
    repositoryId: process.env.DATACITE_REPOSITORY_ID,
    
    /**
     * Präfix für DOIs
     */
    doiPrefix: process.env.DATACITE_DOI_PREFIX || '10.5281',
    
    /**
     * Timeout für API-Anfragen in Millisekunden
     */
    timeout: parseInt(process.env.DATACITE_API_TIMEOUT) || 10000,
    
    /**
     * Maximale Anzahl von Wiederholungsversuchen bei Fehlern
     */
    maxRetries: parseInt(process.env.DATACITE_API_MAX_RETRIES) || 3
  },
  
  /**
   * Konfiguration für die Crossref-Integration
   */
  crossref: {
    /**
     * API-Basis-URL
     */
    apiBaseUrl: process.env.CROSSREF_API_URL || 'https://api.crossref.org',
    
    /**
     * E-Mail-Adresse für die Authentifizierung (für bessere Rate Limits)
     */
    email: process.env.CROSSREF_EMAIL,
    
    /**
     * API-Token für die Authentifizierung
     */
    token: process.env.CROSSREF_API_TOKEN,
    
    /**
     * Timeout für API-Anfragen in Millisekunden
     */
    timeout: parseInt(process.env.CROSSREF_API_TIMEOUT) || 10000,
    
    /**
     * Maximale Anzahl von Wiederholungsversuchen bei Fehlern
     */
    maxRetries: parseInt(process.env.CROSSREF_API_MAX_RETRIES) || 3
  },
  
  /**
   * Konfiguration für die Patent-Integration
   */
  patent: {
    /**
     * Direkte Verknüpfung von Patent-IDs mit NFTs aktivieren
     */
    directNftLinking: process.env.PATENT_DIRECT_NFT_LINKING !== 'false',
    
    /**
     * Patentämter-Konfiguration
     */
    offices: {
      uspto: {
        baseUrl: process.env.USPTO_API_URL || 'https://developer.uspto.gov/ibd-api/v1',
        apiKey: process.env.USPTO_API_KEY
      },
      epo: {
        baseUrl: process.env.EPO_API_URL || 'https://data.epo.org/api/v1',
        apiKey: process.env.EPO_API_KEY
      },
      wipo: {
        baseUrl: process.env.WIPO_API_URL || 'https://patentscope.wipo.int/api',
        apiKey: process.env.WIPO_API_KEY
      }
    }
  },
  
  /**
   * Konfiguration für die NFT-Prägung
   */
  nft: {
    /**
     * Standardmäßig Metadaten auf IPFS speichern
     */
    storeOnIpfs: process.env.DOI_NFT_STORE_ON_IPFS !== 'false',
    
    /**
     * Standardmäßig Torrents für Publikationen erstellen
     */
    createTorrent: process.env.DOI_NFT_CREATE_TORRENT === 'true',
    
    /**
     * Blockchain-Netzwerk für die NFT-Prägung
     */
    network: process.env.DOI_NFT_NETWORK || 'polkadot',
    
    /**
     * Smart-Contract-Adresse für die NFT-Prägung
     */
    contractAddress: process.env.DOI_NFT_CONTRACT_ADDRESS,
    
    /**
     * Verwende den erweiterten NFT-Contract
     */
    useEnhancedContract: process.env.DOI_NFT_USE_ENHANCED_CONTRACT !== 'false',
    
    /**
     * Gas-Limit für Transaktionen
     */
    gasLimit: parseInt(process.env.DOI_NFT_GAS_LIMIT) || 3000000,
    
    /**
     * Maximale Gas-Gebühr
     */
    maxFeePerGas: process.env.DOI_NFT_MAX_FEE_PER_GAS || '50000000000'
  },
  
  /**
   * Konfiguration für die Metadaten
   */
  metadata: {
    /**
     * Basis-URL für Bilder
     */
    imageBaseUrl: process.env.DOI_NFT_IMAGE_BASE_URL || 'https://desci-scholar.org/api/paper-image',
    
    /**
     * Schema-URL für Metadaten
     */
    schemaUrl: process.env.DOI_NFT_SCHEMA_URL || 'https://desci-scholar.org/schemas/doi-nft/v1',
    
    /**
     * Standardlizenz für Metadaten
     */
    defaultLicense: process.env.DOI_NFT_DEFAULT_LICENSE || 'CC-BY-4.0'
  },
  
  /**
   * Konfiguration für die Speicherung
   */
  storage: {
    /**
     * Speichere DOI-zu-NFT-Zuordnungen in der Datenbank
     */
    storeMapping: process.env.DOI_NFT_STORE_MAPPING !== 'false',
    
    /**
     * Speichere Patent-ID-zu-NFT-Zuordnungen in der Datenbank
     */
    storePatentMapping: process.env.PATENT_NFT_STORE_MAPPING !== 'false',
    
    /**
     * Cache-Größe für DOI-Metadaten
     */
    metadataCacheSize: parseInt(process.env.DOI_NFT_METADATA_CACHE_SIZE) || 1000,
    
    /**
     * Cache-TTL für DOI-Metadaten in Millisekunden
     */
    metadataCacheTtl: parseInt(process.env.DOI_NFT_METADATA_CACHE_TTL) || 3600000 // 1 Stunde
  }
};

export default doiNftConfig;