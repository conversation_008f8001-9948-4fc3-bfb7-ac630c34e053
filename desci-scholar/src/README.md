# DeSci-Scholar

Eine dezentrale Plattform für wissenschaftliche Publikationen mit robustem Speichermanagement, umfassender Statistikerfassung und fortschrittlicher Suche.

## Übersicht

DeSci-Scholar ist eine umfassende Plattform für die Veröffentlichung, Verwaltung und Verbreitung wissenschaftlicher Publikationen, die durch dezentrale Technologien gestützt wird. Die Plattform ermöglicht:

- Protokolloptimierte Speicherung wissenschaftlicher Inhalte (IPFS für Metadaten, BitTorrent für große Datensätze)
- Umfassende Such- und Entdeckungsfunktionen für wissenschaftliche Arbeiten
- Erfassung und Analyse detaillierter Nutzungsstatistiken
- Benutzerauthentifizierung und Rechteverwaltung
- REST-API für die Interaktion mit allen Systemkomponenten

## Architektur

Die DeSci-Scholar-Plattform besteht aus mehreren Kernkomponenten:

### Speichermodule

- **EnhancedStorageService**: Hybrid-Speicherlösung, die IPFS und BitTorrent kombiniert
- **MetadataManager**: Optimiert für Metadaten-Speicherung in IPFS mit Versionierung
- **FileDistributionManager**: Verantwortlich für effiziente Verteilung großer Forschungsdatensätze

### Controller

- **PublicationController**: Zentrale Komponente für die Verwaltung wissenschaftlicher Publikationen
- **AuthService**: Benutzerauthentifizierung und -autorisierung

### Datenbank und Suche

- **DatabaseService**: Datenbankverwaltung für strukturierte Daten
- **SearchService**: Spezielle Suchfunktionalität für wissenschaftliche Inhalte

### Statistik und Analyse

- **StatsService**: Erfassung und Analyse von Plattformnutzung und Publikationsmetriken
  - Nutzeraktivität
  - Download-Statistiken
  - Impact-Scores
  - Trend-Analysen
  - Geografische Verteilung

### API-Schnittstellen

- **Publication API**: Verwaltung wissenschaftlicher Publikationen
- **Auth API**: Benutzerkonten und Anmeldung
- **Search API**: Suche und Entdeckung
- **Storage API**: Dateioperationen und Speichermanagement
- **Stats API**: Statistik- und Analysefunktionen

## Beispielanwendungen

Im Verzeichnis `/examples` finden Sie mehrere Demonstrationsanwendungen, die die Kernfunktionalitäten veranschaulichen:

- `publication-storage-integration.js`: Integration zwischen Publikationen und Speicherlösungen
- `enhanced-storage-example.js`: Nutzung des hybriden Speichersystems
- `stats-example.js`: Erfassung und Analyse von Plattformstatistiken
- `publication-stats-integration.js`: Integration zwischen Publikationsmanagement und Statistiken
- `controller-database-example.js`: Integration zwischen Controllern und Datenbank
- Weitere Beispiele für spezifische Komponenten

## Installation und Einrichtung

### Voraussetzungen

- Node.js (>= 14.x)
- MongoDB (>= 4.x)
- Optional: IPFS-Node
- Optional: BitTorrent-Client für erweiterte Funktionen

### Installationsschritte

1. Klonen Sie das Repository:
   ```bash
   git clone https://github.com/desci-labs/desci-scholar.git
   cd desci-scholar
   ```

2. Installieren Sie die Abhängigkeiten:
   ```bash
   npm install
   ```

3. Konfigurieren Sie die Umgebungsvariablen:
   ```bash
   cp .env.example .env
   # Bearbeiten Sie .env mit Ihren eigenen Werten
   ```

4. Starten Sie den Server:
   ```bash
   npm start
   ```

## API-Nutzung

### Publikationen

```javascript
// Neue Publikation erstellen
POST /api/publications
{
  "title": "Quantenmechanik und ihre Auswirkungen",
  "authors": [
    { "id": "author1", "name": "Dr. Beispiel", "affiliation": "Universität Beispiel" }
  ],
  "abstract": "Diese Studie untersucht...",
  "keywords": ["Quantenmechanik", "Physik"],
  "license": "CC-BY-4.0"
}

// Publikation abrufen
GET /api/publications/:id
```

### Statistiken

```javascript
// Gesamtstatistiken abrufen
GET /api/stats/publications

// Downloadstatistiken mit Zeitraum
GET /api/stats/downloads?start=2023-01-01&end=2023-12-31&groupBy=month

// Trend-Publikationen abrufen
GET /api/stats/trending?period=30&limit=10

// Detaillierte Statistiken für eine Publikation
GET /api/stats/publication/:id
```

### Suche

```javascript
// Suche nach Publikationen
GET /api/search/publications?query=quantenphysik&authors=Einstein&from=2020-01-01
```

## Projektstruktur

```
desci-scholar/
├── src/
│   ├── api/             # API-Endpunkte
│   ├── auth/            # Authentifizierung und Autorisierung
│   ├── database/        # Datenbankzugriff
│   ├── examples/        # Beispielanwendungen
│   ├── publications/    # Publikationsmanagement
│   ├── search/          # Suchfunktionalität
│   ├── stats/           # Statistik und Analyse
│   │   ├── bittorrent/  # BitTorrent-Implementierung
│   │   └── ipfs/        # IPFS-Implementierung
│   └── server.js        # Hauptserver-Einstiegspunkt
├── test/                # Tests
├── .env                 # Umgebungsvariablen
└── package.json         # Projektabhängigkeiten
```

## Weiterentwicklung

Die Plattform ist für zukünftige Erweiterungen konzipiert, einschließlich:

- Integration mit dezentralen Indexierungsdiensten
- KI-gestützte Literaturempfehlungen
- Erweiterte Peer-Review-Funktionen
- Interoperabilität mit anderen wissenschaftlichen Plattformen

## Lizenz

Dieses Projekt ist unter der MIT-Lizenz lizenziert. 