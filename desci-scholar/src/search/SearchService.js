/**
 * @fileoverview Suchservice für DeSci-Scholar
 * 
 * Dieser Service implementiert die Suchfunktionalität für wissenschaftliche Publikationen.
 * Er unterstützt Volltext-, Metadaten- und erweiterte Suche mit Filterung und Facettierung.
 */

import DatabaseService from '../db/DatabaseService.js';
import { normalizeText, extractKeywords, calculateTextSimilarity, textContains } from '../utils/text.js';

/**
 * Service für die Suche in wissenschaftlichen Publikationen
 */
export class SearchService {
  /**
   * Erstellt eine neue Instanz des SearchService
   * @param {Object} options - Konfigurationsoptionen
   */
  constructor(options = {}) {
    this.options = {
      maxResults: options.maxResults || 100,
      cacheSize: options.cacheSize || 1000,
      ...options
    };
    
    this.dbService = new DatabaseService();
    this.searchCache = new Map();
    this.searchStats = {
      totalSearches: 0,
      totalResults: 0,
      searchesByType: {},
      popularTerms: {},
      averageResults: 0
    };
    
    console.log('SearchService initialisiert');
  }
  
  /**
   * Initialisiert den SearchService
   * @returns {Promise<boolean>} Erfolg der Initialisierung
   */
  async initialize() {
    try {
      await this.dbService.initialize();
      console.log('SearchService erfolgreich initialisiert');
      return true;
    } catch (error) {
      console.error('Fehler bei der Initialisierung des SearchService:', error);
      return false;
    }
  }
  
  /**
   * Führt eine einfache Textsuche durch
   * @param {string} query - Suchbegriff
   * @param {Object} options - Suchoptionen
   * @returns {Promise<Object>} Suchergebnisse
   */
  async searchText(query, options = {}) {
    try {
      if (!query || query.trim().length === 0) {
        return { results: [], total: 0, facets: {} };
      }
      
      const normalizedQuery = normalizeText(query);
      const keywords = extractKeywords(normalizedQuery);
      
      // Cache-Schlüssel erstellen
      const cacheKey = `text:${normalizedQuery}:${JSON.stringify(options)}`;
      
      // Prüfe Cache
      if (this.searchCache.has(cacheKey)) {
        console.log('Suchergebnis aus Cache zurückgegeben');
        return this.searchCache.get(cacheKey);
      }
      
      // Suche in der Datenbank
      const collection = this.dbService.getCollection('publications');
      
      // Erstelle Suchquery
      const searchQuery = {
        $or: [
          { title: { $regex: normalizedQuery, $options: 'i' } },
          { abstract: { $regex: normalizedQuery, $options: 'i' } },
          { content: { $regex: normalizedQuery, $options: 'i' } },
          { keywords: { $in: keywords } }
        ]
      };
      
      // Führe Suche aus
      const results = await collection
        .find(searchQuery)
        .limit(this.options.maxResults)
        .toArray();
      
      // Berechne Relevanz-Scores
      const scoredResults = this._calculateRelevanceScores(results, normalizedQuery, keywords);
      
      // Sortiere nach Relevanz
      scoredResults.sort((a, b) => b.relevanceScore - a.relevanceScore);
      
      // Generiere Facetten
      const facets = this._generateFacets(scoredResults);
      
      const searchResult = {
        results: scoredResults,
        total: scoredResults.length,
        facets,
        query: normalizedQuery
      };
      
      // Speichere im Cache
      this._addToCache(cacheKey, searchResult);
      
      // Aktualisiere Statistiken
      this._updateSearchStats({ query });
      
      return searchResult;
    } catch (error) {
      console.error('Fehler bei der Textsuche:', error);
      return { results: [], total: 0, facets: {}, error: error.message };
    }
  }
  
  /**
   * Berechnet Relevanz-Scores für Suchergebnisse
   * @param {Array} results - Suchergebnisse
   * @param {string} query - Suchbegriff
   * @param {Array} keywords - Extrahierte Keywords
   * @returns {Array} Ergebnisse mit Relevanz-Scores
   * @private
   */
  _calculateRelevanceScores(results, query, keywords) {
    return results.map(result => {
      let score = 0;
      
      // Score für Titel-Übereinstimmung
      if (result.title && textContains(result.title, query)) {
        score += 10;
      }
      
      // Score für Abstract-Übereinstimmung
      if (result.abstract && textContains(result.abstract, query)) {
        score += 5;
      }
      
      // Score für Keyword-Übereinstimmungen
      if (result.keywords && Array.isArray(result.keywords)) {
        const matchingKeywords = result.keywords.filter(keyword => 
          keywords.includes(keyword.toLowerCase())
        );
        score += matchingKeywords.length * 3;
      }
      
      // Score für Textähnlichkeit
      if (result.abstract) {
        const similarity = calculateTextSimilarity(query, result.abstract);
        score += similarity * 2;
      }
      
      return {
        ...result,
        relevanceScore: score
      };
    });
  }
  
  /**
   * Generiert Facetten aus den Suchergebnissen
   * @param {Array} results - Suchergebnisse
   * @returns {Object} Facetten
   * @private
   */
  _generateFacets(results) {
    if (!results || !Array.isArray(results)) {
      return {
        authors: [],
        keywords: [],
        years: []
      };
    }
    
    const facets = {
      authors: {},
      keywords: {},
      years: {}
    };
    
    // Facetten aus den Ergebnissen extrahieren
    results.forEach(result => {
      // Autoren
      if (result.metadata?.authors && Array.isArray(result.metadata.authors)) {
        result.metadata.authors.forEach(author => {
          if (author && typeof author === 'string') {
            facets.authors[author] = (facets.authors[author] || 0) + 1;
          }
        });
      }
      
      // Keywords
      if (result.keywords && Array.isArray(result.keywords)) {
        result.keywords.forEach(keyword => {
          if (keyword && typeof keyword === 'string') {
            facets.keywords[keyword] = (facets.keywords[keyword] || 0) + 1;
          }
        });
      }
      
      // Jahre
      if (result.metadata?.publishedDate) {
        try {
          const date = new Date(result.metadata.publishedDate);
          if (!isNaN(date.getTime())) {
            const year = date.getFullYear();
            facets.years[year] = (facets.years[year] || 0) + 1;
          }
        } catch (error) {
          console.warn('Ungültiges Datum in Publikation:', result.metadata.publishedDate);
        }
      }
    });
    
    // Sortierte Arrays der häufigsten Facetten zurückgeben
    return {
      authors: Object.entries(facets.authors)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 20)
        .map(([name, count]) => ({ name, count })),
      
      keywords: Object.entries(facets.keywords)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 20)
        .map(([name, count]) => ({ name, count })),
      
      years: Object.entries(facets.years)
        .sort((a, b) => b[0] - a[0])
        .map(([year, count]) => ({ year: parseInt(year, 10), count }))
    };
  }
  
  /**
   * Fügt ein Ergebnis zum Cache hinzu
   * @param {string} key - Cache-Schlüssel
   * @param {Object} result - Zu cachendes Ergebnis
   * @private
   */
  _addToCache(key, result) {
    // Entferne älteste Einträge, wenn Cache voll ist
    if (this.searchCache.size >= this.options.cacheSize) {
      const firstKey = this.searchCache.keys().next().value;
      this.searchCache.delete(firstKey);
    }
    
    this.searchCache.set(key, result);
  }
  
  /**
   * Aktualisiert die Suchstatistiken
   * @param {Object} params - Suchparameter
   * @private
   */
  _updateSearchStats(params) {
    if (!params) return;
    
    this.searchStats.totalSearches++;
    
    // Zähle Suchen nach Typ
    let searchType = 'other';
    if (params.query && params.query.trim().length > 0) {
      searchType = 'query';
    } else if (params.authors && params.authors.length > 0) {
      searchType = 'authors';
    } else if (params.keywords && params.keywords.length > 0) {
      searchType = 'keywords';
    }
    
    this.searchStats.searchesByType[searchType] = (this.searchStats.searchesByType[searchType] || 0) + 1;
    
    // Zähle beliebte Suchbegriffe
    if (params.query && typeof params.query === 'string') {
      const normalizedQuery = normalizeText(params.query);
      if (normalizedQuery) {
        this.searchStats.popularTerms[normalizedQuery] = (this.searchStats.popularTerms[normalizedQuery] || 0) + 1;
      }
    }
  }
  
  /**
   * Gibt die aktuellen Suchstatistiken zurück
   * @returns {Object} Suchstatistiken
   */
  getSearchStats() {
    // Berechne die durchschnittliche Anzahl von Ergebnissen
    if (this.searchStats.totalSearches > 0 && this.searchStats.totalResults > 0) {
      this.searchStats.averageResults = this.searchStats.totalResults / this.searchStats.totalSearches;
    } else {
      this.searchStats.averageResults = 0;
    }
    
    // Top-Suchbegriffe extrahieren
    const topTerms = Object.entries(this.searchStats.popularTerms || {})
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([term, count]) => ({ term, count }));
    
    return {
      ...this.searchStats,
      topTerms
    };
  }
  
  /**
   * Leert den Suchcache
   */
  clearCache() {
    this.searchCache.clear();
    console.log('SearchService: Suchcache wurde geleert');
  }
}

export default SearchService;
