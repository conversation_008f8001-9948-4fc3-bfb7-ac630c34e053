import DataCiteService from '../api/integrationss/DataCiteService.js';
import CrossrefService from '../api/integrationss/CrossrefService.js';
import { logger } from '../utils/logger.js';

/**
 * Controller für die Verwaltung von DOIs über verschiedene Dienste
 */
export class DOIController {
  /**
   * Erstellt eine neue Instanz des DOIControllers
   * @param {Object} options - Konfigurationsoptionen
   * @param {Object} options.dataCiteConfig - Konfiguration für DataCite
   * @param {Object} options.crossrefConfig - Konfiguration für Crossref
   * @param {string} options.defaultService - Standarddienst ('datacite' oder 'crossref')
   */
  constructor(options = {}) {
    const {
      dataCiteConfig,
      crossrefConfig,
      defaultService = 'datacite'
    } = options;
    
    // DOI-Services initialisieren
    this.dataCiteService = dataCiteConfig ? new DataCiteService(dataCiteConfig) : null;
    this.crossrefService = crossrefConfig ? new CrossrefService(crossrefConfig) : null;
    
    // Standarddienst festlegen
    this.defaultService = defaultService.toLowerCase();
    
    // Prüfen, ob mindestens ein Dienst konfiguriert ist
    if (!this.dataCiteService && !this.crossrefService) {
      logger.warn('DOIController: Kein DOI-Dienst konfiguriert. DOI-Funktionalität wird nicht verfügbar sein.');
    }
    
    // Initialisieren
    this.initialize();
  }
  
  /**
   * Initialisiert die DOI-Dienste
   */
  async initialize() {
    try {
      // DataCite initialisieren
      if (this.dataCiteService) {
        const dataCiteInitialized = await this.dataCiteService.initialize();
        if (dataCiteInitialized) {
          logger.info('DOIController: DataCite-Service erfolgreich initialisiert');
        } else {
          logger.warn('DOIController: DataCite-Service konnte nicht initialisiert werden');
        }
      }
      
      // Crossref initialisieren
      if (this.crossrefService) {
        const crossrefInitialized = await this.crossrefService.initialize();
        if (crossrefInitialized) {
          logger.info('DOIController: Crossref-Service erfolgreich initialisiert');
        } else {
          logger.warn('DOIController: Crossref-Service konnte nicht initialisiert werden');
        }
      }
    } catch (error) {
      logger.error('DOIController: Fehler bei der Initialisierung', {
        error: error.message,
        stack: error.stack
      });
    }
  }
  
  /**
   * Erstellt einen neuen DOI
   * @param {Object} metadata - Metadaten für den DOI
   * @param {Object} options - Optionen für die DOI-Erstellung
   * @param {string} options.service - Zu verwendender Dienst ('datacite' oder 'crossref')
   * @param {string} options.suffix - Benutzerdefiniertes DOI-Suffix
   * @returns {Promise<Object>} Ergebnis der DOI-Erstellung
   */
  async createDOI(metadata, options = {}) {
    const service = options.service || this.defaultService;
    
    try {
      logger.info('DOIController: Erstelle neuen DOI', {
        service,
        title: metadata.title
      });
      
      // Dienst auswählen
      if (service === 'datacite') {
        if (!this.dataCiteService) {
          throw new Error('DataCite-Service ist nicht konfiguriert');
        }
        return await this.dataCiteService.createDOI(metadata, options);
      } else if (service === 'crossref') {
        if (!this.crossrefService) {
          throw new Error('Crossref-Service ist nicht konfiguriert');
        }
        return await this.crossrefService.createDOI(metadata, options);
      } else {
        throw new Error(`Unbekannter DOI-Dienst: ${service}`);
      }
    } catch (error) {
      logger.error('DOIController: Fehler bei der DOI-Erstellung', {
        error: error.message,
        stack: error.stack,
        service,
        metadata
      });
      
      return {
        success: false,
        error: error.message,
        service,
        metadata
      };
    }
  }
  
  /**
   * Aktualisiert einen bestehenden DOI
   * @param {string} doi - DOI, der aktualisiert werden soll
   * @param {Object} metadata - Neue Metadaten
   * @param {Object} options - Optionen für die DOI-Aktualisierung
   * @param {string} options.service - Zu verwendender Dienst ('datacite' oder 'crossref')
   * @returns {Promise<Object>} Ergebnis der DOI-Aktualisierung
   */
  async updateDOI(doi, metadata, options = {}) {
    // Dienst aus DOI-Präfix bestimmen, falls nicht angegeben
    let service = options.service;
    if (!service) {
      // DOI-Präfix extrahieren
      const prefix = doi.split('/')[0];
      
      // Dienst basierend auf Präfix bestimmen
      if (this.dataCiteService && this.dataCiteService.prefix === prefix) {
        service = 'datacite';
      } else if (this.crossrefService && this.crossrefService.prefix === prefix) {
        service = 'crossref';
      } else {
        service = this.defaultService;
      }
    }
    
    try {
      logger.info('DOIController: Aktualisiere DOI', {
        doi,
        service,
        title: metadata.title
      });
      
      // Dienst auswählen
      if (service === 'datacite') {
        if (!this.dataCiteService) {
          throw new Error('DataCite-Service ist nicht konfiguriert');
        }
        return await this.dataCiteService.updateDOI(doi, metadata);
      } else if (service === 'crossref') {
        if (!this.crossrefService) {
          throw new Error('Crossref-Service ist nicht konfiguriert');
        }
        return await this.crossrefService.updateDOI(doi, metadata);
      } else {
        throw new Error(`Unbekannter DOI-Dienst: ${service}`);
      }
    } catch (error) {
      logger.error('DOIController: Fehler bei der DOI-Aktualisierung', {
        error: error.message,
        stack: error.stack,
        doi,
        service,
        metadata
      });
      
      return {
        success: false,
        error: error.message,
        doi,
        service,
        metadata
      };
    }
  }
  
  /**
   * Ruft Metadaten für einen DOI ab
   * @param {string} doi - DOI, dessen Metadaten abgerufen werden sollen
   * @param {Object} options - Optionen für den Abruf
   * @param {string} options.service - Zu verwendender Dienst ('datacite' oder 'crossref')
   * @returns {Promise<Object>} DOI-Metadaten
   */
  async getDOI(doi, options = {}) {
    // Dienst aus DOI-Präfix bestimmen, falls nicht angegeben
    let service = options.service;
    if (!service) {
      // Versuche zuerst mit Crossref, da dieser Dienst öffentlich zugänglich ist
      if (this.crossrefService) {
        service = 'crossref';
      } else if (this.dataCiteService) {
        service = 'datacite';
      } else {
        throw new Error('Kein DOI-Dienst konfiguriert');
      }
    }
    
    try {
      logger.info('DOIController: Rufe DOI-Metadaten ab', {
        doi,
        service
      });
      
      // Dienst auswählen
      if (service === 'datacite') {
        if (!this.dataCiteService) {
          throw new Error('DataCite-Service ist nicht konfiguriert');
        }
        return await this.dataCiteService.getDOI(doi);
      } else if (service === 'crossref') {
        if (!this.crossrefService) {
          throw new Error('Crossref-Service ist nicht konfiguriert');
        }
        return await this.crossrefService.getWork(doi);
      } else {
        throw new Error(`Unbekannter DOI-Dienst: ${service}`);
      }
    } catch (error) {
      logger.error('DOIController: Fehler beim Abrufen der DOI-Metadaten', {
        error: error.message,
        stack: error.stack,
        doi,
        service
      });
      
      // Wenn der erste Versuch fehlschlägt, versuche es mit dem anderen Dienst
      if (!options.fallbackAttempted) {
        const fallbackService = service === 'crossref' ? 'datacite
