/**
 * PublicationController - Steuert die Interaktion mit Publikationen
 * Verbindet Frontend-Anfragen mit dem Publikationsmodell und dezentraler Speicherung
 */

import Publication from '../models/Publication.js';
import StorageService, { StorageProtocol } from '../storage/StorageService.js';
import { sha256Hash, toBuffer } from '../utils/buffer.js';
import DataCiteAPI from '../api/integrations/DataCiteAPI.js';
import DatabaseService from '../db/DatabaseService.js';
import PidManager from '../blockchain/identifiers/PidManager.js';
import DoiNftMetadata from '../blockchain/contracts/DoiNftMetadata.js';

/**
 * Controller für die Verwaltung wissenschaftlicher Publikationen
 */
class PublicationController {
  /**
   * Konstruktor für den PublicationController
   * @param {Object} config Konfiguration
   * @param {Object} config.storage Konfiguration für den StorageService
   * @param {Object} config.dataCite Konfiguration für die DataCite-API
   * @param {Object} config.database Konfiguration für DatabaseService
   */
  constructor(config = {}) {
    this.config = {
      storage: {
        defaultProtocol: StorageProtocol.BOTH,
        ipfs: {
          gateway: 'https://ipfs.io/ipfs/',
          pinningService: 'local'
        },
        bittorrent: {
          downloadPath: './downloads',
          dht: true
        }
      },
      dataCite: {
        apiUrl: 'https://api.datacite.org',
        testMode: true
      },
      database: {
        uri: process.env.MONGO_URI || 'mongodb://localhost:27017',
        dbName: process.env.MONGO_DB_NAME || 'desci-scholar'
      },
      ...config
    };
    
    // Initialisiere Services
    this.storageService = new StorageService(this.config.storage);
    this.dataCiteAPI = new DataCiteAPI(this.config.dataCite);
    this.dbService = new DatabaseService(this.config.database);
    this.pidManager = new PidManager();

    // Cache für kürzlich abgerufene Publikationen
    this.publicationCache = new Map();

    // Initialisierungsstatus
    this.initialized = false;
  }
  
  /**
   * Initialisiert den Controller und alle abhängigen Services
   * @returns {Promise<boolean>} Initialisierungsstatus
   */
  async initialize() {
    if (this.initialized) return true;
    
    try {
      // Initialisiere Storage-Service
      await this.storageService.initialize();
      
      // Initialisiere Datenbank-Service
      await this.dbService.initialize();
      
      // Teste Verbindung zur DataCite-API
      await this.dataCiteAPI.testConnection();
      
      this.initialized = true;
      return true;
    } catch (error) {
      console.error('Fehler bei der Initialisierung des PublicationController:', error);
      return false;
    }
  }
  
  /**
   * Stellt sicher, dass der Controller initialisiert ist
   * @throws {Error} Wenn die Initialisierung fehlschlägt
   * @private
   */
  async _ensureInitialized() {
    if (!this.initialized) {
      const success = await this.initialize();
      if (!success) {
        throw new Error('PublicationController konnte nicht initialisiert werden');
      }
    }
  }
  
  /**
   * Erstellt eine neue Publikation
   * @param {Object} publicationData Daten für die neue Publikation
   * @returns {Promise<Object>} Ergebnis der Publikationserstellung
   */
  async createPublication(publicationData) {
    await this._ensureInitialized();
    
    try {
      // Erstelle neue Publikation
      const publication = new Publication(publicationData);
      
      // Validiere die Daten
      const validation = publication.validate();
      if (!validation.valid) {
        return {
          success: false,
          errors: validation.errors,
          message: 'Ungültige Publikationsdaten'
        };
      }
      
      // Speichere die Publikation in dezentraler Speicherung
      const saveResult = await publication.save(this.storageService);
      
      if (!saveResult.success) {
        throw new Error(`Speicherfehler: ${saveResult.error}`);
      }
      
      // Speichere Metadaten in der Datenbank
      const dbSaveResult = await this.dbService.savePublication({
        ...publication.toJSON(false),
        storageIds: publication.storage
      });
      
      if (!dbSaveResult.success) {
        throw new Error(`Datenbankfehler: ${dbSaveResult.error}`);
      }
      
      // Füge die Publikation zum Cache hinzu
      this._addToCache(publication);
      
      return {
        success: true,
        publication: publication.toJSON(false), // Ohne Volltext
        id: publication.id,
        message: 'Publikation erfolgreich erstellt'
      };
    } catch (error) {
      console.error('Fehler bei createPublication:', error);
      return {
        success: false,
        message: `Fehler beim Erstellen der Publikation: ${error.message}`
      };
    }
  }
  
  /**
   * Aktualisiert eine bestehende Publikation
   * @param {string} publicationId ID der zu aktualisierenden Publikation
   * @param {Object} updates Zu aktualisierende Felder
   * @param {boolean} createNewVersion Neue Version erstellen?
   * @returns {Promise<Object>} Aktualisierungsergebnis
   */
  async updatePublication(publicationId, updates, createNewVersion = false) {
    await this._ensureInitialized();
    
    try {
      // Lade die Publikation
      const getResult = await this.getPublication(publicationId);
      if (!getResult.success) {
        return getResult; // Fehler weitergeben
      }
      
      const publication = getResult.publication;
      
      // Bei neuer Version oder veröffentlichter Publikation, erstelle neue Version
      if (createNewVersion || publication.status === 'published') {
        publication.createNewVersion(updates);
      } else {
        // Aktualisiere die Felder direkt
        Object.keys(updates).forEach(key => {
          if (publication.hasOwnProperty(key)) {
            publication[key] = updates[key];
          }
        });
      }
      
      // Validiere und speichere
      const validation = publication.validate();
      if (!validation.valid) {
        return {
          success: false,
          errors: validation.errors,
          message: 'Ungültige Publikationsdaten'
        };
      }
      
      // Speichere die aktualisierte Publikation in dezentraler Speicherung
      const saveResult = await publication.save(this.storageService);
      
      if (!saveResult.success) {
        throw new Error(`Speicherfehler: ${saveResult.error}`);
      }
      
      // Aktualisiere Metadaten in der Datenbank
      const dbUpdateResult = await this.dbService.savePublication({
        ...publication.toJSON(false),
        storageIds: publication.storage
      });
      
      if (!dbUpdateResult.success) {
        throw new Error(`Datenbankfehler: ${dbUpdateResult.error}`);
      }
      
      // Aktualisiere Cache
      this._addToCache(publication);
      
      return {
        success: true,
        publication: publication.toJSON(false),
        version: publication.version,
        message: createNewVersion ? 
          'Neue Publikationsversion erstellt' : 
          'Publikation erfolgreich aktualisiert'
      };
    } catch (error) {
      console.error('Fehler bei updatePublication:', error);
      return {
        success: false,
        message: `Fehler beim Aktualisieren der Publikation: ${error.message}`
      };
    }
  }
  
  /**
   * Ruft eine Publikation anhand ihrer ID ab
   * @param {string} publicationId ID der Publikation
   * @param {boolean} includeContent Volltext einschließen?
   * @returns {Promise<Object>} Abrufergebnis mit Publikation
   */
  async getPublication(publicationId, includeContent = false) {
    await this._ensureInitialized();
    
    try {
      // Prüfe, ob Publikation im Cache ist
      const cachedPublication = this._getFromCache(publicationId);
      if (cachedPublication) {
        return {
          success: true,
          publication: cachedPublication,
          fromCache: true
        };
      }
      
      // Suche in der Datenbank nach der Publikations-ID
      const dbResult = await this.dbService.getPublication(publicationId);
      
      if (!dbResult.success) {
        return {
          success: false,
          message: `Publikation mit ID ${publicationId} nicht gefunden`
        };
      }
      
      // Wenn wir nur Metadaten benötigen und diese in der Datenbank gefunden wurden
      if (!includeContent && dbResult.publication) {
        // Wandle Datenbankmodell in Publication-Objekt um
        const publication = new Publication({
          ...dbResult.publication,
          id: dbResult.publication.id || dbResult.publication._id.toString(),
          storage: dbResult.publication.storageIds
        });
        
        // Füge die Publikation zum Cache hinzu
        this._addToCache(publication);
        
        return {
          success: true,
          publication: publication,
          fromCache: false,
          fromDb: true
        };
      }
      
      // Wenn Volltext benötigt wird oder das Dokument nicht in der Datenbank gefunden wurde,
      // versuche es aus dem dezentralen Speicher zu laden
      const storageId = dbResult.publication?.storageIds || 
                        await this._resolveStorageId(publicationId);
      
      if (!storageId) {
        return {
          success: false,
          message: `Publikation mit ID ${publicationId} nicht gefunden`
        };
      }
      
      // Lade die Publikation aus dem dezentralen Speicher
      let publication;
      try {
        publication = await Publication.load(storageId, this.storageService);
      } catch (error) {
        return {
          success: false,
          message: `Fehler beim Laden der Publikation: ${error.message}`
        };
      }
      
      // Füge die Publikation zum Cache hinzu
      this._addToCache(publication);
      
      return {
        success: true,
        publication: includeContent ? 
          publication.toJSON(true) : 
          publication.toJSON(false),
        fromCache: false,
        fromDb: false
      };
    } catch (error) {
      console.error('Fehler bei getPublication:', error);
      return {
        success: false,
        message: `Fehler beim Abrufen der Publikation: ${error.message}`
      };
    }
  }
  
  /**
   * Gibt eine Publikation mit DOI-Registrierung frei
   * @param {string} publicationId ID der Publikation
   * @param {boolean} registerDOI DOI registrieren?
   * @returns {Promise<Object>} Ergebnis der Veröffentlichung
   */
  async publishPublication(publicationId, registerDOI = false) {
    await this._ensureInitialized();
    
    try {
      // Lade die Publikation
      const getResult = await this.getPublication(publicationId, true);
      if (!getResult.success) {
        return getResult; // Fehler weitergeben
      }
      
      const publication = getResult.publication;
      
      // Veröffentliche die Publikation
      const publishResult = await publication.publish(this.storageService);
      
      if (!publishResult.success) {
        throw new Error(`Veröffentlichungsfehler: ${publishResult.error}`);
      }
      
      // Registriere DOI, wenn gewünscht
      let doiResult = null;
      if (registerDOI) {
        if (!publication.doi) {
          // Generiere einen DOI, wenn keiner vorhanden ist
          publication.doi = await this._generateDOI(publication);
          
          // Speichere die Publikation mit dem neuen DOI
          await publication.save(this.storageService);
        }
        
        // Registriere den DOI bei DataCite
        doiResult = await this._registerDOI(publication);
      }
      
      // Aktualisiere die Datenbank mit dem veröffentlichten Status und DOI
      const dbUpdateResult = await this.dbService.savePublication({
        ...publication.toJSON(false),
        storageIds: publication.storage
      });
      
      if (!dbUpdateResult.success) {
        throw new Error(`Datenbankfehler: ${dbUpdateResult.error}`);
      }
      
      // Aktualisiere Cache
      this._addToCache(publication);
      
      return {
        success: true,
        publication: publication.toJSON(false),
        doi: publication.doi,
        doiRegistered: doiResult ? doiResult.success : false,
        message: 'Publikation erfolgreich veröffentlicht',
        doiMessage: doiResult ? doiResult.message : null
      };
    } catch (error) {
      console.error('Fehler bei publishPublication:', error);
      return {
        success: false,
        message: `Fehler beim Veröffentlichen der Publikation: ${error.message}`
      };
    }
  }
  
  /**
   * Lädt eine Datei zu einer Publikation hoch
   * @param {string} publicationId ID der Publikation
   * @param {Buffer|string} fileData Dateiinhalt
   * @param {Object} fileInfo Dateiinformationen
   * @returns {Promise<Object>} Upload-Ergebnis
   */
  async uploadPublicationFile(publicationId, fileData, fileInfo) {
    await this._ensureInitialized();
    
    try {
      // Validiere Dateieingaben
      if (!fileData || !fileInfo || !fileInfo.name) {
        return {
          success: false,
          message: 'Ungültige Dateieingaben'
        };
      }
      
      // Lade die Publikation
      const getResult = await this.getPublication(publicationId);
      if (!getResult.success) {
        return getResult; // Fehler weitergeben
      }
      
      const publication = getResult.publication;
      
      // Bereite Dateiinformationen vor
      const completeFileInfo = {
        name: fileInfo.name,
        type: fileInfo.type || this._guessMimeType(fileInfo.name),
        size: fileInfo.size || (fileData instanceof Buffer ? fileData.length : Buffer.from(fileData).length)
      };
      
      // Lade die Datei hoch
      const fileResult = await publication.saveFile(
        fileData, 
        completeFileInfo, 
        this.storageService
      );
      
      if (!fileResult.success) {
        throw new Error(`Dateispeicherfehler: ${fileResult.error}`);
      }
      
      // Aktualisiere die Datenbank mit Dateiinformationen
      const dbUpdateResult = await this.dbService.savePublication({
        ...publication.toJSON(false),
        storageIds: publication.storage,
        file: publication.file
      });
      
      if (!dbUpdateResult.success) {
        throw new Error(`Datenbankfehler: ${dbUpdateResult.error}`);
      }
      
      // Aktualisiere Cache
      this._addToCache(publication);
      
      return {
        success: true,
        file: publication.file,
        urls: fileResult.urls,
        message: 'Datei erfolgreich hochgeladen'
      };
    } catch (error) {
      console.error('Fehler bei uploadPublicationFile:', error);
      return {
        success: false,
        message: `Fehler beim Hochladen der Datei: ${error.message}`
      };
    }
  }
  
  /**
   * Lädt die Datei einer Publikation herunter
   * @param {string} publicationId ID der Publikation
   * @param {string} format Rückgabeformat ('buffer', 'text', etc.)
   * @returns {Promise<Object>} Download-Ergebnis
   */
  async downloadPublicationFile(publicationId, format = 'buffer') {
    await this._ensureInitialized();
    
    try {
      // Lade die Publikation
      const getResult = await this.getPublication(publicationId);
      if (!getResult.success) {
        return getResult; // Fehler weitergeben
      }
      
      const publication = getResult.publication;
      
      // Prüfe, ob eine Datei angehängt ist
      if (!publication.file || !publication.file.storage) {
        return {
          success: false,
          message: 'Keine Datei mit dieser Publikation verknüpft'
        };
      }
      
      // Lade die Datei
      const fileResult = await Publication.loadFile(
        publication.file.storage,
        this.storageService,
        format
      );
      
      if (!fileResult.success) {
        throw new Error(`Dateiladefehler: ${fileResult.error}`);
      }
      
      // Aktualisiere Statistiken in der Datenbank
      this.dbService.updateStats(publicationId, 'download')
        .catch(err => console.error('Fehler beim Aktualisieren der Download-Statistik:', err));
      
      return {
        success: true,
        data: fileResult.data,
        format: fileResult.format,
        filename: publication.file.name,
        fileType: publication.file.type,
        fileSize: publication.file.size
      };
    } catch (error) {
      console.error('Fehler bei downloadPublicationFile:', error);
      return {
        success: false,
        message: `Fehler beim Herunterladen der Datei: ${error.message}`
      };
    }
  }
  
  /**
   * Sucht nach Publikationen anhand verschiedener Kriterien
   * @param {Object} criteria Suchkriterien
   * @param {Array} criteria.keywords Schlüsselwörter
   * @param {Array} criteria.authors Autoren
   * @param {string} criteria.title Titel (Teil des Titels)
   * @param {string} criteria.doi DOI
   * @param {string} criteria.status Status
   * @param {Object} options Suchoptionen
   * @param {number} options.limit Maximale Anzahl Ergebnisse
   * @param {number} options.offset Offset für Paginierung
   * @returns {Promise<Object>} Suchergebnis
   */
  async searchPublications(criteria = {}, options = {}) {
    await this._ensureInitialized();
    
    try {
      // Durchsuche die Datenbank mit den angegebenen Kriterien
      const searchResult = await this.dbService.searchPublications(criteria, options);
      
      if (!searchResult.success) {
        throw new Error(`Suchfehler: ${searchResult.error}`);
      }
      
      // Konvertiere Datenbankobjekte in Publication-Objekte
      const results = searchResult.publications.map(dbPub => {
        // Erstelle ein neues Publication-Objekt aus den Datenbankdaten
        const publication = new Publication({
          ...dbPub,
          id: dbPub.id || dbPub._id.toString(),
          storage: dbPub.storageIds
        });
        
        // Füge zu Cache hinzu
        this._addToCache(publication);
        
        return publication.toJSON(false); // Ohne Volltext
      });
      
      return {
        success: true,
        results,
        total: searchResult.total,
        limit: searchResult.limit,
        offset: searchResult.offset
      };
    } catch (error) {
      console.error('Fehler bei searchPublications:', error);
      return {
        success: false,
        message: `Fehler bei der Publikationssuche: ${error.message}`
      };
    }
  }
  
  /**
   * Aktualisiert Publikationsstatistiken (Ansichten, Downloads, Zitierungen)
   * @param {string} publicationId Publikations-ID
   * @param {string} action Art der Aktion ('view', 'download', 'cite')
   * @returns {Promise<Object>} Aktualisierungsergebnis
   */
  async updateStatistics(publicationId, action) {
    await this._ensureInitialized();
    
    try {
      // Aktualisiere Statistiken in der Datenbank
      const updateResult = await this.dbService.updateStats(publicationId, action);
      
      if (!updateResult.success) {
        throw new Error(`Statistikfehler: ${updateResult.error}`);
      }
      
      return {
        success: true,
        message: `Statistiken für ${action} erfolgreich aktualisiert`
      };
    } catch (error) {
      console.error('Fehler bei updateStatistics:', error);
      return {
        success: false,
        message: `Fehler beim Aktualisieren der Statistiken: ${error.message}`
      };
    }
  }
  
  /**
   * Ruft Statistiken für eine Publikation ab
   * @param {string} publicationId Publikations-ID
   * @returns {Promise<Object>} Statistikergebnis
   */
  async getStatistics(publicationId) {
    await this._ensureInitialized();
    
    try {
      // Rufe Statistiken aus der Datenbank ab
      const statsResult = await this.dbService.getStats(publicationId);
      
      if (!statsResult.success) {
        throw new Error(`Statistikabruffehler: ${statsResult.error}`);
      }
      
      return {
        success: true,
        statistics: statsResult.stats
      };
    } catch (error) {
      console.error('Fehler bei getStatistics:', error);
      return {
        success: false,
        message: `Fehler beim Abrufen der Statistiken: ${error.message}`
      };
    }
  }
  
  /**
   * Ruft Zitationsinformationen für eine Publikation ab
   * @param {string} publicationId Publikations-ID
   * @returns {Promise<Object>} Zitationsergebnis
   */
  async getCitations(publicationId) {
    await this._ensureInitialized();
    
    try {
      // Rufe Zitationen aus der Datenbank ab
      const citationsResult = await this.dbService.getCitationsForPublication(publicationId);
      
      if (!citationsResult.success) {
        throw new Error(`Zitationsabruffehler: ${citationsResult.error}`);
      }
      
      return {
        success: true,
        citedBy: citationsResult.citedBy,
        cites: citationsResult.cites
      };
    } catch (error) {
      console.error('Fehler bei getCitations:', error);
      return {
        success: false,
        message: `Fehler beim Abrufen der Zitationen: ${error.message}`
      };
    }
  }
  
  /**
   * Erstellt einen DOI für eine Publikation
   * @param {Publication} publication Publikation
   * @returns {Promise<string>} Generierter DOI
   * @private
   */
  async _generateDOI(publication) {
    // Basisprefix von DataCite (in Produktion würde dies aus der Konfiguration kommen)
    const prefix = this.config.dataCite.prefix || '10.5072';
    
    // Generiere einen eindeutigen Suffix basierend auf der Publikations-ID
    const suffix = `desci-scholar.${publication.id}`;
    
    return `${prefix}/${suffix}`;
  }
  
  /**
   * Registriert einen DOI bei DataCite
   * @param {Publication} publication Publikation mit DOI
   * @returns {Promise<Object>} Registrierungsergebnis
   * @private
   */
  async _registerDOI(publication) {
    try {
      // Konvertiere Publikationsmetadaten in DataCite-Format
      const metadata = this._createDataCiteMetadata(publication);
      
      // Registriere DOI bei DataCite
      const result = await this.dataCiteAPI.registerDOI(publication.doi, metadata);
      
      if (result.success) {
        return {
          success: true,
          doi: publication.doi,
          message: 'DOI erfolgreich registriert'
        };
      } else {
        return {
          success: false,
          message: `DOI-Registrierungsfehler: ${result.error}`
        };
      }
    } catch (error) {
      console.error('Fehler bei der DOI-Registrierung:', error);
      return {
        success: false,
        message: `DOI-Registrierungsfehler: ${error.message}`
      };
    }
  }
  
  /**
   * Erstellt Metadaten für DataCite
   * @param {Publication} publication Publikation
   * @returns {Object} DataCite-Metadaten
   * @private
   */
  _createDataCiteMetadata(publication) {
    // Erstelle Metadaten im DataCite-Format
    const creators = publication.authors.map(author => {
      // Teile Namen in Vor- und Nachname auf
      const nameParts = author.split(' ');
      const givenName = nameParts.slice(0, -1).join(' ');
      const familyName = nameParts[nameParts.length - 1];
      
      return {
        name: author,
        nameType: 'Personal',
        givenName,
        familyName
      };
    });
    
    // Erstelle URL für die Publikation (in einer realen Anwendung würde hier eine permanente URL stehen)
    let url;
    if (publication.storage.ipfs) {
      url = `https://ipfs.io/ipfs/${publication.storage.ipfs}`;
    } else if (publication.storage.bittorrent) {
      // Für BitTorrent würde hier eine Gateway-URL stehen
      url = `https://desci-scholar.org/publications/${publication.id}`;
    } else {
      url = `https://desci-scholar.org/publications/${publication.id}`;
    }
    
    return {
      titles: [{ title: publication.title }],
      creators,
      publisher: 'DeSci Scholar',
      publicationYear: new Date(publication.publishedDate).getFullYear().toString(),
      resourceType: {
        resourceTypeGeneral: 'Text',
        resourceType: 'Journal Article'
      },
      subjects: publication.keywords.map(keyword => ({ subject: keyword })),
      descriptions: [
        {
          description: publication.abstract,
          descriptionType: 'Abstract'
        }
      ],
      version: publication.version,
      url
    };
  }
  
  /**
   * Löst eine Publikations-ID zu einer Speicher-ID auf
   * @param {string} publicationId Publikations-ID
   * @returns {Promise<Object|null>} Speicher-ID oder null
   * @private
   */
  async _resolveStorageId(publicationId) {
    try {
      // Versuche, die Speicher-IDs aus der Datenbank zu laden
      const dbResult = await this.dbService.getPublication(publicationId);
      
      if (dbResult.success && dbResult.publication && dbResult.publication.storageIds) {
        return dbResult.publication.storageIds;
      }
    } catch (error) {
      console.error('Fehler beim Auflösen der Storage-ID aus der Datenbank:', error);
    }
    
    // Fallback: Simulierte ID zurückgeben
    return {
      ipfs: `QmSimulated${publicationId}Hash`,
      bittorrent: `${sha256Hash(publicationId).substring(0, 40)}`
    };
  }
  
  /**
   * Fügt eine Publikation zum Cache hinzu
   * @param {Publication} publication Publikation
   * @private
   */
  _addToCache(publication) {
    this.publicationCache.set(publication.id, publication);
    
    // Begrenze die Größe des Caches auf 100 Einträge
    if (this.publicationCache.size > 100) {
      // Entferne den ältesten Eintrag
      const firstKey = this.publicationCache.keys().next().value;
      this.publicationCache.delete(firstKey);
    }
  }
  
  /**
   * Ruft eine Publikation aus dem Cache ab
   * @param {string} publicationId Publikations-ID
   * @returns {Publication|null} Publikation oder null
   * @private
   */
  _getFromCache(publicationId) {
    return this.publicationCache.get(publicationId) || null;
  }
  
  /**
   * Prüft, ob eine Publikation die Suchkriterien erfüllt
   * @param {Publication} publication Publikation
   * @param {Object} criteria Suchkriterien
   * @returns {boolean} Übereinstimmung
   * @private
   */
  _matchesSearchCriteria(publication, criteria) {
    // Wenn keine Kriterien angegeben sind, passt alles
    if (Object.keys(criteria).length === 0) {
      return true;
    }
    
    // Prüfe jedes Kriterium
    if (criteria.keywords && criteria.keywords.length > 0) {
      // Mindestens ein Schlüsselwort muss passen
      const hasMatchingKeyword = criteria.keywords.some(keyword =>
        publication.keywords.some(k => k.toLowerCase().includes(keyword.toLowerCase()))
      );
      
      if (!hasMatchingKeyword) {
        return false;
      }
    }
    
    if (criteria.authors && criteria.authors.length > 0) {
      // Mindestens ein Autor muss passen
      const hasMatchingAuthor = criteria.authors.some(author =>
        publication.authors.some(a => a.toLowerCase().includes(author.toLowerCase()))
      );
      
      if (!hasMatchingAuthor) {
        return false;
      }
    }
    
    if (criteria.title) {
      // Titel muss den Suchbegriff enthalten
      if (!publication.title.toLowerCase().includes(criteria.title.toLowerCase())) {
        return false;
      }
    }
    
    if (criteria.doi) {
      // DOI muss übereinstimmen oder enthalten sein
      if (!publication.doi.toLowerCase().includes(criteria.doi.toLowerCase())) {
        return false;
      }
    }
    
    if (criteria.status) {
      // Status muss genau übereinstimmen
      if (publication.status !== criteria.status) {
        return false;
      }
    }
    
    // Alle Kriterien erfüllt
    return true;
  }
  
  /**
   * Rät den MIME-Typ einer Datei anhand der Dateiendung
   * @param {string} filename Dateiname
   * @returns {string} MIME-Typ
   * @private
   */
  _guessMimeType(filename) {
    const extension = filename.split('.').pop().toLowerCase();
    
    const mimeTypes = {
      pdf: 'application/pdf',
      doc: 'application/msword',
      docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      xls: 'application/vnd.ms-excel',
      xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      ppt: 'application/vnd.ms-powerpoint',
      pptx: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      txt: 'text/plain',
      csv: 'text/csv',
      html: 'text/html',
      htm: 'text/html',
      xml: 'application/xml',
      json: 'application/json',
      zip: 'application/zip',
      rar: 'application/x-rar-compressed',
      tar: 'application/x-tar',
      gz: 'application/gzip',
      jpg: 'image/jpeg',
      jpeg: 'image/jpeg',
      png: 'image/png',
      gif: 'image/gif',
      svg: 'image/svg+xml',
      mp3: 'audio/mpeg',
      mp4: 'video/mp4',
      avi: 'video/x-msvideo',
      mov: 'video/quicktime',
      wmv: 'video/x-ms-wmv'
    };
    
    return mimeTypes[extension] || 'application/octet-stream';
  }
  
  /**
   * Beendet den Controller und alle Services
   * @returns {Promise<boolean>} Erfolg
   */
  async shutdown() {
    try {
      // Beende Storage-Service
      await this.storageService.shutdown();
      
      // Beende Datenbankverbindung
      await this.dbService.close();
      
      // Leere Cache
      this.publicationCache.clear();
      
      this.initialized = false;
      return true;
    } catch (error) {
      console.error('Fehler beim Herunterfahren des PublicationController:', error);
      return false;
    }
  }

  /**
   * Löscht eine Publikation anhand ihrer ID
   * @param {string} publicationId ID der zu löschenden Publikation
   * @returns {Promise<Object>} Lösch-Ergebnis
   */
  async deletePublication(publicationId) {
    await this._ensureInitialized();

    try {
      // Lade die Publikation
      const getResult = await this.getPublication(publicationId);
      if (!getResult.success) {
        return getResult; // Fehler weitergeben
      }

      const publication = getResult.publication;

      // Lösche die Publikation aus dem dezentralen Speicher
      const deleteResult = await publication.delete(this.storageService);
      if (!deleteResult.success) {
        throw new Error(`Speicherlöschfehler: ${deleteResult.error}`);
      }

      // Lösche die Publikation aus der Datenbank
      const dbDeleteResult = await this.dbService.deletePublication(publicationId);
      if (!dbDeleteResult.success) {
        throw new Error(`Datenbanklöschfehler: ${dbDeleteResult.error}`);
      }

      // Entferne die Publikation aus dem Cache
      this.publicationCache.delete(publicationId);

      return {
        success: true,
        message: 'Publikation erfolgreich gelöscht'
      };
    } catch (error) {
      console.error('Fehler bei deletePublication:', error);
      return {
        success: false,
        message: `Fehler beim Löschen der Publikation: ${error.message}`
      };
    }
  }

  /**
   * Erstellt ein NFT für eine veröffentlichte Publikation
   * @param {string} publicationId ID der Publikation
   * @param {Object} options Optionen für die NFT-Erstellung
   * @param {string} options.recipient Blockchain-Adresse des Empfängers
   * @param {boolean} options.extractCitations Zitationen automatisch extrahieren?
   * @param {boolean} options.extractMetrics Metriken automatisch extrahieren?
   * @param {boolean} options.requireOrcidValidation ORCID-Validierung erforderlich?
   * @returns {Promise<Object>} Ergebnis der NFT-Erstellung
   */
  async createNftForPublication(publicationId, options = {}) {
    await this._ensureInitialized();

    try {
      // Lade die Publikation mit Volltext
      const getResult = await this.getPublication(publicationId, true);
      if (!getResult.success) {
        return getResult; // Fehler weitergeben
      }

      const publication = getResult.publication;

      // Prüfe, ob die Publikation bereits veröffentlicht ist
      if (publication.status !== 'published') {
        return {
          success: false,
          message: 'Nur veröffentlichte Publikationen können als NFT erstellt werden'
        };
      }

      // Prüfe, ob die Publikation einen DOI hat
      if (!publication.doi) {
        return {
          success: false,
          message: 'Publikation benötigt einen DOI für die NFT-Erstellung'
        };
      }

      // Bereite Publikationsdaten für NFT vor
      const publicationData = {
        doi: publication.doi,
        title: publication.title,
        abstract: publication.abstract,
        authors: publication.authors.map(author => {
          // Wenn Autor ein Objekt mit name und orcid ist
          if (typeof author === 'object' && author.name) {
            return {
              name: author.name,
              orcid: author.orcid || null
            };
          }
          // Wenn Autor ein String ist
          return { name: author, orcid: null };
        }),
        publicationDate: publication.publishedDate || new Date().toISOString(),
        journal: publication.journal || null,
        version: publication.version || '1.0',
        license: publication.license || 'CC-BY-4.0',
        keywords: publication.keywords || []
      };

      // Erweitere die Optionen mit Datei-Informationen, falls vorhanden
      if (publication.file && publication.file.storage) {
        options.fileInfo = {
          name: publication.file.name,
          type: publication.file.type,
          size: publication.file.size,
          storage: publication.file.storage
        };
      }

      // Erstelle NFT über den PID-Manager
      const nftResult = await this.pidManager.convertDoiToNft(publication.doi, {
        ...options,
        // Füge zusätzliche Metadaten hinzu
        additionalMetadata: {
          storageProtocol: publication.storage ? Object.keys(publication.storage)[0] : null,
          storageId: publication.storage ? publication.storage[Object.keys(publication.storage)[0]] : null
        }
      });

      // Speichere NFT-Informationen in der Datenbank
      const dbUpdateResult = await this.dbService.savePublication({
        ...publication.toJSON(false),
        nft: {
          tokenId: nftResult.tokenId,
          ipfsCid: nftResult.ipfsCid,
          transaction: nftResult.transaction,
          created: nftResult.created,
          owner: options.recipient || options.owner
        }
      });

      if (!dbUpdateResult.success) {
        throw new Error(`Datenbankfehler: ${dbUpdateResult.error}`);
      }

      // Aktualisiere Cache
      publication.nft = {
        tokenId: nftResult.tokenId,
        ipfsCid: nftResult.ipfsCid,
        transaction: nftResult.transaction,
        created: nftResult.created,
        owner: options.recipient || options.owner
      };
      this._addToCache(publication);

      return {
        success: true,
        publication: publication.toJSON(false),
        nft: {
          tokenId: nftResult.tokenId,
          ipfsCid: nftResult.ipfsCid,
          transaction: nftResult.transaction,
          owner: options.recipient || options.owner,
          created: nftResult.created
        },
        message: 'NFT für Publikation erfolgreich erstellt'
      };
    } catch (error) {
      console.error('Fehler bei createNftForPublication:', error);
      return {
        success: false,
        message: `Fehler beim Erstellen des NFTs: ${error.message}`
      };
    }
  }

  /**
   * Ruft NFT-Informationen für eine Publikation ab
   * @param {string} publicationId ID der Publikation
   * @returns {Promise<Object>} NFT-Informationen
   */
  async getNftInfo(publicationId) {
    await this._ensureInitialized();

    try {
      // Lade die Publikation
      const getResult = await this.getPublication(publicationId);
      if (!getResult.success) {
        return getResult; // Fehler weitergeben
      }

      const publication = getResult.publication;

      // Prüfe, ob die Publikation ein NFT hat
      if (!publication.nft || !publication.nft.tokenId) {
        return {
          success: false,
          message: 'Publikation hat kein zugeordnetes NFT'
        };
      }

      // Rufe detaillierte NFT-Informationen ab
      const nftInfo = await this.pidManager.verifyDoiNft(publication.nft.tokenId);

      return {
        success: true,
        nft: {
          ...publication.nft,
          verified: nftInfo.verified,
          orcidValidated: nftInfo.orcidValidated,
          metadata: nftInfo.nftMetadata
        }
      };
    } catch (error) {
      console.error('Fehler bei getNftInfo:', error);
      return {
        success: false,
        message: `Fehler beim Abrufen der NFT-Informationen: ${error.message}`
      };
    }
  }

  /**
   * Aktualisiert die Lizenz- und Royalty-Informationen eines NFTs
   * @param {string} publicationId ID der Publikation
   * @param {Object} licenseInfo Lizenzinformationen
   * @param {Object} royaltyInfo Royalty-Informationen
   * @returns {Promise<Object>} Aktualisierungsergebnis
   */
  async updateNftLicenseAndRoyalty(publicationId, licenseInfo, royaltyInfo) {
    await this._ensureInitialized();

    try {
      // Lade die Publikation
      const getResult = await this.getPublication(publicationId);
      if (!getResult.success) {
        return getResult; // Fehler weitergeben
      }

      const publication = getResult.publication;

      // Prüfe, ob die Publikation ein NFT hat
      if (!publication.nft || !publication.nft.tokenId) {
        return {
          success: false,
          message: 'Publikation hat kein zugeordnetes NFT'
        };
      }

      // Aktualisiere Lizenz- und Royalty-Informationen
      const updateResult = await this.pidManager.setNftLicenseAndRoyalty(
        publication.nft.tokenId,
        licenseInfo,
        royaltyInfo
      );

      // Aktualisiere NFT-Informationen in der Datenbank
      const dbUpdateResult = await this.dbService.savePublication({
        ...publication.toJSON(false),
        nft: {
          ...publication.nft,
          license: licenseInfo,
          royalty: royaltyInfo
        }
      });

      if (!dbUpdateResult.success) {
        throw new Error(`Datenbankfehler: ${dbUpdateResult.error}`);
      }

      // Aktualisiere Cache
      publication.nft = {
        ...publication.nft,
        license: licenseInfo,
        royalty: royaltyInfo
      };
      this._addToCache(publication);

      return {
        success: true,
        nft: updateResult,
        message: 'NFT-Lizenz und Royalty-Informationen erfolgreich aktualisiert'
      };
    } catch (error) {
      console.error('Fehler bei updateNftLicenseAndRoyalty:', error);
      return {
        success: false,
        message: `Fehler beim Aktualisieren der NFT-Informationen: ${error.message}`
      };
    }
  }
}

export default PublicationController;
