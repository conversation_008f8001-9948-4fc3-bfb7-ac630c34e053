/**
 * @fileoverview Controller für die Integration von arXiv
 *
 * Dieser Controller bietet eine einheitliche Schnittstelle für die Interaktion mit arXiv,
 * einschließlich Suche, Import und Verarbeitung von arXiv-Publikationen.
 */

import { ArXivAPI } from '../api/integrations/ArXivAPI.js';
import { logger } from '../utils/logger.js';

/**
 * Controller für die Integration von arXiv
 */
class ArXivIntegrationController {
  /**
   * Erstellt eine neue ArXivIntegrationController-Instanz
   * @param {Object} options - Konfigurationsoptionen
   * @param {Object} options.storageService - StorageService-Instanz
   * @param {Object} options.crossrefAPI - Optional: CrossrefAPI-Instanz
   * @param {Object} options.nftService - Optional: NFTService-Instanz
   * @param {Object} options.pidManager - Optional: PidManager-Instanz für DOI-Verwaltung
   * @param {Object} options.cacheConfig - Optional: Cache-Konfiguration
   * @param {Object} options.rateLimitConfig - Optional: Rate Limiting Konfiguration
   */
  constructor(options = {}) {
    const {
      storageService,
      crossrefAPI,
      nftService,
      pidManager,
      cacheConfig = {},
      rateLimitConfig = {}
    } = options;

    // Dienste
    this.storageService = storageService;
    this.crossrefAPI = crossrefAPI;
    this.nftService = nftService;
    this.pidManager = pidManager;

    // ArXiv API initialisieren
    this.arxivAPI = new ArXivAPI({
      timeout: 15000,
      maxRetries: 3,
      maxResults: 100
    });

    // Cache-Konfiguration
    this.cache = {
      papers: new Map(),
      searches: new Map(),
      recentPapers: new Map(),
      maxItems: cacheConfig.maxItems || 1000,
      ttl: cacheConfig.ttl || 3600000 // 1 Stunde in Millisekunden
    };

    // Rate-Limiting-Konfiguration
    this.rateLimit = {
      maxRequestsPerMinute: rateLimitConfig.maxRequestsPerMinute || 30,
      timeWindow: rateLimitConfig.timeWindow || 60000, // 1 Minute in Millisekunden
      requestTimestamps: []
    };

    // Statistiken
    this.stats = {
      searches: 0,
      papersRetrieved: 0,
      papersProcessed: 0,
      doisCreated: 0,
      nftsMinted: 0,
      cacheHits: 0,
      cacheMisses: 0,
      rateLimitedRequests: 0,
      errors: 0
    };

    this.initialized = false;
  }
  
  /**
   * Initialisiert den Controller
   * @returns {Promise<boolean>} Erfolgsstatus
   */
  async initialize() {
    if (this.initialized) return true;
    
    try {
      logger.info('ArXivIntegrationController: Initialisiere');
      
      // Initialisiere die arXiv-API
      await this.arxivAPI.initialize();
      
      this.initialized = true;
      logger.info('ArXivIntegrationController: Erfolgreich initialisiert');
      
      return true;
    } catch (error) {
      logger.error('ArXivIntegrationController: Fehler bei der Initialisierung', {
        error: error.message,
        stack: error.stack
      });

      return false;
    }
  }
  
  /**
   * Validiert Suchparameter
   * @param {Object} params - Suchparameter
   * @throws {Error} Wenn die Parameter ungültig sind
   */
  validateSearchParams(params) {
    if (!params || typeof params !== 'object') {
      throw new Error('Suchparameter müssen ein Objekt sein');
    }
    
    const { query, title, author, category, id, maxResults, start, sortBy, sortOrder } = params;

    // Mindestens ein Suchkriterium muss vorhanden sein
    if (!query && !title && !author && !category && !id) {
      throw new Error('Mindestens ein Suchparameter (query, title, author, category, id) muss angegeben werden');
    }

    // Validiere maxResults
    if (maxResults !== undefined) {
      const maxResultsNum = parseInt(maxResults);
      if (isNaN(maxResultsNum) || maxResultsNum < 1 || maxResultsNum > 100) {
        throw new Error('maxResults muss eine Zahl zwischen 1 und 100 sein');
      }
    }
    
    // Validiere start
    if (start !== undefined) {
      const startNum = parseInt(start);
      if (isNaN(startNum) || startNum < 0) {
        throw new Error('start muss eine positive Zahl sein');
      }
    }
    
    // Validiere sortBy
    if (sortBy !== undefined && !['relevance', 'lastUpdated', 'submitted'].includes(sortBy)) {
      throw new Error('sortBy muss einer der folgenden Werte sein: relevance, lastUpdated, submitted');
    }
    
    // Validiere sortOrder
    if (sortOrder !== undefined && !['ascending', 'descending'].includes(sortOrder)) {
      throw new Error('sortOrder muss einer der folgenden Werte sein: ascending, descending');
    }
  }
  
  /**
   * Validiert eine arXiv-ID
   * @param {string} arxivId - arXiv-ID
   * @throws {Error} Wenn die ID ungültig ist
   */
  validateArxivId(arxivId) {
    if (!arxivId || typeof arxivId !== 'string') {
      throw new Error('arXiv-ID muss ein String sein');
    }
    
    // Einfache Validierung des arXiv-ID-Formats
    // Beispiel: 2307.12345 oder quant-ph/0701001
    const arxivIdPattern = /^(\d{4}\.\d{4,5}|[a-z-]+\/\d{7})v?\d*$/;
    if (!arxivIdPattern.test(arxivId)) {
      throw new Error(`Ungültiges arXiv-ID-Format: ${arxivId}`);
    }
  }
  
  /**
   * Validiert eine Kategorie
   * @param {string} category - Kategorie
   * @throws {Error} Wenn die Kategorie ungültig ist
   */
  validateCategory(category) {
    if (!category || typeof category !== 'string') {
      throw new Error('Kategorie muss ein String sein');
    }
    
    // Liste gültiger arXiv-Kategorien
    const validCategories = [
      'cs', 'econ', 'eess', 'math', 'physics', 'q-bio', 'q-fin', 'stat',
      'cs.AI', 'cs.CL', 'cs.CV', 'cs.LG', 'cs.NE', 'cs.RO', 'cs.SE',
      'math.AC', 'math.AG', 'math.AT', 'math.CT', 'math.FA', 'math.NT',
      'physics.acc-ph', 'physics.ao-ph', 'physics.atom-ph', 'physics.bio-ph',
      'q-bio.BM', 'q-bio.CB', 'q-bio.GN', 'q-bio.MN', 'q-bio.NC',
      'q-fin.CP', 'q-fin.EC', 'q-fin.PM', 'q-fin.RM', 'q-fin.ST',
      'stat.AP', 'stat.CO', 'stat.ME', 'stat.ML', 'stat.TH'
    ];
    
    // Überprüfe, ob die Kategorie gültig ist oder eine Hauptkategorie
    const isMainCategory = validCategories.some(c => c.split('.')[0] === category);
    const isValidCategory = validCategories.includes(category);
    
    if (!isMainCategory && !isValidCategory) {
      throw new Error(`Ungültige arXiv-Kategorie: ${category}`);
    }
  }
  
  /**
   * Prüft, ob eine Anfrage das Rate Limit überschreitet
   * @returns {boolean} True, wenn das Rate Limit überschritten wurde
   */
  checkRateLimit() {
    const now = Date.now();
    
    // Entferne alte Timestamps
    this.rateLimit.requestTimestamps = this.rateLimit.requestTimestamps.filter(
      timestamp => now - timestamp < this.rateLimit.timeWindow
    );
    
    // Prüfe, ob das Limit überschritten wurde
    if (this.rateLimit.requestTimestamps.length >= this.rateLimit.maxRequestsPerMinute) {
      this.stats.rateLimitedRequests++;
      return true;
    }
    
    // Füge den aktuellen Timestamp hinzu
    this.rateLimit.requestTimestamps.push(now);
    return false;
  }
  
  /**
   * Generiert einen Cache-Schlüssel für Suchanfragen
   * @param {Object} params - Suchparameter
   * @returns {string} Cache-Schlüssel
   */
  generateSearchCacheKey(params) {
    return JSON.stringify(params);
  }
  
  /**
   * Generiert einen Cache-Schlüssel für neueste Publikationen
   * @param {string} category - Kategorie
   * @param {number} maxResults - Maximale Anzahl von Ergebnissen
   * @returns {string} Cache-Schlüssel
   */
  generateRecentPapersCacheKey(category, maxResults) {
    return `${category}:${maxResults}`;
  }
  
  /**
   * Speichert ein Ergebnis im Cache
   * @param {Map} cacheMap - Cache-Map
   * @param {string} key - Cache-Schlüssel
   * @param {Object} data - Zu speichernde Daten
   */
  setCacheItem(cacheMap, key, data) {
    // Prüfe, ob der Cache voll ist
    if (cacheMap.size >= this.cache.maxItems) {
      // Entferne den ältesten Eintrag
      const oldestKey = cacheMap.keys().next().value;
      cacheMap.delete(oldestKey);
    }
    
    cacheMap.set(key, {
      data,
      timestamp: Date.now()
    });
  }
  
  /**
   * Ruft ein Ergebnis aus dem Cache ab
   * @param {Map} cacheMap - Cache-Map
   * @param {string} key - Cache-Schlüssel
   * @returns {Object|null} Daten aus dem Cache oder null, wenn nicht gefunden
   */
  getCacheItem(cacheMap, key) {
    const cached = cacheMap.get(key);
    if (!cached) {
      this.stats.cacheMisses++;
      return null;
    }
    
    // Prüfe, ob der Cache abgelaufen ist
    if (Date.now() - cached.timestamp > this.cache.ttl) {
      cacheMap.delete(key);
      this.stats.cacheMisses++;
      return null;
    }
    
    this.stats.cacheHits++;
    return cached.data;
  }
  
  /**
   * Sucht nach Publikationen in arXiv
   * @param {Object} params - Suchparameter
   * @returns {Promise<Object>} Suchergebnisse
   */
  async searchPapers(params = {}) {
    try {
      // Stelle sicher, dass der Controller initialisiert ist
      if (!this.initialized) {
        await this.initialize();
      }
      
      // Validiere Suchparameter
      this.validateSearchParams(params);
      
      // Prüfe Cache
      const cacheKey = this.generateSearchCacheKey(params);
      const cachedResult = this.getCacheItem(this.cache.searches, cacheKey);
      if (cachedResult) {
        logger.info('ArXivIntegrationController: Cache-Treffer für Suche', { params });
        return cachedResult;
      }
      
      // Prüfe Rate Limit
      if (this.checkRateLimit()) {
        logger.warn('ArXivIntegrationController: Rate Limit überschritten', { params });
        return {
          success: false,
          error: 'Rate Limit überschritten. Bitte versuchen Sie es später erneut.'
        };
      }
      
      this.stats.searches++;
      
      logger.info('ArXivIntegrationController: Suche nach Publikationen', params);
      
      const result = await this.arxivAPI.searchPapers(params);
      
      if (result.success) {
        this.stats.papersRetrieved += result.items.length;
        
        // Speichere Ergebnis im Cache
        this.setCacheItem(this.cache.searches, cacheKey, result);
      }
      
      return result;
    } catch (error) {
      this.stats.errors++;
      
      logger.error('ArXivIntegrationController: Fehler bei der Suche', {
        error: error.message,
        stack: error.stack,
        params
      });
      
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * Ruft Details zu einer Publikation anhand ihrer arXiv-ID ab
   * @param {string} arxivId - arXiv-ID
   * @returns {Promise<Object>} Publikationsdetails
   */
  async getPaper(arxivId) {
    try {
      // Stelle sicher, dass der Controller initialisiert ist
      if (!this.initialized) {
        await this.initialize();
      }

      // Validiere arXiv-ID
      try {
        this.validateArxivId(arxivId);
      } catch (error) {
        return {
          success: false,
          error: error.message,
          arxivId
        };
      }

      // Prüfe Cache
      const cachedResult = this.getCacheItem(this.cache.papers, arxivId);
      if (cachedResult) {
        logger.info('ArXivIntegrationController: Cache-Treffer für Paper', { arxivId });
        return cachedResult;
      }

      // Prüfe Rate Limit
      if (this.checkRateLimit()) {
        logger.warn('ArXivIntegrationController: Rate Limit überschritten', { arxivId });
        return {
          success: false,
          error: 'Rate Limit überschritten. Bitte versuchen Sie es später erneut.',
          arxivId
        };
      }
      
      logger.info('ArXivIntegrationController: Rufe Publikationsdetails ab', { arxivId });

      const result = await this.arxivAPI.getPaper(arxivId);

      if (result.success) {
        this.stats.papersRetrieved++;
        
        // Speichere Ergebnis im Cache
        this.setCacheItem(this.cache.papers, arxivId, result);
      }

      return result;
    } catch (error) {
      this.stats.errors++;

      logger.error('ArXivIntegrationController: Fehler beim Abrufen der Publikationsdetails', {
        error: error.message,
        stack: error.stack,
        arxivId
      });
      
      return {
        success: false,
        error: error.message,
        arxivId
      };
    }
  }

  /**
   * Ruft die neuesten Publikationen in einer bestimmten Kategorie ab
   * @param {string} category - Kategorie
   * @param {number} maxResults - Maximale Anzahl von Ergebnissen
   * @returns {Promise<Object>} Suchergebnisse
   */
  async getRecentPapers(category, maxResults = 10) {
    try {
      // Stelle sicher, dass der Controller initialisiert ist
      if (!this.initialized) {
        await this.initialize();
      }

      // Validiere Kategorie
      try {
        this.validateCategory(category);
      } catch (error) {
        return {
          success: false,
          error: error.message,
          category
        };
      }
      
      // Validiere maxResults
      if (isNaN(parseInt(maxResults)) || parseInt(maxResults) < 1 || parseInt(maxResults) > 100) {
        return {
          success: false,
          error: 'maxResults muss eine Zahl zwischen 1 und 100 sein',
          category
        };
      }
      
      // Prüfe Cache
      const cacheKey = this.generateRecentPapersCacheKey(category, maxResults);
      const cachedResult = this.getCacheItem(this.cache.recentPapers, cacheKey);
      if (cachedResult) {
        logger.info('ArXivIntegrationController: Cache-Treffer für neueste Publikationen', { category, maxResults });
        return cachedResult;
      }

      // Prüfe Rate Limit
      if (this.checkRateLimit()) {
        logger.warn('ArXivIntegrationController: Rate Limit überschritten', { category, maxResults });
        return {
          success: false,
          error: 'Rate Limit überschritten. Bitte versuchen Sie es später erneut.',
          category
        };
      }
      
      this.stats.searches++;

      logger.info('ArXivIntegrationController: Rufe neueste Publikationen ab', {
        category,
        maxResults
      });

      const result = await this.arxivAPI.getRecentPapers({
        category,
        maxResults
      });

      if (result.success) {
        this.stats.papersRetrieved += result.items.length;

        // Speichere Ergebnis im Cache
        this.setCacheItem(this.cache.recentPapers, cacheKey, result);
      }

      return result;
    } catch (error) {
      this.stats.errors++;

      logger.error('ArXivIntegrationController: Fehler beim Abrufen der neuesten Publikationen', {
        error: error.message,
        stack: error.stack,
        category,
        maxResults
      });

      return {
        success: false,
        error: error.message,
        category
      };
    }
  }
  
  /**
   * Verarbeitet ein arXiv-Paper: Ruft Metadaten ab und speichert Daten
   * @param {string} arxivId - arXiv-ID des Papers
   * @param {Object} options - Verarbeitungsoptionen
   * @param {boolean} options.createDOI - Ob ein DOI erstellt werden soll
   * @param {boolean} options.mintNFT - Ob ein NFT geprägt werden soll
   * @param {boolean} options.storeData - Ob das Paper gespeichert werden soll
   * @returns {Promise<Object>} Verarbeitungsergebnis
   */
  async processPaper(arxivId, options = {}) {
    // Stelle sicher, dass der Controller initialisiert ist
    if (!this.initialized) {
      await this.initialize();
    }
    
    // Validiere arXiv-ID
    try {
      this.validateArxivId(arxivId);
    } catch (error) {
      return {
        arxivId,
        success: false,
        error: error.message
      };
    }
    
    const {
      createDOI = false,
      mintNFT = false,
      storeData = true
    } = options;
    
    try {
      logger.info('ArXivIntegrationController: Verarbeite arXiv-Paper', {
        arxivId,
        options
      });
      
      // arXiv-Metadaten abrufen
      const paperResult = await this.getPaper(arxivId);
      
      if (!paperResult.success) {
        throw new Error(`Keine Daten für arXiv-ID ${arxivId} gefunden: ${paperResult.error}`);
      }
      
      const paperData = paperResult.paper;
      
      this.stats.papersProcessed++;
      
      // Ergebnisobjekt initialisieren
      const result = {
        arxivId,
        title: paperData.title,
        success: true,
        steps: {}
      };
      
      // Paper speichern, falls gewünscht
      if (storeData && this.storageService) {
        try {
          const storageResult = await this.storageService.storePaper({
            source: 'arxiv',
            sourceId: arxivId,
            title: paperData.title,
            authors: paperData.authors,
            abstract: paperData.abstract,
            publicationDate: paperData.published,
            categories: paperData.categories,
            pdfUrl: paperData.pdfUrl,
            sourceUrl: paperData.arxivUrl
          });
          
          result.steps.storage = {
            success: true,
            storageId: storageResult.id
          };
        } catch (storageError) {
          result.steps.storage = {
            success: false,
            error: storageError.message
          };
        }
      }
      
      // DOI erstellen, falls gewünscht
      if (createDOI) {
        try {
          if (!this.pidManager) {
            throw new Error('PID-Manager nicht verfügbar');
          }
          
          // Prüfen, ob bereits ein DOI existiert
          const existingDOI = await this.findExistingDOI(arxivId);
          
          if (existingDOI) {
            result.steps.doi = {
              success: true,
              doi: existingDOI,
              message: 'Bestehende DOI gefunden'
            };
          } else {
            // Publikationsdaten für DOI-Registrierung vorbereiten
            const publication = this.preparePublicationForDOI(paperData);
            
            // DOI registrieren
            const doiResult = await this.pidManager.registerDoi(publication);
            
            if (doiResult.status === 'success') {
              // DOI mit arXiv-ID verknüpfen
              await this.storageService.linkDOIToArXiv(doiResult.doi, arxivId);
              
              result.steps.doi = {
                success: true,
                doi: doiResult.doi,
                provider: doiResult.provider
              };
              
              this.stats.doisCreated++;
              
              logger.info('ArXivIntegrationController: DOI erfolgreich erstellt', {
                arxivId,
                doi: doiResult.doi
              });
            } else {
              throw new Error(`DOI-Registrierung fehlgeschlagen: ${doiResult.error || 'Unbekannter Fehler'}`);
            }
          }
        } catch (doiError) {
          result.steps.doi = {
            success: false,
            error: doiError.message
          };
          
          logger.error('ArXivIntegrationController: Fehler bei der DOI-Erstellung', {
            error: doiError.message,
            stack: doiError.stack,
            arxivId
          });
        }
      }
      
      // NFT prägen, falls gewünscht
      if (mintNFT) {
        try {
          if (!this.nftService) {
            throw new Error('NFT-Service nicht verfügbar');
          }
          
          // Prüfen, ob eine DOI vorhanden ist
          const doi = result.steps.doi?.doi || await this.findExistingDOI(arxivId);
          
          if (!doi) {
            throw new Error('Keine DOI für NFT-Prägung verfügbar. Bitte zuerst eine DOI erstellen.');
          }
          
          // NFT-Metadaten vorbereiten
          const nftMetadata = this.prepareNFTMetadata(paperData, doi);
          
          // NFT prägen
          const nftResult = await this.nftService.mintNFT(nftMetadata);
          
          result.steps.nft = {
            success: true,
            tokenId: nftResult.tokenId,
            transactionHash: nftResult.transactionHash,
            blockchainExplorer: nftResult.blockchainExplorer
          };
          
          this.stats.nftsMinted++;
          
          logger.info('ArXivIntegrationController: NFT erfolgreich geprägt', {
            arxivId,
            doi,
            tokenId: nftResult.tokenId
          });
        } catch (nftError) {
          result.steps.nft = {
            success: false,
            error: nftError.message
          };
          
          logger.error('ArXivIntegrationController: Fehler bei der NFT-Prägung', {
            error: nftError.message,
            stack: nftError.stack,
            arxivId
          });
        }
      }
      
      logger.info('ArXivIntegrationController: Paper erfolgreich verarbeitet', {
        arxivId,
        title: paperData.title
      });
      
      return result;
    } catch (error) {
      this.stats.errors++;
      
      logger.error('ArXivIntegrationController: Fehler bei der Verarbeitung des Papers', {
        error: error.message,
        stack: error.stack,
        arxivId
      });
      
      return {
        arxivId,
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * Sucht nach einer existierenden DOI für eine arXiv-ID
   * @param {string} arxivId - arXiv-ID
   * @returns {Promise<string|null>} DOI oder null, wenn keine gefunden wurde
   */
  async findExistingDOI(arxivId) {
    try {
      // 1. Zuerst in der lokalen Datenbank suchen
      if (this.storageService) {
        const storedDOI = await this.storageService.getDOIForArXiv(arxivId);
        if (storedDOI) {
          return storedDOI;
        }
      }
      
      // 2. Bei Crossref nach DOI suchen
      if (this.crossrefAPI) {
        const crossrefResult = await this.crossrefAPI.searchByExternalId('arxiv', arxivId);
        if (crossrefResult.success && crossrefResult.items.length > 0) {
          return crossrefResult.items[0].doi;
        }
      }
      
      // 3. In den arXiv-Metadaten nach DOI suchen
      const paperResult = await this.getPaper(arxivId);
      if (paperResult.success && paperResult.paper.doi) {
        return paperResult.paper.doi;
      }
      
      return null;
    } catch (error) {
      logger.error('ArXivIntegrationController: Fehler bei der DOI-Suche', {
        error: error.message,
        stack: error.stack,
        arxivId
      });
      return null;
    }
  }
  
  /**
   * Bereitet Publikationsdaten für die DOI-Registrierung vor
   * @param {Object} paperData - arXiv-Paperdaten
   * @returns {Object} Publikationsdaten für DOI-Registrierung
   */
  preparePublicationForDOI(paperData) {
    // Extrahiere das Jahr aus dem Veröffentlichungsdatum
    const publishedDate = new Date(paperData.published);
    const year = publishedDate.getFullYear();
    
    // Formatiere Autoren
    const authors = paperData.authors.map(authorName => {
      const nameParts = authorName.split(' ');
      const lastName = nameParts.pop();
      const firstName = nameParts.join(' ');
      
           return {
        firstName,
        lastName,
        name: authorName
      };
    });
    
    return {
      title: paperData.title,
      authors,
      abstract: paperData.abstract,
      year,
      type: 'preprint',
      url: paperData.arxivUrl,
      source: 'arxiv',
      sourceId: paperData.id,
      categories: paperData.categories
    };
  }
  
  /**
   * Bereitet Metadaten für die NFT-Prägung vor
   * @param {Object} paperData - arXiv-Paperdaten
   * @param {string} doi - DOI des Papers
   * @returns {Object} NFT-Metadaten
   */
  prepareNFTMetadata(paperData, doi) {
    return {
      name: paperData.title,
      description: paperData.abstract.substring(0, 500) + (paperData.abstract.length > 500 ? '...' : ''),
      image: `https://desci-scholar.org/api/paper-image/${encodeURIComponent(doi)}`,
      external_url: `https://doi.org/${doi}`,
      attributes: [
        {
          trait_type: "Source",
          value: "arXiv"
        },
        {
          trait_type: "Authors",
          value: paperData.authors.join(", ")
        },
        {
          trait_type: "DOI",
          value: doi
        },
        {
          trait_type: "arXiv ID",
          value: paperData.id
        },
        {
          trait_type: "Publication Date",
          value: new Date(paperData.published).toISOString().split('T')[0]
        },
        {
          trait_type: "Primary Category",
          value: paperData.primaryCategory || paperData.categories[0]
        }
  ''),
      image: `https://desci-scholar.org/api/paper-image/${encodeURIComponent(doi)}`,
      external_url: `https://doi.org/${doi}`,
      attributes: [
        {
          trait_type: "Source",
          value: "arXiv"
        },
        {
          trait_type: "Authors",
          value: paperData.authors.join(", ")
    ''),
      image: `https://desci-scholar.org/api/paper-image/${encodeURIComponent(doi)}`,
      external_url: `https://doi.org/${doi}`,
      attributes: [
        {
          trait_type: "Source",
          value: "arXiv"
        },
        {
          trait_type: "Authors",
          value: paperData.authors.join(", ")
        },
     },
     ]
    };
  }
}

export default ArXivIntegrationController;