import { logger } from '../utils/logger.js';
import { generateId } from '../utils/idGenerator.js';

/**
 * Controller für die Verwaltung von wissenschaftlichen Publikationen
 */
export class PaperController {
  /**
   * Erstellt eine neue PaperController-Instanz
   * @param {Object} options - Konfigurationsoptionen
   * @param {Object} options.databaseService - DatabaseService-Instanz
   * @param {Object} options.arxivAPI - ArXivAPI-Instanz
   * @param {Object} options.crossrefAPI - CrossrefAPI-Instanz
   * @param {Object} options.arxivDOIController - ArXivDOIController-Instanz
   * @param {Object} options.vectorDatabase - VectorDatabase-Instanz
   * @param {Object} options.documentProcessor - DocumentProcessor-Instanz
   * @param {Object} options.textEmbeddingService - TextEmbeddingService-Instanz
   */
  constructor(options = {}) {
    const {
      databaseService,
      arxivAPI,
      crossrefAPI,
      arxivDOIController,
      vectorDatabase,
      documentProcessor,
      textEmbeddingService
    } = options;
    
    this.databaseService = databaseService;
    this.arxivAPI = arxivAPI;
    this.crossrefAPI = crossrefAPI;
    this.arxivDOIController = arxivDOIController;
    this.vectorDatabase = vectorDatabase;
    this.documentProcessor = documentProcessor;
    this.textEmbeddingService = textEmbeddingService;
    
    this.stats = {
      papersAdded: 0,
      papersUpdated: 0,
      papersDeleted: 0,
      papersIndexed: 0,
      errors: 0
    };
  }
  
  /**
   * Initialisiert den PaperController
   * @returns {Promise<boolean>} Erfolgsstatus
   */
  async initialize() {
    try {
      logger.info('PaperController: Initialisiere');
      
      // Prüfen, ob die erforderlichen Abhängigkeiten vorhanden sind
      if (!this.databaseService) {
        logger.warn('PaperController: DatabaseService nicht verfügbar, Funktionalität wird eingeschränkt sein');
      }
      
      if (!this.arxivAPI) {
        logger.warn('PaperController: ArXivAPI nicht verfügbar, arXiv-Integration wird deaktiviert');
      }
      
      if (!this.vectorDatabase) {
        logger.warn('PaperController: VectorDatabase nicht verfügbar, Vektorsuche wird deaktiviert');
      }
      
      if (!this.textEmbeddingService) {
        logger.warn('PaperController: TextEmbeddingService nicht verfügbar, Embedding-Funktionalität wird deaktiviert');
      }
      
      logger.info('PaperController: Erfolgreich initialisiert');
      
      return true;
    } catch (error) {
      logger.error('PaperController: Fehler bei der Initialisierung', {
        error: error.message,
        stack: error.stack
      });
      
      return false;
    }
  }
  
  /**
   * Fügt ein Paper zur Datenbank hinzu
   * @param {Object} paper - Paper-Daten
   * @param {Object} options - Optionen
   * @param {boolean} options.index - Ob das Paper indiziert werden soll
   * @param {boolean} options.fetchMetadata - Ob zusätzliche Metadaten abgerufen werden sollen
   * @returns {Promise<Object>} Hinzugefügtes Paper
   */
  async addPaper(paper, options = {}) {
    try {
      const {
        index = true,
        fetchMetadata = true
      } = options;
      
      logger.info('PaperController: Füge Paper hinzu', {
        title: paper.title,
        id: paper.id,
        index,
        fetchMetadata
      });
      
      // Sicherstellen, dass das Paper eine ID hat
      if (!paper.id) {
        paper.id = generateId();
      }
      
      // Zusätzliche Metadaten abrufen, falls gewünscht
      let enrichedPaper = { ...paper };
      
      if (fetchMetadata) {
        enrichedPaper = await this._enrichPaperMetadata(enrichedPaper);
      }
      
      // Paper in der Datenbank speichern
      if (this.databaseService) {
        await this._savePaperToDatabase(enrichedPaper);
      }
      
      // Paper indizieren, falls gewünscht
      if (index && this.vectorDatabase && this.textEmbeddingService) {
        await this._indexPaper(enrichedPaper);
      }
      
      this.stats.papersAdded++;
      
      logger.info('PaperController: Paper erfolgreich hinzugefügt', {
        id: enrichedPaper.id,
        title: enrichedPaper.title
      });
      
      return {
        success: true,
        paper: enrichedPaper
      };
    } catch (error) {
      this.stats.errors++;
      
      logger.error('PaperController: Fehler beim Hinzufügen des Papers', {
        error: error.message,
        stack: error.stack,
        title: paper.title,
        id: paper.id
      });
      
      return {
        success: false,
        error: error.message,
        paper
      };
    }
  }
  
  /**
   * Reichert ein Paper mit zusätzlichen Metadaten an
   * @private
   * @param {Object} paper - Paper-Daten
   * @returns {Promise<Object>} Angereichertes Paper
   */
  async _enrichPaperMetadata(paper) {
    try {
      logger.info('PaperController: Reichere Paper mit Metadaten an', {
        id: paper.id,
        title: paper.title
      });
      
      let enrichedPaper = { ...paper };
      
      // Wenn es sich um ein arXiv-Paper handelt und kein DOI vorhanden ist
      if (paper.id.match(/^\d+\.\d+$/) && !paper.doi && this.arxivDOIController) {
        logger.info('PaperController: Suche DOI für arXiv-Paper', {
          arxivId: paper.id
        });
        
        const doiResult = await this.arxivDOIController.findDOI(paper);
        
        if (doiResult.success && doiResult.doi) {
          enrichedPaper.doi = doiResult.doi;
          enrichedPaper.doiSource = doiResult.source;
          
          logger.info('PaperController: DOI für arXiv-Paper gefunden', {
            arxivId: paper.id,
            doi: doiResult.doi,
            source: doiResult.source
          });
        }
      }
      
      // Wenn ein DOI vorhanden ist, Metadaten von Crossref abrufen
      if (enrichedPaper.doi && this.crossrefAPI) {
        logger.info('PaperController: Rufe Crossref-Metadaten ab', {
          doi: enrichedPaper.doi
        });
        
        const metadataResult = await this.crossrefAPI.getMetadata(enrichedPaper.doi);
        
        if (metadataResult.success && metadataResult.metadata) {
          // Metadaten in das Paper integrieren
          enrichedPaper = this._mergeCrossrefMetadata(enrichedPaper, metadataResult.metadata);
          
          logger.info('PaperController: Crossref-Metadaten erfolgreich integriert', {
            doi: enrichedPaper.doi
          });
        }
      }
      
      // Aktualisierungsdatum setzen
      enrichedPaper.updatedAt = new Date().toISOString();
      
      return enrichedPaper;
    } catch (error) {
      logger.error('PaperController: Fehler beim Anreichern mit Metadaten', {
        error: error.message,
        stack: error.stack,
        id: paper.id,
        title: paper.title
      });
      
      // Originalpaper zurückgeben
      return paper;
    }
  }
  
  /**
   * Führt Crossref-Metadaten mit Paper-Daten zusammen
   * @private
   * @param {Object} paper - Paper-Daten
   * @param {Object} crossrefData - Crossref-Metadaten
   * @returns {Object} Zusammengeführtes Paper
   */
  _mergeCrossrefMetadata(paper, crossrefData) {
    const mergedPaper = { ...paper };
    
    // Titel übernehmen, falls vorhanden und nicht leer
    if (crossrefData.title && crossrefData.title.length > 0) {
      mergedPaper.title = crossrefData.title[0];
    }
    
    // Autoren übernehmen, falls vorhanden
    if (crossrefData.author && crossrefData.author.length > 0) {
      mergedPaper.authors = crossrefData.author.map(author => ({
        name: `${author.given || ''} ${author.family || ''}`.trim(),
        orcid: author.ORCID,
        affiliation: author.affiliation ? author.affiliation.map(aff => aff.name).join('; ') : null
      }));
    }
    
    // Veröffentlichungsdatum übernehmen
    if (crossrefData.published) {
      const date = crossrefData.published['date-parts'] && crossrefData.published['date-parts'][0];
      if (date) {
        // Format: [Jahr, Monat, Tag]
        const year = date[0];
        const month = date[1] ? String(date[1]).padStart(2, '0') : '01';
        const day = date[2] ? String(date[2]).padStart(2, '0') : '01';
        
        mergedPaper.published = `${year}-${month}-${day}`;
      }
    }
    
    // Journal-Informationen übernehmen
    if (crossrefData['container-title'] && crossrefData['container-title'].length > 0) {
      mergedPaper.journal = {
        title: crossrefData['container-title'][0],
        issn: crossrefData.ISSN ? crossrefData.ISSN[0] : null,
        volume: crossrefData.volume,
        issue: crossrefData.issue,
        page: crossrefData.page
      };
    }
    
    // Abstract übernehmen, falls vorhanden
    if (crossrefData.abstract) {
      mergedPaper.abstract = crossrefData.abstract.replace(/<\/?[^>]+(>|$)/g, ''); // HTML-Tags entfernen
    }
    
    // Referenzen übernehmen, falls vorhanden
    if (crossrefData.reference && crossrefData.reference.length > 0) {
      mergedPaper.references = crossrefData.reference.map(ref => ({
        doi: ref.DOI,
        title: ref['article-title'],
        authors: ref.author,
        year: ref.year,
        journal: ref['journal-title']
      }));
    }
    
    // Weitere Metadaten
    mergedPaper.type = crossrefData.type;
    mergedPaper.publisher = crossrefData.publisher;
    mergedPaper.license = crossrefData.license ? crossrefData.license[0]?.URL : null;
    
    // Crossref-spezifische Metadaten
    mergedPaper.crossrefData = {
      score: crossrefData.score,
      citationCount: crossrefData['is-referenced-by-count'],
      referenceCount: crossrefData['references-count'],
      updatePolicy: crossrefData['update-policy'],
      depositedDate: crossrefData.deposited?.['date-time']
    };
    
    return mergedPaper;
  }
  
  /**
   * Speichert ein Paper in der Datenbank
   * @private
   * @param {Object} paper - Paper-Daten
   * @returns {Promise<boolean>} Erfolgsstatus
   */
  async _savePaperToDatabase(paper) {
    try {
      if (!this.databaseService) {
        return false;
      }
      
      logger.info('PaperController: Speichere Paper in der Datenbank', {
        id: paper.id,
        title: paper.title
      });
      
      // Prüfen, ob das Paper bereits existiert
      const existingPaper = await this.databaseService.query(
        'SELECT id FROM papers WHERE id = ?',
        [paper.id]
      );
      
      if (existingPaper && existingPaper.length > 0) {
        // Paper aktualisieren
        await this.databaseService.execute(
          'UPDATE papers SET title = ?, abstract = ?, authors = ?, published = ?, doi = ?, updated_at = NOW(), metadata = ? WHERE id = ?',
          [
            paper.title,
            paper.abstract || null,
            JSON.stringify(paper.authors || []),
            paper.published || null,
            paper.doi || null,
            JSON.stringify(paper),
            paper
