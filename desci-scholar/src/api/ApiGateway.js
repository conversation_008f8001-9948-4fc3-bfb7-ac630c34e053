const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const { createProxyMiddleware } = require('http-proxy-middleware');

/**
 * ApiGateway - Zentraler Zugangspunkt für alle Dienste
 * Implementiert API-Routing, Authentifizierung und Rate-Limiting
 */
class ApiGateway {
  constructor(options = {}) {
    this.options = {
      port: process.env.API_GATEWAY_PORT || 3000,
      services: {},
      ...options
    };
    
    this.app = express();
    this.configureMiddleware();
    this.configureRoutes();
  }
  
  /**
   * Konfiguriert Express-Middleware
   */
  configureMiddleware() {
    // Sicherheits-Middleware
    this.app.use(helmet());
    this.app.use(cors());
    
    // Body-Parser
    this.app.use(express.json());
    this.app.use(express.urlencoded({ extended: true }));
    
    // Logging
    this.app.use((req, res, next) => {
      console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
      next();
    });
  }
  
  /**
   * Konfiguriert API-Routen und Proxies zu Microservices
   */
  configureRoutes() {
    // API-Dokumentation
    this.app.get('/', (req, res) => {
      res.json({
        name: 'DeSci-Scholar API Gateway',
        version: '1.0.0',
        services: Object.keys(this.options.services)
      });
    });
    
    // Proxy-Routen zu Microservices
    Object.entries(this.options.services).forEach(([path, serviceConfig]) => {
      this.app.use(
        `/api/${path}`,
        createProxyMiddleware({
          target: serviceConfig.url,
          changeOrigin: true,
          pathRewrite: {
            [`^/api/${path}`]: serviceConfig.pathRewrite || ''
          }
        })
      );
    });
    
    // 404-Handler
    this.app.use((req, res) => {
      res.status(404).json({
        error: 'Not Found',
        message: `Die angeforderte Route ${req.path} existiert nicht`
      });
    });
    
    // Fehler-Handler
    this.app.use((err, req, res, next) => {
      console.error(err);
      res.status(err.status || 500).json({
        error: err.name || 'Internal Server Error',
        message: err.message || 'Ein unerwarteter Fehler ist aufgetreten'
      });
    });
  }
  
  /**
   * Registriert einen neuen Dienst
   * @param {string} path - API-Pfad
   * @param {object} config - Dienstkonfiguration
   */
  registerService(path, config) {
    this.options.services[path] = config;
    
    // Aktualisiere Routen
    this.app._router = null;
    this.configureMiddleware();
    this.configureRoutes();
  }
  
  /**
   * Startet den API-Gateway
   * @returns {Promise<object>} Server-Instanz
   */
  start() {
    return new Promise((resolve) => {
      const server = this.app.listen(this.options.port, () => {
        console.log(`API Gateway läuft auf Port ${this.options.port}`);
        resolve(server);
      });
    });
  }
}

module.exports = ApiGateway;