import express from 'express';
import DOIController from '../../controllers/DOIController.js';
import { createAuthMiddleware } from '../auth-middleware.js';
import { logger } from '../../utils/logger.js';

/**
 * Erstellt Router für DOI-Verwaltung
 * @param {Object} services - Dienst-Instanzen
 * @param {Object} services.authService - AuthService-Instanz
 * @param {Object} services.config - Konfiguration
 * @returns {Object} Express Router
 */
export function createDOIRouter(services) {
  const router = express.Router();
  const { authenticate, requirePermission } = createAuthMiddleware(services);
  
  // DOI-Controller initialisieren
  const doiController = new DOIController({
    dataCiteConfig: services.config.dataCite,
    crossrefConfig: services.config.crossref,
    defaultService: services.config.defaultDOIService || 'datacite'
  });
  
  /**
   * @route GET /api/doi/:doi
   * @desc Ruft Metadaten für einen DOI ab
   * @access Public
   */
  router.get('/:doi', async (req, res) => {
    try {
      const { doi } = req.params;
      const { service } = req.query;
      
      logger.info('DOI-Metadaten angefordert', {
        doi,
        service,
        ip: req.ip
      });
      
      const result = await doiController.getDOI(doi, { service });
      
      if (result.success) {
        res.json({
          success: true,
          doi,
          metadata: result.metadata
        });
      } else {
        res.status(404).json({
          success: false,
          message: `DOI ${doi} nicht gefunden`,
          error: result.error
        });
      }
    } catch (error) {
      logger.error('Fehler beim Abrufen von DOI-Metadaten', {
        error: error.message,
        stack: error.stack,
        doi: req.params.doi
      });
      
      res.status(500).json({
        success: false,
        message: 'Fehler beim Abrufen der DOI-Metadaten',
        error: error.message
      });
    }
  });
  
  /**
   * @route GET /api/doi/search
   * @desc Sucht nach DOIs
   * @access Public
   */
  router.get('/search', async (req, res) => {
    try {
      const { title, author, publisher, limit = 20, service } = req.query;
      
      logger.info('DOI-Suche angefordert', {
        title,
        author,
        publisher,
        limit,
        service,
        ip: req.ip
      });
      
      const result = await doiController.searchDOIs({
        title,
        author,
        publisher,
        limit: parseInt(limit)
      }, { service });
      
      if (result.success) {
        res.json({
          success: true,
          query: {
            title,
            author,
            publisher,
            limit
          },
          results: result.results,
          total: result.total
        });
      } else {
        res.status(400).json({
          success: false,
          message: 'Fehler bei der DOI-Suche',
          error: result.error
        });
      }
    } catch (error) {
      logger.error('Fehler bei der DOI-Suche', {
        error: error.message,
        stack: error.stack,
        query: req.query
      });
      
      res.status(500).json({
        success: false,
        message: 'Fehler bei der DOI-Suche',
        error: error.message
      });
    }
  });
  
  /**
   * @route POST /api/doi/publication
   * @desc Erstellt einen DOI für eine Publikation
   * @access Protected
   */
  router.post('/publication', authenticate, requirePermission('create:doi'), async (req, res) => {
    try {
      const publication = req.body;
      const { service, suffix } = req.query;
      
      // Validierung
      if (!publication.title || !publication.authors || !publication.url) {
        return res.status(400).json({
          success: false,
          message: 'Unvollständige Publikationsdaten. Titel, Autoren und URL sind erforderlich.'
        });
      }
      
      logger.info('DOI-Erstellung für Publikation angefordert', {
        title: publication.title,
        service,
        userId: req.user.id
      });
      
      const result = await doiController.createDOIForPublication(publication, {
        service,
        suffix
      });
      
      if (result.success) {
        res.status(201).json({
          success: true,
          doi: result.doi,
          url: result.url,
          message: 'DOI für Publikation erfolgreich erstellt'
        });
      } else {
