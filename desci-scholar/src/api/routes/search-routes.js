/**
 * Such-API-Routen für DeSci-Scholar
 * 
 * Definiert die REST-Endpunkte für die Suche nach wissenschaftlichen
 * Publikationen, Autoren und Schlüsselwörtern.
 */

import express from 'express';
import { query, validationResult } from 'express-validator';

/**
 * Erstellt einen Router für Such-Endpoints
 * 
 * @param {Object} services Service-Instanzen
 * @param {Object} services.searchService SearchService-Instanz
 * @param {Object} services.authService AuthService-Instanz
 * @returns {express.Router} Express-Router mit Suchrouten
 */
export function createSearchRoutes(services) {
  const { searchService, authService } = services;
  const router = express.Router();

  /**
   * Middleware für die Validierung von Anfrageparametern
   * 
   * @param {Object} req Express-Anfrage
   * @param {Object} res Express-Antwort
   * @param {Function} next Nächste Middleware aufrufen
   */
  const validateRequest = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        success: false, 
        errors: errors.array() 
      });
    }
    next();
  };

  /**
   * Middleware für optionale Authentifizierung
   * Wenn ein Token vorhanden ist, werden Benutzerdaten an req.user angehängt
   * 
   * @param {Object} req Express-Anfrage
   * @param {Object} res Express-Antwort
   * @param {Function} next Nächste Middleware aufrufen
   */
  const optionalAuthenticate = async (req, res, next) => {
    try {
      const authHeader = req.headers.authorization;
      
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        // Ohne Token weitermachen
        req.user = null;
        return next();
      }
      
      const token = authHeader.split(' ')[1];
      const userData = await authService.validateToken(token);
      
      // Benutzerdaten setzen, wenn das Token gültig ist
      req.user = userData || null;
      next();
    } catch (error) {
      // Bei Fehlern ohne Authentifizierung weitermachen
      req.user = null;
      next();
    }
  };

  // GET /api/search/publications - Publikationen suchen
  router.get('/publications', [
    optionalAuthenticate,
    query('query').optional().isString(),
    query('authors').optional().isString(),
    query('keywords').optional().isString(),
    query('startDate').optional().isDate().toDate(),
    query('endDate').optional().isDate().toDate(),
    query('sortBy').optional().isString().isIn(['title', 'date', 'relevance']),
    query('sortOrder').optional().isString().isIn(['asc', 'desc']),
    query('limit').optional().isInt({ min: 1, max: 100 }).toInt(),
    query('offset').optional().isInt({ min: 0 }).toInt(),
    query('includeFulltext').optional().isBoolean().toBoolean(),
    validateRequest
  ], async (req, res) => {
    try {
      // Anfrageparameter verarbeiten
      const {
        query: searchQuery,
        authors,
        keywords,
        startDate,
        endDate,
        sortBy = 'relevance',
        sortOrder = 'desc',
        limit = 20,
        offset = 0,
        includeFulltext = false
      } = req.query;
      
      // Autoren- und Schlüsselwörterstrings in Arrays umwandeln
      const authorsList = authors ? authors.split(',').map(a => a.trim()) : [];
      const keywordsList = keywords ? keywords.split(',').map(k => k.trim()) : [];
      
      // Datumsbereich erstellen
      const dateRange = {};
      if (startDate) dateRange.from = startDate;
      if (endDate) dateRange.to = endDate;
      
      // Suche durchführen
      const searchResults = await searchService.search({
        query: searchQuery,
        authors: authorsList,
        keywords: keywordsList,
        dateRange,
        sortBy,
        sortOrder,
        limit,
        offset,
        includeFulltext
      });
      
      // Ergebnisse zurückgeben
      res.json({
        success: true,
        data: searchResults
      });
    } catch (error) {
      console.error('Fehler bei der Publikationssuche:', error);
      res.status(500).json({
        success: false,
        message: `Fehler bei der Suche: ${error.message}`
      });
    }
  });

  // GET /api/search/semantic - Semantische Suche
  router.get('/semantic', [
    optionalAuthenticate,
    query('text').isString().notEmpty().withMessage('Suchtext ist erforderlich'),
    query('limit').optional().isInt({ min: 1, max: 50 }).toInt(),
    validateRequest
  ], async (req, res) => {
    try {
      const { text, limit = 20 } = req.query;
      
      // Semantische Suche durchführen
      const results = await searchService.semanticSearch(text, { limit });
      
      res.json({
        success: true,
        data: {
          total: results.length,
          results
        }
      });
    } catch (error) {
      console.error('Fehler bei der semantischen Suche:', error);
      res.status(500).json({
        success: false,
        message: `Fehler bei der semantischen Suche: ${error.message}`
      });
    }
  });

  // GET /api/search/authors - Nach Autoren suchen
  router.get('/authors', [
    optionalAuthenticate,
    query('name').isString().notEmpty().withMessage('Autorenname ist erforderlich'),
    query('limit').optional().isInt({ min: 1, max: 50 }).toInt(),
    validateRequest
  ], async (req, res) => {
    try {
      const { name, limit = 20 } = req.query;
      
      // Diese Funktion müsste im SearchService implementiert werden
      // Hier wird ein einfaches Beispiel gezeigt
      const authors = await searchService.searchAuthors(name, { limit });
      
      res.json({
        success: true,
        data: {
          total: authors.length,
          authors
        }
      });
    } catch (error) {
      console.error('Fehler bei der Autorensuche:', error);
      res.status(500).json({
        success: false,
        message: `Fehler bei der Autorensuche: ${error.message}`
      });
    }
  });

  // GET /api/search/keywords - Nach Schlüsselwörtern suchen
  router.get('/keywords', [
    optionalAuthenticate,
    query('term').isString().notEmpty().withMessage('Suchbegriff ist erforderlich'),
    query('limit').optional().isInt({ min: 1, max: 50 }).toInt(),
    validateRequest
  ], async (req, res) => {
    try {
      const { term, limit = 20 } = req.query;
      
      // Diese Funktion müsste im SearchService implementiert werden
      // Hier wird ein einfaches Beispiel gezeigt
      const keywords = await searchService.searchKeywords(term, { limit });
      
      res.json({
        success: true,
        data: {
          total: keywords.length,
          keywords
        }
      });
    } catch (error) {
      console.error('Fehler bei der Schlüsselwortsuche:', error);
      res.status(500).json({
        success: false,
        message: `Fehler bei der Schlüsselwortsuche: ${error.message}`
      });
    }
  });

  // GET /api/search/stats - Suchstatistiken abrufen
  router.get('/stats', [
    optionalAuthenticate,
    validateRequest
  ], async (req, res) => {
    try {
      // Prüfen, ob der Benutzer autorisiert ist (Admin oder Editor)
      const isAuthorized = req.user && 
        (req.user.roles.includes('admin') || req.user.roles.includes('editor'));
      
      if (!isAuthorized) {
        return res.status(403).json({
          success: false,
          message: 'Keine Berechtigung zum Abrufen von Suchstatistiken'
        });
      }
      
      const stats = searchService.getSearchStats();
      
      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      console.error('Fehler beim Abrufen der Suchstatistiken:', error);
      res.status(500).json({
        success: false,
        message: `Fehler beim Abrufen der Suchstatistiken: ${error.message}`
      });
    }
  });
  
  return router;
}

export default createSearchRoutes; 