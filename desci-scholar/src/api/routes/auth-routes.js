/**
 * Authentifizierungs-API-Routen für DeSci-Scholar
 * 
 * Definiert die REST-Endpunkte für Benutzerregistrierung, Anmeldung, 
 * Abmeldung, Token-Aktualisierung und Profilverwaltung.
 */

import express from 'express';
import { body, validationResult } from 'express-validator';
import { PERMISSIONS } from '..../middleware/middleware/auth/AuthService.js';
import { createAuthMiddleware } from '......./middleware/middleware/middleware/middleware/middleware/middleware/auth-middleware.js'; // Importiere createAuthMiddleware

/**
 * Erstellt einen Router für Authentifizierungs-Endpoints
 * 
 * @param {Object} services Service-Instanzen
 * @param {Object} services.authService AuthService-Instanz
 * @returns {express.Router} Express-Router mit Authentifizierungsrouten
 */
export function createAuthRoutes(services) {
  const { authService } = services;
  const router = express.Router();

  /**
   * Middleware für die Validierung von Anfragekörpern
   * 
   * @param {Object} req Express-Anfrage
   * @param {Object} res Express-Antwort
   * @param {Function} next Nächste Middleware aufrufen
   */
  const validateRequest = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        success: false, 
        errors: errors.array() 
      });
    }
    next();
  };

  // Importiere die Middleware aus auth-middleware.js statt sie zu duplizieren
  const { authenticate, requirePermission } = createAuthMiddleware(services);

  // POST /api/auth/register - Benutzer registrieren
  router.post('/register', [
    body('email').isEmail().withMessage('Gültige E-Mail-Adresse erforderlich'),
    body('password').isLength({ min: 8 }).withMessage('Passwort muss mindestens 8 Zeichen lang sein'),
    body('name').isLength({ min: 2 }).withMessage('Name muss mindestens 2 Zeichen lang sein'),
    body('institution').optional(),
    validateRequest
  ], async (req, res) => {
    try {
      const { email, password, name, institution } = req.body;
      
      const user = await authService.registerUser({
        email,
        password,
        name,
        institution
      });
      
      // Verification-E-Mail würde hier gesendet werden
      
      res.status(201).json({
        success: true,
        message: 'Benutzer erfolgreich registriert. Bitte überprüfen Sie Ihre E-Mail für den Verifizierungslink.',
        user: {
          id: user._id,
          email: user.email,
          name: user.name,
          institution: user.institution,
          roles: user.roles
        }
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error.message
      });
    }
  });

  // POST /api/auth/login - Benutzeranmeldung
  router.post('/login', [
    body('email').isEmail().withMessage('Gültige E-Mail-Adresse erforderlich'),
    body('password').notEmpty().withMessage('Passwort ist erforderlich'),
    validateRequest
  ], async (req, res) => {
    try {
      const { email, password } = req.body;
      const deviceInfo = req.headers['user-agent'] || 'unknown';
      
      const loginResult = await authService.loginUser(email, password, {
        deviceInfo,
        createRefreshToken: true
      });
      
      // Sitzungs-ID für spätere Referenz als Cookie setzen
      res.cookie('sessionId', loginResult.sessionId, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        maxAge: 24 * 60 * 60 * 1000 // 24 Stunden
      });
      
      res.json({
        success: true,
        message: 'Anmeldung erfolgreich',
        token: loginResult.token,
        refreshToken: loginResult.refreshToken,
        user: loginResult.user
      });
    } catch (error) {
      res.status(401).json({
        success: false,
        message: error.message
      });
    }
  });

  // POST /api/auth/logout - Benutzerabmeldung
  router.post('/logout', authenticate, async (req, res) => {
    try {
      const sessionId = req.cookies.sessionId;
      const { allSessions } = req.body;
      
      await authService.logoutUser(sessionId, req.user.userId, allSessions);
      
      // Cookie löschen
      res.clearCookie('sessionId');
      
      res.json({
        success: true,
        message: 'Abmeldung erfolgreich'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  });

  // POST /api/auth/refresh - Token aktualisieren
  router.post('/refresh', [
    body('refreshToken').notEmpty().withMessage('Refresh-Token ist erforderlich'),
    validateRequest
  ], async (req, res) => {
    try {
      const { refreshToken } = req.body;
      
      const refreshResult = await authService.refreshToken(refreshToken);
      
      res.json({
        success: true,
        token: refreshResult.token,
        user: refreshResult.user
      });
    } catch (error) {
      res.status(401).json({
        success: false,
        message: error.message
      });
    }
  });

  // GET /api/auth/profile - Benutzerprofil abrufen
  router.get('/profile', authenticate, async (req, res) => {
    try {
      // Da req.user nur die grundlegenden Informationen enthält,
      // können wir hier bei Bedarf zusätzliche Profilinformationen abrufen
      
      res.json({
        success: true,
        user: {
          id: req.user.userId,
          email: req.user.email,
          roles: req.user.roles,
          permissions: req.user.permissions
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  });

  // PUT /api/auth/profile - Benutzerprofil aktualisieren
  router.put('/profile', [
    authenticate,
    body('name').optional().isLength({ min: 2 }).withMessage('Name muss mindestens 2 Zeichen lang sein'),
    body('institution').optional(),
    validateRequest
  ], async (req, res) => {
    try {
      const updates = req.body;
      
      const updatedUser = await authService.updateUser(req.user.userId, updates);
      
      res.json({
        success: true,
        message: 'Profil erfolgreich aktualisiert',
        user: updatedUser
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error.message
      });
    }
  });

  // POST /api/auth/password/reset-request - Passwort-Reset anfordern
  router.post('/password/reset-request', [
    body('email').isEmail().withMessage('Gültige E-Mail-Adresse erforderlich'),
    validateRequest
  ], async (req, res) => {
    try {
      const { email } = req.body;
      
      await authService.requestPasswordReset(email);
      
      // Immer Erfolg zurückgeben, um keine Informationen über existierende E-Mails preiszugeben
      res.json({
        success: true,
        message: 'Falls die E-Mail-Adresse existiert, wurde eine Passwort-Reset-E-Mail gesendet.'
      });
    } catch (error) {
      // Auch bei Fehlern Erfolg zurückgeben
      res.json({
        success: true,
        message: 'Falls die E-Mail-Adresse existiert, wurde eine Passwort-Reset-E-Mail gesendet.'
      });
    }
  });

  // POST /api/auth/password/reset - Passwort zurücksetzen
  router.post('/password/reset', [
    body('resetToken').notEmpty().withMessage('Reset-Token ist erforderlich'),
    body('newPassword').isLength({ min: 8 }).withMessage('Neues Passwort muss mindestens 8 Zeichen lang sein'),
    validateRequest
  ], async (req, res) => {
    try {
      const { resetToken, newPassword } = req.body;
      
      await authService.resetPassword(resetToken, newPassword);
      
      res.json({
        success: true,
        message: 'Passwort erfolgreich zurückgesetzt. Bitte melden Sie sich mit Ihrem neuen Passwort an.'
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error.message
      });
    }
  });

  // GET /api/auth/verify-email/:token - E-Mail-Adresse verifizieren
  router.get('/verify-email/:token', async (req, res) => {
    try {
      const { token } = req.params;
      
      await authService.verifyEmail(token);
      
      // In einer echten Anwendung würden wir hier eine HTML-Seite zurückgeben
      res.json({
        success: true,
        message: 'E-Mail-Adresse erfolgreich verifiziert. Sie können sich jetzt anmelden.'
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error.message
      });
    }
  });

  // === Admin-Routen ===

  // GET /api/auth/users - Alle Benutzer abrufen (nur für Administratoren)
  router.get('/users', [
    authenticate,
    requirePermission(PERMISSIONS.MANAGE_USERS)
  ], async (req, res) => {
    try {
      const usersCollection = authService.databaseService.getCollection('users');
      
      // Passwörter und sensible Informationen ausschließen
      const users = await usersCollection.find(
        {}, 
        { 
          projection: { 
            password: 0, 
            resetToken: 0, 
            resetTokenExpiry: 0, 
            verificationToken: 0 
          } 
        }
      ).toArray();
      
      res.json({
        success: true,
        users
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  });

  // PUT /api/auth/users/:userId/roles - Benutzerrollen aktualisieren (nur für Administratoren)
  router.put('/users/:userId/roles', [
    authenticate,
    requirePermission(PERMISSIONS.MANAGE_USERS),
    body('roles').isArray().withMessage('Rollen müssen als Array angegeben werden'),
    validateRequest
  ], async (req, res) => {
    try {
      const { userId } = req.params;
      const { roles } = req.body;
      
      const updatedUser = await authService.updateUserRoles(userId, roles, req.user.userId);
      
      res.json({
        success: true,
        message: 'Benutzerrollen erfolgreich aktualisiert',
        user: updatedUser
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error.message
      });
    }
  });
  
  return router;
}