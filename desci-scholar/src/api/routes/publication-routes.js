/**
 * Publikations-API-Routen für DeSci-Scholar
 * 
 * Definiert REST-Endpunkte für die Verwaltung von wissenschaftlichen
 * Publikationen, einschließlich Erstellung, Aktualisierung, Versionierung,
 * DOI-Generierung und dem Peer-Review-Prozess.
 */

import express from 'express';
import { body, param, query, validationResult } from 'express-validator';
import { PERMISSIONS } from '../auth/AuthService.js';

/**
 * Erstellt einen Router für Publikations-Endpoints
 * 
 * @param {Object} options Service-Instanzen und Middleware
 * @param {Object} options.publicationController PublicationController-Instanz
 * @param {Object} options.authService AuthService-Instanz
 * @param {Object} options.authMiddleware Authentifizierungs-Middleware
 * @returns {express.Router} Express-Router mit Publikationsrouten
 */
export function createPublicationRoutes(options) {
  const { publicationController, authService, authMiddleware } = options;
  const router = express.Router();

  /**
   * Middleware zur Validierung von Anfragen
   */
  const validateRequest = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }
    next();
  };

  // === Öffentliche Routen ===

  // GET /api/publications - Publikationen durchsuchen/auflisten
  router.get('/', [
    query('query').optional().isString(),
    query('authors').optional().isString(),
    query('keywords').optional().isString(),
    query('startDate').optional().isDate().toDate(),
    query('endDate').optional().isDate().toDate(),
    query('sortBy').optional().isString().isIn(['title', 'date', 'citations']),
    query('sortOrder').optional().isString().isIn(['asc', 'desc']),
    query('limit').optional().isInt({ min: 1, max: 100 }).toInt(),
    query('offset').optional().isInt({ min: 0 }).toInt(),
    query('status').optional().isString(),
    validateRequest
  ], async (req, res) => {
    try {
      // Anfrageparameter verarbeiten
      const {
        query: searchQuery,
        authors,
        keywords,
        startDate,
        endDate,
        sortBy = 'date',
        sortOrder = 'desc',
        limit = 20,
        offset = 0,
        status = 'published'
      } = req.query;
      
      // Autoren- und Schlüsselwörterstrings in Arrays umwandeln
      const authorsList = authors ? authors.split(',').map(a => a.trim()) : [];
      const keywordsList = keywords ? keywords.split(',').map(k => k.trim()) : [];
      
      // Datumsbereich erstellen
      const dateRange = {};
      if (startDate) dateRange.from = startDate;
      if (endDate) dateRange.to = endDate;
      
      // Publikationen abrufen
      const publications = await publicationController.searchPublications({
        query: searchQuery,
        authors: authorsList,
        keywords: keywordsList,
        dateRange,
        sortBy,
        sortOrder,
        limit,
        offset,
        status
      });
      
      res.json({
        success: true,
        data: publications
      });
    } catch (error) {
      console.error('Fehler beim Abrufen der Publikationen:', error);
      res.status(500).json({
        success: false,
        message: `Fehler beim Abrufen der Publikationen: ${error.message}`
      });
    }
  });

  // GET /api/publications/:id - Detailinformationen zu einer Publikation abrufen
  router.get('/:id', [
    param('id').isString().withMessage('Ungültige Publikations-ID'),
    validateRequest
  ], async (req, res) => {
    try {
      const { id } = req.params;
      
      // Publikationsdetails abrufen
      const publication = await publicationController.getPublicationById(id);
      
      if (!publication) {
        return res.status(404).json({
          success: false,
          message: 'Publikation nicht gefunden'
        });
      }
      
      res.json({
        success: true,
        data: publication
      });
    } catch (error) {
      console.error('Fehler beim Abrufen der Publikationsdetails:', error);
      res.status(500).json({
        success: false,
        message: `Fehler beim Abrufen der Publikationsdetails: ${error.message}`
      });
    }
  });

  // GET /api/publications/:id/versions - Versionshistorie einer Publikation abrufen
  router.get('/:id/versions', [
    param('id').isString().withMessage('Ungültige Publikations-ID'),
    validateRequest
  ], async (req, res) => {
    try {
      const { id } = req.params;
      
      // Versionshistorie abrufen
      const versions = await publicationController.getPublicationVersions(id);
      
      if (!versions) {
        return res.status(404).json({
          success: false,
          message: 'Publikation nicht gefunden'
        });
      }
      
      res.json({
        success: true,
        data: versions
      });
    } catch (error) {
      console.error('Fehler beim Abrufen der Versionshistorie:', error);
      res.status(500).json({
        success: false,
        message: `Fehler beim Abrufen der Versionshistorie: ${error.message}`
      });
    }
  });

  // GET /api/publications/:id/files - Dateien einer Publikation abrufen
  router.get('/:id/files', [
    param('id').isString().withMessage('Ungültige Publikations-ID'),
    validateRequest
  ], async (req, res) => {
    try {
      const { id } = req.params;
      
      // Publikationsdateien abrufen
      const files = await publicationController.getPublicationFiles(id);
      
      if (!files) {
        return res.status(404).json({
          success: false,
          message: 'Publikation nicht gefunden'
        });
      }
      
      res.json({
        success: true,
        data: files
      });
    } catch (error) {
      console.error('Fehler beim Abrufen der Publikationsdateien:', error);
      res.status(500).json({
        success: false,
        message: `Fehler beim Abrufen der Publikationsdateien: ${error.message}`
      });
    }
  });

  // GET /api/publications/:id/citations - Zitierungen einer Publikation abrufen
  router.get('/:id/citations', [
    param('id').isString().withMessage('Ungültige Publikations-ID'),
    validateRequest
  ], async (req, res) => {
    try {
      const { id } = req.params;
      
      // Zitierungen abrufen
      const citations = await publicationController.getPublicationCitations(id);
      
      if (!citations) {
        return res.status(404).json({
          success: false,
          message: 'Publikation nicht gefunden'
        });
      }
      
      res.json({
        success: true,
        data: citations
      });
    } catch (error) {
      console.error('Fehler beim Abrufen der Zitierungen:', error);
      res.status(500).json({
        success: false,
        message: `Fehler beim Abrufen der Zitierungen: ${error.message}`
      });
    }
  });

  // GET /api/publications/:id/doi - DOI-Informationen einer Publikation abrufen
  router.get('/:id/doi', [
    param('id').isString().withMessage('Ungültige Publikations-ID'),
    validateRequest
  ], async (req, res) => {
    try {
      const { id } = req.params;
      
      // DOI-Informationen abrufen
      const doiInfo = await publicationController.getPublicationDOI(id);
      
      if (!doiInfo) {
        return res.status(404).json({
          success: false,
          message: 'DOI-Informationen nicht gefunden'
        });
      }
      
      res.json({
        success: true,
        data: doiInfo
      });
    } catch (error) {
      console.error('Fehler beim Abrufen der DOI-Informationen:', error);
      res.status(500).json({
        success: false,
        message: `Fehler beim Abrufen der DOI-Informationen: ${error.message}`
      });
    }
  });

  // === Authentifizierte Routen ===

  // POST /api/publications - Neue Publikation erstellen
  router.post('/', [
    authMiddleware.authenticate,
    authMiddleware.requirePermission(PERMISSIONS.CREATE_PUBLICATION),
    body('title').isString().notEmpty().withMessage('Titel ist erforderlich'),
    body('abstract').isString().notEmpty().withMessage('Abstract ist erforderlich'),
    body('authors').isArray().withMessage('Autoren müssen als Array angegeben werden'),
    body('authors.*.name').isString().notEmpty().withMessage('Autorenname ist erforderlich'),
    body('keywords').isArray().optional(),
    body('license').isString().optional(),
    validateRequest
  ], async (req, res) => {
    try {
      const publicationData = req.body;
      
      // Benutzer-ID des Erstellers hinzufügen
      publicationData.createdBy = req.user.userId;
      
      // Publikation erstellen
      const publication = await publicationController.createPublication(publicationData);
      
      res.status(201).json({
        success: true,
        message: 'Publikation erfolgreich erstellt',
        data: publication
      });
    } catch (error) {
      console.error('Fehler beim Erstellen der Publikation:', error);
      res.status(500).json({
        success: false,
        message: `Fehler beim Erstellen der Publikation: ${error.message}`
      });
    }
  });

  // PUT /api/publications/:id - Publikation aktualisieren
  router.put('/:id', [
    authMiddleware.authenticate,
    param('id').isString().withMessage('Ungültige Publikations-ID'),
    body('title').optional().isString().notEmpty().withMessage('Titel darf nicht leer sein'),
    body('abstract').optional().isString().notEmpty().withMessage('Abstract darf nicht leer sein'),
    body('authors').optional().isArray().withMessage('Autoren müssen als Array angegeben werden'),
    body('keywords').optional().isArray(),
    body('license').optional().isString(),
    validateRequest
  ], async (req, res) => {
    try {
      const { id } = req.params;
      const updates = req.body;
      
      // Publikation abrufen
      const publication = await publicationController.getPublicationById(id);
      
      if (!publication) {
        return res.status(404).json({
          success: false,
          message: 'Publikation nicht gefunden'
        });
      }
      
      // Berechtigungsprüfung
      const isAuthor = publication.createdBy === req.user.userId;
      const isAdmin = req.user.roles.includes('admin');
      
      if (!isAuthor && !isAdmin) {
        return res.status(403).json({
          success: false,
          message: 'Keine Berechtigung zum Aktualisieren dieser Publikation'
        });
      }
      
      // Publikation aktualisieren
      const updatedPublication = await publicationController.updatePublication(id, updates, {
        userId: req.user.userId,
        createNewVersion: updates.createNewVersion === true
      });
      
      res.json({
        success: true,
        message: 'Publikation erfolgreich aktualisiert',
        data: updatedPublication
      });
    } catch (error) {
      console.error('Fehler beim Aktualisieren der Publikation:', error);
      res.status(500).json({
        success: false,
        message: `Fehler beim Aktualisieren der Publikation: ${error.message}`
      });
    }
  });

  // POST /api/publications/:id/files - Datei zu einer Publikation hochladen
  router.post('/:id/files', [
    authMiddleware.authenticate,
    param('id').isString().withMessage('Ungültige Publikations-ID'),
    body('fileId').isString().withMessage('Datei-ID ist erforderlich'),
    body('fileType').isString().withMessage('Dateityp ist erforderlich'),
    body('description').optional().isString(),
    validateRequest
  ], async (req, res) => {
    try {
      const { id } = req.params;
      const { fileId, fileType, description } = req.body;
      
      // Publikation abrufen
      const publication = await publicationController.getPublicationById(id);
      
      if (!publication) {
        return res.status(404).json({
          success: false,
          message: 'Publikation nicht gefunden'
        });
      }
      
      // Berechtigungsprüfung
      const isAuthor = publication.createdBy === req.user.userId;
      const isAdmin = req.user.roles.includes('admin');
      
      if (!isAuthor && !isAdmin) {
        return res.status(403).json({
          success: false,
          message: 'Keine Berechtigung zum Aktualisieren dieser Publikation'
        });
      }
      
      // Datei zur Publikation hinzufügen
      const result = await publicationController.addFileToPublication(id, {
        fileId,
        fileType,
        description,
        userId: req.user.userId
      });
      
      res.status(201).json({
        success: true,
        message: 'Datei erfolgreich zur Publikation hinzugefügt',
        data: result
      });
    } catch (error) {
      console.error('Fehler beim Hinzufügen der Datei:', error);
      res.status(500).json({
        success: false,
        message: `Fehler beim Hinzufügen der Datei: ${error.message}`
      });
    }
  });

  // DELETE /api/publications/:id/files/:fileId - Datei von einer Publikation entfernen
  router.delete('/:id/files/:fileId', [
    authMiddleware.authenticate,
    param('id').isString().withMessage('Ungültige Publikations-ID'),
    param('fileId').isString().withMessage('Ungültige Datei-ID'),
    validateRequest
  ], async (req, res) => {
    try {
      const { id, fileId } = req.params;
      
      // Publikation abrufen
      const publication = await publicationController.getPublicationById(id);
      
      if (!publication) {
        return res.status(404).json({
          success: false,
          message: 'Publikation nicht gefunden'
        });
      }
      
      // Berechtigungsprüfung
      const isAuthor = publication.createdBy === req.user.userId;
      const isAdmin = req.user.roles.includes('admin');
      
      if (!isAuthor && !isAdmin) {
        return res.status(403).json({
          success: false,
          message: 'Keine Berechtigung zum Aktualisieren dieser Publikation'
        });
      }
      
      // Datei von der Publikation entfernen
      const result = await publicationController.removeFileFromPublication(id, fileId, {
        userId: req.user.userId
      });
      
      res.json({
        success: true,
        message: 'Datei erfolgreich von der Publikation entfernt',
        data: result
      });
    } catch (error) {
      console.error('Fehler beim Entfernen der Datei:', error);
      res.status(500).json({
        success: false,
        message: `Fehler beim Entfernen der Datei: ${error.message}`
      });
    }
  });

  // POST /api/publications/:id/publish - Publikation veröffentlichen
  router.post('/:id/publish', [
    authMiddleware.authenticate,
    param('id').isString().withMessage('Ungültige Publikations-ID'),
    validateRequest
  ], async (req, res) => {
    try {
      const { id } = req.params;
      
      // Publikation abrufen
      const publication = await publicationController.getPublicationById(id);
      
      if (!publication) {
        return res.status(404).json({
          success: false,
          message: 'Publikation nicht gefunden'
        });
      }
      
      // Berechtigungsprüfung
      const isAuthor = publication.createdBy === req.user.userId;
      const isAdmin = req.user.roles.includes('admin');
      
      if (!isAuthor && !isAdmin) {
        return res.status(403).json({
          success: false,
          message: 'Keine Berechtigung zum Veröffentlichen dieser Publikation'
        });
      }
      
      // Publikation veröffentlichen
      const publishedPublication = await publicationController.publishPublication(id, {
        userId: req.user.userId,
        generateDOI: req.body.generateDOI === true
      });
      
      res.json({
        success: true,
        message: 'Publikation erfolgreich veröffentlicht',
        data: publishedPublication
      });
    } catch (error) {
      console.error('Fehler beim Veröffentlichen der Publikation:', error);
      res.status(500).json({
        success: false,
        message: `Fehler beim Veröffentlichen der Publikation: ${error.message}`
      });
    }
  });

  // POST /api/publications/:id/retract - Publikation zurückziehen
  router.post('/:id/retract', [
    authMiddleware.authenticate,
    param('id').isString().withMessage('Ungültige Publikations-ID'),
    body('reason').isString().notEmpty().withMessage('Grund für die Zurückziehung ist erforderlich'),
    validateRequest
  ], async (req, res) => {
    try {
      const { id } = req.params;
      const { reason } = req.body;
      
      // Publikation abrufen
      const publication = await publicationController.getPublicationById(id);
      
      if (!publication) {
        return res.status(404).json({
          success: false,
          message: 'Publikation nicht gefunden'
        });
      }
      
      // Berechtigungsprüfung
      const isAuthor = publication.createdBy === req.user.userId;
      const isAdmin = req.user.roles.includes('admin');
      
      if (!isAuthor && !isAdmin) {
        return res.status(403).json({
          success: false,
          message: 'Keine Berechtigung zum Zurückziehen dieser Publikation'
        });
      }
      
      // Publikation zurückziehen
      const result = await publicationController.retractPublication(id, reason, {
        userId: req.user.userId
      });
      
      res.json({
        success: true,
        message: 'Publikation erfolgreich zurückgezogen',
        data: result
      });
    } catch (error) {
      console.error('Fehler beim Zurückziehen der Publikation:', error);
      res.status(500).json({
        success: false,
        message: `Fehler beim Zurückziehen der Publikation: ${error.message}`
      });
    }
  });

  // === Review-Routen ===

  // POST /api/publications/:id/reviews - Review zu einer Publikation hinzufügen
  router.post('/:id/reviews', [
    authMiddleware.authenticate,
    authMiddleware.requirePermission(PERMISSIONS.REVIEW_PUBLICATION),
    param('id').isString().withMessage('Ungültige Publikations-ID'),
    body('content').isString().notEmpty().withMessage('Review-Inhalt ist erforderlich'),
    body('rating').isInt({ min: 1, max: 5 }).withMessage('Bewertung muss zwischen 1 und 5 liegen'),
    body('recommendation').isString().isIn(['accept', 'accept_with_minor_revisions', 'accept_with_major_revisions', 'reject']).withMessage('Ungültige Empfehlung'),
    validateRequest
  ], async (req, res) => {
    try {
      const { id } = req.params;
      const { content, rating, recommendation } = req.body;
      
      // Review hinzufügen
      const review = await publicationController.addReview(id, {
        reviewerId: req.user.userId,
        content,
        rating,
        recommendation
      });
      
      res.status(201).json({
        success: true,
        message: 'Review erfolgreich hinzugefügt',
        data: review
      });
    } catch (error) {
      console.error('Fehler beim Hinzufügen des Reviews:', error);
      res.status(500).json({
        success: false,
        message: `Fehler beim Hinzufügen des Reviews: ${error.message}`
      });
    }
  });

  // GET /api/publications/:id/reviews - Reviews einer Publikation abrufen
  router.get('/:id/reviews', [
    param('id').isString().withMessage('Ungültige Publikations-ID'),
    validateRequest
  ], async (req, res) => {
    try {
      const { id } = req.params;
      
      // Reviews abrufen
      const reviews = await publicationController.getPublicationReviews(id);
      
      if (!reviews) {
        return res.status(404).json({
          success: false,
          message: 'Publikation nicht gefunden'
        });
      }
      
      res.json({
        success: true,
        data: reviews
      });
    } catch (error) {
      console.error('Fehler beim Abrufen der Reviews:', error);
      res.status(500).json({
        success: false,
        message: `Fehler beim Abrufen der Reviews: ${error.message}`
      });
    }
  });

  // === Admin-Routen ===

  // POST /api/publications/:id/approve - Publikation genehmigen (nur Admin/Editor)
  router.post('/:id/approve', [
    authMiddleware.authenticate,
    authMiddleware.requirePermission(PERMISSIONS.APPROVE_PUBLICATION),
    param('id').isString().withMessage('Ungültige Publikations-ID'),
    validateRequest
  ], async (req, res) => {
    try {
      const { id } = req.params;
      
      // Publikation genehmigen
      const approvedPublication = await publicationController.approvePublication(id, {
        approverId: req.user.userId,
        generateDOI: req.body.generateDOI === true
      });
      
      res.json({
        success: true,
        message: 'Publikation erfolgreich genehmigt',
        data: approvedPublication
      });
    } catch (error) {
      console.error('Fehler beim Genehmigen der Publikation:', error);
      res.status(500).json({
        success: false,
        message: `Fehler beim Genehmigen der Publikation: ${error.message}`
      });
    }
  });

  // POST /api/publications/:id/reject - Publikation ablehnen (nur Admin/Editor)
  router.post('/:id/reject', [
    authMiddleware.authenticate,
    authMiddleware.requirePermission(PERMISSIONS.APPROVE_PUBLICATION),
    param('id').isString().withMessage('Ungültige Publikations-ID'),
    body('reason').isString().notEmpty().withMessage('Ablehnungsgrund ist erforderlich'),
    validateRequest
  ], async (req, res) => {
    try {
      const { id } = req.params;
      const { reason } = req.body;
      
      // Publikation ablehnen
      const rejectedPublication = await publicationController.rejectPublication(id, reason, {
        reviewerId: req.user.userId
      });
      
      res.json({
        success: true,
        message: 'Publikation erfolgreich abgelehnt',
        data: rejectedPublication
      });
    } catch (error) {
      console.error('Fehler beim Ablehnen der Publikation:', error);
      res.status(500).json({
        success: false,
        message: `Fehler beim Ablehnen der Publikation: ${error.message}`
      });
    }
  });

  // GET /api/publications/admin/pending - Ausstehende Publikationen abrufen (nur Admin/Editor)
  router.get('/admin/pending', [
    authMiddleware.authenticate,
    authMiddleware.requirePermission(PERMISSIONS.APPROVE_PUBLICATION),
    validateRequest
  ], async (req, res) => {
    try {
      // Anfrageparameter verarbeiten
      const {
        limit = 20,
        offset = 0
      } = req.query;
      
      // Ausstehende Publikationen abrufen
      const pendingPublications = await publicationController.getPendingPublications({
        limit: parseInt(limit, 10),
        offset: parseInt(offset, 10)
      });
      
      res.json({
        success: true,
        data: pendingPublications
      });
    } catch (error) {
      console.error('Fehler beim Abrufen der ausstehenden Publikationen:', error);
      res.status(500).json({
        success: false,
        message: `Fehler beim Abrufen der ausstehenden Publikationen: ${error.message}`
      });
    }
  });

  return router;
}

export default createPublicationRoutes; 