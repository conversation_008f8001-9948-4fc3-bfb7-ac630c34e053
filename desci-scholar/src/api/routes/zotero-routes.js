/**
 * @fileoverview Routen für die Zotero-Integration
 * 
 * Diese Datei definiert die API-Routen für die Interaktion mit Zotero,
 * einschließlich Bibliotheksabruf, DOI-Import und NFT-Erstellung für Zotero-Elemente.
 */

import express from 'express';
import ZoteroAPI from './integrations/ZoteroAPI.js';

/**
 * Erstellt die Zotero-Routen
 * 
 * @param {Object} options Konfigurationsoptionen
 * @param {Object} options.authService Authentifizierungsdienst
 * @param {Object} options.nftService NFT-Dienst (optional)
 * @returns {express.Router} Express-Router mit Zotero-Routen
 */
export default function createZoteroRoutes(options = {}) {
  const { authService, nftService } = options;
  
  const router = express.Router();
  
  /**
   * @route POST /api/zotero/connect
   * @desc Verbindet DeSci-Scholar mit einem Zotero-Konto
   * @access Protected
   */
  router.post('/connect', async (req, res) => {
    try {
      const { apiKey } = req.body;
      
      if (!apiKey) {
        return res.status(400).json({
          success: false,
          message: 'Zotero API-Schlüssel ist erforderlich'
        });
      }
      
      // Zotero API initialisieren
      const zoteroAPI = new ZoteroAPI({ apiKey });
      
      // Testen, ob der API-Schlüssel gültig ist
      const userInfo = await zoteroAPI.getUserInfo();
      
      if (!userInfo.success) {
        return res.status(401).json({
          success: false,
          message: 'Ungültiger Zotero API-Schlüssel',
          error: userInfo.error
        });
      }
      
      // Wenn ein Authentifizierungsdienst vorhanden ist, API-Schlüssel speichern
      if (authService && req.user) {
        await authService.updateUserPreferences(req.user.id, {
          zoteroApiKey: apiKey,
          zoteroUserId: userInfo.data.userID,
          zoteroUsername: userInfo.data.username
        });
      }
      
      return res.json({
        success: true,
        message: 'Erfolgreich mit Zotero verbunden',
        user: {
          id: userInfo.data.userID,
          username: userInfo.data.username,
          name: userInfo.data.name
        }
      });
    } catch (error) {
      console.error('Fehler bei der Zotero-Verbindung:', error);
      return res.status(500).json({
        success: false,
        message: `Interner Serverfehler: ${error.message}`
      });
    }
  });
  
  /**
   * @route GET /api/zotero/libraries
   * @desc Ruft die Bibliotheken des verbundenen Zotero-Kontos ab
   * @access Protected
   */
  router.get('/libraries', async (req, res) => {
    try {
      // API-Schlüssel aus Benutzereinstellungen oder Anfrage abrufen
      const apiKey = req.query.apiKey || (req.user && req.user.zoteroApiKey);
      
      if (!apiKey) {
        return res.status(401).json({
          success: false,
          message: 'Zotero API-Schlüssel ist erforderlich'
        });
      }
      
      const zoteroAPI = new ZoteroAPI({ apiKey });
      const libraries = await zoteroAPI.getLibraries();
      
      if (!libraries.success) {
        return res.status(500).json({
          success: false,
          message: 'Fehler beim Abrufen der Bibliotheken',
          error: libraries.error
        });
      }
      
      return res.json({
        success: true,
        libraries: libraries.data
      });
    } catch (error) {
      console.error('Fehler beim Abrufen der Zotero-Bibliotheken:', error);
      return res.status(500).json({
        success: false,
        message: `Interner Serverfehler: ${error.message}`
      });
    }
  });
  
  /**
   * @route GET /api/zotero/collections
   * @desc Ruft die Sammlungen einer Bibliothek ab
   * @access Protected
   */
  router.get('/collections', async (req, res) => {
    try {
      const { libraryType, libraryId, apiKey } = req.query;
      
      // API-Schlüssel aus Benutzereinstellungen oder Anfrage abrufen
      const key = apiKey || (req.user && req.user.zoteroApiKey);
      
      if (!key) {
        return res.status(401).json({
          success: false,
          message: 'Zotero API-Schlüssel ist erforderlich'
        });
      }
      
      if (!libraryType || !libraryId) {
        return res.status(400).json({
          success: false,
          message: 'Bibliothekstyp und Bibliotheks-ID sind erforderlich'
        });
      }
      
      const zoteroAPI = new ZoteroAPI({ apiKey: key });
      const collections = await zoteroAPI.getCollections(libraryType, libraryId);
      
      if (!collections.success) {
        return res.status(500).json({
          success: false,
          message: 'Fehler beim Abrufen der Sammlungen',
          error: collections.error
        });
      }
      
      return res.json({
        success: true,
        collections: collections.data
      });
    } catch (error) {
      console.error('Fehler beim Abrufen der Zotero-Sammlungen:', error);
      return res.status(500).json({
        success: false,
        message: `Interner Serverfehler: ${error.message}`
      });
    }
  });
  
  /**
   * @route GET /api/zotero/items
   * @desc Ruft Elemente einer Bibliothek oder Sammlung ab
   * @access Protected
   */
  router.get('/items', async (req, res) => {
    try {
      const { 
        libraryType, 
        libraryId, 
        collectionId, 
        limit, 
        start, 
        sort, 
        direction,
        apiKey 
      } = req.query;
      
      // API-Schlüssel aus Benutzereinstellungen oder Anfrage abrufen
      const key = apiKey || (req.user && req.user.zoteroApiKey);
      
      if (!key) {
        return res.status(401).json({
          success: false,
          message: 'Zotero API-Schlüssel ist erforderlich'
        });
      }
      
      if (!libraryType || !libraryId) {
        return res.status(400).json({
          success: false,
          message: 'Bibliothekstyp und Bibliotheks-ID sind erforderlich'
        });
      }
      
      const zoteroAPI = new ZoteroAPI({ apiKey: key });
      const items = await zoteroAPI.getItems(libraryType, libraryId, collectionId, {
        limit: limit ? parseInt(limit) : undefined,
        start: start ? parseInt(start) : undefined,
        sort,
        direction
      });
      
      if (!items.success) {
        return res.status(500).json({
          success: false,
          message: 'Fehler beim Abrufen der Elemente',
          error: items.error
        });
      }
      
      return res.json({
        success: true,
        items: items.data,
        total: items.data.length
      });
    } catch (error) {
      console.error('Fehler beim Abrufen der Zotero-Elemente:', error);
      return res.status(500).json({
        success: false,
        message: `Interner Serverfehler: ${error.message}`
      });
    }
  });
  
  /**
   * @route GET /api/zotero/items-with-doi
   * @desc Ruft alle Elemente mit DOIs aus allen Bibliotheken ab
   * @access Protected
   */
  router.get('/items-with-doi', async (req, res) => {
    try {
      const { apiKey } = req.query;
      
      // API-Schlüssel aus Benutzereinstellungen oder Anfrage abrufen
      const key = apiKey || (req.user && req.user.zoteroApiKey);
      
      if (!key) {
        return res.status(401).json({
          success: false,
          message: 'Zotero API-Schlüssel ist erforderlich'
        });
      }
      
      const zoteroAPI = new ZoteroAPI({ apiKey: key });
      const items = await zoteroAPI.getAllItemsWithDOI();
      
      if (!items.success) {
        return res.status(500).json({
          success: false,
          message: 'Fehler beim Abrufen der Elemente mit DOIs',
          error: items.error
        });
      }
      
      // Konvertiere Zotero-Format in DeSci-Scholar-Format
      const desciItems = zoteroAPI.convertToDeSciFormat(items.data);
      
      return res.json({
        success: true,
        items: desciItems,
        total: desciItems.length,
        errors: items.errors
      });
    } catch (error) {
      console.error('Fehler beim Abrufen der Zotero-Elemente mit DOIs:', error);
      return res.status(500).json({
        success: false,
        message: `Interner Serverfehler: ${error.message}`
      });
    }
  });
  
  /**
   * @route POST /api/zotero/import-dois
   * @desc Importiert ausgewählte DOIs aus Zotero und erstellt optional NFTs
   * @access Protected
   */
  router.post('/import-dois', async (req, res) => {
    try {
      const { itemKeys, createNFTs, apiKey } = req.body;
      
      // API-Schlüssel aus Benutzereinstellungen oder Anfrage abrufen
      const key = apiKey || (req.user && req.user.zoteroApiKey);
      
      if (!key) {
        return res.status(401).json({
          success: false,
          message: 'Zotero API-Schlüssel ist erforderlich'
        });
      }
      
      if (!itemKeys || !Array.isArray(itemKeys) || itemKeys.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Mindestens ein Element-Schlüssel ist erforderlich'
        });
      }
      
      const zoteroAPI = new ZoteroAPI({ apiKey: key });
      
      // Benutzerinformationen abrufen
      const userInfo = await zoteroAPI.getUserInfo();
      
      if (!userInfo.success) {
        return res.status(401).json({
          success: false,
          message: 'Fehler beim Abrufen der Benutzerinformationen',
          error: userInfo.error
        });
      }
      
      const userId = userInfo.data.userID;
      
      // Elemente abrufen
      const items = [];
      const errors = [];
      
      for (const itemKey of itemKeys) {
        const item = await zoteroAPI.getItem('user', userId, itemKey);
        
        if (item.success) {
          items.push(item.data);
        } else {
          errors.push({
            itemKey,
            error: item.error
          });
        }
      }
      
      // Konvertiere Zotero-Format in DeSci-Scholar-Format
      const desciItems = zoteroAPI.convertToDeSciFormat(items);
      
      // Wenn NFTs erstellt werden sollen und ein NFT-Dienst verfügbar ist
      let nftResults = [];
      if (createNFTs && nftService) {
        nftResults = await Promise.all(
          desciItems.filter(item => item.doi).map(async (item) => {
            try {
              // NFT-Metadaten erstellen
              const nftMetadata = {
                name: item.title,
                description: item.abstract || `${item.title} - ${item.authors.join(', ')}`,
                external_url: item.url || `https://doi.org/${item.doi}`,
                image: '', // Hier könnte ein Bild oder Thumbnail generiert werden
                attributes: [
                  { trait_type: 'Type', value: 'Scientific Paper' },
                  { trait_type: 'Source', value: 'Zotero' },
                  { trait_type: 'DOI', value: item.doi },
                  { trait_type: 'Authors', value: item.authors.join(', ') },
                  { trait_type: 'Journal', value: item.journal || 'Unknown' },
                  { trait_type: 'Publication Date', value: item.publishedDate || 'Unknown' }
                ],
                // Zusätzliche Metadaten für die Verifizierung
                doi: item.doi,
                zotero_key: item.zoteroData.key,
                publication_date: item.publishedDate
              };
              
              // NFT erstellen
              const nftResult = await nftService.createNFT({
                metadata: nftMetadata,
                contentUri: item.url || `https://doi.org/${item.doi}`
              });
              
              return {
                item,
                nft: nftResult.success ? {
                  tokenId: nftResult.tokenId,
                  transactionHash: nftResult.transactionHash,
                  metadata: nftMetadata
                } : null,
                success: nftResult.success,
                error: nftResult.success ? null : nftResult.error
              };
            } catch (error) {
              return {
                item,
                nft: null,
                success: false,
                error: error.message
              };
            }
          })
        );
      }
      
      return res.json({
        success: true,
        imported: desciItems.length,
        items: desciItems,
        errors: errors.length > 0 ? errors : null,
        nfts: createNFTs ? {
          created: nftResults.filter(r => r.success).length,
          failed: nftResults.filter(r => !r.success).length,
          results: nftResults
        } : null
      });
    } catch (error) {
      console.error('Fehler beim Importieren von DOIs aus Zotero:', error);
      return res.status(500).json({
        success: false,
        message: `Interner Serverfehler: ${error.message}`
      });
    }
  });
  
  return router;
}/**
 * @fileoverview Routen für die Zotero-Integration
 * 
 * Diese Datei definiert die API-Routen für die Interaktion mit Zotero,
 * einschließlich Bibliotheksabruf, DOI-Import und NFT-Erstellung für Zotero-Elemente.
 */

import express from 'express';
import ZoteroAPI from './integrations/ZoteroAPI.js';

/**
 * Erstellt die Zotero-Routen
 * 
 * @param {Object} options Konfigurationsoptionen
 * @param {Object} options.authService Authentifizierungsdienst
 * @param {Object} options.nftService NFT-Dienst (optional)
 * @returns {express.Router} Express-Router mit Zotero-Routen
 */
export default function createZoteroRoutes(options = {}) {
  const { authService, nftService } = options;
  
  const router = express.Router();
  
  /**
   * @route POST /api/zotero/connect
   * @desc Verbindet DeSci-Scholar mit einem Zotero-Konto
   * @access Protected
   */
  router.post('/connect', async (req, res) => {
    try {
      const { apiKey } = req.body;
      
      if (!apiKey) {
        return res.status(400).json({
          success: false,
          message: 'Zotero API-Schlüssel ist erforderlich'
        });
      }
      
      // Zotero API initialisieren
      const zoteroAPI = new ZoteroAPI({ apiKey });
      
      // Testen, ob der API-Schlüssel gültig ist
      const userInfo = await zoteroAPI.getUserInfo();
      
      if (!userInfo.success) {
        return res.status(401).json({
          success: false,
          message: 'Ungültiger Zotero API-Schlüssel',
          error: userInfo.error
        });
      }
      
      // Wenn ein Authentifizierungsdienst vorhanden ist, API-Schlüssel speichern
      if (authService && req.user) {
        await authService.updateUserPreferences(req.user.id, {
          zoteroApiKey: apiKey,
          zoteroUserId: userInfo.data.userID,
          zoteroUsername: userInfo.data.username
        });
      }
      
      return res.json({
        success: true,
        message: 'Erfolgreich mit Zotero verbunden',
        user: {
          id: userInfo.data.userID,
          username: userInfo.data.username,
          name: userInfo.data.name
        }
      });
    } catch (error) {
      console.error('Fehler bei der Zotero-Verbindung:', error);
      return res.status(500).json({
        success: false,
        message: `Interner Serverfehler: ${error.message}`
      });
    }
  });
  
  /**
   * @route GET /api/zotero/libraries
   * @desc Ruft die Bibliotheken des verbundenen Zotero-Kontos ab
   * @access Protected
   */
  router.get('/libraries', async (req, res) => {
    try {
      // API-Schlüssel aus Benutzereinstellungen oder Anfrage abrufen
      const apiKey = req.query.apiKey || (req.user && req.user.zoteroApiKey);
      
      if (!apiKey) {
        return res.status(401).json({
          success: false,
          message: 'Zotero API-Schlüssel ist erforderlich'
        });
      }
      
      const zoteroAPI = new ZoteroAPI({ apiKey });
      const libraries = await zoteroAPI.getLibraries();
      
      if (!libraries.success) {
        return res.status(500).json({
          success: false,
          message: 'Fehler beim Abrufen der Bibliotheken',
          error: libraries.error
        });
      }
      
      return res.json({
        success: true,
        libraries: libraries.data
      });
    } catch (error) {
      console.error('Fehler beim Abrufen der Zotero-Bibliotheken:', error);
      return res.status(500).json({
        success: false,
        message: `Interner Serverfehler: ${error.message}`
      });
    }
  });
  
  /**
   * @route GET /api/zotero/collections
   * @desc Ruft die Sammlungen einer Bibliothek ab
   * @access Protected
   */
  router.get('/collections', async (req, res) => {
    try {
      const { libraryType, libraryId, apiKey } = req.query;
      
      // API-Schlüssel aus Benutzereinstellungen oder Anfrage abrufen
      const key = apiKey || (req.user && req.user.zoteroApiKey);
      
      if (!key) {
        return res.status(401).json({
          success: false,
          message: 'Zotero API-Schlüssel ist erforderlich'
        });
      }
      
      if (!libraryType || !libraryId) {
        return res.status(400).json({
          success: false,
          message: 'Bibliothekstyp und Bibliotheks-ID sind erforderlich'
        });
      }
      
      const zoteroAPI = new ZoteroAPI({ apiKey: key });
      const collections = await zoteroAPI.getCollections(libraryType, libraryId);
      
      if (!collections.success) {
        return res.status(500).json({
          success: false,
          message: 'Fehler beim Abrufen der Sammlungen',
          error: collections.error
        });
      }
      
      return res.json({
        success: true,
        collections: collections.data
      });
    } catch (error) {
      console.error('Fehler beim Abrufen der Zotero-Sammlungen:', error);
      return res.status(500).json({
        success: false,
        message: `Interner Serverfehler: ${error.message}`
      });
    }
  });
  
  /**
   * @route GET /api/zotero/items
   * @desc Ruft Elemente einer Bibliothek oder Sammlung ab
   * @access Protected
   */
  router.get('/items', async (req, res) => {
    try {
      const { 
        libraryType, 
        libraryId, 
        collectionId, 
        limit, 
        start, 
        sort, 
        direction,
        apiKey 
      } = req.query;
      
      // API-Schlüssel aus Benutzereinstellungen oder Anfrage abrufen
      const key = apiKey || (req.user && req.user.zoteroApiKey);
      
      if (!key) {
        return res.status(401).json({
          success: false,
          message: 'Zotero API-Schlüssel ist erforderlich'
        });
      }
      
      if (!libraryType || !libraryId) {
        return res.status(400).json({
          success: false,
          message: 'Bibliothekstyp und Bibliotheks-ID sind erforderlich'
        });
      }
      
      const zoteroAPI = new ZoteroAPI({ apiKey: key });
      const items = await zoteroAPI.getItems(libraryType, libraryId, collectionId, {
        limit: limit ? parseInt(limit) : undefined,
        start: start ? parseInt(start) : undefined,
        sort,
        direction
      });
      
      if (!items.success) {
        return res.status(500).json({
          success: false,
          message: 'Fehler beim Abrufen der Elemente',
          error: items.error
        });
      }
      
      return res.json({
        success: true,
        items: items.data,
        total: items.data.length
      });
    } catch (error) {
      console.error('Fehler beim Abrufen der Zotero-Elemente:', error);
      return res.status(500).json({
        success: false,
        message: `Interner Serverfehler: ${error.message}`
      });
    }
  });
  
  /**
   * @route GET /api/zotero/items-with-doi
   * @desc Ruft alle Elemente mit DOIs aus allen Bibliotheken ab
   * @access Protected
   */
  router.get('/items-with-doi', async (req, res) => {
    try {
      const { apiKey } = req.query;
      
      // API-Schlüssel aus Benutzereinstellungen oder Anfrage abrufen
      const key = apiKey || (req.user && req.user.zoteroApiKey);
      
      if (!key) {
        return res.status(401).json({
          success: false,
          message: 'Zotero API-Schlüssel ist erforderlich'
        });
      }
      
      const zoteroAPI = new ZoteroAPI({ apiKey: key });
      const items = await zoteroAPI.getAllItemsWithDOI();
      
      if (!items.success) {
        return res.status(500).json({
          success: false,
          message: 'Fehler beim Abrufen der Elemente mit DOIs',
          error: items.error
        });
      }
      
      // Konvertiere Zotero-Format in DeSci-Scholar-Format
      const desciItems = zoteroAPI.convertToDeSciFormat(items.data);
      
      return res.json({
        success: true,
        items: desciItems,
        total: desciItems.length,
        errors: items.errors
      });
    } catch (error) {
      console.error('Fehler beim Abrufen der Zotero-Elemente mit DOIs:', error);
      return res.status(500).json({
        success: false,
        message: `Interner Serverfehler: ${error.message}`
      });
    }
  });
  
  /**
   * @route POST /api/zotero/import-dois
   * @desc Importiert ausgewählte DOIs aus Zotero und erstellt optional NFTs
   * @access Protected
   */
  router.post('/import-dois', async (req, res) => {
    try {
      const { itemKeys, createNFTs, apiKey } = req.body;
      
      // API-Schlüssel aus Benutzereinstellungen oder Anfrage abrufen
      const key = apiKey || (req.user && req.user.zoteroApiKey);
      
      if (!key) {
        return res.status(401).json({
          success: false,
          message: 'Zotero API-Schlüssel ist erforderlich'
        });
      }
      
      if (!itemKeys || !Array.isArray(itemKeys) || itemKeys.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Mindestens ein Element-Schlüssel ist erforderlich'
        });
      }
      
      const zoteroAPI = new ZoteroAPI({ apiKey: key });
      
      // Benutzerinformationen abrufen
      const userInfo = await zoteroAPI.getUserInfo();
      
      if (!userInfo.success) {
        return res.status(401).json({
          success: false,
          message: 'Fehler beim Abrufen der Benutzerinformationen',
          error: userInfo.error
        });
      }
      
      const userId = userInfo.data.userID;
      
      // Elemente abrufen
      const items = [];
      const errors = [];
      
      for (const itemKey of itemKeys) {
        const item = await zoteroAPI.getItem('user', userId, itemKey);
        
        if (item.success) {
          items.push(item.data);
        } else {
          errors.push({
            itemKey,
            error: item.error
          });
        }
      }
      
      // Konvertiere Zotero-Format in DeSci-Scholar-Format
      const desciItems = zoteroAPI.convertToDeSciFormat(items);
      
      // Wenn NFTs erstellt werden sollen und ein NFT-Dienst verfügbar ist
      let nftResults = [];
      if (createNFTs && nftService) {
        nftResults = await Promise.all(
          desciItems.filter(item => item.doi).map(async (item) => {
            try {
              // NFT-Metadaten erstellen
              const nftMetadata = {
                name: item.title,
                description: item.abstract || `${item.title} - ${item.authors.join(', ')}`,
                external_url: item.url || `https://doi.org/${item.doi}`,
                image: '', // Hier könnte ein Bild oder Thumbnail generiert werden
                attributes: [
                  { trait_type: 'Type', value: 'Scientific Paper' },
                  { trait_type: 'Source', value: 'Zotero' },
                  { trait_type: 'DOI', value: item.doi },
                  { trait_type: 'Authors', value: item.authors.join(', ') },
                  { trait_type: 'Journal', value: item.journal || 'Unknown' },
                  { trait_type: 'Publication Date', value: item.publishedDate || 'Unknown' }
                ],
                // Zusätzliche Metadaten für die Verifizierung
                doi: item.doi,
                zotero_key: item.zoteroData.key,
                publication_date: item.publishedDate
              };
              
              // NFT erstellen
              const nftResult = await nftService.createNFT({
                metadata: nftMetadata,
                contentUri: item.url || `https://doi.org/${item.doi}`
              });
              
              return {
                item,
                nft: nftResult.success ? {
                  tokenId: nftResult.tokenId,
                  transactionHash: nftResult.transactionHash,
                  metadata: nftMetadata
                } : null,
                success: nftResult.success,
                error: nftResult.success ? null : nftResult.error
              };
            } catch (error) {
              return {
                item,
                nft: null,
                success: false,
                error: error.message
              };
            }
          })
        );
      }
      
      return res.json({
        success: true,
        imported: desciItems.length,
        items: desciItems,
        errors: errors.length > 0 ? errors : null,
        nfts: createNFTs ? {
          created: nftResults.filter(r => r.success).length,
          failed: nftResults.filter(r => !r.success).length,
          results: nftResults
        } : null
      });
    } catch (error) {
      console.error('Fehler beim Importieren von DOIs aus Zotero:', error);
      return res.status(500).json({
        success: false,
        message: `Interner Serverfehler: ${error.message}`
      });
    }
  });
  
  return router;
}/**
 * @fileoverview Routen für die Zotero-Integration
 * 
 * Diese Datei definiert die API-Routen für die Interaktion mit Zotero,
 * einschließlich Bibliotheksabruf, DOI-Import und NFT-Erstellung für Zotero-Elemente.
 */

import express from 'express';
import ZoteroAPI from './integrations/ZoteroAPI.js';

/**
 * Erstellt die Zotero-Routen
 * 
 * @param {Object} options Konfigurationsoptionen
 * @param {Object} options.authService Authentifizierungsdienst
 * @param {Object} options.nftService NFT-Dienst (optional)
 * @returns {express.Router} Express-Router mit Zotero-Routen
 */
export default function createZoteroRoutes(options = {}) {
  const { authService, nftService } = options;
  
  const router = express.Router();
  
  /**
   * @route POST /api/zotero/connect
   * @desc Verbindet DeSci-Scholar mit einem Zotero-Konto
   * @access Protected
   */
  router.post('/connect', async (req, res) => {
    try {
      const { apiKey } = req.body;
      
      if (!apiKey) {
        return res.status(400).json({
          success: false,
          message: 'Zotero API-Schlüssel ist erforderlich'
        });
      }
      
      // Zotero API initialisieren
      const zoteroAPI = new ZoteroAPI({ apiKey });
      
      // Testen, ob der API-Schlüssel gültig ist
      const userInfo = await zoteroAPI.getUserInfo();
      
      if (!userInfo.success) {
        return res.status(401).json({
          success: false,
          message: 'Ungültiger Zotero API-Schlüssel',
          error: userInfo.error
        });
      }
      
      // Wenn ein Authentifizierungsdienst vorhanden ist, API-Schlüssel speichern
      if (authService && req.user) {
        await authService.updateUserPreferences(req.user.id, {
          zoteroApiKey: apiKey,
          zoteroUserId: userInfo.data.userID,
          zoteroUsername: userInfo.data.username
        });
      }
      
      return res.json({
        success: true,
        message: 'Erfolgreich mit Zotero verbunden',
        user: {
          id: userInfo.data.userID,
          username: userInfo.data.username,
          name: userInfo.data.name
        }
      });
    } catch (error) {
      console.error('Fehler bei der Zotero-Verbindung:', error);
      return res.status(500).json({
        success: false,
        message: `Interner Serverfehler: ${error.message}`
      });
    }
  });
  
  /**
   * @route GET /api/zotero/libraries
   * @desc Ruft die Bibliotheken des verbundenen Zotero-Kontos ab
   * @access Protected
   */
  router.get('/libraries', async (req, res) => {
    try {
      // API-Schlüssel aus Benutzereinstellungen oder Anfrage abrufen
      const apiKey = req.query.apiKey || (req.user && req.user.zoteroApiKey);
      
      if (!apiKey) {
        return res.status(401).json({
          success: false,
          message: 'Zotero API-Schlüssel ist erforderlich'
        });
      }
      
      const zoteroAPI = new ZoteroAPI({ apiKey });
      const libraries = await zoteroAPI.getLibraries();
      
      if (!libraries.success) {
        return res.status(500).json({
          success: false,
          message: 'Fehler beim Abrufen der Bibliotheken',
          error: libraries.error
        });
      }
      
      return res.json({
        success: true,
        libraries: libraries.data
      });
    } catch (error) {
      console.error('Fehler beim Abrufen der Zotero-Bibliotheken:', error);
      return res.status(500).json({
        success: false,
        message: `Interner Serverfehler: ${error.message}`
      });
    }
  });
  
  /**
   * @route GET /api/zotero/collections
   * @desc Ruft die Sammlungen einer Bibliothek ab
   * @access Protected
   */
  router.get('/collections', async (req, res) => {
    try {
      const { libraryType, libraryId, apiKey } = req.query;
      
      // API-Schlüssel aus Benutzereinstellungen oder Anfrage abrufen
      const key = apiKey || (req.user && req.user.zoteroApiKey);
      
      if (!key) {
        return res.status(401).json({
          success: false,
          message: 'Zotero API-Schlüssel ist erforderlich'
        });
      }
      
      if (!libraryType || !libraryId) {
        return res.status(400).json({
          success: false,
          message: 'Bibliothekstyp und Bibliotheks-ID sind erforderlich'
        });
      }
      
      const zoteroAPI = new ZoteroAPI({ apiKey: key });
      const collections = await zoteroAPI.getCollections(libraryType, libraryId);
      
      if (!collections.success) {
        return res.status(500).json({
          success: false,
          message: 'Fehler beim Abrufen der Sammlungen',
          error: collections.error
        });
      }
      
      return res.json({
        success: true,
        collections: collections.data
      });
    } catch (error) {
      console.error('Fehler beim Abrufen der Zotero-Sammlungen:', error);
      return res.status(500).json({
        success: false,
        message: `Interner Serverfehler: ${error.message}`
      });
    }
  });
  
  /**
   * @route GET /api/zotero/items
   * @desc Ruft Elemente einer Bibliothek oder Sammlung ab
   * @access Protected
   */
  router.get('/items', async (req, res) => {
    try {
      const { 
        libraryType, 
        libraryId, 
        collectionId, 
        limit, 
        start, 
        sort, 
        direction,
        apiKey 
      } = req.query;
      
      // API-Schlüssel aus Benutzereinstellungen oder Anfrage abrufen
      const key = apiKey || (req.user && req.user.zoteroApiKey);
      
      if (!key) {
        return res.status(401).json({
          success: false,
          message: 'Zotero API-Schlüssel ist erforderlich'
        });
      }
      
      if (!libraryType || !libraryId) {
        return res.status(400).json({
          success: false,
          message: 'Bibliothekstyp und Bibliotheks-ID sind erforderlich'
        });
      }
      
      const zoteroAPI = new ZoteroAPI({ apiKey: key });
      const items = await zoteroAPI.getItems(libraryType, libraryId, collectionId, {
        limit: limit ? parseInt(limit) : undefined,
        start: start ? parseInt(start) : undefined,
        sort,
        direction
      });
      
      if (!items.success) {
        return res.status(500).json({
          success: false,
          message: 'Fehler beim Abrufen der Elemente',
          error: items.error
        });
      }
      
      return res.json({
        success: true,
        items: items.data,
        total: items.data.length
      });
    } catch (error) {
      console.error('Fehler beim Abrufen der Zotero-Elemente:', error);
      return res.status(500).json({
        success: false,
        message: `Interner Serverfehler: ${error.message}`
      });
    }
  });
  
  /**
   * @route GET /api/zotero/items-with-doi
   * @desc Ruft alle Elemente mit DOIs aus allen Bibliotheken ab
   * @access Protected
   */
  router.get('/items-with-doi', async (req, res) => {
    try {
      const { apiKey } = req.query;
      
      // API-Schlüssel aus Benutzereinstellungen oder Anfrage abrufen
      const key = apiKey || (req.user && req.user.zoteroApiKey);
      
      if (!key) {
        return res.status(401).json({
          success: false,
          message: 'Zotero API-Schlüssel ist erforderlich'
        });
      }
      
      const zoteroAPI = new ZoteroAPI({ apiKey: key });
      const items = await zoteroAPI.getAllItemsWithDOI();
      
      if (!items.success) {
        return res.status(500).json({
          success: false,
          message: 'Fehler beim Abrufen der Elemente mit DOIs',
          error: items.error
        });
      }
      
      // Konvertiere Zotero-Format in DeSci-Scholar-Format
      const desciItems = zoteroAPI.convertToDeSciFormat(items.data);
      
      return res.json({
        success: true,
        items: desciItems,
        total: desciItems.length,
        errors: items.errors
      });
    } catch (error) {
      console.error('Fehler beim Abrufen der Zotero-Elemente mit DOIs:', error);
      return res.status(500).json({
        success: false,
        message: `Interner Serverfehler: ${error.message}`
      });
    }
  });
  
  /**
   * @route POST /api/zotero/import-dois
   * @desc Importiert ausgewählte DOIs aus Zotero und erstellt optional NFTs
   * @access Protected
   */
  router.post('/import-dois', async (req, res) => {
    try {
      const { itemKeys, createNFTs, apiKey } = req.body;
      
      // API-Schlüssel aus Benutzereinstellungen oder Anfrage abrufen
      const key = apiKey || (req.user && req.user.zoteroApiKey);
      
      if (!key) {
        return res.status(401).json({
          success: false,
          message: 'Zotero API-Schlüssel ist erforderlich'
        });
      }
      
      if (!itemKeys || !Array.isArray(itemKeys) || itemKeys.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Mindestens ein Element-Schlüssel ist erforderlich'
        });
      }
      
      const zoteroAPI = new ZoteroAPI({ apiKey: key });
      
      // Benutzerinformationen abrufen
      const userInfo = await zoteroAPI.getUserInfo();
      
      if (!userInfo.success) {
        return res.status(401).json({
          success: false,
          message: 'Fehler beim Abrufen der Benutzerinformationen',
          error: userInfo.error
        });
      }
      
      const userId = userInfo.data.userID;
      
      // Elemente abrufen
      const items = [];
      const errors = [];
      
      for (const itemKey of itemKeys) {
        const item = await zoteroAPI.getItem('user', userId, itemKey);
        
        if (item.success) {
          items.push(item.data);
        } else {
          errors.push({
            itemKey,
            error: item.error
          });
        }
      }
      
      // Konvertiere Zotero-Format in DeSci-Scholar-Format
      const desciItems = zoteroAPI.convertToDeSciFormat(items);
      
      // Wenn NFTs erstellt werden sollen und ein NFT-Dienst verfügbar ist
      let nftResults = [];
      if (createNFTs && nftService) {
        nftResults = await Promise.all(
          desciItems.filter(item => item.doi).map(async (item) => {
            try {
              // NFT-Metadaten erstellen
              const nftMetadata = {
                name: item.title,
                description: item.abstract || `${item.title} - ${item.authors.join(', ')}`,
                external_url: item.url || `https://doi.org/${item.doi}`,
                image: '', // Hier könnte ein Bild oder Thumbnail generiert werden
                attributes: [
                  { trait_type: 'Type', value: 'Scientific Paper' },
                  { trait_type: 'Source', value: 'Zotero' },
                  { trait_type: 'DOI', value: item.doi },
                  { trait_type: 'Authors', value: item.authors.join(', ') },
                  { trait_type: 'Journal', value: item.journal || 'Unknown' },
                  { trait_type: 'Publication Date', value: item.publishedDate || 'Unknown' }
                ],
                // Zusätzliche Metadaten für die Verifizierung
                doi: item.doi,
                zotero_key: item.zoteroData.key,
                publication_date: item.publishedDate
              };
              
              // NFT erstellen
              const nftResult = await nftService.createNFT({
                metadata: nftMetadata,
                contentUri: item.url || `https://doi.org/${item.doi}`
              });
              
              return {
                item,
                nft: nftResult.success ? {
                  tokenId: nftResult.tokenId,
                  transactionHash: nftResult.transactionHash,
                  metadata: nftMetadata
                } : null,
                success: nftResult.success,
                error: nftResult.success ? null : nftResult.error
              };
            } catch (error) {
              return {
                item,
                nft: null,
                success: false,
                error: error.message
              };
            }
          })
        );
      }
      
      return res.json({
        success: true,
        imported: desciItems.length,
        items: desciItems,
        errors: errors.length > 0 ? errors : null,
        nfts: createNFTs ? {
          created: nftResults.filter(r => r.success).length,
          failed: nftResults.filter(r => !r.success).length,
          results: nftResults
        } : null
      });
    } catch (error) {
      console.error('Fehler beim Importieren von DOIs aus Zotero:', error);
      return res.status(500).json({
        success: false,
        message: `Interner Serverfehler: ${error.message}`
      });
    }
  });
  
  return router;
}/**
 * @fileoverview Routen für die Zotero-Integration
 * 
 * Diese Datei definiert die API-Routen für die Interaktion mit Zotero,
 * einschließlich Bibliotheksabruf, DOI-Import und NFT-Erstellung für Zotero-Elemente.
 */

import express from 'express';
import ZoteroAPI from './integrations/ZoteroAPI.js';

/**
 * Erstellt die Zotero-Routen
 * 
 * @param {Object} options Konfigurationsoptionen
 * @param {Object} options.authService Authentifizierungsdienst
 * @param {Object} options.nftService NFT-Dienst (optional)
 * @returns {express.Router} Express-Router mit Zotero-Routen
 */
export default function createZoteroRoutes(options = {}) {
  const { authService, nftService } = options;
  
  const router = express.Router();
  
  /**
   * @route POST /api/zotero/connect
   * @desc Verbindet DeSci-Scholar mit einem Zotero-Konto
   * @access Protected
   */
  router.post('/connect', async (req, res) => {
    try {
      const { apiKey } = req.body;
      
      if (!apiKey) {
        return res.status(400).json({
          success: false,
          message: 'Zotero API-Schlüssel ist erforderlich'
        });
      }
      
      // Zotero API initialisieren
      const zoteroAPI = new ZoteroAPI({ apiKey });
      
      // Testen, ob der API-Schlüssel gültig ist
      const userInfo = await zoteroAPI.getUserInfo();
      
      if (!userInfo.success) {
        return res.status(401).json({
          success: false,
          message: 'Ungültiger Zotero API-Schlüssel',
          error: userInfo.error
        });
      }
      
      // Wenn ein Authentifizierungsdienst vorhanden ist, API-Schlüssel speichern
      if (authService && req.user) {
        await authService.updateUserPreferences(req.user.id, {
          zoteroApiKey: apiKey,
          zoteroUserId: userInfo.data.userID,
          zoteroUsername: userInfo.data.username
        });
      }
      
      return res.json({
        success: true,
        message: 'Erfolgreich mit Zotero verbunden',
        user: {
          id: userInfo.data.userID,
          username: userInfo.data.username,
          name: userInfo.data.name
        }
      });
    } catch (error) {
      console.error('Fehler bei der Zotero-Verbindung:', error);
      return res.status(500).json({
        success: false,
        message: `Interner Serverfehler: ${error.message}`
      });
    }
  });
  
  /**
   * @route GET /api/zotero/libraries
   * @desc Ruft die Bibliotheken des verbundenen Zotero-Kontos ab
   * @access Protected
   */
  router.get('/libraries', async (req, res) => {
    try {
      // API-Schlüssel aus Benutzereinstellungen oder Anfrage abrufen
      const apiKey = req.query.apiKey || (req.user && req.user.zoteroApiKey);
      
      if (!apiKey) {
        return res.status(401).json({
          success: false,
          message: 'Zotero API-Schlüssel ist erforderlich'
        });
      }
      
      const zoteroAPI = new ZoteroAPI({ apiKey });
      const libraries = await zoteroAPI.getLibraries();
      
      if (!libraries.success) {
        return res.status(500).json({
          success: false,
          message: 'Fehler beim Abrufen der Bibliotheken',
          error: libraries.error
        });
      }
      
      return res.json({
        success: true,
        libraries: libraries.data
      });
    } catch (error) {
      console.error('Fehler beim Abrufen der Zotero-Bibliotheken:', error);
      return res.status(500).json({
        success: false,
        message: `Interner Serverfehler: ${error.message}`
      });
    }
  });
  
  /**
   * @route GET /api/zotero/collections
   * @desc Ruft die Sammlungen einer Bibliothek ab
   * @access Protected
   */
  router.get('/collections', async (req, res) => {
    try {
      const { libraryType, libraryId, apiKey } = req.query;
      
      // API-Schlüssel aus Benutzereinstellungen oder Anfrage abrufen
      const key = apiKey || (req.user && req.user.zoteroApiKey);
      
      if (!key) {
        return res.status(401).json({
          success: false,
          message: 'Zotero API-Schlüssel ist erforderlich'
        });
      }
      
      if (!libraryType || !libraryId) {
        return res.status(400).json({
          success: false,
          message: 'Bibliothekstyp und Bibliotheks-ID sind erforderlich'
        });
      }
      
      const zoteroAPI = new ZoteroAPI({ apiKey: key });
      const collections = await zoteroAPI.getCollections(libraryType, libraryId);
      
      if (!collections.success) {
        return res.status(500).json({
          success: false,
          message: 'Fehler beim Abrufen der Sammlungen',
          error: collections.error
        });
      }
      
      return res.json({
        success: true,
        collections: collections.data
      });
    } catch (error) {
      console.error('Fehler beim Abrufen der Zotero-Sammlungen:', error);
      return res.status(500).json({
        success: false,
        message: `Interner Serverfehler: ${error.message}`
      });
    }
  });
  
  /**
   * @route GET /api/zotero/items
   * @desc Ruft Elemente einer Bibliothek oder Sammlung ab
   * @access Protected
   */
  router.get('/items', async (req, res) => {
    try {
      const { 
        libraryType, 
        libraryId, 
        collectionId, 
        limit, 
        start, 
        sort, 
        direction,
        apiKey 
      } = req.query;
      
      // API-Schlüssel aus Benutzereinstellungen oder Anfrage abrufen
      const key = apiKey || (req.user && req.user.zoteroApiKey);
      
      if (!key) {
        return res.status(401).json({
          success: false,
          message: 'Zotero API-Schlüssel ist erforderlich'
        });
      }
      
      if (!libraryType || !libraryId) {
        return res.status(400).json({
          success: false,
          message: 'Bibliothekstyp und Bibliotheks-ID sind erforderlich'
        });
      }
      
      const zoteroAPI = new ZoteroAPI({ apiKey: key });
      const items = await zoteroAPI.getItems(libraryType, libraryId, collectionId, {
        limit: limit ? parseInt(limit) : undefined,
        start: start ? parseInt(start) : undefined,
        sort,
        direction
      });
      
      if (!items.success) {
        return res.status(500).json({
          success: false,
          message: 'Fehler beim Abrufen der Elemente',
          error: items.error
        });
      }
      
      return res.json({
        success: true,
        items: items.data,
        total: items.data.length
      });
    } catch (error) {
      console.error('Fehler beim Abrufen der Zotero-Elemente:', error);
      return res.status(500).json({
        success: false,
        message: `Interner Serverfehler: ${error.message}`
      });
    }
  });
  
  /**
   * @route GET /api/zotero/items-with-doi
   * @desc Ruft alle Elemente mit DOIs aus allen Bibliotheken ab
   * @access Protected
   */
  router.get('/items-with-doi', async (req, res) => {
    try {
      const { apiKey } = req.query;
      
      // API-Schlüssel aus Benutzereinstellungen oder Anfrage abrufen
      const key = apiKey || (req.user && req.user.zoteroApiKey);
      
      if (!key) {
        return res.status(401).json({
          success: false,
          message: 'Zotero API-Schlüssel ist erforderlich'
        });
      }
      
      const zoteroAPI = new ZoteroAPI({ apiKey: key });
      const items = await zoteroAPI.getAllItemsWithDOI();
      
      if (!items.success) {
        return res.status(500).json({
          success: false,
          message: 'Fehler beim Abrufen der Elemente mit DOIs',
          error: items.error
        });
      }
      
      // Konvertiere Zotero-Format in DeSci-Scholar-Format
      const desciItems = zoteroAPI.convertToDeSciFormat(items.data);
      
      return res.json({
        success: true,
        items: desciItems,
        total: desciItems.length,
        errors: items.errors
      });
    } catch (error) {
      console.error('Fehler beim Abrufen der Zotero-Elemente mit DOIs:', error);
      return res.status(500).json({
        success: false,
        message: `Interner Serverfehler: ${error.message}`
      });
    }
  });
  
  /**
   * @route POST /api/zotero/import-dois
   * @desc Importiert ausgewählte DOIs aus Zotero und erstellt optional NFTs
   * @access Protected
   */
  router.post('/import-dois', async (req, res) => {
    try {
      const { itemKeys, createNFTs, apiKey } = req.body;
      
      // API-Schlüssel aus Benutzereinstellungen oder Anfrage abrufen
      const key = apiKey || (req.user && req.user.zoteroApiKey);
      
      if (!key) {
        return res.status(401).json({
          success: false,
          message: 'Zotero API-Schlüssel ist erforderlich'
        });
      }
      
      if (!itemKeys || !Array.isArray(itemKeys) || itemKeys.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Mindestens ein Element-Schlüssel ist erforderlich'
        });
      }
      
      const zoteroAPI = new ZoteroAPI({ apiKey: key });
      
      // Benutzerinformationen abrufen
      const userInfo = await zoteroAPI.getUserInfo();
      
      if (!userInfo.success) {
        return res.status(401).json({
          success: false,
          message: 'Fehler beim Abrufen der Benutzerinformationen',
          error: userInfo.error
        });
      }
      
      const userId = userInfo.data.userID;
      
      // Elemente abrufen
      const items = [];
      const errors = [];
      
      for (const itemKey of itemKeys) {
        const item = await zoteroAPI.getItem('user', userId, itemKey);
        
        if (item.success) {
          items.push(item.data);
        } else {
          errors.push({
            itemKey,
            error: item.error
          });
        }
      }
      
      // Konvertiere Zotero-Format in DeSci-Scholar-Format
      const desciItems = zoteroAPI.convertToDeSciFormat(items);
      
      // Wenn NFTs erstellt werden sollen und ein NFT-Dienst verfügbar ist
      let nftResults = [];
      if (createNFTs && nftService) {
        nftResults = await Promise.all(
          desciItems.filter(item => item.doi).map(async (item) => {
            try {
              // NFT-Metadaten erstellen
              const nftMetadata = {
                name: item.title,
                description: item.abstract || `${item.title} - ${item.authors.join(', ')}`,
                external_url: item.url || `https://doi.org/${item.doi}`,
                image: '', // Hier könnte ein Bild oder Thumbnail generiert werden
                attributes: [
                  { trait_type: 'Type', value: 'Scientific Paper' },
                  { trait_type: 'Source', value: 'Zotero' },
                  { trait_type: 'DOI', value: item.doi },
                  { trait_type: 'Authors', value: item.authors.join(', ') },
                  { trait_type: 'Journal', value: item.journal || 'Unknown' },
                  { trait_type: 'Publication Date', value: item.publishedDate || 'Unknown' }
                ],
                // Zusätzliche Metadaten für die Verifizierung
                doi: item.doi,
                zotero_key: item.zoteroData.key,
                publication_date: item.publishedDate
              };
              
              // NFT erstellen
              const nftResult = await nftService.createNFT({
                metadata: nftMetadata,
                contentUri: item.url || `https://doi.org/${item.doi}`
              });
              
              return {
                item,
                nft: nftResult.success ? {
                  tokenId: nftResult.tokenId,
                  transactionHash: nftResult.transactionHash,
                  metadata: nftMetadata
                } : null,
                success: nftResult.success,
                error: nftResult.success ? null : nftResult.error
              };
            } catch (error) {
              return {
                item,
                nft: null,
                success: false,
                error: error.message
              };
            }
          })
        );
      }
      
      return res.json({
        success: true,
        imported: desciItems.length,
        items: desciItems,
        errors: errors.length > 0 ? errors : null,
        nfts: createNFTs ? {
          created: nftResults.filter(r => r.success).length,
          failed: nftResults.filter(r => !r.success).length,
          results: nftResults
        } : null
      });
    } catch (error) {
      console.error('Fehler beim Importieren von DOIs aus Zotero:', error);
      return res.status(500).json({
        success: false,
        message: `Interner Serverfehler: ${error.message}`
      });
    }
  });
  
  return router;
}