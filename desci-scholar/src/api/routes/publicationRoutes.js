/**
 * API-Routen für die Publikationsverwaltung
 * Implementiert REST-Endpunkte für die Interaktion mit dem PublicationController
 */

import express from 'express';
import multer from 'multer';
import PublicationController from '../../controllers/PublicationController';
import { authMiddleware } from '../middleware/auth';

// Konfiguriere Multer für Datei-Uploads
const storage = multer.memoryStorage();
const upload = multer({ 
  storage,
  limits: { 
    fileSize: 100 * 1024 * 1024, // 100 MB Limit
    files: 1 
  }
});

/**
 * Erstellt und konfiguriert Router für Publikationen
 * @param {Object} config Konfigurationsobjekt
 * @returns {express.Router} Konfigurierter Router
 */
export function setupPublicationRoutes(config = {}) {
  const router = express.Router();
  
  // Initialisiere den PublicationController
  const controller = new PublicationController(config);
  
  // Middleware, die sicherstellt, dass der Controller initialisiert ist
  router.use(async (req, res, next) => {
    if (!controller.initialized) {
      try {
        const success = await controller.initialize();
        if (!success) {
          return res.status(500).json({
            success: false,
            message: 'Fehler beim Initialisieren des PublicationControllers'
          });
        }
      } catch (error) {
        return res.status(500).json({
          success: false,
          message: `Initialisierungsfehler: ${error.message}`
        });
      }
    }
    next();
  });
  
  // API-Endpunkte

  /**
   * @route GET /api/publications
   * @description Ruft Publikationen basierend auf Suchkriterien ab
   * @access Öffentlich
   */
  router.get('/', async (req, res) => {
    try {
      // Parse Suchkriterien aus Query-Parametern
      const criteria = {};
      
      if (req.query.keywords) {
        criteria.keywords = req.query.keywords.split(',').map(k => k.trim());
      }
      
      if (req.query.authors) {
        criteria.authors = req.query.authors.split(',').map(a => a.trim());
      }
      
      if (req.query.title) {
        criteria.title = req.query.title;
      }
      
      if (req.query.doi) {
        criteria.doi = req.query.doi;
      }
      
      if (req.query.status) {
        criteria.status = req.query.status;
      }
      
      // Parse Paginierungsparameter
      const options = {
        limit: parseInt(req.query.limit) || 20,
        offset: parseInt(req.query.offset) || 0
      };
      
      const result = await controller.searchPublications(criteria, options);
      
      if (result.success) {
        res.json(result);
      } else {
        res.status(400).json(result);
      }
    } catch (error) {
      res.status(500).json({
        success: false,
        message: `Fehler bei der Publikationssuche: ${error.message}`
      });
    }
  });
  
  /**
   * @route GET /api/publications/:id
   * @description Ruft eine einzelne Publikation anhand ihrer ID ab
   * @access Öffentlich
   */
  router.get('/:id', async (req, res) => {
    try {
      const includeContent = req.query.content === 'true';
      const result = await controller.getPublication(req.params.id, includeContent);
      
      if (result.success) {
        res.json(result);
      } else {
        res.status(404).json(result);
      }
    } catch (error) {
      res.status(500).json({
        success: false,
        message: `Fehler beim Abrufen der Publikation: ${error.message}`
      });
    }
  });
  
  /**
   * @route POST /api/publications
   * @description Erstellt eine neue Publikation
   * @access Privat - nur für authentifizierte Benutzer
   */
  router.post('/', authMiddleware, async (req, res) => {
    try {
      // Validiere minimale Eingaben
      if (!req.body.title || !req.body.authors || !req.body.abstract) {
        return res.status(400).json({
          success: false,
          message: 'Titel, Autoren und Abstract sind erforderlich'
        });
      }
      
      // Optional Benutzer-ID aus JWT-Token hinzufügen
      const publicationData = {
        ...req.body,
        submittedBy: req.user?.id
      };
      
      const result = await controller.createPublication(publicationData);
      
      if (result.success) {
        res.status(201).json(result);
      } else {
        res.status(400).json(result);
      }
    } catch (error) {
      res.status(500).json({
        success: false,
        message: `Fehler beim Erstellen der Publikation: ${error.message}`
      });
    }
  });
  
  /**
   * @route PUT /api/publications/:id
   * @description Aktualisiert eine Publikation oder erstellt eine neue Version
   * @access Privat - nur für authentifizierte Benutzer
   */
  router.put('/:id', authMiddleware, async (req, res) => {
    try {
      const createNewVersion = req.query.newVersion === 'true';
      
      const result = await controller.updatePublication(
        req.params.id,
        req.body,
        createNewVersion
      );
      
      if (result.success) {
        res.json(result);
      } else {
        res.status(400).json(result);
      }
    } catch (error) {
      res.status(500).json({
        success: false,
        message: `Fehler beim Aktualisieren der Publikation: ${error.message}`
      });
    }
  });
  
  /**
   * @route POST /api/publications/:id/publish
   * @description Veröffentlicht eine Publikation und registriert optional einen DOI
   * @access Privat - nur für authentifizierte Benutzer
   */
  router.post('/:id/publish', authMiddleware, async (req, res) => {
    try {
      const registerDOI = req.body.registerDOI === true;
      
      const result = await controller.publishPublication(req.params.id, registerDOI);
      
      if (result.success) {
        res.json(result);
      } else {
        res.status(400).json(result);
      }
    } catch (error) {
      res.status(500).json({
        success: false,
        message: `Fehler beim Veröffentlichen der Publikation: ${error.message}`
      });
    }
  });
  
  /**
   * @route POST /api/publications/:id/file
   * @description Lädt eine Datei zu einer Publikation hoch
   * @access Privat - nur für authentifizierte Benutzer
   */
  router.post('/:id/file', authMiddleware, upload.single('file'), async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({
          success: false,
          message: 'Keine Datei im Upload gefunden'
        });
      }
      
      const fileInfo = {
        name: req.file.originalname,
        type: req.file.mimetype,
        size: req.file.size
      };
      
      const result = await controller.uploadPublicationFile(
        req.params.id,
        req.file.buffer,
        fileInfo
      );
      
      if (result.success) {
        res.json(result);
      } else {
        res.status(400).json(result);
      }
    } catch (error) {
      res.status(500).json({
        success: false,
        message: `Fehler beim Hochladen der Datei: ${error.message}`
      });
    }
  });
  
  /**
   * @route GET /api/publications/:id/file
   * @description Lädt die Datei einer Publikation herunter
   * @access Öffentlich
   */
  router.get('/:id/file', async (req, res) => {
    try {
      const result = await controller.downloadPublicationFile(req.params.id, 'buffer');
      
      if (!result.success) {
        return res.status(404).json(result);
      }
      
      // Setze Header für den Download
      res.setHeader('Content-Type', result.fileType);
      res.setHeader('Content-Disposition', `attachment; filename="${result.filename}"`);
      res.setHeader('Content-Length', result.fileSize);
      
      // Sende die Datei als Download
      res.send(result.data);
    } catch (error) {
      res.status(500).json({
        success: false,
        message: `Fehler beim Herunterladen der Datei: ${error.message}`
      });
    }
  });
  
  /**
   * @route GET /api/publications/:id/cite
   * @description Generiert eine Zitation für eine Publikation im gewünschten Format
   * @access Öffentlich
   */
  router.get('/:id/cite', async (req, res) => {
    try {
      // Lade die Publikation
      const getResult = await controller.getPublication(req.params.id);
      
      if (!getResult.success) {
        return res.status(404).json(getResult);
      }
      
      const publication = getResult.publication;
      
      // Bestimme das Format
      const format = req.query.format || 'apa';
      const allowedFormats = ['apa', 'mla', 'chicago', 'harvard', 'bibtex', 'ris', 'endnote'];
      
      if (!allowedFormats.includes(format.toLowerCase())) {
        return res.status(400).json({
          success: false,
          message: `Ungültiges Zitationsformat. Erlaubte Formate: ${allowedFormats.join(', ')}`
        });
      }
      
      // Generiere die Zitation (hier würde der tatsächliche Zitationsgenerator verwendet werden)
      // Für dieses Beispiel erstellen wir eine einfache Zitation
      let citation;
      const year = new Date(publication.publishedDate).getFullYear();
      const authors = publication.authors.join(', ');
      
      switch(format.toLowerCase()) {
        case 'apa':
          citation = `${authors} (${year}). ${publication.title}. DeSci Scholar. https://doi.org/${publication.doi}`;
          break;
        case 'mla':
          citation = `${authors}. "${publication.title}." DeSci Scholar, ${year}. doi:${publication.doi}`;
          break;
        case 'bibtex':
          citation = `@article{${publication.id},
  author = {${publication.authors.join(' and ')}},
  title = {${publication.title}},
  journal = {DeSci Scholar},
  year = {${year}},
  doi = {${publication.doi}}
}`;
          break;
        default:
          citation = `${authors} (${year}). ${publication.title}. DeSci Scholar. DOI: ${publication.doi}`;
      }
      
      res.json({
        success: true,
        publication: {
          id: publication.id,
          title: publication.title
        },
        format,
        citation
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: `Fehler beim Generieren der Zitation: ${error.message}`
      });
    }
  });
  
  // Fehlerbehandlung für den Router
  router.use((err, req, res, next) => {
    console.error('API-Fehler in Publikationsrouten:', err);
    res.status(500).json({
      success: false,
      message: 'Interner Serverfehler',
      error: process.env.NODE_ENV === 'development' ? err.message : undefined
    });
  });
  
  return router;
}

/**
 * Erstellt und konfiguriert den Publikations-Router mit Standardeinstellungen
 */
const publicationRouter = setupPublicationRoutes();

export default publicationRouter; 