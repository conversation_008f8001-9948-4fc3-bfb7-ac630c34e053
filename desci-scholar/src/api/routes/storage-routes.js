/**
 * Speicher-API-Routen für DeSci-Scholar
 * 
 * Definiert REST-Endpunkte für das Hochladen, Herunterladen und Verwalten
 * von wissenschaftlichen Daten im dezentralen Speichersystem.
 */

import express from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { v4 as uuidv4 } from 'uuid';
import { body, param, validationResult } from 'express-validator';

/**
 * Erstellt einen Router für Speicher-Endpoints
 * 
 * @param {Object} options Service-Instanzen und Middleware
 * @param {Object} options.storageService EnhancedStorageService-Instanz
 * @param {Object} options.authService AuthService-Instanz
 * @param {Object} options.authMiddleware Authentifizierungs-Middleware
 * @returns {express.Router} Express-Router mit Speicherrouten
 */
export function createStorageRoutes(options) {
  const { storageService, authService, authMiddleware } = options;
  const router = express.Router();
  
  // Upload-Verzeichnis für temporäre Dateiablage konfigurieren
  const uploadDir = path.resolve('./uploads');
  
  // Sicherstellen, dass das Upload-Verzeichnis existiert
  if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
  }
  
  // Multer für Datei-Uploads konfigurieren
  const storage = multer.diskStorage({
    destination: (req, file, cb) => {
      cb(null, uploadDir);
    },
    filename: (req, file, cb) => {
      const uniqueFilename = `${uuidv4()}-${file.originalname}`;
      cb(null, uniqueFilename);
    }
  });
  
  // Multer-Upload-Konfiguration
  const upload = multer({
    storage,
    limits: {
      fileSize: 500 * 1024 * 1024, // 500 MB maximale Dateigröße
    },
    fileFilter: (req, file, cb) => {
      // Hier könnten Dateitypbeschränkungen hinzugefügt werden, wenn nötig
      cb(null, true);
    }
  });

  /**
   * Middleware zur Validierung von Anfragen
   */
  const validateRequest = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      // Wenn temporäre Dateien vorhanden sind, diese löschen
      if (req.file) {
        fs.unlinkSync(req.file.path);
      }
      
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }
    next();
  };

  /**
   * Hilfsfunktion zum Löschen temporärer Dateien nach Abschluss
   * 
   * @param {string} filePath Pfad zur temporären Datei
   */
  const cleanupTempFile = (filePath) => {
    try {
      if (filePath && fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    } catch (error) {
      console.error(`Fehler beim Löschen der temporären Datei ${filePath}:`, error);
    }
  };

  // === Öffentliche Routen ===

  // GET /api/storage/ipfs/:cid - Metadaten aus IPFS abrufen
  router.get('/ipfs/:cid', [
    param('cid').isString().withMessage('Ungültige CID'),
    validateRequest
  ], async (req, res) => {
    try {
      const { cid } = req.params;
      
      // Metadaten abrufen
      const metadata = await storageService.getMetadata(cid);
      
      if (!metadata) {
        return res.status(404).json({
          success: false,
          message: 'Metadaten nicht gefunden'
        });
      }
      
      res.json({
        success: true,
        data: metadata
      });
    } catch (error) {
      console.error('Fehler beim Abrufen der IPFS-Metadaten:', error);
      res.status(500).json({
        success: false,
        message: `Fehler beim Abrufen der Metadaten: ${error.message}`
      });
    }
  });

  // GET /api/storage/bt/:infoHash - BitTorrent-Magnetlink abrufen
  router.get('/bt/:infoHash', [
    param('infoHash').isString().withMessage('Ungültiger InfoHash'),
    validateRequest
  ], async (req, res) => {
    try {
      const { infoHash } = req.params;
      
      // Magnetlink abrufen
      const magnetlink = await storageService.getMagnetlink(infoHash);
      
      if (!magnetlink) {
        return res.status(404).json({
          success: false,
          message: 'Magnetlink nicht gefunden'
        });
      }
      
      res.json({
        success: true,
        data: {
          infoHash,
          magnetlink
        }
      });
    } catch (error) {
      console.error('Fehler beim Abrufen des Magnetlinks:', error);
      res.status(500).json({
        success: false,
        message: `Fehler beim Abrufen des Magnetlinks: ${error.message}`
      });
    }
  });

  // GET /api/storage/stats/:resourceId - Speicherstatistiken für eine Ressource abrufen
  router.get('/stats/:resourceId', [
    param('resourceId').isString().withMessage('Ungültige Ressourcen-ID'),
    validateRequest
  ], async (req, res) => {
    try {
      const { resourceId } = req.params;
      
      // Speicherstatistiken abrufen
      const stats = await storageService.getResourceStats(resourceId);
      
      if (!stats) {
        return res.status(404).json({
          success: false,
          message: 'Ressource nicht gefunden'
        });
      }
      
      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      console.error('Fehler beim Abrufen der Speicherstatistiken:', error);
      res.status(500).json({
        success: false,
        message: `Fehler beim Abrufen der Speicherstatistiken: ${error.message}`
      });
    }
  });

  // === Authentifizierte Routen ===

  // POST /api/storage/upload - Datei hochladen
  router.post('/upload', [
    authMiddleware.authenticate,
    upload.single('file'),
    body('metadata').optional().isObject().withMessage('Ungültiges Metadaten-Format'),
    body('description').optional().isString(),
    body('resourceType').optional().isString(),
    validateRequest
  ], async (req, res) => {
    // Pfad zur hochgeladenen Datei
    const filePath = req.file ? req.file.path : null;
    
    try {
      if (!filePath) {
        return res.status(400).json({
          success: false,
          message: 'Keine Datei hochgeladen'
        });
      }
      
      // Metadaten aus der Anfrage extrahieren
      let metadata = {};
      if (req.body.metadata) {
        try {
          // Falls metadata als String übergeben wurde, parsen
          metadata = JSON.parse(req.body.metadata);
        } catch (error) {
          console.error('Fehler beim Parsen der Metadaten:', error);
          return res.status(400).json({
            success: false,
            message: 'Ungültiges Metadaten-Format'
          });
        }
      }
      
      // Allgemeine Metadaten hinzufügen
      metadata.uploadedBy = req.user.userId;
      metadata.filename = req.file.originalname;
      metadata.mimeType = req.file.mimetype;
      metadata.size = req.file.size;
      metadata.description = req.body.description || '';
      metadata.resourceType = req.body.resourceType || 'generic';
      metadata.uploadDate = new Date().toISOString();
      
      // Datei im optimierten Speichersystem speichern
      const storageResult = await storageService.storeFile({
        filePath,
        metadata,
        options: {
          userId: req.user.userId,
          // Weitere Optionen können hier hinzugefügt werden
        }
      });
      
      // Temporäre Datei löschen
      cleanupTempFile(filePath);
      
      res.status(201).json({
        success: true,
        message: 'Datei erfolgreich gespeichert',
        data: {
          ...storageResult,
          metadata: {
            ...metadata,
            // Sensible Informationen filtern
            uploadedBy: undefined
          }
        }
      });
    } catch (error) {
      console.error('Fehler beim Hochladen der Datei:', error);
      
      // Temporäre Datei löschen, falls vorhanden
      cleanupTempFile(filePath);
      
      res.status(500).json({
        success: false,
        message: `Fehler beim Speichern der Datei: ${error.message}`
      });
    }
  });

  // POST /api/storage/metadata - Nur Metadaten speichern
  router.post('/metadata', [
    authMiddleware.authenticate,
    body('metadata').isObject().withMessage('Metadaten sind erforderlich'),
    validateRequest
  ], async (req, res) => {
    try {
      const { metadata } = req.body;
      
      // Benutzer-ID hinzufügen
      metadata.createdBy = req.user.userId;
      metadata.createdAt = new Date().toISOString();
      
      // Metadaten speichern
      const result = await storageService.storeMetadata(metadata);
      
      res.status(201).json({
        success: true,
        message: 'Metadaten erfolgreich gespeichert',
        data: result
      });
    } catch (error) {
      console.error('Fehler beim Speichern der Metadaten:', error);
      res.status(500).json({
        success: false,
        message: `Fehler beim Speichern der Metadaten: ${error.message}`
      });
    }
  });

  // DELETE /api/storage/unpublish/:resourceId - Ressource aus dem Netzwerk entfernen
  router.delete('/unpublish/:resourceId', [
    authMiddleware.authenticate,
    param('resourceId').isString().withMessage('Ungültige Ressourcen-ID'),
    validateRequest
  ], async (req, res) => {
    try {
      const { resourceId } = req.params;
      
      // Ressource aus dem Netzwerk entfernen
      const result = await storageService.unpublishResource(resourceId, {
        userId: req.user.userId
      });
      
      res.json({
        success: true,
        message: 'Ressource erfolgreich entfernt',
        data: result
      });
    } catch (error) {
      console.error('Fehler beim Entfernen der Ressource:', error);
      res.status(500).json({
        success: false,
        message: `Fehler beim Entfernen der Ressource: ${error.message}`
      });
    }
  });

  // GET /api/storage/user-resources - Alle Ressourcen des angemeldeten Benutzers abrufen
  router.get('/user-resources', [
    authMiddleware.authenticate,
    validateRequest
  ], async (req, res) => {
    try {
      // Ressourcen des Benutzers abrufen
      const resources = await storageService.getUserResources(req.user.userId);
      
      res.json({
        success: true,
        data: {
          total: resources.length,
          resources
        }
      });
    } catch (error) {
      console.error('Fehler beim Abrufen der Benutzerressourcen:', error);
      res.status(500).json({
        success: false,
        message: `Fehler beim Abrufen der Ressourcen: ${error.message}`
      });
    }
  });

  // === Admin-Routen ===

  // GET /api/storage/system-stats - Systemstatistiken abrufen (nur für Administratoren)
  router.get('/system-stats', [
    authMiddleware.authenticate,
    authMiddleware.requireRole('admin'),
    validateRequest
  ], async (req, res) => {
    try {
      // Systemstatistiken abrufen
      const stats = await storageService.getSystemStats();
      
      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      console.error('Fehler beim Abrufen der Systemstatistiken:', error);
      res.status(500).json({
        success: false,
        message: `Fehler beim Abrufen der Systemstatistiken: ${error.message}`
      });
    }
  });

  return router;
}

export default createStorageRoutes;