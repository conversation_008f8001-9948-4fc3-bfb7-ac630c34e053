/**
 * @fileoverview Semantic Scholar API-Routen für DeSci-Scholar
 * 
 * <PERSON><PERSON>i definiert Express-Routen für die Integration mit der
 * Semantic Scholar Academic Graph API.
 */

import express from 'express';
import SemanticScholarAPI from './integrations/SemanticScholarAPI.js';

const router = express.Router();

// Semantic Scholar API-Client initialisieren
const semanticScholarApi = new SemanticScholarAPI({
  apiKey: process.env.S2_API_KEY,
  useMcpServer: process.env.USE_MCP_SERVER === 'true',
  mcpServerUrl: process.env.MCP_SERVER_URL || 'http://localhost:3001'
});

// Middleware für API-Initialisierung
const ensureApiInitialized = async (req, res, next) => {
  try {
    if (!semanticScholarApi._initialized) {
      semanticScholarApi._initialized = await semanticScholarApi.initialize();
      
      if (!semanticScholarApi._initialized) {
        return res.status(503).json({
          error: 'Semantic Scholar API ist nicht verfügbar'
        });
      }
    }
    
    next();
  } catch (error) {
    console.error('Fehler bei der API-Initialisierung:', error);
    res.status(503).json({
      error: 'Semantic Scholar API konnte nicht initialisiert werden'
    });
  }
};

// Alle Routen mit der Initialisierungs-Middleware versehen
router.use(ensureApiInitialized);

/**
 * @route GET /api/semantic-scholar/papers/search
 * @desc Suche nach Publikationen
 * @access Public
 */
router.get('/papers/search', async (req, res) => {
  try {
    const { query, limit, offset, fields, year, venue } = req.query;
    
    if (!query) {
      return res.status(400).json({
        error: 'Suchanfrage ist erforderlich'
      });
    }
    
    const results = await semanticScholarApi.searchPapers({
      query,
      limit: limit ? parseInt(limit, 10) : undefined,
      offset: offset ? parseInt(offset, 10) : undefined,
      fields,
      year,
      venue
    });
    
    res.json(results);
  } catch (error) {
    console.error('Fehler bei der Publikationssuche:', error);
    res.status(500).json({
      error: `Fehler bei der Publikationssuche: ${error.message}`
    });
  }
});

/**
 * @route GET /api/semantic-scholar/papers/:paperId
 * @desc Details zu einer Publikation abrufen
 * @access Public
 */
router.get('/papers/:paperId', async (req, res) => {
  try {
    const { paperId } = req.params;
    const { fields } = req.query;
    
    const paper = await semanticScholarApi.getPaper(paperId, fields);
    
    res.json(paper);
  } catch (error) {
    console.error('Fehler beim Abrufen der Publikationsdetails:', error);
    res.status(500).json({
      error: `Fehler beim Abrufen der Publikationsdetails: ${error.message}`
    });
  }
});

/**
 * @route GET /api/semantic-scholar/papers/:paperId/citations
 * @desc Zitationen einer Publikation abrufen
 * @access Public
 */
router.get('/papers/:paperId/citations', async (req, res) => {
  try {
    const { paperId } = req.params;
    const { limit, offset, fields } = req.query;
    
    const citations = await semanticScholarApi.getCitations(paperId, {
      limit: limit ? parseInt(limit, 10) : undefined,
      offset: offset ? parseInt(offset, 10) : undefined,
      fields
    });
    
    res.json(citations);
  } catch (error) {
    console.error('Fehler beim Abrufen der Zitationen:', error);
    res.status(500).json({
      error: `Fehler beim Abrufen der Zitationen: ${error.message}`
    });
  }
});

/**
 * @route GET /api/semantic-scholar/papers/:paperId/references
 * @desc Referenzen einer Publikation abrufen
 * @access Public
 */
router.get('/papers/:paperId/references', async (req, res) => {
  try {
    const { paperId } = req.params;
    const { limit, offset, fields } = req.query;
    
    const references = await semanticScholarApi.getReferences(paperId, {
      limit: limit ? parseInt(limit, 10) : undefined,
      offset: offset ? parseInt(offset, 10) : undefined,
      fields
    });
    
    res.json(references);
  } catch (error) {
    console.error('Fehler beim Abrufen der Referenzen:', error);
    res.status(500).json({
      error: `Fehler beim Abrufen der Referenzen: ${error.message}`
    });
  }
});

/**
 * @route GET /api/semantic-scholar/authors/search
 * @desc Suche nach Autoren
 * @access Public
 */
router.get('/authors/search', async (req, res) => {
  try {
    const { query, limit, offset, fields } = req.query;
    
    if (!query) {
      return res.status(400).json({
        error: 'Suchanfrage ist erforderlich'
      });
    }
    
    const results = await semanticScholarApi.searchAuthors({
      query,
      limit: limit ? parseInt(limit, 10) : undefined,
      offset: offset ? parseInt(offset, 10) : undefined,
      fields
    });
    
    res.json(results);
  } catch (error) {
    console.error('Fehler bei der Autorensuche:', error);
    res.status(500).json({
      error: `Fehler bei der Autorensuche: ${error.message}`
    });
  }
});

/**
 * @route GET /api/semantic-scholar/authors/:authorId
 * @desc Details zu einem Autor abrufen
 * @access Public
 */
router.get('/authors/:authorId', async (req, res) => {
  try {
    const { authorId } = req.params;
    const { fields } = req.query;
    
    const author = await semanticScholarApi.getAuthor(authorId, fields);
    
    res.json(author);
  } catch (error) {
    console.error('Fehler beim Abrufen der Autorendetails:', error);
    res.status(500).json({
      error: `Fehler beim Abrufen der Autorendetails: ${error.message}`
    });
  }
});

/**
 * @route GET /api/semantic-scholar/authors/:authorId/papers
 * @desc Publikationen eines Autors abrufen
 * @access Public
 */
router.get('/authors/:authorId/papers', async (req, res) => {
  try {
    const { authorId } = req.params;
    const { limit, offset, fields } = req.query;
    
    const papers = await semanticScholarApi.getAuthorPapers(authorId, {
      limit: limit ? parseInt(limit, 10) : undefined,
      offset: offset ? parseInt(offset, 10) : undefined,
      fields
    });
    
    res.json(papers);
  } catch (error) {
    console.error('Fehler beim Abrufen der Autorenpublikationen:', error);
    res.status(500).json({
      error: `Fehler beim Abrufen der Autorenpublikationen: ${error.message}`
    });
  }
});

/**
 * @route GET /api/semantic-scholar/snippets/search
 * @desc Suche nach Textausschnitten
 * @access Public
 */
router.get('/snippets/search', async (req, res) => {
  try {
    const { query, limit } = req.query;
    
    if (!query) {
      return res.status(400).json({
        error: 'Suchanfrage ist erforderlich'
      });
    }
    
    const results = await semanticScholarApi.searchSnippets({
      query,
      limit: limit ? parseInt(limit, 10) : undefined
    });
    
    res.json(results);
  } catch (error) {
    console.error('Fehler bei der Textausschnittsuche:', error);
    res.status(500).json({
      error: `Fehler bei der Textausschnittsuche: ${error.message}`
    });
  }
});

/**
 * @route GET /api/semantic-scholar/stats
 * @desc API-Nutzungsstatistiken abrufen
 * @access Public
 */
router.get('/stats', (req, res) => {
  try {
    const stats = semanticScholarApi.getStats();
    res.json(stats);
  } catch (error) {
    console.error('Fehler beim Abrufen der API-Statistiken:', error);
    res.status(500).json({
      error: `Fehler beim Abrufen der API-Statistiken: ${error.message}`
    });
  }
});

export default router;