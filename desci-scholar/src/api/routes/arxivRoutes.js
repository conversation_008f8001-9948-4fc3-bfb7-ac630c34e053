/**
 * @fileoverview Routen für die arXiv-Integration
 *
 * Die<PERSON>i definiert die API-Routen für die Interaktion mit arXiv,
 * einsch<PERSON>ßlich Suche, Import und NFT-Erstellung für arXiv-Publikationen.
 */

import express from 'express';
import ArXivIntegrationController from '../../controllers/ArXivIntegrationController.js';
import { StorageService } from '../../storage/StorageService.js';
import { createAuthMiddleware } from '../middleware/auth-middleware.js';

// Initialisiere Router
const router = express.Router();

// Initialisiere Auth-Middleware
const { authenticate } = createAuthMiddleware({});

// Initialisiere StorageService
const storageService = new StorageService();

// Cache- und Rate-Limit-Konfiguration
const cacheConfig = {
  ttl: 24 * 60 * 60 * 1000, // 24 Stunden
  maxItems: 1000
};

const rateLimitConfig = {
  maxRequestsPerMinute: 30,
  timeWindow: 60 * 1000 // 1 Minute
};

// Initialisiere Controller
// Hinweis: In einer realen Anwendung würden crossrefAPI und nftService
// aus einer zentralen Konfiguration oder Dependency Injection kommen
const arxivController = new ArXivIntegrationController({
  storageService,
  cacheConfig,
  rateLimitConfig
});

/**
 * @route GET /api/arxiv/search
 * @desc Sucht nach Publikationen in arXiv
 * @access Public
 */
router.get('/search', async (req, res) => {
  try {
    const {
      query,
      title,
      author,
      category,
      id,
      maxResults = 10,
      start = 0,
      sortBy = 'relevance',
      sortOrder = 'descending'
    } = req.query;
    
    // Validiere Anfrage
    if (!query && !title && !author && !category && !id) {
      return res.status(400).json({
        success: false,
        error: 'Mindestens ein Suchparameter (query, title, author, category, id) muss angegeben werden'
      });
    }
    
    // Führe die Suche durch
    const searchParams = {
      query,
      title,
      author,
      category,
      id,
      maxResults: parseInt(maxResults),
      start: parseInt(start),
      sortBy,
      sortOrder
    };
    
    const result = await arxivController.searchPapers(searchParams);
    
    if (result.success) {
      return res.status(200).json(result);
    } else {
      return res.status(500).json(result);
    }
  } catch (error) {
    return res.status(500).json({
      success: false,
      error: 'Fehler bei der Suche nach arXiv-Publikationen',
      message: error.message
    });
  }
});

/**
 * @route GET /api/arxiv/paper/:id
 * @desc Ruft Details zu einer Publikation anhand ihrer arXiv-ID ab
 * @access Public
 */
router.get('/paper/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const result = await arxivController.getPaper(id);
    
    if (result.success) {
      return res.status(200).json(result);
    } else {
      return res.status(404).json(result);
    }
  } catch (error) {
    return res.status(500).json({
      success: false,
      error: 'Fehler beim Abrufen der Publikationsdetails',
      message: error.message
    });
  }
});

/**
 * @route GET /api/arxiv/recent/:category
 * @desc Ruft die neuesten Publikationen in einer bestimmten Kategorie ab
 * @access Public
 */
router.get('/recent/:category', async (req, res) => {
  try {
    const { category } = req.params;
    const { maxResults = 10 } = req.query;
    
    const result = await arxivController.getRecentPapers(category, parseInt(maxResults));
    
    if (result.success) {
      return res.status(200).json(result);
    } else {
      return res.status(500).json(result);
    }
  } catch (error) {
    return res.status(500).json({
      success: false,
      error: 'Fehler beim Abrufen der neuesten Publikationen',
      message: error.message
    });
  }
});

/**
 * @route POST /api/arxiv/import/:id
 * @desc Importiert eine arXiv-Publikation in DeSci-Scholar
 * @access Protected
 */
router.post('/import/:id', authenticate, async (req, res) => {
  try {
    const { id } = req.params;
    const { createDOI = true, mintNFT = false, storeData = true } = req.body;
    
    const result = await arxivController.processPaper(id, {
      createDOI,
      mintNFT,
      storeData
    });
    
    if (result.success) {
      return res.status(200).json(result);
    } else {
      return res.status(500).json(result);
    }
  } catch (error) {
    return res.status(500).json({
      success: false,
      error: 'Fehler beim Import der arXiv-Publikation',
      message: error.message
    });
  }
});

/**
 * @route POST /api/arxiv/import
 * @desc Importiert mehrere arXiv-Publikationen basierend auf einer Suchanfrage
 * @access Protected
 */
router.post('/import', authenticate, async (req, res) => {
  try {
    const { query, maxResults = 10, createDOI = true, mintNFT = false, storeData = true } = req.body;
    
    // Validiere Anfrage
    if (!query) {
      return res.status(400).json({
        success: false,
        error: 'Suchanfrage ist erforderlich'
      });
    }
    
    // Führe die Suche durch
    const searchResult = await arxivController.searchPapers({
      query,
      maxResults: parseInt(maxResults)
    });
    
    if (!searchResult.success) {
      return res.status(500).json(searchResult);
    }
    
    // Importiere die gefundenen Publikationen
    const importResults = [];
    
    for (const paper of searchResult.data) {
      const importResult = await arxivController.processPaper(paper.id, {
        createDOI,
        mintNFT,
        storeData
      });
      
      importResults.push(importResult);
    }
    
    return res.status(200).json({
      success: true,
      query,
      totalResults: searchResult.data.length,
      importedResults: importResults
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      error: 'Fehler beim Import der arXiv-Publikationen',
      message: error.message
    });
  }
});

/**
 * @route POST /api/arxiv/nft/:id
 * @desc Erstellt ein NFT für eine arXiv-Publikation
 * @access Protected
 */
router.post('/nft/:id', authenticate, async (req, res) => {
  try {
    const { id } = req.params;
    
    // Importiere das Paper und erstelle ein NFT
    const result = await arxivController.processPaper(id, {
      createDOI: true,
      mintNFT: true,
      storeData: true
    });
    
    if (result.success && result.steps.nft && result.steps.nft.success) {
      return res.status(200).json({
        success: true,
        arxivId: id,
        doi: result.doi,
        tokenId: result.tokenId,
        tokenURI: result.tokenURI
      });
    } else {
      return res.status(500).json({
        success: false,
        error: result.steps.nft ? result.steps.nft.error : 'NFT konnte nicht erstellt werden',
        arxivId: id
      });
    }
  } catch (error) {
    return res.status(500).json({
      success: false,
      error: 'Fehler bei der NFT-Erstellung',
      message: error.message
    });
  }
});

/**
 * @route POST /api/arxiv/nft
 * @desc Erstellt NFTs für arXiv-Publikationen basierend auf einer Suchanfrage
 * @access Protected
 */
router.post('/nft', authenticate, async (req, res) => {
  try {
    const { query, maxResults = 5 } = req.body;
    
    // Validiere Anfrage
    if (!query) {
      return res.status(400).json({
        success: false,
        error: 'Suchanfrage ist erforderlich'
      });
    }
    
    // Führe die Suche durch
    const searchResult = await arxivController.searchPapers({
      query,
      maxResults: parseInt(maxResults)
    });
    
    if (!searchResult.success) {
      return res.status(500).json(searchResult);
    }
    
    // Erstelle NFTs für die gefundenen Publikationen
    const nftResults = [];
    
    for (const paper of searchResult.data) {
      const nftResult = await arxivController.processPaper(paper.id, {
        createDOI: true,
        mintNFT: true,
        storeData: true
      });
      
      nftResults.push({
        arxivId: paper.id,
        title: paper.title,
        success: nftResult.success && nftResult.steps.nft && nftResult.steps.nft.success,
        doi: nftResult.doi,
        tokenId: nftResult.tokenId,
        tokenURI: nftResult.tokenURI,
        error: nftResult.steps.nft && !nftResult.steps.nft.success ? nftResult.steps.nft.error : null
      });
    }
    
    return res.status(200).json({
      success: true,
      query,
      totalResults: searchResult.data.length,
      nftResults
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      error: 'Fehler bei der NFT-Erstellung',
      message: error.message
    });
  }
});

/**
 * @route GET /api/arxiv/stats
 * @desc Ruft die Statistiken der arXiv-Integration ab
 * @access Protected
 */
router.get('/stats', authenticate, (req, res) => {
  try {
    const stats = arxivController.getStats();
    
    return res.status(200).json(stats);
  } catch (error) {
    return res.status(500).json({
      success: false,
      error: 'Fehler beim Abrufen der Statistiken',
      message: error.message
    });
  }
});

/**
 * @route POST /api/arxiv/cache/clear
 * @desc Löscht den Cache der arXiv-Integration
 * @access Protected
 */
router.post('/cache/clear', authenticate, (req, res) => {
  try {
    const { type = 'all' } = req.body;

    // Validiere Cache-Typ
    const validTypes = ['papers', 'searches', 'recentPapers', 'all'];
    if (!validTypes.includes(type)) {
      return res.status(400).json({
        success: false,
        error: `Ungültiger Cache-Typ: ${type}. Gültige Typen sind: ${validTypes.join(', ')}`
      });
    }

    const result = arxivController.clearCache(type);

    return res.status(200).json(result);
  } catch (error) {
    return res.status(500).json({
      success: false,
      error: 'Fehler beim Löschen des Caches',
      message: error.message
    });
  }
});

export default router;