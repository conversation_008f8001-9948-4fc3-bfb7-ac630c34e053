/**
 * Statistik-API-Routen für DeSci-Scholar
 * 
 * Definiert REST-Endpunkte zum Abrufen verschiedener Statistiken
 * zur Plattformnutzung, Veröffentlichungen und Speichernutzung.
 */

import express from 'express';
import { query, validationResult } from 'express-validator';

/**
 * Erstellt einen Router für Statistik-Endpoints
 * 
 * @param {Object} options Service-Instanzen und Middleware
 * @param {Object} options.statsService StatsService-Instanz
 * @param {Object} options.authService AuthService-Instanz
 * @param {Object} options.authMiddleware Authentifizierungs-Middleware
 * @returns {express.Router} Express-Router mit Statistikrouten
 */
export function createStatsRoutes(options) {
  const { statsService, authService, authMiddleware } = options;
  const router = express.Router();

  /**
   * Middleware zur Validierung von Anfragen
   */
  const validateRequest = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }
    next();
  };

  // === Öffentliche Routen ===

  // GET /api/stats/publications - Gesamtstatistiken zu Publikationen abrufen
  router.get('/publications', [
    validateRequest
  ], async (req, res) => {
    try {
      const stats = await statsService.getTotalStats();
      res.json({
        success: true,
        data: {
          totalPublications: stats.publications,
          totalViews: stats.views,
          totalDownloads: stats.downloads,
          averageDownloadsPerPublication: stats.avgDownloadsPerPublication,
          lastUpdated: stats.lastUpdated
        }
      });
    } catch (error) {
      console.error('Fehler beim Abrufen der Publikationsstatistiken:', error);
      res.status(500).json({
        success: false,
        error: 'Interner Serverfehler beim Abrufen der Publikationsstatistiken'
      });
    }
  });

  // GET /api/stats/storage - Speicherplatzstatistiken abrufen
  router.get('/storage', [
    validateRequest
  ], async (req, res) => {
    try {
      const storageStats = await statsService.getStorageStats();
      res.json({
        success: true,
        data: storageStats
      });
    } catch (error) {
      console.error('Fehler beim Abrufen der Speicherstatistiken:', error);
      res.status(500).json({
        success: false,
        error: 'Interner Serverfehler beim Abrufen der Speicherstatistiken'
      });
    }
  });

  // GET /api/stats/users - Allgemeine Benutzerstatistiken abrufen
  router.get('/users', [
    validateRequest
  ], async (req, res) => {
    try {
      const userStats = await statsService.getUserStats();
      res.json({
        success: true,
        data: userStats
      });
    } catch (error) {
      console.error('Fehler beim Abrufen der Benutzerstatistiken:', error);
      res.status(500).json({
        success: false,
        error: 'Interner Serverfehler beim Abrufen der Benutzerstatistiken'
      });
    }
  });

  // GET /api/stats/downloads - Download-Statistiken abrufen
  router.get('/downloads', [
    query('start').optional().isDate().toDate(),
    query('end').optional().isDate().toDate(),
    query('groupBy').optional().isString().isIn(['day', 'week', 'month']),
    validateRequest
  ], async (req, res) => {
    try {
      const { start, end, groupBy } = req.query;
      
      let startDate, endDate;
      
      if (start) {
        startDate = new Date(start);
        if (isNaN(startDate.getTime())) {
          return res.status(400).json({
            success: false,
            error: 'Ungültiges Startdatum'
          });
        }
      }
      
      if (end) {
        endDate = new Date(end);
        if (isNaN(endDate.getTime())) {
          return res.status(400).json({
            success: false,
            error: 'Ungültiges Enddatum'
          });
        }
      }
      
      // Validiere groupBy-Parameter
      const validGroupings = ['day', 'week', 'month'];
      const validGroupBy = validGroupings.includes(groupBy) ? groupBy : 'day';
      
      const downloadStats = await statsService.getDownloadStats({
        startDate,
        endDate,
        groupBy: validGroupBy
      });
      
      res.json({
        success: true,
        data: {
          groupBy: validGroupBy,
          stats: downloadStats
        }
      });
    } catch (error) {
      console.error('Fehler beim Abrufen der Download-Statistiken:', error);
      res.status(500).json({
        success: false,
        error: 'Interner Serverfehler beim Abrufen der Download-Statistiken'
      });
    }
  });

  // GET /api/stats/trending - Trendende Publikationen abrufen
  router.get('/trending', [
    query('period').optional().isString().isIn(['day', 'week', 'month', 'year']),
    query('limit').optional().isInt({ min: 1, max: 50 }).toInt(),
    validateRequest
  ], async (req, res) => {
    try {
      const { period = 'week', limit = 10 } = req.query;
      
      const trendingPublications = await statsService.getTrendingPublications({
        period,
        limit: parseInt(limit, 10)
      });
      
      res.json({
        success: true,
        data: trendingPublications
      });
    } catch (error) {
      console.error('Fehler beim Abrufen der trendenden Publikationen:', error);
      res.status(500).json({
        success: false,
        message: `Fehler beim Abrufen der trendenden Publikationen: ${error.message}`
      });
    }
  });

  // GET /api/stats/publication/:id - Statistiken für eine bestimmte Publikation abrufen
  router.get('/publication/:id', [
    validateRequest
  ], async (req, res) => {
    try {
      const publicationId = req.params.id;
      
      if (!publicationId) {
        return res.status(400).json({
          success: false,
          error: 'Publikations-ID erforderlich'
        });
      }
      
      const publicationStats = await statsService.getPublicationStats(publicationId);
      
      res.json({
        success: true,
        data: publicationStats
      });
    } catch (error) {
      console.error(`Fehler beim Abrufen der Statistiken für Publikation ${req.params.id}:`, error);
      
      // Spezifische Fehlerbehandlung für "nicht gefunden"
      if (error.message && error.message.includes('nicht gefunden')) {
        return res.status(404).json({
          success: false,
          error: `Publikation mit ID ${req.params.id} nicht gefunden`
        });
      }
      
      res.status(500).json({
        success: false,
        error: 'Interner Serverfehler beim Abrufen der Publikationsstatistiken'
      });
    }
  });

  // === Authentifizierte Routen ===

  // GET /api/stats/user/publications - Statistiken zu eigenen Publikationen abrufen
  router.get('/user/publications', [
    authMiddleware.authenticate,
    validateRequest
  ], async (req, res) => {
    try {
      const userPublicationStats = await statsService.getUserPublicationStats(req.user.userId);
      
      res.json({
        success: true,
        data: userPublicationStats
      });
    } catch (error) {
      console.error('Fehler beim Abrufen der Benutzer-Publikationsstatistiken:', error);
      res.status(500).json({
        success: false,
        message: `Fehler beim Abrufen der Statistiken: ${error.message}`
      });
    }
  });

  // GET /api/stats/user/storage - Speicherplatzstatistiken des Benutzers abrufen
  router.get('/user/storage', [
    authMiddleware.authenticate,
    validateRequest
  ], async (req, res) => {
    try {
      const userStorageStats = await statsService.getUserStorageStats(req.user.userId);
      
      res.json({
        success: true,
        data: userStorageStats
      });
    } catch (error) {
      console.error('Fehler beim Abrufen der Benutzer-Speicherstatistiken:', error);
      res.status(500).json({
        success: false,
        message: `Fehler beim Abrufen der Statistiken: ${error.message}`
      });
    }
  });

  // === Admin-Routen ===

  // GET /api/stats/admin/overview - Administrative Übersichtsstatistiken abrufen
  router.get('/admin/overview', [
    authMiddleware.authenticate,
    authMiddleware.requireRole('admin'),
    validateRequest
  ], async (req, res) => {
    try {
      const overviewStats = await statsService.getAdminOverviewStats();
      
      res.json({
        success: true,
        data: overviewStats
      });
    } catch (error) {
      console.error('Fehler beim Abrufen der Übersichtsstatistiken:', error);
      res.status(500).json({
        success: false,
        message: `Fehler beim Abrufen der Statistiken: ${error.message}`
      });
    }
  });

  // GET /api/stats/admin/storage-usage - Detaillierte Speichernutzungsstatistiken abrufen
  router.get('/admin/storage-usage', [
    authMiddleware.authenticate,
    authMiddleware.requireRole('admin'),
    validateRequest
  ], async (req, res) => {
    try {
      const storageUsageStats = await statsService.getDetailedStorageStats();
      
      res.json({
        success: true,
        data: storageUsageStats
      });
    } catch (error) {
      console.error('Fehler beim Abrufen der detaillierten Speicherstatistiken:', error);
      res.status(500).json({
        success: false,
        message: `Fehler beim Abrufen der Statistiken: ${error.message}`
      });
    }
  });

  // GET /api/stats/admin/activity - Aktivitätsstatistiken abrufen
  router.get('/admin/activity', [
    authMiddleware.authenticate,
    authMiddleware.requireRole('admin'),
    query('startDate').optional().isDate().toDate(),
    query('endDate').optional().isDate().toDate(),
    validateRequest
  ], async (req, res) => {
    try {
      const { startDate, endDate } = req.query;
      
      // Datumsbereich erstellen
      const dateRange = {};
      if (startDate) dateRange.from = startDate;
      if (endDate) dateRange.to = endDate;
      
      const activityStats = await statsService.getActivityStats(dateRange);
      
      res.json({
        success: true,
        data: activityStats
      });
    } catch (error) {
      console.error('Fehler beim Abrufen der Aktivitätsstatistiken:', error);
      res.status(500).json({
        success: false,
        message: `Fehler beim Abrufen der Statistiken: ${error.message}`
      });
    }
  });

  // POST /api/stats/record - Zeichnet einen Zugriff oder eine Interaktion auf
  router.post('/record', optionalAuthenticateenticateenticate, async (req, res) => {
    try {
      const { publicationId, type, metadata } = req.body;
      
      // Validiere erforderliche Parameter
      if (!publicationId || !type) {
        return res.status(400).json({
          success: false,
          error: 'PublicationId und type sind erforderliche Parameter'
        });
      }
      
      // Validiere den Zugriffstyp
      const validTypes = ['view', 'download', 'citation'];
      if (!validTypes.includes(type)) {
        return res.status(400).json({
          success: false,
          error: `Ungültiger Zugriffstyp. Erlaubte Werte: ${validTypes.join(', ')}`
        });
      }
      
      // Füge Benutzer-ID hinzu, falls authentifiziert
      const userId = req.user ? req.user.useruseruserId : null;
      
      // Erfasse den Zugriff
      await statsService.recordAccess({
        publicationId,
        type,
        userId,
        metadata: {
          ...metadata,
          userAgent: req.headers['user-agent'],
          ip: req.ip // Speichern der IP-Adresse gemäß Datenschutzrichtlinien
        }
      });
      
      res.json({
        success: true,
        message: 'Zugriff erfolgreich aufgezeichnet'
      });
    } catch (error) {
      console.error('Fehler beim Aufzeichnen des Zugriffs:', error);
      res.status(500).json({
        success: false,
        error: 'Interner Serverfehler beim Aufzeichnen des Zugriffs'
      });
    }
  });

  return router;
}

export default createStatsRoutes; 