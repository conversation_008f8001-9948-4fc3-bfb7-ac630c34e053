# API-Routen für DeSci-Scholar

<PERSON><PERSON> Verzeichnis enthält alle API-Routen für die DeSci-Scholar Plattform.

## Aktuelle Routen

- `auth-routes.js` - Authentifizierungsrouten (Login, Registrierung, etc.)
- `search-routes.js` - Suchrouten für die Plattform
- `publication-routes.js` - Hauptrouten für die Publikationsverwaltung
- `storage-routes.js` - Routen für die Speicherverwaltung
- `stats-routes.js` - Routen für Statistiken
- `semantic-scholar-routes.js` - Integration mit Semantic Scholar
- `arxivRoutes.js` - Integration mit arXiv
- `zotero-routes.js` - Integration mit Zotero
- `doi-nft-routes.js` - Routen für die DOI-zu-NFT-Konvertierung

## Legacy-Routen

Die folgenden Dateien sind veraltet und sollten in neuen Code nicht mehr verwendet werden:

- `publication-routes-legacy.js` - Alte Version der Publikationsrouten
- `doiRoutes.js` - Alte Version der DOI-Routen

## Hinweise zur Verwendung

Alle Routen werden in der Hauptdatei `src/api/index.js` registriert. Neue Routen sollten dort hinzugefügt werden.

Die Namenskonvention für neue Routendateien ist `feature-routes.js` (mit Bindestrich).

## Authentifizierung

Die meisten Routen verwenden die `auth-middleware.js` für die Authentifizierung. Diese bietet verschiedene Funktionen:

- `authenticate` - Prüft, ob der Benutzer authentifiziert ist
- `requirePermission` - Prüft, ob der Benutzer eine bestimmte Berechtigung hat
- `requireRole` - Prüft, ob der Benutzer eine bestimmte Rolle hat
- `requireOwnership` - Prüft, ob der Benutzer der Eigentümer einer Ressource ist
- `optionalAuthenticate` - Authentifiziert den Benutzer, wenn ein Token vorhanden ist, aber blockiert die Anfrage nicht, wenn kein Token vorhanden ist
- `logApiRequest` - Protokolliert API-Anfragen

## Fehlerbehandlung

Alle Routen sollten eine konsistente Fehlerbehandlung implementieren:

```javascript
try {
  // Route-Logik
} catch (error) {
  console.error('Fehler in Route:', error);
  res.status(500).json({
    success: false,
    message: `Fehlermeldung: ${error.message}`
  });
}
```