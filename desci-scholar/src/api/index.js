
/**
 * API-Integration für DeSci-Scholar
 *
 * Zentraler Einstiegspunkt für die REST-API der DeSci-Scholar Plattform.
 * Konfiguriert Express mit Middleware und registriert alle API-Routen.
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import cookieParser from 'cookie-parser';
import morgan from 'morgan';
import compression from 'compression';
import rateLimit from 'express-rate-limit';

import createAuthRoutes from './routes/auth-routes.js';
import createSearchRoutes from './routes/search-routes.js';
import createPublicationRoutes from './routes/publication-routes.js';
import createStorageRoutes from './routes/storage-routes.js';
import createStatsRoutes from './routes/stats-routes.js';
import semanticScholarRoutes from './routes/semantic-scholar-routes.js';
import arxivRoutes from './routes/arxivRoutes.js';
import createZoteroRoutes from './routes/zotero-routes.js';
import createDoiNftRoutes from './routes/doi-nft-routes.js';

import { createAuthMiddleware } from './middleware/auth-middleware.js';

/**
 * Erstellt und konfiguriert die Express-App mit allen API-Routen
 * 
 * @param {Object} options Konfigurationsoptionen
 * @param {Object} options.services Dienst-Instanzen (authService, searchService, usw.)
 * @param {Object} options.config API-Konfigurationsoptionen
 * @returns {express.Application} Konfigurierte Express-App
 */
export function createAPI(options = {}) {
  const { services = {}, config = {} } = options;
  
  // Express-App erstellen
  const app = express();
  
  // Middleware konfigurieren
  configureMiddleware(app, config);

  // Auth-Middleware erstellen
  const authMiddleware = createAuthMiddleware(services);

  // API-Routen registrieren
  registerRoutes(app, services, authMiddleware);

  // Fehlerbehandlung
  configureErrorHandling(app);
  
  return app;
}

/**
 * Konfiguriert die Express-Middleware
 * 
 * @param {express.Application} app Express-App
 * @param {Object} config Konfigurationsoptionen
 */
function configureMiddleware(app, config) {
  // Sicherheits-Middleware
  app.use(helmet());

  // CORS konfigurieren
  const corsOptions = {
    origin: config.corsOrigin || '*',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization'],
    credentials: true,
    maxAge: 86400 // 24 Stunden
  };
  app.use(cors(corsOptions));

  // Body-Parser für JSON
  app.use(express.json({ limit: '10mb' }));
  app.use(express.urlencoded({ extended: true, limit: '10mb' }));

  // Cookie-Parser für Sitzungs-IDs
  app.use(cookieParser());

  // Komprimierung für bessere Performance
  app.use(compression());

  // Logging
  const logFormat = config.isProduction
    ? 'combined'  // Ausführlicheres Logging in Produktion
    : 'dev';  // Farbiges Logging in Entwicklung
  app.use(morgan(logFormat));

  // Rate Limiting zum Schutz vor Brute-Force und DoS
  if (config.enableRateLimit !== false) {
    const limiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 Minuten
      max: 100, // 100 Anfragen pro IP
      standardHeaders: true,
      legacyHeaders: false,
      message: {
        success: false,
        message: 'Zu viele Anfragen, bitte versuchen Sie es später erneut'
      }
    });
    app.use('/api/', limiter);
  }
}

/**
 * Registriert alle API-Routen
 * 
 * @param {express.Application} app Express-App
 * @param {Object} services Dienst-Instanzen
 * @param {Object} authMiddleware Auth-Middleware-Funktionen
 */
function registerRoutes(app, services, authMiddleware) {
  // API-Versionsprefix
  const apiPrefix = '/api/v1';

  // Alle Anfragen loggen (wenn sie die API-Route erreichen)
  app.use(apiPrefix, authMiddleware.logApiRequest);

  // Verfügbare Dienste überprüfen
  const {
    authService,
    searchService,
    publicationController,
    storageService,
    statsService,
    doiIntegrationService,
    nftService
  } = services;

  // Basisroute mit API-Metadaten
  app.get(apiPrefix, (req, res) => {
    res.json({
      name: 'DeSci-Scholar API',
      version: '1.0.0',
      endpoints: [
        `${apiPrefix}/auth`,
        `${apiPrefix}/publications`,
        `${apiPrefix}/semantic-scholar`,
        `${apiPrefix}/arxiv`,
        `${apiPrefix}/zotero`,
        `${apiPrefix}/storage`,
        `${apiPrefix}/stats`,
        `${apiPrefix}/doi-nft`
      ]
    });
  });
  
  // Authentifizierungsrouten
  if (authService) {
    app.use(`${apiPrefix}/auth`, createAuthRoutes({ authService }));
  }

  // Suchenrouten
  if (searchService) {
    app.use(`${apiPrefix}/search`, createSearchRoutes({ searchService, authService }));
  }
  
  // Publikationsrouten
  if (publicationController) {
    app.use(`${apiPrefix}/publications`, createPublicationRoutes({
      publicationController, 
      authService,
      authMiddleware
    }));
  }
  
  // Semantic Scholar Routen
  app.use(`${apiPrefix}/semantic-scholar`, semanticScholarRoutes);

  // DOI-zu-NFT Routen
  if (doiIntegrationService) {
    app.use(`${apiPrefix}/doi-nft`, createDoiNftRoutes({
      doiIntegrationService,
      authMiddleware
    }));
  }

  // Zotero Routen
  if (authService) {
    app.use(`${apiPrefix}/zotero`, createZoteroRoutes({
      authService,
      nftService: services.nftService
    }));
  }

  // Storage Routen
  if (storageService) {
    app.use(`${apiPrefix}/storage`, createStorageRoutes({
      storageService,
      authMiddleware
    }));
  }

  // Statistik-Routen
  if (statsService) {
    app.use(`${apiPrefix}/stats`, createStatsRoutes({
      statsService,
      authMiddleware
    }));
  }

  // arXiv Routen
  app.use(`${apiPrefix}/arxiv`, arxivRoutes);

  // Fallback für unbekannte Routen
  app.use(`${apiPrefix}/*`, (req, res) => {
    res.status(404).json({
      success: false,
      message: 'Endpoint nicht gefunden'
    });
  });
}

/**
 * Konfiguriert die globale Fehlerbehandlung
 * 
 * @param {express.Application} app Express-App
 */
function configureErrorHandling(app) {
  // 404-Handler
  app.use((req, res, next) => {
    res.status(404).json({
      success: false,
      message: 'Route nicht gefunden'
    });
  });
  
  // Globaler Fehlerhandler
  app.use((err, req, res, next) => {
    console.error('Unbehandelter Fehler:', err);
    
    // Standardantwort
    const statusCode = err.statusCode || 500;
    const message = process.env.NODE_ENV === 'production'
      ? 'Ein interner Serverfehler ist aufgetreten' 
      : err.message || 'Ein interner Serverfehler ist aufgetreten';
    
    res.status(statusCode).json({  
      success: false,
      message,
      error: process.env.NODE_ENV === 'production' ? undefined : {
        name: err.name,
        stack: err.stack
      }
    });
  });
}

export default createAPI;
