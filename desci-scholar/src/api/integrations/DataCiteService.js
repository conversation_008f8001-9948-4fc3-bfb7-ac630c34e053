/**
 * @fileoverview Service für die Interaktion mit der DataCite API
 * 
 * Dieser Service bietet eine einheitliche Schnittstelle für die Interaktion mit der DataCite API,
 * einschließlich der Erstellung, Aktualisierung und Abfrage von DOIs.
 */

import axios from 'axios';
import { logger } from '../../utils/logger.js';

/**
 * Service für die Interaktion mit der DataCite API
 */
export class DataCiteService {
  /**
   * Erstellt eine neue DataCiteService-Instanz
   * @param {Object} config - Konfigurationsoptionen
   * @param {string} config.apiBaseUrl - Basis-URL der DataCite API
   * @param {string} config.apiKey - API-Schlüssel für die Authentifizierung
   * @param {string} config.repositoryId - Repository-ID für die DOI-Registrierung
   * @param {string} config.doiPrefix - Präfix für DOIs
   * @param {number} config.timeout - Timeout für API-Anfragen in Millisekunden
   * @param {number} config.maxRetries - Maximale Anzahl von Wiederholungsversuchen bei Fehlern
   */
  constructor(config = {}) {
    const {
      apiBaseUrl = 'https://api.datacite.org',
      apiKey,
      repositoryId,
      doiPrefix = '10.5281',
      timeout = 10000,
      maxRetries = 3
    } = config;
    
    this.apiBaseUrl = apiBaseUrl;
    this.apiKey = apiKey;
    this.repositoryId = repositoryId;
    this.prefix = doiPrefix;
    this.timeout = timeout;
    this.maxRetries = maxRetries;
    
    this.client = null;
    this.initialized = false;
  }
  
  /**
   * Initialisiert den DataCiteService
   * @returns {Promise<boolean>} Erfolgsstatus
   */
  async initialize() {
    try {
      logger.info('DataCiteService: Initialisiere');
      
      // Prüfen, ob die erforderlichen Konfigurationsparameter vorhanden sind
      if (!this.apiKey) {
        logger.warn('DataCiteService: API-Schlüssel fehlt, DOI-Erstellung wird nicht verfügbar sein');
      }
      
      if (!this.repositoryId) {
        logger.warn('DataCiteService: Repository-ID fehlt, DOI-Erstellung wird nicht verfügbar sein');
      }
      
      // Erstelle Axios-Instanz mit Basis-Konfiguration
      this.client = axios.create({
        baseURL: this.apiBaseUrl,
        timeout: this.timeout,
        headers: {
          'Content-Type': 'application/vnd.api+json',
          'Accept': 'application/vnd.api+json'
        },
        auth: this.apiKey ? {
          username: this.repositoryId,
          password: this.apiKey
        } : undefined
      });
      
      this.initialized = true;
      
      logger.info('DataCiteService: Erfolgreich initialisiert');
      
      return true;
    } catch (error) {
      logger.error('DataCiteService: Fehler bei der Initialisierung', {
        error: error.message,
        stack: error.stack
      });
      
      return false;
    }
  }
  
  /**
   * Erstellt einen neuen DOI
   * @param {Object} metadata - Metadaten für den DOI
   * @param {Object} options - Optionen für die DOI-Erstellung
   * @param {string} options.suffix - Benutzerdefiniertes DOI-Suffix
   * @returns {Promise<Object>} Ergebnis der DOI-Erstellung
   */
  async createDOI(metadata, options = {}) {
    try {
      this._ensureInitialized();
      
      // Prüfen, ob die erforderlichen Konfigurationsparameter vorhanden sind
      if (!this.apiKey || !this.repositoryId) {
        throw new Error('API-Schlüssel und Repository-ID sind erforderlich für die DOI-Erstellung');
      }
      
      logger.info('DataCiteService: Erstelle neuen DOI', {
        title: metadata.title,
        suffix: options.suffix
      });
      
      // DOI-Suffix generieren oder verwenden
      const suffix = options.suffix || this._generateSuffix();
      const doi = `${this.prefix}/${suffix}`;
      
      // Metadaten in DataCite-Format konvertieren
      const dataCiteMetadata = this._convertToDataCiteFormat(metadata, doi);
      
      // DOI erstellen
      const response = await this.client.post('/dois', dataCiteMetadata);
      
      logger.info('DataCiteService: DOI erfolgreich erstellt', {
        doi,
        url: response.data.data.attributes.url
      });
      
      return {
        success: true,
        doi,
        url: response.data.data.attributes.url,
        metadata: response.data.data.attributes
      };
    } catch (error) {
      logger.error('DataCiteService: Fehler bei der DOI-Erstellung', {
        error: error.message,
        stack: error.stack,
        metadata
      });
      
      return {
        success: false,
        error: error.message,
        metadata
      };
    }
  }
  
  /**
   * Aktualisiert einen bestehenden DOI
   * @param {string} doi - DOI, der aktualisiert werden soll
   * @param {Object} metadata - Neue Metadaten
   * @returns {Promise<Object>} Ergebnis der DOI-Aktualisierung
   */
  async updateDOI(doi, metadata) {
    try {
      this._ensureInitialized();
      
      // Prüfen, ob die erforderlichen Konfigurationsparameter vorhanden sind
      if (!this.apiKey || !this.repositoryId) {
        throw new Error('API-Schlüssel und Repository-ID sind erforderlich für die DOI-Aktualisierung');
      }
      
      logger.info('DataCiteService: Aktualisiere DOI', {
        doi,
        title: metadata.title
      });
      
      // Metadaten in DataCite-Format konvertieren
      const dataCiteMetadata = this._convertToDataCiteFormat(metadata, doi);
      
      // DOI aktualisieren
      const response = await this.client.put(`/dois/${doi}`, dataCiteMetadata);
      
      logger.info('DataCiteService: DOI erfolgreich aktualisiert', {
        doi,
        url: response.data.data.attributes.url
      });
      
      return {
        success: true,
        doi,
        url: response.data.data.attributes.url,
        metadata: response.data.data.attributes
      };
    } catch (error) {
      logger.error('DataCiteService: Fehler bei der DOI-Aktualisierung', {
        error: error.message,
        stack: error.stack,
        doi,
        metadata
      });
      
      return {
        success: false,
        error: error.message,
        doi,
        metadata
      };
    }
  }
  
  /**
   * Ruft Metadaten für einen DOI ab
   * @param {string} doi - DOI, dessen Metadaten abgerufen werden sollen
   * @returns {Promise<Object>} DOI-Metadaten
   */
  async getDOI(doi) {
    try {
      this._ensureInitialized();

      logger.info('DataCiteService: Rufe DOI-Metadaten ab', { doi });

      // DOI abrufen
      const response = await this.client.get(`/dois/${doi}`);

      // Metadaten in einheitliches Format konvertieren
      const metadata = this._convertFromDataCiteFormat(response.data.data.attributes);

      // Zusätzliche DataCite-spezifische Anreicherungen
      const enrichedMetadata = await this._enrichDataCiteMetadata(metadata, doi);

      logger.info('DataCiteService: DOI-Metadaten erfolgreich abgerufen', { doi });

      return {
        success: true,
        doi,
        metadata: enrichedMetadata,
        source: 'DataCite',
        retrievedAt: new Date().toISOString()
      };
    } catch (error) {
      logger.error('DataCiteService: Fehler beim Abrufen der DOI-Metadaten', {
        error: error.message,
        stack: error.stack,
        doi
      });

      return {
        success: false,
        error: error.message,
        doi
      };
    }
  }

  /**
   * Reichert DataCite-Metadaten mit zusätzlichen Informationen an
   * @param {Object} metadata - Basis-Metadaten
   * @param {string} doi - DOI
   * @returns {Promise<Object>} Angereicherte Metadaten
   */
  async _enrichDataCiteMetadata(metadata, doi) {
    try {
      // Zusätzliche DataCite-Services nutzen
      const enrichments = {
        registrationInfo: await this._getRegistrationInfo(doi),
        relatedIdentifiers: await this._getRelatedIdentifiers(doi),
        fundingInfo: await this._getFundingInfo(doi),
        usageStatistics: await this._getUsageStatistics(doi)
      };

      return {
        ...metadata,
        dataCiteEnrichments: enrichments
      };
    } catch (error) {
      logger.warn('DataCiteService: Warnung bei Metadaten-Anreicherung', { error: error.message, doi });
      return metadata;
    }
  }

  /**
   * Ruft Registrierungsinformationen ab
   */
  async _getRegistrationInfo(doi) {
    try {
      const response = await this.client.get(`/dois/${doi}`);
      const data = response.data.data.attributes;

      return {
        registrationAgency: 'DataCite',
        registeredDate: data.registered,
        lastUpdated: data.updated,
        state: data.state,
        url: data.url
      };
    } catch (error) {
      logger.warn('Fehler beim Abrufen der Registrierungsinfo:', error.message);
      return null;
    }
  }

  /**
   * Ruft verwandte Identifikatoren ab
   */
  async _getRelatedIdentifiers(doi) {
    try {
      const response = await this.client.get(`/dois/${doi}`);
      const relatedIdentifiers = response.data.data.attributes.relatedIdentifiers || [];

      return relatedIdentifiers.map(identifier => ({
        identifier: identifier.relatedIdentifier,
        identifierType: identifier.relatedIdentifierType,
        relationType: identifier.relationType,
        resourceTypeGeneral: identifier.resourceTypeGeneral
      }));
    } catch (error) {
      logger.warn('Fehler beim Abrufen verwandter Identifikatoren:', error.message);
      return [];
    }
  }

  /**
   * Ruft Finanzierungsinformationen ab
   */
  async _getFundingInfo(doi) {
    try {
      const response = await this.client.get(`/dois/${doi}`);
      const fundingReferences = response.data.data.attributes.fundingReferences || [];

      return fundingReferences.map(funding => ({
        funderName: funding.funderName,
        funderIdentifier: funding.funderIdentifier,
        awardNumber: funding.awardNumber,
        awardTitle: funding.awardTitle
      }));
    } catch (error) {
      logger.warn('Fehler beim Abrufen der Finanzierungsinfo:', error.message);
      return [];
    }
  }

  /**
   * Ruft Nutzungsstatistiken ab (falls verfügbar)
   */
  async _getUsageStatistics(doi) {
    try {
      // DataCite Usage Statistics API (falls verfügbar)
      const response = await this.client.get(`/reports/usage/${doi}`);
      return response.data;
    } catch (error) {
      // Nutzungsstatistiken sind optional
      return null;
    }
  }
  
  /**
   * Generiert ein zufälliges DOI-Suffix
   * @private
   * @returns {string} Generiertes DOI-Suffix
   */
  _generateSuffix() {
    // Generiere ein zufälliges Suffix im Format "desci.XXXXXXXX"
    const randomPart = Math.random().toString(36).substring(2, 10);
    return `desci.${randomPart}`;
  }
  
  /**
   * Konvertiert Metadaten in das DataCite-Format
   * @private
   * @param {Object} metadata - Metadaten
   * @param {string} doi - DOI
   * @returns {Object} Metadaten im DataCite-Format
   */
  _convertToDataCiteFormat(metadata, doi) {
    // Erstelle Autoren
    const creators = (metadata.authors || []).map(author => {
      if (typeof author === 'string') {
        // Einfache Namensaufteilung
        const nameParts = author.split(' ');
        const lastName = nameParts.pop();
        const firstNames = nameParts.join(' ');
        
        return {
          name: author,
          nameType: 'Personal',
          givenName: firstNames,
          familyName: lastName
        };
      } else if (typeof author === 'object') {
        return {
          name: author.name,
          nameType: 'Personal',
          givenName: author.firstName || author.given,
          familyName: author.lastName || author.family,
          nameIdentifiers: author.orcid ? [
            {
              nameIdentifier: author.orcid,
              nameIdentifierScheme: 'ORCID',
              schemeUri: 'https://orcid.org'
            }
          ] : undefined,
          affiliation: author.affiliation ? [
            {
              name: author.affiliation
            }
          ] : undefined
        };
      }
      
      return { name: 'Unknown Author' };
    });
    
    // Erstelle Titel
    const titles = [
      {
        title: metadata.title
      }
    ];
    
    // Erstelle Beschreibungen
    const descriptions = metadata.abstract ? [
      {
        description: metadata.abstract,
        descriptionType: 'Abstract'
      }
    ] : undefined;
    
    // Erstelle Themen
    const subjects = metadata.keywords ? metadata.keywords.map(keyword => ({
      subject: keyword
    })) : undefined;
    
    // Erstelle Datumsangaben
    const dates = [];
    
    if (metadata.published) {
      dates.push({
        date: metadata.published,
        dateType: 'Issued'
      });
    }
    
    if (metadata.updated) {
      dates.push({
        date: metadata.updated,
        dateType: 'Updated'
      });
    }
    
    // Erstelle Ressourcentyp
    const resourceType = {
      resourceTypeGeneral: metadata.type || 'Text',
      resourceType: metadata.subtype || 'Journal Article'
    };
    
    // Erstelle URL
    const url = metadata.url;
    
    // Erstelle DataCite-Metadaten
    return {
      data: {
        type: 'dois',
        attributes: {
          doi,
          creators,
          titles,
          publisher: metadata.publisher || 'DeSci Scholar',
          publicationYear: metadata.year || new Date().getFullYear(),
          types: {
            resourceTypeGeneral: resourceType.resourceTypeGeneral,
            resourceType: resourceType.resourceType
          },
          url,
          descriptions,
          subjects,
          dates,
          language: metadata.language || 'en',
          version: metadata.version,
          schemaVersion: 'http://datacite.org/schema/kernel-4'
        }
      }
    };
  }
  
  /**
   * Konvertiert Metadaten aus dem DataCite-Format in ein einheitliches Format
   * @private
   * @param {Object} dataCiteMetadata - Metadaten im DataCite-Format
   * @returns {Object} Metadaten im einheitlichen Format
   */
  _convertFromDataCiteFormat(dataCiteMetadata) {
    // Extrahiere Autoren
    const authors = (dataCiteMetadata.creators || []).map(creator => ({
      name: creator.name,
      firstName: creator.givenName,
      lastName: creator.familyName,
      orcid: creator.nameIdentifiers?.find(id => id.nameIdentifierScheme === 'ORCID')?.nameIdentifier,
      affiliation: creator.affiliation?.map(aff => aff.name).join(', ')
    }));
    
    // Extrahiere Titel
    const title = dataCiteMetadata.titles?.[0]?.title;
    
    // Extrahiere Abstract
    const abstract = dataCiteMetadata.descriptions?.find(desc => desc.descriptionType === 'Abstract')?.description;
    
    // Extrahiere Keywords
    const keywords = dataCiteMetadata.subjects?.map(subject => subject.subject);
    
    // Extrahiere Datumsangaben
    const published = dataCiteMetadata.dates?.find(date => date.dateType === 'Issued')?.date;
    const updated = dataCiteMetadata.dates?.find(date => date.dateType === 'Updated')?.date;
    
    // Erstelle einheitliches Metadaten-Objekt
    return {
      doi: dataCiteMetadata.doi,
      title,
      authors,
      abstract,
      keywords,
      published,
      updated,
      publisher: dataCiteMetadata.publisher,
      year: dataCiteMetadata.publicationYear,
      type: dataCiteMetadata.types?.resourceTypeGeneral,
      subtype: dataCiteMetadata.types?.resourceType,
      url: dataCiteMetadata.url,
      language: dataCiteMetadata.language,
      version: dataCiteMetadata.version
    };
  }
  
  /**
   * Stellt sicher, dass der Service initialisiert ist
   * @private
   * @throws {Error} Wenn der Service nicht initialisiert ist
   */
  _ensureInitialized() {
    if (!this.initialized || !this.client) {
      throw new Error('DataCiteService ist nicht initialisiert');
    }
  }
}

export default DataCiteService;/**
 * @fileoverview Service für die Interaktion mit der DataCite API
 * 
 * Dieser Service bietet eine einheitliche Schnittstelle für die Interaktion mit der DataCite API,
 * einschließlich der Erstellung, Aktualisierung und Abfrage von DOIs.
 */

import axios from 'axios';
import { logger } from '../../utils/logger.js';

/**
 * Service für die Interaktion mit der DataCite API
 */
export class DataCiteService {
  /**
   * Erstellt eine neue DataCiteService-Instanz
   * @param {Object} config - Konfigurationsoptionen
   * @param {string} config.apiBaseUrl - Basis-URL der DataCite API
   * @param {string} config.apiKey - API-Schlüssel für die Authentifizierung
   * @param {string} config.repositoryId - Repository-ID für die DOI-Registrierung
   * @param {string} config.doiPrefix - Präfix für DOIs
   * @param {number} config.timeout - Timeout für API-Anfragen in Millisekunden
   * @param {number} config.maxRetries - Maximale Anzahl von Wiederholungsversuchen bei Fehlern
   */
  constructor(config = {}) {
    const {
      apiBaseUrl = 'https://api.datacite.org',
      apiKey,
      repositoryId,
      doiPrefix = '10.5281',
      timeout = 10000,
      maxRetries = 3
    } = config;
    
    this.apiBaseUrl = apiBaseUrl;
    this.apiKey = apiKey;
    this.repositoryId = repositoryId;
    this.prefix = doiPrefix;
    this.timeout = timeout;
    this.maxRetries = maxRetries;
    
    this.client = null;
    this.initialized = false;
  }
  
  /**
   * Initialisiert den DataCiteService
   * @returns {Promise<boolean>} Erfolgsstatus
   */
  async initialize() {
    try {
      logger.info('DataCiteService: Initialisiere');
      
      // Prüfen, ob die erforderlichen Konfigurationsparameter vorhanden sind
      if (!this.apiKey) {
        logger.warn('DataCiteService: API-Schlüssel fehlt, DOI-Erstellung wird nicht verfügbar sein');
      }
      
      if (!this.repositoryId) {
        logger.warn('DataCiteService: Repository-ID fehlt, DOI-Erstellung wird nicht verfügbar sein');
      }
      
      // Erstelle Axios-Instanz mit Basis-Konfiguration
      this.client = axios.create({
        baseURL: this.apiBaseUrl,
        timeout: this.timeout,
        headers: {
          'Content-Type': 'application/vnd.api+json',
          'Accept': 'application/vnd.api+json'
        },
        auth: this.apiKey ? {
          username: this.repositoryId,
          password: this.apiKey
        } : undefined
      });
      
      this.initialized = true;
      
      logger.info('DataCiteService: Erfolgreich initialisiert');
      
      return true;
    } catch (error) {
      logger.error('DataCiteService: Fehler bei der Initialisierung', {
        error: error.message,
        stack: error.stack
      });
      
      return false;
    }
  }
  
  /**
   * Erstellt einen neuen DOI
   * @param {Object} metadata - Metadaten für den DOI
   * @param {Object} options - Optionen für die DOI-Erstellung
   * @param {string} options.suffix - Benutzerdefiniertes DOI-Suffix
   * @returns {Promise<Object>} Ergebnis der DOI-Erstellung
   */
  async createDOI(metadata, options = {}) {
    try {
      this._ensureInitialized();
      
      // Prüfen, ob die erforderlichen Konfigurationsparameter vorhanden sind
      if (!this.apiKey || !this.repositoryId) {
        throw new Error('API-Schlüssel und Repository-ID sind erforderlich für die DOI-Erstellung');
      }
      
      logger.info('DataCiteService: Erstelle neuen DOI', {
        title: metadata.title,
        suffix: options.suffix
      });
      
      // DOI-Suffix generieren oder verwenden
      const suffix = options.suffix || this._generateSuffix();
      const doi = `${this.prefix}/${suffix}`;
      
      // Metadaten in DataCite-Format konvertieren
      const dataCiteMetadata = this._convertToDataCiteFormat(metadata, doi);
      
      // DOI erstellen
      const response = await this.client.post('/dois', dataCiteMetadata);
      
      logger.info('DataCiteService: DOI erfolgreich erstellt', {
        doi,
        url: response.data.data.attributes.url
      });
      
      return {
        success: true,
        doi,
        url: response.data.data.attributes.url,
        metadata: response.data.data.attributes
      };
    } catch (error) {
      logger.error('DataCiteService: Fehler bei der DOI-Erstellung', {
        error: error.message,
        stack: error.stack,
        metadata
      });
      
      return {
        success: false,
        error: error.message,
        metadata
      };
    }
  }
  
  /**
   * Aktualisiert einen bestehenden DOI
   * @param {string} doi - DOI, der aktualisiert werden soll
   * @param {Object} metadata - Neue Metadaten
   * @returns {Promise<Object>} Ergebnis der DOI-Aktualisierung
   */
  async updateDOI(doi, metadata) {
    try {
      this._ensureInitialized();
      
      // Prüfen, ob die erforderlichen Konfigurationsparameter vorhanden sind
      if (!this.apiKey || !this.repositoryId) {
        throw new Error('API-Schlüssel und Repository-ID sind erforderlich für die DOI-Aktualisierung');
      }
      
      logger.info('DataCiteService: Aktualisiere DOI', {
        doi,
        title: metadata.title
      });
      
      // Metadaten in DataCite-Format konvertieren
      const dataCiteMetadata = this._convertToDataCiteFormat(metadata, doi);
      
      // DOI aktualisieren
      const response = await this.client.put(`/dois/${doi}`, dataCiteMetadata);
      
      logger.info('DataCiteService: DOI erfolgreich aktualisiert', {
        doi,
        url: response.data.data.attributes.url
      });
      
      return {
        success: true,
        doi,
        url: response.data.data.attributes.url,
        metadata: response.data.data.attributes
      };
    } catch (error) {
      logger.error('DataCiteService: Fehler bei der DOI-Aktualisierung', {
        error: error.message,
        stack: error.stack,
        doi,
        metadata
      });
      
      return {
        success: false,
        error: error.message,
        doi,
        metadata
      };
    }
  }
  
  /**
   * Ruft Metadaten für einen DOI ab
   * @param {string} doi - DOI, dessen Metadaten abgerufen werden sollen
   * @returns {Promise<Object>} DOI-Metadaten
   */
  async getDOI(doi) {
    try {
      this._ensureInitialized();
      
      logger.info('DataCiteService: Rufe DOI-Metadaten ab', { doi });
      
      // DOI abrufen
      const response = await this.client.get(`/dois/${doi}`);
      
      // Metadaten in einheitliches Format konvertieren
      const metadata = this._convertFromDataCiteFormat(response.data.data.attributes);
      
      logger.info('DataCiteService: DOI-Metadaten erfolgreich abgerufen', { doi });
      
      return {
        success: true,
        doi,
        metadata
      };
    } catch (error) {
      logger.error('DataCiteService: Fehler beim Abrufen der DOI-Metadaten', {
        error: error.message,
        stack: error.stack,
        doi
      });
      
      return {
        success: false,
        error: error.message,
        doi
      };
    }
  }
  
  /**
   * Generiert ein zufälliges DOI-Suffix
   * @private
   * @returns {string} Generiertes DOI-Suffix
   */
  _generateSuffix() {
    // Generiere ein zufälliges Suffix im Format "desci.XXXXXXXX"
    const randomPart = Math.random().toString(36).substring(2, 10);
    return `desci.${randomPart}`;
  }
  
  /**
   * Konvertiert Metadaten in das DataCite-Format
   * @private
   * @param {Object} metadata - Metadaten
   * @param {string} doi - DOI
   * @returns {Object} Metadaten im DataCite-Format
   */
  _convertToDataCiteFormat(metadata, doi) {
    // Erstelle Autoren
    const creators = (metadata.authors || []).map(author => {
      if (typeof author === 'string') {
        // Einfache Namensaufteilung
        const nameParts = author.split(' ');
        const lastName = nameParts.pop();
        const firstNames = nameParts.join(' ');
        
        return {
          name: author,
          nameType: 'Personal',
          givenName: firstNames,
          familyName: lastName
        };
      } else if (typeof author === 'object') {
        return {
          name: author.name,
          nameType: 'Personal',
          givenName: author.firstName || author.given,
          familyName: author.lastName || author.family,
          nameIdentifiers: author.orcid ? [
            {
              nameIdentifier: author.orcid,
              nameIdentifierScheme: 'ORCID',
              schemeUri: 'https://orcid.org'
            }
          ] : undefined,
          affiliation: author.affiliation ? [
            {
              name: author.affiliation
            }
          ] : undefined
        };
      }
      
      return { name: 'Unknown Author' };
    });
    
    // Erstelle Titel
    const titles = [
      {
        title: metadata.title
      }
    ];
    
    // Erstelle Beschreibungen
    const descriptions = metadata.abstract ? [
      {
        description: metadata.abstract,
        descriptionType: 'Abstract'
      }
    ] : undefined;
    
    // Erstelle Themen
    const subjects = metadata.keywords ? metadata.keywords.map(keyword => ({
      subject: keyword
    })) : undefined;
    
    // Erstelle Datumsangaben
    const dates = [];
    
    if (metadata.published) {
      dates.push({
        date: metadata.published,
        dateType: 'Issued'
      });
    }
    
    if (metadata.updated) {
      dates.push({
        date: metadata.updated,
        dateType: 'Updated'
      });
    }
    
    // Erstelle Ressourcentyp
    const resourceType = {
      resourceTypeGeneral: metadata.type || 'Text',
      resourceType: metadata.subtype || 'Journal Article'
    };
    
    // Erstelle URL
    const url = metadata.url;
    
    // Erstelle DataCite-Metadaten
    return {
      data: {
        type: 'dois',
        attributes: {
          doi,
          creators,
          titles,
          publisher: metadata.publisher || 'DeSci Scholar',
          publicationYear: metadata.year || new Date().getFullYear(),
          types: {
            resourceTypeGeneral: resourceType.resourceTypeGeneral,
            resourceType: resourceType.resourceType
          },
          url,
          descriptions,
          subjects,
          dates,
          language: metadata.language || 'en',
          version: metadata.version,
          schemaVersion: 'http://datacite.org/schema/kernel-4'
        }
      }
    };
  }
  
  /**
   * Konvertiert Metadaten aus dem DataCite-Format in ein einheitliches Format
   * @private
   * @param {Object} dataCiteMetadata - Metadaten im DataCite-Format
   * @returns {Object} Metadaten im einheitlichen Format
   */
  _convertFromDataCiteFormat(dataCiteMetadata) {
    // Extrahiere Autoren
    const authors = (dataCiteMetadata.creators || []).map(creator => ({
      name: creator.name,
      firstName: creator.givenName,
      lastName: creator.familyName,
      orcid: creator.nameIdentifiers?.find(id => id.nameIdentifierScheme === 'ORCID')?.nameIdentifier,
      affiliation: creator.affiliation?.map(aff => aff.name).join(', ')
    }));
    
    // Extrahiere Titel
    const title = dataCiteMetadata.titles?.[0]?.title;
    
    // Extrahiere Abstract
    const abstract = dataCiteMetadata.descriptions?.find(desc => desc.descriptionType === 'Abstract')?.description;
    
    // Extrahiere Keywords
    const keywords = dataCiteMetadata.subjects?.map(subject => subject.subject);
    
    // Extrahiere Datumsangaben
    const published = dataCiteMetadata.dates?.find(date => date.dateType === 'Issued')?.date;
    const updated = dataCiteMetadata.dates?.find(date => date.dateType === 'Updated')?.date;
    
    // Erstelle einheitliches Metadaten-Objekt
    return {
      doi: dataCiteMetadata.doi,
      title,
      authors,
      abstract,
      keywords,
      published,
      updated,
      publisher: dataCiteMetadata.publisher,
      year: dataCiteMetadata.publicationYear,
      type: dataCiteMetadata.types?.resourceTypeGeneral,
      subtype: dataCiteMetadata.types?.resourceType,
      url: dataCiteMetadata.url,
      language: dataCiteMetadata.language,
      version: dataCiteMetadata.version
    };
  }
  
  /**
   * Stellt sicher, dass der Service initialisiert ist
   * @private
   * @throws {Error} Wenn der Service nicht initialisiert ist
   */
  _ensureInitialized() {
    if (!this.initialized || !this.client) {
      throw new Error('DataCiteService ist nicht initialisiert');
    }
  }
}

export default DataCiteService;