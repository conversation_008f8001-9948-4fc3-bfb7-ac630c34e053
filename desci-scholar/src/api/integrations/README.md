# API-Integrationen für DeSci-Scholar

Dieses Verzeichnis enthält Integrationen mit externen APIs und Diensten für die DeSci-Scholar Plattform.

## Crossref-Integration

Die Crossref-Integration besteht aus mehreren Komponenten:

- `CrossrefClient.js` - Low-Level-Client für die direkte Interaktion mit der Crossref API
- `CrossrefService.js` - High-Level-Service, der den Client verwendet und zusätzliche Funktionalität bietet
- `CrossrefAPI.js` - Alternative Implementierung für spezifische Anwendungsfälle

### Verwendung

```javascript
// Verwendung des CrossrefService
import CrossrefService from './api/integrations/CrossrefService.js';

const crossrefService = new CrossrefService({
  apiBaseUrl: 'https://api.crossref.org',
  email: '<EMAIL>',
  token: 'your-token',
  timeout: 10000,
  maxRetries: 3
});

await crossrefService.initialize();
const result = await crossrefService.getWork('10.1234/example.doi');

// Verwendung der CrossrefAPI
import CrossrefAPI from './api/integrations/CrossrefAPI.js';

const crossrefAPI = new CrossrefAPI({
  apiUrl: 'https://api.crossref.org',
  mailto: '<EMAIL>',
  token: 'your-token'
});

const result = await crossrefAPI.getWorkByDOI('10.1234/example.doi');
```

## DataCite-Integration

Die DataCite-Integration ermöglicht die Erstellung und Verwaltung von DOIs:

- `DataCiteService.js` - Service für die Interaktion mit der DataCite API

### Verwendung

```javascript
import DataCiteService from './api/integrations/DataCiteService.js';

const dataCiteService = new DataCiteService({
  apiBaseUrl: 'https://api.datacite.org',
  apiKey: 'your-api-key',
  repositoryId: 'your-repository-id',
  doiPrefix: '10.5281',
  timeout: 10000,
  maxRetries: 3
});

await dataCiteService.initialize();
const result = await dataCiteService.createDOI({
  title: 'Example Publication',
  authors: ['John Doe', 'Jane Smith'],
  abstract: 'This is an example publication.',
  url: 'https://example.com/publication'
});
```

## Hinweise zur Verwendung

- Alle Integrationen sollten vor der Verwendung initialisiert werden
- Die Services bieten eine einheitliche Fehlerbehandlung und Logging
- Die Konfiguration sollte über Umgebungsvariablen oder eine zentrale Konfigurationsdatei erfolgen

## Fehlerbehandlung

Alle Integrationen geben ein einheitliches Ergebnisobjekt zurück:

```javascript
// Erfolgsfall
{
  success: true,
  // Weitere Daten je nach Methode
}

// Fehlerfall
{
  success: false,
  error: 'Fehlermeldung',
  // Weitere Daten je nach Methode
}
```# API-Integrationen für DeSci-Scholar

Dieses Verzeichnis enthält Integrationen mit externen APIs und Diensten für die DeSci-Scholar Plattform.

## Crossref-Integration

Die Crossref-Integration besteht aus mehreren Komponenten:

- `CrossrefClient.js` - Low-Level-Client für die direkte Interaktion mit der Crossref API
- `CrossrefService.js` - High-Level-Service, der den Client verwendet und zusätzliche Funktionalität bietet
- `CrossrefAPI.js` - Alternative Implementierung für spezifische Anwendungsfälle

### Verwendung

```javascript
// Verwendung des CrossrefService
import CrossrefService from './api/integrations/CrossrefService.js';

const crossrefService = new CrossrefService({
  apiBaseUrl: 'https://api.crossref.org',
  email: '<EMAIL>',
  token: 'your-token',
  timeout: 10000,
  maxRetries: 3
});

await crossrefService.initialize();
const result = await crossrefService.getWork('10.1234/example.doi');

// Verwendung der CrossrefAPI
import CrossrefAPI from './api/integrations/CrossrefAPI.js';

const crossrefAPI = new CrossrefAPI({
  apiUrl: 'https://api.crossref.org',
  mailto: '<EMAIL>',
  token: 'your-token'
});

const result = await crossrefAPI.getWorkByDOI('10.1234/example.doi');
```

## DataCite-Integration

Die DataCite-Integration ermöglicht die Erstellung und Verwaltung von DOIs:

- `DataCiteService.js` - Service für die Interaktion mit der DataCite API

### Verwendung

```javascript
import DataCiteService from './api/integrations/DataCiteService.js';

const dataCiteService = new DataCiteService({
  apiBaseUrl: 'https://api.datacite.org',
  apiKey: 'your-api-key',
  repositoryId: 'your-repository-id',
  doiPrefix: '10.5281',
  timeout: 10000,
  maxRetries: 3
});

await dataCiteService.initialize();
const result = await dataCiteService.createDOI({
  title: 'Example Publication',
  authors: ['John Doe', 'Jane Smith'],
  abstract: 'This is an example publication.',
  url: 'https://example.com/publication'
});
```

## Hinweise zur Verwendung

- Alle Integrationen sollten vor der Verwendung initialisiert werden
- Die Services bieten eine einheitliche Fehlerbehandlung und Logging
- Die Konfiguration sollte über Umgebungsvariablen oder eine zentrale Konfigurationsdatei erfolgen

## Fehlerbehandlung

Alle Integrationen geben ein einheitliches Ergebnisobjekt zurück:

```javascript
// Erfolgsfall
{
  success: true,
  // Weitere Daten je nach Methode
}

// Fehlerfall
{
  success: false,
  error: 'Fehlermeldung',
  // Weitere Daten je nach Methode
}
```# API-Integrationen für DeSci-Scholar

Dieses Verzeichnis enthält Integrationen mit externen APIs, die von DeSci-Scholar verwendet werden, um wissenschaftliche Publikationen und Metadaten abzurufen.

## Verfügbare Integrationen

### ArXivAPI

Die `ArXivAPI`-Klasse bietet eine Schnittstelle zur arXiv API, um wissenschaftliche Preprints und Publikationen abzurufen. Sie ermöglicht:

- Suche nach Publikationen mit verschiedenen Parametern
- Abruf von Publikationsdetails anhand der arXiv-ID
- Abruf der neuesten Publikationen in bestimmten Kategorien
- Extraktion von DOIs aus arXiv-Metadaten
- Anreicherung von arXiv-Daten mit Crossref-Metadaten

## Verwendung

### ArXivAPI

```javascript
import ArXivAPI from '../api/integrations/ArXivAPI.js';

// Initialisiere die API
const arxivAPI = new ArXivAPI();
await arxivAPI.initialize();

// Suche nach Publikationen
const searchResults = await arxivAPI.searchPapers({
  query: 'quantum computing',
  category: 'quant-ph',
  maxResults: 10
});

// Rufe Details zu einer Publikation ab
const paper = await arxivAPI.getPaper('2304.12345');

// Rufe die neuesten Publikationen in einer Kategorie ab
const recentPapers = await arxivAPI.getRecentPapers({
  category: 'cs.AI',
  maxResults: 30
});
```

### ArXivIntegrationController

Für erweiterte Funktionen wie DOI-Erstellung und NFT-Prägung verwenden Sie den `ArXivIntegrationController` aus dem Controllers-Verzeichnis:

```javascript
import ArXivIntegrationController from '../../controllers/ArXivIntegrationController.js';
import { StorageService } from '../../storage/StorageService.js';

// Initialisiere den Controller
const storageService = new StorageService();
const arxivController = new ArXivIntegrationController({
  storageService,
  // Optional: crossrefAPI, nftService
});
await arxivController.initialize();

// Verarbeite eine Publikation (DOI erstellen, NFT prägen, speichern)
const result = await arxivController.processPaper('2304.12345', {
  createDOI: true,
  mintNFT: false,
  storeData: true
});

// Suche nach Publikationen
const searchResults = await arxivController.searchPapers({
  query: 'quantum computing',
  maxResults: 10
});
```

## Hinweise

- Die arXiv API hat Ratenbegrenzungen. Es wird empfohlen, nicht mehr als eine Anfrage alle 3 Sekunden zu stellen.
- Die meisten arXiv-Publikationen stehen unter der "arXiv non-exclusive license to distribute", die keine Weiterverbreitung erlaubt.
- Nur Publikationen mit DOIs können als NFTs tokenisiert werden. Nicht alle arXiv-Publikationen haben DOIs.
- Die arXiv API liefert nur Metadaten, keinen Volltext. Der Volltext kann über die PDF-URL abgerufen werden.

## Weitere Informationen

- [arXiv API-Dokumentation](https://arxiv.org/help/api/user-manual.html)
- [arXiv API-Nutzungsbedingungen](https://arxiv.org/help/api/tou.html)
 Verwendung

### ArXivAPI

```javascript
import ArXivAPI from '../api/integrations/ArXivAPI.js';

// Initialisiere die API
const arxivAPI = new ArXivAPI();
await arxivAPI.initialize();

// Suche nach Publikationen
const searchResults = await arxivAPI.searchPapers({
  query: 'quantum computing',
  category: 'quant-ph',
  maxResults: 10
});

// Rufe Details zu einer Publikation ab
const paper = await arxivAPI.getPaper('2304.12345');

// Rufe die neuesten Publikationen in einer Kategorie ab
const recentPapers = await arxivAPI.getRecentPapers({
  category: 'cs.AI',
  maxResults: 30
});
```

### ArXivIntegrationController

Für erweiterte Funktionen wie DOI-Erstellung und NFT-Prägung verwenden Sie den `ArXivIntegrationController` aus dem Controllers-Verzeichnis:

```javascript
import ArXivIntegrationController from '../../controllers/ArXivIntegrationController.js';
import { StorageService } from '../../storage/StorageService.js';

// Initialisiere den Controller
const storageService = new StorageService();
const arxivController = new ArXivIntegrationController({
  storageService,
  // Optional: crossrefAPI, nftService
});
await arxivController.initialize();

// Verarbeite eine Publikation (DOI erstellen, NFT prägen, speichern)
const result = await arxivController.processPaper('2304.12345', {
  createDOI: true,
  mintNFT: false,
  storeData: true
});

// Suche nach Publikationen
const searchResults = await arxivController.searchPapers({
  query: 'quantum computing',
  maxResults: 10
});
```

## Hinweise

- Die arXiv API hat Ratenbegrenzungen. Es wird empfohlen, nicht mehr als eine Anfrage alle 3 Sekunden zu stellen.
- Die meisten arXiv-Publikationen stehen unter der "arXiv non-exclusive license to distribute", die keine Weiterverbreitung erlaubt.
- Nur Publikationen mit DOIs können als NFTs tokenisiert werden. Nicht alle arXiv-Publikationen haben DOIs.
- Die arXiv API liefert nur Metadaten, keinen Volltext. Der Volltext kann über die PDF-URL abgerufen werden.

## Weitere Informationen

- [arXiv API-Dokumentation](https://arxiv.org/help/api/user-manual.html)
- [arXiv API-Nutzungsbedingungen](https://arxiv.org/help/api/tou.html)
- [arXiv-Taxonomie](https://arxiv.org/category_taxonomy)
### ArXivAPI

```javascript
import ArXivAPI from '../api/integrations/ArXivAPI.js';

// Initialisiere die API
const arxivAPI = new ArXivAPI();
await arxivAPI.initialize();

// Suche nach Publikationen
const searchResults = await arxivAPI.searchPapers({
  query: 'quantum computing',
  category: 'quant-ph',
  maxResults: 10
});

// Rufe Details zu einer Publikation ab
const paper = await arxivAPI.getPaper('2304.12345');

// Rufe die neuesten Publikationen in einer Kategorie ab
const recentPapers = await arxivAPI.getRecentPapers({
  category: 'cs.AI',
  maxResults: 30
});
```

### ArXivIntegrationController

Für erweiterte Funktionen wie DOI-Erstellung und NFT-Prägung verwenden Sie den `ArXivIntegrationController` aus dem Controllers-Verzeichnis:

```javascript
import ArXivIntegrationController from '../../controllers/ArXivIntegrationController.js';
import { StorageService } from '../../storage/StorageService.js';

// Initialisiere den Controller
const storageService = new StorageService();
const arxivController = new ArXivIntegrationController({
  storageService,
  // Optional: crossrefAPI, nftService
});
await arxivController.initialize();

// Verarbeite eine Publikation (DOI erstellen, NFT prägen, speichern)
const result = await arxivController.processPaper('2304.12345', {
  createDOI: true,
  mintNFT: false,
  storeData: true
});

// Suche nach Publikationen
const searchResults = await arxivController.searchPapers({
  query: 'quantum computing',
  maxResults: 10
});
```

## Hinweise

- Die arXiv API hat Ratenbegrenzungen. Es wird empfohlen, nicht mehr als eine Anfrage alle 3 Sekunden zu stellen.
- Die meisten arXiv-Publikationen stehen unter der "arXiv non-exclusive license to distribute", die keine Weiterverbreitung erlaubt.
- Nur Publikationen mit DOIs können als NFTs tokenisiert werden. Nicht alle arXiv-Publikationen haben DOIs.
- Die arXiv API liefert nur Metadaten, keinen Volltext. Der Volltext kann über die PDF-URL abgerufen werden.

## Weitere Informationen

- [arXiv API-Dokumentation](https://arxiv.org/help/api/user-manual.html)
- [arXiv API-Nutzungsbedingungen](https://arxiv.org/help/api/tou.html)
 Verwendung

### ArXivAPI

```javascript
import ArXivAPI from '../api/integrations/ArXivAPI.js';

// Initialisiere die API
const arxivAPI = new ArXivAPI();
await arxivAPI.initialize();

// Suche nach Publikationen
const searchResults = await arxivAPI.searchPapers({
  query: 'quantum computing',
  category: 'quant-ph',
  maxResults: 10
});

// Rufe Details zu einer Publikation ab
const paper = await arxivAPI.getPaper('2304.12345');

// Rufe die neuesten Publikationen in einer Kategorie ab
const recentPapers = await arxivAPI.getRecentPapers({
  category: 'cs.AI',
  maxResults: 30
});
```

### ArXivIntegrationController

Für erweiterte Funktionen wie DOI-Erstellung und NFT-Prägung verwenden Sie den `ArXivIntegrationController` aus dem Controllers-Verzeichnis:

```javascript
import ArXivIntegrationController from '../../controllers/ArXivIntegrationController.js';
import { StorageService } from '../../storage/StorageService.js';

// Initialisiere den Controller
const storageService = new StorageService();
const arxivController = new ArXivIntegrationController({
  storageService,
  // Optional: crossrefAPI, nftService
});
await arxivController.initialize();

// Verarbeite eine Publikation (DOI erstellen, NFT prägen, speichern)
const result = await arxivController.processPaper('2304.12345', {
  createDOI: true,
  mintNFT: false,
  storeData: true
});

// Suche nach Publikationen
const searchResults = await arxivController.searchPapers({
  query: 'quantum computing',
  maxResults: 10
});
```

## Hinweise

- Die arXiv API hat Ratenbegrenzungen. Es wird empfohlen, nicht mehr als eine Anfrage alle 3 Sekunden zu stellen.
- Die meisten arXiv-Publikationen stehen unter der "arXiv non-exclusive license to distribute", die keine Weiterverbreitung erlaubt.
- Nur Publikationen mit DOIs können als NFTs tokenisiert werden. Nicht alle arXiv-Publikationen haben DOIs.
- Die arXiv API liefert nur Metadaten, keinen Volltext. Der Volltext kann über die PDF-URL abgerufen werden.

## Weitere Informationen

- [arXiv API-Dokumentation](https://arxiv.org/help/api/user-manual.html)
- [arXiv API-Nutzungsbedingungen](https://arxiv.org/help/api/tou.html)
- [arXiv-Taxonomie](https://arxiv.org/category_taxonomy)### ArXivAPI

```javascript
import ArXivAPI from '../api/integrations/ArXivAPI.js';

// Initialisiere die API
const arxivAPI = new ArXivAPI();
await arxivAPI.initialize();

// Suche nach Publikationen
const searchResults = await arxivAPI.searchPapers({
  query: 'quantum computing',
  category: 'quant-ph',
  maxResults: 10
});

// Rufe Details zu einer Publikation ab
const paper = await arxivAPI.getPaper('2304.12345');

// Rufe die neuesten Publikationen in einer Kategorie ab
const recentPapers = await arxivAPI.getRecentPapers({
  category: 'cs.AI',
  maxResults: 30
});
```

### ArXivIntegrationController

Für erweiterte Funktionen wie DOI-Erstellung und NFT-Prägung verwenden Sie den `ArXivIntegrationController` aus dem Controllers-Verzeichnis:

```javascript
import ArXivIntegrationController from '../../controllers/ArXivIntegrationController.js';
import { StorageService } from '../../storage/StorageService.js';

// Initialisiere den Controller
const storageService = new StorageService();
const arxivController = new ArXivIntegrationController({
  storageService,
  // Optional: crossrefAPI, nftService
});
await arxivController.initialize();

// Verarbeite eine Publikation (DOI erstellen, NFT prägen, speichern)
const result = await arxivController.processPaper('2304.12345', {
  createDOI: true,
  mintNFT: false,
  storeData: true
});

// Suche nach Publikationen
const searchResults = await arxivController.searchPapers({
  query: 'quantum computing',
  maxResults: 10
});
```

## Hinweise

- Die arXiv API hat Ratenbegrenzungen. Es wird empfohlen, nicht mehr als eine Anfrage alle 3 Sekunden zu stellen.
- Die meisten arXiv-Publikationen stehen unter der "arXiv non-exclusive license to distribute", die keine Weiterverbreitung erlaubt.
- Nur Publikationen mit DOIs können als NFTs tokenisiert werden. Nicht alle arXiv-Publikationen haben DOIs.
- Die arXiv API liefert nur Metadaten, keinen Volltext. Der Volltext kann über die PDF-URL abgerufen werden.

## Weitere Informationen

- [arXiv API-Dokumentation](https://arxiv.org/help/api/user-manual.html)
- [arXiv API-Nutzungsbedingungen](https://arxiv.org/help/api/tou.html)
 Verwendung

### ArXivAPI

```javascript
import ArXivAPI from '../api/integrations/ArXivAPI.js';

// Initialisiere die API
const arxivAPI = new ArXivAPI();
await arxivAPI.initialize();

// Suche nach Publikationen
const searchResults = await arxivAPI.searchPapers({
  query: 'quantum computing',
  category: 'quant-ph',
  maxResults: 10
});

// Rufe Details zu einer Publikation ab
const paper = await arxivAPI.getPaper('2304.12345');

// Rufe die neuesten Publikationen in einer Kategorie ab
const recentPapers = await arxivAPI.getRecentPapers({
  category: 'cs.AI',
  maxResults: 30
});
```

### ArXivIntegrationController

Für erweiterte Funktionen wie DOI-Erstellung und NFT-Prägung verwenden Sie den `ArXivIntegrationController` aus dem Controllers-Verzeichnis:

```javascript
import ArXivIntegrationController from '../../controllers/ArXivIntegrationController.js';
import { StorageService } from '../../storage/StorageService.js';

// Initialisiere den Controller
const storageService = new StorageService();
const arxivController = new ArXivIntegrationController({
  storageService,
  // Optional: crossrefAPI, nftService
});
await arxivController.initialize();

// Verarbeite eine Publikation (DOI erstellen, NFT prägen, speichern)
const result = await arxivController.processPaper('2304.12345', {
  createDOI: true,
  mintNFT: false,
  storeData: true
});

// Suche nach Publikationen
const searchResults = await arxivController.searchPapers({
  query: 'quantum computing',
  maxResults: 10
});
```

## Hinweise

- Die arXiv API hat Ratenbegrenzungen. Es wird empfohlen, nicht mehr als eine Anfrage alle 3 Sekunden zu stellen.
- Die meisten arXiv-Publikationen stehen unter der "arXiv non-exclusive license to distribute", die keine Weiterverbreitung erlaubt.
- Nur Publikationen mit DOIs können als NFTs tokenisiert werden. Nicht alle arXiv-Publikationen haben DOIs.
- Die arXiv API liefert nur Metadaten, keinen Volltext. Der Volltext kann über die PDF-URL abgerufen werden.

## Weitere Informationen

- [arXiv API-Dokumentation](https://arxiv.org/help/api/user-manual.html)
- [arXiv API-Nutzungsbedingungen](https://arxiv.org/help/api/tou.html)
- [arXiv-Taxonomie](https://arxiv.org/category_taxonomy)
### ArXivAPI

```javascript
import ArXivAPI from '../api/integrations/ArXivAPI.js';

// Initialisiere die API
const arxivAPI = new ArXivAPI();
await arxivAPI.initialize();

// Suche nach Publikationen
const searchResults = await arxivAPI.searchPapers({
  query: 'quantum computing',
  category: 'quant-ph',
  maxResults: 10
});

// Rufe Details zu einer Publikation ab
const paper = await arxivAPI.getPaper('2304.12345');

// Rufe die neuesten Publikationen in einer Kategorie ab
const recentPapers = await arxivAPI.getRecentPapers({
  category: 'cs.AI',
  maxResults: 30
});
```

### ArXivIntegrationController

Für erweiterte Funktionen wie DOI-Erstellung und NFT-Prägung verwenden Sie den `ArXivIntegrationController` aus dem Controllers-Verzeichnis:

```javascript
import ArXivIntegrationController from '../../controllers/ArXivIntegrationController.js';
import { StorageService } from '../../storage/StorageService.js';

// Initialisiere den Controller
const storageService = new StorageService();
const arxivController = new ArXivIntegrationController({
  storageService,
  // Optional: crossrefAPI, nftService
});
await arxivController.initialize();

// Verarbeite eine Publikation (DOI erstellen, NFT prägen, speichern)
const result = await arxivController.processPaper('2304.12345', {
  createDOI: true,
  mintNFT: false,
  storeData: true
});

// Suche nach Publikationen
const searchResults = await arxivController.searchPapers({
  query: 'quantum computing',
  maxResults: 10
});
```

## Hinweise

- Die arXiv API hat Ratenbegrenzungen. Es wird empfohlen, nicht mehr als eine Anfrage alle 3 Sekunden zu stellen.
- Die meisten arXiv-Publikationen stehen unter der "arXiv non-exclusive license to distribute", die keine Weiterverbreitung erlaubt.
- Nur Publikationen mit DOIs können als NFTs tokenisiert werden. Nicht alle arXiv-Publikationen haben DOIs.
- Die arXiv API liefert nur Metadaten, keinen Volltext. Der Volltext kann über die PDF-URL abgerufen werden.

## Weitere Informationen

- [arXiv API-Dokumentation](https://arxiv.org/help/api/user-manual.html)
- [arXiv API-Nutzungsbedingungen](https://arxiv.org/help/api/tou.html)
 Verwendung

### ArXivAPI

```javascript
import ArXivAPI from '../api/integrations/ArXivAPI.js';

// Initialisiere die API
const arxivAPI = new ArXivAPI();
await arxivAPI.initialize();

// Suche nach Publikationen
const searchResults = await arxivAPI.searchPapers({
  query: 'quantum computing',
  category: 'quant-ph',
  maxResults: 10
});

// Rufe Details zu einer Publikation ab
const paper = await arxivAPI.getPaper('2304.12345');

// Rufe die neuesten Publikationen in einer Kategorie ab
const recentPapers = await arxivAPI.getRecentPapers({
  category: 'cs.AI',
  maxResults: 30
});
```

### ArXivIntegrationController

Für erweiterte Funktionen wie DOI-Erstellung und NFT-Prägung verwenden Sie den `ArXivIntegrationController` aus dem Controllers-Verzeichnis:

```javascript
import ArXivIntegrationController from '../../controllers/ArXivIntegrationController.js';
import { StorageService } from '../../storage/StorageService.js';

// Initialisiere den Controller
const storageService = new StorageService();
const arxivController = new ArXivIntegrationController({
  storageService,
  // Optional: crossrefAPI, nftService
});
await arxivController.initialize();

// Verarbeite eine Publikation (DOI erstellen, NFT prägen, speichern)
const result = await arxivController.processPaper('2304.12345', {
  createDOI: true,
  mintNFT: false,
  storeData: true
});

// Suche nach Publikationen
const searchResults = await arxivController.searchPapers({
  query: 'quantum computing',
  maxResults: 10
});
```

## Hinweise

- Die arXiv API hat Ratenbegrenzungen. Es wird empfohlen, nicht mehr als eine Anfrage alle 3 Sekunden zu stellen.
- Die meisten arXiv-Publikationen stehen unter der "arXiv non-exclusive license to distribute", die keine Weiterverbreitung erlaubt.
- Nur Publikationen mit DOIs können als NFTs tokenisiert werden. Nicht alle arXiv-Publikationen haben DOIs.
- Die arXiv API liefert nur Metadaten, keinen Volltext. Der Volltext kann über die PDF-URL abgerufen werden.

## Weitere Informationen

- [arXiv API-Dokumentation](https://arxiv.org/help/api/user-manual.html)
- [arXiv API-Nutzungsbedingungen](https://arxiv.org/help/api/tou.html)
- [arXiv-Taxonomie](https://arxiv.org/category_taxonomy)- [arXiv-Taxonomie](https://arxiv.org/category_taxonomy)### ArXivAPI

```javascript
import ArXivAPI from '../api/integrations/ArXivAPI.js';

// Initialisiere die API
const arxivAPI = new ArXivAPI();
await arxivAPI.initialize();

// Suche nach Publikationen
const searchResults = await arxivAPI.searchPapers({
  query: 'quantum computing',
  category: 'quant-ph',
  maxResults: 10
});

// Rufe Details zu einer Publikation ab
const paper = await arxivAPI.getPaper('2304.12345');

// Rufe die neuesten Publikationen in einer Kategorie ab
const recentPapers = await arxivAPI.getRecentPapers({
  category: 'cs.AI',
  maxResults: 30
});
```

### ArXivIntegrationController

Für erweiterte Funktionen wie DOI-Erstellung und NFT-Prägung verwenden Sie den `ArXivIntegrationController` aus dem Controllers-Verzeichnis:

```javascript
import ArXivIntegrationController from '../../controllers/ArXivIntegrationController.js';
import { StorageService } from '../../storage/StorageService.js';

// Initialisiere den Controller
const storageService = new StorageService();
const arxivController = new ArXivIntegrationController({
  storageService,
  // Optional: crossrefAPI, nftService
});
await arxivController.initialize();

// Verarbeite eine Publikation (DOI erstellen, NFT prägen, speichern)
const result = await arxivController.processPaper('2304.12345', {
  createDOI: true,
  mintNFT: false,
  storeData: true
});

// Suche nach Publikationen
const searchResults = await arxivController.searchPapers({
  query: 'quantum computing',
  maxResults: 10
});
```

## Hinweise

- Die arXiv API hat Ratenbegrenzungen. Es wird empfohlen, nicht mehr als eine Anfrage alle 3 Sekunden zu stellen.
- Die meisten arXiv-Publikationen stehen unter der "arXiv non-exclusive license to distribute", die keine Weiterverbreitung erlaubt.
- Nur Publikationen mit DOIs können als NFTs tokenisiert werden. Nicht alle arXiv-Publikationen haben DOIs.
- Die arXiv API liefert nur Metadaten, keinen Volltext. Der Volltext kann über die PDF-URL abgerufen werden.

## Weitere Informationen

- [arXiv API-Dokumentation](https://arxiv.org/help/api/user-manual.html)
- [arXiv API-Nutzungsbedingungen](https://arxiv.org/help/api/tou.html)
- [arXiv-Taxonomie](https://arxiv.org/category_taxonomy)