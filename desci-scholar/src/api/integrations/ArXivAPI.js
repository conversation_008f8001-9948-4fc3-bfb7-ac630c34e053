/**
 * @fileoverview arXiv API Integration für DeSci-Scholar
 * 
 * Diese Klasse bietet eine Schnittstelle zur arXiv API,
 * um wissenschaftliche Preprints und Publikationen abzurufen.
 * Sie ergänzt die bestehenden SemanticScholar-, Crossref- und DataCite-Integrationen.
 */

import axios from 'axios';
import { parse } from 'fast-xml-parser';
import { normalizeText } from '../../utils/text.js';

/**
 * Klasse für die Integration mit der arXiv API
 */
export class ArXivAPI {
  /**
   * Konstruktor für die arXiv API Integration
   * 
   * @param {Object} options Konfigurationsoptionen
   * @param {string} options.baseUrl Basis-URL für die API (Standard: offizielle API)
   * @param {number} options.timeout Timeout für Anfragen in ms (Standard: 10000)
   * @param {number} options.maxRetries Maximale Anzahl von Wiederholungsversuchen (Standard: 3)
   * @param {number} options.maxResults Maximale Anzahl von E<PERSON>bnissen pro Anfrage (Standard: 100)
   */
  constructor(options = {}) {
    const {
      baseUrl = 'http://export.arxiv.org/api/query',
      timeout = 10000,
      maxRetries = 3,
      maxResults = 100
    } = options;

    this.baseUrl = baseUrl;
    this.timeout = timeout;
    this.maxRetries = maxRetries;
    this.maxResults = Math.min(maxResults, 2000); // arXiv API-Limits beachten

    // Axios-Instanz für API-Anfragen
    this.client = axios.create({
      baseURL: this.baseUrl,
      timeout: this.timeout
    });

    // Statistiken für API-Nutzung
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      retries: 0,
      requestsByEndpoint: {}
    };
  }

  /**
   * Initialisiert die API-Verbindung und prüft die Verfügbarkeit
   * 
   * @returns {Promise<boolean>} Ob die API verfügbar ist
   */
  async initialize() {
    try {
      // Einfache Anfrage, um die API-Verfügbarkeit zu prüfen
      // Wir verwenden eine kleine Suche mit minimalen Ergebnissen
      const testResponse = await this.searchPapers({ query: 'physics', maxResults: 1 });
      
      console.log('ArXivAPI: Erfolgreich initialisiert');
      
      return true;
    } catch (error) {
      console.error('ArXivAPI: Fehler bei der Initialisierung', error.message);
      return false;
    }
  }

  /**
   * Führt eine API-Anfrage mit Wiederholungslogik durch
   * 
   * @private
   * @param {Object} params Anfrageparameter
   * @returns {Promise<Object>} API-Antwort
   */
  async _makeRequest(params = {}) {
    // Statistik aktualisieren
    this.stats.totalRequests++;
    const endpoint = 'query'; // arXiv hat nur einen Hauptendpunkt
    this.stats.requestsByEndpoint[endpoint] = (this.stats.requestsByEndpoint[endpoint] || 0) + 1;
    
    let retries = 0;
    
    while (retries <= this.maxRetries) {
      try {
        const response = await this.client.get('', { params });
        
        // Erfolgreiche Anfrage
        this.stats.successfulRequests++;
        
        // Parse XML zu JSON
        const options = {
          attributeNamePrefix: "",
          ignoreAttributes: false,
          parseAttributeValue: true
        };
        
        const result = parse(response.data, options);
        return result.feed;
      } catch (error) {
        retries++;
        this.stats.retries++;
        
        // Prüfen, ob weitere Versuche sinnvoll sind
        const status = error.response?.status;
        
        // Bei Rate-Limiting oder Serverfehlern erneut versuchen
        if ((status === 429 || (status >= 500 && status < 600)) && retries <= this.maxRetries) {
          // Exponentielles Backoff
          const delay = Math.min(1000 * Math.pow(2, retries), 30000);
          console.warn(`ArXivAPI: Wiederhole Anfrage nach ${delay}ms (${retries}/${this.maxRetries})`);
          await new Promise(resolve => setTimeout(resolve, delay));
          continue;
        }
        
        // Bei anderen Fehlern oder nach maximalen Versuchen aufgeben
        this.stats.failedRequests++;
        
        // Fehler mit zusätzlichen Informationen werfen
        const errorMessage = error.response?.data || error.message;
        throw new Error(`arXiv API-Fehler: ${errorMessage}`);
      }
    }
  }

  /**
   * Formatiert einen arXiv-Eintrag in ein strukturiertes Format
   * 
   * @private
   * @param {Object} entry arXiv-API-Eintrag
   * @returns {Object} Formatierte Publikation
   */
  _formatEntry(entry) {
    // Extrahiere arXiv-ID aus dem Link
    let arxivId = '';
    if (Array.isArray(entry.link)) {
      const idLink = entry.link.find(link => link.rel === 'alternate');
      if (idLink) {
        arxivId = idLink.href.split('/').pop();
      }
    }

    // Extrahiere Kategorien
    const categories = Array.isArray(entry.category) 
      ? entry.category.map(cat => cat.term) 
      : (entry.category ? [entry.category.term] : []);

    // Formatiere Autoren
    const authors = Array.isArray(entry.author) 
      ? entry.author.map(author => author.name) 
      : (entry.author ? [entry.author.name] : []);

    return {
      id: arxivId,
      title: entry.title ? normalizeText(entry.title) : '',
      abstract: entry.summary ? normalizeText(entry.summary) : '',
      authors,
      categories,
      primaryCategory: Array.isArray(entry.category) ? entry.category[0].term : '',
      published: entry.published || '',
      updated: entry.updated || '',
      doi: entry.doi || null,
      journalRef: entry['journal-ref'] || null,
      comment: entry.comment || null,
      pdfUrl: Array.isArray(entry.link) 
        ? entry.link.find(link => link.title === 'pdf')?.href || ''
        : '',
      arxivUrl: `https://arxiv.org/abs/${arxivId}`
    };
  }

  /**
   * Sucht nach Publikationen in arXiv
   * 
   * @param {Object} params Suchparameter
   * @param {string} params.query Allgemeiner Suchbegriff
   * @param {string} params.title Suche im Titel
   * @param {string} params.author Suche nach Autor
   * @param {string} params.category Suche nach Kategorie (z.B. "physics", "cs.AI")
   * @param {string} params.id Suche nach arXiv-ID
   * @param {number} params.maxResults Maximale Anzahl von Ergebnissen
   * @param {number} params.start Startposition für Paginierung
   * @param {string} params.sortBy Sortierkriterium ("relevance", "lastUpdatedDate", "submittedDate")
   * @param {string} params.sortOrder Sortierreihenfolge ("ascending", "descending")
   * @returns {Promise<Object>} Suchergebnisse
   */
  async searchPapers(params = {}) {
    const { 
      query, 
      title, 
      author, 
      category, 
      id,
      maxResults = 10, 
      start = 0,
      sortBy = 'relevance',
      sortOrder = 'descending'
    } = params;

    // Baue die Suchanfrage
    let searchQuery = [];
    
    if (query) searchQuery.push(`all:${query}`);
    if (title) searchQuery.push(`ti:${title}`);
    if (author) searchQuery.push(`au:${author}`);
    if (category) searchQuery.push(`cat:${category}`);
    if (id) searchQuery.push(`id:${id}`);

    const searchString = searchQuery.join('+AND+');
    
    if (!searchString) {
      throw new Error('Mindestens ein Suchparameter ist erforderlich');
    }

    const requestParams = {
      search_query: searchString,
      max_results: Math.min(maxResults, this.maxResults),
      start,
      sortBy,
      sortOrder
    };

    try {
      const response = await this._makeRequest(requestParams);
      
      // Formatiere die Antwort
      const entries = Array.isArray(response.entry) ? response.entry : [response.entry];
      const formattedEntries = entries.map(entry => this._formatEntry(entry));
      
      return {
        success: true,
        totalResults: response['opensearch:totalResults'] || formattedEntries.length,
        startIndex: response['opensearch:startIndex'] || start,
        itemsPerPage: response['opensearch:itemsPerPage'] || maxResults,
        items: formattedEntries
      };
    } catch (error) {
      console.error('arXiv-Suchfehler:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Ruft Details zu einer Publikation anhand ihrer arXiv-ID ab
   * 
   * @param {string} arxivId arXiv-ID der Publikation
   * @returns {Promise<Object>} Publikationsdetails
   */
  async getPaper(arxivId) {
    if (!arxivId) {
      throw new Error('arXiv-ID ist erforderlich');
    }

    try {
      const response = await this.searchPapers({ id: arxivId });
      
      if (!response.success || response.items.length === 0) {
        return {
          success: false,
          error: 'Publikation nicht gefunden',
          arxivId
        };
      }
      
      return {
        success: true,
        arxivId,
        paper: response.items[0]
      };
    } catch (error) {
      console.error('arXiv-Publikationsabruffehler:', error);
      return {
        success: false,
        error: error.message,
        arxivId
      };
    }
  }

  /**
   * Sucht nach den neuesten Publikationen in einer bestimmten Kategorie
   * 
   * @param {Object} params Suchparameter
   * @param {string} params.category Kategorie (z.B. "physics", "cs.AI")
   * @param {number} params.maxResults Maximale Anzahl von Ergebnissen
   * @returns {Promise<Object>} Suchergebnisse
   */
  async getRecentPapers(params = {}) {
    const { category, maxResults = 30 } = params;

    if (!category) {
      throw new Error('Kategorie ist erforderlich');
    }

    return this.searchPapers({
      category,
      maxResults,
      sortBy: 'submittedDate',
      sortOrder: 'descending'
    });
  }

  /**
   * Konvertiert arXiv-Kategorien in lesbare Namen
   * 
   * @param {string} category arXiv-Kategorie-Code
   * @returns {string} Lesbarer Name der Kategorie
   */
  getCategoryName(category) {
    const categoryMap = {
      'astro-ph': 'Astrophysik',
      'astro-ph.CO': 'Kosmologie und Nichtgalaktische Astrophysik',
      'astro-ph.EP': 'Erd- und Planetenastrophysik',
      'astro-ph.GA': 'Astrophysik von Galaxien',
      'astro-ph.HE': 'Hochenergetische Astrophysik',
      'astro-ph.IM': 'Instrumentierung und Methoden der Astrophysik',
      'astro-ph.SR': 'Solar- und Stellarastrophysik',
      'cond-mat': 'Kondensierte Materie',
      'cond-mat.dis-nn': 'Ungeordnete Systeme und neuronale Netzwerke',
      'cond-mat.mes-hall': 'Mesoskopische und Nanoskalen-Physik',
      'cond-mat.mtrl-sci': 'Materialwissenschaft',
      'cond-mat.other': 'Andere kondensierte Materie',
      'cond-mat.quant-gas': 'Quantengase',
      'cond-mat.soft': 'Weiche kondensierte Materie',
      'cond-mat.stat-mech': 'Statistische Mechanik',
      'cond-mat.str-el': 'Stark korrelierte Elektronen',
      'cond-mat.supr-con': 'Supraleitfähigkeit',
      'cs': 'Informatik',
      'cs.AI': 'Künstliche Intelligenz',
      'cs.AR': 'Hardware-Architektur',
      'cs.CC': 'Berechnungskomplexität',
      'cs.CE': 'Computational Engineering, Finanzen und Wissenschaft',
      'cs.CG': 'Computational Geometry',
      'cs.CL': 'Computation and Language',
      'cs.CR': 'Kryptographie und Sicherheit',
      'cs.CV': 'Computer Vision und Pattern Recognition',
      'cs.CY': 'Computer und Gesellschaft',
      'cs.DB': 'Datenbanken',
      'cs.DC': 'Verteiltes, Paralleles und Cluster-Computing',
      'cs.DL': 'Digitale Bibliotheken',
      'cs.DM': 'Diskrete Mathematik',
      'cs.DS': 'Datenstrukturen und Algorithmen',
      'cs.ET': 'Emerging Technologies',
      'cs.FL': 'Formale Sprachen und Automatentheorie',
      'cs.GL': 'Allgemeine Literatur',
      'cs.GR': 'Grafik',
      'cs.GT': 'Informatik und Spieltheorie',
      'cs.HC': 'Mensch-Computer-Interaktion',
      'cs.IR': 'Information Retrieval',
      'cs.IT': 'Informationstheorie',
      'cs.LG': 'Machine Learning',
      'cs.LO': 'Logik in der Informatik',
      'cs.MA': 'Multiagentensysteme',
      'cs.MM': 'Multimedia',
      'cs.MS': 'Mathematische Software',
      'cs.NA': 'Numerische Analysis',
      'cs.NE': 'Neuronales und Evolutionäres Computing',
      'cs.NI': 'Netzwerk- und Internet-Architektur',
      'cs.OS': 'Betriebssysteme',
      'cs.PF': 'Performance',
      'cs.PL': 'Programmiersprachen',
      'cs.RO': 'Robotik',
      'cs.SC': 'Symbolisches Computing',
      'cs.SD': 'Sound',
      'cs.SE': 'Software Engineering',
      'cs.SI': 'Soziale und Informationsnetzwerke',
      'cs.SY': 'Systeme und Steuerung',
      'econ': 'Wirtschaftswissenschaften',
      'eess': 'Elektrotechnik und Systemwissenschaft',
      'gr-qc': 'Allgemeine Relativitätstheorie und Quantenkosmologie',
      'hep-ex': 'Hochenergiephysik - Experiment',
      'hep-lat': 'Hochenergiephysik - Gitter',
      'hep-ph': 'Hochenergiephysik - Phänomenologie',
      'hep-th': 'Hochenergiephysik - Theorie',
      'math': 'Mathematik',
      'math-ph': 'Mathematische Physik',
      'nlin': 'Nichtlineare Wissenschaften',
      'nucl-ex': 'Kernphysik - Experiment',
      'nucl-th': 'Kernphysik - Theorie',
      'physics': 'Physik',
      'q-bio': 'Quantitative Biologie',
      'q-fin': 'Quantitative Finanzen',
      'quant-ph': 'Quantenphysik',
      'stat': 'Statistik'
    };
    
    return categoryMap[category] || category;
  }
  
  /**
   * Extrahiert DOI aus arXiv-Metadaten, falls vorhanden
   * 
   * @param {Object} paper arXiv-Publikation
   * @returns {string|null} DOI oder null, wenn nicht vorhanden
   */
  extractDOI(paper) {
    // Prüfe direkt auf DOI-Feld
    if (paper.doi) {
      return paper.doi;
    }
    
    // Prüfe im Journal-Referenz-Feld auf DOI-Muster
    if (paper.journalRef) {
      const doiMatch = paper.journalRef.match(/10\.\d{4,9}\/[-._;()\/:A-Z0-9]+/i);
      if (doiMatch) {
        return doiMatch[0];
      }
    }
    
    // Prüfe im Kommentar-Feld auf DOI-Muster
    if (paper.comment) {
      const doiMatch = paper.comment.match(/10\.\d{4,9}\/[-._;()\/:A-Z0-9]+/i);
      if (doiMatch) {
        return doiMatch[0];
      }
    }
    
    return null;
  }

  /**
   * Verknüpft arXiv-Daten mit DOI und Crossref-Metadaten (falls verfügbar)
   * 
   * @param {Object} paper arXiv-Publikation
   * @param {Object} crossrefAPI Instanz der CrossrefAPI
   * @returns {Promise<Object>} Erweiterte Publikationsdaten
   */
  async enrichWithCrossref(paper, crossrefAPI) {
    if (!crossrefAPI) {
      throw new Error('CrossrefAPI-Instanz ist erforderlich');
    }
    
    // Extrahiere DOI
    const doi = this.extractDOI(paper);
    
    if (!doi) {
      return {
        ...paper,
        hasDOI: false
      };
    }
    
    try {
      // Versuche, Metadaten über CrossrefAPI zu holen
      const crossrefData = await crossrefAPI.getWorkByDOI(doi);
      
      if (crossrefData.success) {
        return {
          ...paper,
          doi,
          hasDOI: true,
          crossrefMetadata: crossrefData.metadata
        };
      } else {
        return {
          ...paper,
          doi,
          hasDOI: true
        };
      }
    } catch (error) {
      console.warn(`Konnte keine Crossref-Daten für DOI ${doi} abrufen:`, error.message);
      
      return {
        ...paper,
        doi,
        hasDOI: true
      };
    }
  }

  /**
   * Gibt die aktuellen API-Nutzungsstatistiken zurück
   * 
   * @returns {Object} API-Nutzungsstatistiken
   */
  getStats() {
    return {
      ...this.stats,
      successRate: this.stats.totalRequests > 0 
        ? (this.stats.successfulRequests / this.stats.totalRequests * 100).toFixed(2) + '%' 
        : '0%'
    };
  }
}

export default ArXivAPI;