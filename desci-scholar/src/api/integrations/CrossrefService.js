/**
 * @fileoverview Service für die Interaktion mit der Crossref API
 * 
 * Dieser Service bietet eine einheitliche Schnittstelle für die Interaktion mit der Crossref API,
 * einschließlich der Suche nach DOIs, dem Abru<PERSON> von Metadaten und der Validierung von Publikationen.
 */

import CrossrefClient from './CrossrefClient.js';
import { logger } from '../../utils/logger.js';

/**
 * Service für die Interaktion mit der Crossref API
 */
export class CrossrefService {
  /**
   * Erstellt eine neue CrossrefService-Instanz
   * @param {Object} config - Konfigurationsoptionen
   * @param {string} config.apiBaseUrl - Basis-URL der Crossref API
   * @param {string} config.email - E-Mail-Adresse für die Authentifizierung (für bessere Rate Limits)
   * @param {string} config.token - API-Token für die Authentifizierung
   * @param {number} config.timeout - Timeout für API-Anfragen in Millisekunden
   * @param {number} config.maxRetries - Maximale An<PERSON>hl von Wiederholungsversuchen bei Fehlern
   */
  constructor(config = {}) {
    this.config = config;
    this.client = null;
    this.initialized = false;
  }
  
  /**
   * Initialisiert den CrossrefService
   * @returns {Promise<boolean>} Erfolgsstatus
   */
  async initialize() {
    try {
      logger.info('CrossrefService: Initialisiere');
      
      // Erstelle CrossrefClient
      this.client = new CrossrefClient({
        baseUrl: this.config.apiBaseUrl,
        email: this.config.email,
        token: this.config.token,
        timeout: this.config.timeout,
        maxRetries: this.config.maxRetries
      });
      
      this.initialized = true;
      
      logger.info('CrossrefService: Erfolgreich initialisiert');
      
      return true;
    } catch (error) {
      logger.error('CrossrefService: Fehler bei der Initialisierung', {
        error: error.message,
        stack: error.stack
      });
      
      return false;
    }
  }
  
  /**
   * Ruft Metadaten für einen DOI ab
   * @param {string} doi - DOI, für die Metadaten abgerufen werden sollen
   * @returns {Promise<Object>} Die abgerufenen Metadaten
   */
  async getWork(doi) {
    try {
      this._ensureInitialized();
      
      logger.info('CrossrefService: Rufe Metadaten für DOI ab', { doi });
      
      const metadata = await this.client.getMetadata(doi);
      
      return {
        success: true,
        doi,
        metadata
      };
    } catch (error) {
      logger.error('CrossrefService: Fehler beim Abrufen von Metadaten', {
        error: error.message,
        stack: error.stack,
        doi
      });
      
      return {
        success: false,
        error: error.message,
        doi
      };
    }
  }
  
  /**
   * Sucht nach DOIs anhand von Suchkriterien
   * @param {Object} params - Suchparameter
   * @returns {Promise<Object>} Die Suchergebnisse
   */
  async searchDOIs(params = {}, options = {}) {
    try {
      this._ensureInitialized();
      
      logger.info('CrossrefService: Suche nach DOIs', params);
      
      const results = await this.client.searchDois(params);
      
      return {
        success: true,
        results: results.items,
        total: results.total
      };
    } catch (error) {
      logger.error('CrossrefService: Fehler bei der DOI-Suche', {
        error: error.message,
        stack: error.stack,
        params
      });
      
      return {
        success: false,
        error: error.message,
        params
      };
    }
  }
  
  /**
   * Sucht nach DOIs anhand einer externen ID (z.B. arXiv-ID)
   * @param {string} idType - Typ der externen ID (z.B. 'arxiv')
   * @param {string} idValue - Wert der externen ID
   * @returns {Promise<Object>} Die Suchergebnisse
   */
  async searchByExternalId(idType, idValue) {
    try {
      this._ensureInitialized();
      
      logger.info('CrossrefService: Suche nach DOIs anhand externer ID', { idType, idValue });
      
      const results = await this.client.searchByExternalId(idType, idValue);
      
      return {
        success: true,
        results: results.items,
        total: results.total
      };
    } catch (error) {
      logger.error('CrossrefService: Fehler bei der Suche nach DOIs anhand externer ID', {
        error: error.message,
        stack: error.stack,
        idType,
        idValue
      });
      
      return {
        success: false,
        error: error.message,
        idType,
        idValue
      };
    }
  }
  
  /**
   * Stellt sicher, dass der Service initialisiert ist
   * @private
   * @throws {Error} Wenn der Service nicht initialisiert ist
   */
  _ensureInitialized() {
    if (!this.initialized || !this.client) {
      throw new Error('CrossrefService ist nicht initialisiert');
    }
  }
}

export default CrossrefService;/**
 * @fileoverview Service für die Interaktion mit der Crossref API
 * 
 * Dieser Service bietet eine einheitliche Schnittstelle für die Interaktion mit der Crossref API,
 * einschließlich der Suche nach DOIs, dem Abrufen von Metadaten und der Validierung von Publikationen.
 */

import CrossrefClient from './CrossrefClient.js';
import { logger } from '../../utils/logger.js';

/**
 * Service für die Interaktion mit der Crossref API
 */
export class CrossrefService {
  /**
   * Erstellt eine neue CrossrefService-Instanz
   * @param {Object} config - Konfigurationsoptionen
   * @param {string} config.apiBaseUrl - Basis-URL der Crossref API
   * @param {string} config.email - E-Mail-Adresse für die Authentifizierung (für bessere Rate Limits)
   * @param {string} config.token - API-Token für die Authentifizierung
   * @param {number} config.timeout - Timeout für API-Anfragen in Millisekunden
   * @param {number} config.maxRetries - Maximale Anzahl von Wiederholungsversuchen bei Fehlern
   */
  constructor(config = {}) {
    this.config = config;
    this.client = null;
    this.initialized = false;
  }
  
  /**
   * Initialisiert den CrossrefService
   * @returns {Promise<boolean>} Erfolgsstatus
   */
  async initialize() {
    try {
      logger.info('CrossrefService: Initialisiere');
      
      // Erstelle CrossrefClient
      this.client = new CrossrefClient({
        baseUrl: this.config.apiBaseUrl,
        email: this.config.email,
        token: this.config.token,
        timeout: this.config.timeout,
        maxRetries: this.config.maxRetries
      });
      
      this.initialized = true;
      
      logger.info('CrossrefService: Erfolgreich initialisiert');
      
      return true;
    } catch (error) {
      logger.error('CrossrefService: Fehler bei der Initialisierung', {
        error: error.message,
        stack: error.stack
      });
      
      return false;
    }
  }
  
  /**
   * Ruft Metadaten für einen DOI ab
   * @param {string} doi - DOI, für die Metadaten abgerufen werden sollen
   * @returns {Promise<Object>} Die abgerufenen Metadaten
   */
  async getWork(doi) {
    try {
      this._ensureInitialized();
      
      logger.info('CrossrefService: Rufe Metadaten für DOI ab', { doi });
      
      const metadata = await this.client.getMetadata(doi);
      
      return {
        success: true,
        doi,
        metadata
      };
    } catch (error) {
      logger.error('CrossrefService: Fehler beim Abrufen von Metadaten', {
        error: error.message,
        stack: error.stack,
        doi
      });
      
      return {
        success: false,
        error: error.message,
        doi
      };
    }
  }
  
  /**
   * Sucht nach DOIs anhand von Suchkriterien
   * @param {Object} params - Suchparameter
   * @returns {Promise<Object>} Die Suchergebnisse
   */
  async searchDOIs(params = {}, options = {}) {
    try {
      this._ensureInitialized();
      
      logger.info('CrossrefService: Suche nach DOIs', params);
      
      const results = await this.client.searchDois(params);
      
      return {
        success: true,
        results: results.items,
        total: results.total
      };
    } catch (error) {
      logger.error('CrossrefService: Fehler bei der DOI-Suche', {
        error: error.message,
        stack: error.stack,
        params
      });
      
      return {
        success: false,
        error: error.message,
        params
      };
    }
  }
  
  /**
   * Sucht nach DOIs anhand einer externen ID (z.B. arXiv-ID)
   * @param {string} idType - Typ der externen ID (z.B. 'arxiv')
   * @param {string} idValue - Wert der externen ID
   * @returns {Promise<Object>} Die Suchergebnisse
   */
  async searchByExternalId(idType, idValue) {
    try {
      this._ensureInitialized();
      
      logger.info('CrossrefService: Suche nach DOIs anhand externer ID', { idType, idValue });
      
      const results = await this.client.searchByExternalId(idType, idValue);
      
      return {
        success: true,
        results: results.items,
        total: results.total
      };
    } catch (error) {
      logger.error('CrossrefService: Fehler bei der Suche nach DOIs anhand externer ID', {
        error: error.message,
        stack: error.stack,
        idType,
        idValue
      });
      
      return {
        success: false,
        error: error.message,
        idType,
        idValue
      };
    }
  }
  
  /**
   * Stellt sicher, dass der Service initialisiert ist
   * @private
   * @throws {Error} Wenn der Service nicht initialisiert ist
   */
  _ensureInitialized() {
    if (!this.initialized || !this.client) {
      throw new Error('CrossrefService ist nicht initialisiert');
    }
  }
}

export default CrossrefService;