/**
 * DataCiteAPI - Integration für DOI-Registrierung
 * Ermöglicht die Registrierung von DOIs bei DataCite
 */

import axios from 'axios';

/**
 * Klasse für die Interaktion mit der DataCite API
 */
class DataCiteAPI {
  /**
   * Erstellt eine neue Instanz der DataCiteAPI
   * @param {Object} config Konfiguration
   * @param {string} config.apiUrl DataCite API URL
   * @param {string} config.username DataCite Benutzername
   * @param {string} config.password DataCite Passwort
   * @param {string} config.prefix DOI-Präfix
   * @param {boolean} config.testMode Testmodus (simuliert API-Aufrufe)
   */
  constructor(config = {}) {
    this.config = {
      apiUrl: 'https://api.datacite.org',
      username: process.env.DATACITE_USERNAME,
      password: process.env.DATACITE_PASSWORD,
      prefix: process.env.DATACITE_PREFIX || '10.5072', // DataCite-Testpräfix
      testMode: process.env.NODE_ENV !== 'production',
      ...config
    };
    
    // HTTP-Client konfigurieren
    if (!this.config.testMode) {
      this.client = axios.create({
        baseURL: this.config.apiUrl,
        auth: {
          username: this.config.username,
          password: this.config.password
        },
        headers: {
          'Content-Type': 'application/vnd.api+json'
        }
      });
    }
  }
  
  /**
   * Testet die Verbindung zur DataCite API
   * @returns {Promise<boolean>} Erfolg
   */
  async testConnection() {
    if (this.config.testMode) {
      console.log('DataCite API im Testmodus, überspringe Verbindungstest');
      return true;
    }
    
    try {
      const response = await this.client.get('/heartbeat');
      return response.status === 200;
    } catch (error) {
      console.error('DataCite Verbindungsfehler:', error.message);
      return false;
    }
  }
  
  /**
   * Registriert einen DOI mit den angegebenen Metadaten
   * @param {string} doi DOI zu registrieren
   * @param {Object} metadata Metadaten für den DOI
   * @returns {Promise<Object>} Registrierungsergebnis
   */
  async registerDOI(doi, metadata) {
    if (this.config.testMode) {
      console.log(`[MOCK] DOI '${doi}' wird registriert mit Metadaten:`, metadata);
      
      // Simuliere Antwort im Testmodus
      return {
        success: true,
        doi,
        url: `https://doi.org/${doi}`,
        message: 'DOI erfolgreich im Testmodus registriert'
      };
    }
    
    try {
      // Bereite Daten für die API vor
      const payload = this._formatDataCitePayload(doi, metadata);
      
      // Sende Anfrage an DataCite
      const response = await this.client.post('/dois', payload);
      
      if (response.status === 201) {
        return {
          success: true,
          doi,
          url: `https://doi.org/${doi}`,
          message: 'DOI erfolgreich registriert'
        };
      } else {
        return {
          success: false,
          doi,
          error: `Unerwarteter Statuscode: ${response.status}`
        };
      }
    } catch (error) {
      console.error('Fehler bei der DOI-Registrierung:', error);
      
      return {
        success: false,
        doi,
        error: error.response?.data?.errors || error.message
      };
    }
  }
  
  /**
   * Formatiert Metadaten für die DataCite API
   * @param {string} doi DOI
   * @param {Object} metadata Metadaten
   * @returns {Object} Formatierte Payload
   * @private
   */
  _formatDataCitePayload(doi, metadata) {
    // Erstelle Payload nach DataCite API v2 Schema
    return {
      data: {
        type: 'dois',
        attributes: {
          doi,
          titles: metadata.titles,
          creators: metadata.creators,
          publisher: metadata.publisher,
          publicationYear: metadata.publicationYear,
          types: {
            resourceTypeGeneral: metadata.resourceType.resourceTypeGeneral,
            resourceType: metadata.resourceType.resourceType
          },
          url: metadata.url,
          subjects: metadata.subjects,
          descriptions: metadata.descriptions,
          version: metadata.version,
          schemaVersion: 'http://datacite.org/schema/kernel-4'
        }
      }
    };
  }
  
  /**
   * Aktualisiert Metadaten für einen existierenden DOI
   * @param {string} doi DOI zu aktualisieren
   * @param {Object} metadata Neue Metadaten
   * @returns {Promise<Object>} Aktualisierungsergebnis
   */
  async updateDOI(doi, metadata) {
    if (this.config.testMode) {
      console.log(`[MOCK] DOI '${doi}' wird aktualisiert mit Metadaten:`, metadata);
      
      // Simuliere Antwort im Testmodus
      return {
        success: true,
        doi,
        message: 'DOI erfolgreich im Testmodus aktualisiert'
      };
    }
    
    try {
      // Bereite Daten für die API vor
      const payload = this._formatDataCitePayload(doi, metadata);
      
      // Sende Anfrage an DataCite
      const response = await this.client.put(`/dois/${doi}`, payload);
      
      if (response.status === 200) {
        return {
          success: true,
          doi,
          message: 'DOI erfolgreich aktualisiert'
        };
      } else {
        return {
          success: false,
          doi,
          error: `Unerwarteter Statuscode: ${response.status}`
        };
      }
    } catch (error) {
      console.error('Fehler bei der DOI-Aktualisierung:', error);
      
      return {
        success: false,
        doi,
        error: error.response?.data?.errors || error.message
      };
    }
  }
  
  /**
   * Ruft Metadaten für einen DOI ab
   * @param {string} doi DOI
   * @returns {Promise<Object>} Metadaten
   */
  async getDOI(doi) {
    if (this.config.testMode) {
      console.log(`[MOCK] Metadaten für DOI '${doi}' werden abgerufen`);
      
      // Simuliere Antwort im Testmodus
      return {
        success: true,
        doi,
        metadata: {
          titles: [{ title: 'Beispielpublikation' }],
          creators: [{ name: 'Testautor', nameType: 'Personal' }],
          publisher: 'DeSci Scholar',
          publicationYear: '2023',
          url: `https://desci-scholar.org/publications/${doi.split('/').pop()}`
        }
      };
    }
    
    try {
      // Sende Anfrage an DataCite
      const response = await this.client.get(`/dois/${doi}`);
      
      if (response.status === 200) {
        return {
          success: true,
          doi,
          metadata: response.data.data.attributes
        };
      } else {
        return {
          success: false,
          doi,
          error: `Unerwarteter Statuscode: ${response.status}`
        };
      }
    } catch (error) {
      console.error('Fehler beim Abrufen der DOI-Metadaten:', error);
      
      return {
        success: false,
        doi,
        error: error.response?.data?.errors || error.message
      };
    }
  }
}

export default DataCiteAPI; 