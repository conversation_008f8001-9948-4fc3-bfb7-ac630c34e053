/**
 * @fileoverview Zotero API Integration für DeSci-Scholar
 * 
 * Diese Datei implementiert die Integration mit der Zotero API, um Zugriff auf
 * Literatursammlungen, Bibliotheken und Metadaten zu ermöglichen.
 * 
 * Zotero API Dokumentation: https://www.zotero.org/support/dev/web_api/v3/start
 */

import axios from 'axios';

/**
 * Klasse für die Integration mit der Zotero API
 */
export class ZoteroAPI {
  /**
   * Konstruktor für die Zotero API Integration
   * 
   * @param {Object} options Konfigurationsoptionen
   * @param {string} options.apiKey Zotero API-Schlüssel (optional)
   * @param {number} options.timeout Timeout für API-Anfragen in ms (Standard: 10000)
   * @param {number} options.maxRetries Maximale Anzahl von Wiederholungsversuchen (Standard: 3)
   */
  constructor(options = {}) {
    const {
      apiKey,
      timeout = 10000,
      maxRetries = 3
    } = options;
    
    this.apiKey = apiKey;
    this.baseUrl = 'https://api.zotero.org';
    this.timeout = timeout;
    this.maxRetries = maxRetries;
    
    // Statistiken für API-Aufrufe
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      itemsRetrieved: 0
    };
  }
  
  /**
   * Führt eine Anfrage an die Zotero API durch
   * 
   * @param {string} endpoint API-Endpunkt
   * @param {Object} options Anfrage-Optionen
   * @param {string} options.method HTTP-Methode (Standard: 'GET')
   * @param {Object} options.params Query-Parameter
   * @param {Object} options.data Request-Body für POST/PUT
   * @param {Object} options.headers Zusätzliche HTTP-Header
   * @returns {Promise<Object>} API-Antwort
   */
  async request(endpoint, options = {}) {
    const {
      method = 'GET',
      params = {},
      data = null,
      headers = {}
    } = options;
    
    // API-Schlüssel hinzufügen, falls vorhanden
    const requestHeaders = {
      'Content-Type': 'application/json',
      ...headers
    };
    
    if (this.apiKey) {
      requestHeaders['Zotero-API-Key'] = this.apiKey;
    }
    
    // Statistik aktualisieren
    this.stats.totalRequests++;
    
    // Anfrage mit Wiederholungslogik
    let lastError = null;
    for (let attempt = 0; attempt < this.maxRetries; attempt++) {
      try {
        // Bei Wiederholungsversuchen warten
        if (attempt > 0) {
          const delay = 1000 * Math.pow(2, attempt - 1);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
        
        // Anfrage ausführen
        const response = await axios({
          method,
          url: `${this.baseUrl}${endpoint}`,
          params,
          data,
          headers: requestHeaders,
          timeout: this.timeout
        });
        
        // Statistik aktualisieren
        this.stats.successfulRequests++;
        
        // Rückgabe der Antwort
        return {
          success: true,
          data: response.data,
          headers: response.headers,
          status: response.status
        };
      } catch (error) {
        lastError = error;
        
        // Prüfen, ob der Fehler wiederholbar ist
        const status = error.response?.status;
        const isRetryable = 
          error.code === 'ECONNRESET' || 
          error.code === 'ETIMEDOUT' ||
          status === 429 || // Too Many Requests
          status === 503 || // Service Unavailable
          status === 500;   // Internal Server Error
        
        if (!isRetryable) {
          break;
        }
      }
    }
    
    // Statistik aktualisieren
    this.stats.failedRequests++;
    
    // Fehlerbehandlung
    const errorResponse = lastError.response;
    return {
      success: false,
      error: lastError.message,
      status: errorResponse?.status,
      data: errorResponse?.data
    };
  }
  
  /**
   * Ruft Benutzerinformationen ab
   * 
   * @returns {Promise<Object>} Benutzerinformationen
   */
  async getUserInfo() {
    return this.request('/keys/current');
  }
  
  /**
   * Ruft Bibliotheken des Benutzers ab
   * 
   * @returns {Promise<Object>} Bibliotheken des Benutzers
   */
  async getLibraries() {
    const userInfo = await this.getUserInfo();
    
    if (!userInfo.success) {
      return userInfo;
    }
    
    const userId = userInfo.data.userID;
    const userLibrary = await this.request(`/users/${userId}/libraries`);
    
    return userLibrary;
  }
  
  /**
   * Ruft Sammlungen einer Bibliothek ab
   * 
   * @param {string} libraryType Bibliothekstyp ('user' oder 'group')
   * @param {string} libraryId Bibliotheks-ID
   * @returns {Promise<Object>} Sammlungen der Bibliothek
   */
  async getCollections(libraryType, libraryId) {
    return this.request(`/${libraryType}s/${libraryId}/collections`);
  }
  
  /**
   * Ruft Elemente einer Bibliothek oder Sammlung ab
   * 
   * @param {string} libraryType Bibliothekstyp ('user' oder 'group')
   * @param {string} libraryId Bibliotheks-ID
   * @param {string} collectionId Sammlungs-ID (optional)
   * @param {Object} options Abfrage-Optionen
   * @param {number} options.limit Maximale Anzahl von Elementen (Standard: 50)
   * @param {number} options.start Startindex (Standard: 0)
   * @param {string} options.sort Sortierfeld (Standard: 'dateAdded')
   * @param {string} options.direction Sortierrichtung ('asc' oder 'desc', Standard: 'desc')
   * @returns {Promise<Object>} Elemente der Bibliothek oder Sammlung
   */
  async getItems(libraryType, libraryId, collectionId = null, options = {}) {
    const {
      limit = 50,
      start = 0,
      sort = 'dateAdded',
      direction = 'desc'
    } = options;
    
    const endpoint = collectionId
      ? `/${libraryType}s/${libraryId}/collections/${collectionId}/items`
      : `/${libraryType}s/${libraryId}/items`;
    
    const response = await this.request(endpoint, {
      params: {
        limit,
        start,
        sort,
        direction,
        format: 'json'
      }
    });
    
    if (response.success) {
      this.stats.itemsRetrieved += response.data.length;
    }
    
    return response;
  }
  
  /**
   * Sucht nach Elementen mit DOIs in einer Bibliothek
   * 
   * @param {string} libraryType Bibliothekstyp ('user' oder 'group')
   * @param {string} libraryId Bibliotheks-ID
   * @param {Object} options Abfrage-Optionen (siehe getItems)
   * @returns {Promise<Object>} Elemente mit DOIs
   */
  async getItemsWithDOI(libraryType, libraryId, options = {}) {
    const items = await this.getItems(libraryType, libraryId, null, {
      ...options,
      limit: 100 // Höheres Limit für effizientere Suche
    });
    
    if (!items.success) {
      return items;
    }
    
    // Filtern nach Elementen mit DOI
    const itemsWithDOI = items.data.filter(item => {
      return item.data && item.data.DOI && item.data.DOI.trim() !== '';
    });
    
    return {
      success: true,
      data: itemsWithDOI,
      total: itemsWithDOI.length
    };
  }
  
  /**
   * Ruft ein einzelnes Element anhand seiner ID ab
   * 
   * @param {string} libraryType Bibliothekstyp ('user' oder 'group')
   * @param {string} libraryId Bibliotheks-ID
   * @param {string} itemKey Element-Schlüssel
   * @returns {Promise<Object>} Element-Details
   */
  async getItem(libraryType, libraryId, itemKey) {
    return this.request(`/${libraryType}s/${libraryId}/items/${itemKey}`);
  }
  
  /**
   * Ruft alle Elemente mit DOIs aus allen Bibliotheken des Benutzers ab
   * 
   * @returns {Promise<Object>} Alle Elemente mit DOIs
   */
  async getAllItemsWithDOI() {
    const libraries = await this.getLibraries();
    
    if (!libraries.success) {
      return libraries;
    }
    
    const allItems = [];
    let errors = [];
    
    // Persönliche Bibliothek
    const userInfo = await this.getUserInfo();
    if (userInfo.success) {
      const userId = userInfo.data.userID;
      const userItems = await this.getItemsWithDOI('user', userId);
      
      if (userItems.success) {
        allItems.push(...userItems.data);
      } else {
        errors.push(`Fehler beim Abrufen der persönlichen Bibliothek: ${userItems.error}`);
      }
    }
    
    // Gruppenbibliotheken
    if (libraries.data && libraries.data.length > 0) {
      for (const library of libraries.data) {
        const groupItems = await this.getItemsWithDOI('group', library.id);
        
        if (groupItems.success) {
          allItems.push(...groupItems.data);
        } else {
          errors.push(`Fehler beim Abrufen der Gruppe ${library.name}: ${groupItems.error}`);
        }
      }
    }
    
    return {
      success: true,
      data: allItems,
      total: allItems.length,
      errors: errors.length > 0 ? errors : null
    };
  }
  
  /**
   * Konvertiert Zotero-Elemente in ein Format, das für DeSci-Scholar geeignet ist
   * 
   * @param {Array} zoteroItems Zotero-Elemente
   * @returns {Array} Konvertierte Elemente im DeSci-Scholar-Format
   */
  convertToDeSciFormat(zoteroItems) {
    return zoteroItems.map(item => {
      const data = item.data;
      
      // Autoren extrahieren
      let authors = [];
      if (data.creators) {
        authors = data.creators.map(creator => {
          if (creator.firstName && creator.lastName) {
            return `${creator.lastName}, ${creator.firstName}`;
          } else if (creator.name) {
            return creator.name;
          }
          return '';
        }).filter(name => name !== '');
      }
      
      // Publikationsdatum extrahieren
      let publishedDate = null;
      if (data.date) {
        // Versuche, ein vollständiges Datum zu extrahieren
        const dateMatch = data.date.match(/\d{4}-\d{2}-\d{2}/);
        if (dateMatch) {
          publishedDate = dateMatch[0];
        } else {
          // Fallback auf Jahr
          const yearMatch = data.date.match(/\d{4}/);
          if (yearMatch) {
            publishedDate = `${yearMatch[0]}-01-01`; // Standardmäßig 1. Januar
          }
        }
      }
      
      // Konvertiertes Element im DeSci-Scholar-Format
      return {
        title: data.title || 'Unbekannter Titel',
        authors,
        abstract: data.abstractNote || '',
        doi: data.DOI || null,
        url: data.url || null,
        publishedDate,
        journal: data.publicationTitle || data.journalAbbreviation || null,
        volume: data.volume || null,
        issue: data.issue || null,
        pages: data.pages || null,
        publisher: data.publisher || null,
        itemType: data.itemType || null,
        tags: data.tags ? data.tags.map(tag => tag.tag) : [],
        zoteroData: {
          key: item.key,
          version: item.version,
          libraryType: item.library?.type,
          libraryId: item.library?.id
        }
      };
    });
  }
  
  /**
   * Gibt die aktuellen API-Statistiken zurück
   * 
   * @returns {Object} API-Statistiken
   */
  getStats() {
    return {
      ...this.stats,
      successRate: this.stats.totalRequests > 0
        ? (this.stats.successfulRequests / this.stats.totalRequests * 100).toFixed(2) + '%'
        : '0%'
    };
  }
}

export default ZoteroAPI;/**
 * @fileoverview Zotero API Integration für DeSci-Scholar
 * 
 * Diese Datei implementiert die Integration mit der Zotero API, um Zugriff auf
 * Literatursammlungen, Bibliotheken und Metadaten zu ermöglichen.
 * 
 * Zotero API Dokumentation: https://www.zotero.org/support/dev/web_api/v3/start
 */

import axios from 'axios';

/**
 * Klasse für die Integration mit der Zotero API
 */
export class ZoteroAPI {
  /**
   * Konstruktor für die Zotero API Integration
   * 
   * @param {Object} options Konfigurationsoptionen
   * @param {string} options.apiKey Zotero API-Schlüssel (optional)
   * @param {number} options.timeout Timeout für API-Anfragen in ms (Standard: 10000)
   * @param {number} options.maxRetries Maximale Anzahl von Wiederholungsversuchen (Standard: 3)
   */
  constructor(options = {}) {
    const {
      apiKey,
      timeout = 10000,
      maxRetries = 3
    } = options;
    
    this.apiKey = apiKey;
    this.baseUrl = 'https://api.zotero.org';
    this.timeout = timeout;
    this.maxRetries = maxRetries;
    
    // Statistiken für API-Aufrufe
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      itemsRetrieved: 0
    };
  }
  
  /**
   * Führt eine Anfrage an die Zotero API durch
   * 
   * @param {string} endpoint API-Endpunkt
   * @param {Object} options Anfrage-Optionen
   * @param {string} options.method HTTP-Methode (Standard: 'GET')
   * @param {Object} options.params Query-Parameter
   * @param {Object} options.data Request-Body für POST/PUT
   * @param {Object} options.headers Zusätzliche HTTP-Header
   * @returns {Promise<Object>} API-Antwort
   */
  async request(endpoint, options = {}) {
    const {
      method = 'GET',
      params = {},
      data = null,
      headers = {}
    } = options;
    
    // API-Schlüssel hinzufügen, falls vorhanden
    const requestHeaders = {
      'Content-Type': 'application/json',
      ...headers
    };
    
    if (this.apiKey) {
      requestHeaders['Zotero-API-Key'] = this.apiKey;
    }
    
    // Statistik aktualisieren
    this.stats.totalRequests++;
    
    // Anfrage mit Wiederholungslogik
    let lastError = null;
    for (let attempt = 0; attempt < this.maxRetries; attempt++) {
      try {
        // Bei Wiederholungsversuchen warten
        if (attempt > 0) {
          const delay = 1000 * Math.pow(2, attempt - 1);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
        
        // Anfrage ausführen
        const response = await axios({
          method,
          url: `${this.baseUrl}${endpoint}`,
          params,
          data,
          headers: requestHeaders,
          timeout: this.timeout
        });
        
        // Statistik aktualisieren
        this.stats.successfulRequests++;
        
        // Rückgabe der Antwort
        return {
          success: true,
          data: response.data,
          headers: response.headers,
          status: response.status
        };
      } catch (error) {
        lastError = error;
        
        // Prüfen, ob der Fehler wiederholbar ist
        const status = error.response?.status;
        const isRetryable = 
          error.code === 'ECONNRESET' || 
          error.code === 'ETIMEDOUT' ||
          status === 429 || // Too Many Requests
          status === 503 || // Service Unavailable
          status === 500;   // Internal Server Error
        
        if (!isRetryable) {
          break;
        }
      }
    }
    
    // Statistik aktualisieren
    this.stats.failedRequests++;
    
    // Fehlerbehandlung
    const errorResponse = lastError.response;
    return {
      success: false,
      error: lastError.message,
      status: errorResponse?.status,
      data: errorResponse?.data
    };
  }
  
  /**
   * Ruft Benutzerinformationen ab
   * 
   * @returns {Promise<Object>} Benutzerinformationen
   */
  async getUserInfo() {
    return this.request('/keys/current');
  }
  
  /**
   * Ruft Bibliotheken des Benutzers ab
   * 
   * @returns {Promise<Object>} Bibliotheken des Benutzers
   */
  async getLibraries() {
    const userInfo = await this.getUserInfo();
    
    if (!userInfo.success) {
      return userInfo;
    }
    
    const userId = userInfo.data.userID;
    const userLibrary = await this.request(`/users/${userId}/libraries`);
    
    return userLibrary;
  }
  
  /**
   * Ruft Sammlungen einer Bibliothek ab
   * 
   * @param {string} libraryType Bibliothekstyp ('user' oder 'group')
   * @param {string} libraryId Bibliotheks-ID
   * @returns {Promise<Object>} Sammlungen der Bibliothek
   */
  async getCollections(libraryType, libraryId) {
    return this.request(`/${libraryType}s/${libraryId}/collections`);
  }
  
  /**
   * Ruft Elemente einer Bibliothek oder Sammlung ab
   * 
   * @param {string} libraryType Bibliothekstyp ('user' oder 'group')
   * @param {string} libraryId Bibliotheks-ID
   * @param {string} collectionId Sammlungs-ID (optional)
   * @param {Object} options Abfrage-Optionen
   * @param {number} options.limit Maximale Anzahl von Elementen (Standard: 50)
   * @param {number} options.start Startindex (Standard: 0)
   * @param {string} options.sort Sortierfeld (Standard: 'dateAdded')
   * @param {string} options.direction Sortierrichtung ('asc' oder 'desc', Standard: 'desc')
   * @returns {Promise<Object>} Elemente der Bibliothek oder Sammlung
   */
  async getItems(libraryType, libraryId, collectionId = null, options = {}) {
    const {
      limit = 50,
      start = 0,
      sort = 'dateAdded',
      direction = 'desc'
    } = options;
    
    const endpoint = collectionId
      ? `/${libraryType}s/${libraryId}/collections/${collectionId}/items`
      : `/${libraryType}s/${libraryId}/items`;
    
    const response = await this.request(endpoint, {
      params: {
        limit,
        start,
        sort,
        direction,
        format: 'json'
      }
    });
    
    if (response.success) {
      this.stats.itemsRetrieved += response.data.length;
    }
    
    return response;
  }
  
  /**
   * Sucht nach Elementen mit DOIs in einer Bibliothek
   * 
   * @param {string} libraryType Bibliothekstyp ('user' oder 'group')
   * @param {string} libraryId Bibliotheks-ID
   * @param {Object} options Abfrage-Optionen (siehe getItems)
   * @returns {Promise<Object>} Elemente mit DOIs
   */
  async getItemsWithDOI(libraryType, libraryId, options = {}) {
    const items = await this.getItems(libraryType, libraryId, null, {
      ...options,
      limit: 100 // Höheres Limit für effizientere Suche
    });
    
    if (!items.success) {
      return items;
    }
    
    // Filtern nach Elementen mit DOI
    const itemsWithDOI = items.data.filter(item => {
      return item.data && item.data.DOI && item.data.DOI.trim() !== '';
    });
    
    return {
      success: true,
      data: itemsWithDOI,
      total: itemsWithDOI.length
    };
  }
  
  /**
   * Ruft ein einzelnes Element anhand seiner ID ab
   * 
   * @param {string} libraryType Bibliothekstyp ('user' oder 'group')
   * @param {string} libraryId Bibliotheks-ID
   * @param {string} itemKey Element-Schlüssel
   * @returns {Promise<Object>} Element-Details
   */
  async getItem(libraryType, libraryId, itemKey) {
    return this.request(`/${libraryType}s/${libraryId}/items/${itemKey}`);
  }
  
  /**
   * Ruft alle Elemente mit DOIs aus allen Bibliotheken des Benutzers ab
   * 
   * @returns {Promise<Object>} Alle Elemente mit DOIs
   */
  async getAllItemsWithDOI() {
    const libraries = await this.getLibraries();
    
    if (!libraries.success) {
      return libraries;
    }
    
    const allItems = [];
    let errors = [];
    
    // Persönliche Bibliothek
    const userInfo = await this.getUserInfo();
    if (userInfo.success) {
      const userId = userInfo.data.userID;
      const userItems = await this.getItemsWithDOI('user', userId);
      
      if (userItems.success) {
        allItems.push(...userItems.data);
      } else {
        errors.push(`Fehler beim Abrufen der persönlichen Bibliothek: ${userItems.error}`);
      }
    }
    
    // Gruppenbibliotheken
    if (libraries.data && libraries.data.length > 0) {
      for (const library of libraries.data) {
        const groupItems = await this.getItemsWithDOI('group', library.id);
        
        if (groupItems.success) {
          allItems.push(...groupItems.data);
        } else {
          errors.push(`Fehler beim Abrufen der Gruppe ${library.name}: ${groupItems.error}`);
        }
      }
    }
    
    return {
      success: true,
      data: allItems,
      total: allItems.length,
      errors: errors.length > 0 ? errors : null
    };
  }
  
  /**
   * Konvertiert Zotero-Elemente in ein Format, das für DeSci-Scholar geeignet ist
   * 
   * @param {Array} zoteroItems Zotero-Elemente
   * @returns {Array} Konvertierte Elemente im DeSci-Scholar-Format
   */
  convertToDeSciFormat(zoteroItems) {
    return zoteroItems.map(item => {
      const data = item.data;
      
      // Autoren extrahieren
      let authors = [];
      if (data.creators) {
        authors = data.creators.map(creator => {
          if (creator.firstName && creator.lastName) {
            return `${creator.lastName}, ${creator.firstName}`;
          } else if (creator.name) {
            return creator.name;
          }
          return '';
        }).filter(name => name !== '');
      }
      
      // Publikationsdatum extrahieren
      let publishedDate = null;
      if (data.date) {
        // Versuche, ein vollständiges Datum zu extrahieren
        const dateMatch = data.date.match(/\d{4}-\d{2}-\d{2}/);
        if (dateMatch) {
          publishedDate = dateMatch[0];
        } else {
          // Fallback auf Jahr
          const yearMatch = data.date.match(/\d{4}/);
          if (yearMatch) {
            publishedDate = `${yearMatch[0]}-01-01`; // Standardmäßig 1. Januar
          }
        }
      }
      
      // Konvertiertes Element im DeSci-Scholar-Format
      return {
        title: data.title || 'Unbekannter Titel',
        authors,
        abstract: data.abstractNote || '',
        doi: data.DOI || null,
        url: data.url || null,
        publishedDate,
        journal: data.publicationTitle || data.journalAbbreviation || null,
        volume: data.volume || null,
        issue: data.issue || null,
        pages: data.pages || null,
        publisher: data.publisher || null,
        itemType: data.itemType || null,
        tags: data.tags ? data.tags.map(tag => tag.tag) : [],
        zoteroData: {
          key: item.key,
          version: item.version,
          libraryType: item.library?.type,
          libraryId: item.library?.id
        }
      };
    });
  }
  
  /**
   * Gibt die aktuellen API-Statistiken zurück
   * 
   * @returns {Object} API-Statistiken
   */
  getStats() {
    return {
      ...this.stats,
      successRate: this.stats.totalRequests > 0
        ? (this.stats.successfulRequests / this.stats.totalRequests * 100).toFixed(2) + '%'
        : '0%'
    };
  }
}

export default ZoteroAPI;/**
 * @fileoverview Zotero API Integration für DeSci-Scholar
 * 
 * Diese Datei implementiert die Integration mit der Zotero API, um Zugriff auf
 * Literatursammlungen, Bibliotheken und Metadaten zu ermöglichen.
 * 
 * Zotero API Dokumentation: https://www.zotero.org/support/dev/web_api/v3/start
 */

import axios from 'axios';

/**
 * Klasse für die Integration mit der Zotero API
 */
export class ZoteroAPI {
  /**
   * Konstruktor für die Zotero API Integration
   * 
   * @param {Object} options Konfigurationsoptionen
   * @param {string} options.apiKey Zotero API-Schlüssel (optional)
   * @param {number} options.timeout Timeout für API-Anfragen in ms (Standard: 10000)
   * @param {number} options.maxRetries Maximale Anzahl von Wiederholungsversuchen (Standard: 3)
   */
  constructor(options = {}) {
    const {
      apiKey,
      timeout = 10000,
      maxRetries = 3
    } = options;
    
    this.apiKey = apiKey;
    this.baseUrl = 'https://api.zotero.org';
    this.timeout = timeout;
    this.maxRetries = maxRetries;
    
    // Statistiken für API-Aufrufe
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      itemsRetrieved: 0
    };
  }
  
  /**
   * Führt eine Anfrage an die Zotero API durch
   * 
   * @param {string} endpoint API-Endpunkt
   * @param {Object} options Anfrage-Optionen
   * @param {string} options.method HTTP-Methode (Standard: 'GET')
   * @param {Object} options.params Query-Parameter
   * @param {Object} options.data Request-Body für POST/PUT
   * @param {Object} options.headers Zusätzliche HTTP-Header
   * @returns {Promise<Object>} API-Antwort
   */
  async request(endpoint, options = {}) {
    const {
      method = 'GET',
      params = {},
      data = null,
      headers = {}
    } = options;
    
    // API-Schlüssel hinzufügen, falls vorhanden
    const requestHeaders = {
      'Content-Type': 'application/json',
      ...headers
    };
    
    if (this.apiKey) {
      requestHeaders['Zotero-API-Key'] = this.apiKey;
    }
    
    // Statistik aktualisieren
    this.stats.totalRequests++;
    
    // Anfrage mit Wiederholungslogik
    let lastError = null;
    for (let attempt = 0; attempt < this.maxRetries; attempt++) {
      try {
        // Bei Wiederholungsversuchen warten
        if (attempt > 0) {
          const delay = 1000 * Math.pow(2, attempt - 1);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
        
        // Anfrage ausführen
        const response = await axios({
          method,
          url: `${this.baseUrl}${endpoint}`,
          params,
          data,
          headers: requestHeaders,
          timeout: this.timeout
        });
        
        // Statistik aktualisieren
        this.stats.successfulRequests++;
        
        // Rückgabe der Antwort
        return {
          success: true,
          data: response.data,
          headers: response.headers,
          status: response.status
        };
      } catch (error) {
        lastError = error;
        
        // Prüfen, ob der Fehler wiederholbar ist
        const status = error.response?.status;
        const isRetryable = 
          error.code === 'ECONNRESET' || 
          error.code === 'ETIMEDOUT' ||
          status === 429 || // Too Many Requests
          status === 503 || // Service Unavailable
          status === 500;   // Internal Server Error
        
        if (!isRetryable) {
          break;
        }
      }
    }
    
    // Statistik aktualisieren
    this.stats.failedRequests++;
    
    // Fehlerbehandlung
    const errorResponse = lastError.response;
    return {
      success: false,
      error: lastError.message,
      status: errorResponse?.status,
      data: errorResponse?.data
    };
  }
  
  /**
   * Ruft Benutzerinformationen ab
   * 
   * @returns {Promise<Object>} Benutzerinformationen
   */
  async getUserInfo() {
    return this.request('/keys/current');
  }
  
  /**
   * Ruft Bibliotheken des Benutzers ab
   * 
   * @returns {Promise<Object>} Bibliotheken des Benutzers
   */
  async getLibraries() {
    const userInfo = await this.getUserInfo();
    
    if (!userInfo.success) {
      return userInfo;
    }
    
    const userId = userInfo.data.userID;
    const userLibrary = await this.request(`/users/${userId}/libraries`);
    
    return userLibrary;
  }
  
  /**
   * Ruft Sammlungen einer Bibliothek ab
   * 
   * @param {string} libraryType Bibliothekstyp ('user' oder 'group')
   * @param {string} libraryId Bibliotheks-ID
   * @returns {Promise<Object>} Sammlungen der Bibliothek
   */
  async getCollections(libraryType, libraryId) {
    return this.request(`/${libraryType}s/${libraryId}/collections`);
  }
  
  /**
   * Ruft Elemente einer Bibliothek oder Sammlung ab
   * 
   * @param {string} libraryType Bibliothekstyp ('user' oder 'group')
   * @param {string} libraryId Bibliotheks-ID
   * @param {string} collectionId Sammlungs-ID (optional)
   * @param {Object} options Abfrage-Optionen
   * @param {number} options.limit Maximale Anzahl von Elementen (Standard: 50)
   * @param {number} options.start Startindex (Standard: 0)
   * @param {string} options.sort Sortierfeld (Standard: 'dateAdded')
   * @param {string} options.direction Sortierrichtung ('asc' oder 'desc', Standard: 'desc')
   * @returns {Promise<Object>} Elemente der Bibliothek oder Sammlung
   */
  async getItems(libraryType, libraryId, collectionId = null, options = {}) {
    const {
      limit = 50,
      start = 0,
      sort = 'dateAdded',
      direction = 'desc'
    } = options;
    
    const endpoint = collectionId
      ? `/${libraryType}s/${libraryId}/collections/${collectionId}/items`
      : `/${libraryType}s/${libraryId}/items`;
    
    const response = await this.request(endpoint, {
      params: {
        limit,
        start,
        sort,
        direction,
        format: 'json'
      }
    });
    
    if (response.success) {
      this.stats.itemsRetrieved += response.data.length;
    }
    
    return response;
  }
  
  /**
   * Sucht nach Elementen mit DOIs in einer Bibliothek
   * 
   * @param {string} libraryType Bibliothekstyp ('user' oder 'group')
   * @param {string} libraryId Bibliotheks-ID
   * @param {Object} options Abfrage-Optionen (siehe getItems)
   * @returns {Promise<Object>} Elemente mit DOIs
   */
  async getItemsWithDOI(libraryType, libraryId, options = {}) {
    const items = await this.getItems(libraryType, libraryId, null, {
      ...options,
      limit: 100 // Höheres Limit für effizientere Suche
    });
    
    if (!items.success) {
      return items;
    }
    
    // Filtern nach Elementen mit DOI
    const itemsWithDOI = items.data.filter(item => {
      return item.data && item.data.DOI && item.data.DOI.trim() !== '';
    });
    
    return {
      success: true,
      data: itemsWithDOI,
      total: itemsWithDOI.length
    };
  }
  
  /**
   * Ruft ein einzelnes Element anhand seiner ID ab
   * 
   * @param {string} libraryType Bibliothekstyp ('user' oder 'group')
   * @param {string} libraryId Bibliotheks-ID
   * @param {string} itemKey Element-Schlüssel
   * @returns {Promise<Object>} Element-Details
   */
  async getItem(libraryType, libraryId, itemKey) {
    return this.request(`/${libraryType}s/${libraryId}/items/${itemKey}`);
  }
  
  /**
   * Ruft alle Elemente mit DOIs aus allen Bibliotheken des Benutzers ab
   * 
   * @returns {Promise<Object>} Alle Elemente mit DOIs
   */
  async getAllItemsWithDOI() {
    const libraries = await this.getLibraries();
    
    if (!libraries.success) {
      return libraries;
    }
    
    const allItems = [];
    let errors = [];
    
    // Persönliche Bibliothek
    const userInfo = await this.getUserInfo();
    if (userInfo.success) {
      const userId = userInfo.data.userID;
      const userItems = await this.getItemsWithDOI('user', userId);
      
      if (userItems.success) {
        allItems.push(...userItems.data);
      } else {
        errors.push(`Fehler beim Abrufen der persönlichen Bibliothek: ${userItems.error}`);
      }
    }
    
    // Gruppenbibliotheken
    if (libraries.data && libraries.data.length > 0) {
      for (const library of libraries.data) {
        const groupItems = await this.getItemsWithDOI('group', library.id);
        
        if (groupItems.success) {
          allItems.push(...groupItems.data);
        } else {
          errors.push(`Fehler beim Abrufen der Gruppe ${library.name}: ${groupItems.error}`);
        }
      }
    }
    
    return {
      success: true,
      data: allItems,
      total: allItems.length,
      errors: errors.length > 0 ? errors : null
    };
  }
  
  /**
   * Konvertiert Zotero-Elemente in ein Format, das für DeSci-Scholar geeignet ist
   * 
   * @param {Array} zoteroItems Zotero-Elemente
   * @returns {Array} Konvertierte Elemente im DeSci-Scholar-Format
   */
  convertToDeSciFormat(zoteroItems) {
    return zoteroItems.map(item => {
      const data = item.data;
      
      // Autoren extrahieren
      let authors = [];
      if (data.creators) {
        authors = data.creators.map(creator => {
          if (creator.firstName && creator.lastName) {
            return `${creator.lastName}, ${creator.firstName}`;
          } else if (creator.name) {
            return creator.name;
          }
          return '';
        }).filter(name => name !== '');
      }
      
      // Publikationsdatum extrahieren
      let publishedDate = null;
      if (data.date) {
        // Versuche, ein vollständiges Datum zu extrahieren
        const dateMatch = data.date.match(/\d{4}-\d{2}-\d{2}/);
        if (dateMatch) {
          publishedDate = dateMatch[0];
        } else {
          // Fallback auf Jahr
          const yearMatch = data.date.match(/\d{4}/);
          if (yearMatch) {
            publishedDate = `${yearMatch[0]}-01-01`; // Standardmäßig 1. Januar
          }
        }
      }
      
      // Konvertiertes Element im DeSci-Scholar-Format
      return {
        title: data.title || 'Unbekannter Titel',
        authors,
        abstract: data.abstractNote || '',
        doi: data.DOI || null,
        url: data.url || null,
        publishedDate,
        journal: data.publicationTitle || data.journalAbbreviation || null,
        volume: data.volume || null,
        issue: data.issue || null,
        pages: data.pages || null,
        publisher: data.publisher || null,
        itemType: data.itemType || null,
        tags: data.tags ? data.tags.map(tag => tag.tag) : [],
        zoteroData: {
          key: item.key,
          version: item.version,
          libraryType: item.library?.type,
          libraryId: item.library?.id
        }
      };
    });
  }
  
  /**
   * Gibt die aktuellen API-Statistiken zurück
   * 
   * @returns {Object} API-Statistiken
   */
  getStats() {
    return {
      ...this.stats,
      successRate: this.stats.totalRequests > 0
        ? (this.stats.successfulRequests / this.stats.totalRequests * 100).toFixed(2) + '%'
        : '0%'
    };
  }
}

export default ZoteroAPI;/**
 * @fileoverview Zotero API Integration für DeSci-Scholar
 * 
 * Diese Datei implementiert die Integration mit der Zotero API, um Zugriff auf
 * Literatursammlungen, Bibliotheken und Metadaten zu ermöglichen.
 * 
 * Zotero API Dokumentation: https://www.zotero.org/support/dev/web_api/v3/start
 */

import axios from 'axios';

/**
 * Klasse für die Integration mit der Zotero API
 */
export class ZoteroAPI {
  /**
   * Konstruktor für die Zotero API Integration
   * 
   * @param {Object} options Konfigurationsoptionen
   * @param {string} options.apiKey Zotero API-Schlüssel (optional)
   * @param {number} options.timeout Timeout für API-Anfragen in ms (Standard: 10000)
   * @param {number} options.maxRetries Maximale Anzahl von Wiederholungsversuchen (Standard: 3)
   */
  constructor(options = {}) {
    const {
      apiKey,
      timeout = 10000,
      maxRetries = 3
    } = options;
    
    this.apiKey = apiKey;
    this.baseUrl = 'https://api.zotero.org';
    this.timeout = timeout;
    this.maxRetries = maxRetries;
    
    // Statistiken für API-Aufrufe
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      itemsRetrieved: 0
    };
  }
  
  /**
   * Führt eine Anfrage an die Zotero API durch
   * 
   * @param {string} endpoint API-Endpunkt
   * @param {Object} options Anfrage-Optionen
   * @param {string} options.method HTTP-Methode (Standard: 'GET')
   * @param {Object} options.params Query-Parameter
   * @param {Object} options.data Request-Body für POST/PUT
   * @param {Object} options.headers Zusätzliche HTTP-Header
   * @returns {Promise<Object>} API-Antwort
   */
  async request(endpoint, options = {}) {
    const {
      method = 'GET',
      params = {},
      data = null,
      headers = {}
    } = options;
    
    // API-Schlüssel hinzufügen, falls vorhanden
    const requestHeaders = {
      'Content-Type': 'application/json',
      ...headers
    };
    
    if (this.apiKey) {
      requestHeaders['Zotero-API-Key'] = this.apiKey;
    }
    
    // Statistik aktualisieren
    this.stats.totalRequests++;
    
    // Anfrage mit Wiederholungslogik
    let lastError = null;
    for (let attempt = 0; attempt < this.maxRetries; attempt++) {
      try {
        // Bei Wiederholungsversuchen warten
        if (attempt > 0) {
          const delay = 1000 * Math.pow(2, attempt - 1);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
        
        // Anfrage ausführen
        const response = await axios({
          method,
          url: `${this.baseUrl}${endpoint}`,
          params,
          data,
          headers: requestHeaders,
          timeout: this.timeout
        });
        
        // Statistik aktualisieren
        this.stats.successfulRequests++;
        
        // Rückgabe der Antwort
        return {
          success: true,
          data: response.data,
          headers: response.headers,
          status: response.status
        };
      } catch (error) {
        lastError = error;
        
        // Prüfen, ob der Fehler wiederholbar ist
        const status = error.response?.status;
        const isRetryable = 
          error.code === 'ECONNRESET' || 
          error.code === 'ETIMEDOUT' ||
          status === 429 || // Too Many Requests
          status === 503 || // Service Unavailable
          status === 500;   // Internal Server Error
        
        if (!isRetryable) {
          break;
        }
      }
    }
    
    // Statistik aktualisieren
    this.stats.failedRequests++;
    
    // Fehlerbehandlung
    const errorResponse = lastError.response;
    return {
      success: false,
      error: lastError.message,
      status: errorResponse?.status,
      data: errorResponse?.data
    };
  }
  
  /**
   * Ruft Benutzerinformationen ab
   * 
   * @returns {Promise<Object>} Benutzerinformationen
   */
  async getUserInfo() {
    return this.request('/keys/current');
  }
  
  /**
   * Ruft Bibliotheken des Benutzers ab
   * 
   * @returns {Promise<Object>} Bibliotheken des Benutzers
   */
  async getLibraries() {
    const userInfo = await this.getUserInfo();
    
    if (!userInfo.success) {
      return userInfo;
    }
    
    const userId = userInfo.data.userID;
    const userLibrary = await this.request(`/users/${userId}/libraries`);
    
    return userLibrary;
  }
  
  /**
   * Ruft Sammlungen einer Bibliothek ab
   * 
   * @param {string} libraryType Bibliothekstyp ('user' oder 'group')
   * @param {string} libraryId Bibliotheks-ID
   * @returns {Promise<Object>} Sammlungen der Bibliothek
   */
  async getCollections(libraryType, libraryId) {
    return this.request(`/${libraryType}s/${libraryId}/collections`);
  }
  
  /**
   * Ruft Elemente einer Bibliothek oder Sammlung ab
   * 
   * @param {string} libraryType Bibliothekstyp ('user' oder 'group')
   * @param {string} libraryId Bibliotheks-ID
   * @param {string} collectionId Sammlungs-ID (optional)
   * @param {Object} options Abfrage-Optionen
   * @param {number} options.limit Maximale Anzahl von Elementen (Standard: 50)
   * @param {number} options.start Startindex (Standard: 0)
   * @param {string} options.sort Sortierfeld (Standard: 'dateAdded')
   * @param {string} options.direction Sortierrichtung ('asc' oder 'desc', Standard: 'desc')
   * @returns {Promise<Object>} Elemente der Bibliothek oder Sammlung
   */
  async getItems(libraryType, libraryId, collectionId = null, options = {}) {
    const {
      limit = 50,
      start = 0,
      sort = 'dateAdded',
      direction = 'desc'
    } = options;
    
    const endpoint = collectionId
      ? `/${libraryType}s/${libraryId}/collections/${collectionId}/items`
      : `/${libraryType}s/${libraryId}/items`;
    
    const response = await this.request(endpoint, {
      params: {
        limit,
        start,
        sort,
        direction,
        format: 'json'
      }
    });
    
    if (response.success) {
      this.stats.itemsRetrieved += response.data.length;
    }
    
    return response;
  }
  
  /**
   * Sucht nach Elementen mit DOIs in einer Bibliothek
   * 
   * @param {string} libraryType Bibliothekstyp ('user' oder 'group')
   * @param {string} libraryId Bibliotheks-ID
   * @param {Object} options Abfrage-Optionen (siehe getItems)
   * @returns {Promise<Object>} Elemente mit DOIs
   */
  async getItemsWithDOI(libraryType, libraryId, options = {}) {
    const items = await this.getItems(libraryType, libraryId, null, {
      ...options,
      limit: 100 // Höheres Limit für effizientere Suche
    });
    
    if (!items.success) {
      return items;
    }
    
    // Filtern nach Elementen mit DOI
    const itemsWithDOI = items.data.filter(item => {
      return item.data && item.data.DOI && item.data.DOI.trim() !== '';
    });
    
    return {
      success: true,
      data: itemsWithDOI,
      total: itemsWithDOI.length
    };
  }
  
  /**
   * Ruft ein einzelnes Element anhand seiner ID ab
   * 
   * @param {string} libraryType Bibliothekstyp ('user' oder 'group')
   * @param {string} libraryId Bibliotheks-ID
   * @param {string} itemKey Element-Schlüssel
   * @returns {Promise<Object>} Element-Details
   */
  async getItem(libraryType, libraryId, itemKey) {
    return this.request(`/${libraryType}s/${libraryId}/items/${itemKey}`);
  }
  
  /**
   * Ruft alle Elemente mit DOIs aus allen Bibliotheken des Benutzers ab
   * 
   * @returns {Promise<Object>} Alle Elemente mit DOIs
   */
  async getAllItemsWithDOI() {
    const libraries = await this.getLibraries();
    
    if (!libraries.success) {
      return libraries;
    }
    
    const allItems = [];
    let errors = [];
    
    // Persönliche Bibliothek
    const userInfo = await this.getUserInfo();
    if (userInfo.success) {
      const userId = userInfo.data.userID;
      const userItems = await this.getItemsWithDOI('user', userId);
      
      if (userItems.success) {
        allItems.push(...userItems.data);
      } else {
        errors.push(`Fehler beim Abrufen der persönlichen Bibliothek: ${userItems.error}`);
      }
    }
    
    // Gruppenbibliotheken
    if (libraries.data && libraries.data.length > 0) {
      for (const library of libraries.data) {
        const groupItems = await this.getItemsWithDOI('group', library.id);
        
        if (groupItems.success) {
          allItems.push(...groupItems.data);
        } else {
          errors.push(`Fehler beim Abrufen der Gruppe ${library.name}: ${groupItems.error}`);
        }
      }
    }
    
    return {
      success: true,
      data: allItems,
      total: allItems.length,
      errors: errors.length > 0 ? errors : null
    };
  }
  
  /**
   * Konvertiert Zotero-Elemente in ein Format, das für DeSci-Scholar geeignet ist
   * 
   * @param {Array} zoteroItems Zotero-Elemente
   * @returns {Array} Konvertierte Elemente im DeSci-Scholar-Format
   */
  convertToDeSciFormat(zoteroItems) {
    return zoteroItems.map(item => {
      const data = item.data;
      
      // Autoren extrahieren
      let authors = [];
      if (data.creators) {
        authors = data.creators.map(creator => {
          if (creator.firstName && creator.lastName) {
            return `${creator.lastName}, ${creator.firstName}`;
          } else if (creator.name) {
            return creator.name;
          }
          return '';
        }).filter(name => name !== '');
      }
      
      // Publikationsdatum extrahieren
      let publishedDate = null;
      if (data.date) {
        // Versuche, ein vollständiges Datum zu extrahieren
        const dateMatch = data.date.match(/\d{4}-\d{2}-\d{2}/);
        if (dateMatch) {
          publishedDate = dateMatch[0];
        } else {
          // Fallback auf Jahr
          const yearMatch = data.date.match(/\d{4}/);
          if (yearMatch) {
            publishedDate = `${yearMatch[0]}-01-01`; // Standardmäßig 1. Januar
          }
        }
      }
      
      // Konvertiertes Element im DeSci-Scholar-Format
      return {
        title: data.title || 'Unbekannter Titel',
        authors,
        abstract: data.abstractNote || '',
        doi: data.DOI || null,
        url: data.url || null,
        publishedDate,
        journal: data.publicationTitle || data.journalAbbreviation || null,
        volume: data.volume || null,
        issue: data.issue || null,
        pages: data.pages || null,
        publisher: data.publisher || null,
        itemType: data.itemType || null,
        tags: data.tags ? data.tags.map(tag => tag.tag) : [],
        zoteroData: {
          key: item.key,
          version: item.version,
          libraryType: item.library?.type,
          libraryId: item.library?.id
        }
      };
    });
  }
  
  /**
   * Gibt die aktuellen API-Statistiken zurück
   * 
   * @returns {Object} API-Statistiken
   */
  getStats() {
    return {
      ...this.stats,
      successRate: this.stats.totalRequests > 0
        ? (this.stats.successfulRequests / this.stats.totalRequests * 100).toFixed(2) + '%'
        : '0%'
    };
  }
}

export default ZoteroAPI;