/**
 * Crossref API Integration für Zitationsverfolgung und Publikationsverifikation
 */

/**
 * Crossref API Client
 */
class CrossrefAPI {
  /**
   * Konstruktor für den Crossref API-Client
   * @param {Object} config Konfigurationsobjekt
   * @param {string} config.apiUrl Crossref API-URL (Standard: https://api.crossref.org)
   * @param {string} config.mailto E-Mail-Adresse für bessere API-Rate-Limits
   * @param {string} config.token API-Token für registrierte Nutzer (optional)
   */
  constructor(config = {}) {
    this.config = {
      apiUrl: 'https://api.crossref.org',
      mailto: '',
      token: '',
      ...config
    };
  }
  
  /**
   * Führt einen API-Aufruf an Crossref durch
   * @param {string} endpoint API-Endpunkt
   * @param {Object} params Query-Parameter
   * @returns {Promise<Object>} API-Antwort
   * @private
   */
  async callApi(endpoint, params = {}) {
    const queryParams = new URLSearchParams(params);
    
    // Füge die E-Mail-Adresse hinzu, um bessere API-Rate-Limits zu erhalten
    if (this.config.mailto) {
      queryParams.append('mailto', this.config.mailto);
    }
    
    // Füge den User-Agent hinzu
    queryParams.append('ua', 'DeSci-Scholar/1.0');
    
    const url = `${this.config.apiUrl}/${endpoint}?${queryParams.toString()}`;
    const headers = {
      'Accept': 'application/json'
    };
    
    // Füge den API-Token hinzu, falls vorhanden
    if (this.config.token) {
      headers['Crossref-Plus-API-Token'] = `Bearer ${this.config.token}`;
    }
    
    try {
      const response = await fetch(url, { headers });
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Crossref API-Fehler: ${response.status} ${response.statusText} - ${errorText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Crossref API-Aufruffehler:', error);
      throw error;
    }
  }
  
  /**
   * Sucht nach Publikationen basierend auf Suchkriterien
   * @param {Object} query Suchparameter
   * @param {string} query.query Allgemeiner Suchbegriff
   * @param {string} query.title Publikationstitel
   * @param {string} query.author Autor
   * @param {string} query.doi DOI
   * @param {number} query.rows Anzahl der Ergebnisse pro Seite (Standard: 20)
   * @param {number} query.offset Ergebnisoffset für Paginierung
   * @returns {Promise<Object>} Suchergebnisse
   */
  async searchWorks(query = {}) {
    const params = {};
    
    // Füge Suchparameter hinzu
    if (query.query) params.query = query.query;
    if (query.title) params['query.title'] = query.title;
    if (query.author) params['query.author'] = query.author;
    if (query.doi) params['query.doi'] = query.doi;
    if (query.rows) params.rows = query.rows;
    if (query.offset) params.offset = query.offset;
    
    try {
      const result = await this.callApi('works', params);
      
      return {
        success: true,
        totalResults: result.message['total-results'],
        items: result.message.items,
        nextCursor: result.message['next-cursor'],
        facets: result.message.facets
      };
    } catch (error) {
      console.error('Crossref-Suchfehler:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * Ruft Metadaten für einen bestimmten DOI ab
   * @param {string} doi DOI
   * @returns {Promise<Object>} Publikationsmetadaten
   */
  async getWorkByDOI(doi) {
    if (!doi) {
      throw new Error('DOI ist erforderlich');
    }
    
    try {
      const result = await this.callApi(`works/${encodeURIComponent(doi)}`);
      
      return {
        success: true,
        doi,
        metadata: result.message
      };
    } catch (error) {
      console.error('Crossref DOI-Abruffehler:', error);
      return {
        success: false,
        error: error.message,
        doi
      };
    }
  }
  
  /**
   * Ruft Zitationen für einen bestimmten DOI ab
   * @param {string} doi DOI
   * @param {Object} options Optionale Parameter
   * @param {number} options.rows Anzahl der Ergebnisse pro Seite
   * @param {number} options.offset Ergebnisoffset für Paginierung
   * @returns {Promise<Object>} Zitationsdaten
   */
  async getCitationsByDOI(doi, options = {}) {
    if (!doi) {
      throw new Error('DOI ist erforderlich');
    }
    
    const params = {
      ...options
    };
    
    try {
      // Rufe Zitationen über den API-Endpunkt für zitierte Werke ab
      // Hinweis: In der aktuellen Crossref-API könnte dies eingeschränkt sein
      const result = await this.callApi(`works/${encodeURIComponent(doi)}/citations`, params);
      
      return {
        success: true,
        doi,
        citations: result.message.items,
        totalCitations: result.message['total-results']
      };
    } catch (error) {
      console.error('Crossref Zitationsabruffehler:', error);
      
      // Fallback: Suche nach Werken, die diesen DOI zitieren
      // Dies ist ein alternativer Ansatz, falls der direkte Endpunkt nicht verfügbar ist
      try {
        const searchResult = await this.searchWorks({
          'filter': `references:${doi}`,
          'rows': options.rows || 20,
          'offset': options.offset || 0
        });
        
        return {
          success: true,
          doi,
          citations: searchResult.items,
          totalCitations: searchResult.totalResults,
          note: 'Verwendet Suchergebnisse als Fallback für Zitationen'
        };
      } catch (fallbackError) {
        console.error('Crossref Zitationssuche-Fallback-Fehler:', fallbackError);
        return {
          success: false,
          error: `${error.message}. Fallback-Fehler: ${fallbackError.message}`,
          doi
        };
      }
    }
  }
  
  /**
   * Ruft Mitgliedsinformationen für einen Verlag oder eine Organisation ab
   * @param {string} memberId Crossref-Mitglieds-ID
   * @returns {Promise<Object>} Mitgliedsinformationen
   */
  async getMemberInfo(memberId) {
    if (!memberId) {
      throw new Error('Mitglieds-ID ist erforderlich');
    }
    
    try {
      const result = await this.callApi(`members/${encodeURIComponent(memberId)}`);
      
      return {
        success: true,
        memberId,
        info: result.message
      };
    } catch (error) {
      console.error('Crossref Mitgliedsinfo-Abruffehler:', error);
      return {
        success: false,
        error: error.message,
        memberId
      };
    }
  }
  
  /**
   * Konvertiert ein Datum in das Crossref-Datumsformat
   * @param {Date|string|number} date Datum
   * @returns {Object} Formatiertes Datum für Crossref-API
   * @private
   */
  formatDateForCrossref(date) {
    const dateObj = new Date(date);
    
    if (isNaN(dateObj.getTime())) {
      return null;
    }
    
    return {
      'date-parts': [
        [dateObj.getFullYear(), dateObj.getMonth() + 1, dateObj.getDate()]
      ]
    };
  }
  
  /**
   * Sucht nach Publikationen innerhalb eines Zeitraums
   * @param {Object} options Suchoptionen
   * @param {Date|string|number} options.from Startdatum
   * @param {Date|string|number} options.until Enddatum
   * @param {string} options.type Publikationstyp (z.B. 'journal-article', 'book', 'dataset')
   * @param {number} options.rows Anzahl der Ergebnisse pro Seite
   * @returns {Promise<Object>} Suchergebnisse
   */
  async searchByTimespan(options = {}) {
    const params = {};
    
    // Formatiere Datumswerte
    if (options.from) {
      const fromDate = this.formatDateForCrossref(options.from);
      if (fromDate) {
        params.from = `${fromDate['date-parts'][0][0]}-${fromDate['date-parts'][0][1].toString().padStart(2, '0')}-${fromDate['date-parts'][0][2].toString().padStart(2, '0')}`;
      }
    }
    
    if (options.until) {
      const untilDate = this.formatDateForCrossref(options.until);
      if (untilDate) {
        params.until = `${untilDate['date-parts'][0][0]}-${untilDate['date-parts'][0][1].toString().padStart(2, '0')}-${untilDate['date-parts'][0][2].toString().padStart(2, '0')}`;
      }
    }
    
    // Füge Publikationstyp hinzu
    if (options.type) {
      params.filter = `type:${options.type}`;
    }
    
    // Füge Paginierungsparameter hinzu
    if (options.rows) params.rows = options.rows;
    if (options.offset) params.offset = options.offset;
    
    // Füge Sortierung hinzu (standardmäßig nach Relevanz)
    params.sort = options.sort || 'relevance';
    
    try {
      const result = await this.callApi('works', params);
      
      return {
        success: true,
        totalResults: result.message['total-results'],
        items: result.message.items,
        facets: result.message.facets
      };
    } catch (error) {
      console.error('Crossref Zeitraum-Suchfehler:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * Überprüft, ob ein DOI bereits existiert
   * @param {string} doi DOI
   * @returns {Promise<boolean>} True, wenn der DOI existiert
   */
  async checkDOIExists(doi) {
    if (!doi) {
      throw new Error('DOI ist erforderlich');
    }
    
    try {
      await this.getWorkByDOI(doi);
      return true;
    } catch (error) {
      // Wenn der API-Aufruf mit 404 fehlschlägt, existiert der DOI nicht
      return false;
    }
  }
  
  /**
   * Ruft Informationen über Zeitschriften basierend auf ISSN ab
   * @param {string} issn ISSN der Zeitschrift
   * @returns {Promise<Object>} Zeitschrifteninformationen
   */
  async getJournalByISSN(issn) {
    if (!issn) {
      throw new Error('ISSN ist erforderlich');
    }
    
    try {
      const result = await this.callApi(`journals/${encodeURIComponent(issn)}`);
      
      return {
        success: true,
        issn,
        journal: result.message
      };
    } catch (error) {
      console.error('Crossref ISSN-Abruffehler:', error);
      return {
        success: false,
        error: error.message,
        issn
      };
    }
  }
  
  /**
   * Validiert und formatiert eine Publikation gemäß Crossref-Standards
   * @param {Object} publication Publikationsdaten
   * @returns {Object} Validierungsergebnis
   */
  validatePublication(publication) {
    const requiredFields = ['title', 'authors', 'type', 'year'];
    const missingFields = requiredFields.filter(field => !publication[field]);
    
    if (missingFields.length > 0) {
      return {
        valid: false,
        missingFields,
        message: `Fehlende Pflichtfelder: ${missingFields.join(', ')}`
      };
    }
    
    // Formatiere Autoren
    const formattedAuthors = publication.authors.map(author => {
      if (typeof author === 'string') {
        // Einfache Namensaufteilung
        const nameParts = author.split(' ');
        const lastName = nameParts.pop();
        const firstNames = nameParts.join(' ');
        
        return {
          family: lastName,
          given: firstNames
        };
      } else if (typeof author === 'object') {
        // Bereits formatierter Autor
        return {
          family: author.lastName || author.family || '',
          given: author.firstName || author.given || ''
        };
      }
      
      return null;
    }).filter(Boolean);
    
    return {
      valid: true,
      formattedPublication: {
        title: publication.title,
        author: formattedAuthors,
        type: publication.type,
        issued: {
          'date-parts': [[publication.year, publication.month || 1, publication.day || 1]]
        },
        'container-title': publication.journal || '',
        volume: publication.volume || '',
        issue: publication.issue || '',
        page: publication.pages || '',
        DOI: publication.doi || '',
        ISSN: publication.issn || '',
        URL: publication.url || ''
      }
    };
  }
}

export default CrossrefAPI; 