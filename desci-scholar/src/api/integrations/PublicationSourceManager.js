/**
 * @fileoverview Zentraler Manager für alle Publikationsquellen
 * 
 * Diese Klasse konsolidiert den Zugriff auf verschiedene Publikationsquellen
 * (arXiv, Zotero, etc.) und bietet eine einheitliche Schnittstelle.
 */

import { ArXivAPI } from './ArXivAPI.js';
import { ZoteroAPI } from './ZoteroAPI.js';
import { LoggerFactory } from '../../blockchain/polkadot-improved/LoggerFactory.js';

const logger = LoggerFactory.createLogger('PublicationSourceManager');

class PublicationSourceManager {
  constructor(config = {}) {
    this.sources = {};
    this.initialized = false;
    this.config = config;
    
    // Registriere Standardquellen
    this.registerSource('arxiv', new ArXivAPI(config.arxiv));
    if (config.zotero) {
      this.registerSource('zotero', new ZoteroAPI(config.zotero));
    }
  }
  
  /**
   * Registriert eine neue Publikationsquelle
   * @param {string} name - Name der Quelle
   * @param {Object} source - API-Instanz
   */
  registerSource(name, source) {
    this.sources[name] = source;
    logger.info(`Publikationsquelle registriert: ${name}`);
  }
  
  /**
   * Initialisiert alle registrierten Quellen
   */
  async initialize() {
    if (this.initialized) return true;
    
    try {
      const initPromises = Object.entries(this.sources).map(async ([name, source]) => {
        try {
          await source.initialize();
          logger.info(`Quelle initialisiert: ${name}`);
          return true;
        } catch (error) {
          logger.error(`Fehler bei der Initialisierung von ${name}`, {
            error: error.message
          });
          return false;
        }
      });
      
      const results = await Promise.all(initPromises);
      this.initialized = results.some(result => result);
      
      return this.initialized;
    } catch (error) {
      logger.error('Fehler bei der Initialisierung der Publikationsquellen', {
        error: error.message
      });
      return false;
    }
  }
  
  /**
   * Sucht nach Publikationen über alle Quellen hinweg
   * @param {Object} params - Suchparameter
   * @param {Array} sources - Zu durchsuchende Quellen (optional, Standard: alle)
   */
  async searchPublications(params, sources = Object.keys(this.sources)) {
    if (!this.initialized) await this.initialize();
    
    const results = {};
    const searchPromises = sources.map(async (sourceName) => {
      if (!this.sources[sourceName]) {
        logger.warn(`Quelle nicht gefunden: ${sourceName}`);
        return;
      }
      
      try {
        const sourceResults = await this.sources[sourceName].searchPapers(params);
        results[sourceName] = sourceResults;
      } catch (error) {
        logger.error(`Fehler bei der Suche in ${sourceName}`, {
          error: error.message,
          params
        });
        results[sourceName] = { error: error.message };
      }
    });
    
    await Promise.all(searchPromises);
    return results;
  }
  
  /**
   * Holt eine Publikation anhand ihrer ID
   * @param {string} source - Quellname (arxiv, zotero, etc.)
   * @param {string} id - Publikations-ID
   */
  async getPublication(source, id) {
    if (!this.initialized) await this.initialize();
    
    if (!this.sources[source]) {
      throw new Error(`Quelle nicht gefunden: ${source}`);
    }
    
    try {
      return await this.sources[source].getPaperById(id);
    } catch (error) {
      logger.error(`Fehler beim Abrufen der Publikation`, {
        source,
        id,
        error: error.message
      });
      throw error;
    }
  }
}

export default PublicationSourceManager;
