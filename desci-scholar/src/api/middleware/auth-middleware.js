/**
 * Authentifizierungs-Middleware für DeSci-Scholar
 * 
 * Bietet wiederverwendbare Middleware-Funktionen für die Authentifizierung und
 * Autorisierung von API-Anfragen.
 */

/**
 * Erstellt Middleware-Funktionen für Authentifizierung und Autorisierung
 * 
 * @param {Object} services Dienst-Instanzen
 * @param {Object} services.authService AuthService-Instanz
 * @returns {Object} Middleware-Funktionen
 */
export function createAuthMiddleware(services) {
  const { authService } = services;

  /**
   * Middleware zum Authentifizieren von Benutzeranfragen
   * 
   * @param {Object} req Express-Anfrage
   * @param {Object} res Express-Antwort
   * @param {Function} next Nächste Middleware aufrufen
   */
  const authenticate = async (req, res, next) => {
    try {
      // Token aus Authorization-Header extrahieren
      const authHeader = req.headers.authorization;
      
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({ 
          success: false, 
          message: 'Nicht authentifiziert'
        });
      }
      
      const token = authHeader.split(' ')[1];
      
      // Token validieren
      const userData = await authService.validateToken(token);
      
      if (!userData) {
        return res.status(401).json({ 
          success: false, 
          message: 'Ungültiges oder abgelaufenes Token'
        });
      }
      
      // Benutzerdaten an die Anfrage anhängen
      req.user = userData;
      next();
    } catch (error) {
      console.error('Fehler bei der Authentifizierung:', error);
      res.status(401).json({ 
        success: false, 
        message: 'Authentifizierungsfehler'
      });
    }
  };

  /**
   * Middleware zum Prüfen einer bestimmten Berechtigung
   * 
   * @param {string} permission Erforderliche Berechtigung
   * @returns {Function} Middleware-Funktion
   */
  const requirePermission = (permission) => {
    return async (req, res, next) => {
      try {
        // Sicherstellen, dass authenticate zuerst aufgerufen wurde
        if (!req.user) {
          return res.status(401).json({ 
            success: false, 
            message: 'Nicht authentifiziert'
          });
        }
        
        // Berechtigungen prüfen
        const hasPermission = req.user.permissions.includes(permission);
        
        if (!hasPermission) {
          return res.status(403).json({ 
            success: false, 
            message: 'Keine Berechtigung für diese Aktion'
          });
        }
        
        next();
      } catch (error) {
        console.error('Fehler bei der Berechtigungsprüfung:', error);
        res.status(403).json({ 
          success: false, 
          message: 'Berechtigungsfehler'
        });
      }
    };
  };

  /**
   * Middleware zum Prüfen einer bestimmten Rolle
   * 
   * @param {string} role Erforderliche Rolle
   * @returns {Function} Middleware-Funktion
   */
  const requireRole = (role) => {
    return async (req, res, next) => {
      try {
        // Sicherstellen, dass authenticate zuerst aufgerufen wurde
        if (!req.user) {
          return res.status(401).json({ 
            success: false, 
            message: 'Nicht authentifiziert'
          });
        }
        
        // Rolle prüfen
        const hasRole = req.user.roles.includes(role);
        
        if (!hasRole) {
          return res.status(403).json({ 
            success: false, 
            message: `Diese Aktion erfordert die Rolle '${role}'`
          });
        }
        
        next();
      } catch (error) {
        console.error('Fehler bei der Rollenprüfung:', error);
        res.status(403).json({ 
          success: false, 
          message: 'Berechtigungsfehler'
        });
      }
    };
  };

  /**
   * Middleware zum Prüfen des Ressourceneigentums
   * 
   * @param {Function} getOwnerId Funktion zum Abrufen der Eigentümer-ID aus der Anfrage
   * @returns {Function} Middleware-Funktion
   */
  const requireOwnership = (getOwnerId) => {
    return async (req, res, next) => {
      try {
        // Sicherstellen, dass authenticate zuerst aufgerufen wurde
        if (!req.user) {
          return res.status(401).json({ 
            success: false, 
            message: 'Nicht authentifiziert'
          });
        }
        
        // Eigentümer-ID abrufen
        const ownerId = await getOwnerId(req);
        
        // Prüfen, ob der Benutzer der Eigentümer ist
        const isOwner = ownerId && ownerId.toString() === req.user.userId.toString();
        
        // Prüfen, ob der Benutzer Administrator ist
        const isAdmin = req.user.roles.includes('admin');
        
        if (!isOwner && !isAdmin) {
          return res.status(403).json({ 
            success: false, 
            message: 'Keine Berechtigung für diese Ressource'
          });
        }
        
        next();
      } catch (error) {
        console.error('Fehler bei der Eigentumsprüfung:', error);
        res.status(403).json({ 
          success: false, 
          message: 'Berechtigungsfehler'
        });
      }
    };
  };

  /**
   * Middleware für die optionale Authentifizierung
   * Setzt Benutzerdaten, wenn ein gültiges Token vorhanden ist, beendet die Anfrage aber nicht bei fehlendem Token
   * 
   * @param {Object} req Express-Anfrage
   * @param {Object} res Express-Antwort
   * @param {Function} next Nächste Middleware aufrufen
   */
  const optionalAuthenticate = async (req, res, next) => {
    try {
      const authHeader = req.headers.authorization;
      
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        // Ohne Token weitermachen, aber req.user auf null setzen
        req.user = null;
        return next();
      }
      
      const token = authHeader.split(' ')[1];
      const userData = await authService.validateToken(token);
      
      // Benutzerdaten setzen, wenn das Token gültig ist
      req.user = userData || null;
      next();
    } catch (error) {
      // Bei Fehlern ohne Authentifizierung weitermachen
      console.error('Fehler bei der optionalen Authentifizierung:', error);
      req.user = null;
      next();
    }
  };

  /**
   * Middleware zum Loggen von API-Anfragen mit Benutzerinformationen
   *
   * @param {Object} req Express-Anfrage
   * @param {Object} res Express-Antwort
   * @param {Function} next Nächste Middleware aufrufen
   */
  const logApiRequest = (req, res, next) => {
    const start = Date.now();

    // Nach Abschluss der Anfrage
    res.on('finish', () => {
      const duration = Date.now() - start;
      const userId = req.user ? req.user.userId : 'unauthenticated';
      const userRoles = req.user?.roles?.join(',') || '-';
      console.log(
        `[${new Date().toISOString()}] ${req.method} ${req.originalUrl} - Status: ${res.statusCode} - Duration: ${duration}ms - ` +
      `User: ${userId} - Roles: ${userRoles}`
      );
    });

    next();
  };

  return {
    authenticate,
    requirePermission,
    requireRole,
    requireOwnership,
    optionalAuthenticate,
    logApiRequest
  };
}
