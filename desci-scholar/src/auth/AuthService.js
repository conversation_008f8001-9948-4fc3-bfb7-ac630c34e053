/**
 * AuthService für DeSci-Scholar
 * 
 * Bietet Authentifizierungs- und Autorisierungsfunktionalität für die
 * dezentralisierte wissenschaftliche Publikationsplattform.
 * Umfasst Benutzerregistrierung, Anmeldung, Session-Management, JWT-Token-Verwaltung,
 * und rollenbasierte Zugriffskontrollen.
 */

import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import crypto from 'crypto';

/**
 * Benutzerrollen für die Plattform
 */
export const USER_ROLES = {
  READER: 'reader',           // Grundlegende Leseberechtigung
  AUTHOR: 'author',           // Kann Publikationen erstellen/bearbeiten
  REVIEWER: 'reviewer',       // Kann Reviews erstellen
  EDITOR: 'editor',           // Kann Publikationen annehmen/ablehnen
  ADMIN: 'admin'              // Volle administrative Rechte
};

/**
 * Berechtigungen für rollenbasierten Zugriff
 */
export const PERMISSIONS = {
  READ_PUBLICATION: 'read_publication',
  CREATE_PUBLICATION: 'create_publication',
  EDIT_PUBLICATION: 'edit_publication',
  DELETE_PUBLICATION: 'delete_publication',
  REVIEW_PUBLICATION: 'review_publication',
  APPROVE_PUBLICATION: 'approve_publication',
  MANAGE_USERS: 'manage_users',
  MANAGE_SYSTEM: 'manage_system'
};

/**
 * Zuordnung von Rollen zu Berechtigungen
 */
const ROLE_PERMISSIONS = {
  [USER_ROLES.READER]: [
    PERMISSIONS.READ_PUBLICATION
  ],
  [USER_ROLES.AUTHOR]: [
    PERMISSIONS.READ_PUBLICATION,
    PERMISSIONS.CREATE_PUBLICATION,
    PERMISSIONS.EDIT_PUBLICATION
  ],
  [USER_ROLES.REVIEWER]: [
    PERMISSIONS.READ_PUBLICATION,
    PERMISSIONS.REVIEW_PUBLICATION
  ],
  [USER_ROLES.EDITOR]: [
    PERMISSIONS.READ_PUBLICATION,
    PERMISSIONS.EDIT_PUBLICATION,
    PERMISSIONS.APPROVE_PUBLICATION
  ],
  [USER_ROLES.ADMIN]: [
    PERMISSIONS.READ_PUBLICATION,
    PERMISSIONS.CREATE_PUBLICATION,
    PERMISSIONS.EDIT_PUBLICATION,
    PERMISSIONS.DELETE_PUBLICATION,
    PERMISSIONS.REVIEW_PUBLICATION,
    PERMISSIONS.APPROVE_PUBLICATION,
    PERMISSIONS.MANAGE_USERS,
    PERMISSIONS.MANAGE_SYSTEM
  ]
};

/**
 * AuthService: Verwaltet Authentifizierung, Token-Erstellung und -Validierung,
 * sowie Berechtigungen und Zugriffskontrollmechanismen
 */
export class AuthService {
  /**
   * Konstruktor
   * 
   * @param {Object} options Optionen für den Authentifizierungsdienst
   * @param {Object} options.databaseService Instanz des DatabaseService
   * @param {string} options.jwtSecret Geheimnis für die JWT-Signierung
   * @param {number} options.tokenExpiresIn Token-Gültigkeit in Sekunden (Standard: 24h)
   * @param {number} options.refreshTokenExpiresIn Refresh-Token-Gültigkeit in Sekunden (Standard: 7 Tage)
   * @param {number} options.saltRounds Anzahl der Salzrunden für Passwort-Hashing (Standard: 10)
   */
  constructor(options = {}) {
    const {
      databaseService,
      jwtSecret = crypto.randomBytes(32).toString('hex'),
      tokenExpiresIn = 86400,          // 24 Stunden
      refreshTokenExpiresIn = 604800,  // 7 Tage
      saltRounds = 10
    } = options;

    this.databaseService = databaseService;
    this.jwtSecret = jwtSecret;
    this.tokenExpiresIn = tokenExpiresIn;
    this.refreshTokenExpiresIn = refreshTokenExpiresIn;
    this.saltRounds = saltRounds;
    
    // Collection-Namen für den Datenbankzugriff
    this.usersCollection = 'users';
    this.tokensCollection = 'auth_tokens';
    
    // Aktive Sitzungen (in-memory)
    this.activeSessions = new Map();
    
    // Event-Listener für Authentifizierungsereignisse
    this.eventListeners = {
      'user.login': [],
      'user.logout': [],
      'user.register': [],
      'user.passwordChange': [],
      'token.refresh': [],
      'auth.failure': []
    };
  }

  /**
   * Initialisiert den Authentifizierungsdienst
   * 
   * @returns {Promise<boolean>} Erfolg der Initialisierung
   */
  async initialize() {
    try {
      if (!this.databaseService) {
        throw new Error('DatabaseService ist erforderlich für die Initialisierung');
      }
      
      // Prüfen, ob die Datenbank verbunden ist
      const isConnected = await this.databaseService.isConnected();
      if (!isConnected) {
        throw new Error('Datenbankverbindung ist nicht aktiv');
      }
      
      // Indices für Benutzer-Collection erstellen
      await this._createUserIndices();
      
      // Adminkonto erstellen, wenn noch keines existiert
      await this._ensureAdminExists();
      
      console.log('AuthService: Erfolgreich initialisiert');
      return true;
    } catch (error) {
      console.error('AuthService: Fehler bei der Initialisierung', error);
      return false;
    }
  }

  /**
   * Benutzerregistrierung
   * 
   * @param {Object} userData Benutzerdaten für die Registrierung
   * @param {string} userData.email E-Mail-Adresse
   * @param {string} userData.password Passwort
   * @param {string} userData.name Name des Benutzers
   * @param {string} userData.institution Institution des Benutzers
   * @param {string[]} userData.roles Rollen des Benutzers (optional)
   * @returns {Promise<Object>} Registrierter Benutzer (ohne Passwort)
   */
  async registerUser(userData) {
    try {
      // Validierung der Benutzerdaten
      this._validateUserData(userData);
      
      const { email, password, name, institution, roles = [USER_ROLES.READER] } = userData;
      
      // Sicherstellen, dass die E-Mail-Adresse eindeutig ist
      const existingUser = await this._getUserByEmail(email);
      if (existingUser) {
        throw new Error(`Benutzer mit der E-Mail-Adresse ${email} existiert bereits`);
      }
      
      // Validierung der Rollen
      const validRoles = roles.filter(role => Object.values(USER_ROLES).includes(role));
      if (validRoles.length === 0) {
        validRoles.push(USER_ROLES.READER);
      }
      
      // Passwort hashen
      const hashedPassword = await bcrypt.hash(password, this.saltRounds);
      
      // Benutzer erstellen
      const user = {
        email,
        password: hashedPassword,
        name,
        institution,
        roles: validRoles,
        verified: false,
        createdAt: new Date(),
        updatedAt: new Date(),
        verificationToken: crypto.randomBytes(32).toString('hex'),
        lastLogin: null
      };
      
      // Benutzer in der Datenbank speichern
      const collection = this.databaseService.getCollection(this.usersCollection);
      const result = await collection.insertOne(user);
      
      if (!result.insertedId) {
        throw new Error('Fehler beim Speichern des Benutzers');
      }
      
      // Benutzerdaten ohne Passwort zurückgeben
      const { password: _, verificationToken: __, ...userWithoutSensitiveData } = user;
      
      // Registrierungsereignis auslösen
      this._triggerEvent('user.register', userWithoutSensitiveData);
      
      return {
        ...userWithoutSensitiveData,
        _id: result.insertedId
      };
    } catch (error) {
      console.error('AuthService: Fehler bei der Benutzerregistrierung', error);
      this._triggerEvent('auth.failure', { 
        action: 'register', 
        email: userData.email, 
        reason: error.message 
      });
      throw error;
    }
  }

  /**
   * Benutzeranmeldung
   * 
   * @param {string} email E-Mail-Adresse des Benutzers
   * @param {string} password Passwort des Benutzers
   * @param {Object} options Optionen für die Anmeldung
   * @param {boolean} options.createRefreshToken Refresh-Token erstellen (Standard: true)
   * @param {string} options.deviceInfo Geräteinformationen für die Sitzung
   * @returns {Promise<Object>} Anmeldedaten mit Token
   */
  async loginUser(email, password, options = {}) {
    try {
      const { createRefreshToken = true, deviceInfo = 'unknown' } = options;
      
      // Benutzer aus der Datenbank holen
      const user = await this._getUserByEmail(email);
      if (!user) {
        throw new Error('Ungültige Anmeldedaten');
      }
      
      // Passwort überprüfen
      const isPasswordValid = await bcrypt.compare(password, user.password);
      if (!isPasswordValid) {
        throw new Error('Ungültige Anmeldedaten');
      }
      
      // Prüfen, ob der Benutzer verifiziert ist
      if (!user.verified) {
        throw new Error('Bitte bestätigen Sie zuerst Ihre E-Mail-Adresse');
      }
      
      // Login-Zeit aktualisieren
      await this._updateLastLogin(user._id);
      
      // Token generieren
      const token = this._generateToken(user);
      
      // Refresh-Token erstellen, wenn gewünscht
      let refreshToken = null;
      if (createRefreshToken) {
        refreshToken = await this._createRefreshToken(user._id, deviceInfo);
      }
      
      // Session erstellen
      const sessionId = crypto.randomBytes(16).toString('hex');
      this.activeSessions.set(sessionId, {
        userId: user._id,
        email: user.email,
        roles: user.roles,
        createdAt: new Date(),
        deviceInfo
      });
      
      // Anmeldeereignis auslösen
      const { password: _, ...userWithoutPassword } = user;
      this._triggerEvent('user.login', { 
        user: userWithoutPassword, 
        deviceInfo 
      });
      
      return {
        token,
        refreshToken,
        sessionId,
        user: {
          _id: user._id,
          email: user.email,
          name: user.name,
          roles: user.roles,
          institution: user.institution
        }
      };
    } catch (error) {
      console.error('AuthService: Fehler bei der Benutzeranmeldung', error);
      this._triggerEvent('auth.failure', { 
        action: 'login', 
        email, 
        reason: error.message 
      });
      throw error;
    }
  }

  /**
   * Benutzerabmeldung
   * 
   * @param {string} sessionId Sitzungs-ID
   * @param {string} userId Benutzer-ID
   * @param {boolean} allSessions Alle Sitzungen des Benutzers abmelden
   * @returns {Promise<boolean>} Erfolg der Abmeldung
   */
  async logoutUser(sessionId, userId, allSessions = false) {
    try {
      if (!sessionId && !userId) {
        throw new Error('SessionId oder UserId ist erforderlich');
      }
      
      // Bestimmte Sitzung beenden
      if (sessionId && this.activeSessions.has(sessionId)) {
        const session = this.activeSessions.get(sessionId);
        this.activeSessions.delete(sessionId);
        
        // Abmeldeereignis auslösen
        this._triggerEvent('user.logout', { 
          userId: session.userId, 
          email: session.email 
        });
      }
      
      // Alle Sitzungen des Benutzers beenden, wenn gewünscht
      if (allSessions && userId) {
        for (const [id, session] of this.activeSessions.entries()) {
          if (session.userId.toString() === userId.toString()) {
            this.activeSessions.delete(id);
          }
        }
        
        // Refresh-Tokens löschen
        await this._deleteAllRefreshTokens(userId);
      }
      
      return true;
    } catch (error) {
      console.error('AuthService: Fehler bei der Benutzerabmeldung', error);
      return false;
    }
  }

  /**
   * Token validieren
   * 
   * @param {string} token JWT-Token zur Validierung
   * @returns {Promise<Object|null>} Entschlüsselte Token-Daten oder null
   */
  async validateToken(token) {
    try {
      if (!token) {
        return null;
      }
      
      // Token entschlüsseln und validieren
      const decoded = jwt.verify(token, this.jwtSecret);
      
      // Benutzer-ID aus dem Token extrahieren
      const userId = decoded.userId;
      
      // Benutzer aus der Datenbank holen, um zu prüfen, ob er noch existiert
      const user = await this._getUserById(userId);
      if (!user) {
        return null;
      }
      
      return {
        userId: user._id,
        email: user.email,
        roles: user.roles,
        permissions: this._getUserPermissions(user.roles)
      };
    } catch (error) {
      console.error('AuthService: Fehler bei der Token-Validierung', error);
      return null;
    }
  }

  /**
   * Token mit Refresh-Token aktualisieren
   * 
   * @param {string} refreshToken Refresh-Token
   * @returns {Promise<Object|null>} Neues Token oder null
   */
  async refreshToken(refreshToken) {
    try {
      if (!refreshToken) {
        throw new Error('Refresh-Token ist erforderlich');
      }
      
      // Refresh-Token aus der Datenbank holen
      const tokenCollection = this.databaseService.getCollection(this.tokensCollection);
      const tokenDocument = await tokenCollection.findOne({ token: refreshToken });
      
      if (!tokenDocument) {
        throw new Error('Ungültiger Refresh-Token');
      }
      
      // Prüfen, ob der Refresh-Token abgelaufen ist
      const now = new Date();
      if (now > tokenDocument.expiresAt) {
        await tokenCollection.deleteOne({ token: refreshToken });
        throw new Error('Refresh-Token ist abgelaufen');
      }
      
      // Benutzer aus der Datenbank holen
      const user = await this._getUserById(tokenDocument.userId);
      if (!user) {
        throw new Error('Benutzer existiert nicht mehr');
      }
      
      // Neues Token generieren
      const newToken = this._generateToken(user);
      
      // Refresh-Token-Ereignis auslösen
      this._triggerEvent('token.refresh', { 
        userId: user._id, 
        email: user.email 
      });
      
      return {
        token: newToken,
        user: {
          _id: user._id,
          email: user.email,
          name: user.name,
          roles: user.roles
        }
      };
    } catch (error) {
      console.error('AuthService: Fehler beim Token-Refresh', error);
      throw error;
    }
  }

  /**
   * Benutzerberechtigungen prüfen
   * 
   * @param {string|string[]} userId Benutzer-ID(s)
   * @param {string} permission Zu prüfende Berechtigung
   * @returns {Promise<boolean>} Hat der Benutzer die Berechtigung?
   */
  async hasPermission(userId, permission) {
    try {
      if (!userId || !permission) {
        return false;
      }
      
      // Benutzer aus der Datenbank holen
      const user = await this._getUserById(userId);
      if (!user) {
        return false;
      }
      
      // Berechtigungen des Benutzers abfragen
      const userPermissions = this._getUserPermissions(user.roles);
      
      // Prüfen, ob die angeforderte Berechtigung vorhanden ist
      return userPermissions.includes(permission);
    } catch (error) {
      console.error('AuthService: Fehler bei der Berechtigungsprüfung', error);
      return false;
    }
  }

  /**
   * Benutzerpasswort zurücksetzen (Anforderung)
   * 
   * @param {string} email E-Mail-Adresse des Benutzers
   * @returns {Promise<boolean>} Erfolg der Anforderung
   */
  async requestPasswordReset(email) {
    try {
      // Benutzer aus der Datenbank holen
      const user = await this._getUserByEmail(email);
      if (!user) {
        // Aus Sicherheitsgründen geben wir trotzdem true zurück,
        // um keine Informationen darüber preiszugeben, ob die E-Mail existiert
        return true;
      }
      
      // Reset-Token generieren
      const resetToken = crypto.randomBytes(32).toString('hex');
      const resetTokenExpiry = new Date();
      resetTokenExpiry.setHours(resetTokenExpiry.getHours() + 1); // 1 Stunde gültig
      
      // Token in der Datenbank speichern
      const collection = this.databaseService.getCollection(this.usersCollection);
      await collection.updateOne(
        { _id: user._id },
        { 
          $set: { 
            resetToken, 
            resetTokenExpiry,
            updatedAt: new Date()
          } 
        }
      );
      
      // In einer realen Anwendung würde hier eine E-Mail gesendet werden
      console.log(`Password-Reset-Token für ${email}: ${resetToken}`);
      
      return true;
    } catch (error) {
      console.error('AuthService: Fehler bei der Passwort-Reset-Anforderung', error);
      return false;
    }
  }

  /**
   * Benutzerpasswort zurücksetzen (Durchführung)
   * 
   * @param {string} resetToken Reset-Token
   * @param {string} newPassword Neues Passwort
   * @returns {Promise<boolean>} Erfolg der Passwortänderung
   */
  async resetPassword(resetToken, newPassword) {
    try {
      if (!resetToken || !newPassword) {
        throw new Error('Reset-Token und neues Passwort sind erforderlich');
      }
      
      // Validierung des neuen Passworts
      if (newPassword.length < 8) {
        throw new Error('Das Passwort muss mindestens 8 Zeichen lang sein');
      }
      
      // Benutzer mit dem Token finden
      const collection = this.databaseService.getCollection(this.usersCollection);
      const user = await collection.findOne({ 
        resetToken,
        resetTokenExpiry: { $gt: new Date() }
      });
      
      if (!user) {
        throw new Error('Ungültiger oder abgelaufener Reset-Token');
      }
      
      // Passwort hashen
      const hashedPassword = await bcrypt.hash(newPassword, this.saltRounds);
      
      // Passwort aktualisieren und Token entfernen
      await collection.updateOne(
        { _id: user._id },
        {
          $set: {
            password: hashedPassword,
            updatedAt: new Date()
          },
          $unset: {
            resetToken: '',
            resetTokenExpiry: ''
          }
        }
      );
      
      // Alle Sitzungen des Benutzers beenden
      await this.logoutUser(null, user._id, true);
      
      // Passwortänderungsereignis auslösen
      this._triggerEvent('user.passwordChange', { 
        userId: user._id, 
        email: user.email 
      });
      
      return true;
    } catch (error) {
      console.error('AuthService: Fehler beim Zurücksetzen des Passworts', error);
      throw error;
    }
  }

  /**
   * Benutzer-E-Mail verifizieren
   * 
   * @param {string} verificationToken Verifikationstoken
   * @returns {Promise<boolean>} Erfolg der Verifikation
   */
  async verifyEmail(verificationToken) {
    try {
      if (!verificationToken) {
        throw new Error('Verifikationstoken ist erforderlich');
      }
      
      // Benutzer mit dem Token finden
      const collection = this.databaseService.getCollection(this.usersCollection);
      const user = await collection.findOne({ verificationToken });
      
      if (!user) {
        throw new Error('Ungültiger Verifikationstoken');
      }
      
      // Benutzer als verifiziert markieren und Token entfernen
      await collection.updateOne(
        { _id: user._id },
        {
          $set: {
            verified: true,
            updatedAt: new Date()
          },
          $unset: {
            verificationToken: ''
          }
        }
      );
      
      return true;
    } catch (error) {
      console.error('AuthService: Fehler bei der E-Mail-Verifikation', error);
      throw error;
    }
  }

  /**
   * Benutzerinformationen aktualisieren
   * 
   * @param {string} userId Benutzer-ID
   * @param {Object} updates Zu aktualisierende Informationen
   * @returns {Promise<Object>} Aktualisierter Benutzer
   */
  async updateUser(userId, updates) {
    try {
      if (!userId) {
        throw new Error('Benutzer-ID ist erforderlich');
      }
      
      // Sensible Felder entfernen
      const { password, email, verified, roles, ...safeUpdates } = updates;
      
      // Aktualisierungsfelder vorbereiten
      const updateFields = {
        ...safeUpdates,
        updatedAt: new Date()
      };
      
      // Benutzer aktualisieren
      const collection = this.databaseService.getCollection(this.usersCollection);
      const result = await collection.updateOne(
        { _id: this.databaseService.createObjectId(userId) },
        { $set: updateFields }
      );
      
      if (result.modifiedCount === 0) {
        throw new Error('Benutzer konnte nicht aktualisiert werden');
      }
      
      // Aktualisierten Benutzer zurückgeben
      const updatedUser = await this._getUserById(userId);
      const { password: _, ...userWithoutPassword } = updatedUser;
      
      return userWithoutPassword;
    } catch (error) {
      console.error('AuthService: Fehler bei der Benutzeraktualisierung', error);
      throw error;
    }
  }

  /**
   * Benutzerrollen aktualisieren (Admin-Funktion)
   * 
   * @param {string} userId Benutzer-ID
   * @param {string[]} roles Neue Rollen
   * @param {string} adminId ID des Administrators, der die Änderung vornimmt
   * @returns {Promise<Object>} Aktualisierter Benutzer
   */
  async updateUserRoles(userId, roles, adminId) {
    try {
      if (!userId || !roles || !adminId) {
        throw new Error('Benutzer-ID, Rollen und Admin-ID sind erforderlich');
      }
      
      // Prüfen, ob der Administrator die Berechtigung hat
      const hasPermission = await this.hasPermission(adminId, PERMISSIONS.MANAGE_USERS);
      if (!hasPermission) {
        throw new Error('Keine Berechtigung zum Ändern von Benutzerrollen');
      }
      
      // Validierung der Rollen
      const validRoles = roles.filter(role => Object.values(USER_ROLES).includes(role));
      if (validRoles.length === 0) {
        throw new Error('Mindestens eine gültige Rolle ist erforderlich');
      }
      
      // Rollen aktualisieren
      const collection = this.databaseService.getCollection(this.usersCollection);
      const result = await collection.updateOne(
        { _id: this.databaseService.createObjectId(userId) },
        {
          $set: {
            roles: validRoles,
            updatedAt: new Date()
          }
        }
      );
      
      if (result.modifiedCount === 0) {
        throw new Error('Benutzerrollen konnten nicht aktualisiert werden');
      }
      
      // Aktualisierten Benutzer zurückgeben
      const updatedUser = await this._getUserById(userId);
      const { password: _, ...userWithoutPassword } = updatedUser;
      
      return userWithoutPassword;
    } catch (error) {
      console.error('AuthService: Fehler bei der Aktualisierung der Benutzerrollen', error);
      throw error;
    }
  }

  /**
   * Event-Listener registrieren
   * 
   * @param {string} event Event-Name
   * @param {Function} callback Callback-Funktion
   */
  on(event, callback) {
    if (!this.eventListeners[event]) {
      this.eventListeners[event] = [];
    }
    
    this.eventListeners[event].push(callback);
  }

  /**
   * Event-Listener entfernen
   * 
   * @param {string} event Event-Name
   * @param {Function} callback Callback-Funktion
   */
  off(event, callback) {
    if (!this.eventListeners[event]) {
      return;
    }
    
    this.eventListeners[event] = this.eventListeners[event]
      .filter(listener => listener !== callback);
  }

  /**
   * Benutzer anhand der E-Mail-Adresse abrufen
   * 
   * @private
   * @param {string} email E-Mail-Adresse
   * @returns {Promise<Object|null>} Benutzerobjekt oder null
   */
  async _getUserByEmail(email) {
    try {
      const collection = this.databaseService.getCollection(this.usersCollection);
      return await collection.findOne({ email: email.toLowerCase() });
    } catch (error) {
      console.error('AuthService: Fehler beim Abrufen des Benutzers nach E-Mail', error);
      return null;
    }
  }

  /**
   * Benutzer anhand der ID abrufen
   * 
   * @private
   * @param {string} userId Benutzer-ID
   * @returns {Promise<Object|null>} Benutzerobjekt oder null
   */
  async _getUserById(userId) {
    try {
      const collection = this.databaseService.getCollection(this.usersCollection);
      return await collection.findOne({ 
        _id: this.databaseService.createObjectId(userId) 
      });
    } catch (error) {
      console.error('AuthService: Fehler beim Abrufen des Benutzers nach ID', error);
      return null;
    }
  }

  /**
   * Token für einen Benutzer generieren
   * 
   * @private
   * @param {Object} user Benutzerobjekt
   * @returns {string} JWT-Token
   */
  _generateToken(user) {
    // Token-Daten
    const tokenData = {
      userId: user._id,
      email: user.email,
      roles: user.roles
    };
    
    // Token mit den Daten und einem Geheimnis signieren
    return jwt.sign(tokenData, this.jwtSecret, {
      expiresIn: this.tokenExpiresIn
    });
  }

  /**
   * Refresh-Token erstellen
   * 
   * @private
   * @param {string} userId Benutzer-ID
   * @param {string} deviceInfo Geräteinformationen
   * @returns {Promise<string>} Refresh-Token
   */
  async _createRefreshToken(userId, deviceInfo) {
    try {
      // Token generieren
      const token = crypto.randomBytes(40).toString('hex');
      const now = new Date();
      const expiresAt = new Date(now.getTime() + this.refreshTokenExpiresIn * 1000);
      
      // Token in der Datenbank speichern
      const tokenCollection = this.databaseService.getCollection(this.tokensCollection);
      await tokenCollection.insertOne({
        userId: this.databaseService.createObjectId(userId),
        token,
        deviceInfo,
        createdAt: now,
        expiresAt
      });
      
      return token;
    } catch (error) {
      console.error('AuthService: Fehler beim Erstellen des Refresh-Tokens', error);
      throw error;
    }
  }

  /**
   * Alle Refresh-Tokens eines Benutzers löschen
   * 
   * @private
   * @param {string} userId Benutzer-ID
   * @returns {Promise<void>}
   */
  async _deleteAllRefreshTokens(userId) {
    try {
      const tokenCollection = this.databaseService.getCollection(this.tokensCollection);
      await tokenCollection.deleteMany({ 
        userId: this.databaseService.createObjectId(userId) 
      });
    } catch (error) {
      console.error('AuthService: Fehler beim Löschen der Refresh-Tokens', error);
    }
  }

  /**
   * Letzte Anmeldezeit eines Benutzers aktualisieren
   * 
   * @private
   * @param {string} userId Benutzer-ID
   * @returns {Promise<void>}
   */
  async _updateLastLogin(userId) {
    try {
      const collection = this.databaseService.getCollection(this.usersCollection);
      await collection.updateOne(
        { _id: this.databaseService.createObjectId(userId) },
        {
          $set: {
            lastLogin: new Date(),
            updatedAt: new Date()
          }
        }
      );
    } catch (error) {
      console.error('AuthService: Fehler beim Aktualisieren der letzten Anmeldezeit', error);
    }
  }

  /**
   * Berechtigungen für Benutzerrollen abrufen
   * 
   * @private
   * @param {string[]} roles Rollen des Benutzers
   * @returns {string[]} Berechtigungen
   */
  _getUserPermissions(roles) {
    // Alle Berechtigungen für die angegebenen Rollen sammeln
    const permissions = new Set();
    
    for (const role of roles) {
      const rolePermissions = ROLE_PERMISSIONS[role] || [];
      for (const permission of rolePermissions) {
        permissions.add(permission);
      }
    }
    
    return Array.from(permissions);
  }

  /**
   * Benutzerdaten validieren
   * 
   * @private
   * @param {Object} userData Benutzerdaten
   * @throws {Error} Validierungsfehler
   */
  _validateUserData(userData) {
    const { email, password, name } = userData;
    
    if (!email || !password || !name) {
      throw new Error('E-Mail, Passwort und Name sind erforderlich');
    }
    
    // E-Mail-Format validieren
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      throw new Error('Ungültige E-Mail-Adresse');
    }
    
    // Passwortlänge validieren
    if (password.length < 8) {
      throw new Error('Das Passwort muss mindestens 8 Zeichen lang sein');
    }
    
    // Namenlänge validieren
    if (name.length < 2) {
      throw new Error('Der Name muss mindestens 2 Zeichen lang sein');
    }
  }

  /**
   * Datenbankindizes für Benutzer erstellen
   * 
   * @private
   * @returns {Promise<void>}
   */
  async _createUserIndices() {
    try {
      const collection = this.databaseService.getCollection(this.usersCollection);
      
      // Index für E-Mail-Eindeutigkeit
      await collection.createIndex({ email: 1 }, { unique: true });
      
      // Index für die Verifikation
      await collection.createIndex({ verificationToken: 1 });
      
      // Index für Passwort-Reset-Tokens
      await collection.createIndex({ resetToken: 1 });
      await collection.createIndex({ resetTokenExpiry: 1 }, { expireAfterSeconds: 3600 });
    } catch (error) {
      console.error('AuthService: Fehler beim Erstellen von Indizes', error);
      throw error;
    }
  }

  /**
   * Sicherstellen, dass ein Administratorkonto existiert
   * 
   * @private
   * @returns {Promise<void>}
   */
  async _ensureAdminExists() {
    try {
      const collection = this.databaseService.getCollection(this.usersCollection);
      
      // Prüfen, ob bereits ein Administrator existiert
      const adminExists = await collection.findOne({ roles: USER_ROLES.ADMIN });
      
      if (!adminExists) {
        console.log('AuthService: Erstelle Standardadministrator');
        
        // Adminpasswort generieren
        const adminPassword = crypto.randomBytes(8).toString('hex');
        const hashedPassword = await bcrypt.hash(adminPassword, this.saltRounds);
        
        // Administratorkonto erstellen
        const admin = {
          email: '<EMAIL>',
          password: hashedPassword,
          name: 'Administrator',
          institution: 'DeSci Scholar',
          roles: [USER_ROLES.ADMIN],
          verified: true,
          createdAt: new Date(),
          updatedAt: new Date(),
          lastLogin: null
        };
        
        await collection.insertOne(admin);
        
        console.log(`AuthService: Standardadministrator erstellt mit Passwort: ${adminPassword}`);
      }
    } catch (error) {
      console.error('AuthService: Fehler beim Erstellen des Administratorkontos', error);
    }
  }

  /**
   * Event auslösen
   * 
   * @private
   * @param {string} event Event-Name
   * @param {Object} data Event-Daten
   */
  _triggerEvent(event, data) {
    if (!this.eventListeners[event]) {
      return;
    }
    
    for (const callback of this.eventListeners[event]) {
      try {
        callback(data);
      } catch (error) {
        console.error(`AuthService: Fehler im Event-Listener für ${event}`, error);
      }
    }
  }
}

export default AuthService; 