/**
 * StatsService.js
 * 
 * Service für die Erfassung, Verarbeitung und Bereitstellung von Statistiken zur Plattformnutzung,
 * Publikationen, Speichernutzung und Benutzeraktivitäten im DeSci-Scholar-System.
 */

import { ObjectId } from 'mongodb';

class StatsService {
  /**
   * Initialisiert den StatsService
   * @param {Object} options - Konfigurationsoptionen
   * @param {DatabaseService} options.databaseService - Datenbankdienst für den Zugriff auf Sammlungen
   * @param {StorageService} options.storageService - Speicherdienst für Speicherstatistiken
   * @param {Object} options.config - Konfigurationseinstellungen
   * @param {number} options.config.trendingPeriodDays - Standardzeitraum in Tagen für Trend-Analysen
   * @param {number} options.config.cacheTimeMs - Dauer in Millisekunden für die Cache-Gültigkeit
   */
  constructor({ databaseService, storageService, config = {} }) {
    this.db = databaseService;
    this.storage = storageService;
    this.config = {
      trendingPeriodDays: config.trendingPeriodDays || 30,
      cacheTimeMs: config.cacheTimeMs || 1000 * 60 * 15, // 15 Minuten Standardwert
    };
    this.cache = {
      totalStats: { data: null, timestamp: 0 },
      trendingPublications: { data: null, timestamp: 0, period: null },
      storageStats: { data: null, timestamp: 0 },
      userStats: { data: null, timestamp: 0 },
    };
    this.initialized = false;
  }

  /**
   * Initialisiert den Dienst und stellt die Datenbankverbindung sicher
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.initialized) return;
    
    if (!this.db || !this.db.isConnected()) {
      throw new Error('StatsService erfordert einen verbundenen DatabaseService');
    }

    // Stelle sicher, dass die erforderlichen Sammlungen und Indizes existieren
    await this._ensureCollections();
    
    console.log('StatsService erfolgreich initialisiert');
    this.initialized = true;
  }

  /**
   * Stellt sicher, dass die erforderlichen Sammlungen und Indizes vorhanden sind
   * @private
   */
  async _ensureCollections() {
    // Erstelle eine dedizierte Sammlung für Zugriffsstatistiken, falls noch nicht vorhanden
    const collections = await this.db.listCollections();
    if (!collections.includes('accessStats')) {
      await this.db.createCollection('accessStats');
      
      // Erstelle Indizes für effiziente Abfragen
      await this.db.createIndex('accessStats', { publicationId: 1 });
      await this.db.createIndex('accessStats', { timestamp: 1 });
      await this.db.createIndex('accessStats', { type: 1 });
      await this.db.createIndex('accessStats', { userId: 1 });
    }
  }

  /**
   * Zeichnet einen Zugriff auf eine Publikation auf
   * @param {Object} accessData - Zugriffsdaten
   * @param {string} accessData.publicationId - ID der Publikation
   * @param {string} accessData.type - Zugriffstyp (view, download, citation)
   * @param {string} [accessData.userId] - ID des Benutzers (optional)
   * @param {Object} [accessData.metadata] - Zusätzliche Metadaten zum Zugriff
   * @returns {Promise<boolean>} - Erfolg der Operation
   */
  async recordAccess({ publicationId, type, userId = null, metadata = {} }) {
    if (!this.initialized) await this.initialize();
    
    try {
      const accessRecord = {
        publicationId: typeof publicationId === 'string' ? publicationId : publicationId.toString(),
        type,
        timestamp: new Date(),
        userId,
        ...metadata
      };
      
      await this.db.insertOne('accessStats', accessRecord);
      
      // Inkrementiere auch die Zähler direkt in der Publikationssammlung für schnelle Zugriffe
      await this._updatePublicationCounters(publicationId, type);
      
      return true;
    } catch (error) {
      console.error('Fehler beim Aufzeichnen des Zugriffs:', error);
      return false;
    }
  }

  /**
   * Aktualisiert Zähler in der Publikationsdatenbank
   * @private
   * @param {string} publicationId - ID der Publikation
   * @param {string} type - Zugriffstyp
   */
  async _updatePublicationCounters(publicationId, type) {
    const updateField = {};
    
    switch (type) {
      case 'view':
        updateField['stats.views'] = 1;
        break;
      case 'download':
        updateField['stats.downloads'] = 1;
        break;
      case 'citation':
        updateField['stats.citations'] = 1;
        break;
    }
    
    if (Object.keys(updateField).length > 0) {
      await this.db.updateOne(
        'publications',
        { _id: typeof publicationId === 'string' ? new ObjectId(publicationId) : publicationId },
        { $inc: updateField }
      );
    }
  }

  /**
   * Liefert aggregierte Gesamtstatistiken zur Plattform
   * @returns {Promise<Object>} Aggregierte Statistiken
   */
  async getTotalStats() {
    if (!this.initialized) await this.initialize();
    
    // Prüfe, ob ein gültiger Cache vorhanden ist
    const now = Date.now();
    if (this.cache.totalStats.data && 
        (now - this.cache.totalStats.timestamp) < this.config.cacheTimeMs) {
      return this.cache.totalStats.data;
    }
    
    try {
      const [publications, downloads, views, users, totalStorage] = await Promise.all([
        this.db.countDocuments('publications', {}),
        this.getTotalDownloads(),
        this.getTotalViews(),
        this.db.countDocuments('users', {}),
        this.storage ? this.storage.getTotalStorageUsage() : 0
      ]);
      
      const stats = {
        publications,
        downloads,
        views,
        users,
        totalStorageBytes: totalStorage,
        totalStorageFormatted: this._formatBytes(totalStorage),
        avgDownloadsPerPublication: publications > 0 ? Math.round(downloads / publications) : 0,
        lastUpdated: new Date()
      };
      
      // Aktualisiere den Cache
      this.cache.totalStats = {
        data: stats,
        timestamp: now
      };
      
      return stats;
    } catch (error) {
      console.error('Fehler beim Abrufen der Gesamtstatistiken:', error);
      throw error;
    }
  }

  /**
   * Liefert die Gesamtzahl der Downloads
   * @param {Object} [options] - Abfrageoptionen
   * @param {Date} [options.startDate] - Startdatum für die Filterung
   * @param {Date} [options.endDate] - Enddatum für die Filterung
   * @returns {Promise<number>} Anzahl der Downloads
   */
  async getTotalDownloads(options = {}) {
    const query = { type: 'download' };
    
    if (options.startDate || options.endDate) {
      query.timestamp = {};
      if (options.startDate) query.timestamp.$gte = options.startDate;
      if (options.endDate) query.timestamp.$lte = options.endDate;
    }
    
    return await this.db.countDocuments('accessStats', query);
  }

  /**
   * Liefert die Gesamtzahl der Aufrufe
   * @param {Object} [options] - Abfrageoptionen
   * @param {Date} [options.startDate] - Startdatum für die Filterung
   * @param {Date} [options.endDate] - Enddatum für die Filterung
   * @returns {Promise<number>} Anzahl der Aufrufe
   */
  async getTotalViews(options = {}) {
    const query = { type: 'view' };
    
    if (options.startDate || options.endDate) {
      query.timestamp = {};
      if (options.startDate) query.timestamp.$gte = options.startDate;
      if (options.endDate) query.timestamp.$lte = options.endDate;
    }
    
    return await this.db.countDocuments('accessStats', query);
  }

  /**
   * Liefert die Trend-Publikationen basierend auf aktueller Aktivität
   * @param {Object} [options] - Abfrageoptionen
   * @param {number} [options.period=30] - Zeitraum in Tagen
   * @param {number} [options.limit=10] - Maximale Anzahl der zurückgegebenen Einträge
   * @returns {Promise<Array>} Liste der Trend-Publikationen
   */
  async getTrendingPublications({ period = this.config.trendingPeriodDays, limit = 10 } = {}) {
    if (!this.initialized) await this.initialize();
    
    // Prüfe, ob ein gültiger Cache vorhanden ist
    const now = Date.now();
    if (this.cache.trendingPublications.data && 
        this.cache.trendingPublications.period === period &&
        (now - this.cache.trendingPublications.timestamp) < this.config.cacheTimeMs) {
      return this.cache.trendingPublications.data.slice(0, limit);
    }
    
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - period);
      
      // Aggregationspipeline für Trend-Berechnung
      const pipeline = [
        { 
          $match: { 
            timestamp: { $gte: startDate },
            type: { $in: ['view', 'download', 'citation'] }
          }
        },
        {
          $group: {
            _id: '$publicationId',
            views: { 
              $sum: { $cond: [{ $eq: ['$type', 'view'] }, 1, 0] }
            },
            downloads: { 
              $sum: { $cond: [{ $eq: ['$type', 'download'] }, 1, 0] }
            },
            citations: { 
              $sum: { $cond: [{ $eq: ['$type', 'citation'] }, 1, 0] }
            }
          }
        },
        {
          $project: {
            _id: 1,
            views: 1,
            downloads: 1,
            citations: 1,
            score: { 
              $add: [
                '$views', 
                { $multiply: ['$downloads', 3] }, // Downloads werden höher gewichtet
                { $multiply: ['$citations', 5] }  // Zitierungen werden am höchsten gewichtet
              ]
            }
          }
        },
        { $sort: { score: -1 } },
        { $limit: 100 } // Holen mehr für den Cache
      ];
      
      const trendingIds = await this.db.aggregate('accessStats', pipeline);
      
      // Hole Publikationsdetails für die Trend-IDs
      const trendingPublications = [];
      for (const item of trendingIds) {
        try {
          const pubDetails = await this.db.findOne('publications', {
            _id: typeof item._id === 'string' ? new ObjectId(item._id) : item._id
          });
          
          if (pubDetails) {
            trendingPublications.push({
              id: item._id,
              title: pubDetails.title,
              authors: pubDetails.authors,
              metrics: {
                views: item.views,
                downloads: item.downloads,
                citations: item.citations,
                score: item.score
              },
              abstract: pubDetails.abstract ? pubDetails.abstract.substring(0, 200) + '...' : '',
              publicationDate: pubDetails.publicationDate,
              doi: pubDetails.doi
            });
          }
        } catch (err) {
          console.error(`Fehler beim Abrufen von Details für Publikation ${item._id}:`, err);
        }
      }
      
      // Aktualisiere den Cache
      this.cache.trendingPublications = {
        data: trendingPublications,
        timestamp: now,
        period
      };
      
      return trendingPublications.slice(0, limit);
    } catch (error) {
      console.error('Fehler beim Abrufen der Trend-Publikationen:', error);
      throw error;
    }
  }

  /**
   * Liefert Speicherstatistiken der Plattform
   * @returns {Promise<Object>} Speicherstatistiken
   */
  async getStorageStats() {
    if (!this.initialized) await this.initialize();
    
    // Prüfe, ob ein gültiger Cache vorhanden ist
    const now = Date.now();
    if (this.cache.storageStats.data && 
        (now - this.cache.storageStats.timestamp) < this.config.cacheTimeMs) {
      return this.cache.storageStats.data;
    }
    
    try {
      // Bei Verwendung des EnhancedStorageService gibt es detailliertere Statistiken
      let detailedStats = {};
      if (this.storage && typeof this.storage.getDetailedStorageStats === 'function') {
        detailedStats = await this.storage.getDetailedStorageStats();
      } else {
        // Fallback für den grundlegenden StorageService
        const totalBytes = this.storage ? await this.storage.getTotalStorageUsage() : 0;
        detailedStats = {
          totalBytes,
          totalFormatted: this._formatBytes(totalBytes),
          protocols: { standard: totalBytes }
        };
      }
      
      // Aggregation aus der Datenbank für dateityp-spezifische Analyse
      const fileSizeByType = await this.db.aggregate('publications', [
        { $unwind: '$files' },
        { 
          $group: {
            _id: '$files.mimeType',
            totalSize: { $sum: '$files.size' },
            count: { $sum: 1 }
          }
        },
        { $sort: { totalSize: -1 } }
      ]);
      
      const fileTypeStats = fileSizeByType.map(type => ({
        mimeType: type._id,
        totalSize: type.totalSize,
        formattedSize: this._formatBytes(type.totalSize),
        count: type.count,
        percentageOfTotal: detailedStats.totalBytes > 0 
          ? (type.totalSize / detailedStats.totalBytes * 100).toFixed(2) + '%'
          : '0%'
      }));
      
      const storageStats = {
        ...detailedStats,
        fileTypes: fileTypeStats,
        totalPublications: await this.db.countDocuments('publications', {}),
        totalFiles: await this.db.aggregate('publications', [
          { $group: { _id: null, count: { $sum: { $size: '$files' } } } }
        ]).then(result => result[0]?.count || 0),
        averageFileSize: fileTypeStats.length > 0 
          ? this._formatBytes(detailedStats.totalBytes / fileTypeStats.reduce((acc, type) => acc + type.count, 0))
          : '0 B',
        lastUpdated: new Date()
      };
      
      // Aktualisiere den Cache
      this.cache.storageStats = {
        data: storageStats,
        timestamp: now
      };
      
      return storageStats;
    } catch (error) {
      console.error('Fehler beim Abrufen der Speicherstatistiken:', error);
      throw error;
    }
  }

  /**
   * Liefert Benutzerstatistiken der Plattform
   * @returns {Promise<Object>} Benutzerstatistiken
   */
  async getUserStats() {
    if (!this.initialized) await this.initialize();
    
    // Prüfe, ob ein gültiger Cache vorhanden ist
    const now = Date.now();
    if (this.cache.userStats.data && 
        (now - this.cache.userStats.timestamp) < this.config.cacheTimeMs) {
      return this.cache.userStats.data;
    }
    
    try {
      // Zähle Benutzertypen
      const roleDistribution = await this.db.aggregate('users', [
        { 
          $group: {
            _id: '$role',
            count: { $sum: 1 }
          }
        }
      ]);
      
      // Aktivität der letzten 30 Tage
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      
      const activeUsers = await this.db.countDocuments('users', {
        lastActivity: { $gte: thirtyDaysAgo }
      });
      
      // Aktivste Autoren
      const topAuthors = await this.db.aggregate('publications', [
        { $unwind: '$authors' },
        { 
          $group: {
            _id: '$authors.id',
            name: { $first: '$authors.name' },
            publicationCount: { $sum: 1 }
          }
        },
        { $sort: { publicationCount: -1 } },
        { $limit: 10 }
      ]);
      
      // Formatierte Benutzerstatistiken
      const totalUsers = await this.db.countDocuments('users', {});
      const userStats = {
        totalUsers,
        activeUsersLast30Days: activeUsers,
        activePercentage: totalUsers > 0 ? (activeUsers / totalUsers * 100).toFixed(2) + '%' : '0%',
        roleDistribution: roleDistribution.map(role => ({
          role: role._id || 'unspecified',
          count: role.count,
          percentage: totalUsers > 0 ? (role.count / totalUsers * 100).toFixed(2) + '%' : '0%'
        })),
        topAuthors: <AUTHORS>
          id: author._id,
          name: author.name,
          publicationCount: author.publicationCount
        })),
        newUsersLast30Days: await this.db.countDocuments('users', {
          createdAt: { $gte: thirtyDaysAgo }
        }),
        lastUpdated: new Date()
      };
      
      // Aktualisiere den Cache
      this.cache.userStats = {
        data: userStats,
        timestamp: now
      };
      
      return userStats;
    } catch (error) {
      console.error('Fehler beim Abrufen der Benutzerstatistiken:', error);
      throw error;
    }
  }

  /**
   * Liefert detaillierte Statistiken für eine spezifische Publikation
   * @param {string} publicationId - ID der Publikation
   * @returns {Promise<Object>} Detaillierte Publikationsstatistiken
   */
  async getPublicationStats(publicationId) {
    if (!this.initialized) await this.initialize();
    
    try {
      const publication = await this.db.findOne('publications', {
        _id: typeof publicationId === 'string' ? new ObjectId(publicationId) : publicationId
      });
      
      if (!publication) {
        throw new Error(`Publikation mit ID ${publicationId} nicht gefunden`);
      }
      
      // Grundlegende direkt gespeicherte Statistiken
      const baseStats = publication.stats || {
        views: 0,
        downloads: 0,
        citations: 0
      };
      
      // Zeitreihenanalyse der letzten 90 Tage
      const ninteyDaysAgo = new Date();
      ninteyDaysAgo.setDate(ninteyDaysAgo.getDate() - 90);
      
      // Aggregiere tägliche Zugriffsstatistiken
      const dailyStats = await this.db.aggregate('accessStats', [
        {
          $match: {
            publicationId: typeof publicationId === 'string' ? publicationId : publicationId.toString(),
            timestamp: { $gte: ninteyDaysAgo }
          }
        },
        {
          $group: {
            _id: {
              date: { $dateToString: { format: '%Y-%m-%d', date: '$timestamp' } },
              type: '$type'
            },
            count: { $sum: 1 }
          }
        },
        {
          $group: {
            _id: '$_id.date',
            stats: {
              $push: {
                type: '$_id.type',
                count: '$count'
              }
            }
          }
        },
        { $sort: { _id: 1 } }
      ]);
      
      // Verarbeite die Zeitreihendaten in ein tägliches Format
      const timeSeriesData = [];
      const currentDate = new Date(ninteyDaysAgo);
      const today = new Date();
      today.setHours(23, 59, 59, 999);
      
      while (currentDate <= today) {
        const dateString = currentDate.toISOString().split('T')[0];
        const dayData = dailyStats.find(d => d._id === dateString);
        
        const entry = {
          date: dateString,
          views: 0,
          downloads: 0,
          citations: 0
        };
        
        if (dayData) {
          dayData.stats.forEach(stat => {
            if (entry[stat.type] !== undefined) {
              entry[stat.type] = stat.count;
            }
          });
        }
        
        timeSeriesData.push(entry);
        
        // Gehe zum nächsten Tag
        currentDate.setDate(currentDate.getDate() + 1);
      }
      
      // Geografische Verteilung, falls vorhanden
      const geoDistribution = await this.db.aggregate('accessStats', [
        {
          $match: {
            publicationId: typeof publicationId === 'string' ? publicationId : publicationId.toString(),
            'metadata.country': { $exists: true }
          }
        },
        {
          $group: {
            _id: '$metadata.country',
            count: { $sum: 1 }
          }
        },
        { $sort: { count: -1 } }
      ]);
      
      // Format der Antwort
      const publicationStats = {
        id: publicationId,
        title: publication.title,
        authors: publication.authors,
        publishedDate: publication.publicationDate,
        doi: publication.doi,
        metrics: {
          views: baseStats.views,
          downloads: baseStats.downloads,
          citations: baseStats.citations,
          // Einige abgeleitete Metriken
          impactScore: this._calculateImpactScore(baseStats),
          downloadConversionRate: baseStats.views > 0 
            ? (baseStats.downloads / baseStats.views * 100).toFixed(2) + '%' 
            : '0%'
        },
        timeSeries: timeSeriesData,
        geoDistribution: geoDistribution.map(geo => ({
          country: geo._id,
          count: geo.count,
          percentage: (geo.count / (baseStats.views + baseStats.downloads) * 100).toFixed(2) + '%'
        })),
        // Dateistatistiken
        files: publication.files ? publication.files.map(file => ({
          name: file.name,
          size: file.size,
          formattedSize: this._formatBytes(file.size),
          mimeType: file.mimeType,
          downloads: file.downloads || 0
        })) : [],
        lastUpdated: new Date()
      };
      
      return publicationStats;
    } catch (error) {
      console.error(`Fehler beim Abrufen der Statistiken für Publikation ${publicationId}:`, error);
      throw error;
    }
  }

  /**
   * Liefert Download-Statistiken über einen bestimmten Zeitraum
   * @param {Object} [options] - Abfrageoptionen
   * @param {Date} [options.startDate] - Startdatum für die Filterung
   * @param {Date} [options.endDate] - Enddatum für die Filterung
   * @param {string} [options.groupBy='day'] - Gruppierung ('day', 'week', 'month')
   * @returns {Promise<Array>} Download-Statistiken nach Zeitraum
   */
  async getDownloadStats({ startDate, endDate, groupBy = 'day' } = {}) {
    if (!this.initialized) await this.initialize();
    
    const query = { type: 'download' };
    
    // Setze Standardzeiträume, falls nicht angegeben
    if (!startDate) {
      startDate = new Date();
      startDate.setDate(startDate.getDate() - 30); // Standard: letzte 30 Tage
    }
    
    if (!endDate) {
      endDate = new Date();
    }
    
    query.timestamp = { $gte: startDate, $lte: endDate };
    
    // Format für die Datumsgruppierung basierend auf groupBy
    let dateFormat;
    switch (groupBy) {
      case 'week':
        dateFormat = '%Y-%U'; // Jahr-Wochennummer
        break;
      case 'month':
        dateFormat = '%Y-%m'; // Jahr-Monat
        break;
      case 'day':
      default:
        dateFormat = '%Y-%m-%d'; // Jahr-Monat-Tag
        break;
    }
    
    // Aggregationspipeline für zeitbasierte Gruppierung
    const pipeline = [
      { $match: query },
      {
        $group: {
          _id: {
            date: { $dateToString: { format: dateFormat, date: '$timestamp' } },
            publicationId: '$publicationId'
          },
          count: { $sum: 1 }
        }
      },
      {
        $group: {
          _id: '$_id.date',
          uniquePublications: { $addToSet: '$_id.publicationId' },
          totalDownloads: { $sum: '$count' }
        }
      },
      {
        $project: {
          _id: 0,
          period: '$_id',
          uniquePublications: { $size: '$uniquePublications' },
          totalDownloads: 1
        }
      },
      { $sort: { period: 1 } }
    ];
    
    try {
      const downloadStats = await this.db.aggregate('accessStats', pipeline);
      
      // Fülle fehlende Zeiträume auf
      const fullDownloadStats = this._fillMissingTimePeriodsInStats(
        downloadStats, 
        startDate, 
        endDate, 
        groupBy
      );
      
      return fullDownloadStats;
    } catch (error) {
      console.error('Fehler beim Abrufen der Download-Statistiken:', error);
      throw error;
    }
  }

  /**
   * Fügt fehlende Zeiträume in Statistiken ein
   * @private
   * @param {Array} stats - Vorhandene Statistikdaten
   * @param {Date} startDate - Startdatum
   * @param {Date} endDate - Enddatum
   * @param {string} groupBy - Gruppierungstyp ('day', 'week', 'month')
   * @returns {Array} Vervollständigte Statistiken
   */
  _fillMissingTimePeriodsInStats(stats, startDate, endDate, groupBy) {
    const result = [...stats];
    const existingPeriods = new Set(stats.map(s => s.period));
    
    const currentDate = new Date(startDate);
    while (currentDate <= endDate) {
      let period;
      
      switch (groupBy) {
        case 'week': {
          const year = currentDate.getFullYear();
          const weekNum = this._getWeekNumber(currentDate);
          period = `${year}-${weekNum.toString().padStart(2, '0')}`;
          // Gehe eine Woche weiter
          currentDate.setDate(currentDate.getDate() + 7);
          break;
        }
        case 'month': {
          const year = currentDate.getFullYear();
          const month = currentDate.getMonth() + 1;
          period = `${year}-${month.toString().padStart(2, '0')}`;
          // Gehe einen Monat weiter
          currentDate.setMonth(currentDate.getMonth() + 1);
          break;
        }
        case 'day':
        default: {
          period = currentDate.toISOString().split('T')[0];
          // Gehe einen Tag weiter
          currentDate.setDate(currentDate.getDate() + 1);
          break;
        }
      }
      
      if (!existingPeriods.has(period)) {
        result.push({
          period,
          uniquePublications: 0,
          totalDownloads: 0
        });
      }
    }
    
    // Sortiere nach Periode
    return result.sort((a, b) => a.period.localeCompare(b.period));
  }

  /**
   * Berechnet die Wochennummer für ein Datum
   * @private
   * @param {Date} date - Datum
   * @returns {number} Wochennummer
   */
  _getWeekNumber(date) {
    const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
    const dayNum = d.getUTCDay() || 7;
    d.setUTCDate(d.getUTCDate() + 4 - dayNum);
    const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
    return Math.ceil((((d - yearStart) / 86400000) + 1) / 7);
  }

  /**
   * Berechnet einen Impact-Score basierend auf verschiedenen Metriken
   * @private
   * @param {Object} metrics - Publikationsmetriken
   * @param {number} metrics.views - Anzahl der Aufrufe
   * @param {number} metrics.downloads - Anzahl der Downloads
   * @param {number} metrics.citations - Anzahl der Zitierungen
   * @returns {number} Berechneter Impact-Score
   */
  _calculateImpactScore(metrics) {
    // Einfacher gewichteter Score-Algorithmus
    const viewWeight = 1;
    const downloadWeight = 3;
    const citationWeight = 10;
    
    const score = (
      (metrics.views || 0) * viewWeight +
      (metrics.downloads || 0) * downloadWeight +
      (metrics.citations || 0) * citationWeight
    );
    
    return Math.round(score);
  }
  
  /**
   * Formatiert Bytes in eine menschenlesbare Größenangabe
   * @private
   * @param {number} bytes - Anzahl der Bytes
   * @param {number} [decimals=2] - Anzahl der Nachkommastellen
   * @returns {string} Formatierte Größenangabe
   */
  _formatBytes(bytes, decimals = 2) {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
    
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  }
}

export default StatsService; 